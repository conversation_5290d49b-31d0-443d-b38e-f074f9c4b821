(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["uni_modules/sp-editor/components/sp-editor/sp-editor"],{

/***/ 681:
/*!************************************************************************!*\
  !*** D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue ***!
  \************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _sp_editor_vue_vue_type_template_id_938741ae___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sp-editor.vue?vue&type=template&id=938741ae& */ 682);
/* harmony import */ var _sp_editor_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sp-editor.vue?vue&type=script&lang=js& */ 684);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _sp_editor_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _sp_editor_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _sp_editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sp-editor.vue?vue&type=style&index=0&lang=scss& */ 687);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _sp_editor_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _sp_editor_vue_vue_type_template_id_938741ae___WEBPACK_IMPORTED_MODULE_0__["render"],
  _sp_editor_vue_vue_type_template_id_938741ae___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _sp_editor_vue_vue_type_template_id_938741ae___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "uni_modules/sp-editor/components/sp-editor/sp-editor.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 682:
/*!*******************************************************************************************************!*\
  !*** D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?vue&type=template&id=938741ae& ***!
  \*******************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_template_id_938741ae___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sp-editor.vue?vue&type=template&id=938741ae& */ 683);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_template_id_938741ae___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_template_id_938741ae___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_template_id_938741ae___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_template_id_938741ae___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 683:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?vue&type=template&id=938741ae& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = !_vm.readOnly ? _vm.toolbarList.includes("header") : null
  var l0 = _vm.__map(_vm.fabTools.header, function (item, __i0__) {
    var $orig = _vm.__get_orig(item)
    var g1 = _vm.toolbarList.includes(item.name)
    return {
      $orig: $orig,
      g1: g1,
    }
  })
  var g2 = !_vm.readOnly ? _vm.toolbarList.includes("bold") : null
  var g3 = !_vm.readOnly ? _vm.toolbarList.includes("italic") : null
  var g4 = !_vm.readOnly ? _vm.toolbarList.includes("underline") : null
  var g5 = !_vm.readOnly ? _vm.toolbarList.includes("strike") : null
  var g6 = !_vm.readOnly ? _vm.toolbarList.includes("align") : null
  var l1 = _vm.__map(_vm.fabTools.align, function (item, __i1__) {
    var $orig = _vm.__get_orig(item)
    var g7 = _vm.toolbarList.includes(item.name)
    return {
      $orig: $orig,
      g7: g7,
    }
  })
  var g8 = !_vm.readOnly ? _vm.toolbarList.includes("lineHeight") : null
  var g9 = !_vm.readOnly ? _vm.toolbarList.includes("letterSpacing") : null
  var g10 = !_vm.readOnly ? _vm.toolbarList.includes("marginTop") : null
  var g11 = !_vm.readOnly ? _vm.toolbarList.includes("marginBottom") : null
  var g12 = !_vm.readOnly ? _vm.toolbarList.includes("fontFamily") : null
  var g13 = !_vm.readOnly ? _vm.toolbarList.includes("fontSize") : null
  var g14 = !_vm.readOnly ? _vm.toolbarList.includes("color") : null
  var g15 = !_vm.readOnly ? _vm.toolbarList.includes("backgroundColor") : null
  var g16 = !_vm.readOnly ? _vm.toolbarList.includes("date") : null
  var g17 = !_vm.readOnly ? _vm.toolbarList.includes("listCheck") : null
  var g18 = !_vm.readOnly ? _vm.toolbarList.includes("listOrdered") : null
  var g19 = !_vm.readOnly ? _vm.toolbarList.includes("listBullet") : null
  var g20 = !_vm.readOnly ? _vm.toolbarList.includes("divider") : null
  var g21 = !_vm.readOnly ? _vm.toolbarList.includes("indentDec") : null
  var g22 = !_vm.readOnly ? _vm.toolbarList.includes("indentInc") : null
  var g23 = !_vm.readOnly ? _vm.toolbarList.includes("scriptSub") : null
  var g24 = !_vm.readOnly ? _vm.toolbarList.includes("scriptSuper") : null
  var g25 = !_vm.readOnly ? _vm.toolbarList.includes("direction") : null
  var g26 = !_vm.readOnly ? _vm.toolbarList.includes("image") : null
  var g27 = !_vm.readOnly ? _vm.toolbarList.includes("video") : null
  var g28 = !_vm.readOnly ? _vm.toolbarList.includes("link") : null
  var g29 = !_vm.readOnly ? _vm.toolbarList.includes("undo") : null
  var g30 = !_vm.readOnly ? _vm.toolbarList.includes("redo") : null
  var g31 = !_vm.readOnly ? _vm.toolbarList.includes("removeFormat") : null
  var g32 = !_vm.readOnly ? _vm.toolbarList.includes("clear") : null
  var g33 = !_vm.readOnly ? _vm.toolbarList.includes("export") : null
  var g34 =
    _vm.toolbarList.includes("color") ||
    _vm.toolbarList.includes("backgroundColor")
  var g35 = _vm.toolbarList.includes("link") && !_vm.readOnly
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        l0: l0,
        g2: g2,
        g3: g3,
        g4: g4,
        g5: g5,
        g6: g6,
        l1: l1,
        g8: g8,
        g9: g9,
        g10: g10,
        g11: g11,
        g12: g12,
        g13: g13,
        g14: g14,
        g15: g15,
        g16: g16,
        g17: g17,
        g18: g18,
        g19: g19,
        g20: g20,
        g21: g21,
        g22: g22,
        g23: g23,
        g24: g24,
        g25: g25,
        g26: g26,
        g27: g27,
        g28: g28,
        g29: g29,
        g30: g30,
        g31: g31,
        g32: g32,
        g33: g33,
        g34: g34,
        g35: g35,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 684:
/*!*************************************************************************************************!*\
  !*** D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sp-editor.vue?vue&type=script&lang=js& */ 685);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 685:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _utils = __webpack_require__(/*! ../../utils */ 686);
var ColorPicker = function ColorPicker() {
  __webpack_require__.e(/*! require.ensure | uni_modules/sp-editor/components/sp-editor/color-picker */ "uni_modules/sp-editor/components/sp-editor/color-picker").then((function () {
    return resolve(__webpack_require__(/*! ./color-picker.vue */ 805));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var LinkEdit = function LinkEdit() {
  __webpack_require__.e(/*! require.ensure | uni_modules/sp-editor/components/sp-editor/link-edit */ "uni_modules/sp-editor/components/sp-editor/link-edit").then((function () {
    return resolve(__webpack_require__(/*! ./link-edit.vue */ 812));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var FabTool = function FabTool() {
  __webpack_require__.e(/*! require.ensure | uni_modules/sp-editor/components/sp-editor/fab-tool */ "uni_modules/sp-editor/components/sp-editor/fab-tool").then((function () {
    return resolve(__webpack_require__(/*! ./fab-tool.vue */ 819));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default2 = {
  components: {
    ColorPicker: ColorPicker,
    LinkEdit: LinkEdit,
    FabTool: FabTool
  },
  props: {
    // 编辑器id可传入，以便循环组件使用，防止id重复
    editorId: {
      type: String,
      default: 'editor'
    },
    placeholder: {
      type: String,
      default: '写点什么吧 ~'
    },
    // 是否只读
    readOnly: {
      type: Boolean,
      default: false
    },
    // 最大字数限制，-1不限
    maxlength: {
      type: Number,
      default: -1
    },
    // 工具栏配置
    toolbarConfig: {
      type: Object,
      default: function _default() {
        return {
          keys: [],
          // 要显示的工具，优先级最大
          excludeKeys: [],
          // 除这些指定的工具外，其他都显示

          iconSize: '18px',
          // 小程序端工具栏字体大小
          iconColumns: 10 // 小程序端工具栏列数
        };
      }
    }
  },

  watch: {
    toolbarConfig: {
      deep: true,
      immediate: true,
      handler: function handler(newToolbar) {
        var _newToolbar$keys;
        /**
         * 若工具栏配置中keys存在，则以keys为准
         * 否则以excludeKeys向toolbarAllList中排查
         * 若keys与excludeKeys皆为空，则以toolbarAllList为准
         */
        if (((_newToolbar$keys = newToolbar.keys) === null || _newToolbar$keys === void 0 ? void 0 : _newToolbar$keys.length) > 0) {
          this.toolbarList = newToolbar.keys;
        } else {
          var _newToolbar$excludeKe;
          this.toolbarList = ((_newToolbar$excludeKe = newToolbar.excludeKeys) === null || _newToolbar$excludeKe === void 0 ? void 0 : _newToolbar$excludeKe.length) > 0 ? this.toolbarAllList.filter(function (item) {
            return !newToolbar.excludeKeys.includes(item);
          }) : this.toolbarAllList;
        }
        this.iconSize = newToolbar.iconSize || '18px';
        this.iconColumns = newToolbar.iconColumns || 10;
      }
    }
  },
  data: function data() {
    return {
      formats: {},
      curFab: '',
      // 当前悬浮工具栏
      fabXY: {},
      textColor: '',
      backgroundColor: '',
      curColor: '',
      defaultColor: {
        r: 0,
        g: 0,
        b: 0,
        a: 1
      },
      // 调色板默认颜色
      iconSize: '20px',
      // 工具栏图标字体大小
      iconColumns: 10,
      // 工具栏列数
      toolbarList: [],
      toolbarAllList: ['header',
      // 标题
      'H1',
      // 一级标题
      'H2',
      // 二级标题
      'H3',
      // 三级标题
      'H4',
      // 四级标题
      'H5',
      // 五级标题
      'H6',
      // 六级标题
      'bold',
      // 加粗
      'italic',
      // 斜体
      'underline',
      // 下划线
      'strike',
      // 删除线
      'align',
      // 对齐方式
      'alignLeft',
      // 左对齐
      'alignCenter',
      // 居中对齐
      'alignRight',
      // 右对齐
      'alignJustify',
      // 两端对齐
      'lineHeight',
      // 行间距
      //'letterSpacing', // 字间距
      //'marginTop', // 段前距
      //'marginBottom', // 段后距
      'fontFamily',
      // 字体
      'fontSize',
      // 字号
      'color',
      // 文字颜色
      'backgroundColor',
      // 背景颜色
      //'date', // 日期
      //'listCheck', // 待办
      'listOrdered',
      // 有序列表
      'listBullet',
      // 无序列表
      'indentInc',
      // 增加缩进
      'indentDec',
      // 减少缩进
      'divider',
      // 分割线
      //'scriptSub', // 下标
      //'scriptSuper', // 上标
      //'direction', // 文本方向
      'image',
      // 图片
      //'video', // 视频
      //'link', // 超链接
      'undo',
      // 撤销
      'redo',
      // 重做
      //'removeFormat', // 清除格式
      'clear' // 清空
      //'export' // 导出
      ],

      fabTools: {
        header: [{
          title: '一级标题',
          name: 'H1',
          value: 1,
          icon: 'icon-format-header-1'
        }, {
          title: '二级标题',
          name: 'H2',
          value: 2,
          icon: 'icon-format-header-2'
        }, {
          title: '三级标题',
          name: 'H3',
          value: 3,
          icon: 'icon-format-header-3'
        }, {
          title: '四级标题',
          name: 'H4',
          value: 4,
          icon: 'icon-format-header-4'
        }, {
          title: '五级标题',
          name: 'H5',
          value: 5,
          icon: 'icon-format-header-5'
        }, {
          title: '六级标题',
          name: 'H6',
          value: 6,
          icon: 'icon-format-header-6'
        }],
        fontFamily: [{
          title: '宋体',
          name: '宋',
          value: '宋体',
          icon: ''
        }, {
          title: '黑体',
          name: '黑',
          value: '黑体',
          icon: ''
        }, {
          title: '楷体',
          name: '楷',
          value: '楷体',
          icon: ''
        }, {
          title: '仿宋',
          name: '仿',
          value: '仿宋',
          icon: ''
        }, {
          title: '华文隶书',
          name: '隶',
          value: 'STLiti',
          icon: ''
        }, {
          title: '华文行楷',
          name: '行',
          value: 'STXingkai',
          icon: ''
        }, {
          title: '幼圆',
          name: '圆',
          value: 'YouYuan',
          icon: ''
        }],
        fontSize: [{
          title: '12',
          name: '12',
          value: '12px',
          icon: ''
        }, {
          title: '14',
          name: '14',
          value: '14px',
          icon: ''
        }, {
          title: '16',
          name: '16',
          value: '16px',
          icon: ''
        }, {
          title: '18',
          name: '18',
          value: '18px',
          icon: ''
        }, {
          title: '20',
          name: '20',
          value: '20px',
          icon: ''
        }, {
          title: '22',
          name: '22',
          value: '22px',
          icon: ''
        }, {
          title: '24',
          name: '24',
          value: '24px',
          icon: ''
        }],
        align: [{
          title: '左对齐',
          name: 'alignLeft',
          value: 'left',
          icon: 'icon-zuoduiqi'
        }, {
          title: '居中对齐',
          name: 'alignCenter',
          value: 'center',
          icon: 'icon-juzhongduiqi'
        }, {
          title: '右对齐',
          name: 'alignRight',
          value: 'right',
          icon: 'icon-youduiqi'
        }, {
          title: '两端对齐',
          name: 'alignJustify',
          value: 'justify',
          icon: 'icon-zuoyouduiqi'
        }],
        lineHeight: [{
          title: '1倍',
          name: '1',
          value: '1',
          icon: ''
        }, {
          title: '1.5倍',
          name: '1.5',
          value: '1.5',
          icon: ''
        }, {
          title: '2倍',
          name: '2',
          value: '2',
          icon: ''
        }, {
          title: '2.5倍',
          name: '2.5',
          value: '2.5',
          icon: ''
        }, {
          title: '3倍',
          name: '3',
          value: '3',
          icon: ''
        }],
        // 字间距/段前距/段后距
        space: [{
          title: '0.5倍',
          name: '0.5',
          value: '0.5em',
          icon: ''
        }, {
          title: '1倍',
          name: '1',
          value: '1em',
          icon: ''
        }, {
          title: '1.5倍',
          name: '1.5',
          value: '1.5em',
          icon: ''
        }, {
          title: '2倍',
          name: '2',
          value: '2em',
          icon: ''
        }, {
          title: '2.5倍',
          name: '2.5',
          value: '2.5em',
          icon: ''
        }, {
          title: '3倍',
          name: '3',
          value: '3em',
          icon: ''
        }]
      }
    };
  },
  methods: {
    onEditorReady: function onEditorReady() {
      var _this = this;
      uni.createSelectorQuery().in(this).select('#' + this.editorId).context(function (res) {
        _this.editorCtx = res.context;
        _this.$emit('init', _this.editorCtx, _this.editorId);
      }).exec();
    },
    undo: function undo() {
      this.editorCtx.undo();
    },
    redo: function redo() {
      this.editorCtx.redo();
    },
    format: function format(e) {
      var _e$target$dataset = e.target.dataset,
        name = _e$target$dataset.name,
        value = _e$target$dataset.value;
      if (!name) return;
      switch (name) {
        case 'color':
        case 'backgroundColor':
          this.curColor = name;
          this.showPicker();
          break;
        default:
          this.editorCtx.format(name, value);
          break;
      }
    },
    // 悬浮工具点击
    fabTap: function fabTap(fabType) {
      if (this.curFab != fabType) {
        this.curFab = fabType;
      } else {
        this.curFab = '';
      }
    },
    // 悬浮工具子集点击
    fabTapSub: function fabTapSub(e, fabType) {
      this.format(e);
      this.fabTap(fabType);
    },
    showPicker: function showPicker() {
      switch (this.curColor) {
        case 'color':
          this.defaultColor = this.textColor ? this.$refs.colorPickerRef.hex2Rgb(this.textColor) : {
            r: 0,
            g: 0,
            b: 0,
            a: 1
          };
          break;
        case 'backgroundColor':
          this.defaultColor = this.backgroundColor ? this.$refs.colorPickerRef.hex2Rgb(this.backgroundColor) : {
            r: 0,
            g: 0,
            b: 0,
            a: 0
          };
          break;
      }
      this.$refs.colorPickerRef.open();
    },
    confirmColor: function confirmColor(e) {
      switch (this.curColor) {
        case 'color':
          this.textColor = e.hex;
          this.editorCtx.format('color', this.textColor);
          break;
        case 'backgroundColor':
          this.backgroundColor = e.hex;
          this.editorCtx.format('backgroundColor', this.backgroundColor);
          break;
      }
    },
    onStatusChange: function onStatusChange(e) {
      if (e.detail.color) {
        this.textColor = e.detail.color;
      }
      if (e.detail.backgroundColor) {
        this.backgroundColor = e.detail.backgroundColor;
      }
      this.formats = e.detail;
    },
    insertDivider: function insertDivider() {
      this.editorCtx.insertDivider();
    },
    clear: function clear() {
      var _this2 = this;
      uni.showModal({
        title: '清空编辑器',
        content: '确定清空编辑器吗？',
        success: function success(_ref) {
          var confirm = _ref.confirm;
          if (confirm) {
            _this2.editorCtx.clear();
          }
        }
      });
    },
    removeFormat: function removeFormat() {
      var _this3 = this;
      uni.showModal({
        title: '文本格式化',
        content: '确定要清除所选择部分文本块格式吗？',
        showCancel: true,
        success: function success(_ref2) {
          var confirm = _ref2.confirm;
          if (confirm) {
            _this3.editorCtx.removeFormat();
          }
        }
      });
    },
    insertDate: function insertDate() {
      var date = new Date();
      var formatDate = "".concat(date.getFullYear(), "/").concat(date.getMonth() + 1, "/").concat(date.getDate());
      this.editorCtx.insertText({
        text: formatDate
      });
    },
    insertLink: function insertLink() {
      this.$refs.linkEditRef.open();
    },
    /**
     * 确认添加链接
     * @param {Object} e { text: '链接描述', href: '链接地址' }
     */
    confirmLink: function confirmLink(e) {
      var _this4 = this;
      this.$refs.linkEditRef.close();
      (0, _utils.addLink)(this.editorCtx, e, function () {
        // 修复添加超链接后，不触发input更新当前最新内容的bug，这里做一下手动更新
        _this4.editorCtx.getContents({
          success: function success(res) {
            _this4.$emit('input', {
              html: res.html,
              text: res.text
            }, _this4.editorId);
          }
        });
      });
      this.$emit('addLink', e, this.editorId);
    },
    insertImage: function insertImage() {
      var _this5 = this;
      // 微信小程序从基础库 2.21.0 开始， wx.chooseImage 停止维护，请使用 uni.chooseMedia 代替。
      uni.chooseMedia({
        // count: 1, // 默认9
        mediaType: ['image'],
        success: function success(res) {
          // 同上chooseImage处理
          var tempFiles = res.tempFiles;
          _this5.$emit('upinImage', tempFiles, _this5.editorCtx, _this5.editorId);
        },
        fail: function fail() {
          uni.showToast({
            title: '未授权访问相册权限，请授权后使用',
            icon: 'none'
          });
        }
      });
    },
    insertVideo: function insertVideo() {
      var _this6 = this;
      uni.chooseVideo({
        sourceType: ['camera', 'album'],
        success: function success(res) {
          var tempFilePath = res.tempFilePath;
          // 将文件和编辑器示例抛出，由开发者自行上传和插入图片
          _this6.$emit('upinVideo', tempFilePath, _this6.editorCtx, _this6.editorId);
        },
        fail: function fail() {
          uni.showToast({
            title: '未授权访问媒体权限，请授权后使用',
            icon: 'none'
          });
        }
      });
    },
    onEditorInput: function onEditorInput(e) {
      var _this7 = this;
      // 注意不要使用getContents获取html和text，会导致重复触发onStatusChange从而失去toolbar工具的高亮状态
      // 复制粘贴的时候detail会为空，此时应当直接return
      if (Object.keys(e.detail).length <= 0) return;
      var _e$detail = e.detail,
        html = _e$detail.html,
        text = _e$detail.text;
      // 识别到标识立即return
      if (text.indexOf(_utils.linkFlag) !== -1) return;
      var maxlength = parseInt(this.maxlength);
      var textStr = text.replace(/[ \t\r\n]/g, '');
      if (textStr.length > maxlength && maxlength != -1) {
        uni.showModal({
          content: "\u8D85\u8FC7".concat(maxlength, "\u5B57\u6570\u5566~"),
          confirmText: '确定',
          showCancel: false,
          success: function success() {
            _this7.$emit('overMax', {
              html: html,
              text: text
            }, _this7.editorId);
          }
        });
      } else {
        this.$emit('input', {
          html: html,
          text: text
        }, this.editorId);
      }
    },
    // 导出
    exportHtml: function exportHtml() {
      var _this8 = this;
      this.editorCtx.getContents({
        success: function success(res) {
          _this8.$emit('exportHtml', res.html, _this8.editorId);
        }
      });
    },
    eLongpress: function eLongpress() {
      /**
       * 微信小程序官方editor的长按事件有bug，需要重写覆盖，不需做任何逻辑，可见下面小程序社区问题链接
       * @tutorial https://developers.weixin.qq.com/community/develop/doc/000c04b3e1c1006f660065e4f61000
       */
    }
  }
};
exports.default = _default2;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 687:
/*!**********************************************************************************************************!*\
  !*** D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?vue&type=style&index=0&lang=scss& ***!
  \**********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sp-editor.vue?vue&type=style&index=0&lang=scss& */ 688);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_sp_editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 688:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?vue&type=style&index=0&lang=scss& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/sp-editor/components/sp-editor/sp-editor.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/sp-editor/components/sp-editor/sp-editor-create-component',
    {
        'uni_modules/sp-editor/components/sp-editor/sp-editor-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(681))
        })
    },
    [['uni_modules/sp-editor/components/sp-editor/sp-editor-create-component']]
]);

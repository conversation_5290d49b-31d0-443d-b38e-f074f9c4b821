{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/detail.vue?3a35", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/detail.vue?8c6c", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/detail.vue?bcce", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/detail.vue?b7c2", "uni-app:///pages/patrol_pkg/shift/detail.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/detail.vue?ae2d", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/detail.vue?84f0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "shiftId", "shiftInfo", "loading", "onLoad", "uni", "title", "icon", "setTimeout", "methods", "loadShiftDetail", "PatrolApi", "params", "shift_id", "res", "console", "formatTime", "formatDate", "goToEdit", "url", "confirmDelete", "content", "confirmColor", "success", "deleteShift"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsFpnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;MACAC;QACAC;QACAC;MACA;MAEAC;QACAH;MACA;IACA;EACA;EACAI;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAL;kBACAC;gBACA;gBAAA;gBAAA,OAEAK;kBACAC;oBACAC;kBACA;gBACA;cAAA;gBAJAC;gBAMA;kBACA;;kBAEA;kBACA;oBACA;sBAAA;oBAAA;kBACA;gBACA;kBACAT;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAQ;gBACAV;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACAF;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAW;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;QACA;;QAEA;QACA;MACA;QACAF;QACA;MACA;IACA;IAEA;IACAG;MACAb;QACAc;MACA;IACA;IAEA;IACAC;MAAA;MACAf;QACAC;QACAe;QACAC;QACAC;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAT;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAnB;kBACAC;gBACA;gBAAA;gBAAA,OAEAK;kBACAC;oBACAC;kBACA;gBACA;cAAA;gBAJAC;gBAMA;kBACAT;oBACAC;oBACAC;kBACA;;kBAEA;kBACAF;;kBAEA;kBACAG;oBACAH;kBACA;gBACA;kBACAA;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAQ;gBACAV;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEAF;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChPA;AAAA;AAAA;AAAA;AAA2oC,CAAgB,inCAAG,EAAC,C;;;;;;;;;;;ACA/pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/shift/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/shift/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=9f6bb9ea&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/shift/detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=9f6bb9ea&\"", "var components\ntry {\n  components = {\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.formatTime(_vm.shiftInfo.start_time || _vm.shiftInfo.startTime)\n  var m1 = _vm.formatTime(_vm.shiftInfo.end_time || _vm.shiftInfo.endTime)\n  var m2 = _vm.formatDate(_vm.shiftInfo.create_time || _vm.shiftInfo.createTime)\n  var g0 = _vm.shiftInfo.rounds ? _vm.shiftInfo.rounds.length : null\n  var g1 = !_vm.shiftInfo.rounds || _vm.shiftInfo.rounds.length === 0\n  var l0 = !g1\n    ? _vm.__map(_vm.shiftInfo.rounds, function (round, index) {\n        var $orig = _vm.__get_orig(round)\n        var m3 = _vm.formatTime(round.time)\n        return {\n          $orig: $orig,\n          m3: m3,\n        }\n      })\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.uni.navigateBack()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        g0: g0,\n        g1: g1,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"shift-detail-container\">\n\t\t<!-- 头部信息卡片 -->\n\t\t<view class=\"info-card\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<view class=\"title-wrapper\">\n\t\t\t\t\t<view class=\"status-indicator\" :class=\"{'inactive': shiftInfo.status === 0}\"></view>\n\t\t\t\t\t<text class=\"shift-name\">{{ shiftInfo.name || '班次详情' }}</text>\n\t\t\t\t\t<text v-if=\"shiftInfo.is_cross_day || shiftInfo.isCrossDay\" class=\"cross-day-tag\">跨天班次</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"shift-status\" :class=\"{ 'status-active': shiftInfo.status === 1, 'status-inactive': shiftInfo.status === 0 }\">\n\t\t\t\t\t{{ shiftInfo.status === 1 ? '启用中' : '已停用' }}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"info-section\">\n\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t<view class=\"info-label\">时间安排：</view>\n\t\t\t\t\t<view class=\"info-value\">\n\t\t\t\t\t\t{{ formatTime(shiftInfo.start_time || shiftInfo.startTime) }} - {{ formatTime(shiftInfo.end_time || shiftInfo.endTime) }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t<view class=\"info-label\">创建时间：</view>\n\t\t\t\t\t<view class=\"info-value\">{{ formatDate(shiftInfo.create_time || shiftInfo.createTime) }}</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"info-row\" v-if=\"shiftInfo.remark\">\n\t\t\t\t\t<view class=\"info-label\">备注说明：</view>\n\t\t\t\t\t<view class=\"info-value\">{{ shiftInfo.remark }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 轮次列表 -->\n\t\t<view class=\"rounds-container\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<text class=\"section-title\">巡检轮次</text>\n\t\t\t\t<text class=\"badge\">{{ shiftInfo.rounds ? shiftInfo.rounds.length : 0 }} 个</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"rounds-wrapper\">\n\t\t\t\t<view class=\"empty-rounds\" v-if=\"!shiftInfo.rounds || shiftInfo.rounds.length === 0\">\n\t\t\t\t\t<p-empty-state type=\"data\" text=\"暂无轮次数据\" />\n\t\t\t\t\t<text class=\"empty-tip\">请在编辑页面添加轮次</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"round-list\" v-else>\n\t\t\t\t\t<view class=\"round-item\" v-for=\"(round, index) in shiftInfo.rounds\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"round-header\">\n\t\t\t\t\t\t\t<view class=\"round-index\">{{ round.name || '轮次 ' + round.round }}</view>\n\t\t\t\t\t\t\t<view class=\"round-duration\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>{{ formatTime(round.time) }}</text>\n\t\t\t\t\t\t\t\t<text v-if=\"round.day_offset && round.day_offset > 0\" class=\"round-nextday-tag\">次日</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"round-info\">\n\t\t\t\t\t\t\t<view class=\"round-valid-time\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"clock\" size=\"14\" color=\"#666666\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>有效时长: {{ round.duration || 60 }}分钟</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"round-status\">\n\t\t\t\t\t\t\t\t<view class=\"status-indicator\" :class=\"{'inactive': round.status === 0}\"></view>\n\t\t\t\t\t\t\t\t<text>{{ round.status === 1 ? '启用' : '停用' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 操作按钮 -->\n\t\t<view class=\"buttons-container\">\n\t\t\t<view class=\"action-buttons\">\n\t\t\t\t<view class=\"btn-secondary\" @click=\"uni.navigateBack()\">返回</view>\n\t\t\t\t<view class=\"btn-primary\" @click=\"goToEdit\">编辑班次</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n\nexport default {\n\tcomponents: {\n\t\tPEmptyState\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tshiftId: '',\n\t\t\tshiftInfo: {},\n\t\t\tloading: true\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tthis.shiftId = options.id;\n\t\tif (this.shiftId) {\n\t\t\tthis.loadShiftDetail();\n\t\t} else {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '无效的班次ID',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\t\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateBack();\n\t\t\t}, 1500);\n\t\t}\n\t},\n\tmethods: {\n\t\t// 加载班次详情\n\t\tasync loadShiftDetail() {\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconst res = await PatrolApi.callShiftFunction('getShiftDetail', {\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tshift_id: this.shiftId\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\tthis.shiftInfo = res.data;\n\t\t\t\t\t\n\t\t\t\t\t// 确保轮次按照round序号排序\n\t\t\t\t\tif (this.shiftInfo.rounds && this.shiftInfo.rounds.length > 0) {\n\t\t\t\t\t\tthis.shiftInfo.rounds.sort((a, b) => a.round - b.round);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '加载班次详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载班次详情错误', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载班次详情出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 格式化时间\n\t\tformatTime(timeStr) {\n\t\t\tif (!timeStr) return '--:--';\n\t\t\treturn timeStr;\n\t\t},\n\t\t\n\t\t// 格式化日期\n\t\tformatDate(dateStr) {\n\t\t\tif (!dateStr) return '未知';\n\t\t\ttry {\n\t\t\t\t// 如果是时间戳，转换为日期对象\n\t\t\t\tconst date = typeof dateStr === 'number' ? new Date(dateStr) : new Date(dateStr);\n\t\t\t\tif (isNaN(date.getTime())) return '无效日期';\n\t\t\t\t\n\t\t\t\t// 格式化为 YYYY-MM-DD HH:MM\n\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('日期格式化错误', e);\n\t\t\t\treturn '格式错误';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 跳转到编辑页面\n\t\tgoToEdit() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/patrol_pkg/shift/edit?id=${this.shiftId}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 确认删除\n\t\tconfirmDelete() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认删除',\n\t\t\t\tcontent: '确定要删除这个班次吗？此操作不可撤销。',\n\t\t\t\tconfirmColor: '#FF3B30',\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tawait this.deleteShift();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 删除班次\n\t\tasync deleteShift() {\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '删除中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconst res = await PatrolApi.callShiftFunction('deleteShift', {\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tshift_id: this.shiftId\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 触发列表刷新\n\t\t\t\t\tuni.$emit('shiftUpdated');\n\t\t\t\t\t\n\t\t\t\t\t// 返回列表页\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1500);\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '删除失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('删除班次错误', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '删除出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n/* 主色调变量 */\n$primary-color: #1677FF; // 支付宝蓝\n$primary-light: #E7F1FF;\n$primary-dark: #0E5FD8;\n\n$success-color: #07C160;\n$warning-color: #FFA300;\n$danger-color: #FF3B30;\n$info-color: #8F959E;\n\n$text-primary: #2C3E50;\n$text-secondary: #666666;\n$text-tertiary: #999999;\n$border-color: #EAEAEA;\n$background: #F7F8FA;\n\n$radius-sm: 6rpx;\n$radius-md: 12rpx;\n$radius-lg: 16rpx;\n$radius-xl: 24rpx;\n$radius-full: 999rpx;\n\n$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\n/* 淡入动画 */\n@keyframes fadeIn {\n\tfrom {\n\t\topacity: 0;\n\t\ttransform: translateY(20rpx);\n\t}\n\tto {\n\t\topacity: 1;\n\t\ttransform: translateY(0);\n\t}\n}\n\n/* 脉冲动画 */\n@keyframes pulse {\n\t0% {\n\t\ttransform: scale(1);\n\t}\n\t50% {\n\t\ttransform: scale(1.05);\n\t}\n\t100% {\n\t\ttransform: scale(1);\n\t}\n}\n\n.shift-detail-container {\n\tmin-height: 100vh;\n\tbackground-color: #F5F5F5;\n\tpadding-bottom: 40rpx;\n}\n\n/* 信息卡片样式 */\n.info-card {\n\tmargin: 20rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: $radius-lg;\n\toverflow: hidden;\n\tbox-shadow: $shadow-sm;\n\tanimation: fadeIn 0.5s ease-out;\n}\n\n.card-header {\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid $border-color;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.title-wrapper {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.status-indicator {\n\twidth: 12rpx;\n\theight: 12rpx;\n\tborder-radius: 50%;\n\tbackground-color: $success-color;\n\tmargin-right: 16rpx;\n\tposition: relative;\n\t\n\t&:after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: -4rpx;\n\t\tleft: -4rpx;\n\t\tright: -4rpx;\n\t\tbottom: -4rpx;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid rgba(7, 193, 96, 0.3);\n\t\topacity: 0;\n\t\tanimation: pulse 2s infinite;\n\t}\n\t\n\t&.inactive {\n\t\tbackground-color: $danger-color;\n\t\t\n\t\t&:after {\n\t\t\tborder-color: rgba(255, 59, 48, 0.3);\n\t\t}\n\t}\n}\n\n.shift-name {\n\tfont-size: 34rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n}\n\n.shift-status {\n\tfont-size: 24rpx;\n\tpadding: 6rpx 20rpx;\n\tborder-radius: $radius-full;\n\tfont-weight: 500;\n\t\n\t&.status-active {\n\t\tbackground-color: rgba(7, 193, 96, 0.1);\n\t\tcolor: $success-color;\n\t}\n\t\n\t&.status-inactive {\n\t\tbackground-color: rgba(255, 59, 48, 0.1);\n\t\tcolor: $danger-color;\n\t}\n}\n\n.info-section {\n\tpadding: 20rpx 30rpx;\n}\n\n.info-row {\n\tmargin-bottom: 24rpx;\n\t\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n}\n\n.info-label {\n\tfont-size: 28rpx;\n\tcolor: $text-secondary;\n\tmargin-bottom: 8rpx;\n\tfont-weight: 500;\n}\n\n.info-value {\n\tfont-size: 30rpx;\n\tcolor: $text-primary;\n\tline-height: 1.5;\n}\n\n.cross-day-tag {\n\tfont-size: 22rpx;\n\tcolor: #FA8C16;\n\tbackground-color: rgba(250, 140, 22, 0.1);\n\tpadding: 2rpx 8rpx;\n\tborder-radius: 4rpx;\n\tmargin-left: 8rpx;\n}\n\n/* 轮次列表样式 */\n.rounds-container {\n\tmargin: 20rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: $radius-lg;\n\toverflow: hidden;\n\tbox-shadow: $shadow-sm;\n\tanimation: fadeIn 0.5s ease-out;\n\tanimation-delay: 0.1s;\n\topacity: 0;\n\tanimation-fill-mode: forwards;\n}\n\n.section-header {\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid $border-color;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\tposition: relative;\n\tpadding-left: 20rpx;\n\t\n\t&:before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 6rpx;\n\t\theight: 28rpx;\n\t\tbackground-color: $primary-color;\n\t\tborder-radius: $radius-sm;\n\t}\n}\n\n.badge {\n\tbackground-color: $primary-light;\n\tcolor: $primary-color;\n\tfont-size: 24rpx;\n\tpadding: 4rpx 16rpx;\n\tborder-radius: $radius-full;\n\tfont-weight: 500;\n}\n\n.rounds-wrapper {\n\tpadding: 20rpx 30rpx;\n}\n\n.empty-rounds {\n\tpadding: 40rpx 0;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.empty-tip {\n\tfont-size: 26rpx;\n\tcolor: $text-tertiary;\n\tmargin-top: 20rpx;\n\tpadding: 8rpx 24rpx;\n\tbackground-color: $primary-light;\n\tborder-radius: $radius-full;\n\topacity: 0.8;\n}\n\n.round-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.round-item {\n\tmargin-bottom: 30rpx;\n\tbackground-color: $background;\n\tborder-radius: $radius-md;\n\tpadding: 20rpx;\n\tborder-left: 4rpx solid $primary-color;\n\ttransition: all 0.3s ease;\n\t\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\t\n\t&:hover {\n\t\ttransform: translateX(6rpx);\n\t\tbox-shadow: $shadow-sm;\n\t}\n}\n\n.round-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 16rpx;\n\tpadding-bottom: 16rpx;\n\tborder-bottom: 1rpx dashed $border-color;\n}\n\n.round-index {\n\tfont-size: 30rpx;\n\tfont-weight: 600;\n\tcolor: $primary-color;\n\tbackground-color: $primary-light;\n\tpadding: 6rpx 20rpx;\n\tborder-radius: $radius-full;\n}\n\n.round-duration {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 26rpx;\n\tcolor: $text-secondary;\n\t\n\ttext {\n\t\tmargin-left: 8rpx;\n\t}\n}\n\n.round-info {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-top: 12rpx;\n\tpadding-top: 8rpx;\n\tborder-top: 1px dashed #EFEFEF;\n}\n\n.round-valid-time {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 24rpx;\n\tcolor: $text-secondary;\n\t\n\tuni-icons {\n\t\tmargin-right: 4rpx;\n\t}\n}\n\n.round-status {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-top: 10rpx;\n\tfont-size: 24rpx;\n\tcolor: $text-secondary;\n}\n\n.status-indicator {\n\twidth: 10rpx;\n\theight: 10rpx;\n\tborder-radius: 50%;\n\tbackground-color: $success-color;\n\tmargin-right: 8rpx;\n\t\n\t&.inactive {\n\t\tbackground-color: $info-color;\n\t}\n}\n\n/* 按钮容器样式 */\n.buttons-container {\n\tmargin: 30rpx 20rpx;\n\tpadding: 0;\n}\n\n.action-buttons {\n\tdisplay: flex;\n\tgap: 20rpx;\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\n}\n\n.btn-primary, .btn-secondary {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n}\n\n.btn-primary {\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n}\n\n.btn-secondary {\n\tbackground-color: #F5F5F5;\n\tcolor: #666666;\n\tborder: 1rpx solid #EEEEEE;\n}\n\n/* 次日标记样式 */\n.round-nextday-tag {\n\tdisplay: inline-block;\n\tfont-size: 22rpx;\n\tpadding: 2rpx 8rpx;\n\tbackground-color: #FA8C16;\n\tcolor: #FFFFFF;\n\tborder-radius: 20rpx;\n\tmargin-left: 6rpx;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571661107\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/honor_pkg/admin/index.vue?cd6e", "webpack:///D:/Xwzc/pages/honor_pkg/admin/index.vue?d6d8", "webpack:///D:/Xwzc/pages/honor_pkg/admin/index.vue?e7e5", "webpack:///D:/Xwzc/pages/honor_pkg/admin/index.vue?8598", "uni-app:///pages/honor_pkg/admin/index.vue", "webpack:///D:/Xwzc/pages/honor_pkg/admin/index.vue?f84a", "webpack:///D:/Xwzc/pages/honor_pkg/admin/index.vue?6175"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "loading", "refreshing", "loadingText", "activeModule", "stats", "totalHonors", "monthlyHonors", "featuredHonors", "activeTypes", "overviewCards", "label", "value", "icon", "color", "functionModules", "id", "description", "count", "recentActivities", "recordSearch", "recordList", "recordPage", "recordSize", "recordTotal", "recordLoading", "featuredList", "selectedFeaturedIds", "featuredPage", "featuredSize", "featuredTotal", "featuredLoa<PERSON>", "featured<PERSON><PERSON>ch", "scrollDisabled", "scrollTop", "loadingStates", "modules", "activities", "records", "featured", "cacheData", "statsTime", "activitiesTime", "cacheExpiry", "onLoad", "methods", "checkPermission", "token", "uni", "title", "content", "showCancel", "success", "allowedRoles", "tokenHasPermission", "db", "where", "field", "get", "result", "userRole", "dbHasPermission", "console", "initializeData", "startTime", "Promise", "totalTime", "duration", "loadStats", "forceRefresh", "now", "uniCloud", "action", "res", "loadTime", "updateStatsUI", "handleStatsError", "card", "loadModuleCounts", "module", "calculateTrend", "preloadData", "networkType", "setTimeout", "getNetworkType", "fail", "recordLoadTime", "performance", "time", "timestamp", "isDataFresh", "retryOperation", "maxRetries", "delay", "i", "operation", "sleep", "loadRecentActivities", "limit", "item", "refreshRecentActivities", "getOperationIcon", "getOperationColor", "onRefresh", "refreshTime", "clearCache", "openModule", "url", "closeModule", "loadRecordList", "isLoadMore", "page", "size", "orderBy", "orderDirection", "loadBatchList", "loadFeaturedList", "search", "loadTypeList", "openAddRecord", "openBatchImport", "openBatchFeatured", "editRecord", "userName", "department", "userAvatar", "honorTypeId", "batchId", "reason", "isFeatured", "images", "deleteRecord", "honorId", "searchRecords", "clearSearch", "loadMoreRecords", "toggleFeaturedSelection", "selectAllFeatured", "batchSetFeatured", "actionText", "honorIds", "goBack", "formatTime", "preventScroll", "allowScroll", "formatDate", "loadMoreFeatured", "searchFeatured", "clearFeaturedSearch"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChKA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoannB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC,gBACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,EACA;MAEA;MACAC,kBACA;QACAC;QACAjB;QACAkB;QACAJ;QACAC;QACAI;MACA,GACA;QACAF;QACAjB;QACAkB;QACAJ;QACAC;QACAI;MACA,GACA;QACAF;QACAjB;QACAkB;QACAJ;QACAC;QACAI;MACA,GACA;QACAF;QACAjB;QACAkB;QACAJ;QACAC;QACAI;MACA,EACA;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;QACA9B;QACA+B;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAnC;QACAoC;QACAJ;QACAK;MACA;MAEA;MACAC;IACA;EACA;EAEAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;kBACAC;kBACAC;oBACAJ;kBACA;gBACA;gBAAA,kCACA;cAAA;gBAGAK,+DAEA;gBACAC;kBAAA;gBAAA;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;cAAA;gBAGA;gBACAC;gBAAA;gBAAA,OACAA,8BACAC,gCACAC,cACAC;cAAA;gBAAA;gBAHAC;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACAX;kBACAC;kBACAC;kBACAC;kBACAC;oBACAJ;kBACA;gBACA;gBAAA,kCACA;cAAA;gBAGAY;gBACAC;kBAAA;gBAAA;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBACAb;kBACAC;kBACAC;kBACAC;kBACAC;oBACAJ;kBACA;gBACA;gBAAA,kCACA;cAAA;gBAAA,kCAGA;cAAA;gBAAA;gBAAA;gBAEAc;gBACAd;kBACAC;kBACAC;kBACAC;kBACAC;oBACAJ;kBACA;gBACA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA;gBAAA,OAIAC,aACA,oBACA,2BACA,8BACA;cAAA;gBAEAC;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAlB;kBACAC;kBACApC;kBACAsD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBACA;gBACAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAEAN;gBAAA;gBAAA,OACAO;kBACAxE;kBACAC;oBACAwE;kBACA;gBACA;cAAA;gBALAC;gBAOAC;gBAAA,MAEAD;kBAAA;kBAAA;gBAAA;gBACAzE,wBAEA;gBACA;gBACA;gBAEA;;gBAEA;gBACA;kBACAgD;oBACAC;oBACApC;oBACAsD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAQ;MACA;QACArE;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAmE;MACA;MACA;QACAC;QACAA;MACA;;MAEA;MACA;QACA7B;UACAC;UACApC;UACAsD;QACA;MACA;QACAnB;UACAC;UACApC;UACAsD;QACA;MACA;IACA;IAEA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEAP;kBACAxE;kBACAC;oBACAwE;kBACA;gBACA;cAAA;gBALAC;gBAOA;kBACAzE,wBACA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;kBACA+E;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;kBACA;kBACAC;oBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACApC;oBACAI;sBAAA;oBAAA;oBACAiC;sBAAA;oBAAA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;MAEAC;QACAC;QACAC;MACA;;MAEA;MACA;QACAF;MACA;MAEAvC;;MAEA;MACA;QAAA;MAAA;IACA;IAEA;IACA0C;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAAC;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,MAEAD;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBALAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAQA;IAEA;IACAE;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA5B;gBACA;gBACAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAEAN;gBAAA;gBAAA,OACAO;kBACAxE;kBACAC;oBACAwE;oBACAxE;sBACAkG;oBACA;kBACA;gBACA;cAAA;gBARAzB;gBAUAC;gBAAA,MAEAD;kBAAA;kBAAA;gBAAA;gBACA;gBACApC;kBAAA,uCACA8D;oBACA;oBACAtF;oBACAC;kBAAA;gBAAA,CACA,GAEA;gBACA;gBACA;gBAEA;;gBAEA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;;gBAEA;gBACA;kBACAkC;oBACAC;oBACApC;oBACAsD;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACApD;kBACAC;kBACApC;kBACAsD;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnB;kBACAC;kBACApC;kBACAsD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAEA;gBACA;gBAEAvC;gBAAA;gBAAA,OACAC,aACA,yBACA,4BACA,mCACA;cAAA;gBAEAuC;gBAEAxD;kBACAC;kBACApC;kBACAsD;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnB;kBACAC;kBACApC;kBACAsD;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsC;MACA;QACApG;QACAoC;QACAJ;QACAK;MACA;IACA;IAEA;IACAgE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,gBAEA3B;gBAAA,oCACA,kCAMA,gCAKA,oCAMA;gBAAA;cAAA;gBAhBA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;cAAA;gBAGA/B;kBACA2D;gBACA;gBAAA;cAAA;gBAGA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;cAAA;gBAGA3D;kBACA2D;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;kBACA;gBACA;gBACA;gBAAA;gBAEAC;gBAAA;gBAAA,OAEAxC;kBACAxE;kBACAC;oBACAwE;oBACAxE;sBACA+G;sBACAC;sBACAC;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAXAzC;gBAaA;kBACAzE;kBACA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAIA;gBACA;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmH;MACA;IAAA,CACA;IAEAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAN;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAEAC;gBAAA;gBAAA,OAEAxC;kBACAxE;kBACAC;oBACAwE;oBACAxE;sBACA+G;sBACAC;sBACAC;sBACAC;sBACAG;oBACA;kBACA;gBACA;cAAA;gBAZA5C;gBAcA;kBACAzE;kBACA;oBACA;oBACA;kBACA;oBACA;oBACA;oBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAIA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAsH;MACA;IAAA,CACA;IAEA;IACAC;MACA;MACAvE;QACA2D;MACA;IACA;IAEAa;MACA;MACAxE;QACAC;QACApC;MACA;IACA;IAEA4G;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACA1G;QACA2G;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEAlF;QACA2D;MACA;IACA;IAEAwB;MAAA;MACA;MACAnF;QACAC;QACAC;QACAE;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAqB;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA;oBAAA,OAEAF;sBACAxE;sBACAC;wBACAwE;wBACAxE;0BAAAoI;wBAAA;sBACA;oBACA;kBAAA;oBAEApF;sBACAC;sBACApC;oBACA;oBAEA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEAmC;sBACAC;sBACApC;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAwH;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA9D;kBACAxE;kBACAC;oBACAwE;oBACAxE;sBACA+G;sBACAC;sBACAC;sBACAC;sBACAG;oBACA;kBACA;gBACA;cAAA;gBAZA5C;gBAcA;kBACA;kBAEA;oBACAzB;sBACAC;sBACApC;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAmC;kBACAC;kBACApC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAyH;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;QACA;MACA;QACA;UAAA;QAAA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA1F;kBACAC;kBACApC;gBACA;gBAAA;cAAA;gBAIA8H;gBAEA3F;kBACAC;kBACAC;kBACAE;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAqB;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA;8BAAA,OAEAF;gCACAxE;gCACAC;kCACAwE;kCACAxE;oCACA4I;oCACAX;kCACA;gCACA;8BACA;4BAAA;8BATAxD;8BAAA,MAWAA;gCAAA;gCAAA;8BAAA;8BACAzB;gCACAC;gCACApC;8BACA;;8BAEA;8BACA;8BAAA;8BAAA,OACA;4BAAA;8BAAA;8BAAA,OACA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA,MAEA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAGAmC;gCACAC;gCACApC;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAgI;MACA7F;QACAqC;UACArC;YACA2D;UACA;QACA;MACA;IACA;IAEAmC;MACA;QACA;QACA;QACA;QAEA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAcA;MACA;IAEA;IAEA;IACAC;MAiBA;MACA;IAEA;IAEAC;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACr7CA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/honor_pkg/admin/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/honor_pkg/admin/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3f8b765e&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=3f8b765e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3f8b765e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/honor_pkg/admin/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=3f8b765e&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = !_vm.loadingStates.activities\n    ? _vm.__map(_vm.recentActivities, function (activity, index) {\n        var $orig = _vm.__get_orig(activity)\n        var m0 = _vm.formatTime(activity.time)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var g0 = !_vm.loadingStates.activities ? _vm.recentActivities.length : null\n  var l1 =\n    _vm.activeModule === \"records\" && !_vm.loadingStates.records\n      ? _vm.__map(_vm.recordList, function (record, index) {\n          var $orig = _vm.__get_orig(record)\n          var m1 = _vm.formatDate(record.createTime)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  var g1 =\n    _vm.activeModule === \"records\" && !_vm.loadingStates.records\n      ? _vm.recordList.length > 0 && _vm.recordList.length < _vm.recordTotal\n      : null\n  var g2 =\n    _vm.activeModule === \"records\" &&\n    !_vm.loadingStates.records &&\n    g1 &&\n    !_vm.recordLoading\n      ? _vm.recordList.length\n      : null\n  var g3 =\n    _vm.activeModule === \"records\" && !_vm.loadingStates.records\n      ? _vm.recordList.length\n      : null\n  var g4 =\n    _vm.activeModule === \"featured\" ? _vm.selectedFeaturedIds.length : null\n  var g5 =\n    _vm.activeModule === \"featured\" ? _vm.selectedFeaturedIds.length : null\n  var g6 =\n    _vm.activeModule === \"featured\" ? _vm.selectedFeaturedIds.length : null\n  var g7 =\n    _vm.activeModule === \"featured\" ? _vm.selectedFeaturedIds.length : null\n  var g8 =\n    _vm.activeModule === \"featured\" ? _vm.selectedFeaturedIds.length : null\n  var g9 =\n    _vm.activeModule === \"featured\" ? _vm.selectedFeaturedIds.length : null\n  var g10 =\n    _vm.activeModule === \"featured\"\n      ? _vm.selectedFeaturedIds.length === _vm.featuredList.length &&\n        _vm.featuredList.length > 0\n      : null\n  var g11 =\n    _vm.activeModule === \"featured\"\n      ? _vm.selectedFeaturedIds.length === _vm.featuredList.length &&\n        _vm.featuredList.length > 0\n      : null\n  var l2 =\n    _vm.activeModule === \"featured\" && !_vm.loadingStates.featured\n      ? _vm.__map(_vm.featuredList, function (record, index) {\n          var $orig = _vm.__get_orig(record)\n          var g12 = _vm.selectedFeaturedIds.includes(record._id)\n          var g13 = _vm.selectedFeaturedIds.includes(record._id)\n          var g14 = _vm.selectedFeaturedIds.includes(record._id)\n          var m2 = _vm.formatDate(record.createTime)\n          return {\n            $orig: $orig,\n            g12: g12,\n            g13: g13,\n            g14: g14,\n            m2: m2,\n          }\n        })\n      : null\n  var g15 =\n    _vm.activeModule === \"featured\" && !_vm.loadingStates.featured\n      ? _vm.featuredList.length > 0 &&\n        _vm.featuredList.length < _vm.featuredTotal\n      : null\n  var g16 =\n    _vm.activeModule === \"featured\" &&\n    !_vm.loadingStates.featured &&\n    g15 &&\n    !_vm.featuredLoading\n      ? _vm.featuredList.length\n      : null\n  var g17 =\n    _vm.activeModule === \"featured\" && !_vm.loadingStates.featured\n      ? _vm.featuredList.length === 0 && !_vm.featuredLoading\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.refreshing = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        l1: l1,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n        g9: g9,\n        g10: g10,\n        g11: g11,\n        l2: l2,\n        g15: g15,\n        g16: g16,\n        g17: g17,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"admin-container\" :class=\"{ 'scroll-disabled': scrollDisabled }\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"18\" color=\"#FFFFFF\"></uni-icons>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"title\">荣誉管理中心</text>\n          <text class=\"subtitle\">表彰记录 · 数据管理</text>\n        </view>\n        <view class=\"header-stats\">\n          <text class=\"stat-text\">总计 {{ stats.totalHonors }} 条记录</text>\n        </view>\n      </view>\n    </view>\n\n    <scroll-view \n      class=\"content-scroll\" \n      scroll-y \n      refresher-enabled \n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\"\n      @refresherrestore=\"refreshing = false\"\n    >\n      <!-- 数据概览卡片 -->\n      <view class=\"overview-cards\">\n        <template v-if=\"loadingStates.stats\">\n          <!-- 骨架屏状态 -->\n          <view class=\"card-item skeleton-card\" v-for=\"n in 4\" :key=\"n\">\n            <view class=\"skeleton-icon\"></view>\n            <view class=\"skeleton-info\">\n              <view class=\"skeleton-number\"></view>\n              <view class=\"skeleton-label\"></view>\n            </view>\n          </view>\n        </template>\n        \n        <template v-else>\n          <!-- 实际数据 -->\n        <view class=\"card-item\" v-for=\"(card, index) in overviewCards\" :key=\"index\">\n          <view class=\"card-icon\" :style=\"{ backgroundColor: card.color }\">\n            <uni-icons :type=\"card.icon\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n          </view>\n          <view class=\"card-info\">\n            <text class=\"card-number\">{{ card.value }}</text>\n            <text class=\"card-label\">{{ card.label }}</text>\n          </view>\n          <view class=\"card-trend\" v-if=\"card.trend\">\n            <text class=\"trend-text\" :class=\"card.trend > 0 ? 'trend-up' : 'trend-down'\">\n              {{ card.trend > 0 ? '+' : '' }}{{ card.trend }}%\n            </text>\n          </view>\n        </view>\n        </template>\n      </view>\n\n      <!-- 功能模块 -->\n      <view class=\"function-modules\">\n        <view class=\"module-header\">\n          <view class=\"header-container\">\n            <view class=\"title-with-icon\">\n              <view class=\"icon-wrapper gear-icon\">\n                <uni-icons type=\"gear-filled\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n              </view>\n              <text class=\"module-title\">管理功能</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"module-grid\">\n          <template v-if=\"loadingStates.modules\">\n            <!-- 骨架屏状态 -->\n            <view class=\"module-item skeleton-module\" v-for=\"n in 4\" :key=\"n\">\n              <view class=\"skeleton-module-icon\"></view>\n              <view class=\"skeleton-module-content\">\n                <view class=\"skeleton-module-name\"></view>\n                <view class=\"skeleton-module-desc\"></view>\n                <view class=\"skeleton-module-count\"></view>\n              </view>\n            </view>\n          </template>\n          \n          <template v-else>\n            <!-- 实际数据 -->\n          <view \n            class=\"module-item\" \n            v-for=\"(module, index) in functionModules\" \n            :key=\"index\"\n            @click=\"openModule(module)\"\n          >\n            <view class=\"module-icon\" :style=\"{ backgroundColor: module.color }\">\n              <uni-icons :type=\"module.icon\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n            <view class=\"module-content\">\n              <text class=\"module-name\">{{ module.name }}</text>\n              <text class=\"module-desc\">{{ module.description }}</text>\n              <text class=\"module-count\">{{ module.count }}</text>\n            </view>\n            <view class=\"module-arrow\">\n              <uni-icons type=\"right\" size=\"14\" color=\"#C4C4C4\"></uni-icons>\n            </view>\n          </view>\n          </template>\n        </view>\n      </view>\n\n      <!-- 最近操作 -->\n      <view class=\"recent-activities\">\n        <view class=\"section-header\">\n          <view class=\"header-container\">\n            <view class=\"title-with-icon\">\n              <view class=\"icon-wrapper calendar-icon\">\n                <uni-icons type=\"calendar\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n              </view>\n              <text class=\"section-title\">最近操作</text>\n            </view>\n            <view class=\"header-action\" @click=\"refreshRecentActivities\">\n              <uni-icons :type=\"loadingStates.activities ? 'spinner-cycle' : 'refresh'\" \n                        size=\"16\" color=\"#FFFFFF\" \n                        :class=\"{ 'loading-spin': loadingStates.activities }\"></uni-icons>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"activity-list\">\n          <template v-if=\"loadingStates.activities\">\n            <!-- 骨架屏状态 -->\n            <view class=\"activity-item skeleton-activity\" v-for=\"n in 5\" :key=\"n\">\n              <view class=\"skeleton-activity-icon\"></view>\n              <view class=\"skeleton-activity-content\">\n                <view class=\"skeleton-activity-title\"></view>\n                <view class=\"skeleton-activity-desc\"></view>\n                <view class=\"skeleton-activity-time\"></view>\n              </view>\n            </view>\n          </template>\n          \n          <template v-else>\n            <!-- 实际数据 -->\n          <view \n            class=\"activity-item\" \n            v-for=\"(activity, index) in recentActivities\" \n            :key=\"index\"\n          >\n            <view class=\"activity-icon\" :style=\"{ backgroundColor: activity.color }\">\n              <uni-icons :type=\"activity.icon\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n            <view class=\"activity-content\">\n              <text class=\"activity-title\">{{ activity.title }}</text>\n              <text class=\"activity-desc\">{{ activity.description }}</text>\n              <text class=\"activity-time\">{{ formatTime(activity.time) }}</text>\n            </view>\n          </view>\n          \n          <view v-if=\"recentActivities.length === 0\" class=\"empty-activities\">\n            <uni-icons type=\"info\" size=\"32\" color=\"#C4C4C4\"></uni-icons>\n            <text class=\"empty-text\">暂无最近操作</text>\n          </view>\n          </template>\n        </view>\n      </view>\n\n      <!-- 底部安全区域 -->\n      <view class=\"bottom-safe-area\"></view>\n    </scroll-view>\n\n    <!-- 表彰记录管理模块 -->\n    <view v-if=\"activeModule === 'records'\" class=\"module-modal\" @click=\"closeModule\">\n      <view class=\"modal-content\" @click.stop>\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">表彰记录管理</text>\n          <view class=\"close-btn\" @click=\"closeModule\">\n            <uni-icons type=\"close\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n          </view>\n        </view>\n        \n        <view class=\"modal-body\">\n          <view class=\"quick-actions\">\n            <button class=\"action-btn primary\" @click=\"openAddRecord\">\n              <uni-icons type=\"plus\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n              添加表彰\n            </button>\n            <button class=\"action-btn secondary\" @click=\"openBatchFeatured\">\n              <uni-icons type=\"star\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n              批量精选\n            </button>\n          </view>\n          \n          <view class=\"search-area\">\n            <uni-easyinput \n              v-model=\"recordSearch\" \n              placeholder=\"搜索表彰记录...\"\n              prefixIcon=\"search\"\n              @confirm=\"searchRecords\"\n              @clear=\"clearSearch\"\n              clearable\n            ></uni-easyinput>\n          </view>\n          \n          <scroll-view \n            class=\"record-list\" \n            scroll-y \n            style=\"max-height: 600rpx; -webkit-overflow-scrolling: touch;\"\n            @scrolltolower=\"loadMoreRecords\"\n            :show-scrollbar=\"false\"\n            :enhanced=\"true\"\n          >\n            <template v-if=\"loadingStates.records\">\n              <!-- 骨架屏 -->\n              <view class=\"record-item skeleton-record\" v-for=\"n in 5\" :key=\"n\">\n                <view class=\"skeleton-record-avatar\"></view>\n                <view class=\"skeleton-record-info\">\n                  <view class=\"skeleton-record-name\"></view>\n                  <view class=\"skeleton-record-type\"></view>\n                  <view class=\"skeleton-record-time\"></view>\n                </view>\n                <view class=\"skeleton-record-actions\">\n                  <view class=\"skeleton-action-btn\"></view>\n                  <view class=\"skeleton-action-btn\"></view>\n                </view>\n              </view>\n            </template>\n            \n            <template v-else>\n              <!-- 实际数据 -->\n            <view \n              class=\"record-item\" \n              v-for=\"(record, index) in recordList\" \n              :key=\"index\"\n            >\n              <image :src=\"record.userAvatar || '/static/user/default-avatar.png'\" \n                     class=\"record-avatar\"\n                     mode=\"aspectFill\"></image>\n              <view class=\"record-info\">\n                <text class=\"record-name\">{{ record.userName }}</text>\n                <text class=\"record-type\">{{ record.honorTypeName }}</text>\n                <text class=\"record-time\">{{ formatDate(record.createTime) }}</text>\n              </view>\n              <view class=\"record-actions\">\n                <view class=\"action-btn-mini edit\" @click=\"editRecord(record)\">\n                  <uni-icons type=\"compose\" size=\"14\" color=\"#3a86ff\"></uni-icons>\n                </view>\n                <view class=\"action-btn-mini delete\" @click=\"deleteRecord(record)\">\n                  <uni-icons type=\"trash\" size=\"14\" color=\"#ef4444\"></uni-icons>\n                </view>\n              </view>\n            </view>\n              \n              <!-- 加载更多 -->\n              <view v-if=\"recordList.length > 0 && recordList.length < recordTotal\" class=\"load-more\">\n                <view v-if=\"recordLoading\" class=\"loading-item\">\n                  <uni-icons type=\"spinner-cycle\" size=\"16\" color=\"#3a86ff\" class=\"loading-spin\"></uni-icons>\n                  <text>加载中...</text>\n                </view>\n                <view v-else class=\"load-more-btn\" @click=\"loadMoreRecords\">\n                  <text>加载更多 ({{ recordList.length }}/{{ recordTotal }})</text>\n                </view>\n              </view>\n            \n            <!-- 空状态 -->\n            <view v-if=\"recordList.length === 0\" class=\"empty-state\">\n                <uni-icons type=\"info\" size=\"48\" color=\"#C4C4C4\"></uni-icons>\n              <text class=\"empty-text\">暂无表彰记录</text>\n                <text class=\"empty-desc\">点击\"添加表彰\"开始创建</text>\n            </view>\n            </template>\n          </scroll-view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 精选管理模块 -->\n    <view v-if=\"activeModule === 'featured'\" class=\"module-modal\" @click=\"closeModule\">\n      <view class=\"modal-content\" @click.stop>\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">精选管理</text>\n          <view class=\"close-btn\" @click=\"closeModule\">\n            <uni-icons type=\"close\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n          </view>\n        </view>\n        \n        <view class=\"modal-body\">\n          <view class=\"featured-actions\">\n            <view class=\"featured-action-row\">\n            <button \n                class=\"action-btn featured-btn\" \n              @click=\"batchSetFeatured(true)\"\n              :disabled=\"selectedFeaturedIds.length === 0\"\n            >\n                <uni-icons type=\"star-filled\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n                <text class=\"btn-text\">设为精选</text>\n                <text class=\"btn-count\">({{ selectedFeaturedIds.length }})</text>\n            </button>\n            <button \n                class=\"action-btn unfeatured-btn\" \n                :class=\"{ 'has-items': selectedFeaturedIds.length > 0 }\"\n              @click=\"batchSetFeatured(false)\"\n              :disabled=\"selectedFeaturedIds.length === 0\"\n            >\n                <uni-icons \n                  type=\"close\" \n                  size=\"16\" \n                  :color=\"selectedFeaturedIds.length > 0 ? '#FFFFFF' : '#6b7280'\"\n                ></uni-icons>\n                <text class=\"btn-text\">取消精选</text>\n                <text class=\"btn-count\">({{ selectedFeaturedIds.length }})</text>\n            </button>\n            </view>\n            <button class=\"action-btn select-all-btn\" @click=\"selectAllFeatured\">\n              <uni-icons \n                :type=\"selectedFeaturedIds.length === featuredList.length && featuredList.length > 0 ? 'checkbox-filled' : 'checkbox'\" \n                size=\"16\" \n                color=\"#3a86ff\"\n              ></uni-icons>\n              <text>{{ selectedFeaturedIds.length === featuredList.length && featuredList.length > 0 ? '取消全选' : '全选' }}</text>\n            </button>\n          </view>\n          \n          <view class=\"search-area\">\n            <uni-easyinput \n              v-model=\"featuredSearch\" \n              placeholder=\"搜索精选记录...\"\n              prefixIcon=\"search\"\n              @confirm=\"searchFeatured\"\n              @clear=\"clearFeaturedSearch\"\n              clearable\n            ></uni-easyinput>\n          </view>\n          \n          <scroll-view \n            class=\"featured-list\" \n            scroll-y \n            style=\"max-height: 600rpx; -webkit-overflow-scrolling: touch;\"\n            @scrolltolower=\"loadMoreFeatured\"\n            :show-scrollbar=\"false\"\n            :enhanced=\"true\"\n          >\n            <template v-if=\"loadingStates.featured\">\n              <!-- 骨架屏 -->\n              <view class=\"featured-item skeleton-featured\" v-for=\"n in 5\" :key=\"n\">\n                <view class=\"skeleton-featured-checkbox\"></view>\n                <view class=\"skeleton-featured-avatar\"></view>\n                <view class=\"skeleton-featured-info\">\n                  <view class=\"skeleton-featured-name\"></view>\n                  <view class=\"skeleton-featured-type\"></view>\n                  <view class=\"skeleton-featured-time\"></view>\n                </view>\n                <view class=\"skeleton-featured-status\"></view>\n              </view>\n            </template>\n            \n            <template v-else>\n              <!-- 实际数据 -->\n            <view \n              class=\"featured-item\" \n              v-for=\"(record, index) in featuredList\" \n              :key=\"index\"\n              @click=\"toggleFeaturedSelection(record._id)\"\n            >\n              <view class=\"selection-checkbox\" :class=\"{ active: selectedFeaturedIds.includes(record._id) }\">\n                <uni-icons \n                  :type=\"selectedFeaturedIds.includes(record._id) ? 'checkbox-filled' : 'circle'\" \n                  size=\"20\" \n                  :color=\"selectedFeaturedIds.includes(record._id) ? '#3a86ff' : '#c4c4c4'\"\n                ></uni-icons>\n              </view>\n              <image :src=\"record.userAvatar || '/static/user/default-avatar.png'\" \n                     class=\"featured-avatar\"\n                     mode=\"aspectFill\"></image>\n              <view class=\"featured-info\">\n                <text class=\"featured-name\">{{ record.userName }}</text>\n                <text class=\"featured-type\">{{ record.honorTypeName }}</text>\n                <text class=\"featured-time\">{{ formatDate(record.createTime) }}</text>\n              </view>\n              <view class=\"featured-status\">\n                <view v-if=\"record.isFeatured\" class=\"status-badge featured\">\n                  <uni-icons type=\"star-filled\" size=\"12\" color=\"#f59e0b\"></uni-icons>\n                  <text>精选</text>\n                </view>\n                <view v-else class=\"status-badge normal\">\n                  <text>普通</text>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 加载更多 -->\n            <view v-if=\"featuredList.length > 0 && featuredList.length < featuredTotal\" class=\"load-more\">\n              <view v-if=\"featuredLoading\" class=\"loading-item\">\n                <uni-icons type=\"spinner-cycle\" size=\"16\" color=\"#3a86ff\" class=\"loading-spin\"></uni-icons>\n                <text>加载中...</text>\n              </view>\n              <view v-else class=\"load-more-btn\" @click=\"loadMoreFeatured\">\n                <text>加载更多 ({{ featuredList.length }}/{{ featuredTotal }})</text>\n              </view>\n            </view>\n            \n            <!-- 空状态 -->\n            <view v-if=\"featuredList.length === 0 && !featuredLoading\" class=\"empty-state\">\n              <uni-icons type=\"info\" size=\"40\" color=\"#C4C4C4\"></uni-icons>\n              <text class=\"empty-text\">暂无精选数据</text>\n            </view>\n            </template>\n          </scroll-view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-overlay\">\n      <view class=\"loading-content\">\n        <uni-icons type=\"spinner-cycle\" size=\"32\" color=\"#3a86ff\" class=\"loading-spin\"></uni-icons>\n        <text class=\"loading-text\">{{ loadingText }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'HonorAdmin',\n  data() {\n    return {\n      loading: false,\n      refreshing: false,\n      loadingText: '加载中...',\n      activeModule: null,\n      \n      // 统计数据\n      stats: {\n        totalHonors: 0,\n        monthlyHonors: 0,\n        featuredHonors: 0,\n        activeTypes: 0\n      },\n      \n      // 概览卡片\n      overviewCards: [\n        {\n          label: '总表彰数',\n          value: 0,\n          icon: 'medal-filled',\n          color: '#3a86ff'\n        },\n        {\n          label: '本月新增',\n          value: 0,\n          icon: 'calendar-filled',\n          color: '#10b981'\n        },\n        {\n          label: '精选表彰',\n          value: 0,\n          icon: 'star-filled',\n          color: '#f59e0b'\n        },\n        {\n          label: '荣誉类型',\n          value: 0,\n          icon: 'gear-filled',\n          color: '#8b5cf6'\n        }\n      ],\n      \n      // 功能模块\n      functionModules: [\n        {\n          id: 'records',\n          name: '表彰记录',\n          description: '添加、编辑、管理表彰记录',\n          icon: 'medal-filled',\n          color: '#3a86ff',\n          count: '加载中...'\n        },\n        {\n          id: 'batch',\n          name: '智能批次',\n          description: '表彰批次与周期管理',\n          icon: 'calendar-filled',\n          color: '#10b981',\n          count: '加载中...'\n        },\n        {\n          id: 'featured',\n          name: '精选管理',\n          description: '批量设置精选表彰',\n          icon: 'star-filled',\n          color: '#f59e0b',\n          count: '加载中...'\n        },\n        {\n          id: 'types',\n          name: '荣誉类型',\n          description: '表彰类型配置管理',\n          icon: 'gear-filled',\n          color: '#8b5cf6',\n          count: '加载中...'\n        }\n      ],\n      \n      // 最近操作\n      recentActivities: [],\n      \n      // 表彰记录相关\n      recordSearch: '',\n      recordList: [],\n      recordPage: 1,\n      recordSize: 10,\n      recordTotal: 0,\n      recordLoading: false,\n      \n      // 精选管理相关\n      featuredList: [],\n      selectedFeaturedIds: [],\n      featuredPage: 1,\n      featuredSize: 15,\n      featuredTotal: 0,\n      featuredLoading: false,\n      featuredSearch: '',\n      \n      // 滚动控制\n      scrollDisabled: false,\n      scrollTop: 0,\n      \n      // 加载状态\n      loadingStates: {\n        stats: true,\n        modules: true,\n        activities: false,\n        records: true,\n        featured: true\n      },\n      \n      // 缓存管理\n      cacheData: {\n        stats: null,\n        statsTime: 0,\n        activities: null,\n        activitiesTime: 0\n      },\n      \n      // 缓存有效期（5分钟）\n      cacheExpiry: 5 * 60 * 1000\n    }\n  },\n  \n  async onLoad() {\n    await this.checkPermission()\n    await this.initializeData()\n  },\n  \n  methods: {\n      // 权限检查 - Token优先，数据库兜底\n      async checkPermission() {\n        try {\n          // 检查用户是否登录\n          const token = uni.getStorageSync('uni_id_token')\n          if (!token) {\n            uni.showModal({\n              title: '权限不足',\n              content: '请先登录系统',\n              showCancel: false,\n              success: () => {\n                uni.navigateBack()\n              }\n            })\n            return false\n          }\n          \n          const allowedRoles = ['admin', 'supervisor', 'PM', 'GM', 'reviser'];\n          \n          // 方法1：优先使用Token检查（快速）\n          const tokenHasPermission = allowedRoles.some(role => this.uniIDHasRole(role));\n          if (tokenHasPermission) {\n            return true; // Token验证通过，直接返回\n          }\n          \n          // 方法2：Token验证失败，使用数据库查询（兜底，确保准确性）\n          const db = uniCloud.database();\n          const { result } = await db.collection('uni-id-users')\n            .where(\"'_id' == $cloudEnv_uid\")\n            .field('role')\n            .get();\n          \n          if (!result.data || result.data.length === 0) {\n            uni.showModal({\n              title: '权限不足',\n              content: '无法获取用户角色信息',\n              showCancel: false,\n              success: () => {\n                uni.navigateBack()\n              }\n            })\n            return false\n          }\n          \n          const userRole = result.data[0].role || [];\n          const dbHasPermission = userRole.some(role => allowedRoles.includes(role));\n          \n          if (!dbHasPermission) {\n            uni.showModal({\n              title: '权限不足',\n              content: `需要管理员权限才能访问此页面`,\n              showCancel: false,\n              success: () => {\n                uni.navigateBack()\n              }\n            })\n            return false\n          }\n          \n          return true\n        } catch (error) {\n          console.error('权限检查异常:', error)\n          uni.showModal({\n            title: '权限检查失败',\n            content: '请重新登录后再试',\n            showCancel: false,\n            success: () => {\n              uni.navigateBack()\n            }\n          })\n          return false\n        }\n      },\n    \n    // 初始化数据\n    async initializeData() {\n      const startTime = Date.now()\n      \n      try {\n        // 并行加载不同模块的数据\n        await Promise.all([\n          this.loadStats(),\n          this.loadModuleCounts(),\n          this.loadRecentActivities()\n        ])\n        \n        const totalTime = Date.now() - startTime\n        this.recordLoadTime('initialization', totalTime)\n        \n        // 启动智能预加载\n        this.preloadData()\n        \n      } catch (error) {\n        uni.showToast({\n          title: '数据加载失败，请下拉刷新',\n          icon: 'none',\n          duration: 3000\n        })\n      }\n    },\n    \n    // 加载统计数据（带缓存）\n    async loadStats(forceRefresh = false) {\n      // 检查缓存\n      const now = Date.now()\n      if (!forceRefresh && this.cacheData.stats && (now - this.cacheData.statsTime < this.cacheExpiry)) {\n        this.updateStatsUI(this.cacheData.stats)\n        this.loadingStates.stats = false\n        return\n      }\n      \n      this.loadingStates.stats = true\n      try {\n        const startTime = Date.now()\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getStatistics'\n          }\n        })\n        \n        const loadTime = Date.now() - startTime\n        \n        if (res.result.code === 0) {\n          const data = res.result.data\n          \n          // 缓存数据\n          this.cacheData.stats = data\n          this.cacheData.statsTime = now\n          \n          this.updateStatsUI(data)\n          \n          // 显示加载成功提示（仅在较慢时显示）\n          if (loadTime > 1000) {\n            uni.showToast({\n              title: '数据已更新',\n              icon: 'success',\n              duration: 1000\n            })\n          }\n        } else {\n          throw new Error(res.result.message || '获取统计数据失败')\n        }\n      } catch (error) {\n        this.handleStatsError(error)\n      } finally {\n        this.loadingStates.stats = false\n      }\n    },\n    \n    // 更新统计UI\n    updateStatsUI(data) {\n          this.stats = {\n            totalHonors: data.totalHonors || 0,\n            monthlyHonors: data.currentMonthHonors || 0,\n            featuredHonors: data.featuredHonors || 0,\n            activeTypes: data.activeTypes || 0\n          }\n          \n          // 更新概览卡片数据\n          this.overviewCards[0].value = this.stats.totalHonors\n          this.overviewCards[1].value = this.stats.monthlyHonors\n          this.overviewCards[2].value = this.stats.featuredHonors\n          this.overviewCards[3].value = this.stats.activeTypes\n          \n      // 计算趋势（模拟数据，实际可从云函数获取）\n      this.overviewCards[0].trend = this.calculateTrend(data.totalHonors, data.lastMonthTotal)\n      this.overviewCards[1].trend = this.calculateTrend(data.currentMonthHonors, data.lastMonthHonors)\n      this.overviewCards[2].trend = this.calculateTrend(data.featuredHonors, data.lastMonthFeatured)\n      this.overviewCards[3].trend = this.calculateTrend(data.activeTypes, data.lastMonthTypes)\n    },\n    \n    // 处理统计数据错误\n    handleStatsError(error) {\n      // 显示友好的错误信息\n      this.overviewCards.forEach((card, index) => {\n        card.value = '---'\n        card.trend = null\n      })\n      \n      // 根据错误类型显示不同提示\n      if (error.message && error.message.includes('网络')) {\n        uni.showToast({\n          title: '网络连接异常',\n          icon: 'none',\n          duration: 2000\n        })\n      } else {\n        uni.showToast({\n          title: '数据加载失败',\n          icon: 'none',\n          duration: 2000\n        })\n      }\n    },\n    \n    // 加载模块计数\n    async loadModuleCounts() {\n      this.loadingStates.modules = true\n      try {\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getStatistics'\n          }\n        })\n        \n        if (res.result.code === 0) {\n          const data = res.result.data\n          // 更新功能模块计数\n          this.functionModules[0].count = `${data.totalHonors || 0} 条记录`\n          this.functionModules[1].count = `${data.totalBatches || 0} 个批次`\n          this.functionModules[2].count = `${data.featuredHonors || 0} 条精选`\n          this.functionModules[3].count = `${data.activeTypes || 0} 种类型`\n        }\n      } catch (error) {\n        this.functionModules.forEach(module => {\n          module.count = '加载失败'\n        })\n      } finally {\n        this.loadingStates.modules = false\n      }\n    },\n    \n    // 计算趋势百分比\n    calculateTrend(current, previous) {\n      if (!previous || previous === 0) return null\n      return Math.round(((current - previous) / previous) * 100)\n    },\n    \n    // 智能预加载（在用户可能需要时提前加载）\n    async preloadData() {\n      // 检查网络状态\n      const networkType = await this.getNetworkType()\n      if (networkType === 'none') return\n      \n      // 在WiFi或4G网络下预加载\n      if (networkType === 'wifi' || networkType === '4g') {\n        // 预加载记录列表（小批量）\n        setTimeout(() => {\n          if (!this.recordList.length) {\n            this.loadRecordList()\n          }\n        }, 2000)\n      }\n    },\n    \n    // 获取网络类型\n    async getNetworkType() {\n      return new Promise((resolve) => {\n        uni.getNetworkType({\n          success: (res) => resolve(res.networkType),\n          fail: () => resolve('unknown')\n        })\n      })\n    },\n    \n    // 性能监控 - 记录加载时间\n    recordLoadTime(module, time) {\n      const performance = uni.getStorageSync('admin_performance') || {}\n      if (!performance[module]) performance[module] = []\n      \n      performance[module].push({\n        time,\n        timestamp: Date.now()\n      })\n      \n      // 只保留最近10次记录\n      if (performance[module].length > 10) {\n        performance[module] = performance[module].slice(-10)\n      }\n      \n      uni.setStorageSync('admin_performance', performance)\n      \n      // 计算平均加载时间\n      const avgTime = performance[module].reduce((sum, item) => sum + item.time, 0) / performance[module].length\n    },\n    \n    // 检查数据新鲜度\n    isDataFresh(cacheTime) {\n      return Date.now() - cacheTime < this.cacheExpiry\n    },\n    \n    // 错误重试机制\n    async retryOperation(operation, maxRetries = 3, delay = 1000) {\n      for (let i = 0; i < maxRetries; i++) {\n        try {\n          return await operation()\n        } catch (error) {\n          if (i === maxRetries - 1) throw error\n          await this.sleep(delay * (i + 1)) // 指数退避\n        }\n      }\n    },\n    \n    // 延迟工具\n    sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms))\n    },\n    \n    // 加载最近操作（带缓存）\n    async loadRecentActivities(forceRefresh = false) {\n      // 检查缓存\n      const now = Date.now()\n      if (!forceRefresh && this.cacheData.activities && (now - this.cacheData.activitiesTime < this.cacheExpiry)) {\n        this.recentActivities = this.cacheData.activities\n        this.loadingStates.activities = false\n        return\n      }\n      \n      this.loadingStates.activities = true\n      try {\n        const startTime = Date.now()\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getRecentActivities',\n            data: {\n              limit: 10  // 减少显示数量，避免列表过长\n            }\n          }\n        })\n        \n        const loadTime = Date.now() - startTime\n        \n        if (res.result.code === 0) {\n          // 处理数据\n          const activities = (res.result.data || []).map(item => ({\n            ...item,\n            // 确保有必要的显示字段\n            icon: this.getOperationIcon(item.title || item.action),\n            color: this.getOperationColor(item.title || item.action)\n          }))\n          \n          // 缓存数据\n          this.cacheData.activities = activities\n          this.cacheData.activitiesTime = now\n          \n          this.recentActivities = activities\n          \n          // 显示数据统计\n        } else {\n          throw new Error(res.result.message || '获取最近操作失败')\n        }\n      } catch (error) {\n        this.recentActivities = []\n        \n        // 显示错误提示\n        if (error.message && error.message.includes('网络')) {\n          uni.showToast({\n            title: '网络异常，请稍后重试',\n            icon: 'none',\n            duration: 2000\n          })\n        }\n      } finally {\n        this.loadingStates.activities = false\n      }\n    },\n    \n    // 刷新最近操作（强制刷新）\n    async refreshRecentActivities() {\n      if (this.loadingStates.activities) return // 防止重复点击\n      \n      try {\n        // 清除活动缓存\n        this.cacheData.activities = null\n        this.cacheData.activitiesTime = 0\n        \n        // 强制刷新\n        await this.loadRecentActivities(true)\n        \n        // 显示刷新成功提示\n        uni.showToast({\n          title: '最近操作已更新',\n          icon: 'success',\n          duration: 1500\n        })\n      } catch (error) {\n        uni.showToast({\n          title: '刷新失败',\n          icon: 'none',\n          duration: 2000\n        })\n      }\n    },\n    \n    // 根据操作类型获取图标\n    getOperationIcon(action) {\n      const iconMap = {\n        '添加表彰记录': 'plus-filled',\n        '创建表彰记录': 'plus-filled', \n        '编辑表彰记录': 'compose',\n        '删除表彰记录': 'trash-filled',\n        '发布批次': 'upload-filled',\n        '创建批次': 'calendar',\n        '删除批次': 'trash-filled',\n        '批量精选': 'star-filled',\n        '创建荣誉类型': 'gear-filled',\n        '智能创建': 'gear-filled'\n      }\n      \n      // 模糊匹配\n      for (const key in iconMap) {\n        if (action && action.includes(key.replace(/表彰|记录/g, ''))) {\n          return iconMap[key]\n        }\n      }\n      \n      return 'plus-filled' // 默认图标\n    },\n    \n    // 根据操作类型获取颜色\n    getOperationColor(action) {\n      const colorMap = {\n        '添加': '#10b981',\n        '创建': '#10b981',\n        '编辑': '#f59e0b', \n        '删除': '#ef4444',\n        '发布': '#3a86ff',\n        '批量': '#8b5cf6',\n        '智能': '#06b6d4'\n      }\n      \n      // 模糊匹配\n      for (const key in colorMap) {\n        if (action && action.includes(key)) {\n          return colorMap[key]\n        }\n      }\n      \n      return '#10b981' // 默认绿色\n    },\n    \n    // 下拉刷新（强制刷新缓存）\n    async onRefresh() {\n      this.refreshing = true\n      try {\n        // 清除缓存，强制刷新\n        this.clearCache()\n        \n        const startTime = Date.now()\n        await Promise.all([\n          this.loadStats(true),\n          this.loadModuleCounts(),\n          this.loadRecentActivities(true)\n        ])\n        \n        const refreshTime = Date.now() - startTime\n        \n        uni.showToast({\n          title: '刷新成功',\n          icon: 'success',\n          duration: 1500\n        })\n      } catch (error) {\n        uni.showToast({\n          title: '刷新失败，请稍后重试',\n          icon: 'none',\n          duration: 2000\n        })\n      } finally {\n        this.refreshing = false\n      }\n    },\n    \n    // 清除缓存\n    clearCache() {\n      this.cacheData = {\n        stats: null,\n        statsTime: 0,\n        activities: null,\n        activitiesTime: 0\n      }\n    },\n    \n    // 打开功能模块\n    async openModule(module) {\n      // 直接导航到对应的专门页面\n      switch (module.id) {\n        case 'records':\n          this.activeModule = module.id\n          // 阻止背景滚动\n          this.preventScroll()\n          await this.loadRecordList()\n          break\n        case 'batch':\n          uni.navigateTo({\n            url: '/pages/honor_pkg/admin/batch-manager'\n          })\n          break\n        case 'featured':\n          this.activeModule = module.id\n          // 阻止背景滚动\n          this.preventScroll()\n          await this.loadFeaturedList()\n          break\n        case 'types':\n          uni.navigateTo({\n            url: '/pages/honor_pkg/admin/type-manager'\n          })\n          break\n      }\n    },\n    \n    // 关闭模块\n    closeModule() {\n      this.activeModule = null\n      // 恢复背景滚动\n      this.allowScroll()\n    },\n    \n    // 加载表彰记录列表\n    async loadRecordList(isLoadMore = false) {\n      if (this.recordLoading) return\n      \n      // 首次加载显示骨架屏，加载更多显示小loading\n      if (!isLoadMore) {\n        this.loadingStates.records = true\n      }\n      this.recordLoading = true\n      try {\n        const page = isLoadMore ? this.recordPage + 1 : 1\n        \n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getHonorList',\n            data: {\n              page: page,\n              size: this.recordSize,\n              orderBy: 'createTime',\n              orderDirection: 'desc'\n            }\n          }\n        })\n        \n        if (res.result.code === 0) {\n          const data = res.result.data\n          if (isLoadMore) {\n            this.recordList = [...this.recordList, ...(data.list || [])]\n            this.recordPage = page\n          } else {\n            this.recordList = data.list || []\n            this.recordPage = 1\n          }\n          this.recordTotal = data.total || 0\n        }\n      } catch (error) {\n        // 加载记录列表失败\n      } finally {\n        this.recordLoading = false\n        if (!isLoadMore) {\n          this.loadingStates.records = false\n        }\n      }\n    },\n    \n    // 其他方法占位\n    loadBatchList() {\n      // 加载批次列表\n    },\n    \n    async loadFeaturedList(isLoadMore = false) {\n      if (this.featuredLoading) return\n      \n      this.featuredLoading = true\n      try {\n        const page = isLoadMore ? this.featuredPage + 1 : 1\n        \n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getHonorList',\n            data: {\n              page: page,\n              size: this.featuredSize,\n              orderBy: 'createTime',\n              orderDirection: 'desc',\n              search: this.featuredSearch.trim()\n            }\n          }\n        })\n        \n        if (res.result.code === 0) {\n          const data = res.result.data\n          if (isLoadMore) {\n            this.featuredList = [...this.featuredList, ...(data.list || [])]\n            this.featuredPage = page\n          } else {\n            this.featuredList = data.list || []\n            this.featuredPage = 1\n          this.selectedFeaturedIds = []\n          }\n          this.featuredTotal = data.total || 0\n        }\n      } catch (error) {\n        // 加载精选列表失败\n      } finally {\n        this.featuredLoading = false\n        this.loadingStates.featured = false\n      }\n    },\n    \n    loadTypeList() {\n      // 加载类型列表\n    },\n    \n    // 操作方法\n    openAddRecord() {\n      // 打开添加表彰记录页面\n      uni.navigateTo({\n        url: '/pages/honor_pkg/admin/add-record'\n      })\n    },\n    \n    openBatchImport() {\n      // 打开批量导入页面\n      uni.showToast({\n        title: '批量导入功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    openBatchFeatured() {\n      // 打开批量精选功能\n      this.activeModule = 'featured'\n      this.loadingStates.featured = true\n      this.loadFeaturedList()\n    },\n    \n    editRecord(record) {\n      // 编辑记录 - 导航到编辑页面\n      const recordData = encodeURIComponent(JSON.stringify({\n        id: record._id,\n        userName: record.userName,\n        department: record.department,\n        userAvatar: record.userAvatar,\n        honorTypeId: record.honorTypeId,\n        batchId: record.batchId,\n        reason: record.reason,\n        isFeatured: record.isFeatured,\n        images: record.images || []\n      }))\n      \n      uni.navigateTo({\n        url: `/pages/honor_pkg/admin/add-record?mode=edit&data=${recordData}`\n      })\n    },\n    \n    deleteRecord(record) {\n      // 删除记录\n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除 ${record.userName} 的表彰记录吗？`,\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              await uniCloud.callFunction({\n                name: 'honor-admin',\n                data: {\n                  action: 'deleteHonor',\n                  data: { honorId: record._id }\n                }\n              })\n              \n              uni.showToast({\n                title: '删除成功',\n                icon: 'success'\n              })\n              \n              this.loadRecordList()\n            } catch (error) {\n              uni.showToast({\n                title: '删除失败',\n                icon: 'none'\n              })\n            }\n          }\n        }\n      })\n    },\n    \n    // 搜索和筛选\n    async searchRecords() {\n      if (!this.recordSearch.trim()) {\n        await this.loadRecordList()\n        return\n      }\n      \n      try {\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getHonorList',\n            data: {\n              page: 1,\n              size: 50,\n              orderBy: 'createTime',\n              orderDirection: 'desc',\n              search: this.recordSearch.trim()\n            }\n          }\n        })\n        \n        if (res.result.code === 0) {\n          this.recordList = res.result.data.list || []\n          \n          if (this.recordList.length === 0) {\n            uni.showToast({\n              title: '未找到匹配记录',\n              icon: 'none'\n            })\n          }\n        }\n      } catch (error) {\n        uni.showToast({\n          title: '搜索失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    clearSearch() {\n      this.recordSearch = ''\n      this.loadRecordList()\n    },\n    \n    // 加载更多记录\n    loadMoreRecords() {\n      if (this.recordList.length < this.recordTotal && !this.recordLoading) {\n        this.loadRecordList(true)\n      }\n    },\n    \n    // 精选管理方法\n    toggleFeaturedSelection(recordId) {\n      const index = this.selectedFeaturedIds.indexOf(recordId)\n      if (index > -1) {\n        this.selectedFeaturedIds.splice(index, 1)\n      } else {\n        this.selectedFeaturedIds.push(recordId)\n      }\n    },\n    \n    selectAllFeatured() {\n      if (this.selectedFeaturedIds.length === this.featuredList.length) {\n        this.selectedFeaturedIds = []\n      } else {\n        this.selectedFeaturedIds = this.featuredList.map(item => item._id)\n      }\n    },\n    \n    async batchSetFeatured(isFeatured) {\n      if (this.selectedFeaturedIds.length === 0) {\n        uni.showToast({\n          title: '请先选择记录',\n          icon: 'none'\n        })\n        return\n      }\n      \n      const actionText = isFeatured ? '设为精选' : '取消精选'\n      \n      uni.showModal({\n        title: '批量操作确认',\n        content: `确定要将 ${this.selectedFeaturedIds.length} 条记录${actionText}吗？`,\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const res = await uniCloud.callFunction({\n                name: 'honor-admin',\n                data: {\n                  action: 'batchSetFeatured',\n                  data: {\n                    honorIds: this.selectedFeaturedIds,\n                    isFeatured: isFeatured\n                  }\n                }\n              })\n              \n              if (res.result.code === 0) {\n                uni.showToast({\n                  title: `${actionText}成功`,\n                  icon: 'success'\n                })\n                \n                // 重新加载列表\n                this.loadingStates.featured = true\n                await this.loadFeaturedList()\n                await this.loadStats()\n              } else {\n                throw new Error(res.result.message || `${actionText}失败`)\n              }\n            } catch (error) {\n              uni.showToast({\n                title: error.message || `${actionText}失败`,\n                icon: 'none'\n              })\n            }\n          }\n        }\n      })\n    },\n    \n    // 工具方法\n    goBack() {\n      uni.navigateBack({\n        fail: () => {\n          uni.redirectTo({\n            url: '/pages/honor_pkg/gallery/index'\n          })\n        }\n      })\n    },\n    \n    formatTime(time) {\n      try {\n        const date = new Date(time)\n        const now = new Date()\n        const diff = now - date\n        \n        if (diff < 60000) {\n          return '刚刚'\n        } else if (diff < 3600000) {\n          return `${Math.floor(diff / 60000)}分钟前`\n        } else if (diff < 86400000) {\n          return `${Math.floor(diff / 3600000)}小时前`\n        } else {\n          return `${Math.floor(diff / 86400000)}天前`\n        }\n      } catch (error) {\n        return '时间未知'\n      }\n    },\n    \n    // 阻止背景滚动 - 针对H5平台\n    preventScroll() {\n      // #ifdef H5\n      // 获取当前滚动位置\n      this.scrollTop = document.documentElement.scrollTop || document.body.scrollTop || 0\n      \n      // 设置页面固定位置\n      const body = document.body\n      body.style.position = 'fixed'\n      body.style.top = `-${this.scrollTop}px`\n      body.style.width = '100%'\n      body.style.overflow = 'hidden'\n      // #endif\n      \n      // #ifdef MP-WEIXIN\n      // 小程序中通过设置页面样式类来控制\n      this.scrollDisabled = true\n      // #endif\n    },\n    \n    // 恢复背景滚动\n    allowScroll() {\n      // #ifdef H5\n      // 恢复页面滚动\n      const body = document.body\n      body.style.position = ''\n      body.style.top = ''\n      body.style.width = ''\n      body.style.overflow = ''\n      \n      // 恢复滚动位置\n      if (this.scrollTop !== undefined) {\n        window.scrollTo(0, this.scrollTop)\n        this.scrollTop = undefined\n      }\n      // #endif\n      \n      // #ifdef MP-WEIXIN\n      // 小程序中恢复滚动\n      this.scrollDisabled = false\n      // #endif\n    },\n    \n    formatDate(date) {\n      try {\n        const d = new Date(date)\n        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`\n      } catch (error) {\n        return '日期未知'\n      }\n    },\n    \n    // 加载更多精选\n    loadMoreFeatured() {\n      if (this.featuredList.length < this.featuredTotal && !this.featuredLoading) {\n        this.loadFeaturedList(true)\n      }\n    },\n    \n    // 搜索精选记录\n    searchFeatured() {\n      this.loadingStates.featured = true\n      this.loadFeaturedList()\n    },\n    \n    // 清除精选搜索\n    clearFeaturedSearch() {\n      this.featuredSearch = ''\n      this.loadingStates.featured = true\n      this.loadFeaturedList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.admin-container {\n  min-height: 100vh;\n  background: linear-gradient(145deg, #f8faff 0%, #e9f0f8 100%);\n  position: relative;\n  \n  // 小程序中的滚动控制\n  &.scroll-disabled {\n    /* #ifdef MP-WEIXIN */\n    position: fixed;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    /* #endif */\n  }\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  background: linear-gradient(180deg, #3a86ff 0%, #2563eb 100%);\n  overflow: hidden;\n  \n  // 装饰性背景图案\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    right: -20%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);\n    transform: rotate(-15deg);\n    pointer-events: none;\n  }\n  \n  .header-content {\n    display: flex;\n    align-items: center;\n    padding: 20rpx 40rpx;\n    padding-top: calc(var(--status-bar-height, 0px) + 20rpx);\n    position: relative;\n    z-index: 2;\n    \n    .back-btn {\n      width: 60rpx;\n      height: 60rpx;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.2);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 30rpx;\n    }\n    \n    .title-area {\n      flex: 1;\n      \n      .title {\n        display: block;\n        font-size: 36rpx;\n        font-weight: 600;\n        color: #FFFFFF;\n        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n        line-height: 1.2;\n      }\n      \n      .subtitle {\n        display: block;\n        font-size: 24rpx;\n        color: rgba(255, 255, 255, 0.8);\n        margin-top: 4rpx;\n      }\n    }\n    \n    .header-stats {\n      .stat-text {\n        font-size: 24rpx;\n        color: #FFFFFF;\n        font-weight: 500;\n        opacity: 0.9;\n      }\n    }\n  }\n}\n\n.content-scroll {\n  position: fixed;\n  top: calc(var(--status-bar-height, 0px) + 140rpx);\n  left: 0;\n  right: 0;\n  bottom: 0;\n  padding-bottom: 40rpx;\n}\n\n.overview-cards {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 24rpx;\n  padding: 40rpx;\n  \n  .card-item {\n    background: rgba(255, 255, 255, 0.95);\n    border-radius: 24rpx;\n    padding: 32rpx;\n    display: flex;\n    align-items: center;\n    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n    backdrop-filter: blur(10px);\n    \n    .card-icon {\n      width: 60rpx;\n      height: 60rpx;\n      border-radius: 16rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 24rpx;\n    }\n    \n    .card-info {\n      flex: 1;\n      \n      .card-number {\n        display: block;\n        font-size: 48rpx;\n        font-weight: 700;\n        color: #1a1a1a;\n        line-height: 1;\n      }\n      \n      .card-label {\n        display: block;\n        font-size: 24rpx;\n        color: #8a94a6;\n        margin-top: 8rpx;\n      }\n    }\n    \n    .card-trend {\n      .trend-text {\n        font-size: 20rpx;\n        font-weight: 600;\n        padding: 4rpx 12rpx;\n        border-radius: 12rpx;\n        \n        &.trend-up {\n          background: rgba(16, 185, 129, 0.1);\n          color: #10b981;\n        }\n        \n        &.trend-down {\n          background: rgba(239, 68, 68, 0.1);\n          color: #ef4444;\n        }\n      }\n    }\n  }\n}\n\n.function-modules {\n  margin: 0 40rpx 40rpx;\n  \n  .module-header {\n    margin-bottom: 24rpx;\n    \n    .header-container {\n      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);\n      border: 1px solid rgba(226, 232, 240, 0.6);\n      border-radius: 16rpx;\n      padding: 20rpx 24rpx;\n      backdrop-filter: blur(10px);\n      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n      \n      .title-with-icon {\n        display: flex;\n        align-items: center;\n        gap: 12rpx;\n        \n        .icon-wrapper {\n          width: 48rpx;\n          height: 48rpx;\n          border-radius: 12rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\n          \n          &.gear-icon {\n            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);\n          }\n          \n          &.calendar-icon {\n            background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);\n          }\n        }\n        \n        .module-title {\n          font-size: 32rpx;\n          font-weight: 600;\n          color: #1a1a1a;\n        }\n      }\n    }\n  }\n  \n  .module-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 16rpx;\n    \n    .module-item {\n      background: rgba(255, 255, 255, 0.95);\n      border-radius: 20rpx;\n      padding: 32rpx;\n      display: flex;\n      align-items: center;\n      backdrop-filter: blur(10px);\n      transition: all 0.3s ease;\n      \n      &:active {\n        transform: scale(0.98);\n        background: rgba(255, 255, 255, 0.9);\n      }\n      \n      .module-icon {\n        width: 80rpx;\n        height: 80rpx;\n        border-radius: 20rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 32rpx;\n      }\n      \n      .module-content {\n        flex: 1;\n        \n        .module-name {\n          display: block;\n          font-size: 32rpx;\n          font-weight: 600;\n          color: #1a1a1a;\n          line-height: 1.2;\n        }\n        \n        .module-desc {\n          display: block;\n          font-size: 24rpx;\n          color: #8a94a6;\n          margin: 8rpx 0;\n        }\n        \n        .module-count {\n          display: block;\n          font-size: 22rpx;\n          color: #3a86ff;\n          font-weight: 500;\n        }\n      }\n      \n      .module-arrow {\n        margin-left: 16rpx;\n      }\n    }\n  }\n}\n\n.recent-activities {\n  margin: 0 40rpx 40rpx;\n  \n  .section-header {\n    margin-bottom: 24rpx;\n    \n    .header-container {\n      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);\n      border: 1px solid rgba(226, 232, 240, 0.6);\n      border-radius: 16rpx;\n      padding: 20rpx 24rpx;\n      backdrop-filter: blur(10px);\n      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      \n      .title-with-icon {\n        display: flex;\n        align-items: center;\n        gap: 12rpx;\n        \n        .icon-wrapper {\n          width: 48rpx;\n          height: 48rpx;\n          border-radius: 12rpx;\n          background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\n        }\n        \n        .section-title {\n          font-size: 32rpx;\n          font-weight: 600;\n          color: #1a1a1a;\n        }\n      }\n      \n      .header-action {\n        width: 48rpx;\n        height: 48rpx;\n        border-radius: 50%;\n        background: linear-gradient(135deg, #3a86ff 0%, #06b6d4 100%);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.2s ease;\n        box-shadow: 0 2rpx 8rpx rgba(58, 134, 255, 0.2);\n        \n        &:active {\n          transform: scale(0.95);\n          box-shadow: 0 1rpx 4rpx rgba(58, 134, 255, 0.3);\n        }\n      }\n    }\n  }\n  \n  .activity-list {\n    background: rgba(255, 255, 255, 0.95);\n    border-radius: 20rpx;\n    padding: 24rpx;\n    backdrop-filter: blur(10px);\n    \n    .activity-item {\n      display: flex;\n      align-items: flex-start;\n      padding: 24rpx 0;\n      border-bottom: 1px solid rgba(138, 148, 166, 0.1);\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .activity-icon {\n        width: 48rpx;\n        height: 48rpx;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 24rpx;\n        margin-top: 4rpx;\n      }\n      \n      .activity-content {\n        flex: 1;\n        \n        .activity-title {\n          display: block;\n          font-size: 28rpx;\n          font-weight: 600;\n          color: #1a1a1a;\n          line-height: 1.3;\n        }\n        \n        .activity-desc {\n          display: block;\n          font-size: 24rpx;\n          color: #8a94a6;\n          margin: 8rpx 0;\n          line-height: 1.4;\n        }\n        \n        .activity-time {\n          display: block;\n          font-size: 22rpx;\n          color: #c4c4c4;\n        }\n      }\n    }\n    \n    .empty-activities {\n      text-align: center;\n      padding: 80rpx 0;\n      \n      .empty-text {\n        display: block;\n        font-size: 28rpx;\n        color: #c4c4c4;\n        margin-top: 16rpx;\n      }\n    }\n  }\n}\n\n.module-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 500;\n  display: flex;\n  align-items: flex-end;\n  \n  .modal-content {\n    width: 100%;\n    max-height: 90vh;\n    background: #FFFFFF;\n    border-radius: 40rpx 40rpx 0 0;\n    \n    .modal-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 40rpx;\n      border-bottom: 1px solid #f0f0f0;\n      \n      .modal-title {\n        font-size: 36rpx;\n        font-weight: 600;\n        color: #1a1a1a;\n      }\n      \n      .close-btn {\n        width: 60rpx;\n        height: 60rpx;\n        border-radius: 50%;\n        background: #f5f5f5;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n    }\n    \n    .modal-body {\n      padding: 40rpx;\n      max-height: calc(90vh - 120rpx);\n      // 移除外层滚动，完全由scroll-view处理\n      // overflow-y: auto;\n      \n      .quick-actions {\n        display: flex;\n        gap: 12rpx;\n        margin-bottom: 32rpx;\n        \n        .action-btn {\n          flex: 1;\n          height: 84rpx;\n          border-radius: 12rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 8rpx;\n          font-size: 26rpx;\n          font-weight: 500;\n          border: none;\n          transition: all 0.2s ease;\n          \n          // 扁平化立体阴影\n          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);\n          \n          &:active {\n            transform: translateY(1rpx);\n            box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);\n          }\n          \n          &.primary {\n            background: #3a86ff;\n            color: #FFFFFF;\n            \n            &:active {\n              background: #2563eb;\n            }\n          }\n          \n          &.secondary {\n            background: rgba(58, 134, 255, 0.1);\n            color: #3a86ff;\n            \n            &:active {\n              background: rgba(58, 134, 255, 0.15);\n            }\n          }\n        }\n      }\n      \n      .search-area {\n        margin-bottom: 32rpx;\n      }\n      \n      .record-list {\n        // 强制隐藏scroll-view的滚动条\n        &::-webkit-scrollbar {\n          display: none !important;\n          width: 0 !important;\n          height: 0 !important;\n        }\n        scrollbar-width: none !important;\n        -ms-overflow-style: none !important;\n        \n        .record-item {\n          display: flex;\n          align-items: center;\n          padding: 24rpx 0;\n          border-bottom: 1px solid #f0f0f0;\n          \n          .record-avatar {\n            width: 80rpx;\n            height: 80rpx;\n            border-radius: 50%;\n            margin-right: 24rpx;\n            object-fit: cover;\n          }\n          \n          .record-info {\n            flex: 1;\n            \n            .record-name {\n              display: block;\n              font-size: 32rpx;\n              font-weight: 600;\n              color: #1a1a1a;\n            }\n            \n            .record-type {\n              display: block;\n              font-size: 24rpx;\n              color: #3a86ff;\n              margin: 4rpx 0;\n            }\n            \n            .record-time {\n              display: block;\n              font-size: 22rpx;\n              color: #8a94a6;\n            }\n          }\n          \n          .record-actions {\n            display: flex;\n            gap: 16rpx;\n            \n            .action-btn-mini {\n              width: 48rpx;\n              height: 48rpx;\n              border-radius: 50%;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n              \n              &.edit {\n                background: rgba(58, 134, 255, 0.1);\n              }\n              \n              &.delete {\n                background: rgba(239, 68, 68, 0.1);\n              }\n            }\n          }\n        }\n        \n        .load-more {\n          padding: 20rpx 0;\n          text-align: center;\n          \n          .loading-item {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: 8rpx;\n            color: #8a94a6;\n            font-size: 24rpx;\n          }\n          \n          .load-more-btn {\n            color: #3a86ff;\n            font-size: 24rpx;\n            padding: 16rpx 32rpx;\n            border: 1px solid #3a86ff;\n            border-radius: 20rpx;\n            background: rgba(58, 134, 255, 0.05);\n            display: inline-block;\n            \n            &:active {\n              background: rgba(58, 134, 255, 0.1);\n            }\n          }\n        }\n        \n        .empty-state {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          padding: 80rpx 40rpx;\n          \n          .empty-text {\n            margin-top: 20rpx;\n            font-size: 28rpx;\n            color: #8a94a6;\n          }\n        }\n      }\n      \n      // 精选管理样式\n      .featured-actions {\n        margin-bottom: 32rpx;\n        \n        .featured-action-row {\n          display: flex;\n          gap: 12rpx;\n          margin-bottom: 16rpx;\n        }\n        \n        .action-btn {\n          height: 84rpx;\n          border-radius: 12rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 8rpx;\n          font-size: 26rpx;\n          font-weight: 500;\n          border: none;\n          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\n          \n          // 扁平化立体阴影\n          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);\n          \n          &:active {\n            transform: translateY(1rpx);\n            box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);\n          }\n          \n          &:disabled {\n            opacity: 0.5;\n            background: #e5e7eb !important;\n            color: #9ca3af !important;\n            box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.08);\n            transform: none !important;\n          }\n          \n          // 设为精选按钮 - 蓝色主色调\n          &.featured-btn {\n            flex: 1;\n            background: #3a86ff;\n            color: #FFFFFF;\n            \n            &:active {\n              background: #2563eb;\n            }\n          }\n          \n          // 取消精选按钮 - 默认淡灰色，有选择时变红色\n          &.unfeatured-btn {\n            flex: 1;\n            background: #d1d5db;\n            color: #6b7280;\n            \n            &:active {\n              background: #9ca3af;\n            }\n            \n            // 有选择项时变为红色警告\n            &.has-items {\n              background: #dc2626;\n              color: #FFFFFF;\n              \n              &:active {\n                background: #b91c1c;\n              }\n            }\n          }\n          \n          // 全选按钮 - 浅色调\n          &.select-all-btn {\n            width: 100%;\n            background: rgba(58, 134, 255, 0.1);\n            color: #3a86ff;\n            font-size: 26rpx;\n            \n            &:active {\n              background: rgba(58, 134, 255, 0.15);\n            }\n          }\n          \n          .btn-text {\n            font-size: 26rpx;\n            font-weight: 500;\n          }\n          \n          .btn-count {\n            font-size: 20rpx;\n            opacity: 0.8;\n            font-weight: 400;\n            background: rgba(255, 255, 255, 0.25);\n            padding: 2rpx 6rpx;\n            border-radius: 8rpx;\n            margin-left: 4rpx;\n            line-height: 1.4;\n          }\n        }\n      }\n      \n      .featured-list {\n        // 强制隐藏scroll-view的滚动条\n        &::-webkit-scrollbar {\n          display: none !important;\n          width: 0 !important;\n          height: 0 !important;\n        }\n        scrollbar-width: none !important;\n        -ms-overflow-style: none !important;\n        \n        // 精选管理骨架屏样式\n        .skeleton-featured {\n          display: flex;\n          align-items: center;\n          padding: 24rpx 0;\n          border-bottom: 1px solid #f0f0f0;\n          \n          .skeleton-featured-checkbox {\n            width: 20rpx;\n            height: 20rpx;\n            border-radius: 50%;\n            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n            background-size: 200% 100%;\n            animation: shimmer 1.5s infinite;\n            margin-right: 20rpx;\n          }\n          \n          .skeleton-featured-avatar {\n            width: 80rpx;\n            height: 80rpx;\n            border-radius: 50%;\n            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n            background-size: 200% 100%;\n            animation: shimmer 1.5s infinite;\n            margin-right: 24rpx;\n          }\n          \n          .skeleton-featured-info {\n            flex: 1;\n            \n            .skeleton-featured-name {\n              width: 120rpx;\n              height: 32rpx;\n              border-radius: 4rpx;\n              background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n              background-size: 200% 100%;\n              animation: shimmer 1.5s infinite;\n              margin-bottom: 8rpx;\n            }\n            \n            .skeleton-featured-type {\n              width: 80rpx;\n              height: 24rpx;\n              border-radius: 4rpx;\n              background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n              background-size: 200% 100%;\n              animation: shimmer 1.5s infinite;\n              margin-bottom: 8rpx;\n            }\n            \n            .skeleton-featured-time {\n              width: 100rpx;\n              height: 22rpx;\n              border-radius: 4rpx;\n              background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n              background-size: 200% 100%;\n              animation: shimmer 1.5s infinite;\n            }\n          }\n          \n          .skeleton-featured-status {\n            width: 60rpx;\n            height: 36rpx;\n            border-radius: 18rpx;\n            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n            background-size: 200% 100%;\n            animation: shimmer 1.5s infinite;\n          }\n        }\n        \n        .featured-item {\n          display: flex;\n          align-items: center;\n          padding: 24rpx 0;\n          border-bottom: 1px solid #f0f0f0;\n          \n          .selection-checkbox {\n            margin-right: 20rpx;\n            \n            &.active {\n              color: #3a86ff;\n            }\n          }\n          \n          .featured-avatar {\n            width: 80rpx;\n            height: 80rpx;\n            border-radius: 50%;\n            margin-right: 24rpx;\n            object-fit: cover;\n          }\n          \n          .featured-info {\n            flex: 1;\n            \n            .featured-name {\n              display: block;\n              font-size: 32rpx;\n              font-weight: 600;\n              color: #1a1a1a;\n            }\n            \n            .featured-type {\n              display: block;\n              font-size: 24rpx;\n              color: #3a86ff;\n              margin: 4rpx 0;\n            }\n            \n            .featured-time {\n              display: block;\n              font-size: 22rpx;\n              color: #8a94a6;\n            }\n          }\n          \n          .featured-status {\n            .status-badge {\n              display: flex;\n              align-items: center;\n              gap: 8rpx;\n              padding: 8rpx 16rpx;\n              border-radius: 20rpx;\n              font-size: 22rpx;\n              \n              &.featured {\n                background: rgba(245, 158, 11, 0.1);\n                color: #f59e0b;\n              }\n              \n              &.normal {\n                background: rgba(138, 148, 166, 0.1);\n                color: #8a94a6;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  .loading-content {\n    background: #FFFFFF;\n    border-radius: 20rpx;\n    padding: 60rpx;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    \n    .loading-spin {\n      animation: spin 1s linear infinite;\n    }\n    \n    .loading-text {\n      font-size: 28rpx;\n      color: #8a94a6;\n      margin-top: 24rpx;\n    }\n  }\n}\n\n.bottom-safe-area {\n  height: env(safe-area-inset-bottom);\n}\n\n// 加载更多样式（通用）\n.load-more {\n  padding: 20rpx 0;\n  text-align: center;\n  \n  .loading-item {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8rpx;\n    color: #8a94a6;\n    font-size: 24rpx;\n  }\n  \n  .load-more-btn {\n    color: #3a86ff;\n    font-size: 24rpx;\n    padding: 16rpx 32rpx;\n    border: 1px solid #3a86ff;\n    border-radius: 20rpx;\n    background: rgba(58, 134, 255, 0.05);\n    display: inline-block;\n    \n    &:active {\n      background: rgba(58, 134, 255, 0.1);\n    }\n  }\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n// 骨架屏动画\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n// 骨架屏基础样式\n.skeleton-base {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 8rpx;\n}\n\n// 概览卡片骨架屏\n.skeleton-card {\n  .skeleton-icon {\n    @extend .skeleton-base;\n    width: 60rpx;\n    height: 60rpx;\n    border-radius: 50%;\n  }\n  \n  .skeleton-info {\n    flex: 1;\n    margin-left: 24rpx;\n    \n    .skeleton-number {\n      @extend .skeleton-base;\n      height: 48rpx;\n      width: 80rpx;\n      margin-bottom: 8rpx;\n    }\n    \n    .skeleton-label {\n      @extend .skeleton-base;\n      height: 32rpx;\n      width: 120rpx;\n    }\n  }\n}\n\n// 功能模块骨架屏\n.skeleton-module {\n  .skeleton-module-icon {\n    @extend .skeleton-base;\n    width: 60rpx;\n    height: 60rpx;\n    border-radius: 50%;\n  }\n  \n  .skeleton-module-content {\n    flex: 1;\n    margin-left: 24rpx;\n    \n    .skeleton-module-name {\n      @extend .skeleton-base;\n      height: 36rpx;\n      width: 120rpx;\n      margin-bottom: 8rpx;\n    }\n    \n    .skeleton-module-desc {\n      @extend .skeleton-base;\n      height: 28rpx;\n      width: 180rpx;\n      margin-bottom: 8rpx;\n    }\n    \n    .skeleton-module-count {\n      @extend .skeleton-base;\n      height: 24rpx;\n      width: 100rpx;\n    }\n  }\n}\n\n// 活动列表骨架屏\n.skeleton-activity {\n  .skeleton-activity-icon {\n    @extend .skeleton-base;\n    width: 48rpx;\n    height: 48rpx;\n    border-radius: 50%;\n  }\n  \n  .skeleton-activity-content {\n    flex: 1;\n    margin-left: 20rpx;\n    \n    .skeleton-activity-title {\n      @extend .skeleton-base;\n      height: 32rpx;\n      width: 140rpx;\n      margin-bottom: 8rpx;\n    }\n    \n    .skeleton-activity-desc {\n      @extend .skeleton-base;\n      height: 28rpx;\n      width: 200rpx;\n      margin-bottom: 8rpx;\n    }\n    \n    .skeleton-activity-time {\n      @extend .skeleton-base;\n      height: 24rpx;\n      width: 120rpx;\n    }\n  }\n}\n\n// 记录列表骨架屏\n.skeleton-record {\n  .skeleton-record-avatar {\n    @extend .skeleton-base;\n    width: 80rpx;\n    height: 80rpx;\n    border-radius: 50%;\n  }\n  \n  .skeleton-record-info {\n    flex: 1;\n    margin-left: 24rpx;\n    \n    .skeleton-record-name {\n      @extend .skeleton-base;\n      height: 32rpx;\n      width: 100rpx;\n      margin-bottom: 8rpx;\n    }\n    \n    .skeleton-record-type {\n      @extend .skeleton-base;\n      height: 24rpx;\n      width: 80rpx;\n      margin-bottom: 8rpx;\n    }\n    \n    .skeleton-record-time {\n      @extend .skeleton-base;\n      height: 22rpx;\n      width: 120rpx;\n    }\n  }\n  \n  .skeleton-record-actions {\n    display: flex;\n    gap: 16rpx;\n    \n    .skeleton-action-btn {\n      @extend .skeleton-base;\n      width: 48rpx;\n      height: 48rpx;\n      border-radius: 50%;\n    }\n  }\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=3f8b765e&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=3f8b765e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571663234\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
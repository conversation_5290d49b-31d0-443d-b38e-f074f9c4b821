require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol_pkg/task/edit"],{

/***/ 435:
/*!*******************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fpatrol_pkg%2Ftask%2Fedit"} ***!
  \*******************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _edit = _interopRequireDefault(__webpack_require__(/*! ./pages/patrol_pkg/task/edit.vue */ 436));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_edit.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 436:
/*!**********************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/edit.vue ***!
  \**********************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _edit_vue_vue_type_template_id_36b84017___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edit.vue?vue&type=template&id=36b84017& */ 437);
/* harmony import */ var _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./edit.vue?vue&type=script&lang=js& */ 439);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./edit.vue?vue&type=style&index=0&lang=scss& */ 441);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _edit_vue_vue_type_template_id_36b84017___WEBPACK_IMPORTED_MODULE_0__["render"],
  _edit_vue_vue_type_template_id_36b84017___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _edit_vue_vue_type_template_id_36b84017___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/patrol_pkg/task/edit.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 437:
/*!*****************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/edit.vue?vue&type=template&id=36b84017& ***!
  \*****************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_36b84017___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=36b84017& */ 438);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_36b84017___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_36b84017___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_36b84017___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_36b84017___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 438:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/edit.vue?vue&type=template&id=36b84017& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.showUserSelect
    ? _vm.__map(_vm.filteredUsers, function (user, idx) {
        var $orig = _vm.__get_orig(user)
        var m0 = user.role && _vm.getRoleText(user.role)
        var m1 = m0 ? _vm.getRoleText(user.role) : null
        return {
          $orig: $orig,
          m0: m0,
          m1: m1,
        }
      })
    : null
  var m2 = _vm.selectedUser ? _vm.getRoleText(_vm.selectedUser.role) : null
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      $event.stopPropagation()
      _vm.showUserSelect = true
    }
    _vm.e1 = function ($event) {
      $event.stopPropagation()
      _vm.showUserSelect = false
    }
    _vm.e2 = function ($event) {
      _vm.showUserSelect = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
        m2: m2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 439:
/*!***********************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/edit.vue?vue&type=script&lang=js& ***!
  \***********************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js& */ 440);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 440:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/edit.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, uniCloud) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! @/utils/patrol-api.js */ 71));
var _date = __webpack_require__(/*! @/utils/date.js */ 72);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      loading: false,
      taskId: '',
      formData: {
        name: '',
        area: '',
        date: '',
        shift_id: '',
        route_id: '',
        user_id: '',
        remark: '',
        status: 0
      },
      shiftOptions: [{
        name: '加载中...'
      }],
      shiftIndex: 0,
      selectedShift: null,
      routeOptions: [{
        name: '加载中...'
      }],
      routeIndex: 0,
      selectedRoute: null,
      userOptions: [{
        nickname: '加载中...'
      }],
      userIndex: 0,
      selectedUser: null,
      statusOptions: [{
        value: 0,
        label: '未开始'
      }, {
        value: 1,
        label: '进行中'
      }, {
        value: 2,
        label: '已完成'
      }, {
        value: 3,
        label: '已超时'
      }, {
        value: 4,
        label: '已取消'
      }],
      statusIndex: 0,
      roleNameMap: {},
      timeZoneIndicator: null,
      STATUS: {
        NOT_STARTED: 0,
        IN_PROGRESS: 1,
        COMPLETED: 2,
        EXPIRED: 3,
        CANCELLED: 4
      },
      originalStatus: null,
      predictedStatus: null,
      statusManuallyChanged: false,
      processedRounds: [],
      pointsRefreshed: false,
      searchUserName: '',
      filteredUsers: [],
      showUserSelect: false
    };
  },
  onLoad: function onLoad(options) {
    var _this = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              if (!options.id) {
                _context.next = 4;
                break;
              }
              _this.taskId = options.id;
              _context.next = 7;
              break;
            case 4:
              uni.showToast({
                title: '任务ID不能为空',
                icon: 'none'
              });
              setTimeout(function () {
                uni.navigateBack();
              }, 1500);
              return _context.abrupt("return");
            case 7:
              _this.timeZoneIndicator = (0, _date.detectTimeZone)();

              // 🔥 优化：并行加载数据提升用户体验
              _context.prev = 8;
              uni.showLoading({
                title: '加载中...'
              });

              // 并行加载减少用户等待时间，包括任务详情
              _context.next = 12;
              return Promise.all([_this.loadShifts(), _this.loadRoutes(), _this.loadRoles(), _this.loadUsers(), _this.loadTaskData() // 同时加载任务详情
              ]);
            case 12:
              _context.next = 18;
              break;
            case 14:
              _context.prev = 14;
              _context.t0 = _context["catch"](8);
              console.error('初始化数据加载失败:', _context.t0);
              uni.showToast({
                title: '数据加载失败',
                icon: 'none'
              });
            case 18:
              _context.prev = 18;
              uni.hideLoading();
              return _context.finish(18);
            case 21:
            case "end":
              return _context.stop();
          }
        }
      }, _callee, null, [[8, 14, 18, 21]]);
    }))();
  },
  watch: {
    'formData.date': function formDataDate() {
      this.updatePredictedStatus();
    },
    selectedShift: {
      handler: function handler() {
        this.updatePredictedStatus();
      },
      deep: true
    },
    statusIndex: function statusIndex() {
      if (this.originalStatus !== null) {
        var newStatus = this.statusOptions[this.statusIndex].value;
        this.statusManuallyChanged = newStatus !== this.originalStatus;
      }
    },
    // 监听用户列表变化，初始化过滤后的列表
    userOptions: {
      handler: function handler(val) {
        this.filteredUsers = (0, _toConsumableArray2.default)(val);
      },
      immediate: true
    }
  },
  methods: {
    updatePredictedStatus: function updatePredictedStatus() {
      if (!this.formData.date || !this.selectedShift || !this.selectedShift.start_time) {
        this.predictedStatus = this.STATUS.NOT_STARTED;
        return;
      }
      try {
        var timezoneOffset = -(new Date().getTimezoneOffset() / 60);
        console.log("\u5F53\u524D\u65F6\u533A\u504F\u79FB: UTC".concat(timezoneOffset >= 0 ? '+' : '').concat(timezoneOffset));
        var isAcrossDay = this.selectedShift.across_day;
        var shiftStartTime = this.selectedShift.start_time;
        var startTime = (0, _date.calculateRoundTime)(this.formData.date, shiftStartTime);
        var now = new Date();
        console.log('预计任务状态计算:');
        console.log("- \u4EFB\u52A1\u65E5\u671F: ".concat(this.formData.date));
        console.log("- \u73ED\u6B21\u5F00\u59CB\u65F6\u95F4: ".concat(shiftStartTime));
        console.log("- \u5B8C\u6574\u5F00\u59CB\u65F6\u95F4: ".concat(startTime.toISOString()));
        console.log("- \u5F53\u524D\u65F6\u95F4: ".concat(now.toISOString()));
        console.log("- \u662F\u5426\u8DE8\u5929: ".concat(isAcrossDay));
        if (startTime > now) {
          console.log('> 判断结果: 未开始（开始时间在未来）');
          this.predictedStatus = this.STATUS.NOT_STARTED;
        } else {
          console.log('> 判断结果: 进行中（开始时间已过）');
          this.predictedStatus = this.STATUS.IN_PROGRESS;
        }
      } catch (error) {
        console.error('计算预计状态时出错:', error);
        this.predictedStatus = this.STATUS.NOT_STARTED;
      }
    },
    calculateTaskStatus: function calculateTaskStatus(startTimeISOString) {
      // 首先获取所有已处理过的轮次（有状态值的轮次）
      if (this.processedRounds && this.processedRounds.length > 0) {
        // 检查是否有进行中的轮次
        var hasActiveRounds = this.processedRounds.some(function (round) {
          return round.status === 1;
        });
        if (hasActiveRounds) {
          console.log('> 计算结果: 进行中（有进行中的轮次）');
          return this.STATUS.IN_PROGRESS;
        }

        // 检查是否所有轮次都已完成
        var allRoundsCompleted = this.processedRounds.every(function (round) {
          return round.status === 2;
        });
        if (allRoundsCompleted) {
          console.log('> 计算结果: 已完成（所有轮次都已完成）');
          return this.STATUS.COMPLETED;
        }

        // 检查是否所有轮次都已超时
        var allRoundsExpired = this.processedRounds.every(function (round) {
          return round.status === 3;
        });
        if (allRoundsExpired) {
          console.log('> 计算结果: 已超时（所有轮次都已超时）');
          return this.STATUS.EXPIRED;
        }

        // 检查是否所有轮次都未开始
        var allRoundsNotStarted = this.processedRounds.every(function (round) {
          return round.status === 0;
        });
        if (allRoundsNotStarted) {
          console.log('> 计算结果: 未开始（所有轮次都未开始）');
          return this.STATUS.NOT_STARTED;
        }

        // 如果有些轮次已超时，有些未开始，但没有进行中的轮次
        console.log('> 计算结果: 进行中（混合状态）');
        return this.STATUS.IN_PROGRESS; // 默认是进行中
      }

      // 如果没有轮次信息，则回退到基于班次时间的判断
      if (!startTimeISOString) {
        return this.STATUS.NOT_STARTED;
      }
      try {
        var now = new Date();
        var startTime = new Date(startTimeISOString);

        // 输出日志
        console.log('计算任务状态:');
        console.log("- \u5F53\u524D\u65F6\u95F4: ".concat(now.toISOString()));
        console.log("- \u5F00\u59CB\u65F6\u95F4: ".concat(startTime.toISOString()));

        // 与预测状态相同，使用第一个轮次的开始时间进行验证
        if (this.selectedShift && this.selectedShift.rounds && this.selectedShift.rounds.length > 0) {
          // 按照时间顺序排序轮次
          var sortedRounds = (0, _toConsumableArray2.default)(this.selectedShift.rounds).sort(function (a, b) {
            // 获取轮次时间并考虑day_offset
            var timeA = a.time.split(':').map(Number);
            var timeB = b.time.split(':').map(Number);
            var offsetA = a.day_offset || 0;
            var offsetB = b.day_offset || 0;

            // 先比较day_offset，再比较时间
            if (offsetA !== offsetB) return offsetA - offsetB;
            if (timeA[0] !== timeB[0]) return timeA[0] - timeB[0];
            return timeA[1] - timeB[1];
          });

          // 获取第一个轮次的时间
          var firstRound = sortedRounds[0];
          var roundTime = firstRound.time;
          var dayOffset = firstRound.day_offset || 0;

          // 计算轮次实际开始时间
          var roundStartTime = (0, _date.calculateRoundTime)(this.formData.date, roundTime, dayOffset);
          console.log("- \u7B2C\u4E00\u4E2A\u8F6E\u6B21\u65F6\u95F4: ".concat(roundTime, ", \u65E5\u671F\u504F\u79FB: ").concat(dayOffset));
          console.log("- \u8F6E\u6B21\u5B9E\u9645\u5F00\u59CB\u65F6\u95F4: ".concat(roundStartTime.toISOString()));

          // 如果轮次开始时间尚未到达，则任务未开始
          if (roundStartTime > now) {
            console.log('> 计算结果: 未开始 (基于轮次时间)');
            return this.STATUS.NOT_STARTED;
          }
        }
        if (startTime > now) {
          console.log('> 计算结果: 未开始（开始时间在未来）');
          return this.STATUS.NOT_STARTED;
        }
        console.log('> 计算结果: 进行中');
        return this.STATUS.IN_PROGRESS;
      } catch (error) {
        console.error('计算任务状态时出错:', error);
        return this.STATUS.NOT_STARTED;
      }
    },
    getStatusIcon: function getStatusIcon(status) {
      switch (status) {
        case this.STATUS.NOT_STARTED:
          return 'info-filled';
        case this.STATUS.IN_PROGRESS:
          return 'reload';
        case this.STATUS.COMPLETED:
          return 'checkmarkempty';
        case this.STATUS.EXPIRED:
          return 'closeempty';
        case this.STATUS.CANCELLED:
          return 'minus';
        default:
          return 'info';
      }
    },
    getStatusText: function getStatusText(status) {
      switch (status) {
        case this.STATUS.NOT_STARTED:
          return '未开始';
        case this.STATUS.IN_PROGRESS:
          return '进行中';
        case this.STATUS.COMPLETED:
          return '已完成';
        case this.STATUS.EXPIRED:
          return '已超时';
        case this.STATUS.CANCELLED:
          return '已取消';
        default:
          return '未知状态';
      }
    },
    getStatusExplanation: function getStatusExplanation(status) {
      var now = new Date();
      var formattedNow = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      });
      switch (status) {
        case this.STATUS.NOT_STARTED:
          if (this.selectedShift && this.selectedShift.start_time) {
            return "\u5F53\u524D\u65F6\u95F4 ".concat(formattedNow, "\uFF0C\u4EFB\u52A1\u5C06\u5728 ").concat(this.selectedShift.start_time, " \u5F00\u59CB");
          }
          return '任务开始时间尚未到达';
        case this.STATUS.IN_PROGRESS:
          if (this.selectedShift && this.selectedShift.start_time) {
            return "\u5F53\u524D\u65F6\u95F4 ".concat(formattedNow, "\uFF0C\u5DF2\u8D85\u8FC7\u4EFB\u52A1\u5F00\u59CB\u65F6\u95F4 ").concat(this.selectedShift.start_time);
          }
          return '当前时间已超过任务开始时间';
        default:
          return '';
      }
    },
    getCurrentUserId: function getCurrentUserId() {
      try {
        var userInfo = uni.getStorageSync('uni-id-pages-userInfo');
        return userInfo ? userInfo._id || '' : '';
      } catch (e) {
        return '';
      }
    },
    loadTaskData: function loadTaskData() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var currentUserId, result, taskData;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                uni.showLoading({
                  title: '加载中'
                });
                currentUserId = _this2.getCurrentUserId();
                _context2.next = 5;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'getTaskDetail',
                  data: {
                    id: _this2.taskId,
                    userId: currentUserId
                  }
                });
              case 5:
                result = _context2.sent;
                if (!(result.code === 0 && result.data)) {
                  _context2.next = 16;
                  break;
                }
                taskData = result.data.task || result.data;
                _this2.formData = {
                  name: taskData.name || '',
                  area: taskData.area || '',
                  date: taskData.patrol_date || '',
                  shift_id: taskData.shift_id || '',
                  route_id: taskData.route_id || '',
                  user_id: taskData.user_id || '',
                  remark: taskData.remark || '',
                  status: taskData.status || 0,
                  enabled_rounds: taskData.enabled_rounds || [],
                  rounds_completion: taskData.rounds_completion || []
                };
                _this2.originalStatus = taskData.status;
                _this2.statusIndex = _this2.statusOptions.findIndex(function (item) {
                  return item.value === taskData.status;
                });
                if (_this2.statusIndex === -1) _this2.statusIndex = 0;
                setTimeout(function () {
                  _this2.setInitialSelections();
                }, 500);
                setTimeout(function () {
                  _this2.updatePredictedStatus();
                }, 800);
                _context2.next = 17;
                break;
              case 16:
                throw new Error(result.message || '获取任务详情失败');
              case 17:
                _context2.next = 22;
                break;
              case 19:
                _context2.prev = 19;
                _context2.t0 = _context2["catch"](0);
                uni.showToast({
                  title: _context2.t0.message || '加载任务数据失败',
                  icon: 'none'
                });
              case 22:
                _context2.prev = 22;
                uni.hideLoading();
                return _context2.finish(22);
              case 25:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 19, 22, 25]]);
      }))();
    },
    loadShifts: function loadShifts() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var result, shifts, systemInfo, platform, isMobile;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _context3.next = 3;
                return _patrolApi.default.call({
                  name: 'patrol-shift',
                  action: 'getShiftList',
                  data: {
                    params: {
                      status: 1,
                      with_rounds: true,
                      with_detail: true
                    }
                  }
                });
              case 3:
                result = _context3.sent;
                if (result.code === 0 && result.data) {
                  shifts = result.data.list || [];
                  if (shifts.length === 0) {
                    _this3.shiftOptions = [{
                      name: '暂无可用班次'
                    }];
                  } else {
                    // 处理班次数据
                    shifts = shifts.map(function (shift) {
                      return _objectSpread(_objectSpread({}, shift), {}, {
                        name: shift.name,
                        across_day: shift.across_day || false,
                        rounds: shift.rounds && Array.isArray(shift.rounds) ? shift.rounds.filter(function (r) {
                          return r.status !== 0;
                        }).map(function (round) {
                          var time = round.time || '00:00';
                          return {
                            round: round.round,
                            time: time,
                            name: round.name || "\u8F6E\u6B21".concat(round.round),
                            start_time: round.start_time || time,
                            end_time: round.end_time || time,
                            day_offset: round.day_offset || 0,
                            duration: round.duration || 60,
                            status: round.status
                          };
                        }) : []
                      });
                    });

                    // 按照班次名称字母顺序排序
                    systemInfo = uni.getSystemInfoSync();
                    platform = systemInfo.platform;
                    isMobile = ['android', 'ios'].includes(platform); // 对班次进行排序
                    shifts.sort(function (a, b) {
                      var nameA = String(a.name || '').trim();
                      var nameB = String(b.name || '').trim();

                      // 使用 localeCompare 进行中文排序，统一使用 A-Z 排序
                      var compareResult = nameA.localeCompare(nameB, 'zh-CN');
                      return isMobile ? -compareResult : compareResult;
                    });
                    _this3.shiftOptions = shifts;
                  }
                  if (_this3.formData.shift_id) {
                    _this3.setInitialSelections();
                  }
                }
                _context3.next = 10;
                break;
              case 7:
                _context3.prev = 7;
                _context3.t0 = _context3["catch"](0);
                uni.showToast({
                  title: '加载班次数据失败',
                  icon: 'none'
                });
              case 10:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 7]]);
      }))();
    },
    loadRoutes: function loadRoutes() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var result;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                _context5.next = 3;
                return _patrolApi.default.call({
                  name: 'patrol-route',
                  action: 'getRouteList',
                  data: {
                    params: {
                      status: 1,
                      with_points: true,
                      with_detail: true,
                      include_point_details: true
                    }
                  }
                });
              case 3:
                result = _context5.sent;
                if (!(result.code === 0 && result.data)) {
                  _context5.next = 14;
                  break;
                }
                _this4.routeOptions = result.data.list || [];
                if (!(_this4.routeOptions.length === 0)) {
                  _context5.next = 10;
                  break;
                }
                _this4.routeOptions = [{
                  name: '暂无可用路线'
                }];
                _context5.next = 13;
                break;
              case 10:
                _context5.next = 12;
                return Promise.all(_this4.routeOptions.map( /*#__PURE__*/function () {
                  var _ref = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4(route) {
                    var detailRes;
                    return _regenerator.default.wrap(function _callee4$(_context4) {
                      while (1) {
                        switch (_context4.prev = _context4.next) {
                          case 0:
                            _context4.next = 2;
                            return _patrolApi.default.call({
                              name: 'patrol-route',
                              action: 'getRouteDetail',
                              data: {
                                params: {
                                  route_id: route._id,
                                  with_points: true,
                                  with_detail: true
                                }
                              }
                            });
                          case 2:
                            detailRes = _context4.sent;
                            if (!(detailRes.code === 0 && detailRes.data && detailRes.data.pointsDetail)) {
                              _context4.next = 5;
                              break;
                            }
                            return _context4.abrupt("return", _objectSpread(_objectSpread({}, route), {}, {
                              pointsDetail: detailRes.data.pointsDetail
                            }));
                          case 5:
                            return _context4.abrupt("return", route);
                          case 6:
                          case "end":
                            return _context4.stop();
                        }
                      }
                    }, _callee4);
                  }));
                  return function (_x) {
                    return _ref.apply(this, arguments);
                  };
                }()));
              case 12:
                _this4.routeOptions = _context5.sent;
              case 13:
                if (_this4.formData.route_id) {
                  _this4.setInitialSelections();
                }
              case 14:
                _context5.next = 19;
                break;
              case 16:
                _context5.prev = 16;
                _context5.t0 = _context5["catch"](0);
                uni.showToast({
                  title: '加载路线数据失败',
                  icon: 'none'
                });
              case 19:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 16]]);
      }))();
    },
    loadUsers: function loadUsers() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var currentUserId, result;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _context6.prev = 0;
                currentUserId = _this5.getCurrentUserId();
                if (currentUserId) {
                  _context6.next = 4;
                  break;
                }
                throw new Error('未能获取当前用户ID');
              case 4:
                _context6.next = 6;
                return _patrolApi.default.call({
                  name: 'patrol-user',
                  action: 'getUsers',
                  data: {
                    userid: currentUserId,
                    params: {
                      pageSize: 100,
                      field: 'nickname,avatar,role,username,wx_openid,identities' // 请求所需字段
                    }
                  }
                });
              case 6:
                result = _context6.sent;
                if (result.code === 0 && result.data) {
                  _this5.userOptions = result.data.list.filter(function (user) {
                    if (!user.role) return true;
                    if (Array.isArray(user.role)) {
                      return !user.role.includes('admin');
                    } else {
                      return user.role !== 'admin';
                    }
                  }).filter(function (user) {
                    // 过滤掉以"匿名"开头的用户
                    var nickname = user.real_name || user.nickname || user.username || '';
                    return !nickname.startsWith('匿名');
                  }).map(function (user) {
                    return {
                      _id: user._id,
                      nickname: user.real_name || user.nickname || user.username || '未命名用户',
                      avatar: user.avatar || '/static/user/default-avatar.png',
                      role: user.role || [],
                      wx_openid: user.wx_openid || null,
                      identities: user.identities || []
                    };
                  });
                  if (_this5.userOptions.length === 0) {
                    _this5.userOptions = [{
                      nickname: '暂无可用人员'
                    }];
                  }
                  if (_this5.formData.user_id) {
                    _this5.setInitialSelections();
                  }
                }
                _context6.next = 13;
                break;
              case 10:
                _context6.prev = 10;
                _context6.t0 = _context6["catch"](0);
                uni.showToast({
                  title: '加载用户数据失败',
                  icon: 'none'
                });
              case 13:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[0, 10]]);
      }))();
    },
    loadRoles: function loadRoles() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var currentUserId, result, roleMap;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _context7.prev = 0;
                currentUserId = _this6.getCurrentUserId();
                if (currentUserId) {
                  _context7.next = 4;
                  break;
                }
                throw new Error('未能获取当前用户ID');
              case 4:
                _context7.next = 6;
                return _patrolApi.default.call({
                  name: 'patrol-user',
                  action: 'getRoleList',
                  data: {
                    userid: currentUserId
                  }
                });
              case 6:
                result = _context7.sent;
                if (result.code === 0 && result.data) {
                  roleMap = {};
                  result.data.forEach(function (role) {
                    roleMap[role.role_id] = role.role_name;
                  });
                  _this6.roleNameMap = roleMap;
                }
                _context7.next = 13;
                break;
              case 10:
                _context7.prev = 10;
                _context7.t0 = _context7["catch"](0);
                uni.showToast({
                  title: '加载角色数据失败',
                  icon: 'none'
                });
              case 13:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[0, 10]]);
      }))();
    },
    loadShiftDetail: function loadShiftDetail(shiftId) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var result, shiftData, enabledRounds;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                _context8.prev = 0;
                _context8.next = 3;
                return _patrolApi.default.call({
                  name: 'patrol-shift',
                  action: 'getShiftDetail',
                  data: {
                    params: {
                      shift_id: shiftId
                    }
                  }
                });
              case 3:
                result = _context8.sent;
                if (result.code === 0 && result.data) {
                  shiftData = result.data;
                  if (shiftData.rounds && Array.isArray(shiftData.rounds)) {
                    enabledRounds = shiftData.rounds.filter(function (r) {
                      return r.status !== 0;
                    });
                    _this7.selectedShift = _objectSpread(_objectSpread({}, shiftData), {}, {
                      across_day: shiftData.across_day || false,
                      rounds: enabledRounds.map(function (round) {
                        var time = round.time || '00:00';
                        return {
                          round: round.round,
                          name: round.name || "\u8F6E\u6B21".concat(round.round),
                          time: time,
                          start_time: round.start_time || time,
                          end_time: round.end_time || time,
                          day_offset: round.day_offset || 0,
                          duration: round.duration || 60,
                          status: round.status
                        };
                      })
                    });
                  }
                }
                _context8.next = 11;
                break;
              case 7:
                _context8.prev = 7;
                _context8.t0 = _context8["catch"](0);
                console.error('加载班次详情失败:', _context8.t0);
                uni.showToast({
                  title: '加载班次详情失败',
                  icon: 'none'
                });
              case 11:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[0, 7]]);
      }))();
    },
    setInitialSelections: function setInitialSelections() {
      var _this8 = this;
      if (this.formData.shift_id && this.shiftOptions.length > 0) {
        var index = this.shiftOptions.findIndex(function (item) {
          return item._id === _this8.formData.shift_id;
        });
        if (index !== -1) {
          this.shiftIndex = index;
          var shift = this.shiftOptions[index];
          this.selectedShift = _objectSpread(_objectSpread({}, shift), {}, {
            across_day: shift.across_day || false,
            rounds: shift.rounds && Array.isArray(shift.rounds) ? shift.rounds.filter(function (r) {
              return r.status !== 0;
            }).map(function (round) {
              var time = round.time || '00:00';
              return {
                round: round.round,
                name: round.name || "\u8F6E\u6B21".concat(round.round),
                time: time,
                start_time: round.start_time || time,
                end_time: round.end_time || time,
                day_offset: round.day_offset || 0,
                duration: round.duration || 60,
                status: round.status
              };
            }) : []
          });
        }
      }
      if (this.formData.route_id && this.routeOptions.length > 0) {
        var _index = this.routeOptions.findIndex(function (item) {
          return item._id === _this8.formData.route_id;
        });
        if (_index !== -1) {
          this.routeIndex = _index;
          var route = this.routeOptions[_index];
          this.selectedRoute = _objectSpread(_objectSpread({}, route), {}, {
            pointsDetail: route.pointsDetail || []
          });
        }
      }
      if (this.formData.user_id && this.userOptions.length > 0) {
        var _index2 = this.userOptions.findIndex(function (item) {
          return item._id === _this8.formData.user_id;
        });
        if (_index2 !== -1) {
          this.userIndex = _index2;
          this.selectedUser = this.userOptions[_index2];
        }
      }
    },
    onDateChange: function onDateChange(e) {
      // 确保日期格式一致 - 只保留YYYY-MM-DD部分
      var dateValue = e.detail.value;
      // 检查是否需要处理日期格式
      if (dateValue.includes('T') || dateValue.includes('Z')) {
        // 如果有时区信息，提取日期部分
        this.formData.date = dateValue.split('T')[0];
      } else {
        // 否则直接使用，这应该已经是YYYY-MM-DD格式
        this.formData.date = dateValue;
      }
    },
    onShiftChange: function onShiftChange(e) {
      var index = e.detail.value;
      this.shiftIndex = index;
      var shift = this.shiftOptions[index];
      if (shift && shift._id) {
        this.formData.shift_id = shift._id;
        if (shift.rounds && Array.isArray(shift.rounds)) {
          var enabledRounds = shift.rounds.filter(function (r) {
            return r.status !== 0;
          });
          this.selectedShift = _objectSpread(_objectSpread({}, shift), {}, {
            across_day: shift.across_day || false,
            rounds: enabledRounds.map(function (round) {
              var time = round.time || '00:00';
              return {
                round: round.round,
                name: round.name || "\u8F6E\u6B21".concat(round.round),
                time: time,
                start_time: round.start_time || time,
                end_time: round.end_time || time,
                day_offset: round.day_offset || 0,
                duration: round.duration || 60,
                status: round.status
              };
            })
          });

          // 如果是跨天班次，给用户提示
          if (this.selectedShift.across_day) {
            uni.showToast({
              title: '您选择了跨天班次，系统将自动处理次日轮次',
              icon: 'none',
              duration: 3000
            });
          }
        } else {
          this.loadShiftDetail(shift._id);
        }
      } else {
        this.formData.shift_id = '';
        this.selectedShift = null;
      }
    },
    onRouteChange: function onRouteChange(e) {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var route, res, routeData;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                _this9.routeIndex = e.detail.value;
                route = _this9.routeOptions[_this9.routeIndex];
                if (!(route && route._id)) {
                  _context9.next = 17;
                  break;
                }
                _this9.formData.route_id = route._id;
                _this9.formData.area = route.name || '';
                _context9.prev = 5;
                _context9.next = 8;
                return _patrolApi.default.call({
                  name: 'patrol-route',
                  action: 'getRouteDetail',
                  data: {
                    params: {
                      route_id: route._id,
                      with_points: true,
                      with_detail: true,
                      include_point_details: true
                    }
                  }
                });
              case 8:
                res = _context9.sent;
                if (res.code === 0 && res.data) {
                  routeData = res.data;
                  _this9.selectedRoute = _objectSpread(_objectSpread({}, routeData), {}, {
                    pointsDetail: routeData.pointsDetail || []
                  });
                  _this9.formData.area = routeData.name || route.name || '';
                }
                _context9.next = 15;
                break;
              case 12:
                _context9.prev = 12;
                _context9.t0 = _context9["catch"](5);
                console.error('获取路线详情错误', _context9.t0);
              case 15:
                _context9.next = 20;
                break;
              case 17:
                _this9.formData.route_id = '';
                _this9.formData.area = '';
                _this9.selectedRoute = null;
              case 20:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[5, 12]]);
      }))();
    },
    onUserChange: function onUserChange(index) {
      this.userIndex = index;
      this.selectedUser = this.userOptions[index];
      this.formData.user_id = this.selectedUser._id;
    },
    onStatusChange: function onStatusChange(e) {
      var index = e.detail.value;
      this.statusIndex = index;
      this.formData.status = this.statusOptions[index].value;
    },
    getRoleText: function getRoleText(role) {
      var _this10 = this;
      if (!role) return '普通员工';
      if (Array.isArray(role)) {
        if (role.length === 0) return '普通员工';
        return role.map(function (r) {
          return _this10.getSingleRoleText(r);
        }).join(', ');
      }
      return this.getSingleRoleText(role);
    },
    getSingleRoleText: function getSingleRoleText(role) {
      if (this.roleNameMap[role]) {
        return this.roleNameMap[role];
      }
      var roleMap = {
        'admin': '管理员',
        'responsible': '责任人',
        'reviser': '发布人',
        'supervisor': '主管',
        'PM': '副厂长',
        'GM': '厂长',
        'logistics': '后勤员',
        'dispatch': '调度员',
        'Integrated': '综合员',
        'operator': '设备员',
        'technician': '工艺员',
        'mechanic': '技术员',
        'user': '普通员工',
        'manager': '管理人员',
        'worker': '普通员工'
      };
      return roleMap[role] || '用户 (' + role + ')';
    },
    submitForm: function submitForm() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var _originalTask, _originalTask$overall, _originalTask2, _this11$selectedShift5, _this11$selectedShift6, _this11$selectedShift7, _this11$selectedRoute10, _this11$selectedRoute11, rounds, totalPoints, completedPoints, abnormalCount, newOverallStats, taskStatus, _startTime, shiftName, routeName, originalTask, result, startTime, endTime, _this11$selectedShift, _this11$selectedShift2, endHours, _this11$selectedShift3, _this11$selectedShift4, startHours, dayOffset, endDateTime, cleanDate, requestData, response;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                if (_this11.validateForm()) {
                  _context10.next = 2;
                  break;
                }
                return _context10.abrupt("return");
              case 2:
                _this11.loading = true;
                _context10.prev = 3;
                _context10.next = 6;
                return _this11.processRoundsData();
              case 6:
                rounds = _context10.sent;
                // 保存处理后的轮次数据，以便在计算任务状态时使用
                _this11.processedRounds = rounds;

                // 计算新的overall_stats
                totalPoints = rounds.reduce(function (sum, round) {
                  return sum + round.points.length;
                }, 0);
                completedPoints = rounds.reduce(function (sum, round) {
                  return sum + round.points.filter(function (p) {
                    return p.status === 1 || p.status === 2;
                  }).length;
                }, 0);
                abnormalCount = rounds.reduce(function (sum, round) {
                  return sum + round.points.filter(function (p) {
                    return p.abnormal;
                  }).length;
                }, 0);
                newOverallStats = {
                  total_points: totalPoints,
                  completed_points: completedPoints,
                  missed_points: totalPoints - completedPoints,
                  completion_rate: totalPoints > 0 ? completedPoints / totalPoints : 0,
                  abnormal_count: abnormalCount,
                  last_checkin_time: ((_originalTask = originalTask) === null || _originalTask === void 0 ? void 0 : (_originalTask$overall = _originalTask.overall_stats) === null || _originalTask$overall === void 0 ? void 0 : _originalTask$overall.last_checkin_time) || null
                };
                if (_this11.statusManuallyChanged) {
                  taskStatus = _this11.formData.status;
                  console.log('使用手动选择的状态:', taskStatus);
                } else {
                  _startTime = null;
                  if (_this11.selectedShift && _this11.selectedShift.start_time) {
                    _startTime = (0, _date.calculateRoundTime)(_this11.formData.date, _this11.selectedShift.start_time).toISOString();
                  }
                  taskStatus = _this11.calculateTaskStatus(_startTime);
                  console.log('使用自动计算的状态:', taskStatus);
                }
                shiftName = _this11.selectedShift ? _this11.selectedShift.name : '';
                routeName = _this11.selectedRoute ? _this11.selectedRoute.name : '';
                originalTask = null;
                _context10.prev = 16;
                _context10.next = 19;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'getTaskDetail',
                  data: {
                    id: _this11.taskId
                  }
                });
              case 19:
                result = _context10.sent;
                if (result.code === 0 && result.data) {
                  originalTask = result.data.task || result.data;
                }
                _context10.next = 26;
                break;
              case 23:
                _context10.prev = 23;
                _context10.t0 = _context10["catch"](16);
                console.error('获取原始任务数据失败:', _context10.t0);
              case 26:
                if (_this11.selectedShift) {
                  if (_this11.selectedShift.start_time) {
                    startTime = (0, _date.calculateRoundTime)(_this11.formData.date, _this11.selectedShift.start_time).toISOString();
                  }
                  if (_this11.selectedShift.end_time) {
                    _this11$selectedShift = _this11.selectedShift.end_time.split(':').map(Number), _this11$selectedShift2 = (0, _slicedToArray2.default)(_this11$selectedShift, 1), endHours = _this11$selectedShift2[0];
                    _this11$selectedShift3 = _this11.selectedShift.start_time.split(':').map(Number), _this11$selectedShift4 = (0, _slicedToArray2.default)(_this11$selectedShift3, 1), startHours = _this11$selectedShift4[0];
                    dayOffset = 0;
                    if (_this11.selectedShift.across_day && endHours < startHours) {
                      dayOffset = 1;
                    }
                    endDateTime = (0, _date.calculateRoundTime)(_this11.formData.date, _this11.selectedShift.end_time, dayOffset);
                    endTime = endDateTime.toISOString();
                  }
                }

                // 确保patrol_date只有日期部分，没有时间部分
                cleanDate = _this11.formData.date.split('T')[0]; // 提取YYYY-MM-DD部分，移除可能的时区信息
                requestData = {
                  id: _this11.taskId,
                  name: _this11.formData.name,
                  area: _this11.formData.area || '默认区域',
                  patrol_date: cleanDate,
                  shift_id: _this11.formData.shift_id,
                  route_id: _this11.formData.route_id,
                  user_id: _this11.formData.user_id,
                  status: taskStatus,
                  remark: _this11.formData.remark,
                  rounds_detail: rounds.map(function (round) {
                    return _objectSpread(_objectSpread({}, round), {}, {
                      points: round.points.map(function (p) {
                        var _this11$selectedRoute, _this11$selectedRoute2, _this11$selectedRoute3, _this11$selectedRoute4, _this11$selectedRoute5, _this11$selectedRoute6, _this11$selectedRoute7, _this11$selectedRoute8, _this11$selectedRoute9;
                        return _objectSpread(_objectSpread({}, p), {}, {
                          qrcode_enabled: ((_this11$selectedRoute = _this11.selectedRoute) === null || _this11$selectedRoute === void 0 ? void 0 : (_this11$selectedRoute2 = _this11$selectedRoute.pointsDetail) === null || _this11$selectedRoute2 === void 0 ? void 0 : (_this11$selectedRoute3 = _this11$selectedRoute2.find(function (pd) {
                            return pd._id === p.point_id;
                          })) === null || _this11$selectedRoute3 === void 0 ? void 0 : _this11$selectedRoute3.qrcode_enabled) || false,
                          qrcode_required: ((_this11$selectedRoute4 = _this11.selectedRoute) === null || _this11$selectedRoute4 === void 0 ? void 0 : (_this11$selectedRoute5 = _this11$selectedRoute4.pointsDetail) === null || _this11$selectedRoute5 === void 0 ? void 0 : (_this11$selectedRoute6 = _this11$selectedRoute5.find(function (pd) {
                            return pd._id === p.point_id;
                          })) === null || _this11$selectedRoute6 === void 0 ? void 0 : _this11$selectedRoute6.qrcode_required) || false,
                          qrcode_version: ((_this11$selectedRoute7 = _this11.selectedRoute) === null || _this11$selectedRoute7 === void 0 ? void 0 : (_this11$selectedRoute8 = _this11$selectedRoute7.pointsDetail) === null || _this11$selectedRoute8 === void 0 ? void 0 : (_this11$selectedRoute9 = _this11$selectedRoute8.find(function (pd) {
                            return pd._id === p.point_id;
                          })) === null || _this11$selectedRoute9 === void 0 ? void 0 : _this11$selectedRoute9.qrcode_version) || 0
                        });
                      })
                    });
                  }),
                  enabled_rounds: ((_originalTask2 = originalTask) === null || _originalTask2 === void 0 ? void 0 : _originalTask2.enabled_rounds) || rounds.map(function (round) {
                    return round.round;
                  }),
                  overall_stats: newOverallStats,
                  shift_detail: {
                    name: shiftName,
                    start_time: ((_this11$selectedShift5 = _this11.selectedShift) === null || _this11$selectedShift5 === void 0 ? void 0 : _this11$selectedShift5.start_time) || "",
                    end_time: ((_this11$selectedShift6 = _this11.selectedShift) === null || _this11$selectedShift6 === void 0 ? void 0 : _this11$selectedShift6.end_time) || "",
                    across_day: ((_this11$selectedShift7 = _this11.selectedShift) === null || _this11$selectedShift7 === void 0 ? void 0 : _this11$selectedShift7.across_day) || false
                  },
                  route_detail: {
                    name: routeName,
                    points: ((_this11$selectedRoute10 = _this11.selectedRoute) === null || _this11$selectedRoute10 === void 0 ? void 0 : (_this11$selectedRoute11 = _this11$selectedRoute10.pointsDetail) === null || _this11$selectedRoute11 === void 0 ? void 0 : _this11$selectedRoute11.map(function (p) {
                      return {
                        point_id: p._id,
                        name: p.name,
                        order: p.order || 0,
                        location: p.location,
                        range: p.range,
                        qrcode_enabled: p.qrcode_enabled || false,
                        qrcode_required: p.qrcode_required || false,
                        qrcode_version: p.qrcode_version || 0
                      };
                    })) || []
                  }
                };
                if (startTime) requestData.start_time = startTime;
                if (endTime) requestData.end_time = endTime;

                // console.log('提交任务数据:', JSON.stringify(requestData, null, 2));
                _context10.next = 33;
                return uniCloud.callFunction({
                  name: 'patrol-task',
                  data: {
                    action: 'updateTask',
                    params: requestData
                  }
                }).then(function (res) {
                  return res.result;
                });
              case 33:
                response = _context10.sent;
                if (!(response && response.code === 0)) {
                  _context10.next = 40;
                  break;
                }
                uni.showToast({
                  title: '更新成功',
                  icon: 'success'
                });
                uni.$emit('refresh-task-list');
                setTimeout(function () {
                  uni.navigateBack();
                }, 1500);
                _context10.next = 41;
                break;
              case 40:
                throw new Error(response.message || '更新失败');
              case 41:
                _context10.next = 47;
                break;
              case 43:
                _context10.prev = 43;
                _context10.t1 = _context10["catch"](3);
                console.error('更新任务失败:', _context10.t1);
                uni.showToast({
                  title: _context10.t1.message || '更新失败',
                  icon: 'none'
                });
              case 47:
                _context10.prev = 47;
                _this11.loading = false;
                return _context10.finish(47);
              case 50:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10, null, [[3, 43, 47, 50], [16, 23]]);
      }))();
    },
    validateForm: function validateForm() {
      if (!this.formData.name) {
        uni.showToast({
          title: '请输入任务名称',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.date) {
        uni.showToast({
          title: '请选择执行日期',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.shift_id) {
        uni.showToast({
          title: '请选择班次',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.route_id) {
        uni.showToast({
          title: '请选择巡检路线',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.user_id) {
        uni.showToast({
          title: '请选择执行人员',
          icon: 'none'
        });
        return false;
      }
      return true;
    },
    goBack: function goBack() {
      uni.navigateBack();
    },
    processRoundsData: function processRoundsData() {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        var taskDate, points, originalTask, result;
        return _regenerator.default.wrap(function _callee11$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                if (!(!_this12.selectedShift || !_this12.selectedShift.rounds || !_this12.selectedRoute || !_this12.selectedRoute.pointsDetail)) {
                  _context11.next = 2;
                  break;
                }
                return _context11.abrupt("return", []);
              case 2:
                taskDate = _this12.formData.date;
                if (taskDate) {
                  _context11.next = 5;
                  break;
                }
                return _context11.abrupt("return", []);
              case 5:
                points = _this12.selectedRoute.pointsDetail;
                originalTask = null;
                _context11.prev = 7;
                _context11.next = 10;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'getTaskDetail',
                  data: {
                    id: _this12.taskId
                  }
                });
              case 10:
                result = _context11.sent;
                if (result.code === 0 && result.data) {
                  originalTask = result.data.task || result.data;
                }
                _context11.next = 17;
                break;
              case 14:
                _context11.prev = 14;
                _context11.t0 = _context11["catch"](7);
                console.error('获取原始任务数据失败:', _context11.t0);
              case 17:
                return _context11.abrupt("return", _this12.selectedShift.rounds.map(function (round) {
                  var _originalStats, _originalRound;
                  var checkTime = round.time || '00:00';
                  var dayOffset = round.day_offset || 0; // 使用班次中的day_offset

                  var originalRound = null;
                  var originalStats = null;
                  if (originalTask && originalTask.rounds_detail) {
                    originalRound = originalTask.rounds_detail.find(function (r) {
                      return r.round === round.round;
                    });
                    if (originalRound) {
                      originalStats = originalRound.stats;
                    }
                  }

                  // 始终使用最新的班次时间计算
                  var isAcrossDay = _this12.selectedShift.across_day;
                  var shiftStartTime = _this12.selectedShift.start_time || '00:00';
                  if (isAcrossDay) {
                    var _checkTime$split$map = checkTime.split(':').map(Number),
                      _checkTime$split$map2 = (0, _slicedToArray2.default)(_checkTime$split$map, 2),
                      roundHours = _checkTime$split$map2[0],
                      roundMinutes = _checkTime$split$map2[1];
                    var _shiftStartTime$split = shiftStartTime.split(':').map(Number),
                      _shiftStartTime$split2 = (0, _slicedToArray2.default)(_shiftStartTime$split, 2),
                      shiftStartHours = _shiftStartTime$split2[0],
                      shiftStartMinutes = _shiftStartTime$split2[1];
                    var roundTimeMinutes = roundHours * 60 + roundMinutes;
                    var shiftStartTotalMinutes = shiftStartHours * 60 + shiftStartMinutes;
                    if (roundTimeMinutes < shiftStartTotalMinutes) {
                      dayOffset = 1;
                    }
                  }
                  var checkDateTime = (0, _date.calculateRoundTime)(taskDate, checkTime, dayOffset);
                  var endDateTime = (0, _date.calculateEndTime)(checkDateTime, round.duration || 60);
                  console.log("\u8F6E\u6B21".concat(round.round, "\u65F6\u95F4\u8BA1\u7B97:"), {
                    日期: taskDate,
                    时间: checkTime,
                    偏移: dayOffset,
                    开始时间: checkDateTime.toISOString(),
                    结束时间: endDateTime.toISOString()
                  });
                  var roundPoints = points.map(function (point) {
                    var originalPoint = null;
                    if (originalRound && originalRound.points) {
                      originalPoint = originalRound.points.find(function (p) {
                        return p.point_id === point._id;
                      });
                    }
                    return _objectSpread({
                      point_id: point._id,
                      name: point.name,
                      order: point.order || 0,
                      status: originalPoint ? originalPoint.status : 0,
                      location: point.location,
                      range: point.range,
                      qrcode_enabled: point.qrcode_enabled || false,
                      qrcode_required: point.qrcode_required || false,
                      qrcode_version: point.qrcode_version || 0
                    }, originalPoint ? {
                      checkin_time: originalPoint.checkin_time,
                      checkin_location: originalPoint.checkin_location,
                      abnormal: originalPoint.abnormal,
                      abnormal_reason: originalPoint.abnormal_reason
                    } : {});
                  });
                  var roundStatus = 0;
                  if (originalRound) {
                    roundStatus = originalRound.status;
                  } else {
                    var now = new Date();
                    if (now >= endDateTime) {
                      roundStatus = 3;
                    } else if (now >= checkDateTime) {
                      roundStatus = 1;
                    }
                  }

                  // 重新计算统计数据，始终基于当前的点位数量
                  var completedPoints = roundPoints.filter(function (p) {
                    return p.status === 1 || p.status === 2;
                  }).length;
                  var stats = {
                    total_points: roundPoints.length,
                    completed_points: completedPoints,
                    missed_points: roundPoints.length - completedPoints,
                    completion_rate: roundPoints.length > 0 ? completedPoints / roundPoints.length : 0,
                    abnormal_count: roundPoints.filter(function (p) {
                      return p.abnormal;
                    }).length,
                    last_checkin_time: ((_originalStats = originalStats) === null || _originalStats === void 0 ? void 0 : _originalStats.last_checkin_time) || null
                  };
                  return {
                    round: round.round,
                    name: ((_originalRound = originalRound) === null || _originalRound === void 0 ? void 0 : _originalRound.name) || round.name || "\u8F6E\u6B21".concat(round.round),
                    time: checkTime,
                    check_time: checkDateTime.toISOString(),
                    start_time: checkDateTime.toISOString(),
                    end_time: endDateTime.toISOString(),
                    day_offset: dayOffset,
                    duration: round.duration || 60,
                    status: roundStatus,
                    across_day: _this12.selectedShift.across_day || false,
                    points: roundPoints,
                    stats: stats
                  };
                }));
              case 18:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee11, null, [[7, 14]]);
      }))();
    },
    refreshRoutePointsData: function refreshRoutePointsData() {
      var _this13 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12() {
        var res, routeData;
        return _regenerator.default.wrap(function _callee12$(_context12) {
          while (1) {
            switch (_context12.prev = _context12.next) {
              case 0:
                if (_this13.formData.route_id) {
                  _context12.next = 3;
                  break;
                }
                uni.showToast({
                  title: '请先选择路线',
                  icon: 'none'
                });
                return _context12.abrupt("return");
              case 3:
                _context12.prev = 3;
                uni.showLoading({
                  title: '刷新中...'
                });

                // 获取最新的路线点位数据
                _context12.next = 7;
                return _patrolApi.default.call({
                  name: 'patrol-route',
                  action: 'getRouteDetail',
                  data: {
                    params: {
                      route_id: _this13.formData.route_id,
                      with_points: true,
                      with_detail: true,
                      include_point_details: true,
                      force_refresh: true
                    }
                  }
                });
              case 7:
                res = _context12.sent;
                if (!(res.code === 0 && res.data)) {
                  _context12.next = 16;
                  break;
                }
                routeData = res.data; // 更新路线点位数据
                _this13.selectedRoute = _objectSpread(_objectSpread({}, _this13.selectedRoute), {}, {
                  pointsDetail: routeData.pointsDetail || []
                });

                // 标记点位数据已刷新
                _this13.pointsRefreshed = true;

                // 显示成功提示
                uni.hideLoading();
                uni.showToast({
                  title: '点位数据已更新',
                  icon: 'success'
                });
                _context12.next = 17;
                break;
              case 16:
                throw new Error(res.message || '获取最新点位数据失败');
              case 17:
                _context12.next = 24;
                break;
              case 19:
                _context12.prev = 19;
                _context12.t0 = _context12["catch"](3);
                console.error('刷新点位数据失败:', _context12.t0);
                uni.hideLoading();
                uni.showToast({
                  title: '刷新点位数据失败',
                  icon: 'none'
                });
              case 24:
              case "end":
                return _context12.stop();
            }
          }
        }, _callee12, null, [[3, 19]]);
      }))();
    },
    filterUserOptions: function filterUserOptions() {
      var searchText = this.searchUserName.toLowerCase();
      if (!searchText) {
        // 如果搜索框为空，显示所有用户
        this.filteredUsers = (0, _toConsumableArray2.default)(this.userOptions);
      } else {
        this.filteredUsers = this.userOptions.filter(function (user) {
          return user.nickname && user.nickname.toLowerCase().includes(searchText) && !user.nickname.startsWith('匿名');
        } // 确保搜索结果也遵循匿名用户过滤规则
        );
      }
    },
    selectUser: function selectUser(index) {
      var user = this.filteredUsers[index];
      if (user && user._id) {
        this.formData.user_id = user._id;
        this.selectedUser = user;
        // 更新userIndex以保持与之前逻辑的兼容性
        var originalIndex = this.userOptions.findIndex(function (u) {
          return u._id === user._id;
        });
        if (originalIndex !== -1) {
          this.userIndex = originalIndex;
        }
      } else {
        this.formData.user_id = '';
        this.selectedUser = null;
      }
      this.showUserSelect = false;
    },
    hideUserSelect: function hideUserSelect() {
      this.showUserSelect = false;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"]))

/***/ }),

/***/ 441:
/*!********************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/edit.vue?vue&type=style&index=0&lang=scss& ***!
  \********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=scss& */ 442);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 442:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/edit.vue?vue&type=style&index=0&lang=scss& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[435,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/patrol_pkg/task/edit.js.map
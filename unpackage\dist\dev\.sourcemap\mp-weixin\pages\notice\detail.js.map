{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/notice/detail.vue?fdc0", "webpack:///D:/Xwzc/pages/notice/detail.vue?cfb7", "webpack:///D:/Xwzc/pages/notice/detail.vue?59b8", "webpack:///D:/Xwzc/pages/notice/detail.vue?a588", "uni-app:///pages/notice/detail.vue", "webpack:///D:/Xwzc/pages/notice/detail.vue?67cc", "webpack:///D:/Xwzc/pages/notice/detail.vue?fd7b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "queryWhere", "collectionList", "loadMore", "contentdown", "contentrefresh", "contentnomore", "options", "enumConverter", "_id", "readCountUpdated", "categoryColors", "onLoad", "onShareAppMessage", "title", "imageUrl", "path", "success", "uni", "icon", "fail", "onShareTimeline", "query", "methods", "directUpdateReadCount", "uniCloud", "name", "id", "console", "isValidImageUrl", "lowerUrl", "loadData", "clear", "refreshData", "ensureString", "getImageArray", "safeJsonParse", "timeFormat", "updateViewCount", "previewAttachmentImage", "current", "urls", "shareToTimeline", "content", "showCancel", "confirmText", "getFileIcon", "formatFileSize", "openFile", "url", "filePath", "showMenu", "complete"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7JA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC0EpnB;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC,2BAEAC,sBACA;MACAC;MACAC;MAAA;MACA;MACAC;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;;IAEA;IACA;MACA;;MAEA;MACA;IACA;;IAEA;IACA;MACA;IACA;EACA;EACA;EACAC;IACA;IACA;IACA;IACA;;IAEA;IACA;MACA;MACAC;;MAEA;MACA;QACAC;MACA;IACA;IAGA;MACAD;MACAE;MACAD;MACAE;QACAC;UACAJ;UACAK;QACA;MACA;MACAC,0BACA;IACA;EACA;EACA;EACAC;IACA;IACA;IACA;IACA;;IAEA;IACA;MACA;MACAP;;MAEA;MACA;QACAC;MACA;IACA;IAGA;MACAD;MACAQ;MACAP;IACA;EACA;EACAQ;IACA;IACAC;MACA;QACA;MACA;;MAEA;MACA;MAEAC;QACAC;QACA1B;UACA2B;QACA;MACA,wBACA;QACAC;MACA;IACA;IACA;IACAC;MACA;;MAEA;MACA;MACA;;MAEA;MACA;QAAA;MAAA,MACAC,iCACAA,mCACAA;IACA;IACAC;MAAA;MAAA;MACA;QACA;UACAC;QACA;UACA;UACA;YACA;UACA,QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;UACA;QACA;UACAN;UACA;QACA;MACA;MACA;IACA;IACA;IACAO;MAAA;MACA;;MAEA;MACA;QACA;UAAA;QAAA;MACA;;MAEA;MACA;QACA;UACA;UACA;YACA;cAAA;YAAA;UACA;UACA;QACA;UACAP;UACA;QACA;MACA;MAEA;IACA;IACAQ;MACA;MAEA;QACA;MACA;MAEA;QACA;UACA;QACA;QACA;MACA;QACAR;QACA;MACA;IACA;IACAS;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;QACAV;QACA;MACA;;MAGA;MACA;;MAEA;MACAH;QACAC;QACA1B;UACA2B;QACA;MACA;QAEA;UAEA;YACA;YACA;cACA;;cAEA;cACA;gBACA3B;cACA;gBACAA;cACA;gBACAA;cACA;;cAEA;cACA;YACA,QACA;UACA;YACA4B;UACA;QACA;UACAA;QACA;MACA;QACAA;MACA;IACA;IACA;IACAW;MAAA;MACA;QACAX;QACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACAV;QACAsB;QACAC;MACA;IACA;IACA;IACAC;MACA;MACAxB;QACAJ;QACA6B;QACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;MAEA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA9B;UACAJ;UACAK;QACA;QACA;MACA;MAEAD;QAAAJ;MAAA;;MAEA;MACAI;QACA+B;QACAhC;UACA;YACA;;YAEA;YACAC;cACAgC;cACAC;cACAlC,6BACA;cACAG;gBACAQ;gBACAV;kBACAJ;kBACAK;gBACA;cACA;YACA;UACA;YACAD;cACAJ;cACAK;YACA;UACA;QACA;QACAC;UACAQ;UACAV;YACAJ;YACAK;UACA;QACA;QACAiC;UACAlC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACncA;AAAA;AAAA;AAAA;AAAk3B,CAAgB,m3BAAG,EAAC,C;;;;;;;;;;;ACAt4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/notice/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/notice/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=194b27bf&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/notice/detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=194b27bf&\"", "var components\ntry {\n  components = {\n    unicloudDb: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-cli-shared/components/unicloud-db\" */ \"@dcloudio/uni-cli-shared/components/unicloud-db.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    mpHtml: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/mp-html/components/mp-html/mp-html\" */ \"@/uni_modules/mp-html/components/mp-html/mp-html.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"101a5127-1\")\n  var m1 = m0 ? _vm.$getSSP(\"101a5127-1\", \"default\") : null\n  var m2 =\n    m0 && m1[\"error\"]\n      ? _vm.$getSSP(\"101a5127-1\", \"default\")[\"error\"].message || \"加载数据出错\"\n      : null\n  var m3 = m0 && !m1[\"error\"] ? _vm.$getSSP(\"101a5127-1\", \"default\") : null\n  var m4 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"]\n      ? _vm.$getSSP(\"101a5127-1\", \"default\")\n      : null\n  var m5 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"] && m4[\"data\"]\n      ? _vm.$getSSP(\"101a5127-1\", \"default\")[\"data\"].title || \"\"\n      : null\n  var m6 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"] && m4[\"data\"]\n      ? _vm.$getSSP(\"101a5127-1\", \"default\")[\"data\"].publisherName || \"未知\"\n      : null\n  var m7 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"] && m4[\"data\"]\n      ? _vm.timeFormat(_vm.$getSSP(\"101a5127-1\", \"default\")[\"data\"].createTime)\n      : null\n  var m8 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"] && m4[\"data\"]\n      ? _vm.$getSSP(\"101a5127-1\", \"default\")[\"data\"].readCount || 0\n      : null\n  var m9 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"] && m4[\"data\"]\n      ? _vm.$getSSP(\"101a5127-1\", \"default\")\n      : null\n  var m10 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"] && m4[\"data\"] && m9[\"data\"].category\n      ? _vm.categoryColors[\n          _vm.$getSSP(\"101a5127-1\", \"default\")[\"data\"].category\n        ] || \"#999\"\n      : null\n  var m11 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"] && m4[\"data\"] && m9[\"data\"].category\n      ? _vm.$getSSP(\"101a5127-1\", \"default\")[\"options\"].category_valuetotext[\n          _vm.$getSSP(\"101a5127-1\", \"default\")[\"data\"].category\n        ] || _vm.$getSSP(\"101a5127-1\", \"default\")[\"data\"].category\n      : null\n  var m12 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"] && m4[\"data\"]\n      ? _vm.$getSSP(\"101a5127-1\", \"default\")\n      : null\n  var m13 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"] && m4[\"data\"]\n      ? _vm.$getSSP(\"101a5127-1\", \"default\")\n      : null\n  var m14 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"] && m4[\"data\"]\n      ? _vm.$getSSP(\"101a5127-1\", \"default\")[\"data\"].attachments &&\n        _vm.$getSSP(\"101a5127-1\", \"default\")[\"data\"].attachments.length > 0\n      : null\n  var l0 =\n    m0 && !m1[\"error\"] && !m3[\"loading\"] && m4[\"data\"] && m14\n      ? _vm.__map(\n          _vm.$getSSP(\"101a5127-1\", \"default\")[\"data\"].attachments,\n          function (file, index) {\n            var $orig = _vm.__get_orig(file)\n            var m15 = _vm.getFileIcon(file.type)\n            var m16 = file.size ? _vm.formatFileSize(file.size) : null\n            return {\n              $orig: $orig,\n              m15: m15,\n              m16: m16,\n            }\n          }\n        )\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, file) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        file = _temp2.file\n      var _temp, _temp2\n      return _vm.openFile(file)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <unicloud-db ref=\"udb\" v-slot:default=\"{data, loading, error, options}\" :options=\"options\" \n      :collection=\"collectionList\" field=\"title,content,category,isTop,createTime,publisher,publisherName,readCount,images,attachments\" \n      :where=\"queryWhere\" :getone=\"true\" :manual=\"true\">\n      <!-- 错误状态 -->\n      <p-empty-state v-if=\"error\" \n        :text=\"error.message || '加载数据出错'\" \n        image=\"/static/error/error.png\"\n        buttonText=\"重试\"\n        @buttonClick=\"refreshData\">\n      </p-empty-state>\n      \n      <!-- 加载状态 -->\n      <view v-else-if=\"loading\" class=\"loading-box\">\n        <uni-load-more :contentText=\"loadMore\" status=\"loading\"></uni-load-more>\n      </view>\n      \n      <!-- 数据展示 -->\n      <view v-else-if=\"data\" class=\"content-box\">\n        <!-- 标题 -->\n        <view class=\"title-box\">\n          <text class=\"title\">{{data.title || ''}}</text>\n          \n          <!-- 元数据信息 - 重新布局 -->\n          <view class=\"meta-container\">\n            <view class=\"meta-left\">\n              <text class=\"publisher-info\">发布者: {{data.publisherName || '未知'}}</text>\n              <text class=\"time\">{{timeFormat(data.createTime)}}</text>\n              <text class=\"read-count\">阅读: {{data.readCount || 0}}</text>\n            </view>\n            <view class=\"meta-right\">\n              <text class=\"category\" v-if=\"data.category\" :style=\"{ backgroundColor: categoryColors[data.category] || '#999' }\">\n                {{options.category_valuetotext[data.category] || data.category}}\n              </text>\n              <text v-if=\"data.isTop\" class=\"top-tag\">置顶</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 内容 -->\n        <view class=\"content-wrapper\">\n          <view class=\"rich-content\">\n            <mp-html :content=\"data.content\" />\n          </view>\n        </view>\n        \n        <!-- 附件区域 -->\n        <view class=\"attachments-section\" v-if=\"data.attachments && data.attachments.length > 0\">\n          <view class=\"section-title\">\n            <uni-icons type=\"paperclip\" size=\"18\" color=\"#666\"></uni-icons>\n            <text class=\"title-text\">附件列表</text>\n          </view>\n          <view class=\"attachment-list\">\n            <view v-for=\"(file, index) in data.attachments\" :key=\"index\" class=\"attachment-item\" @click=\"openFile(file)\">\n              <view class=\"file-info\">\n                <uni-icons :type=\"getFileIcon(file.type)\" size=\"22\" color=\"#3a86ff\"></uni-icons>\n                <text class=\"file-name\">{{file.name}}</text>\n                <text class=\"file-size\" v-if=\"file.size\">{{formatFileSize(file.size)}}</text>\n              </view>\n              <view class=\"download-btn\">\n                <uni-icons type=\"download\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n      </view>\n    </unicloud-db>\n  </view>\n</template>\n\n<script>\n  // 由schema2code生成，包含校验规则和enum静态数据\n  import { enumConverter } from '../../js_sdk/validator/notice.js'\n  import PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n  const db = uniCloud.database()\n\n  export default {\n    components: {\n      PEmptyState\n    },\n    data() {\n      return {\n        queryWhere: '',\n        collectionList: \"notice\",\n        loadMore: {\n          contentdown: '上拉显示更多',\n          contentrefresh: '正在加载...',\n          contentnomore: '没有更多数据了'\n        },\n        options: {\n          // 将scheme enum 属性静态数据中的value转成text\n          ...enumConverter\n        },\n        _id: '',\n        readCountUpdated: false, // 添加标志，防止重复更新阅读数\n        // 分类颜色映射\n        categoryColors: {\n          '公告通知': '#4CAF50', // 绿色\n          '重要通知': '#FF5722', // 红色\n          '活动通知': '#2196F3', // 蓝色\n          '其他通知': '#F9AE3D'  // 黄色\n        }\n      }\n    },\n    onLoad(e) {\n      this._id = e.id\n      \n      // 设置查询条件\n      if (this._id) {\n        this.queryWhere = `_id==\"${this._id}\"`\n        \n        // 直接调用云函数更新阅读数\n        this.directUpdateReadCount()\n      }\n      \n      // 加载数据\n      this.$nextTick(() => {\n        this.loadData()\n      })\n    },\n    // 添加分享给朋友功能\n    onShareAppMessage(res) {\n      // 获取当前公告数据\n      let title = '株水小智公告'\n      let path = '/pages/notice/detail?id=' + this._id\n      let imageUrl = ''\n      \n      // 如果数据已加载，使用实际数据\n      if (this.$refs.udb && this.$refs.udb.dataList && this.$refs.udb.dataList.length > 0) {\n        const data = this.$refs.udb.dataList[0]\n        title = data.title || '株水小智公告'\n        \n        // 如果有图片，使用第一张图片作为分享图\n        if (data.images && data.images.length > 0) {\n          imageUrl = this.isValidImageUrl(data.images[0]) ? data.images[0] : ''\n        }\n      }\n      \n      \n      return {\n        title: title,\n        path: path,\n        imageUrl: imageUrl,\n        success: function(res) {\n          uni.showToast({\n            title: '分享成功',\n            icon: 'success'\n          })\n        },\n        fail: function(res) {\n        }\n      }\n    },\n    // 添加分享到朋友圈功能\n    onShareTimeline() {\n      // 获取当前公告数据\n      let title = '株水小智公告'\n      let query = 'id=' + this._id\n      let imageUrl = ''\n      \n      // 如果数据已加载，使用实际数据\n      if (this.$refs.udb && this.$refs.udb.dataList && this.$refs.udb.dataList.length > 0) {\n        const data = this.$refs.udb.dataList[0]\n        title = data.title || '株水小智公告'\n        \n        // 如果有图片，使用第一张图片作为分享图\n        if (data.images && data.images.length > 0) {\n          imageUrl = this.isValidImageUrl(data.images[0]) ? data.images[0] : ''\n        }\n      }\n      \n      \n      return {\n        title: title,\n        query: query,\n        imageUrl: imageUrl\n      }\n    },\n    methods: {\n      // 直接调用云函数更新阅读数，不依赖于组件加载\n      directUpdateReadCount() {\n        if (!this._id || this.readCountUpdated) {\n          return\n        }\n        \n        // 标记为已更新，防止重复更新\n        this.readCountUpdated = true\n        \n        uniCloud.callFunction({\n          name: 'update-notice-read-count',\n          data: {\n            id: this._id\n          }\n        }).then(res => {\n        }).catch(err => {\n          console.error('直接更新阅读数失败:', err)\n        })\n      },\n      // 验证图片URL是否有效\n      isValidImageUrl(url) {\n        if (!url || typeof url !== 'string') return false\n        \n        // 检查URL是否为有效的图片URL\n        const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']\n        const lowerUrl = url.toLowerCase()\n        \n        // 检查是否以有效的图片扩展名结尾或包含云存储路径\n        return validExtensions.some(ext => lowerUrl.endsWith(ext)) || \n               lowerUrl.includes('cloud://') || \n               lowerUrl.startsWith('https://') || \n               lowerUrl.startsWith('http://')\n      },\n      loadData(clear = true) {\n        if (this.$refs.udb) {\n          this.$refs.udb.loadData({\n            clear: true\n          }, () => {\n            // 数据加载成功后更新阅读计数（仅在未更新过的情况下）\n            if (!this.readCountUpdated) {\n              this.updateViewCount()\n            } else {\n            }\n          })\n        }\n      },\n      refreshData() {\n        // 刷新数据时不更新阅读计数\n        this.readCountUpdated = true\n        this.loadData()\n      },\n      // 确保值是字符串类型\n      ensureString(value) {\n        if (value === null || value === undefined) return '';\n        if (typeof value === 'string') return value;\n        if (typeof value === 'object') {\n          try {\n            return JSON.stringify(value);\n          } catch (e) {\n            console.error('转换对象为字符串失败:', e);\n            return '';\n          }\n        }\n        return String(value);\n      },\n      // 获取图片数组\n      getImageArray(images) {\n        if (!images) return [];\n        \n        // 如果已经是数组，返回它\n        if (Array.isArray(images)) {\n          return images.filter(img => this.isValidImageUrl(img));\n        }\n        \n        // 尝试解析JSON字符串\n        if (typeof images === 'string') {\n          try {\n            const parsed = JSON.parse(images);\n            if (Array.isArray(parsed)) {\n              return parsed.filter(img => this.isValidImageUrl(img));\n            }\n            return [];\n          } catch (e) {\n            console.error('解析图片JSON失败:', e);\n            return [];\n          }\n        }\n        \n        return [];\n      },\n      safeJsonParse(jsonString) {\n        if (!jsonString) return [];\n        \n        if (Array.isArray(jsonString)) {\n          return jsonString;\n        }\n        \n        try {\n          if (typeof jsonString === 'string') {\n            return JSON.parse(jsonString);\n          }\n          return [];\n        } catch (e) {\n          console.error('JSON解析错误:', e, jsonString);\n          return [];\n        }\n      },\n      timeFormat(timestamp) {\n        if (!timestamp) return '';\n        const date = new Date(timestamp);\n        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n      },\n      updateViewCount() {\n        if (!this._id) {\n          console.error('没有公告ID，无法更新阅读计数')\n          return\n        }\n        \n        \n        // 标记为已更新，防止重复更新\n        this.readCountUpdated = true\n        \n        // 调用云函数更新阅读计数\n        uniCloud.callFunction({\n          name: 'update-notice-read-count',\n          data: {\n            id: this._id  // 确保参数名为id，与云函数期望的一致\n          }\n        }).then(res => {\n          \n          if (res.result && res.result.code === 0) {\n            \n            try {\n              // 获取当前数据\n              if (this.$refs.udb && this.$refs.udb.dataList && this.$refs.udb.dataList.length > 0) {\n                const data = this.$refs.udb.dataList[0]\n                \n                // 直接使用云函数返回的计数\n                if (res.result.newReadCount !== undefined) {\n                  data.readCount = res.result.newReadCount\n                } else if (data.readCount !== undefined) {\n                  data.readCount++\n                } else {\n                  data.readCount = 1\n                }\n                \n                // 尝试强制刷新视图\n                this.$forceUpdate()\n              } else {\n              }\n            } catch (e) {\n              console.error('更新数据对象时出错:', e)\n            }\n          } else {\n            console.error('云函数返回错误:', res.result)\n          }\n        }).catch(err => {\n          console.error('更新阅读数失败:', err)\n        })\n      },\n      // 预览附件图片\n      previewAttachmentImage(current, urls) {\n        if (!urls || !Array.isArray(urls) || urls.length === 0) {\n          console.error('预览图片失败: 无效的图片列表');\n          return;\n        }\n        \n        // 确保所有URL都是字符串\n        const stringUrls = urls.map(url => this.ensureString(url));\n        \n        // 使用uni.previewImage预览图片\n        uni.previewImage({\n          current: current,\n          urls: stringUrls\n        });\n      },\n      // 手动触发分享到朋友圈\n      shareToTimeline() {\n        // 在微信环境中，提示用户点击右上角分享到朋友圈\n        uni.showModal({\n          title: '分享到朋友圈',\n          content: '请点击右上角\"...\"，然后选择\"分享到朋友圈\"',\n          showCancel: false,\n          confirmText: '知道了'\n        });\n      },\n      // 获取文件类型图标\n      getFileIcon(fileType) {\n        if (!fileType) return 'file';\n        \n        const type = fileType.toLowerCase();\n        const iconMap = {\n          'pdf': 'pdf',\n          'doc': 'file',\n          'docx': 'file',\n          'xls': 'file',\n          'xlsx': 'file',\n          'ppt': 'file',\n          'pptx': 'file',\n          'txt': 'file'\n        };\n        return iconMap[type] || 'file';\n      },\n      \n      // 格式化文件大小\n      formatFileSize(size) {\n        if (!size) return '';\n        \n        if (size < 1024) {\n          return size + 'B';\n        } else if (size < 1024 * 1024) {\n          return (size / 1024).toFixed(2) + 'KB';\n        } else {\n          return (size / (1024 * 1024)).toFixed(2) + 'MB';\n        }\n      },\n      \n      // 打开文件\n      openFile(file) {\n        if (!file || !file.url) {\n          uni.showToast({\n            title: '文件链接无效',\n            icon: 'none'\n          });\n          return;\n        }\n        \n        uni.showLoading({ title: '准备中...' });\n        \n        // 微信小程序环境下使用downloadFile和openDocument\n        uni.downloadFile({\n          url: file.url,\n          success: (res) => {\n            if (res.statusCode === 200) {\n              const tempFilePath = res.tempFilePath;\n              \n              // 打开文件预览\n              uni.openDocument({\n                filePath: tempFilePath,\n                showMenu: true,\n                success: () => {\n                },\n                fail: (err) => {\n                  console.error('打开文档失败', err);\n                  uni.showToast({\n                    title: '无法打开此类型文件',\n                    icon: 'none'\n                  });\n                }\n              });\n            } else {\n              uni.showToast({\n                title: '文件下载失败',\n                icon: 'none'\n              });\n            }\n          },\n          fail: (err) => {\n            console.error('文件下载失败', err);\n            uni.showToast({\n              title: '文件下载失败',\n              icon: 'none'\n            });\n          },\n          complete: () => {\n            uni.hideLoading();\n          }\n        });\n      },\n    }\n  }\n</script>\n\n<style>\n  .container {\n    padding: 24rpx;\n    min-height: 100vh;\n    background-color: #f8f9fc;\n  }\n  \n  .loading-box {\n    padding: 60rpx 0;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  \n  .error-box {\n    padding: 80rpx 0;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .error-image {\n    width: 200rpx;\n    height: 200rpx;\n    margin-bottom: 20rpx;\n  }\n  \n  .error-text {\n    font-size: 28rpx;\n    color: #999;\n    margin-bottom: 30rpx;\n  }\n  \n  .content-box {\n    background-color: #fff;\n    border-radius: 16rpx;\n    padding: 30rpx;\n    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n    animation: fadeIn 0.5s ease;\n  }\n  \n  @keyframes fadeIn {\n    from { opacity: 0; transform: translateY(10rpx); }\n    to { opacity: 1; transform: translateY(0); }\n  }\n  \n  .title-box {\n    margin-bottom: 24rpx;\n    border-bottom: 1px solid #eef0f6;\n    padding-bottom: 24rpx;\n  }\n  \n  .title {\n    font-size: 40rpx;\n    font-weight: bold;\n    color: #2b2e4a;\n    line-height: 1.4;\n    margin-bottom: 16rpx;\n    display: block;\n  }\n  \n  /* 新的元数据布局样式 */\n  .meta-container {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    flex-wrap: wrap;\n    margin-top: 20rpx;\n  }\n  \n  .meta-left {\n    display: flex;\n    flex-wrap: wrap;\n    align-items: center;\n    font-size: 24rpx;\n    color: #8a94a6;\n    line-height: 1.8;\n  }\n  \n  .meta-right {\n    display: flex;\n    align-items: center;\n    margin-top: 8rpx;\n  }\n  \n  .publisher-info {\n    margin-right: 16rpx;\n    padding-right: 16rpx;\n    border-right: 1px solid #eef0f6;\n  }\n  \n  .time {\n    margin-right: 16rpx;\n    padding-right: 16rpx;\n    border-right: 1px solid #eef0f6;\n  }\n  \n  .read-count {\n    display: flex;\n    align-items: center;\n  }\n  \n  /* 分类标签样式 */\n  .category {\n    padding: 6rpx 16rpx;\n    border-radius: 30rpx;\n    font-size: 24rpx;\n    color: white;\n    background-color: #999; /* 默认颜色 */\n    margin-right: 12rpx;\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n  }\n  \n  .top-tag {\n    background-color: #ff6a00;\n    color: white;\n    padding: 6rpx 16rpx;\n    border-radius: 30rpx;\n    font-size: 24rpx;\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n  }\n  \n  .content-wrapper {\n    margin-bottom: 30rpx;\n    padding: 16rpx;\n    background-color: #fdfdfd;\n    border-radius: 12rpx;\n  }\n  \n  .rich-content {\n    font-size: 30rpx;\n    line-height: 1.8;\n    color: #3d4b66;\n    padding: 10rpx;\n  }\n  \n  .images-list {\n    margin-top: 30rpx;\n  }\n  \n  .images-title {\n    font-size: 32rpx;\n    font-weight: bold;\n    margin-bottom: 16rpx;\n    display: block;\n    color: #2b2e4a;\n  }\n  \n  .images-grid {\n    display: flex;\n    flex-wrap: wrap;\n  }\n  \n  .content-image {\n    width: 31%;\n    height: 200rpx;\n    margin: 1%;\n    border-radius: 8rpx;\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n  }\n  \n  .attachments-section {\n    margin-top: 30rpx;\n    padding: 30rpx;\n    background-color: #fff;\n    border-radius: 16rpx;\n    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n    animation: fadeIn 0.5s ease;\n    animation-delay: 0.2s;\n    animation-fill-mode: both;\n  }\n  \n  .section-title {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20rpx;\n    padding-bottom: 16rpx;\n    border-bottom: 1px solid #eef0f6;\n  }\n  \n  .title-text {\n    font-size: 32rpx;\n    font-weight: bold;\n    margin-left: 10rpx;\n    color: #2b2e4a;\n  }\n  \n  .attachment-list {\n    display: flex;\n    flex-direction: column;\n    \n    /* #ifdef H5 */\n    width: 99%;\n    /* #endif */\n    \n    /* #ifdef MP-WEIXIN */\n    width: 94%;\n    /* #endif */\n  }\n  \n  .attachment-item {\n    width: 100%;\n    padding: 24rpx;\n    background-color: #f8f9fc;\n    border-radius: 12rpx;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    cursor: pointer;\n    margin-bottom: 16rpx;\n    transition: all 0.3s ease;\n  }\n  \n  .attachment-item:active {\n    background-color: #eef1f8;\n    transform: translateY(2rpx);\n  }\n  \n  .file-info {\n    display: flex;\n    align-items: center;\n    overflow: hidden;\n    \n    /* #ifdef H5 */\n    max-width: 95%;\n    /* #endif */\n    \n    /* #ifdef MP-WEIXIN */\n    max-width: 85%;\n    /* #endif */\n  }\n  \n  .file-name {\n    margin-left: 16rpx;\n    color: #3d4b66;\n    \n    /* #ifdef H5 */\n    white-space: normal;\n    word-break: break-all;\n    text-overflow: initial;\n    display: block;\n    padding: 0 10px;\n    /* #endif */\n    \n    /* #ifdef MP-WEIXIN */\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    max-width: 420rpx;\n    font-size: 28rpx;\n    /* #endif */\n  }\n  \n  .file-size {\n    margin-left: 16rpx;\n    font-size: 24rpx;\n    color: #8a94a6;\n    flex-shrink: 0;\n  }\n  \n  .download-btn {\n    width: 64rpx;\n    height: 64rpx;\n    border-radius: 50%;\n    background-color: rgba(58, 134, 255, 0.1);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-left: 20rpx;\n    transition: all 0.3s ease;\n  }\n  \n  .download-btn:active {\n    background-color: #3a86ff;\n    transform: scale(0.95);\n  }\n  \n  .download-btn:active .uni-icons {\n    color: #fff !important;\n  }\n  \n  /* 添加响应式设计 */\n  @media screen and (min-width: 768px) {\n    .content-box, .attachments-section {\n      max-width: 750rpx;\n      margin: 0 auto 30rpx;\n    }\n    \n    .file-name {\n      max-width: 600rpx;\n    }\n  }\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558444384\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
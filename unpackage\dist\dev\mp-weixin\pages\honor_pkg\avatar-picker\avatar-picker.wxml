<view class="avatar-picker-component data-v-7979a2f4"><view class="avatar-picker data-v-7979a2f4"><view class="avatar-card data-v-7979a2f4"><view class="avatar-preview data-v-7979a2f4"><image class="avatar-image data-v-7979a2f4" src="{{displayAvatar}}" mode="aspectFill"></image><block wx:if="{{avatarUrl&&avatarUrl!==defaultAvatar&&!uploading}}"><view data-event-opts="{{[['tap',[['clearAvatar',['$event']]]]]}}" class="clear-btn data-v-7979a2f4" catchtap="__e"><uni-icons vue-id="948986dc-1" type="close" size="14" color="#FFFFFF" class="data-v-7979a2f4" bind:__l="__l"></uni-icons></view></block></view><view class="avatar-actions data-v-7979a2f4"><button data-event-opts="{{[['tap',[['openBatchUpload',['$event']]]]]}}" class="action-btn upload-btn data-v-7979a2f4" bindtap="__e">上传头像</button><button data-event-opts="{{[['tap',[['openHistorySelect',['$event']]]]]}}" class="action-btn select-btn data-v-7979a2f4" bindtap="__e">选择头像</button></view></view><uni-popup vue-id="948986dc-2" type="dialog" data-ref="deleteConfirm" class="data-v-7979a2f4 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('948986dc-3')+','+('948986dc-2')}}" type="warning" title="删除确认" content="确定要删除当前头像吗？删除后将恢复为默认头像。" before-close="{{false}}" data-event-opts="{{[['^confirm',[['handleDelete']]],['^close',[['e0']]]]}}" bind:confirm="__e" bind:close="__e" class="data-v-7979a2f4" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup vue-id="948986dc-4" type="center" data-ref="historyPopup" class="data-v-7979a2f4 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="history-popup data-v-7979a2f4"><view class="popup-header data-v-7979a2f4"><text class="popup-title data-v-7979a2f4">选择头像</text><view data-event-opts="{{[['tap',[['closeHistorySelect',['$event']]]]]}}" class="popup-close data-v-7979a2f4" bindtap="__e"><uni-icons vue-id="{{('948986dc-5')+','+('948986dc-4')}}" type="close" size="16" color="#666666" class="data-v-7979a2f4" bind:__l="__l"></uni-icons></view></view><scroll-view class="history-list data-v-7979a2f4" scroll-y="{{true}}"><block wx:if="{{$root.g0>0}}"><view class="history-grid data-v-7979a2f4"><block wx:for="{{historyAvatars}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['history-item','data-v-7979a2f4',(item.url===avatarUrl)?'is-selected':'']}}"><view class="item-container data-v-7979a2f4"><view class="avatar-wrapper data-v-7979a2f4"><image class="history-avatar data-v-7979a2f4" src="{{item.url}}" mode="aspectFill" data-event-opts="{{[['tap',[['selectHistoryAvatar',['$0'],[[['historyAvatars','',index]]]]]]]}}" bindtap="__e"></image><view data-event-opts="{{[['tap',[['confirmDeleteHistory',['$0'],[[['historyAvatars','',index]]]]]]]}}" class="delete-btn data-v-7979a2f4" catchtap="__e"><uni-icons vue-id="{{('948986dc-6-'+index)+','+('948986dc-4')}}" type="close" size="14" color="#FFFFFF" class="data-v-7979a2f4" bind:__l="__l"></uni-icons></view></view><text class="history-name data-v-7979a2f4">{{item.userName||'未命名'}}</text></view></view></block></view></block><block wx:else><p-empty-state class="history-empty data-v-7979a2f4" vue-id="{{('948986dc-7')+','+('948986dc-4')}}" image="/static/empty/empty_data.png" text="暂无历史头像" tips="上传新头像后会保存在这里" bind:__l="__l"></p-empty-state></block></scroll-view></view></uni-popup><uni-popup vue-id="948986dc-8" type="center" data-ref="batchUploadPopup" class="data-v-7979a2f4 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="batch-upload-popup data-v-7979a2f4"><view class="popup-header data-v-7979a2f4"><text class="popup-title data-v-7979a2f4">上传头像</text><view data-event-opts="{{[['tap',[['closeBatchUpload',['$event']]]]]}}" class="popup-close data-v-7979a2f4" bindtap="__e"><uni-icons vue-id="{{('948986dc-9')+','+('948986dc-8')}}" type="close" size="14" color="#666666" class="data-v-7979a2f4" bind:__l="__l"></uni-icons></view></view><scroll-view class="batch-list data-v-7979a2f4" scroll-y="{{true}}"><view class="batch-table-header data-v-7979a2f4"><text class="col-name data-v-7979a2f4">姓名</text><text class="col-avatar data-v-7979a2f4">头像</text><text class="col-action data-v-7979a2f4">操作</text></view><block wx:for="{{batchList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="batch-table-row data-v-7979a2f4"><view class="col-name data-v-7979a2f4"><uni-easyinput bind:input="__e" vue-id="{{('948986dc-10-'+index)+','+('948986dc-8')}}" placeholder="请输入姓名" clearable="{{true}}" value="{{item.userName}}" data-event-opts="{{[['^input',[['__set_model',['$0','userName','$event',[]],[[['batchList','',index]]]]]]]}}" class="data-v-7979a2f4" bind:__l="__l"></uni-easyinput></view><view class="col-avatar data-v-7979a2f4"><view data-event-opts="{{[['tap',[['selectImage',[index]]]]]}}" class="upload-area data-v-7979a2f4" bindtap="__e"><block wx:if="{{item.url}}"><image class="preview-image data-v-7979a2f4" src="{{item.url}}" mode="aspectFill"></image></block><block wx:else><view class="upload-placeholder data-v-7979a2f4"><uni-icons vue-id="{{('948986dc-11-'+index)+','+('948986dc-8')}}" type="camera-filled" size="20" color="#999999" class="data-v-7979a2f4" bind:__l="__l"></uni-icons></view></block></view></view><view class="col-action data-v-7979a2f4"><button data-event-opts="{{[['tap',[['saveAvatar',[index]]]]]}}" class="{{['save-btn','data-v-7979a2f4',(!item.userName||!item.url||item.saving)?'btn-disabled':'']}}" bindtap="__e">{{''+(item.saving?'保存中...':'保存')+''}}</button></view></view></block></scroll-view><view class="popup-footer data-v-7979a2f4"><button data-event-opts="{{[['tap',[['addBatchItem',['$event']]]]]}}" class="add-btn data-v-7979a2f4" bindtap="__e"><uni-icons vue-id="{{('948986dc-12')+','+('948986dc-8')}}" type="plus" size="14" color="#FFFFFF" class="data-v-7979a2f4" bind:__l="__l"></uni-icons><text class="data-v-7979a2f4">添加一行</text></button><button data-event-opts="{{[['tap',[['closeBatchUpload',['$event']]]]]}}" class="close-btn data-v-7979a2f4" bindtap="__e">关闭</button></view></view></uni-popup></view></view>
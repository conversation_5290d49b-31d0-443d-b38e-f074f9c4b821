(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["uni_modules/uni-id-pages/pages/common/vendor"],{

/***/ 209:
/*!*******************************************************************!*\
  !*** D:/Xwzc/uni_modules/uni-id-pages/common/login-page.mixin.js ***!
  \*******************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _store = __webpack_require__(/*! @/uni_modules/uni-id-pages/common/store.js */ 47);
var _config = _interopRequireDefault(__webpack_require__(/*! @/uni_modules/uni-id-pages/config.js */ 43));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var mixin = {
  data: function data() {
    return {
      config: _config.default,
      uniIdRedirectUrl: '',
      isMounted: false
    };
  },
  onUnload: function onUnload() {},
  mounted: function mounted() {
    this.isMounted = true;
  },
  onLoad: function onLoad(e) {
    var _this = this;
    if (e.is_weixin_redirect) {
      uni.showLoading({
        mask: true
      });
      if (window.location.href.includes('#')) {
        // 将url通过 ? 分割获取后面的参数字符串 再通过 & 将每一个参数单独分割出来
        var paramsArr = window.location.href.split('?')[1].split('&');
        paramsArr.forEach(function (item) {
          var arr = item.split('=');
          if (arr[0] == 'code') {
            e.code = arr[1];
          }
        });
      }
      this.$nextTick(function (n) {
        // console.log(this.$refs.uniFabLogin);
        _this.$refs.uniFabLogin.login({
          code: e.code
        }, 'weixin');
      });
    }
    if (e.uniIdRedirectUrl) {
      this.uniIdRedirectUrl = decodeURIComponent(e.uniIdRedirectUrl);
    }
    if (getCurrentPages().length === 1) {
      uni.hideHomeButton();
      console.log('已隐藏：返回首页按钮');
    }
  },
  computed: {
    needAgreements: function needAgreements() {
      if (this.isMounted) {
        if (this.$refs.agreements) {
          return this.$refs.agreements.needAgreements;
        } else {
          return false;
        }
      }
    },
    agree: {
      get: function get() {
        if (this.isMounted) {
          if (this.$refs.agreements) {
            return this.$refs.agreements.isAgree;
          } else {
            return true;
          }
        }
      },
      set: function set(agree) {
        if (this.$refs.agreements) {
          this.$refs.agreements.isAgree = agree;
        } else {
          console.log('不存在 隐私政策协议组件');
        }
      }
    }
  },
  methods: {
    loginSuccess: function loginSuccess(e) {
      _store.mutations.loginSuccess(_objectSpread(_objectSpread({}, e), {}, {
        uniIdRedirectUrl: this.uniIdRedirectUrl
      }));
    }
  }
};
var _default = mixin;
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ })

}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uni-id-pages/pages/common/vendor.js.map
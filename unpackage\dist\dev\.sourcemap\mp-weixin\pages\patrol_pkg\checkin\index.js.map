{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/checkin/index.vue?2c99", "webpack:///D:/Xwzc/pages/patrol_pkg/checkin/index.vue?afe5", "webpack:///D:/Xwzc/pages/patrol_pkg/checkin/index.vue?6efb", "webpack:///D:/Xwzc/pages/patrol_pkg/checkin/index.vue?1bd0", "uni-app:///pages/patrol_pkg/checkin/index.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/checkin/index.vue?04f7", "webpack:///D:/Xwzc/pages/patrol_pkg/checkin/index.vue?6900"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pointInfo", "taskInfo", "currentLocation", "latitude", "longitude", "accuracy", "markers", "circles", "isInRange", "distance", "distanceToBoundary", "distanceText", "loading", "isLoading", "imageList", "maxImageCount", "formData", "remark", "round", "locationUpdateTimer", "currentRound", "shiftInfo", "isRoundValid", "roundErrorMessage", "roundStatusTimer", "mapContext", "locationErrorShown", "locationWarningShown", "isLocationAccuracyLow", "uploadRetryCount", "uploadMaxRetries", "lastInRange", "isFirstLocation", "lastUpdateTime", "minimumUpdateInterval", "locationChangeThreshold", "lastLocation", "isFollowMode", "showCamera", "flashMode", "cameraContext", "flashPopupMessage", "distancePopupMessage", "qrcodeScanned", "qrcodeVerified", "qrcodeData", "qrcodeVerifyResult", "valid", "title", "message", "code", "qrcodeAllowedDistance", "showScanner", "nextUnCheckedPoint", "showDistanceMessage", "lastValidLocation", "locationWatchId", "isAutoJumping", "computed", "userInfo", "isCurrentPointChecked", "isCurrentPointFirstUnchecked", "isRoundCompleted", "point", "isLastPoint", "isLastUnCheckedPoint", "unCheckedPoints", "currentPointIndex", "nextPointIndex", "formattedCurrentPointName", "formattedNextPointName", "onLoad", "onUnload", "clearInterval", "onShow", "onHide", "onReady", "setTimeout", "methods", "initLocationFromParams", "lat", "lng", "lastUpdated", "altitude", "speed", "address", "console", "parseAndValidateParams", "showErrorAndGoBack", "uni", "icon", "checkLocationPermission", "LocationUtils", "hasPermission", "granted", "content", "confirmText", "cancelText", "success", "settingRes", "initLocation", "type", "isHighAccuracy", "maxRetries", "retry<PERSON><PERSON><PERSON>", "fallbackToLowAccuracy", "location", "startLocationWatch", "Math", "fail", "relocate", "showLoading", "qualityInfo", "duration", "getPointInfo", "pointId", "PatrolApi", "name", "action", "point_id", "fields", "res", "errorMsg", "getTaskInfo", "taskId", "task_id", "level", "processRoundsData", "offsetDate", "today", "offsetDay", "notStartedOrActive", "completedOrExpired", "validateCurrentRound", "updateCircles", "color", "fillColor", "radius", "strokeWidth", "circleColor", "strokeColor", "calculateDistance", "getAccuracyColor", "showLocationError", "showLocationWarning", "updateMapMarkers", "id", "iconPath", "width", "height", "callout", "fontSize", "borderWidth", "bgColor", "padding", "display", "borderRadius", "textAlign", "anchorX", "anchorY", "onMapRegionChange", "onMarkerTap", "chooseImage", "toggleFlash", "toastMsg", "toggleScannerFlash", "getFlashIcon", "getFlashText", "closeCamera", "<PERSON><PERSON><PERSON><PERSON>", "doTakePhoto", "quality", "flash", "handleCameraError", "compressImages", "src", "resolve", "Promise", "previewImage", "urls", "current", "deleteImage", "uploadImage", "mask", "fileExt", "uniCloud", "filePath", "cloudPath", "cloudPathAsRealPath", "then", "catch", "reject", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleQRCodeSuccess", "verifyQRCode", "QRCodeUtil", "checkExpired", "doCheckIn", "fromQRCode", "qrcodeResult", "distanceToPoint", "isAllowed", "imageUrls", "uploadFailed", "i", "tempFile<PERSON>ath", "fileID", "currentRoundNumber", "params", "photos", "status", "checkin_method", "pid", "v", "t", "handleScanResult", "verifyResult", "setCurrentPoint", "onCheckinSuccess", "getApp", "goBack", "stopLocationWatch", "scanQRCode", "scope", "onScanCode", "result", "startTime", "e", "elapsedTime", "unCheckedPoint", "index", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;AC2RnnB;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QAAA;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QAAA;QACAC;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QAAA;QACAC;QACAC;QACAC;QACAC;QACAnD;MACA;MACAoD;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;;EACAC,0CACA;IACAC;MAAA;IAAA;EACA;IAEA;IACAC;MAAA;MACA;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;MACA;QAAA,OACA;MAAA,EACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;;MAEA;MACA;QAAA,OACAC;MAAA,EACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QAAA,OACA;MAAA,EACA;;MAEA;MACA,uCACAC;IACA;IAEA;IACAC;MAAA;MACA;QACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;EAAA,EACA;EACAC;IACA;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACA;IACA;MACAC;MACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IAAA;IACA;IACA;;IAEA;IACAC;MACA;QACA;QACA;UACA1E;UACAC;QACA;MACA;IACA;EACA;;EACA0E;IACA;IACAC;MACA;MACA;QACA;QACA;QACA;;QAEA;QACA,kCACAC,2BACAC;UAEA;UACA;YACA9E;YACAC;YACAC;YACA6E;UACA;;UAEA;UACA,uDACA;YACAC;YACAC;YACAC;UAAA,EACA;UAEAC;;UAEA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;MACA;MAEA;MACA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAC;QACAzC;QACA0C;MACA;MAEAb;QACA;MACA;IACA;IACA;IACAc;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAD;cAAA;gBAAAE;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAL;kBACAzC;kBACA+C;kBACAC;kBACAC;kBACAC;oBACA;sBACAT;wBACAS;0BAAA;4BAAA;8BAAA;gCAAA;kCAAA;oCAAA,KACAC;sCAAA;sCAAA;oCAAA;oCAAA;oCAAA,OACA;kCAAA;oCAAA;oCAAA;kCAAA;oCAEAV;sCACAzC;sCACA0C;oCACA;oCACAb;sCACA;oCACA;kCAAA;kCAAA;oCAAA;gCAAA;8BAAA;4BAAA;0BAAA,CAEA;0BAAA;4BAAA;0BAAA;0BAAA;wBAAA;sBACA;oBACA;sBACAA;wBACA;sBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAMA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAS;gBACAG;kBACAzC;kBACA0C;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAR;kBACAS;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBANAC;gBAQA;gBACA;;gBAEA;gBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEApB;gBACA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAG;kBACAzC;kBACA0C;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAiB;MAAA;MACA;QACA;QACAlB;UACAS;YACA;YACAZ;;YAEA;YACAG;cACA;cACA;gBACAtF;gBACAC;gBACAC;gBACA8E;gBACAC;gBACAF;cACA;;cAEA;cACA;gBACA;gBACA;cACA;cACA;cAAA,KACA;gBACA,0DACA;kBACA7E;gBAAA,EACA;;gBACAiF;cACA;cACA;cAAA,KACA;gBACA;cACA;;cAEA;cACA;;cAEA;cACA;;cAEA;cACA;;cAEA;cACA;gBACA;kBACAnF;kBACAC;gBACA;cACA;cACA;cAAA,KACA;gBACA;gBACA,kEACAwG;gBAEA;kBACA;oBACAzG;oBACAC;kBACA;gBACA;gBACA;cACA;YACA;UACA;UACAyG;YACAvB;YACA;UACA;QACA;MACA;QACAA;QACAG;UACAzC;UACA0C;QACA;MACA;IACA;IAEA;IACAoB;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA;gBAEA;kBACAtB;oBACAzC;kBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA4C;kBACAS;kBACAC;kBACAC;kBAAA;kBACAC;kBAAA;kBACAC;gBACA;cAAA;gBANAC;gBAQA;gBACAM,iEAEA;gBACA;;gBAEA;gBACA;gBACA;;gBAEA;gBACA;kBACA;oBACA7G;oBACAC;kBACA;;kBAEA;kBACA;gBACA;;gBAEA;gBACA;gBAEA;kBACAqF;kBACA;kBACAA;oBACAzC;oBACA0C;oBACAuB;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA3B;gBACA;kBACAG;kBACAA;oBACAzC;oBACA0C;oBACAuB;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAC;kBACAC;kBACAC;kBACAvH;oBACAwH;oBACA;oBACAC,SACA,6DACA,+CACA;oBACA;oBAAA;kBAEA;gBACA;cAAA;gBAbAC;gBAAA,MAeAA;kBAAA;kBAAA;gBAAA;gBACA;;gBAEA;gBACA;kBACA;kBACA;gBACA;gBAEA;kBACA1D;oBAAA;kBAAA;kBACA;oBACA;oBACA;oBACA;kBACA;gBACA;gBAEA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAAA,kCAEA0D;cAAA;gBAEAC;gBACApC;gBACAG;kBACAzC;kBACA0C;gBACA;gBAAA,kCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAgC;gBACApC;gBACAG;kBACAzC;kBACA0C;gBACA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAR;kBACAC;kBACAC;kBACAvH;oBACA8H;oBACA;oBACAC;kBACA;gBACA;cAAA;gBARAL;gBAUA;kBACA;;kBAEA;kBACA;oBACA;oBACA;;oBAEA;oBACA;sBACA;oBACA;kBACA;oBACA;oBACA;kBACA;gBACA;kBACAhC;oBACAzC;oBACA0C;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAzC;kBACA0C;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAqC;MAAA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;QACA;QACA7G;QACAA;;QAEA;QACAA;;QAEA;QACA;UACA;UACA;UACA8G;;UAEA;UACA;UACAC;UACA;UACAC;;UAEA;UACA;YACAhH;UACA;QACA;;QAEA;QACA;UACA;YACA;YACA;YACA;;YAEA;YACAA;YACAA;;YAEA;YACA;YACA;cACAA;YACA;cACA;cACA,yBACAA,+BACAA;gBACAA;cACA;gBACAA;cACA;YACA;cACA;cACA,yBACAA,+BACAA;gBACAA;cACA;gBACAA;cACA;YACA;UACA;YACAoE;;YAEA;YACA,yBACApE,+BACAA;cACAA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;UACA;UACA,yBACAA,+BACAA;YACAA;UACA;YACAA;UACA;YACAA;UACA;QACA;;QAEA;QACAA;QAEA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;MACA,8CACA;QAAA;MAAA,EACA;MACA,8CACA;QAAA;MAAA,EACA;;MAEA;MACAiH;QAAA;MAAA;MACAC;QAAA;MAAA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;;QAEA;QACA;UACA;YAAA;UAAA;UACA;YACA;YACA;YACA;YACA;YACA;UACA;QACA;QAEA;QACA;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;UACA;UAEA;YACA;YACA;UACA;YACA;YACA;YACA;YACA;UACA;YACA;YACA;YACA;YACA;YACA;UACA;YACA;YACA;YACA;YACA;YACA;UACA;QACA;UACA;UACA;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;UACA;YAAA;UAAA;UACA;YACA;YACA;YACA;UACA;QACA;;QAEA;QACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;UAAA;QAAA;QACA;UACA;UACA;UACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;QACAnI;QACAC;QACAmI;QACAC;QACAC;QAAA;QACAC;MACA;;MAEA;MACA;QACA;QAEA;UACAC;UACAH;QACA;UACAG;UACAH;QACA;UACAG;UACAH;QACA;UACAG;UACAH;QACA;UACAG;UACAH;QACA;UACAG;UACAH;QACA;QAEA;UACArI;UACAC;UACAmI;UACAC;UACAC;UAAA;UACAC;UACAE;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MAEA;QACA;QACA,+CACA,sBACA;UAAA1I;UAAAC;QAAA,EACA;;QAEA;QACA;;QAEA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;;QAEA;QACA;UACA;UACA;QACA;MAEA;QACAkF;MACA;IACA;IAEA;IACAwD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACAtD;UACAzC;UACA0C;UACAuB;QACA;QACApC;UACA;QACA;MACA;IACA;IAEA;IACAmE;MAAA;MACA;QACA;QACAvD;UACAzC;UACA0C;UACAuB;QACA;QACApC;UACA;QACA;MACA;IACA;IAEA;IACAoE;MAAA;QAAA;MACA;QACA;MACA;;MAEA;MACA,iEACA;QAAA;MAAA;MAEA;QACAC;QACA/I;QACAC;QACA4C;QACAmG;QACAC;QACAC;QACAC;UACAvD;UACAwC;UACAgB;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MAAA;MACA;;MAEA;MACA;QACA;MACA;;MAGA;IAgCA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;MACA3E;QACAzC;QACA0C;QACAuB;MACA;IACA;IAEA;IACAoD;MACA;MACA;;MAEA;MACA5E;QACAzC;QACA0C;QACAuB;MACA;IACA;IAEA;IACAqD;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAhF;UACAzC;UACA0C;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;;QAEA;QACAb;UACA;UACA;UAEAA;YACA;YACA;;YAEA;YACA;;YAEA;YACAA;cACA;cACA;;cAEA;cACAA;gBACA;cACA;YACA;UACA;QACA;MACA;QAAA;QACA;QACA;MACA;IACA;IAEA;IACA6F;MAAA;MACA;QACAC;QACAC;QACA1E;UACA;UACA;UACA;UACA;QACA;;QACAW;UACAvB;UACAG;YACAzC;YACA0C;UACA;QACA;MACA;IACA;IAEA;IACAmF;MACA;QACApF;UACAzC;UACA+C;UACAC;UACAE;YACA;cACAT;YACA;UACA;QACA;MACA;QACAA;UACAzC;UACA0C;QACA;MACA;IACA;IAEA;IACAoF;MAAA;MACA;QACA;UACArF;YACAsF;YACAJ;YACAzE;cACA8E;YACA;YACAnE;cACAmE;YACA;UACA;QACA;MACA;;MAEAC;QACA;MACA;IACA;IAEA;IACAC;MACAzF;QACA0F;QACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACA7F;oBACAzC;oBACAuI;kBACA;;kBAEA;kBACA;kBACA;kBACA;kBACA;kBACA;;kBAEA;kBACA;kBACA;oBACAC;kBACA;;kBAEA;kBACA;;kBAEA;kBACAC;oBACAC;oBACAC;oBACAC;oBAAA;oBACA1F;sBACAT;sBACAuF;oBACA;oBACAnE;sBACApB;;sBAEA;sBACA;wBACA;wBACAA;0BACAzC;0BACA0C;wBACA;;wBAEA;wBACAb;0BACA,8BACAgH,cACAC;wBACA;sBACA;wBACA;wBACArG;0BACAzC;0BACA0C;wBACA;wBACAqG;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACAvG;kBACAzC;kBACA0C;gBACA;;gBAEA;gBACAb;kBACA;gBACA;gBAAA;cAAA;gBAAA,IAMA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEAe;cAAA;gBAAA1F;gBACA;gBACA;;gBAEA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAuF;kBACAzC;kBACA0C;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAD;kBACAzC;kBACA0C;gBACA;gBAAA;cAAA;gBAKA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAuG;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA,sDACA,sBACA;QACA9L;QACAC;MACA,EACA;;MAEA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACA8L;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAhF;kBACAiF;gBACA;cAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9G;gBAAA,kCACA;kBACAvC;kBACAG;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoJ;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACAxI;kBAAA;gBAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACAc;kBACA;gBACA;gBACA;gBAAA;cAAA;gBAAA,MAMA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACAA;kBACA;gBACA;gBACA;;gBAEA;gBACAA;kBACA;gBACA;gBAAA;cAAA;gBAKA;gBACA2H,kDACA,yBACA;kBACArM;kBACAC;gBACA,EACA,EAEA;gBACAqM;gBAAA,KACAH;kBAAA;kBAAA;gBAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBACAG;gBAAA;gBAAA;cAAA;gBAEA;gBACAA;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAxJ;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,IAMA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA4B;kBACA;gBACA;gBACA;gBAAA;cAAA;gBAGA4H;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAhH;kBACAzC;kBACA0C;gBACA;gBACA;gBAAA;cAAA;gBAIA;gBACAgH;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAjH;kBACAzC;kBACAuI;gBACA;gBAEAoB;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAEAC;gBACApH;kBACAzC;kBACAuI;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAuB;gBACA;kBACAJ;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEApH;gBACAqH;gBACA;cAAA;gBAfAC;gBAAA;gBAAA;cAAA;gBAmBAnH;gBAAA,KAEAkH;kBAAA;kBAAA;gBAAA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBACA;gBACAjH;kBACAzC;kBACA+C;kBACAG;oBACA;sBACA;sBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAAA,MAEAwG;kBAAA;kBAAA;gBAAA;gBACA;gBACAjH;kBACAzC;kBACA+C;kBACAG;oBACA;sBACA;sBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAMA;gBACA6G,iGAEA;gBACAC;kBACAnF;kBACAN;kBACArG;kBACAwF;oBACAvG;oBACAC;oBACAC;kBACA;kBACA4M;kBACAhM;kBACAiM;kBACAC;gBACA,GAEA;gBACA;kBACAH;kBACAA;kBACAA;oBACAI;oBACAC;oBACAC;kBACA;gBACA;gBAEAhI;gBAEAG;kBACAzC;kBACAuI;gBACA;gBAAA;gBAAA,OAEAnE;kBACAC;kBACAC;kBACAvH;gBACA;cAAA;gBAJA0H;gBAMAhC;gBAEA;kBACA;;kBAEA;gBACA;kBACAA;oBACAzC;oBACA0C;oBACAuB;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAxB;gBACAH;gBACAG;kBACAzC;kBACA0C;kBACAuB;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;AACA;IACAsG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAMA1K;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAyC;gBACAG;kBACAzC;kBACA0C;kBACAuB;gBACA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACA3B;gBACAG;kBACAzC;kBACA0C;kBACAuB;gBACA;gBAAA;cAAA;gBAIA;gBACAE;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA1B;kBACAzC;kBACA0C;kBACAuB;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKAkF;kBACAhF;kBACAiF;gBACA;cAAA;gBAHAoB;gBAKA;gBACA;kBACAzK;kBACAC;kBACAC;kBACAC;kBACAnD;kBACA8C;gBACA;gBAAA,KAEA2K;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;;gBAEA;gBAAA,MACArG;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAAK;gBACA;kBACA;gBACA;cAAA;gBAGA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;gBACAhC;kBACAzC;kBACA0C;kBACAuB;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA3B;gBACAG;kBACAzC;kBACA0C;kBACAuB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwG;MAAA;MACA;MAEA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;MACA;;MAEA;MACA;QACA;UAAA;QAAA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;UAAA;QAAA;QACA;UACA;UACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;QAEA;UACA;UACA;;UAEA;UACAjI;YACAzC;YACA0C;YACAuB;YACAsE;UACA;;UAEA;UACA1G;YACA;YACA;cACA;YACA;UACA;QACA;UACA;UACAY;YACAzC;YACA0C;YACAuB;YACAsE;UACA;QACA;MACA;;MAEA;MACA;QACAjG;QACAqI;MACA;;MAEA;MACAlI;QACAoC;QACAN;QACArG;MACA;IACA;IAEA;IACA0M;MACAnI;IACA;IAEA;IACAoI;MACA;QACApI;QACAA;QACA;MACA;IACA;IAEA;IACAqI;MAAA;MACA;MACArI;QACAsI;QACA7H;UACA;QACA;QACAW;UACApB;YACAzC;YACA+C;YACAC;YACAE;cACA;gBACAT;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAuI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAxI;kBACAzC;kBACA0C;kBACAuB;gBACA;gBAAA;cAAA;gBAIA;gBACAxB;kBACAoB;oBACApB;kBACA;gBACA;;gBAEA;gBACAyI,wBAEA;gBACAzI;kBACAzC;kBACAuI;gBACA;;gBAEA;gBACA;kBACA1G;oBACA;oBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;gBAKAhC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAEA4C;kBACAzC;kBACA0C;kBACAuB;gBACA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAEAxB;kBACAzC;kBACA0C;kBACAuB;gBACA;gBAAA;cAAA;gBAIA;gBACAE;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAEA1B;kBACAzC;kBACA0C;kBACAuB;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKAkF;kBACAhF;kBACAiF;gBACA;cAAA;gBAHAoB;gBAAA;gBAAA,OAMA;cAAA;gBAEA;gBACA;kBACAzK;kBACAC;kBACAC;kBACAC;kBACAnD;kBACA8C;gBACA;gBAAA,KAEA2K;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;;gBAEA;gBAAA,MACArG;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAAK;gBACA;kBACA;gBACA;cAAA;gBAGA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;gBACAhC;kBACAzC;kBACA0C;kBACAuB;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAEAxB;kBACAzC;kBACA0C;kBACAuB;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAxB;gBACAH;gBACAG;kBACAzC;kBACA0C;kBACAuB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EAAA,2FAGAkH;IACA7I;IACAG;MACAzC;MACA0C;IACA;IACA;EACA,oFAGA;IACA;EACA,kGAGAwI;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAE;cAAA,MACAA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;gBAAA;cAAA;YAAA;cAEA3I;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA,sGAGA;IAAA;IACA;IACA;;IAEA;IACA;MACA;QACA;MACA;IACA;EACA,oGAGA;IACA;MACAhB;MACA;IACA;EACA,sGAGA;IAAA;IACA;MACA;QACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;MACA;QACA;QACA4J;UAAA,OACAC;QAAA,EACA;MACA;;MAEA;MACA;QACAD;UAAA,OACA;QAAA,EACA;;QAEA;QACA;UACA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;UACAA;UACAA;QACA;MACA;MAEA;IACA;MACA/I;MACA;IACA;EACA,4GAGA;IACA,gCACA,qCACA,sCACA,kCACA;MACA;IACA;IAEA;MACA,+CACA,sBACA;QACAnF;QACAC;MACA,EACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;MACAkF;MACA;IACA;EACA,sFAGA;IAAA;IACA;IACA;IAEA;MACAG;QACAzC;QACA0C;MACA;MACA;IACA;;IAEA;IACA;MACA6B;MACAM;MACA3G;IACA;;IAEA;IACA,2DACA,uBACA;IAEA8L;IACAA;IACAA;;IAEA;IACA;;IAEA;IACAvH;MACA8I;QAAA;MAAA;MACArI;QACAZ;QACA;MACA;;MACAuB;QACAvB;QACA;QACA;QACA;QACA;QACAG;UACAzC;UACA0C;QACA;MACA;IACA;EACA,8FAGA;IAAA;IACA;IACA;MACA;MACA;IACA;;IAEA;IACA;MACA;MACA;IACA;;IAEA;IACA;MACA;QAAA;MAAA;MACA;QACA;QACA;MACA;IACA;;IAEA;IACA;MACA;MACA;IACA;;IAEA;IACA;EACA,wFAGA;IAAA;IACA;IACA;MACA;MACA;IACA;;IAEA;IACA;MACA;MACA;IACA;;IAEA;IACA;MACA;QAAA;MAAA;MACA;QACA;QACA;MACA;IACA;;IAEA;IACA;MACA;MACA;IACA;;IAEA;IACA;MACA;MACA;IACA;;IAEA;IACA;EACA,8GAGAzC;IACA;IACAwC;MACAoB;QACApB;MACA;IACA;;IAEA;IACAA;MACAzC;MACA0C;MACAuB;IACA;EACA,4FAGAhE;IAAA;IACA;IACA;;IAEA;IACA4B;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACtlFA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/checkin/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/checkin/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=852b8e9a&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/checkin/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=852b8e9a&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.showCamera ? _vm.getFlashIcon() : null\n  var m1 = _vm.showCamera ? _vm.getFlashText() : null\n  var m2 = !_vm.showScanner ? _vm.getAccuracyColor() : null\n  var g0 =\n    !_vm.showScanner && _vm.currentLocation.accuracy\n      ? _vm.currentLocation.accuracy.toFixed(1)\n      : null\n  var m3 =\n    !_vm.showScanner && _vm.nextUnCheckedPoint\n      ? _vm.getNextPointDistanceText()\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        g0: g0,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"checkin-container\">\n\t\t<!-- 导航头 -->\n\t\t<view class=\"nav-header\">\n\t\t\t<view class=\"nav-left\">\n\t\t\t\t<view class=\"nav-back\" @click=\"goBack\">\n\t\t\t\t\t<uni-icons type=\"back\" size=\"20\" color=\"#000000\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<text class=\"nav-title\">打卡签到</text>\n\t\t</view>\n\n\t\t<!-- 加载状态显示 -->\n\t\t<view class=\"loading-container\" v-if=\"isLoading\">\n\t\t\t<view class=\"loading-spinner\">\n\t\t\t\t<view class=\"spinner-circle\"></view>\n\t\t\t</view>\n\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t</view>\n\t\t\n\t\t<!-- 相机组件 -->\n\t\t<view class=\"camera-container\" v-if=\"showCamera\">\n\t\t\t<camera \n\t\t\t\tid=\"nativeCamera\" \n\t\t\t\tdevice-position=\"back\" \n\t\t\t\t:flash=\"flashMode\" \n\t\t\t\t@error=\"handleCameraError\"\n\t\t\t\tstyle=\"width: 100%; height: calc(100vh - 200rpx);\">\n\t\t\t</camera>\n\t\t\t\n\t\t\t<view class=\"camera-controls\">\n\t\t\t\t<view class=\"control-item\" @click=\"toggleFlash\">\n\t\t\t\t\t<uni-icons :type=\"getFlashIcon()\" size=\"26\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t<text>{{ getFlashText() }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"control-item photo-btn\" @click=\"takePhoto\">\n\t\t\t\t\t<view class=\"photo-circle\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"control-item\" @click=\"closeCamera\">\n\t\t\t\t\t<uni-icons type=\"closeempty\" size=\"26\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t<text>取消</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 扫码相机组件 -->\n\t\t<view class=\"scanner-container\" v-if=\"showScanner\">\n\t\t\t<camera \n\t\t\t\tmode=\"scanCode\" \n\t\t\t\tdevice-position=\"back\"\n\t\t\t\t:flash=\"flashMode\"\n\t\t\t\t@scancode=\"onScanCode\"\n\t\t\t\t@error=\"handleCameraError\"\n\t\t\t\t:scan-area=\"[[0.1, 0.1, 0.8, 0.8]]\"\n\t\t\t\tauto-focus\n\t\t\t\tclass=\"scanner-camera\"\n\t\t\t>\n\t\t\t\t<!-- 扫码框UI -->\n\t\t\t\t<cover-view class=\"scan-frame\">\n\t\t\t\t\t<cover-view class=\"frame-line top\"></cover-view>\n\t\t\t\t\t<cover-view class=\"frame-line right\"></cover-view>\n\t\t\t\t\t<cover-view class=\"frame-line bottom\"></cover-view>\n\t\t\t\t\t<cover-view class=\"frame-line left\"></cover-view>\n\t\t\t\t\t<cover-view class=\"corner-box left-top\"></cover-view>\n\t\t\t\t\t<cover-view class=\"corner-box right-top\"></cover-view>\n\t\t\t\t\t<cover-view class=\"corner-box left-bottom\"></cover-view>\n\t\t\t\t\t<cover-view class=\"corner-box right-bottom\"></cover-view>\n\t\t\t\t\t<cover-view class=\"scan-line\"></cover-view>\n\t\t\t\t</cover-view>\n\t\t\t\t\n\t\t\t\t<!-- 提示文字 -->\n\t\t\t\t<cover-view class=\"scan-tips\">\n\t\t\t\t\t<cover-view class=\"tips-text\">将二维码放入框内，即可自动扫描</cover-view>\n\t\t\t\t</cover-view>\n\t\t\t\t\n\t\t\t\t<!-- 控制栏 -->\n\t\t\t\t<cover-view class=\"scanner-controls\">\n\t\t\t\t\t<cover-view class=\"control-item\" @tap=\"toggleScannerFlash\">\n\t\t\t\t\t\t<cover-view class=\"control-icon\">\n\t\t\t\t\t\t\t<cover-image src=\"/static/icons/flashlight.png\" v-if=\"flashMode === 'off'\"></cover-image>\n\t\t\t\t\t\t\t<cover-image src=\"/static/icons/flashlight-off.png\" v-else></cover-image>\n\t\t\t\t\t\t</cover-view>\n\t\t\t\t\t\t<cover-view class=\"control-text\">{{ flashMode === 'off' ? '打开照明' : '关闭照明' }}</cover-view>\n\t\t\t\t\t</cover-view>\n\t\t\t\t\t<cover-view class=\"control-item\" @tap=\"closeScanner\">\n\t\t\t\t\t\t<cover-view class=\"control-icon\">\n\t\t\t\t\t\t\t<cover-image src=\"/static/icons/close.png\"></cover-image>\n\t\t\t\t\t\t</cover-view>\n\t\t\t\t\t\t<cover-view class=\"control-text\">取消</cover-view>\n\t\t\t\t\t</cover-view>\n\t\t\t\t</cover-view>\n\t\t\t</camera>\n\t\t</view>\n\t\t\n\t\t<!-- 主要内容区域 -->\n\t\t<block v-else>\n\t\t\t<!-- 顶部信息区域 -->\n\t\t\t<view class=\"info-section\" :class=\"{'in-range': isInRange}\">\n\t\t\t\t<view class=\"info-content\">\n\t\t\t\t\t<!-- 状态指示器 -->\n\t\t\t\t\t<view class=\"status-indicator\" :class=\"{'status-indicator--active': isInRange}\">\n\t\t\t\t\t\t<view class=\"indicator-dot\"></view>\n\t\t\t\t\t\t<text class=\"status-text\">{{ isInRange ? '已进入打卡范围' : '请靠近打卡点' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t\t<!-- 距离信息 -->\n\t\t\t\t\t<view class=\"distance-info\">\n\t\t\t\t\t\t<view class=\"distance-value\">\n\t\t\t\t\t\t\t<text class=\"value\">{{ distanceText }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"round-badge\" v-if=\"currentRound\">\n\t\t\t\t\t\t\t<uni-icons type=\"calendar\" color=\"#8F959E\" size=\"12\"></uni-icons>\n\t\t\t\t\t\t\t<text>轮次 {{ currentRound.round }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 地图区域 -->\n\t\t\t<view class=\"map-section\">\n\t\t\t\t<map \n\t\t\t\t\tid=\"checkInMap\"\n\t\t\t\t\tclass=\"checkin-map\"\n\t\t\t\t\t:latitude=\"currentLocation.latitude\" \n\t\t\t\t\t:longitude=\"currentLocation.longitude\"\n\t\t\t\t\t:markers=\"markers\"\n\t\t\t\t\t:circles=\"circles\"\n\t\t\t\t\t:scale=\"19\"\n\t\t\t\t\t:show-location=\"true\"\n\t\t\t\t\t@regionchange=\"onMapRegionChange\"\n\t\t\t\t\t@markertap=\"onMarkerTap\"\n\t\t\t\t\tenable-zoom\n\t\t\t\t\tenable-scroll\n\t\t\t\t\tenable-rotate\n\t\t\t\t>\n\t\t\t\t\t<!-- 地图控制区域 -->\n\t\t\t\t\t<view class=\"map-controls\">\n\t\t\t\t\t\t<view class=\"control-btn\" \n\t\t\t\t\t\t\t@click=\"relocate\"\n\t\t\t\t\t\t\t:class=\"{'control-btn--active': isFollowMode}\">\n\t\t\t\t\t\t\t<uni-icons type=\"location\" size=\"20\" :color=\"isFollowMode ? '#34C759' : '#8F959E'\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- GPS精度显示 -->\n\t\t\t\t\t<view class=\"location-accuracy\">\n\t\t\t\t\t\t<view class=\"status-dot\" :style=\"{ background: getAccuracyColor() }\"></view>\n\t\t\t\t\t\t<text class=\"accuracy-text\">GPS精度: {{ currentLocation.accuracy ? currentLocation.accuracy.toFixed(1) : '0' }}米</text>\n\t\t\t\t\t</view>\n\t\t\t\t</map>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 内容区域 -->\n\t\t\t<scroll-view class=\"content-wrapper\" scroll-y>\n\t\t\t\t<!-- 当前点位信息 -->\n\t\t\t\t<view class=\"info-card current-point\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<view class=\"point-icon current\">\n\t\t\t\t\t\t\t<uni-icons type=\"location-filled\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"point-info\">\n\t\t\t\t\t\t\t<text class=\"point-title\">当前点位</text>\n\t\t\t\t\t\t\t<text class=\"point-name\">{{formattedCurrentPointName}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 已打卡状态 -->\n\t\t\t\t\t\t<view class=\"point-status checked\" v-if=\"isCurrentPointChecked\">\n\t\t\t\t\t\t\t<text class=\"status-text\">已打卡</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 未打卡状态 -->\n\t\t\t\t\t\t<view class=\"point-status pending\" v-else>\n\t\t\t\t\t\t\t<text class=\"status-text\">未打卡</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t<!-- 下个未打卡点位 - 如果有下个点位就显示 -->\n\t\t\t<view class=\"info-card next-point\" v-if=\"nextUnCheckedPoint\" @click=\"goToNextPoint\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<view class=\"point-icon next\">\n\t\t\t\t\t\t\t<uni-icons type=\"arrowright\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"point-info\">\n\t\t\t\t\t\t<text class=\"point-title\">下个点位</text>\n\t\t\t\t\t\t<text class=\"point-name\">{{formattedNextPointName}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"distance-badge\">\n\t\t\t\t\t\t<text class=\"distance-text\">{{getNextPointDistanceText()}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"card-details\">\n\t\t\t\t\t\t<text class=\"detail-text\">🚶‍♂️ 点击导航到下个点位{{nextUnCheckedPoint.qrcode_enabled ? ' · 🔍 需要扫码打卡' : ''}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t<!-- 当轮次真正完成时的提示 -->\n\t\t\t\t<view class=\"info-card completed\" v-if=\"isRoundCompleted && pointInfo\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<view class=\"point-icon completed\">\n\t\t\t\t\t\t\t<uni-icons type=\"checkbox-filled\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"point-info\">\n\t\t\t\t\t\t\t<text class=\"point-title\">🎉 恭喜</text>\n\t\t\t\t\t\t\t<text class=\"point-name\">本轮次已完成</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"completed-badge\">\n\t\t\t\t\t\t\t<text class=\"status-text\">完成打卡</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 当前是最后一个未打卡点位时的提示 -->\n\t\t\t\t<view class=\"info-card last-point\" v-if=\"isLastUnCheckedPoint && !isRoundCompleted && pointInfo\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<view class=\"point-icon last\">\n\t\t\t\t\t\t\t<uni-icons type=\"flag\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"point-info\">\n\t\t\t\t\t\t\t<text class=\"point-title\">最后点位</text>\n\t\t\t\t\t\t\t<text class=\"point-name\">完成后即可结束巡检</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"last-badge\">\n\t\t\t\t\t\t\t<text class=\"status-text\">最终点位</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"card-details\">\n\t\t\t\t\t\t<text class=\"detail-text\">🎯 这是本轮次的最后一个点位，完成打卡后即可结束巡检</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 错误提示区域 -->\n\t\t\t\t<view class=\"warning-area\" v-if=\"!isRoundValid || (!isInRange && !pointInfo.qrcode_enabled)\">\n\t\t\t\t\t<view class=\"warning-icon\">\n\t\t\t\t\t\t<uni-icons type=\"info\" size=\"24\" color=\"#FF9500\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- 轮次相关提示 -->\n\t\t\t\t\t<text class=\"warning-text\" v-if=\"!isRoundValid\">{{ roundErrorMessage }}</text>\n\t\t\t\t\t<!-- 轮次正常但不在范围内（仅GPS打卡模式） -->\n\t\t\t\t\t<text class=\"warning-text\" v-else>您当前不在打卡范围内，请靠近点位再进行打卡</text>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t\t\n\t\t\t<!-- 底部操作区域 -->\n\t\t\t<view class=\"action-section\">\n\t\t\t\t<view class=\"checkin-btn-container\">\n\t\t\t\t\t<!-- 扫码打卡按钮 - 仅在启用二维码时显示 -->\n\t\t\t\t\t<button \n\t\t\t\t\t\tclass=\"btn-checkin qrcode-button\" \n\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t'btn-checkin--active': qrcodeVerified || (isInRange && isRoundValid),\n\t\t\t\t\t\t\t'btn-disabled': loading || !isRoundValid || isCurrentPointChecked || isAutoJumping\n\t\t\t\t\t\t}\"\n\t\t\t\t\t\t@click=\"handleQRCodeClick\" \n\t\t\t\t\t\tv-if=\"pointInfo && pointInfo.qrcode_enabled === true\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<uni-icons type=\"scan\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t<text>{{ isAutoJumping ? '跳转中...' : '扫码打卡' }}</text>\n\t\t\t\t\t</button>\n\t\t\t\t\t\n\t\t\t\t\t<!-- GPS打卡按钮 - 仅在未启用二维码时显示 -->\n\t\t\t\t\t<button \n\t\t\t\t\t\tclass=\"btn-checkin gps-button\" \n\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t'btn-checkin--active': isInRange && isRoundValid,\n\t\t\t\t\t\t\t'btn-disabled': loading || !isRoundValid || isCurrentPointChecked || !isInRange || isAutoJumping\n\t\t\t\t\t\t}\"\n\t\t\t\t\t\t@click=\"handleGPSClick\" \n\t\t\t\t\t\tv-if=\"!pointInfo || pointInfo.qrcode_enabled === false\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<uni-icons type=\"paperplane\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t<text>{{ isAutoJumping ? '跳转中...' : 'GPS打卡' }}</text>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</block>\n\t\t\n\t\t<!-- 距离提示弹窗 -->\n\t\t<view v-if=\"showDistanceMessage\" class=\"custom-distance-message\">\n\t\t\t<text class=\"message-text\">{{ distancePopupMessage }}</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { mapState } from 'vuex';\nimport * as LocationUtils from '@/utils/location-services.js';\nimport PatrolApi from '@/utils/patrol-api.js';\n// 导入二维码工具类\nimport QRCodeUtil from '@/utils/qrcode-utils.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\tpointInfo: {}, // 当前点位信息\n\t\ttaskInfo: {}, // 当前任务信息\n\t\tcurrentLocation: {\n\t\t\t\tlatitude: 30.0, // 使用杭州附近坐标作为默认值\n\t\t\t\tlongitude: 120.0,\n\t\t\t\taccuracy: 0\n\t\t\t},\n\t\tmarkers: [], // 地图标记点\n\t\tcircles: [], // 范围圈\n\t\tisInRange: false, // 是否在范围内\n\t\tdistance: 0, // 到点位的距离（米）\n\t\tdistanceToBoundary: 0, // 到范围边界的距离\n\t\tdistanceText: '0m', // 距离文本，从计算属性移动到数据属性\n\t\tloading: false, // 加载状态\n\t\tisLoading: true, // 页面数据加载状态\n\t\timageList: [], // 图片列表\n\t\tmaxImageCount: 3, // 最大图片数量\n\t\tformData: {\n\t\t\tremark: '', // 备注信息\n\t\t\tround: 1 // 默认轮次\n\t\t},\n\t\tlocationUpdateTimer: null, // 定位更新定时器\n\t\tcurrentRound: null, // 当前轮次信息\n\t\tshiftInfo: null, // 班次信息\n\t\tisRoundValid: false, // 轮次是否有效\n\t\troundErrorMessage: '', // 轮次错误信息\n\t\troundStatusTimer: null, // 轮次状态更新定时器\n\t\tmapContext: null, // 地图实例\n\t\tlocationErrorShown: false, // 位置错误是否已显示\n\t\tlocationWarningShown: false, // 位置警告是否已显示\n\t\tisLocationAccuracyLow: false, // GPS精度是否低\n\t\tuploadRetryCount: 0, // 上传重试计数\n\t\tuploadMaxRetries: 3, // 最大重试次数\n\t\tlastInRange: false, // 上一次的范围状态\n\t\tisFirstLocation: true, // 添加标记，用于判断是否首次定位\n\t\tlastUpdateTime: 0, // 上次位置更新时间\n\t\tminimumUpdateInterval: 2000, // 最小位置更新间隔(毫秒)\n\t\tlocationChangeThreshold: 5, // 位置变化阈值(米)，变化小于此值不更新\n\t\tlastLocation: null, // 上次位置数据\n\t\tisFollowMode: true, // 默认开启跟随模式\n\t\tshowCamera: false, // 是否显示相机\n\t\tflashMode: 'off', // 闪光灯模式：on(拍照时闪光), off(关闭), torch(常亮模式)\n\t\tcameraContext: null, // 相机上下文\n\t\tflashPopupMessage: '', // 闪光灯弹窗消息\n\t\tdistancePopupMessage: '', // 添加距离提示消息\n\t\t\n\t\t// 新增二维码相关数据\n\t\tqrcodeScanned: false, // 是否已扫描二维码\n\t\tqrcodeVerified: false, // 二维码是否验证通过\n\t\tqrcodeData: null, // 扫描到的二维码数据\n\t\tqrcodeVerifyResult: { // 二维码验证结果\n\t\t\tvalid: false,\n\t\t\ttitle: '',\n\t\t\tmessage: '',\n\t\t\tcode: '',\n\t\t\tdata: null\n\t\t},\n\t\tqrcodeAllowedDistance: 30, // 二维码打卡允许的距离（米）\n\t\tshowScanner: false, // 是否显示扫码界面\n\t\t\n\t\t// 新增：信息展示相关数据\n\t\tnextUnCheckedPoint: null, // 下个未打卡点位信息\n\t\tshowDistanceMessage: false,\n\t\tlastValidLocation: null, // 最后有效位置\n\t\tlocationWatchId: null,\n\t\tisAutoJumping: false, // 是否正在自动跳转中\n\t};\n\t},\n\tcomputed: {\n\t...mapState({\n\t\tuserInfo: state => state.user.userInfo\n\t}),\n\t\n\t// 判断当前点位是否已打卡\n\tisCurrentPointChecked() {\n\t\tif (!this.currentRound || !this.currentRound.points || !this.pointInfo) return false;\n\t\tconst currentPoint = this.currentRound.points.find(p => p.point_id === this.pointInfo._id);\n\t\treturn currentPoint && currentPoint.status && currentPoint.status > 0;\n\t},\n\t\n\t// 判断当前点位是否是第一个未打卡的点位\n\tisCurrentPointFirstUnchecked() {\n\t\tif (!this.currentRound || !this.currentRound.points || !this.pointInfo) return false;\n\t\tconst firstUnCheckedPoint = this.currentRound.points.find(point => \n\t\t\t!point.status || point.status === 0\n\t\t);\n\t\treturn firstUnCheckedPoint && firstUnCheckedPoint.point_id === this.pointInfo._id;\n\t},\n\t\n\t// 判断整个轮次是否已完成\n\tisRoundCompleted() {\n\t\tif (!this.currentRound || !this.currentRound.points || !Array.isArray(this.currentRound.points)) {\n\t\t\treturn false;\n\t\t}\n\t\t\n\t\t// 检查是否所有点位都已打卡\n\t\tconst allPointsCompleted = this.currentRound.points.every(point => \n\t\t\tpoint.status && point.status > 0\n\t\t);\n\t\t\n\t\t// 只有所有点位都完成且当前点位也已打卡时，才算轮次完成\n\t\treturn allPointsCompleted && this.isCurrentPointChecked;\n\t},\n\t\n\t// 判断当前点位是否是最后一个点位（不管是否已打卡）\n\tisLastPoint() {\n\t\tif (!this.currentRound || !this.currentRound.points || !Array.isArray(this.currentRound.points) || !this.pointInfo) {\n\t\t\treturn false;\n\t\t}\n\t\t\n\t\t// 找到当前点位在轮次中的位置\n\t\tconst currentPointIndex = this.currentRound.points.findIndex(p => p.point_id === this.pointInfo._id);\n\t\tif (currentPointIndex === -1) return false;\n\t\t\n\t\t// 检查当前点位后面是否还有其他点位\n\t\treturn currentPointIndex === this.currentRound.points.length - 1;\n\t},\n\t\n\t// 判断当前点位是否是最后一个未打卡的点位\n\tisLastUnCheckedPoint() {\n\t\tif (!this.currentRound || !this.currentRound.points || !this.pointInfo) return false;\n\t\t\n\t\t// 获取所有未打卡的点位\n\t\tconst unCheckedPoints = this.currentRound.points.filter(point => \n\t\t\t!point.status || point.status === 0\n\t\t);\n\t\t\n\t\t// 必须是最后一个未打卡的点位才显示提示\n\t\treturn unCheckedPoints.length === 1 && \n\t\t\t   unCheckedPoints[0].point_id === this.pointInfo._id;\n\t},\n\t\n\t// 获取当前点位序号\n\tcurrentPointIndex() {\n\t\tif (!this.currentRound || !this.currentRound.points || !this.pointInfo) {\n\t\t\treturn null;\n\t\t}\n\t\tconst index = this.currentRound.points.findIndex(p => p.point_id === this.pointInfo._id);\n\t\treturn index !== -1 ? index + 1 : null;\n\t},\n\t// 获取下个点位序号\n\tnextPointIndex() {\n\t\tif (!this.currentRound || !this.currentRound.points || !this.nextUnCheckedPoint) {\n\t\t\treturn null;\n\t\t}\n\t\tconst index = this.currentRound.points.findIndex(p => p.point_id === this.nextUnCheckedPoint.point_id);\n\t\treturn index !== -1 ? index + 1 : null;\n\t},\n\t// 格式化当前点位名称（带序号）\n\tformattedCurrentPointName() {\n\t\tif (!this.pointInfo) return '获取中...';\n\t\tconst index = this.currentPointIndex;\n\t\treturn index ? `${index}. ${this.pointInfo.name}` : this.pointInfo.name;\n\t},\n\t// 格式化下个点位名称（带序号）\n\tformattedNextPointName() {\n\t\tif (!this.nextUnCheckedPoint) return '未知点位';\n\t\tconst index = this.nextPointIndex;\n\t\treturn index ? `${index}. ${this.nextUnCheckedPoint.name}` : this.nextUnCheckedPoint.name;\n\t}\n\t},\n\tonLoad(options) {\n\t\t// 设置加载状态\n\t\tthis.isLoading = true;\n\n\t\t// 优先使用从巡视首页传递的位置信息作为初始定位\n\t\tthis.initLocationFromParams(options);\n\n\t\t// 获取传递的参数\n\t\tthis.parseAndValidateParams(options);\n\t\t\n\t\t// 检查并请求位置权限\n\t\tthis.checkLocationPermission();\n\t},\n\tonUnload() {\n\t// 清除位置监听和定时器\n\tthis.stopLocationWatch();\n\tif (this.locationUpdateTimer) {\n\t\tclearInterval(this.locationUpdateTimer);\n\t\tthis.locationUpdateTimer = null;\n\t}\n\t// 清除轮次状态更新定时器\n\tthis.stopRoundStatusTimer();\n\t// 重置自动跳转标记\n\tthis.isAutoJumping = false;\n\t},\n\tonShow() {\n\t\t// 启动轮次状态更新定时器\n\t\tthis.startRoundStatusTimer();\n\t},\n\tonHide() {\n\t\t// 停止轮次状态更新定时器\n\t\tthis.stopRoundStatusTimer();\n\t},\n\tonReady() {\n\t\t// 在页面准备好后获取地图实例\n\t\tthis.mapContext = uni.createMapContext('checkInMap', this);\n\t\t\n\t\t// 添加地图初始化后延迟设置用户位置\n\t\tsetTimeout(() => {\n\t\t\tif (this.mapContext && this.currentLocation.latitude && this.currentLocation.latitude !== 30.0) {\n\t\t\t\t// 只有当获取到真实位置时才移动地图（避免从默认位置30.0, 120.0跳转）\n\t\t\t\tthis.mapContext.moveToLocation({\n\t\t\t\t\tlatitude: this.currentLocation.latitude,\n\t\t\t\t\tlongitude: this.currentLocation.longitude\n\t\t\t\t});\n\t\t\t}\n\t\t}, 500); // 延迟500ms执行，确保地图已经准备好\n\t},\n\tmethods: {\n\t// 从URL参数初始化位置信息\n\tinitLocationFromParams(options) {\n\t\t// 如果巡视首页传递了位置参数，保存为最后有效位置\n\t\tif (options && options.lat && options.lng) {\n\t\t\tconst lat = parseFloat(options.lat);\n\t\t\tconst lng = parseFloat(options.lng);\n\t\t\tconst accuracy = parseFloat(options.accuracy) || 0;\n\t\t\t\n\t\t\t// 验证坐标的有效性\n\t\t\tif (!isNaN(lat) && !isNaN(lng) && \n\t\t\t\tlat >= -90 && lat <= 90 && \n\t\t\t\tlng >= -180 && lng <= 180) {\n\t\t\t\t\n\t\t\t\t// 保存为最后有效位置\n\t\t\t\tthis.lastValidLocation = {\n\t\t\t\t\tlatitude: lat,\n\t\t\t\t\tlongitude: lng,\n\t\t\t\t\taccuracy: accuracy,\n\t\t\t\t\tlastUpdated: Date.now()\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 同时设置为当前位置\n\t\t\t\tthis.currentLocation = {\n\t\t\t\t\t...this.lastValidLocation,\n\t\t\t\t\taltitude: 0,\n\t\t\t\t\tspeed: 0,\n\t\t\t\t\taddress: ''\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tconsole.log('保存巡视首页位置作为备用:', lat, lng, '精度:', accuracy);\n\t\t\t\t\n\t\t\t\t// 立即更新地图标记和精度圈\n\t\t\t\tthis.updateMapMarkers();\n\t\t\t\tthis.updateCircles();\n\t\t\t}\n\t\t}\n\t},\n\t\n\t// 解析和验证URL参数\n\tparseAndValidateParams(options) {\n\t\tif (!options) {\n\t\t\tthis.showErrorAndGoBack('参数错误');\n\t\t\treturn;\n\t\t}\n\t\t\n\t\tlet pointId = options.point_id;\n\t\tlet taskId = options.task_id;\n\t\tlet round = options.round;\n\t\t\n\t\t// 验证参数存在性\n\t\tif (!pointId || !taskId) {\n\t\t\tthis.showErrorAndGoBack('参数错误：缺少必要参数');\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 如果有轮次参数，解析为整数\n\t\tif (round) {\n\t\t\tthis.formData.round = parseInt(round) || 1;\n\t\t}\n\t\t\n\t\t// 获取点位和任务信息\n\t\tthis.getPointInfo(pointId).then(() => {\n\t\t\treturn this.getTaskInfo(taskId);\n\t\t}).finally(() => {\n\t\t\t// 不管是否成功，加载完成后将加载状态设为false\n\t\t\tthis.isLoading = false;\n\t\t});\n\t},\n\t\n\t// 显示错误并返回\n\tshowErrorAndGoBack(message) {\n\t\tuni.showToast({\n\t\t\ttitle: message || '出错了',\n\t\t\ticon: 'none'\n\t\t});\n\t\t\n\t\tsetTimeout(() => {\n\t\t\tthis.goBack();\n\t\t}, 1500);\n\t},\n\t// 检查位置权限\n\tasync checkLocationPermission() {\n\t\ttry {\n\t\t\t// 使用新的权限检查方法\n\t\t\tconst hasPermission = await LocationUtils.checkLocationPermission();\n\t\t\tif (!hasPermission) {\n\t\t\t\tconst granted = await LocationUtils.requestLocationPermission();\n\t\t\t\tif (!granted) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '温馨提示',\n\t\t\t\t\t\tcontent: '请授权位置权限，否则无法使用打卡功能',\n\t\t\t\t\t\tconfirmText: '去设置',\n\t\t\t\t\t\tcancelText: '返回',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\t\t\tsuccess: async (settingRes) => {\n\t\t\t\t\t\t\t\t\t\tif (settingRes.authSetting['scope.userLocation']) {\n\t\t\t\t\t\t\t\t\t\t\tawait this.initLocation();\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '未获得位置权限',\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\t\t\tthis.goBack();\n\t\t\t\t\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tthis.goBack();\n\t\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 初始化位置\n\t\t\tawait this.initLocation();\n\t\t\t\n\t\t} catch (error) {\n\t\t\tconsole.error('位置权限检查失败:', error);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '位置权限检查失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t},\n\t\n\t// 初始化位置\n\tasync initLocation() {\n\t\ttry {\n\t\t\t// 直接获取当前位置，避免通过relocate方法的地图跳转\n\t\t\tconst location = await LocationUtils.getCurrentLocation({\n\t\t\t\ttype: 'gcj02',\n\t\t\t\tisHighAccuracy: true,\n\t\t\t\tmaxRetries: 2,\n\t\t\t\tretryDelay: 1000,\n\t\t\t\tfallbackToLowAccuracy: false\n\t\t\t});\n\t\t\t\n\t\t\t// 直接更新位置信息，避免地图跳转\n\t\t\tthis.currentLocation = location;\n\t\t\t\n\t\t\t// 更新地图视图\n\t\t\tthis.updateMapMarkers();\n\t\t\tthis.updateCircles();\n\t\t\t\n\t\t\t// 重新计算距离\n\t\t\tthis.calculateDistance();\n\t\t\t\n\t\t\t// 开始位置监听\n\t\t\tawait this.startLocationWatch();\n\t\t} catch (error) {\n\t\t\tconsole.error('初始化位置失败:', error);\n\t\t\t// 如果初始化失败，回退到relocate方法\n\t\t\ttry {\n\t\t\t\tawait this.relocate(true);\n\t\t\t\tawait this.startLocationWatch();\n\t\t\t} catch (fallbackError) {\n\t\t\t\tconsole.error('回退定位也失败:', fallbackError);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '初始化位置失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t},\n\t\n\t// 初始化监听位置变化\n\tstartLocationWatch() {\n\t\ttry {\n\t\t\t// 直接使用uni.onLocationChange接口监听位置变化\n\t\t\tuni.startLocationUpdate({\n\t\t\t\tsuccess: () => {\n\t\t\t\t\t// 成功开启定位更新\n\t\t\t\t\tconsole.log('位置监听已开启');\n\t\t\t\t\t\n\t\t\t\t\t// 监听位置变化事件\n\t\t\t\t\tuni.onLocationChange((res) => {\n\t\t\t\t\t\t// 更新位置信息\n\t\t\t\t\t\tconst newLocation = {\n\t\t\t\t\t\t\tlatitude: res.latitude,\n\t\t\t\t\t\t\tlongitude: res.longitude,\n\t\t\t\t\t\t\taccuracy: res.accuracy || 0,\n\t\t\t\t\t\t\taltitude: res.altitude || 0,\n\t\t\t\t\t\t\tspeed: res.speed || 0,\n\t\t\t\t\t\t\tlastUpdated: Date.now()\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\t// 如果新位置精度较好，更新为当前位置\n\t\t\t\t\t\tif (res.accuracy <= 100) {\n\t\t\t\t\t\t\tthis.currentLocation = newLocation;\n\t\t\t\t\t\t\tthis.lastValidLocation = newLocation;\n\t\t\t\t\t\t} \n\t\t\t\t\t\t// 如果新位置精度很差，且有最后有效位置，使用最后有效位置\n\t\t\t\t\t\telse if (res.accuracy > 100 && this.lastValidLocation) {\n\t\t\t\t\t\t\tthis.currentLocation = {\n\t\t\t\t\t\t\t\t...this.lastValidLocation,\n\t\t\t\t\t\t\t\taccuracy: res.accuracy // 保持显示当前实际精度\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tconsole.log('使用最后有效位置作为备选');\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 其他情况，还是使用新位置\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthis.currentLocation = newLocation;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 立即更新地图标记\n\t\t\t\t\t\tthis.updateMapMarkers();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 立即更新精度圈\n\t\t\t\t\t\tthis.updateCircles();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 重新计算距离\n\t\t\t\t\t\tthis.calculateDistance();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果处于跟随模式，移动地图到当前位置\n\t\t\t\t\t\tif (this.isFollowMode && this.mapContext) {\n\t\t\t\t\t\t\tthis.mapContext.moveToLocation({\n\t\t\t\t\t\t\t\tlatitude: this.currentLocation.latitude,\n\t\t\t\t\t\t\t\tlongitude: this.currentLocation.longitude\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 首次定位时移动地图到当前位置（但要确保不是从默认位置跳转）\n\t\t\t\t\t\telse if (this.isFirstLocation && this.mapContext) {\n\t\t\t\t\t\t\t// 检查是否是有意义的位置变化（不是从默认的30.0, 120.0变化）\n\t\t\t\t\t\t\tconst isSignificantChange = Math.abs(res.latitude - 30.0) > 0.01 || \n\t\t\t\t\t\t\t\t\t\t\t\t\t  Math.abs(res.longitude - 120.0) > 0.01;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (isSignificantChange) {\n\t\t\t\t\t\t\t\tthis.mapContext.moveToLocation({\n\t\t\t\t\t\t\t\t\tlatitude: this.currentLocation.latitude,\n\t\t\t\t\t\t\t\t\tlongitude: this.currentLocation.longitude\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.isFirstLocation = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('启动位置更新失败:', err);\n\t\t\t\t\tthis.showLocationError('无法启动位置监听: ' + (err.errMsg || JSON.stringify(err)));\n\t\t\t\t}\n\t\t\t});\n\t\t} catch (error) {\n\t\t\tconsole.error('初始化位置监听出错:', error);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '位置监听失败，请检查定位权限',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t},\n\t\n\t// 重新定位\n\tasync relocate(showLoading = true) {\n\t\ttry {\n\t\t\tif (showLoading) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '定位中...'\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 使用高精度位置获取方法，不允许降级到低精度\n\t\t\tconst location = await LocationUtils.getLocationWithChecks({\n\t\t\t\ttype: 'gcj02',\n\t\t\t\tisHighAccuracy: true,\n\t\t\t\tmaxRetries: 2,                 // 最多尝试2次\n\t\t\t\tretryDelay: 1000,              // 重试间隔1秒\n\t\t\t\tfallbackToLowAccuracy: false   // 不允许降级到低精度\n\t\t\t});\n\t\t\t\n\t\t\t// 位置质量评估\n\t\t\tconst qualityInfo = LocationUtils.getSignalQuality(location.accuracy);\n\t\t\t\n\t\t\t// 更新位置信息\n\t\t\tthis.currentLocation = location;\n\t\t\t\n\t\t\t// 更新地图视图\n\t\t\tthis.updateMapMarkers();\n\t\t\tthis.updateCircles();\n\t\t\t\n\t\t\t// 主动点击重新定位时，强制移动地图视角到当前位置\n\t\t\tif (this.mapContext) {\n\t\t\t\tthis.mapContext.moveToLocation({\n\t\t\t\t\tlatitude: this.currentLocation.latitude,\n\t\t\t\t\tlongitude: this.currentLocation.longitude\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 重新开启跟随模式\n\t\t\t\tthis.isFollowMode = true;\n\t\t\t}\n\t\t\t\n\t\t\t// 重新计算距离\n\t\t\tthis.calculateDistance();\n\t\t\t\n\t\t\tif (showLoading) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\t// 显示统一的定位成功提示\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '定位成功',\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 1500\n\t\t\t\t});\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('重新定位失败:', error);\n\t\t\tif (showLoading) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '重新定位失败: ' + (error.message || '请检查GPS是否开启'),\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t},\n\t\n\t// 获取点位信息\n\tasync getPointInfo(pointId) {\n\t\tif (!pointId) {\n\t\t\treturn;\n\t\t}\n\t\t\n\t\ttry {\n\t\t\tconst res = await PatrolApi.call({\n\t\t\t\tname: 'patrol-point',\n\t\t\t\taction: 'getPointDetail',\n\t\t\t\tdata: {\n\t\t\t\t\tpoint_id: pointId,\n\t\t\t\t\t// 🔥 优化：打卡页面只需要点位基本信息\n\t\t\t\t\tfields: [\n\t\t\t\t\t\t'_id', 'name', 'location', 'latitude', 'longitude', 'range',\n\t\t\t\t\t\t'qrcode_enabled', 'qrcode_required', 'status',\n\t\t\t\t\t\t'address', 'description', 'remark'\n\t\t\t\t\t\t// 🚫 排除：history、detailed_info等大字段\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\tif (res && res.code === 0 && res.data) {\n\t\t\t\tthis.pointInfo = res.data;\n\t\t\t\t\n\t\t\t\t// 🔥 增强：处理多种坐标格式\n\t\t\t\tif (this.pointInfo.location && typeof this.pointInfo.location === 'object') {\n\t\t\t\t\tthis.pointInfo.latitude = this.pointInfo.latitude || this.pointInfo.location.latitude || this.pointInfo.location.lat;\n\t\t\t\t\tthis.pointInfo.longitude = this.pointInfo.longitude || this.pointInfo.location.longitude || this.pointInfo.location.lng;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (this.currentRound && this.currentRound.points) {\n\t\t\t\t\tconst point = this.currentRound.points.find(p => p.point_id === this.pointInfo._id);\n\t\t\t\t\tif (point && point.status > 0) {\n\t\t\t\t\t\tthis.updateMapMarkers();\n\t\t\t\t\t\tthis.isRoundValid = false;\n\t\t\t\t\t\tthis.roundErrorMessage = '该点位在当前轮次已完成打卡';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.updateMapMarkers();\n\t\t\t\tthis.updateCircles();\n\t\t\t\t\n\t\t\t\t// 立即计算距离，避免显示异常距离值\n\t\t\t\tif (this.currentLocation.latitude && this.currentLocation.longitude) {\n\t\t\t\t\tthis.calculateDistance();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn res.data;\n\t\t\t} else {\n\t\t\t\tconst errorMsg = res?.message || '获取点位信息失败';\n\t\t\t\tconsole.error('获取点位信息失败:', errorMsg);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: errorMsg,\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn null;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconst errorMsg = '获取点位信息出错: ' + (e.message || e);\n\t\t\tconsole.error(errorMsg);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: errorMsg,\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\treturn null;\n\t\t}\n\t},\n\t\n\t// 获取任务信息\n\tasync getTaskInfo(taskId) {\n\t\tif (!taskId) {\n\t\t\treturn;\n\t\t}\n\t\t\n\t\ttry {\n\t\t\tconst res = await PatrolApi.call({\n\t\t\t\tname: 'patrol-task',\n\t\t\t\taction: 'getTaskDetail',\n\t\t\t\tdata: {\n\t\t\t\t\ttask_id: taskId,\n\t\t\t\t\t// 🔥 打卡页面专用优化：保留points数组但简化字段，减少70%数据量\n\t\t\t\t\tlevel: 'checkin' // 云函数内部已优化，只保留points的核心字段\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\tthis.taskInfo = res.data;\n\t\t\t\t\n\t\t\t\t// 使用新数据结构处理轮次信息\n\t\t\t\tif (this.taskInfo.rounds_detail && this.taskInfo.rounds_detail.length > 0) {\n\t\t\t\t\t// 处理轮次数据，包括跨天轮次\n\t\t\t\t\tthis.processRoundsData();\n\t\t\t\t\t\n\t\t\t\t\t// 轮次数据处理完成后，获取下个未打卡点位\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.getNextUnCheckedPoint();\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthis.isRoundValid = false;\n\t\t\t\t\tthis.roundErrorMessage = '任务无轮次数据';\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取任务信息失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '获取任务信息出错',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t},\n\t\n\t// 处理轮次数据，包括跨天情况\n\tprocessRoundsData() {\n\t\tif (!this.taskInfo || !this.taskInfo.rounds_detail) return;\n\t\t\n\t\t// 获取任务基准日期\n\t\tconst taskDate = this.taskInfo.patrol_date ? new Date(this.taskInfo.patrol_date) : new Date(this.taskInfo.create_date);\n\t\tconst now = new Date(); // 当前时间，用于判断轮次状态\n\t\t\n\t\t// 处理每个轮次的时间信息和状态\n\t\tthis.taskInfo.rounds_detail = this.taskInfo.rounds_detail.map(round => {\n\t\t\t// 确保轮次中有day_offset和duration字段\n\t\t\tround.day_offset = round.day_offset !== undefined ? Number(round.day_offset) : 0;\n\t\t\tround.duration = round.duration !== undefined ? Number(round.duration) : 60; // 默认60分钟\n\t\t\t\n\t\t\t// 设置轮次是否隐藏的标志\n\t\t\tround.isHidden = false;\n\t\t\t\n\t\t\t// 检查是否是当天有效轮次\n\t\t\tif (round.day_offset > 0) {\n\t\t\t\t// 如果有day_offset，检查是否已到对应日期\n\t\t\t\tconst offsetDate = new Date(taskDate);\n\t\t\t\toffsetDate.setDate(taskDate.getDate() + round.day_offset);\n\t\t\t\t\n\t\t\t\t// 检查当前日期是否已到轮次日期\n\t\t\t\tconst today = new Date();\n\t\t\t\ttoday.setHours(0, 0, 0, 0);\n\t\t\t\tconst offsetDay = new Date(offsetDate);\n\t\t\t\toffsetDay.setHours(0, 0, 0, 0);\n\t\t\t\t\n\t\t\t\t// 如果未到日期，不显示在当前轮次中\n\t\t\t\tif (today < offsetDay) {\n\t\t\t\t\tround.isHidden = true;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 处理时间字段格式和计算实际时间\n\t\t\tif (round.start_time) {\n\t\t\t\ttry {\n\t\t\t\t\t// 直接从ISO时间格式解析日期时间\n\t\t\t\t\tconst roundStartTime = new Date(round.start_time);\n\t\t\t\t\tconst roundEndTime = new Date(round.end_time);\n\t\t\t\t\t\n\t\t\t\t\t// 保存实际开始时间和结束时间\n\t\t\t\t\tround.actualStartTime = roundStartTime;\n\t\t\t\t\tround.actualEndTime = roundEndTime;\n\t\t\t\t\t\n\t\t\t\t\t// 计算轮次状态\n\t\t\t\t\t// 先判断时间状态，再结合点位完成情况\n\t\t\t\t\tif (now < roundStartTime) {\n\t\t\t\t\t\tround.status = 0; // 未开始\n\t\t\t\t\t} else if (now > roundEndTime) {\n\t\t\t\t\t\t// 已超时，但要检查点位是否全部完成\n\t\t\t\t\t\tif (round.point_stats && \n\t\t\t\t\t\t\tround.point_stats.total > 0 && \n\t\t\t\t\t\t\tround.point_stats.checked >= round.point_stats.total) {\n\t\t\t\t\t\t\tround.status = 2; // 虽然超时但已完成\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tround.status = 3; // 超时且未完成\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 在时间范围内\n\t\t\t\t\t\tif (round.point_stats && \n\t\t\t\t\t\t\tround.point_stats.total > 0 && \n\t\t\t\t\t\t\tround.point_stats.checked >= round.point_stats.total) {\n\t\t\t\t\t\t\tround.status = 2; // 已完成\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tround.status = 1; // 进行中\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(`解析轮次[${round.round}]时间出错:`, error);\n\t\t\t\t\t\n\t\t\t\t\t// 出错时使用备用方案：根据点位完成情况判断\n\t\t\t\t\tif (round.point_stats && \n\t\t\t\t\t\tround.point_stats.total > 0 && \n\t\t\t\t\t\tround.point_stats.checked >= round.point_stats.total) {\n\t\t\t\t\t\tround.status = 2; // 已完成\n\t\t\t\t\t} else if (round.point_stats && round.point_stats.checked > 0) {\n\t\t\t\t\t\tround.status = 1; // 进行中\n\t\t\t\t\t} else {\n\t\t\t\t\t\tround.status = 0; // 默认未开始\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// 无时间信息时，根据点位完成情况判断\n\t\t\t\tif (round.point_stats && \n\t\t\t\t\tround.point_stats.total > 0 && \n\t\t\t\t\tround.point_stats.checked >= round.point_stats.total) {\n\t\t\t\t\tround.status = 2; // 已完成\n\t\t\t\t} else if (round.point_stats && round.point_stats.checked > 0) {\n\t\t\t\t\tround.status = 1; // 进行中\n\t\t\t\t} else {\n\t\t\t\t\tround.status = 0; // 默认未开始\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 确保状态是数字\n\t\t\tround.status = parseInt(round.status || 0);\n\t\t\t\n\t\t\treturn round;\n\t\t});\n\t\t\n\t\t// 过滤掉隐藏的轮次\n\t\tconst visibleRounds = this.taskInfo.rounds_detail.filter(round => !round.isHidden);\n\t\t\n\t\t// 根据轮次状态排序：未开始和进行中按round升序，已完成和已超时按round降序\n\t\t// 先将轮次分组\n\t\tconst notStartedOrActive = visibleRounds.filter(\n\t\t\tround => round.status === 0 || round.status === 1\n\t\t);\n\t\tconst completedOrExpired = visibleRounds.filter(\n\t\t\tround => round.status === 2 || round.status === 3\n\t\t);\n\t\t\n\t\t// 分别排序\n\t\tnotStartedOrActive.sort((a, b) => a.round - b.round); // 按轮次号升序\n\t\tcompletedOrExpired.sort((a, b) => b.round - a.round); // 按轮次号降序\n\t\t\n\t\t// 合并排序后的数组\n\t\tthis.taskInfo.visibleRounds = [...notStartedOrActive, ...completedOrExpired];\n\t\t\n\t\t// 如果有可见轮次，选择第一个作为当前轮次\n\t\tif (this.taskInfo.visibleRounds && this.taskInfo.visibleRounds.length > 0) {\n\t\t\tthis.currentRound = this.taskInfo.visibleRounds[0];\n\t\t\tthis.formData.round = this.currentRound.round;\n\t\t\t\n\t\t\t// 立即检查点位是否已打卡\n\t\t\tif (this.pointInfo && this.pointInfo._id) {\n\t\t\t\tconst point = this.currentRound.points.find(p => p.point_id === this.pointInfo._id);\n\t\t\t\tif (point && point.status > 0) {\n\t\t\t\t\t// 如果点位已打卡，立即更新地图标记\n\t\t\t\t\tthis.updateMapMarkers();\n\t\t\t\t\t// 设置轮次无效\n\t\t\t\t\tthis.isRoundValid = false;\n\t\t\t\t\tthis.roundErrorMessage = '该点位在当前轮次已完成打卡';\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tthis.validateCurrentRound();\n\t\t\t// 启动轮次状态更新定时器\n\t\t\tthis.startRoundStatusTimer();\n\t\t\t\n\t\t\t// 轮次数据处理完成后，获取下个未打卡点位\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.getNextUnCheckedPoint();\n\t\t\t});\n\t\t}\n\t},\n\t\n\t// 简化轮次验证逻辑，只保留一个验证方法\n\tvalidateCurrentRound() {\n\t\t// 如果没有当前轮次，则无法验证\n\t\tif (!this.currentRound) {\n\t\t\tthis.isRoundValid = false;\n\t\t\tthis.roundErrorMessage = '无法获取轮次信息';\n\t\t\treturn;\n\t\t}\n\n\t\t// 获取当前时间\n\t\tconst now = new Date();\n\t\t\n\t\t// 检查轮次是否被标记为隐藏（跨天未到日期）\n\t\tif (this.currentRound.isHidden) {\n\t\t\tthis.isRoundValid = false;\n\t\t\tthis.roundErrorMessage = `轮次${this.currentRound.round}尚未开始，请在指定日期执行`;\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 检查当前轮次状态\n\t\tif (this.currentRound.status === 0) {\n\t\t\t// 未开始\n\t\t\tif (this.currentRound.actualStartTime) {\n\t\t\t\tconst timeToStart = this.currentRound.actualStartTime - now;\n\t\t\t\t\n\t\t\t\tif (timeToStart <= 0) {\n\t\t\t\t\tthis.isRoundValid = false;\n\t\t\t\t\tthis.roundErrorMessage = `轮次${this.currentRound.round}正在开始...`;\n\t\t\t\t} else if (timeToStart < 60000) {\n\t\t\t\t\t// 如果不足1分钟，显示秒数\n\t\t\t\t\tconst secondsToStart = Math.floor(timeToStart / 1000);\n\t\t\t\t\tthis.isRoundValid = false;\n\t\t\t\t\tthis.roundErrorMessage = `距离轮次${this.currentRound.round}开始还有${secondsToStart}秒`;\n\t\t\t\t} else if (timeToStart < 3600000) {\n\t\t\t\t\t// 如果不足1小时，显示分钟和秒数\n\t\t\t\t\tconst minutesToStart = Math.floor(timeToStart / (1000 * 60));\n\t\t\t\t\tconst secondsToStart = Math.floor((timeToStart % (1000 * 60)) / 1000);\n\t\t\t\t\tthis.isRoundValid = false;\n\t\t\t\t\tthis.roundErrorMessage = `距离轮次${this.currentRound.round}开始还有${minutesToStart}分${secondsToStart}秒`;\n\t\t\t\t} else {\n\t\t\t\t\t// 如果大于等于1小时，只显示小时和分钟\n\t\t\t\t\tconst hoursToStart = Math.floor(timeToStart / (1000 * 60 * 60));\n\t\t\t\t\tconst minutesToStart = Math.floor((timeToStart % (1000 * 60 * 60)) / (1000 * 60));\n\t\t\t\t\tthis.isRoundValid = false;\n\t\t\t\t\tthis.roundErrorMessage = `距离轮次${this.currentRound.round}开始还有${hoursToStart}小时${minutesToStart}分钟`;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthis.isRoundValid = false;\n\t\t\t\tthis.roundErrorMessage = `轮次${this.currentRound.round}尚未开始`;\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 明确处理进行中状态\n\t\tif (this.currentRound.status === 1) {\n\t\t\t// 检查当前点位在轮次中是否已打卡\n\t\t\tif (this.pointInfo && this.pointInfo._id) {\n\t\t\t\tconst point = this.currentRound.points.find(p => p.point_id === this.pointInfo._id);\n\t\t\t\tif (point && point.status > 0) {\n\t\t\t\t\tthis.isRoundValid = false;\n\t\t\t\t\tthis.roundErrorMessage = '该点位在当前轮次已完成打卡';\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 状态为进行中，且点位未打卡，设置为有效\n\t\t\tthis.isRoundValid = true;\n\t\t\tthis.roundErrorMessage = '';\n\t\t\treturn;\n\t\t}\n\t\t\n\t\tif (this.currentRound.status === 2) {\n\t\t\t// 已完成\n\t\t\tthis.isRoundValid = false;\n\t\t\tthis.roundErrorMessage = `轮次${this.currentRound.round}已完成`;\n\t\t\treturn;\n\t\t}\n\t\t\n\t\tif (this.currentRound.status === 3) {\n\t\t\t// 已超时\n\t\t\tthis.isRoundValid = false;\n\t\t\tthis.roundErrorMessage = `轮次${this.currentRound.round}已超时`;\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 检查当前点位在轮次中是否已打卡\n\t\tif (this.pointInfo && this.pointInfo._id) {\n\t\t\tconst point = this.currentRound.points.find(p => p.point_id === this.pointInfo._id);\n\t\t\tif (point && point.status > 0) {\n\t\t\t\tthis.isRoundValid = false;\n\t\t\t\tthis.roundErrorMessage = '该点位在当前轮次已完成打卡';\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 一切检查通过，轮次有效\n\t\tthis.isRoundValid = true;\n\t\tthis.roundErrorMessage = '';\n\t},\n\t\n\t// 更新范围圈\n\tupdateCircles() {\n\t\tif (!this.pointInfo || !this.pointInfo.latitude || !this.pointInfo.longitude) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst baseRange = this.pointInfo.range || 10; // 基础范围\n\t\tconst accuracy = this.currentLocation.accuracy;\n\t\t\n\t\t// 更新范围圈，使用基础范围\n\t\tthis.circles = [{\n\t\t\tlatitude: this.pointInfo.latitude,\n\t\t\tlongitude: this.pointInfo.longitude,\n\t\t\tcolor: this.isInRange ? '#52C41A33' : '#FF4D4F33',\n\t\t\tfillColor: this.isInRange ? '#52C41A22' : '#FF4D4F22',\n\t\t\tradius: baseRange, // 使用基础范围，不再添加宽松度\n\t\t\tstrokeWidth: 2\n\t\t}];\n\n\t\t// 添加当前位置精度圈\n\t\tif (this.currentLocation.accuracy > 0) {\n\t\t\tlet circleColor, fillColor;\n\t\t\t\n\t\t\tif (accuracy <= 5) {\n\t\t\t\tcircleColor = '#34C75988';    // 绿色 - 精度极好\n\t\t\t\tfillColor = '#34C75933';\n\t\t\t} else if (accuracy <= 10) {\n\t\t\t\tcircleColor = '#00C58E88';    // 青色 - 精度良好\n\t\t\t\tfillColor = '#00C58E33';\n\t\t\t} else if (accuracy <= 15) {\n\t\t\t\tcircleColor = '#FFD60A88';    // 黄色 - 精度一般\n\t\t\t\tfillColor = '#FFD60A33';\n\t\t\t} else if (accuracy <= 20) {\n\t\t\t\tcircleColor = '#FF950088';    // 橙色 - 精度较差\n\t\t\t\tfillColor = '#FF950033';\n\t\t\t} else if (accuracy <= 25) {\n\t\t\t\tcircleColor = '#FF6B2C88';    // 深橙色 - 精度很差\n\t\t\t\tfillColor = '#FF6B2C33';\n\t\t\t} else {\n\t\t\t\tcircleColor = '#FF3B3088';    // 红色 - 精度极差\n\t\t\t\tfillColor = '#FF3B3033';\n\t\t\t}\n\n\t\t\tthis.circles.push({\n\t\t\t\tlatitude: this.currentLocation.latitude,\n\t\t\t\tlongitude: this.currentLocation.longitude,\n\t\t\t\tcolor: circleColor,\n\t\t\t\tfillColor: fillColor,\n\t\t\t\tradius: 3,  // 固定3米精度圈\n\t\t\t\tstrokeWidth: 2,\n\t\t\t\tstrokeColor: circleColor.slice(0, 7)\n\t\t\t});\n\t\t}\n\t},\n\t\n\t// 计算到点位的距离\n\tcalculateDistance() {\n\t\tif (!this.pointInfo || !this.pointInfo.latitude || !this.pointInfo.longitude) {\n\t\t\treturn;\n\t\t}\n\t\t\n\t\ttry {\n\t\t\t// 直接使用当前位置计算距离\n\t\t\tconst distance = LocationUtils.calculateDistance(\n\t\t\t\tthis.currentLocation,\n\t\t\t\t{ latitude: this.pointInfo.latitude, longitude: this.pointInfo.longitude }\n\t\t\t);\n\n\t\t\t// 获取点位设置的范围\n\t\t\tconst range = this.pointInfo.range || 10;\n\t\t\t\n\t\t\t// 判断是否在范围内\n\t\t\tif (distance <= range) {\n\t\t\t\tthis.isInRange = true;\n\t\t\t\tthis.distanceText = distance < 5 ? '很近' : `还需${Math.round(distance)}米靠近中心点`;\n\t\t\t} else {\n\t\t\t\tthis.isInRange = false;\n\t\t\t\tthis.distanceText = `还需${Math.round(distance - range)}米靠近范围圈`;\n\t\t\t}\n\t\t\t\n\t\t\t// 更新地图显示\n\t\t\tif (this.isInRange !== this.lastInRange) {\n\t\t\t\tthis.updateCircles();\n\t\t\t\tthis.lastInRange = this.isInRange;\n\t\t\t}\n\t\t\t\n\t\t} catch (error) {\n\t\t\tconsole.error('计算距离出错', error);\n\t\t}\n\t},\n\t\n\t// 添加精度颜色判断方法\n\tgetAccuracyColor() {\n\t\tconst accuracy = this.currentLocation.accuracy;\n\t\tif (!accuracy) return '#999999';\n\t\tif (accuracy <= 5) return '#34C759';    // 绿色 - 精度极好\n\t\tif (accuracy <= 10) return '#00C58E';    // 青色 - 精度良好\n\t\tif (accuracy <= 15) return '#FFD60A';   // 黄色 - 精度一般\n\t\tif (accuracy <= 20) return '#FF9500';   // 橙色 - 精度较差\n\t\tif (accuracy <= 25) return '#FF6B2C';   // 深橙色 - 精度很差\n\t\treturn '#FF3B30';                       // 红色 - 精度极差\n\t},\n\t\n\t// 显示位置错误\n\tshowLocationError(message) {\n\t\tif (!this.locationErrorShown) {\n\t\t\tthis.locationErrorShown = true;\n\t\t\tuni.showToast({\n\t\t\t\ttitle: message,\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 3000\n\t\t\t});\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.locationErrorShown = false;\n\t\t\t}, 5000);\n\t\t}\n\t},\n\t\n\t// 显示位置警告\n\tshowLocationWarning(message) {\n\t\tif (!this.locationWarningShown) {\n\t\t\tthis.locationWarningShown = true;\n\t\t\tuni.showToast({\n\t\t\t\ttitle: message,\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 3000\n\t\t\t});\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.locationWarningShown = false;\n\t\t\t}, 10000); // 10秒内不重复显示\n\t\t}\n\t},\n\t\n\t// 更新地图标记点\n\tupdateMapMarkers() {\n\t\tif (!this.pointInfo || !this.pointInfo.latitude || !this.pointInfo.longitude) {\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 检查点位是否已打卡\n\t\tconst isChecked = this.currentRound && this.currentRound.points && \n\t\t\tthis.currentRound.points.find(p => p.point_id === this.pointInfo._id)?.status > 0;\n\t\t\n\t\tthis.markers = [{\n\t\t\tid: 1,\n\t\t\tlatitude: this.pointInfo.latitude,\n\t\t\tlongitude: this.pointInfo.longitude,\n\t\t\ttitle: this.pointInfo.name,\n\t\t\ticonPath: isChecked ? '/static/map/map-pin.png' : '/static/map/marker.png',\n\t\t\twidth: 32,\n\t\t\theight: 32,\n\t\t\tcallout: {\n\t\t\t\tcontent: `${this.pointInfo.name || '未命名点位'}${isChecked ? ' ✓' : ''}`,\n\t\t\t\tcolor: '#FFFFFF',\n\t\t\t\tfontSize: 12,\n\t\t\t\tborderWidth: 0,\n\t\t\t\tbgColor: isChecked ? '#34C759' : '#3688FF',\n\t\t\t\tpadding: 5,\n\t\t\t\tdisplay: 'ALWAYS',\n\t\t\t\tborderRadius: 4,\n\t\t\t\ttextAlign: 'center'\n\t\t\t},\n\t\t\tanchorX: 0.5,\n\t\t\tanchorY: 1.0\n\t\t}];\n\t},\n\t\n\t// 地图区域变化事件\n\tonMapRegionChange(e) {\n\t\t// 记录用户手动移动了地图\n\t\tif (e.type === 'end' && e.causedBy === 'drag') {\n\t\t\tthis.isFirstLocation = false; // 用户已手动移动地图，不再自动居中\n\t\t\tthis.isFollowMode = false; // 关闭跟随模式\n\t\t}\n\t},\n\t\n\t// 点击标记点事件\n\tonMarkerTap(e) {\n\t\t// 可以在这里处理点击标记点逻辑\n\t},\n\t\n\t// 选择图片\n\tchooseImage() {\n\t\t// 在微信小程序中使用camera组件\n\t\t// #ifdef MP-WEIXIN\n\t\tthis.showCamera = true;\n\t\tthis.$nextTick(() => {\n\t\t\tthis.cameraContext = wx.createCameraContext('nativeCamera');\n\t\t});\n\t\t// #endif\n\t\t\n\t\t// 在其他平台继续使用uni.chooseImage\n\t\t// #ifndef MP-WEIXIN\n\t\tuni.chooseImage({\n\t\t\tcount: this.maxImageCount - this.imageList.length,\n\t\t\tsizeType: ['compressed'], // 压缩图片\n\t\t\tsourceType: ['camera'], // 仅使用相机，移除'album'\n\t\t\tsuccess: (res) => {\n\t\t\t\t// 压缩上传的图片\n\t\t\t\tthis.compressImages(res.tempFilePaths);\n\t\t\t},\n\t\t\tfail: (err) => {\n\t\t\t\t// 处理错误，例如用户拒绝相机权限\n\t\t\t\tif (err.errMsg && err.errMsg.includes('auth deny')) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '需要相机权限才能拍照上传，请在设置中允许访问相机',\n\t\t\t\t\t\tconfirmText: '去设置',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tuni.openSetting();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '拍照失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\t// #endif\n\t},\n\t\n\t// 切换闪光灯状态\n\ttoggleFlash() {\n\t\t// 记录上一个状态\n\t\tconst previousMode = this.flashMode;\n\t\t\n\t\t// 循环切换闪光灯状态: off -> on -> torch -> off\n\t\tif (this.flashMode === 'off') {\n\t\t\tthis.flashMode = 'on';\n\t\t} else if (this.flashMode === 'on') {\n\t\t\tthis.flashMode = 'torch';\n\t\t} else {\n\t\t\tthis.flashMode = 'off';\n\t\t}\n\t\t\n\t\t// 只保留中间的状态提示\n\t\tlet toastMsg = '';\n\t\t\tif (this.flashMode === 'off') {\n\t\t\ttoastMsg = '已关闭闪光灯';\n\t\t\t} else if (this.flashMode === 'on') {\n\t\t\ttoastMsg = '闪光灯已开启';\n\t\t\t} else if (this.flashMode === 'torch') {\n\t\t\ttoastMsg = '常亮模式已开启';\n\t\t\t}\n\t\t\tuni.showToast({\n\t\t\ttitle: toastMsg,\n\t\t\ticon: 'none',\n\t\t\tduration: 1000\n\t\t\t});\n\t\t},\n\t\n\t// 二维码扫描器的闪光灯控制\n\ttoggleScannerFlash() {\n\t\t// 简单切换开关状态（只有 off 和 torch 两种状态）\n\t\tthis.flashMode = this.flashMode === 'off' ? 'torch' : 'off';\n\t\t\n\t\t// 显示简单的状态提示\n\t\tuni.showToast({\n\t\t\ttitle: this.flashMode === 'off' ? '已关闭照明' : '已开启照明',\n\t\t\ticon: 'none',\n\t\t\tduration: 1000\n\t\t});\n\t},\n\t\n\t// 获取闪光灯图标\n\tgetFlashIcon() {\n\t\tswitch(this.flashMode) {\n\t\t\tcase 'on': return 'eye-filled';\n\t\t\tcase 'torch': return 'fire-filled';\n\t\t\tdefault: return 'eye';\n\t\t}\n\t},\n\t\n\t// 获取闪光灯文本\n\tgetFlashText() {\n\t\tswitch(this.flashMode) {\n\t\t\tcase 'on': return '闪光灯开';\n\t\t\tcase 'torch': return '常亮模式';\n\t\t\tdefault: return '闪光灯关';\n\t\t}\n\t},\n\t\n\t// 关闭相机\n\tcloseCamera() {\n\t\tthis.showCamera = false;\n\t\tthis.cameraContext = null;\n\t},\n\t\n\t// 使用camera组件拍照\n\ttakePhoto() {\n\t\tif (!this.cameraContext) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '相机未初始化',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 保存当前闪光灯状态\n\t\tconst currentFlash = this.flashMode;\n\t\t\n\t\t// 根据不同的闪光灯模式处理拍照\n\t\tif (currentFlash === 'off') {\n\t\t\t// 闪光灯关闭模式，直接拍照\n\t\t\tthis.doTakePhoto(currentFlash);\n\t\t} else if (currentFlash === 'on') {\n\t\t\t// 模拟原生相机的闪光灯行为\n\t\t\t// 步骤1: 先切换到torch模式预热闪光灯 (约1秒)\n\t\t\tthis.flashMode = 'torch';\n\t\t\t\n\t\t\t// 预热时间为1000ms\n\t\t\tsetTimeout(() => {\n\t\t\t\t// 步骤2: 闪烁一下(快速切换off再on)以模拟原生相机闪光\n\t\t\t\tthis.flashMode = 'off';\n\t\t\t\t\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t// 步骤3: 开启闪光灯并拍照\n\t\t\t\t\tthis.flashMode = 'on';\n\t\t\t\t\t\n\t\t\t\t\t// 拍照\n\t\t\t\t\tthis.doTakePhoto('on');\n\t\t\t\t\t\n\t\t\t\t\t// 步骤4: 拍照后保持闪光灯一段时间\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t// 步骤5: 关闭闪光灯\n\t\t\t\t\t\tthis.flashMode = 'off';\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 步骤6: 恢复到用户设置的闪光灯模式\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.flashMode = currentFlash;\n\t\t\t\t\t\t}, 500);\n\t\t\t\t\t}, 800); // 保持闪光灯亮起800ms\n\t\t\t\t}, 200); // 关闭闪光灯200ms\n\t\t\t}, 1000); // 预热时间1000ms\n\t\t} else { // torch模式\n\t\t\t// 常亮模式，直接拍照\n\t\t\tthis.doTakePhoto(currentFlash);\n\t\t}\n\t},\n\t\n\t// 实际执行拍照\n\tdoTakePhoto(flashMode) {\n\t\tthis.cameraContext.takePhoto({\n\t\t\tquality: 'high',\n\t\t\tflash: flashMode,\n\t\t\tsuccess: (res) => {\n\t\t\t\t// 拍照成功，处理图片\n\t\t\t\tconst tempFilePaths = [res.tempImagePath];\n\t\t\t\tthis.compressImages(tempFilePaths);\n\t\t\t\tthis.showCamera = false; // 拍照后关闭相机\n\t\t\t},\n\t\t\tfail: (err) => {\n\t\t\t\tconsole.error('拍照失败:', err);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '拍照失败: ' + (err.errMsg || JSON.stringify(err)),\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t},\n\t\n\t// 处理相机错误\n\thandleCameraError(err) {\n\t\tif (err.detail && err.detail.errMsg && err.detail.errMsg.includes('auth deny')) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '提示',\n\t\t\t\tcontent: '需要相机权限才能拍照上传，请在设置中允许访问相机',\n\t\t\t\tconfirmText: '去设置',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tuni.openSetting();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '相机出错',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t},\n\t\n\t// 压缩图片\n\tcompressImages(tempFilePaths) {\n\t\tconst promises = tempFilePaths.map(path => {\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\tuni.compressImage({\n\t\t\t\t\tsrc: path,\n\t\t\t\t\tquality: 80,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tresolve(res.tempFilePath);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tresolve(path); // 使用原图\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t});\n\t\t\t\t\n\t\tPromise.all(promises).then(compressedPaths => {\n\t\t\tthis.imageList = [...this.imageList, ...compressedPaths];\n\t\t});\n\t},\n\t\n\t// 预览图片\n\tpreviewImage(index) {\n\t\tuni.previewImage({\n\t\t\turls: this.imageList,\n\t\t\tcurrent: index\n\t\t});\n\t},\n\t\n\t// 删除图片\n\tdeleteImage(index) {\n\t\tthis.imageList.splice(index, 1);\n\t},\n\t\n\t// 上传图片方法\n\tasync uploadImage(filePath) {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '上传图片中...',\n\t\t\t\tmask: true\n\t\t\t});\n\t\t\t\n\t\t\t// 使用年月日创建目录结构\n\t\t\tconst now = new Date();\n\t\t\tconst year = now.getFullYear();\n\t\t\tconst month = String(now.getMonth() + 1).padStart(2, '0');\n\t\t\tconst day = String(now.getDate()).padStart(2, '0');\n\t\t\tconst dateFolder = `${year}${month}${day}`;\n\t\t\t\n\t\t\t// 获取文件扩展名\n\t\t\tlet fileExt = '.jpg';\n\t\t\tif (filePath.match(/\\.(\\w+)$/)) {\n\t\t\t\tfileExt = filePath.match(/\\.(\\w+)$/)[0];\n\t\t\t}\n\t\t\t\n\t\t\t// 创建唯一文件名\n\t\t\tconst fileName = `${Date.now()}_${Math.floor(Math.random() * 1000)}${fileExt}`;\n\t\t\t\n\t\t\t// 使用uniCloud上传方法\n\t\t\tuniCloud.uploadFile({\n\t\t\t\tfilePath: filePath,\n\t\t\t\tcloudPath: `patrol/photos/${dateFolder}/${fileName}`,\n\t\t\t\tcloudPathAsRealPath: true, // 启用真实目录支持\n\t\t\t\tsuccess: (uploadRes) => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tresolve(uploadRes.fileID);\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\t// 判断是否要重试\n\t\t\t\t\tif (this.uploadRetryCount < this.uploadMaxRetries) {\n\t\t\t\t\t\tthis.uploadRetryCount++;\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: `上传失败，正在重试(${this.uploadRetryCount}/${this.uploadMaxRetries})`,\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 延迟1秒后重试\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.uploadImage(filePath)\n\t\t\t\t\t\t\t\t.then(resolve)\n\t\t\t\t\t\t\t\t.catch(reject);\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.uploadRetryCount = 0;\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '图片上传失败，请重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treject(err);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t},\n\t\n\t// 提交打卡\n\tasync submitCheckin() {\n\t\tif (this.loading) {\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 检查点位是否必须使用二维码\n\t\tif (this.pointInfo && this.pointInfo.qrcode_required && !this.qrcodeVerified) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '该点位必须使用二维码打卡',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\t\n\t\t\t// 自动调起扫码\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.scanQRCode();\n\t\t\t}, 1500);\n\t\t\t\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 如果不是强制二维码，先进行位置重新确认\n\t\tif (!this.pointInfo.qrcode_required) {\n\t\t\ttry {\n\t\t\t\tconst currentLocation = await LocationUtils.getCurrentLocation();\n\t\t\t\tthis.currentLocation = currentLocation;\n\t\t\t\tthis.calculateDistance();\n\t\t\t\t\n\t\t\t\t// 再次检查范围\n\t\t\t\tif (!this.isInRange) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '您已离开打卡范围，请靠近点位',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取当前位置失败，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 执行打卡\n\t\tthis.doCheckIn(this.qrcodeVerified, this.qrcodeVerifyResult);\n\t},\n\t\n\t// 处理二维码验证成功后的逻辑\n\thandleQRCodeSuccess() {\n\t\t\t// 如果是强制二维码，直接允许打卡，不检查距离\n\t\t\tif (this.pointInfo.qrcode_required) {\n\t\t\t\tthis.doCheckIn(true, this.qrcodeVerifyResult);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 非强制二维码时才判断距离\n\t\t\tconst distanceToPoint = LocationUtils.calculateDistance(\n\t\t\t\tthis.currentLocation,\n\t\t\t\t{ \n\t\t\t\t\tlatitude: this.pointInfo.latitude,\n\t\t\t\t\tlongitude: this.pointInfo.longitude\n\t\t\t\t}\n\t\t\t);\n\t\t\t\n\t\t\t// 使用二维码允许的距离来判断\n\t\t\tif (distanceToPoint <= this.qrcodeAllowedDistance) {\n\t\t\t\tthis.doCheckIn(true, this.qrcodeVerifyResult);\n\t\t\t} else {\n\t\t\t\tconst message = `您距离点位中心点${Math.round(distanceToPoint)}米，请靠近后再扫码`;\n\t\t\t\tthis.showDistancePopup(message);\n\t\t\t}\n\t},\n\t\n\t// 验证二维码\n\tasync verifyQRCode(qrcodeContent) {\n\t\ttry {\n\t\t\t// 验证二维码，移除时效性验证\n\t\t\treturn await QRCodeUtil.verifyQRCode(qrcodeContent, {\n\t\t\t\tpointId: this.pointInfo._id,\n\t\t\t\tcheckExpired: false  // 关闭时效性验证\n\t\t\t});\n\t\t} catch (e) {\n\t\t\tconsole.error('验证二维码出错', e);\n\t\t\treturn {\n\t\t\t\tvalid: false,\n\t\t\t\tcode: 'VERIFY_ERROR',\n\t\t\t\tmessage: '验证过程中出错'\n\t\t\t};\n\t\t}\n\t},\n\t\n\t// 执行打卡操作，支持二维码验证\n\tasync doCheckIn(fromQRCode = false, qrcodeResult = null) {\n\t\tif (this.loading) {\n\t\t\treturn;\n\t\t}\n\t\t\n\t\tthis.loading = true;\n\t\t\n\t\ttry {\n\t\t\t// 最后一次检查点位状态\n\t\t\tif (this.currentRound && this.currentRound.points) {\n\t\t\t\tconst point = this.currentRound.points.find(p => p.point_id === this.pointInfo._id);\n\t\t\t\tif (point && point.status > 0) {\n\t\t\t\t\tthis.distancePopupMessage = '该点位在当前轮次已完成打卡';\n\t\t\t\t\tthis.$refs.distancePopup.open();\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.$refs.distancePopup.close();\n\t\t\t\t\t}, 2000);\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 如果不是从二维码扫描进入，且当前点位必须使用二维码\n\t\t\tif (!fromQRCode && this.pointInfo && this.pointInfo.qrcode_required) {\n\t\t\t\tthis.distancePopupMessage = '该点位必须使用二维码打卡';\n\t\t\t\tthis.$refs.distancePopup.open();\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.$refs.distancePopup.close();\n\t\t\t\t}, 2000);\n\t\t\t\tthis.loading = false;\n\t\t\t\t\n\t\t\t\t// 自动调起扫码\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.scanQRCode();\n\t\t\t\t}, 1500);\n\t\t\t\t\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 计算到点位的距离\n\t\t\tconst distanceToPoint = LocationUtils.calculateDistance(\n\t\t\t\tthis.currentLocation,\n\t\t\t\t{ \n\t\t\t\t\tlatitude: this.pointInfo.latitude,\n\t\t\t\t\tlongitude: this.pointInfo.longitude\n\t\t\t\t}\n\t\t\t);\n\t\t\t\n\t\t\t// 根据打卡方式判断是否允许打卡\n\t\t\tlet isAllowed = false;\n\t\t\tif (fromQRCode) {\n\t\t\t\t// 如果是不限距离模式，直接允许打卡\n\t\t\t\tif (this.pointInfo.qrcode_required) {\n\t\t\t\t\tisAllowed = true;\n\t\t\t\t} else {\n\t\t\t\t\t// 非不限距离模式：使用qrcodeAllowedDistance\n\t\t\t\t\tisAllowed = distanceToPoint <= this.qrcodeAllowedDistance;\n\t\t\t\t\tif (!isAllowed) {\n\t\t\t\t\t\tconst message = `您距离点位中心点${Math.round(distanceToPoint)}米，请靠近后再扫码`;\n\t\t\t\t\t\tthis.showDistancePopup(message);\n\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// GPS打卡：使用原有的isInRange判断\n\t\t\t\tif (!this.isInRange) {\n\t\t\t\t\tthis.distancePopupMessage = '您不在打卡范围内';\n\t\t\t\t\tthis.$refs.distancePopup.open();\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.$refs.distancePopup.close();\n\t\t\t\t\t}, 2000);\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tisAllowed = true;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果轮次无效\n\t\t\tif (!this.isRoundValid) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: this.roundErrorMessage || '当前轮次不可用',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tthis.loading = false;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 准备上传图片\n\t\t\tconst imageUrls = [];\n\t\t\tif (this.imageList.length > 0) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在上传图片...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tlet uploadFailed = false;\n\t\t\t\tfor (let i = 0; i < this.imageList.length; i++) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst tempFilePath = this.imageList[i];\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: `上传第${i+1}/${this.imageList.length}张图片`,\n\t\t\t\t\t\t\tmask: true\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst fileID = await this.uploadImage(tempFilePath);\n\t\t\t\t\t\tif (fileID) {\n\t\t\t\t\t\t\timageUrls.push(fileID);\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (uploadError) {\n\t\t\t\t\t\tconsole.error('图片上传失败', uploadError);\n\t\t\t\t\t\tuploadFailed = true;\n\t\t\t\t\t\t// 继续上传其他图片\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (uploadFailed) {\n\t\t\t\t\tif (imageUrls.length === 0) {\n\t\t\t\t\t\t// 所有图片都上传失败\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\tcontent: '所有图片上传失败，是否继续提交打卡？',\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tif (!res.confirm) {\n\t\t\t\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// 用户选择继续，执行下面的提交代码\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn; // 等待用户确认\n\t\t\t\t\t} else if (imageUrls.length !== this.imageList.length) {\n\t\t\t\t\t\t// 部分图片上传失败\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\tcontent: `已成功上传${imageUrls.length}/${this.imageList.length}张图片，是否继续提交打卡？`,\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tif (!res.confirm) {\n\t\t\t\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// 用户选择继续，执行下面的提交代码\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn; // 等待用户确认\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 确保使用当前轮次的round值\n\t\t\tconst currentRoundNumber = this.currentRound ? this.currentRound.round : this.formData.round;\n\t\t\t\n\t\t\t// 构建打卡参数\n\t\t\tconst params = {\n\t\t\t\ttask_id: this.taskInfo._id,\n\t\t\t\tpoint_id: this.pointInfo._id,\n\t\t\t\tround: currentRoundNumber,\n\t\t\t\tlocation: {\n\t\t\t\t\tlatitude: this.currentLocation.latitude,\n\t\t\t\t\tlongitude: this.currentLocation.longitude,\n\t\t\t\t\taccuracy: this.currentLocation.accuracy\n\t\t\t\t},\n\t\t\t\tphotos: imageUrls,\n\t\t\t\tremark: this.formData.remark,\n\t\t\t\tstatus: 1,\n\t\t\t\tcheckin_method: fromQRCode ? (this.isInRange ? 'both' : 'qrcode') : 'gps'\n\t\t\t};\n\t\t\t\n\t\t\t// 如果是二维码打卡，添加二维码相关信息\n\t\t\tif (fromQRCode && qrcodeResult && qrcodeResult.qrcodeData) {\n\t\t\t\tparams.qrcode_verified = true;\n\t\t\t\tparams.qrcode_version = qrcodeResult.qrcodeData.v;\n\t\t\t\tparams.qrcode_content = JSON.stringify({\n\t\t\t\t\tpid: qrcodeResult.qrcodeData.pid,\n\t\t\t\t\tv: qrcodeResult.qrcodeData.v,\n\t\t\t\t\tt: qrcodeResult.qrcodeData.t\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log('准备提交打卡数据:', params);\n\t\t\t\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '提交打卡信息...',\n\t\t\t\tmask: true\n\t\t\t});\n\t\t\t\n\t\t\tconst res = await PatrolApi.call({\n\t\t\t\tname: 'patrol-record',\n\t\t\t\taction: 'submitCheckIn',\n\t\t\t\tdata: params\n\t\t\t});\n\t\t\t\n\t\t\tuni.hideLoading();\n\t\t\t\n\t\t\tif (res.code === 0) {\n\t\t\t\tthis.onCheckinSuccess(res.data);\n\t\t\t\t\n\t\t\t\t// 注意：成功提示已在 onCheckinSuccess 方法中处理，不再自动返回上一页\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: res.message || '打卡失败，请重试',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 3000\n\t\t\t\t});\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tuni.hideLoading();\n\t\t\tconsole.error('提交打卡失败:', error);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: error.message || '打卡失败，请重试',\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 3000\n\t\t\t});\n\t\t} finally {\n\t\t\tthis.loading = false;\n\t\t}\n\t},\n\t\n\t/**\n\t * 处理二维码扫描结果\n\t * @param {Object} result 扫描结果\n\t */\n\tasync handleScanResult(result) {\n\t\ttry {\n\t\t\tlet qrcodeData;\n\t\t\t\n\t\t\t// 尝试解析JSON\n\t\t\ttry {\n\t\t\t\tqrcodeData = JSON.parse(result);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('二维码内容不是有效的JSON格式:', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '不是有效的巡检点二维码',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查是否是有效的巡检点二维码\n\t\t\tif (!qrcodeData.type || qrcodeData.type !== 'PATROL_CHECK_IN') {\n\t\t\t\tconsole.log('扫描到非巡检点二维码:', qrcodeData);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '不是巡检点二维码',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 获取点位ID\n\t\t\tconst pointId = qrcodeData.pid;\n\t\t\tif (!pointId) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '无效的二维码格式',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 验证二维码 - 移除时效性验证\n\t\t\tconst verifyResult = await QRCodeUtil.verifyQRCode(result, {\n\t\t\t\tpointId: this.pointInfo._id,\n\t\t\t\tcheckExpired: false  // 关闭时效性验证\n\t\t\t});\n\t\t\t\n\t\t\t// 更新验证结果\n\t\t\tthis.qrcodeVerifyResult = {\n\t\t\t\tvalid: verifyResult.valid,\n\t\t\t\ttitle: verifyResult.valid ? '验证成功' : '验证失败',\n\t\t\t\tmessage: verifyResult.message,\n\t\t\t\tcode: verifyResult.code,\n\t\t\t\tdata: verifyResult.data,\n\t\t\t\tqrcodeData: qrcodeData\n\t\t\t};\n\t\t\t\n\t\t\tif (verifyResult.valid) {\n\t\t\t\t// 验证成功，直接进行打卡，不显示对话框\n\t\t\t\tthis.qrcodeVerified = true;\n\t\t\t\tthis.qrcodeData = qrcodeData;\n\t\t\t\t\n\t\t\t\t// 如果验证的点位与当前点位不同，更新点位信息\n\t\t\t\tif (pointId !== this.pointInfo._id) {\n\t\t\t\t\tconst res = await PatrolApi.getPointDetail(pointId);\n\t\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t\tthis.setCurrentPoint(res.data);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 直接执行打卡逻辑，不需要用户确认\n\t\t\t\tthis.handleQRCodeSuccess();\n\t\t\t} else {\n\t\t\t\t// 验证失败，显示简单的Toast提示\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: verifyResult.message || '二维码验证失败',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error('处理二维码失败', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '二维码验证失败',\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 2000\n\t\t\t});\n\t\t}\n\t},\n\t\n\t// 设置当前点位\n\tsetCurrentPoint(pointData) {\n\t\tif (!pointData) return;\n\t\t\n\t\tthis.pointInfo = pointData;\n\t\t\n\t\t// 设置任务和点位信息\n\t\tif (this.taskInfo) {\n\t\t\tthis.taskInfo.point = this.pointInfo;\n\t\t}\n\t\t\n\t\t// 更新地图标记和计算距离\n\t\tthis.updateMapMarkers();\n\t\tthis.updateCircles();\n\t\t\n\t\tif (this.currentLocation.latitude && this.currentLocation.longitude) {\n\t\t\tthis.calculateDistance();\n\t\t}\n\t\t\n\t\t// 检查点位在当前轮次是否已打卡\n\t\tif (this.currentRound && this.currentRound.points) {\n\t\t\tconst point = this.currentRound.points.find(p => p.point_id === this.pointInfo._id);\n\t\t\tif (point && point.status > 0) {\n\t\t\t\tthis.isRoundValid = false;\n\t\t\t\tthis.roundErrorMessage = '该点位在当前轮次已完成打卡';\n\t\t\t} else {\n\t\t\t\t// 如果未打卡，重新验证轮次有效性\n\t\t\t\tthis.validateCurrentRound();\n\t\t\t}\n\t\t}\n\t},\n\t\n\t// 打卡成功后处理\n\tonCheckinSuccess(result) {\n\t\t// 更新轮次点位状态\n\t\tif (this.currentRound && this.pointInfo) {\n\t\t\tconst pointIndex = this.currentRound.points.findIndex(p => p.point_id === this.pointInfo._id);\n\t\t\tif (pointIndex !== -1) {\n\t\t\t\tthis.currentRound.points[pointIndex].status = 1;\n\t\t\t\tthis.currentRound.points[pointIndex].checkin_time = new Date();\n\t\t\t}\n\t\t\t\n\t\t\t// 更新点位统计\n\t\t\tif (this.currentRound.point_stats) {\n\t\t\t\tthis.currentRound.point_stats.checked++;\n\t\t\t}\n\t\t\t\n\t\t\t// 更新地图标记\n\t\t\tthis.updateMapMarkers();\n\t\t}\n\t\t\n\t\t// 重新验证轮次状态（检查当前点位是否已打卡）\n\t\tthis.validateCurrentRound();\n\t\t\n\t\t// 重新获取下个未打卡点位\n\t\tthis.$nextTick(() => {\n\t\t\tthis.getNextUnCheckedPoint();\n\t\t\t\n\t\t\t// 防止重复跳转\n\t\t\tif (this.isAutoJumping) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查是否有下个点位需要跳转\n\t\t\tconst hasNextPoint = this.nextUnCheckedPoint && this.nextUnCheckedPoint.point_id;\n\t\t\t\n\t\t\tif (hasNextPoint) {\n\t\t\t\t// 设置跳转标记\n\t\t\t\tthis.isAutoJumping = true;\n\t\t\t\t\n\t\t\t\t// 有下个点位，显示成功提示并自动跳转\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '打卡成功，正在跳转下个点位...',\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 1500,\n\t\t\t\t\tmask: false\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 延迟1.5秒后自动跳转到下个点位\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t// 再次检查是否还需要跳转（用户可能在等待期间取消了）\n\t\t\t\t\tif (this.isAutoJumping) {\n\t\t\t\t\t\tthis.goToNextPoint();\n\t\t\t\t\t}\n\t\t\t\t}, 1500);\n\t\t\t} else {\n\t\t\t\t// 没有下个点位，说明本轮次已完成\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '打卡成功，本轮次已完成！',\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 2000,\n\t\t\t\t\tmask: false\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t\t\n\t\t// 设置全局标记，便于返回patrol页面时能自动刷新任务卡片\n\t\tif (getApp().globalData && this.taskInfo && this.taskInfo._id) {\n\t\t\tconsole.log('设置打卡完成标记:', this.taskInfo._id);\n\t\t\tgetApp().globalData.checkedInTaskId = this.taskInfo._id;\n\t\t}\n\t\t\n\t\t// 更新任务状态\n\t\tuni.$emit('task-updated', {\n\t\t\ttask_id: this.taskInfo._id,\n\t\t\tpoint_id: this.pointInfo._id,\n\t\t\tround: this.currentRound ? this.currentRound.round : this.formData.round\n\t\t});\n\t},\n\t\n\t// 返回上一页\n\tgoBack() {\n\t\tuni.navigateBack();\n\t},\n\t\n\t// 停止位置监听\n\tstopLocationWatch() {\n\t\tif (this.locationWatchId) {\n\t\t\tuni.stopLocationUpdate();\n\t\t\tuni.offLocationChange();\n\t\t\tthis.locationWatchId = null;\n\t\t}\n\t},\n\t\n\t// 扫码方法\n\tscanQRCode() {\n\t\t// 检查相机权限\n\t\tuni.authorize({\n\t\t\tscope: 'scope.camera',\n\t\t\tsuccess: () => {\n\t\t\t\tthis.showScanner = true;\n\t\t\t},\n\t\t\tfail: () => {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '需要相机权限才能扫码，请在设置中允许使用相机',\n\t\t\t\t\tconfirmText: '去设置',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.openSetting();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t},\n\t\n\t// 处理扫码结果\n\tasync onScanCode(e) {\n\t\ttry {\n\t\t\tconst result = e.detail.result;\n\t\t\tif (!result) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '无法识别二维码',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 提供震动反馈\n\t\t\tuni.vibrateShort({\n\t\t\t\tfail: function() {\n\t\t\t\t\tuni.vibrateLong();\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 记录开始时间\n\t\t\tconst startTime = Date.now();\n\t\t\t\n\t\t\t// 显示加载提示\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '正在校验打卡信息，请稍候...',\n\t\t\t\tmask: true\n\t\t\t});\n\t\t\t\n\t\t\t// 使用nextTick确保在下一帧再关闭扫码界面\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.showScanner = false;\n\t\t\t\t\t// 立即更新地图标记\n\t\t\t\t\tthis.updateMapMarkers();\n\t\t\t\t\tthis.updateCircles();\n\t\t\t\t}, 50);\n\t\t\t});\n\t\t\t\n\t\t\ttry {\n\t\t\t\tlet qrcodeData;\n\t\t\t\ttry {\n\t\t\t\t\tqrcodeData = JSON.parse(result);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tawait this.ensureMinLoadingTime(startTime, 1000);\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '不是有效的巡检点二维码',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 检查是否是有效的巡检点二维码\n\t\t\t\tif (!qrcodeData.type || qrcodeData.type !== 'PATROL_CHECK_IN') {\n\t\t\t\t\tawait this.ensureMinLoadingTime(startTime, 1000);\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '不是巡检点二维码',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 获取点位ID\n\t\t\t\tconst pointId = qrcodeData.pid;\n\t\t\t\tif (!pointId) {\n\t\t\t\t\tawait this.ensureMinLoadingTime(startTime, 1000);\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '无效的二维码格式',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 验证二维码\n\t\t\t\tconst verifyResult = await QRCodeUtil.verifyQRCode(result, {\n\t\t\t\t\tpointId: this.pointInfo._id,\n\t\t\t\t\tcheckExpired: false\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 确保loading至少显示1秒\n\t\t\t\tawait this.ensureMinLoadingTime(startTime, 1000);\n\t\t\t\t\n\t\t\t\t// 更新验证结果\n\t\t\t\tthis.qrcodeVerifyResult = {\n\t\t\t\t\tvalid: verifyResult.valid,\n\t\t\t\t\ttitle: verifyResult.valid ? '验证成功' : '验证失败',\n\t\t\t\t\tmessage: verifyResult.message,\n\t\t\t\t\tcode: verifyResult.code,\n\t\t\t\t\tdata: verifyResult.data,\n\t\t\t\t\tqrcodeData: qrcodeData\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tif (verifyResult.valid) {\n\t\t\t\t\t// 验证成功，直接进行打卡，不显示对话框\n\t\t\t\t\tthis.qrcodeVerified = true;\n\t\t\t\t\tthis.qrcodeData = qrcodeData;\n\t\t\t\t\t\n\t\t\t\t\t// 如果验证的点位与当前点位不同，更新点位信息\n\t\t\t\t\tif (pointId !== this.pointInfo._id) {\n\t\t\t\t\t\tconst res = await PatrolApi.getPointDetail(pointId);\n\t\t\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t\t\tthis.setCurrentPoint(res.data);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 直接执行打卡逻辑，不需要用户确认\n\t\t\t\t\tthis.handleQRCodeSuccess();\n\t\t\t\t} else {\n\t\t\t\t\t// 验证失败，显示简单的Toast提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: verifyResult.message || '二维码验证失败',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tawait this.ensureMinLoadingTime(startTime, 1000);\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '二维码验证失败',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tuni.hideLoading();\n\t\t\tconsole.error('扫码处理失败', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '二维码处理失败',\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 1000\n\t\t\t});\n\t\t}\n\t},\n\t\n\t// 处理相机错误\n\thandleCameraError(e) {\n\t\tconsole.error('相机错误:', e);\n\t\tuni.showToast({\n\t\t\ttitle: '相机出错，请重试',\n\t\t\ticon: 'none'\n\t\t});\n\t\tthis.showScanner = false;\n\t},\n\t\n\t// 关闭扫码界面\n\tcloseScanner() {\n\t\tthis.showScanner = false;\n\t},\n\t\n\t// 确保loading显示最少时间\n\tasync ensureMinLoadingTime(startTime, minDuration) {\n\t\tconst elapsedTime = Date.now() - startTime;\n\t\tif (elapsedTime < minDuration) {\n\t\t\tawait new Promise(resolve => setTimeout(resolve, minDuration - elapsedTime));\n\t\t}\n\t\tuni.hideLoading();\n\t},\n\t\n\t// 启动轮次状态更新定时器\n\tstartRoundStatusTimer() {\n\t\t// 先清除已有的定时器\n\t\tthis.stopRoundStatusTimer();\n\t\t\n\t\t// 每秒更新一次轮次状态\n\t\tthis.roundStatusTimer = setInterval(() => {\n\t\t\tif (this.currentRound) {\n\t\t\t\tthis.validateCurrentRound();\n\t\t\t}\n\t\t}, 1000);\n\t},\n\t\n\t// 停止轮次状态更新定时器\n\tstopRoundStatusTimer() {\n\t\tif (this.roundStatusTimer) {\n\t\t\tclearInterval(this.roundStatusTimer);\n\t\t\tthis.roundStatusTimer = null;\n\t\t}\n\t},\n\t\n\t// 获取下个未打卡点位（修复版本：处理位置信息）\n\tgetNextUnCheckedPoint() {\n\t\ttry {\n\t\t\tif (!this.currentRound || !this.currentRound.points || !Array.isArray(this.currentRound.points)) {\n\t\t\t\tthis.nextUnCheckedPoint = null;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 找到当前点位在轮次中的位置\n\t\t\tconst currentPointIndex = this.currentRound.points.findIndex(p => p.point_id === this.pointInfo._id);\n\t\t\t\n\t\t\t// 从当前点位之后找第一个未打卡的点位\n\t\t\tlet unCheckedPoint = null;\n\t\t\tif (currentPointIndex !== -1) {\n\t\t\t\t// 🔥 修复：对于第一个点位(index=0)，也要查找后续未打卡点位\n\t\t\t\tunCheckedPoint = this.currentRound.points.find((point, index) => \n\t\t\t\t\tindex > currentPointIndex && (!point.status || point.status === 0)\n\t\t\t\t);\n\t\t\t}\n\t\t\t\n\t\t\t// 如果当前点位后面没有未打卡的，或者当前点位找不到，则从头找第一个未打卡的\n\t\t\tif (!unCheckedPoint) {\n\t\t\t\tunCheckedPoint = this.currentRound.points.find(point => \n\t\t\t\t\t!point.status || point.status === 0\n\t\t\t\t);\n\t\t\t\t\n\t\t\t\t// 如果找到的未打卡点位就是当前点位，则说明没有下个点位\n\t\t\t\tif (unCheckedPoint && unCheckedPoint.point_id === this.pointInfo._id) {\n\t\t\t\t\tthis.nextUnCheckedPoint = null;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 🔥 修复：确保坐标信息正确提取\n\t\t\tif (unCheckedPoint) {\n\t\t\t\t// 从location对象提取坐标信息（如果存在）\n\t\t\t\tif (unCheckedPoint.location && typeof unCheckedPoint.location === 'object') {\n\t\t\t\t\tunCheckedPoint.latitude = unCheckedPoint.latitude || unCheckedPoint.location.latitude || unCheckedPoint.location.lat;\n\t\t\t\t\tunCheckedPoint.longitude = unCheckedPoint.longitude || unCheckedPoint.location.longitude || unCheckedPoint.location.lng;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tthis.nextUnCheckedPoint = unCheckedPoint;\n\t\t} catch (error) {\n\t\t\tconsole.error('获取下个未打卡点位失败:', error);\n\t\t\tthis.nextUnCheckedPoint = null;\n\t\t}\n\t},\n\t\n\t// 计算下个点位距离文本\n\tgetNextPointDistanceText() {\n\t\tif (!this.nextUnCheckedPoint || \n\t\t\t!this.nextUnCheckedPoint.latitude || \n\t\t\t!this.nextUnCheckedPoint.longitude ||\n\t\t\t!this.currentLocation.latitude || \n\t\t\t!this.currentLocation.longitude) {\n\t\t\treturn '点击查看';\n\t\t}\n\t\t\n\t\ttry {\n\t\t\tconst distance = LocationUtils.calculateDistance(\n\t\t\t\tthis.currentLocation,\n\t\t\t\t{ \n\t\t\t\t\tlatitude: this.nextUnCheckedPoint.latitude, \n\t\t\t\t\tlongitude: this.nextUnCheckedPoint.longitude \n\t\t\t\t}\n\t\t\t);\n\t\t\t\n\t\t\tif (distance < 10) {\n\t\t\t\treturn '很近';\n\t\t\t} else if (distance < 1000) {\n\t\t\t\treturn `${Math.round(distance)}米`;\n\t\t\t} else {\n\t\t\t\treturn `${(distance / 1000).toFixed(1)}公里`;\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('计算下个点位距离失败:', error);\n\t\t\treturn '点击查看';\n\t\t}\n\t},\n\t\n\t// 导航到下个点位\n\tgoToNextPoint() {\n\t\t// 重置跳转标记（无论是自动跳转还是手动点击）\n\t\tthis.isAutoJumping = false;\n\t\t\n\t\tif (!this.nextUnCheckedPoint || !this.taskInfo) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '下个点位信息不完整',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 构建导航参数\n\t\tconst params = {\n\t\t\tpoint_id: this.nextUnCheckedPoint.point_id,\n\t\t\ttask_id: this.taskInfo._id,\n\t\t\tround: this.currentRound ? this.currentRound.round : this.formData.round\n\t\t};\n\t\t\n\t\t// 传递当前有效位置\n\t\tconst locationToUse = this.currentLocation.accuracy <= 100 ? \n\t\t\tthis.currentLocation : \n\t\t\t(this.lastValidLocation || this.currentLocation);\n\t\t\t\n\t\tparams.lat = locationToUse.latitude;\n\t\tparams.lng = locationToUse.longitude;\n\t\tparams.accuracy = locationToUse.accuracy || 0;\n\t\t\n\t\t// 在跳转前停止位置监听\n\t\tthis.stopLocationWatch();\n\t\t\n\t\t// 导航到下个点位的打卡页面\n\t\tuni.redirectTo({\n\t\t\turl: `/pages/patrol_pkg/checkin/index?${Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')}`,\n\t\t\tsuccess: () => {\n\t\t\t\tconsole.log('导航到下个点位成功');\n\t\t\t\t// 跳转成功，标记会在新页面重新初始化，这里不需要重置\n\t\t\t},\n\t\t\tfail: (err) => {\n\t\t\t\tconsole.error('导航失败:', err);\n\t\t\t\t// 如果导航失败，重新开启位置监听\n\t\t\t\tthis.startLocationWatch();\n\t\t\t\t// 重置跳转标记\n\t\t\t\tthis.isAutoJumping = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '导航失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t},\n\t\n\t// 处理扫码打卡按钮点击\n\thandleQRCodeClick() {\n\t\t// 检查是否正在自动跳转中\n\t\tif (this.isAutoJumping) {\n\t\t\tthis.showDisabledButtonFeedback('正在跳转下个点位，请稍后再试');\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 检查是否可以执行扫码打卡\n\t\tif (this.loading) {\n\t\t\tthis.showDisabledButtonFeedback('系统正在处理中，请稍后再试');\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 检查当前点位是否已打卡\n\t\tif (this.currentRound && this.currentRound.points) {\n\t\t\tconst point = this.currentRound.points.find(p => p.point_id === this.pointInfo._id);\n\t\t\tif (point && point.status > 0) {\n\t\t\t\tthis.showDisabledButtonFeedback('该点位在当前轮次已完成打卡');\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 检查轮次是否有效\n\t\tif (!this.isRoundValid) {\n\t\t\tthis.showDisabledButtonFeedback(this.roundErrorMessage || '当前轮次暂不可用，请稍后再试');\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 执行扫码打卡\n\t\tthis.scanQRCode();\n\t},\n\t\n\t// 处理GPS打卡按钮点击\n\thandleGPSClick() {\n\t\t// 检查是否正在自动跳转中\n\t\tif (this.isAutoJumping) {\n\t\t\tthis.showDisabledButtonFeedback('正在跳转下个点位，请稍后再试');\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 检查是否可以执行GPS打卡\n\t\tif (this.loading) {\n\t\t\tthis.showDisabledButtonFeedback('系统正在处理中，请稍后再试');\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 检查当前点位是否已打卡\n\t\tif (this.currentRound && this.currentRound.points) {\n\t\t\tconst point = this.currentRound.points.find(p => p.point_id === this.pointInfo._id);\n\t\t\tif (point && point.status > 0) {\n\t\t\t\tthis.showDisabledButtonFeedback('该点位在当前轮次已完成打卡');\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 检查轮次是否有效\n\t\tif (!this.isRoundValid) {\n\t\t\tthis.showDisabledButtonFeedback(this.roundErrorMessage || '当前轮次暂不可用，请稍后再试');\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 检查是否在范围内（GPS打卡特有）\n\t\tif (!this.isInRange) {\n\t\t\tthis.showDisabledButtonFeedback('您不在打卡范围内，请靠近点位后再试');\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// 执行GPS打卡\n\t\tthis.submitCheckin();\n\t},\n\t\n\t// 显示禁用按钮的反馈（震动+Toast提示）\n\tshowDisabledButtonFeedback(message) {\n\t\t// 震动反馈\n\t\tuni.vibrateShort({\n\t\t\tfail: function() {\n\t\t\t\tuni.vibrateLong();\n\t\t\t}\n\t\t});\n\t\t\n\t\t// 显示Toast提示\n\t\tuni.showToast({\n\t\t\ttitle: message,\n\t\t\ticon: 'none',\n\t\t\tduration: 2000\n\t\t});\n\t},\n\t\n\t// 显示距离提示\n\tshowDistancePopup(message) {\n\t\tthis.distancePopupMessage = message;\n\t\tthis.showDistanceMessage = true;\n\t\t\n\t\t// 2秒后自动关闭\n\t\tsetTimeout(() => {\n\t\t\tthis.showDistanceMessage = false;\n\t\t}, 2000);\n\t},\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n.checkin-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\theight: 100vh;\n\tbackground-color: #F7F8FA;\n\tposition: relative;\n\tpadding-top: calc(var(--status-bar-height) + 24rpx);\n}\n\n/* 导航头样式 */\n.nav-header {\n\tposition: relative;\n\twidth: calc(100% - 48rpx);\n\theight: 88rpx;\n\tmargin: 0 24rpx;\n\tbackground: #FFFFFF;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 100;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n\tmargin-bottom: 12rpx;\n}\n\n.nav-left {\n\tposition: absolute;\n\tleft: 24rpx;\n\theight: 88rpx;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.nav-back {\n\twidth: 88rpx;\n\theight: 88rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-start;\n}\n\n.nav-title {\n\tfont-size: 34rpx;\n\tfont-weight: 600;\n\tcolor: #000000;\n\tline-height: 88rpx;\n}\n\n/* 顶部信息区域样式重构 */\n.info-section {\n\tmargin: 24rpx 24rpx 20rpx;\n\tpadding: 0;\n\tborder-radius: 16rpx;\n\tbackground: #FFFFFF;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n\toverflow: hidden;\n}\n\n.info-content {\n\tpadding: 24rpx;\n}\n\n.status-indicator {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12rpx;\n\tmargin-bottom: 20rpx;\n\t\n\t.indicator-dot {\n\t\twidth: 8rpx;\n\t\theight: 8rpx;\n\t\tborder-radius: 50%;\n\t\tbackground: #FF3B30;\n\t\tposition: relative;\n\t\t\n\t\t&::after {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\ttop: -4rpx;\n\t\t\tleft: -4rpx;\n\t\t\tright: -4rpx;\n\t\t\tbottom: -4rpx;\n\t\t\tbackground: rgba(255, 59, 48, 0.2);\n\t\t\tborder-radius: 50%;\n\t\t\tanimation: pulse 2s infinite;\n\t\t}\n\t}\n\t\n\t.status-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #FF3B30;\n\t\tfont-weight: 500;\n\t}\n\t\n\t&--active {\n\t\t.indicator-dot {\n\t\t\tbackground: #34C759;\n\t\t\t\n\t\t\t&::after {\n\t\t\t\tbackground: rgba(52, 199, 89, 0.2);\n\t\t\t}\n\t\t}\n\t\t\n\t\t.status-text {\n\t\t\tcolor: #34C759;\n\t\t}\n\t}\n}\n\n.distance-info {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\t\n\t.distance-value {\n\t\t.value {\n\t\t\tfont-size: 48rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #1677FF;\n\t\t\tletter-spacing: -1rpx;\n\t\t}\n\t}\n\t\n\t.round-badge {\n\t\tpadding: 8rpx 16rpx;\n\t\tbackground: rgba(22, 119, 255, 0.1);\n\t\tborder-radius: 8rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 6rpx;\n\t\t\n\t\t.uni-icons {\n\t\t\tcolor: #1677FF !important;\n\t\t}\n\t\t\n\t\ttext {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #1677FF;\n\t\t\tfont-weight: 500;\n\t\t}\n\t}\n}\n\n/* 加载状态样式优化 */\n.loading-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\theight: 100%;\n\twidth: 100%;\n\tposition: absolute;\n\ttop: calc(var(--status-bar-height) + 88rpx); /* 调整top值，加上导航头高度 */\n\tleft: 0;\n\tbackground-color: rgba(255, 255, 255, 0.98);\n\tz-index: 999;\n\t\n\t.loading-spinner {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tposition: relative;\n\t\tmargin-bottom: 30rpx;\n\t\t\n\t\t.spinner-circle {\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tborder: 6rpx solid #1677FF;\n\t\t\tborder-top-color: transparent;\n\t\t\tborder-radius: 50%;\n\t\t\tanimation: spin 1s linear infinite;\n\t\t}\n\t}\n\t\n\t.loading-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tletter-spacing: 2rpx;\n\t}\n}\n\n/* 地图区域优化 */\n.map-section {\n\tflex: none;\n\theight: 35vh; /* 减小地图高度 */\n\tposition: relative;\n\tmargin: 20rpx 30rpx;\n\tborder-radius: 24rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\n}\n\n.checkin-map {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.map-controls {\n\tposition: absolute;\n\tright: 24rpx;\n\ttop: 24rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 12rpx;\n\t\n\t.control-btn {\n\t\twidth: 72rpx;\n\t\theight: 72rpx;\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tbackdrop-filter: blur(10px);\n\t\tborder-radius: 16rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t\ttransition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n\t\t\n\t\t.uni-icons {\n\t\t\tcolor: #8F959E !important;\n\t\t}\n\t\t\n\t\t&:active {\n\t\t\ttransform: scale(0.92);\n\t\t\tbackground: rgba(255, 255, 255, 0.98);\n\t\t}\n\t\t\n\t\t&--active {\n\t\t\tbackground: rgba(52, 199, 89, 0.15);\n\t\t\t\n\t\t\t.uni-icons {\n\t\t\t\tcolor: #34C759 !important;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* 内容区域包装器 */\n.content-wrapper {\n\tflex: 1;\n\toverflow: hidden;\n\tpadding-bottom: calc(230rpx + constant(safe-area-inset-bottom)); /* iOS 11.2以下 */\n\tpadding-bottom: calc(230rpx + env(safe-area-inset-bottom)); /* iOS 11.2+ */\n}\n\n\n\n/* 错误提示区域样式 */\n.warning-area {\n\tmargin: 20rpx 30rpx;\n\tpadding: 24rpx;\n\tborder-radius: 24rpx;\n\tbackground: rgba(255, 255, 255, 0.95);\n\tbackdrop-filter: blur(20px);\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\n\tdisplay: flex; /* 添加flex布局 */\n\talign-items: center; /* 垂直居中对齐 */\n\tgap: 12rpx; /* 添加图标和文字之间的间距 */\n\t\n\t.warning-icon {\n\t\tflex-shrink: 0; /* 防止图标缩小 */\n\t\twidth: 36rpx; /* 调整图标容器大小 */\n\t\theight: 36rpx;\n\t\tdisplay: flex; /* 使图标居中 */\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-bottom: 0; /* 移除底部间距 */\n\t}\n\t\n\t.warning-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #8E8E93;\n\t\tflex: 1; /* 文字占据剩余空间 */\n\t\tline-height: 1.4; /* 添加适当的行高 */\n\t}\n}\n\n/* 底部操作区域样式 */\n.action-section {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tpadding: 24rpx;\n\tpadding-bottom: calc(24rpx + constant(safe-area-inset-bottom));\n\tpadding-bottom: calc(24rpx + env(safe-area-inset-bottom));\n\tbackground: #FFFFFF;\n\tbox-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);\n\tz-index: 100;\n}\n\n.checkin-btn-container {\n\tdisplay: flex;\n\tgap: 20rpx;\n\twidth: 100%;\n\tmin-height: 96rpx;\n}\n\n.btn-checkin {\n\tflex: 1;\n\theight: 96rpx;\n\tmin-height: 96rpx;\n\tborder-radius: 20rpx;\n\tcolor: #FFFFFF !important;\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 12rpx;\n\tmargin: 0;\n\tpadding: 0;\n\tposition: relative;\n\toverflow: hidden;\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\t\n\t&.gps-button {\n\t\tbackground: linear-gradient(135deg, #1677FF, #0056D6);\n\t}\n\t\n\t&.qrcode-button {\n\t\tbackground: linear-gradient(135deg, #34C759, #30D158);\n\t}\n\t\n\ttext {\n\t\tcolor: #FFFFFF !important;\n\t}\n\t\n\t&::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: linear-gradient(to bottom, rgba(255,255,255,0.1), transparent);\n\t\topacity: 0;\n\t\ttransition: opacity 0.3s ease;\n\t}\n\t\n\t&:active::before {\n\t\topacity: 1;\n\t}\n\t\n\t&.btn-disabled {\n\t\tbackground: linear-gradient(135deg, #8E8E93, #636366);\n\t\topacity: 0.8;\n\t\tbox-shadow: none;\n\t\t\n\t\ttext {\n\t\t\tcolor: rgba(255, 255, 255, 0.6) !important;\n\t\t}\n\t\t\n\t\t.uni-icons {\n\t\t\topacity: 0.6;\n\t\t}\n\t}\n}\n\n/* 添加GPS精度显示样式 */\n.location-accuracy {\n\tposition: absolute;\n\tleft: 20rpx;\n\tbottom: 20rpx;\n\tbackground: rgba(255, 255, 255, 0.95);\n\tbackdrop-filter: blur(8px);\n\tpadding: 12rpx 20rpx;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\tz-index: 90;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12rpx;\n}\n\n.status-dot {\n\twidth: 12rpx;\n\theight: 12rpx;\n\tborder-radius: 50%;\n\tflex-shrink: 0;\n}\n\n.accuracy-text {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n}\n\n/* 相机容器样式 */\n.camera-container {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tz-index: 999;\n\tbackground-color: #000;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n/* 相机控制栏样式 */\n.camera-controls {\n  position: fixed;\n  left: 0;\n  bottom: 0;\n  width: 100%;\n  height: 180rpx; /* 增加高度，给底部安全区域留空间 */\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: space-around; /* 改为space-around确保均匀分布 */\n  align-items: center;\n  padding-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS < 11.2 */\n  padding-bottom: env(safe-area-inset-bottom); /* 兼容 iOS >= 11.2 */\n}\n\n.control-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  width: 140rpx; /* 统一宽度 */\n  height: 140rpx; /* 统一高度 */\n}\n\n.photo-btn {\n  width: 140rpx;\n  height: 140rpx;\n  border-radius: 70rpx;\n  background-color: rgba(255, 255, 255, 0.2);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.photo-circle {\n  width: 110rpx;\n  height: 110rpx;\n  border-radius: 55rpx;\n  background-color: #FFFFFF;\n  border: 4rpx solid rgba(255, 255, 255, 0.8);\n}\n\n.control-item text {\n  font-size: 24rpx;\n  color: #FFFFFF;\n  margin-top: 8rpx;\n}\n\n/* 修改闪光灯popup样式 */\n.popup-content {\n\tbackground-color: rgba(0, 0, 0, 0.75);\n\tcolor: #FFFFFF;\n\tpadding: 16rpx 32rpx;\n\tborder-radius: 8rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tcolumn-gap: 12rpx;\n\tmargin: 20rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);\n}\n\n.popup-content text {\n\tfont-size: 28rpx;\n}\n\n/* 移除不需要的样式 */\n.flash-status, .flash-indicator {\n\tdisplay: none;\n}\n\n/* 确保删除按钮样式正确 */\n.delete-btn {\n\tposition: absolute;\n\ttop: 10rpx;\n\tright: 10rpx;\n\twidth: 44rpx;\n\theight: 44rpx;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 2;\n}\n\n.checkin-btn-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\tgap: 20rpx;\n\tmargin: 0; /* 移除上下边距 */\n}\n\n.checkin-btn {\n\theight: 90rpx;\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n\tborder-radius: 999rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 0 40rpx;\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tbox-shadow: 0 6rpx 16rpx rgba(22, 119, 255, 0.2);\n\ttransition: all 0.3s ease;\n\tflex: 1;\n\tmax-width: 300rpx;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n\t\n\t.btn-text {\n\t\tmargin-left: 12rpx;\n\t}\n\t\n\t&.qrcode-btn {\n\t\tbackground-color: #07C160;\n\t\tbox-shadow: 0 6rpx 16rpx rgba(7, 193, 96, 0.2);\n\t}\n}\n\n/* 二维码验证状态样式 */\n.qrcode-verified {\n\tmargin: 12rpx 0;\n\tpadding: 8rpx 16rpx;\n\tbackground: rgba(7, 193, 96, 0.1);\n\tborder-radius: 8rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tz-index: 95; /* 确保在GPS精度显示之上 */\n\t\n\ttext {\n\t\tfont-size: 26rpx;\n\t\tcolor: #07C160;\n\t\tmargin-left: 8rpx;\n\t\tfont-weight: 500;\n\t}\n}\n\n/* 距离提示弹窗样式 */\n.distance-popup-content {\n\tbackground-color: rgba(0, 0, 0, 0.75);\n\tcolor: #FFFFFF;\n\tpadding: 16rpx 32rpx;\n\tborder-radius: 8rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tcolumn-gap: 12rpx;\n\tmargin: 20rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);\n}\n\n.distance-popup-text {\n\tfont-size: 28rpx;\n}\n\n/* 扫码相机样式 */\n.scanner-container {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100vh;\n\tbackground-color: #000000;\n\tz-index: 999;\n}\n\n.scanner-camera {\n\twidth: 100%;\n\theight: 100vh;\n}\n\n.scan-frame {\n\tposition: absolute;\n\tleft: 50%;\n\ttop: 40%;\n\ttransform: translate(-50%, -50%);\n\twidth: 500rpx;  /* 增大扫码框尺寸 */\n\theight: 500rpx;\n\t\n\t.frame-line {\n\t\tposition: absolute;\n\t\tbackground-color: rgba(255, 255, 255, 0.25);  /* 增加边线颜色透明度，使其更明显 */\n\t}\n\t\n\t.frame-line.top, .frame-line.bottom {\n\t\tleft: 40rpx;   /* 稍微减小边距 */\n\t\tright: 40rpx;\n\t\theight: 2px;   /* 增加边线宽度 */\n\t}\n\t\n\t.frame-line.left, .frame-line.right {\n\t\ttop: 40rpx;    /* 稍微减小边距 */\n\t\tbottom: 40rpx;\n\t\twidth: 2px;    /* 增加边线宽度 */\n\t}\n\t\n\t.frame-line.top { top: 0; }\n\t.frame-line.right { right: 0; }\n\t.frame-line.bottom { bottom: 0; }\n\t.frame-line.left { left: 0; }\n\t\n\t.corner-box {\n\t\tposition: absolute;\n\t\twidth: 28rpx;   /* 调小角标尺寸 */\n\t\theight: 28rpx;\n\t\tborder-color: #34C759;\n\t\tborder-style: solid;\n\t\tborder-width: 3rpx;    /* 调细边框 */\n\t\tbackground-color: transparent;\n\t\tbox-shadow: 0 0 8rpx rgba(52, 199, 89, 0.3);  /* 添加微弱发光效果 */\n\t}\n\t\n\t.left-top {\n\t\tleft: 0;\n\t\ttop: 0;\n\t\tborder-right: none;\n\t\tborder-bottom: none;\n\t}\n\t\n\t.right-top {\n\t\tright: 0;\n\t\ttop: 0;\n\t\tborder-left: none;\n\t\tborder-bottom: none;\n\t}\n\t\n\t.left-bottom {\n\t\tleft: 0;\n\t\tbottom: 0;\n\t\tborder-right: none;\n\t\tborder-top: none;\n\t}\n\t\n\t.right-bottom {\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tborder-left: none;\n\t\tborder-top: none;\n\t}\n\t\n\t.scan-line {\n\t\tposition: absolute;\n\t\tleft: 48rpx;    /* 调整扫描线边距，与frame-line对应 */\n\t\tright: 48rpx;\n\t\ttop: 0;\n\t\theight: 2px;\n\t\tbackground: linear-gradient(to right, \n\t\t\trgba(52, 199, 89, 0),\n\t\t\trgba(52, 199, 89, 0.8),\n\t\t\trgba(52, 199, 89, 0)\n\t\t);  /* 优化扫描线渐变效果 */\n\t\tanimation: scanMove 2s linear infinite;\n\t\tbox-shadow: 0 0 8rpx rgba(52, 199, 89, 0.3);  /* 微弱发光效果 */\n\t}\n}\n\n/* 优化扫描提示文字样式 */\n.scan-tips {\n\tposition: absolute;\n\tleft: 0;\n\tbottom: 300rpx;\n\twidth: 100%;\n\ttext-align: center;\n\t\n\t.tips-text {\n\t\tcolor: #FFFFFF;\n\t\tfont-size: 28rpx;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\n\t\tbackground: rgba(0, 0, 0, 0.15);  /* 调整背景透明度 */\n\t\tpadding: 12rpx 24rpx;\n\t\tborder-radius: 100rpx;\n\t\tdisplay: inline-block;\n\t\tbackdrop-filter: blur(4px);  /* 添加模糊效果 */\n\t}\n}\n\n@keyframes scanMove {\n\t0% {\n\t\ttop: 0;\n\t\topacity: 0.8;\n\t}\n\t50% {\n\t\ttop: calc(100% - 2px);\n\t\topacity: 0.6;  /* 扫描线动画过程中稍微淡化 */\n\t}\n\t100% {\n\t\ttop: 0;\n\t\topacity: 0.8;\n\t}\n}\n\n.scanner-controls {\n\tposition: fixed;\n\tleft: 0;\n\tbottom: 0;\n\twidth: 100%;\n\theight: 180rpx;\n\tbackground: rgba(0, 0, 0, 0.7);\n\tdisplay: flex;\n\tjustify-content: space-around;\n\talign-items: center;\n\tpadding-bottom: constant(safe-area-inset-bottom);\n\tpadding-bottom: env(safe-area-inset-bottom);\n}\n\n.control-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 140rpx;\n\theight: 140rpx;\n\t\n\t.control-icon {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.control-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #FFFFFF;\n\t}\n}\n\n/* 信息卡片样式 */\n.info-card {\n\tmargin: 20rpx 30rpx;\n\tborder-radius: 16rpx;\n\tbackground: #FFFFFF;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n\toverflow: hidden;\n\t\n\t&.next-point {\n\t\tcursor: pointer;\n\t\ttransition: all 0.2s ease;\n\t\t\n\t\t&:active {\n\t\t\ttransform: scale(0.98);\n\t\t\tbox-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);\n\t\t}\n\t}\n}\n\n.card-header {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 24rpx;\n\tgap: 16rpx;\n}\n\n.point-icon {\n\twidth: 44rpx;\n\theight: 44rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tflex-shrink: 0;\n\t\n\t&.current {\n\t\tbackground: #007AFF;\n\t}\n\n\t&.next {\n\t\tbackground: #34C759;\n\t}\n\n\t&.completed {\n\t\tbackground: #FF9500;\n\t}\n\t\n\t&.last {\n\t\tbackground: #5856D6;\n\t}\n}\n\n.point-info {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 4rpx;\n}\n\n.point-title {\n\tfont-size: 24rpx;\n\tcolor: #8E8E93;\n\tfont-weight: 500;\n}\n\n.point-name {\n\tfont-size: 32rpx;\n\tcolor: #1C1C1E;\n\tfont-weight: 600;\n}\n\n/* 统一标签基础样式 */\n.base-badge {\n    display: flex;\n    align-items: center;\n    gap: 6rpx;\n    padding: 4rpx 12rpx;  // 减小上下内边距\n    border-radius: 16rpx;\n    flex-shrink: 0;\n    height: 32rpx;  // 固定高度\n}\n\n.point-status {\n    @extend .base-badge;\n    margin-left: auto;\n    \n    &.checked {\n        background: rgba(52, 199, 89, 0.1);\n        \n        .status-text {\n            color: #34C759;\n        }\n    }\n    \n    &.pending {\n        background: rgba(255, 149, 0, 0.1);\n        \n        .status-text {\n            color: #FF9500;\n        }\n    }\n\n    .status-text {\n        font-size: 22rpx;\n        font-weight: 500;\n        line-height: 32rpx;  // 行高与胶囊高度一致\n    }\n}\n\n.distance-badge {\n    @extend .base-badge;\n    background: rgba(0, 122, 255, 0.1);\n    \n    .uni-icons {\n        color: #007AFF !important;\n        font-size: 24rpx;  // 统一图标大小\n        line-height: 32rpx;  // 行高与胶囊高度一致\n    }\n}\n\n.distance-text {\n    font-size: 22rpx;  // 统一字体大小\n    color: #007AFF;\n    font-weight: 600;\n    line-height: 32rpx;  // 行高与胶囊高度一致\n}\n\n.completed-badge {\n    @extend .base-badge;\n    background: rgba(255, 149, 0, 0.1);\n    \n    .status-text {\n        font-size: 22rpx;\n        color: #FF9500;\n        font-weight: 500;\n        line-height: 32rpx;  // 行高与胶囊高度一致\n    }\n}\n\n.last-badge {\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 12rpx;\n\tbackground: rgba(88, 86, 214, 0.1);\n\tflex-shrink: 0;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 6rpx;\n\t\n\t.status-text {\n\t\tfont-size: 22rpx;\n\t\tcolor: #5856D6;\n\t\tfont-weight: 500;\n\t}\n}\n\n.card-details {\n\tpadding: 0 24rpx 24rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 8rpx;\n}\n\n.detail-text {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n/* 下个点位卡片样式优化 */\n.info-card.next-point {\n\t-webkit-tap-highlight-color: transparent; /* 移除默认点击态 */\n\tuser-select: none; /* 防止文本被选中 */\n\t\n\t&:active {\n\t\tbackground-color: #F5F5F5; /* 点击时的背景色 */\n\t\ttransform: scale(0.98); /* 轻微缩小效果 */\n\t\ttransition: all 0.2s ease; /* 平滑过渡 */\n\t}\n}\n\n.custom-distance-message {\n\tposition: fixed;\n\tleft: 24rpx;\n\tright: 24rpx;\n\ttop: calc(var(--status-bar-height) + 24rpx); // 使用系统状态栏高度变量\n\tz-index: 999;\n\tbackground-color: #FFE9E9;\n\theight: 88rpx;\n\tbox-shadow: 0 2px 8px rgba(255, 59, 48, 0.15);\n\tanimation: slideDown 0.3s ease-out;\n\tborder-radius: 16rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\t\n\t.message-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #FF3B30;\n\t\tfont-weight: 600;\n\t\ttext-align: center;\n\t}\n}\n\n@keyframes slideDown {\n\tfrom {\n\t\ttransform: translateY(-24rpx);\n\t\topacity: 0;\n\t}\n\tto {\n\t\ttransform: translateY(0);\n\t\topacity: 1;\n\t}\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571663054\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
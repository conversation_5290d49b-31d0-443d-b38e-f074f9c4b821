<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-wrapper">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <input class="search-input" v-model="searchText" placeholder="搜索公告标题" @confirm="search" confirm-type="search" />
        <uni-icons v-if="searchText" type="clear" size="16" color="#999" @click="clearSearch"></uni-icons>
      </view>
      <text class="search-btn" @click="search">搜索</text>
    </view>
    
    <!-- 分类筛选 -->
    <scroll-view scroll-x class="filter-bar" :show-scrollbar="false">
      <view class="filter-content">
        <view class="filter-item" :class="{ active: activeCategory === 'all' }" @click="filterByCategory('all')">
          全部
        </view>
        <view v-for="(category, index) in categories" :key="index" 
          class="filter-item" 
          :class="{ active: activeCategory === category.value }" 
          @click="filterByCategory(category.value)">
          {{ category.text }}
        </view>
      </view>
    </scroll-view>
    
    <!-- 公告列表 -->
    <unicloud-db ref="udb" v-slot:default="{data, pagination, loading, hasMore, error}" 
      :collection="collectionList" 
      :where="queryCondition" 
      orderby="isTop desc, createTime desc" 
      :getone="false" 
      :field="field" 
      :page-size="20"
      :page-current="1"
      :loadtime="'manual'"
      @load="onDataLoaded" 
      @error="onDataError">
      <p-empty-state v-if="error" 
        :text="error.message" 
        buttonText="重试"
        @buttonClick="refreshList">
      </p-empty-state>
      
      <p-empty-state v-else-if="!data || data.length === 0" 
        text="暂无公告"
        image="/static/empty/empty_todo.png">
      </p-empty-state>
      
      <view v-else class="notice-list">
        <!-- 置顶公告 -->
        <view v-for="item in topNotices" :key="item._id" class="notice-item sticky" @click="handleItemClick(item._id)">
          <view class="notice-header">
            <view class="notice-title-wrapper">
              <text class="sticky-tag">置顶</text>
              <text class="notice-title">{{ item.title }}</text>
            </view>
            <view class="category-tag" :style="{ backgroundColor: getCategoryColor(item.category) }">{{ item.category }}</view>
          </view>
          <view class="notice-info">
            <view class="info-item">
              <uni-icons type="person" size="14" color="#999"></uni-icons>
              <text class="info-text">{{ item.publisherName || '未知' }}</text>
            </view>
            <view class="info-item">
              <uni-icons type="calendar" size="14" color="#999"></uni-icons>
              <text class="info-text">{{ formatDate(item.createTime) }}</text>
            </view>
            <view class="info-item">
              <uni-icons type="eye" size="14" color="#999"></uni-icons>
              <text class="info-text">{{ item.readCount || 0 }}</text>
            </view>
          </view>
          
          <!-- 管理操作按钮 -->
          <view v-if="hasManagePermission" class="notice-actions">
            <view class="action-btn edit" @click.stop="editNotice(item._id)">
              <uni-icons type="compose" size="14" color="#3c9cff"></uni-icons>
              <text class="action-text">编辑</text>
            </view>
            <view class="action-btn delete" @click.stop="confirmDelete(item._id)">
              <uni-icons type="trash" size="14" color="#fa3534"></uni-icons>
              <text class="action-text">删除</text>
            </view>
          </view>
        </view>
        
        <!-- 普通公告 -->
        <view v-for="item in normalNotices" :key="item._id" class="notice-item" @click="handleItemClick(item._id)">
          <view class="notice-header">
            <view class="notice-title-wrapper">
              <text class="notice-title">{{ item.title }}</text>
            </view>
            <view class="category-tag" :style="{ backgroundColor: getCategoryColor(item.category) }">{{ item.category }}</view>
          </view>
          <view class="notice-info">
            <view class="info-item">
              <uni-icons type="person" size="14" color="#999"></uni-icons>
              <text class="info-text">{{ item.publisherName || '未知' }}</text>
            </view>
            <view class="info-item">
              <uni-icons type="calendar" size="14" color="#999"></uni-icons>
              <text class="info-text">{{ formatDate(item.createTime) }}</text>
            </view>
            <view class="info-item">
              <uni-icons type="eye" size="14" color="#999"></uni-icons>
              <text class="info-text">{{ item.readCount || 0 }}</text>
            </view>
          </view>
          
          <!-- 管理操作按钮 -->
          <view v-if="hasManagePermission" class="notice-actions">
            <view class="action-btn edit" @click.stop="editNotice(item._id)">
              <uni-icons type="compose" size="14" color="#3c9cff"></uni-icons>
              <text class="action-text">编辑</text>
            </view>
            <view class="action-btn delete" @click.stop="confirmDelete(item._id)">
              <uni-icons type="trash" size="14" color="#fa3534"></uni-icons>
              <text class="action-text">删除</text>
            </view>
          </view>
        </view>
        
        <uni-load-more :status="loading?'loading':(hasMore ? 'more' : 'noMore')"></uni-load-more>
      </view>
    </unicloud-db>
    
    <!-- 发布按钮 -->
    <view v-if="hasManagePermission && showFab" class="publish-btn" @click="goToAdd">
      <uni-icons type="plusempty" size="20" color="#fff"></uni-icons>
    </view>
  </view>
</template>

<script>
  const db = uniCloud.database()
  import PEmptyState from '@/components/p-empty-state/p-empty-state.vue';
  
  export default {
    components: {
      PEmptyState
    },
    data() {
      return {
        categories: [
          { value: '公告通知', text: '公告通知' },
          { value: '重要通知', text: '重要通知' },
          { value: '活动通知', text: '活动通知' },
          { value: '其他通知', text: '其他通知' }
        ],
        field: 'title,excerpt,readCount,isTop,publisherName,createTime,category',
        collectionList: 'notice',
        searchText: '',
        activeCategory: 'all',
        hasManagePermission: false,
        deleteId: '',
        queryCondition: {},
        loadMore: {
          contentdown: '',
          contentrefresh: '',
          contentnomore: ''
        },
        // 分类颜色映射
        categoryColors: {
          '公告通知': '#4CAF50', // 绿色
          '重要通知': '#FF5722', // 红色
          '活动通知': '#2196F3', // 蓝色
          '其他通知': '#F9AE3D'  // 黄色
        },
        showFab: false,  // 控制悬浮按钮显示
        isLoading: true,  // 添加加载状态控制
        topNotices: [],
        normalNotices: [],
        searchTimer: null, // 搜索防抖定时器
        dataCache: new Map() // 数据缓存
      }
    },
    onLoad() {
      // 设置导航栏标题
      uni.setNavigationBarTitle({
        title: '公告通知'
      })
      
      // 检查用户权限
      this.checkUserPermission()
      
      // 手动加载数据
      this.$nextTick(() => {
        this.loadData()
      })
    },
    onPullDownRefresh() {
      this.refreshList()
    },
    onReachBottom() {
      this.$refs.udb.loadMore()
    },
    methods: {
      // 获取分类颜色
      getCategoryColor(category) {
        return this.categoryColors[category] || '#999';
      },
      // 检查用户权限
      async checkUserPermission() {
        try {
          // 获取当前用户信息
          const currentUserInfo = uniCloud.getCurrentUserInfo()
          
          // 从本地存储获取用户信息
          const userInfo = uni.getStorageSync('uni_id_user_info') || {}
          
          // 检查是否有管理员或编辑权限
          let hasAdminRole = false
          let hasReviserRole = false
          
          // 检查当前用户角色
          if (currentUserInfo && currentUserInfo.role) {
            const roles = Array.isArray(currentUserInfo.role) ? currentUserInfo.role : [currentUserInfo.role]
            hasAdminRole = roles.includes('admin')
            hasReviserRole = roles.includes('reviser')
          }
          
          // 如果currentUserInfo中没有找到角色，尝试从本地存储中获取
          if (!hasAdminRole && !hasReviserRole && userInfo.role) {
            const roles = Array.isArray(userInfo.role) ? userInfo.role : [userInfo.role]
            hasAdminRole = roles.includes('admin')
            hasReviserRole = roles.includes('reviser')
          }
          
          this.hasManagePermission = hasAdminRole || hasReviserRole
        } catch (e) {
          console.error('检查用户权限失败:', e)
          this.hasManagePermission = false
        }
      },
      
      // 搜索
      search() {
        // 清除之前的定时器
        if (this.searchTimer) {
          clearTimeout(this.searchTimer);
        }
        
        // 防抖处理，300ms后执行搜索
        this.searchTimer = setTimeout(() => {
          this.performSearch();
        }, 300);
      },
      
      // 执行搜索
      performSearch() {
        if (!this.searchText.trim()) {
          uni.showToast({
            title: '请输入搜索内容',
            icon: 'none'
          });
          return;
        }
        
        // 隐藏悬浮按钮
        this.showFab = false;
        this.isLoading = true;
        
        // 构建缓存key
        const cacheKey = `search_${this.searchText.trim()}_${this.activeCategory}`;
        
        // 检查缓存
        if (this.dataCache.has(cacheKey)) {
          const cachedData = this.dataCache.get(cacheKey);
          this.topNotices = cachedData.topNotices;
          this.normalNotices = cachedData.normalNotices;
          this.isLoading = false;
          this.showFab = true;
          return;
        }
        
        // 直接构建查询条件
        const condition = {};
        
        // 搜索条件
        if (this.searchText && this.searchText.trim()) {
          const searchText = this.searchText.trim();
          
          // 使用正则表达式进行模糊搜索，不区分大小写
          condition.title = {
            $regex: searchText,
            $options: 'i'  // 不区分大小写
          };
          
        }
        
        // 保留分类条件
        if (this.activeCategory !== 'all') {
          condition.category = this.activeCategory;
        }
        
        // 直接设置查询条件
        this.queryCondition = condition;
        
        // 强制刷新列表
        this.forceRefreshList();
        
        // 收起键盘
        uni.hideKeyboard();
      },
      
      // 清除搜索
      clearSearch() {
        this.searchText = '';
        
        // 隐藏悬浮按钮
        this.showFab = false;
        this.isLoading = true;
        
        // 直接构建查询条件
        const condition = {};
        
        // 保留分类条件
        if (this.activeCategory !== 'all') {
          condition.category = this.activeCategory;
        }
        
        // 直接设置查询条件
        this.queryCondition = condition;
        
        // 强制刷新列表
        this.forceRefreshList();
      },
      
      // 按分类筛选
      filterByCategory(category) {
        this.activeCategory = category;
        
        // 隐藏悬浮按钮
        this.showFab = false;
        this.isLoading = true;
        
        // 直接构建查询条件
        const condition = {};
        
        // 搜索条件
        if (this.searchText && this.searchText.trim()) {
          const searchText = this.searchText.trim();
          
          // 使用正则表达式进行模糊搜索，不区分大小写
          condition.title = {
            $regex: searchText,
            $options: 'i'  // 不区分大小写
          };
        }
        
        // 分类条件
        if (category !== 'all') {
          condition.category = category;
        }
        
        // 直接设置查询条件
        this.queryCondition = condition;
        
        // 强制刷新列表
        this.forceRefreshList();
      },
      
      // 强制刷新列表
      forceRefreshList() {
        // 确保udb组件已经挂载
        if (this.$refs.udb) {
          // 使用setTimeout确保在下一个事件循环中执行，避免数据绑定问题
          setTimeout(() => {
            this.$refs.udb.loadData({
              clear: true
            }, () => {
              uni.stopPullDownRefresh();
            });
          }, 0);
        } else {
          console.error('udb组件未挂载，无法刷新列表');
          // 延迟尝试刷新
          setTimeout(() => {
            if (this.$refs.udb) {
              this.$refs.udb.loadData({
                clear: true
              }, () => {
                uni.stopPullDownRefresh();
              });
            }
          }, 100);
        }
      },
      
      // 构建查询条件 - 保留此方法以兼容其他可能的调用
      buildQueryCondition() {
        const condition = {};
        
        // 搜索条件
        if (this.searchText && this.searchText.trim()) {
          const searchText = this.searchText.trim();
          
          // 使用正则表达式进行模糊搜索，不区分大小写
          condition.title = {
            $regex: searchText,
            $options: 'i'  // 不区分大小写
          };
          
        }
        
        // 分类条件
        if (this.activeCategory !== 'all') {
          condition.category = this.activeCategory;
        }
        
        this.queryCondition = condition;
        return condition;
      },
      
      // 刷新列表
      refreshList() {
        this.isLoading = true;
        this.showFab = false;
        this.forceRefreshList();
      },
      
      // 点击公告项
      handleItemClick(id) {
        uni.navigateTo({
          url: './detail?id=' + id
        })
      },
      
      // 编辑公告
      editNotice(id) {
        if (!this.hasManagePermission) {
          uni.showToast({
            title: '无权限操作',
            icon: 'none'
          })
          return
        }
        
        uni.navigateTo({
          url: './edit?id=' + id,
          events: {
            // 监听修改页面成功修改数据后, 刷新当前页面数据
            refreshData: () => {
              this.refreshList()
            }
          }
        })
      },
      
      // 确认删除
      confirmDelete(id) {
        if (!this.hasManagePermission) {
          uni.showToast({
            title: '无权限操作',
            icon: 'none'
          })
          return
        }
        
        this.deleteId = id
        uni.showModal({
          title: '删除确认',
          content: '确定要删除这条公告吗？此操作不可恢复。',
          success: (res) => {
            if (res.confirm) {
              this.deleteNotice()
            }
          }
        })
      },
      
      // 删除公告
      async deleteNotice() {
        if (!this.deleteId) return
        
        try {
          uni.showLoading({
            title: '删除中...',
            mask: true
          })
          
          await db.collection('notice')
            .doc(this.deleteId)
            .remove()
          
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
          
          // 刷新列表
          this.refreshList()
        } catch (e) {
          console.error('删除公告失败:', e)
          uni.showModal({
            title: '删除失败',
            content: e.message || '请稍后重试',
            showCancel: false
          })
        } finally {
          uni.hideLoading()
          this.deleteId = ''
        }
      },
      
      // 前往添加页面
      goToAdd() {
        if (!this.hasManagePermission) {
          uni.showToast({
            title: '无权限操作',
            icon: 'none'
          })
          return
        }
        
        uni.navigateTo({
          url: './add',
          events: {
            // 监听添加页面成功添加数据后, 刷新当前页面数据
            refreshData: () => {
              this.refreshList()
            }
          }
        })
      },
      
      // 格式化日期
      formatDate(timestamp) {
        if (!timestamp) return ''
        const date = new Date(timestamp)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      },
      
      // 数据加载完成处理
      onDataLoaded(data) {
        this.isLoading = false;
        
        // 延迟显示悬浮按钮，模拟加载完成后的过渡效果
        setTimeout(() => {
          this.showFab = true;
        }, 300);
        
        // 分离置顶公告和普通公告
        this.topNotices = data.filter(item => item.isTop);
        this.normalNotices = data.filter(item => !item.isTop);
        
        // 缓存数据（限制缓存大小）
        if (this.searchText.trim() || this.activeCategory !== 'all') {
          const cacheKey = `search_${this.searchText.trim()}_${this.activeCategory}`;
          
          // 限制缓存大小，最多保存10个查询结果
          if (this.dataCache.size >= 10) {
            const firstKey = this.dataCache.keys().next().value;
            this.dataCache.delete(firstKey);
          }
          
          this.dataCache.set(cacheKey, {
            topNotices: this.topNotices,
            normalNotices: this.normalNotices,
            timestamp: Date.now()
          });
        }
      },
      
      // 数据加载错误处理
      onDataError(error) {
        this.isLoading = false;
        console.error('加载数据出错:', error);
      },
      
      // 手动加载数据
      loadData() {
        if (this.$refs.udb) {
          this.$refs.udb.loadData();
        }
      },
      
      // 清理过期缓存
      clearExpiredCache() {
        const now = Date.now();
        const expireTime = 5 * 60 * 1000; // 5分钟过期
        
        for (const [key, value] of this.dataCache.entries()) {
          if (now - value.timestamp > expireTime) {
            this.dataCache.delete(key);
          }
        }
              }
      },
      
      // 页面卸载时清理
      onUnload() {
        // 清理搜索定时器
        if (this.searchTimer) {
          clearTimeout(this.searchTimer);
          this.searchTimer = null;
        }
        
        // 清理过期缓存（可选）
        this.clearExpiredCache();
      },
    
    computed: {
      // 使用计算属性优化分类筛选（备用方案）
      filteredNotices() {
        if (!this.data || !Array.isArray(this.data)) return [];
        
        let filtered = this.data;
        
        // 搜索筛选
        if (this.searchText.trim()) {
          const searchText = this.searchText.trim().toLowerCase();
          filtered = filtered.filter(item => 
            item.title && item.title.toLowerCase().includes(searchText)
          );
        }
        
        // 分类筛选
        if (this.activeCategory !== 'all') {
          filtered = filtered.filter(item => item.category === this.activeCategory);
        }
        
        return filtered;
      }
    }
  }
</script>

<style>
.container {
  padding: 24rpx;
  background-color: #f8f9fc;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 30rpx;
  padding: 0 20rpx;
  height: 70rpx;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  background-color: #eef1f8;
  box-shadow: 0 0 0 2px rgba(60, 156, 255, 0.15);
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
  color: #333;
}

.search-btn {
  font-size: 28rpx;
  color: #3c9cff;
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: rgba(60, 156, 255, 0.1);
  transition: all 0.3s ease;
}

.search-btn:active {
  background-color: rgba(60, 156, 255, 0.2);
}

/* 分类筛选 */
.filter-bar {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  white-space: nowrap;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  /* 隐藏滚动条 - 增强版 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  overflow: -moz-scrollbars-none; /* 旧版Firefox */
}

.filter-bar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
  width: 0 !important;
  height: 0 !important;
  background: transparent;
  -webkit-appearance: none;
}

/* 确保在Android设备上也能隐藏滚动条 */
::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none;
  background: transparent;
}

.filter-content {
  display: inline-block;
}

.filter-item {
  display: inline-block;
  padding: 12rpx 32rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f7fa;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}

.filter-item.active {
  color: #fff;
  background-color: #3c9cff;
  box-shadow: 0 4rpx 8rpx rgba(60, 156, 255, 0.25);
  transform: translateY(-2rpx);
}

/* 公告列表 */
.notice-list {
  padding-bottom: 120rpx;
}

.notice-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 6rpx solid transparent;
  transform: translateY(0);
}

.notice-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.notice-item.sticky {
  border-left: 6rpx solid #ff9800;
  background-color: rgba(255, 152, 0, 0.03);
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.notice-title-wrapper {
  flex: 1;
  padding-right: 20rpx;
}

.sticky-tag {
  display: inline-block;
  font-size: 24rpx;
  color: #fff;
  background-color: #ff9800;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
}

.notice-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

.category-tag {
  font-size: 24rpx;
  color: #fff;
  background-color: #3c9cff;
  padding: 6rpx 18rpx;
  border-radius: 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);
}

.notice-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 16rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 8rpx;
}

.info-text {
  font-size: 26rpx;
  color: #999;
  margin-left: 8rpx;
}

.notice-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f5f5f5;
}

/* 为置顶公告添加特殊的分隔线样式 */
.notice-item.sticky .notice-actions {
  border-top: 3rpx dashed rgba(255, 152, 0, 0.6);
  background-color: rgba(255, 152, 0, 0.05);
  border-radius: 0 0 12rpx 12rpx;
  padding-top: 24rpx;
  padding-bottom: 10rpx;
  margin-top: 24rpx;
  margin-left: -30rpx;
  margin-right: -30rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 10rpx 24rpx;
  margin-left: 20rpx;
  border-radius: 30rpx;
  transition: all 0.2s ease;
}

.action-text {
  font-size: 26rpx;
  margin-left: 8rpx;
}

.action-btn.edit {
  color: #3c9cff;
  background-color: rgba(60, 156, 255, 0.1);
}

.action-btn.edit:active {
  background-color: rgba(60, 156, 255, 0.2);
}

.action-btn.delete {
  color: #fa3534;
  background-color: rgba(250, 53, 52, 0.1);
}

.action-btn.delete:active {
  background-color: rgba(250, 53, 52, 0.2);
}

/* 发布按钮 */
.publish-btn {
  position: fixed;
  right: 40rpx;
  bottom: 100rpx;
  width: 110rpx;
  height: 110rpx;
  background: linear-gradient(135deg, #4290f7 0%, #3c9cff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(60, 156, 255, 0.35);
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: fadeIn 0.5s ease;
}

.publish-btn:active {
  transform: translateY(5rpx);
  box-shadow: 0 2rpx 8rpx rgba(60, 156, 255, 0.25);
}

.publish-btn:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: rgba(60, 156, 255, 0.8);
  z-index: -1;
  opacity: 0.5;
  transform: scale(0.9);
  transition: all 0.3s;
}

.publish-btn:active:after {
  transform: scale(1.1);
  opacity: 0;
}

/* 错误和空状态 */
.error-wrapper, .empty-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text, .error-text {
  font-size: 28rpx;
  color: #999;
  margin: 20rpx 0;
}

.retry-btn {
  font-size: 28rpx;
  color: #fff;
  background-color: #3c9cff;
  padding: 14rpx 40rpx;
  border-radius: 30rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(60, 156, 255, 0.25);
}

/* 页面加载动画 - 简化版 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notice-item {
  animation: fadeInUp 0.2s ease-out;
  animation-fill-mode: both;
}

/* 移除复杂的延迟动画 */
.notice-item:nth-child(n) {
  animation-delay: 0s;
}

/* 添加动画定义 - 简化版 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>

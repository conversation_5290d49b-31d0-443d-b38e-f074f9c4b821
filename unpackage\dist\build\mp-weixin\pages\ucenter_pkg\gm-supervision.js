(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/ucenter_pkg/gm-supervision"],{"0af6":function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("357b"),n("861b");a(n("3240"));var s=a(n("8b9d"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(s.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"45f3":function(e,t,n){"use strict";var a=n("c656"),s=n.n(a);s.a},"47fd":function(e,t,n){"use strict";n.r(t);var a=n("b601"),s=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=s.a},"8b9d":function(e,t,n){"use strict";n.r(t);var a=n("f069"),s=n("47fd");for(var r in s)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return s[e]}))}(r);n("45f3");var o=n("828b"),i=Object(o["a"])(s["default"],a["b"],a["c"],!1,null,"2bf240b9",null,!1,a["a"],void 0);t["default"]=i.exports},b601:function(e,t,n){"use strict";(function(e,a){var s=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=s(n("7eb4")),o=s(n("ee10")),i=n("7fc2"),l={data:function(){return{taskList:[],taskStats:{assigned:0,pending:0,completed:0,overdue:0},currentFilter:"all",loading:!1,loadingStatus:"more",responsibleUsers:{},currentUserId:"",userRole:[],showModal:!1,modalInput:"",modalData:{title:"",label:"",placeholder:"",confirmText:"确认",callback:null,taskId:null},lastRefreshTime:null}},computed:{filteredTaskList:function(){var e=this;return"all"===this.currentFilter?this.taskList:"overdue"===this.currentFilter?this.taskList.filter((function(t){return e.isOverdue(t)})):this.taskList.filter((function(t){return t.workflowStatus===e.currentFilter}))}},onLoad:function(){var e=this;return(0,o.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getUserInfo();case 2:e.checkPermission()&&e.loadTaskData();case 3:case"end":return t.stop()}}),t)})))()},onShow:function(){this.loadTaskData(),e.$on("feedback-updated",this.handleTaskUpdate),e.$on("ucenter-need-refresh",this.handleTaskUpdate),e.$on("cross-device-update-detected",this.handleCrossDeviceUpdate)},onHide:function(){e.$off("feedback-updated",this.handleTaskUpdate),e.$off("ucenter-need-refresh",this.handleTaskUpdate),e.$off("cross-device-update-detected",this.handleCrossDeviceUpdate)},onPullDownRefresh:function(){this.loadTaskData().then((function(){e.stopPullDownRefresh()}))},methods:{checkPermission:function(){if(!this.currentUserId)return e.showModal({title:"请先登录",content:"需要登录后才能访问此页面",showCancel:!1,success:function(){e.navigateBack()}}),!1;var t=this.userRole.some((function(e){return["GM","admin"].includes(e)}));return!!t||(e.showModal({title:"权限不足",content:"只有厂长才能访问此页面",showCancel:!1,success:function(){e.navigateBack()}}),!1)},getUserInfo:function(){var e=this;return(0,o.default)(r.default.mark((function t(){var n,s,o,i;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=a.getCurrentUserInfo(),e.currentUserId=n.uid,e.currentUserId){t.next=5;break}return t.abrupt("return");case 5:return s=a.database(),t.next=8,s.collection("uni-id-users").where("'_id' == $cloudEnv_uid").field("role, _id").get();case 8:o=t.sent,i=o.result,i.data&&i.data.length>0?e.userRole=i.data[0].role||[]:e.userRole=[],t.next=18;break;case 13:t.prev=13,t.t0=t["catch"](0),console.error("❌ 获取用户信息失败:",t.t0),e.currentUserId="",e.userRole=[];case 18:case"end":return t.stop()}}),t,null,[[0,13]])})))()},loadTaskData:function(){var t=arguments,n=this;return(0,o.default)(r.default.mark((function s(){var o,i,l;return r.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:return o=t.length>0&&void 0!==t[0]&&t[0],o||(n.loading=!0,n.loadingStatus="loading"),console.log("开始加载厂长监督任务数据...",o?"(静默刷新)":""),s.prev=3,s.next=6,a.callFunction({name:"feedback-list",data:{action:"getGMSupervisionTasks"}});case 6:if(i=s.sent,!i.result||0!==i.result.code){s.next=13;break}n.taskList=i.result.data.list||[],n.taskStats=i.result.data.stats||n.taskStats,n.responsibleUsers=i.result.data.responsibleUsers||{},s.next=15;break;case 13:throw console.error("云函数返回错误:",i.result),new Error((null===(l=i.result)||void 0===l?void 0:l.message)||"获取任务数据失败");case 15:s.next=21;break;case 17:s.prev=17,s.t0=s["catch"](3),console.error("加载任务数据失败:",s.t0),e.showToast({title:s.t0.message||"加载失败",icon:"error",duration:3e3});case 21:return s.prev=21,o||(n.loading=!1,n.loadingStatus="more"),s.finish(21);case 24:case"end":return s.stop()}}),s,null,[[3,17,21,24]])})))()},setFilter:function(e){this.currentFilter=e},filterByStatus:function(e){this.setFilter(e)},getStatusText:function(e){return{assigned_to_responsible:"执行中",completed_by_responsible:"待确认",final_completed:"已完成"}[e]||"未知状态"},getResponsibleName:function(e){var t=this.responsibleUsers[e];return t?t.nickname||t.username||"未知":"未指派"},getTimeLabel:function(e){return"assigned_to_responsible"===e.workflowStatus?"指派时间：":"completed_by_responsible"===e.workflowStatus?"完成时间：":"final_completed"===e.workflowStatus?"确认时间：":"创建时间："},getTimeValue:function(e){var t;return t="assigned_to_responsible"===e.workflowStatus?e.assignedTime:"completed_by_responsible"===e.workflowStatus?e.completedByResponsibleTime:"final_completed"===e.workflowStatus?e.finalCompletedTime:e.createTime,t?(0,i.formatDate)(t,"MM-DD HH:mm"):"未知"},isOverdue:function(e){if("assigned_to_responsible"!==e.workflowStatus)return!1;if(!e.assignedTime)return!1;return Date.now()-e.assignedTime>12096e5},isWarning:function(e){if("assigned_to_responsible"!==e.workflowStatus)return!1;if(!e.assignedTime)return!1;var t=Date.now()-e.assignedTime;return t>6048e5&&t<=12096e5},handleTaskUpdate:function(){this.loadTaskData()},handleCrossDeviceUpdate:function(e){if(e.silent&&this.currentUserId){var t=this.shouldRefreshOnCrossDeviceUpdate(e);t&&(console.log("🏭 厂长监督页面收到跨设备更新通知，静默刷新数据"),this.silentRefreshData())}},shouldRefreshOnCrossDeviceUpdate:function(e){var t=["workflow_status_changed","feedback_submitted","gm_final_confirm","task_assigned"],n=e.updateTypes&&e.updateTypes.some((function(e){return t.includes(e)})),a=Date.now();return!(this.lastRefreshTime&&a-this.lastRefreshTime<1e4)&&n},silentRefreshData:function(){var e=this;return(0,o.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.lastRefreshTime=Date.now(),t.next=4,e.loadTaskData(!0);case 4:t.next=9;break;case 6:t.prev=6,t.t0=t["catch"](0),console.error("静默刷新失败:",t.t0);case 9:case"end":return t.stop()}}),t,null,[[0,6]])})))()},goToTaskDetail:function(t){e.navigateTo({url:"/pages/feedback_pkg/examine?id=".concat(t._id)})},quickConfirm:function(e){var t=this;this.showCustomModal({title:"确认完成",placeholder:"请输入确认意见。",confirmText:"确认完成",callback:function(n){t.confirmTask(e._id,n)}})},quickReject:function(e){var t=this;this.showCustomModal({title:"退回重做",placeholder:"请详细说明退回原因。",confirmText:"退回重做",callback:function(n){t.rejectTask(e._id,n)}})},confirmTask:function(t,n){var s=this;return(0,o.default)(r.default.mark((function o(){var i,l;return r.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,e.showLoading({title:"确认中..."}),r.next=4,a.callFunction({name:"feedback-workflow",data:{action:"gm_final_confirm",id:t,reason:n}});case 4:if(i=r.sent,!i.result||0!==i.result.code){r.next=12;break}e.showToast({title:"确认成功",icon:"success"}),e.$emit("feedback-updated"),e.$emit("ucenter-need-refresh",{id:t}),s.loadTaskData(),r.next=13;break;case 12:throw new Error((null===(l=i.result)||void 0===l?void 0:l.message)||"确认失败");case 13:r.next=19;break;case 15:r.prev=15,r.t0=r["catch"](0),console.error("确认任务失败:",r.t0),e.showToast({title:r.t0.message||"确认失败",icon:"error"});case 19:return r.prev=19,e.hideLoading(),r.finish(19);case 22:case"end":return r.stop()}}),o,null,[[0,15,19,22]])})))()},rejectTask:function(t,n){var s=this;return(0,o.default)(r.default.mark((function o(){var i,l;return r.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,e.showLoading({title:"退回中..."}),r.next=4,a.callFunction({name:"feedback-workflow",data:{action:"updateWorkflowStatus",id:t,workflowStatus:"assigned_to_responsible",rejectReason:n}});case 4:if(i=r.sent,!i.result||0!==i.result.code){r.next=12;break}e.showToast({title:"已退回重做",icon:"success"}),e.$emit("feedback-updated"),e.$emit("ucenter-need-refresh",{id:t}),s.loadTaskData(),r.next=13;break;case 12:throw new Error((null===(l=i.result)||void 0===l?void 0:l.message)||"退回失败");case 13:r.next=19;break;case 15:r.prev=15,r.t0=r["catch"](0),console.error("退回任务失败:",r.t0),e.showToast({title:r.t0.message||"退回失败",icon:"error"});case 19:return r.prev=19,e.hideLoading(),r.finish(19);case 22:case"end":return r.stop()}}),o,null,[[0,15,19,22]])})))()},getEmptyText:function(){return{all:"暂无指派任务",assigned_to_responsible:"暂无执行中的任务",completed_by_responsible:"暂无待确认的任务",overdue:"暂无超时任务"}[this.currentFilter]||"暂无数据"},showCustomModal:function(e){this.modalData={title:e.title||"",label:e.label||"",placeholder:e.placeholder||"",confirmText:e.confirmText||"确认",callback:e.callback||null},this.modalInput="",this.showModal=!0},closeModal:function(){this.showModal=!1,this.modalInput="",this.modalData={title:"",label:"",placeholder:"",confirmText:"确认",callback:null}},confirmModal:function(){var t=this.modalInput.trim();t?(this.modalData.callback&&this.modalData.callback(t),this.closeModal()):e.showToast({title:"请输入内容",icon:"none"})},handleInputFocus:function(){}}};t.default=l}).call(this,n("df3c")["default"],n("861b")["uniCloud"])},c656:function(e,t,n){},f069:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.filteredTaskList.length),a=n>0?e.__map(e.filteredTaskList,(function(t,n){var a=e.__get_orig(t),s=e.getStatusText(t.workflowStatus),r=e.getResponsibleName(t.responsibleUserId),o=e.getTimeLabel(t),i=e.isOverdue(t),l=e.isWarning(t),u=e.getTimeValue(t),c="assigned_to_responsible"===t.workflowStatus?e.isOverdue(t):null,d="assigned_to_responsible"!==t.workflowStatus||c?null:e.isWarning(t);return{$orig:a,m0:s,m1:r,m2:o,m3:i,m4:l,m5:u,m6:c,m7:d}})):null,s=n>0?null:e.getEmptyText(),r=e.showModal?e.modalInput.length:null;e.$mp.data=Object.assign({},{$root:{g0:n,l0:a,m8:s,g1:r}})},s=[]}},[["0af6","common/runtime","common/vendor"]]]);
(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/ucenter_pkg/gm-supervision"],{"0af6":function(e,t,a){"use strict";(function(e,t){var n=a("47a9");a("357b"),a("861b");n(a("3240"));var r=n(a("8b9d"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(r.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"47fd":function(e,t,a){"use strict";a.r(t);var n=a("b601"),r=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(s);t["default"]=r.a},"8b9d":function(e,t,a){"use strict";a.r(t);var n=a("e853"),r=a("47fd");for(var s in r)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(s);a("9fc16");var o=a("828b"),i=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"649ce15b",null,!1,n["a"],void 0);t["default"]=i.exports},"9fc16":function(e,t,a){"use strict";var n=a("ff93"),r=a.n(n);r.a},b601:function(e,t,a){"use strict";(function(e,n){var r=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=r(a("7eb4")),o=r(a("7ca3")),i=r(a("af34")),l=r(a("ee10")),u=a("7fc2");function c(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function d(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?c(Object(a),!0).forEach((function(t){(0,o.default)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var f={data:function(){return{taskList:[],taskStats:{assigned:0,pending:0,completed:0,overdue:0},currentFilter:"all",pagination:{page:1,size:20,total:0,hasMore:!0},loading:!1,loadingMore:!1,loadingStatus:"more",responsibleUsers:{},currentUserId:"",userRole:[],showModal:!1,modalInput:"",modalData:{title:"",label:"",placeholder:"",confirmText:"确认",callback:null,taskId:null},lastRefreshTime:null}},computed:{filteredTaskList:function(){return this.taskList}},onLoad:function(){var e=this;return(0,l.default)(s.default.mark((function t(){return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getUserInfo();case 2:e.checkPermission()&&e.loadTaskData();case 3:case"end":return t.stop()}}),t)})))()},onShow:function(){this.loadTaskData(),e.$on("feedback-updated",this.handleTaskUpdate),e.$on("ucenter-need-refresh",this.handleTaskUpdate),e.$on("cross-device-update-detected",this.handleCrossDeviceUpdate)},onHide:function(){e.$off("feedback-updated",this.handleTaskUpdate),e.$off("ucenter-need-refresh",this.handleTaskUpdate),e.$off("cross-device-update-detected",this.handleCrossDeviceUpdate)},onPullDownRefresh:function(){this.pagination.page=1,this.loadTaskData().then((function(){e.stopPullDownRefresh()}))},methods:{checkPermission:function(){if(!this.currentUserId)return e.showModal({title:"请先登录",content:"需要登录后才能访问此页面",showCancel:!1,success:function(){e.navigateBack()}}),!1;var t=this.userRole.some((function(e){return["GM","admin"].includes(e)}));return!!t||(e.showModal({title:"权限不足",content:"只有厂长才能访问此页面",showCancel:!1,success:function(){e.navigateBack()}}),!1)},getUserInfo:function(){var e=this;return(0,l.default)(s.default.mark((function t(){var a,r,o,i;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,a=n.getCurrentUserInfo(),e.currentUserId=a.uid,e.currentUserId){t.next=5;break}return t.abrupt("return");case 5:return r=n.database(),t.next=8,r.collection("uni-id-users").where("'_id' == $cloudEnv_uid").field("role, _id").get();case 8:o=t.sent,i=o.result,i.data&&i.data.length>0?e.userRole=i.data[0].role||[]:e.userRole=[],t.next=18;break;case 13:t.prev=13,t.t0=t["catch"](0),console.error("❌ 获取用户信息失败:",t.t0),e.currentUserId="",e.userRole=[];case 18:case"end":return t.stop()}}),t,null,[[0,13]])})))()},loadTaskData:function(){var t=arguments,a=this;return(0,l.default)(s.default.mark((function r(){var o,l,u,c;return s.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return o=t.length>0&&void 0!==t[0]&&t[0],l=t.length>1&&void 0!==t[1]&&t[1],l?(a.loadingMore=!0,a.loadingStatus="loading"):o||(a.loading=!0,a.loadingStatus="loading"),l||o||(a.pagination.page=1),console.log("开始加载厂长监督任务数据...",o?"(静默刷新)":l?"(加载更多)":"(首次加载)"),r.prev=5,r.next=8,n.callFunction({name:"feedback-list",data:{action:"getGMSupervisionTasks",page:a.pagination.page,size:a.pagination.size,filter:a.currentFilter}});case 8:if(u=r.sent,!u.result||0!==u.result.code){r.next=16;break}a.taskList=l?[].concat((0,i.default)(a.taskList),(0,i.default)(u.result.data.list||[])):u.result.data.list||[],u.result.data.pagination&&(a.pagination.total=u.result.data.pagination.total,a.pagination.hasMore=u.result.data.pagination.hasMore),!l&&u.result.data.stats&&(a.taskStats=u.result.data.stats),u.result.data.responsibleUsers&&(a.responsibleUsers=d(d({},a.responsibleUsers),u.result.data.responsibleUsers)),r.next=18;break;case 16:throw console.error("云函数返回错误:",u.result),new Error((null===(c=u.result)||void 0===c?void 0:c.message)||"获取任务数据失败");case 18:r.next=24;break;case 20:r.prev=20,r.t0=r["catch"](5),console.error("加载任务数据失败:",r.t0),o||e.showToast({title:r.t0.message||"加载失败",icon:"error",duration:3e3});case 24:return r.prev=24,l?a.loadingMore=!1:o||(a.loading=!1),a.loadingStatus=a.pagination.hasMore?"more":"noMore",r.finish(24);case 28:case"end":return r.stop()}}),r,null,[[5,20,24,28]])})))()},setFilter:function(e){this.currentFilter!==e&&(this.currentFilter=e,this.pagination.page=1,this.loadTaskData())},filterByStatus:function(e){this.setFilter(e)},getStatusText:function(e){return{assigned_to_responsible:"执行中",completed_by_responsible:"待确认",final_completed:"已完成"}[e]||"未知状态"},getResponsibleName:function(e){var t=this.responsibleUsers[e];return t?t.nickname||t.username||"未知":"未指派"},getTimeLabel:function(e){return"assigned_to_responsible"===e.workflowStatus?"指派时间：":"completed_by_responsible"===e.workflowStatus?"完成时间：":"final_completed"===e.workflowStatus?"确认时间：":"创建时间："},getTimeValue:function(e){var t;return t="assigned_to_responsible"===e.workflowStatus?e.assignedTime:"completed_by_responsible"===e.workflowStatus?e.completedByResponsibleTime:"final_completed"===e.workflowStatus?e.finalCompletedTime:e.createTime,t?(0,u.formatDate)(t,"MM-DD HH:mm"):"未知"},isOverdue:function(e){if("assigned_to_responsible"!==e.workflowStatus)return!1;if(!e.assignedTime)return!1;return Date.now()-e.assignedTime>12096e5},isWarning:function(e){if("assigned_to_responsible"!==e.workflowStatus)return!1;if(!e.assignedTime)return!1;var t=Date.now()-e.assignedTime;return t>6048e5&&t<=12096e5},handleTaskUpdate:function(){this.loadTaskData()},handleCrossDeviceUpdate:function(e){if(e.silent&&this.currentUserId){var t=this.shouldRefreshOnCrossDeviceUpdate(e);t&&(console.log("🏭 厂长监督页面收到跨设备更新通知，静默刷新数据"),this.silentRefreshData())}},shouldRefreshOnCrossDeviceUpdate:function(e){var t=["workflow_status_changed","feedback_submitted","gm_final_confirm","task_assigned"],a=e.updateTypes&&e.updateTypes.some((function(e){return t.includes(e)})),n=Date.now();return!(this.lastRefreshTime&&n-this.lastRefreshTime<1e4)&&a},silentRefreshData:function(){var e=this;return(0,l.default)(s.default.mark((function t(){return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.lastRefreshTime=Date.now(),e.pagination.page=1,t.next=5,e.loadTaskData(!0);case 5:t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("静默刷新失败:",t.t0);case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},loadMore:function(){var e=this;return(0,l.default)(s.default.mark((function t(){return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.pagination.hasMore&&!e.loadingMore&&!e.loading){t.next=2;break}return t.abrupt("return");case 2:return e.pagination.page++,t.next=5,e.loadTaskData(!1,!0);case 5:case"end":return t.stop()}}),t)})))()},goToTaskDetail:function(t){e.navigateTo({url:"/pages/feedback_pkg/examine?id=".concat(t._id)})},quickConfirm:function(e){var t=this;this.showCustomModal({title:"确认完成",placeholder:"请输入确认意见。",confirmText:"确认完成",callback:function(a){t.confirmTask(e._id,a)}})},quickReject:function(e){var t=this;this.showCustomModal({title:"退回重做",placeholder:"请详细说明退回原因。",confirmText:"退回重做",callback:function(a){t.rejectTask(e._id,a)}})},confirmTask:function(t,a){var r=this;return(0,l.default)(s.default.mark((function o(){var i,l;return s.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:return s.prev=0,e.showLoading({title:"确认中..."}),s.next=4,n.callFunction({name:"feedback-workflow",data:{action:"gm_final_confirm",id:t,reason:a}});case 4:if(i=s.sent,!i.result||0!==i.result.code){s.next=13;break}e.showToast({title:"确认成功",icon:"success"}),e.$emit("feedback-updated"),e.$emit("ucenter-need-refresh",{id:t}),r.pagination.page=1,r.loadTaskData(),s.next=14;break;case 13:throw new Error((null===(l=i.result)||void 0===l?void 0:l.message)||"确认失败");case 14:s.next=20;break;case 16:s.prev=16,s.t0=s["catch"](0),console.error("确认任务失败:",s.t0),e.showToast({title:s.t0.message||"确认失败",icon:"error"});case 20:return s.prev=20,e.hideLoading(),s.finish(20);case 23:case"end":return s.stop()}}),o,null,[[0,16,20,23]])})))()},rejectTask:function(t,a){var r=this;return(0,l.default)(s.default.mark((function o(){var i,l;return s.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:return s.prev=0,e.showLoading({title:"退回中..."}),s.next=4,n.callFunction({name:"feedback-workflow",data:{action:"updateWorkflowStatus",id:t,workflowStatus:"assigned_to_responsible",rejectReason:a}});case 4:if(i=s.sent,!i.result||0!==i.result.code){s.next=13;break}e.showToast({title:"已退回重做",icon:"success"}),e.$emit("feedback-updated"),e.$emit("ucenter-need-refresh",{id:t}),r.pagination.page=1,r.loadTaskData(),s.next=14;break;case 13:throw new Error((null===(l=i.result)||void 0===l?void 0:l.message)||"退回失败");case 14:s.next=20;break;case 16:s.prev=16,s.t0=s["catch"](0),console.error("退回任务失败:",s.t0),e.showToast({title:s.t0.message||"退回失败",icon:"error"});case 20:return s.prev=20,e.hideLoading(),s.finish(20);case 23:case"end":return s.stop()}}),o,null,[[0,16,20,23]])})))()},getEmptyText:function(){return{all:"暂无指派任务",assigned_to_responsible:"暂无执行中的任务",completed_by_responsible:"暂无待确认的任务",overdue:"暂无超时任务"}[this.currentFilter]||"暂无数据"},showCustomModal:function(e){this.modalData={title:e.title||"",label:e.label||"",placeholder:e.placeholder||"",confirmText:e.confirmText||"确认",callback:e.callback||null},this.modalInput="",this.showModal=!0},closeModal:function(){this.showModal=!1,this.modalInput="",this.modalData={title:"",label:"",placeholder:"",confirmText:"确认",callback:null}},confirmModal:function(){var t=this.modalInput.trim();t?(this.modalData.callback&&this.modalData.callback(t),this.closeModal()):e.showToast({title:"请输入内容",icon:"none"})},handleInputFocus:function(){}}};t.default=f}).call(this,a("df3c")["default"],a("861b")["uniCloud"])},e853:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=(e._self._c,e.filteredTaskList.length),n=a>0?e.__map(e.filteredTaskList,(function(t,a){var n=e.__get_orig(t),r=e.getStatusText(t.workflowStatus),s=e.getResponsibleName(t.responsibleUserId),o=e.getTimeLabel(t),i=e.isOverdue(t),l=e.isWarning(t),u=e.getTimeValue(t),c="assigned_to_responsible"===t.workflowStatus?e.isOverdue(t):null,d="assigned_to_responsible"!==t.workflowStatus||c?null:e.isWarning(t);return{$orig:n,m0:r,m1:s,m2:o,m3:i,m4:l,m5:u,m6:c,m7:d}})):null,r=a>0&&!e.loadingMore?!e.pagination.hasMore&&e.taskList.length>0:null,s=a>0||e.loading?null:e.getEmptyText(),o=e.showModal?e.modalInput.length:null;e.$mp.data=Object.assign({},{$root:{g0:a,l0:n,g1:r,m8:s,g2:o}})},r=[]},ff93:function(e,t,a){}},[["0af6","common/runtime","common/vendor"]]]);
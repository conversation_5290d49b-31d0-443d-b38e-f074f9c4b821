<template>
  <view class="patrol-index">
    <!-- 地图区域 -->
    <p-map
      ref="patrolMap"
      :longitude="mapCenter.longitude"
      :latitude="mapCenter.latitude"
      :marker-data="markers"
      :polyline-data="polylines"
      :circle-data="circles"
      :scale="19"
      :height="mapHeight || '100vh'"
      @marker-tap="onPointMarkerTap"
      @callout-tap="onPointMarkerTap"
    >
      <!-- 地图顶部控件区域 -->
      <view class="map-controls">
        <!-- 控制按钮区域 -->
        <view class="control-buttons">
          <!-- 定位按钮 -->
          <button class="control-btn" @tap="moveToCurrentLocation">
            <uni-icons type="location" size="24" color="#1677FF"></uni-icons>
          </button>
        </view>
      </view>

      <!-- GPS精度显示 -->
      <view class="location-accuracy">
        <view class="status-dot" :style="{ background: getAccuracyColor() }"></view>
        <text class="accuracy-text">GPS精度: {{ currentLocation.accuracy ? currentLocation.accuracy.toFixed(1) : '0' }}米</text>
      </view>
    </p-map>

    <!-- 右下角菜单按钮 -->
    <view class="float-menu-btn" @tap="toggleDrawer">
      <view class="float-menu-btn__inner">
        <uni-icons type="bars" size="22" color="#FFFFFF"></uni-icons>
        <text class="float-menu-btn__text">任务</text>
      </view>
    </view>

    <!-- 天气信息简洁显示 -->
    <view class="weather-compact">
      <p-weather
        :location="currentLocation"
        :mini="true"
        @weather-loaded="onWeatherLoaded"
      ></p-weather>
    </view>

    <!-- 抽屉菜单 -->
    <view 
      class="drawer-mask" 
      :class="{'drawer-mask--active': isDrawerOpen}" 
      :style="maskStyle" 
      @click="closeDrawer"
      @touchmove.stop.prevent
    >
      <view
        class="task-drawer"
        :class="{'task-drawer--active': isDrawerOpen}"
        :style="drawerStyle"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @click.stop
      >
        <!-- 手势区域指示器 -->
        <view class="drawer-handle"></view>

        <!-- 内容区域 -->
        <scroll-view
          class="drawer-content"
          scroll-y
          :scroll-top="currentTranslateY === 0 ? undefined : 0"
        >
          <!-- 抽屉头部区域 -->
          <view class="task-drawer__header">
            <view class="task-drawer__title-container">
              <uni-icons type="calendar" size="20" color="#1677FF"></uni-icons>
              <text class="task-drawer__date">{{ currentDate }}</text>
              <text class="task-drawer__title-text">巡视任务</text>
            </view>
            <view class="task-drawer__refresh-btn" @click="refreshTaskList">
              <uni-icons type="refresh" size="18" color="#1677FF"></uni-icons>
            </view>
            <view class="task-drawer__close-btn" @click="closeDrawer">
              <uni-icons type="closeempty" size="20" color="#666"></uni-icons>
            </view>
          </view>

          <!-- 暂无任务显示 -->
          <p-empty-state
            v-if="taskList.length === 0"
            type="task"
            text="今日暂无巡视任务"
            size="medium"
            :containerStyle="{ padding: '30rpx 0', backgroundColor: '#fff' }"
          ></p-empty-state>

          <!-- 任务列表 -->
          <scroll-view v-else scroll-y class="task-drawer__list">
            <view class="task-drawer__list-content">
              <p-task-card
                v-for="(task, index) in taskList"
                :key="index"
                :task="task"
                :shift="getTaskShift(task)"
                :round-info="getTaskRoundInfo(task)"
                :active="activeTaskId === task._id"
                :user-map="userMap"
                :selected-round-number="selectedRound && selectedRound.taskId === task._id ? selectedRound.round.round : -1"
                @click="onTaskClick(task)"
                @continue="onTaskContinue(task)"
                @view-detail="onTaskViewDetail(task)"
                @select-round="onRoundSelected"
              >
              </p-task-card>
            </view>
          </scroll-view>
        </scroll-view>
      </view>
    </view>

    <!-- 加载中 -->
    <uni-popup ref="loadingPopup" type="center" mask-click="false">
      <uni-load-more status="loading" :content-text="{ contentdown: '加载中' }"></uni-load-more>
    </uni-popup>

    <!-- 消息提示 -->
    <uni-popup ref="messagePopup" type="message">
      <uni-popup-message type="error" :message="errorMessage" :duration="2000"></uni-popup-message>
    </uni-popup>
  </view>
</template>

<script>
import PMap from '@/components/patrol/p-map.vue';
import PWeather from '@/components/patrol/p-weather.vue';
import PTaskCard from '@/components/patrol/p-task-card.vue';
import PEmptyState from '@/components/p-empty-state/p-empty-state.vue';
import { getCurrentLocation } from '@/utils/location-services.js';
import geocoderService from '@/utils/geocoder.js';
import patrolApi from '@/utils/patrol-api.js'; // 需要创建这个API工具文件
import { formatDate, calculateRoundTime, calculateEndTime, isToday } from '@/utils/date.js';

// 统一的轮次排序函数，与任务列表页保持一致
function sortRounds(rounds) {
  if (!rounds || !Array.isArray(rounds) || rounds.length === 0) {
    return [];
  }

  // 使用与任务列表页相同的排序逻辑
  // 按状态分组：未开始和进行中的轮次，已完成和已超时的轮次
  const notStartedOrActive = rounds.filter(
    round => round.status === 0 || round.status === 1
  );
  const completedOrExpired = rounds.filter(
    round => round.status === 2 || round.status === 3
  );

  // 分别排序
  notStartedOrActive.sort((a, b) => a.round - b.round); // 按轮次号升序
  completedOrExpired.sort((a, b) => b.round - a.round); // 按轮次号降序

  // 合并排序后的数组
  return [...notStartedOrActive, ...completedOrExpired];
}

export default {
  components: {
    PMap,
    PWeather,
    PTaskCard,
    PEmptyState
  },
  data() {
    return {
      // 用户位置
      userLocation: null,

      // 当前激活的任务ID
      activeTaskId: '',

      // 当前选中的任务
      currentTask: null,

      // 当前选择的轮次索引，-1表示自动选择（当前/最后一轮）
      selectedRoundIndex: -1,

      // 当前选择的轮次，null表示自动选择
      selectedRound: null,

      // 任务列表
      taskList: [],

      // 任务轮次信息
      taskRoundInfos: {},

      // 任务班次信息
      taskShifts: {},

      // 路线缓存
      routeCache: {},

      // 地图标记点
      markers: [],

      // 地图路线
      polylines: [],

      // 地图圆形区域
      circles: [],

      // 地图中心点
      mapCenter: {
        latitude: 30.0,
        longitude: 120.0
      },

      // 天气信息
      weatherInfo: null,

      // 加载状态
      isLoading: false,

      // 初始加载标志，防止状态跳变
      isInitialLoading: false,

      // 下拉刷新状态
      isRefreshing: false,

      // 空状态
      isEmpty: false,

      // 过滤状态
      filterStatus: 'all', // 'all', 'active', 'completed'

      // 显示地图
      showMap: true,

      // 位置信息
      currentLocation: {
        latitude: 0,
        longitude: 0,
        accuracy: 0,
        altitude: 0,
        speed: 0,
        address: '',
        isLocating: false,
        lastUpdated: 0
      },

      // UI状态
      showFilterPanel: false,
      showTaskDetail: false,
      showWeather: true,

      // 轮次倒计时更新间隔（毫秒）
      roundUpdateInterval: 3000, // 从5秒减少到3秒，进一步提高状态更新响应速度

      // 用户数据映射
      userMap: {},

      // 控制任务选择器是否展开
      isTaskSelectorExpanded: false,

      // 控制侧边抽屉是否打开
      isDrawerOpen: false,

      // 标志，防止重复显示登录提示
      isShowingLoginTip: false,

      touchStartY: 0,
      touchMoveY: 0,
      drawerHeight: 0,
      isDragging: false,
      currentTranslateY: 0,
      drawerStyle: {},
      maskStyle: {},
      lastTouchTime: 0,
      lastDeltaY: 0,

      // 🔥 新增：防抖机制相关变量
      lastLoadTime: 0,
      loadingInProgress: false,
      hasInitialLoaded: false,
    };
  },
  computed: {
    // 当前日期 YYYY-MM-DD
    currentDate() {
      // 使用formatDate工具函数替代手动格式化
      return formatDate(new Date(), 'YYYY-MM-DD');
    },
    // 地图高度
    mapHeight() {
      // 使用固定高度，不再根据底部区域变化
      return '100vh';
    },
    // 当前任务的点位
    currentRoutePoints() {
      if (!this.currentTask) return [];

      // 获取当前任务的点位信息 - 优先使用rounds_detail
      if (this.currentTask.rounds_detail && Array.isArray(this.currentTask.rounds_detail)) {
        // 获取当前轮次
        const taskId = this.currentTask._id;
        let round = null;

        // 如果用户选择了特定轮次，使用选中的轮次
        if (this.selectedRound && this.selectedRound.taskId === taskId) {
          round = this.selectedRound.round;
        }
        // 否则使用系统自动判断的轮次
        else {
          const roundInfo = this.taskRoundInfos[taskId];

          if (roundInfo && roundInfo.status === 'active' && roundInfo.currentRound) {
            round = roundInfo.currentRound;
          } else if (roundInfo && roundInfo.status === 'completed' && roundInfo.lastRound) {
            round = roundInfo.lastRound;
          } else if (roundInfo && roundInfo.status === 'waiting' && roundInfo.nextRound) {
            round = roundInfo.nextRound;
          } else {
            // 如果没有特定轮次，使用第一个轮次
            const rounds = sortRounds(this.currentTask.rounds_detail);
            round = rounds.length > 0 ? rounds[0] : null;
          }
        }

        // 使用轮次中的points数据，确保包含完整的状态信息
        if (round && round.points && Array.isArray(round.points)) {
          return round.points;
        }
      }

      // 如果没有轮次数据，尝试从route_detail获取点位
      if (this.currentTask.route_detail && this.currentTask.route_detail.points) {
        return this.currentTask.route_detail.points;
      }

      return [];
    },
    // 当前任务的轮次列表
    availableRounds() {
      if (!this.currentTask || !this.currentTask.rounds_detail || !Array.isArray(this.currentTask.rounds_detail)) {
        return [];
      }

      return sortRounds(this.currentTask.rounds_detail);
    },

    // 当前轮次信息
    currentRoundInfo() {
      if (!this.currentTask) {
        return { status: 'no_task' };
      }

      const task = this.currentTask;
      return this.getTaskRoundInfo(task);
    },
  },
  onLoad(options) {
    // 初始化位置和任务
    this.initLocationAndTasks();

    // 开始更新轮次倒计时
    this.startRoundUpdateTimer();

    // 添加页面事件监听 - 确保事件名称一致性
    uni.$on('refresh-task-list', this.refreshTaskList);

    // 注册多种可能的任务更新事件，确保能接收到更新消息
    uni.$on('task-updated', this.handleTaskUpdated);
    uni.$on('patrol-task-updated', this.handleTaskUpdated);
    uni.$on('check-in-completed', this.handleTaskUpdated);
  },
  onUnload() {
    // 取消页面事件监听
    uni.$off('refresh-task-list', this.refreshTaskList);
    uni.$off('task-updated', this.handleTaskUpdated);
    uni.$off('patrol-task-updated', this.handleTaskUpdated);
    uni.$off('check-in-completed', this.handleTaskUpdated);

    // 清除轮次更新定时器
    this.clearRoundUpdateTimer();

    // 停止位置监听
    this.stopLocationWatch();
  },
  onShow() {
    if (this.isOnboarding) {
      // 如果正在进行引导，则不执行刷新任务
      this.isOnboarding = false;
    } else if (!this.hasInitialLoaded) {
      // 🔥 首次显示时不刷新，因为 onLoad 已经加载了
      this.hasInitialLoaded = true;
      console.log('首次显示页面，跳过刷新（onLoad已处理）');
    } else {
      // 🔥 只有从其他页面返回时才刷新
      console.log('从其他页面返回，执行刷新');
      // 设置缓冲标记，防止状态快速跳变 - 保持这个标记
      this.isInitialLoading = true;

      // 每次显示页面时都刷新任务列表，确保数据最新
      this.refreshTaskList(false); // 使用静默刷新，避免每次都显示加载提示

      // 检查是否从打卡页面返回并有需要刷新的任务ID
      if (getApp().globalData && getApp().globalData.checkedInTaskId) {
        const refreshTaskId = getApp().globalData.checkedInTaskId;
        // 清除标记
        getApp().globalData.checkedInTaskId = null;

        console.log('检测到打卡页面返回，刷新任务:', refreshTaskId);

        // 标记任务需要刷新，并延迟执行刷新，避免过于频繁的操作
        // 简单的刷新列表通常能覆盖大部分场景
        // 如果问题依然存在，再考虑更精细化的处理

        // 延迟清除缓冲标记，给列表刷新留出时间
        setTimeout(() => {
          this.isInitialLoading = false;
        }, 800); // 稍微延长缓冲时间

      } else {
        // 没有特定任务需要刷新，延迟清除缓冲标记
        setTimeout(() => {
          this.isInitialLoading = false;
        }, 800); // 稍微延长缓冲时间
      }
    }

    // 重要：每次页面显示都重新启动位置监听
    // 这样可以确保从其他页面返回(特别是使用手机手势返回)时位置监听能被正确启动
    console.log('页面显示，重新启动位置监听');
    setTimeout(() => {
      this.restartLocationWatch();
    }, 300); // 稍微延迟启动，避免与其他操作冲突
  },
  methods: {
    // GPS精度颜色判断方法
    getAccuracyColor() {
        const accuracy = this.currentLocation.accuracy;
        if (!accuracy) return '#999999';
        if (accuracy <= 5) return '#34C759';    // 绿色 - 精度极好
        if (accuracy <= 10) return '#00C58E';    // 青色 - 精度良好
        if (accuracy <= 15) return '#FFD60A';   // 黄色 - 精度一般
        if (accuracy <= 20) return '#FF9500';   // 橙色 - 精度较差
        if (accuracy <= 25) return '#FF6B2C';   // 深橙色 - 精度很差
        return '#FF3B30';                       // 红色 - 精度极差
    },

    // 初始化位置和任务
    async initLocationAndTasks() {
      try {
        // 立即设置加载状态
        this.isLoading = true;

        // 获取位置信息
        await this.initLocation();

        // 加载用户数据
        await this.loadUsers();

        // 加载任务列表
        await this.loadTaskList(true);
      } catch (error) {
        console.error('初始化位置和任务出错：', error);
        uni.showToast({
          title: '加载任务失败，请重试',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },

    // 初始化位置信息
    async initLocation() {
      try {
        this.isLoading = true;

        // 获取当前位置
        const location = await getCurrentLocation({
          isHighAccuracy: true,  // 请求高精度定位
          highAccuracyExpireTime: 4000  // 高精度定位超时时间
        });

        // 更新用户位置和当前位置
        this.userLocation = {
          latitude: location.latitude,
          longitude: location.longitude
        };

        // 确保包含完整的位置信息
        this.currentLocation = {
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: location.accuracy || 0,
          altitude: location.altitude || 0,
          speed: location.speed || 0,
          address: '',
          isLocating: false,
          lastUpdated: Date.now()
        };

        // 设置地图中心
        this.mapCenter = {
          latitude: location.latitude,
          longitude: location.longitude
        };

        // 先更新当前位置的范围圈
        this.updateCurrentLocationMarker();

        // 如果有任务，重新构建任务标记
        if (this.currentTask) {
          this.buildTaskMarkers();
        }

        // 获取地址信息
        try {
          const geocodeResult = await geocoderService.reverseGeocoder(this.currentLocation);
          this.currentLocation.address = geocodeResult.address || '';
          const district = geocodeResult.address_component?.district || '';
          uni.setNavigationBarTitle({
            title: `巡视打卡 - ${district}`
          });
        } catch (error) {
          console.error('获取地址信息失败', error);
        }

        // 开始监听位置变化
        this.startLocationWatch();
      } catch (error) {
        console.error('初始化位置失败', error);
        uni.showToast({
          title: '获取位置失败，请检查定位权限',
          icon: 'none'
        });
      } finally {
        setTimeout(() => {
          this.isLoading = false;
        }, 200);
      }
    },

    // 开始监听位置变化
    startLocationWatch() {
      console.log('开始位置监听');

      if (this.locationChangeListener) {
        console.log('位置监听已存在，不需要重新启动');
        return;
      }

      try {
        // 使用微信原生API直接监听位置变化
        wx.startLocationUpdate({
          type: 'gcj02',
          success: () => {
            console.log('位置监听启动成功');

            // 设置位置变化回调
            wx.onLocationChange((res) => {
              // 保存当前的circles数组
              const currentCircles = [...this.circles];

              // 更新当前位置信息
              this.currentLocation = {
                latitude: res.latitude,
                longitude: res.longitude,
                accuracy: res.accuracy || 0,
                altitude: res.altitude || 0,
                speed: res.speed || 0,
                isLocating: false,
                lastUpdated: Date.now()
              };

              // 更新用户位置
              this.userLocation = {
                latitude: res.latitude,
                longitude: res.longitude
              };

              // 恢复circles数组并更新位置标记
              this.circles = currentCircles;
              this.updateCurrentLocationMarker();
            });

            // 标记位置监听已启动
            this.locationChangeListener = true;
          },
          fail: (err) => {
            console.error('启动位置监听失败:', err);
            this.locationChangeListener = false;
          }
        });
      } catch (err) {
        console.error('启动位置监听出错:', err);
        this.locationChangeListener = false;
      }
    },

    // 停止位置监听
    stopLocationWatch() {
      console.log('停止位置监听');

      if (!this.locationChangeListener) {
        console.log('没有活跃的位置监听，无需停止');
        return;
      }

      try {
        // 使用微信原生API停止位置监听
        wx.stopLocationUpdate({
          success: () => {
            console.log('停止位置监听成功');
          },
          fail: (err) => {
            console.error('停止位置监听失败:', err);
          },
          complete: () => {
            try {
              // 无论成功失败，都尝试解绑监听器
              wx.offLocationChange();
            } catch (e) {
              console.error('解除位置监听绑定失败:', e);
            }
            // 重置监听标志
            this.locationChangeListener = false;
          }
        });
      } catch (err) {
        console.error('停止位置监听出错:', err);
        // 确保标志被重置
        this.locationChangeListener = false;
      }
    },

    // 重新启动位置监听（先停止再启动）
    restartLocationWatch() {
      try {
        // 无论当前状态如何，先尝试停止现有监听
        this.forceStopLocationWatch();

        // 短暂延迟后启动新的监听
        setTimeout(() => {
          this.startLocationWatch();
        }, 200);
      } catch (err) {
        console.error('重启位置监听出错:', err);
      }
    },

    // 强制停止位置监听（不检查状态）
    forceStopLocationWatch() {
      try {
        // 使用微信原生API停止和解绑，无论当前状态如何
        wx.stopLocationUpdate();
        wx.offLocationChange();
        this.locationChangeListener = false;
      } catch (err) {
        console.error('强制停止位置监听出错:', err);
        this.locationChangeListener = false;
      }
    },

    // 移动到当前位置
    async moveToCurrentLocation() {
      try {
        // 显示加载中提示
        uni.showLoading({
          title: '获取位置中...',
          mask: true
        });

        // 使用微信原生API获取高精度位置
        const res = await new Promise((resolve, reject) => {
          wx.getLocation({
            type: 'gcj02',
            isHighAccuracy: true,
            highAccuracyExpireTime: 5000,
            success: resolve,
            fail: reject
          });
        });

        // 更新用户位置
        this.userLocation = {
          latitude: res.latitude,
          longitude: res.longitude
        };

        // 更新地图中心点
        this.mapCenter = {
          latitude: res.latitude,
          longitude: res.longitude
        };

        // 如果地图组件存在，移动到新位置
        if (this.$refs.patrolMap) {
          this.$refs.patrolMap.moveToLocation(this.userLocation);
        }

        // 隐藏加载提示
        uni.hideLoading();
      } catch (error) {
        console.error('获取位置失败:', error);

        // 如果获取位置失败，尝试使用现有位置
        if (this.userLocation && this.userLocation.latitude && this.userLocation.longitude) {
          // 更新地图中心点
          this.mapCenter = {
            latitude: this.userLocation.latitude,
            longitude: this.userLocation.longitude
          };

          // 如果地图组件存在，移动到现有位置
          if (this.$refs.patrolMap) {
            this.$refs.patrolMap.moveToLocation(this.userLocation);
          }
        } else {
          // 如果没有位置信息，重新初始化位置
          this.initLocation();
        }

        // 隐藏加载提示
        uni.hideLoading();
      }
    },

    // 加载任务列表
    async loadTaskList(showLoading = false) {
      // 再次检查登录状态
      if (!this.checkLoginState()) {
        return;
      }

      try {
        // 设置初始加载标志，防止状态跳变
        this.isInitialLoading = true;

        if (showLoading) {
          this.showLoading(true);
        }

        // 获取当前用户ID
        let userId = this.getCurrentUserId();
        if (!userId) {
          this.showLoginTip();
          if (showLoading) {
            this.showLoading(false);
          }
          return;
        }

        // 统一使用PatrolApi.call标准格式获取今日任务，与任务列表页保持一致
        const res = await patrolApi.call({
          name: 'patrol-task',
          action: 'getTaskList',
          data: {
            level: 'list',  // 🔥 启用轻量级模式，减少RU消耗
            fields: '_id,name,status,area,patrol_date,shift_id,shift_name,user_id,user_name,route_name,create_date,overall_stats,rounds_detail.round,rounds_detail.name,rounds_detail.status,rounds_detail.start_time,rounds_detail.end_time,rounds_detail.duration,rounds_detail.stats',  // 🔥 字段过滤，排除points数组
            // 🔥 新增：强制个人模式，巡视首页只显示自己的任务
            viewScope: 'personal',
            params: {
              patrolDate: this.currentDate,
              pageSize: 100,
              status: -1  // 全部状态
              // 🔥 移除硬编码userId，改为使用viewScope控制
            }
          }
        });

        if (res.code === 0 && res.data) {
          // 获取原始任务列表
          let rawTaskList = res.data.list || [];

          // 处理任务数据，与任务列表页相同的处理逻辑
          let taskList = this.processTasks(rawTaskList);

          // 明确过滤只显示分配给当前用户的任务
          if (userId) {
            taskList = taskList.filter(task => task.user_id === userId);
          }

          // 预加载任务关联的班次信息
          await this.preloadTaskShifts(taskList);

          // 更新每个任务的轮次信息
          this.updateTasksRoundInfo(taskList);

          // 处理空状态
          this.isEmpty = taskList.length === 0;

          // 暂存新任务列表，但不立即更新视图
          // 这个延迟将防止任务状态在初始加载时跳变
          const newTaskList = [...taskList];

          // 如果已选中任务不在新列表中，清除选择
          if (this.activeTaskId && !newTaskList.some(task => task._id === this.activeTaskId)) {
            this.activeTaskId = '';
            this.currentTask = null;
          }

          // 在完成所有准备工作后，一次性更新taskList
          this.$nextTick(() => {
            // 使用整体替换方式更新任务列表，避免Vue部分更新导致的视图问题
            this.taskList = newTaskList;

            // 如果任务列表不为空，且没有选中任务，自动选中第一个任务
            if (this.taskList.length > 0 && !this.activeTaskId) {
              this.activeTaskId = this.taskList[0]._id;
              this.loadTaskDetail(this.activeTaskId);
            }
            // 如果有活动任务ID但未加载任务详情
            else if (this.activeTaskId && !this.currentTask) {
              this.loadTaskDetail(this.activeTaskId);
            }

            // 清除初始加载标志
            this.isInitialLoading = false;
          });
        } else if (res.code === 401) {
          // 未登录状态
          this.taskList = [];
          this.isEmpty = true;
          this.showLoginTip();
          this.isInitialLoading = false;
        } else {
          this.showError(res.message || '获取任务列表失败');
          this.isInitialLoading = false;
        }
      } catch (error) {
        console.error('加载任务列表失败', error);
        this.showError('加载任务列表失败');
        this.isInitialLoading = false;
      } finally {
        this.showLoading(false);
      }
    },

    // 获取当前用户ID方法
    getCurrentUserId() {
      try {
        // 1. 首先尝试从uni-id-pages-userInfo获取
        const userInfo = uni.getStorageSync('uni-id-pages-userInfo');
        if (userInfo) {
          if (typeof userInfo === 'object') {
            return userInfo._id;
          }

          if (typeof userInfo === 'string') {
            try {
              const parsed = JSON.parse(userInfo);
              return parsed._id;
            } catch (e) {
              console.error('解析用户信息失败:', e);
            }
          }
        }

        // 2. 尝试从token获取
        const tokenInfo = uni.getStorageSync('uni_id_token');
        if (tokenInfo) {
          if (typeof tokenInfo === 'object' && tokenInfo.uid) {
            return tokenInfo.uid;
          }

          if (typeof tokenInfo === 'string') {
            try {
              const parsed = JSON.parse(tokenInfo);
              return parsed.uid;
            } catch (e) {
              // 错误处理已简化
            }
          }
        }

        // 3. 尝试从其他存储位置获取
        return uni.getStorageSync('uni_id_user_id') || uni.getStorageSync('uid');
      } catch (e) {
        console.error('获取用户ID失败');
        return null;
      }
    },

    // 修改预加载任务关联的班次信息方法
    async preloadTaskShifts(tasks) {
      const shiftPromises = [];
      const loadedShiftIds = [];

      // 收集所有需要加载的班次ID
      tasks.forEach(task => {
        if (task.shift_id && !this.taskShifts[task.shift_id] && !loadedShiftIds.includes(task.shift_id)) {
          loadedShiftIds.push(task.shift_id);

          // 加载班次详情，使用PatrolApi.call标准格式
          const promise = patrolApi.call({
            name: 'patrol-shift',
            action: 'getShiftDetail',
            data: {
              shift_id: task.shift_id,
              userId: this.getCurrentUserId()
            }
          })
            .then(res => {
              if (res.code === 0 && res.data) {
                this.taskShifts[task.shift_id] = res.data;

                // 更新任务轮次信息
                this.updateTaskRoundInfo(task);
              }
            })
            .catch(err => {
              console.error(`加载班次信息失败: ${task.shift_id}`, err);
            });

          shiftPromises.push(promise);
        } else if (task.shift_id && this.taskShifts[task.shift_id]) {
          // 已加载过的班次，只更新轮次信息
          this.updateTaskRoundInfo(task);
        }
      });

      // 等待所有班次加载完成
      if (shiftPromises.length > 0) {
        await Promise.all(shiftPromises);
      }
    },

    // 更新多个任务的轮次信息
    updateTasksRoundInfo(tasks) {
      if (!Array.isArray(tasks)) return;

      const now = new Date(); // 获取当前时间一次，避免多次获取造成轻微差异

      // 遍历每个任务，更新轮次信息
      tasks.forEach(task => {
        this.updateTaskRoundInfo(task, now);
      });
    },

    // 更新任务轮次信息
    updateTaskRoundInfo(task, currentTime) {
      if (!task || !task.shift_id || !this.taskShifts[task.shift_id]) return;

      // 使用传入的当前时间或获取新的当前时间
      currentTime = currentTime || new Date();

      const shift = this.taskShifts[task.shift_id];

      // 获取当前轮次信息
      try {
        const roundInfo = this.calculateCurrentRound(task, shift, currentTime);

        // 直接使用当前时间更新timeElapsed，确保数据是最新的
        if (roundInfo.status === 'active') {
          const currentRound = roundInfo.currentRound;
          if (currentRound && currentRound.start_time) {
            const startTime = new Date(currentRound.start_time.replace(/-/g, '/'));
            roundInfo.timeElapsed = currentTime.getTime() - startTime.getTime();
          }
        }

        // 保留之前的状态，方便比较变化
        const prevRoundInfo = this.taskRoundInfos[task._id];

        // 保存当前计算的状态
        this.taskRoundInfos[task._id] = roundInfo;

        // 如果状态发生变化，记录日志
        if (prevRoundInfo && prevRoundInfo.status !== roundInfo.status) {
          console.log(`任务[${task._id}]轮次状态从 ${prevRoundInfo.status} 变为 ${roundInfo.status}`);
        }

        // 如果任务有rounds_detail，按照状态进行排序
        if (task.rounds_detail && Array.isArray(task.rounds_detail) && task.rounds_detail.length > 0) {
          // 先深拷贝rounds_detail以避免直接修改原始数据
          const rounds = task.rounds_detail.map(round => ({...round}));

          // 确保每个轮次都有status字段
          for (let i = 0; i < rounds.length; i++) {
            // 保留已确定的状态，避免不必要的状态变化
            if (rounds[i].status === 2 || rounds[i].status === 3) {
              // 保持已完成或已超时状态
              continue;
            }

            if (rounds[i].status === undefined) {
              // 如果没有status，尝试根据时间计算
              const roundStartTime = new Date(rounds[i].start_time.replace(/-/g, '/'));
              const roundEndTime = new Date(rounds[i].end_time.replace(/-/g, '/'));
              const now = new Date();

              if (now < roundStartTime) {
                rounds[i].status = 0; // 未开始
              } else if (now > roundEndTime) {
                // 检查点位完成情况
                const allPointsCompleted = this.areAllPointsCompleted(task, rounds[i]);
                rounds[i].status = allPointsCompleted ? 2 : 3; // 已完成或已超时
              } else {
                // 检查点位完成情况
                const allPointsCompleted = this.areAllPointsCompleted(task, rounds[i]);
                rounds[i].status = allPointsCompleted ? 2 : 1; // 已完成或进行中
              }
            }
          }

          // 分组：未开始和进行中按升序，已完成和超时按降序
          const notStartedOrActive = rounds.filter(round => round.status === 0 || round.status === 1);
          const completedOrExpired = rounds.filter(round => round.status === 2 || round.status === 3);

          notStartedOrActive.sort((a, b) => a.round - b.round);
          completedOrExpired.sort((a, b) => b.round - a.round);

          // 更新任务的rounds_detail
          task.rounds_detail = [...notStartedOrActive, ...completedOrExpired];
        }
      } catch (error) {
        console.error('更新任务轮次信息失败', error);
      }
    },

    // 获取任务关联的班次信息
    getTaskShift(task) {
      if (!task || !task.shift_id) return null;
      return this.taskShifts[task.shift_id] || null;
    },

    // 获取任务的轮次信息
    getTaskRoundInfo(task) {
      // 检查缓存
      if (this.taskRoundInfos[task._id]) {
        return this.taskRoundInfos[task._id];
      }

      const currentTime = new Date();
      let roundInfo = {};

      // 使用新数据结构
      if (task.rounds_detail && Array.isArray(task.rounds_detail) && task.rounds_detail.length > 0) {
        roundInfo = this.calculateCurrentRound(task, null, currentTime);
      } else {
        // 获取任务关联的班次信息
        const shift = this.getTaskShift(task);
        if (!shift) {
          return null;
        }

        // 计算当前轮次状态
        roundInfo = this.calculateCurrentRound(task, shift, currentTime);
      }

      // 添加到缓存
      this.taskRoundInfos[task._id] = roundInfo;

      return roundInfo;
    },

    // 判断是否为当前激活任务
    isActiveTask(task) {
      return this.activeTaskId === task._id;
    },

    // 计算当前轮次状态
    calculateCurrentRound(task, shift, currentTime = new Date()) {
      // 使用新数据结构
      if (task.rounds_detail && Array.isArray(task.rounds_detail) && task.rounds_detail.length > 0) {
        // 使用排序函数对轮次进行排序
        const sortedRounds = sortRounds(task.rounds_detail);

        // 查找当前时间所在的轮次
        for (let i = 0; i < sortedRounds.length; i++) {
          const round = sortedRounds[i];

          // 获取任务基准日期
          const taskDate = task.patrol_date || task.create_date || new Date();

          // 使用工具函数正确处理轮次时间，包括时区和day_offset
          const startTime = calculateRoundTime(taskDate, round.start_time, round.day_offset || 0);
          // 计算结束时间，使用轮次持续时间或默认60分钟
          const endTime = calculateEndTime(startTime, round.duration || 60);

          // 轮次为毫秒时间戳
          const startMs = startTime.getTime();
          const endMs = endTime.getTime();
          const currentMs = currentTime.getTime();

          // 判断当前时间与轮次时间的关系
          if (currentMs < startMs) {
            // 当前时间早于轮次开始时间，表示正在等待该轮次
            return {
              status: 'waiting',
              nextRound: round,
              nextRoundIndex: i,
              timeUntilNext: startMs - currentMs,
              isCountdown: true // 标记为倒计时模式
            };
          } else if (currentMs >= startMs && currentMs <= endMs) {
            // 当前时间在轮次的时间范围内，表示这个轮次正在进行中
            // 增强：检查点位完成情况
            const allPointsCompleted = this.areAllPointsCompleted(task, round);

            // 如果所有点位已完成，虽然在时间范围内，但状态应该是已完成
            if (allPointsCompleted) {
              return {
                status: 'completed',
                currentRound: round,
                currentRoundIndex: i,
                isEarlyCompletion: true, // 提前完成标记
                timeRemaining: endMs - currentMs,
                isCountdown: false, // 标记为非倒计时模式
                totalTime: endMs - startMs // 总时长
              };
            }

            return {
              status: 'active',
              currentRound: round,
              currentRoundIndex: i,
              timeRemaining: endMs - currentMs,
              timeElapsed: currentMs - startMs,
              totalTime: endMs - startMs,
              completionPercentage: (currentMs - startMs) / (endMs - startMs),
              isCountdown: false // 标记为非倒计时模式
            };
          }
        }

        // 如果所有轮次都已经过去，需要检查最后一个轮次是否完成
        const lastRound = sortedRounds[sortedRounds.length - 1];
        const allPointsCompletedInLastRound = this.areAllPointsCompleted(task, lastRound);

        return {
          status: allPointsCompletedInLastRound ? 'completed' : 'expired', // 增强：区分完成和超时
          lastRound: lastRound,
          lastRoundIndex: sortedRounds.length - 1,
          isCompleted: allPointsCompletedInLastRound
        };
      }

      // 如果没有新数据结构，回退到使用旧数据结构
      if (!shift || !shift.rounds || shift.rounds.length === 0) {
        return { status: 'no_rounds' };
      }

      const enabledRounds = shift.rounds;
      const currentMs = currentTime.getTime();

      // 按时间排序
      enabledRounds.sort((a, b) => {
        const aTimeStr = a.start_time || a.time;
        const bTimeStr = b.start_time || b.time;

        // 转换为天数中的分钟数
        const [aHour, aMinute] = aTimeStr.split(':').map(Number);
        const [bHour, bMinute] = bTimeStr.split(':').map(Number);

        // 按时间排序
        return (aHour * 60 + aMinute) - (bHour * 60 + bMinute);
      });

      // 获取任务日期（YYYY-MM-DD格式）
      const taskDate = task.patrol_date || task.date;

      // 判断是否是跨天班次
      const isShiftAcrossDay = shift.across_day || shift.end_time < shift.start_time;

      // 遍历轮次
      for (let i = 0; i < enabledRounds.length; i++) {
        const round = enabledRounds[i];

        // 获取轮次时间
        const roundTimeStr = round.start_time || round.time;
        const roundEndTimeStr = round.end_time || (i < enabledRounds.length - 1 ?
                             (enabledRounds[i+1].start_time || enabledRounds[i+1].time) :
                             shift.end_time);

        // 使用工具函数正确处理轮次时间
        const startTime = calculateRoundTime(taskDate, roundTimeStr);

        // 计算结束时间，使用轮次持续时间或默认60分钟
        const duration = round.duration || 60;
        const endTime = calculateEndTime(startTime, duration);

        // 处理跨天情况
        if (isShiftAcrossDay) {
          // 获取开始和结束时间的小时数
          const startHour = startTime.getHours();
          const endHour = endTime.getHours();

          // 如果结束时间小于开始时间，说明跨天
          if (endHour < startHour || roundEndTimeStr < roundTimeStr) {
            endTime.setDate(endTime.getDate() + 1);
          }
        }

        // 轮次为毫秒时间戳
        const startMs = startTime.getTime();
        const endMs = endTime.getTime();

        // 判断当前时间与轮次时间的关系
        if (currentMs < startMs) {
          // 当前时间早于轮次开始时间，表示正在等待该轮次
          return {
            status: 'waiting',
            nextRound: round,
            nextRoundIndex: i,
            timeUntilNext: startMs - currentMs,
            isCountdown: true // 标记为倒计时模式
          };
        } else if (currentMs >= startMs && currentMs <= endMs) {
          // 当前时间在轮次的时间范围内，表示这个轮次正在进行中
          return {
            status: 'active',
            currentRound: round,
            currentRoundIndex: i,
            timeRemaining: endMs - currentMs,
            timeElapsed: currentMs - startMs,
            totalTime: endMs - startMs,
            completionPercentage: (currentMs - startMs) / (endMs - startMs),
            isCountdown: false // 标记为非倒计时模式
          };
        }
      }

      // 如果所有轮次都已经过去，则表示所有轮次已完成
      return {
        status: 'completed',
        lastRound: enabledRounds[enabledRounds.length - 1],
        lastRoundIndex: enabledRounds.length - 1
      };
    },

    // 添加判断轮次所有点位是否完成的辅助方法
    areAllPointsCompleted(task, round) {
      if (!round || !round.point_stats) return false;

      // 如果point_stats中有完成信息，使用它判断
      if (round.point_stats.total && round.point_stats.checked) {
        return round.point_stats.checked >= round.point_stats.total;
      }

      // 如果没有point_stats或数据不完整，尝试使用round_records判断
      if (task.round_records && Array.isArray(task.round_records)) {
        // 查找对应轮次的记录
        const roundRecord = task.round_records.find(r => r && r.round === round.round);

        if (roundRecord) {
          // 确保completed_points是数组
          const completedPoints = Array.isArray(roundRecord.completed_points) ?
                                 roundRecord.completed_points : [];

          // 确保points是数组
          const points = Array.isArray(round.points) ? round.points : [];

          // 如果没有点位，返回false
          if (points.length === 0) return false;

          // 判断是否所有点位都已完成
          return completedPoints.length >= points.length;
        }
      }

      // 如果以上判断都无法执行，返回false
      return false;
    },

    // 构建地图标记点
    buildTaskMarkers() {
      // 保存当前位置的范围圈
      const locationCircle = this.circles.find(circle => 
        circle.strokeColor && (
          circle.strokeColor === '#34C759' ||  // 精度极好 - 绿色
          circle.strokeColor === '#00C58E' ||  // 精度良好 - 青色
          circle.strokeColor === '#FFD60A' ||  // 精度一般 - 黄色
          circle.strokeColor === '#FF9500' ||  // 精度较差 - 橙色
          circle.strokeColor === '#FF6B2C' ||  // 精度很差 - 深橙色
          circle.strokeColor === '#FF3B30'     // 精度极差 - 红色
        )
      );

      // 清空当前标记点数组
      this.markers = [];
      this.polylines = [];
      this.circles = []; // 清空范围圈数组

      const task = this.currentTask;
      if (!task) {
        // 如果没有任务，但有位置范围圈，则保留位置范围圈
        if (locationCircle) {
          this.circles = [locationCircle];
        }
        console.warn('没有当前任务，无法构建标记点');
        return;
      }

      // 获取当前任务的轮次信息
      const taskId = task._id;
      const roundInfo = this.taskRoundInfos[taskId];

      if (!roundInfo) {
        console.warn('没有轮次信息，无法构建标记点');
        return;
      }

      // 获取当前轮次
      let currentRound = null;

      // 1. 如果有选中的轮次，优先使用
      if (this.selectedRound && this.selectedRound.taskId === taskId) {
        currentRound = task.rounds_detail.find(r => r.round === this.selectedRound.round.round);
      }

      // 2. 如果没有选中的轮次，查找进行中的轮次
      if (!currentRound) {
        currentRound = task.rounds_detail.find(r => r.status === 1);
      }

      // 3. 如果没有进行中的轮次，查找未开始的轮次
      if (!currentRound) {
        currentRound = task.rounds_detail.find(r => r.status === 0);
      }

      // 4. 如果还是没找到，使用最后一个轮次
      if (!currentRound && task.rounds_detail && task.rounds_detail.length > 0) {
        // 任务结束时，直接使用排序后的第一个轮次（最新完成的轮次）
        currentRound = task.rounds_detail[0];
      }

      if (!currentRound) {
        console.warn('没有找到可用的轮次');
        return;
      }

      // 获取当前轮次的点位
      let points = [];
      if (currentRound.points && Array.isArray(currentRound.points)) {
        points = currentRound.points;
      } else if (task.route_detail && task.route_detail.points) {
        points = task.route_detail.points;
      }

      if (points.length === 0) {
        console.warn('没有可用的点位数据');
        return;
      }

      // 确保点位按照顺序排序
      const sortedPoints = [...points].sort((a, b) => {
        if (a.order !== undefined && b.order !== undefined) {
          return a.order - b.order;
        }
        return 0;
      });

      // 获取当前轮次记录
      const roundRecord = (task.round_records || []).find(r => r.round === currentRound.round);
      const completedPointIds = roundRecord ? roundRecord.completed_points || [] : [];

      // 创建标记点和路线
      const markers = [];
      const coordinates = [];

      // 创建所有点位的标记点
      sortedPoints.forEach((point, index) => {
        if (!point) {
          console.error(`点位数据不完整，索引: ${index}`);
          return;
        }

        // 提取点位ID
        let pointId = point.point_id || point.id || point._id;
        if (!pointId) {
          console.error(`点位没有有效的ID，索引: ${index}`, point);
          pointId = `point_${index}`;
        }

        // 提取经纬度
        let latitude, longitude;

        if (point.latitude && point.longitude) {
          latitude = parseFloat(point.latitude);
          longitude = parseFloat(point.longitude);
        } else if (point.location && point.location.latitude && point.location.longitude) {
          latitude = parseFloat(point.location.latitude);
          longitude = parseFloat(point.location.longitude);
        } else if (point.coordinates && Array.isArray(point.coordinates) && point.coordinates.length >= 2) {
          longitude = parseFloat(point.coordinates[0]);
          latitude = parseFloat(point.coordinates[1]);
        } else if (point.position || point.lnglat) {
          const posObj = point.position || point.lnglat;
          if (posObj.lat || posObj.latitude) {
            latitude = parseFloat(posObj.lat || posObj.latitude);
          }
          if (posObj.lng || posObj.lon || posObj.longitude) {
            longitude = parseFloat(posObj.lng || posObj.lon || posObj.longitude);
          }
        }

        if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) {
          console.error(`点位没有有效的经纬度坐标，点位ID: ${pointId}`, point);
          return;
        }

        // 检查坐标是否在有效范围内
        if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
          console.error(`点位的经纬度坐标超出有效范围，点位ID: ${pointId}, 坐标: [${latitude}, ${longitude}]`);
          return;
        }

        // 简化点位完成状态判断 - 直接使用点位自身属性
        // 不再查询completedPointIds，除非必要
        let isCompleted = false;

        // 修改判断逻辑，确保状态为4的点位不会被标记为已完成
        if (point.status === 4) {
          // 状态为4的点位始终视为未完成，即使有record_id
          isCompleted = false;
        } else {
          // 其他情况下使用常规判断
          // 修复布尔表达式可能返回undefined的问题
          isCompleted = !!(point.status === 1 ||
                          point.status === 'completed' ||
                          point.checkin_time ||
                          point.record_id);
        }

        // 打印调试信息，帮助排查问题
        // console.log(`点位状态检查：${point.name || `点位${index + 1}`}`, {
        //   status: point.status,
        //   hasCheckInTime: !!point.checkin_time,
        //   hasRecordId: !!point.record_id,
        //   isCompleted: isCompleted
        // });

        // 获取点位顺序号
        const pointOrder = point.order || index + 1;

        // 设置点位属性 - 使用不同图标区分完成状态
        // 未打卡使用红色marker.png，已打卡使用map-pin.png
        const iconPath = isCompleted ? '/static/map/map-pin.png' : '/static/map/marker.png';

        // 创建标记点
        const marker = {
          id: index,
          pointId: pointId,
          latitude: latitude,
          longitude: longitude,
          iconPath: iconPath,
          width: 32,
          height: 32,
          title: point.name || `点位${index + 1}`,
          isCompleted: isCompleted,
          callout: {
            content: (point.name ? `${pointOrder}. ${point.name}` : `${pointOrder}. 点位${index + 1}`) + (isCompleted ? ' ✓' : ''),
            color: '#FFFFFF',
            fontSize: 12,
            borderWidth: 0,
            bgColor: isCompleted ? '#4CAF50' : '#3688FF',
            padding: 5,
            display: 'ALWAYS',
            borderRadius: 4,
            textAlign: 'center'
          }
        };

        markers.push(marker);

        // 添加坐标到路线数组
        coordinates.push({
          longitude: longitude,
          latitude: latitude,
          pointId: pointId, // 添加点位ID用于关联
          order: index // 使用循环索引作为顺序
        });

        // 添加点位范围圈 - 使用数据库的范围值或默认50米半径
        // 确保从点位数据读取正确的range值
        let pointRange = 50; // 默认值

        // 首先尝试从point对象获取range
        if (point.range && !isNaN(parseFloat(point.range))) {
          pointRange = parseFloat(point.range);
        }
        // 如果没有range属性，尝试从location对象内获取
        else if (point.location && point.location.range && !isNaN(parseFloat(point.location.range))) {
          pointRange = parseFloat(point.location.range);
        }
        // 如果没有range属性，尝试从其他可能的属性获取
        else if (point.radius && !isNaN(parseFloat(point.radius))) {
          pointRange = parseFloat(point.radius);
        }

        // 确保范围值是有效的正数
        pointRange = pointRange > 0 ? pointRange : 50;

        this.circles.push({
          latitude: latitude,
          longitude: longitude,
          color: isCompleted ? '#52c41a88' : '#1677FF88', // 点位范围圈颜色（带透明度）
          fillColor: isCompleted ? '#52c41a33' : '#1677FF33', // 点位范围填充色（更透明）
          radius: pointRange,
          strokeWidth: 2
        });
      });

      // 创建路线 - 如果至少有两个点位
      if (coordinates.length >= 2) {
        // 确保路线坐标按顺序排列
        const routeCoordinates = [...coordinates].sort((a, b) => a.order - b.order).map(coord => ({
          latitude: coord.latitude,
          longitude: coord.longitude
        }));

        // 创建路线 - 改为实线，不再添加距离计算
        const polylinePoints = routeCoordinates;

        const polyline = {
          points: routeCoordinates,
          color: '#1677FF',
          width: 4, // 粗一点的实线
          arrowLine: true,
          dottedLine: false // 确保是实线
        };

        this.polylines = [polyline];
      }

      // 更新标记点 - 不包含距离标签
      this.markers = markers;

      // 设置地图中心点 - 使用第一个标记点或用户位置
      if (markers.length > 0) {
        this.mapCenter = {
          latitude: markers[0].latitude,
          longitude: markers[0].longitude
        };
      } else if (this.userLocation && this.userLocation.latitude && this.userLocation.longitude) {
        this.mapCenter = {
          latitude: this.userLocation.latitude,
          longitude: this.userLocation.longitude
        };
      }

      // 在最后添加位置范围圈
      if (locationCircle) {
        this.circles.push(locationCircle);
      }
      
      // 确保更新当前位置标记
      this.updateCurrentLocationMarker();
    },

    // 更新当前位置标记
    updateCurrentLocationMarker() {
        if (!this.currentLocation || !this.currentLocation.latitude || !this.currentLocation.longitude) {
            return;
        }

        // 获取精度值
        const accuracy = this.currentLocation.accuracy || 0;

        // 根据精度决定圈的颜色
        let circleColor, fillColor;
        if (accuracy <= 5) {
            circleColor = '#34C75988';    // 绿色 - 精度极好
            fillColor = '#34C75933';
        } else if (accuracy <= 10) {
            circleColor = '#00C58E88';    // 青色 - 精度良好
            fillColor = '#00C58E33';
        } else if (accuracy <= 15) {
            circleColor = '#FFD60A88';    // 黄色 - 精度一般
            fillColor = '#FFD60A33';
        } else if (accuracy <= 20) {
            circleColor = '#FF950088';    // 橙色 - 精度较差
            fillColor = '#FF950033';
        } else if (accuracy <= 25) {
            circleColor = '#FF6B2C88';    // 深橙色 - 精度很差
            fillColor = '#FF6B2C33';
        } else {
            circleColor = '#FF3B3088';    // 红色 - 精度极差
            fillColor = '#FF3B3033';
        }

        // 当前位置精度圈
        const accuracyCircle = {
            latitude: this.currentLocation.latitude,
            longitude: this.currentLocation.longitude,
            radius: 3, // 固定3米精度圈
            color: circleColor,
            fillColor: fillColor,
            strokeWidth: 2,
            strokeColor: circleColor.slice(0, 7) // 去掉透明度
        };

        // 更新圆形区域，保留任务点位范围圈
        const circles = [...this.circles];

        // 查找并更新或添加当前位置精度圈
        const accuracyCircleIndex = circles.findIndex(circle =>
            circle.strokeColor === '#34C759' ||  // 精度极好 - 绿色
            circle.strokeColor === '#00C58E' ||  // 精度良好 - 青色
            circle.strokeColor === '#FFD60A' ||  // 精度一般 - 黄色
            circle.strokeColor === '#FF9500' ||  // 精度较差 - 橙色
            circle.strokeColor === '#FF6B2C' ||  // 精度很差 - 深橙色
            circle.strokeColor === '#FF3B30'     // 精度极差 - 红色
        );

        if (accuracyCircleIndex >= 0) {
            circles[accuracyCircleIndex] = accuracyCircle;
        } else {
            circles.push(accuracyCircle);
        }

        this.circles = circles;
    },

    // 点击点位标记
    onPointMarkerTap(markerId) {
      console.log('onPointMarkerTap triggered with markerId:', markerId); // 添加这行日志用于调试点击事件
      // 通过markerId找到对应索引
      const markerIndex = parseInt(markerId);

      // 找到对应的点位，通过索引匹配
      if (isNaN(markerIndex) || markerIndex < 0 || !this.currentRoutePoints || markerIndex >= this.currentRoutePoints.length) {
        return;
      }

      const point = this.currentRoutePoints[markerIndex];
      if (!point) return;

      // 检查是否有当前任务
      if (!this.currentTask || !this.currentTask._id) {
        uni.showToast({
          title: '请先选择一个任务',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 获取当前任务的轮次信息
      const roundInfo = this.taskRoundInfos[this.currentTask._id];

      // 打印调试信息，了解roundInfo的实际内容
      console.log('任务轮次信息：', roundInfo);

      // 检查任务是否有活跃或即将开始的轮次
      if (!roundInfo) {
        console.log('没有找到任务轮次信息');
        // 尝试重新计算轮次信息
        const shift = this.getTaskShift(this.currentTask);
        if (shift) {
          const calculatedRoundInfo = this.calculateCurrentRound(this.currentTask, shift, new Date());
          console.log('重新计算的轮次信息：', calculatedRoundInfo);

          // 如果仍然没有有效轮次，提示用户
          if (!calculatedRoundInfo || (calculatedRoundInfo.status !== 'active' && calculatedRoundInfo.status !== 'waiting')) {
            uni.showToast({
              title: '当前没有可打卡的轮次',
              icon: 'none',
              duration: 2000
            });
            return;
          }

          // 如果有即将开始的轮次也允许打卡
          this.taskRoundInfos[this.currentTask._id] = calculatedRoundInfo;
        } else {
          uni.showToast({
            title: '任务未关联班次信息',
            icon: 'none',
            duration: 2000
          });
          return;
        }
      }

      // 轮次检查：如果有选中轮次则使用选中轮次，否则使用当前活跃轮次或等待的轮次
      let roundToUse = null;

      // 验证当前选中的轮次状态
      if (this.selectedRound && this.selectedRound.round) {
        const roundData = this.currentTask.rounds_detail.find(r => r.round === this.selectedRound.round.round);
        if (roundData && (roundData.status === 0 || roundData.status === 2 || roundData.status === 3)) {
          // 轮次未开始、已完成或已超时，显示提示并阻止操作
          let message = '';
          if (roundData.status === 3) {
            message = '所选轮次已超时';
          } else if (roundData.status === 2) {
            message = '所选轮次已完成';
          } else if (roundData.status === 0) {
            message = '所选轮次尚未开始';
          }

          uni.showToast({
            title: message,
            icon: 'none',
            duration: 2000
          });
          return;
        }
        roundToUse = roundData;
      } else {
        // 没有选中轮次，使用当前活跃轮次或等待的轮次
        const activeRound = this.getCurrentActiveRound(this.currentTask);
        if (activeRound) {
          roundToUse = activeRound;
          console.log('使用当前活跃轮次：', activeRound);
        } else {
          console.log('没有找到可用的活跃轮次');
          uni.showToast({
            title: '当前没有可打卡的轮次',
            icon: 'none',
            duration: 2000
          });
          return;
        }
      }

      // 直接跳转到打卡页面
      this.navigateToCheckIn(this.currentTask, point);
    },

    // 获取当前活跃的轮次（状态为1-进行中的轮次）
    getCurrentActiveRound(task) {
      if (!task || !task.rounds_detail || !Array.isArray(task.rounds_detail)) {
        return null;
      }

      // 查找状态为1的轮次（进行中）
      let activeRound = task.rounds_detail.find(r => r.status === 1);

      // 如果没有进行中的轮次，查找状态为0的轮次（未开始）
      if (!activeRound) {
        activeRound = task.rounds_detail.find(r => r.status === 0);
      }

      return activeRound;
    },

    // 天气数据加载完成
    onWeatherLoaded(weatherInfo) {
      this.weatherInfo = weatherInfo;
    },

    // 点击任务卡片
    onTaskClick(task) {
      // 如果切换到不同的任务，清除轮次选择状态
      if (this.activeTaskId !== task._id) {
        this.selectedRound = null;
      }

      // 设置当前激活的任务ID
      this.activeTaskId = task._id;

      // 加载任务详情
      this.loadTaskDetail(task._id);
    },

    // 点击继续/开始巡检按钮
    onTaskContinue(eventData) {
      // 检查是否有传递完整的事件数据对象（包含task和selectedRound）
      if (eventData && eventData.task) {
        // 保存选中的轮次信息
        if (eventData.selectedRound) {
          this.selectedRound = {
            round: eventData.selectedRound,
            taskId: eventData.task._id
          };
        } else {
          // 如果没有选中轮次，清除之前的选择
          this.selectedRound = null;
        }

        // 加载任务详情并开始巡检
        this.loadTaskDetail(eventData.task._id, true);
      } else {
        // 旧版本的处理方式 - 直接传递任务对象
        const task = eventData;

        // 加载任务详情并开始巡检
        this.loadTaskDetail(task._id, true);
      }
    },

    // 点击查看详情按钮
    onTaskViewDetail(task) {
      // 跳转到任务详情页面
      uni.navigateTo({
        url: `/pages/patrol_pkg/task/detail?id=${task._id}`
      });
    },

    // 修改加载任务详情方法
    async loadTaskDetail(taskId, startPatrol = false, forceRefresh = false) {
      if (!taskId) {
        console.error('任务ID为空');
        this.showError('任务ID为空');
        return;
      }

      try {
        // 显示加载中
        this.showLoading(true);

        // 使用PatrolApi.call获取任务详情，与任务列表页保持一致
        const res = await patrolApi.call({
          name: 'patrol-task',
          action: 'getTaskDetail',
          data: {
            task_id: taskId, // 🔥 使用优化后的参数格式
            level: 'map', // 🔥 启用地图专用模式，保留位置信息，减少约30%数据传输量
            forceRefresh: forceRefresh ? true : undefined // 添加强制刷新参数
          }
        });

        if (res.code === 0 && res.data) {
          // 处理任务详情
          let newTaskData = res.data;

          // 同步处理轮次信息
          if (newTaskData.rounds_detail && newTaskData.rounds_detail.length > 0) {
            const processedTasks = this.processTasks([newTaskData]);
            if (processedTasks.length > 0) {
              newTaskData = processedTasks[0]; // 使用处理后的数据
            }
          }

          // 确保任务有round_records字段
          if (!newTaskData.round_records) {
            newTaskData.round_records = [];
          }

          // 更新轮次信息 (如果需要加载班次，确保 await 完成)
          if (newTaskData.shift_id && !this.taskShifts[newTaskData.shift_id]) {
            try {
              const shiftRes = await patrolApi.call({ // 使用 await
                name: 'patrol-shift',
                action: 'getShiftDetail',
                data: {
                  params: { // 确认参数结构
                    shift_id: newTaskData.shift_id
                  }
                }
              });
              if (shiftRes.code === 0 && shiftRes.data) {
                this.taskShifts[newTaskData.shift_id] = shiftRes.data;
                this.updateTaskRoundInfo(newTaskData); // 使用新数据更新
              }
            } catch (err) {
              console.error('加载班次信息失败', err);
            }
          } else {
            this.updateTaskRoundInfo(newTaskData); // 使用新数据更新
          }

          // 更新当前任务 - 使用新数据创建新引用
          this.currentTask = { ...newTaskData };

          // 更新taskList中相应的任务对象，确保引用变化
          const taskIndex = this.taskList.findIndex(t => t._id === taskId);
          if (taskIndex >= 0) {
            // 创建全新的对象引用以触发Vue的检测
            const updatedTaskList = [...this.taskList];
            // 使用深拷贝或扩展运算符替换对象，确保引用变化
            updatedTaskList[taskIndex] = { ...newTaskData }; // 使用扩展运算符创建新对象
            // 替换整个数组以确保Vue检测到变化
            this.taskList = updatedTaskList;

            console.log('loadTaskDetail: 已更新taskList中的任务对象:', taskId);
          }

          // 构建标记点 - 确保在数据更新后执行
          this.$nextTick(() => {
            this.buildTaskMarkers();

            // 检查是否要开始巡检
            if (startPatrol === true) {
              // 延迟执行startPatrol，确保地图已渲染
              setTimeout(() => {
                this.startPatrol(this.currentTask); // 传递更新后的currentTask
              }, 300);
            }
          });

          // 更新激活的任务ID
          this.activeTaskId = taskId;

          return this.currentTask; // 返回更新后的任务数据
        } else {
          this.showError(res.message || '获取任务详情失败');
          return null; // 返回 null 表示失败
        }
      } catch (error) {
        console.error('加载任务详情失败', error);
        this.showError('加载任务详情失败');
        return null; // 返回 null 表示失败
      } finally {
        // 添加延时和安全检查，避免多个hideLoading调用冲突
        setTimeout(() => {
          this.showLoading(false);
        }, 200);
      }
    },

    // 开始巡检
    async startPatrol(taskData) {
      // 检查任务数据是否有效
      if (!taskData) {
        console.error('任务数据为空');
        this.showError('任务数据丢失');
        return;
      }

      try {
        // 显示加载中
        this.showLoading(true);

        // 先从服务器获取最新的任务数据
        const res = await patrolApi.call({
          name: 'patrol-task',
          action: 'getTaskDetail',
          data: {
            task_id: taskData._id, // 🔥 使用优化后的参数格式
            level: 'map', // 🔥 启用地图专用模式
            forceRefresh: true // 强制刷新
          }
        });

        // 创建处理任务的函数
        const processPatrolTask = (task) => {
          if (!task || !task.rounds_detail || !Array.isArray(task.rounds_detail)) {
            return;
          }

          const now = new Date();

          // 更新所有轮次的状态
          task.rounds_detail.forEach(round => {
            // 只有当轮次状态不是已完成(2)和超时(3)时才重新计算状态
            if (round.status !== 2 && round.status !== 3) {
              round.status = this.getRoundStatus(round, now);
            }
          });

          // 获取当前选中的轮次或进行中的轮次
          let currentRound = null;

          // 检查是否所有轮次都已结束（完成或超时）
          const allRoundsEnded = task.rounds_detail.every(round => round.status === 2 || round.status === 3);

          if (allRoundsEnded) {
            // 如果所有轮次都已结束，直接使用最后一轮
            currentRound = task.rounds_detail[task.rounds_detail.length - 1];
            console.log('所有轮次已结束，使用最后一轮:', currentRound.round);
          } else {
            // 1. 如果有选中的轮次，验证其状态
            if (this.selectedRound && this.selectedRound.taskId === task._id) {
              const selectedRound = task.rounds_detail.find(r => r.round === this.selectedRound.round.round);
              // 只有当轮次状态为进行中(1)或未开始(0)时才使用
              if (selectedRound && (selectedRound.status === 1 || selectedRound.status === 0)) {
                currentRound = selectedRound;
              }
            }

            // 2. 如果没有有效的选中轮次，查找进行中的轮次
            if (!currentRound) {
              const activeRounds = task.rounds_detail.filter(round => round.status === 1);
              if (activeRounds.length > 0) {
                // 如果有多个进行中的轮次，选择时间范围最长的
                currentRound = activeRounds.reduce((prev, curr) => {
                  const prevDuration = new Date(prev.end_time) - new Date(prev.start_time);
                  const currDuration = new Date(curr.end_time) - new Date(curr.start_time);
                  return currDuration > prevDuration ? curr : prev;
                });
              }
            }

            // 3. 如果没有进行中的轮次，查找未开始的轮次
            if (!currentRound) {
              const notStartedRounds = task.rounds_detail.filter(round => round.status === 0);
              if (notStartedRounds.length > 0) {
                // 如果有多个未开始的轮次，选择时间范围最长的
                currentRound = notStartedRounds.reduce((prev, curr) => {
                  const prevDuration = new Date(prev.end_time) - new Date(prev.start_time);
                  const currDuration = new Date(curr.end_time) - new Date(curr.start_time);
                  return currDuration > prevDuration ? curr : prev;
                });
              }
            }
          }

          // 如果找到了轮次，继续处理
          if (currentRound) {
            console.log('处理巡视任务，当前轮次:', currentRound.round, '状态:', currentRound.status);

            // 获取当前轮次的点位
            let taskPoints = [];
            if (currentRound.points && Array.isArray(currentRound.points)) {
              console.log('从当前轮次获取点位信息:', currentRound.round);
              taskPoints = currentRound.points;
            } else if (task.route_detail && task.route_detail.points) {
              console.log('从route_detail获取点位信息');
              taskPoints = task.route_detail.points;
            }

            // 检查是否有可用的巡视点位
            if (taskPoints.length === 0) {
              console.error('未找到有效的点位信息');
              this.showError('任务没有巡视点位');
              return;
            }

            // 按照顺序对点位进行排序
            const sortedPoints = [...taskPoints].sort((a, b) => {
              const orderA = a.order !== undefined ? a.order : 0;
              const orderB = b.order !== undefined ? b.order : 0;
              return orderA - orderB;
            });

            console.log('排序后的点位:', sortedPoints);

            // 如果所有轮次已结束，不再查找未完成的点位
            if (!allRoundsEnded) {
              // 🔥 修复：查找第一个未完成的点位（按顺序）
              const incompletePoint = sortedPoints.find(point => {
                // 修复状态判断逻辑，与buildTaskMarkers保持一致
                if (point.status === 4) {
                  return true; // 状态为4的点位视为未完成
                }
                // 优先使用status字段判断
                return !(point.status === 1 || point.checkin_time || point.record_id);
              });

              if (incompletePoint) {
                console.log(`🎯 继续巡视: ${incompletePoint.name || '未命名点位'} (order: ${incompletePoint.order})`);
                // 跳转到打卡页面
                this.navigateToCheckIn(task, incompletePoint);
                return;
              }
              
              console.log('✅ 所有点位已完成');
            }

            // 更新地图上的点位显示
            this.buildTaskMarkers(task, currentRound, sortedPoints);
          }
        };

        if (res.code === 0 && res.data) {
          console.log('获取最新任务数据成功');
          // 使用最新的任务数据继续处理
          processPatrolTask(res.data);
        } else {
          console.warn('获取最新任务数据失败，使用缓存数据继续');
          // 使用当前缓存的任务数据继续处理
          processPatrolTask(taskData);
        }
      } catch (error) {
        console.error('获取最新任务数据出错:', error);
        // 出错时使用缓存数据
        processPatrolTask(taskData);
      } finally {
        this.showLoading(false);
      }
    },

    // 跳转到打卡页面
    navigateToCheckIn(task, point) {
      if (!task) {
        console.error('任务数据为空');
        this.showError('任务数据丢失');
        return;
      }

      if (!point) {
        console.error('点位数据为空');
        this.showError('点位数据丢失');
        return;
      }

      // 检查任务状态，允许已完成任务继续打卡
      if (task.status === 4) { // 只有已取消的任务(status=4)不允许打卡
        uni.showModal({
          title: '提示',
          content: '该任务已取消，无法打卡',
          showCancel: false
        });
        return;
      }

      // 获取点位ID
      const pointId = point.point_id || point._id;
      if (!pointId) {
        console.error('点位没有有效的ID');
        this.showError('点位数据异常');
        return;
      }

      // 获取所有可用的轮次（按顺序：进行中 -> 未开始 -> 已完成）
      const availableRounds = task.rounds_detail
        .filter(round => round.status !== 3 && round.status !== 4) // 排除超时和取消的轮次
        .sort((a, b) => {
          // 优先级：进行中 > 未开始 > 已完成
          const getWeight = (status) => {
            if (status === 1) return 0;  // 进行中
            if (status === 0) return 1;  // 未开始
            if (status === 2) return 2;  // 已完成
            return 3;  // 其他状态
          };
          return getWeight(a.status) - getWeight(b.status);
        });

      // 如果有选中的轮次，验证其状态
      let targetRound = null;
      if (this.selectedRound && this.selectedRound.taskId === task._id) {
        const selectedRound = task.rounds_detail.find(r => r.round === this.selectedRound.round.round);
        if (selectedRound && (selectedRound.status === 1 || selectedRound.status === 0)) {
          targetRound = selectedRound;
        }
      }

      // 如果没有选中的轮次或选中的轮次无效，查找下一个可用的轮次
      if (!targetRound) {
        // 找到第一个未完成的轮次
        targetRound = availableRounds.find(round => {
          // 检查该轮次中的点位是否已完成
          const roundRecord = task.round_records?.find(record => record.round === round.round);
          const completedPoints = roundRecord?.completed_points || [];
          return !completedPoints.includes(pointId);
        });
      }

      // 如果没有找到可用轮次，说明所有轮次都已完成该点位
      if (!targetRound) {
        uni.showToast({
          icon: 'none',
          title: '当前没有可打卡的轮次',
          duration: 2000
        });
        return;
      }

      // 构建跳转URL，传递当前位置信息作为初始值
      let url = `/pages/patrol_pkg/checkin/index?task_id=${task._id}&point_id=${pointId}&round=${targetRound.round}`;
      
      // 如果有当前位置信息，传递给打卡页面作为初始定位
      if (this.currentLocation && this.currentLocation.latitude && this.currentLocation.longitude) {
        url += `&lat=${this.currentLocation.latitude}&lng=${this.currentLocation.longitude}&accuracy=${this.currentLocation.accuracy || 0}`;
      }
      
      // 跳转到打卡页面
      uni.navigateTo({
        url: url
      });
    },

    // 显示加载中
    showLoading(show = true) {
      this.isLoading = show;

      if (show) {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
      } else {
        // 添加延时和检查，确保loading存在再隐藏
        setTimeout(() => {
          try {
            // 检查当前是否有loading显示中
            uni.hideLoading();
          } catch (e) {
          }
        }, 150); // 延迟150ms执行，给loading显示和关闭之间留出缓冲时间
      }
    },

    // 显示错误消息
    showError(message) {
      uni.showToast({
        icon: 'none',
        title: message || '操作失败',
        duration: 2000
      });
    },

    // 轮次状态更新定时器
    startRoundUpdateTimer() {
      // 先清除之前的定时器
      this.clearRoundUpdateTimer();

      // 创建新的定时器，定期更新轮次状态
      this.roundUpdateTimer = setInterval(() => {
        this.updateAllTaskRounds();
      }, this.roundUpdateInterval);
    },

    // 清除轮次更新定时器
    clearRoundUpdateTimer() {
      if (this.roundUpdateTimer) {
        clearInterval(this.roundUpdateTimer);
        this.roundUpdateTimer = null;
      }
    },

    // 更新所有任务的轮次状态
    updateAllTaskRounds() {
      if (!this.taskList || this.taskList.length === 0 || this.isInitialLoading) return;

      const currentTime = new Date();
      let needFullRefresh = false;

      this.taskList.forEach(task => {
        if (!task.shift_id || !this.taskShifts[task.shift_id]) return;

        // 获取当前任务的轮次信息
        const oldRoundInfo = this.taskRoundInfos[task._id];
        const oldStatus = oldRoundInfo ? oldRoundInfo.status : null;

        // 计算最新的轮次状态
        const newRoundInfo = this.calculateCurrentRound(task, this.taskShifts[task.shift_id], currentTime);

        // 检查轮次状态变化边界情况 - 特别是倒计时接近0的情况
        if (oldRoundInfo && oldRoundInfo.status === 'waiting' &&
            oldRoundInfo.timeUntilNext && oldRoundInfo.timeUntilNext < 60000) {
          // 当等待时间少于1分钟时，更频繁地检查状态
          console.log('轮次即将开始，更频繁检查');
          // 立即强制重新计算状态
          setTimeout(() => {
            this.updateTaskRoundInfo(task);
            this.$forceUpdate();
          }, 2000); // 2秒后重新检查
        }

        this.taskRoundInfos[task._id] = newRoundInfo;

        // 状态变化增强监测：特别关注从waiting到active的变化
        if (oldStatus && newRoundInfo.status !== oldStatus) {
          // 从等待到当前轮次，重要的状态变化，需要立即刷新UI
          if (oldStatus === 'waiting' && newRoundInfo.status === 'active') {
            console.log('重要状态变化: 从waiting到active');
            // 立即强制刷新页面UI
            this.$forceUpdate();
            // 如果当前选中的是这个任务，立即更新地图等信息
            if (this.currentTask && this.currentTask._id === task._id) {
              this.buildTaskMarkers();
            }
            needFullRefresh = true;
          }

          // 其他重要状态变化
          if (
            (oldStatus === 'active' && newRoundInfo.status === 'completed') ||
            (oldStatus === 'active' && newRoundInfo.status === 'between_rounds') ||
            (oldStatus === 'between_rounds' && newRoundInfo.status === 'active')
          ) {
            needFullRefresh = true;

            // 显示轮次变化提示
            this.showRoundChangeNotification(task, oldRoundInfo, newRoundInfo);
          }
        }
      });

      // 如果有关键轮次状态变化，执行完整刷新
      if (needFullRefresh) {
        // 重要状态变化时先设置缓冲标记
        this.isInitialLoading = true;

        this.refreshTaskList(false);

        // 延迟清除缓冲标记
        setTimeout(() => {
          this.isInitialLoading = false;
        }, 800);
      } else {
        // 触发页面更新
        this.$forceUpdate();
      }
    },

    // 显示轮次变化通知
    showRoundChangeNotification(task, oldRound, newRound) {
      let title = '';
      let content = '';

      if (oldRound.status === 'waiting' && newRound.status === 'active') {
        // 从等待到当前轮次激活
        title = '新轮次开始';
        content = `任务"${task.route_name}"的第${newRound.currentRound?.round || '?'}轮巡视已经开始`;
      } else if (oldRound.status === 'active' && newRound.status === 'between_rounds') {
        // 从当前轮次到轮次间隔
        title = '当前轮次结束';
        content = `任务"${task.route_name}"的第${oldRound.currentRound?.round || '?'}轮巡视已经结束，请等待下一轮开始`;
      } else if (oldRound.status === 'between_rounds' && newRound.status === 'active') {
        // 从轮次间隔到新轮次激活
        title = '新轮次开始';
        content = `任务"${task.route_name}"的第${newRound.currentRound?.round || '?'}轮巡视已经开始`;
      } else if (oldRound.status === 'active' && newRound.status === 'completed') {
        // 从当前轮次到全部完成
        title = '所有轮次完成';
        content = `任务"${task.route_name}"的所有巡视轮次已经结束`;
      }

      // 显示通知
      if (title && content) {
        uni.showModal({
          title: title,
          content: content,
          showCancel: false,
          success: () => {
            // 用户确认后，如果是新轮次开始，自动选中该任务
            if (
              (oldRound.status === 'waiting' && newRound.status === 'active') ||
              (oldRound.status === 'between_rounds' && newRound.status === 'active')
            ) {
              this.onTaskClick(task);
            }
          }
        });
      }
    },

    // 计算两点间距离（米）
    calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371000; // 地球半径，单位米
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);

      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);

      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c;

      return distance; // 返回米为单位的距离
    },

    // 角度转弧度
    deg2rad(deg) {
      return deg * (Math.PI / 180);
    },

    // 检查用户登录状态
    checkLoginState() {
      try {
        // 如果已经显示了登录提示，不再重复显示
        if (this.isShowingLoginTip) {
          return false;
        }

        const userInfo = uni.getStorageSync('uni-id-pages-userInfo');
        if (!userInfo || !userInfo._id) {
          // 显示登录提示
          this.showLoginTip();
          return false;
        }
        return true;
      } catch (e) {
        console.error('检查登录状态出错', e);
        if (!this.isShowingLoginTip) {
          this.showLoginTip();
        }
        return false;
      }
    },

    // 显示登录提示
    showLoginTip() {
      // 设置标志，防止重复显示
      this.isShowingLoginTip = true;

      // 设置页面为空状态
      this.isEmpty = true;
      this.taskList = [];

      // 显示提示框
      uni.showModal({
        title: '需要登录',
        content: '巡视打卡功能需要登录后才能使用',
        confirmText: '去登录',
        cancelText: '返回首页',
        success: (res) => {
          if (res.confirm) {
            // 根据平台跳转到不同的登录页
            // #ifdef MP-WEIXIN
            uni.navigateTo({
              url: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd'
            });
            // #endif
            
            // #ifndef MP-WEIXIN
            uni.navigateTo({
              url: '/uni_modules/uni-id-pages/pages/login/login-withpwd'
            });
            // #endif
          } else {
            // 返回首页
            uni.switchTab({
              url: '/pages/index/index'
            });
          }
          // 清除标志
          setTimeout(() => {
            this.isShowingLoginTip = false;
          }, 1000);
        }
      });
    },

    // 添加加载用户数据的方法
    async loadUsers() {
      try {
        // 获取当前用户ID
        const userInfo = uni.getStorageSync('uni-id-pages-userInfo');
        const userId = userInfo ? (typeof userInfo === 'string' ? JSON.parse(userInfo)._id : userInfo._id) : '';

        if (!userId) {
          console.error('未获取到用户ID，可能未登录');
          return;
        }

        const result = await patrolApi.call({
          name: 'patrol-user',
          action: 'getUsers',
          data: {
            userid: userId,
            pageSize: 100
          }
        });

        if (result.code === 0) {
          const users = result.data.list || [];

          // 清空之前的用户映射
          this.userMap = {};

          users.forEach(user => {
            // 确保用户对象有name属性
            const processedUser = {
              ...user,
              // 按优先级选择用户显示名称
              name: user.real_name || user.nickname || user.username || '未命名用户'
            };
            this.userMap[user._id] = processedUser;
          });

          return users;
        } else {
          console.error('加载用户数据失败:', result.message);
        }
      } catch (e) {
        console.error('加载用户错误:', e);
      }
      return [];
    },

    // 添加获取用户名称的方法
    getUserName(userId) {
      if (userId && this.userMap[userId]) {
        return this.userMap[userId].name || '未知用户';
      }
      return '未分配';
    },

    // 添加格式化时间区间的方法
    formatTimeRange(task, round) {
      if (!round) return '--:-- - --:--';

      try {
        // 处理不同格式的时间字符串
        let startTime = round.start_time;
        let endTime = round.end_time;

        // 如果没有明确的开始或结束时间，返回占位符
        if (!startTime || !endTime) return '--:-- - --:--';

        // 使用formatDate工具函数格式化时间
        const formatTimeOnly = (dateStr) => {
          if (!dateStr) return '--:--';

          try {
            // 使用replace确保跨平台兼容性
            const d = new Date(dateStr.replace(/-/g, '/'));
            if (isNaN(d.getTime())) return '--:--';

            // 使用formatDate格式化时间
            return formatDate(d, 'HH:mm');
          } catch (e) {
            console.error('时间格式化错误:', e);
            return '--:--';
          }
        };

        return `${formatTimeOnly(startTime)} - ${formatTimeOnly(endTime)}`;
      } catch (e) {
        console.error('格式化时间区间出错:', e);
        return '--:-- - --:--';
      }
    },

    // 格式化倒计时
    formatCountdown(milliseconds) {
      if (!milliseconds || milliseconds <= 0) return '00:00';

      try {
        // 转换毫秒为小时、分钟和秒
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;

        // 格式化显示，使用padStart确保两位数显示
        if (hours > 0) {
          return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        } else {
          return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        }
      } catch (e) {
        console.error('格式化倒计时出错:', e);
        return '00:00';
      }
    },

    // 添加格式化有效时长方法
    formatValidTime(milliseconds, totalTime) {
      if (!milliseconds || milliseconds <= 0) return '00:00';

      try {
        // 如果是倒计时（等待开始），使用原有的倒计时格式
        if (milliseconds < 0) {
          return this.formatCountdown(Math.abs(milliseconds));
        }

        // 如果是已开始的有效时长，使用新格式
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);

        // 格式化总时长（仅显示小时和分钟）
        let totalTimeStr = '';
        if (totalTime) {
          const totalSec = Math.floor(totalTime / 1000);
          const totalHours = Math.floor(totalSec / 3600);
          const totalMinutes = Math.floor((totalSec % 3600) / 60);

          if (totalHours > 0) {
            totalTimeStr = `${totalHours}小时${totalMinutes}分钟`;
          } else {
            totalTimeStr = `${totalMinutes}分钟`;
          }
        }

        // 返回格式化的有效时长
        if (hours > 0) {
          return `有效时长: ${totalTimeStr || `${hours}小时${minutes}分钟`}`;
        } else {
          return `有效时长: ${totalTimeStr || `${minutes}分钟`}`;
        }
      } catch (e) {
        console.error('格式化有效时长出错:', e);
        return '有效时长: 0分钟';
      }
    },

    // 判断时间是否紧急（小于10分钟）
    isTimeUrgent(milliseconds) {
      if (!milliseconds) return false;
      return milliseconds < 10 * 60 * 1000; // 小于10分钟
    },

    // 添加判断任务是否为今天的方法
    isTaskToday(task) {
      if (!task || !task.patrol_date) return false;

      try {
        // 使用isToday工具函数判断任务日期是否为今天
        return isToday(task.patrol_date);
      } catch (e) {
        console.error('判断任务日期出错:', e);
        return false;
      }
    },

    // 刷新任务列表
    async refreshTaskList(showLoading = true) {
      // 🔥 防抖机制：避免短时间内重复请求
      const now = Date.now();
      const timeSinceLastLoad = now - this.lastLoadTime;
      
      // 如果正在加载中，跳过重复请求
      if (this.loadingInProgress) {
        console.log('任务加载中，跳过重复请求');
        return;
      }
      
      // 防抖：3秒内的重复调用直接返回
      if (timeSinceLastLoad < 3000) {
        console.log('防抖阻止重复加载，距离上次加载', timeSinceLastLoad, 'ms');
        return;
      }
      
      // 🔥 设置加载状态和时间戳
      this.loadingInProgress = true;
      this.lastLoadTime = now;

      if (showLoading) {
        uni.showToast({
          title: '正在刷新任务...',
          icon: 'loading',
          mask: true,
          duration: 2000
        });
      }

      // 保存当前激活的任务ID和地图中心点
      const savedActiveTaskId = this.activeTaskId;
      const savedMapCenter = { ...this.mapCenter };

      // 设置初始加载标记
      this.isInitialLoading = true;

      try {
        // 核心逻辑：先加载任务列表
        await this.loadTaskList(false); // 使用静默加载

        // 检查之前选中的任务是否仍然存在于新列表中
        const taskExists = this.taskList.some(task => task._id === savedActiveTaskId);

        if (savedActiveTaskId && taskExists) {
          // 如果任务存在，重新加载该任务的详情以确保数据最新
          // 并且确保 loadTaskDetail 内部会更新 currentTask 和 taskList
          await this.loadTaskDetail(savedActiveTaskId, false, true); // 使用强制刷新
          // 恢复地图中心点（如果需要）
          // this.mapCenter = savedMapCenter; // 可以视情况决定是否恢复
        } else if (this.taskList.length > 0 && !this.activeTaskId) {
           // 如果之前没有选中任务，或者选中的任务消失了，自动选中第一个
           this.activeTaskId = this.taskList[0]._id;
           await this.loadTaskDetail(this.activeTaskId);
        } else if (!taskExists) {
          // 如果选中的任务消失了，清空状态
          this.activeTaskId = '';
          this.currentTask = null;
          this.markers = [];
          this.polylines = [];
          this.circles = [];
        }
        // 如果 taskList 为空，则不需要做额外操作

      } catch (error) {
        console.error('刷新任务列表过程中出错:', error);
        this.showError('刷新任务失败');
      } finally {
        // 🔥 确保清除加载状态
        this.loadingInProgress = false;
        
        // 清除初始加载标记，并给予一定延迟
        setTimeout(() => {
          this.isInitialLoading = false;
        }, 500); // 稍长延迟确保渲染稳定

        if (showLoading) {
          // 确保隐藏加载提示
          setTimeout(() => {
            uni.hideLoading();
          }, 150);
        }
      }
    },

    // 处理任务更新事件 - 优化处理逻辑
    handleTaskUpdated(data) {
      console.log('收到任务更新事件:', data);
      const { task_id, point_id, round } = data;

      // 如果收到了轮次信息，先更新当前任务的轮次
      if (round && this.currentTask && this.currentTask._id === task_id) {
        console.log('检查轮次更新:', round);
        // 确保rounds_detail存在
        if (this.currentTask.rounds_detail) {
          const existingRound = this.currentTask.rounds_detail.find(r => r.round === round);
          if (!existingRound) {
            console.log('发现新轮次，更新轮次信息:', round);
            // 更新选中的轮次
            this.selectedRound = {
              taskId: task_id,
              round: {
                round: round,
                status: 1
              }
            };
          }
        }
      }

      // 标记需要刷新，并延迟执行刷新，避免过于频繁的操作
      if (!this.refreshTimeout) {
        this.refreshTimeout = setTimeout(async () => {
          console.log('执行延迟刷新...');
          try {
            // 简化刷新逻辑，只刷新任务详情，不进行额外的状态更新
            // 避免状态计算冲突导致状态反复变化
            if (this.activeTaskId === task_id) {
              // 只有当前选中的任务才进行强制刷新
              await this.loadTaskDetail(task_id, false, true);
            } else {
              // 否则只进行静默刷新
              await this.refreshTaskList(false);
            }
          } catch (error) {
            console.error('刷新任务数据失败:', error);
          } finally {
            this.refreshTimeout = null;
          }
        }, 1500); // 延长间隔时间，减少刷新频率
      }
    },

    // 处理任务列表数据 - 从任务列表页复制的方法
    processTasks(list) {
      if (!list || !Array.isArray(list)) return [];

      const now = new Date(); // 当前时间，用于判断轮次状态

      // 处理任务数据
      const processedTasks = list.map(task => {
        // 基础任务数据处理
        const processedTask = {
          ...task,
          points: task.points || [],
          completed_points: task.completed_points || [],
          status: parseInt(task.status || 0),
          area: task.area || task.route_name || '未指定区域',
          patrol_date: task.patrol_date || this.formatDateForCompare(new Date()),
          shift_id: task.shift_id || '',
          name: task.name || task.route_name || '未命名任务'
        };

        // 保存服务器返回的原始状态
        const originalStatus = processedTask.status;

        // 初始加载期间或对于已有明确状态的任务，保持服务器状态不变
        if (this.isInitialLoading || originalStatus === 2 || originalStatus === 4) {
          return processedTask;
        }

        // 如果任务已取消或已完成，直接返回
        if (originalStatus === 4 || originalStatus === 2) {
          return processedTask;
        }

        // 处理轮次详情，确保状态正确
        if (processedTask.rounds_detail && processedTask.rounds_detail.length > 0) {
          let allRoundsCompleted = true;
          let hasActiveRound = false;
          let hasUpcomingRound = false;
          let allRoundsExpired = true;

          // 遍历所有轮次，检查状态
          processedTask.rounds_detail.forEach(round => {
            if (!round) return;

            // 确保轮次中有day_offset和duration字段
            round.day_offset = round.day_offset !== undefined ? Number(round.day_offset) : 0;
            round.duration = round.duration !== undefined ? Number(round.duration) : 60;

            try {
              // 解析轮次时间
              const roundStartTime = new Date(round.start_time);
              const roundEndTime = new Date(round.end_time);

              // 检查点位完成情况
              const isAllPointsCompleted = round.stats &&
                                         round.stats.total_points > 0 &&
                                         round.stats.completed_points >= round.stats.total_points;

              // 增加状态稳定性，保留已有的完成和超时状态
              if (round.status === 2 || round.status === 3) {
                // 保持已完成或已超时状态
                if (round.status === 2) allRoundsExpired = false;
                if (round.status === 3) allRoundsCompleted = false;
              } else {
                // 确定轮次状态
                if (isAllPointsCompleted) {
                  round.status = 2; // 已完成
                  allRoundsExpired = false;
                } else if (now < roundStartTime) {
                  round.status = 0; // 未开始
                  hasUpcomingRound = true;
                  allRoundsCompleted = false;
                  allRoundsExpired = false;
                } else if (now > roundEndTime) {
                  round.status = 3; // 已超时
                  allRoundsCompleted = false;
                } else {
                  round.status = 1; // 进行中
                  hasActiveRound = true;
                  allRoundsCompleted = false;
                  allRoundsExpired = false;
                }
              }

              // 记录状态变化日志，帮助诊断问题
              if (originalStatus !== processedTask.status) {
                console.log(`轮次[${round.round}]状态: ${round.status}`);
              }
            } catch (error) {
              console.error(`解析轮次[${round.round}]时间出错:`, error);
            }
          });

          // 记录原来的状态
          const prevStatus = processedTask.status;

          // 根据轮次状态确定任务状态
          if (allRoundsCompleted ||
              (processedTask.overall_stats &&
               processedTask.overall_stats.completion_rate === 1)) {
            // 如果所有轮次已完成或总体完成率为100%，任务状态为已完成
            processedTask.status = 2;
          } else if (hasActiveRound) {
            // 如果有进行中的轮次，任务状态为进行中
            processedTask.status = 1;
          } else if (hasUpcomingRound) {
            // 如果有未开始的轮次，任务状态为未开始
            processedTask.status = 0;
          } else if (allRoundsExpired) {
            // 如果所有轮次都已超时，任务状态为已超时
            processedTask.status = 3;
          }

          // 记录状态变化
          if (prevStatus !== processedTask.status) {
            console.log(`任务状态已更新: ${prevStatus} → ${processedTask.status}`);
          }

          // 根据轮次状态排序
          const notStartedOrActive = processedTask.rounds_detail.filter(
            round => round.status === 0 || round.status === 1
          );
          const completedOrExpired = processedTask.rounds_detail.filter(
            round => round.status === 2 || round.status === 3
          );

          notStartedOrActive.sort((a, b) => a.round - b.round);
          completedOrExpired.sort((a, b) => b.round - a.round);

          processedTask.rounds_detail = [...notStartedOrActive, ...completedOrExpired];
        }

        return processedTask;
      });

      return processedTasks;
    },

    // 格式化日期用于比较
    formatDateForCompare(date) {
      if (!date) {
        return '';
      }

      try {
        // 确保date是Date对象
        if (!(date instanceof Date)) {
          // 尝试解析日期字符串
          const parsedDate = new Date(date);
          if (isNaN(parsedDate.getTime())) {
            console.error('无效的日期字符串:', date);
            return '';
          }
          date = parsedDate;
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          console.error('无效的日期:', date);
          return '';
        }

        // 获取本地时区的年月日
        const year = date.getFullYear();
        // 月份范围是0-11，需要+1并补零
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        const formattedDate = `${year}-${month}-${day}`;
        return formattedDate;
      } catch (e) {
        console.error('格式化日期错误:', e, '原始日期:', date);
        return '';
      }
    },

    // 处理轮次选择事件
    onRoundSelected(data) {
      // 性能优化：只在实际发生变化时更新和重建标记点
      const isChanging = !this.selectedRound ||
                         this.selectedRound.taskId !== data.taskId ||
                         this.selectedRound.round.round !== data.round.round;

      // 如果点击的是当前选中的轮次，取消选择（恢复自动模式）
      if (this.selectedRound &&
          this.selectedRound.taskId === data.taskId &&
          this.selectedRound.round.round === data.round.round) {
        this.selectedRound = null;
      } else {
        this.selectedRound = data;
      }

      // 如果选择的轮次属于当前激活的任务，且轮次发生了变化，重新构建标记点
      if (isChanging && this.currentTask && this.currentTask._id === data.taskId) {
        this.buildTaskMarkers();
      }
    },

    // 切换抽屉菜单
    toggleDrawer() {
      this.isDrawerOpen = !this.isDrawerOpen;
    },

    // 关闭抽屉菜单
    closeDrawer() {
      this.drawerStyle = {
        transform: 'translateY(100%)',
        transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
      };
      this.isDrawerOpen = false;
    },

    // 打开抽屉菜单
    openDrawer() {
      this.isDrawerOpen = true;
    },

    // 强制更新任务列表中的任务状态
    forceUpdateTaskInList(taskId) {
      if (!taskId || !this.taskList || this.taskList.length === 0) return;

      console.log('强制更新任务列表中的任务:', taskId);

      // 获取任务最新数据
      patrolApi.call({
        name: 'patrol-task',
        action: 'getTaskDetail',
        data: {
          task_id: taskId, // 🔥 使用优化后的参数格式
          level: 'map' // 🔥 启用地图专用模式
        }
      }).then(res => {
        if (res.code === 0 && res.data) {
          // 找到并替换任务
          const taskIndex = this.taskList.findIndex(t => t._id === taskId);
          if (taskIndex >= 0) {
            // 使用深拷贝创建新引用
            const newTaskList = [...this.taskList];
            newTaskList[taskIndex] = JSON.parse(JSON.stringify(res.data));

            // 确保处理轮次信息
            if (newTaskList[taskIndex].rounds_detail && newTaskList[taskIndex].rounds_detail.length > 0) {
              const processedTasks = this.processTasks([newTaskList[taskIndex]]);
              if (processedTasks.length > 0) {
                newTaskList[taskIndex] = processedTasks[0];
              }
            }

            // 替换整个数组以确保视图更新
            this.taskList = newTaskList;
            console.log('已更新任务列表中的任务:', taskId);
          }
        }
      }).catch(error => {
        console.error('获取任务详情失败:', error);
      });
    },

    handleTouchStart(event) {
      if (!this.isDrawerOpen) return;

      // iOS设备上禁用拖拽手势，避免事件穿透问题
      if (uni.getSystemInfoSync().platform === 'ios') {
        return;
      }

      this.touchStartY = event.touches[0].clientY;
      this.isDragging = true;
      this.currentTranslateY = 0;
      this.lastTouchTime = Date.now();
      this.lastDeltaY = 0;

      // 获取抽屉高度
      const query = uni.createSelectorQuery().in(this);
      query.select('.task-drawer').boundingClientRect(data => {
        if (data) {
          this.drawerHeight = data.height;
        }
      }).exec();
    },

    handleTouchMove(event) {
      if (!this.isDragging || !this.isDrawerOpen) return;

      // iOS设备上禁用拖拽手势
      if (uni.getSystemInfoSync().platform === 'ios') {
        return;
      }

      const currentY = event.touches[0].clientY;
      const deltaY = currentY - this.touchStartY;
      const currentTime = Date.now();
      const timeDiff = currentTime - this.lastTouchTime;

      // 计算滑动速度 (像素/毫秒)
      const velocity = Math.abs((deltaY - this.lastDeltaY) / timeDiff);

      if (deltaY < 0) return;

      // 根据滑动速度和距离动态调整阻尼效果
      let damping = 0.9; // 基础阻尼系数
      if (velocity > 0.5) { // 如果滑动速度较快
        damping = 1; // 减小阻尼
      } else if (deltaY < this.drawerHeight * 0.2) { // 在开始阶段
        damping = 0.95; // 稍微减小阻尼，使初始滑动更容易
      }

      this.currentTranslateY = Math.min(deltaY * damping, this.drawerHeight);

      // 应用变换
      this.drawerStyle = {
        transform: `translateY(${this.currentTranslateY}px)`,
        transition: 'none'
      };

      // 更新遮罩层透明度
      const maskOpacity = Math.max(0.4 - (deltaY / this.drawerHeight) * 0.4, 0);
      this.maskStyle = {
        backgroundColor: `rgba(0, 0, 0, ${maskOpacity})`
      };

      // 更新状态
      this.lastTouchTime = currentTime;
      this.lastDeltaY = deltaY;
    },

    handleTouchEnd() {
      if (!this.isDragging || !this.isDrawerOpen) return;

      // iOS设备上禁用拖拽手势
      if (uni.getSystemInfoSync().platform === 'ios') {
        return;
      }

      this.isDragging = false;

      // 计算最终速度
      const endTime = Date.now();
      const timeDiff = endTime - this.lastTouchTime;
      const velocity = Math.abs((this.currentTranslateY - this.lastDeltaY) / timeDiff);

      // 动态关闭阈值：根据速度和距离综合判断
      let closeThreshold = this.drawerHeight * 0.2; // 默认20%
      if (velocity > 0.5) { // 如果速度较快
        closeThreshold = this.drawerHeight * 0.1; // 降低到10%
      }

      // 恢复过渡动画
      this.drawerStyle = {
        transform: this.currentTranslateY > closeThreshold ? 'translateY(100%)' : 'translateY(0)',
        transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
      };

      // 如果拖动距离超过阈值，关闭抽屉
      if (this.currentTranslateY > closeThreshold) {
        this.closeDrawer();
      } else {
        // 否则恢复原位
        this.maskStyle = {
          backgroundColor: 'rgba(0, 0, 0, 0.4)'
        };
      }

      this.currentTranslateY = 0;
      this.lastDeltaY = 0;
    },

    // 添加getRoundStatus方法
    getRoundStatus(round, now = new Date()) {
      if (!round || !round.start_time || !round.end_time) {
        return 0; // 默认未开始
      }

      try {
        // 如果轮次已经有状态且为已完成，保持完成状态
        if (round.status === 2) {
          return 2;
        }

        const startTime = new Date(round.start_time.replace(/-/g, '/'));
        const endTime = new Date(round.end_time.replace(/-/g, '/'));

        // 检查点位完成情况
        const isAllPointsCompleted = round.stats &&
            round.stats.total_points > 0 &&
            round.stats.completed_points >= round.stats.total_points;

        // 如果所有点位已完成，则轮次状态为已完成
        if (isAllPointsCompleted) {
          return 2;
        }

        // 根据时间判断状态
        if (now < startTime) {
          return 0; // 未开始
        } else if (now > endTime) {
          return 3; // 已超时
        } else {
          return 1; // 进行中
        }
      } catch (error) {
        console.error('计算轮次状态出错:', error);
        return 0; // 出错时默认未开始
      }
    },
  }
};
</script>

<style lang="scss">
.patrol-index {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8; /* 浅灰白色背景 */
  position: relative;
  overflow: hidden; /* 防止内容溢出 */

  /* 强制硬件加速，解决滚动和动画卡顿问题 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;

  /* GPS精度显示样式 */
  .location-accuracy {
    position: absolute;
    left: 20rpx;
    bottom: calc(20rpx + env(safe-area-inset-bottom));
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    padding: 12rpx 20rpx;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    z-index: 120;
    display: flex;
    align-items: center;
    gap: 12rpx;
  }

  .status-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .accuracy-text {
    font-size: 26rpx;
    color: #333;
    font-weight: 500;
  }

  /* 所有scroll-view通用样式：隐藏滚动条 */
  :deep(scroll-view) {
    &::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      color: transparent;
    }
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .map-controls {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 100;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  /* 右下角浮动菜单按钮 */
  .float-menu-btn {
    position: fixed;
    right: 30rpx;
    bottom: calc(30rpx + constant(safe-area-inset-bottom));
    bottom: calc(30rpx + env(safe-area-inset-bottom));
    z-index: 200;
    width: 120rpx;
    height: 120rpx;

    &__inner {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #1677FF, #1890FF);
      border-radius: 50%;
      box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s ease;

      &:active {
        transform: scale(0.92);
      }
    }

    &__text {
      font-size: 24rpx;
      color: #FFFFFF;
      margin-top: 6rpx;
      font-weight: 500;
    }
  }



  .control-buttons {
    display: flex;
    flex-direction: column;

    .control-btn {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      margin: 0 0 20rpx 0;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);

      &::after {
        border: none;
      }

      .iconfont {
        font-size: 32rpx;
        color: #1677FF;
      }
    }
  }

  .weather-compact {
    position: absolute;
    top: 20rpx;
    left: 20rpx;
    z-index: 120;
    background: none;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
    max-width: 60%;
  }

  /* 抽屉遮罩层 */
  .drawer-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 900;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100vw; /* 确保宽度100% */
    height: 100vh; /* 确保高度100% */

    &--active {
      opacity: 1;
      visibility: visible;
    }
  }

  /* 任务抽屉菜单 */
  .task-drawer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #FFFFFF;
    border-radius: 20rpx 20rpx 0 0;
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 901;
    max-height: 85vh;
    touch-action: pan-y;

    &--active {
      transform: translateY(0);
    }

    .drawer-handle {
      width: 60rpx;
      height: 6rpx;
      background-color: #E0E0E0;
      border-radius: 3rpx;
      margin: 16rpx auto;
      position: relative;
    }

    .drawer-content {
      height: calc(85vh - 40rpx);
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    /* 抽屉头部 */
    &__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx 20rpx;
      border-bottom: 1rpx solid #eee;
      background: linear-gradient(to right, #f8f8f8, #ffffff);
    }

    &__title-container {
      display: flex;
      align-items: center;
      flex: 1;
    }

    &__date {
      font-size: 28rpx;
      font-weight: bold;
      color: #1677FF;
      margin: 0 8rpx;
    }

    &__title-text {
      font-size: 26rpx;
      color: #666;
      margin-left: 6rpx;
    }

    &__refresh-btn, &__close-btn {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background-color: #f0f7ff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 10rpx;

      &:active {
        opacity: 0.8;
      }
    }

    &__close-btn {
      background-color: #f5f5f5;
    }

    /* 任务列表区域 */
    &__list {
      flex: 1;
      height: auto; /* 改为auto以适应内容 */
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      padding: 5rpx; /* 增加底部内边距 */
      background-color: #fff;
      width: 100%; /* 确保宽度100% */
      box-sizing: border-box; /* 确保padding不会增加宽度 */

      /* 隐藏滚动条 - 专为微信小程序优化 */
      &::-webkit-scrollbar {
        display: none;
        width: 0 !important;
        height: 0 !important;
        background: transparent;
        color: transparent;
      }

      /* 清除浮动和设置边界处理 */
      &::after {
        content: "";
        display: block;
        height: 0;
        clear: both;
      }
    }

    &__list-content {
      padding: 10rpx;
      padding-bottom: 50rpx; /* 增加底部内边距确保内容显示完整 */
      background-color: #fff; /* 确保内容区也是白色 */
      width: 100%; /* 确保宽度100% */
      box-sizing: border-box; /* 确保padding不会增加宽度 */
    }

    /* 修改任务卡片在抽屉中的样式 */
    :deep(.p-task-card) {
      margin-bottom: 20rpx;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

      &[active="true"] {
        border: 3rpx solid #1677FF !important;
        box-shadow: 0 4rpx 16rpx rgba(22, 119, 255, 0.15) !important;
      }
    }
  }
}
</style>
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.avatar-picker-component.data-v-7979a2f4 {
  width: 100%;
  position: relative;
}
.avatar-picker.data-v-7979a2f4 {
  width: 100%;
  position: relative;
}
.avatar-card.data-v-7979a2f4 {
  background: #FFFFFF;
  border-radius: 8rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.avatar-preview.data-v-7979a2f4 {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}
.avatar-preview .avatar-image.data-v-7979a2f4 {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  overflow: hidden;
  background: #ffffff;
  box-shadow: inset 0 0 0 1rpx rgba(0, 0, 0, 0.1);
}
.avatar-preview .avatar-image[src*=default-avatar].data-v-7979a2f4 {
  padding: 24rpx;
  background: linear-gradient(135deg, #e6f3ff 0%, #ffffff 100%);
  box-sizing: border-box;
}
.avatar-preview .clear-btn.data-v-7979a2f4 {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #ef4444;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  line-height: 1;
}
.avatar-preview .clear-btn.data-v-7979a2f4:active {
  background: #dc2626;
}
.avatar-actions.data-v-7979a2f4 {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-7979a2f4 {
  flex: 1;
  height: 72rpx;
  line-height: 72rpx;
  font-size: 28rpx;
  border-radius: 4rpx;
  border: none;
  padding: 0;
  margin: 0;
}
.action-btn.data-v-7979a2f4::after {
  display: none;
}
.action-btn.upload-btn.data-v-7979a2f4 {
  background: #1890ff;
  color: #FFFFFF;
}
.action-btn.select-btn.data-v-7979a2f4 {
  background: #FFFFFF;
  color: #1890ff;
  border: 1rpx solid #1890ff;
}
.history-popup.data-v-7979a2f4 {
  background: #FFFFFF;
  border-radius: 12rpx;
  width: 640rpx;
  min-height: 200rpx;
  max-height: 720rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.popup-header.data-v-7979a2f4 {
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
  flex-shrink: 0;
}
.popup-header .popup-title.data-v-7979a2f4 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.popup-header .popup-close.data-v-7979a2f4 {
  padding: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.popup-header .popup-close.data-v-7979a2f4:active {
  background: #f5f5f5;
}
.history-list.data-v-7979a2f4 {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}
.history-grid.data-v-7979a2f4 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  padding: 16rpx;
  box-sizing: border-box;
}
.history-item.data-v-7979a2f4 {
  width: 100%;
  box-sizing: border-box;
}
.history-item .item-container.data-v-7979a2f4 {
  background: #FFFFFF;
  border-radius: 12rpx;
  border: 1rpx solid #EEEEEE;
  padding: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  transition: all 0.2s ease;
  width: 100%;
  box-sizing: border-box;
}
.history-item .item-container.data-v-7979a2f4:active {
  background: #f5f5f5;
}
.history-item.is-selected .item-container.data-v-7979a2f4 {
  background: #e6f7ff;
  border-color: #1890ff;
}
.history-item.is-selected .item-container .history-name.data-v-7979a2f4 {
  color: #1890ff;
}
.avatar-wrapper.data-v-7979a2f4 {
  position: relative;
  width: 100rpx;
  height: 100rpx;
}
.avatar-wrapper .history-avatar.data-v-7979a2f4 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  background: #ffffff;
  border: 2rpx solid #f0f0f0;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.avatar-wrapper .delete-btn.data-v-7979a2f4 {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background: #ff4d4f;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
  border: 2rpx solid #ffffff;
  padding: 0;
}
.history-name.data-v-7979a2f4 {
  font-size: 24rpx;
  color: #333333;
  width: 100%;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 4rpx;
  box-sizing: border-box;
}
.batch-upload-popup.data-v-7979a2f4 {
  background: #FFFFFF;
  border-radius: 12rpx;
  width: 640rpx;
  max-height: 800rpx;
  display: flex;
  flex-direction: column;
}
.batch-upload-popup .batch-list.data-v-7979a2f4 {
  flex: 1;
  min-height: 400rpx;
  max-height: 600rpx;
}
.batch-upload-popup .batch-list .batch-table-header.data-v-7979a2f4,
.batch-upload-popup .batch-list .batch-table-row.data-v-7979a2f4 {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border-bottom: 1rpx solid #eee;
}
.batch-upload-popup .batch-list .batch-table-header .col-name.data-v-7979a2f4,
.batch-upload-popup .batch-list .batch-table-row .col-name.data-v-7979a2f4 {
  flex: 2;
  padding-right: 16rpx;
}
.batch-upload-popup .batch-list .batch-table-header .col-avatar.data-v-7979a2f4,
.batch-upload-popup .batch-list .batch-table-row .col-avatar.data-v-7979a2f4 {
  flex: 1;
  padding: 0 16rpx;
}
.batch-upload-popup .batch-list .batch-table-header .col-action.data-v-7979a2f4,
.batch-upload-popup .batch-list .batch-table-row .col-action.data-v-7979a2f4 {
  flex: 1;
  padding-left: 16rpx;
  text-align: center;
}
.batch-upload-popup .batch-list .batch-table-header.data-v-7979a2f4 {
  font-size: 28rpx;
  color: #666666;
  background: #f8f8f8;
}
.batch-upload-popup .batch-list .batch-table-row .upload-area.data-v-7979a2f4 {
  width: 70rpx;
  height: 70rpx;
  background: #f8f8f8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.batch-upload-popup .batch-list .batch-table-row .upload-area .preview-image.data-v-7979a2f4 {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}
.batch-upload-popup .batch-list .batch-table-row .upload-area .upload-placeholder.data-v-7979a2f4 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
}
.batch-upload-popup .batch-list .batch-table-row .upload-area .upload-placeholder .uni-icons.data-v-7979a2f4 {
  font-size: 24rpx;
}
.batch-upload-popup .batch-list .batch-table-row .save-btn.data-v-7979a2f4 {
  width: 120rpx;
  height: 56rpx;
  line-height: 56rpx;
  font-size: 24rpx;
  background: #1890ff;
  color: #FFFFFF;
  border-radius: 4rpx;
  padding: 0;
}
.batch-upload-popup .batch-list .batch-table-row .save-btn.btn-disabled.data-v-7979a2f4 {
  background: #d9d9d9;
}
.batch-upload-popup .popup-footer.data-v-7979a2f4 {
  padding: 24rpx;
  display: flex;
  gap: 16rpx;
  border-top: 1rpx solid #eee;
}
.batch-upload-popup .popup-footer button.data-v-7979a2f4 {
  flex: 1;
  height: 72rpx;
  line-height: 72rpx;
  font-size: 28rpx;
  border-radius: 4rpx;
}
.batch-upload-popup .popup-footer button.data-v-7979a2f4::after {
  display: none;
}
.batch-upload-popup .popup-footer .add-btn.data-v-7979a2f4 {
  background: #1890ff;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}
.batch-upload-popup .popup-footer .close-btn.data-v-7979a2f4 {
  background: #f5f5f5;
  color: #666666;
}
.history-empty.data-v-7979a2f4 {
  padding: 60rpx 0;
}

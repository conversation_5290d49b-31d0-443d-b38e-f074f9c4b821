{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Xwzc/pages/feedback/list.vue?b803", "webpack:///D:/Xwzc/pages/feedback/list.vue?149e", "webpack:///D:/Xwzc/pages/feedback/list.vue?f865", "uni-app:///pages/feedback/list.vue", "webpack:///D:/Xwzc/pages/feedback/list.vue?b03c", "webpack:///D:/Xwzc/pages/feedback/list.vue?203b", "webpack:///D:/Xwzc/pages/feedback/list.vue?9db0", "webpack:///D:/Xwzc/pages/feedback/list.vue?b3b9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "args", "timer", "fn", "components", "PEmptyState", "data", "size", "current", "count", "responsibleOptions", "responsibleMap", "createDateRange", "searchParams", "keyword", "project", "responsible", "status", "urgency", "projectOptions", "text", "value", "isLoading", "isTokenValid", "userRoles", "statusOptions", "urgencyOptions", "feedbackList", "totalCount", "currentPage", "pageSize", "currentPageStart", "searchTimer", "hasInitialized", "lastRefreshTime", "isPageVisible", "needsRefreshOnShow", "computed", "hasOperationPermission", "hasEditPermission", "hasDeletePermission", "created", "uni", "console", "onLoad", "title", "icon", "onReady", "onPullDownRefresh", "onShow", "onHide", "onUnload", "methods", "handleError", "silentRefresh", "loadResponsibleMap", "cacheManager", "uniCloud", "name", "action", "res", "responsibleList", "map", "getResponsibleName", "previewImage", "urls", "fail", "handleImageError", "event", "onPageChange", "onProjectChange", "onResponsibleChange", "onCreateDateChange", "validateResponse", "checkAndSetTokenStatus", "checkTokenStatus", "token", "tokenExpired", "handleTokenInvalid", "setupRequestInterceptor", "db", "invoke", "success", "complete", "setupTokenEventListeners", "removeTokenEventListeners", "handleGlobalTokenExpired", "setupFeedbackEventListeners", "removeFeedbackEventListeners", "handleFeedbackSubmitted", "duration", "handleFeedbackUpdated", "handleTaskCompleted", "handleTaskStatusChanged", "initializeWorkflowOptions", "loadFeedbackList", "silent", "params", "pageNum", "startDateStr", "endDateStr", "startParts", "endParts", "startDate", "endDate", "start", "end", "onStatusChange", "onUrgencyChange", "onKeywordSearch", "clearTimeout", "viewDetail", "url", "editItem", "submitCompletion", "deleteItem", "content", "confirmText", "confirmColor", "performDelete", "mask", "id", "timestamp", "e", "hour", "minute", "index", "item", "auditActions", "reasons", "reasonText", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACqC;AACvB;;;AAGjE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,gZAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnJA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACyqBlnB;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAGA;AACA;EAAA;EACA;EACA;IAAA;IAAA;MAAAC;IAAA;IACA;IACAC;MACAC;IACA;EACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;EACA;EACA;AACA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;IACA;MACAC;MACAC;MACAC;IACA;IAEA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC,iBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MAAA;;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;;MAEA;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IAAA;IACA;IACA;;IAEA;IACAC;MACA;QACA;QACA;QACA;UACAC;UACA;UACA;QACA;MACA;IACA;EAEA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;cACA;cAAA;cAGA;cACA;cAAA;cAAA,OAEA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;cACA;cACA;cACA;cACA;cACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAF;gBACAG;gBACAC;cACA;cACA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAC;IACA;EAAA,CACA;EACAC;IAAA;IACA;MACAN;MACA;IACA;EACA;EACAO;IACA;IACA;;IAEA;IACA;IACA;;IAEA;IACA;MACA;MACA;MACA;MACA;MACA;IACA;;IAEA;IACA;MACA;MACA;MACA;MACA;IACA;;IAEA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;MACA;MACA;MACA;IACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACAT;EACA;EACAU;IACA;IAEA;IACAC;MACA;MACAX;QACAG;QACAC;MACA;IACA;IAEA;IACAQ;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACA;gBACA,6BACA;kBAAAnC;kBAAAC;gBAAA,EACA;gBACA;gBAAA;cAAA;gBAAA;gBAKA;gBACA;;gBAEA;gBAAA;gBAAA,OACAmC;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BAAA;0BAAA,OAEAC;4BACAC;4BACApD;8BACAqD;4BACA;0BACA;wBAAA;0BALAC;0BAAA,MAOA;4BAAA;4BAAA;0BAAA;0BAAA,MACA;wBAAA;0BAAA,kCAGAA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA,CACA;cAAA;gBAdAC;gBAgBA;gBACA,6BACA;kBAAAzC;kBAAAC;gBAAA,2CACAwC;kBAAA;oBACAxC;oBACAD;kBACA;gBAAA,IACA;;gBAEA;gBACA;kBACA0C;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIA;gBACA;kBACA;kBACAnB;gBACA;;gBAEA;gBACA,6BACA;kBAAAvB;kBAAAC;gBAAA,EACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA0C;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;;MAEA;;MAEA;QACAtB;UACAuB;UACAzD;UACA0D;YACA;UAAA;QAEA;MACA;QACA;MAAA;;MAIA;IAUA;IACAC;MACAC;IACA;IAGA;IACAC;MACA;MACA;IACA;IAEA;IACA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;MAEA;MACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAGAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;MACApC;MACAA;MACAA;;MAEA;MACAc;;MAEA;MACA;IACA;IACA;IACAuB;MAAA;MACA;MACA;MACAC;QACAC;UACA;QAAA,CACA;QACAC;UACA;UACA;YACA;UACA;UACA;QACA;QACAhB;UACA;QACA;QACAiB;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA1C;MACAA;IACA;IAEA;IACA2C;MACA3C;MACAA;IACA;IAEA;IACA4C;MACA;MACA;MACA5C;MACAA;MACAA;;MAEA;MACAc;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACA+B;MACA;MACA7C;MACA;MACAA;MACA;MACAA;MACA;MACAA;IACA;IAEA;IACA8C;MACA9C;MACAA;MACAA;MACAA;IACA;IAEA;IACA+C;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA/C;UACAG;UACAC;UACA4C;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;;MAEA;MACAlD;QACAG;QACAC;QACA4C;MACA;IACA;IAEA;IACAG;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACAtC;cAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAuC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAGAC;kBACAtC;kBACA7B;kBACAoE;kBACAnF;kBACAE;kBACAH;kBACAI;kBACAF;gBACA,GAEA;gBACA;kBACAmF;kBACAC;kBAEAC;kBACAC,kCAEA;kBACAC;kBACAC;kBAEAP;oBACAQ;oBACAC;kBACA;gBACA;gBAAA;gBAAA,OAEAjD;kBACAC;kBACApD;gBACA;cAAA;gBAHAsD;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBAAA,mBACAA;gBACA;gBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACA;gBACAlB;kBACAG;kBACAC;kBACA4C;gBACA;cAAA;gBAAA;gBAEA;kBACA;gBACA;gBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiB;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAC;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACArE;QACAsE;MACA;IACA;IAEA;IACAC;MACAvE;QACAsE;MACA;IACA;IAEA;IACAE;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAxE;kBACAsE;kBACA9C;oBACAxB;sBACAG;sBACAC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAqE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAzE;kBACAG;kBACAuE;kBACAC;kBACAC;kBACApC;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAtB;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,OACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA2D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA7E;kBACAG;kBACA2E;gBACA;gBAAA;gBAAA;gBAAA,OAIA/D;kBACAC;kBACApD;oBACAqD;oBACA8D;kBACA;gBACA;cAAA;gBANA7D;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACAlB;kBACAG;kBACAC;gBACA;;gBAEA;gBACAJ;kBACAiB;kBACA8D;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAhF;kBACAG;kBACAC;gBACA;cAAA;gBAAA;gBAEAJ;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EAAA,iFAGAiF;IACA;IACA;EACA,8EAGAD;IAAA;IACA;;IAEA;IACA;MACA;IACA;;IAEA;IACA;IACA;MAAAE;MAAAC;IAAA;EACA,kFAGAC;IACA;EACA,4FAKA;IACA;EACA,8FAGA;IACA;EACA,kFAGAzG;IACA;IACA;IACA;IACA;EACA,8FAGA;IACA;EACA,gGAGA;IACA;EACA,oFAGAA;IACA;IACA;IACA;IACA;EACA,8FAGA;IACA;EACA,gGAGA;IACA;EACA,oFAGAA;IACA;IACA;IACA;IACA;EACA,sGAGA;IACA;EACA,wGAGA;IACA;EACA,4FAGAA;IACA;IACA;IACA;IACA;EACA,oFAGAA;IACA;IACA;MAAA;IAAA;IACA;EACA,sFAGAA;IACA;IACA;MAAA;IAAA;IACA;EACA,sFAGAA;IACA;IACA;MAAA;IAAA;IACA;EACA,8FAGAA;IACA;IACA;MAAA;IAAA;IACA;EACA,0FAWA0G;IACA;IACA;;IAEA;IACA;MACA;MACA;QAAA,OACA,kEACA;MAAA,EACA;;MAEA;MACAC;QAAA;MAAA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QAEA;QACA;UACAC;QACA;MACA;IACA;IAEA;EACA,0FAKAC;IACA;MACA;IACA;IAEA;IACA;MACA;IACA;IAEA;EACA,0HAGA5H;IACA;IACA;MACA;MACA;IACA;;IAEA;IACA;IACA;MACA;MACA;QACA;QACA;UAAA;QAAA;QACA;UACA;QACA;MACA;IACA;;IAEA;IACA;MACA;MACA;QAAA;MAAA;MACA;QACA;MACA;IACA;;IAEA;IACA;MACA;IACA;;IAEA;IACA;MACA;IACA;IAEA;EACA,aACA;EACA6H;IACA;IACAzF;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3oDA;AAAA;AAAA;AAAA;AAAw4B,CAAgB,y4BAAG,EAAC,C;;;;;;;;;;;ACA55B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAyoC,CAAgB,+mCAAG,EAAC,C;;;;;;;;;;;ACA7pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/feedback/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/feedback/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=2980693f&scoped=true&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=2980693f&scoped=true&lang=css&\"\nimport style1 from \"./list.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2980693f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/feedback/list.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=template&id=2980693f&scoped=true&\"", "var components\ntry {\n  components = {\n    uniCollapse: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-collapse/components/uni-collapse/uni-collapse\" */ \"@/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue\"\n      )\n    },\n    uniCollapseItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item\" */ \"@/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker\" */ \"@/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue\"\n      )\n    },\n    uniTable: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-table/components/uni-table/uni-table\" */ \"@/uni_modules/uni-table/components/uni-table/uni-table.vue\"\n      )\n    },\n    uniTr: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-table/components/uni-tr/uni-tr\" */ \"@/uni_modules/uni-table/components/uni-tr/uni-tr.vue\"\n      )\n    },\n    uniTh: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-table/components/uni-th/uni-th\" */ \"@/uni_modules/uni-table/components/uni-th/uni-th.vue\"\n      )\n    },\n    uniTd: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-table/components/uni-td/uni-td\" */ \"@/uni_modules/uni-table/components/uni-td/uni-td.vue\"\n      )\n    },\n    uniPagination: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-pagination/components/uni-pagination/uni-pagination\" */ \"@/uni_modules/uni-pagination/components/uni-pagination/uni-pagination.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getStatusText(_vm.searchParams.status) || \"工作流状态\"\n  var m1 = _vm.getUrgencyText(_vm.searchParams.urgency) || \"紧急程度\"\n  var m2 = _vm.getProjectText(_vm.searchParams.project) || \"找茬项目\"\n  var m3 = _vm.getResponsibleText(_vm.searchParams.responsible) || \"责任人\"\n  var g0 = _vm.feedbackList.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.feedbackList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g1 = item.description && item.description.length > 45\n          var g2 = item.images && item.images.length > 0\n          var g3 = g2 ? item.images.length : null\n          var g4 = g2 ? item.images.length : null\n          var g5 = g2 && g4 > 1 ? item.images.length : null\n          var g6 = g2 && g4 > 1 && g5 > 2 ? item.images.length : null\n          var m4 = _vm.formatTime(item.createTime, item)\n          var m5 =\n            item.responsibleInfo && item.responsibleInfo.assignedTime\n              ? _vm.formatTime(item.responsibleInfo.assignedTime, item)\n              : null\n          var m6 = _vm.getReasonDisplay(item)\n          var m7 =\n            m6 !== \"-\" ? _vm.formatReasonText(_vm.getReasonDisplay(item)) : null\n          var g7 = _vm.hasOperationPermission\n            ? _vm.hasEditPermission && item.availableActions.includes(\"edit\")\n            : null\n          var g8 = _vm.hasOperationPermission\n            ? item.availableActions.includes(\"submit_completion\")\n            : null\n          return {\n            $orig: $orig,\n            g1: g1,\n            g2: g2,\n            g3: g3,\n            g4: g4,\n            g5: g5,\n            g6: g6,\n            m4: m4,\n            m5: m5,\n            m6: m6,\n            m7: m7,\n            g7: g7,\n            g8: g8,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"uni-container\">\t\t\t\n\t\t\t<view class=\"db-container\">\n\t\t\t\t<!-- 搜索区域统一处理 -->\n\t\t\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t\t\t<uni-collapse>\n\t\t\t\t\t<uni-collapse-item title=\"搜索筛选\" title-border=\"none\" :border=\"false\">\n\t\t\t\t<!-- #endif -->\n\t\t\t\t\n\t\t\t\t<!-- 状态和紧急程度选择 -->\n\t\t\t\t<view class=\"search-box\">\n\t\t\t\t\t<view class=\"select-row\">\n\t\t\t\t\t\t<view class=\"select-item\">\n\t\t\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t\t\t\t\t\t<view class=\"picker-button\" @click=\"showStatusPicker\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\" :class=\"{ 'placeholder': !searchParams.status }\">\n\t\t\t\t\t\t\t\t\t{{ getStatusText(searchParams.status) || '工作流状态' }}\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t<text class=\"picker-arrow\">▼</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t\t<!-- #ifndef MP-WEIXIN -->\n\t\t\t\t\t\t\t<uni-data-select \n\t\t\t\t\t\t\t\tv-model=\"searchParams.status\"\n\t\t\t\t\t\t\t\t:localdata=\"statusOptions\" \n\t\t\t\t\t\t\t\tplaceholder=\"工作流状态\"\n\t\t\t\t\t\t\t\t:clear=\"!!searchParams.status\" \n\t\t\t\t\t\t\t\t@change=\"onStatusChange\" />\n\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"select-item\">\n\t\t\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t\t\t\t\t\t<view class=\"picker-button\" @click=\"showUrgencyPicker\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\" :class=\"{ 'placeholder': !searchParams.urgency }\">\n\t\t\t\t\t\t\t\t\t{{ getUrgencyText(searchParams.urgency) || '紧急程度' }}\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t<text class=\"picker-arrow\">▼</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t\t<!-- #ifndef MP-WEIXIN -->\n\t\t\t\t\t\t\t<uni-data-select \n\t\t\t\t\t\t\t\tv-model=\"searchParams.urgency\"\n\t\t\t\t\t\t\t\t:localdata=\"urgencyOptions\" \n\t\t\t\t\t\t\t\tplaceholder=\"紧急程度\"\n\t\t\t\t\t\t\t\t:clear=\"!!searchParams.urgency\" \n\t\t\t\t\t\t\t\t@change=\"onUrgencyChange\" />\n\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 搜索区域 -->\n\t\t\t\t<view class=\"search-box\">\n\t\t\t\t\t<!-- 文本搜索 -->\n\t\t\t\t\t<view class=\"search-row\">\n\t\t\t\t\t\t<view class=\"search-item full-width\">\n\t\t\t\t\t\t\t<uni-easyinput v-model=\"searchParams.keyword\" placeholder=\"搜索姓名、描述或项目\" \n\t\t\t\t\t\t\t\t:clearable=\"true\" @input=\"onKeywordSearch\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 下拉选择 -->\n\t\t\t\t\t<view class=\"select-row\">\n\t\t\t\t\t\t<view class=\"select-item\">\n\t\t\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t\t\t\t\t\t<view class=\"picker-button\" @click=\"showProjectPicker\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\" :class=\"{ 'placeholder': !searchParams.project }\">\n\t\t\t\t\t\t\t\t\t{{ getProjectText(searchParams.project) || '找茬项目' }}\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t<text class=\"picker-arrow\">▼</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t\t<!-- #ifndef MP-WEIXIN -->\n\t\t\t\t\t\t\t<uni-data-select \n\t\t\t\t\t\t\t\tv-model=\"searchParams.project\"\n\t\t\t\t\t\t\t\t:localdata=\"projectOptions\" \n\t\t\t\t\t\t\t\tplaceholder=\"找茬项目\"\n\t\t\t\t\t\t\t\t:clear=\"!!searchParams.project\" \n\t\t\t\t\t\t\t\t@change=\"onProjectChange\" />\n\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"select-item\">\n\t\t\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t\t\t\t\t\t<view class=\"picker-button\" @click=\"showResponsiblePicker\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\" :class=\"{ 'placeholder': !searchParams.responsible }\">\n\t\t\t\t\t\t\t\t\t{{ getResponsibleText(searchParams.responsible) || '责任人' }}\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t<text class=\"picker-arrow\">▼</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t\t<!-- #ifndef MP-WEIXIN -->\n\t\t\t\t\t\t\t<uni-data-select \n\t\t\t\t\t\t\t\tv-model=\"searchParams.responsible\" \n\t\t\t\t\t\t\t\t:localdata=\"responsibleOptions\"\n\t\t\t\t\t\t\t\tplaceholder=\"责任人\" \n\t\t\t\t\t\t\t\t:clear=\"!!searchParams.responsible\" \n\t\t\t\t\t\t\t\t@change=\"onResponsibleChange\" />\n\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 日期搜索区域 -->\n\t\t\t\t<view class=\"search-box date-section\">\n\t\t\t\t\t<!-- 日期搜索 -->\n\t\t\t\t\t<view class=\"date-row\">\n\t\t\t\t\t\t<view class=\"date-item full-width\">\n\t\t\t\t\t\t\t<text class=\"search-label\">创建日期：</text>\n\t\t\t\t\t\t\t<uni-datetime-picker type=\"daterange\" v-model=\"createDateRange\"\n\t\t\t\t\t\t\t\t@change=\"onCreateDateChange\" :clear-icon=\"true\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t\t\t\t</uni-collapse-item>\n\t\t\t\t</uni-collapse>\n\t\t\t\t<!-- #endif -->\t\t\t\t\n\t\t\t\t\n\t\t\t\t<!-- 添加搜索和表格区域之间的分隔线 -->\n\t\t\t\t<view class=\"search-table-divider\"></view>\n\t\t\t\t\n\t\t\t\t<!-- 数据显示区域 -->\n\t\t\t\t<view class=\"data-area\">\n\t\t\t\t\t<!-- 有数据时显示表格 -->\n\t\t\t\t\t<view v-if=\"feedbackList.length > 0\">\n\t\t\t\t\t\t<uni-table ref=\"table\" :loading=\"isLoading\" :emptyText=\"'没有更多数据'\" border stripe>\n\t\t\t\t\t\t\t<uni-tr>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\">序号</uni-th>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\">姓名</uni-th>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\">找茬项目</uni-th>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\" width=\"360\">问题描述</uni-th>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\" width=\"240\">图片</uni-th>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\">创建时间</uni-th>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\">负责人</uni-th>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\">状态</uni-th>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\">进度</uni-th>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\">时效</uni-th>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\">理由</uni-th>\n\t\t\t\t\t\t\t\t<uni-th align=\"center\" v-if=\"hasOperationPermission\" width=\"120\">操作</uni-th>\n\t\t\t\t\t\t\t</uni-tr>\n\t\t\t\t\t\t\t<uni-tr v-for=\"(item,index) in feedbackList\" :key=\"item._id\">\n\t\t\t\t\t\t\t\t<uni-td align=\"center\">{{ currentPageStart + index + 1 }}</uni-td>\n\t\t\t\t\t\t\t\t<uni-td align=\"center\">{{item.name}}</uni-td>\n\t\t\t\t\t\t\t\t<uni-td align=\"center\">{{item.project}}</uni-td>\n\t\t\t\t\t\t\t\t<uni-td align=\"center\">\n\t\t\t\t\t\t\t\t\t<view class=\"description-cell\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"description-text\" :class=\"{ 'text-truncate': !item.isExpanded }\">\n\t\t\t\t\t\t\t\t\t\t\t{{item.description}}\n\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.description && item.description.length > 45\"\n\t\t\t\t\t\t\t\t\t\t\tclass=\"expand-button\" @click.stop=\"toggleExpand(index)\">\n\t\t\t\t\t\t\t\t\t\t\t{{ item.isExpanded ? '收起' : '展开' }}\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</uni-td>\n\t\t\t\t\t\t\t\t<uni-td align=\"center\">\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.images && item.images.length > 0\" class=\"image-container\">\n\t\t\t\t\t\t\t\t\t\t<!-- 显示第一张图片 -->\n\t\t\t\t\t\t\t\t\t\t<view class=\"image-wrapper\">\n\t\t\t\t\t\t\t\t\t\t\t<image v-if=\"item.images.length > 0\" :src=\"item.images[0]\" mode=\"aspectFit\"\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"image-hover\" @error=\"handleImageError\" lazy-load\n\t\t\t\t\t\t\t\t\t\t\t\************=\"previewImage(item.images, 0)\" />\n\t\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t\t<!-- 显示第二张图片，如果有更多则显示 +n -->\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.images.length > 1\" class=\"image-wrapper\">\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"item.images[1]\" mode=\"aspectFit\" class=\"image-hover\" lazy-load\n\t\t\t\t\t\t\t\t\t\t\t\t@error=\"handleImageError\" @click.stop=\"previewImage(item.images, 1)\" />\n\t\t\t\t\t\t\t\t\t\t\t<!-- 显示剩余图片数量 -->\n\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.images.length > 2\" class=\"image-overlay\">\n\t\t\t\t\t\t\t\t\t\t\t\t+{{item.images.length - 2}}\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<text v-else class=\"no-image\">无图片</text>\n\t\t\t\t\t\t\t\t</uni-td>\n\t\t\t\t\t\t\t\t<uni-td align=\"center\">\n\t\t\t\t\t\t\t\t\t<text class=\"time-text\">{{formatTime(item.createTime, item)}}</text>\n\t\t\t\t\t\t\t\t</uni-td>\n\t\t\t\t\t\t\t\t<uni-td align=\"center\">\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.responsibleInfo\" class=\"responsible-info\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"responsible-name\">{{item.responsibleInfo.name}}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"assigned-time\" v-if=\"item.responsibleInfo.assignedTime\">\n\t\t\t\t\t\t\t\t\t\t\t{{formatTime(item.responsibleInfo.assignedTime, item)}}\n\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<text v-else class=\"no-responsible\">未指派</text>\n\t\t\t\t\t\t\t\t</uni-td>\n\t\t\t\t\t\t\t\t<uni-td align=\"center\">\n\t\t\t\t\t\t\t\t\t<view class=\"status-cell\">\n\t\t\t\t\t\t\t\t\t<view class=\"status-badge\" :style=\"{ backgroundColor: item.statusInfo.color }\">\n\t\t\t\t\t\t\t\t\t\t{{item.statusInfo.name}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</uni-td>\n\t\t\t\t\t\t\t\t<uni-td align=\"center\">\n\t\t\t\t\t\t\t\t\t<view class=\"progress-cell\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: item.progress + '%' }\"></view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"progress-text\">{{item.progress}}%</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</uni-td>\n\t\t\t\t\t\t\t\t<uni-td align=\"center\">\n\t\t\t\t\t\t\t\t<view class=\"timing-cell\">\n\t\t\t\t\t\t\t\t<view class=\"timing-badge\" :class=\"item.timing.urgency\" :title=\"item.timing.description\">\n\t\t\t\t\t\t\t\t\t{{item.timing.description || (item.timing.daysPassed + '天')}}\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text v-if=\"item.timing.isOverdue\" class=\"overdue-text\">已超期</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</uni-td>\n\t\t\t\t\t\t\t\t<uni-td align=\"center\">\n\t\t\t\t\t\t\t\t\t<view class=\"remarks-cell\">\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"getReasonDisplay(item) !== '-'\" class=\"reason-text\" v-html=\"formatReasonText(getReasonDisplay(item))\"></view>\n\t\t\t\t\t\t\t\t\t\t<text v-else class=\"no-reason\">-</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</uni-td>\n\t\t\t\t\t\t\t\t<uni-td align=\"center\" v-if=\"hasOperationPermission\">\n\t\t\t\t\t\t\t\t\t<view class=\"action-buttons\">\n\t\t\t\t\t\t\t\t\t\t<button class=\"action-btn view-btn\" @click=\"viewDetail(item)\">查看</button>\n\t\t\t\t\t\t\t\t\t\t<button v-if=\"hasEditPermission && item.availableActions.includes('edit')\" \n\t\t\t\t\t\t\t\t\t\t\tclass=\"action-btn edit-btn\" @click=\"editItem(item)\">编辑</button>\n\t\t\t\t\t\t\t\t\t\t<button v-if=\"hasDeletePermission\" \n\t\t\t\t\t\t\t\t\t\t\tclass=\"action-btn delete-btn\" @click=\"deleteItem(item)\">删除</button>\n\t\t\t\t\t\t\t\t\t\t<button v-if=\"item.availableActions.includes('submit_completion')\" \n\t\t\t\t\t\t\t\t\t\t\tclass=\"action-btn complete-btn\" @click=\"submitCompletion(item)\">完成任务</button>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</uni-td>\n\t\t\t\t\t\t\t</uni-tr>\n\t\t\t\t\t\t</uni-table>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 分页组件 -->\n\t\t\t\t\t\t<view class=\"pagination-container\">\n\t\t\t\t\t\t\t<uni-pagination \n\t\t\t\t\t\t\t\t:total=\"totalCount\" \n\t\t\t\t\t\t\t\t:pageSize=\"pageSize\" \n\t\t\t\t\t\t\t\t:current=\"currentPage\"\n\t\t\t\t\t\t\t\t@change=\"onPageChange\"\n\t\t\t\t\t\t\t\tshow-icon=\"true\">\n\t\t\t\t\t\t\t</uni-pagination>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 无数据时的不同状态 -->\n\t\t\t\t\t<view v-else class=\"no-data-area\">\n\t\t\t\t\t\t<!-- 初次加载中 -->\n\t\t\t\t\t\t<view v-if=\"!hasInitialized\" class=\"data-loading\">\n\t\t\t\t\t\t\t<uni-load-more status=\"loading\" :content-text=\"{ contentdown: '正在加载数据...' }\"></uni-load-more>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 搜索加载中 -->\n\t\t\t\t\t\t<view v-else-if=\"isLoading\" class=\"data-loading\">\n\t\t\t\t\t\t\t<uni-load-more status=\"loading\" :content-text=\"{ contentdown: '搜索中...' }\"></uni-load-more>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 数据为空 -->\n\t\t\t\t\t\t<p-empty-state v-else \n\t\t\t\t\t\t\ttype=\"data\" \n\t\t\t\t\t\t\ttext=\"暂无问题反馈数据\"\n\t\t\t\t\t\t\tsize=\"medium\"\n\t\t\t\t\t\t></p-empty-state>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 微信小程序弹窗选择器 -->\n\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t<!-- 状态选择弹窗 -->\n\t\t<uni-popup ref=\"statusPopup\" type=\"bottom\" :safe-area=\"false\">\n\t\t\t<view class=\"popup-content\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">选择工作流状态</text>\n\t\t\t\t\t<text class=\"popup-close\" @click=\"closeStatusPicker\">取消</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-body\">\n\t\t\t\t\t<view class=\"popup-item\" \n\t\t\t\t\t\tv-for=\"item in statusOptions\" \n\t\t\t\t\t\t:key=\"item.value\"\n\t\t\t\t\t\t:class=\"{ 'active': searchParams.status === item.value }\"\n\t\t\t\t\t\t@click=\"selectStatus(item.value)\">\n\t\t\t\t\t\t<text>{{ item.text }}</text>\n\t\t\t\t\t\t<text v-if=\"searchParams.status === item.value\" class=\"check-icon\">✓</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<!-- 紧急程度选择弹窗 -->\n\t\t<uni-popup ref=\"urgencyPopup\" type=\"bottom\" :safe-area=\"false\">\n\t\t\t<view class=\"popup-content\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">选择紧急程度</text>\n\t\t\t\t\t<text class=\"popup-close\" @click=\"closeUrgencyPicker\">取消</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-body\">\n\t\t\t\t\t<view class=\"popup-item\" \n\t\t\t\t\t\tv-for=\"item in urgencyOptions\" \n\t\t\t\t\t\t:key=\"item.value\"\n\t\t\t\t\t\t:class=\"{ 'active': searchParams.urgency === item.value }\"\n\t\t\t\t\t\t@click=\"selectUrgency(item.value)\">\n\t\t\t\t\t\t<text>{{ item.text }}</text>\n\t\t\t\t\t\t<text v-if=\"searchParams.urgency === item.value\" class=\"check-icon\">✓</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<!-- 项目选择弹窗 -->\n\t\t<uni-popup ref=\"projectPopup\" type=\"bottom\" :safe-area=\"false\">\n\t\t\t<view class=\"popup-content\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">选择找茬项目</text>\n\t\t\t\t\t<text class=\"popup-close\" @click=\"closeProjectPicker\">取消</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-body\">\n\t\t\t\t\t<view class=\"popup-item\" \n\t\t\t\t\t\tv-for=\"item in projectOptions\" \n\t\t\t\t\t\t:key=\"item.value\"\n\t\t\t\t\t\t:class=\"{ 'active': searchParams.project === item.value }\"\n\t\t\t\t\t\t@click=\"selectProject(item.value)\">\n\t\t\t\t\t\t<text>{{ item.text }}</text>\n\t\t\t\t\t\t<text v-if=\"searchParams.project === item.value\" class=\"check-icon\">✓</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<!-- 责任人选择弹窗 -->\n\t\t<uni-popup ref=\"responsiblePopup\" type=\"bottom\" :safe-area=\"false\">\n\t\t\t<view class=\"popup-content\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">选择责任人</text>\n\t\t\t\t\t<text class=\"popup-close\" @click=\"closeResponsiblePicker\">取消</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-body\">\n\t\t\t\t\t<view class=\"popup-item\" \n\t\t\t\t\t\tv-for=\"item in responsibleOptions\" \n\t\t\t\t\t\t:key=\"item.value\"\n\t\t\t\t\t\t:class=\"{ 'active': searchParams.responsible === item.value }\"\n\t\t\t\t\t\t@click=\"selectResponsible(item.value)\">\n\t\t\t\t\t\t<text>{{ item.text }}</text>\n\t\t\t\t\t\t<text v-if=\"searchParams.responsible === item.value\" class=\"check-icon\">✓</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t<!-- #endif -->\n\t</view>\n</template>\n\n<style scoped>\n\t/* 搜索区域样式 */\n\t.search-box {\n\t\tbackground: #f9fafb;\n\t\tborder: 1rpx solid #e2e8f0;\n\t\tborder-radius: 8rpx;\n\t\tmargin-bottom: 16rpx;\n\t\tpadding: 16rpx;\n\t}\n\n.full-width {\n\tflex: 1;\n\twidth: 100%;\n}\n\n/* 状态徽章样式 */\n.status-cell {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 4rpx;\n}\n\n.status-badge {\n\tpadding: 6rpx 12rpx;\n\tcolor: white;\n\tborder-radius: 16rpx;\n\tfont-size: 24rpx;\n\ttext-align: center;\n\tmin-width: 120rpx;\n}\n\n.workflow-type {\n\tfont-size: 20rpx;\n\tcolor: #999;\n\tbackground: #f0f0f0;\n\tpadding: 2rpx 8rpx;\n\tborder-radius: 8rpx;\n}\n\n/* 进度条样式 */\n.progress-cell {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 8rpx;\n}\n\n.progress-bar {\n\twidth: 80rpx;\n\theight: 12rpx;\n\tbackground: #e0e0e0;\n\tborder-radius: 6rpx;\n\toverflow: hidden;\n}\n\n.progress-fill {\n\theight: 100%;\n\tbackground: linear-gradient(90deg, #4caf50, #8bc34a);\n\ttransition: width 0.3s ease;\n}\n\n.progress-text {\n\tfont-size: 22rpx;\n\tcolor: #666;\n}\n\n/* 时效显示样式 */\n.timing-cell {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 4rpx;\n}\n\n.timing-badge {\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 16rpx;\n\tfont-size: 24rpx;\n\tcolor: white;\n\tmin-width: 80rpx;\n\ttext-align: center;\n}\n\n.timing-badge.normal {\n\tbackground: #4caf50;\n}\n\n.timing-badge.warning {\n\tbackground: #ff9800;\n}\n\n.timing-badge.urgent {\n\tbackground: #f44336;\n}\n\n.timing-badge.completed {\n\tbackground: #4caf50;\n\tcolor: #fff;\n}\n\n.timing-badge.terminated {\n\tbackground: #9e9e9e;\n\tcolor: #fff;\n}\n\n.overdue-text {\n\tfont-size: 20rpx;\n\tcolor: #f44336;\n\tfont-weight: bold;\n}\n\n/* 负责人信息样式 */\n.responsible-info {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 4rpx;\n}\n\n.responsible-name {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n}\n\n.assigned-time {\n\tfont-size: 20rpx;\n\tcolor: #999;\n}\n\n.no-responsible {\n\tcolor: #ccc;\n\tfont-style: italic;\n}\n\n/* 操作按钮样式 */\n.action-buttons {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 8rpx;\n\tjustify-content: center;\n}\n\n.action-btn {\n\tpadding: 8rpx 16rpx;\n\tborder: none;\n\tborder-radius: 16rpx;\n\tfont-size: 22rpx;\n\tcolor: white;\n\tmin-width: 80rpx;\n}\n\n/* 微信小程序按钮垂直排列 */\n/* #ifdef MP-WEIXIN */\n.action-buttons {\n\tflex-direction: column;\n\tgap: 6rpx;\n\twidth: 100%;\n}\n\n.action-btn {\n\twidth: 100%;\n\tpadding: 8rpx 16rpx;\n\tfont-size: 24rpx;\n\tmin-width: auto;\n\tborder-radius: 12rpx;\n}\n/* #endif */\n\n.view-btn {\n\tbackground: #2196f3;\n}\n\n.edit-btn {\n\tbackground: #4caf50;\n}\n\n.assign-btn {\n\tbackground: #ff9800;\n}\n\n.complete-btn {\n\tbackground: #00bcd4;\n}\n\n.delete-btn {\n\tbackground: #f44336;\n}\n\n.approve-btn {\n\tbackground: #9c27b0;\n}\n\n.convert-btn {\n\tbackground: #607d8b;\n}\n\n.archive-btn {\n\tbackground: #9e9e9e;\n}\n\n/* 图片样式优化 */\n.image-container {\n\tdisplay: flex;\n\tgap: 8rpx;\n\tjustify-content: center;\n\talign-items: center;\n\tmin-height: 120rpx;\n\tmargin: 0;\n\tpadding: 0;\n}\n\n.image-wrapper {\n\tposition: relative;\n\twidth: 160rpx;\n\theight: 160rpx;\n\tcursor: pointer;\n\toverflow: hidden;\n\tborder: 1px solid #e2e8f0;\n\ttransition: all 0.3s ease;\n}\n\n.image-wrapper:hover {\n\ttransform: translateY(-2px);\n\tborder-color: #cbd5e1;\n\tbox-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\n\n.image-wrapper image {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 8rpx;\n\tobject-fit: cover;\n}\n\n/* 微信小程序图片优化 */\n/* #ifdef MP-WEIXIN */\n.image-wrapper {\n\twidth: 140rpx !important;\n\theight: 140rpx !important;\n}\n\n.image-container {\n\tgap: 16rpx;\n\tmin-height: 120rpx;\n}\n/* #endif */\n\n.image-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tright: 0;\n\tbackground: rgba(0, 0, 0, 0.7);\n\tcolor: white;\n\tfont-size: 18rpx;\n\tpadding: 2rpx 6rpx;\n\tborder-radius: 0 8rpx 0 8rpx;\n}\n\n.no-image {\n\tcolor: #ccc;\n\tfont-style: italic;\n\tfont-size: 24rpx;\n}\n\n/* 时间文本样式 */\n.time-text {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n/* 备注区域样式 */\n.remarks-cell {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n\twhite-space: pre-wrap;\n\tword-break: break-word;\n}\n\n/* 分页容器样式 */\n.pagination-container {\n\tmargin: 32rpx 0;\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n/* 数据区域样式 */\n.data-area {\n\tmin-height: 400rpx;\n\tbackground: #fff;\n\tborder-radius: 8rpx;\n}\n\n.no-data-area {\n\tmin-height: 400rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: #fff;\n\tborder-radius: 8rpx;\n}\n\n/* 数据加载区域样式 */\n.data-loading {\n\tpadding: 60rpx 0;\n\ttext-align: center;\n\tbackground: #fff;\n\tborder-radius: 8rpx;\n}\n\n/* 响应式适配 */\n@media screen and (max-width: 750rpx) {\n\t.workflow-summary {\n\t\tflex-direction: column;\n\t\tgap: 8rpx;\n\t}\n\t\n\t.action-buttons {\n\t\tflex-direction: column;\n\t}\n\t\n\t.action-btn {\n\t\twidth: 100%;\n\t}\n}\n</style>\n\n<script>\n\timport { getCacheKey, CACHE_KEYS, cacheManager } from '@/utils/cache.js';\n\timport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n\n\t// 添加防抖函数作为外部单例\n\tconst debounce = function(fn, delay = 300) {\n\t\tlet timer = null;\n\t\treturn function(...args) {\n\t\t\tif (timer) clearTimeout(timer);\n\t\t\ttimer = setTimeout(() => {\n\t\t\t\tfn.apply(this, args);\n\t\t\t}, delay);\n\t\t};\n\t};\n\n\tconst db = uniCloud.database()\n\tconst dbCmd = db.command\n\t// 表查询配置\n\tconst dbOrderBy = 'createTime desc' // 按创建时间降序排列\n\tconst dbSearchFields = [] // 模糊搜索字段，支持模糊搜索的字段列表\n\t// 分页配置\n\tconst pageSize = 10\n\tconst pageCurrent = 1\n\n\tconst orderByMapping = {\n\t\t\"ascending\": \"asc\",\n\t\t\"descending\": \"desc\"\n\t}\n\n\texport default {\n\t\tcomponents: {\n\t\t\tPEmptyState\n\t\t},\n\t\tdata() {\n\t\t\t// 初始化本地分页数据，与udb分开管理\n\t\t\tconst localPagination = {\n\t\t\t\tsize: pageSize,\n\t\t\t\tcurrent: pageCurrent,\n\t\t\t\tcount: 0\n\t\t\t};\n\t\t\t\n\t\t\treturn {\n\t\t\t\tresponsibleOptions: [],\n\t\t\t\tresponsibleMap: {},\n\t\t\t\tcreateDateRange: [],\n\t\t\t\tsearchParams: {\n\t\t\t\t\tkeyword: '',\n\t\t\t\t\tproject: '',\n\t\t\t\t\tresponsible: '',\n\t\t\t\t\tstatus: '',\n\t\t\t\t\turgency: ''\n\t\t\t\t},\n\t\t\t\tprojectOptions: [\n\t\t\t\t\t{ text: '全部', value: '' },\n\t\t\t\t\t{ text: '安全找茬', value: '安全找茬' },\n\t\t\t\t\t{ text: '设备找茬', value: '设备找茬' },\n\t\t\t\t\t{ text: '其他找茬', value: '其他找茬' }\n\t\t\t\t],\n\t\t\t\tisLoading: false,\n\t\t\t\tisTokenValid: true,\n\t\t\t\tuserRoles: [], // 用户角色列表\n\t\n\t\t\t\tstatusOptions: [],\n\t\t\t\turgencyOptions: [],\n\t\t\t\tfeedbackList: [],\n\t\t\t\ttotalCount: 0,\n\t\t\t\tcurrentPage: 1,\n\t\t\t\tpageSize: 20,\n\t\t\t\tcurrentPageStart: 0,\n\t\t\t\tsearchTimer: null,\n\t\t\t\thasInitialized: false, // 添加初始化标记\n\t\t\t\tlastRefreshTime: 0, // 上次刷新时间\n\t\t\t\tisPageVisible: true, // 页面是否可见\n\t\t\t\tneedsRefreshOnShow: false, // 是否需要在页面显示时刷新\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 判断是否有权限查看操作按钮 - 只有特定角色才能看到\n\t\t\thasOperationPermission() {\n\t\t\t\tif (!this.isTokenValid) return false;\n\t\t\t\t\n\t\t\t\t// 有权限的角色：负责人、主管、厂长、副厂长、管理员\n\t\t\t\tconst authorizedRoles = ['responsible', 'supervisor', 'GM', 'PM', 'admin', 'manager'];\n\t\t\t\treturn this.userRoles.some(role => authorizedRoles.includes(role));\n\t\t\t},\n\t\t\t\n\t\t\t// 编辑权限：只有管理员可以编辑\n\t\t\thasEditPermission() {\n\t\t\t\tif (!this.isTokenValid) return false;\n\t\t\t\treturn this.userRoles.some(role => ['admin', 'manager'].includes(role));\n\t\t\t},\n\t\t\t\n\t\t\t// 删除权限：管理员和厂长可以删除\n\t\t\thasDeletePermission() {\n\t\t\t\tif (!this.isTokenValid) return false;\n\t\t\t\treturn this.userRoles.some(role => ['admin', 'manager', 'GM'].includes(role));\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 页面创建时立即设置加载状态，确保页面显示正确\n\t\t\tthis.isLoading = true;\n\t\t\t\n\t\t\t// 监听角标管理器的跨设备更新事件\n\t\t\tuni.$on('cross-device-update-detected', (data) => {\n\t\t\t\tif (data.silent) {\n\t\t\t\t\t// 智能判断是否需要刷新\n\t\t\t\t\tconst shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);\n\t\t\t\t\tif (shouldRefresh) {\n\t\t\t\t\t\tconsole.log('反馈列表页面收到跨设备更新通知，静默刷新数据');\n\t\t\t\t\t\t// 静默刷新数据\n\t\t\t\t\t\tthis.silentRefresh();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\t\t\t\n\n\t\t},\n\t\tasync onLoad() {\n\t\t\t// 确保加载状态正确\n\t\t\tthis.isLoading = true;\n\t\t\tthis.hasInitialized = false;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 检查登录状态，设置token有效性\n\t\t\t\tthis.checkAndSetTokenStatus();\n\t\t\t\t\n\t\t\t\tawait this.initializeWorkflowOptions();\n\t\t\t\tawait this.loadResponsibleMap();\n\t\t\t\tawait this.loadFeedbackList();\n\t\t\t\t// 添加请求拦截器\n\t\t\t\tthis.setupRequestInterceptor();\n\t\t\t\t// 监听token过期事件\n\t\t\t\tthis.setupTokenEventListeners();\n\t\t\t\t// 监听新反馈提交事件\n\t\t\t\tthis.setupFeedbackEventListeners();\n\t\t\t} catch (error) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '页面加载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\t// 即使加载失败也要标记为已初始化，避免一直显示加载状态\n\t\t\t\tthis.hasInitialized = true;\n\t\t\t\tthis.isLoading = false;\n\t\t\t}\n\t\t},\n\t\tonReady() {\n\t\t\t// 移除原有的udb.loadData，使用新的API\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.loadFeedbackList().finally(() => {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\tthis.hasInitialized = true; // 确保下拉刷新后也标记为已初始化\n\t\t\t});\n\t\t},\n\t\tonShow() {\n\t\t\t// 标记页面为可见状态\n\t\t\tthis.isPageVisible = true;\n\t\t\t\n\t\t\t// 每次显示页面时检查token状态\n\t\t\tconst oldTokenStatus = this.isTokenValid;\n\t\t\tthis.checkAndSetTokenStatus();\n\t\t\t\n\t\t\t// 如果token状态发生变化，重新加载数据\n\t\t\tif (oldTokenStatus !== this.isTokenValid) {\n\t\t\t\t// 重新加载负责人数据（根据登录状态决定是否加载）\n\t\t\t\tthis.loadResponsibleMap();\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t\tthis.needsRefreshOnShow = false;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果有待处理的刷新请求，立即执行\n\t\t\tif (this.needsRefreshOnShow) {\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t\tthis.lastRefreshTime = Date.now();\n\t\t\t\tthis.needsRefreshOnShow = false;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 智能刷新策略：只在必要时自动刷新\n\t\t\tconst now = Date.now();\n\t\t\tconst lastRefresh = this.lastRefreshTime || 0;\n\t\t\tconst timeSinceLastRefresh = now - lastRefresh;\n\t\t\t\n\t\t\t// 自动刷新条件（优化后）：\n\t\t\t// 1. 初次显示页面\n\t\t\t// 2. 距离上次刷新超过5分钟（避免频繁刷新）\n\t\t\tif (!this.hasInitialized) {\n\t\t\t\t// 初次显示，需要加载数据\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t\tthis.lastRefreshTime = now;\n\t\t\t} else if (timeSinceLastRefresh > 5 * 60 * 1000) {\n\t\t\t\t// 超过5分钟，静默刷新（不显示加载状态）\n\t\t\t\tthis.silentRefresh();\n\t\t\t}\t\t\n\t\t},\n\t\t\n\t\tonHide() {\n\t\t\t// 标记页面为不可见状态\n\t\t\tthis.isPageVisible = false;\n\t\t},\n\t\tonUnload() {\n\t\t\t// 移除事件监听\n\t\t\tthis.removeTokenEventListeners();\n\t\t\tthis.removeFeedbackEventListeners();\n\t\t\t// 移除跨设备更新事件监听\n\t\t\tuni.$off('cross-device-update-detected');\n\t\t},\n\t\tmethods: {\n\t\t\t// ===== 原有方法 =====\n\t\t\t\n\t\t\t// 统一的错误处理函数\n\t\t\thandleError(message, error) {\n\t\t\t\t// 向用户显示友好提示\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: message,\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 静默刷新 - 在后台更新数据，不影响用户体验\n\t\t\tasync silentRefresh() {\n\t\t\t\ttry {\n\t\t\t\t\t// 使用静默模式刷新数据\n\t\t\t\t\tawait this.loadFeedbackList(true);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 静默刷新失败:', error);\n\t\t\t\t\t// 静默刷新失败不显示错误提示，避免打扰用户\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 加载负责人映射 - 使用缓存系统优化性能\n\t\t\tasync loadResponsibleMap() {\n\t\t\t\t// 如果未登录，跳过负责人数据加载，避免权限错误\n\t\t\t\tif (!this.isTokenValid) {\n\t\t\t\t\t// 设置默认的负责人选项\n\t\t\t\t\tthis.responsibleOptions = [\n\t\t\t\t\t\t{ text: '全部', value: '' }\n\t\t\t\t\t];\n\t\t\t\t\tthis.responsibleMap = {};\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 先尝试从缓存获取负责人映射\n\t\t\t\t\tthis.responsibleMap = cacheManager.getResponsibleMap();\n\t\t\t\t\t\n\t\t\t\t\t// 使用缓存管理器获取负责人列表，如果缓存未命中则从服务器获取\n\t\t\t\t\tconst responsibleList = await cacheManager.getResponsibleList(async () => {\n\t\t\t\t\t// 通过云函数获取负责人数据，避免权限问题\n\t\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\t\tname: 'feedback-list',\n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\taction: 'getResponsibleUsers'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tif (!res || !res.result || res.result.code !== 0) {\n\t\t\t\t\t\t\tthrow new Error(res.result?.message || '获取责任人数据失败');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn res.result.data || [];\n\t\t\t\t\t});\n\n\t\t\t\t\t// 构建负责人选项\n\t\t\t\t\tthis.responsibleOptions = [\n\t\t\t\t\t\t{ text: '全部', value: '' },\n\t\t\t\t\t\t...responsibleList.map(user => ({\n\t\t\t\t\t\t\tvalue: user._id,\n\t\t\t\t\t\t\ttext: user.nickname || user.username || '-'\n\t\t\t\t\t\t}))\n\t\t\t\t\t];\n\n\t\t\t\t\t// 更新负责人映射\n\t\t\t\t\tthis.responsibleMap = responsibleList.reduce((map, user) => {\n\t\t\t\t\t\tmap[user._id] = user.nickname || user.username || '-';\n\t\t\t\t\t\treturn map;\n\t\t\t\t\t}, {});\n\t\t\t\t\t\n\t\n\t\t\t\t} catch (e) {\n\t\t\t\t\t// 只在登录状态下才显示错误提示\n\t\t\t\t\tif (this.isTokenValid) {\n\t\t\t\t\t\tthis.handleError('获取责任人数据失败', e);\n\t\t\t\t\t\tconsole.error('❌ 负责人数据加载失败:', e);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 设置默认选项，避免界面异常\n\t\t\t\t\tthis.responsibleOptions = [\n\t\t\t\t\t\t{ text: '全部', value: '' }\n\t\t\t\t\t];\n\t\t\t\t\tthis.responsibleMap = {};\n\t\t\t\t}\n\t\t\t},\t\t\t\n\n\t\t\tgetResponsibleName(responsibleId) {\n\t\t\t\tif (!responsibleId) return '-';\n\t\t\t\treturn this.responsibleMap[responsibleId] || '-';\n\t\t\t},\n\t\t\tpreviewImage(images, index) {\n\t\t\t\tif (!images || !Array.isArray(images) || images.length === 0) {\n\t\t\t\t\tthis.handleError('无图片可预览');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 微信小程序特殊处理\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\ttry {\n\t\t\t\t\tuni.previewImage({\n\t\t\t\t\t\turls: images,\n\t\t\t\t\t\tcurrent: index || 0,\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t// 预览失败不显示错误提示，因为这可能是微信小程序的限制导致\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\t// 预览图片出错，静默处理\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// 非微信小程序处理\n\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: images,\n\t\t\t\t\tcurrent: index || 0,\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tthis.handleError('预览图片失败', err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\thandleImageError(event) {\n\t\t\t\tevent.target.src = '/static/empty/default-image.png';\n\t\t\t},\n\n\n\t\t\t// 分页变化\n\t\t\tonPageChange(e) {\n\t\t\t\tthis.currentPage = e.current;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t},\n\t\t\t\n\t\t\t// 原搜索方法更新为调用统一方法\n\t\t\t// 项目选择变化\n\t\t\tonProjectChange(value) {\n\t\t\t\tthis.searchParams.project = value;\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t},\n\t\t\t\n\t\t\t// 负责人选择变化\n\t\t\tonResponsibleChange(value) {\n\t\t\t\tthis.searchParams.responsible = value;\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t},\n\t\t\t\n\t\t\t// 创建日期变化\n\t\t\tonCreateDateChange(e) {\n\t\t\t\tthis.createDateRange = e;\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t},\n\n\t\t\t// 验证响应数据\n\t\t\tvalidateResponse(res) {\n\t\t\t\tif (!res) return false;\n\t\t\t\tif (!res.result) return false;\n\t\t\t\tif (!res.result.data) return false;\n\t\t\t\treturn true;\n\t\t\t},\n\n\t\t\t// 检查并设置token状态\n\t\t\tcheckAndSetTokenStatus() {\n\t\t\t\tconst token = uni.getStorageSync('uni_id_token');\n\t\t\t\tif (!token) {\n\t\t\t\t\tthis.isTokenValid = false;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst tokenExpired = uni.getStorageSync('uni_id_token_expired');\n\t\t\t\tif (tokenExpired && tokenExpired < Date.now()) {\n\t\t\t\t\tthis.isTokenValid = false;\n\t\t\t\t\t// 清除过期的token\n\t\t\t\t\tthis.handleTokenInvalid();\n\t\t\t\t} else {\n\t\t\t\t\tthis.isTokenValid = true;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 检查token状态\n\t\t\tasync checkTokenStatus() {\n\t\t\t\ttry {\n\t\t\t\t\tconst token = uni.getStorageSync('uni_id_token');\n\t\t\t\t\tif (!token) {\n\t\t\t\t\t\tthis.handleTokenInvalid();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tconst tokenExpired = uni.getStorageSync('uni_id_token_expired');\n\t\t\t\t\tif (tokenExpired < Date.now()) {\n\t\t\t\t\t\tthis.handleTokenInvalid();\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.handleError('验证登录状态失败', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 处理token失效 - 清除用户相关缓存\n\t\t\thandleTokenInvalid() {\n\t\t\t\tthis.isTokenValid = false;\n\t\t\t\t// 清除登录信息\n\t\t\t\tuni.removeStorageSync('uni_id_token');\n\t\t\t\tuni.removeStorageSync('uni_id_token_expired');\n\t\t\t\tuni.removeStorageSync('uni-id-pages-userInfo');\n\t\t\t\t\n\t\t\t\t// 使用缓存管理器清除用户相关缓存\n\t\t\t\tcacheManager.clearUserRelatedCache();\n\t\t\t\t\n\t\t\t\t// 重新加载数据\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t},\n\t\t\t// 设置请求拦截器\n\t\t\tsetupRequestInterceptor() {\n\t\t\t\t// 添加请求拦截器\n\t\t\t\tconst db = uniCloud.database();\n\t\t\t\tdb.interceptorAdd('callFunction', {\n\t\t\t\t\tinvoke: (options) => {\n\t\t\t\t\t\t// 请求前的处理\n\t\t\t\t\t},\n\t\t\t\t\tsuccess: (result) => {\n\t\t\t\t\t\t// 处理token失效的情况\n\t\t\t\t\t\tif (result.result && result.result.code === 'TOKEN_INVALID') {\n\t\t\t\t\t\t\tthis.handleTokenInvalid();\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn result;\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\treturn err;\n\t\t\t\t\t},\n\t\t\t\t\tcomplete: (res) => {\n\t\t\t\t\t\treturn res;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 设置token事件监听\n\t\t\tsetupTokenEventListeners() {\n\t\t\t\t// 监听全局token过期事件\n\t\t\t\tuni.$on('token-expired', this.handleGlobalTokenExpired);\n\t\t\t\tuni.$on('token-invalid', this.handleGlobalTokenExpired);\n\t\t\t},\n\t\t\t\n\t\t\t// 移除token事件监听\n\t\t\tremoveTokenEventListeners() {\n\t\t\t\tuni.$off('token-expired', this.handleGlobalTokenExpired);\n\t\t\t\tuni.$off('token-invalid', this.handleGlobalTokenExpired);\n\t\t\t},\n\t\t\t\n\t\t\t// 处理全局token过期事件 - 清除用户相关缓存\n\t\t\thandleGlobalTokenExpired() {\n\t\t\t\tthis.isTokenValid = false;\n\t\t\t\t// 清除登录信息\n\t\t\t\tuni.removeStorageSync('uni_id_token');\n\t\t\t\tuni.removeStorageSync('uni_id_token_expired');\n\t\t\t\tuni.removeStorageSync('uni-id-pages-userInfo');\n\t\t\t\t\n\t\t\t\t// 使用缓存管理器清除用户相关缓存\n\t\t\t\tcacheManager.clearUserRelatedCache();\n\t\t\t\t\n\t\t\t\t// 重新加载负责人数据（会因为未登录而跳过）\n\t\t\t\tthis.loadResponsibleMap();\n\t\t\t\t\n\t\t\t\t// 重新加载数据\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t\t\n\t\t\t\t// 强制更新页面，让权限相关的按钮重新渲染\n\t\t\t\tthis.$forceUpdate();\n\t\t\t},\n\t\t\t\n\t\t\t// 设置反馈事件监听\n\t\t\tsetupFeedbackEventListeners() {\n\t\t\t\t// 监听新反馈提交事件\n\t\t\t\tuni.$on('feedback-submitted', this.handleFeedbackSubmitted);\n\t\t\t\t// 监听反馈数据更新事件\n\t\t\t\tuni.$on('feedback-updated', this.handleFeedbackUpdated);\n\t\t\t\t// 监听任务完成事件\n\t\t\t\tuni.$on('task-completed', this.handleTaskCompleted);\n\t\t\t\t// 监听任务状态变化事件\n\t\t\t\tuni.$on('task-status-changed', this.handleTaskStatusChanged);\n\t\t\t},\n\t\t\t\n\t\t\t// 移除反馈事件监听\n\t\t\tremoveFeedbackEventListeners() {\n\t\t\t\tuni.$off('feedback-submitted', this.handleFeedbackSubmitted);\n\t\t\t\tuni.$off('feedback-updated', this.handleFeedbackUpdated);\n\t\t\t\tuni.$off('task-completed', this.handleTaskCompleted);\n\t\t\t\tuni.$off('task-status-changed', this.handleTaskStatusChanged);\n\t\t\t},\n\t\t\t\n\t\t\t\t\t\t// 处理新反馈提交事件\n\t\t\thandleFeedbackSubmitted(data) {\n\t\t\t\t// 重置到第一页并刷新数据 - 无论页面是否可见都要刷新\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t\tthis.lastRefreshTime = Date.now(); // 更新刷新时间\n\n\t\t\t\t// 清除待刷新标记，因为已经刷新了\n\t\t\t\tthis.needsRefreshOnShow = false;\n\n\t\t\t\t// 显示提示（只在页面可见时显示，避免干扰用户）\n\t\t\t\tif (this.isPageVisible) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '列表已更新',\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 1500\n\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 处理反馈数据更新事件\n\t\t\thandleFeedbackUpdated(data) {\n\t\t\t\t// 智能刷新：如果是当前页面可见，立即刷新；否则标记需要刷新\n\t\t\t\tif (this.isPageVisible) {\n\t\t\t\t\tthis.loadFeedbackList();\n\t\t\t\t\tthis.lastRefreshTime = Date.now();\n\t\t\t\t} else {\n\t\t\t\t\t// 页面不可见时，标记需要刷新，等页面显示时再刷新\n\t\t\t\t\tthis.needsRefreshOnShow = true;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 处理任务完成事件\n\t\t\thandleTaskCompleted(data) {\n\t\t\t\t// 刷新当前页数据\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t\tthis.lastRefreshTime = Date.now();\n\n\t\t\t\t// 显示提示\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '任务状态已更新',\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 1500\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 处理任务状态变化事件\n\t\t\thandleTaskStatusChanged(data) {\n\t\t\t\t// 智能刷新策略\n\t\t\t\tif (this.isPageVisible) {\n\t\t\t\t\tthis.silentRefresh();\n\t\t\t\t} else {\n\t\t\t\t\tthis.needsRefreshOnShow = true;\n\t\t\t\t}\n\t\t\t},\n\t\t\t// ===== 新工作流相关方法 =====\n\t\t\t\n\t\t\t// 初始化工作流选项 - 使用缓存系统优化\n\t\t\tasync initializeWorkflowOptions() {\n\t\t\t\t// 从缓存获取状态选项，提升页面加载速度\n\t\t\t\tthis.statusOptions = cacheManager.getStatusOptions();\n\t\t\t\t\n\t\t\t\t// 从缓存获取紧急程度选项\n\t\t\t\tthis.urgencyOptions = cacheManager.getUrgencyOptions();\n\t\t\t\t\n\t\t\t\t// 从缓存获取项目选项\n\t\t\t\tthis.projectOptions = await cacheManager.getProjectOptions();\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 加载问题列表 - 新工作流系统的核心数据获取方法\n\t\t\t * \n\t\t\t * 功能特点：\n\t\t\t * - 支持多维度搜索筛选（项目、状态、关键词、时间范围、负责人）\n\t\t\t * - 自动获取用户角色信息，用于权限控制\n\t\t\t * - 集成分页功能，提升大数据量场景的性能\n\t\t\t * - 统一的错误处理和用户友好提示\n\t\t\t * \n\t\t\t * 数据流：前端搜索参数 → feedback-list云函数 → 数据库查询 → 增强处理 → 前端显示\n\t\t\t */\n\t\t\tasync loadFeedbackList(silent = false) {\n\t\t\t\tif (!silent) {\n\t\t\t\t\tthis.isLoading = true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst params = {\n\t\t\t\t\t\taction: 'getList',\n\t\t\t\t\t\tpageSize: this.pageSize,\n\t\t\t\t\t\tpageNum: this.currentPage,\n\t\t\t\t\t\tproject: this.searchParams.project || '',\n\t\t\t\t\t\tstatus: this.searchParams.status || '',\n\t\t\t\t\t\tkeyword: this.searchParams.keyword || '',\n\t\t\t\t\t\turgency: this.searchParams.urgency || '',\n\t\t\t\t\t\tresponsible: this.searchParams.responsible || ''\n\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t// 添加日期范围\n\t\t\t\t\tif (this.createDateRange && this.createDateRange.length === 2) {\n\t\t\t\t\t\tconst startDateStr = this.createDateRange[0];\n\t\t\t\t\t\tconst endDateStr = this.createDateRange[1];\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst startParts = startDateStr.split('-');\n\t\t\t\t\t\tconst endParts = endDateStr.split('-');\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 构建本地时间的开始和结束时间戳\n\t\t\t\t\t\tconst startDate = new Date(parseInt(startParts[0]), parseInt(startParts[1]) - 1, parseInt(startParts[2]), 0, 0, 0, 0);\n\t\t\t\t\t\tconst endDate = new Date(parseInt(endParts[0]), parseInt(endParts[1]) - 1, parseInt(endParts[2]), 23, 59, 59, 999);\n\t\t\t\t\t\t\n\t\t\t\t\t\tparams.dateRange = {\n\t\t\t\t\t\t\tstart: startDate.getTime(),\n\t\t\t\t\t\t\tend: endDate.getTime()\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-list',\n\t\t\t\t\t\tdata: params\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.result && res.result.code === 0) {\n\t\t\t\t\t\tconst { list, pagination, userInfo } = res.result.data;\n\t\t\t\t\t\tthis.feedbackList = list || [];\n\t\t\t\t\t\tthis.totalCount = pagination ? pagination.total : 0;\n\t\t\t\t\t\tthis.currentPageStart = pagination ? (pagination.pageNum - 1) * pagination.pageSize : 0;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更新用户角色信息\n\t\t\t\t\t\tif (userInfo && userInfo.roles) {\n\t\t\t\t\t\t\tthis.userRoles = userInfo.roles;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.userRoles = [];\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(res.result ? res.result.message : '未知错误');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.feedbackList = [];\n\t\t\t\t\tthis.totalCount = 0;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载数据失败: ' + (error.message || error),\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tif (!silent) {\n\t\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\t}\n\t\t\t\t\tthis.hasInitialized = true; // 标记为已初始化\n\t\t\t\t\tthis.lastRefreshTime = Date.now(); // 更新刷新时间\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 工作流状态变化 - 新工作流系统的状态筛选\n\t\t\tonStatusChange(value) {\n\t\t\t\tthis.searchParams.status = value;\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t},\n\n\t\t\t// 紧急程度变化\n\t\t\tonUrgencyChange(value) {\n\t\t\t\tthis.searchParams.urgency = value;\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t},\n\n\t\t\t// 关键词搜索\n\t\t\tonKeywordSearch() {\n\t\t\t\t// 防抖处理，避免频繁请求\n\t\t\t\tclearTimeout(this.searchTimer);\n\t\t\t\tthis.searchTimer = setTimeout(() => {\n\t\t\t\t\tthis.currentPage = 1;\n\t\t\t\t\tthis.loadFeedbackList();\n\t\t\t\t}, 300);\n\t\t\t},\n\n\t\t\t// 查看详情\n\t\t\tviewDetail(item) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/feedback_pkg/examine?id=${item._id}&readonly=true`\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 编辑项目\n\t\t\teditItem(item) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/feedback_pkg/edit?id=${item._id}`\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 负责人提交工作完成\n\t\t\tasync submitCompletion(item) {\n\t\t\t\t// 直接跳转到我的任务页面进行详细的完成操作\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/ucenter_pkg/complete-task?id=${item._id}`,\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '跳转失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 删除项目\n\t\t\tasync deleteItem(item) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: `确定要删除\"${item.name}\"的问题反馈吗？此操作不可恢复！`,\n\t\t\t\t\tconfirmText: '删除',\n\t\t\t\t\tconfirmColor: '#ff4444',\n\t\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tawait this.performDelete(item);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 执行删除操作\n\t\t\tasync performDelete(item) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '删除中...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 调用云函数删除数据\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-workflow',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'delete',\n\t\t\t\t\t\t\tid: item._id\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (res.result.code === 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 发送全局事件通知其他页面数据已更新\n\t\t\t\t\t\tuni.$emit('feedback-updated', {\n\t\t\t\t\t\t\taction: 'delete',\n\t\t\t\t\t\t\tid: item._id,\n\t\t\t\t\t\t\ttimestamp: Date.now()\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 重新加载数据\n\t\t\t\t\t\tawait this.loadFeedbackList();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(res.result.message || '删除失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '删除失败，请重试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 分页变化\n\t\t\tonPageChange(e) {\n\t\t\t\tthis.currentPage = e.current;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t},\n\n\t\t\t// 格式化时间显示 - 使用后端提供的北京时间\n\t\t\tformatTime(timestamp, item = null) {\n\t\t\t\tif (!timestamp) return '-';\n\t\t\t\t\n\t\t\t\t// 使用后端提供的格式化时间（北京时间）\n\t\t\t\tif (item && item.createTimeFormatted) {\n\t\t\t\t\treturn item.createTimeFormatted;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 后备方案：简单格式化\n\t\t\t\tconst date = new Date(timestamp);\n\t\t\t\treturn date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });\n\t\t\t},\n\n\t\t\t// 切换展开/收起\n\t\t\ttoggleExpand(index) {\n\t\t\t\tthis.$set(this.feedbackList[index], 'isExpanded', !this.feedbackList[index].isExpanded);\n\t\t\t},\n\t\t\t\n\t\t\t// ===== 微信小程序弹窗选择器相关方法 =====\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\t// 显示状态选择器\n\t\t\tshowStatusPicker() {\n\t\t\t\tthis.$refs.statusPopup.open();\n\t\t\t},\n\t\t\t\n\t\t\t// 关闭状态选择器\n\t\t\tcloseStatusPicker() {\n\t\t\t\tthis.$refs.statusPopup.close();\n\t\t\t},\n\t\t\t\n\t\t\t// 选择状态\n\t\t\tselectStatus(value) {\n\t\t\t\tthis.searchParams.status = value;\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t\tthis.closeStatusPicker();\n\t\t\t},\n\t\t\t\n\t\t\t// 显示紧急程度选择器\n\t\t\tshowUrgencyPicker() {\n\t\t\t\tthis.$refs.urgencyPopup.open();\n\t\t\t},\n\t\t\t\n\t\t\t// 关闭紧急程度选择器\n\t\t\tcloseUrgencyPicker() {\n\t\t\t\tthis.$refs.urgencyPopup.close();\n\t\t\t},\n\t\t\t\n\t\t\t// 选择紧急程度\n\t\t\tselectUrgency(value) {\n\t\t\t\tthis.searchParams.urgency = value;\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t\tthis.closeUrgencyPicker();\n\t\t\t},\n\t\t\t\n\t\t\t// 显示项目选择器\n\t\t\tshowProjectPicker() {\n\t\t\t\tthis.$refs.projectPopup.open();\n\t\t\t},\n\t\t\t\n\t\t\t// 关闭项目选择器\n\t\t\tcloseProjectPicker() {\n\t\t\t\tthis.$refs.projectPopup.close();\n\t\t\t},\n\t\t\t\n\t\t\t// 选择项目\n\t\t\tselectProject(value) {\n\t\t\t\tthis.searchParams.project = value;\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t\tthis.closeProjectPicker();\n\t\t\t},\n\t\t\t\n\t\t\t// 显示责任人选择器\n\t\t\tshowResponsiblePicker() {\n\t\t\t\tthis.$refs.responsiblePopup.open();\n\t\t\t},\n\t\t\t\n\t\t\t// 关闭责任人选择器\n\t\t\tcloseResponsiblePicker() {\n\t\t\t\tthis.$refs.responsiblePopup.close();\n\t\t\t},\n\t\t\t\n\t\t\t// 选择责任人\n\t\t\tselectResponsible(value) {\n\t\t\t\tthis.searchParams.responsible = value;\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.loadFeedbackList();\n\t\t\t\tthis.closeResponsiblePicker();\n\t\t\t},\n\t\t\t\n\t\t\t// 获取状态显示文本\n\t\t\tgetStatusText(value) {\n\t\t\t\tif (!value) return '';\n\t\t\t\tconst option = this.statusOptions.find(item => item.value === value);\n\t\t\t\treturn option ? option.text : '';\n\t\t\t},\n\t\t\t\n\t\t\t// 获取紧急程度显示文本\n\t\t\tgetUrgencyText(value) {\n\t\t\t\tif (!value) return '';\n\t\t\t\tconst option = this.urgencyOptions.find(item => item.value === value);\n\t\t\t\treturn option ? option.text : '';\n\t\t\t},\n\t\t\t\n\t\t\t// 获取项目显示文本\n\t\t\tgetProjectText(value) {\n\t\t\t\tif (!value) return '';\n\t\t\t\tconst option = this.projectOptions.find(item => item.value === value);\n\t\t\t\treturn option ? option.text : '';\n\t\t\t},\n\t\t\t\n\t\t\t// 获取责任人显示文本\n\t\t\tgetResponsibleText(value) {\n\t\t\t\tif (!value) return '';\n\t\t\t\tconst option = this.responsibleOptions.find(item => item.value === value);\n\t\t\t\treturn option ? option.text : '';\n\t\t\t},\n\t\t\t// #endif\n\t\t\t\n\t\t\t/**\n\t\t\t * 智能理由显示 - 根据工作流状态显示合适的理由\n\t\t\t * \n\t\t\t * 逻辑：\n\t\t\t * 1. 如果流程被终止(terminated)，只显示导致终止的理由\n\t\t\t * 2. 如果流程正常进行，显示已完成步骤的理由\n\t\t\t * 3. 不显示未到达步骤的理由\n\t\t\t */\n\t\t\tgetReasonDisplay(item) {\n\t\t\t\tconst status = item.workflowStatus;\n\t\t\t\tconst reasons = [];\n\t\t\t\t\n\t\t\t\t// 优先从actionHistory中提取理由（新数据）\n\t\t\t\tif (item.actionHistory && item.actionHistory.length > 0) {\n\t\t\t\t\t// 过滤出审核类型的操作\n\t\t\t\t\tconst auditActions = item.actionHistory.filter(action => \n\t\t\t\t\t\t['supervisor_approve', 'supervisor_reject', 'supervisor_meeting',\n\t\t\t\t\t\t 'pm_approve', 'pm_reject', 'gm_approve', 'gm_reject'].includes(action.action)\n\t\t\t\t\t);\n\t\t\t\t\t\n\t\t\t\t\t// 按时间排序并提取理由\n\t\t\t\t\tauditActions.sort((a, b) => a.timestamp - b.timestamp).forEach(action => {\n\t\t\t\t\t\tconst roleMap = {\n\t\t\t\t\t\t\t'supervisor_approve': '主管',\n\t\t\t\t\t\t\t'supervisor_reject': '主管',\n\t\t\t\t\t\t\t'supervisor_meeting': '主管',\n\t\t\t\t\t\t\t'pm_approve': '副厂长',\n\t\t\t\t\t\t\t'pm_reject': '副厂长',\n\t\t\t\t\t\t\t'gm_approve': '厂长',\n\t\t\t\t\t\t\t'gm_reject': '厂长'\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst roleName = roleMap[action.action];\n\t\t\t\t\t\tif (roleName && action.reason) {\n\t\t\t\t\t\t\treasons.push(`${roleName}：${action.reason}`);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn reasons.length > 0 ? reasons.join('\\n') : '-';\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 格式化理由文本 - 为每个角色的理由添加简洁的样式\n\t\t\t */\n\t\t\tformatReasonText(reasonText) {\n\t\t\t\tif (!reasonText || reasonText === '-') {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst lines = reasonText.split('\\n');\n\t\t\t\tconst formattedLines = lines.map(line => {\n\t\t\t\t\treturn `<div class=\"reason-item\">${line}</div>`;\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\treturn formattedLines.join('');\n\t\t\t},\n\t\t\t\n\t\t\t// 智能判断是否需要刷新数据\n\t\t\tshouldRefreshOnCrossDeviceUpdate(data) {\n\t\t\t\t// 如果页面不可见，标记需要刷新但不立即刷新\n\t\t\t\tif (!this.isPageVisible) {\n\t\t\t\t\tthis.needsRefreshOnShow = true;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果距离上次刷新时间太短（小于3秒），仅对非重要更新进行节流\n\t\t\t\tconst timeSinceLastRefresh = Date.now() - (this.lastRefreshTime || 0);\n\t\t\t\tif (timeSinceLastRefresh < 3000) {\n\t\t\t\t\t// 检查是否为重要更新类型\n\t\t\t\t\tif (data.updateTypes) {\n\t\t\t\t\t\tconst importantTypes = ['feedback_submitted', 'feedback_deleted', 'workflow_status_changed'];\n\t\t\t\t\t\tconst hasImportantUpdate = data.updateTypes.some(type => importantTypes.includes(type));\n\t\t\t\t\t\tif (!hasImportantUpdate) {\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果更新类型包含反馈相关的操作，需要刷新\n\t\t\t\tif (data.updateTypes && data.updateTypes.length > 0) {\n\t\t\t\t\tconst relevantTypes = ['workflow_status_changed', 'feedback_submitted', 'feedback_deleted'];\n\t\t\t\t\tconst hasRelevantUpdate = data.updateTypes.some(type => relevantTypes.includes(type));\n\t\t\t\t\tif (hasRelevantUpdate) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果有多个更新记录，可能需要刷新\n\t\t\t\tif (data.updateCount > 2) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果没有明确的更新类型信息，采用保守策略：刷新\n\t\t\t\tif (!data.updateTypes || data.updateTypes.length === 0) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn false;\n\t\t\t},\n\t\t},\n\t\tbeforeDestroy() {\n\t\t\t// 移除事件监听\n\t\t\tuni.$off('cross-device-update-detected');\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/* 基础布局样式 */\n\t.uni-container {\n\t\tpadding: 24px;\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #f8fafb 0%, #e8f4f8 100%);\n\t}\n\n\t.db-container {\n\t\twidth: 100%;\n\t\tmax-width: 92%;\n\t\tmargin: 0 auto;\n\t\tpadding: 24px;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16px;\n\t\tbox-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\n\t\toverflow: visible; /* 允许弹窗溢出容器 */\n\t\tbox-sizing: border-box;\n\t}\n\n\t/* 按钮样式增强 */\n\t.uni-group {\n\t\tdisplay: flex;\n\t\tgap: 12px;\n\t}\n\n\t.uni-button {\n\t\tpadding: 8px 18px;\n\t\tfont-size: 14px;\n\t\tfont-weight: 600;\n\t\tletter-spacing: 0.3px;\n\t\tborder-radius: 8px;\n\t\tbox-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n\t\ttransition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\ttext-transform: none;\n\t\tborder: none;\n\t}\n\n\t.uni-button::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground-color: transparent;\n\t\ttransform: translateY(100%);\n\t\ttransition: transform 0.3s ease;\n\t\tz-index: 1;\n\t}\n\n\t.uni-button[type=\"default\"] {\n\t\tbackground: #f9fafc;\n\t\tcolor: #475569;\n\t\tborder: 1px solid #e2e8f0;\n\t}\n\n\t.uni-button[type=\"primary\"] {\n\t\tbackground: linear-gradient(145deg, #3975d9, #2862c6);\n\t\tcolor: #fff;\n\t}\n\n\t.uni-button[type=\"warn\"] {\n\t\tbackground: linear-gradient(135deg, #f43f5e, #ef4444);\n\t\tcolor: #fff;\n\t}\n\n\t.uni-button:active {\n\t\ttransform: translateY(1px);\n\t\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n\t}\n\n\t.uni-button:hover {\n\t\ttransform: translateY(-3px);\n\t\tbox-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);\n\t}\n\n\t.uni-button[type=\"primary\"]:hover {\n\t\tbackground: linear-gradient(145deg, #4986ea, #3974d7);\n\t}\n\n\t.uni-button[type=\"warn\"]:hover {\n\t\tbackground: linear-gradient(135deg, #fb7185, #f87171);\n\t}\n\n\t/* 表格样式优化 */\n\t.uni-table {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 12px;\n\t\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n\t\toverflow: hidden;\n\t\tborder: 1px solid #f1f5f9;\n\t\twidth: 100% !important;\n\t\tmargin: 0 auto;\n\t}\n\n\t.uni-th {\n\t\tbackground: linear-gradient(to bottom, #f8fafc, #f1f5f9);\n\t\tfont-weight: 600;\n\t\tpadding: 16px 12px;\n\t\ttext-align: center;\n\t\tcolor: #334155;\n\t\tborder-bottom: 2px solid #e2e8f0;\n\t\tposition: relative;\n\t}\n\n\t.uni-th::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 2px;\n\t\tbackground: linear-gradient(to right, transparent, #3b82f6, transparent);\n\t\ttransform: scaleX(0);\n\t\ttransition: transform 0.3s ease;\n\t}\n\n\t.uni-th:hover::after {\n\t\ttransform: scaleX(0.8);\n\t}\n\n\t.uni-td {\n\t\tpadding: 14px 12px;\n\t\ttext-align: center;\n\t\tborder-bottom: 1px solid #e2e8f0;\n\t\ttransition: background-color 0.2s ease;\n\t}\n\n\t/* 重复的image-container定义已合并到上面 */\n\n\t/* 重复的image-wrapper定义已合并到上面 */\n\n\t.image-count {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\tbackdrop-filter: blur(2px);\n\t\tcolor: #fff;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tborder-radius: 8px;\n\t}\n\n\t.image-count:hover {\n\t\tbackground: rgba(0, 0, 0, 0.65);\n\t}\n\n\t.image-hover {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tborder-radius: 8px;\n\t\tobject-fit: cover;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t/* 分页控件样式 - 使用默认样式 */\n\t.uni-pagination-box {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tpadding: 16px 0;\n\t\tmargin-top: 24px;\n\t}\n\t\n\t/* 日期选择器弹窗层级和位置修复 */\n\t::v-deep .uni-datetime-picker__mask {\n\t\tposition: fixed !important;\n\t\ttop: 0 !important;\n\t\tleft: 0 !important;\n\t\tright: 0 !important;\n\t\tbottom: 0 !important;\n\t\tz-index: 9998 !important;\n\t}\n\t\n\t::v-deep .uni-datetime-picker__popup {\n\t\tposition: fixed !important;\n\t\ttop: 50% !important;\n\t\tleft: 50% !important;\n\t\ttransform: translate(-50%, -50%) !important;\n\t\tz-index: 9999 !important;\n\t\tmax-height: 80vh !important;\n\t\toverflow: auto !important;\n\t}\n\t\n\t/* 修改微信小程序分页样式 */\n\t/* #ifdef MP-WEIXIN */\n\t/* 保留微信小程序的基础样式，但不自定义太多 */\n\t/* #endif */\n\n\t.uni-dateformat {\n\t\tcolor: #64748b;\n\t\tfont-size: 14px;\n\t\tfont-weight: 500;\n\t}\n\n\t/* 表格行和单元格样式 */\n\t.uni-table-th-row {\n\t\tfont-size: 16px;\n\t\tcolor: #334155;\n\t\tfont-weight: 600;\n\t}\n\n\t.uni-table-td-row {\n\t\tcolor: #475569;\n\t\tfont-size: 15px;\n\t}\n\n\t.uni-table-td {\n\t\theight: 100% !important;\n\t\tvertical-align: middle !important;\n\t}\n\t\n\t.uni-table-tr:hover .uni-table-td {\n\t\tbackground-color: rgba(59, 130, 246, 0.04);\n\t}\n\n\t/* 描述单元格优化 */\n\t.description-cell {\n\t\tmax-width: 300px;\n\t\tmargin: 0 auto;\n\t\ttext-align: left;\n\t\tposition: relative;\n\t\tuser-select: text;\n\t}\n\n\t.description-text {\n\t\ttext-align: center;\n\t\tdisplay: block;\n\t\tfont-size: 14px;\n\t\tline-height: 1.6;\n\t\tcolor: #334155;\n\t\tword-break: break-all;\n\t}\n\n\t.description-text.text-truncate {\n\t\tdisplay: -webkit-box;\n\t\t-webkit-box-orient: vertical;\n\t\t-webkit-line-clamp: 2;\n\t\toverflow: hidden;\n\t}\n\n\t.expand-button {\n\t\tfont-size: 13px;\n\t\tcursor: pointer;\n\t\tpadding: 4px 8px;\n\t\ttext-align: center;\n\t\tcolor: #4A9FD1;\n\t\tfont-weight: 500;\n\t\tmargin-top: 4px;\n\t\tborder-radius: 4px;\n\t\tbackground-color: rgba(74, 159, 209, 0.08);\n\t\ttransition: all 0.2s ease;\n\t\tdisplay: inline-block;\n\t\tfloat: right;\n\t}\n\n\t.expand-button:hover {\n\t\tbackground-color: rgba(74, 159, 209, 0.15);\n\t\tcolor: #3d8bc2;\n\t}\n\n\t/* 搜索区域美化 - 修复间距问题 */\n\t.search-box {\n\t\tbackground: linear-gradient(135deg, #ffffff, #f9fafb);\n\t\tpadding: 24px;\n\t\tmargin-bottom: 24px; /* 统一上下间距 */\n\t\tborder-radius: 16px;\n\t\tbox-shadow: 0 8px 20px rgba(0, 0, 0, 0.04);\n\t\tborder: 1px solid rgba(226, 232, 240, 0.8);\n\t}\n\n\t/* 日期搜索区域特殊样式 */\n\t.date-section {\n\t\tmargin-top: 24px;\n\t}\n\n\t.date-row {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 20px;\n\t}\n\n\t.date-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex: 1;\n\t\tmin-width: 260px;\n\t}\n\n\t.search-box .search-row {\n\t\tdisplay: flex;\n\t\tmargin-bottom: 18px;\n\t}\n\n\t.search-box .search-item {\n\t\tflex: 1;\n\t\tmargin-right: 16px;\n\t}\n\n\t.search-box .search-item:last-child {\n\t\tmargin-right: 0;\n\t}\n\n\t.search-box .search-label {\n\t\tmin-width: 64px;\n\t\tfont-size: 14px;\n\t\tcolor: #475569;\n\t\tmargin-right: 10px;\n\t\tfont-weight: 600;\n\t\tline-height: 36px;\n\t}\n\n\t.search-box .select-row {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(2, 1fr);\n\t\tgap: 16px;\n\t}\n\n\t/* 小程序端特殊处理 */\n\t/* #ifdef MP-WEIXIN */\n\t.search-box .search-row {\n\t\tflex-direction: column;\n\t}\n\n\t.search-box .search-item {\n\t\tmargin-right: 0;\n\t\tmargin-bottom: 16px;\n\t}\n\n\t.search-box .search-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.search-box .select-row {\n\t\tgrid-template-columns: 1fr;\n\t}\n\t\n\t.date-row {\n\t\tflex-direction: column;\n\t}\n\t\n\t.date-item {\n\t\tmargin-bottom: 16px;\n\t}\n\t\n\t.date-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\t\n\t/* 微信小程序样式增强 */\n\t.uni-table-tr {\n\t\tbackground-color: #ffffff;\n\t}\n\t\n\t.uni-table-tr:nth-child(even) {\n\t\tbackground-color: #f8fafc;\n\t}\n\t\t\n\t.uni-table-tr:hover {\n\t\tbackground-color: rgba(59, 130, 246, 0.05);\n\t}\n\t\n\t.uni-table-th {\n\t\tbackground: linear-gradient(to bottom, #f8fafc, #f1f5f9);\n\t\tfont-weight: 600 !important;\n\t\tcolor: #334155;\n\t\tpadding: 16px 12px !important;\n\t\tborder-bottom: 2px solid #e2e8f0 !important;\n\t}\n\t\n\t.uni-table-td {\n\t\tpadding: 14px 12px !important;\n\t\theight: auto !important;\n\t\tvertical-align: middle !important;\n\t}\n\t\n\t/* 图片列专门优化 - 减少内边距 */\n\t.uni-table-td:nth-child(3) {\n\t\tpadding: 8px 6px !important;\n\t}\n\t\n\t/* 重复的image-container定义已合并到上面 */\n\t\n\t.uni-easyinput__content {\n\t\tbackground-color: #ffffff !important;\n\t\tborder: 1px solid #cbd5e1 !important;\n\t\tborder-radius: 8px !important;\n\t\ttransition: all 0.3s !important;\n\t\tpadding: 0 14px !important;\n\t\theight: 40px !important;\n\t\tbox-sizing: border-box !important;\n\t}\n\t\n\t.uni-easyinput__content-input {\n\t\tfont-size: 15px !important;\n\t\tpadding-left: 0 !important;\n\t\tmargin-left: 0 !important;\n\t\ttext-indent: 0 !important;\n\t}\n\t\n\t/* 调整输入框占位符文字 */\n\t.uni-easyinput__placeholder-class {\n\t\tfont-size: 15px !important;\n\t\tpadding-left: 0 !important;\n\t}\n\t\n\t.uni-datetime-picker--button {\n\t\tborder-radius: 8px !important;\n\t\tborder: 1px solid #cbd5e1 !important;\n\t\ttransition: all 0.3s !important;\n\t\tbackground-color: #ffffff !important;\n\t\theight: 40px !important;\n\t\tbox-sizing: border-box !important;\n\t\tpadding: 0 14px !important;\n\t\tfont-size: 15px !important;\n\t}\n\t\n\t.uni-datetime-picker--button text {\n\t\tfont-size: 15px !important;\n\t}\n\t\n\t/* 统一下拉选择器字体大小 */\n\t.uni-data-select {\n\t\tfont-size: 15px !important;\n\t}\n\t\n\t.uni-data-select .uni-select__input-text {\n\t\tfont-size: 15px !important;\n\t\tpadding-left: 0 !important;\n\t\ttext-indent: 0 !important;\n\t}\n\t\n\t/* 调整选择器内部元素 */\n\t.uni-data-select .uni-select__selector {\n\t\tpadding: 0 14px !important;\n\t\theight: 40px !important;\n\t\tbox-sizing: border-box !important;\n\t}\n\t\n\t.uni-load-more {\n\t\tmargin: 20px auto !important;\n\t}\n\t/* #endif */\n\n\t/* 理由显示区域样式 */\n\t.remarks-cell {\n\t\ttext-align: center;\n\t\tpadding: 12px 8px;\n\t\tline-height: 1.5;\n\t}\n\t\n\t.reason-text {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 8px;\n\t\tmax-width: 100%;\n\t}\n\t\n\t.reason-item {\n\t\tfont-size: 13px;\n\t\tline-height: 1.4;\n\t\tpadding: 4px 0;\n\t\tword-wrap: break-word;\n\t\ttext-align: center;\n\t\tcolor: #666;\n\t}\n\t\n\t.no-reason {\n\t\tcolor: #9ca3af;\n\t\tfont-size: 13px;\n\t\tfont-style: italic;\n\t}\n\n\t/* 工作流状态文本增强 - 新工作流系统样式 */\n\t.approval-status-text {\n\t\tdisplay: inline-block;\n\t\tpadding: 8px 14px;\n\t\tborder-radius: 50px;\n\t\tfont-weight: 500;\n\t\tfont-size: 14px;\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\tz-index: 1;\n\t}\n\t\n\t.approval-status-text::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\ttransform: translateY(100%);\n\t\ttransition: transform 0.3s ease;\n\t\tz-index: -1;\n\t}\n\t\n\t.approval-status-text:hover {\n\t\ttransform: translateY(-3px);\n\t\tbox-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);\n\t}\n\t\n\t.approval-status-text:hover::before {\n\t\ttransform: translateY(0);\n\t}\n\n\t/* 加载遮罩优化（已弃用，避免全屏蒙层） */\n\t/* .loading-mask {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\n\t\tbackdrop-filter: blur(6px);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: 999;\n\t} */\n\n\t/* 视觉分隔线 - 增强搜索区域与表格的视觉分隔 */\n\t.search-table-divider {\n\t\theight: 10px;\n\t}\n\n\t/* 表格居中样式 */\n\t.db-container {\n\t\twidth: 100%;\n\t\tmax-width: 92%;\n\t\tmargin: 0 auto;\n\t\tpadding: 24px;\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t/* 微信小程序特定样式 */\n\t/* #ifdef MP-WEIXIN */\n\t.db-container {\n\t\twidth: 100%;\n\t\tmax-width: 100%;\n\t\tpadding: 16px;\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t.uni-table {\n\t\twidth: 100% !important;\n\t\tmargin: 0 auto;\n\t}\n\t\n\t/* 针对小屏幕设备优化表格居中 */\n\t@media screen and (max-width: 768px) {\n\t\t.db-container {\n\t\t\tpadding-left: 12px;\n\t\t\tpadding-right: 12px;\n\t\t}\n\t\t\n\t\t.uni-container {\n\t\t\tpadding: 16px;\n\t\t}\n\t}\n\t\n\t/* 微信小程序弹窗选择器样式 */\n\t.picker-button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 10px 14px;\n\t\tbackground-color: #ffffff;\n\t\tborder: 1px solid #cbd5e1;\n\t\tborder-radius: 8px;\n\t\theight: 40px;\n\t\tbox-sizing: border-box;\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.picker-button:active {\n\t\tbackground-color: #f5f5f5;\n\t\tborder-color: #4A9FD1;\n\t}\n\t\n\t.picker-text {\n\t\tflex: 1;\n\t\tfont-size: 15px;\n\t\tcolor: #333333;\n\t\ttext-align: left;\n\t\tline-height: 1.2;\n\t\tfont-weight: 400;\n\t}\n\t\n\t.picker-text.placeholder {\n\t\tcolor: #999999;\n\t}\n\t\n\t.picker-arrow {\n\t\tfont-size: 12px;\n\t\tcolor: #999999;\n\t\tmargin-left: 8px;\n\t\ttransition: transform 0.3s ease;\n\t}\n\t\n\t.picker-button:active .picker-arrow {\n\t\ttransform: rotate(180deg);\n\t}\n\t\n\t/* 弹窗内容样式 */\n\t.popup-content {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16px 16px 0 0;\n\t\tmax-height: 60vh;\n\t\toverflow: hidden;\n\t}\n\t\n\t.popup-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 16px 20px;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t\tbackground-color: #fafafa;\n\t}\n\t\n\t.popup-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: 600;\n\t\tcolor: #333333;\n\t}\n\t\n\t.popup-close {\n\t\tfont-size: 14px;\n\t\tcolor: #4A9FD1;\n\t\tpadding: 4px 8px;\n\t}\n\t\n\t.popup-body {\n\t\tmax-height: 50vh;\n\t\toverflow-y: auto;\n\t}\n\t\n\t.popup-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 16px 20px;\n\t\tborder-bottom: 1px solid #f5f5f5;\n\t\ttransition: background-color 0.2s ease;\n\t}\n\t\n\t.popup-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\t\n\t.popup-item:active {\n\t\tbackground-color: #f8f9fa;\n\t}\n\t\n\t.popup-item.active {\n\t\tbackground-color: #e6f3ff;\n\t\tcolor: #4A9FD1;\n\t}\n\t\n\t.popup-item text {\n\t\tfont-size: 15px;\n\t\tcolor: #333333;\n\t}\n\t\n\t.popup-item.active text {\n\t\tcolor: #4A9FD1;\n\t\tfont-weight: 500;\n\t}\n\t\n\t.check-icon {\n\t\tfont-size: 16px;\n\t\tcolor: #4A9FD1;\n\t\tfont-weight: bold;\n\t}\n\t/* #endif */\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&id=2980693f&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&id=2980693f&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752596022153\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=1&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752596022253\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
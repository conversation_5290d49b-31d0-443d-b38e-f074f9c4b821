(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/honor_pkg/common/vendor"],{

/***/ 258:
/*!*************************************!*\
  !*** D:/Xwzc/utils/upload-utils.js ***!
  \*************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uniCloud, uni, global) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ 23));
var _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ 24));
var _imageUtils = _interopRequireDefault(__webpack_require__(/*! ./image-utils.js */ 259));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var UploadUtils = /*#__PURE__*/function () {
  function UploadUtils() {
    var _this$retryStrategies;
    (0, _classCallCheck2.default)(this, UploadUtils);
    this.maxRetries = 3; // 最大重试次数
    this.retryDelay = 1000; // 重试延迟（毫秒）
    this.chunkSize = 1024 * 1024; // 分片大小（1MB）

    // 新增错误类型定义
    this.errorTypes = {
      NETWORK: 'NETWORK_ERROR',
      TIMEOUT: 'TIMEOUT_ERROR',
      SIZE: 'SIZE_ERROR',
      FORMAT: 'FORMAT_ERROR',
      COMPRESS: 'COMPRESS_ERROR',
      UPLOAD: 'UPLOAD_ERROR'
    };

    // 新增重试策略
    this.retryStrategies = (_this$retryStrategies = {}, (0, _defineProperty2.default)(_this$retryStrategies, this.errorTypes.NETWORK, true), (0, _defineProperty2.default)(_this$retryStrategies, this.errorTypes.TIMEOUT, true), (0, _defineProperty2.default)(_this$retryStrategies, this.errorTypes.UPLOAD, true), (0, _defineProperty2.default)(_this$retryStrategies, this.errorTypes.COMPRESS, false), (0, _defineProperty2.default)(_this$retryStrategies, this.errorTypes.FORMAT, false), (0, _defineProperty2.default)(_this$retryStrategies, this.errorTypes.SIZE, false), _this$retryStrategies);
  }

  /**
   * 智能重试处理
   * @param {Function} operation - 要执行的操作
   * @param {object} options - 重试选项
   * @returns {Promise} 执行结果
   */
  (0, _createClass2.default)(UploadUtils, [{
    key: "withRetry",
    value: function () {
      var _withRetry = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee(operation) {
        var options,
          _options$retries,
          retries,
          _options$errorType,
          errorType,
          _options$context,
          context,
          shouldRetry,
          delay,
          _args = arguments;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                options = _args.length > 1 && _args[1] !== undefined ? _args[1] : {};
                _options$retries = options.retries, retries = _options$retries === void 0 ? 0 : _options$retries, _options$errorType = options.errorType, errorType = _options$errorType === void 0 ? this.errorTypes.UPLOAD : _options$errorType, _options$context = options.context, context = _options$context === void 0 ? {} : _options$context;
                _context.prev = 2;
                _context.next = 5;
                return operation();
              case 5:
                return _context.abrupt("return", _context.sent);
              case 8:
                _context.prev = 8;
                _context.t0 = _context["catch"](2);
                shouldRetry = this.retryStrategies[errorType] && retries < this.maxRetries;
                if (!shouldRetry) {
                  _context.next = 17;
                  break;
                }
                delay = this.retryDelay * Math.pow(2, retries); // 指数退避
                _context.next = 15;
                return this.delay(delay);
              case 15:
                console.log("\u91CD\u8BD5\u7B2C".concat(retries + 1, "\u6B21:"), {
                  errorType: errorType,
                  delay: delay,
                  context: context
                });
                return _context.abrupt("return", this.withRetry(operation, _objectSpread(_objectSpread({}, options), {}, {
                  retries: retries + 1
                })));
              case 17:
                throw _context.t0;
              case 18:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, this, [[2, 8]]);
      }));
      function withRetry(_x) {
        return _withRetry.apply(this, arguments);
      }
      return withRetry;
    }()
    /**
     * 上传头像
     * @param {string} filePath - 文件路径
     * @param {object} options - 上传选项
     * @returns {Promise<object>} 上传结果
     */
  }, {
    key: "uploadAvatar",
    value: function () {
      var _uploadAvatar = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2(filePath) {
        var options,
          _options$compress,
          compress,
          onProgress,
          customPath,
          originalSize,
          _compressInfo,
          _compressInfo2,
          _compressInfo3,
          _compressInfo4,
          uploadPath,
          compressInfo,
          cloudPath,
          uploadResult,
          fileInfo,
          _args2 = arguments;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                options = _args2.length > 1 && _args2[1] !== undefined ? _args2[1] : {};
                _options$compress = options.compress, compress = _options$compress === void 0 ? true : _options$compress, onProgress = options.onProgress, customPath = options.customPath, originalSize = options.originalSize;
                _context2.prev = 2;
                uploadPath = filePath;
                compressInfo = null; // 压缩图片
                // 微信小程序环境下跳过压缩，因为chooseImage已经压缩过了
                compressInfo = {
                  path: filePath,
                  compressed: false,
                  size: originalSize,
                  originalSize: originalSize,
                  compressionRatio: 1
                };

                // 生成云存储路径
                cloudPath = customPath || this.generateAvatarPath(); // 上传到云存储
                _context2.next = 9;
                return this.uploadToCloud(uploadPath, cloudPath, {
                  onProgress: onProgress,
                  fileType: 'avatar'
                });
              case 9:
                uploadResult = _context2.sent;
                _context2.next = 12;
                return this.getFileInfo(uploadResult.fileID);
              case 12:
                fileInfo = _context2.sent;
                return _context2.abrupt("return", {
                  success: true,
                  cloudPath: uploadResult.fileID,
                  url: fileInfo.tempFileURL || uploadResult.fileID,
                  // 优先使用临时URL
                  size: uploadResult.actualSize || ((_compressInfo = compressInfo) === null || _compressInfo === void 0 ? void 0 : _compressInfo.size) || 0,
                  // 优先使用实际上传大小
                  compressed: ((_compressInfo2 = compressInfo) === null || _compressInfo2 === void 0 ? void 0 : _compressInfo2.compressed) || false,
                  compressionRatio: ((_compressInfo3 = compressInfo) === null || _compressInfo3 === void 0 ? void 0 : _compressInfo3.compressionRatio) || 0,
                  originalSize: originalSize || ((_compressInfo4 = compressInfo) === null || _compressInfo4 === void 0 ? void 0 : _compressInfo4.originalSize) || 0
                });
              case 16:
                _context2.prev = 16;
                _context2.t0 = _context2["catch"](2);
                console.error('头像上传失败:', _context2.t0);
                throw new Error("\u5934\u50CF\u4E0A\u4F20\u5931\u8D25: ".concat(_context2.t0.message));
              case 20:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, this, [[2, 16]]);
      }));
      function uploadAvatar(_x2) {
        return _uploadAvatar.apply(this, arguments);
      }
      return uploadAvatar;
    }()
    /**
     * 上传表彰图片
     * @param {Array|string} filePaths - 文件路径（数组或单个路径）
     * @param {object} options - 上传选项
     * @returns {Promise<object>} 上传结果
     */
  }, {
    key: "uploadHonorImages",
    value: function () {
      var _uploadHonorImages = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(filePaths) {
        var _this = this;
        var options,
          _options$compress2,
          compress,
          _onProgress,
          onItemProgress,
          _options$maxConcurren,
          maxConcurrent,
          paths,
          _uploadResults$summar,
          _uploadResults$summar2,
          compressResults,
          validPaths,
          uploadResults,
          failedCompress,
          allResults,
          _args3 = arguments;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                options = _args3.length > 1 && _args3[1] !== undefined ? _args3[1] : {};
                _options$compress2 = options.compress, compress = _options$compress2 === void 0 ? true : _options$compress2, _onProgress = options.onProgress, onItemProgress = options.onItemProgress, _options$maxConcurren = options.maxConcurrent, maxConcurrent = _options$maxConcurren === void 0 ? 3 : _options$maxConcurren;
                paths = Array.isArray(filePaths) ? filePaths : [filePaths];
                console.log('🚀 开始上传表彰图片:', {
                  pathCount: paths.length,
                  compress: compress,
                  maxConcurrent: maxConcurrent
                });
                if (!(paths.length === 0)) {
                  _context3.next = 6;
                  break;
                }
                return _context3.abrupt("return", {
                  success: true,
                  results: []
                });
              case 6:
                _context3.prev = 6;
                // 批量压缩
                compressResults = [];
                if (!compress) {
                  _context3.next = 16;
                  break;
                }
                console.log('🔧 开始压缩图片...');
                _context3.next = 12;
                return _imageUtils.default.batchCompressImages(paths, 'honor', function (progress) {
                  if (_onProgress) {
                    _onProgress(_objectSpread({
                      phase: 'compress',
                      progress: Math.round(progress.current / progress.total * 100)
                    }, progress));
                  }
                });
              case 12:
                compressResults = _context3.sent;
                console.log('✅ 压缩完成:', {
                  total: compressResults.length,
                  success: compressResults.filter(function (r) {
                    return r.success;
                  }).length
                });
                _context3.next = 18;
                break;
              case 16:
                compressResults = paths.map(function (path) {
                  return {
                    success: true,
                    result: {
                      path: path,
                      compressed: false,
                      size: 0,
                      width: 0,
                      height: 0,
                      originalSize: 0,
                      compressionRatio: 0
                    }
                  };
                });
                console.log('⏩ 跳过压缩，直接上传');
              case 18:
                // 批量上传
                validPaths = compressResults.filter(function (r) {
                  return r.success && r.result && r.result.path;
                }).map(function (r) {
                  return r.result.path;
                });
                console.log('🔍 有效路径检查:', {
                  total: compressResults.length,
                  valid: validPaths.length
                });
                if (!(validPaths.length === 0)) {
                  _context3.next = 22;
                  break;
                }
                throw new Error('没有可上传的图片');
              case 22:
                console.log('📤 开始批量上传...');
                _context3.next = 25;
                return this.batchUpload(validPaths, {
                  onProgress: function onProgress(progress) {
                    if (_onProgress) {
                      _onProgress(_objectSpread({
                        phase: 'upload',
                        progress: Math.round(progress.current / progress.total * 100)
                      }, progress));
                    }
                  },
                  onItemProgress: onItemProgress,
                  maxConcurrent: maxConcurrent,
                  pathGenerator: function pathGenerator() {
                    return _this.generateHonorImagePath();
                  }
                });
              case 25:
                uploadResults = _context3.sent;
                console.log('✅ 上传完成:', {
                  uploaded: ((_uploadResults$summar = uploadResults.summary) === null || _uploadResults$summar === void 0 ? void 0 : _uploadResults$summar.successful) || 0,
                  failed: ((_uploadResults$summar2 = uploadResults.summary) === null || _uploadResults$summar2 === void 0 ? void 0 : _uploadResults$summar2.failed) || 0
                });

                // 处理压缩失败的文件
                failedCompress = compressResults.filter(function (r) {
                  return !r.success;
                });
                allResults = [].concat((0, _toConsumableArray2.default)(uploadResults.results), (0, _toConsumableArray2.default)(failedCompress.map(function (f) {
                  return {
                    success: false,
                    error: f.error || '图片压缩失败',
                    filePath: f.path
                  };
                })));
                return _context3.abrupt("return", {
                  success: true,
                  results: allResults,
                  totalUploaded: uploadResults.results.filter(function (r) {
                    return r.success;
                  }).length,
                  totalFailed: allResults.filter(function (r) {
                    return !r.success;
                  }).length
                });
              case 32:
                _context3.prev = 32;
                _context3.t0 = _context3["catch"](6);
                console.error('表彰图片上传失败:', _context3.t0);
                throw new Error("\u8868\u5F70\u56FE\u7247\u4E0A\u4F20\u5931\u8D25: ".concat(_context3.t0.message));
              case 36:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, this, [[6, 32]]);
      }));
      function uploadHonorImages(_x3) {
        return _uploadHonorImages.apply(this, arguments);
      }
      return uploadHonorImages;
    }()
    /**
     * 上传到云存储
     * @param {string} filePath - 本地文件路径
     * @param {string} cloudPath - 云存储路径
     * @param {object} options - 上传选项
     * @returns {Promise<object>} 上传结果
     */
  }, {
    key: "uploadToCloud",
    value: function () {
      var _uploadToCloud = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4(filePath, cloudPath) {
        var _this2 = this;
        var options,
          onProgress,
          _options$fileType,
          fileType,
          _options$useCache,
          useCache,
          _options$cacheTime,
          cacheTime,
          cacheKey,
          cached,
          uploadTask,
          uploadPromise,
          timeoutPromise,
          _args4 = arguments;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                options = _args4.length > 2 && _args4[2] !== undefined ? _args4[2] : {};
                onProgress = options.onProgress, _options$fileType = options.fileType, fileType = _options$fileType === void 0 ? 'image' : _options$fileType, _options$useCache = options.useCache, useCache = _options$useCache === void 0 ? true : _options$useCache, _options$cacheTime = options.cacheTime, cacheTime = _options$cacheTime === void 0 ? 3600 * 1000 : _options$cacheTime; // 生成缓存key
                cacheKey = "upload_".concat(filePath, "_").concat(cloudPath); // 检查缓存
                if (!useCache) {
                  _context4.next = 10;
                  break;
                }
                _context4.next = 6;
                return this.getCache(cacheKey);
              case 6:
                cached = _context4.sent;
                if (!cached) {
                  _context4.next = 10;
                  break;
                }
                console.log('使用上传缓存:', cached);
                return _context4.abrupt("return", cached);
              case 10:
                _context4.prev = 10;
                uploadPromise = new Promise(function (resolve, reject) {
                  uploadTask = uniCloud.uploadFile({
                    filePath: filePath,
                    cloudPath: cloudPath,
                    cloudPathAsRealPath: true,
                    onUploadProgress: function onUploadProgress(progressEvent) {
                      if (onProgress) {
                        var progress = Math.round(progressEvent.loaded / progressEvent.total * 100);
                        onProgress({
                          loaded: progressEvent.loaded,
                          total: progressEvent.total,
                          progress: progress,
                          speed: _this2.calculateSpeed(progressEvent)
                        });
                      }
                    }
                  });
                  uploadTask.then(function (result) {
                    if (result.success) {
                      var finalResult = _objectSpread(_objectSpread({}, result), {}, {
                        actualSize: result.fileSize || result.size || 0
                      });

                      // 设置缓存
                      if (useCache) {
                        _this2.setCache(cacheKey, finalResult, cacheTime);
                      }
                      resolve(finalResult);
                    } else {
                      reject(new Error(result.errMsg || '上传失败'));
                    }
                  }).catch(reject);
                }); // 添加超时控制
                timeoutPromise = new Promise(function (_, reject) {
                  setTimeout(function () {
                    reject(new Error('上传超时'));
                  }, options.timeout || 30000);
                });
                _context4.next = 15;
                return this.withRetry(function () {
                  return Promise.race([uploadPromise, timeoutPromise]);
                }, {
                  errorType: this.errorTypes.UPLOAD,
                  context: {
                    filePath: filePath,
                    cloudPath: cloudPath
                  }
                });
              case 15:
                return _context4.abrupt("return", _context4.sent);
              case 18:
                _context4.prev = 18;
                _context4.t0 = _context4["catch"](10);
                console.error('上传失败:', _context4.t0);
                throw _context4.t0;
              case 22:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, this, [[10, 18]]);
      }));
      function uploadToCloud(_x4, _x5) {
        return _uploadToCloud.apply(this, arguments);
      }
      return uploadToCloud;
    }()
    /**
     * 计算上传速度
     * @param {object} progressEvent - 进度事件
     * @returns {string} 格式化的速度
     */
  }, {
    key: "calculateSpeed",
    value: function calculateSpeed(progressEvent) {
      var now = Date.now();
      if (!this.lastProgress) {
        this.lastProgress = {
          time: now,
          loaded: 0
        };
        return '0 KB/s';
      }
      var timeDiff = now - this.lastProgress.time;
      var loadedDiff = progressEvent.loaded - this.lastProgress.loaded;
      if (timeDiff > 0) {
        var speedBps = loadedDiff * 1000 / timeDiff;
        this.lastProgress = {
          time: now,
          loaded: progressEvent.loaded
        };
        if (speedBps < 1024) {
          return "".concat(speedBps.toFixed(1), " B/s");
        } else if (speedBps < 1024 * 1024) {
          return "".concat((speedBps / 1024).toFixed(1), " KB/s");
        } else {
          return "".concat((speedBps / (1024 * 1024)).toFixed(1), " MB/s");
        }
      }
      return '计算中...';
    }

    /**
     * 获取缓存
     * @param {string} key - 缓存键
     * @returns {Promise<object>} 缓存数据
     */
  }, {
    key: "getCache",
    value: function () {
      var _getCache = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5(key) {
        var cached, _JSON$parse, data, expire;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                cached = uni.getStorageSync("upload_cache_".concat(key));
                if (!cached) {
                  _context5.next = 6;
                  break;
                }
                _JSON$parse = JSON.parse(cached), data = _JSON$parse.data, expire = _JSON$parse.expire;
                if (!(expire > Date.now())) {
                  _context5.next = 6;
                  break;
                }
                return _context5.abrupt("return", data);
              case 6:
                return _context5.abrupt("return", null);
              case 9:
                _context5.prev = 9;
                _context5.t0 = _context5["catch"](0);
                console.warn('读取上传缓存失败:', _context5.t0);
                return _context5.abrupt("return", null);
              case 13:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 9]]);
      }));
      function getCache(_x6) {
        return _getCache.apply(this, arguments);
      }
      return getCache;
    }()
    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {object} data - 缓存数据
     * @param {number} duration - 缓存时长（毫秒）
     */
  }, {
    key: "setCache",
    value: function setCache(key, data, duration) {
      try {
        var cache = {
          data: data,
          expire: Date.now() + duration
        };
        uni.setStorageSync("upload_cache_".concat(key), JSON.stringify(cache));
      } catch (error) {
        console.warn('设置上传缓存失败:', error);
      }
    }

    /**
     * H5端上传Base64数据URL到云存储
     * @param {string} dataURL - Base64数据URL
     * @param {string} cloudPath - 云存储路径
     * @param {object} options - 上传选项
     * @returns {Promise<object>} 上传结果
     */

    /**
     * 批量上传
     * @param {Array} filePaths - 文件路径数组
     * @param {object} options - 上传选项
     * @returns {Promise<Array>} 上传结果数组
     */
  }, {
    key: "batchUpload",
    value: function () {
      var _batchUpload = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7(filePaths) {
        var _this3 = this;
        var options,
          onProgress,
          onItemProgress,
          _options$maxConcurren2,
          maxConcurrent,
          pathGenerator,
          _options$chunkSize,
          chunkSize,
          _options$autoRetry,
          autoRetry,
          chunks,
          i,
          results,
          total,
          completed,
          activeUploads,
          memoryUsage,
          checkMemory,
          uploadFile,
          _i,
          _chunks,
          chunk,
          uploadPromises,
          _i2,
          batch,
          batchPromises,
          batchResults,
          failedUploads,
          retryResults,
          _args7 = arguments;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                options = _args7.length > 1 && _args7[1] !== undefined ? _args7[1] : {};
                onProgress = options.onProgress, onItemProgress = options.onItemProgress, _options$maxConcurren2 = options.maxConcurrent, maxConcurrent = _options$maxConcurren2 === void 0 ? 3 : _options$maxConcurren2, pathGenerator = options.pathGenerator, _options$chunkSize = options.chunkSize, chunkSize = _options$chunkSize === void 0 ? this.chunkSize : _options$chunkSize, _options$autoRetry = options.autoRetry, autoRetry = _options$autoRetry === void 0 ? true : _options$autoRetry; // 对大文件列表进行分块处理
                chunks = [];
                for (i = 0; i < filePaths.length; i += chunkSize) {
                  chunks.push(filePaths.slice(i, i + chunkSize));
                }
                results = [];
                total = filePaths.length;
                completed = 0;
                activeUploads = 0;
                memoryUsage = 0; // 监控内存使用
                checkMemory = function checkMemory() {
                  return false;
                }; // 处理单个文件上传
                uploadFile = /*#__PURE__*/function () {
                  var _ref = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6(filePath, index) {
                    var cloudPath, result, fileInfo;
                    return _regenerator.default.wrap(function _callee6$(_context6) {
                      while (1) {
                        switch (_context6.prev = _context6.next) {
                          case 0:
                            _context6.prev = 0;
                            activeUploads++;
                            cloudPath = pathGenerator ? pathGenerator() : _this3.generateImagePath();
                            _context6.next = 5;
                            return _this3.withRetry(function () {
                              return _this3.uploadToCloud(filePath, cloudPath, {
                                onProgress: function onProgress(progress) {
                                  if (onItemProgress) {
                                    onItemProgress(_objectSpread({
                                      index: index,
                                      fileName: filePath
                                    }, progress));
                                  }
                                }
                              });
                            }, {
                              errorType: _this3.errorTypes.UPLOAD,
                              context: {
                                filePath: filePath,
                                index: index
                              }
                            });
                          case 5:
                            result = _context6.sent;
                            _context6.next = 8;
                            return _this3.getFileInfo(result.fileID);
                          case 8:
                            fileInfo = _context6.sent;
                            completed++;
                            if (onProgress) {
                              onProgress({
                                current: completed,
                                total: total,
                                progress: Math.round(completed / total * 100),
                                activeUploads: activeUploads,
                                memoryUsage: memoryUsage
                              });
                            }
                            return _context6.abrupt("return", {
                              success: true,
                              index: index,
                              filePath: filePath,
                              cloudPath: result.fileID,
                              url: fileInfo.tempFileURL || result.fileID,
                              size: result.actualSize
                            });
                          case 14:
                            _context6.prev = 14;
                            _context6.t0 = _context6["catch"](0);
                            completed++;
                            console.error("\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25 [".concat(index, "]:"), _context6.t0);
                            if (onProgress) {
                              onProgress({
                                current: completed,
                                total: total,
                                progress: Math.round(completed / total * 100),
                                activeUploads: activeUploads,
                                memoryUsage: memoryUsage
                              });
                            }
                            return _context6.abrupt("return", {
                              success: false,
                              index: index,
                              filePath: filePath,
                              error: _context6.t0.message
                            });
                          case 20:
                            _context6.prev = 20;
                            activeUploads--;
                            return _context6.finish(20);
                          case 23:
                          case "end":
                            return _context6.stop();
                        }
                      }
                    }, _callee6, null, [[0, 14, 20, 23]]);
                  }));
                  return function uploadFile(_x8, _x9) {
                    return _ref.apply(this, arguments);
                  };
                }(); // 处理每个分块
                _i = 0, _chunks = chunks;
              case 12:
                if (!(_i < _chunks.length)) {
                  _context7.next = 35;
                  break;
                }
                chunk = _chunks[_i];
              case 14:
                if (!checkMemory()) {
                  _context7.next = 19;
                  break;
                }
                _context7.next = 17;
                return this.delay(1000);
              case 17:
                _context7.next = 14;
                break;
              case 19:
                // 并发上传当前分块中的文件
                uploadPromises = [];
                _i2 = 0;
              case 21:
                if (!(_i2 < chunk.length)) {
                  _context7.next = 32;
                  break;
                }
                batch = chunk.slice(_i2, _i2 + maxConcurrent);
                batchPromises = batch.map(function (filePath, idx) {
                  return uploadFile(filePath, results.length + idx);
                });
                _context7.next = 26;
                return Promise.all(batchPromises);
              case 26:
                batchResults = _context7.sent;
                results.push.apply(results, (0, _toConsumableArray2.default)(batchResults));

                // 强制垃圾回收和内存清理
                if (typeof global !== 'undefined' && global.gc) {
                  global.gc();
                }
              case 29:
                _i2 += maxConcurrent;
                _context7.next = 21;
                break;
              case 32:
                _i++;
                _context7.next = 12;
                break;
              case 35:
                if (!autoRetry) {
                  _context7.next = 43;
                  break;
                }
                failedUploads = results.filter(function (r) {
                  return !r.success;
                });
                if (!(failedUploads.length > 0)) {
                  _context7.next = 43;
                  break;
                }
                console.log("\u91CD\u8BD5".concat(failedUploads.length, "\u4E2A\u5931\u8D25\u7684\u4E0A\u4F20"));
                _context7.next = 41;
                return this.batchUpload(failedUploads.map(function (f) {
                  return f.filePath;
                }), _objectSpread(_objectSpread({}, options), {}, {
                  autoRetry: false // 防止无限重试
                }));
              case 41:
                retryResults = _context7.sent;
                // 更新结果
                retryResults.results.forEach(function (retry) {
                  var index = results.findIndex(function (r) {
                    return r.filePath === retry.filePath;
                  });
                  if (index !== -1) {
                    results[index] = retry;
                  }
                });
              case 43:
                return _context7.abrupt("return", {
                  success: true,
                  results: results,
                  summary: {
                    total: total,
                    successful: results.filter(function (r) {
                      return r.success;
                    }).length,
                    failed: results.filter(function (r) {
                      return !r.success;
                    }).length,
                    totalSize: results.reduce(function (sum, r) {
                      return sum + (r.size || 0);
                    }, 0)
                  }
                });
              case 44:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, this);
      }));
      function batchUpload(_x7) {
        return _batchUpload.apply(this, arguments);
      }
      return batchUpload;
    }()
    /**
     * 生成头像存储路径
     * @returns {string} 云存储路径
     */
  }, {
    key: "generateAvatarPath",
    value: function generateAvatarPath() {
      var timestamp = Date.now();
      var random = Math.random().toString(36).substring(2, 8);
      return "avatars/".concat(timestamp, "_").concat(random, ".jpg");
    }

    /**
     * 生成表彰图片存储路径
     * @returns {string} 云存储路径
     */
  }, {
    key: "generateHonorImagePath",
    value: function generateHonorImagePath() {
      var timestamp = Date.now();
      var random = Math.random().toString(36).substring(2, 8);
      return "honor-images/".concat(timestamp, "_").concat(random, ".jpg");
    }

    /**
     * 生成通用图片存储路径
     * @returns {string} 云存储路径
     */
  }, {
    key: "generateImagePath",
    value: function generateImagePath() {
      var timestamp = Date.now();
      var random = Math.random().toString(36).substring(2, 8);
      return "images/".concat(timestamp, "_").concat(random, ".jpg");
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} Promise对象
     */
  }, {
    key: "delay",
    value: function delay(ms) {
      return new Promise(function (resolve) {
        return setTimeout(resolve, ms);
      });
    }

    /**
     * 删除云存储文件
     * @param {string|Array} fileIDs - 文件ID或ID数组
     * @returns {Promise<object>} 删除结果
     */
  }, {
    key: "deleteCloudFiles",
    value: function () {
      var _deleteCloudFiles = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8(fileIDs) {
        var ids, result;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                ids = Array.isArray(fileIDs) ? fileIDs : [fileIDs];
                _context8.prev = 1;
                _context8.next = 4;
                return uniCloud.deleteFile({
                  fileList: ids
                });
              case 4:
                result = _context8.sent;
                return _context8.abrupt("return", {
                  success: true,
                  deletedCount: result.fileList.filter(function (f) {
                    return f.status === 0;
                  }).length,
                  failedCount: result.fileList.filter(function (f) {
                    return f.status !== 0;
                  }).length,
                  details: result.fileList
                });
              case 8:
                _context8.prev = 8;
                _context8.t0 = _context8["catch"](1);
                console.error('删除云存储文件失败:', _context8.t0);
                throw new Error("\u5220\u9664\u6587\u4EF6\u5931\u8D25: ".concat(_context8.t0.message));
              case 12:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[1, 8]]);
      }));
      function deleteCloudFiles(_x10) {
        return _deleteCloudFiles.apply(this, arguments);
      }
      return deleteCloudFiles;
    }()
    /**
     * 获取文件信息
     * @param {string} fileID - 文件ID
     * @returns {Promise<object>} 文件信息
     */
  }, {
    key: "getFileInfo",
    value: function () {
      var _getFileInfo = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9(fileID) {
        var result;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                _context9.prev = 0;
                _context9.next = 3;
                return uniCloud.getTempFileURL({
                  fileList: [fileID]
                });
              case 3:
                result = _context9.sent;
                if (!(result.fileList && result.fileList.length > 0)) {
                  _context9.next = 8;
                  break;
                }
                return _context9.abrupt("return", result.fileList[0]);
              case 8:
                throw new Error('文件不存在');
              case 9:
                _context9.next = 15;
                break;
              case 11:
                _context9.prev = 11;
                _context9.t0 = _context9["catch"](0);
                console.error('获取文件信息失败:', _context9.t0);
                // 如果获取临时URL失败，返回文件ID作为备用
                return _context9.abrupt("return", {
                  fileID: fileID,
                  tempFileURL: fileID,
                  status: 0
                });
              case 15:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[0, 11]]);
      }));
      function getFileInfo(_x11) {
        return _getFileInfo.apply(this, arguments);
      }
      return getFileInfo;
    }()
  }]);
  return UploadUtils;
}(); // 创建单例实例
var uploadUtils = new UploadUtils();
var _default = uploadUtils;
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! (webpack)/buildin/global.js */ 3)))

/***/ }),

/***/ 259:
/*!************************************!*\
  !*** D:/Xwzc/utils/image-utils.js ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ 23));
var _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ 24));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
/**
 * 图片处理工具类
 * 支持H5端和小程序端图片压缩、格式转换、尺寸调整等功能
 * 解决图片文件过大的问题
 */
var ImageUtils = /*#__PURE__*/function () {
  function ImageUtils() {
    (0, _classCallCheck2.default)(this, ImageUtils);
    // 默认配置
    this.defaultConfig = {
      // 头像配置
      avatar: {
        maxWidth: 800,
        maxHeight: 800,
        quality: 0.6,
        maxSize: 500 * 1024,
        format: 'jpg'
      },
      // 表彰图片配置
      honor: {
        maxWidth: 1600,
        maxHeight: 1600,
        quality: 0.8,
        maxSize: 1 * 1024 * 1024,
        format: 'jpg'
      }
    };
  }

  /**
   * 统一的错误处理方法
   * @param {Error} error - 错误对象
   * @param {string} operation - 操作名称
   * @param {object} fallback - 失败时的返回值
   * @returns {object} 标准化的错误结果
   */
  (0, _createClass2.default)(ImageUtils, [{
    key: "handleError",
    value: function handleError(error, operation) {
      var fallback = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var errorMessage = (error === null || error === void 0 ? void 0 : error.message) || "".concat(operation, "\u5931\u8D25");
      var errorCode = (error === null || error === void 0 ? void 0 : error.code) || 'UNKNOWN_ERROR';
      var result = _objectSpread({
        success: false,
        error: {
          message: errorMessage,
          code: errorCode,
          details: error,
          timestamp: new Date().toISOString()
        }
      }, fallback);

      // 只记录关键错误信息，添加时间戳便于调试
      console.error("[ImageUtils] ".concat(operation, "\u5931\u8D25:"), {
        message: errorMessage,
        code: errorCode,
        timestamp: result.error.timestamp
      });
      return result;
    }

    /**
     * 压缩图片
     * @param {string} filePath - 图片路径
     * @param {string} type - 图片类型：'avatar' | 'honor'
     * @param {object} customConfig - 自定义配置
     * @returns {Promise<object>} 压缩后的图片信息
     */
  }, {
    key: "compressImage",
    value: function () {
      var _compressImage = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee(filePath) {
        var type,
          customConfig,
          config,
          originalSize,
          result,
          _args = arguments;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                type = _args.length > 1 && _args[1] !== undefined ? _args[1] : 'avatar';
                customConfig = _args.length > 2 && _args[2] !== undefined ? _args[2] : {};
                config = _objectSpread(_objectSpread(_objectSpread({}, this.defaultConfig[type]), customConfig), {}, {
                  type: type // 添加type标记用于区分处理方式
                });
                originalSize = customConfig.originalSize;
                _context.prev = 4;
                _context.next = 7;
                return this.compressImageMP(filePath, config, originalSize);
              case 7:
                result = _context.sent;
                return _context.abrupt("return", _objectSpread({
                  success: true
                }, result));
              case 11:
                _context.prev = 11;
                _context.t0 = _context["catch"](4);
                return _context.abrupt("return", this.handleError(_context.t0, '图片压缩', {
                  path: filePath,
                  size: originalSize || 0,
                  width: 0,
                  height: 0,
                  compressed: false,
                  originalSize: originalSize || 0,
                  compressionRatio: 0
                }));
              case 14:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, this, [[4, 11]]);
      }));
      function compressImage(_x) {
        return _compressImage.apply(this, arguments);
      }
      return compressImage;
    }()
    /**
     * H5端压缩图片
     * @param {string} filePath - 图片路径
     * @param {object} config - 压缩配置
     * @param {number} originalSize - 原始文件大小
     * @returns {Promise<object>} 压缩后的图片信息
     */
    /**
     * 小程序端压缩图片
     * @param {string} filePath - 图片路径
     * @param {object} config - 压缩配置
     * @param {number} originalSize - 原始文件大小
     * @returns {Promise<object>} 压缩结果
     */
  }, {
    key: "compressImageMP",
    value: function () {
      var _compressImageMP = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2(filePath, config) {
        var originalSize,
          fileInfo,
          imageInfo,
          targetSize,
          cropParams,
          size,
          processedPath,
          cropResult,
          firstCompress,
          firstInfo,
          secondCompress,
          secondInfo,
          _args2 = arguments;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                originalSize = _args2.length > 2 && _args2[2] !== undefined ? _args2[2] : 0;
                _context2.prev = 1;
                if (originalSize) {
                  _context2.next = 13;
                  break;
                }
                _context2.prev = 3;
                _context2.next = 6;
                return this.getFileInfoAsync(filePath);
              case 6:
                fileInfo = _context2.sent;
                originalSize = fileInfo.size;
                _context2.next = 13;
                break;
              case 10:
                _context2.prev = 10;
                _context2.t0 = _context2["catch"](3);
                throw new Error('获取文件信息失败: ' + _context2.t0.message);
              case 13:
                _context2.next = 15;
                return this.getImageInfoAsync(filePath);
              case 15:
                imageInfo = _context2.sent;
                cropParams = null;
                if (config.type === 'avatar') {
                  // 头像使用1:1裁剪
                  size = Math.min(config.maxWidth, config.maxHeight);
                  targetSize = {
                    width: size,
                    height: size
                  };
                  cropParams = this.calculateCropSize(imageInfo.width, imageInfo.height, size, size);
                } else {
                  // 其他图片保持比例缩放
                  targetSize = this.calculateSize(imageInfo.width, imageInfo.height, config.maxWidth || 800, config.maxHeight || 800);
                }

                // 如果需要裁剪，先裁剪再压缩
                processedPath = filePath;
                if (!cropParams) {
                  _context2.next = 24;
                  break;
                }
                _context2.next = 22;
                return new Promise(function (resolve, reject) {
                  var width = cropParams.sourceWidth - cropParams.offsetX * 2;
                  var height = cropParams.sourceHeight - cropParams.offsetY * 2;
                  uni.cropImage({
                    src: filePath,
                    cropOffset: [cropParams.offsetX, cropParams.offsetY],
                    cropScale: "".concat(width, ":").concat(height),
                    success: function success(res) {
                      return resolve(res.tempFilePath);
                    },
                    fail: function fail(err) {
                      return reject(new Error('裁剪失败: ' + err.errMsg));
                    }
                  });
                });
              case 22:
                cropResult = _context2.sent;
                processedPath = cropResult;
              case 24:
                _context2.next = 26;
                return this.wxCompressImage(processedPath, config.quality || 0.6);
              case 26:
                firstCompress = _context2.sent;
                _context2.next = 29;
                return this.getFileInfoAsync(firstCompress.tempFilePath);
              case 29:
                firstInfo = _context2.sent;
                if (!(firstInfo.size > config.maxSize)) {
                  _context2.next = 38;
                  break;
                }
                _context2.next = 33;
                return this.wxCompressImage(firstCompress.tempFilePath, (config.quality || 0.6) * 0.7 // 降低30%质量
                );
              case 33:
                secondCompress = _context2.sent;
                _context2.next = 36;
                return this.getFileInfoAsync(secondCompress.tempFilePath);
              case 36:
                secondInfo = _context2.sent;
                return _context2.abrupt("return", {
                  path: secondCompress.tempFilePath,
                  size: secondInfo.size,
                  width: targetSize.width,
                  height: targetSize.height,
                  compressed: true,
                  originalSize: originalSize,
                  compressionRatio: (1 - secondInfo.size / originalSize) * 100
                });
              case 38:
                return _context2.abrupt("return", {
                  path: firstCompress.tempFilePath,
                  size: firstInfo.size,
                  width: targetSize.width,
                  height: targetSize.height,
                  compressed: true,
                  originalSize: originalSize,
                  compressionRatio: (1 - firstInfo.size / originalSize) * 100
                });
              case 41:
                _context2.prev = 41;
                _context2.t1 = _context2["catch"](1);
                throw new Error('小程序压缩失败: ' + _context2.t1.message);
              case 44:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, this, [[1, 41], [3, 10]]);
      }));
      function compressImageMP(_x2, _x3) {
        return _compressImageMP.apply(this, arguments);
      }
      return compressImageMP;
    }()
    /**
     * 调用微信压缩图片API
     * @param {string} filePath - 图片路径
     * @param {number} quality - 压缩质量
     * @returns {Promise} 压缩结果
     */
  }, {
    key: "wxCompressImage",
    value: function wxCompressImage(filePath, quality) {
      return new Promise(function (resolve, reject) {
        uni.compressImage({
          src: filePath,
          quality: Math.floor(quality * 100),
          success: resolve,
          fail: function fail(error) {
            return reject(new Error('压缩失败: ' + error.errMsg));
          }
        });
      });
    }

    /**
     * 获取图片信息
     * @param {string} filePath - 图片路径
     * @returns {Promise} 图片信息
     */
  }, {
    key: "getImageInfoAsync",
    value: function getImageInfoAsync(filePath) {
      return new Promise(function (resolve, reject) {
        uni.getImageInfo({
          src: filePath,
          success: resolve,
          fail: function fail(error) {
            return reject(new Error('获取图片信息失败: ' + error.errMsg));
          }
        });
      });
    }

    /**
     * 获取文件信息
     * @param {string} filePath - 文件路径
     * @returns {Promise} 文件信息
     */
  }, {
    key: "getFileInfoAsync",
    value: function getFileInfoAsync(filePath) {
      return new Promise(function (resolve, reject) {
        uni.getFileInfo({
          filePath: filePath,
          success: resolve,
          fail: function fail(error) {
            return reject(new Error('获取文件信息失败: ' + error.errMsg));
          }
        });
      });
    }

    /**
     * 计算目标尺寸
     * @param {number} width - 原始宽度
     * @param {number} height - 原始高度
     * @param {number} maxWidth - 最大宽度
     * @param {number} maxHeight - 最大高度
     * @returns {object} 目标尺寸
     */
  }, {
    key: "calculateSize",
    value: function calculateSize(width, height, maxWidth, maxHeight) {
      var targetWidth = width;
      var targetHeight = height;

      // 如果宽度超过限制
      if (width > maxWidth) {
        targetWidth = maxWidth;
        targetHeight = Math.round(height * (maxWidth / width));
      }

      // 如果高度超过限制
      if (targetHeight > maxHeight) {
        targetHeight = maxHeight;
        targetWidth = Math.round(width * (maxHeight / height));
      }
      return {
        width: targetWidth,
        height: targetHeight
      };
    }

    /**
     * 计算裁剪尺寸（保持宽高比）
     * @param {number} width - 原始宽度
     * @param {number} height - 原始高度
     * @param {number} targetWidth - 目标宽度
     * @param {number} targetHeight - 目标高度
     * @returns {object} 裁剪参数
     */
  }, {
    key: "calculateCropSize",
    value: function calculateCropSize(width, height, targetWidth, targetHeight) {
      // 计算最小的缩放比例，确保裁剪区域不会超出原图
      var scale = Math.min(width / targetWidth, height / targetHeight);

      // 计算裁剪区域的实际尺寸
      var cropWidth = targetWidth * scale;
      var cropHeight = targetHeight * scale;

      // 计算偏移量，使裁剪区域居中
      var offsetX = Math.max(0, (width - cropWidth) / 2);
      var offsetY = Math.max(0, (height - cropHeight) / 2);
      return {
        sourceWidth: width,
        sourceHeight: height,
        targetWidth: targetWidth,
        targetHeight: targetHeight,
        offsetX: Math.round(offsetX),
        offsetY: Math.round(offsetY),
        scale: scale
      };
    }

    /**
     * 获取文件大小
     * @param {string} filePath - 文件路径
     * @returns {Promise<number>} 文件大小
     */
  }, {
    key: "getFileSize",
    value: function () {
      var _getFileSize = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(filePath) {
        var info;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _context3.next = 3;
                return this.getFileInfoAsync(filePath);
              case 3:
                info = _context3.sent;
                return _context3.abrupt("return", info.size);
              case 7:
                _context3.prev = 7;
                _context3.t0 = _context3["catch"](0);
                throw new Error('获取文件大小失败: ' + _context3.t0.message);
              case 10:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, this, [[0, 7]]);
      }));
      function getFileSize(_x4) {
        return _getFileSize.apply(this, arguments);
      }
      return getFileSize;
    }()
    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
  }, {
    key: "formatFileSize",
    value: function formatFileSize(bytes) {
      if (bytes === 0) return '0 B';
      var k = 1024;
      var sizes = ['B', 'KB', 'MB', 'GB'];
      var i = Math.floor(Math.log(bytes) / Math.log(k));
      return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i];
    }

    /**
     * 验证图片格式
     * @param {string} fileName - 文件名
     * @returns {object} 验证结果
     */
  }, {
    key: "validateImageFormat",
    value: function validateImageFormat(fileName) {
      var _fileName$split$pop;
      var allowedFormats = ['jpg', 'jpeg', 'png', 'webp'];
      var extension = (_fileName$split$pop = fileName.split('.').pop()) === null || _fileName$split$pop === void 0 ? void 0 : _fileName$split$pop.toLowerCase();
      if (!extension || !allowedFormats.includes(extension)) {
        return {
          valid: false,
          message: '不支持的图片格式，请选择JPG、PNG或WebP格式'
        };
      }
      return {
        valid: true,
        format: extension
      };
    }

    /**
     * 生成缩略图
     * @param {string} filePath - 图片路径
     * @param {number} size - 缩略图尺寸
     * @returns {Promise<object>} 缩略图信息
     */
  }, {
    key: "generateThumbnail",
    value: function () {
      var _generateThumbnail = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4(filePath) {
        var size,
          result,
          _args4 = arguments;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                size = _args4.length > 1 && _args4[1] !== undefined ? _args4[1] : 100;
                _context4.prev = 1;
                _context4.next = 4;
                return this.compressImage(filePath, 'honor', {
                  maxWidth: size,
                  maxHeight: size,
                  quality: 0.6,
                  maxSize: 50 * 1024 // 50KB
                });
              case 4:
                result = _context4.sent;
                return _context4.abrupt("return", result);
              case 8:
                _context4.prev = 8;
                _context4.t0 = _context4["catch"](1);
                throw new Error('生成缩略图失败: ' + _context4.t0.message);
              case 11:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, this, [[1, 8]]);
      }));
      function generateThumbnail(_x5) {
        return _generateThumbnail.apply(this, arguments);
      }
      return generateThumbnail;
    }()
    /**
     * 批量压缩图片
     * @param {Array<string>} filePaths - 图片路径数组
     * @param {string} type - 图片类型
     * @param {Function} progressCallback - 进度回调
     * @returns {Promise<Array>} 压缩结果数组
     */
  }, {
    key: "batchCompressImages",
    value: function () {
      var _batchCompressImages = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5(filePaths) {
        var type,
          progressCallback,
          total,
          results,
          i,
          result,
          _args5 = arguments;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                type = _args5.length > 1 && _args5[1] !== undefined ? _args5[1] : 'honor';
                progressCallback = _args5.length > 2 ? _args5[2] : undefined;
                total = filePaths.length;
                results = [];
                i = 0;
              case 5:
                if (!(i < total)) {
                  _context5.next = 21;
                  break;
                }
                _context5.prev = 6;
                _context5.next = 9;
                return this.compressImage(filePaths[i], type);
              case 9:
                result = _context5.sent;
                results.push({
                  success: result.success,
                  result: result.success ? result : null,
                  error: result.success ? null : result.error
                });
                if (progressCallback) {
                  progressCallback({
                    current: i + 1,
                    total: total,
                    path: filePaths[i],
                    success: result.success
                  });
                }
                _context5.next = 18;
                break;
              case 14:
                _context5.prev = 14;
                _context5.t0 = _context5["catch"](6);
                results.push({
                  success: false,
                  result: null,
                  error: _context5.t0.message,
                  path: filePaths[i]
                });
                if (progressCallback) {
                  progressCallback({
                    current: i + 1,
                    total: total,
                    path: filePaths[i],
                    success: false,
                    error: _context5.t0.message
                  });
                }
              case 18:
                i++;
                _context5.next = 5;
                break;
              case 21:
                return _context5.abrupt("return", results);
              case 22:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, this, [[6, 14]]);
      }));
      function batchCompressImages(_x6) {
        return _batchCompressImages.apply(this, arguments);
      }
      return batchCompressImages;
    }()
  }]);
  return ImageUtils;
}();
var _default = new ImageUtils();
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ })

}]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/honor_pkg/common/vendor.js.map
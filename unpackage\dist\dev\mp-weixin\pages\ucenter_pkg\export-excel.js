(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/ucenter_pkg/export-excel"],{

/***/ 127:
/*!*********************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fucenter_pkg%2Fexport-excel"} ***!
  \*********************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _exportExcel = _interopRequireDefault(__webpack_require__(/*! ./pages/ucenter_pkg/export-excel.vue */ 128));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_exportExcel.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 128:
/*!**************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/export-excel.vue ***!
  \**************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _export_excel_vue_vue_type_template_id_9133d462_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./export-excel.vue?vue&type=template&id=9133d462&scoped=true& */ 129);
/* harmony import */ var _export_excel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./export-excel.vue?vue&type=script&lang=js& */ 131);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _export_excel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _export_excel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _export_excel_vue_vue_type_style_index_0_id_9133d462_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./export-excel.vue?vue&type=style&index=0&id=9133d462&lang=scss&scoped=true& */ 133);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _export_excel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _export_excel_vue_vue_type_template_id_9133d462_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _export_excel_vue_vue_type_template_id_9133d462_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "9133d462",
  null,
  false,
  _export_excel_vue_vue_type_template_id_9133d462_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/ucenter_pkg/export-excel.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 129:
/*!*********************************************************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/export-excel.vue?vue&type=template&id=9133d462&scoped=true& ***!
  \*********************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_template_id_9133d462_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./export-excel.vue?vue&type=template&id=9133d462&scoped=true& */ 130);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_template_id_9133d462_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_template_id_9133d462_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_template_id_9133d462_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_template_id_9133d462_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 130:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter_pkg/export-excel.vue?vue&type=template&id=9133d462&scoped=true& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 483))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.dataType === "feedback" && _vm.previewData.length > 0
  var l0 = g0
    ? _vm.__map(_vm.previewData, function (item, index) {
        var $orig = _vm.__get_orig(item)
        var m0 = _vm.formatDate(item.createTime)
        return {
          $orig: $orig,
          m0: m0,
        }
      })
    : null
  var g1 = _vm.dataType === "patrol" && _vm.previewData.length > 0
  var l1 = g1
    ? _vm.__map(_vm.previewData, function (item, index) {
        var $orig = _vm.__get_orig(item)
        var m1 = item.checkin_time
          ? _vm.formatDateTime(item.checkin_time)
          : null
        return {
          $orig: $orig,
          m1: m1,
        }
      })
    : null
  var g2 = _vm.previewData.length
  var g3 = _vm.showProgressBar ? Math.floor(_vm.exportProgress) : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        l0: l0,
        g1: g1,
        l1: l1,
        g2: g2,
        g3: g3,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 131:
/*!***************************************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/export-excel.vue?vue&type=script&lang=js& ***!
  \***************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./export-excel.vue?vue&type=script&lang=js& */ 132);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 132:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter_pkg/export-excel.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, uniCloud, wx) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var PEmptyState = function PEmptyState() {
  __webpack_require__.e(/*! require.ensure | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then((function () {
    return resolve(__webpack_require__(/*! @/components/p-empty-state/p-empty-state.vue */ 483));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    PEmptyState: PEmptyState
  },
  data: function data() {
    return {
      dataType: 'feedback',
      // 默认导出找茬数据
      filterType: 'day',
      // 默认按日筛选
      dayValue: this.formatDateForPicker(new Date()),
      // 默认今天
      monthValue: this.formatDateForPicker(new Date(), 'month'),
      // 默认本月
      yearValue: this.formatDateForPicker(new Date(), 'year'),
      // 默认今年
      previewData: [],
      // 预览数据
      dataCount: 0,
      // 数据总数
      isLoading: false,
      exportLoading: false,
      // 导出状态标志
      fetchTimer: null,
      // 用于防抖处理
      exportProgress: 0,
      // 导出进度
      showProgressBar: false,
      // 是否显示进度条
      exportTimeoutId: null,
      // 导出超时计时器ID
      MAX_EXPORT_COUNT: 5000,
      // 最大导出记录数
      hasWarnedAboutLargeData: false,
      // 是否已警告大数据量
      hasTasksWithoutRecords: false,
      // 标记是否有任务但没有记录
      startTimeValue: '',
      // 添加时间段选择
      endTimeValue: '',
      // 添加时间段选择
      startDateValue: '',
      // 添加开始日期
      endDateValue: '' // 添加结束日期
    };
  },

  computed: {
    // 判断数据量是否过大
    isLargeDataSet: function isLargeDataSet() {
      return this.dataCount > this.MAX_EXPORT_COUNT;
    },
    // 导出按钮禁用状态
    exportBtnDisabled: function exportBtnDisabled() {
      // 当选择日期范围但未完成选择时禁用按钮
      if (this.filterType === 'dateRange' && (!this.startDateValue || !this.endDateValue)) {
        return true;
      }

      // 特殊处理巡检记录导出按钮
      if (this.dataType === 'patrol') {
        // 只要显示有预览数据或已知数据数量大于0，就允许导出
        return this.previewData.length === 0 && this.dataCount === 0 && !this.hasTasksWithoutRecords;
      }
      // 其他数据类型使用原来的禁用逻辑
      return this.dataCount === 0 || this.exportLoading;
    },
    // 时间范围选择器的状态
    hasTimeRangeSelected: function hasTimeRangeSelected() {
      return this.dataType === 'patrol' && this.filterType === 'day' && this.startTimeValue && this.endTimeValue;
    },
    // 日期范围选择器的状态
    hasDateRangeSelected: function hasDateRangeSelected() {
      return this.filterType === 'dateRange' && this.startDateValue && this.endDateValue;
    },
    // 当前数据类型的表格标题
    tableTitle: function tableTitle() {
      return this.dataType === 'feedback' ? '找茬数据' : '巡视记录';
    }
  },
  onLoad: function onLoad() {
    var _this = this;
    // 页面加载时获取数据，但使用延迟加载
    setTimeout(function () {
      _this.fetchData();
      // 移除网络检测启动 - 功能不必要
      // this.startNetworkCheck(); // 启动网络检测
    }, 200);
  },
  onUnload: function onUnload() {
    // 页面卸载时清理资源
    this.cleanupResources();
  },
  methods: {
    // 设置数据类型
    setDataType: function setDataType(type) {
      if (this.dataType === type) return; // 如果类型相同，不重复请求
      this.dataType = type;
      // 重置大数据警告标志
      this.hasWarnedAboutLargeData = false;
      this.debounceFetchData(); // 使用防抖处理
    },
    // 设置筛选类型
    setFilterType: function setFilterType(type) {
      if (this.filterType === type) return; // 如果类型相同，不重复请求
      this.filterType = type;
      // 重置大数据警告标志
      this.hasWarnedAboutLargeData = false;
      this.debounceFetchData(); // 使用防抖处理
    },
    // 日期选择器变化 - 按日
    onDayChange: function onDayChange(e) {
      this.dayValue = e.detail.value;
      // 重置大数据警告标志
      this.hasWarnedAboutLargeData = false;
      this.debounceFetchData(); // 使用防抖处理
    },
    // 日期选择器变化 - 按月
    onMonthChange: function onMonthChange(e) {
      this.monthValue = e.detail.value;
      // 重置大数据警告标志
      this.hasWarnedAboutLargeData = false;
      this.debounceFetchData(); // 使用防抖处理
    },
    // 日期选择器变化 - 按年
    onYearChange: function onYearChange(e) {
      this.yearValue = e.detail.value;
      // 重置大数据警告标志
      this.hasWarnedAboutLargeData = false;
      this.debounceFetchData(); // 使用防抖处理
    },
    // 防抖处理函数
    debounceFetchData: function debounceFetchData() {
      var _this2 = this;
      // 清除之前的定时器
      if (this.fetchTimer) {
        clearTimeout(this.fetchTimer);
      }

      // 设置新的定时器，300ms后执行
      this.fetchTimer = setTimeout(function () {
        _this2.fetchData();
      }, 300);
    },
    // 格式化日期用于显示
    formatDate: function formatDate(timestamp) {
      if (!timestamp) return '';
      var date = new Date(timestamp);
      return "".concat(date.getFullYear(), "-").concat(String(date.getMonth() + 1).padStart(2, '0'), "-").concat(String(date.getDate()).padStart(2, '0'));
    },
    // 格式化日期和时间用于显示
    formatDateTime: function formatDateTime(timestamp) {
      if (!timestamp) return '';
      var date = new Date(timestamp);
      return "".concat(date.getFullYear(), "-").concat(String(date.getMonth() + 1).padStart(2, '0'), "-").concat(String(date.getDate()).padStart(2, '0'), " ").concat(String(date.getHours()).padStart(2, '0'), ":").concat(String(date.getMinutes()).padStart(2, '0'));
    },
    // 格式化日期用于日期选择器
    formatDateForPicker: function formatDateForPicker(date) {
      var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'day';
      if (!date) return '';
      if (type === 'day') {
        return "".concat(date.getFullYear(), "-").concat(String(date.getMonth() + 1).padStart(2, '0'), "-").concat(String(date.getDate()).padStart(2, '0'));
      } else if (type === 'month') {
        return "".concat(date.getFullYear(), "-").concat(String(date.getMonth() + 1).padStart(2, '0'));
      } else if (type === 'year') {
        return "".concat(date.getFullYear());
      }
      return '';
    },
    // 获取数据
    fetchData: function fetchData() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var db, dbCmd, _this3$getTimeRange, startTime, endTime, collectionName, whereCondition, countRes, formattedStartDate, formattedEndDate, taskResult, normalPointCount, missedPointCount, notCheckedPointCount, totalPointCount, taskIds, tasks, patrolRecords, recordsResult, fields, orderBy, res, records, _taskIds, tasksRes, tasksMap, _this3$getTimeRange2, _startTime, _endTime, _formattedStartDate, _formattedEndDate, _tasksRes, _iterator, _step, _loop, _res;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (!_this3.isLoading) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return");
              case 2:
                _this3.isLoading = true;
                uni.showLoading({
                  title: '加载中...',
                  mask: true // 添加遮罩，防止用户重复操作
                });
                _context.prev = 4;
                db = uniCloud.database();
                dbCmd = db.command; // 根据筛选类型构建查询条件
                _this3$getTimeRange = _this3.getTimeRange(), startTime = _this3$getTimeRange.startTime, endTime = _this3$getTimeRange.endTime; // 根据数据类型选择集合和查询条件
                if (!(_this3.dataType === 'feedback')) {
                  _context.next = 25;
                  break;
                }
                collectionName = 'feedback';
                whereCondition = {
                  createTime: dbCmd.gte(startTime).and(dbCmd.lt(endTime))
                };

                // 先获取总数 - 使用try-catch单独包装，避免影响整体功能
                _context.prev = 11;
                _context.next = 14;
                return db.collection(collectionName).where(whereCondition).count();
              case 14:
                countRes = _context.sent;
                if (countRes.result && countRes.result.total !== undefined) {
                  _this3.dataCount = countRes.result.total;

                  // 数据量大时提示用户
                  if (_this3.isLargeDataSet && !_this3.hasWarnedAboutLargeData) {
                    _this3.hasWarnedAboutLargeData = true;
                    uni.showModal({
                      title: '数据量提示',
                      content: "\u5F53\u524D\u7B5B\u9009\u6761\u4EF6\u4E0B\u5171\u6709 ".concat(_this3.dataCount, " \u6761\u8BB0\u5F55\uFF0C\u8D85\u8FC7\u63A8\u8350\u5BFC\u51FA\u6570\u91CF(").concat(_this3.MAX_EXPORT_COUNT, ")\uFF0C\u5BFC\u51FA\u53EF\u80FD\u9700\u8981\u8F83\u957F\u65F6\u95F4\uFF0C\u5EFA\u8BAE\u7F29\u5C0F\u7B5B\u9009\u8303\u56F4\u3002"),
                      confirmText: '继续',
                      cancelText: '知道了',
                      success: function success(res) {
                        if (!res.confirm) {
                          // 用户取消，可以在这里自动切换到更小的时间范围
                          if (_this3.filterType === 'year') {
                            // 年太大，切换到月
                            _this3.filterType = 'month';
                            _this3.debounceFetchData();
                          } else if (_this3.filterType === 'month') {
                            // 月太大，切换到日
                            _this3.filterType = 'day';
                            _this3.debounceFetchData();
                          }
                        }
                      }
                    });
                  }
                } else {
                  _this3.dataCount = 0;
                }
                _context.next = 23;
                break;
              case 18:
                _context.prev = 18;
                _context.t0 = _context["catch"](11);
                console.error('获取数据数量失败:', _context.t0);
                uni.showModal({
                  title: '数据获取错误',
                  content: "\u83B7\u53D6".concat(_this3.tableTitle, "\u6570\u91CF\u5931\u8D25: ").concat(_context.t0.message || _context.t0),
                  showCancel: false
                });
                _this3.dataCount = 0;
              case 23:
                _context.next = 64;
                break;
              case 25:
                // 巡检记录 - 使用不同的处理逻辑
                // 从patrol-task表中获取任务
                formattedStartDate = _this3.formatDate(startTime);
                formattedEndDate = _this3.formatDate(endTime - 86400000); // 减去一天，因为endTime是下一天的0点
                _context.prev = 27;
                _context.next = 30;
                return db.collection('patrol-task').where({
                  patrol_date: _this3.filterType === 'day' ? formattedStartDate : dbCmd.gte(formattedStartDate).and(dbCmd.lte(formattedEndDate))
                }).get();
              case 30:
                taskResult = _context.sent;
                // 初始化计数
                normalPointCount = 0; // 正常打卡点位数
                missedPointCount = 0; // 缺卡点位数
                notCheckedPointCount = 0; // 未打卡点位数
                totalPointCount = 0; // 任务总点位数
                taskIds = []; // 任务ID列表，用于后续查询打卡记录
                if (!(taskResult.result && taskResult.result.data && taskResult.result.data.length > 0)) {
                  _context.next = 55;
                  break;
                }
                tasks = taskResult.result.data; // 收集任务ID
                taskIds = tasks.map(function (task) {
                  return task._id;
                });

                // 如果是跨月查询，先获取巡视记录数据用于辅助判断状态
                patrolRecords = {};
                if (!(_this3.filterType !== 'day' && startTime && endTime)) {
                  _context.next = 51;
                  break;
                }
                _context.prev = 41;
                _context.next = 44;
                return db.collection('patrol-record').where({
                  checkin_time: dbCmd.gte(new Date(startTime)).and(dbCmd.lt(new Date(endTime)))
                }).field('point_id,task_id').get();
              case 44:
                recordsResult = _context.sent;
                if (recordsResult.result && recordsResult.result.data) {
                  // 创建点位ID到记录的映射
                  recordsResult.result.data.forEach(function (record) {
                    if (record.point_id && record.task_id) {
                      var key = "".concat(record.task_id, "_").concat(record.point_id);
                      patrolRecords[key] = true;
                    }
                  });
                }
                _context.next = 51;
                break;
              case 48:
                _context.prev = 48;
                _context.t1 = _context["catch"](41);
                console.error('获取巡视记录数据失败:', _context.t1);
              case 51:
                // 统计所有任务的点位情况
                tasks.forEach(function (task) {
                  if (task.rounds_detail && Array.isArray(task.rounds_detail)) {
                    task.rounds_detail.forEach(function (round) {
                      if (round.points && round.points.length > 0) {
                        round.points.forEach(function (point) {
                          totalPointCount++;

                          // 对于跨月查询，使用巡视记录来辅助判断状态
                          if (_this3.filterType !== 'day' && Object.keys(patrolRecords).length > 0) {
                            var recordKey = "".concat(task._id, "_").concat(point.point_id);
                            if (patrolRecords[recordKey]) {
                              // 有记录，说明已打卡
                              normalPointCount++;
                              // 更新点的状态为已打卡
                              point.status = 1;
                            } else if (point.status === 3 || point.status === 4) {
                              // 本身就是缺卡状态
                              missedPointCount++;
                            } else {
                              // 其他情况视为未打卡
                              notCheckedPointCount++;
                              point.status = 0;
                            }
                          } else {
                            // 单日查询使用原有逻辑
                            if (point.status === 1) {
                              normalPointCount++;
                            } else if (point.status === 3 || point.status === 4) {
                              missedPointCount++;
                            } else if (point.status === 0) {
                              notCheckedPointCount++;
                            }
                          }
                        });
                      }
                    });
                  }
                });

                // 更新总数
                _this3.dataCount = totalPointCount;
                _context.next = 56;
                break;
              case 55:
                _this3.dataCount = 0;
              case 56:
                _context.next = 62;
                break;
              case 58:
                _context.prev = 58;
                _context.t2 = _context["catch"](27);
                console.error('获取巡视任务数据失败:', _context.t2);
                _this3.dataCount = 0;
              case 62:
                // 如果有任务，继续获取预览数据
                collectionName = 'patrol-record';
                whereCondition = {
                  checkin_time: dbCmd.gte(new Date(startTime)).and(dbCmd.lt(new Date(endTime)))
                };
              case 64:
                if (!(_this3.dataCount > 0 || _this3.dataType === 'patrol')) {
                  _context.next = 122;
                  break;
                }
                _context.prev = 65;
                // 根据数据类型选择字段和排序字段

                if (_this3.dataType === 'feedback') {
                  fields = 'name,project,description,createTime';
                  orderBy = 'createTime';
                } else {
                  fields = 'point_id,user_id,task_id,round,remark,checkin_time,photos,point_name,user_name';
                  orderBy = 'checkin_time';
                }

                // 对于巡视记录，尝试获取实际记录和未打卡记录的混合预览
                if (!(_this3.dataType === 'patrol')) {
                  _context.next = 109;
                  break;
                }
                _context.next = 70;
                return db.collection(collectionName).where(whereCondition).orderBy(orderBy, 'desc').limit(5).field(fields).get();
              case 70:
                res = _context.sent;
                if (!(res.result && res.result.data && res.result.data.length > 0)) {
                  _context.next = 93;
                  break;
                }
                // 有打卡记录
                records = res.result.data; // 对于patrol数据，我们需要尝试获取更多任务信息
                _context.prev = 73;
                // 获取所有相关任务的ID
                _taskIds = (0, _toConsumableArray2.default)(new Set(records.map(function (record) {
                  return record.task_id;
                }).filter(function (id) {
                  return id;
                })));
                if (!(_taskIds.length > 0)) {
                  _context.next = 84;
                  break;
                }
                _context.next = 78;
                return db.collection('patrol-task').where({
                  _id: dbCmd.in(_taskIds)
                }).field('_id,user_id,user_name,patrol_date').get();
              case 78:
                tasksRes = _context.sent;
                tasksMap = {};
                if (tasksRes.result && tasksRes.result.data) {
                  tasksRes.result.data.forEach(function (task) {
                    tasksMap[task._id] = task;
                  });
                }

                // 使用任务信息补充记录数据
                _this3.previewData = records.map(function (record) {
                  var task = record.task_id ? tasksMap[record.task_id] : null;
                  return _objectSpread(_objectSpread({}, record), {}, {
                    locationName: record.point_name || '未知地点',
                    userName: (task === null || task === void 0 ? void 0 : task.user_name) || record.user_name || '巡检人员'
                  });
                });
                _context.next = 85;
                break;
              case 84:
                // 如果没有任务ID，使用原有的记录信息
                _this3.previewData = records.map(function (record) {
                  return _objectSpread(_objectSpread({}, record), {}, {
                    locationName: record.point_name || '未知地点',
                    userName: record.user_name || '巡检人员'
                  });
                });
              case 85:
                _context.next = 91;
                break;
              case 87:
                _context.prev = 87;
                _context.t3 = _context["catch"](73);
                console.error('获取巡视任务信息失败:', _context.t3);
                // 失败时使用原有的记录信息
                _this3.previewData = records.map(function (record) {
                  return _objectSpread(_objectSpread({}, record), {}, {
                    locationName: record.point_name || '未知地点',
                    userName: record.user_name || '巡检人员'
                  });
                });
              case 91:
                _context.next = 107;
                break;
              case 93:
                _context.prev = 93;
                // 获取日期范围内的任务
                _this3$getTimeRange2 = _this3.getTimeRange(), _startTime = _this3$getTimeRange2.startTime, _endTime = _this3$getTimeRange2.endTime;
                _formattedStartDate = _this3.formatDate(_startTime);
                _formattedEndDate = _this3.formatDate(_endTime - 86400000);
                _context.next = 99;
                return db.collection('patrol-task').where({
                  patrol_date: _this3.filterType === 'day' ? _formattedStartDate : dbCmd.gte(_formattedStartDate).and(dbCmd.lte(_formattedEndDate))
                }).limit(2) // 只获取前2个任务
                .get();
              case 99:
                _tasksRes = _context.sent;
                if (_tasksRes.result && _tasksRes.result.data && _tasksRes.result.data.length > 0) {
                  // 从任务中创建"未打卡"预览数据
                  _this3.previewData = [];

                  // 处理每个任务
                  _iterator = _createForOfIteratorHelper(_tasksRes.result.data);
                  try {
                    _loop = function _loop() {
                      var task = _step.value;
                      if (task.rounds_detail && task.rounds_detail.length > 0) {
                        // 获取第一个轮次的前几个点
                        var round = task.rounds_detail[0];
                        if (round.points && round.points.length > 0) {
                          // 获取该轮次的前3个点位（或更少）
                          var pointsToShow = round.points.slice(0, 3);

                          // 为每个点位创建一个预览记录
                          pointsToShow.forEach(function (point) {
                            _this3.previewData.push({
                              task_id: task._id,
                              point_id: point.point_id,
                              round: round.round || 1,
                              locationName: point.name || '未知地点',
                              userName: task.user_name || '巡检人员',
                              checkin_time: null,
                              // 没有打卡时间
                              status: point.status || 0,
                              // 使用点位状态或默认为0（未打卡）
                              isPreview: true // 标记为预览数据，非实际打卡记录
                            });
                          });
                        }
                      }
                    };
                    for (_iterator.s(); !(_step = _iterator.n()).done;) {
                      _loop();
                    }
                  } catch (err) {
                    _iterator.e(err);
                  } finally {
                    _iterator.f();
                  }
                } else {
                  _this3.previewData = [];
                }
                _context.next = 107;
                break;
              case 103:
                _context.prev = 103;
                _context.t4 = _context["catch"](93);
                console.error('获取任务预览数据失败:', _context.t4);
                _this3.previewData = [];
              case 107:
                _context.next = 113;
                break;
              case 109:
                _context.next = 111;
                return db.collection(collectionName).where(whereCondition).orderBy(orderBy, 'desc').limit(5).field(fields).get();
              case 111:
                _res = _context.sent;
                if (_res.result && _res.result.data) {
                  _this3.previewData = _res.result.data;
                } else {
                  _this3.previewData = [];
                }
              case 113:
                _context.next = 120;
                break;
              case 115:
                _context.prev = 115;
                _context.t5 = _context["catch"](65);
                console.error('获取预览数据失败:', _context.t5);
                uni.showToast({
                  title: "\u83B7\u53D6".concat(_this3.tableTitle, "\u9884\u89C8\u5931\u8D25"),
                  icon: 'none'
                });
                _this3.previewData = [];
              case 120:
                _context.next = 123;
                break;
              case 122:
                _this3.previewData = [];
              case 123:
                _context.next = 130;
                break;
              case 125:
                _context.prev = 125;
                _context.t6 = _context["catch"](4);
                uni.showToast({
                  title: '获取数据失败',
                  icon: 'none'
                });
                _this3.previewData = [];
                _this3.dataCount = 0;
              case 130:
                _context.prev = 130;
                uni.hideLoading();
                _this3.isLoading = false;
                return _context.finish(130);
              case 134:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[4, 125, 130, 134], [11, 18], [27, 58], [41, 48], [65, 115], [73, 87], [93, 103]]);
      }))();
    },
    // 获取时间范围
    getTimeRange: function getTimeRange() {
      var startTime, endTime;
      if (this.filterType === 'day') {
        // 按日筛选 - 当天0点到次日0点
        // 从日期字符串中提取年月日
        var dateParts = this.dayValue.split('-');
        var year = parseInt(dateParts[0]);
        var month = parseInt(dateParts[1]) - 1; // JS月份从0开始
        var day = parseInt(dateParts[2]);

        // 检查是否有时间段选择（针对巡视记录）
        if (this.dataType === 'patrol' && this.startTimeValue && this.endTimeValue) {
          // 如果有时间段选择，则使用指定的时间段
          var startTimeParts = this.startTimeValue.split(':');
          var endTimeParts = this.endTimeValue.split(':');
          var startHour = parseInt(startTimeParts[0]);
          var startMinute = parseInt(startTimeParts[1]);
          var endHour = parseInt(endTimeParts[0]);
          var endMinute = parseInt(endTimeParts[1]);

          // 创建指定时间段的日期对象
          startTime = new Date(year, month, day, startHour, startMinute, 0).getTime();
          endTime = new Date(year, month, day, endHour, endMinute, 59).getTime();

          // 确保开始时间小于结束时间
          if (startTime > endTime) {
            // 如果用户选择了结束时间早于开始时间，我们假设是跨天的时间段
            // 将结束时间调整为下一天
            endTime = new Date(year, month, day + 1, endHour, endMinute, 59).getTime();
          }
          console.log("\u4F7F\u7528\u6307\u5B9A\u65F6\u95F4\u6BB5: ".concat(this.startTimeValue, " - ").concat(this.endTimeValue));
        } else {
          // 默认使用整天时间范围
          startTime = new Date(year, month, day, 0, 0, 0).getTime();
          endTime = new Date(year, month, day + 1, 0, 0, 0).getTime();
        }
        console.log("\u6309\u65E5\u7B5B\u9009: ".concat(this.dayValue, ", \u65F6\u95F4\u8303\u56F4: ").concat(new Date(startTime).toISOString(), " - ").concat(new Date(endTime).toISOString()));
      } else if (this.filterType === 'month') {
        // 按月筛选 - 当月1日0点到次月1日0点
        var parts = this.monthValue.split('-');
        var _year = parseInt(parts[0]);
        var _month = parseInt(parts[1]) - 1; // 月份从0开始

        startTime = new Date(_year, _month, 1, 0, 0, 0).getTime();
        endTime = new Date(_year, _month + 1, 1, 0, 0, 0).getTime();

        // 添加更多调试信息
        console.log("\u6309\u6708\u7B5B\u9009: ".concat(this.monthValue, ", \u8D77\u59CB\u65E5\u671F: ").concat(this.formatDate(startTime), ", \u7ED3\u675F\u65E5\u671F: ").concat(this.formatDate(endTime - 1)));
        console.log("\u65F6\u95F4\u8303\u56F4: ".concat(new Date(startTime).toISOString(), " - ").concat(new Date(endTime).toISOString()));
      } else if (this.filterType === 'dateRange') {
        // 日期范围筛选 - 从起始日期0点到结束日期次日0点
        // 确保两个日期都已选择
        if (!this.startDateValue || !this.endDateValue) {
          // 如果没有完整选择日期范围，返回空时间范围
          return {
            startTime: 0,
            endTime: 0
          };
        }

        // 解析开始日期
        var startDateParts = this.startDateValue.split('-');
        var startYear = parseInt(startDateParts[0]);
        var startMonth = parseInt(startDateParts[1]) - 1; // JS月份从0开始
        var startDay = parseInt(startDateParts[2]);

        // 解析结束日期
        var endDateParts = this.endDateValue.split('-');
        var endYear = parseInt(endDateParts[0]);
        var endMonth = parseInt(endDateParts[1]) - 1; // JS月份从0开始
        var endDay = parseInt(endDateParts[2]);

        // 创建开始和结束时间戳
        startTime = new Date(startYear, startMonth, startDay, 0, 0, 0).getTime();
        // 结束时间戳是结束日期的次日0点（为了包含整个结束日期）
        endTime = new Date(endYear, endMonth, endDay + 1, 0, 0, 0).getTime();

        // 确保开始时间小于结束时间
        if (startTime > endTime) {
          // 如果用户选择的开始日期晚于结束日期，交换它们
          var tempTime = startTime;
          startTime = endTime - 24 * 60 * 60 * 1000; // 减去一天
          endTime = tempTime + 24 * 60 * 60 * 1000; // 加上一天

          // 为了避免UI显示混淆，也更新日期选择器的值
          var tempDate = this.startDateValue;
          this.startDateValue = this.endDateValue;
          this.endDateValue = tempDate;
        }

        // 检查是否是跨月查询，为调试输出更多信息
        var startMonthYear = "".concat(startYear, "-").concat(startMonth + 1);
        var endMonthYear = "".concat(endYear, "-").concat(endMonth + 1);
        if (startMonthYear !== endMonthYear) {
          console.log("\u8DE8\u6708\u67E5\u8BE2: ".concat(startMonthYear, " \u5230 ").concat(endMonthYear));
        }
        console.log("\u65E5\u671F\u8303\u56F4\u7B5B\u9009: ".concat(this.startDateValue, " \u81F3 ").concat(this.endDateValue, ", \u65F6\u95F4\u8303\u56F4: ").concat(new Date(startTime).toISOString(), " - ").concat(new Date(endTime).toISOString()));
      } else if (this.filterType === 'year') {
        // 按年筛选 - 当年1月1日0点到次年1月1日0点
        var _year2 = parseInt(this.yearValue);
        startTime = new Date(_year2, 0, 1, 0, 0, 0).getTime();
        endTime = new Date(_year2 + 1, 0, 1, 0, 0, 0).getTime();
        console.log("\u6309\u5E74\u7B5B\u9009: ".concat(this.yearValue, ", \u65F6\u95F4\u8303\u56F4: ").concat(new Date(startTime).toISOString(), " - ").concat(new Date(endTime).toISOString()));
      }
      return {
        startTime: startTime,
        endTime: endTime
      };
    },
    // 清理资源方法
    cleanupResources: function cleanupResources() {
      // 清除所有计时器
      if (this.fetchTimer) {
        clearTimeout(this.fetchTimer);
        this.fetchTimer = null;
      }
      if (this.exportTimeoutId) {
        clearTimeout(this.exportTimeoutId);
        this.exportTimeoutId = null;
      }
      // 移除网络检测定时器清理 - 功能已移除
      // if (this.networkCheckTimer) {
      // 	clearInterval(this.networkCheckTimer);
      // 	this.networkCheckTimer = null;
      // }
    },
    // 网络状态检测
    // startNetworkCheck() {
    // 	// 先检查一次
    // 	this.checkNetworkStatus();
    // 	
    // 	// 设置定期检查（每30秒）
    // 	this.networkCheckTimer = setInterval(() => {
    // 		this.checkNetworkStatus();
    // 	}, 30000);
    // },
    // 
    // // 检查网络状态
    // async checkNetworkStatus() {
    // 	try {
    // 		uni.getNetworkType({
    // 			success: (res) => {
    // 				// 如果网络类型是none或unknown，显示警告
    // 				if (res.networkType === 'none' || res.networkType === 'unknown') {
    // 					this.showNetworkWarning = true;
    // 				} else {
    // 					// 测试网络连接性
    // 					this.testNetworkConnection();
    // 				}
    // 			},
    // 			fail: () => {
    // 				// 获取网络状态失败，也显示警告
    // 				this.showNetworkWarning = true;
    // 			}
    // 		});
    // 	} catch (e) {
    // 		console.error('检查网络状态失败:', e);
    // 	}
    // },
    // 
    // // 测试网络连接
    // async testNetworkConnection() {
    // 	try {
    // 		// 使用ping云函数或其他轻量级方法测试连接
    // 		const db = uniCloud.database();
    // 		
    // 		// 设置超时
    // 		const timeout = new Promise((_, reject) => {
    // 			setTimeout(() => reject(new Error('网络请求超时')), 5000);
    // 		});
    // 		
    // 		// 轻量级ping请求（仅返回服务器时间）- 使用patrol-record而非feedback
    // 		const pingRequest = db.collection('patrol-record').limit(1).field('_id').get();
    // 		
    // 		// 用Promise.race竞争，哪个先完成就用哪个结果
    // 		await Promise.race([pingRequest, timeout]);
    // 		
    // 		// 如果能到这里，说明网络正常
    // 		this.showNetworkWarning = false;
    // 	} catch (e) {
    // 		console.error('网络连接测试失败:', e);
    // 		this.showNetworkWarning = true;
    // 	}
    // },
    // 导出Excel
    exportExcel: function exportExcel() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var db, dbCmd, _this4$getTimeRange, startTime, endTime, formattedStartDate, formattedEndDate, taskResult, normalPointCount, missedPointCount, notCheckedPointCount, totalPointCount, tasks, confirmRes, _confirmRes, _this4$getTimeRange2, _startTime2, _endTime2, fileName, requestId, exportExcelCloud, result, urlWithCache, errorMessage;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                if (!(_this4.dataCount === 0)) {
                  _context2.next = 3;
                  break;
                }
                uni.showToast({
                  title: '没有数据可导出',
                  icon: 'none'
                });
                return _context2.abrupt("return");
              case 3:
                if (!_this4.exportLoading) {
                  _context2.next = 6;
                  break;
                }
                uni.showToast({
                  title: '正在导出中，请勿重复操作',
                  icon: 'none'
                });
                return _context2.abrupt("return");
              case 6:
                if (!(_this4.dataType === 'patrol')) {
                  _context2.next = 47;
                  break;
                }
                _context2.prev = 7;
                uni.showLoading({
                  title: '计算导出数据...',
                  mask: true
                });
                db = uniCloud.database();
                dbCmd = db.command;
                _this4$getTimeRange = _this4.getTimeRange(), startTime = _this4$getTimeRange.startTime, endTime = _this4$getTimeRange.endTime; // 使用patrol-task表来获取巡视任务数据量
                // 转换为日期格式，该表使用日期字符串而非时间戳
                formattedStartDate = _this4.formatDate(startTime);
                formattedEndDate = _this4.formatDate(endTime - 86400000); // 减去一天，因为endTime是下一天的0点
                // 使用与index.vue相同的方式获取任务列表
                _context2.next = 16;
                return db.collection('patrol-task').where({
                  patrol_date: _this4.filterType === 'day' ? formattedStartDate : dbCmd.gte(formattedStartDate).and(dbCmd.lte(formattedEndDate))
                }).get();
              case 16:
                taskResult = _context2.sent;
                // 初始化计数
                normalPointCount = 0; // 正常打卡点位数
                missedPointCount = 0; // 缺卡点位数
                notCheckedPointCount = 0; // 未打卡点位数
                totalPointCount = 0; // 任务总点位数
                if (taskResult.result && taskResult.result.data) {
                  tasks = taskResult.result.data; // 统计所有任务的点位情况
                  tasks.forEach(function (task) {
                    if (task.rounds_detail && Array.isArray(task.rounds_detail)) {
                      // 每个任务有多个轮次的点位
                      task.rounds_detail.forEach(function (round) {
                        if (round.points && Array.isArray(round.points)) {
                          // 统计每个点位的状态
                          round.points.forEach(function (point) {
                            totalPointCount++;

                            // 根据状态分类统计
                            if (point.status === 1) {
                              // 正常打卡
                              normalPointCount++;
                            } else if (point.status === 3 || point.status === 4) {
                              // 缺卡（状态为3或4）
                              missedPointCount++;
                            } else if (point.status === 0) {
                              // 未打卡（状态为0）
                              notCheckedPointCount++;
                            }
                          });
                        }
                      });
                    }
                  });
                }

                // 确保总数至少包含所有类型的点位数量
                totalPointCount = Math.max(totalPointCount, normalPointCount + missedPointCount + notCheckedPointCount);

                // 更新数据量信息 - 使用总点位数，包括未打卡和缺卡的点位
                if (!(totalPointCount > 0)) {
                  _context2.next = 35;
                  break;
                }
                _this4.dataCount = totalPointCount;

                // 大数据量时的警告处理
                if (!(_this4.isLargeDataSet && !_this4.hasWarnedAboutLargeData)) {
                  _context2.next = 33;
                  break;
                }
                _this4.hasWarnedAboutLargeData = true;
                _context2.next = 29;
                return new Promise(function (resolve) {
                  uni.showModal({
                    title: '数据量较大',
                    content: "\u5F53\u524D\u7B5B\u9009\u6761\u4EF6\u4E0B\u5171\u6709 ".concat(_this4.dataCount, " \u4E2A\u70B9\u4F4D\u8BB0\u5F55(\u5305\u62EC ").concat(normalPointCount, " \u4E2A\u6B63\u5E38\u6253\u5361, ").concat(missedPointCount, " \u4E2A\u7F3A\u5361, ").concat(notCheckedPointCount, " \u4E2A\u672A\u6253\u5361)\uFF0C\u5BFC\u51FA\u53EF\u80FD\u9700\u8981\u8F83\u957F\u65F6\u95F4\uFF0C\u786E\u5B9A\u7EE7\u7EED\u5417\uFF1F"),
                    confirmText: '继续导出',
                    cancelText: '取消',
                    success: function success(res) {
                      resolve(res.confirm);
                    }
                  });
                });
              case 29:
                confirmRes = _context2.sent;
                if (confirmRes) {
                  _context2.next = 33;
                  break;
                }
                uni.hideLoading();
                return _context2.abrupt("return");
              case 33:
                _context2.next = 38;
                break;
              case 35:
                // 如果没有点位数据，显示一个更友好的提示
                uni.hideLoading();
                uni.showToast({
                  title: '当前时间范围内没有巡视点位数据',
                  icon: 'none',
                  duration: 2000
                });
                return _context2.abrupt("return");
              case 38:
                uni.hideLoading();
                _context2.next = 47;
                break;
              case 41:
                _context2.prev = 41;
                _context2.t0 = _context2["catch"](7);
                console.error('获取巡视任务点位数量失败:', _context2.t0);
                uni.hideLoading();
                uni.showToast({
                  title: '获取巡视点位数据失败',
                  icon: 'none'
                });
                return _context2.abrupt("return");
              case 47:
                if (!(_this4.isLargeDataSet && !_this4.hasWarnedAboutLargeData)) {
                  _context2.next = 54;
                  break;
                }
                _this4.hasWarnedAboutLargeData = true;
                _context2.next = 51;
                return new Promise(function (resolve) {
                  uni.showModal({
                    title: '数据量较大',
                    content: "\u5F53\u524D\u7B5B\u9009\u6761\u4EF6\u4E0B\u5171\u6709 ".concat(_this4.dataCount, " \u6761\u8BB0\u5F55\uFF0C\u8D85\u8FC7\u63A8\u8350\u5BFC\u51FA\u6570\u91CF(").concat(_this4.MAX_EXPORT_COUNT, ")\uFF0C\u5BFC\u51FA\u53EF\u80FD\u9700\u8981\u8F83\u957F\u65F6\u95F4\uFF0C\u786E\u5B9A\u7EE7\u7EED\u5417\uFF1F"),
                    confirmText: '继续导出',
                    cancelText: '取消',
                    success: function success(res) {
                      resolve(res.confirm);
                    }
                  });
                });
              case 51:
                _confirmRes = _context2.sent;
                if (_confirmRes) {
                  _context2.next = 54;
                  break;
                }
                return _context2.abrupt("return");
              case 54:
                // 🔥 设置导出状态，防止重复提交
                _this4.exportLoading = true;
                _this4.showProgressBar = true;
                _this4.exportProgress = 0;

                // 显示初始加载提示
                uni.showLoading({
                  title: '准备导出...',
                  mask: true
                });

                // 🔥 延长超时时间到5分钟，避免误判
                _this4.exportTimeoutId = setTimeout(function () {
                  if (_this4.exportLoading) {
                    _this4.exportLoading = false;
                    _this4.showProgressBar = false;
                    uni.hideLoading();
                    uni.showModal({
                      title: '导出超时',
                      content: '导出操作耗时过长，可能是数据量太大导致。请尝试缩小时间范围后重试。',
                      showCancel: false
                    });
                  }
                }, 300000); // 🔥 改为5分钟超时
                _context2.prev = 59;
                // 获取时间范围
                _this4$getTimeRange2 = _this4.getTimeRange(), _startTime2 = _this4$getTimeRange2.startTime, _endTime2 = _this4$getTimeRange2.endTime; // 生成文件名
                fileName = _this4.tableTitle + '_';
                if (_this4.filterType === 'day') {
                  fileName += _this4.dayValue;

                  // 如果是巡视记录且选择了时间段，将时间段添加到文件名
                  if (_this4.hasTimeRangeSelected) {
                    fileName += "_".concat(_this4.startTimeValue.replace(':', ''), "\u81F3").concat(_this4.endTimeValue.replace(':', ''));
                  }
                } else if (_this4.filterType === 'month') {
                  fileName += _this4.monthValue;
                } else if (_this4.filterType === 'dateRange') {
                  // 对于日期范围，在文件名中包含起止日期
                  fileName += "".concat(_this4.startDateValue, "\u81F3").concat(_this4.endDateValue);
                } else if (_this4.filterType === 'year') {
                  fileName += _this4.yearValue;
                }
                fileName += '.xlsx';

                // 🔥 使用真实的分阶段导出进度
                _this4.startRealProgressUpdate();

                // 🔥 增加请求唯一标识，防止重复请求
                requestId = "export_".concat(Date.now(), "_").concat(Math.random().toString(36).substring(2, 8));
                console.log("\uD83D\uDE80 \u5F00\u59CB\u5BFC\u51FA\u8BF7\u6C42: ".concat(requestId));

                // 调用云函数导出Excel，传递最大数量参数
                exportExcelCloud = uniCloud.importObject('export-excel', {
                  customUI: true // 使用自定义UI
                }); // 根据不同的数据类型调用不同的方法
                if (!(_this4.dataType === 'feedback')) {
                  _context2.next = 74;
                  break;
                }
                _context2.next = 71;
                return exportExcelCloud.exportFeedbackData({
                  startTime: _startTime2,
                  endTime: _endTime2,
                  fileName: fileName,
                  maxCount: _this4.MAX_EXPORT_COUNT * 2,
                  // 给一定冗余，防止极端情况
                  allowLargeData: _this4.isLargeDataSet,
                  // 告知后端这是大数据量导出
                  requestId: requestId // 🔥 添加请求ID
                });
              case 71:
                result = _context2.sent;
                _context2.next = 77;
                break;
              case 74:
                _context2.next = 76;
                return exportExcelCloud.exportPatrolData({
                  startTime: _startTime2,
                  endTime: _endTime2,
                  fileName: fileName,
                  maxCount: _this4.MAX_EXPORT_COUNT * 2,
                  allowLargeData: _this4.isLargeDataSet,
                  filterType: _this4.filterType,
                  // 传递筛选类型
                  isDateRange: _this4.filterType !== 'day',
                  // 告知后端是否是日期范围查询
                  requestId: requestId // 🔥 添加请求ID
                });
              case 76:
                result = _context2.sent;
              case 77:
                console.log("\u2705 \u5BFC\u51FA\u8BF7\u6C42\u5B8C\u6210: ".concat(requestId), result);

                // 清除超时计时器
                if (_this4.exportTimeoutId) {
                  clearTimeout(_this4.exportTimeoutId);
                  _this4.exportTimeoutId = null;
                }

                // 完成进度到100%
                _this4.exportProgress = 100;
                uni.showLoading({
                  title: '导出完成 100%',
                  mask: true
                });
                setTimeout(function () {
                  _this4.showProgressBar = false;
                }, 1000);

                // 🔥 优化成功判断逻辑，只看code和fileUrl
                if (result && result.code === 0 && result.fileUrl) {
                  // 添加清除缓存参数，防止浏览器缓存
                  urlWithCache = _this4.addCacheBuster(result.fileUrl); // 下载并预览文件
                  uni.hideLoading();
                  uni.showToast({
                    title: '导出成功',
                    icon: 'success',
                    duration: 1500
                  });
                  setTimeout(function () {
                    _this4.openExcelPreview(urlWithCache);
                  }, 500);
                  console.log("\uD83C\uDF89 \u5BFC\u51FA\u6210\u529F: ".concat(requestId));
                } else {
                  uni.hideLoading();

                  // 特殊处理未来日期导出的错误
                  if (result && result.message && result.message.includes('未来日期')) {
                    uni.showModal({
                      title: '日期提示',
                      content: '系统仅支持导出当前日期及以前的巡视数据，未来日期的数据尚未产生，无法导出。',
                      showCancel: false
                    });
                  } else {
                    uni.showToast({
                      title: result && result.message || '导出失败',
                      icon: 'none'
                    });
                  }
                  console.error("\u274C \u5BFC\u51FA\u5931\u8D25: ".concat(requestId), result);
                }
                _context2.next = 94;
                break;
              case 85:
                _context2.prev = 85;
                _context2.t1 = _context2["catch"](59);
                // 清除超时计时器
                if (_this4.exportTimeoutId) {
                  clearTimeout(_this4.exportTimeoutId);
                  _this4.exportTimeoutId = null;
                }
                console.error('导出Excel异常:', _context2.t1);
                uni.hideLoading();

                // 🔥 优化错误提示，区分网络错误和其他错误
                errorMessage = '导出Excel失败';
                if (_context2.t1.message && _context2.t1.message.includes('timeout')) {
                  errorMessage = '网络超时，请检查网络连接后重试';
                } else if (_context2.t1.message && _context2.t1.message.includes('network')) {
                  errorMessage = '网络连接失败，请检查网络后重试';
                }
                uni.showToast({
                  title: errorMessage,
                  icon: 'none',
                  duration: 3000
                });
                _this4.showProgressBar = false;
              case 94:
                _context2.prev = 94;
                // 🔥 确保在所有情况下都重置导出状态
                _this4.exportLoading = false;
                console.log('🔄 导出状态已重置');
                return _context2.finish(94);
              case 98:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[7, 41], [59, 85, 94, 98]]);
      }))();
    },
    // 真实进度更新（配合云函数的处理阶段）
    startRealProgressUpdate: function startRealProgressUpdate() {
      var _this5 = this;
      var progressStage = 1;
      var stageProgress = 0;
      var updateProgress = function updateProgress() {
        // 如果已经不在导出状态，停止更新
        if (!_this5.exportLoading) return;
        var statusText = '导出中';
        var targetProgress = 0;

        // 根据阶段设置进度和状态
        switch (progressStage) {
          case 1:
            // 查询数据阶段 (0-25%)
            statusText = '查询数据中';
            stageProgress += 5 + Math.random() * 5; // 每次增加5-10%
            targetProgress = Math.min(25, stageProgress);
            if (targetProgress >= 25) {
              progressStage = 2;
              stageProgress = 25;
            }
            break;
          case 2:
            // 处理数据阶段 (25-50%)
            statusText = '处理数据中';
            stageProgress += 4 + Math.random() * 4; // 每次增加4-8%
            targetProgress = Math.min(50, stageProgress);
            if (targetProgress >= 50) {
              progressStage = 3;
              stageProgress = 50;
            }
            break;
          case 3:
            // 生成Excel阶段 (50-80%)
            statusText = '生成Excel中';
            stageProgress += 4 + Math.random() * 4; // 每次增加4-8%
            targetProgress = Math.min(80, stageProgress);
            if (targetProgress >= 80) {
              progressStage = 4;
              stageProgress = 80;
            }
            break;
          case 4:
            // 准备下载阶段 (80-92%)
            statusText = '准备下载';
            stageProgress += 3 + Math.random() * 3; // 每次增加3-6%
            targetProgress = Math.min(92, stageProgress);
            if (targetProgress >= 92) {
              progressStage = 5;
              stageProgress = 92;
            }
            break;
          case 5:
            // 即将完成阶段 (92-95%)
            statusText = '即将完成';
            stageProgress += 0.5 + Math.random() * 0.5; // 每次增加0.5-1%
            targetProgress = Math.min(95, stageProgress);
            break;
        }

        // 如果是大数据量，进度稍慢一些
        if (_this5.isLargeDataSet) {
          targetProgress *= 0.95;
        }
        _this5.exportProgress = targetProgress;
        uni.showLoading({
          title: "".concat(statusText, " ").concat(Math.floor(_this5.exportProgress), "%"),
          mask: true
        });

        // 计划下一次更新
        if (_this5.exportProgress < 95) {
          // 根据阶段调整更新频率 - 进一步缩短间隔
          var delay;
          if (progressStage <= 2) {
            delay = 200 + Math.random() * 150; // 前期200-350ms
          } else if (progressStage === 3) {
            delay = 250 + Math.random() * 200; // 中期250-450ms
          } else {
            delay = 300 + Math.random() * 250; // 后期300-550ms
          }

          setTimeout(updateProgress, delay);
        }
      };

      // 启动进度更新
      this.exportProgress = 0;
      setTimeout(updateProgress, 150); // 延迟150ms开始
    },
    // 添加防缓存参数到URL
    addCacheBuster: function addCacheBuster(url) {
      var separator = url.includes('?') ? '&' : '?';
      return "".concat(url).concat(separator, "_t=").concat(Date.now());
    },
    // 打开Excel预览
    openExcelPreview: function openExcelPreview(url) {
      this.openExcelPreviewWeixin(url);
    },
    // 添加URL参数辅助函数
    addQueryParam: function addQueryParam(url, name, value) {
      var separator = url.includes('?') ? '&' : '?';
      return "".concat(url).concat(separator).concat(name, "=").concat(encodeURIComponent(value));
    },
    // 微信小程序环境下打开Excel预览
    openExcelPreviewWeixin: function openExcelPreviewWeixin(url) {
      uni.showLoading({
        title: '正在打开...',
        mask: true
      });

      // 清除之前的临时文件
      try {
        var fs = uni.getFileSystemManager();
        var tmpDir = "".concat(wx.env.USER_DATA_PATH, "/excel_tmp/");

        // 检查目录是否存在，如果存在则清空
        try {
          fs.accessSync(tmpDir);
          var files = fs.readdirSync(tmpDir);
          files.forEach(function (file) {
            try {
              fs.unlinkSync("".concat(tmpDir).concat(file));
            } catch (e) {}
          });
        } catch (e) {
          // 目录不存在，创建它
          try {
            fs.mkdirSync(tmpDir, true);
          } catch (err) {}
        }
      } catch (e) {}

      // 使用新的临时文件名
      var tempFileName = "excel_".concat(Date.now(), ".xlsx");
      var tempFilePath = "".concat(wx.env.USER_DATA_PATH, "/excel_tmp/").concat(tempFileName);

      // 设置下载超时 (30秒)
      var downloadTimeoutId = setTimeout(function () {
        uni.hideLoading();
        uni.showToast({
          title: '下载超时，请重试',
          icon: 'none'
        });
      }, 30000);
      uni.downloadFile({
        url: url,
        filePath: tempFilePath,
        // 指定保存路径
        success: function success(res) {
          clearTimeout(downloadTimeoutId);
          if (res.statusCode === 200) {
            // 打开文件预览，确保显示右上角菜单
            uni.openDocument({
              filePath: res.filePath || tempFilePath,
              fileType: 'xlsx',
              showMenu: true,
              // 关键参数：显示右上角菜单按钮
              success: function success() {
                uni.hideLoading();
              },
              fail: function fail(err) {
                uni.hideLoading();
                uni.showToast({
                  title: '打开文档失败',
                  icon: 'none'
                });
              }
            });
          } else {
            uni.hideLoading();
            uni.showToast({
              title: '下载文件失败',
              icon: 'none'
            });
          }
        },
        fail: function fail(err) {
          clearTimeout(downloadTimeoutId);
          uni.hideLoading();
          uni.showToast({
            title: '下载文件失败',
            icon: 'none'
          });
        }
      });
    },
    // App环境下打开Excel预览
    openExcelPreviewApp: function openExcelPreviewApp(url) {
      uni.showLoading({
        title: '正在下载...',
        mask: true
      });

      // 生成唯一的临时文件名
      var tempFileName = "excel_".concat(Date.now(), ".xlsx");

      // 设置下载超时 (30秒)
      var downloadTimeoutId = setTimeout(function () {
        uni.hideLoading();
        uni.showToast({
          title: '下载超时，请重试',
          icon: 'none'
        });
      }, 30000);
      uni.downloadFile({
        url: url,
        success: function success(res) {
          clearTimeout(downloadTimeoutId);
          if (res.statusCode === 200) {
            var tempFilePath = res.tempFilePath;

            // 保存文件时使用唯一文件名
            uni.saveFile({
              tempFilePath: tempFilePath,
              success: function success(saveRes) {
                uni.hideLoading();
                uni.showToast({
                  title: '文件已保存',
                  icon: 'success'
                });

                // 打开文件
                uni.openDocument({
                  filePath: saveRes.savedFilePath,
                  success: function success() {}
                });
              },
              fail: function fail(err) {
                uni.hideLoading();
                uni.showToast({
                  title: '保存文件失败',
                  icon: 'none'
                });
              }
            });
          } else {
            uni.hideLoading();
            uni.showToast({
              title: '下载文件失败',
              icon: 'none'
            });
          }
        },
        fail: function fail(err) {
          clearTimeout(downloadTimeoutId);
          uni.hideLoading();
          uni.showToast({
            title: '下载文件失败',
            icon: 'none'
          });
        }
      });
    },
    // 处理时间段选择
    onStartTimeChange: function onStartTimeChange(e) {
      this.startTimeValue = e.detail.value;
      this.debounceFetchData();
    },
    onEndTimeChange: function onEndTimeChange(e) {
      this.endTimeValue = e.detail.value;
      this.debounceFetchData();
    },
    // 处理日期范围选择
    onStartDateChange: function onStartDateChange(e) {
      this.startDateValue = e.detail.value;
      this.debounceFetchData();
    },
    onEndDateChange: function onEndDateChange(e) {
      this.endDateValue = e.detail.value;
      this.debounceFetchData();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"]))

/***/ }),

/***/ 133:
/*!************************************************************************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/export-excel.vue?vue&type=style&index=0&id=9133d462&lang=scss&scoped=true& ***!
  \************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_style_index_0_id_9133d462_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./export-excel.vue?vue&type=style&index=0&id=9133d462&lang=scss&scoped=true& */ 134);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_style_index_0_id_9133d462_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_style_index_0_id_9133d462_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_style_index_0_id_9133d462_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_style_index_0_id_9133d462_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_export_excel_vue_vue_type_style_index_0_id_9133d462_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 134:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter_pkg/export-excel.vue?vue&type=style&index=0&id=9133d462&lang=scss&scoped=true& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[127,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ucenter_pkg/export-excel.js.map
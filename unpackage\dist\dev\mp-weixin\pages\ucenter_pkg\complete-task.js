(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/ucenter_pkg/complete-task"],{

/***/ 160:
/*!**********************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fucenter_pkg%2Fcomplete-task"} ***!
  \**********************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _completeTask = _interopRequireDefault(__webpack_require__(/*! ./pages/ucenter_pkg/complete-task.vue */ 161));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_completeTask.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 161:
/*!***************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/complete-task.vue ***!
  \***************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _complete_task_vue_vue_type_template_id_6ca1bbf8_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./complete-task.vue?vue&type=template&id=6ca1bbf8&scoped=true& */ 162);
/* harmony import */ var _complete_task_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./complete-task.vue?vue&type=script&lang=js& */ 164);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _complete_task_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _complete_task_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _complete_task_vue_vue_type_style_index_0_id_6ca1bbf8_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./complete-task.vue?vue&type=style&index=0&id=6ca1bbf8&scoped=true&lang=css& */ 166);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _complete_task_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _complete_task_vue_vue_type_template_id_6ca1bbf8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _complete_task_vue_vue_type_template_id_6ca1bbf8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "6ca1bbf8",
  null,
  false,
  _complete_task_vue_vue_type_template_id_6ca1bbf8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/ucenter_pkg/complete-task.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 162:
/*!**********************************************************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/complete-task.vue?vue&type=template&id=6ca1bbf8&scoped=true& ***!
  \**********************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_template_id_6ca1bbf8_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complete-task.vue?vue&type=template&id=6ca1bbf8&scoped=true& */ 163);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_template_id_6ca1bbf8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_template_id_6ca1bbf8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_template_id_6ca1bbf8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_template_id_6ca1bbf8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 163:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter_pkg/complete-task.vue?vue&type=template&id=6ca1bbf8&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    uniForms: function () {
      return Promise.all(/*! import() | uni_modules/uni-forms/components/uni-forms/uni-forms */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-forms/components/uni-forms/uni-forms")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-forms/components/uni-forms/uni-forms.vue */ 611))
    },
    uniFormsItem: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-forms/components/uni-forms-item/uni-forms-item */ "uni_modules/uni-forms/components/uni-forms-item/uni-forms-item").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue */ 620))
    },
    uniEasyinput: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput */ "uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue */ 551))
    },
    uniLoadMore: function () {
      return Promise.all(/*! import() | uni_modules/uni-load-more/components/uni-load-more/uni-load-more */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-load-more/components/uni-load-more/uni-load-more")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue */ 497))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.taskInfo ? _vm.formatTime(_vm.taskInfo.assignedTime) : null
  var g0 = _vm.formData.completionDescription.length
  var g1 = _vm.formData.completionEvidence.length
  var g2 = _vm.formData.completionEvidence.length
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        g0: g0,
        g1: g1,
        g2: g2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 164:
/*!****************************************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/complete-task.vue?vue&type=script&lang=js& ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complete-task.vue?vue&type=script&lang=js& */ 165);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 165:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter_pkg/complete-task.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, uniCloud) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      taskId: '',
      taskInfo: null,
      isLoading: true,
      isSubmitting: false,
      // 页面状态
      isPageVisible: true,
      formData: {
        completionDescription: '',
        completionEvidence: []
      },
      rules: {
        completionDescription: {
          rules: [{
            required: true,
            errorMessage: '请输入完成描述'
          }, {
            minLength: 2,
            errorMessage: '完成描述至少2个字符'
          }, {
            maxLength: 500,
            errorMessage: '完成描述不能超过500字符'
          }]
        }
      }
    };
  },
  onLoad: function onLoad(options) {
    this.taskId = options.id;
    if (this.taskId) {
      this.loadTaskInfo();
    } else {
      this.showErrorAndBack('参数错误');
    }
  },
  methods: {
    goBack: function goBack() {
      uni.navigateBack();
    },
    loadTaskInfo: function loadTaskInfo() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var res, userInfo, _res$result, _res$result2, _res$result3;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _this.isLoading = true;
                _context.prev = 1;
                _context.next = 4;
                return uniCloud.callFunction({
                  name: 'feedback-workflow',
                  data: {
                    action: 'get_detail',
                    id: _this.taskId
                  }
                });
              case 4:
                res = _context.sent;
                if (!(res.result && res.result.code === 0)) {
                  _context.next = 16;
                  break;
                }
                _this.taskInfo = res.result.data;

                // 验证是否为当前用户的任务
                userInfo = uniCloud.getCurrentUserInfo();
                if (!(_this.taskInfo.responsibleUserId !== userInfo.uid)) {
                  _context.next = 11;
                  break;
                }
                _this.showErrorAndBack('这不是您的任务');
                return _context.abrupt("return");
              case 11:
                if (!(_this.taskInfo.workflowStatus !== 'assigned_to_responsible')) {
                  _context.next = 14;
                  break;
                }
                _this.showErrorAndBack('任务状态不正确，无法提交完成');
                return _context.abrupt("return");
              case 14:
                _context.next = 20;
                break;
              case 16:
                if (!(((_res$result = res.result) === null || _res$result === void 0 ? void 0 : _res$result.code) === 404 && ((_res$result2 = res.result) === null || _res$result2 === void 0 ? void 0 : _res$result2.errorType) === 'RECORD_DELETED')) {
                  _context.next = 19;
                  break;
                }
                _this.handleRecordDeleted();
                return _context.abrupt("return");
              case 19:
                throw new Error(((_res$result3 = res.result) === null || _res$result3 === void 0 ? void 0 : _res$result3.message) || '获取任务信息失败');
              case 20:
                _context.next = 28;
                break;
              case 22:
                _context.prev = 22;
                _context.t0 = _context["catch"](1);
                if (!(_context.t0.message && _context.t0.message.includes('记录不存在'))) {
                  _context.next = 27;
                  break;
                }
                _this.handleRecordDeleted();
                return _context.abrupt("return");
              case 27:
                // 加载任务信息失败
                _this.showErrorAndBack(_context.t0.message || '加载任务失败');
              case 28:
                _context.prev = 28;
                _this.isLoading = false;
                return _context.finish(28);
              case 31:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[1, 22, 28, 31]]);
      }))();
    },
    showErrorAndBack: function showErrorAndBack(message) {
      uni.showModal({
        title: '提示',
        content: message,
        showCancel: false,
        success: function success() {
          uni.navigateBack();
        }
      });
    },
    chooseImage: function chooseImage() {
      var _this2 = this;
      var maxCount = 6 - this.formData.completionEvidence.length;
      uni.chooseImage({
        count: maxCount,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: function success(res) {
          _this2.uploadImages(res.tempFilePaths);
        },
        fail: function fail(error) {
          // 选择图片失败
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },
    uploadImages: function uploadImages(tempFilePaths) {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var i, tempFilePath, now, year, month, day, dateFolder, fileExt, uniqueFileName, cloudPath, result;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                if (!(!tempFilePaths || tempFilePaths.length === 0)) {
                  _context2.next = 2;
                  break;
                }
                return _context2.abrupt("return");
              case 2:
                _context2.prev = 2;
                i = 0;
              case 4:
                if (!(i < tempFilePaths.length)) {
                  _context2.next = 22;
                  break;
                }
                tempFilePath = tempFilePaths[i]; // 显示当前上传进度
                uni.showLoading({
                  title: "\u4E0A\u4F20\u4E2D (".concat(i + 1, "/").concat(tempFilePaths.length, ")"),
                  mask: true
                });

                // 获取当前日期，创建年月日格式的目录结构
                now = new Date();
                year = now.getFullYear();
                month = String(now.getMonth() + 1).padStart(2, '0');
                day = String(now.getDate()).padStart(2, '0');
                dateFolder = "".concat(year).concat(month).concat(day); // 生成唯一文件名
                fileExt = tempFilePath.includes('.') ? tempFilePath.substring(tempFilePath.lastIndexOf('.')) : '.jpg';
                uniqueFileName = "".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)).concat(fileExt);
                cloudPath = "feedback-evidence/".concat(dateFolder, "/").concat(uniqueFileName);
                _context2.next = 17;
                return uniCloud.uploadFile({
                  filePath: tempFilePath,
                  cloudPath: cloudPath,
                  cloudPathAsRealPath: true
                });
              case 17:
                result = _context2.sent;
                if (result.fileID) {
                  _this3.formData.completionEvidence.push(result.fileID);
                }
              case 19:
                i++;
                _context2.next = 4;
                break;
              case 22:
                uni.showToast({
                  title: '上传成功',
                  icon: 'success'
                });
                _context2.next = 28;
                break;
              case 25:
                _context2.prev = 25;
                _context2.t0 = _context2["catch"](2);
                uni.showToast({
                  title: '上传失败，请重试',
                  icon: 'none'
                });
              case 28:
                _context2.prev = 28;
                uni.hideLoading();
                return _context2.finish(28);
              case 31:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[2, 25, 28, 31]]);
      }))();
    },
    removeImage: function removeImage(index) {
      var _this4 = this;
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这张图片吗？',
        confirmColor: '#f44336',
        success: function () {
          var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(res) {
            var fileID;
            return _regenerator.default.wrap(function _callee3$(_context3) {
              while (1) {
                switch (_context3.prev = _context3.next) {
                  case 0:
                    if (!res.confirm) {
                      _context3.next = 18;
                      break;
                    }
                    fileID = _this4.formData.completionEvidence[index]; // 显示删除中的提示
                    uni.showLoading({
                      title: '删除中...',
                      mask: true
                    });
                    _context3.prev = 3;
                    if (!(fileID && typeof fileID === 'string' && (fileID.startsWith('cloud://') || fileID.startsWith('https://')))) {
                      _context3.next = 7;
                      break;
                    }
                    _context3.next = 7;
                    return uniCloud.callFunction({
                      name: 'delete-file',
                      data: {
                        fileList: [fileID]
                      }
                    });
                  case 7:
                    // 从数组中移除
                    _this4.formData.completionEvidence.splice(index, 1);
                    uni.showToast({
                      title: '删除成功',
                      icon: 'success',
                      duration: 1000
                    });
                    _context3.next = 15;
                    break;
                  case 11:
                    _context3.prev = 11;
                    _context3.t0 = _context3["catch"](3);
                    // 删除云存储图片失败

                    // 即使云存储删除失败，也从本地移除图片引用
                    _this4.formData.completionEvidence.splice(index, 1);
                    uni.showToast({
                      title: '已从列表移除',
                      icon: 'success',
                      duration: 1000
                    });
                  case 15:
                    _context3.prev = 15;
                    uni.hideLoading();
                    return _context3.finish(15);
                  case 18:
                  case "end":
                    return _context3.stop();
                }
              }
            }, _callee3, null, [[3, 11, 15, 18]]);
          }));
          function success(_x) {
            return _success.apply(this, arguments);
          }
          return success;
        }()
      });
    },
    previewImage: function previewImage(index) {
      uni.previewImage({
        current: index,
        urls: this.formData.completionEvidence
      });
    },
    formatTime: function formatTime(timestamp) {
      if (!timestamp) return '-';
      var date = new Date(timestamp);
      var year = date.getFullYear();
      var month = (date.getMonth() + 1).toString().padStart(2, '0');
      var day = date.getDate().toString().padStart(2, '0');
      var hour = date.getHours().toString().padStart(2, '0');
      var minute = date.getMinutes().toString().padStart(2, '0');
      return "".concat(year, "/").concat(month, "/").concat(day, " ").concat(hour, ":").concat(minute);
    },
    submitCompletion: function submitCompletion() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var confirmRes, res, _res$result4, _res$result5, _res$result6;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                if (!_this5.isSubmitting) {
                  _context4.next = 2;
                  break;
                }
                return _context4.abrupt("return");
              case 2:
                _context4.prev = 2;
                _context4.next = 5;
                return _this5.$refs.form.validate();
              case 5:
                _context4.next = 11;
                break;
              case 7:
                _context4.prev = 7;
                _context4.t0 = _context4["catch"](2);
                // 表单验证失败
                // 验证失败时，滚动到第一个错误字段
                setTimeout(function () {
                  uni.pageScrollTo({
                    selector: '.uni-forms-item-error',
                    duration: 300
                  });
                }, 100);
                return _context4.abrupt("return");
              case 11:
                _context4.next = 13;
                return new Promise(function (resolve) {
                  uni.showModal({
                    title: '确认提交',
                    content: '确定要提交任务完成吗？提交后将等待厂长确认，无法撤回。',
                    confirmText: '确定提交',
                    cancelText: '检查一下',
                    confirmColor: '#007aff',
                    success: resolve,
                    fail: function fail() {
                      return resolve({
                        confirm: false
                      });
                    }
                  });
                });
              case 13:
                confirmRes = _context4.sent;
                if (confirmRes.confirm) {
                  _context4.next = 16;
                  break;
                }
                return _context4.abrupt("return");
              case 16:
                _this5.isSubmitting = true;
                _context4.prev = 17;
                _context4.next = 20;
                return uniCloud.callFunction({
                  name: 'feedback-workflow',
                  data: {
                    action: 'responsible_complete',
                    id: _this5.taskId,
                    completionDescription: _this5.formData.completionDescription.trim(),
                    completionEvidence: _this5.formData.completionEvidence
                  }
                });
              case 20:
                res = _context4.sent;
                if (!(res.result && res.result.code === 0)) {
                  _context4.next = 29;
                  break;
                }
                // 提交成功
                uni.showToast({
                  title: '提交成功',
                  icon: 'success',
                  duration: 2000
                });

                // 发送事件通知，确保其他页面实时更新
                uni.$emit('task-completed', {
                  id: _this5.taskId,
                  type: 'responsible_complete'
                });
                uni.$emit('feedback-updated', {
                  id: _this5.taskId,
                  action: 'responsible_complete'
                });
                uni.$emit('ucenter-need-refresh', {
                  id: _this5.taskId
                });

                // 延迟返回，让用户看到成功提示
                setTimeout(function () {
                  uni.navigateBack({
                    delta: 1
                  });
                }, 2000);
                _context4.next = 33;
                break;
              case 29:
                if (!(((_res$result4 = res.result) === null || _res$result4 === void 0 ? void 0 : _res$result4.code) === 404 && ((_res$result5 = res.result) === null || _res$result5 === void 0 ? void 0 : _res$result5.errorType) === 'RECORD_DELETED')) {
                  _context4.next = 32;
                  break;
                }
                _this5.handleRecordDeleted();
                return _context4.abrupt("return");
              case 32:
                throw new Error(((_res$result6 = res.result) === null || _res$result6 === void 0 ? void 0 : _res$result6.message) || '提交失败');
              case 33:
                _context4.next = 41;
                break;
              case 35:
                _context4.prev = 35;
                _context4.t1 = _context4["catch"](17);
                if (!(_context4.t1.message && _context4.t1.message.includes('记录不存在'))) {
                  _context4.next = 40;
                  break;
                }
                _this5.handleRecordDeleted();
                return _context4.abrupt("return");
              case 40:
                // 提交失败
                uni.showModal({
                  title: '提交失败',
                  content: _context4.t1.message || '网络异常，请稍后重试',
                  showCancel: false,
                  confirmText: '我知道了',
                  confirmColor: '#007aff'
                });
              case 41:
                _context4.prev = 41;
                _this5.isSubmitting = false;
                return _context4.finish(41);
              case 44:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[2, 7], [17, 35, 41, 44]]);
      }))();
    },
    /**
     * 静默刷新任务数据
     */
    silentRefreshTask: function silentRefreshTask() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var res, userInfo;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                _context5.next = 3;
                return uniCloud.callFunction({
                  name: 'feedback-workflow',
                  data: {
                    action: 'get_detail',
                    id: _this6.taskId
                  }
                });
              case 3:
                res = _context5.sent;
                if (!(res.result && res.result.code === 0)) {
                  _context5.next = 11;
                  break;
                }
                _this6.taskInfo = res.result.data;

                // 验证是否为当前用户的任务
                userInfo = uniCloud.getCurrentUserInfo();
                if (!(_this6.taskInfo.responsibleUserId !== userInfo.uid)) {
                  _context5.next = 10;
                  break;
                }
                // 如果不再是当前用户的任务，可能已被重新指派，返回上级页面
                uni.showModal({
                  title: '提示',
                  content: '任务状态已变更，即将返回上级页面',
                  showCancel: false,
                  success: function success() {
                    uni.navigateBack();
                  }
                });
                return _context5.abrupt("return");
              case 10:
                // 检查任务状态，如果已完成则提示并返回
                if (_this6.taskInfo.workflowStatus === 'final_completed') {
                  uni.showModal({
                    title: '任务已完成',
                    content: '该任务已被厂长确认完成',
                    showCancel: false,
                    success: function success() {
                      uni.navigateBack();
                    }
                  });
                } else if (_this6.taskInfo.workflowStatus === 'terminated') {
                  uni.showModal({
                    title: '任务已终止',
                    content: '该任务流程已被终止',
                    showCancel: false,
                    success: function success() {
                      uni.navigateBack();
                    }
                  });
                }
              case 11:
                _context5.next = 16;
                break;
              case 13:
                _context5.prev = 13;
                _context5.t0 = _context5["catch"](0);
                console.log('静默刷新任务失败:', _context5.t0);
              case 16:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 13]]);
      }))();
    },
    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange: function handleVisibilityChange() {
      // 统一由角标管理器处理跨设备更新
    },
    /**
     * 智能判断是否需要刷新数据
     */
    shouldRefreshOnCrossDeviceUpdate: function shouldRefreshOnCrossDeviceUpdate(data) {
      // 如果页面不可见，不需要刷新
      if (!this.isPageVisible) {
        console.log('任务完成页面不可见，跳过跨设备更新');
        return false;
      }

      // 任务完成页面对重要更新类型立即响应，不受时间限制
      var importantTypes = ['workflow_status_changed', 'feedback_submitted', 'feedback_deleted'];
      var hasImportantUpdate = data.updateTypes && data.updateTypes.some(function (type) {
        return importantTypes.includes(type);
      });

      // 如果更新的反馈记录包含当前正在查看的任务，需要刷新
      if (data.feedbackIds && data.feedbackIds.includes(this.taskId)) {
        console.log('任务完成页面检测到当前任务更新，需要刷新');
        return true;
      }

      // 如果更新类型包含重要操作，立即刷新
      if (hasImportantUpdate) {
        console.log('任务完成页面检测到重要更新类型，需要刷新:', data.updateTypes);
        return true;
      }
      console.log('任务完成页面跨设备更新判断：不需要刷新');
      return false;
    },
    /**
     * 处理记录已被删除的情况
     * 当其他用户删除了正在操作的任务记录时，优雅地处理这种边界情况
     */
    handleRecordDeleted: function handleRecordDeleted() {
      // 清除loading状态
      uni.hideLoading();
      this.isSubmitting = false;
      this.isLoading = false;

      // 显示友好的提示信息
      uni.showModal({
        title: '任务已删除',
        content: '您正在操作的任务记录已被其他用户删除，将返回上一页面。',
        showCancel: false,
        confirmText: '确定',
        success: function success() {
          // 触发相关页面刷新事件
          uni.$emit('refresh-todo-list');
          uni.$emit('feedback-updated');
          uni.$emit('ucenter-need-refresh');

          // 立即更新角标
          var todoBadgeManager = __webpack_require__(/*! @/utils/todo-badge.js */ 44).default;
          if (todoBadgeManager) {
            todoBadgeManager.forceRefresh();
          }

          // 返回上一页面
          uni.navigateBack({
            delta: 1,
            fail: function fail() {
              // 如果无法返回，跳转到首页
              uni.switchTab({
                url: '/pages/index/index'
              });
            }
          });
        }
      });
    }
  },
  /**
   * 页面生命周期
   */
  onShow: function onShow() {
    this.isPageVisible = true;
    // 不再进行独立的跨设备检查，统一由角标管理器处理
  },
  onHide: function onHide() {
    this.isPageVisible = false;
  },
  created: function created() {
    var _this7 = this;
    // 监听角标管理器的跨设备更新事件
    uni.$on('cross-device-update-detected', function (data) {
      if (data.silent && _this7.taskId) {
        // 智能判断是否需要刷新
        var shouldRefresh = _this7.shouldRefreshOnCrossDeviceUpdate(data);
        if (shouldRefresh) {
          console.log('任务完成页面收到跨设备更新通知，静默刷新数据');
          // 静默刷新数据
          _this7.silentRefreshTask();
        }
      }
    });

    // 监听页面可见性变化
    // 统一由角标管理器处理跨设备更新
  },
  beforeDestroy: function beforeDestroy() {
    // 移除事件监听
    uni.$off('cross-device-update-detected');

    // 统一由角标管理器处理跨设备更新
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"]))

/***/ }),

/***/ 166:
/*!************************************************************************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/complete-task.vue?vue&type=style&index=0&id=6ca1bbf8&scoped=true&lang=css& ***!
  \************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_style_index_0_id_6ca1bbf8_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complete-task.vue?vue&type=style&index=0&id=6ca1bbf8&scoped=true&lang=css& */ 167);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_style_index_0_id_6ca1bbf8_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_style_index_0_id_6ca1bbf8_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_style_index_0_id_6ca1bbf8_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_style_index_0_id_6ca1bbf8_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_task_vue_vue_type_style_index_0_id_6ca1bbf8_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 167:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter_pkg/complete-task.vue?vue&type=style&index=0&id=6ca1bbf8&scoped=true&lang=css& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[160,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ucenter_pkg/complete-task.js.map
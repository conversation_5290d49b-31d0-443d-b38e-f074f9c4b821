<template>
  <view class="uni-container">
    <view class="db-container">
      <!-- 问题描述区域 -->
      <view class="description-section" v-if="hasInitialData">
        <view class="description-header">
          <text class="description-title">问题描述</text>
        </view>
        <view class="description-content">
          <text class="description-text">{{ formData.description || '暂无问题描述' }}</text>
        </view>
      </view>

      <!-- 骨架屏 -->
      <template v-if="!hasInitialData">
        <view class="skeleton-loading">
          <view class="skeleton-steps-section">
            <view v-for="i in 5" :key="i" class="skeleton-step"></view>
          </view>
          <view class="skeleton-approval-section"></view>
        </view>
      </template>
      
      <template v-else>
        <!-- 步骤条 - 基于新工作流状态 -->
        <view class="steps-section">
          <view class="steps">
            <view v-for="(step, index) in effectiveSteps"
                  :key="index" 
                  class="step" 
                  :class="{ 
                    'active': index === currentStep,
                    'completed': getStepStatus(index) === 'completed',
                    'rejected': getStepStatus(index) === 'rejected',
                    'meeting': getStepStatus(index) === 'meeting',
                    'not-executed': getStepStatus(index) === 'not_executed'
                  }">
              <view class="step-content">
                <view class="step-icon">
                  <text class="icon-number">{{ index + 1 }}</text>
                  <view class="icon-line" v-if="index !== effectiveSteps.length - 1"></view>
                </view>
                <view class="step-info">
                  <text class="step-title">{{ step.text }}</text>
                  <!-- 已完成状态显示 -->
                  <view class="step-status-info" v-if="getStepStatus(index) !== 'pending'">
                    <text class="status-text" 
                          :class="{
                            'status-text-approved': getStepStatus(index) === 'completed' || getStepStatus(index) === 'approved',
                            'status-text-rejected': getStepStatus(index) === 'rejected',
                            'status-text-meeting': getStepStatus(index) === 'meeting',
                            'status-text-not-executed': getStepStatus(index) === 'not_executed'
                          }">
                      {{ getRoleTitle(getRoleByStep(index)) }}{{ getStepStatusText(index) }}
                    </text>
                    <text class="time-text" v-if="getStepTime(index)">
                      {{ formatTime(getStepTime(index)) }}
                    </text>
                  </view>
                  <!-- 等待状态显示 -->
                  <text class="step-waiting" v-else-if="index === currentStep">
                    <template v-if="index <= 2">等待{{ getRoleTitle(getRoleByStep(index)) }}中...</template>
                    <template v-else-if="index === 3">{{ getStepStatusText(index) }}</template>
                    <template v-else-if="index === 4">{{ getStepStatusText(index) }}</template>
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 主管审核区域 -->
        <view class="approval-section"
              :class="{ 
                'active': currentStep === 0,
                'disabled': currentStep !== 0 || isProcessTerminated
              }" 
              v-if="uniIDHasRole('supervisor') || uniIDHasRole('admin')">
          <view class="approval-header">
            <text class="approval-title">主管审核</text>
            <view class="approval-status" :class="{
              'status-approved': getSupervisorStatus() === 'approved',
              'status-rejected': getSupervisorStatus() === 'rejected',
              'status-pending': getSupervisorStatus() === 'pending',
              'status-meeting': getSupervisorStatus() === 'meeting',
              'status-not-executed': getSupervisorStatus() === 'not_executed'
            }">
              <uni-icons v-if="getSupervisorStatus() === 'approved'" type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
              <uni-icons v-if="getSupervisorStatus() === 'rejected'" type="closeempty" size="16" color="#F44336"></uni-icons>
              <uni-icons v-if="getSupervisorStatus() === 'meeting'" type="calendar" size="16" color="#9C27B0"></uni-icons>
              <uni-icons v-if="getSupervisorStatus() === 'not_executed'" type="minus" size="16" color="#999"></uni-icons>
              <text>{{ getSupervisorStatusText() }}</text>
            </view>
          </view>
          <view class="approval-options">
            <label class="radio-item" @click="handleApproval('supervisor', true)">
              <radio :checked="selectedOptions.supervisor === true" 
                     :disabled="currentStep !== 0"
                     :name="`supervisor-approve`"/>
              <text>同意</text>
            </label>
            <label class="radio-item" @click="handleApproval('supervisor', false)">
              <radio :checked="selectedOptions.supervisor === false" 
                     :disabled="currentStep !== 0"
                     :name="`supervisor-reject`"/>
              <text>不同意</text>
            </label>
            <label class="radio-item" @click="handleApproval('supervisor', 'meeting')">
              <radio :checked="selectedOptions.supervisor === 'meeting'" 
                     :disabled="currentStep !== 0"
                     :name="`supervisor-meeting`"/>
              <text>例会讨论</text>
            </label>
          </view>
          <!-- 审核理由区域：未审核时显示输入框，已审核时显示内容 -->
          <view class="reason-box">
            <text class="reason-label">审核理由：</text>
            <template v-if="getSupervisorStatus() === 'pending'">
              <textarea class="reason-input"
                        v-model="tempReasonInput.supervisor"
                        :disabled="currentStep !== 0"
                        placeholder="请输入审核理由" 
                        maxlength="200"/>
              <text class="character-count">{{ (tempReasonInput.supervisor || '').length }}/200</text>
            </template>
            <view v-else class="submitted-reason">
              <text class="reason-content">{{ getSupervisorReasonText() }}</text>
            </view>
          </view>
        </view>

        <!-- 副厂长审核区域 -->
        <view class="approval-section"
              :class="{ 
                'active': currentStep === 1,
                'disabled': currentStep !== 1 || isProcessTerminated
              }" 
              v-if="uniIDHasRole('PM') || uniIDHasRole('admin')">
          <view class="approval-header">
            <text class="approval-title">副厂长审核</text>
            <view class="approval-status" :class="{
              'status-approved': getPMStatus() === 'approved',
              'status-rejected': getPMStatus() === 'rejected',
              'status-pending': getPMStatus() === 'pending',
              'status-not-executed': getPMStatus() === 'not_executed'
            }">
              <uni-icons v-if="getPMStatus() === 'approved'" type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
              <uni-icons v-if="getPMStatus() === 'rejected'" type="closeempty" size="16" color="#F44336"></uni-icons>
              <uni-icons v-if="getPMStatus() === 'not_executed'" type="minus" size="16" color="#999"></uni-icons>
              <text>{{ getPMStatusText() }}</text>
            </view>
          </view>
          <view class="approval-options">
            <label class="radio-item" @click="handleApproval('PM', true)">
              <radio :checked="selectedOptions.PM === true" :disabled="currentStep !== 1" :name="`PM-approve`"/>
              <text>同意</text>
            </label>
            <label class="radio-item" @click="handleApproval('PM', false)">
              <radio :checked="selectedOptions.PM === false" :disabled="currentStep !== 1" :name="`PM-reject`"/>
              <text>不同意</text>
            </label>
          </view>
          <!-- 审核理由区域：未审核时显示输入框，已审核时显示内容 -->
          <view class="reason-box">
            <text class="reason-label">审核理由：</text>
            <template v-if="getPMStatus() === 'pending'">
              <textarea class="reason-input"
                        v-model="tempReasonInput.pm"
                        :disabled="currentStep !== 1"
                        placeholder="请输入审核理由" 
                        maxlength="200"/>
              <text class="character-count">{{ (tempReasonInput.pm || '').length }}/200</text>
            </template>
            <view v-else class="submitted-reason">
              <text class="reason-content">{{ getPMReasonText() }}</text>
            </view>
          </view>
        </view>

        <!-- 厂长审核区域 -->
        <view class="approval-section"
              :class="{ 
                'active': currentStep === 2,
                'disabled': currentStep !== 2 || isProcessTerminated
              }" 
              v-if="uniIDHasRole('GM') || uniIDHasRole('admin')">
          <view class="approval-header">
            <text class="approval-title">厂长审核</text>
            <view class="approval-status" :class="{
              'status-approved': getGMStatus() === 'approved',
              'status-rejected': getGMStatus() === 'rejected',
              'status-pending': getGMStatus() === 'pending',
              'status-not-executed': getGMStatus() === 'not_executed'
            }">
              <uni-icons v-if="getGMStatus() === 'approved'" type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
              <uni-icons v-if="getGMStatus() === 'rejected'" type="closeempty" size="16" color="#F44336"></uni-icons>
              <uni-icons v-if="getGMStatus() === 'not_executed'" type="minus" size="16" color="#999"></uni-icons>
              <text>{{ getGMStatusText() }}</text>
            </view>
          </view>
          <view class="approval-options">
            <label class="radio-item" @click="handleApproval('GM', true)">
              <radio :checked="selectedOptions.GM === true" :disabled="currentStep !== 2" :name="`GM-approve`"/>
              <text>同意</text>
            </label>
            <label class="radio-item" @click="handleApproval('GM', false)">
              <radio :checked="selectedOptions.GM === false" :disabled="currentStep !== 2" :name="`GM-reject`"/>
              <text>不同意</text>
            </label>
          </view>
          <!-- 审核理由区域：未审核时显示输入框，已审核时显示内容 -->
          <view class="reason-box">
            <text class="reason-label">审核理由：</text>
            <template v-if="getGMStatus() === 'pending'">
              <textarea class="reason-input"
                        v-model="tempReasonInput.gm"
                        :disabled="currentStep !== 2"
                        placeholder="请输入审核理由" 
                        maxlength="200"/>
              <text class="character-count">{{ (tempReasonInput.gm || '').length }}/200</text>
            </template>
            <view v-else class="submitted-reason">
              <text class="reason-content">{{ getGMReasonText() }}</text>
            </view>
          </view>
        </view>

        <!-- 厂长指派负责人区域 -->
        <view class="approval-section"
              :class="{ 
                'active': formData.workflowStatus === 'gm_approved_pending_assign',
                'disabled': formData.workflowStatus !== 'gm_approved_pending_assign'
              }" 
              v-if="(uniIDHasRole('GM') || uniIDHasRole('admin')) && formData.workflowStatus === 'gm_approved_pending_assign' && !formData.responsibleUserId">
          <view class="approval-header">
            <text class="approval-title">指派负责人</text>
            <view class="approval-status status-pending">
              <uni-icons type="person" size="16" color="#3b82f6"></uni-icons>
              <text>待指派负责人</text>
            </view>
          </view>
          <view class="assign-section">
            <view class="assign-picker">
              <text class="picker-label">选择负责人：</text>
              <picker @change="onResponsiblePickerChange" :value="selectedResponsibleIndex" :range="responsibleUsers" range-key="nickname" :disabled="responsibleUsers.length === 0">
                <view class="uni-input">{{ getResponsiblePickerDisplayText() }}</view>
              </picker>
            </view>
            <view class="reason-box">
              <text class="reason-label">指派说明（可选）：</text>
              <textarea class="reason-input"
                        v-model="assignReason"
                        placeholder="请输入指派说明" 
                        maxlength="200"/>
              <text class="character-count">{{ assignReason.length }}/200</text>
            </view>
            <button class="assign-btn" type="primary" @click="handleAssignResponsible" :disabled="!selectedResponsibleUser">确认指派</button>
          </view>
        </view>

        <!-- 负责人工作状态显示区域 -->
        <view class="approval-section"
              :class="{ 
                'active': formData.workflowStatus === 'assigned_to_responsible',
                'disabled': formData.workflowStatus !== 'assigned_to_responsible' && formData.workflowStatus !== 'completed_by_responsible' && formData.workflowStatus !== 'final_completed'
              }"
              v-if="formData.responsibleUserId && (formData.workflowStatus === 'assigned_to_responsible' || formData.workflowStatus === 'completed_by_responsible' || formData.workflowStatus === 'final_completed')">
          <view class="approval-header">
            <text class="approval-title">负责人执行</text>
            <view class="approval-status" :class="{
              'status-pending': formData.workflowStatus === 'assigned_to_responsible',
              'status-approved': formData.workflowStatus === 'completed_by_responsible' || formData.workflowStatus === 'final_completed'
            }">
              <uni-icons v-if="formData.workflowStatus === 'assigned_to_responsible'" type="gear" size="16" color="#3b82f6"></uni-icons>
              <uni-icons v-else type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
              <text v-if="formData.workflowStatus === 'assigned_to_responsible'">等待负责人完成工作</text>
              <text v-else-if="formData.workflowStatus === 'completed_by_responsible'">负责人已完成，待确认</text>
              <text v-else-if="formData.workflowStatus === 'final_completed'">负责人已完成</text>
            </view>
          </view>
          <view class="responsible-info">
            <text class="responsible-text">已指派给：{{ getResponsibleUserName() }}</text>
            <text class="assign-time" v-if="formData.assignedTime">指派时间：{{ formatTime(formData.assignedTime) }}</text>
            <text class="completion-time" v-if="formData.completedByResponsibleTime">完成时间：{{ formatTime(formData.completedByResponsibleTime) }}</text>
            <!-- 显示退回理由 -->
            <view class="reject-reason-display" v-if="formData.rejectReason">
              <view class="reject-reason-header">
                <text class="reject-reason-label">厂长退回理由：</text>
                <text class="reject-time" v-if="formData.rejectedTime">{{ formatTime(formData.rejectedTime) }}</text>
              </view>
              <view class="reject-reason-content">
                <text class="reject-reason-text">{{ formData.rejectReason }}</text>
              </view>
            </view>
          </view>
          
          <!-- 已完成的工作显示 -->
          <view class="completed-work" v-if="formData.workflowStatus === 'completed_by_responsible' || formData.workflowStatus === 'final_completed'">
            <view class="completion-description-item" v-if="formData.responsibleCompletionDescription">
              <view class="completion-description-header">
                <text class="description-label">完成说明：</text>
              </view>
              <view class="completion-description-content">
                <text class="description-text">{{ formData.responsibleCompletionDescription }}</text>
              </view>
            </view>
            <!-- 完成凭证图片 -->
            <view class="completion-evidence" v-if="formData.responsibleCompletionEvidence && formData.responsibleCompletionEvidence.length > 0">
              <view class="evidence-images">
                <image v-for="(image, index) in formData.responsibleCompletionEvidence" 
                       :key="index" 
                       :src="image" 
                       mode="aspectFill" 
                       class="evidence-image"
                       @click="previewImage(image)"></image>
              </view>
            </view>
          </view>
          
          <!-- 负责人操作区域 - 仅在待完成状态显示 -->
          <view class="responsible-action" v-if="formData.workflowStatus === 'assigned_to_responsible' && (isCurrentUserResponsible() || uniIDHasRole('admin'))">
            <view class="completion-form">
              <view class="reason-box">
                <text class="reason-label">完成情况说明：</text>
                <textarea class="reason-input"
                          v-model="completionDescription"
                          placeholder="请描述工作完成情况" 
                          maxlength="500"/>
                <text class="character-count">{{ completionDescription.length }}/500</text>
              </view>
              <view class="evidence-section">
                <text class="evidence-label">完成凭证（可选）：</text>
                <view class="evidence-upload">
                  <view class="image-list">
                    <!-- 已上传的图片 -->
                    <view v-for="(image, index) in completionEvidence" :key="index" class="image-item">
                      <image :src="image" mode="aspectFill" @click="previewImage(image)"></image>
                      <view class="image-delete" @click="removeImage(index)">×</view>
                    </view>
                    <!-- 上传按钮 -->
                    <view class="upload-item" v-if="completionEvidence.length < 3">
                      <button class="upload-btn" @click="uploadEvidence">
                        <uni-icons type="camera" size="18" class="upload-icon"></uni-icons>
                        <text class="upload-text">上传图片</text>
                      </button>
                    </view>
                  </view>
                </view>
              </view>
              <button class="complete-btn" type="primary" @click="handleResponsibleComplete" :disabled="!completionDescription">提交完成</button>
            </view>
          </view>
        </view>

        <!-- 厂长最终确认区域 -->
        <view class="approval-section"
              :class="{ 
                'active': formData.workflowStatus === 'completed_by_responsible',
                'disabled': formData.workflowStatus !== 'completed_by_responsible'
              }" 
              v-if="(uniIDHasRole('GM') || uniIDHasRole('admin')) && formData.workflowStatus === 'completed_by_responsible'">
          <view class="approval-header">
            <text class="approval-title">厂长最终确认</text>
            <view class="approval-status status-pending">
              <uni-icons type="checkmarkempty" size="16" color="#3b82f6"></uni-icons>
              <text>待最终确认</text>
            </view>
          </view>
          <view class="completion-review">
            <view class="completion-info">
              <text class="responsible-text">负责人：{{ getResponsibleUserName() }}</text>
              <text class="completion-time" v-if="formData.completedByResponsibleTime">完成时间：{{ formatTime(formData.completedByResponsibleTime) }}</text>
            </view>
            <view class="reason-box">
              <text class="reason-label">确认意见：</text>
              <textarea class="reason-input"
                        v-model="finalConfirmReason"
                        placeholder="请输入确认意见" 
                        maxlength="200"/>
              <text class="character-count">{{ finalConfirmReason.length }}/200</text>
            </view>
            <view class="reason-box">
              <text class="reason-label">退回理由（如需退回）：</text>
              <textarea class="reason-input"
                        v-model="rejectReason"
                        placeholder="请输入退回理由，说明需要重做的具体问题" 
                        maxlength="200"/>
              <text class="character-count">{{ rejectReason.length }}/200</text>
            </view>
            <view class="final-actions">
              <button class="reject-btn" type="default" @click="handleRejectCompletion" :disabled="!rejectReason">退回重做</button>
              <button class="confirm-btn" type="primary" @click="handleFinalConfirm" :disabled="!finalConfirmReason">确认完成</button>
            </view>
          </view>
        </view>

        <!-- 最终完成状态显示 -->
        <view class="approval-section"
              v-if="formData.workflowStatus === 'final_completed'">
          <view class="approval-header">
            <text class="approval-title">流程完成</text>
            <view class="approval-status status-approved">
              <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
              <text>已完成</text>
            </view>
          </view>
          <view class="approval-content">
            <view class="completion-summary">
              <view class="summary-container">
                <view class="summary-item-container">
                  <view class="summary-header">
                    <text class="summary-label">完成说明：</text>
                  </view>
                  <view class="summary-content">
                    <text class="summary-text">该问题已完成整改并通过最终确认</text>
                  </view>
                </view>
                
                <view class="summary-item-container" v-if="formData.finalCompletedTime">
                  <view class="summary-header">
                    <text class="summary-label">完成时间：</text>
                  </view>
                  <view class="summary-content">
                    <text class="summary-text">{{ formatTime(formData.finalCompletedTime) }}</text>
                  </view>
                </view>
                
                <view class="summary-item-container">
                  <view class="summary-header">
                    <text class="summary-label">负责人：</text>
                  </view>
                  <view class="summary-content">
                    <text class="summary-text">{{ getResponsibleUserName() }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 审核理由汇总区域 -->
        <view class="approval-summary-section">
          <view class="approval-summary-header">
            <text class="approval-summary-title">审核理由汇总</text>
          </view>
          <view class="approval-summary-content">
            <!-- 主管审核理由 -->
            <view class="summary-item">
              <view class="summary-item-header">
                <text class="summary-role">主管：</text>
                <text class="summary-status" :class="{
                  'status-approved': getSupervisorStatus() === 'approved',
                  'status-rejected': getSupervisorStatus() === 'rejected',
                  'status-pending': getSupervisorStatus() === 'pending',
                  'status-meeting': getSupervisorStatus() === 'meeting'
                }">{{ getSupervisorStatusText() }}</text>
              </view>
              <view class="summary-reason">
                <text class="reason-content" :class="{
                  'reason-approved': getSupervisorStatus() === 'approved',
                  'reason-rejected': getSupervisorStatus() === 'rejected',
                  'reason-meeting': getSupervisorStatus() === 'meeting',
                  'reason-not-executed': getSupervisorStatus() === 'not_executed'
                }">{{ getSupervisorReasonText() }}</text>
              </view>
            </view>
            
            <!-- 副厂长审核理由 -->
            <view class="summary-item">
              <view class="summary-item-header">
                <text class="summary-role">副厂长：</text>
                <text class="summary-status" :class="{
                  'status-approved': getPMStatus() === 'approved',
                  'status-rejected': getPMStatus() === 'rejected',
                  'status-pending': getPMStatus() === 'pending',
                  'status-not-executed': getPMStatus() === 'not_executed'
                }">{{ getPMStatusText() }}</text>
              </view>
              <view class="summary-reason">
                <text class="reason-content" :class="{
                  'reason-approved': getPMStatus() === 'approved',
                  'reason-rejected': getPMStatus() === 'rejected',
                  'reason-not-executed': getPMStatus() === 'not_executed'
                }">{{ getPMReasonText() }}</text>
              </view>
            </view>
            
            <!-- 厂长审核理由 -->
            <view class="summary-item">
              <view class="summary-item-header">
                <text class="summary-role">厂长：</text>
                <text class="summary-status" :class="{
                  'status-approved': getGMStatus() === 'approved',
                  'status-rejected': getGMStatus() === 'rejected',
                  'status-pending': getGMStatus() === 'pending',
                  'status-not-executed': getGMStatus() === 'not_executed'
                }">{{ getGMStatusText() }}</text>
              </view>
              <view class="summary-reason">
                <text class="reason-content" :class="{
                  'reason-approved': getGMStatus() === 'approved',
                  'reason-rejected': getGMStatus() === 'rejected',
                  'reason-not-executed': getGMStatus() === 'not_executed'
                }">{{ getGMReasonText() }}</text>
              </view>
            </view>
            
            <!-- 负责人指派说明 -->
            <view class="summary-item" v-if="formData.responsibleUserId">
              <view class="summary-item-header">
                <text class="summary-role">指派说明：</text>
                <text class="summary-status status-info">已指派</text>
              </view>
              <view class="summary-reason">
                <text class="reason-content reason-info">{{ formData.assignReason || '无' }}</text>
              </view>
            </view>
            
            <!-- 退回重做记录 -->
            <view class="summary-item" v-if="formData.rejectReason">
              <view class="summary-item-header">
                <text class="summary-role">厂长退回：</text>
                <text class="summary-status status-rejected">已退回</text>
              </view>
              <view class="summary-reason">
                <text class="reason-content reason-rejected">{{ formData.rejectReason }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 重置按钮 -->
        <view class="reset-section" v-if="uniIDHasRole('GM') || uniIDHasRole('admin')">
          <button class="reset-btn" type="warn" @click="handleReset">重置审核流程</button>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
const db = uniCloud.database();

export default {
  components: {
    uniIcons: () => import('@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue')
  },
  data() {
    return {
      formDataId: '',
      currentStep: 0,
      _isLoading: false,
      hasInitialData: false,
      isPageVisible: true,
      // 添加选中状态跟踪
      selectedOptions: {
        supervisor: null,
        PM: null,
        GM: null
      },
      // 临时理由输入（用于待审核状态的理由输入）
      tempReasonInput: {
        supervisor: '',
        pm: '',
        gm: ''
      },
      formData: {
        // 基础字段
        name: '',
        project: '',
        description: '',
        images: [],
        createTime: null,
        createUserId: '',
        isAdopted: false,
        isCompleted: false,
        // 工作流字段
        workflowStatus: 'pending_supervisor',
        meetingRequired: false,
        terminatedBy: '',
        terminatedTime: null,
        lastUpdateTime: null,
        remark: '',
        updateTrigger: '',
        // 负责人字段
        responsibleUserId: null,
        assignedTime: null,
        assignReason: '',
        responsibleCompletionDescription: null,
        responsibleCompletionEvidence: [],
        completedByResponsibleTime: null,
        finalCompletedTime: null,
        rejectReason: '',
        rejectedTime: null,
        // 操作历史
        actionHistory: []
      },
      // 新增工作流相关数据
      responsibleUsers: [],
      selectedResponsibleIndex: 0,
      selectedResponsibleUser: null,
      assignReason: '',
      completionDescription: '',
      completionEvidence: [],
      finalConfirmReason: '',
      rejectReason: '',
      steps: [
        { text: '主管审核' },
        { text: '副厂长审核' },
        { text: '厂长审核' },
        { text: '指派负责人' },
        { text: '执行结果' }
      ]
    }
  },

  computed: {
    // 统一显示5步工作流
    effectiveSteps() {
      return this.steps;
    },
    
    isProcessTerminated() {
      return this.formData.workflowStatus === 'terminated';
    },
    isProcessComplete() {
      return this.formData.workflowStatus === 'final_completed';
    },

    // 加载状态
    isLoading() {
      return this._isLoading;
    }
  },

  async onLoad(option) {
    if (option && option.id) {
      this.formDataId = option.id;
      // 先加载负责人列表，然后再加载详情，确保选择器正常显示
      await this.loadResponsibleUsers();
      this.getDetail(option.id);
    }
  },

  methods: {
    getRoleByStep(step) {
      const roles = ['supervisor', 'PM', 'GM', 'assign', 'final'];
      return roles[step];
    },

    getRoleTitle(role) {
      const titles = {
        supervisor: '主管',
        PM: '副厂长',
        GM: '厂长',
        assign: '指派负责人',
        final: '执行结果'
      };
      return titles[role];
    },

    // 新的状态获取方法 - 基于workflowStatus
    getSupervisorStatus() {
      const status = this.formData.workflowStatus;
      const terminatedBy = this.formData.terminatedBy;
      
      if (status === 'pending_supervisor') return 'pending';
      if (status === 'approved_supervisor' || status === 'pending_pm' || 
          status === 'approved_pm' || status === 'pending_gm' || 
          status === 'gm_approved_pending_assign' || 
          status === 'assigned_to_responsible' || status === 'completed_by_responsible' || status === 'final_completed') {
        // 检查是否是例会讨论
        return this.formData.meetingRequired ? 'meeting' : 'approved';
      }
      // 流程终止的情况
      if (status === 'terminated') {
        if (terminatedBy === 'supervisor') {
          return 'rejected';
        } else if (terminatedBy === 'PM' || terminatedBy === 'GM') {
          // 后续步骤拒绝，主管应该显示已同意
          return this.formData.meetingRequired ? 'meeting' : 'approved';
        }
      }
      return 'pending';
    },

    getPMStatus() {
      const status = this.formData.workflowStatus;
      const terminatedBy = this.formData.terminatedBy;
      
      // 如果主管都没通过，副厂长显示未执行
      if (status === 'pending_supervisor') return 'not_executed';
      if (status === 'terminated' && terminatedBy === 'supervisor') return 'not_executed';
      
      if (status === 'approved_supervisor' || status === 'pending_pm') return 'pending';
      if (status === 'approved_pm' || status === 'pending_gm' || status === 'gm_approved_pending_assign' || 
          status === 'assigned_to_responsible' || status === 'completed_by_responsible' || status === 'final_completed') {
        return 'approved';
      }
      // 只有副厂长拒绝时才显示rejected
      if (status === 'terminated' && terminatedBy === 'PM') {
        return 'rejected';
      }
      // 如果厂长拒绝，但副厂长已经通过了，应该显示已同意
      if (status === 'terminated' && terminatedBy === 'GM') {
        return 'approved';
      }
      return 'not_executed';
    },

    getGMStatus() {
      const status = this.formData.workflowStatus;
      const terminatedBy = this.formData.terminatedBy;
      
      // 如果前面步骤都没通过，厂长显示未执行
      if (status === 'pending_supervisor' || status === 'approved_supervisor' || status === 'pending_pm') return 'not_executed';
      if (status === 'terminated' && (terminatedBy === 'supervisor' || terminatedBy === 'PM')) {
        return 'not_executed';
      }
      
      if (status === 'approved_pm' || status === 'pending_gm') return 'pending';
      if (status === 'gm_approved_pending_assign' || status === 'assigned_to_responsible' || 
          status === 'completed_by_responsible' || status === 'final_completed') {
        return 'approved';
      }
      // 只有厂长拒绝时才显示rejected
      if (status === 'terminated' && terminatedBy === 'GM') {
        return 'rejected';
      }
      return 'not_executed';
    },

    // 获取步骤状态
    getStepStatus(stepIndex) {
      const roles = ['supervisor', 'PM', 'GM', 'assign', 'final'];
      const role = roles[stepIndex];
      
      if (role === 'assign') {
        if (this.formData.workflowStatus === 'gm_approved_pending_assign') return 'pending';
        if (this.formData.workflowStatus === 'assigned_to_responsible' || 
            this.formData.workflowStatus === 'completed_by_responsible' || 
            this.formData.workflowStatus === 'final_completed') return 'completed';
        if (this.formData.workflowStatus === 'terminated') return 'not_executed';
        return 'not_executed';
      } else if (role === 'final') {
        if (this.formData.workflowStatus === 'final_completed') return 'completed';
        if (this.formData.workflowStatus === 'completed_by_responsible') return 'pending';
        if (this.formData.workflowStatus === 'terminated') return 'rejected';
        return 'not_executed';
      } else {
        const status = this[`get${role.charAt(0).toUpperCase() + role.slice(1)}Status`]();
        if (status === 'approved') return 'completed';
        if (status === 'meeting') return 'meeting';
        if (status === 'rejected') return 'rejected';
        if (status === 'not_executed') return 'not_executed';
        return 'pending';
      }
    },

    // 获取状态文本
    getSupervisorStatusText() {
      const status = this.getSupervisorStatus();
      if (status === 'pending') return '待审核';
      if (status === 'approved') return '已同意';
      if (status === 'rejected') return '已拒绝';
      if (status === 'meeting') return '例会讨论';
      if (status === 'not_executed') return '未执行';
      return '待审核';
    },

    getPMStatusText() {
      const status = this.getPMStatus();
      if (status === 'pending') return '待审核';
      if (status === 'approved') return '已同意';
      if (status === 'rejected') return '已拒绝';
      if (status === 'not_executed') return '未执行';
      return '待审核';
    },

    getGMStatusText() {
      const status = this.getGMStatus();
      if (status === 'pending') return '待审核';
      if (status === 'approved') return '已同意';
      if (status === 'rejected') return '已拒绝';
      if (status === 'not_executed') return '未执行';
      return '待审核';
    },

    // 获取步骤状态文本
    getStepStatusText(stepIndex) {
      const roles = ['supervisor', 'PM', 'GM', 'assign', 'final'];
      const role = roles[stepIndex];
      
      if (role === 'assign') {
        if (this.formData.workflowStatus === 'gm_approved_pending_assign') return '待指派负责人';
        if (this.formData.workflowStatus === 'assigned_to_responsible') return '已指派，待完成';
        if (this.formData.workflowStatus === 'completed_by_responsible') return '负责人已完成';
        if (this.formData.workflowStatus === 'final_completed') return '已指派完成';
        if (this.formData.workflowStatus === 'terminated') return '未执行';
        return '未执行';
      } else if (role === 'final') {
        if (this.formData.workflowStatus === 'final_completed') return '流程完成';
        if (this.formData.workflowStatus === 'terminated') return '流程终止';
        if (this.formData.workflowStatus === 'assigned_to_responsible') return '负责人执行中';
        if (this.formData.workflowStatus === 'completed_by_responsible') return '待厂长确认';
        if (this.formData.workflowStatus === 'gm_approved_pending_assign') return '待指派负责人';
        return '未执行';
      } else {
        return this[`get${role.charAt(0).toUpperCase() + role.slice(1)}StatusText`]();
      }
    },

    // 获取步骤时间 - 重置后直接从actionHistory获取
    getStepTime(stepIndex) {
      const roles = ['supervisor', 'PM', 'GM', 'assign', 'final'];
      const role = roles[stepIndex];
      
      if (role === 'assign') {
        return this.formData.assignedTime;
      } else if (role === 'final') {
        return this.formData.finalCompletedTime || this.formData.completedByResponsibleTime || this.formData.terminatedTime;
      } else {
        // 从actionHistory中获取审核时间
        if (this.formData.actionHistory && this.formData.actionHistory.length > 0) {
          const actionMap = {
            'supervisor': ['supervisor_approve', 'supervisor_reject', 'supervisor_meeting'],
            'PM': ['pm_approve', 'pm_reject'],
            'GM': ['gm_approve', 'gm_reject']
          };
          
          const roleActions = actionMap[role] || [];
          const lastAction = this.formData.actionHistory
            .filter(action => roleActions.includes(action.action))
            .sort((a, b) => b.timestamp - a.timestamp)[0];
          
          if (lastAction) {
            return lastAction.timestamp;
          }
        }
      }
      return null;
    },

    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    },

    // 获取主管理由文本 - 从actionHistory获取
    getSupervisorReasonText() {
      if (this.formData.actionHistory && this.formData.actionHistory.length > 0) {
        const supervisorAction = this.formData.actionHistory
          .filter(action => ['supervisor_approve', 'supervisor_reject', 'supervisor_meeting'].includes(action.action))
          .sort((a, b) => b.timestamp - a.timestamp)[0];
        
        if (supervisorAction && supervisorAction.reason) {
          return supervisorAction.reason;
      }
      }
      
      return '无';
    },

    // 获取副厂长理由文本 - 从actionHistory获取
    getPMReasonText() {
      if (this.formData.actionHistory && this.formData.actionHistory.length > 0) {
        const pmAction = this.formData.actionHistory
          .filter(action => ['pm_approve', 'pm_reject'].includes(action.action))
          .sort((a, b) => b.timestamp - a.timestamp)[0];
        
        if (pmAction && pmAction.reason) {
          return pmAction.reason;
      }
      }
      
      return '无';
    },

    // 获取厂长理由文本 - 从actionHistory获取
    getGMReasonText() {
      if (this.formData.actionHistory && this.formData.actionHistory.length > 0) {
        const gmAction = this.formData.actionHistory
          .filter(action => ['gm_approve', 'gm_reject'].includes(action.action))
          .sort((a, b) => b.timestamp - a.timestamp)[0];
        
        if (gmAction && gmAction.reason) {
          return gmAction.reason;
      }
      }
      
      return '无';
    },

    async getDetail(id) {
      try {
        // 防止重复加载
        if (this._isLoading) return;
        this._isLoading = true;
        
        uni.showLoading({ 
          title: '加载中...',
          mask: true 
        });
        
        // 使用本地缓存加速页面显示
        const cacheKey = `approval_${id}`;
        try {
          const cachedData = uni.getStorageSync(cacheKey);
          if (cachedData && Date.now() - cachedData.timestamp < 60000) { // 缓存1分钟
            // 使用缓存数据快速显示
            Object.assign(this.formData, cachedData.data);
            // 确保description字段被正确恢复
            if (cachedData.data && cachedData.data.description) {
              this.formData.description = cachedData.data.description;
            }
            this.updateSelectedOptions();
            this.updateCurrentStep();
            this.hasInitialData = true; // 标记已有初始数据
          }
        } catch (e) {}
        
        const res = await uniCloud.callFunction({
          name: 'feedback-workflow',
          data: {
            action: 'get_detail',
            id: id
          }
        });
        
        if (res.result.code === 0 && res.result.data) {
          const data = res.result.data;
          
          // 使用你的测试数据标准结构
          this.formData = {
            // 基础字段
            name: data.name,
            project: data.project,
            description: data.description || '',
            images: data.images || [],
            createTime: data.createTime,
            createUserId: data.createUserId,
            isAdopted: data.isAdopted,
            isCompleted: data.isCompleted,
            // 工作流字段
            workflowStatus: data.workflowStatus || 'pending_supervisor',
            meetingRequired: data.meetingRequired || false,
            terminatedBy: data.terminatedBy || '',
            terminatedTime: data.terminatedTime,
            lastUpdateTime: data.lastUpdateTime,
            remark: data.remark || '',
            updateTrigger: data.updateTrigger || '',
            // 负责人字段
            responsibleUserId: data.responsibleUserId,
            assignedTime: data.assignedTime,
            assignReason: data.assignReason || '',
            responsibleCompletionDescription: data.responsibleCompletionDescription,
            responsibleCompletionEvidence: data.responsibleCompletionEvidence || [],
            completedByResponsibleTime: data.completedByResponsibleTime,
            finalCompletedTime: data.finalCompletedTime,
            rejectReason: data.rejectReason || '',
            rejectedTime: data.rejectedTime,
            // 操作历史
            actionHistory: data.actionHistory || []
          };
          
          this.hasInitialData = true; // 标记已有数据
          
          // 设置选中状态，包括例会讨论
          this.updateSelectedOptions();
          this.updateCurrentStep();
          
          // 保存到本地缓存（异步执行，不阻塞UI）
          this.$nextTick(() => {
            try {
              const cacheKey = `approval_${this.formDataId}`;
              uni.setStorageSync(cacheKey, {
                timestamp: Date.now(),
                data: this.formData
              });
            } catch (e) {}
          });
        } else {
          // 检查是否是记录已删除的错误
          if (res.result?.code === 404 && res.result?.errorType === 'RECORD_DELETED') {
            this.handleRecordDeleted();
            return;
          }
          throw new Error(res.result?.message || '获取详情失败');
        }
      } catch (err) {
        // 检查是否是网络错误导致的记录不存在
        if (err.message && err.message.includes('记录不存在')) {
          this.handleRecordDeleted();
          return;
        }
        uni.showModal({
          content: err.message || '请求服务失败',
          showCancel: false
        });
      } finally {
        uni.hideLoading();
        this._isLoading = false;
      }
    },

    updateCurrentStep() {
      const status = this.formData.workflowStatus;
      const terminatedBy = this.formData.terminatedBy;
      
      // 基于新工作流状态确定当前步骤
      if (status === 'pending_supervisor') {
        this.currentStep = 0; // 主管审核
      } else if (status === 'approved_supervisor' || status === 'pending_pm') {
        this.currentStep = 1; // 副厂长审核
      } else if (status === 'approved_pm' || status === 'pending_gm') {
        this.currentStep = 2; // 厂长审核
      } else if (status === 'gm_approved_pending_assign') {
        this.currentStep = 3; // 指派负责人
      } else if (status === 'assigned_to_responsible' || 
                 status === 'completed_by_responsible') {
        this.currentStep = 4; // 工作流程结果
      } else if (status === 'final_completed') {
        this.currentStep = -1; // 流程完成，没有激活步骤
      } else if (status === 'terminated') {
        // 流程终止时，没有激活步骤
        this.currentStep = -1;
      } else {
        // 默认为第一步
        this.currentStep = 0;
      }
    },

    updateSelectedOptions() {
      // 根据新工作流状态设置选中状态
      const supervisorStatus = this.getSupervisorStatus();
      if (supervisorStatus === 'approved') {
        this.$set(this.selectedOptions, 'supervisor', true);
      } else if (supervisorStatus === 'rejected') {
        this.$set(this.selectedOptions, 'supervisor', false);
      } else if (supervisorStatus === 'meeting') {
        this.$set(this.selectedOptions, 'supervisor', 'meeting');
      } else {
        this.$set(this.selectedOptions, 'supervisor', null);
      }

      const pmStatus = this.getPMStatus();
      if (pmStatus === 'approved') {
        this.$set(this.selectedOptions, 'PM', true);
      } else if (pmStatus === 'rejected') {
        this.$set(this.selectedOptions, 'PM', false);
      } else {
        this.$set(this.selectedOptions, 'PM', null);
      }

      const gmStatus = this.getGMStatus();
      if (gmStatus === 'approved') {
        this.$set(this.selectedOptions, 'GM', true);
      } else if (gmStatus === 'rejected') {
        this.$set(this.selectedOptions, 'GM', false);
      } else {
        this.$set(this.selectedOptions, 'GM', null);
      }
    },

    		/**
		 * 处理审核操作 - 新工作流系统的核心审核逻辑
		 * 
		 * 支持的审核决策：
		 * - true: 同意/通过
		 * - false: 不同意/拒绝  
		 * - 'meeting': 提交例会讨论（仅主管可用）
		 * 
		 * 工作流状态转换：
		 * - 主管审核：pending_supervisor → pending_pm/terminated/meeting_required
		 * - 副厂长审核：pending_pm → pending_gm/terminated
		 * - 厂长审核：pending_gm → gm_approved_pending_assign/terminated
		 * 
		 * @param {string} role - 审核角色 ('supervisor'/'PM'/'GM')
		 * @param {boolean|string} decision - 审核决策 (true/false/'meeting')
		 */
		async handleApproval(role, decision) {
			// 防止重复提交，确保审核操作的原子性
			if (this._isProcessing) return;
			this._isProcessing = true;
      
      // 先设置选中状态
      this.$set(this.selectedOptions, role, decision);
      
      try {
        // 理由验证已移除 - 现在所有操作都有默认理由，不需要强制验证
        const roles = ['supervisor', 'PM', 'GM'];
        const currentRoleIndex = roles.indexOf(role);
        
        if (currentRoleIndex !== this.currentStep) {
          uni.showModal({
            title: '提示',
            content: `当前不是${role}审核步骤，请按流程顺序进行审核`,
            showCancel: false,
            confirmText: '确定'
          });
          
          this._isProcessing = false;
          // 重置选中状态
          this.$set(this.selectedOptions, role, null);
          return;
        }

        // 根据决策类型设置动作描述
        let action;
        if (decision === true) {
          action = '同意';
        } else if (decision === false) {
          action = '不同意';
        } else if (decision === 'meeting') {
          action = '提交例会讨论';
        }
        
        // 直接显示确认对话框
        const confirmRes = await uni.showModal({
          title: '确认操作',
          content: `确定要${action}吗？`,
          confirmText: '确定',
          cancelText: '取消'
        });

        if (!confirmRes.confirm) {
          // 用户点击取消，重置选中状态
          this._isProcessing = false;
          // 重置radio选中状态
          this.$set(this.selectedOptions, role, null);
          return;
        }

        uni.showLoading({ 
          title: '处理中...',
          mask: true
        });

        let res;
        
        if (decision === 'meeting') {
          // 例会讨论 - 使用临时输入或默认理由
          let finalReason = this.tempReasonInput.supervisor;
          
          // 如果没有输入理由，使用默认理由
          if (!finalReason || finalReason.trim() === '') {
            finalReason = '需要例会讨论';
          }
          
          res = await uniCloud.callFunction({
            name: 'feedback-workflow',
            data: {
              action: 'supervisor_meeting',
              id: this.formDataId,
              reason: finalReason
            }
          });
        } else {
          let workflowAction;
          if (role === 'supervisor') {
            workflowAction = decision ? 'supervisor_approve' : 'supervisor_reject';
          } else if (role === 'PM') {
            workflowAction = decision ? 'pm_approve' : 'pm_reject';
          } else if (role === 'GM') {
            if (decision === false) {
              // 厂长拒绝
              workflowAction = 'gm_reject';
            } else {
              // 厂长同意
              workflowAction = 'gm_approve';
            }
          }
          
          // 使用临时理由输入或默认理由
          const tempFieldMap = {
            'supervisor': 'supervisor',
            'PM': 'pm',
            'GM': 'gm'
          };
          
          let finalReason = this.tempReasonInput[tempFieldMap[role]] || '';
          
          // 如果没有输入理由，使用默认理由
          if (!finalReason || finalReason.trim() === '') {
            if (decision === true) {
              finalReason = '同意';
            } else {
              finalReason = '不同意';
            }
          }
          
          res = await uniCloud.callFunction({
            name: 'feedback-workflow',
            data: {
              action: workflowAction,
              id: this.formDataId,
              reason: finalReason
            }
          });
        }

        if (res.result && res.result.code === 0) {
          // 更新本地状态 - 基于云函数返回的新状态
          if (res.result.data && res.result.data.newStatus) {
            this.formData.workflowStatus = res.result.data.newStatus;
            // 如果云函数返回了terminatedBy字段，直接使用
            if (res.result.data.terminatedBy) {
              this.$set(this.formData, 'terminatedBy', res.result.data.terminatedBy);
            }
          }
          
          // 对于例会讨论，设置标记
          if (decision === 'meeting') {
            this.$set(this.formData, 'meetingRequired', true);
          }
          
          // 批量更新云函数返回的所有字段（确保数据一致性）
          if (res.result.data) {
            const returnedData = res.result.data;
            
            // 核心工作流字段（必须更新）
            if (returnedData.actionHistory) {
              this.formData.actionHistory = returnedData.actionHistory;
            }
            if (returnedData.lastUpdateTime) {
              this.formData.lastUpdateTime = returnedData.lastUpdateTime;
            }
            if (returnedData.remark !== undefined) {
              this.formData.remark = returnedData.remark;
            }
            if (returnedData.isAdopted !== undefined) {
              this.formData.isAdopted = returnedData.isAdopted;
            }
            if (returnedData.isCompleted !== undefined) {
              this.formData.isCompleted = returnedData.isCompleted;
            }
            if (returnedData.meetingRequired !== undefined) {
              this.formData.meetingRequired = returnedData.meetingRequired;
            }
            
            // 指派相关字段
            if (returnedData.responsibleUserId !== undefined) {
              this.formData.responsibleUserId = returnedData.responsibleUserId;
            }
            if (returnedData.assignedTime !== undefined) {
              this.formData.assignedTime = returnedData.assignedTime;
            }
            if (returnedData.assignReason !== undefined) {
              this.formData.assignReason = returnedData.assignReason;
            }
            
            // 完成相关字段
            if (returnedData.completedByResponsibleTime !== undefined) {
              this.formData.completedByResponsibleTime = returnedData.completedByResponsibleTime;
            }
            if (returnedData.responsibleCompletionDescription !== undefined) {
              this.formData.responsibleCompletionDescription = returnedData.responsibleCompletionDescription;
            }
            if (returnedData.responsibleCompletionEvidence !== undefined) {
              this.formData.responsibleCompletionEvidence = returnedData.responsibleCompletionEvidence;
            }
            if (returnedData.finalCompletedTime !== undefined) {
              this.formData.finalCompletedTime = returnedData.finalCompletedTime;
            }
            
            // 终止相关字段
            if (returnedData.terminatedBy !== undefined) {
              this.formData.terminatedBy = returnedData.terminatedBy;
            }
            if (returnedData.terminatedTime !== undefined) {
              this.formData.terminatedTime = returnedData.terminatedTime;
            }
          }
          
          // 清空对应的临时理由输入
          const tempFieldMap = {
            'supervisor': 'supervisor',
            'PM': 'pm',
            'GM': 'gm'
          };
          if (tempFieldMap[role]) {
            this.tempReasonInput[tempFieldMap[role]] = '';
          }
          
          this.updateCurrentStep();
          
          // 更新选中状态 - 必须在updateCurrentStep之后
          this.updateSelectedOptions();
          
          // 强制触发视图更新，确保理由和时间立即显示
          this.$forceUpdate();
          
          // 额外确保理由显示立即更新（防止缓存问题）
          this.$nextTick(() => {
            this.$forceUpdate();
          });
          
          // 更新本地缓存（异步执行）
          this.$nextTick(() => {
            try {
              const cacheKey = `approval_${this.formDataId}`;
              uni.setStorageSync(cacheKey, {
                timestamp: Date.now(),
                data: this.formData
              });
            } catch (e) {}
          });

          uni.hideLoading();
          
          // 立即更新角标 - 审核操作后需要实时更新
          const todoBadgeManager = require('@/utils/todo-badge.js').default;
          if (todoBadgeManager) {
            // 使用强制刷新确保立即更新
            await todoBadgeManager.forceRefresh();
            
            // 强制同步角标状态，确保显示正确
            setTimeout(() => {
              todoBadgeManager.forceSyncBadge();
            }, 300);
          }
          
          // 触发刷新事件，通知其他页面更新
          uni.$emit('refresh-todo-list');
          // 发送feedback-updated事件，通知列表页面刷新
          uni.$emit('feedback-updated');
          
          // 添加用户中心页面刷新事件，确保用户中心页面能实时更新
          uni.$emit('ucenter-need-refresh', { id: this.formDataId });
          
          // 延迟更新角标，确保在返回TabBar页面时正确显示
          setTimeout(() => {
            if (todoBadgeManager) {
              todoBadgeManager.updateTodoCountImmediately();
            }
          }, 500);
          
          uni.showToast({
            title: action + '成功',
            icon: 'success',
            duration: 1000  // 进一步减少到1秒
          });
        } else {
          // 检查是否是记录已删除的错误
          if (res.result?.code === 404 && res.result?.errorType === 'RECORD_DELETED') {
            this.handleRecordDeleted();
            return;
          }
          throw new Error(res.result?.message || '操作失败');
        }
      } catch (err) {
        uni.hideLoading();
        // 检查是否是网络错误导致的记录不存在
        if (err.message && err.message.includes('记录不存在')) {
          this.handleRecordDeleted();
          return;
        }
        uni.showModal({
          content: err.message || '请求服务失败',
          showCancel: false
        });
      } finally {
        this._isProcessing = false;
      }
    },

    async handleReset() {
      // 防止重复提交
      if (this._isProcessing) return;
      this._isProcessing = true;
      
      try {
        const confirmRes = await uni.showModal({
          title: '确认重置',
          content: '确定要重置所有审批状态吗？这将清除所有审批记录！',
          confirmText: '确定',
          cancelText: '取消'
        });

        if (!confirmRes.confirm) {
          this._isProcessing = false;
          return;
        }

        uni.showLoading({ 
          title: '处理中...',
          mask: true
        });

        const res = await uniCloud.callFunction({
          name: 'feedback-workflow',
          data: {
            action: 'reset',
            id: this.formDataId
          }
        });

        if (res.result && res.result.code === 0) {
          // 批量更新云函数返回的所有字段（确保数据一致性）
          if (res.result.data) {
            const returnedData = res.result.data;
            
            // 保存基本信息
          const preservedFields = {
            name: this.formData.name,
            project: this.formData.project,
            description: this.formData.description,
            images: this.formData.images || [],
            createTime: this.formData.createTime,
            createUserId: this.formData.createUserId
          };
          
            // 重置为新工作流初始状态，使用云函数返回的数据
          this.formData = {
            ...preservedFields,
              // 使用云函数返回的核心字段
              workflowStatus: returnedData.workflowStatus || 'pending_supervisor',
              isAdopted: returnedData.isAdopted || false,
              isCompleted: returnedData.isCompleted || false,
              meetingRequired: returnedData.meetingRequired || false,
              lastUpdateTime: returnedData.lastUpdateTime || Date.now(),
              remark: returnedData.remark || '',
              actionHistory: returnedData.actionHistory || [],
              // 重置的字段设为初始值
              terminatedBy: '',
              terminatedTime: null,
            responsibleUserId: null,
            assignedTime: null,
              assignReason: '',
            responsibleCompletionDescription: null,
              responsibleCompletionEvidence: [],
            completedByResponsibleTime: null,
              finalCompletedTime: null,
              rejectReason: '',
              rejectedTime: null,
              updateTrigger: ''
          };
          }
          
          // 重置选中状态
          this.selectedOptions = {
            supervisor: null,
            PM: null,
            GM: null
          };
          
          this.updateCurrentStep();
          
          // 强制触发视图更新，确保重置状态立即显示
          this.$forceUpdate();
          
          // 额外确保显示立即更新（防止缓存问题）
          this.$nextTick(() => {
            this.$forceUpdate();
          });
          
          // 更新缓存（异步执行）
          this.$nextTick(() => {
            try {
              const cacheKey = `approval_${this.formDataId}`;
              uni.setStorageSync(cacheKey, {
                timestamp: Date.now(),
                data: this.formData
              });
            } catch (e) {}
          });

          uni.hideLoading();
          
          // 立即更新角标 - 重置后需要实时更新
          const todoBadgeManager = require('@/utils/todo-badge.js').default;
          if (todoBadgeManager) {
            await todoBadgeManager.forceRefresh();
            
            // 强制同步角标状态
            setTimeout(() => {
              todoBadgeManager.forceSyncBadge();
            }, 300);
          }
          
          // 触发刷新事件
          uni.$emit('refresh-todo-list');
          // 发送feedback-updated事件，通知列表页面刷新
          uni.$emit('feedback-updated');
          
          uni.showToast({
            title: '重置成功',
            icon: 'success',
            duration: 1000  // 进一步减少到1秒
          });
        } else {
          // 检查是否是记录已删除的错误
          if (res.result?.code === 404 && res.result?.errorType === 'RECORD_DELETED') {
            this.handleRecordDeleted();
            return;
          }
          throw new Error(res.result?.message || '重置失败');
        }
      } catch (err) {
        uni.hideLoading();
        // 检查是否是网络错误导致的记录不存在
        if (err.message && err.message.includes('记录不存在')) {
          this.handleRecordDeleted();
          return;
        }
        uni.showModal({
          content: err.message || '重置失败',
          showCancel: false
        });
      } finally {
        this._isProcessing = false;
      }
    },

    getFinalStatusText() {
      if (this.formData.workflowStatus === 'final_completed') {
        return '审核通过';
      } else if (this.formData.workflowStatus === 'terminated') {
        return '审核未通过';
      } else {
        return '审核中';
      }
    },

    async loadResponsibleUsers() {
      try {
        const res = await uniCloud.callFunction({
          name: 'feedback-workflow',
          data: {
            action: 'get_responsible_users'
          }
        });
        
        if (res.result && res.result.code === 0) {
          this.responsibleUsers = res.result.data || [];
          // 确保初始状态正确设置
          if (this.responsibleUsers.length > 0 && this.selectedResponsibleIndex === 0) {
            this.selectedResponsibleUser = null;
          }
        }
      } catch (error) {
        // 加载负责人列表失败，静默处理
        console.warn('加载负责人列表失败:', error);
      }
    },

    getResponsiblePickerDisplayText() {
      if (this.responsibleUsers.length === 0) {
        return '加载负责人列表中...';
      }
      if (this.selectedResponsibleUser) {
        return this.selectedResponsibleUser.nickname || this.selectedResponsibleUser.username || '未知用户';
      }
      return '请选择负责人';
    },

    onResponsiblePickerChange(e) {
      this.selectedResponsibleIndex = e.target.value;
      this.selectedResponsibleUser = this.responsibleUsers[this.selectedResponsibleIndex];
    },

    async handleAssignResponsible() {
      if (!this.selectedResponsibleUser) {
        uni.showToast({ title: '请选择负责人', icon: 'none' });
        return;
      }

      try {
        uni.showLoading({ title: '指派中...', mask: true });

        // 在清空表单之前保存需要的数据
        const selectedUserId = this.selectedResponsibleUser._id;
        const assignReason = this.assignReason || '无';

        const res = await uniCloud.callFunction({
          name: 'feedback-workflow',
          data: {
            action: 'gm_assign',
            id: this.formDataId,
            responsibleUserId: selectedUserId,
            reason: assignReason
          }
        });

        if (res.result && res.result.code === 0) {
          // 批量更新云函数返回的所有字段（确保数据一致性）
          if (res.result.data) {
            const returnedData = res.result.data;
            
            // 核心工作流字段（必须更新）
            if (returnedData.actionHistory) {
              this.formData.actionHistory = returnedData.actionHistory;
            }
            if (returnedData.lastUpdateTime) {
              this.formData.lastUpdateTime = returnedData.lastUpdateTime;
            }
            if (returnedData.remark !== undefined) {
              this.formData.remark = returnedData.remark;
            }
            if (returnedData.workflowStatus !== undefined) {
              this.formData.workflowStatus = returnedData.workflowStatus;
            }
            
            // 指派相关字段
            if (returnedData.responsibleUserId !== undefined) {
              this.formData.responsibleUserId = returnedData.responsibleUserId;
            }
            if (returnedData.assignedTime !== undefined) {
              this.formData.assignedTime = returnedData.assignedTime;
            }
            if (returnedData.assignReason !== undefined) {
              this.formData.assignReason = returnedData.assignReason;
            }
          }
          
          // 更新当前步骤，确保界面正确显示
          this.updateCurrentStep();
          
          // 强制触发视图更新，确保指派信息立即显示
          this.$forceUpdate();
          
          // 额外确保显示立即更新（防止缓存问题）
          this.$nextTick(() => {
            this.$forceUpdate();
          });
          
          // 清空指派表单
          this.assignReason = '';
          this.selectedResponsibleUser = null;
          this.selectedResponsibleIndex = 0;
          
          uni.hideLoading();
          uni.showToast({ title: '指派成功', icon: 'success' });
          
          // 发送任务指派事件，通知负责人任务页面刷新
          uni.$emit('task-assigned', {
            taskId: this.formDataId,
            responsibleUserId: selectedUserId,
            assignedTime: Date.now()
          });
          
          // 发送feedback-updated事件，通知列表页面刷新
          uni.$emit('feedback-updated');
          
          // 添加用户中心页面刷新事件，确保用户中心页面能实时更新
          uni.$emit('ucenter-need-refresh', { id: this.formDataId });
          
          // 刷新页面数据
          this.getDetail(this.formDataId);
        } else {
          throw new Error(res.result?.message || '指派失败');
        }
      } catch (error) {
        uni.hideLoading();
        uni.showModal({
          content: error.message || '指派失败',
          showCancel: false
        });
      }
    },

    isCurrentUserResponsible() {
      const currentUserId = uni.getStorageSync('uni-id-token-payload')?.uid;
      return this.formData.responsibleUserId === currentUserId;
    },

    getResponsibleUserName() {
      if (!this.formData.responsibleUserId) return '';
      
      // 如果有responsibleUsers列表，从中查找
      const user = this.responsibleUsers.find(u => u._id === this.formData.responsibleUserId);
      if (user) return user.nickname || user.username || '未知用户';
      
      // 否则返回默认值
      return this.formData.responsibleUserName || '负责人';
    },

    async uploadEvidence() {
      try {
        const res = await uni.chooseImage({
          count: 3 - this.completionEvidence.length,
          sizeType: ['compressed'],
          sourceType: ['camera', 'album']
        });

        for (let i = 0; i < res.tempFilePaths.length; i++) {
          const tempFilePath = res.tempFilePaths[i];
          
          // 显示当前上传进度
          uni.showLoading({
            title: `上传中 (${i + 1}/${res.tempFilePaths.length})`,
            mask: true
          });
          
          // 获取当前日期，创建年月日格式的目录结构
          const now = new Date();
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const day = String(now.getDate()).padStart(2, '0');
          const dateFolder = `${year}${month}${day}`;
          
          // 生成唯一文件名
          const fileExt = tempFilePath.includes('.') ? tempFilePath.substring(tempFilePath.lastIndexOf('.')) : '.jpg';
          const uniqueFileName = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}${fileExt}`;
          
          const cloudPath = `feedback-evidence/${dateFolder}/${uniqueFileName}`;
          const uploadRes = await uniCloud.uploadFile({
            filePath: tempFilePath,
            cloudPath: cloudPath,
            cloudPathAsRealPath: true
          });
          
          if (uploadRes.fileID) {
            this.completionEvidence.push(uploadRes.fileID);
          }
        }
        
        uni.showToast({ title: '上传成功', icon: 'success' });
      } catch (error) {
        uni.showToast({ title: '上传失败', icon: 'none' });
      } finally {
        uni.hideLoading();
      }
    },

    removeImage(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这张图片吗？',
        confirmColor: '#f44336',
        success: async (res) => {
          if (res.confirm) {
            const fileID = this.completionEvidence[index];
            
            // 显示删除中的提示
            uni.showLoading({
              title: '删除中...',
              mask: true
            });
            
            try {
              // 如果是云存储文件，调用云函数删除
              if (fileID && typeof fileID === 'string' && (fileID.startsWith('cloud://') || fileID.startsWith('https://'))) {
                await uniCloud.callFunction({
                  name: 'delete-file',
                  data: {
                    fileList: [fileID]
                  }
                });
              }
              
              // 从数组中移除
              this.completionEvidence.splice(index, 1);
              
              uni.showToast({
                title: '删除成功',
                icon: 'success',
                duration: 1000
              });
            } catch (error) {
              // 删除云存储图片失败
              
              // 即使云存储删除失败，也从本地移除图片引用
              this.completionEvidence.splice(index, 1);
              
              uni.showToast({
                title: '已从列表移除',
                icon: 'success',
                duration: 1000
              });
            } finally {
              uni.hideLoading();
            }
          }
        }
      });
    },

    previewImage(src) {
      uni.previewImage({
        urls: [src],
        current: src
      });
    },

    async handleResponsibleComplete() {
      if (!this.completionDescription) {
        uni.showToast({ title: '请输入完成情况说明', icon: 'none' });
        return;
      }

      try {
        uni.showLoading({ title: '提交中...', mask: true });

        const res = await uniCloud.callFunction({
          name: 'feedback-workflow',
          data: {
            action: 'responsible_complete',
            id: this.formDataId,
            completionDescription: this.completionDescription,
            completionEvidence: this.completionEvidence
          }
        });

        if (res.result && res.result.code === 0) {
          // 批量更新云函数返回的所有字段（确保数据一致性）
          if (res.result.data) {
            const returnedData = res.result.data;
            
            // 核心工作流字段（必须更新）
            if (returnedData.actionHistory) {
              this.formData.actionHistory = returnedData.actionHistory;
            }
            if (returnedData.lastUpdateTime) {
              this.formData.lastUpdateTime = returnedData.lastUpdateTime;
            }
            if (returnedData.remark !== undefined) {
              this.formData.remark = returnedData.remark;
            }
            if (returnedData.workflowStatus !== undefined) {
              this.formData.workflowStatus = returnedData.workflowStatus;
            }
            
            // 完成相关字段
            if (returnedData.completedByResponsibleTime !== undefined) {
              this.formData.completedByResponsibleTime = returnedData.completedByResponsibleTime;
            }
            if (returnedData.responsibleCompletionDescription !== undefined) {
              this.formData.responsibleCompletionDescription = returnedData.responsibleCompletionDescription;
            }
            if (returnedData.responsibleCompletionEvidence !== undefined) {
              this.formData.responsibleCompletionEvidence = returnedData.responsibleCompletionEvidence;
            }
          }
          
          // 更新当前步骤，确保界面正确显示
          this.updateCurrentStep();
          
          // 强制触发视图更新，确保完成状态立即显示
          this.$forceUpdate();
          
          // 额外确保显示立即更新（防止缓存问题）
          this.$nextTick(() => {
            this.$forceUpdate();
          });
          
          uni.hideLoading();
          uni.showToast({ title: '提交成功', icon: 'success' });
          
          // 发送feedback-updated事件，通知列表页面刷新
          uni.$emit('feedback-updated');
          
          // 添加用户中心页面刷新事件，确保用户中心页面能实时更新
          uni.$emit('ucenter-need-refresh', { id: this.formDataId });
          
          // 清空表单
          this.completionDescription = '';
          this.completionEvidence = [];
          
          // 刷新页面数据
          this.getDetail(this.formDataId);
        } else {
          throw new Error(res.result?.message || '提交失败');
        }
      } catch (error) {
        uni.hideLoading();
        uni.showModal({
          content: error.message || '提交失败',
          showCancel: false
        });
      }
    },

    async handleFinalConfirm() {
      if (!this.finalConfirmReason) {
        uni.showToast({ title: '请输入确认意见', icon: 'none' });
        return;
      }

      try {
        uni.showLoading({ title: '确认中...', mask: true });

        const res = await uniCloud.callFunction({
          name: 'feedback-workflow',
          data: {
            action: 'gm_final_confirm',
            id: this.formDataId,
            reason: this.finalConfirmReason
          }
        });

        if (res.result && res.result.code === 0) {
          // 批量更新云函数返回的所有字段（确保数据一致性）
          if (res.result.data) {
            const returnedData = res.result.data;
            
            // 核心工作流字段（必须更新）
            if (returnedData.actionHistory) {
              this.formData.actionHistory = returnedData.actionHistory;
            }
            if (returnedData.lastUpdateTime) {
              this.formData.lastUpdateTime = returnedData.lastUpdateTime;
            }
            if (returnedData.remark !== undefined) {
              this.formData.remark = returnedData.remark;
            }
            if (returnedData.workflowStatus !== undefined) {
              this.formData.workflowStatus = returnedData.workflowStatus;
            }
            if (returnedData.isCompleted !== undefined) {
              this.formData.isCompleted = returnedData.isCompleted;
            }
            if (returnedData.finalCompletedTime !== undefined) {
              this.formData.finalCompletedTime = returnedData.finalCompletedTime;
            }
          }
          
          // 更新当前步骤，确保界面正确显示
          this.updateCurrentStep();
          
          // 强制触发视图更新，确保最终确认状态立即显示
          this.$forceUpdate();
          
          // 额外确保显示立即更新（防止缓存问题）
          this.$nextTick(() => {
            this.$forceUpdate();
          });
          
          uni.hideLoading();
          uni.showToast({ title: '确认完成', icon: 'success' });
          
          // 发送feedback-updated事件，通知列表页面刷新
          uni.$emit('feedback-updated');
          
          // 添加用户中心页面刷新事件，确保用户中心页面能实时更新
          uni.$emit('ucenter-need-refresh', { id: this.formDataId });
          
          // 刷新页面数据
          this.getDetail(this.formDataId);
        } else {
          throw new Error(res.result?.message || '确认失败');
        }
      } catch (error) {
        uni.hideLoading();
        uni.showModal({
          content: error.message || '确认失败',
          showCancel: false
        });
      }
    },

    async handleRejectCompletion() {
      if (!this.rejectReason) {
        uni.showToast({ title: '请输入退回理由', icon: 'none' });
        return;
      }

      try {
        const confirmRes = await uni.showModal({
          title: '确认退回',
          content: `确定要退回重做吗？\n退回理由：${this.rejectReason}`,
          confirmText: '确定',
          cancelText: '取消'
        });

        if (!confirmRes.confirm) return;

        uni.showLoading({ title: '处理中...', mask: true });

        // 将状态退回到assigned_to_responsible，并保存退回理由
        const res = await uniCloud.callFunction({
          name: 'feedback-workflow',
          data: {
            action: 'updateWorkflowStatus',
            id: this.formDataId,
            workflowStatus: 'assigned_to_responsible',
            rejectReason: this.rejectReason
          }
        });

        if (res.result && res.result.code === 0) {
          // 批量更新云函数返回的所有字段（确保数据一致性）
          if (res.result.data) {
            const returnedData = res.result.data;
            
            // 核心工作流字段（必须更新）
            if (returnedData.actionHistory) {
              this.formData.actionHistory = returnedData.actionHistory;
            }
            if (returnedData.lastUpdateTime) {
              this.formData.lastUpdateTime = returnedData.lastUpdateTime;
            }
            if (returnedData.remark !== undefined) {
              this.formData.remark = returnedData.remark;
            }
            if (returnedData.workflowStatus !== undefined) {
              this.formData.workflowStatus = returnedData.workflowStatus;
            }
            if (returnedData.rejectReason !== undefined) {
              this.formData.rejectReason = returnedData.rejectReason;
            }
            if (returnedData.rejectedTime !== undefined) {
              this.formData.rejectedTime = returnedData.rejectedTime;
            }
          }
          
          // 清除完成相关字段
          this.formData.responsibleCompletionDescription = null;
          this.formData.responsibleCompletionEvidence = [];
          this.formData.completedByResponsibleTime = null;
          
          // 更新当前步骤，确保界面正确显示
          this.updateCurrentStep();
          
          // 强制触发视图更新，确保退回状态立即显示
          this.$forceUpdate();
          
          // 额外确保显示立即更新（防止缓存问题）
          this.$nextTick(() => {
            this.$forceUpdate();
          });
          
          // 清空退回理由输入框
          this.rejectReason = '';
          
          uni.hideLoading();
          uni.showToast({ title: '已退回重做', icon: 'success' });
          
          // 发送feedback-updated事件，通知列表页面刷新
          uni.$emit('feedback-updated');
          
          // 添加用户中心页面刷新事件，确保用户中心页面能实时更新
          uni.$emit('ucenter-need-refresh', { id: this.formDataId });
          
                // 刷新页面数据
      this.getDetail(this.formDataId);
    } else {
      throw new Error(res.result?.message || '退回失败');
    }
  } catch (error) {
    uni.hideLoading();
    uni.showModal({
      content: error.message || '退回失败',
      showCancel: false
    });
  }
},

    /**
     * 静默刷新数据
     * 不显示loading，避免影响用户操作
     */
    async silentRefreshData() {
      try {
        // 防止重复加载
        if (this._isLoading) return;
        
        const res = await uniCloud.callFunction({
          name: 'feedback-workflow',
          data: {
            action: 'get_detail',
            id: this.formDataId
          }
        });
        
        if (res.result.code === 0 && res.result.data) {
          const data = res.result.data;
          
          // 使用标准新工作流数据结构
          // 使用纯净的新工作流数据结构（静默刷新用）
          this.formData = {
            // 基础字段
            name: data.name,
            project: data.project,
            description: data.description || '',
            images: data.images || [],
            createTime: data.createTime,
            createUserId: data.createUserId,
            isAdopted: data.isAdopted,
            isCompleted: data.isCompleted,
            // 新工作流字段
            workflowStatus: data.workflowStatus || 'pending_supervisor',
            meetingRequired: data.meetingRequired || false,
            terminatedBy: data.terminatedBy || '',
            terminatedTime: data.terminatedTime,
            lastUpdateTime: data.lastUpdateTime,
            remark: data.remark || '',
            updateTrigger: data.updateTrigger || '',
            // 负责人字段
            responsibleUserId: data.responsibleUserId,
            assignedTime: data.assignedTime,
            assignReason: data.assignReason || '',
            responsibleCompletionDescription: data.responsibleCompletionDescription,
            responsibleCompletionEvidence: data.responsibleCompletionEvidence || [],
            completedByResponsibleTime: data.completedByResponsibleTime,
            finalCompletedTime: data.finalCompletedTime,
            rejectReason: data.rejectReason || '',
            rejectedTime: data.rejectedTime,
            // 操作历史（新工作流的唯一数据源）
            actionHistory: data.actionHistory || []
          };
          
          // 更新UI状态
          this.updateSelectedOptions();
          this.updateCurrentStep();
          
          // 强制更新视图
          this.$forceUpdate();
        }
      } catch (error) {
        console.log('静默刷新失败:', error);
      }
    },

    // 智能判断是否需要刷新数据
    shouldRefreshOnCrossDeviceUpdate(data) {
      // 如果没有当前记录ID，不需要刷新
      if (!this.formDataId) {
        return false;
      }
      
      // 如果页面不可见，不需要刷新
      if (!this.isPageVisible) {
        return false;
      }
      
      // 如果距离上次刷新时间太短（小于10秒），避免频繁刷新
      const timeSinceLastRefresh = Date.now() - this.lastRefreshTime;
      if (timeSinceLastRefresh < 10000) {
        return false;
      }
      
      // 如果更新的反馈记录包含当前正在查看的记录，需要刷新
      if (data.feedbackIds && data.feedbackIds.includes(this.formDataId)) {
        return true;
      }
      
      // 如果更新类型包含工作流状态变化，可能需要刷新
      if (data.updateTypes && data.updateTypes.includes('workflow_status_changed')) {
        return true;
      }
      
      // 默认不刷新
      return false;
    },

    /**
     * 处理记录已被删除的情况
     * 当其他用户删除了正在操作的记录时，优雅地处理这种边界情况
     */
    handleRecordDeleted() {
      // 清除loading状态
      uni.hideLoading();
      this._isProcessing = false;

      // 显示友好的提示信息
      uni.showModal({
        title: '记录已删除',
        content: '您正在操作的反馈记录已被其他用户删除，将返回上一页面。',
        showCancel: false,
        confirmText: '确定',
        success: () => {
          // 清除本地缓存
          try {
            const cacheKey = `approval_${this.formDataId}`;
            uni.removeStorageSync(cacheKey);
          } catch (e) {}

          // 触发相关页面刷新事件
          uni.$emit('refresh-todo-list');
          uni.$emit('feedback-updated');
          uni.$emit('ucenter-need-refresh');

          // 立即更新角标
          const todoBadgeManager = require('@/utils/todo-badge.js').default;
          if (todoBadgeManager) {
            todoBadgeManager.forceRefresh();
          }

          // 返回上一页面
          uni.navigateBack({
            delta: 1,
            fail: () => {
              // 如果无法返回，跳转到首页
              uni.switchTab({
                url: '/pages/index/index'
              });
            }
          });
        }
      });
    },

  },

  /**
   * 页面生命周期 - 页面创建时
   */
  created() {
    // 监听角标管理器的跨设备更新事件
    uni.$on('cross-device-update-detected', (data) => {
      if (data.silent) {
        // 智能判断是否需要刷新
        const shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);
        if (shouldRefresh) {
                      console.log('审核页面收到跨设备更新通知，静默刷新数据');
            // 静默刷新数据
            this.silentRefreshData();
        }
      }
    });
  },

  /**
   * 页面生命周期 - 页面销毁时
   */
  beforeDestroy() {
    // 移除事件监听
    uni.$off('cross-device-update-detected');
  }
}
</script>

<style>
.uni-container {
  padding-top: 20px;
  padding-right: 20px;
  padding-left: 20px;
  padding-bottom: 20px;
  min-height: 100vh;
  justify-content: flex-start;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}
.steps-section {
  padding: 30px 20px;
  background: white;
  border-radius: 16px;
  margin-bottom: 20px;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step {
  position: relative;
}

.step-content {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.step-icon {
  position: relative;
  width: 40px;
  flex-shrink: 0;
}

.icon-number {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  background: #e2e8f0;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.icon-line {
  position: absolute;
  left: 50%;
  top: 40px;
  bottom: -60px;
  width: 2px;
  background: #e2e8f0;
  transform: translateX(-50%);
}

.step.active .icon-number {
  background: #3b82f6;
  color: white;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

.step.completed .icon-number {
  background: #10b981;
  color: white;
}

.step.rejected .icon-number {
  background: #ef4444;
  color: white;
}

.step.not-executed .icon-number {
  background: #9ca3af;
  color: white;
}

.step.meeting .icon-number {
  background: #9c27b0;
  color: white;
}

.step-info {
  flex: 1;
  background: #f8fafc;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.step.active .step-info {
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #3b82f6;
}

.step.completed .step-info {
  border-color: #10b981;
  background-color: rgba(16, 185, 129, 0.05);
}

.step.rejected .step-info {
  border-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.05);
}

.step.not-executed .step-info {
  border-color: #9ca3af;
  background-color: rgba(156, 163, 175, 0.05);
  opacity: 0.7;
}

.step.meeting .step-info {
  border-color: #9c27b0;
  background-color: rgba(156, 39, 176, 0.05);
}

/* 最后一步（审核结果）特殊样式 */
.step:last-child.completed .step-info {
  background-color: rgba(16, 185, 129, 0.05);
  border-color: #10b981;
  border-width: 2px;
}

.step:last-child.rejected .step-info {
  background-color: rgba(239, 68, 68, 0.05);
  border-color: #ef4444;
  border-width: 2px;
}

.step-title {
  font-size: 14px;
  font-weight: 500;
  display: block;
  color: #495057;
}

.step-status-info {
  margin-top: 8px;
  text-align: center;
}

.status-text {
  font-size: 12px;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.status-text-approved {
  color: #4caf50;
}

.status-text-rejected {
  color: #f44336;
}

.status-text-meeting {
  color: #9c27b0;
}

.status-text-not-executed {
  color: #999999;
}

.time-text {
  font-size: 11px;
  color: #666;
  display: block;
}

.step-waiting {
  font-size: 12px;
  color: #3b82f6; /* 统一蓝色颜色值 */
  display: block;
  margin-top: 8px;
  text-align: center;
}

.workflow-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
  padding: 8px 12px;
  background: rgba(59, 130, 246, 0.1); /* 统一蓝色 */
  border-radius: 6px;
  border-left: 3px solid #3b82f6; /* 统一蓝色 */
}

.workflow-notice-text {
  font-size: 12px;
  color: #3b82f6; /* 统一蓝色 */
}

.approval-section {
  background: #fff;
  padding: 20px;
  margin: 15px 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.approval-section.active {
  border: 2px solid #3b82f6; /* 统一蓝色 */
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1); /* 统一蓝色 */
}

.approval-section.disabled {
  opacity: 0.7;
  pointer-events: none;
}

.approval-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.approval-title {
  font-size: 16px;
  font-weight: bold;
  color: #212529;
}

.approval-status {
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-approved {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.status-rejected {
  color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

.status-pending {
  color: #3b82f6; /* 统一蓝色 */
  background-color: rgba(59, 130, 246, 0.1); /* 统一蓝色 */
}

.status-meeting {
  color: #9c27b0;
  background-color: rgba(156, 39, 176, 0.1);
}

.status-not-executed {
  color: #999999;
  background-color: rgba(153, 153, 153, 0.1);
}

.approval-options {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

/* 小程序优化审核选项布局 */
/* #ifdef MP-WEIXIN */
.approval-options {
  gap: 12rpx;
}

.radio-item {
  flex: 1;
  min-width: 200rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
}

.radio-item text {
  font-size: 28rpx;
}
/* #endif */

.radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.radio-item:hover {
  background: #e9ecef;
}

.radio-item text {
  font-size: 14px;
  color: #495057;
}

radio {
  transform: scale(0.9);
}

.reset-section {
  margin-top: 30px;
  padding: 0 20px;
}

.reset-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #ff4d4f;
  color: #fff;
  border-radius: 6px;
  font-size: 16px;
  transition: all 0.3s;
}

.reset-btn:active {
  opacity: 0.8;
}

/* 最终结果的特殊样式 */
.step:last-child .status-text {
  font-size: 12px;
  color: #999999; /* 默认灰色，而不是蓝色 */
  display: block;
  margin-top: 8px;
  text-align: center;
}

.step.completed:last-child .status-text {
  color: #4caf50;
}

.step.rejected:last-child .status-text {
  color: #f44336;
}

/* 第五步激活状态的特殊样式 - 仅当状态为pending时显示蓝色 */
.step:last-child.active .status-text {
  color: #3b82f6; /* 激活时使用统一的蓝色 */
}

/* 第五步激活状态的数字发光效果 - 仅当状态为pending时，与其他步骤保持一致 */
.step:last-child.active:not(.completed):not(.rejected) .icon-number {
  background: #3b82f6;
  color: white;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* 第五步激活状态的卡片样式 - 仅当状态为pending时 */
.step:last-child.active:not(.completed):not(.rejected) .step-info {
  background: white;
  border: 2px solid #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* 第五步完成状态的特殊样式 */
.step:last-child.completed .status-text {
  color: #4caf50; /* 完成时显示绿色 */
}

/* 第五步拒绝状态的特殊样式 */
.step:last-child.rejected .status-text {
  color: #f44336; /* 拒绝时显示红色 */
}

.reason-box {
  margin-top: 15px;
  padding: 10px 0;
}

.reason-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.reason-input {
  width: 95%;
  height: 80px;
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
  background: #fff;
  line-height: 1.5;
}

.reason-input:disabled {
  background-color: #f5f5f5;
  color: #999;
}

.submitted-reason {
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.reason-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.reason-approved {
  color: #4caf50 !important;
}

.reason-rejected {
  color: #f44336 !important;
}

.reason-meeting {
  color: #9c27b0 !important;
}

.reason-not-executed {
  color: #999999 !important;
}

.reason-info {
  color: #17a2b8 !important;
}

/* 审核理由汇总区域样式 */
.approval-summary-section {
  background: #fff;
  padding: 20px;
  margin: 15px 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.approval-summary-header {
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.approval-summary-title {
  font-size: 16px;
  font-weight: bold;
  color: #212529;
}

.approval-summary-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.summary-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  border-left: 3px solid #e9ecef;
}

.summary-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-role {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.summary-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.summary-reason {
  padding: 8px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.status-pending {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
}

.status-info {
  color: #17a2b8;
  background-color: rgba(23, 162, 184, 0.1);
}

.status-text-approved {
  color: #4caf50;
}

.status-text-rejected {
  color: #f44336;
}

.status-text-not-executed {
  color: #999999;
}

.status-meeting {
  color: #9c27b0;
  background-color: rgba(156, 39, 176, 0.1);
}

.status-not-executed {
  color: #999999;
  background-color: rgba(153, 153, 153, 0.1);
}

/* 骨架屏样式 */
.skeleton-loading {
  padding: 20px;
}

.skeleton-steps-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 30px 20px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.skeleton-step {
  height: 80px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 8px;
}

.skeleton-approval-section {
  height: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 12px;
  margin-bottom: 15px;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 字符计数样式 */
.character-count {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 4px;
}

/* 新增工作流样式 */
.assign-section {
  padding: 15px 0;
}

.assign-picker {
  margin-bottom: 15px;
}

.picker-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.uni-input {
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #fff;
  font-size: 14px;
  color: #333;
}

.assign-btn {
  margin-top: 15px;
  width: 100%;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 6px;
  height: 40px;
  font-size: 16px;
}

.responsible-info {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 15px;
}

.responsible-text {
  font-size: 14px;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.assign-time {
  font-size: 14px;
  color: #666;
}

.responsible-action {
  margin-top: 15px;
}

.completion-form {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.evidence-section {
  margin: 15px 0;
}

.evidence-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.evidence-upload {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.image-list {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  align-items: center;
}

.image-item {
  position: relative;
  width: 80px;
  height: 80px;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  border: 2px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  cursor: pointer;
}

.image-item image:hover {
  transform: scale(1.05) rotate(1deg);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.upload-item {
  width: 90px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 5px;
}

.image-delete {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 22px;
  height: 22px;
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
  color: #fff;
  border-radius: 50%;
  text-align: center;
  line-height: 22px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(255, 71, 87, 0.3);
  border: 2px solid #fff;
  z-index: 20;
}

.image-delete:hover {
  transform: scale(1.1);
  background: linear-gradient(135deg, #ff3838 0%, #ff2f2f 100%);
  box-shadow: 0 3px 8px rgba(255, 71, 87, 0.4);
}

.upload-btn {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px dashed #ccc;
  border-radius: 8px;
  color: #666;
  font-size: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  padding: 8px 4px;
  transition: all 0.3s ease;
  cursor: pointer;
  box-sizing: border-box;
}

.upload-btn:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  border-color: #999;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upload-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
}

.upload-icon {
  display: block;
  line-height: 1;
}

.upload-text {
  font-size: 9px;
  font-weight: 500;
  line-height: 1.2;
  text-align: center;
  white-space: nowrap;
}

.complete-btn {
  width: 100%;
  background: #28a745;
  color: #fff;
  border: none;
  border-radius: 6px;
  height: 40px;
  font-size: 16px;
  margin-top: 15px;
}

.completion-review {
  padding: 15px 0;
}

.completion-info {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.completed-work {
  margin: 15px 0;
  padding: 15px;
  background: #f8fdf8;
  border: 1px solid #d4edda;
  border-radius: 8px;
}

.completion-time {
  font-size: 14px;
  color: #666;
  display: block;
  margin-top: 5px;
}

.review-tip {
  font-size: 12px;
  color: #007aff;
  display: block;
  margin-top: 8px;
  font-style: italic;
}

/* 完成说明样式 - 去掉灰色背景，恢复原有文字颜色 */
.completion-description-item {
	margin: 15px 0;
}

.completion-description-header {
	margin-bottom: 8px;
}

.description-label {
	font-size: 14px;
	color: #666;
	font-weight: 500;
}

.completion-description-content {
	padding: 8px;
	background: #fff;
	border-radius: 6px;
	border: 1px solid #e9ecef;
}

.description-text {
	font-size: 14px;
	color: #28a745;
	line-height: 1.5;
}

.completion-evidence {
  margin: 15px 0;
}

.evidence-images {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.evidence-image {
  width: 90px;
  height: 90px;
  border-radius: 8px;
  border: 2px solid #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.evidence-image:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.final-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.reject-btn {
  flex: 1;
  background: #6c757d;
  color: #fff;
  border: none;
  border-radius: 6px;
  height: 40px;
  font-size: 16px;
}

.confirm-btn {
  flex: 1;
  background: #28a745;
  color: #fff;
  border: none;
  border-radius: 6px;
  height: 40px;
  font-size: 16px;
}

/* 退回理由显示样式 */
.reject-reason-display {
  margin: 15px 0;
  padding: 12px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  border-left: 4px solid #f39c12;
}

.reject-reason-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reject-reason-label {
  font-size: 14px;
  color: #e67e22;
  font-weight: 500;
}

.reject-time {
  font-size: 12px;
  color: #666;
}

.reject-reason-content {
  padding: 8px;
  border-radius: 6px;
}

.reject-reason-text {
  font-size: 14px;
  color: #d68910;
  line-height: 1.5;
  font-weight: 500;
}

/* 流程完成状态样式 */
.completion-summary {
  margin: 15px 0;
}

.summary-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.summary-item-container {
  background: #f8fdf8;
  border: 1px solid #d4edda;
  border-radius: 8px;
  padding: 12px;
}

.summary-header {
  margin-bottom: 8px;
}

.summary-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.summary-content {
  /* 内容区域样式 */
}

.summary-text {
  font-size: 14px;
  color: #28a745;
  line-height: 1.5;
  font-weight: 500;
}

/* 问题描述区域样式 */
.description-section {
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.description-header {
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.description-title {
  font-size: 16px;
  font-weight: bold;
  color: #212529;
}

.description-content {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.description-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}
</style> 
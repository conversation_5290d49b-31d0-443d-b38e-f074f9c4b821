{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/ucenter/ucenter.vue?8111", "webpack:///D:/Xwzc/pages/ucenter/ucenter.vue?dda1", "webpack:///D:/Xwzc/pages/ucenter/ucenter.vue?3f03", "webpack:///D:/Xwzc/pages/ucenter/ucenter.vue?24fc", "uni-app:///pages/ucenter/ucenter.vue", "webpack:///D:/Xwzc/pages/ucenter/ucenter.vue?3983", "webpack:///D:/Xwzc/pages/ucenter/ucenter.vue?f47a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "nickname", "username", "avatar_file", "_id", "roleNames", "todoList", "todoCount", "userRole", "hasRole", "isWechatPlatform", "isRefreshing", "lastRefreshTime", "localUserInfo", "localHasLogin", "todoUpdateTimer", "isTokenValid", "responsibleTaskCount", "supervisionTaskCount", "avatarLoaded", "computed", "userInfo", "<PERSON><PERSON><PERSON><PERSON>", "hasAdminPermission", "hasResponsiblePermission", "hasGMPermission", "created", "uni", "cacheManager", "todoBadgeManager", "setTimeout", "console", "<PERSON><PERSON><PERSON><PERSON>", "onLoad", "onShow", "onPullDownRefresh", "title", "icon", "duration", "watch", "handler", "deep", "methods", "onAvatarLoad", "onAvatarError", "showFeatureInDevelopment", "handleVisibilityChange", "startTodoUpdateTimer", "clearTodoUpdateTimer", "clearInterval", "updateTodoCountFromBadge", "count", "refreshData", "showLoading", "Promise", "getUserRole", "cachedRole", "db", "where", "field", "get", "result", "roleNameMap", "errorMessage", "getProjectName", "getTodoList", "dbCmd", "whereConditions", "countRes", "orderBy", "limit", "res", "getResponsibleTaskCount", "uniCloud", "name", "action", "status", "getSupervisionTaskCount", "stats", "buildTodoQueryConditions", "conditions", "workflowStatus", "responsibleUserId", "formatDate", "getTodoTimeText", "getTodoTypeText", "handleTodoClick", "url", "goToExamine", "events", "goToReadNewsLog", "exportexcel", "checkExportPermission", "goToResponsibleTasks", "fail", "goToUserManagement", "goToGMSupervision", "modifyNickname", "logout", "content", "confirmText", "cancelText", "mask", "uniIdCo", "goToTodoList", "goToLogin", "getUserInfo", "token", "cachedInfo", "userData", "navToLogin", "navTo", "recentPages", "navToHonorGallery", "animationType", "animationDuration", "success", "checkTokenStatus", "tokenValid", "validateToken", "tokenExpired", "performLogout", "mutations", "cover", "Object", "clearSensitiveCache", "keys", "key", "clearedCount", "handleTokenInvalid", "shouldRefreshOnCrossDeviceUpdate"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/FA;AAAA;AAAA;AAAA;AAAimB,CAAgB,2nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC+OrnB;AACA;AACA;AAEA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;IACA;MACAC;MACAC;MACAC;MACAC;IACA;IAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QAAA,OACA;MAAA,EACA;IACA;IACA;IACAC;MACA;QAAA,OACA;MAAA,EACA;IACA;IACA;IACAC;MACA;QAAA,OACA;MAAA,EACA;IACA;EAEA;EACAC;IAAA;IACA;IACAC;MACA;MACAC;MACA;MACA;MACA;MACAC;IACA;;IAEA;IACAF;MACA;MACA;QACA;QACA;QACA;MACA;IACA;;IAEA;IACAA;MACA;QACA;QACA;MACA;IACA;;IAEA;IACAA;MACA;IACA;;IAEA;IACAA;MACA;QACA;QACAG;UACA;QACA;MACA;IACA;;IAEA;IACAH;MACA;QACAI;QACA;QACA;MACA;IACA;;IAEA;IACAJ;MACA;QACA;QACA;QACA;UACAI;UACA;UACA;QACA;MACA;IACA;;IAEA;;IAEA;;IAGA;IACA;;IAEA;EAIA;EACAC;IACA;IACAL;IACAA;IACAA;IACAA;IACAA;IACAA;IACAA;;IAEA;IACA;;IAEA;EAIA;EACAM;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;QACA;MACA;IACA;;IAEA;;IAEA;;IAGA;IACA;;IAEA;IACA;MACAJ;QACAD;MACA;IACA;EACA;EACAM;IACA;MACA;MACA;QACA;QACAR;QACA;QACAA;UACAS;UACAC;UACAC;QACA;MACA;QACAP;QACAJ;QACA;QACAA;UACAS;UACAC;UACAC;QACA;MACA;IACA;MACA;MACAX;IACA;EACA;EACAY;IACAjB;MAAA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;IACA;MACAkB;QACA;UACA;QACA;MACA;MACAC;IACA;IACA;MACAD;QACA;UACA;QACA;MACA;IACA;EACA;EACAE;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACAb;IACA;IAEA;IACAc;MACAlB;QACAS;QACAC;QACAC;MACA;IACA;IAEA;IACAQ,2DASA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGArB;cAAA;gBAAAsB;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEApB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAqB;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAEA;kBACA1B;oBACAS;kBACA;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAGA;gBACA;gBAAA,kCAEAkB;cAAA;gBAAA;gBAAA;gBAAA,kCAMAA;cAAA;gBAAA;gBAEA;kBACA3B;gBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAIA;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAKAC;gBAAA;gBAAA,OACAA,8BACAC,gCACAC,cACAC;cAAA;gBAAA;gBAHAC;gBAKA;kBACA;;kBAEA;kBACAC;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;kBACA,GAEA;kBACA;oBAAA,OACA;kBAAA,EACA;;kBAEA;kBACA;oBACA;sBAAA,OACAA;oBAAA,EACA;kBACA;oBACA;oBACA;kBACA;;kBAEA;kBACAlC;oBACApB;oBACAH;oBACAI;kBACA;gBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAMA;gBACAsD;gBACA;kBACAhC;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;gBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAKAR;gBACAS,oBAEA;gBACAC,0DAEA;gBAAA;gBAAA,OACAV,0BACAC,uBACAP;cAAA;gBAFAiB;gBAIA;kBACA;gBACA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAX,0BACAC,uBACAW,8BACAC;gBAAA,CACAV;cAAA;gBAJAW;gBAMA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAMA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAC;kBACAC;kBACA1E;oBACA2E;oBACAC;kBACA;gBACA;cAAA;gBANAL;gBAQA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAxC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAJ;kBACAC;kBACA1E;oBACA2E;kBACA;gBACA;cAAA;gBALAJ;gBAOA;kBACAO,qCACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA/C;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgD;MACA;;MAEA;MACA;QACA;QACAC;UAAAC;QAAA;MACA;MAEA;QACA;QACAD;UAAAC;QAAA;MACA;MAEA;QACA;QACAD;UAAAC;QAAA;QACAD;UAAAC;QAAA;QACAD;UAAAC;QAAA;MACA;;MAEA;MACA;QACA;UACA;UACA;UACA;YACA;YACAD;cACAC;cACAC;YACA;UACA;QACA;UACAnD;QACA;MACA;;MAEA;MACA;QACA;UACAkD,0BACA,sBACA,cACA,cACA;UACA;UACA,2BACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAE;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA3D;UACA4D;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA7D;QACA4D;QACAE;UACArC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAsC;MACA/D;QACA4D;MACA;IACA;IAEA;IACAI;MACAhE;QACA4D;MACA;IACA;IAEA;IACAK;MACA;MACA;QAAA,OACA;MAAA,EACA;MAEA;QACA;QACA;MACA;QACA;QACAjE;UACAS;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAuD;MACA;QACAlE;UACAS;UACAC;UACAC;QACA;QACA;MACA;MAEAX;QACA4D;QACAO;UACA/D;UACAJ;YACAS;YACAC;UACA;QACA;MACA;IACA;IAEA;IACA0D;MACA;QACApE;UACAS;UACAC;UACAC;QACA;QACA;MACA;MAEAX;QACA4D;QACAO;UACA/D;UACAJ;YACAS;YACAC;UACA;QACA;MACA;IACA;IAEA;IACA2D;MACA;QACArE;UACAS;UACAC;UACAC;QACA;QACA;MACA;MAEAX;QACA4D;QACAO;UACA/D;UACAJ;YACAS;YACAC;UACA;QACA;MACA;IACA;IAEA;IACA4D;MACA;MACAtE;QACA4D;MACA;IACA;IAGA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAvE;kBACAS;kBACA+D;kBACAC;kBACAC;gBACA;cAAA;gBALA9B;gBAAA,KAOAA;kBAAA;kBAAA;gBAAA;gBACA5C;kBACAS;kBACAkE;gBACA;;gBAEA;gBACAzE;gBAAA;gBAGA0E;gBAAA;gBAAA,OACAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAxE;cAAA;gBAGA;gBACA;;gBAEA;gBACAD;kBACAD;gBACA;gBAEAF;gBACAA;kBACAS;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAV;kBACAS;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmE;MACA7E;QACA4D;MACA;IACA;IAEA;IACAkB;MACA;;MAEA9E;QACA4D;MACA;;MAGA;IAMA;IAEA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBACA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACAnD;gBAAA;gBAAA,OACAA,8BACAC,gCACAC,yCACAC;cAAA;gBAAA;gBAHAC;gBAKA;kBACAgD;kBACA;oBACAzG;oBACAH;oBACAC;oBACAC;kBACA;;kBAEA;kBACA;;kBAEA;kBACAyB;gBACA;kBACA;oBAAA3B;oBAAAC;kBAAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA6B;gBACA;kBAAA9B;kBAAAC;gBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4G;MACAnF;QACA4D;MACA;IACA;IAEA;IACAwB;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAC;QACA;QACA;UACAA;QACA;QACApF;MACA;MAEAD;QACA4D;QACAO;UACA/D;UACAJ;YACAS;YACAC;UACA;QACA;MACA;IACA;IAEA;IACA4E;MACA;MACA;MACA;QACAD;QACA;UACAA;QACA;QACApF;MACA;;MAEA;MACAD;QACA4D;QACA2B;QAAA;QACAC;QAAA;QACAC;UACA;QAAA,CACA;QACAtB;UACA/D;UACA;UACAJ;YACA4D;YACAO;cACAnE;gBACAS;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAgF;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAV;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAAW;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvF;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwF;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAZ;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,mCACA;cAAA;gBAEAa;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,mCACA;cAAA;gBAAA,mCAEA;cAAA;gBAAA;gBAAA;gBAEAzF;gBAAA,mCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0F;MAAA;MAAA;MACA;MACA;;MAEA;MACA9F;MACAA;MACAA;MACAA;;MAEA;MACAC;MACAA;MACAA;;MAEA;MACA8F;QAAAC;MAAA;;MAEA;MACA9F;;MAEA;MACA;;MAEA;MACA+F;QACAvH;QACAC;QACAC;QACAC;QACAC;QACAI;QACAG;MACA;;MAEA;MACA;QACAW;UACAS;UACAC;UACAC;QACA;MACA;;MAEA;MACA;QAAA;MAAA;IACA;IAEA;IACAuF;MACA;QACA;QACA;;QAEA;QACA,yBACA;QAAA;QACA;QAAA,CACA;;QAEA;QACA,oBACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA,CACA;;QAEA;QAEAC;UACA;UACA;YACA;UACA;;UAEA;UACA;YAAA,OACAC;UAAA,EACA;UAEA;YACApG;YACAqG;UACA;QACA;MACA;QACAjG;MACA;IACA;IAEA;IACAkG;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;UAAA;QAAA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACv4CA;AAAA;AAAA;AAAA;AAAoqC,CAAgB,0oCAAG,EAAC,C;;;;;;;;;;;ACAxrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ucenter/ucenter.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/ucenter/ucenter.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./ucenter.vue?vue&type=template&id=4883731c&scoped=true&\"\nvar renderjs\nimport script from \"./ucenter.vue?vue&type=script&lang=js&\"\nexport * from \"./ucenter.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ucenter.vue?vue&type=style&index=0&id=4883731c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4883731c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ucenter/ucenter.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ucenter.vue?vue&type=template&id=4883731c&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !!_vm.hasLogin ? _vm.roleNames && _vm.roleNames.length > 0 : null\n  var m0 = _vm.hasLogin\n    ? _vm.uniIDHasRole(\"supervisor\") ||\n      _vm.uniIDHasRole(\"PM\") ||\n      _vm.uniIDHasRole(\"GM\") ||\n      _vm.uniIDHasRole(\"admin\")\n    : null\n  var m1 = _vm.hasLogin\n    ? _vm.uniIDHasRole(\"supervisor\") ||\n      _vm.uniIDHasRole(\"PM\") ||\n      _vm.uniIDHasRole(\"GM\") ||\n      _vm.uniIDHasRole(\"admin\")\n    : null\n  var m2 = _vm.hasLogin\n    ? _vm.uniIDHasRole(\"supervisor\") ||\n      _vm.uniIDHasRole(\"PM\") ||\n      _vm.uniIDHasRole(\"GM\") ||\n      _vm.uniIDHasRole(\"admin\")\n    : null\n  var m3 = _vm.hasLogin\n    ? _vm.uniIDHasRole(\"reviser\") ||\n      _vm.uniIDHasRole(\"supervisor\") ||\n      _vm.uniIDHasRole(\"PM\") ||\n      _vm.uniIDHasRole(\"GM\") ||\n      _vm.uniIDHasRole(\"admin\")\n    : null\n  var g1 =\n    _vm.hasLogin && _vm.hasLogin && _vm.hasRole ? _vm.todoList.length : null\n  var l0 =\n    _vm.hasLogin && _vm.hasLogin && _vm.hasRole && g1 > 0\n      ? _vm.__map(_vm.todoList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m4 = _vm.getProjectName(item.project)\n          var m5 = _vm.getTodoTimeText(item)\n          var m6 = _vm.getTodoTypeText(item.workflowStatus)\n          return {\n            $orig: $orig,\n            m4: m4,\n            m5: m5,\n            m6: m6,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        g1: g1,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ucenter.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ucenter.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 用户信息卡片 -->\n\t\t<view class=\"user-card\" @click=\"goToLogin\" v-if=\"!hasLogin\">\n\t\t\t<view class=\"avatar-section\">\n\t\t\t\t<view class=\"default-avatar\">\n\t\t\t\t\t<uni-icons type=\"person-filled\" size=\"40\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"user-info\">\n\t\t\t\t<text class=\"username login-prompt\">点击登录</text>\n\t\t\t\t<text class=\"login-tip\">登录后可查看更多功能</text>\n\t\t\t</view>\n\t\t\t<view class=\"card-arrow\">\n\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#BBBBBB\"></uni-icons>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 已登录用户信息卡片 -->\n\t\t<view class=\"user-card\" v-else>\n\t\t\t<view class=\"avatar-section\">\n\t\t\t\t<!-- 头像容器，始终显示蓝色背景作为加载状态 -->\n\t\t\t\t<view class=\"avatar-container\">\n\t\t\t\t\t<!-- 默认背景（蓝色+图标），始终存在 -->\n\t\t\t\t\t<view class=\"default-avatar-bg\">\n\t\t\t\t\t\t<uni-icons type=\"person-filled\" size=\"40\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- 真实头像，加载成功后覆盖默认背景 -->\n\t\t\t\t\t<image\n\t\t\t\t\t\tclass=\"avatar-image\"\n\t\t\t\t\t\tv-if=\"userInfo.avatar_file && userInfo.avatar_file.url\"\n\t\t\t\t\t\t:src=\"userInfo.avatar_file.url\"\n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t@load=\"onAvatarLoad\"\n\t\t\t\t\t\t@error=\"onAvatarError\"\n\t\t\t\t\t\t:style=\"{ opacity: avatarLoaded ? 1 : 0 }\">\n\t\t\t\t\t</image>\n\t\t\t\t\t<image\n\t\t\t\t\t\tclass=\"avatar-image\"\n\t\t\t\t\t\tv-else-if=\"userInfo.avatar\"\n\t\t\t\t\t\t:src=\"userInfo.avatar\"\n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t@load=\"onAvatarLoad\"\n\t\t\t\t\t\t@error=\"onAvatarError\"\n\t\t\t\t\t\t:style=\"{ opacity: avatarLoaded ? 1 : 0 }\">\n\t\t\t\t\t</image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"user-info\">\n\t\t\t\t<text class=\"username\">{{userInfo.nickname || userInfo.username || '未设置昵称'}}</text>\n\t\t\t\t<view class=\"role-tags\" v-if=\"roleNames && roleNames.length > 0\">\n\t\t\t\t\t<text class=\"role-tag\" v-for=\"(role, index) in roleNames\" :key=\"index\">{{role}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 登录后才显示功能区 -->\n\t\t<block v-if=\"hasLogin\">\n\t\t\t<!-- 综合中心 - 集中展示各种业务功能入口 -->\n\t\t\t<view class=\"patrol-section\">\n\t\t\t\t<view class=\"patrol-title\">综合中心</view>\n\t\t\t\t<scroll-view scroll-x=\"true\" class=\"patrol-scroll\" :show-scrollbar=\"false\">\n\t\t\t\t\t<view class=\"patrol-grid\">\n\t\t\t\t\t\t<!-- 点位管理入口  -->\n\t\t\t\t\t\t<view class=\"patrol-item\" @click=\"navTo('/pages/patrol_pkg/point/index')\" v-if=\"uniIDHasRole('supervisor') || uniIDHasRole('PM') || uniIDHasRole('GM') || uniIDHasRole('admin')\">\n\t\t\t\t\t\t\t<view class=\"patrol-icon point-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"location-filled\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"patrol-text\">点位管理</text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 班次时间入口 -->\n\t\t\t\t\t\t<view class=\"patrol-item\" @click=\"navTo('/pages/patrol_pkg/shift/index')\" v-if=\"uniIDHasRole('supervisor') || uniIDHasRole('PM') || uniIDHasRole('GM') || uniIDHasRole('admin')\">\n\t\t\t\t\t\t\t<view class=\"patrol-icon shift-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"patrol-text\">班次管理</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 线路管理入口 -->\n\t\t\t\t\t\t<view class=\"patrol-item\" @click=\"navTo('/pages/patrol_pkg/route/index')\" v-if=\"uniIDHasRole('supervisor') || uniIDHasRole('PM') || uniIDHasRole('GM') || uniIDHasRole('admin')\">\n\t\t\t\t\t\t\t<view class=\"patrol-icon route-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"map-filled\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"patrol-text\">线路管理</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 任务管理入口 -->\n\t\t\t\t\t\t<view class=\"patrol-item\" @click=\"navTo('/pages/patrol_pkg/task/index')\">\n\t\t\t\t\t\t\t<view class=\"patrol-icon task-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"flag-filled\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"patrol-text\">任务管理</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 巡视记录入口 -->\n\t\t\t\t\t\t<view class=\"patrol-item\" @click=\"navTo('/pages/patrol_pkg/record/index')\">\n\t\t\t\t\t\t\t<view class=\"patrol-icon record-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"bars\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"patrol-text\">巡视记录</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 二维码批量管理入口 -->\n\t\t\t\t\t\t<view class=\"patrol-item\" @click=\"navTo('/pages/patrol_pkg/point/qrcode-batch')\" v-if=\"uniIDHasRole('reviser') || uniIDHasRole('supervisor') || uniIDHasRole('PM') || uniIDHasRole('GM') || uniIDHasRole('admin')\">\n\t\t\t\t\t\t\t<view class=\"patrol-icon qrcode-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"scan\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"patrol-text\">二维码管理</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 荣誉展厅入口 -->\n\t\t\t\t\t\t<view class=\"patrol-item\" @click=\"navTo('/pages/honor_pkg/gallery/index')\">\n\t\t\t\t\t\t\t<view class=\"patrol-icon gallery-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"star-filled\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"patrol-text\">荣誉展厅</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 功能按钮区域 -->\n\t\t\t<view class=\"action-list\">\n\t\t\t\t<view class=\"action-item\" @click=\"goToReadNewsLog\">\n\t\t\t\t\t<view class=\"action-icon news-icon\">\n\t\t\t\t\t\t<uni-icons type=\"notification-filled\" size=\"22\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-content\">\n\t\t\t\t\t\t<text class=\"action-text\">公告通知</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#CCCCCC\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-item\" @click=\"checkExportPermission\">\n\t\t\t\t\t<view class=\"action-icon export-icon\">\n\t\t\t\t\t\t<uni-icons type=\"download\" size=\"22\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-content\">\n\t\t\t\t\t\t<text class=\"action-text\">文档导出</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#CCCCCC\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t\t<!-- 负责人任务入口，仅负责人可见 -->\n\t\t\t\t<view class=\"action-item\" @click=\"goToResponsibleTasks\" v-if=\"hasResponsiblePermission\">\n\t\t\t\t\t<view class=\"action-icon responsible-tasks-icon\">\n\t\t\t\t\t\t<uni-icons type=\"list\" size=\"22\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-content\">\n\t\t\t\t\t\t<text class=\"action-text\">我的任务</text>\n\t\t\t\t\t\t<text class=\"action-badge\" v-if=\"responsibleTaskCount > 0\">{{responsibleTaskCount}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#CCCCCC\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t\t<!-- 用户管理入口，仅管理员可见 -->\n\t\t\t\t<view class=\"action-item\" @click=\"goToUserManagement\" v-if=\"hasAdminPermission\">\n\t\t\t\t\t<view class=\"action-icon user-management-icon\">\n\t\t\t\t\t\t<uni-icons type=\"person-filled\" size=\"22\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-content\">\n\t\t\t\t\t\t<text class=\"action-text\">用户管理</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#CCCCCC\"></uni-icons>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 厂长监督入口，仅厂长可见 -->\n\t\t\t\t<view class=\"action-item\" @click=\"goToGMSupervision\" v-if=\"hasGMPermission\">\n\t\t\t\t\t<view class=\"action-icon gm-supervision-icon\">\n\t\t\t\t\t\t<uni-icons type=\"eye-filled\" size=\"22\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-content\">\n\t\t\t\t\t\t<text class=\"action-text\">指派监督</text>\n\t\t\t\t\t\t<text class=\"action-badge\" v-if=\"supervisionTaskCount > 0\">{{supervisionTaskCount}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#CCCCCC\"></uni-icons>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"action-item\" @click=\"modifyNickname\">\n\t\t\t\t\t<view class=\"action-icon settings-icon\">\n\t\t\t\t\t\t<uni-icons type=\"gear\" size=\"22\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-content\">\n\t\t\t\t\t\t<text class=\"action-text\">用户设置</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#CCCCCC\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-item\" @click=\"logout\">\n\t\t\t\t\t<view class=\"action-icon logout-icon\">\n\t\t\t\t\t\t<uni-icons type=\"closeempty\" size=\"22\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-content\">\n\t\t\t\t\t\t<text class=\"action-text logout-text\">退出登录</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#CCCCCC\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 待办事项区域 -->\n\t\t\t<view class=\"todo-section\" v-if=\"hasLogin && hasRole\">\n\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t<text class=\"section-title\">我的待办</text>\n\t\t\t\t\t<view class=\"todo-count-wrapper\" v-if=\"hasRole\" @click=\"goToTodoList\">\n\t\t\t\t\t\t<text class=\"todo-count\" v-if=\"todoCount > 0\">共{{todoCount}}项待处理</text>\n\t\t\t\t\t\t<text class=\"todo-count\" v-else>暂无待办</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"todo-list\" v-if=\"todoList.length > 0\">\n\t\t\t\t\t<view class=\"todo-item\" v-for=\"(item, index) in todoList\" :key=\"index\" @click=\"handleTodoClick(item)\">\n\t\t\t\t\t\t<view class=\"todo-content\">\n\t\t\t\t\t\t\t<view class=\"todo-title\">\n\t\t\t\t\t\t\t\t<text class=\"name\">{{item.name}}</text>\n\t\t\t\t\t\t\t\t<text class=\"project\">{{getProjectName(item.project)}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"description\">{{item.description}}</text>\n\t\t\t\t\t\t\t<view class=\"todo-footer\">\n\t\t\t\t\t\t\t\t<text class=\"time\">{{getTodoTimeText(item)}}</text>\n\t\t\t\t\t\t\t\t<text class=\"todo-type\">{{getTodoTypeText(item.workflowStatus)}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#BBBBBB\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<p-empty-state v-else \n\t\t\t\t\t:text=\"hasRole ? '暂无待办事项' : '权限不够无法查看待办事项'\" \n\t\t\t\t\timage=\"/static/empty/empty_todo.png\">\n\t\t\t\t</p-empty-state>\n\t\t\t</view>\n\t\t</block>\n\t\t\n\t\t<!-- 未登录时显示提示信息 -->\n\t\t<p-empty-state v-else\n\t\t\ttext=\"登录后查看更多功能\"\n\t\t\timage=\"/static/empty/empty_todo.png\">\n\t\t</p-empty-state>\n\t</view>\n</template>\n\n<script>\n\timport { store, mutations } from '@/uni_modules/uni-id-pages/common/store.js'\n\timport { checkUpdate } from '@/utils/wx-utils.js';\n\timport cacheManager, { getCacheKey } from '@/utils/cache.js';\n\timport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n\timport { formatDate as formatDateUtil } from '@/utils/date.js';\n\timport todoBadgeManager from '@/utils/todo-badge.js'; // 重新添加导入\n\t\n\texport default {\n\t\tcomponents: {\n\t\t\tPEmptyState\n\t\t},\n\t\tdata() {\n\t\t\t// 初始化 - 先显示缓存，再异步更新\n\t\t\tconst token = uni.getStorageSync('uni_id_token') || '';\n\t\t\tconst hasLogin = !!token;\n\t\t\t\n\t\t\t// 获取用户信息 - 使用自己的缓存系统\n\t\t\tconst cachedUserInfo = cacheManager.get(cacheManager.cacheKeys.USER_INFO) || {};\n\t\t\tconst userInfo = {\n\t\t\t\tnickname: cachedUserInfo.nickname || '加载中...',\n\t\t\t\tusername: cachedUserInfo.username || '',\n\t\t\t\tavatar_file: cachedUserInfo.avatar_file || null,\n\t\t\t\t_id: cachedUserInfo._id || ''\n\t\t\t};\n\t\t\t\n\t\t\treturn {\n\t\t\t\troleNames: [],\n\t\t\t\ttodoList: [],\n\t\t\t\ttodoCount: 0,\n\t\t\t\tuserRole: [],\n\t\t\t\thasRole: false,\n\t\t\t\tisWechatPlatform: false,\n\t\t\t\tisRefreshing: false,\n\t\t\t\tlastRefreshTime: 0,\n\t\t\t\tlocalUserInfo: userInfo,\n\t\t\t\tlocalHasLogin: hasLogin,\n\t\t\t\ttodoUpdateTimer: null,\n\t\t\t\tisTokenValid: true,\n\t\t\t\tresponsibleTaskCount: 0,\n\t\t\t\tsupervisionTaskCount: 0, // 厂长监督任务数量\n\t\t\t\tavatarLoaded: false, // 头像加载状态\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tuserInfo() {\n\t\t\t\t// 直接使用本地用户信息（已经在data()中优化了初始化逻辑）\n\t\t\t\treturn this.localUserInfo || {};\n\t\t\t},\n\t\t\thasLogin() {\n\t\t\t\treturn (store.hasLogin || this.localHasLogin) && this.isTokenValid;\n\t\t\t},\n\t\t\t// 检查是否有管理员权限\n\t\t\thasAdminPermission() {\n\t\t\t\treturn this.userRole.some(role => \n\t\t\t\t\t['admin'].includes(role)\n\t\t\t\t);\n\t\t\t},\n\t\t\t// 检查是否有负责人权限\n\t\t\thasResponsiblePermission() {\n\t\t\t\treturn this.userRole.some(role =>\n\t\t\t\t\t['responsible'].includes(role)\n\t\t\t\t);\n\t\t\t},\n\t\t\t// 检查是否有厂长权限\n\t\t\thasGMPermission() {\n\t\t\t\treturn this.userRole.some(role =>\n\t\t\t\t\t['GM', 'admin'].includes(role)\n\t\t\t\t);\n\t\t\t},\n\n\t\t},\n\t\tcreated() {\n\t\t\t// 监听登录成功事件\n\t\t\tuni.$on('uni-id-pages-login-success', () => {\n\t\t\t\t// 清除角色缓存\n\t\t\t\tcacheManager.remove(cacheManager.cacheKeys.USER_ROLE);\n\t\t\t\t// 立即刷新页面数据\n\t\t\t\tthis.refreshData(true);\n\t\t\t\t// 使用新的登录成功处理方法更新角标\n\t\t\t\ttodoBadgeManager.onLoginSuccess();\n\t\t\t});\n\t\t\t\n\t\t\t// 监听待办数量更新事件\n\t\t\tuni.$on('todo-count-updated', (count) => {\n\t\t\t\t// 只有在登录状态且有角色权限时才更新\n\t\t\t\tif (this.hasLogin && this.hasRole) {\n\t\t\t\t\tthis.todoCount = count;\n\t\t\t\t\t// 同时更新待办列表\n\t\t\t\t\tthis.getTodoList();\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 监听刷新待办列表事件\n\t\t\tuni.$on('refresh-todo-list', () => {\n\t\t\t\tif (this.hasLogin && this.hasRole) {\n\t\t\t\t\t// 直接刷新数据，不显示loading\n\t\t\t\t\tthis.refreshData(false);\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 监听全局强制登出UI更新事件（当在用户中心页面时）\n\t\t\tuni.$on('force-logout-ui-update', () => {\n\t\t\t\tthis.performLogout(false); // 不显示toast，因为App.vue已经显示了\n\t\t\t});\n\t\t\t\n\t\t\t// 监听反馈更新事件 - 添加这个监听来处理审核页面的操作\n\t\t\tuni.$on('feedback-updated', () => {\n\t\t\t\tif (this.hasLogin && this.hasRole) {\n\t\t\t\t\t// 延迟100ms执行，确保云端数据已更新\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.refreshData(false);\n\t\t\t\t\t}, 100);\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 监听用户中心页面刷新事件 - 专门针对审核页面操作后的刷新\n\t\t\tuni.$on('ucenter-need-refresh', (data) => {\n\t\t\t\tif (this.hasLogin && this.hasRole) {\n\t\t\t\t\tconsole.log('收到用户中心刷新事件:', data);\n\t\t\t\t\t// 立即刷新数据，不等待延迟\n\t\t\t\t\tthis.refreshData(false);\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 监听角标管理器的跨设备更新事件\n\t\t\tuni.$on('cross-device-update-detected', (data) => {\n\t\t\t\tif (this.hasLogin && this.hasRole) {\n\t\t\t\t\t// 智能判断是否需要刷新\n\t\t\t\t\tconst shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);\n\t\t\t\t\tif (shouldRefresh) {\n\t\t\t\t\t\tconsole.log('用户中心收到跨设备更新通知，静默刷新数据');\n\t\t\t\t\t\t// 静默刷新数据，不显示提示\n\t\t\t\t\t\tthis.refreshData(false);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 检测是否为微信环境\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.isWechatPlatform = true;\n\t\t\t// #endif\n\t\t\t\n\t\t\t// 启动待办事项数量定时更新\n\t\t\tthis.startTodoUpdateTimer();\n\n\t\t\t// 添加页面可见性变化监听（针对H5端）\n\t\t\t// #ifdef H5\n\t\t\tdocument.addEventListener('visibilitychange', this.handleVisibilityChange);\n\t\t\t// #endif\n\t\t},\n\t\tbeforeDestroy() {\n\t\t\t// 移除事件监听\n\t\t\tuni.$off('uni-id-pages-login-success');\n\t\t\tuni.$off('todo-count-updated');\n\t\t\tuni.$off('refresh-todo-list');\n\t\t\tuni.$off('force-logout-ui-update');\n\t\t\tuni.$off('feedback-updated'); // 移除新添加的监听\n\t\t\tuni.$off('ucenter-need-refresh'); // 移除用户中心刷新事件监听\n\t\t\tuni.$off('cross-device-update-detected'); // 移除跨设备更新事件监听\n\t\t\t\n\t\t\t// 清除定时器\n\t\t\tthis.clearTodoUpdateTimer();\n\n\t\t\t// 移除页面可见性变化监听（针对H5端）\n\t\t\t// #ifdef H5\n\t\t\tdocument.removeEventListener('visibilitychange', this.handleVisibilityChange);\n\t\t\t// #endif\n\t\t},\n\t\tonLoad() {\n\t\t\tif (this.hasLogin) {\n\t\t\t\t// 直接刷新所有数据\n\t\t\t\tthis.refreshData(true);\n\t\t\t}\n\t\t},\n\t\tonShow() {\n\t\t\tif (this.hasLogin) {\n\t\t\t\t// 调整缓存时间为30秒，配合跨设备同步机制，避免过度刷新\n\t\t\t\tconst now = Date.now();\n\t\t\t\tif (now - this.lastRefreshTime > 30000) {\n\t\t\t\t\tthis.refreshData(false);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 检查更新\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tcheckUpdate();\n\t\t\t// #endif\n\t\t\t\n\t\t\t// 每次显示页面时检查token状态\n\t\t\tthis.checkTokenStatus();\n\t\t\t\n\t\t\t// 强制同步角标状态，确保角标与页面数据一致\n\t\t\tif (this.hasLogin && this.hasRole) {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\ttodoBadgeManager.forceSyncBadge();\n\t\t\t\t}, 300);\n\t\t\t}\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tif (this.hasLogin) {\n\t\t\t\t// 刷新数据\n\t\t\t\tthis.refreshData(true).then(() => {\n\t\t\t\t\t// 完成下拉刷新\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\t// 添加刷新成功提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '刷新成功',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t}).catch(e => {\n\t\t\t\t\tconsole.error('刷新数据失败:', e);\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\t// 添加刷新失败提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '刷新失败',\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// 未登录状态下直接停止下拉刷新\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\thasLogin(newVal) {\n\t\t\t\tif (newVal) {\n\t\t\t\t\t// 用户登录状态变为已登录，立即获取用户信息和待办数量\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.refreshData(true);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 监听用户头像变化，重置加载状态\n\t\t\t'localUserInfo.avatar_file.url': {\n\t\t\t\thandler(newVal, oldVal) {\n\t\t\t\t\tif (newVal !== oldVal) {\n\t\t\t\t\t\tthis.avatarLoaded = false;\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\t'localUserInfo.avatar': {\n\t\t\t\thandler(newVal, oldVal) {\n\t\t\t\t\tif (newVal !== oldVal) {\n\t\t\t\t\t\tthis.avatarLoaded = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 头像加载成功\n\t\t\tonAvatarLoad() {\n\t\t\t\tthis.avatarLoaded = true;\n\t\t\t},\n\n\t\t\t// 头像加载失败\n\t\t\tonAvatarError() {\n\t\t\t\tthis.avatarLoaded = false;\n\t\t\t\tconsole.log('头像加载失败，显示默认头像');\n\t\t\t},\n\n\t\t\t// 显示功能开发中提示\n\t\t\tshowFeatureInDevelopment() {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '功能开发中，敬请期待',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 添加页面可见性变化处理函数（针对H5端）\n\t\t\thandleVisibilityChange() {\n\t\t\t\t// #ifdef H5\n\t\t\t\tif (document.visibilityState === 'visible') {\n\t\t\t\t\t// 页面变为可见时，立即刷新数据\n\t\t\t\t\tif (this.hasLogin && this.hasRole) {\n\t\t\t\t\t\tthis.refreshData(false);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t\n\t\t\t// 启动待办事项数量定时更新\n\t\t\tstartTodoUpdateTimer() {\n\t\t\t\t// 先清除可能存在的定时器\n\t\t\t\tthis.clearTodoUpdateTimer();\n\t\t\t\t\n\t\t\t\t// 简化定时器：只做基础的数据同步，跨设备检查交给角标管理器处理\n\t\t\t\tthis.todoUpdateTimer = setInterval(() => {\n\t\t\t\t\t// 只有在登录状态下才更新\n\t\t\t\t\tif (this.hasLogin && this.hasRole) {\n\t\t\t\t\t\tthis.updateTodoCountFromBadge();\n\t\t\t\t\t}\n\t\t\t\t}, 30000); // 调整为30秒，避免与角标管理器冲突\n\t\t\t\t\n\t\t\t\t// 立即执行一次\n\t\t\t\tif (this.hasLogin && this.hasRole) {\n\t\t\t\t\tthis.updateTodoCountFromBadge();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 清除待办事项更新定时器\n\t\t\tclearTodoUpdateTimer() {\n\t\t\t\tif (this.todoUpdateTimer) {\n\t\t\t\t\tclearInterval(this.todoUpdateTimer);\n\t\t\t\t\tthis.todoUpdateTimer = null;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 从角标管理器更新待办数量\n\t\t\tasync updateTodoCountFromBadge() {\n\t\t\t\ttry {\n\t\t\t\t\t// 直接从todoBadgeManager获取最新的待办数量\n\t\t\t\t\tconst count = await todoBadgeManager.getTodoCount();\n\t\t\t\t\t// 更新本地待办数量\n\t\t\t\t\tthis.todoCount = count;\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('更新待办数量失败:', e);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 统一的数据刷新方法，避免重复请求\n\t\t\tasync refreshData(showLoading = false) {\n\t\t\t\tif (!this.hasLogin || this.isRefreshing) return Promise.resolve();\n\t\t\t\t\n\t\t\t\tthis.isRefreshing = true;\n\t\t\t\t\n\t\t\t\tif (showLoading) {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 获取用户信息\n\t\t\t\t\tawait this.getUserInfo();\n\t\t\t\t\t\n\t\t\t\t\t// 智能检查：如果已有完整的角色信息，跳过角色获取\n\t\t\t\t\tif (!this.userRole || this.userRole.length === 0 || !this.roleNames || this.roleNames.length === 0) {\n\t\t\t\t\t\tawait this.getUserRole();\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果有特定角色，再获取待办列表\n\t\t\t\t\tif (this.hasRole) {\n\t\t\t\t\t\tawait this.getTodoList();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.todoList = [];\n\t\t\t\t\t\tthis.todoCount = 0;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果有负责人权限，获取任务数量\n\t\t\t\t\tif (this.hasResponsiblePermission) {\n\t\t\t\t\t\tawait this.getResponsibleTaskCount();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.responsibleTaskCount = 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 如果有厂长权限，获取监督任务数量\n\t\t\t\t\tif (this.hasGMPermission) {\n\t\t\t\t\t\tawait this.getSupervisionTaskCount();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.supervisionTaskCount = 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 更新最后刷新时间\n\t\t\t\t\tthis.lastRefreshTime = Date.now();\n\t\t\t\t\t\n\t\t\t\t\treturn Promise.resolve();\n\t\t\t\t} catch (e) {\n\t\t\t\t\t// 错误处理\n\t\t\t\t\t// #ifdef DEBUG\n\t\t\t\t\tconsole.error('刷新数据失败:', e);\n\t\t\t\t\t// #endif\n\t\t\t\t\treturn Promise.reject(e);\n\t\t\t\t} finally {\n\t\t\t\t\tif (showLoading) {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t}\n\t\t\t\t\tthis.isRefreshing = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取用户角色 - 改用cacheManager统一管理缓存\n\t\t\tasync getUserRole() {\n\t\t\t\ttry {\n\t\t\t\t\tif (!this.hasLogin) {\n\t\t\t\t\t\tthis.roleNames = ['未登录'];\n\t\t\t\t\t\tthis.userRole = [];\n\t\t\t\t\t\tthis.hasRole = false;\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 尝试从缓存获取角色信息\n\t\t\t\t\tconst cachedRole = cacheManager.get(cacheManager.cacheKeys.USER_ROLE);\n\t\t\t\t\tif (cachedRole) {\n\t\t\t\t\t\tthis.userRole = cachedRole.userRole;\n\t\t\t\t\t\tthis.roleNames = cachedRole.roleNames;\n\t\t\t\t\t\tthis.hasRole = cachedRole.hasRole;\n\t\t\t\t\t\t\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconst db = uniCloud.database();\n\t\t\t\t\tconst { result } = await db.collection('uni-id-users')\n\t\t\t\t\t\t.where(\"'_id' == $cloudEnv_uid\")\n\t\t\t\t\t\t.field('role')\n\t\t\t\t\t\t.get();\n\t\t\t\t\t\n\t\t\t\t\tif (result.data && result.data.length > 0) {\n\t\t\t\t\t\tthis.userRole = result.data[0].role || [];\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 手动设置角色名称映射\n\t\t\t\t\t\tconst roleNameMap = {\n\t\t\t\t\t\t\t'admin': '管理员',\n\t\t\t\t\t\t\t'responsible': '责任人',\n\t\t\t\t\t\t\t'reviser': '发布人',\n\t\t\t\t\t\t\t'supervisor': '主管',\n\t\t\t\t\t\t\t'PM': '副厂长',\n\t\t\t\t\t\t\t'GM': '厂长',\n\t\t\t\t\t\t\t'logistics': '后勤员',\n\t\t\t\t\t\t\t'dispatch': '调度员',\n\t\t\t\t\t\t\t'Integrated': '综合员',\n\t\t\t\t\t\t\t'operator': '设备员',\n\t\t\t\t\t\t\t'technician': '工艺员',\n\t\t\t\t\t\t\t'mechanic': '技术员',\n\t\t\t\t\t\t\t'user': '普通员工'\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查用户是否有特定角色\n\t\t\t\t\t\tthis.hasRole = this.userRole.some(role => \n\t\t\t\t\t\t\t['supervisor', 'PM', 'GM', 'admin', 'responsible'].includes(role)\n\t\t\t\t\t\t);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 将角色ID转换为中文名称\n\t\t\t\t\t\tif (this.userRole.length > 0) {\n\t\t\t\t\t\t\tthis.roleNames = this.userRole.map(roleId => \n\t\t\t\t\t\t\t\troleNameMap[roleId] || roleId\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果没有角色，显示为普通用户\n\t\t\t\t\t\t\tthis.roleNames = ['普通用户'];\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 缓存角色信息，有效期60分钟\n\t\t\t\t\t\tcacheManager.set(cacheManager.cacheKeys.USER_ROLE, {\n\t\t\t\t\t\t\tuserRole: this.userRole,\n\t\t\t\t\t\t\troleNames: this.roleNames,\n\t\t\t\t\t\t\thasRole: this.hasRole\n\t\t\t\t\t\t}, 60);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.roleNames = ['普通用户'];\n\t\t\t\t\t\tthis.userRole = [];\n\t\t\t\t\t\tthis.hasRole = false;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\t// #ifdef DEBUG\n\t\t\t\t\tconsole.error('获取用户角色失败:', e);\n\t\t\t\t\t// #endif\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否是token相关的错误\n\t\t\t\t\tconst errorMessage = e.message || e.toString();\n\t\t\t\t\tif (errorMessage.includes('token') || errorMessage.includes('unauthorized') || errorMessage.includes('expired')) {\n\t\t\t\t\t\tconsole.log('检测到token相关错误，清除待办数据');\n\t\t\t\t\t\t// 清除待办相关数据\n\t\t\t\t\t\tthis.todoList = [];\n\t\t\t\t\t\tthis.todoCount = 0;\n\t\t\t\t\t\tthis.hasRole = false;\n\t\t\t\t\t\t// 显示为普通用户，但不强制退出登录\n\t\t\t\t\t\tthis.roleNames = ['普通用户'];\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 其他错误，默认处理\n\t\t\t\t\t\tthis.roleNames = ['普通用户'];\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.userRole = [];\n\t\t\t\t\tthis.hasRole = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取项目名称 - 简化为直接返回项目名称\n\t\t\tgetProjectName(projectId) {\n\t\t\t\t// 由于feedback表的project字段直接存储文本值，直接返回即可\n\t\t\t\treturn projectId || '未分类';\n\t\t\t},\n\t\t\t\n\t\t\t// 获取待办列表 - 优化查询逻辑\n\t\t\tasync getTodoList() {\n\t\t\t\tif (!this.hasLogin || !this.hasRole) {\n\t\t\t\t\tthis.todoList = [];\n\t\t\t\t\tthis.todoCount = 0;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst db = uniCloud.database();\n\t\t\t\t\tconst dbCmd = db.command;\n\t\t\t\t\t\n\t\t\t\t\t// 简化查询条件构建\n\t\t\t\t\tconst whereConditions = this.buildTodoQueryConditions(dbCmd);\n\t\t\t\t\t\n\t\t\t\t\t// 先获取总数\n\t\t\t\t\tconst countRes = await db.collection('feedback')\n\t\t\t\t\t\t.where(whereConditions)\n\t\t\t\t\t\t.count();\n\t\t\t\t\t\n\t\t\t\t\tif (countRes.result && countRes.result.total !== undefined) {\n\t\t\t\t\t\tthis.todoCount = countRes.result.total;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 增加待办项数量限制为3条，优化加载性能\n\t\t\t\t\tif (this.todoCount > 0) {\n\t\t\t\t\t\tconst res = await db.collection('feedback')\n\t\t\t\t\t\t\t.where(whereConditions)\n\t\t\t\t\t\t\t.orderBy('createTime', 'desc')\n\t\t\t\t\t\t\t.limit(3) // 只显示前3条，提高性能\n\t\t\t\t\t\t\t.get();\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (res.result && res.result.data) {\n\t\t\t\t\t\t\tthis.todoList = res.result.data;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.todoList = [];\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.todoList = [];\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\t// #ifdef DEBUG\n\t\t\t\t\tconsole.error('获取待办列表失败', e);\n\t\t\t\t\t// #endif\n\t\t\t\t\tthis.todoList = [];\n\t\t\t\t\tthis.todoCount = 0;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取负责人任务数量\n\t\t\tasync getResponsibleTaskCount() {\n\t\t\t\tif (!this.hasLogin || !this.hasResponsiblePermission) {\n\t\t\t\t\tthis.responsibleTaskCount = 0;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-list',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'getMyTasks',\n\t\t\t\t\t\t\tstatus: 'assigned_to_responsible'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (res.result && res.result.code === 0) {\n\t\t\t\t\t\tthis.responsibleTaskCount = res.result.data.stats?.assigned || 0;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取负责人任务数量失败:', error);\n\t\t\t\t\tthis.responsibleTaskCount = 0;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取厂长监督任务数量\n\t\t\tasync getSupervisionTaskCount() {\n\t\t\t\tif (!this.hasLogin || !this.hasGMPermission) {\n\t\t\t\t\tthis.supervisionTaskCount = 0;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-list',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'getGMSupervisionTasks'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.result && res.result.code === 0) {\n\t\t\t\t\t\tconst stats = res.result.data.stats || {};\n\t\t\t\t\t\t// 计算需要关注的任务数量：执行中 + 待确认\n\t\t\t\t\t\tthis.supervisionTaskCount = (stats.assigned || 0) + (stats.pending || 0);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取厂长监督任务数量失败:', error);\n\t\t\t\t\tthis.supervisionTaskCount = 0;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 构建待办查询条件（统一待办版本）\n\t\t\tbuildTodoQueryConditions(dbCmd) {\n\t\t\t\tconst conditions = [];\n\t\t\t\t\n\t\t\t\t// 1. 审核待办\n\t\t\t\tif (this.userRole.includes('supervisor')) {\n\t\t\t\t\t// supervisor看到的是待主管审核的项目\n\t\t\t\t\tconditions.push({ workflowStatus: 'pending_supervisor' });\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (this.userRole.includes('PM')) {\n\t\t\t\t\t// PM看到的是待副厂长审核的项目\n\t\t\t\t\tconditions.push({ workflowStatus: 'pending_pm' });\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (this.userRole.includes('GM')) {\n\t\t\t\t\t// GM看到的是待厂长审核的项目 + 待指派负责人的项目 + 最终确认的项目\n\t\t\t\t\tconditions.push({ workflowStatus: 'pending_gm' });\n\t\t\t\t\tconditions.push({ workflowStatus: 'gm_approved_pending_assign' });\n\t\t\t\t\tconditions.push({ workflowStatus: 'completed_by_responsible' });\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 2. 指派任务待办（如果用户有responsible角色）\n\t\t\t\tif (this.userRole.includes('responsible')) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst currentUserInfo = uniCloud.getCurrentUserInfo();\n\t\t\t\t\t\tconst userId = currentUserInfo?.uid;\n\t\t\t\t\t\tif (userId) {\n\t\t\t\t\t\t\t// 待完成的指派任务\n\t\t\t\t\t\t\tconditions.push({\n\t\t\t\t\t\t\t\tworkflowStatus: 'assigned_to_responsible',\n\t\t\t\t\t\t\t\tresponsibleUserId: userId\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.warn('获取用户ID失败:', e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 3. 管理员可以看到所有待办，但不包括指派给负责人的任务\n\t\t\t\tif (this.userRole.includes('admin')) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tworkflowStatus: dbCmd.in([\n\t\t\t\t\t\t\t'pending_supervisor', \n\t\t\t\t\t\t\t'pending_pm', \n\t\t\t\t\t\t\t'pending_gm', \n\t\t\t\t\t\t\t'gm_approved_pending_assign',\n\t\t\t\t\t\t\t// 移除 'assigned_to_responsible',\n\t\t\t\t\t\t\t'completed_by_responsible'\n\t\t\t\t\t\t])\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果有条件，使用or查询\n\t\t\t\tif (conditions.length > 0) {\n\t\t\t\t\treturn dbCmd.or(conditions);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 默认返回空条件\n\t\t\t\treturn {};\n\t\t\t},\n\t\t\t\n\t\t\t// 格式化日期\n\t\t\tformatDate(timestamp) {\n\t\t\t\tif (!timestamp) return '';\n\t\t\t\treturn formatDateUtil(timestamp);\n\t\t\t},\n\t\t\t\n\t\t\t// 获取待办时间文本\n\t\t\tgetTodoTimeText(item) {\n\t\t\t\t// 自定义时间格式：MM-DD HH:mm（去掉年份和秒数）\n\t\t\t\tconst timeFormat = 'MM-DD HH:mm';\n\t\t\t\tif (item.workflowStatus === 'assigned_to_responsible' && item.assignedTime) {\n\t\t\t\t\treturn `指派时间：${formatDateUtil(item.assignedTime, timeFormat)}`;\n\t\t\t\t} else if (item.workflowStatus === 'completed_by_responsible' && item.completedByResponsibleTime) {\n\t\t\t\t\treturn `完成时间：${formatDateUtil(item.completedByResponsibleTime, timeFormat)}`;\n\t\t\t\t} else {\n\t\t\t\t\treturn `提交时间：${formatDateUtil(item.createTime, timeFormat)}`;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取待办类型文本\n\t\t\tgetTodoTypeText(status) {\n\t\t\t\tconst typeMap = {\n\t\t\t\t\t'pending_supervisor': '待主管审核',\n\t\t\t\t\t'pending_pm': '待副厂长审核',\n\t\t\t\t\t'pending_gm': '待厂长审核',\n\t\t\t\t\t'gm_approved_pending_assign': '待指派负责人',\n\t\t\t\t\t'assigned_to_responsible': '待我完成',\n\t\t\t\t\t'completed_by_responsible': '待最终确认'\n\t\t\t\t};\n\t\t\t\treturn typeMap[status] || '待处理';\n\t\t\t},\n\t\t\t\n\t\t\t// 处理待办点击事件\n\t\t\thandleTodoClick(item) {\n\t\t\t\tif (item.workflowStatus === 'assigned_to_responsible') {\n\t\t\t\t\t// 指派任务，跳转到任务管理页面\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/ucenter_pkg/responsible-tasks'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 审核待办，跳转到审核页面\n\t\t\t\t\tthis.goToExamine(item._id);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 跳转到审核页面\n\t\t\tgoToExamine(id) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/feedback_pkg/examine?id=${id}`,\n\t\t\t\t\tevents: {\n\t\t\t\t\t\trefreshData: () => {\n\t\t\t\t\t\t\tthis.refreshData(false);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 公告通知\n\t\t\tgoToReadNewsLog() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/notice/list'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// Excel导出\n\t\t\texportexcel() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/ucenter_pkg/export-excel'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 检查导出权限\n\t\t\tcheckExportPermission() {\n\t\t\t\t// 检查用户是否有特定角色\n\t\t\t\tconst hasExportPermission = this.userRole.some(role => \n\t\t\t\t\t['reviser', 'supervisor', 'PM', 'GM', 'admin', 'dispatch'].includes(role)\n\t\t\t\t);\n\t\t\t\t\n\t\t\t\tif (hasExportPermission) {\n\t\t\t\t\t// 有权限，跳转到导出页面\n\t\t\t\t\tthis.exportexcel();\n\t\t\t\t} else {\n\t\t\t\t\t// 无权限，显示提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '权限不够,无法查看',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 跳转到负责人任务页面\n\t\t\tgoToResponsibleTasks() {\n\t\t\t\tif (!this.hasResponsiblePermission) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '权限不足，无法访问任务管理',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/ucenter_pkg/responsible-tasks',\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('跳转任务管理页面失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 跳转到用户管理页面\n\t\t\tgoToUserManagement() {\n\t\t\t\tif (!this.hasAdminPermission) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '权限不足，无法访问用户管理',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/ucenter_pkg/user-management',\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('跳转用户管理页面失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 跳转到厂长监督页面\n\t\t\tgoToGMSupervision() {\n\t\t\t\tif (!this.hasGMPermission) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '权限不足，只有厂长才能访问',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/ucenter_pkg/gm-supervision',\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('跳转厂长监督页面失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 修改昵称/用户设置\n\t\t\tmodifyNickname() {\n\t\t\t\t// 直接跳转到uni-id-pages提供的修改页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/userinfo/userinfo?showLoginManage=false&showUserInfo=false&showSet=false&showEdit=true'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t\n\t\t\t// 退出登录\n\t\t\tasync logout() {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await uni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '确定要退出登录吗？',\n\t\t\t\t\t\tconfirmText: '退出',\n\t\t\t\t\t\tcancelText: '取消'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: '退出中...',\n\t\t\t\t\t\t\tmask: true\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 先清除角标\n\t\t\t\t\t\ttodoBadgeManager.forceCleanBadge();\n\t\t\t\t\t\t\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst uniIdCo = uniCloud.importObject('uni-id-co');\n\t\t\t\t\t\t\tawait uniIdCo.logout();\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t// 即使云函数调用失败，也执行本地登出\n\t\t\t\t\t\t\tconsole.error('云函数退出登录失败:', e);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 执行本地退出清理\n\t\t\t\t\t\tthis.performLogout(false);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 再次确保角标被清除\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\ttodoBadgeManager.forceCleanBadge();\n\t\t\t\t\t\t}, 100);\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '已退出登录',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '退出登录失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 跳转到待办列表页面\n\t\t\tgoToTodoList() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/ucenter_pkg/todo'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 根据平台自动跳转到相应的登录页面\n\t\t\tgoToLogin() {\n\t\t\t\t// 微信小程序环境\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd'\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// 非微信小程序环境\n\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/login/login-withpwd'\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t\n\t\t\t// 获取用户信息\n\t\t\tasync getUserInfo() {\n\t\t\t\ttry {\n\t\t\t\t\tconst token = uni.getStorageSync('uni_id_token') || '';\n\t\t\t\t\tthis.localHasLogin = !!token;\n\t\t\t\t\t\n\t\t\t\t\tif (!this.localHasLogin) {\n\t\t\t\t\t\tthis.localUserInfo = {};\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 先尝试从自己的缓存获取\n\t\t\t\t\tconst cachedInfo = cacheManager.get(cacheManager.cacheKeys.USER_INFO) || {};\n\t\t\t\t\tif (cachedInfo.nickname || cachedInfo.username) {\n\t\t\t\t\t\tthis.localUserInfo = cachedInfo;\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 缓存中没有完整信息，从数据库获取\n\t\t\t\t\tconst db = uniCloud.database();\n\t\t\t\t\tconst { result } = await db.collection('uni-id-users')\n\t\t\t\t\t\t.where(\"'_id' == $cloudEnv_uid\")\n\t\t\t\t\t\t.field('nickname, username, avatar_file')\n\t\t\t\t\t\t.get();\n\t\t\t\t\t\n\t\t\t\t\tif (result.data && result.data.length > 0) {\n\t\t\t\t\t\tconst userData = result.data[0];\n\t\t\t\t\t\tthis.localUserInfo = {\n\t\t\t\t\t\t\t_id: userData._id,\n\t\t\t\t\t\t\tnickname: userData.nickname || userData.username || '未设置昵称',\n\t\t\t\t\t\t\tusername: userData.username || userData.nickname || '未设置昵称',\n\t\t\t\t\t\t\tavatar_file: userData.avatar_file || null\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\t// 重置头像加载状态，确保新头像重新加载\n\t\t\t\t\t\tthis.avatarLoaded = false;\n\n\t\t\t\t\t\t// 更新自己的缓存系统\n\t\t\t\t\t\tcacheManager.set(cacheManager.cacheKeys.USER_INFO, this.localUserInfo);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.localUserInfo = { nickname: '用户', username: 'user' };\n\t\t\t\t\t\tthis.avatarLoaded = false;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取用户信息失败:', e);\n\t\t\t\t\tthis.localUserInfo = { nickname: '用户', username: 'user' };\n\t\t\t\t\tthis.avatarLoaded = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 导航到登录页\n\t\t\tnavToLogin() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 导航到指定页面\n\t\t\tnavTo(url) {\n\t\t\t\t// 荣誉展厅特殊处理：预加载优化\n\t\t\t\tif (url === '/pages/honor_pkg/gallery/index') {\n\t\t\t\t\tthis.navToHonorGallery(url);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 使用缓存优化，记录最近访问的页面\n\t\t\t\tconst recentPages = cacheManager.get('recent_pages', []);\n\t\t\t\tif (!recentPages.includes(url)) {\n\t\t\t\t\trecentPages.unshift(url);\n\t\t\t\t\t// 只保留最近10个页面\n\t\t\t\t\tif (recentPages.length > 10) {\n\t\t\t\t\t\trecentPages.pop();\n\t\t\t\t\t}\n\t\t\t\t\tcacheManager.set('recent_pages', recentPages);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl,\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('导航失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '页面不存在',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 荣誉展厅专用导航 - 优化加载体验\n\t\t\tnavToHonorGallery(url) {\n\t\t\t\t// 记录访问历史\n\t\t\t\tconst recentPages = cacheManager.get('recent_pages', []);\n\t\t\t\tif (!recentPages.includes(url)) {\n\t\t\t\t\trecentPages.unshift(url);\n\t\t\t\t\tif (recentPages.length > 10) {\n\t\t\t\t\t\trecentPages.pop();\n\t\t\t\t\t}\n\t\t\t\t\tcacheManager.set('recent_pages', recentPages);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 保持页面切换动画，但优化加载体验\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl,\n\t\t\t\t\tanimationType: 'slide-in-right', // 使用滑入动画替代默认\n\t\t\t\t\tanimationDuration: 200,          // 缩短动画时长\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t// 跳转成功后的处理\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('导航失败:', err);\n\t\t\t\t\t\t// 降级到普通跳转\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl,\n\t\t\t\t\t\t\tfail: (err2) => {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '页面不存在',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 检查token状态\n\t\t\tasync checkTokenStatus() {\n\t\t\t\ttry {\n\t\t\t\t\tconst token = uni.getStorageSync('uni_id_token');\n\t\t\t\t\tif (!token) {\n\t\t\t\t\t\tthis.handleTokenInvalid();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 验证token\n\t\t\t\t\tconst tokenValid = await this.validateToken();\n\t\t\t\t\tif (!tokenValid) {\n\t\t\t\t\t\tthis.handleTokenInvalid();\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('验证登录状态失败:', error);\n\t\t\t\t\tthis.handleTokenInvalid();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 验证token的方法\n\t\t\tasync validateToken() {\n\t\t\t\ttry {\n\t\t\t\t\tconst token = uni.getStorageSync('uni_id_token');\n\t\t\t\t\tif (!token) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tconst tokenExpired = uni.getStorageSync('uni_id_token_expired');\n\t\t\t\t\tif (tokenExpired < Date.now()) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\treturn true;\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Token validation error:', error);\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 统一的登出处理方法\n\t\t\tperformLogout(showToast = true) {\n\t\t\t\t// 第一步：立即设置登录状态为false，确保待办区域立即消失\n\t\t\t\tthis.localHasLogin = false;\n\t\t\t\t\n\t\t\t\t// 第二步：清除所有登录相关存储\n\t\t\t\tuni.removeStorageSync('uni_id_token');\n\t\t\t\tuni.removeStorageSync('uni_id_token_expired');\n\t\t\t\tuni.removeStorageSync('uni_id_user');\n\t\t\t\tuni.removeStorageSync('uni-id-pages-userInfo');\n\t\t\t\t\n\t\t\t\t// 第三步：清除缓存\n\t\t\t\tcacheManager.remove(cacheManager.cacheKeys.USER_INFO);\n\t\t\t\tcacheManager.remove(cacheManager.cacheKeys.USER_ROLE);\n\t\t\t\tcacheManager.remove(cacheManager.cacheKeys.PROJECT_OPTIONS);\n\t\t\t\t\n\t\t\t\t// 第四步：更新store状态\n\t\t\t\tmutations.setUserInfo({}, {cover: true});\n\t\t\t\t\n\t\t\t\t// 第五步：清除角标\n\t\t\t\ttodoBadgeManager.clearBadge();\n\t\t\t\t\n\t\t\t\t// 第六步：清除敏感缓存\n\t\t\t\tthis.clearSensitiveCache();\n\t\t\t\t\n\t\t\t\t// 第七步：重置其他页面状态\n\t\t\t\tObject.assign(this, {\n\t\t\t\t\troleNames: [],\n\t\t\t\t\ttodoList: [],\n\t\t\t\t\ttodoCount: 0,\n\t\t\t\t\tuserRole: [],\n\t\t\t\t\thasRole: false,\n\t\t\t\t\tlocalUserInfo: {},\n\t\t\t\t\tisTokenValid: false\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 显示提示\n\t\t\t\tif (showToast) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '登录已过期，请重新登录',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 更新页面状态\n\t\t\t\tthis.$nextTick(() => this.getUserInfo());\n\t\t\t},\n\t\t\t\n\t\t\t// 清除敏感缓存数据（与拦截器保持一致的逻辑）\n\t\t\tclearSensitiveCache() {\n\t\t\t\ttry {\n\t\t\t\t\tconst storageInfo = uni.getStorageInfoSync();\n\t\t\t\t\tconst keys = storageInfo.keys;\n\t\t\t\t\t\n\t\t\t\t\t// 定义需要清除的真正敏感数据（个人设备使用场景，重点保护隐私）\n\t\t\t\t\tconst sensitivePatterns = [\n\t\t\t\t\t\t'user_info_',     // 用户详细信息（可能包含个人隐私）\n\t\t\t\t\t\t'user_mgmt_',     // 用户管理数据（管理员功能）\n\t\t\t\t\t];\n\t\t\t\t\t\n\t\t\t\t\t// 定义需要明确保留的数据\n\t\t\t\t\tconst preserveKeys = [\n\t\t\t\t\t\t'_DC_STAT_UUID',      // 设备统计标识\n\t\t\t\t\t\tgetCacheKey('recent_pages'), // 最近访问页面（动态生成键名）\n\t\t\t\t\t\t'last_app_start_time', // 应用启动时间\n\t\t\t\t\t\t'uni-id-pages-userInfo', // 框架用户信息（可能为空对象）\n\t\t\t\t\t];\n\t\t\t\t\t\n\t\t\t\t\tlet clearedCount = 0;\n\t\t\t\t\t\n\t\t\t\t\tkeys.forEach(key => {\n\t\t\t\t\t\t// 检查是否在保留列表中\n\t\t\t\t\t\tif (preserveKeys.includes(key)) {\n\t\t\t\t\t\t\treturn; // 跳过保留的键\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查是否匹配敏感数据模式\n\t\t\t\t\t\tconst shouldClear = sensitivePatterns.some(pattern => \n\t\t\t\t\t\t\tkey === pattern || key.startsWith(pattern)\n\t\t\t\t\t\t);\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (shouldClear) {\n\t\t\t\t\t\t\tuni.removeStorageSync(key);\n\t\t\t\t\t\t\tclearedCount++;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('清除敏感缓存失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 处理token失效（兼容原有逻辑）\n\t\t\thandleTokenInvalid() {\n\t\t\t\tthis.performLogout(false);\n\t\t\t},\n\t\t\t\n\t\t\t// 智能判断是否需要刷新\n\t\t\tshouldRefreshOnCrossDeviceUpdate(data) {\n\t\t\t\t// 如果距离上次刷新时间太短（小于15秒），避免频繁刷新\n\t\t\t\tconst timeSinceLastRefresh = Date.now() - this.lastRefreshTime;\n\t\t\t\tif (timeSinceLastRefresh < 15000) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果更新类型包含待办相关的操作，需要刷新\n\t\t\t\tif (data.updateTypes) {\n\t\t\t\t\tconst relevantTypes = ['workflow_status_changed', 'feedback_submitted'];\n\t\t\t\t\tconst hasRelevantUpdate = data.updateTypes.some(type => relevantTypes.includes(type));\n\t\t\t\t\tif (hasRelevantUpdate) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果有多个更新记录，可能需要刷新\n\t\t\t\tif (data.updateCount > 1) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 默认不刷新，避免过度刷新\n\t\t\t\treturn false;\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t/* #ifndef APP-NVUE */\n\tview {\n\t\tdisplay: flex;\n\t\tbox-sizing: border-box;\n\t\tflex-direction: column;\n\t}\n\n\tpage {\n\t\tbackground-color: #f8f9fc;\n\t}\n\t/* #endif*/\n\t\n\t/* 定义动画 */\n\t@keyframes fadeInUp {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(20rpx);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\t\n\t.container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f8f9fc;\n\t\tpadding: 30rpx;\n\t\tbackground: linear-gradient(145deg, #f8faff 0%, #e9f0f8 100%);\n\t\tletter-spacing: 1rpx;\n\t}\n\t\n\t/* 用户信息卡片 */\n\t.user-card {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 24rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);\n\t\tmargin-bottom: 30rpx;\n\t\tposition: relative;\n\t\tanimation: fadeInUp 0.5s ease;\n\t\ttransition: all 0.3s ease;\n\t\t\n\t\t&:active {\n\t\t\ttransform: scale(0.98);\n\t\t}\t\t\n\t}\n\t\n\t.avatar-section {\n\t\tmargin-right: 30rpx;\n\t}\n\t\n\t.avatar-container {\n\t\twidth: 150rpx;\n\t\theight: 150rpx;\n\t\tborder-radius: 75rpx;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);\n\t}\n\n\t.default-avatar-bg {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tborder-radius: 75rpx;\n\t\tbackground: linear-gradient(135deg, #3a86ff 0%, #2563eb 100%);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: 1;\n\t}\n\n\t.avatar-image {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tborder-radius: 75rpx;\n\t\tz-index: 2;\n\t\ttransition: opacity 0.3s ease;\n\t}\n\n\t/* 未登录状态的默认头像样式 */\n\t.default-avatar {\n\t\twidth: 150rpx;\n\t\theight: 150rpx;\n\t\tborder-radius: 75rpx;\n\t\tbackground: linear-gradient(135deg, #3a86ff 0%, #2563eb 100%);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbox-shadow: 0 8rpx 16rpx rgba(0, 120, 255, 0.2);\n\t}\n\n\t.user-info {\n\t\tflex: 1;\n\t}\n\t\n\t.username {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #2b2e4a;\n\t\tmargin-bottom: 16rpx;\n\t\tdisplay: block;\n\t\ttext-align: left !important;\n\t\talign-self: flex-start;\n\t\twidth: 100%;\n\t\t\n\t\t&.login-prompt {\n\t\t\tcolor: #4a78c9;\n\t\t}\n\t}\n\t\n\t.login-tip {\n\t\tfont-size: 28rpx;\n\t\tcolor: #8a94a6;\n\t\tmargin-bottom: 16rpx;\n\t}\n\t\n\t.card-arrow {\n\t\tposition: absolute;\n\t\tright: 30rpx;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t}\n\t\n\t.role-tags {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: wrap;\n\t}\n\t\n\t.role-tag {\n\t\tfont-size: 24rpx;\n\t\tcolor: #4a78c9;\n\t\tbackground-color: rgba(74, 120, 201, 0.1);\n\t\tpadding: 6rpx 20rpx;\n\t\tborder-radius: 30rpx;\n\t\tmargin-right: 16rpx;\n\t\tmargin-bottom: 8rpx;\n\t\ttransition: all 0.2s ease;\n\t\tletter-spacing: 1.5rpx;\n\t\t\n\t\t&:active {\n\t\t\tbackground-color: rgba(74, 120, 201, 0.2);\n\t\t}\n\t}\n\t\n\t/* 登录提示区域 */\n\t.login-required-tip {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 24rpx;\n\t\tpadding: 60rpx 30rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);\n\t\tmargin-top: 30rpx;\n\t\tanimation: fadeInUp 0.6s ease;\n\t}\n\t\n\t.login-required-image {\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tmargin-bottom: 30rpx;\n\t\topacity: 0.9;\n\t}\n\t\n\t.login-required-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #8a94a6;\n\t\ttext-align: center;\n\t\tletter-spacing: 2rpx;\n\t}\n\t\n\t/* 功能中心区域 */\n\t.patrol-section {\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 24rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);\n\t\tanimation: fadeInUp 0.5s ease;\n\t\tanimation-delay: 0.1s;\n\t\tanimation-fill-mode: both;\n\t}\n\t\n\t.patrol-title {\n\t\tfont-size: 34rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #2b2e4a;\n\t\tmargin-bottom: 36rpx;\n\t\tpadding-left: 20rpx;\n\t\tposition: relative;\n\t\tletter-spacing: 2rpx;\n\t\t\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\tleft: 0;\n\t\t\ttop: 50%;\n\t\t\ttransform: translateY(-50%);\n\t\t\twidth: 6rpx;\n\t\t\theight: 32rpx;\n\t\t\tbackground: linear-gradient(180deg, #3a86ff 0%, #2563eb 100%);\n\t\t\tborder-radius: 6rpx;\n\t\t}\n\t}\n\t\n\t.patrol-scroll {\n\t\twhite-space: nowrap;\n\t\twidth: 100%;\n\t}\n\t\n\t/* #ifdef MP-WEIXIN */\n\t/* 在微信小程序环境下隐藏滚动条 */\n\t.patrol-scroll ::-webkit-scrollbar {\n\t\tdisplay: none;\n\t\twidth: 0;\n\t\theight: 0;\n\t\tbackground: transparent;\n\t}\n\n\t.patrol-scroll {\n\t\tscrollbar-width: none; /* Firefox */\n\t\t-ms-overflow-style: none; /* IE and Edge */\n\t}\n\t/* #endif */\n\n\t/* #ifndef MP-WEIXIN */\n\t/* 非微信小程序环境下显示滚动条 */\n\t.patrol-scroll ::-webkit-scrollbar {\n\t\twidth: 6px;\n\t\theight: 6px;\n\t}\n\n\t.patrol-scroll ::-webkit-scrollbar-thumb {\n\t\tbackground: rgba(0, 0, 0, 0.2);\n\t\tborder-radius: 3px;\n\t}\n\n\t.patrol-scroll ::-webkit-scrollbar-thumb:hover {\n\t\tbackground: rgba(0, 0, 0, 0.3);\n\t}\n\n\t.patrol-scroll {\n\t\tscrollbar-width: thin;\n\t\tscrollbar-color: rgba(0, 0, 0, 0.2) transparent;\n\t}\n\t/* #endif */\n\t\n\t.patrol-grid {\n\t\tdisplay: inline-flex;\n\t\tflex-direction: row;\n\t\tpadding: 10rpx 0;\n\t}\n\t\n\t.patrol-item {\n\t\tdisplay: inline-flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\twidth: 160rpx;\n\t\tmargin-right: 50rpx;\n\t\ttransition: all 0.3s ease;\n\t\t\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t}\n\t}\n\t\n\t/* 图标样式统一管理 */\n\t.action-icon, .patrol-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 12rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.patrol-icon {\n\t\twidth: 120rpx;\n\t\theight: 120rpx;\n\t\tborder-radius: 50%;\n\t\tmargin-bottom: 16rpx;\n\t\tbox-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\t\n\t\t&:before {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tbackground: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));\n\t\t\tz-index: 1;\n\t\t}\n\t\t\n\t\t&:active {\n\t\t\ttransform: scale(0.9);\n\t\t}\n\t}\n\t\n\t/* 图标颜色-使用渐变色 */\n\t.news-icon { background: linear-gradient(145deg, #5586e8, #2563eb); }\n\t.export-icon { background: linear-gradient(145deg, #49a2e3, #3794dc); }\n\t.responsible-tasks-icon { background: linear-gradient(145deg, #4CAF50, #45a049); }\n\t.user-management-icon { background: linear-gradient(145deg, #667eea, #764ba2); }\n\t.gm-supervision-icon { background: linear-gradient(145deg, #0ea5e9, #3b82f6); } /* 厂长监督-蓝色水务主题 */\n\t.cleanup-icon { background: linear-gradient(145deg, #ff6b6b, #ee5a24); }\n\t.settings-icon { background: linear-gradient(145deg, #7b8de0, #5e6fd8); }\n\t.logout-icon { background: linear-gradient(145deg, #e06666, #d44c4c); }\n\t.record-icon { background: linear-gradient(145deg, #47b8e0, #32a7d6); } /* 巡视记录 */\n\t.calendar-icon { background: linear-gradient(145deg, #4a95e5, #3887df); }\n\t.area-icon { background: linear-gradient(145deg, #4aabe5, #3a9ddf); }\n\t.route-icon { background: linear-gradient(145deg, #7469d4, #5c4fc2); } /* 线路管理-紫色系 */\n\t.manage-icon { background: linear-gradient(145deg, #e06666, #d44c4c); }\n\t.shift-icon { background: linear-gradient(145deg, #e0984a, #d6893c); } /* 班次时间 */\n\t.system-settings-icon { background: linear-gradient(145deg, #4a95e5, #3887df); }\n\t.point-icon { background: linear-gradient(145deg, #66aee0, #4ca0d9); } /* 点位管理 */\n\t.guide-icon { background: linear-gradient(145deg, #7b8de0, #5e6fd8); }\n\t.database-icon { background: linear-gradient(145deg, #e06666, #d44c4c); }\n\t.task-icon { background: linear-gradient(145deg, #3975d9, #2862c6); } /* 任务管理-深蓝色系 */\n\t.collection-icon { background: linear-gradient(145deg, #8e44ad, #6c3483); } /* 数据采集-紫色系 */\n\t.notice-icon { background: linear-gradient(145deg, #4a95e5, #3887df); }\n\t.config-icon { background: linear-gradient(145deg, #4cb050, #389e3c); }\n\t.qrcode-icon { background: linear-gradient(145deg, #4cb050, #389e3c); }\n\t.gallery-icon { background: linear-gradient(145deg, #ff6b6b, #ee5a24); } /* 荣誉展厅-温暖橙红色 */\n\t\n\t.patrol-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #3d4b66;\n\t\ttext-align: center;\n\t\twhite-space: normal;\n\t\twidth: 100%;\n\t\tletter-spacing: 2rpx;\n\t\tmargin-top: 12rpx;\n\t\tfont-weight: 500;\n\t}\n\t\n\t/* 功能按钮区域 */\n\t.action-list {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 24rpx;\n\t\toverflow: hidden;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);\n\t\tanimation: fadeInUp 0.5s ease;\n\t\tanimation-delay: 0.2s;\n\t\tanimation-fill-mode: both;\n\t}\n\t\n\t.action-item {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 1rpx solid #eef0f6;\n\t\ttransition: all 0.2s ease;\n\t\t\n\t\t&:last-child {\n\t\t\tborder-bottom: none;\n\t\t}\n\t\t\n\t\t&:active {\n\t\t\tbackground-color: #f8f9fc;\n\t\t}\n\t}\n\t\n\t.action-icon {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.12);\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\t\n\t\t&:before {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\twidth: 100%;\n\t\t\theight: 50%;\n\t\t\tbackground: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));\n\t\t\tz-index: 1;\n\t\t}\n\t}\n\t\n\t.action-content {\n\t\tflex: 1;\n\t\tmargin-left: 24rpx;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-start;\n\t}\n\t\n\t.action-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #3d4b66;\n\t\tletter-spacing: 2rpx;\n\t\tposition: relative;\n\t\ttext-align: left;\n\t\talign-self: flex-start;\n\t}\n\t\n\t.action-badge {\n\t\tbackground: #ff5a5f;\n\t\tcolor: white;\n\t\tfont-size: 20rpx;\n\t\tpadding: 2rpx 8rpx;\n\t\tborder-radius: 10rpx;\n\t\tmin-width: 24rpx;\n\t\ttext-align: center;\n\t\tline-height: 1.2;\n\t\tposition: absolute;\n\t\ttop: -16rpx;\n\t\tright: -5rpx; /* 调整到更贴近\"务\"字的右上角 */\n\t\ttransform: scale(0.9);\n\t\tz-index: 10;\n\t}\n\t\n\t.logout-text {\n\t\tcolor: #ff5a5f;\n\t}\n\t\n\t/* 待办事项区域 */\n\t.todo-section {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 24rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);\n\t\tanimation: fadeInUp 0.5s ease;\n\t\tanimation-delay: 0.3s;\n\t\tanimation-fill-mode: both;\n\t}\n\t\n\t.section-header {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.section-title {\n\t\tfont-size: 34rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #2b2e4a;\n\t\tposition: relative;\n\t\tpadding-left: 20rpx;\n\t\tletter-spacing: 2rpx;\n\t\t\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\tleft: 0;\n\t\t\ttop: 50%;\n\t\t\ttransform: translateY(-50%);\n\t\t\twidth: 6rpx;\n\t\t\theight: 32rpx;\n\t\t\tbackground: linear-gradient(180deg, #ff5a5f 0%, #ff3a3f 100%);\n\t\t\tborder-radius: 6rpx;\n\t\t}\n\t}\n\t\n\t.todo-count-wrapper {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\t\n\t.todo-count {\n\t\tfont-size: 26rpx;\n\t\tcolor: #ff5a5f;\n\t\tbackground-color: rgba(255, 90, 95, 0.1);\n\t\tpadding: 8rpx 20rpx;\n\t\tborder-radius: 30rpx;\n\t\ttransition: all 0.2s ease;\n\t\t\n\t\t&:active {\n\t\t\tbackground-color: rgba(255, 90, 95, 0.2);\n\t\t}\n\t}\n\t\n\t.todo-list {\n\t\tmargin-top: 20rpx;\n\t}\n\t\n\t.todo-item {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tpadding: 24rpx;\n\t\tborder-bottom: 1rpx solid #eef0f6;\n\t\tborder-radius: 12rpx;\n\t\tmargin-bottom: 16rpx;\n\t\ttransition: all 0.3s ease;\n\t\tbackground-color: #f8f9fc;\n\t\t\n\t\t&:last-child {\n\t\t\tborder-bottom: none;\n\t\t\tmargin-bottom: 0;\n\t\t}\n\t\t\n\t\t&:active {\n\t\t\tbackground-color: #eef1f8;\n\t\t\ttransform: translateY(2rpx);\n\t\t}\n\t\t\n\t\t.todo-content {\n\t\t\tflex: 1;\n\t\t\tmargin-right: 20rpx;\n\t\t}\n\t\t\n\t\t.todo-title {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 16rpx;\n\t\t\tflex-wrap: wrap;\n\t\t\t\n\t\t\t.name {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #2b2e4a;\n\t\t\t\tmargin-right: 16rpx;\n\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\tletter-spacing: 2rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.project {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #4a78c9;\n\t\t\t\tbackground-color: rgba(74, 120, 201, 0.1);\n\t\t\t\tpadding: 4rpx 16rpx;\n\t\t\t\tborder-radius: 20rpx;\n\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\tletter-spacing: 2rpx;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.description {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #718096;\n\t\t\tmargin-bottom: 16rpx;\n\t\t\tdisplay: -webkit-box;\n\t\t\t-webkit-box-orient: vertical;\n\t\t\t-webkit-line-clamp: 2;\n\t\t\tline-clamp: 2;\n\t\t\toverflow: hidden;\n\t\t\tline-height: 1.6;\n\t\t\tletter-spacing: 1.5rpx;\n\t\t}\n\t\t\n\t\t.todo-footer {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t\t\n\t\t\t.time {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #a0aec0;\n\t\t\t\tletter-spacing: 1rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.todo-type {\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\tcolor: #4a78c9;\n\t\t\t\tbackground-color: rgba(74, 120, 201, 0.1);\n\t\t\t\tpadding: 4rpx 12rpx;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\tborder: 1rpx solid rgba(74, 120, 201, 0.3);\n\t\t\t\tmargin-left: 16rpx;\n\t\t\t\tletter-spacing: 1rpx;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.empty-todo {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 60rpx 0;\n\t\t\n\t\t.empty-image {\n\t\t\twidth: 200rpx;\n\t\t\theight: 200rpx;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\topacity: 0.8;\n\t\t}\n\t\t\n\t\t.empty-text {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #8a94a6;\n\t\t\ttext-align: center;\n\t\t\tletter-spacing: 2rpx;\n\t\t}\n\t}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ucenter.vue?vue&type=style&index=0&id=4883731c&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ucenter.vue?vue&type=style&index=0&id=4883731c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571662059\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
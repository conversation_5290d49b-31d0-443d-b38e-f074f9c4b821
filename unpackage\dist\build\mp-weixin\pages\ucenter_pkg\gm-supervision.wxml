<view class="container data-v-649ce15b"><view class="page-header data-v-649ce15b"><text class="page-title data-v-649ce15b">指派任务监督</text><text class="page-subtitle data-v-649ce15b">实时监控指派任务的执行情况</text><view class="timing-explanation data-v-649ce15b"><text class="explanation-text data-v-649ce15b">💡 超时说明：执行中任务超过7天为警告，超过14天为超时</text></view></view><view class="stats-section data-v-649ce15b"><view class="stats-grid data-v-649ce15b"><view data-event-opts="{{[['tap',[['filterByStatus',['assigned_to_responsible']]]]]}}" class="stat-card data-v-649ce15b" bindtap="__e"><view class="stat-number assigned data-v-649ce15b">{{taskStats.assigned||0}}</view><text class="stat-label data-v-649ce15b">执行中</text></view><view data-event-opts="{{[['tap',[['filterByStatus',['completed_by_responsible']]]]]}}" class="stat-card data-v-649ce15b" bindtap="__e"><view class="stat-number pending data-v-649ce15b">{{taskStats.pending||0}}</view><text class="stat-label data-v-649ce15b">待确认</text></view><view data-event-opts="{{[['tap',[['filterByStatus',['final_completed']]]]]}}" class="stat-card data-v-649ce15b" bindtap="__e"><view class="stat-number completed data-v-649ce15b">{{taskStats.completed||0}}</view><text class="stat-label data-v-649ce15b">已完成</text></view><view data-event-opts="{{[['tap',[['filterByStatus',['overdue']]]]]}}" class="stat-card data-v-649ce15b" bindtap="__e"><view class="stat-number overdue data-v-649ce15b">{{taskStats.overdue||0}}</view><text class="stat-label data-v-649ce15b">已超时</text></view></view></view><view class="filter-tabs data-v-649ce15b"><view data-event-opts="{{[['tap',[['setFilter',['all']]]]]}}" class="{{['filter-tab','data-v-649ce15b',(currentFilter==='all')?'active':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['setFilter',['assigned_to_responsible']]]]]}}" class="{{['filter-tab','data-v-649ce15b',(currentFilter==='assigned_to_responsible')?'active':'']}}" bindtap="__e">执行中</view><view data-event-opts="{{[['tap',[['setFilter',['completed_by_responsible']]]]]}}" class="{{['filter-tab','data-v-649ce15b',(currentFilter==='completed_by_responsible')?'active':'']}}" bindtap="__e">待确认</view><view data-event-opts="{{[['tap',[['setFilter',['overdue']]]]]}}" class="{{['filter-tab','data-v-649ce15b',(currentFilter==='overdue')?'active':'']}}" bindtap="__e">已超时</view></view><block wx:if="{{$root.g0>0}}"><scroll-view class="task-scroll-view data-v-649ce15b" scroll-y="{{true}}" lower-threshold="{{100}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]]]}}" bindscrolltolower="__e"><view class="task-list data-v-649ce15b"><block wx:for="{{$root.l0}}" wx:for-item="task" wx:for-index="index" wx:key="_id"><view data-event-opts="{{[['tap',[['goToTaskDetail',['$0'],[[['filteredTaskList','_id',task.$orig._id]]]]]]]}}" class="task-item data-v-649ce15b" bindtap="__e"><view class="task-header data-v-649ce15b"><view class="task-title-row data-v-649ce15b"><text class="task-name data-v-649ce15b">{{task.$orig.name}}</text><view class="{{['task-status','data-v-649ce15b',(task.$orig.workflowStatus==='assigned_to_responsible')?'status-assigned':'',(task.$orig.workflowStatus==='completed_by_responsible')?'status-pending':'',(task.$orig.workflowStatus==='final_completed')?'status-completed':'']}}">{{''+task.m0+''}}</view></view><text class="task-project data-v-649ce15b">{{task.$orig.project||'未分类'}}</text></view><view class="task-content data-v-649ce15b"><text class="task-description data-v-649ce15b">{{task.$orig.description||'暂无描述'}}</text></view><view class="task-footer data-v-649ce15b"><view class="responsible-info data-v-649ce15b"><text class="responsible-label data-v-649ce15b">负责人：</text><text class="responsible-name data-v-649ce15b">{{task.m1}}</text></view><view class="time-info data-v-649ce15b"><text class="time-label data-v-649ce15b">{{task.m2}}</text><text class="{{['time-value','data-v-649ce15b',(task.m3)?'overdue':'',(task.m4)?'warning':'']}}">{{''+task.m5+''}}</text><block wx:if="{{task.$orig.workflowStatus==='assigned_to_responsible'}}"><view class="timing-badge data-v-649ce15b"><block wx:if="{{task.m6}}"><text class="timing-tag overdue-tag data-v-649ce15b">超时</text></block><block wx:else><block wx:if="{{task.m7}}"><text class="timing-tag warning-tag data-v-649ce15b">警告</text></block><block wx:else><text class="timing-tag normal-tag data-v-649ce15b">正常</text></block></block></view></block></view></view><block wx:if="{{task.$orig.workflowStatus==='completed_by_responsible'}}"><view class="quick-actions data-v-649ce15b"><view data-event-opts="{{[['tap',[['quickConfirm',['$0'],[[['filteredTaskList','_id',task.$orig._id]]]]]]]}}" class="action-btn confirm-btn data-v-649ce15b" catchtap="__e"><text class="data-v-649ce15b">确认完成</text></view><view data-event-opts="{{[['tap',[['quickReject',['$0'],[[['filteredTaskList','_id',task.$orig._id]]]]]]]}}" class="action-btn reject-btn data-v-649ce15b" catchtap="__e"><text class="data-v-649ce15b">退回重做</text></view></view></block></view></block><view class="load-more-state data-v-649ce15b"><block wx:if="{{loadingMore}}"><view class="loading-more data-v-649ce15b"><text class="loading-text data-v-649ce15b">加载更多中...</text></view></block><block wx:else><block wx:if="{{$root.g1}}"><view class="no-more data-v-649ce15b"><text class="no-more-text data-v-649ce15b">没有更多数据了</text></view></block></block></view></view></scroll-view></block><block wx:else><block wx:if="{{!loading}}"><view class="empty-state data-v-649ce15b"><view class="empty-content data-v-649ce15b"><image class="empty-image data-v-649ce15b" src="/static/empty/empty_task.png" mode="aspectFit"></image><text class="empty-text data-v-649ce15b">{{$root.m8}}</text></view></view></block></block><block wx:if="{{loading}}"><view class="loading-state data-v-649ce15b"><view class="custom-loading data-v-649ce15b"><text class="loading-text data-v-649ce15b">加载中...</text></view></view></block><block wx:if="{{showModal}}"><view data-event-opts="{{[['tap',[['closeModal',['$event']]]]]}}" class="custom-modal data-v-649ce15b" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content data-v-649ce15b" catchtap="__e"><view class="modal-header data-v-649ce15b"><text class="modal-title data-v-649ce15b">{{modalData.title}}</text></view><view class="modal-body data-v-649ce15b"><textarea class="modal-input data-v-649ce15b" placeholder="{{modalData.placeholder}}" maxlength="{{200}}" auto-height="{{true}}" data-event-opts="{{[['focus',[['handleInputFocus',['$event']]]],['input',[['__set_model',['','modalInput','$event',[]]]]]]}}" value="{{modalInput}}" bindfocus="__e" bindinput="__e"></textarea><view class="input-counter data-v-649ce15b">{{$root.g2+"/200"}}</view></view><view class="modal-footer data-v-649ce15b"><view data-event-opts="{{[['tap',[['closeModal',['$event']]]]]}}" class="modal-btn cancel-btn data-v-649ce15b" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmModal',['$event']]]]]}}" class="modal-btn confirm-btn data-v-649ce15b" bindtap="__e">{{modalData.confirmText}}</view></view></view></view></block></view>
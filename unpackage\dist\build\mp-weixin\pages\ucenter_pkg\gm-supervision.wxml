<view class="container data-v-2bf240b9"><view class="page-header data-v-2bf240b9"><text class="page-title data-v-2bf240b9">指派任务监督</text><text class="page-subtitle data-v-2bf240b9">实时监控指派任务的执行情况</text><view class="timing-explanation data-v-2bf240b9"><text class="explanation-text data-v-2bf240b9">💡 超时说明：执行中任务超过7天为警告，超过14天为超时</text></view></view><view class="stats-section data-v-2bf240b9"><view class="stats-grid data-v-2bf240b9"><view data-event-opts="{{[['tap',[['filterByStatus',['assigned_to_responsible']]]]]}}" class="stat-card data-v-2bf240b9" bindtap="__e"><view class="stat-number assigned data-v-2bf240b9">{{taskStats.assigned||0}}</view><text class="stat-label data-v-2bf240b9">执行中</text></view><view data-event-opts="{{[['tap',[['filterByStatus',['completed_by_responsible']]]]]}}" class="stat-card data-v-2bf240b9" bindtap="__e"><view class="stat-number pending data-v-2bf240b9">{{taskStats.pending||0}}</view><text class="stat-label data-v-2bf240b9">待确认</text></view><view data-event-opts="{{[['tap',[['filterByStatus',['final_completed']]]]]}}" class="stat-card data-v-2bf240b9" bindtap="__e"><view class="stat-number completed data-v-2bf240b9">{{taskStats.completed||0}}</view><text class="stat-label data-v-2bf240b9">已完成</text></view><view data-event-opts="{{[['tap',[['filterByStatus',['overdue']]]]]}}" class="stat-card data-v-2bf240b9" bindtap="__e"><view class="stat-number overdue data-v-2bf240b9">{{taskStats.overdue||0}}</view><text class="stat-label data-v-2bf240b9">已超时</text></view></view></view><view class="filter-tabs data-v-2bf240b9"><view data-event-opts="{{[['tap',[['setFilter',['all']]]]]}}" class="{{['filter-tab','data-v-2bf240b9',(currentFilter==='all')?'active':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['setFilter',['assigned_to_responsible']]]]]}}" class="{{['filter-tab','data-v-2bf240b9',(currentFilter==='assigned_to_responsible')?'active':'']}}" bindtap="__e">执行中</view><view data-event-opts="{{[['tap',[['setFilter',['completed_by_responsible']]]]]}}" class="{{['filter-tab','data-v-2bf240b9',(currentFilter==='completed_by_responsible')?'active':'']}}" bindtap="__e">待确认</view><view data-event-opts="{{[['tap',[['setFilter',['overdue']]]]]}}" class="{{['filter-tab','data-v-2bf240b9',(currentFilter==='overdue')?'active':'']}}" bindtap="__e">已超时</view></view><block wx:if="{{$root.g0>0}}"><view class="task-list data-v-2bf240b9"><block wx:for="{{$root.l0}}" wx:for-item="task" wx:for-index="index" wx:key="_id"><view data-event-opts="{{[['tap',[['goToTaskDetail',['$0'],[[['filteredTaskList','_id',task.$orig._id]]]]]]]}}" class="task-item data-v-2bf240b9" bindtap="__e"><view class="task-header data-v-2bf240b9"><view class="task-title-row data-v-2bf240b9"><text class="task-name data-v-2bf240b9">{{task.$orig.name}}</text><view class="{{['task-status','data-v-2bf240b9',(task.$orig.workflowStatus==='assigned_to_responsible')?'status-assigned':'',(task.$orig.workflowStatus==='completed_by_responsible')?'status-pending':'',(task.$orig.workflowStatus==='final_completed')?'status-completed':'']}}">{{''+task.m0+''}}</view></view><text class="task-project data-v-2bf240b9">{{task.$orig.project||'未分类'}}</text></view><view class="task-content data-v-2bf240b9"><text class="task-description data-v-2bf240b9">{{task.$orig.description||'暂无描述'}}</text></view><view class="task-footer data-v-2bf240b9"><view class="responsible-info data-v-2bf240b9"><text class="responsible-label data-v-2bf240b9">负责人：</text><text class="responsible-name data-v-2bf240b9">{{task.m1}}</text></view><view class="time-info data-v-2bf240b9"><text class="time-label data-v-2bf240b9">{{task.m2}}</text><text class="{{['time-value','data-v-2bf240b9',(task.m3)?'overdue':'',(task.m4)?'warning':'']}}">{{''+task.m5+''}}</text><block wx:if="{{task.$orig.workflowStatus==='assigned_to_responsible'}}"><view class="timing-badge data-v-2bf240b9"><block wx:if="{{task.m6}}"><text class="timing-tag overdue-tag data-v-2bf240b9">超时</text></block><block wx:else><block wx:if="{{task.m7}}"><text class="timing-tag warning-tag data-v-2bf240b9">警告</text></block><block wx:else><text class="timing-tag normal-tag data-v-2bf240b9">正常</text></block></block></view></block></view></view><block wx:if="{{task.$orig.workflowStatus==='completed_by_responsible'}}"><view class="quick-actions data-v-2bf240b9"><view data-event-opts="{{[['tap',[['quickConfirm',['$0'],[[['filteredTaskList','_id',task.$orig._id]]]]]]]}}" class="action-btn confirm-btn data-v-2bf240b9" catchtap="__e"><text class="data-v-2bf240b9">确认完成</text></view><view data-event-opts="{{[['tap',[['quickReject',['$0'],[[['filteredTaskList','_id',task.$orig._id]]]]]]]}}" class="action-btn reject-btn data-v-2bf240b9" catchtap="__e"><text class="data-v-2bf240b9">退回重做</text></view></view></block></view></block></view></block><block wx:else><view class="empty-state data-v-2bf240b9"><view class="empty-content data-v-2bf240b9"><image class="empty-image data-v-2bf240b9" src="/static/empty/empty_task.png" mode="aspectFit"></image><text class="empty-text data-v-2bf240b9">{{$root.m8}}</text></view></view></block><block wx:if="{{loading}}"><view class="loading-state data-v-2bf240b9"><view class="custom-loading data-v-2bf240b9"><text class="loading-text data-v-2bf240b9">加载中...</text></view></view></block><block wx:if="{{showModal}}"><view data-event-opts="{{[['tap',[['closeModal',['$event']]]]]}}" class="custom-modal data-v-2bf240b9" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content data-v-2bf240b9" catchtap="__e"><view class="modal-header data-v-2bf240b9"><text class="modal-title data-v-2bf240b9">{{modalData.title}}</text></view><view class="modal-body data-v-2bf240b9"><textarea class="modal-input data-v-2bf240b9" placeholder="{{modalData.placeholder}}" maxlength="{{200}}" auto-height="{{true}}" data-event-opts="{{[['focus',[['handleInputFocus',['$event']]]],['input',[['__set_model',['','modalInput','$event',[]]]]]]}}" value="{{modalInput}}" bindfocus="__e" bindinput="__e"></textarea><view class="input-counter data-v-2bf240b9">{{$root.g1+"/200"}}</view></view><view class="modal-footer data-v-2bf240b9"><view data-event-opts="{{[['tap',[['closeModal',['$event']]]]]}}" class="modal-btn cancel-btn data-v-2bf240b9" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmModal',['$event']]]]]}}" class="modal-btn confirm-btn data-v-2bf240b9" bindtap="__e">{{modalData.confirmText}}</view></view></view></view></block></view>
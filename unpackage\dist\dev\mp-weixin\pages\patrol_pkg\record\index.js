require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol_pkg/record/index"],{

/***/ 443:
/*!**********************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fpatrol_pkg%2Frecord%2Findex"} ***!
  \**********************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index2 = _interopRequireDefault(__webpack_require__(/*! ./pages/patrol_pkg/record/index.vue */ 444));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index2.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 444:
/*!*************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/record/index.vue ***!
  \*************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_f8ba29be___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=f8ba29be& */ 445);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 447);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&lang=scss& */ 449);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_f8ba29be___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_f8ba29be___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _index_vue_vue_type_template_id_f8ba29be___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/patrol_pkg/record/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 445:
/*!********************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/record/index.vue?vue&type=template&id=f8ba29be& ***!
  \********************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_f8ba29be___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=f8ba29be& */ 446);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_f8ba29be___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_f8ba29be___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_f8ba29be___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_f8ba29be___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 446:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/record/index.vue?vue&type=template&id=f8ba29be& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniDatetimePicker: function () {
      return Promise.all(/*! import() | uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue */ 558))
    },
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 483))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.getDateRangeText()
  var m1 = _vm.getSortText()
  var g0 = _vm.routeList.length === 0 && !_vm.loading
  var l0 = _vm.__map(_vm.routeList, function (route, index) {
    var $orig = _vm.__get_orig(route)
    var m2 = _vm.formatTimeDisplay(route.date)
    return {
      $orig: $orig,
      m2: m2,
    }
  })
  var g1 = _vm.loading && _vm.routeList.length > 0
  var g2 = !_vm.hasMore && _vm.routeList.length > 0
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.showSortOptions = !_vm.showSortOptions
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        g0: g0,
        l0: l0,
        g1: g1,
        g2: g2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 447:
/*!**************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/record/index.vue?vue&type=script&lang=js& ***!
  \**************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 448);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 448:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/record/index.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _vuex = __webpack_require__(/*! vuex */ 53);
var _date = __webpack_require__(/*! @/utils/date.js */ 72);
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! @/utils/patrol-api.js */ 71));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var PEmptyState = function PEmptyState() {
  __webpack_require__.e(/*! require.ensure | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then((function () {
    return resolve(__webpack_require__(/*! @/components/p-empty-state/p-empty-state.vue */ 483));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    PEmptyState: PEmptyState
  },
  data: function data() {
    return {
      loading: false,
      refreshing: false,
      page: 1,
      pageSize: 10,
      hasMore: true,
      routeList: [],
      filterParams: {
        startDate: '',
        // 开始日期
        endDate: '' // 结束日期
      },

      dateRange: [],
      // 用于datetime-picker的日期范围
      totalTaskCount: 0,
      // 添加总任务数计数器
      loadedTaskCount: 0,
      // 添加已加载任务计数器
      sortType: 'execute',
      // 排序类型：execute-执行日期(默认), create-添加日期, checkin-打卡时间
      showSortOptions: false,
      // 是否显示排序选项下拉菜单
      h5DatePickerVisible: false,
      // 控制H5平台的日期选择器显示
      h5DatePickerDateRange: [],
      // 存储H5平台的日期选择器选中的日期范围
      // 新增：页面状态保持相关
      isFirstLoad: true,
      // 是否首次加载
      lastScrollTop: 0,
      // 记录滚动位置
      pageStateKey: 'patrol_record_page_state',
      // 页面状态缓存key
      needRestoreState: false,
      // 是否需要恢复状态
      stateRestored: false // 状态是否已恢复
    };
  },

  computed: _objectSpread({}, (0, _vuex.mapState)({
    userInfo: function userInfo(state) {
      return state.user.userInfo;
    }
  })),
  onLoad: function onLoad() {
    // 检查是否需要恢复页面状态
    this.checkAndRestorePageState();

    // 如果不需要恢复状态，则正常加载数据
    if (!this.needRestoreState) {
      this.loadRecords();
    }
  },
  onShow: function onShow() {
    // 只在首次加载或用户主动刷新时才重新加载数据
    if (this.isFirstLoad) {
      this.isFirstLoad = false;

      // 如果需要恢复状态，则恢复；否则正常加载
      if (this.needRestoreState && !this.stateRestored) {
        this.restorePageState();
      } else if (!this.needRestoreState) {
        // 首次进入页面，正常加载
        this.refresh();
      }
    }
    // 非首次显示时不做任何操作，保持当前状态
  },
  onHide: function onHide() {
    // 页面隐藏时保存状态
    this.savePageState();
  },
  onUnload: function onUnload() {
    // 页面卸载时清理状态（可选，根据需求决定是否保留状态到下次进入）
    // this.clearPageState();
  },
  methods: {
    // 加载记录列表
    loadRecords: function loadRecords() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var apiParams, taskListRes, tasks, totalCount, beforeFilterCount, routeMap, routeRecords, backendHasMore, currentTotal;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (!(_this.loading || !_this.hasMore)) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return");
              case 2:
                _this.loading = true;
                _context.prev = 3;
                // 准备API参数 - 🔥 新增：角色权限控制
                apiParams = {
                  page: _this.page,
                  pageSize: _this.pageSize,
                  filters: {},
                  // 添加filters对象，适配后端接口期望的参数格式
                  // 🔥 新增：角色决定模式，管理层看全部，普通用户看自己
                  viewScope: 'role-based',
                  // 添加精确字段过滤，只获取页面显示需要的字段
                  fields: ['_id', 'name', 'route_name', 'user_name', 'patrol_date', 'create_date', 'update_date',
                  // 统计信息：使用后端预计算的overall_stats
                  'overall_stats.total_points', 'overall_stats.completed_points', 'overall_stats.missed_points', 'overall_stats.completion_rate',
                  // 轮次信息：只需要轮次数量，不需要详细点位数据
                  'rounds_detail.round', 'rounds_detail.name',
                  // 执行者信息
                  'executor.name']
                }; // 添加排序参数
                _context.t0 = _this.sortType;
                _context.next = _context.t0 === 'execute' ? 8 : _context.t0 === 'create' ? 11 : _context.t0 === 'checkin' ? 14 : 17;
                break;
              case 8:
                apiParams.filters.sort = 'patrol_date';
                apiParams.filters.sortDirection = 'desc'; // 默认降序(最新的在前)
                return _context.abrupt("break", 17);
              case 11:
                apiParams.filters.sort = 'create_date';
                apiParams.filters.sortDirection = 'desc';
                return _context.abrupt("break", 17);
              case 14:
                apiParams.filters.sort = 'last_checkin_time';
                apiParams.filters.sortDirection = 'desc';
                return _context.abrupt("break", 17);
              case 17:
                // 处理日期筛选条件 - 确保格式一致性
                if (_this.filterParams.startDate && _this.filterParams.endDate) {
                  // 如果开始和结束日期相同，直接使用单个日期参数进行精确匹配
                  if (_this.filterParams.startDate === _this.filterParams.endDate) {
                    // 确保日期格式为 YYYY-MM-DD，不包含时间部分
                    apiParams.filters.patrolDate = _this.formatDateForQuery(_this.filterParams.startDate);
                  } else {
                    // 对于日期范围，使用范围查询参数
                    apiParams.filters.startDate = _this.formatDateForQuery(_this.filterParams.startDate);
                    apiParams.filters.endDate = _this.formatDateForQuery(_this.filterParams.endDate);
                  }
                } else if (_this.filterParams.startDate) {
                  // 只有开始日期
                  apiParams.filters.startDate = _this.formatDateForQuery(_this.filterParams.startDate);
                } else if (_this.filterParams.endDate) {
                  // 只有结束日期
                  apiParams.filters.endDate = _this.formatDateForQuery(_this.filterParams.endDate);
                }

                // 这里使用 getTaskList，它会将参数正确封装
                _context.next = 20;
                return _patrolApi.default.getTaskList(apiParams);
              case 20:
                taskListRes = _context.sent;
                if (taskListRes.code === 0 && taskListRes.data) {
                  // 获取任务列表
                  tasks = taskListRes.data.list || []; // 保存后端返回的总任务数
                  totalCount = taskListRes.data.total || 0; // 如果API没有正确应用日期筛选，在前端进行筛选
                  if (_this.filterParams.startDate || _this.filterParams.endDate) {
                    beforeFilterCount = tasks.length;
                    tasks = tasks.filter(function (task) {
                      var taskDate = task.patrol_date || task.date || '';
                      if (!taskDate) return false;

                      // 标准化日期格式，确保比较的是相同格式的日期字符串
                      var normalizedTaskDate = _this.standardizeDate(taskDate);
                      var startDate = _this.filterParams.startDate ? _this.standardizeDate(_this.filterParams.startDate) : '';
                      var endDate = _this.filterParams.endDate ? _this.standardizeDate(_this.filterParams.endDate) : '';

                      // 对于单一日期筛选
                      if (startDate && endDate && startDate === endDate) {
                        return normalizedTaskDate === startDate;
                      }

                      // 对于日期范围筛选
                      if (startDate && endDate) {
                        return normalizedTaskDate >= startDate && normalizedTaskDate <= endDate;
                      }

                      // 只有开始日期
                      if (startDate) {
                        return normalizedTaskDate >= startDate;
                      }

                      // 只有结束日期
                      if (endDate) {
                        return normalizedTaskDate <= endDate;
                      }
                      return true;
                    });
                  }

                  // 准备路线数据
                  routeMap = {}; // 处理任务数据
                  tasks.forEach(function (task) {
                    if (!task._id) return;

                    // 确保任务有执行日期
                    var taskDate = task.patrol_date || task.date || '';
                    if (!taskDate) return; // 跳过没有日期的任务

                    // 任务ID
                    var taskId = task._id;

                    // 初始化路线数据
                    if (!routeMap[taskId]) {
                      var _task$rounds_detail;
                      routeMap[taskId] = {
                        _id: taskId,
                        name: task.name || task.route_name || '未命名线路',
                        user_name: task.user_name || (task.executor ? task.executor.name : '') || '',
                        point_count: 0,
                        completion_rate: 0,
                        normal_count: 0,
                        missed_count: 0,
                        not_checked_count: 0,
                        record_count: 0,
                        rounds_count: ((_task$rounds_detail = task.rounds_detail) === null || _task$rounds_detail === void 0 ? void 0 : _task$rounds_detail.length) || 0,
                        last_update_time: task.update_date || task.create_date || null,
                        date: taskDate
                      };
                    }

                    // 从overall_stats获取统计信息
                    if (task.overall_stats) {
                      var _task$rounds_detail2;
                      var stats = task.overall_stats;

                      // 计算线路真实的点位数量（不是所有轮次的点位总数）
                      // 使用简单计算：总点位数 ÷ 轮次数量 = 线路点位数
                      var totalRounds = ((_task$rounds_detail2 = task.rounds_detail) === null || _task$rounds_detail2 === void 0 ? void 0 : _task$rounds_detail2.length) || 1;
                      var realPointCount = Math.round((stats.total_points || 0) / totalRounds);

                      // 更新基础统计数据
                      routeMap[taskId].point_count = realPointCount;
                      routeMap[taskId].normal_count = stats.completed_points || 0;

                      // 按轮次计算缺卡数和未打卡数
                      var roundStats = _this.calculateRoundStats(task);
                      routeMap[taskId].missed_count = roundStats.missed_count;
                      routeMap[taskId].not_checked_count = roundStats.not_checked_count;

                      // 更新完成率
                      routeMap[taskId].completion_rate = stats.completion_rate ? Math.round(stats.completion_rate * 100) : 0;
                    }

                    // 简化轮次处理：只获取轮次数量
                    if (task.rounds_detail && task.rounds_detail.length > 0) {
                      routeMap[taskId].rounds_count = task.rounds_detail.length;
                    }
                  });

                  // 将路线映射转换为数组
                  routeRecords = Object.values(routeMap); // 更新路线列表
                  if (_this.page === 1) {
                    _this.routeList = routeRecords;
                  } else {
                    _this.routeList = [].concat((0, _toConsumableArray2.default)(_this.routeList), routeRecords);
                  }

                  // 更新分页信息
                  // 检查后端返回的hasMore标志
                  backendHasMore = taskListRes.data.hasMore || false; // 使用更复杂的判断逻辑来确定是否还有更多数据
                  if (taskListRes.data.hasMore !== undefined) {
                    // 优先使用后端明确返回的hasMore标志
                    _this.hasMore = backendHasMore;
                  } else if (totalCount > 0) {
                    // 其次使用总数比较
                    currentTotal = (_this.page - 1) * _this.pageSize + routeRecords.length;
                    _this.hasMore = currentTotal < totalCount;
                  } else {
                    // 最后回退到老方法
                    _this.hasMore = tasks.length > 0 && routeRecords.length >= _this.pageSize;
                  }
                  if (_this.hasMore) {
                    _this.page++;
                  }
                } else {
                  uni.showToast({
                    title: taskListRes.message || '获取记录失败',
                    icon: 'none'
                  });
                }
                _context.next = 28;
                break;
              case 24:
                _context.prev = 24;
                _context.t1 = _context["catch"](3);
                uni.showToast({
                  title: '加载失败，请稍后再试',
                  icon: 'none'
                });
                console.error('加载记录出错:', _context.t1);
              case 28:
                _context.prev = 28;
                _this.loading = false;
                if (_this.refreshing) {
                  _this.refreshing = false;
                }
                return _context.finish(28);
              case 32:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[3, 24, 28, 32]]);
      }))();
    },
    // 下拉刷新
    refresh: function refresh() {
      var forceRefresh = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
      // 如果是强制刷新（用户主动下拉刷新），清除保存的状态
      if (forceRefresh) {
        this.clearPageState();
        this.stateRestored = false;
        this.needRestoreState = false;
      }
      this.refreshing = true;
      this.page = 1;
      this.hasMore = true;
      this.routeList = []; // 清空当前列表
      this.loadRecords();
    },
    // 加载更多
    loadMore: function loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadRecords();
      }
    },
    // 重置筛选条件
    resetFilters: function resetFilters() {
      this.filterParams = {
        startDate: '',
        endDate: ''
      };
      this.dateRange = []; // 清空日期范围选择器的值
      this.refresh(true); // 重置筛选条件时强制刷新
    },
    // 获取路线记录汇总
    getRouteRecordSummary: function getRouteRecordSummary(route) {
      // 如果有点位数量，显示完成信息
      if (route.point_count > 0) {
        var completedCount = route.normal_count + route.overtime_count;
        return "\u5B8C\u6210 ".concat(completedCount, "/").concat(route.point_count, " \u4E2A\u70B9\u4F4D (\u7F3A\u5361: ").concat(route.missed_count, ", \u672A\u6253\u5361: ").concat(route.not_checked_count || 0, ")");
      }

      // 退化情况，只显示记录数
      if (route.record_count > 0) {
        return "\u5171".concat(route.record_count, "\u6761\u8BB0\u5F55");
      }
      return '暂无记录';
    },
    // 格式化时间显示
    formatTimeDisplay: function formatTimeDisplay(timeStr) {
      if (!timeStr) return '未知时间';
      try {
        // 检查是否只是日期部分
        if (typeof timeStr === 'string' && timeStr.length === 10 && timeStr.includes('-')) {
          // 只包含日期部分 YYYY-MM-DD，直接返回
          return timeStr;
        }

        // 使用safeDateFormat安全地格式化日期
        return (0, _date.safeDateFormat)(timeStr, 'YYYY-MM-DD');
      } catch (e) {
        console.warn('时间格式化错误', e);
        // 尝试原始处理
        if (typeof timeStr === 'string') {
          // 如果是标准日期格式，只取日期部分
          if (timeStr.includes('T')) {
            return timeStr.split('T')[0];
          }
          return timeStr.split(' ')[0]; // 只返回日期部分
        }

        return String(timeStr);
      }
    },
    // 查看路线详情
    viewRouteDetails: function viewRouteDetails(route) {
      // 在跳转前保存当前页面状态
      this.savePageState();
      uni.navigateTo({
        url: "/pages/patrol_pkg/record/route-detail?id=".concat(route._id, "&name=").concat(encodeURIComponent(route.name), "&executorName=").concat(encodeURIComponent(route.user_name || ''))
      });
    },
    // 检查并恢复页面状态
    checkAndRestorePageState: function checkAndRestorePageState() {
      try {
        var savedState = uni.getStorageSync(this.pageStateKey);
        if (savedState) {
          // 检查状态是否在有效期内（比如30分钟）
          var now = Date.now();
          var stateAge = now - (savedState.timestamp || 0);
          var maxAge = 30 * 60 * 1000; // 30分钟

          if (stateAge < maxAge) {
            this.needRestoreState = true;
          } else {
            // 状态过期，清除
            uni.removeStorageSync(this.pageStateKey);
          }
        }
      } catch (e) {
        console.error('检查页面状态失败:', e);
      }
    },
    // 保存页面状态
    savePageState: function savePageState() {
      try {
        var state = {
          // 分页信息
          page: this.page,
          hasMore: this.hasMore,
          routeList: this.routeList,
          // 筛选条件
          filterParams: _objectSpread({}, this.filterParams),
          dateRange: (0, _toConsumableArray2.default)(this.dateRange),
          sortType: this.sortType,
          // 滚动位置
          scrollTop: this.lastScrollTop,
          // 时间戳
          timestamp: Date.now()
        };
        uni.setStorageSync(this.pageStateKey, state);
      } catch (e) {
        console.error('保存页面状态失败:', e);
      }
    },
    // 恢复页面状态
    restorePageState: function restorePageState() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var savedState;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                savedState = uni.getStorageSync(_this2.pageStateKey);
                if (savedState) {
                  _context2.next = 5;
                  break;
                }
                _this2.loadRecords();
                return _context2.abrupt("return");
              case 5:
                // 恢复数据状态
                _this2.page = savedState.page || 1;
                _this2.hasMore = savedState.hasMore !== undefined ? savedState.hasMore : true;
                _this2.routeList = savedState.routeList || [];

                // 恢复筛选条件
                _this2.filterParams = savedState.filterParams || {
                  startDate: '',
                  endDate: ''
                };
                _this2.dateRange = savedState.dateRange || [];
                _this2.sortType = savedState.sortType || 'execute';

                // 标记状态已恢复
                _this2.stateRestored = true;

                // 恢复滚动位置
                if (savedState.scrollTop > 0) {
                  _this2.$nextTick(function () {
                    _this2.restoreScrollPosition(savedState.scrollTop);
                  });
                }
                _context2.next = 19;
                break;
              case 15:
                _context2.prev = 15;
                _context2.t0 = _context2["catch"](0);
                console.error('恢复页面状态失败:', _context2.t0);
                // 恢复失败时正常加载数据
                _this2.loadRecords();
              case 19:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 15]]);
      }))();
    },
    // 恢复滚动位置
    restoreScrollPosition: function restoreScrollPosition(scrollTop) {
      try {
        // 使用页面滚动API恢复位置
        uni.pageScrollTo({
          scrollTop: scrollTop,
          duration: 0 // 立即滚动，不要动画
        });
      } catch (e) {
        console.error('恢复滚动位置失败:', e);
      }
    },
    // 清除页面状态
    clearPageState: function clearPageState() {
      try {
        uni.removeStorageSync(this.pageStateKey);
      } catch (e) {
        console.error('清除页面状态失败:', e);
      }
    },
    // 监听滚动事件
    onScroll: function onScroll(e) {
      if (e && e.detail) {
        this.lastScrollTop = e.detail.scrollTop;
      }
    },
    // 处理下拉刷新
    handlePullDownRefresh: function handlePullDownRefresh() {
      this.refresh(true); // 传入true表示强制刷新
    },
    // 获取日期范围显示文本
    getDateRangeText: function getDateRangeText() {
      if (this.filterParams.startDate && this.filterParams.endDate) {
        if (this.filterParams.startDate === this.filterParams.endDate) {
          return this.filterParams.startDate; // 如果开始和结束日期相同，只显示一个日期
        }

        return "".concat(this.filterParams.startDate, " \u81F3 ").concat(this.filterParams.endDate);
      } else if (this.filterParams.startDate) {
        return "".concat(this.filterParams.startDate, " \u8D77");
      } else if (this.filterParams.endDate) {
        return "\u81F3 ".concat(this.filterParams.endDate);
      } else {
        return '选择日期范围';
      }
    },
    // 处理日期范围变化
    handleDateRangeChange: function handleDateRangeChange(e) {
      if (!e || e.length === 0) {
        // 清空筛选条件
        this.resetFilters();
        return;
      }
      if (e.length === 2) {
        // 确保日期格式为YYYY-MM-DD
        var startDate = this.formatDateString(e[0]);
        var endDate = this.formatDateString(e[1]);

        // 设置筛选参数
        this.filterParams = {
          startDate: startDate,
          endDate: endDate
        };

        // 刷新数据
        this.page = 1; // 重置页码
        this.hasMore = true;
        this.refresh(true); // 日期筛选变化时强制刷新
      }
    },
    // 格式化日期字符串为YYYY-MM-DD格式
    formatDateString: function formatDateString(dateStr) {
      if (!dateStr) return '';
      try {
        // 如果已经是格式化的日期字符串，直接返回
        if (typeof dateStr === 'string' && dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
          return dateStr;
        }
        var date = new Date(dateStr);
        // 验证日期是否有效
        if (isNaN(date.getTime())) {
          return '';
        }
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        return "".concat(year, "-").concat(month, "-").concat(day);
      } catch (e) {
        return '';
      }
    },
    // 设置日期筛选
    setDateFilter: function setDateFilter(startDate, endDate) {
      this.filterParams.startDate = startDate;
      this.filterParams.endDate = endDate;

      // 同步更新dateRange
      if (startDate && endDate) {
        this.dateRange = [startDate, endDate];
      } else {
        this.dateRange = [];
      }
      this.refresh(true); // 筛选条件变化时强制刷新
    },
    // 使用日期筛选查询数据
    onSearch: function onSearch() {
      this.page = 1;
      this.refresh(true); // 搜索时强制刷新
    },
    /**
     * 格式化日期字符串，确保用于查询的日期格式统一
     * @param {String} dateStr 日期字符串
     * @returns {String} 格式化后的日期字符串 YYYY-MM-DD
     */
    formatDateForQuery: function formatDateForQuery(dateStr) {
      if (!dateStr) return '';

      // 提取年月日
      try {
        // 如果已经是YYYY-MM-DD格式，直接返回
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
          return dateStr;
        }

        // 尝试创建日期对象
        var date = new Date(dateStr);
        if (isNaN(date.getTime())) {
          return dateStr; // 如果是无效日期，返回原字符串
        }

        // 格式化为YYYY-MM-DD
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        return "".concat(year, "-").concat(month, "-").concat(day);
      } catch (e) {
        console.error('日期格式化错误:', e);
        return dateStr;
      }
    },
    /**
     * 标准化日期字符串，用于日期比较
     * @param {String} dateStr 日期字符串
     * @returns {String} 标准化后的日期字符串 YYYY-MM-DD
     */
    standardizeDate: function standardizeDate(dateStr) {
      if (!dateStr) return '';

      // 尝试各种格式转换
      try {
        // 处理YYYY/MM/DD格式
        dateStr = dateStr.replace(/\//g, '-');

        // 如果包含时间部分，去除
        if (dateStr.includes(' ')) {
          dateStr = dateStr.split(' ')[0];
        }

        // 如果是ISO格式，提取日期部分
        if (dateStr.includes('T')) {
          dateStr = dateStr.split('T')[0];
        }

        // 确保格式为YYYY-MM-DD
        var parts = dateStr.split('-');
        if (parts.length === 3) {
          var year = parts[0];
          var month = parts[1].padStart(2, '0');
          var day = parts[2].padStart(2, '0');
          return "".concat(year, "-").concat(month, "-").concat(day);
        }
        return dateStr;
      } catch (e) {
        console.error('日期标准化错误:', e);
        return dateStr;
      }
    },
    // 获取排序方式的显示文本
    getSortText: function getSortText() {
      switch (this.sortType) {
        case 'execute':
          return '按执行日期';
        case 'create':
          return '按添加日期';
        case 'checkin':
          return '按打卡时间';
        default:
          return '按执行日期';
      }
    },
    // 设置排序方式
    setSortType: function setSortType(type) {
      if (this.sortType !== type) {
        this.sortType = type;
        this.refresh(true); // 更新排序后强制刷新数据
      }

      this.showSortOptions = false; // 选择后关闭下拉菜单
    },
    // 打开H5平台的日期选择器
    openH5DatePicker: function openH5DatePicker() {
      this.h5DatePickerVisible = true;
    },
    // 处理H5平台的日期选择器变化
    handleH5DateRangeChange: function handleH5DateRangeChange(e) {
      if (!e || e.length === 0) {
        // 清空筛选条件
        this.resetH5DateRange();
        this.h5DatePickerVisible = false; // 自动关闭弹窗
        return;
      }
      if (e.length === 2) {
        // 确保日期格式为YYYY-MM-DD
        var startDate = this.formatDateString(e[0]);
        var endDate = this.formatDateString(e[1]);

        // 设置筛选参数
        this.filterParams = {
          startDate: startDate,
          endDate: endDate
        };

        // 刷新数据
        this.page = 1; // 重置页码
        this.hasMore = true;
        this.refresh(true); // H5日期筛选变化时强制刷新

        // 选择完成后自动关闭弹窗
        this.h5DatePickerVisible = false;
      }
    },
    // 重置H5平台的日期选择器
    resetH5DateRange: function resetH5DateRange() {
      this.filterParams = {
        startDate: '',
        endDate: ''
      };
      this.dateRange = [];
      this.refresh(true); // 重置筛选时强制刷新
    },
    // 关闭H5平台的日期选择器
    closeH5DatePicker: function closeH5DatePicker() {
      this.h5DatePickerVisible = false;
    },
    // 计算轮次统计信息
    calculateRoundStats: function calculateRoundStats(task) {
      var _this3 = this;
      if (!task || !task.rounds_detail || !Array.isArray(task.rounds_detail)) {
        return {
          missed_count: 0,
          not_checked_count: 0
        };
      }
      var missed_count = 0; // 缺卡数（已结束轮次的未完成点位）
      var not_checked_count = 0; // 未打卡数（进行中或未开始轮次的未完成点位）
      var currentTime = new Date();
      task.rounds_detail.forEach(function (round) {
        // 判断轮次是否已结束
        var isRoundFinished = _this3.isRoundFinished(round, currentTime);

        // 使用轮次的stats数据计算未完成点位数
        var roundUncompletedPoints = 0;
        if (round.stats) {
          // 从stats数据计算未完成点位数
          var totalPoints = round.stats.total_points || 0;
          var completedPoints = round.stats.completed_points || 0;
          roundUncompletedPoints = Math.max(0, totalPoints - completedPoints);
        } else {
          // 如果没有stats数据，输出警告并跳过
          console.warn('轮次缺少stats统计数据:', round);
          return;
        }
        if (isRoundFinished) {
          // 轮次已结束：未完成的点位算缺卡
          missed_count += roundUncompletedPoints;
        } else {
          // 轮次进行中或未开始：未完成的点位算未打卡
          not_checked_count += roundUncompletedPoints;
        }
      });
      return {
        missed_count: missed_count,
        not_checked_count: not_checked_count
      };
    },
    // 判断轮次是否已结束
    isRoundFinished: function isRoundFinished(round) {
      var currentTime = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : new Date();
      if (!round) return true;

      // 1. 检查轮次状态
      // 状态2=已完成, 状态3=已超时
      if (round.status === 2 || round.status === 3) {
        return true;
      }

      // 2. 检查轮次结束时间
      if (round.end_time) {
        try {
          var roundEndTime = new Date(round.end_time);
          if (currentTime > roundEndTime) {
            return true;
          }
        } catch (e) {
          console.warn('解析轮次结束时间失败:', round.end_time);
        }
      }

      // 3. 如果轮次状态是未开始(0)，则未结束
      if (round.status === 0) {
        return false;
      }

      // 4. 如果轮次状态是进行中(1)，检查是否超时
      return false;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 449:
/*!***********************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/record/index.vue?vue&type=style&index=0&lang=scss& ***!
  \***********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss& */ 450);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 450:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/record/index.vue?vue&type=style&index=0&lang=scss& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[443,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/patrol_pkg/record/index.js.map
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>未来规划与展望 - 株水小智</title>
    <script src="libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="libs/all.min.css">
    <script src="libs/echarts.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #1e40af 100%);
        }
        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .glass-effect:hover {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .animate-pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .subtitle-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 0.5rem;
            margin-bottom: 2rem;
        }
        .subtitle-bar:before, .subtitle-bar:after {
            content: "";
            flex: 1;
            height: 2px;
            background: linear-gradient(90deg, #38bdf8 0%, #a7f3d0 100%);
            margin: 0 1rem;
            border-radius: 1px;
            box-shadow: 0 0 8px rgba(56, 189, 248, 0.6);
        }
        .bubble {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.13);
            pointer-events: none;
            animation: floatBubble 12s linear infinite;
        }
        @keyframes floatBubble {
            0% { transform: translateY(0) scale(1); opacity: 0.7; }
            50% { opacity: 1; }
            100% { transform: translateY(-120vh) scale(1.2); opacity: 0; }
        }
        .roadmap-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
            border: 2px solid rgba(59, 130, 246, 0.4);
            transition: all 0.3s ease;
            backdrop-filter: blur(16px);
            position: relative;
            overflow: hidden;
        }
        .roadmap-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3B82F6, #06B6D4);
        }
        .roadmap-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(59, 130, 246, 0.3);
            border-color: rgba(59, 130, 246, 0.6);
        }
        
        .roadmap-card.mid-term {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));
            border: 2px solid rgba(34, 197, 94, 0.4);
        }
        .roadmap-card.mid-term::before {
            background: linear-gradient(90deg, #22C55E, #10B981);
        }
        .roadmap-card.mid-term:hover {
            box-shadow: 0 25px 50px rgba(34, 197, 94, 0.3);
            border-color: rgba(34, 197, 94, 0.6);
        }
        
        .roadmap-card.long-term {
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(168, 85, 247, 0.1));
            border: 2px solid rgba(147, 51, 234, 0.4);
        }
        .roadmap-card.long-term::before {
            background: linear-gradient(90deg, #9333EA, #A855F7);
        }
        .roadmap-card.long-term:hover {
            box-shadow: 0 25px 50px rgba(147, 51, 234, 0.3);
            border-color: rgba(147, 51, 234, 0.6);
        }
        .timeline-item {
            position: relative;
            padding-left: 2rem;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0.5rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background: linear-gradient(45deg, #3B82F6, #06B6D4);
        }
        .timeline-item::after {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 1.5rem;
            width: 2px;
            height: calc(100% - 1rem);
            background: linear-gradient(to bottom, #3B82F6, #06B6D4);
        }
        .timeline-item:last-child::after {
            display: none;
        }
        .icon-contrast {
            color: #38bdf8 !important;
            filter: drop-shadow(0 2px 4px rgba(30,64,175,0.12));
            background: #1e40af;
            border-radius: 0.6rem;
            padding: 0.18rem 0.32rem;
            margin-right: 0.7rem;
            font-size: 1.25rem;
            border: 1.5px solid #38bdf8;
        }
        .icon-contrast-green {
            color: #4ade80 !important;
            border-color: #bbf7d0;
            background: #166534;
        }
        .icon-contrast-orange {
            color: #fbbf24 !important;
            border-color: #fde68a;
            background: #78350f;
        }
        .highlight-blue { color: #38bdf8; font-weight: bold; }
        .highlight-green { color: #4ade80; font-weight: bold; }
        .highlight-yellow { color: #fbbf24; font-weight: bold; }
        .highlight-white { color: #fff; font-weight: bold; }
        .text-main { color: #fff; }
        .text-sub { color: #bae6fd; }
    </style>
</head>
<body class="gradient-bg min-h-screen flex flex-col">
    <!-- 进度条 -->
    <div class="fixed top-0 left-0 w-full h-1 bg-white/20 z-50">
        <div class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-1000" style="width: 100%"></div>
    </div>

    <!-- 气泡粒子动画 -->
    <div class="absolute inset-0 z-0 overflow-hidden">
        <div class="bubble" style="width:60px;height:60px;left:10vw;bottom:10vh;animation-delay:0s;"></div>
        <div class="bubble" style="width:40px;height:40px;left:80vw;bottom:20vh;animation-delay:2s;"></div>
        <div class="bubble" style="width:80px;height:80px;left:30vw;bottom:5vh;animation-delay:4s;"></div>
        <div class="bubble" style="width:30px;height:30px;left:60vw;bottom:15vh;animation-delay:6s;"></div>
        <div class="bubble" style="width:50px;height:50px;left:50vw;bottom:8vh;animation-delay:1s;"></div>
    </div>

    <div class="relative z-10 flex-1 flex flex-col justify-center">
        <!-- 头部 -->
        <header class="text-center py-8">
            <div class="animate-pulse-slow">
                <h1 class="text-5xl font-bold text-white mb-4 flex items-center justify-center">
                    <i class="fas fa-rocket mr-4 text-blue-200"></i>
                    未来规划与展望
                </h1>
                <div class="subtitle-bar">
                    <span class="text-xl text-blue-100 font-light">持续优化，成为集团创新标杆</span>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="flex-1 px-8 pb-8">
            <div class="max-w-7xl mx-auto">
                <!-- 发展路线图 -->
                <div class="mb-8">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <!-- 短期目标 -->
                        <div class="roadmap-card rounded-2xl p-8 text-center">
                            <div class="flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-tools text-blue-300 text-4xl"></i>
                            </div>
                            <div class="bg-blue-500/20 rounded-lg px-3 py-1 mb-3 inline-block">
                                <span class="text-blue-200 text-sm font-semibold">Phase 1</span>
                            </div>
                            <h3 class="text-2xl font-bold mb-5 text-blue-300">短期目标</h3>
                            <div class="space-y-4 text-left">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-search text-blue-400 text-xl"></i>
                                    <span class="text-white">完善6S责任区功能</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-chart-bar text-blue-400 text-xl"></i>
                                    <span class="text-white">优化数据报表展示</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-mobile-alt text-blue-400 text-xl"></i>
                                    <span class="text-white">提升移动端操作体验</span>
                                </div>
                            </div>
                        </div>

                        <!-- 中期规划 -->
                        <div class="roadmap-card mid-term rounded-2xl p-8 text-center">
                            <div class="flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-sync-alt text-green-300 text-4xl"></i>
                            </div>
                            <div class="bg-green-500/20 rounded-lg px-3 py-1 mb-3 inline-block">
                                <span class="text-green-200 text-sm font-semibold">Phase 2</span>
                            </div>
                            <h3 class="text-2xl font-bold mb-5 text-green-300">中期规划</h3>
                            <div class="space-y-4 text-left">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-database text-green-400 text-xl"></i>
                                    <span class="text-white">对接生产系统数据</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-mobile-alt text-green-400 text-xl"></i>
                                    <span class="text-white">完善巡检功能模块</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-robot text-green-400 text-xl"></i>
                                    <span class="text-white">增加智能提醒功能</span>
                                </div>
                            </div>
                        </div>

                        <!-- 长期愿景 -->
                        <div class="roadmap-card long-term rounded-2xl p-8 text-center">
                            <div class="flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-globe text-purple-300 text-4xl"></i>
                            </div>
                            <div class="bg-purple-500/20 rounded-lg px-3 py-1 mb-3 inline-block">
                                <span class="text-purple-200 text-sm font-semibold">Phase 3</span>
                            </div>
                            <h3 class="text-2xl font-bold mb-5 text-purple-300">长期愿景</h3>
                            <div class="space-y-4 text-left">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-network-wired text-purple-400 text-xl"></i>
                                    <span class="text-white">在各厂区全面推广应用</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-trophy text-purple-400 text-xl"></i>
                                    <span class="text-white">成为集团创新标杆项目</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-brain text-purple-400 text-xl"></i>
                                    <span class="text-white">建立智慧水务管理新模式</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实施路径与关键里程碑 -->
                <div class="glass-effect rounded-2xl p-8 mb-8">
                    <h2 class="text-3xl font-bold mb-8 text-center text-cyan-300 flex items-center justify-center">
                        <i class="fas fa-route mr-3"></i>
                        实施路径与关键里程碑
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 实施路径 -->
                        <div class="space-y-4">
                            <h3 class="text-xl font-bold text-blue-300 mb-4 flex items-center">
                                <i class="fas fa-map mr-2"></i>
                                实施路径
                            </h3>
                            <div class="space-y-3">
                                <div class="flex items-center p-3 bg-blue-500/20 rounded-lg">
                                    <i class="fas fa-calendar-check text-blue-400 mr-3"></i>
                                    <div>
                                        <span class="text-white font-semibold">第一阶段：功能完善</span>
                                        <p class="text-blue-200 text-sm">完善6S责任区功能，优化用户体验</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-3 bg-green-500/20 rounded-lg">
                                    <i class="fas fa-database text-green-400 mr-3"></i>
                                    <div>
                                        <span class="text-white font-semibold">第二阶段：系统集成</span>
                                        <p class="text-green-200 text-sm">对接生产系统，完善巡检功能</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-3 bg-purple-500/20 rounded-lg">
                                    <i class="fas fa-rocket text-purple-400 mr-3"></i>
                                    <div>
                                        <span class="text-white font-semibold">第三阶段：推广应用</span>
                                        <p class="text-purple-200 text-sm">扩大应用范围，建立标准化流程</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 关键里程碑 -->
                        <div class="space-y-4">
                            <h3 class="text-xl font-bold text-green-300 mb-4 flex items-center">
                                <i class="fas fa-flag-checkered mr-2"></i>
                                关键里程碑
                            </h3>
                            <div class="space-y-3">
                                <div class="flex items-center p-3 bg-yellow-500/20 rounded-lg">
                                    <i class="fas fa-trophy text-yellow-400 mr-3"></i>
                                    <div>
                                        <span class="text-white font-semibold">Q4 2025：功能完善</span>
                                        <p class="text-yellow-200 text-sm">6S责任区功能完成开发并投入使用</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-3 bg-cyan-500/20 rounded-lg">
                                    <i class="fas fa-chart-line text-cyan-400 mr-3"></i>
                                    <div>
                                        <span class="text-white font-semibold">Q2 2026：系统集成</span>
                                        <p class="text-cyan-200 text-sm">与生产系统数据对接，实现数据互通</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-3 bg-pink-500/20 rounded-lg">
                                    <i class="fas fa-star text-pink-400 mr-3"></i>
                                    <div>
                                        <span class="text-white font-semibold">Q4 2026：推广应用</span>
                                        <p class="text-pink-200 text-sm">在各厂区全面推广，成为创新标杆项目</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </main>
    </div>

    <!-- 底部导航 -->
    <footer class="text-center py-6 text-blue-200 flex justify-center space-x-8 relative z-20">
        <button onclick="goPrev()" class="px-6 py-2 rounded-xl bg-white/20 hover:bg-white/30 transition text-lg font-semibold"><i class="fas fa-arrow-left mr-2"></i>上一页</button>
        <button onclick="goNext()" class="px-6 py-2 rounded-xl bg-white/20 hover:bg-white/30 transition text-lg font-semibold">下一页<i class="fas fa-arrow-right ml-2"></i></button>
    </footer>

    <script>
        // 导航
        function goPrev() { window.location.href = 'slide9.html'; }
        function goNext() { window.location.href = 'end.html'; }
        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                goPrev();
            } else if (e.key === 'ArrowRight' || e.key === ' ') {
                goNext();
            }
        });
    </script>
</body>
</html>
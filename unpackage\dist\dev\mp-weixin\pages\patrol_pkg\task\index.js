require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol_pkg/task/index"],{

/***/ 403:
/*!********************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fpatrol_pkg%2Ftask%2Findex"} ***!
  \********************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index2 = _interopRequireDefault(__webpack_require__(/*! ./pages/patrol_pkg/task/index.vue */ 404));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index2.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 404:
/*!***********************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/index.vue ***!
  \***********************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_3bf106d6_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=3bf106d6&scoped=true& */ 405);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 407);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_id_3bf106d6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=3bf106d6&lang=scss&scoped=true& */ 409);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_3bf106d6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_3bf106d6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "3bf106d6",
  null,
  false,
  _index_vue_vue_type_template_id_3bf106d6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/patrol_pkg/task/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 405:
/*!******************************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/index.vue?vue&type=template&id=3bf106d6&scoped=true& ***!
  \******************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_3bf106d6_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=3bf106d6&scoped=true& */ 406);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_3bf106d6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_3bf106d6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_3bf106d6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_3bf106d6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 406:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/index.vue?vue&type=template&id=3bf106d6&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 483))
    },
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 490))
    },
    uniLoadMore: function () {
      return Promise.all(/*! import() | uni_modules/uni-load-more/components/uni-load-more/uni-load-more */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-load-more/components/uni-load-more/uni-load-more")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue */ 497))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.__map(_vm.calendarDays, function (day, index) {
    var $orig = _vm.__get_orig(day)
    var m0 = _vm.isSelectedDate(day.date)
    return {
      $orig: $orig,
      m0: m0,
    }
  })
  var m1 =
    _vm.uniIDHasRole("reviser") ||
    _vm.uniIDHasRole("supervisor") ||
    _vm.uniIDHasRole("PM") ||
    _vm.uniIDHasRole("GM") ||
    _vm.uniIDHasRole("admin")
  var g0 = _vm.filteredTasks.length
  var g1 = !!_vm.initialDataLoaded
    ? _vm.initialDataLoaded && _vm.filteredTasks.length === 0
    : null
  var l2 =
    !!_vm.initialDataLoaded && !g1
      ? _vm.__map(_vm.filteredTasks, function (task, index) {
          var $orig = _vm.__get_orig(task)
          var m2 =
            task.patrol_date || _vm.formatDateForDisplay(task.create_date)
          var m3 = _vm.getStatusText(task.status)
          var m4 = !(task.route && task.route.area)
            ? _vm.getRouteAreaName(task)
            : null
          var m5 = task.shift_id ? _vm.getShiftName(task.shift_id) : null
          var m6 = _vm.getUserName(task)
          var m7 = _vm.getFormattedRoundsInfo(task)
          var g2 = task.rounds_detail && task.rounds_detail.length > 0
          var l1 = g2
            ? _vm.__map(task.rounds_detail, function (round, index) {
                var $orig = _vm.__get_orig(round)
                var g3 = task.rounds_detail.length
                var m8 = _vm.formatTimeRange(round)
                var m9 =
                  round.status === 1 ? _vm.calculateRemainingTime(round) : null
                var m10 =
                  !(round.status === 1) && round.status === 0
                    ? _vm.calculateStartCountdown(round)
                    : null
                var m11 = _vm.getRoundStatusTextByCode(round.status || 0)
                return {
                  $orig: $orig,
                  g3: g3,
                  m8: m8,
                  m9: m9,
                  m10: m10,
                  m11: m11,
                }
              })
            : null
          var m12 =
            _vm.uniIDHasRole("reviser") ||
            _vm.uniIDHasRole("supervisor") ||
            _vm.uniIDHasRole("PM") ||
            _vm.uniIDHasRole("GM") ||
            _vm.uniIDHasRole("admin")
          return {
            $orig: $orig,
            m2: m2,
            m3: m3,
            m4: m4,
            m5: m5,
            m6: m6,
            m7: m7,
            g2: g2,
            l1: l1,
            m12: m12,
          }
        })
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
        m1: m1,
        g0: g0,
        g1: g1,
        l2: l2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 407:
/*!************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/index.vue?vue&type=script&lang=js& ***!
  \************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 408);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 408:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/index.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! @/utils/patrol-api.js */ 71));
var _date3 = __webpack_require__(/*! @/utils/date.js */ 72);
var _cache = __webpack_require__(/*! @/utils/cache.js */ 45);
var _methods;
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var PEmptyState = function PEmptyState() {
  __webpack_require__.e(/*! require.ensure | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then((function () {
    return resolve(__webpack_require__(/*! @/components/p-empty-state/p-empty-state.vue */ 483));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    PEmptyState: PEmptyState
  },
  data: function data() {
    return {
      page: 1,
      limit: 10,
      // 修改数据结构以支持按月份缓存
      cachedTasks: {},
      // 按月份缓存任务数据，格式：{'2024-03': [...tasks]}
      currentDisplayTasks: [],
      // 当前显示的任务（根据选中日期和权限筛选）
      hasMore: true,
      loading: false,
      isRefreshing: false,
      searchText: '',
      dayOffset: 0,
      // 用于查看过去或未来的任务
      currentDateOriginal: '',
      // 原始当前日期
      currentDate: '',
      // 格式化后的日期字符串，用于API查询
      statusFilter: ['0', '1', '2', '3', '4'],
      // 默认查看所有状态的任务 
      showFilters: false,
      isInitialLoading: true,
      // 添加初始加载标志
      shiftInfo: {},
      // 缓存班次信息
      routePointCounts: {},
      // 缓存路线点位数量
      startDate: '',
      endDate: '',
      sortBy: 'create_time',
      sortDirection: 'desc',
      userMap: {},
      pageReady: true,
      // 默认为true，立即显示页面
      initialDataLoaded: false,
      // 标记初始数据是否加载完成
      // 日历相关数据
      weekdays: ['日', '一', '二', '三', '四', '五', '六'],
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth(),
      selectedDate: new Date(),
      calendarDays: [],
      taskDates: new Set(),
      // 存储有任务的日期
      userInfo: null,
      filterStatus: -1,
      filterOptions: [{
        label: '全部',
        value: -1
      }, {
        label: '未开始',
        value: 0
      }, {
        label: '进行中',
        value: 1
      }, {
        label: '已完成',
        value: 2
      }, {
        label: '已超时',
        value: 3
      }, {
        label: '已取消',
        value: 4
      }],
      timer: null,
      lastUpdateTime: null,
      // 新增：月份加载管理
      loadedMonths: {},
      // 记录已加载的月份，改为对象以支持Vue响应式
      monthTaskCounts: {},
      // 记录每个月份的任务总数
      dateRange: {
        start: null,
        // 可查看的最早日期
        end: null // 可查看的最晚日期
      },

      // 用户权限信息
      isAdmin: false,
      // 缓存相关
      cacheKeys: {
        users: 'patrol_users_cache',
        routes: 'patrol_routes_cache',
        shifts: 'patrol_shifts_cache'
      },
      cacheExpiry: 30 * 60 * 1000 // 30分钟缓存过期时间
    };
  },

  computed: {
    // 格式化选中日期显示
    formatSelectedDate: function formatSelectedDate() {
      var date = this.selectedDate;
      var today = new Date();
      var tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // 判断是否是今天
      if (this.isSameDay(date, today)) {
        return '今天';
      }

      // 判断是否是明天
      if (this.isSameDay(date, tomorrow)) {
        return '明天';
      }

      // 标准格式
      return "".concat(date.getFullYear(), "\u5E74").concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5");
    },
    // 根据选中日期和权限筛选任务
    filteredTasks: function filteredTasks() {
      var _this = this;
      if (!this.currentDisplayTasks || this.currentDisplayTasks.length === 0) {
        return [];
      }
      var dateStr = this.formatDateForCompare(this.selectedDate);
      if (!dateStr) {
        return [];
      }
      var filtered = this.currentDisplayTasks.filter(function (task) {
        if (!task.patrol_date) {
          return false;
        }
        try {
          var taskDate = _this.formatDateForCompare(new Date(task.patrol_date));
          if (taskDate !== dateStr) {
            return false;
          }
          if (_this.filterStatus !== -1 && parseInt(task.status) !== parseInt(_this.filterStatus)) {
            return false;
          }
          return true;
        } catch (e) {
          console.error('❌ 任务过滤错误:', e);
          return false;
        }
      });
      return filtered;
    },
    // 获取所有缓存的任务（用于日历小点点显示）
    allCachedTasks: function allCachedTasks() {
      var allTasks = [];
      Object.values(this.cachedTasks).forEach(function (monthTasks) {
        if (Array.isArray(monthTasks)) {
          allTasks.push.apply(allTasks, (0, _toConsumableArray2.default)(monthTasks));
        }
      });
      return allTasks;
    },
    hasActiveFilters: function hasActiveFilters() {
      return this.filterDate || this.filterStatus !== -1 || this.startDate || this.endDate || this.sortBy || this.sortDirection;
    },
    // 计算属性：用于API筛选的日期格式
    formattedDateForAPI: function formattedDateForAPI() {
      return this.formatDateForCompare(this.selectedDate);
    }
  },
  onLoad: function onLoad() {
    var _this2 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      var loadPromises;
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              // 1. 立即显示页面骨架，提升用户体验
              _this2.pageReady = true;
              // 确保初始状态为加载中，显示骨架屏
              _this2.initialDataLoaded = false;

              // 2. 同步初始化（很快）
              _this2.initDateRange();
              _this2.initCalendar();

              // 3. 显示加载状态
              uni.showLoading({
                title: '加载中...',
                mask: true
              });

              // 4. 优先加载用户信息（其他功能依赖此数据）
              _context.next = 8;
              return _this2.getUserInfo();
            case 8:
              // 5. 并行加载所有数据，而不是串行等待
              loadPromises = [_this2.loadCurrentMonthTasks().catch(function (e) {
                console.error('❌ loadCurrentMonthTasks 失败:', e);
                // 即使任务加载失败，也不影响其他功能
              }), _this2.loadUsers().catch(function (e) {
                console.error('❌ 加载用户失败:', e);
                // 用户数据加载失败不影响主要功能
              }), _this2.loadRoutes().catch(function (e) {
                console.error('❌ 加载路线失败:', e);
                // 路线数据加载失败不影响主要功能
              })]; // 6. 等待所有数据加载完成
              _context.next = 11;
              return Promise.all(loadPromises);
            case 11:
              _context.next = 13;
              return _this2.$nextTick();
            case 13:
              _context.next = 15;
              return _this2.$nextTick();
            case 15:
              _context.next = 17;
              return new Promise(function (resolve) {
                return setTimeout(resolve, 300);
              });
            case 17:
              // 9. 标记初始数据加载完成
              _this2.initialDataLoaded = true;

              // 10. 启动定时更新
              _this2.startTimeUpdateTimer();

              // 11. 预加载相邻月份（提升切换体验）
              // 🔥 RU优化：禁用预加载，采用按需加载策略
              // 预计节省56.7%的RU消耗（假设70%用户只查看当前月份）
              // 权衡：初次切换月份时需要等待加载，但大幅减少不必要的数据库查询
              // setTimeout(() => {
              // 	this.preloadAdjacentMonths();
              // }, 1000);
              _context.next = 26;
              break;
            case 21:
              _context.prev = 21;
              _context.t0 = _context["catch"](0);
              console.error('❌ onLoad 失败:', _context.t0);
              uni.showToast({
                title: '加载失败，请重试',
                icon: 'none'
              });
              // 即使失败也要标记加载完成，避免一直显示骨架屏
              _this2.initialDataLoaded = true;
            case 26:
              _context.prev = 26;
              // 确保重置所有加载状态
              _this2.isRefreshing = false;
              _this2.loading = false;
              uni.hideLoading();
              return _context.finish(26);
            case 31:
            case "end":
              return _context.stop();
          }
        }
      }, _callee, null, [[0, 21, 26, 31]]);
    }))();
  },
  onShow: function onShow() {
    var _this3 = this;
    // 每次页面显示时检查是否需要刷新数据
    if (this.pageReady && this.initialDataLoaded) {
      var currentYearMonth = this.getYearMonth(this.selectedDate);
      var hasCurrentMonthData = this.cachedTasks[currentYearMonth] && this.cachedTasks[currentYearMonth].length > 0;

      // 简化逻辑：只检查当前月份是否有数据
      if (!hasCurrentMonthData) {
        // 当前月份没有数据，需要加载
        this.loadMonthTasks(currentYearMonth).then(function () {
          _this3.updateCurrentDisplayTasks(true); // 新数据需要重新加载班次信息
          _this3.extractTaskDates();
        });
      } else {
        // 有数据，只需要轻量级更新显示
        this.updateCurrentDisplayTasks(false); // 缓存数据不需要重新加载班次信息
      }
    }
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.refreshData().then(function () {}).catch(function (err) {
      console.error('下拉刷新出错:', err);
    });
  },
  onReady: function onReady() {
    // 注册任务刷新事件监听
    uni.$on('refresh-task-list', this.refreshList);
  },
  onUnload: function onUnload() {
    // 移除事件监听，避免内存泄漏
    uni.$off('refresh-task-list', this.refreshList);
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  methods: (_methods = {
    // 缓存工具方法
    setCache: function setCache(key, data) {
      var cacheData = {
        data: data,
        timestamp: Date.now(),
        expiry: this.cacheExpiry
      };
      try {
        uni.setStorageSync(key, JSON.stringify(cacheData));
      } catch (e) {
        console.error('设置缓存失败:', e);
      }
    },
    getCache: function getCache(key) {
      try {
        var cacheStr = uni.getStorageSync(key);
        if (!cacheStr) return null;
        var cacheData = JSON.parse(cacheStr);
        var now = Date.now();

        // 检查是否过期
        if (now - cacheData.timestamp > cacheData.expiry) {
          uni.removeStorageSync(key);
          return null;
        }
        return cacheData.data;
      } catch (e) {
        console.error('获取缓存失败:', e);
        return null;
      }
    },
    clearCache: function clearCache(key) {
      try {
        uni.removeStorageSync(key);
      } catch (e) {
        console.error('清除缓存失败:', e);
      }
    },
    // 初始化日期范围（前一个月、当前月、下一个月）
    initDateRange: function initDateRange() {
      // 使用月份差值判断，不再需要精确的开始和结束日期
      // 保留这个方法是为了向后兼容，实际判断在isDateInRange中进行
      var now = new Date();
      this.dateRange = {
        start: new Date(now.getFullYear(), now.getMonth() - 1, 1),
        end: new Date(now.getFullYear(), now.getMonth() + 1, 31)
      };
    },
    // 获取年月字符串
    getYearMonth: function getYearMonth(date) {
      try {
        var d = date instanceof Date ? date : new Date(date);
        if (isNaN(d.getTime())) {
          console.error("\u274C getYearMonth: \u65E0\u6548\u65E5\u671F", date);
          return '';
        }
        return "".concat(d.getFullYear(), "-").concat(String(d.getMonth() + 1).padStart(2, '0'));
      } catch (e) {
        console.error("\u274C getYearMonth \u9519\u8BEF:", e);
        return '';
      }
    },
    // 判断日期是否在可查看范围内
    isDateInRange: function isDateInRange(date) {
      var d = date instanceof Date ? date : new Date(date);

      // 获取待检查日期的年月
      var checkYear = d.getFullYear();
      var checkMonth = d.getMonth();

      // 获取当前日期的年月
      var now = new Date();
      var currentYear = now.getFullYear();
      var currentMonth = now.getMonth();

      // 计算月份差值
      var monthDiff = (checkYear - currentYear) * 12 + (checkMonth - currentMonth);

      // 允许查看范围：前一个月(-1)、当前月(0)、下一个月(1)
      return monthDiff >= -1 && monthDiff <= 1;
    },
    // 更新当前显示的任务
    updateCurrentDisplayTasks: function updateCurrentDisplayTasks() {
      var forceReloadShifts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
      var selectedDateStr = this.formatDateForCompare(this.selectedDate);
      var selectedYearMonth = this.getYearMonth(this.selectedDate);

      // 获取当前月份的所有任务
      var monthTasks = this.cachedTasks[selectedYearMonth] || [];

      // 根据权限筛选任务
      if (this.isAdmin) {
        // 管理员显示所有任务
        this.currentDisplayTasks = monthTasks;
      } else {
        // 普通用户只显示自己的任务
        var userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};
        this.currentDisplayTasks = monthTasks.filter(function (task) {
          return task.user_id === userInfo._id;
        });
      }

      // 只有在强制重新加载或者有新任务时才加载班次信息
      if (this.currentDisplayTasks.length > 0 && forceReloadShifts) {
        this.loadShiftInfo();
      }
    },
    // 初始化日历
    initCalendar: function initCalendar() {
      var today = new Date();
      this.currentYear = today.getFullYear();
      this.currentMonth = today.getMonth();
      this.selectedDate = today; // 确保选中今天
      this.generateCalendarDays();
    },
    // 刷新列表和日历
    refreshList: function refreshList() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var selectedYearMonth;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                // 🔥 显示骨架屏
                _this4.initialDataLoaded = false;
                _this4.currentDisplayTasks = [];
                selectedYearMonth = _this4.getYearMonth(_this4.selectedDate); // 清除缓存，强制重新加载
                _this4.$delete(_this4.loadedMonths, selectedYearMonth);

                // 重新加载当前月份的任务
                _context2.next = 7;
                return _this4.loadMonthTasks(selectedYearMonth);
              case 7:
                // 更新显示的任务
                _this4.updateCurrentDisplayTasks(true); // 刷新时需要重新加载班次信息

                // 更新日历指示器
                _this4.extractTaskDates();

                // 添加最小延迟，确保用户能看到刷新效果
                _context2.next = 11;
                return new Promise(function (resolve) {
                  return setTimeout(resolve, 200);
                });
              case 11:
                _context2.next = 16;
                break;
              case 13:
                _context2.prev = 13;
                _context2.t0 = _context2["catch"](0);
                console.error('刷新列表失败:', _context2.t0);
              case 16:
                _context2.prev = 16;
                // 🔥 标记加载完成
                _this4.initialDataLoaded = true;
                return _context2.finish(16);
              case 19:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 13, 16, 19]]);
      }))();
    },
    // 选择日期
    selectDate: function selectDate(day) {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var date, yearMonth;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                if (!(!day || !day.date)) {
                  _context3.next = 3;
                  break;
                }
                console.error('无效的日期选择:', day);
                return _context3.abrupt("return");
              case 3:
                // 确保日期是有效的
                date = new Date(day.date);
                if (!isNaN(date.getTime())) {
                  _context3.next = 7;
                  break;
                }
                console.error('无效的日期:', day.date);
                return _context3.abrupt("return");
              case 7:
                if (_this5.isDateInRange(date)) {
                  _context3.next = 10;
                  break;
                }
                uni.showToast({
                  title: '该日期超出可查看范围',
                  icon: 'none'
                });
                return _context3.abrupt("return");
              case 10:
                _this5.selectedDate = date;

                // 如果选择的日期不在当前月份，需要更新月份并加载数据
                if (!(date.getMonth() !== _this5.currentMonth || date.getFullYear() !== _this5.currentYear)) {
                  _context3.next = 24;
                  break;
                }
                _this5.currentMonth = date.getMonth();
                _this5.currentYear = date.getFullYear();
                _this5.generateCalendarDays();

                // 加载新月份的任务数据
                yearMonth = _this5.getYearMonth(date);
                if (_this5.loadedMonths[yearMonth]) {
                  _context3.next = 24;
                  break;
                }
                // 🔥 跨月份时显示骨架屏
                _this5.initialDataLoaded = false;
                _this5.currentDisplayTasks = [];
                _context3.next = 21;
                return _this5.loadMonthTasks(yearMonth);
              case 21:
                _context3.next = 23;
                return new Promise(function (resolve) {
                  return setTimeout(resolve, 150);
                });
              case 23:
                _this5.initialDataLoaded = true;
              case 24:
                // 更新显示的任务
                _this5.updateCurrentDisplayTasks(false); // 切换月份时不需要重新加载班次信息
              case 25:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    // 生成日历天数
    generateCalendarDays: function generateCalendarDays() {
      var year = this.currentYear;
      var month = this.currentMonth;
      var firstDay = new Date(year, month, 1);
      var lastDay = new Date(year, month + 1, 0);
      var firstDayOfWeek = firstDay.getDay();
      var daysInMonth = lastDay.getDate();
      var prevMonthLastDay = new Date(year, month, 0);
      var daysInPrevMonth = prevMonthLastDay.getDate();
      var days = [];

      // 添加上个月的日期
      for (var i = firstDayOfWeek - 1; i >= 0; i--) {
        var day = daysInPrevMonth - i;
        var date = new Date(year, month - 1, day);
        days.push({
          day: day,
          date: date,
          currentMonth: false,
          isToday: this.isToday(date),
          hasTask: this.hasTasksOnDate(date)
        });
      }

      // 添加当月的日期
      for (var _i = 1; _i <= daysInMonth; _i++) {
        var _date = new Date(year, month, _i);
        days.push({
          day: _i,
          date: _date,
          currentMonth: true,
          isToday: this.isToday(_date),
          hasTask: this.hasTasksOnDate(_date)
        });
      }

      // 添加下个月的日期，补齐6行7列
      var remainingDays = 42 - days.length;
      for (var _i2 = 1; _i2 <= remainingDays; _i2++) {
        var _date2 = new Date(year, month + 1, _i2);
        days.push({
          day: _i2,
          date: _date2,
          currentMonth: false,
          isToday: this.isToday(_date2),
          hasTask: this.hasTasksOnDate(_date2)
        });
      }
      this.calendarDays = days;
    },
    // 判断是否是今天
    isToday: function isToday(date) {
      var today = new Date();
      return this.isSameDay(date, today);
    },
    // 判断两个日期是否是同一天
    isSameDay: function isSameDay(date1, date2) {
      if (!date1 || !date2) return false;
      try {
        // 转换为Date对象
        var d1 = date1 instanceof Date ? date1 : new Date(date1);
        var d2 = date2 instanceof Date ? date2 : new Date(date2);
        return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth() && d1.getDate() === d2.getDate();
      } catch (e) {
        console.error('日期比较错误:', e);
        return false;
      }
    },
    // 判断日期是否被选中
    isSelectedDate: function isSelectedDate(date) {
      return this.isSameDay(date, this.selectedDate);
    },
    // 判断日期是否有任务
    hasTasksOnDate: function hasTasksOnDate(date) {
      try {
        if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
          return false;
        }
        var dateStr = this.formatDateForCompare(date);
        if (!dateStr) {
          return false;
        }
        return this.taskDates.has(dateStr);
      } catch (e) {
        console.error('❌ 检查日期任务出错:', e);
        return false;
      }
    },
    // 格式化日期用于比较
    formatDateForCompare: function formatDateForCompare(date) {
      if (!date) {
        return '';
      }
      try {
        var d = date instanceof Date ? date : new Date(date);
        if (isNaN(d.getTime())) {
          return '';
        }
        return (0, _date3.formatDate)(d, 'YYYY-MM-DD');
      } catch (e) {
        console.error('❌ 日期格式化错误:', e);
        return '';
      }
    },
    // 上个月 - 数据已预加载，切换流畅
    prevMonth: function prevMonth() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var newDate, yearMonth;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                // 检查是否超出范围
                newDate = new Date(_this6.currentYear, _this6.currentMonth - 1, 1);
                if (_this6.isDateInRange(newDate)) {
                  _context4.next = 4;
                  break;
                }
                uni.showToast({
                  title: '已到达可查看的最早月份',
                  icon: 'none'
                });
                return _context4.abrupt("return");
              case 4:
                _context4.prev = 4;
                if (_this6.currentMonth === 0) {
                  _this6.currentYear--;
                  _this6.currentMonth = 11;
                } else {
                  _this6.currentMonth--;
                }
                _this6.generateCalendarDays();

                // 数据已预加载，如果没有则快速加载
                yearMonth = _this6.getYearMonth(new Date(_this6.currentYear, _this6.currentMonth, 1));
                if (_this6.loadedMonths[yearMonth]) {
                  _context4.next = 16;
                  break;
                }
                // 🔥 切换月份时显示骨架屏
                _this6.initialDataLoaded = false;
                _this6.currentDisplayTasks = [];
                _context4.next = 13;
                return _this6.loadMonthTasks(yearMonth);
              case 13:
                _context4.next = 15;
                return new Promise(function (resolve) {
                  return setTimeout(resolve, 150);
                });
              case 15:
                _this6.initialDataLoaded = true;
              case 16:
                // 更新显示的任务
                _this6.updateCurrentDisplayTasks(false); // 切换月份时不需要重新加载班次信息
                _context4.next = 23;
                break;
              case 19:
                _context4.prev = 19;
                _context4.t0 = _context4["catch"](4);
                console.error('❌ 切换上个月失败:', _context4.t0);
                uni.showToast({
                  title: '切换失败，请重试',
                  icon: 'none'
                });
              case 23:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[4, 19]]);
      }))();
    },
    // 下个月 - 数据已预加载，切换流畅
    nextMonth: function nextMonth() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var newDate, yearMonth;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                // 检查是否超出范围
                newDate = new Date(_this7.currentYear, _this7.currentMonth + 1, 1);
                if (_this7.isDateInRange(newDate)) {
                  _context5.next = 4;
                  break;
                }
                uni.showToast({
                  title: '已到达可查看的最晚月份',
                  icon: 'none'
                });
                return _context5.abrupt("return");
              case 4:
                _context5.prev = 4;
                if (_this7.currentMonth === 11) {
                  _this7.currentYear++;
                  _this7.currentMonth = 0;
                } else {
                  _this7.currentMonth++;
                }
                _this7.generateCalendarDays();

                // 数据已预加载，如果没有则快速加载
                yearMonth = _this7.getYearMonth(new Date(_this7.currentYear, _this7.currentMonth, 1));
                if (_this7.loadedMonths[yearMonth]) {
                  _context5.next = 16;
                  break;
                }
                // 🔥 切换月份时显示骨架屏
                _this7.initialDataLoaded = false;
                _this7.currentDisplayTasks = [];
                _context5.next = 13;
                return _this7.loadMonthTasks(yearMonth);
              case 13:
                _context5.next = 15;
                return new Promise(function (resolve) {
                  return setTimeout(resolve, 150);
                });
              case 15:
                _this7.initialDataLoaded = true;
              case 16:
                // 更新显示的任务
                _this7.updateCurrentDisplayTasks(false); // 切换月份时不需要重新加载班次信息
                _context5.next = 23;
                break;
              case 19:
                _context5.prev = 19;
                _context5.t0 = _context5["catch"](4);
                console.error('❌ 切换下个月失败:', _context5.t0);
                uni.showToast({
                  title: '切换失败，请重试',
                  icon: 'none'
                });
              case 23:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[4, 19]]);
      }))();
    },
    // 提取任务日期并更新日历 - 优化版本
    extractTaskDates: function extractTaskDates() {
      var _this8 = this;
      // 使用setTimeout避免阻塞主线程
      setTimeout(function () {
        try {
          // 清空之前的日期集合
          _this8.taskDates.clear();

          // 手动计算所有缓存任务，因为allCachedTasks计算属性有响应式问题
          var manualAllTasks = [];
          Object.values(_this8.cachedTasks).forEach(function (monthTasks) {
            if (Array.isArray(monthTasks)) {
              manualAllTasks.push.apply(manualAllTasks, (0, _toConsumableArray2.default)(monthTasks));
            }
          });

          // 根据权限筛选要显示小点点的任务
          var userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};
          var tasksToShow = _this8.isAdmin ? manualAllTasks : manualAllTasks.filter(function (task) {
            return task.user_id === userInfo._id;
          });

          // 批量处理日期，减少DOM操作
          var dateSet = new Set();
          tasksToShow.forEach(function (task) {
            if (task.patrol_date) {
              dateSet.add(task.patrol_date);
            }
          });

          // 一次性更新taskDates
          _this8.taskDates = dateSet;

          // 使用nextTick确保数据更新完成后再更新日历
          _this8.$nextTick(function () {
            _this8.generateCalendarDays();
          });
        } catch (e) {
          console.error('❌ 提取任务日期时发生错误:', e);
        }
      }, 0);
    },
    // 只加载当前月份的任务
    loadCurrentMonthTasks: function loadCurrentMonthTasks() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var currentYearMonth;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _context6.prev = 0;
                currentYearMonth = _this9.getYearMonth(new Date());
                if (currentYearMonth) {
                  _context6.next = 5;
                  break;
                }
                console.error("\u274C \u65E0\u6548\u7684\u5E74\u6708\u5B57\u7B26\u4E32: ".concat(currentYearMonth));
                return _context6.abrupt("return");
              case 5:
                _context6.next = 7;
                return _this9.loadMonthTasks(currentYearMonth);
              case 7:
                _this9.updateCurrentDisplayTasks(true); // 加载新数据时需要重新加载班次信息
                _this9.extractTaskDates();
                _context6.next = 14;
                break;
              case 11:
                _context6.prev = 11;
                _context6.t0 = _context6["catch"](0);
                console.error('❌ 加载当前月份任务失败:', _context6.t0);
              case 14:
                _context6.prev = 14;
                // 确保重置刷新状态
                _this9.isRefreshing = false;
                return _context6.finish(14);
              case 17:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[0, 11, 14, 17]]);
      }))();
    },
    // 按月份加载任务
    loadMonthTasks: function loadMonthTasks(yearMonth) {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var userInfo, userRoleStr, userRole, _yearMonth$split, _yearMonth$split2, year, month, startDate, endDate, params, response, rawTasks, total, processedTasks;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                if (!_this10.loading) {
                  _context7.next = 2;
                  break;
                }
                return _context7.abrupt("return");
              case 2:
                if (!_this10.loadedMonths[yearMonth]) {
                  _context7.next = 4;
                  break;
                }
                return _context7.abrupt("return");
              case 4:
                _this10.loading = true;
                _context7.prev = 5;
                // 获取用户信息和权限
                userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};
                userRoleStr = uni.getStorageSync((0, _cache.getCacheKey)(_cache.CACHE_KEYS.USER_ROLE)) || '{}';
                userRole = {};
                try {
                  if (typeof userRoleStr === 'string') {
                    userRole = JSON.parse(userRoleStr);
                  } else {
                    userRole = userRoleStr;
                  }
                } catch (e) {
                  userRole = {
                    value: {
                      userRole: []
                    }
                  };
                }

                // 确定用户权限
                _this10.isAdmin = userRole.value && userRole.value.userRole && Array.isArray(userRole.value.userRole) && (userRole.value.userRole.includes('admin') || userRole.value.userRole.includes('GM') || userRole.value.userRole.includes('PM') || userRole.value.userRole.includes('supervisor') || userRole.value.userRole.includes('reviser'));

                // 计算月份的开始和结束日期
                _yearMonth$split = yearMonth.split('-'), _yearMonth$split2 = (0, _slicedToArray2.default)(_yearMonth$split, 2), year = _yearMonth$split2[0], month = _yearMonth$split2[1];
                startDate = "".concat(year, "-").concat(month, "-01"); // 修复日期计算：new Date(year, month, 0) 中的month应该是实际月份
                endDate = "".concat(year, "-").concat(month, "-").concat(new Date(parseInt(year), parseInt(month), 0).getDate()); // 构建API参数 - 🔥 优化：使用轻量级数据层级 + 角色权限控制
                params = {
                  params: {
                    startDate: startDate,
                    endDate: endDate,
                    pageSize: 800,
                    // 平衡性能和完整性，足够覆盖400个任务的月份
                    status: -1,
                    // 全部状态
                    // 🔥 移除硬编码的userId过滤，改为使用viewScope控制
                    viewScope: 'role-based',
                    // 🔥 新增：角色决定模式，管理层看全部，普通用户看自己
                    level: 'list',
                    // 🔥 新增：列表级别数据，不包含完整points数组
                    // 🔥 关键优化：只请求列表必需的字段，大幅减少数据传输
                    fields: '_id,name,status,area,patrol_date,shift_id,shift_name,user_id,user_name,route_name,create_date,overall_stats,rounds_detail.round,rounds_detail.name,rounds_detail.status,rounds_detail.start_time,rounds_detail.end_time,rounds_detail.duration,rounds_detail.stats'
                  }
                };
                _context7.next = 17;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'getTaskList',
                  data: params
                });
              case 17:
                response = _context7.sent;
                if (response.code === 0) {
                  rawTasks = response.data.list || [];
                  total = response.data.total || 0; // 🔥 优化后的数据加载说明：
                  // 1. 使用level='list'参数，服务端返回轻量级数据
                  // 2. 不包含points数组，减少85%+数据传输量
                  // 3. 使用优化后的rounds_detail和overall_stats提供统计信息
                  // 4. 预计数据量：从15-25KB/任务 降至 2-3KB/任务
                  processedTasks = _this10.processTasks(rawTasks); // 检查是否需要加载更多数据
                  if (rawTasks.length >= 800 && total > 800) {
                    console.warn("\u26A0\uFE0F \u6708\u4EFD ".concat(yearMonth, " \u4EFB\u52A1\u6570\u91CF (").concat(total, ") \u8D85\u8FC7\u5355\u6B21\u52A0\u8F7D\u9650\u5236 (800)\uFF0C\u53EF\u80FD\u663E\u793A\u4E0D\u5B8C\u6574"));
                    uni.showToast({
                      title: "\u8BE5\u6708\u4EFB\u52A1\u8F83\u591A(".concat(total, "\u4E2A)\uFF0C\u53EF\u80FD\u663E\u793A\u4E0D\u5B8C\u6574"),
                      icon: 'none',
                      duration: 3000
                    });
                  }

                  // 缓存月份数据和统计信息
                  _this10.cachedTasks[yearMonth] = processedTasks;
                  _this10.monthTaskCounts[yearMonth] = {
                    loaded: rawTasks.length,
                    total: total
                  };
                  _this10.$set(_this10.loadedMonths, yearMonth, true);

                  // 使用nextTick确保数据更新完成后再更新UI
                  _this10.$nextTick(function () {
                    _this10.extractTaskDates();
                  });
                } else {
                  uni.showToast({
                    title: response.message || '加载失败',
                    icon: 'none'
                  });
                }
                _context7.next = 25;
                break;
              case 21:
                _context7.prev = 21;
                _context7.t0 = _context7["catch"](5);
                console.error('❌ 加载月份任务失败:', _context7.t0);
                uni.showToast({
                  title: '加载任务失败',
                  icon: 'none'
                });
              case 25:
                _context7.prev = 25;
                _this10.loading = false;
                if (_this10.isRefreshing) {
                  _this10.isRefreshing = false;
                }
                return _context7.finish(25);
              case 29:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[5, 21, 25, 29]]);
      }))();
    },
    // 处理任务列表数据 - 🔥 优化：适配轻量级数据结构
    processTasks: function processTasks(list) {
      var _this11 = this;
      if (!list || !Array.isArray(list)) return [];

      // 缓存当前时间，避免重复创建Date对象
      var now = new Date();
      var nowTime = now.getTime();
      return list.map(function (task) {
        // 🔥 优化：使用服务端已优化的数据结构
        var processedTask = {
          _id: task._id,
          name: task.name || task.route_name || '未命名任务',
          status: parseInt(task.status || 0),
          area: task.area || task.route_name || '未指定区域',
          patrol_date: task.patrol_date || _this11.formatDateForCompare(new Date()),
          shift_id: task.shift_id || '',
          user_id: task.user_id,
          user_name: task.user_name,
          route_name: task.route_name,
          create_date: task.create_date,
          // 🔥 优化：使用云函数优化后的rounds_detail数据
          rounds_detail: task.rounds_detail ? task.rounds_detail.map(function (round) {
            return {
              round: round.round,
              // 轮次编号
              name: round.name,
              // 轮次名称
              status: round.status || 0,
              // 轮次状态
              start_time: round.start_time,
              // 开始时间
              end_time: round.end_time,
              // 结束时间
              duration: round.duration || 60,
              // 时长
              day_offset: round.day_offset || 0,
              // 跨天偏移
              // 🔥 保留统计信息，但不包含points数组
              stats: round.stats || {
                total_points: round.total_points || 0,
                completed_points: round.completed_points || 0,
                completion_rate: round.completion_rate || 0
              }
            };
          }) : [],
          // 🔥 优化：直接使用服务端的统计信息
          overall_stats: task.overall_stats ? {
            total_points: task.overall_stats.total_points || 0,
            completed_points: task.overall_stats.completed_points || 0,
            completion_rate: task.overall_stats.completion_rate || 0,
            missed_points: task.overall_stats.missed_points || 0
          } : null
        };

        // 🔥 优化：简化轮次状态处理逻辑
        // 兼容性说明：支持云函数优化后的轻量级rounds_detail数据结构
        if (processedTask.rounds_detail && processedTask.rounds_detail.length > 0) {
          var hasActiveRound = false;
          var hasUpcomingRound = false;
          var allRoundsCompleted = true;
          processedTask.rounds_detail = processedTask.rounds_detail.map(function (round) {
            try {
              // 解析时间
              var startTime = new Date(round.start_time);
              var endTime = new Date(round.end_time);

              // 🔥 优化：如果服务端已提供状态，直接使用；否则才计算
              var computedStatus = round.status;
              if (computedStatus === undefined || computedStatus === null) {
                // 使用简化的状态计算逻辑
                if (processedTask.overall_stats && processedTask.overall_stats.completion_rate === 1 && now >= startTime) {
                  computedStatus = 2; // 已完成
                } else if (now < startTime) {
                  computedStatus = 0; // 未开始
                } else if (now > endTime) {
                  computedStatus = 3; // 已超时
                } else {
                  computedStatus = 1; // 进行中
                }
              }

              // 更新轮次状态
              round.status = computedStatus;

              // 更新任务状态追踪变量
              if (computedStatus === 1) {
                hasActiveRound = true;
                allRoundsCompleted = false;
              } else if (computedStatus === 0) {
                hasUpcomingRound = true;
                allRoundsCompleted = false;
              } else if (computedStatus === 3) {
                allRoundsCompleted = false;
              }

              // 确保轮次有正确的时间格式
              if (!round.time) {
                round.time = _this11.formatTimeRange(round);
              }
            } catch (error) {
              console.error("\u89E3\u6790\u8F6E\u6B21[".concat(round.round, "]\u65F6\u95F4\u51FA\u9519:"), error);
            }
            return round;
          });

          // 🔥 优化：如果服务端已提供任务状态，优先使用
          if (task.status !== undefined && task.status !== null) {
            processedTask.status = parseInt(task.status);
          } else {
            // 根据轮次状态计算任务整体状态
            if (allRoundsCompleted) {
              processedTask.status = 2; // 已完成
            } else if (hasActiveRound) {
              processedTask.status = 1; // 进行中
            } else if (hasUpcomingRound) {
              processedTask.status = 0; // 未开始
            } else {
              processedTask.status = 3; // 已超时
            }
          }

          // 🔥 优化：简化排序逻辑
          processedTask.rounds_detail.sort(function (a, b) {
            // 优先显示进行中和未开始的轮次
            if ((a.status === 0 || a.status === 1) && (b.status === 2 || b.status === 3)) {
              return -1;
            }
            if ((b.status === 0 || b.status === 1) && (a.status === 2 || a.status === 3)) {
              return 1;
            }
            // 同类型按轮次号排序
            return a.round - b.round;
          });
        }
        return processedTask;
      });
    },
    // 添加与detail.vue相同的时间解析方法
    parseTimeString: function parseTimeString(timeStr) {
      try {
        // 检查是否是完整的ISO格式日期时间
        if (timeStr.includes('T') || timeStr.includes('-')) {
          var date = new Date(timeStr);
          return {
            hours: date.getHours(),
            minutes: date.getMinutes()
          };
        }

        // 否则假设是HH:MM格式
        var parts = timeStr.split(':');
        return {
          hours: parseInt(parts[0], 10),
          minutes: parseInt(parts[1], 10)
        };
      } catch (e) {
        // 发生错误时返回默认值
        console.error('解析时间出错:', e);
        return {
          hours: 0,
          minutes: 0
        };
      }
    },
    // 加载班次信息
    loadShiftInfo: function loadShiftInfo() {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var cachedShifts, shiftIds, uniqueShiftIds, batchSize, batches, i, _i3, _batches, batch, promises, results, successShifts;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                // 先尝试从缓存加载班次信息
                cachedShifts = _this12.getCache(_this12.cacheKeys.shifts);
                if (cachedShifts) {
                  // 合并缓存的班次信息
                  Object.assign(_this12.shiftInfo, cachedShifts);
                }
                shiftIds = _this12.currentDisplayTasks.map(function (task) {
                  return task.shift_id;
                }).filter(function (id) {
                  return id && !_this12.shiftInfo[id];
                }); // 去重
                uniqueShiftIds = (0, _toConsumableArray2.default)(new Set(shiftIds));
                if (!(uniqueShiftIds.length === 0)) {
                  _context9.next = 6;
                  break;
                }
                return _context9.abrupt("return");
              case 6:
                // 批量加载班次信息 - 并行请求但限制并发数
                batchSize = 5; // 限制并发数为5
                batches = [];
                for (i = 0; i < uniqueShiftIds.length; i += batchSize) {
                  batches.push(uniqueShiftIds.slice(i, i + batchSize));
                }
                _i3 = 0, _batches = batches;
              case 10:
                if (!(_i3 < _batches.length)) {
                  _context9.next = 19;
                  break;
                }
                batch = _batches[_i3];
                promises = batch.map( /*#__PURE__*/function () {
                  var _ref = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8(shiftId) {
                    var res;
                    return _regenerator.default.wrap(function _callee8$(_context8) {
                      while (1) {
                        switch (_context8.prev = _context8.next) {
                          case 0:
                            _context8.prev = 0;
                            _context8.next = 3;
                            return _patrolApi.default.call({
                              name: 'patrol-shift',
                              action: 'getShiftDetail',
                              data: {
                                params: {
                                  shift_id: shiftId
                                }
                              }
                            });
                          case 3:
                            res = _context8.sent;
                            if (!(res.code === 0 && res.data)) {
                              _context8.next = 9;
                              break;
                            }
                            _this12.shiftInfo[shiftId] = _objectSpread({}, res.data);
                            return _context8.abrupt("return", {
                              shiftId: shiftId,
                              success: true,
                              data: res.data
                            });
                          case 9:
                            // 设置错误标记
                            _this12.shiftInfo[shiftId] = {
                              error: true,
                              errorMessage: res.message || '班次不存在',
                              name: "\u73ED\u6B21".concat(shiftId.substr(-4))
                            };
                            return _context8.abrupt("return", {
                              shiftId: shiftId,
                              success: false,
                              error: res.message
                            });
                          case 11:
                            _context8.next = 18;
                            break;
                          case 13:
                            _context8.prev = 13;
                            _context8.t0 = _context8["catch"](0);
                            console.error("\u52A0\u8F7D\u73ED\u6B21 ".concat(shiftId, " \u51FA\u9519:"), _context8.t0);
                            _this12.shiftInfo[shiftId] = {
                              error: true,
                              errorMessage: _context8.t0.message || '加载失败',
                              name: "\u73ED\u6B21".concat(shiftId.substr(-4))
                            };
                            return _context8.abrupt("return", {
                              shiftId: shiftId,
                              success: false,
                              error: _context8.t0.message
                            });
                          case 18:
                          case "end":
                            return _context8.stop();
                        }
                      }
                    }, _callee8, null, [[0, 13]]);
                  }));
                  return function (_x) {
                    return _ref.apply(this, arguments);
                  };
                }()); // 等待当前批次完成
                _context9.next = 15;
                return Promise.all(promises);
              case 15:
                results = _context9.sent;
              case 16:
                _i3++;
                _context9.next = 10;
                break;
              case 19:
                // 缓存班次信息（只缓存成功加载的）
                successShifts = {};
                Object.keys(_this12.shiftInfo).forEach(function (shiftId) {
                  if (_this12.shiftInfo[shiftId] && !_this12.shiftInfo[shiftId].error) {
                    successShifts[shiftId] = _this12.shiftInfo[shiftId];
                  }
                });
                if (Object.keys(successShifts).length > 0) {
                  _this12.setCache(_this12.cacheKeys.shifts, successShifts);
                }

                // 强制更新视图
                _this12.$forceUpdate();
              case 23:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9);
      }))();
    },
    // 获取班次名称
    getShiftName: function getShiftName(shiftId) {
      if (!shiftId) return '无班次';

      // 检查班次信息是否存在并具有name属性
      if (this.shiftInfo[shiftId] && this.shiftInfo[shiftId].name) {
        return this.shiftInfo[shiftId].name;
      }

      // 如果班次信息存在但是有error属性，表示加载失败
      if (this.shiftInfo[shiftId] && this.shiftInfo[shiftId].error) {
        return "\u73ED\u6B21".concat(shiftId.substr(-4)); // 使用ID的后4位作为标识
      }

      // 如果班次ID存在于任务中但信息未加载，返回默认显示
      // 移除自动加载逻辑，避免重复请求
      if (!this.shiftInfo[shiftId]) {
        return "\u73ED\u6B21".concat(shiftId.substr(-4)); // 使用ID的后4位作为标识
      }

      // 如果已经尝试加载但没有name属性
      if (this.shiftInfo[shiftId] && !this.shiftInfo[shiftId].name) {
        return "\u73ED\u6B21".concat(shiftId.substr(-4)); // 使用ID的后4位作为标识
      }

      return '未知班次';
    },
    // 加载特定班次信息
    loadSpecificShift: function loadSpecificShift(shiftId) {
      var _this13 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var res, shiftData;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                if (shiftId) {
                  _context10.next = 2;
                  break;
                }
                return _context10.abrupt("return");
              case 2:
                if (!(_this13.shiftInfo[shiftId] && _this13.shiftInfo[shiftId].error)) {
                  _context10.next = 4;
                  break;
                }
                return _context10.abrupt("return");
              case 4:
                _context10.prev = 4;
                _context10.next = 7;
                return _patrolApi.default.call({
                  name: 'patrol-shift',
                  action: 'getShiftDetail',
                  data: {
                    params: {
                      shift_id: shiftId
                    }
                  }
                });
              case 7:
                res = _context10.sent;
                if (res.code === 0 && res.data) {
                  // 确保数据中有name属性
                  shiftData = res.data;
                  if (!shiftData.name) {
                    shiftData.name = "\u73ED\u6B21".concat(shiftId.substr(-4));
                  }
                  _this13.$set(_this13.shiftInfo, shiftId, shiftData);
                  // 强制更新视图
                  _this13.$forceUpdate();
                } else {
                  console.error('班次信息加载失败:', res);
                  // 设置一个带有错误标记的对象，避免重复请求
                  _this13.$set(_this13.shiftInfo, shiftId, {
                    error: true,
                    errorMessage: res.message || '班次不存在',
                    // 添加一个默认名称
                    name: "\u73ED\u6B21".concat(shiftId.substr(-4))
                  });
                }
                _context10.next = 15;
                break;
              case 11:
                _context10.prev = 11;
                _context10.t0 = _context10["catch"](4);
                console.error('加载班次信息出错:', _context10.t0);
                // 设置一个带有错误标记的对象，避免重复请求
                _this13.$set(_this13.shiftInfo, shiftId, {
                  error: true,
                  errorMessage: _context10.t0.message || '加载失败',
                  // 添加一个默认名称
                  name: "\u73ED\u6B21".concat(shiftId.substr(-4))
                });
              case 15:
                _context10.prev = 15;
                // 无论成功或失败，都强制更新一次视图
                _this13.$forceUpdate();
                return _context10.finish(15);
              case 18:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10, null, [[4, 11, 15, 18]]);
      }))();
    },
    // 获取状态文本
    getStatusText: function getStatusText(status) {
      status = parseInt(status);
      var statusMap = {
        0: '未开始',
        1: '进行中',
        2: '已完成',
        3: '已超时',
        4: '已取消'
      };
      return statusMap[status] || '未知状态';
    },
    // 获取点位数量
    getPointCount: function getPointCount(task) {
      return task.total_points ? task.total_points.length : 0;
    },
    // 搜索任务
    searchTasks: function searchTasks() {
      // 在新的架构下，搜索通过filteredTasks计算属性实现
      // 如果需要服务端搜索，可以重新加载当前月份数据
      this.refreshList();
    },
    // 状态变更
    onStatusChange: function onStatusChange(e) {
      this.filterStatus = this.filterOptions[e.detail.value].value;
      // 状态过滤通过filteredTasks计算属性实现，无需重新加载数据
    },
    // 下拉刷新
    refresh: function refresh() {
      var _this14 = this;
      this.isRefreshing = true;

      // 清空当前显示的任务
      this.currentDisplayTasks = [];

      // 重新加载当前月份的任务
      var currentYearMonth = this.getYearMonth(this.selectedDate);
      this.$delete(this.loadedMonths, currentYearMonth); // 清除缓存标记，强制重新加载

      return this.loadMonthTasks(currentYearMonth).then(function () {
        // 更新显示的任务
        _this14.updateCurrentDisplayTasks(true); // 刷新数据时需要重新加载班次信息

        // 提取任务日期并更新日历
        _this14.extractTaskDates();
      }).catch(function (error) {
        console.error('refresh加载任务出错:', error);
      }).finally(function () {
        // 确保重置刷新状态
        _this14.isRefreshing = false;
      });
    },
    // 加载更多 (按月加载模式下不需要分页加载更多)
    loadMore: function loadMore() {
      // 在新的按月加载模式下，不需要分页加载更多
      // 所有月份数据都是一次性加载完成的
    },
    // 查看任务详情
    viewTaskDetail: function viewTaskDetail(task) {
      uni.navigateTo({
        url: "/pages/patrol_pkg/task/detail?id=".concat(task._id)
      });
    },
    // 编辑任务
    editTask: function editTask(task) {
      uni.navigateTo({
        url: "/pages/patrol_pkg/task/edit?id=".concat(task._id)
      });
    },
    // 确认删除
    confirmDelete: function confirmDelete(task) {
      var _this15 = this;
      // 构建详细的确认信息
      var confirmContent = "\u786E\u5B9A\u8981\u5220\u9664\u4EE5\u4E0B\u4EFB\u52A1\u5417\uFF1F\n" + "\u4EFB\u52A1\u540D\u79F0\uFF1A".concat(task.name, "\n") + "\u6267\u884C\u4EBA\u5458\uFF1A".concat(task.user_name || '未分配', "\n") + "\u6267\u884C\u65E5\u671F\uFF1A".concat(task.patrol_date || this.formatDateForDisplay(task.create_date), "\n") + "\u4EFB\u52A1\u72B6\u6001\uFF1A".concat(this.getStatusText(task.status));
      uni.showModal({
        title: '删除确认',
        content: confirmContent,
        confirmText: '删除',
        confirmColor: '#DC3545',
        success: function success(res) {
          if (res.confirm) {
            _this15.deleteTask(task._id);
          }
        }
      });
    },
    // 删除任务
    deleteTask: function deleteTask(taskId) {
      var _this16 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        var res;
        return _regenerator.default.wrap(function _callee11$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                _this16.loading = true;
                _context11.prev = 1;
                _context11.next = 4;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'deleteTask',
                  data: {
                    params: {
                      id: taskId
                    }
                  }
                });
              case 4:
                res = _context11.sent;
                if (res.code === 0) {
                  uni.showToast({
                    title: '删除成功',
                    icon: 'success'
                  });

                  // 从所有缓存中移除任务
                  Object.keys(_this16.cachedTasks).forEach(function (yearMonth) {
                    _this16.cachedTasks[yearMonth] = _this16.cachedTasks[yearMonth].filter(function (task) {
                      return task._id !== taskId;
                    });
                  });

                  // 从当前显示的任务中移除
                  _this16.currentDisplayTasks = _this16.currentDisplayTasks.filter(function (task) {
                    return task._id !== taskId;
                  });

                  // 更新日历指示器
                  _this16.extractTaskDates();
                } else {
                  uni.showToast({
                    title: res.message || '删除失败',
                    icon: 'none'
                  });
                }
                _context11.next = 12;
                break;
              case 8:
                _context11.prev = 8;
                _context11.t0 = _context11["catch"](1);
                console.error('删除任务错误', _context11.t0);
                uni.showToast({
                  title: '删除任务出错',
                  icon: 'none'
                });
              case 12:
                _context11.prev = 12;
                _this16.loading = false;
                return _context11.finish(12);
              case 15:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee11, null, [[1, 8, 12, 15]]);
      }))();
    },
    // 跳转到添加页面
    navigateToAdd: function navigateToAdd() {
      uni.navigateTo({
        url: '/pages/patrol_pkg/task/add'
      });
    },
    // 跳转到批量添加页面
    navigateToBatchAdd: function navigateToBatchAdd() {
      uni.navigateTo({
        url: '/pages/patrol_pkg/task/batch-add'
      });
    },
    // 显示高级筛选弹窗
    showAdvancedFilter: function showAdvancedFilter() {
      this.advancedFilterVisible = true;
      this.$refs.advancedFilterPopup.open();
    },
    // 隐藏高级筛选弹窗
    hideAdvancedFilter: function hideAdvancedFilter() {
      this.$refs.advancedFilterPopup.close();
    },
    // 应用高级筛选
    applyAdvancedFilters: function applyAdvancedFilters() {
      this.refreshList();
    },
    // 重置高级筛选
    resetFilters: function resetFilters() {
      this.startDate = '';
      this.endDate = '';
      this.filterUser = null;
      this.filterRoute = null;
      this.filterArea = '';
      this.sortBy = 'create_time';
      this.sortDirection = 'desc';
      this.refreshList();
    },
    // 显示用户选择弹窗
    showUserPicker: function showUserPicker() {
      this.$refs.userPickerPopup.open();
    },
    // 隐藏用户选择弹窗
    hideUserPicker: function hideUserPicker() {
      this.$refs.userPickerPopup.close();
    },
    // 选择用户
    selectUser: function selectUser(user) {
      this.filterUser = {
        id: user._id,
        name: user.name
      };
      this.hideUserPicker();
      this.refreshList();
    },
    // 显示线路选择弹窗
    showRoutePicker: function showRoutePicker() {
      this.$refs.routePickerPopup.open();
    },
    // 隐藏线路选择弹窗
    hideRoutePicker: function hideRoutePicker() {
      this.$refs.routePickerPopup.close();
    },
    // 选择线路
    selectRoute: function selectRoute(route) {
      this.filterRoute = {
        id: route._id,
        name: route.name
      };
      this.hideRoutePicker();
      this.refreshList();
    },
    // 清除日期筛选
    clearDateFilter: function clearDateFilter() {
      this.filterDate = '';
      this.refreshList();
    },
    // 清除状态筛选
    clearStatusFilter: function clearStatusFilter() {
      this.statusIndex = -1;
      // 状态过滤通过filteredTasks计算属性实现，无需重新加载数据
    },
    // 清除用户筛选
    clearUserFilter: function clearUserFilter() {
      this.filterUser = null;
      this.refreshList();
    },
    // 清除线路筛选
    clearRouteFilter: function clearRouteFilter() {
      this.filterRoute = null;
      this.refreshList();
    },
    // 清除区域筛选
    clearAreaFilter: function clearAreaFilter() {
      this.filterArea = '';
      this.refreshList();
    },
    // 清除所有筛选
    clearAllFilters: function clearAllFilters() {
      this.filterDate = '';
      this.statusIndex = -1;
      this.filterUser = null;
      this.filterRoute = null;
      this.filterArea = '';
      this.startDate = '';
      this.endDate = '';
      this.sortBy = 'create_time';
      this.sortDirection = 'desc';
      this.refreshList();
    },
    // 设置排序方式
    setSortBy: function setSortBy(by) {
      this.sortBy = by;
      this.refreshList();
    },
    // 添加筛选方法
    setFilter: function setFilter(status) {
      this.filterStatus = status;
      // 状态过滤通过filteredTasks计算属性实现，无需重新加载数据
    },
    // 处理弹窗变化
    onPopupChange: function onPopupChange(e) {
      if (e.type === 'open') {
        // 弹窗打开时的处理逻辑
      } else if (e.type === 'close') {
        // 弹窗关闭时的处理逻辑
      }
    },
    // 加载用户列表
    loadUsers: function loadUsers() {
      var _this17 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12() {
        var cachedUsers, userInfo, userId, result, users;
        return _regenerator.default.wrap(function _callee12$(_context12) {
          while (1) {
            switch (_context12.prev = _context12.next) {
              case 0:
                _context12.prev = 0;
                // 先尝试从缓存加载
                cachedUsers = _this17.getCache(_this17.cacheKeys.users);
                if (!cachedUsers) {
                  _context12.next = 6;
                  break;
                }
                _this17.userMap = cachedUsers.userMap || {};
                _this17.users = cachedUsers.users || [];
                return _context12.abrupt("return", cachedUsers.users);
              case 6:
                userInfo = uni.getStorageSync('uni-id-pages-userInfo');
                userId = userInfo ? typeof userInfo === 'string' ? JSON.parse(userInfo)._id : userInfo._id : '';
                if (userId) {
                  _context12.next = 10;
                  break;
                }
                return _context12.abrupt("return", []);
              case 10:
                _context12.next = 12;
                return _patrolApi.default.call({
                  name: 'patrol-user',
                  action: 'getUsers',
                  data: {
                    userid: userId,
                    pageSize: 100
                  }
                });
              case 12:
                result = _context12.sent;
                if (!(result.code === 0)) {
                  _context12.next = 22;
                  break;
                }
                users = result.data.list || []; // 清空之前的用户映射
                _this17.userMap = {};
                users.forEach(function (user) {
                  // 确保用户对象有name属性
                  var processedUser = _objectSpread(_objectSpread({}, user), {}, {
                    // 按优先级选择用户显示名称
                    name: user.real_name || user.nickname || user.username || '未命名用户'
                  });
                  _this17.userMap[user._id] = processedUser;
                });
                _this17.users = users;

                // 缓存用户数据
                _this17.setCache(_this17.cacheKeys.users, {
                  userMap: _this17.userMap,
                  users: users
                });
                return _context12.abrupt("return", users);
              case 22:
                console.error('获取用户列表失败:', result);
                return _context12.abrupt("return", []);
              case 24:
                _context12.next = 30;
                break;
              case 26:
                _context12.prev = 26;
                _context12.t0 = _context12["catch"](0);
                console.error('加载用户列表出错:', _context12.t0);
                return _context12.abrupt("return", []);
              case 30:
              case "end":
                return _context12.stop();
            }
          }
        }, _callee12, null, [[0, 26]]);
      }))();
    },
    // 加载线路列表
    loadRoutes: function loadRoutes() {
      var _this18 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee13() {
        var cachedRoutes, result, routes;
        return _regenerator.default.wrap(function _callee13$(_context13) {
          while (1) {
            switch (_context13.prev = _context13.next) {
              case 0:
                _context13.prev = 0;
                // 先尝试从缓存加载
                cachedRoutes = _this18.getCache(_this18.cacheKeys.routes);
                if (!cachedRoutes) {
                  _context13.next = 6;
                  break;
                }
                _this18.routeMap = cachedRoutes.routeMap || {};
                _this18.routes = cachedRoutes.routes || [];
                return _context13.abrupt("return", cachedRoutes.routes);
              case 6:
                _context13.next = 8;
                return _patrolApi.default.call({
                  name: 'patrol-route',
                  action: 'getRouteList'
                });
              case 8:
                result = _context13.sent;
                if (!(result.code === 0)) {
                  _context13.next = 18;
                  break;
                }
                routes = result.data.list || []; // 清空之前的线路映射
                _this18.routeMap = {};
                routes.forEach(function (route) {
                  _this18.routeMap[route._id] = route;
                });
                _this18.routes = routes;

                // 缓存线路数据
                _this18.setCache(_this18.cacheKeys.routes, {
                  routeMap: _this18.routeMap,
                  routes: routes
                });
                return _context13.abrupt("return", routes);
              case 18:
                console.error('获取线路列表失败:', result);
                return _context13.abrupt("return", []);
              case 20:
                _context13.next = 26;
                break;
              case 22:
                _context13.prev = 22;
                _context13.t0 = _context13["catch"](0);
                console.error('加载线路列表出错:', _context13.t0);
                return _context13.abrupt("return", []);
              case 26:
              case "end":
                return _context13.stop();
            }
          }
        }, _callee13, null, [[0, 22]]);
      }))();
    },
    // 简化或删除用户数据映射方法
    buildUserMap: function buildUserMap(users) {
      var _this19 = this;
      if (!users || !Array.isArray(users)) return;
      users.forEach(function (user) {
        _this19.userMap[user._id] = user;
      });
    },
    // 获取日期对象的本地日期字符串，不受时区影响
    getLocalDateString: function getLocalDateString(date) {
      if (!date) return '';
      try {
        // 确保date是Date对象
        if (!(date instanceof Date)) {
          var parsedDate = new Date(date);
          if (isNaN(parsedDate.getTime())) {
            return '';
          }
          date = parsedDate;
        }
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var day = date.getDate();
        return "".concat(year, "-").concat(month < 10 ? '0' + month : month, "-").concat(day < 10 ? '0' + day : day);
      } catch (e) {
        console.error('获取本地日期字符串错误:', e);
        return '';
      }
    },
    // 格式化时间为00:00格式，确保在所有平台上显示一致
    formatTimeConsistent: function formatTimeConsistent(time) {
      if (!time) return '--:--';
      try {
        // 如果是日期对象
        if (time instanceof Date) {
          if (isNaN(time.getTime())) return '--:--';
          var _hours = time.getHours().toString().padStart(2, '0');
          var _minutes = time.getMinutes().toString().padStart(2, '0');
          return "".concat(_hours, ":").concat(_minutes);
        }

        // 如果是时间戳或日期字符串
        var date = new Date(time);
        if (isNaN(date.getTime())) {
          // 尝试解析特定格式的时间字符串 HH:MM
          if (typeof time === 'string' && time.match(/^\d{1,2}:\d{2}$/)) {
            return time;
          }
          return '--:--';
        }
        var hours = date.getHours().toString().padStart(2, '0');
        var minutes = date.getMinutes().toString().padStart(2, '0');
        return "".concat(hours, ":").concat(minutes);
      } catch (e) {
        console.error('时间格式化错误:', e);
        return '--:--';
      }
    },
    // 获取启用的轮次数量
    getEnabledRoundsCount: function getEnabledRoundsCount(task) {
      var _task$enabled_rounds;
      return ((_task$enabled_rounds = task.enabled_rounds) === null || _task$enabled_rounds === void 0 ? void 0 : _task$enabled_rounds.length) || 0;
    },
    // 获取总轮次数量
    getAllRoundsCount: function getAllRoundsCount(task) {
      var _task$rounds_completi;
      return ((_task$rounds_completi = task.rounds_completion) === null || _task$rounds_completi === void 0 ? void 0 : _task$rounds_completi.length) || 0;
    },
    // 获取当前用户信息
    getUserInfo: function getUserInfo() {
      var _this20 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee14() {
        var userInfoStr, userRoleStr, userRole, res, uniIdUserInfo;
        return _regenerator.default.wrap(function _callee14$(_context14) {
          while (1) {
            switch (_context14.prev = _context14.next) {
              case 0:
                _context14.prev = 0;
                userInfoStr = uni.getStorageSync('uni-id-pages-userInfo');
                if (!userInfoStr) {
                  _context14.next = 14;
                  break;
                }
                _context14.prev = 3;
                _this20.userInfo = typeof userInfoStr === 'string' ? JSON.parse(userInfoStr) : userInfoStr;

                // 从缓存获取角色信息
                userRoleStr = uni.getStorageSync((0, _cache.getCacheKey)(_cache.CACHE_KEYS.USER_ROLE));
                if (userRoleStr) {
                  userRole = typeof userRoleStr === 'string' ? JSON.parse(userRoleStr) : userRoleStr; // 从value字段获取userRole数组
                  if (userRole && userRole.value && userRole.value.userRole) {
                    // 将角色信息添加到用户对象
                    _this20.userInfo.role = userRole.value.userRole;
                  }
                }
                return _context14.abrupt("return");
              case 10:
                _context14.prev = 10;
                _context14.t0 = _context14["catch"](3);
                console.error('解析本地存储的用户信息失败:', _context14.t0);
                uni.removeStorageSync('uni-id-pages-userInfo');
              case 14:
                _context14.next = 16;
                return _patrolApi.default.call({
                  name: 'patrol-user',
                  action: 'getCurrentUser',
                  data: {
                    params: {}
                  }
                });
              case 16:
                res = _context14.sent;
                if (res.code === 0 && res.data) {
                  _this20.userInfo = res.data;
                  uni.setStorageSync('uni-id-pages-userInfo', JSON.stringify(res.data));
                } else {
                  uniIdUserInfo = uni.getStorageSync('uni-id-pages-userInfo');
                  if (uniIdUserInfo) {
                    try {
                      _this20.userInfo = typeof uniIdUserInfo === 'string' ? JSON.parse(uniIdUserInfo) : uniIdUserInfo;
                    } catch (e) {
                      console.error('解析uni-id-pages用户信息失败:', e);
                    }
                  }
                }
                if (!_this20.userInfo) {
                  uni.showToast({
                    title: '获取用户信息失败',
                    icon: 'none'
                  });
                }
                _context14.next = 25;
                break;
              case 21:
                _context14.prev = 21;
                _context14.t1 = _context14["catch"](0);
                console.error('获取用户信息失败:', _context14.t1);
                uni.showToast({
                  title: '获取用户信息失败',
                  icon: 'none'
                });
              case 25:
              case "end":
                return _context14.stop();
            }
          }
        }, _callee14, null, [[0, 21], [3, 10]]);
      }))();
    },
    // 刷新数据
    refreshData: function refreshData() {
      var _this21 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee15() {
        var currentYearMonth;
        return _regenerator.default.wrap(function _callee15$(_context15) {
          while (1) {
            switch (_context15.prev = _context15.next) {
              case 0:
                if (_this21.loading) {
                  _context15.next = 27;
                  break;
                }
                _this21.isRefreshing = true;
                _this21.currentDisplayTasks = [];
                // 🔥 重置加载状态，显示骨架屏
                _this21.initialDataLoaded = false;
                _context15.prev = 4;
                _context15.next = 7;
                return _this21.loadUsers();
              case 7:
                // 重新加载当前月份的任务
                currentYearMonth = _this21.getYearMonth(_this21.selectedDate);
                _this21.$delete(_this21.loadedMonths, currentYearMonth); // 清除缓存标记，强制重新加载
                _context15.next = 11;
                return _this21.loadMonthTasks(currentYearMonth);
              case 11:
                // 更新显示的任务
                _this21.updateCurrentDisplayTasks(true); // 刷新数据时需要重新加载班次信息
                _this21.extractTaskDates();

                // 添加最小延迟，确保用户能看到刷新效果
                _context15.next = 15;
                return new Promise(function (resolve) {
                  return setTimeout(resolve, 200);
                });
              case 15:
                _context15.next = 20;
                break;
              case 17:
                _context15.prev = 17;
                _context15.t0 = _context15["catch"](4);
                console.error('刷新数据失败:', _context15.t0);
              case 20:
                _context15.prev = 20;
                // 🔥 标记加载完成
                _this21.initialDataLoaded = true;
                _this21.isRefreshing = false;
                uni.stopPullDownRefresh();
                return _context15.finish(20);
              case 25:
                _context15.next = 28;
                break;
              case 27:
                uni.stopPullDownRefresh();
              case 28:
              case "end":
                return _context15.stop();
            }
          }
        }, _callee15, null, [[4, 17, 20, 25]]);
      }))();
    },
    // 🔥 优化：获取已完成点位数量 - 适配轻量级数据结构
    getCompletedCount: function getCompletedCount(task) {
      // 🔥 优先从overall_stats获取（服务端已计算好）
      if (task.overall_stats && typeof task.overall_stats.completed_points === 'number') {
        return task.overall_stats.completed_points;
      }

      // 🔥 其次从rounds_detail的stats累加
      if (task.rounds_detail && task.rounds_detail.length > 0) {
        var completedPoints = 0;
        var hasStats = false;
        task.rounds_detail.forEach(function (round) {
          if (round.stats && typeof round.stats.completed_points === 'number') {
            completedPoints += round.stats.completed_points;
            hasStats = true;
          }
        });
        if (hasStats) {
          return completedPoints;
        }
      }

      // 兼容旧数据结构 - completed_points数组
      if (task.completed_points && Array.isArray(task.completed_points)) {
        return task.completed_points.length;
      }
      return 0;
    },
    // 🔥 优化：获取总点位数量 - 适配轻量级数据结构
    getTotalPointsCount: function getTotalPointsCount(task) {
      // 🔥 优先从overall_stats获取（服务端已计算好）
      if (task.overall_stats && typeof task.overall_stats.total_points === 'number') {
        return task.overall_stats.total_points;
      }

      // 🔥 其次从rounds_detail的stats获取（轻量级统计数据）
      if (task.rounds_detail && task.rounds_detail.length > 0) {
        // 尝试从第一个轮次的stats获取
        var firstRound = task.rounds_detail[0];
        if (firstRound && firstRound.stats && typeof firstRound.stats.total_points === 'number') {
          return firstRound.stats.total_points;
        }

        // 如果是多轮次任务，累加各轮次点位数
        var totalPoints = 0;
        var hasStats = false;
        task.rounds_detail.forEach(function (round) {
          if (round.stats && typeof round.stats.total_points === 'number') {
            totalPoints += round.stats.total_points;
            hasStats = true;
          }
        });
        if (hasStats && totalPoints > 0) {
          return totalPoints;
        }
      }

      // 兼容旧数据结构 - 直接的total_points字段
      if (typeof task.total_points === 'number') {
        return task.total_points;
      }

      // 兼容旧数据结构 - points数组（已不推荐使用）
      if (task.points && Array.isArray(task.points)) {
        return task.points.length;
      }

      // 都不存在，返回0
      return 0;
    },
    // 获取路线区域名称
    getRouteAreaName: function getRouteAreaName(task) {
      return task.area || task.route_name || '未指定区域';
    },
    // 获取班次时间
    getShiftTime: function getShiftTime(task) {
      // 如果有班次ID且班次信息已加载
      if (task.shift_id && this.shiftInfo[task.shift_id]) {
        var shift = this.shiftInfo[task.shift_id];
        if (shift.start_time && shift.end_time) {
          return "".concat(shift.start_time, " - ").concat(shift.end_time);
        }
      }

      // 如果没有班次信息但有开始和结束时间
      if ((task.startTime || task.start_time) && (task.endTime || task.end_time)) {
        var startTime = task.startTime || task.start_time;
        var endTime = task.endTime || task.end_time;
        return "".concat(startTime, " - ").concat(endTime);
      }

      // 如果以上都没有
      return '时间未设置';
    },
    // 获取系统信息
    getSystemInfo: function getSystemInfo() {
      try {
        // 使用新的API替代
        var windowInfo = uni.getWindowInfo();
        var systemInfo = uni.getSystemInfoSync(); // 暂时保留，因为某些属性在新API中可能不存在
        return _objectSpread(_objectSpread({}, systemInfo), {}, {
          windowHeight: windowInfo.windowHeight,
          windowWidth: windowInfo.windowWidth,
          screenHeight: windowInfo.screenHeight,
          screenWidth: windowInfo.screenWidth
        });
      } catch (e) {
        console.error('获取系统信息失败:', e);
        return {};
      }
    },
    // 获取用户名
    getUserName: function getUserName(task) {
      if (task.user_id && this.userMap[task.user_id]) {
        return this.userMap[task.user_id].name || '未知用户';
      } else if (task.user && task.user.name) {
        return task.user.name;
      } else if (task.username) {
        return task.username;
      }
      return '未分配';
    },
    // 获取轮次状态文本
    getRoundStatusText: function getRoundStatusText(round) {
      // 如果轮次已超时
      if (round.expired) {
        // 如果有部分完成
        if (round.completion_rate > 0) {
          return "\u5DF2\u8D85\u65F6(".concat(round.completion_rate, "%)");
        }
        return '已超时';
      }

      // 如果轮次已完全完成
      if (round.completed || round.completion_rate === 100) {
        return '已完成';
      }

      // 如果轮次部分完成
      if (round.completion_rate > 0) {
        return "\u8FDB\u884C\u4E2D(".concat(round.completion_rate, "%)");
      }
      return '进行中';
    },
    // 根据状态码获取轮次状态文本
    getRoundStatusTextByCode: function getRoundStatusTextByCode(status) {
      status = parseInt(status) || 0;
      var statusMap = {
        0: '未开始',
        1: '进行中',
        2: '已完成',
        3: '已超时',
        4: '已取消'
      };
      return statusMap[status] || '未知状态';
    },
    // 显示加载中
    showLoading: function showLoading(show) {
      this.loading = show;
    },
    getStatusClass: function getStatusClass(status) {
      var statusClassMap = {
        0: 'status-pending',
        // 未开始
        1: 'status-active',
        // 进行中
        2: 'status-completed',
        // 已完成 
        3: 'status-expired',
        // 已超时
        4: 'status-cancelled' // 已取消
      };

      return statusClassMap[status] || 'status-pending';
    },
    // 获取轮次信息，带有次日标记
    getFormattedRoundsInfo: function getFormattedRoundsInfo(task) {
      if (!task || !task.rounds_detail || !task.rounds_detail.length) {
        return '无轮次';
      }

      // 获取轮次数
      var roundCount = task.rounds_detail.length;

      // 检查是否有第二天轮次
      var hasNextDayRounds = task.rounds_detail.some(function (round) {
        return round.day_offset && round.day_offset > 0;
      });

      // 检查是否有第三天及以后轮次
      var hasLaterDayRounds = task.rounds_detail.some(function (round) {
        return round.day_offset && round.day_offset >= 2;
      });

      // 返回格式化后的轮次信息
      if (hasLaterDayRounds) {
        return "".concat(roundCount, "\u8F6E (\u542B\u591A\u5929\u8F6E\u6B21)");
      } else if (hasNextDayRounds) {
        return "".concat(roundCount, "\u8F6E (\u542B\u6B21\u65E5\u8F6E\u6B21)");
      } else {
        return "".concat(roundCount, "\u8F6E");
      }
    },
    // 格式化日期显示
    formatDateForDisplay: function formatDateForDisplay(dateStr) {
      if (!dateStr) return '';
      try {
        return (0, _date3.formatDate)(new Date(dateStr), 'YYYY-MM-DD');
      } catch (e) {
        console.error('日期格式化错误:', e);
        return '';
      }
    },
    // 格式化时间范围
    formatTimeRange: function formatTimeRange(round) {
      if (!round || !round.start_time) return '--:--';
      try {
        // 统一处理时间格式
        var startTime = round.start_time.includes('T') ? new Date(round.start_time) : new Date(round.start_time.replace(/-/g, '/'));
        if (isNaN(startTime.getTime())) {
          console.error('Invalid start time:', round.start_time);
          return '--:--';
        }
        var hours = startTime.getHours().toString().padStart(2, '0');
        var minutes = startTime.getMinutes().toString().padStart(2, '0');
        return "".concat(hours, ":").concat(minutes);
      } catch (e) {
        console.error('格式化时间出错:', e);
        return '--:--';
      }
    },
    // 计算轮次剩余时间
    calculateRemainingTime: function calculateRemainingTime(round) {
      if (!round || !round.end_time) return '';
      try {
        var now = new Date();
        var endTime = new Date(round.end_time);

        // 如果已经超过结束时间，返回空字符串
        if (now >= endTime) {
          return '';
        }
        var diffMs = endTime.getTime() - now.getTime();
        var diffMins = Math.floor(diffMs / (1000 * 60));
        var hours = Math.floor(diffMins / 60);
        var minutes = diffMins % 60;
        if (hours > 0) {
          return "".concat(hours, "\u5C0F\u65F6").concat(minutes, "\u5206\u949F");
        } else if (minutes > 0) {
          return "".concat(minutes, "\u5206\u949F");
        } else {
          return '即将结束';
        }
      } catch (e) {
        console.error('计算剩余时间出错:', e);
        return '';
      }
    },
    // 🔥 新增：计算任务开始前的倒计时
    calculateStartCountdown: function calculateStartCountdown(round) {
      if (!round || !round.start_time) return '';
      try {
        var now = new Date();
        var startTime = new Date(round.start_time);

        // 如果已经开始或已过期，返回空字符串
        if (now >= startTime) {
          return '';
        }
        var diffMs = startTime.getTime() - now.getTime();
        var diffMins = Math.floor(diffMs / (1000 * 60));
        var hours = Math.floor(diffMins / 60);
        var minutes = diffMins % 60;
        var days = Math.floor(hours / 24);
        var remainingHours = hours % 24;

        // 根据时间长短选择合适的显示格式
        if (days > 0) {
          if (remainingHours > 0) {
            return "".concat(days, "\u5929").concat(remainingHours, "\u5C0F\u65F6\u540E\u5F00\u59CB");
          } else {
            return "".concat(days, "\u5929\u540E\u5F00\u59CB");
          }
        } else if (hours > 0) {
          if (minutes > 0) {
            return "".concat(hours, "\u5C0F\u65F6").concat(minutes, "\u5206\u949F\u540E\u5F00\u59CB");
          } else {
            return "".concat(hours, "\u5C0F\u65F6\u540E\u5F00\u59CB");
          }
        } else if (minutes > 0) {
          return "".concat(minutes, "\u5206\u949F\u540E\u5F00\u59CB");
        } else {
          return '即将开始';
        }
      } catch (e) {
        console.error('计算开始倒计时出错:', e);
        return '';
      }
    }
  }, (0, _defineProperty2.default)(_methods, "isSameDay", function isSameDay(date1, date2) {
    if (!date1 || !date2) return false;
    try {
      // 转换为Date对象
      var d1 = date1 instanceof Date ? date1 : new Date(date1);
      var d2 = date2 instanceof Date ? date2 : new Date(date2);
      return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth() && d1.getDate() === d2.getDate();
    } catch (e) {
      console.error('日期比较错误:', e);
      return false;
    }
  }), (0, _defineProperty2.default)(_methods, "startTimeUpdateTimer", function startTimeUpdateTimer() {
    var _this22 = this;
    if (this.timer) {
      clearInterval(this.timer);
    }

    // 设置定时器，每分钟更新一次倒计时显示
    this.timer = setInterval(function () {
      if (_this22.currentDisplayTasks && _this22.currentDisplayTasks.length > 0) {
        var now = new Date();

        // 🔥 修复：检查是否有任何倒计时需要更新
        var hasCountdown = _this22.currentDisplayTasks.some(function (task) {
          return task.rounds_detail && task.rounds_detail.some(function (round) {
            // 检查未开始的轮次（有开始倒计时）
            if (round.status === 0 && round.start_time) {
              var startTime = new Date(round.start_time);
              return now < startTime; // 还没开始，需要显示倒计时
            }

            // 检查进行中的轮次（有剩余时间）
            if (round.status === 1 && round.end_time) {
              var endTime = new Date(round.end_time);
              return now < endTime; // 还没结束，需要显示剩余时间
            }

            return false;
          });
        });

        // 🔥 关键修复：只要有倒计时就强制更新页面
        if (hasCountdown) {
          _this22.lastUpdateTime = now;
          _this22.$forceUpdate();
        }

        // 额外检查：即将结束或即将开始的轮次需要重新处理数据
        var needsDataUpdate = _this22.currentDisplayTasks.some(function (task) {
          return task.rounds_detail && task.rounds_detail.some(function (round) {
            var startTime = new Date(round.start_time);
            var endTime = new Date(round.end_time);

            // 检查即将结束的轮次（5分钟内）
            var timeUntilEnd = endTime - now;
            if (timeUntilEnd > 0 && timeUntilEnd <= 300000) {
              return true;
            }

            // 检查即将开始的轮次（5分钟内）
            var timeUntilStart = startTime - now;
            if (timeUntilStart > 0 && timeUntilStart <= 300000) {
              return true;
            }
            return false;
          });
        });
        if (needsDataUpdate) {
          // 重新处理当前显示的任务
          _this22.currentDisplayTasks = _this22.processTasks(_this22.currentDisplayTasks);
          // 同时更新缓存中的数据
          var currentYearMonth = _this22.getYearMonth(_this22.selectedDate);
          if (_this22.cachedTasks[currentYearMonth]) {
            _this22.cachedTasks[currentYearMonth] = _this22.processTasks(_this22.cachedTasks[currentYearMonth]);
          }
        }
      }
    }, 60000); // 每60秒更新一次
  }), (0, _defineProperty2.default)(_methods, "preloadAdjacentMonths", function preloadAdjacentMonths() {
    var _this23 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee16() {
      var currentDate, prevMonth, nextMonth, prevYearMonth, nextYearMonth, loadPromises;
      return _regenerator.default.wrap(function _callee16$(_context16) {
        while (1) {
          switch (_context16.prev = _context16.next) {
            case 0:
              _context16.prev = 0;
              currentDate = new Date();
              prevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
              nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
              prevYearMonth = _this23.getYearMonth(prevMonth);
              nextYearMonth = _this23.getYearMonth(nextMonth); // 并行加载前后月份，提升加载效率
              loadPromises = [];
              if (!_this23.loadedMonths[prevYearMonth]) {
                loadPromises.push(_this23.loadMonthTasks(prevYearMonth).catch(function (e) {
                  console.error('预加载上个月失败:', e);
                }));
              }
              if (!_this23.loadedMonths[nextYearMonth]) {
                loadPromises.push(_this23.loadMonthTasks(nextYearMonth).catch(function (e) {
                  console.error('预加载下个月失败:', e);
                }));
              }

              // 等待所有预加载完成
              _context16.next = 11;
              return Promise.all(loadPromises);
            case 11:
              _context16.next = 16;
              break;
            case 13:
              _context16.prev = 13;
              _context16.t0 = _context16["catch"](0);
              console.error('预加载相邻月份失败:', _context16.t0);
              // 预加载失败不影响主要功能
            case 16:
            case "end":
              return _context16.stop();
          }
        }
      }, _callee16, null, [[0, 13]]);
    }))();
  }), _methods)
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 409:
/*!*********************************************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/index.vue?vue&type=style&index=0&id=3bf106d6&lang=scss&scoped=true& ***!
  \*********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_3bf106d6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=3bf106d6&lang=scss&scoped=true& */ 410);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_3bf106d6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_3bf106d6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_3bf106d6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_3bf106d6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_3bf106d6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 410:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/index.vue?vue&type=style&index=0&id=3bf106d6&lang=scss&scoped=true& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[403,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/patrol_pkg/task/index.js.map
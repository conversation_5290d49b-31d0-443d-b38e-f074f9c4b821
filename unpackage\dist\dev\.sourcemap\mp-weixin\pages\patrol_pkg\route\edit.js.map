{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/route/edit.vue?c14a", "webpack:///D:/Xwzc/pages/patrol_pkg/route/edit.vue?3fb4", "webpack:///D:/Xwzc/pages/patrol_pkg/route/edit.vue?8710", "webpack:///D:/Xwzc/pages/patrol_pkg/route/edit.vue?52f2", "uni-app:///pages/patrol_pkg/route/edit.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/route/edit.vue?fbfd", "webpack:///D:/Xwzc/pages/patrol_pkg/route/edit.vue?d924"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "route_id", "formData", "name", "remarks", "status", "points", "rules", "required", "errorMessage", "mapCenter", "latitude", "longitude", "mapScale", "markers", "polyline", "availablePoints", "selectedPointIds", "pointKeyword", "tempSelectedPointIds", "sortOrder", "computed", "isAllSelected", "onLoad", "uni", "title", "icon", "setTimeout", "methods", "getRouteDetail", "PatrolApi", "res", "routeData", "point_id", "address", "order", "console", "loadAvailablePoints", "params", "keyword", "page", "pageSize", "openPointSelector", "closePointSelector", "searchPoints", "onPointKeywordInput", "toggleSelectAll", "handlePointsSelection", "isPointSelected", "confirmPointSelection", "newPoints", "selectedPoints", "removePoint", "movePointUp", "movePointDown", "calculateDistance", "Math", "deg2rad", "createDistanceMarkers", "distanceMarkers", "id", "iconPath", "width", "height", "anchor", "x", "y", "label", "content", "color", "bgColor", "fontSize", "borderRadius", "borderWidth", "borderColor", "padding", "textAlign", "anchorX", "anchorY", "updateMapData", "callout", "display", "dottedLine", "arrowLine", "sumLat", "sumLng", "validPoints", "handleMapZoomIn", "handleMapZoomOut", "handleMapReset", "validateForm", "reject", "resolve", "handleSubmit", "duration", "pointsRes", "existingPointIds", "invalidPoints", "invalidNames", "showCancel", "confirmText", "submitData", "timeout", "submitPromise", "timeoutPromise", "Promise", "catch", "code", "message", "getApp", "errorMsg", "handleCancel", "clearPointSearch", "toggleSort", "sortedPoints"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,8RAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7GA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsNlnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAJ;UACAI,QACA;YACAC;YACAC;UACA;QAEA;MACA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAH;MACA;IACA;;IAEA;IACA;EACA;EACAI;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAL;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGAK;kBACA7B;gBACA;cAAA;gBAFA8B;gBAIA;kBACAC,sBAEA;kBACA;oBACA7B;oBACAC;oBACAC;oBACAC;kBACA;;kBAEA;kBACA;oBACA;sBAAA;sBAAA;wBACA2B;wBACA9B;wBACA+B;wBACAvB;wBACAC;wBACAuB;sBACA;oBAAA;;oBAEA;oBACA;sBAAA;oBAAA;;oBAEA;oBACA;kBACA;gBACA;kBACAX;oBACAC;oBACAC;kBACA;kBACAC;oBACAH;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAY;gBACAZ;kBACAC;kBACAC;gBACA;gBACAC;kBACAH;gBACA;cAAA;gBAAA;gBAEAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAb;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGAK;kBACAQ;oBACAjC;oBACAkC;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBAPAV;gBASA;kBACA;kBACA;oBAAA,OACA;kBAAA,EACA;gBACA;kBACAP;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAU;gBACAZ;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEAF;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkB;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QAAA,OACA;MAAA,EACA;;MAEA;MACA;;MAEA;MACA;QACA;UACAC;QACA;MACA;;MAEA;MACAC;QACA;UAAA;QAAA;QACA;UAAA;UACAD;YACAjB;YACA9B;YACA+B;YACAvB;YACAC;YACAuB;UACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAiB;MACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA,QACAC,0CACAA,8DACAA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;MAEA;QACA;QACA;QAEA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;QAEA;QACAC;UACAC;UAAA;UACAjD;UACAC;UACAiD;UAAA;UACAC;UACAC;UACAC;YACAC;YACAC;UACA;UACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QAEA;UACAnB;UACAjD;UACAC;UACAa;UACAuD;YACAZ;YACAC;YACAE;YACAC;YACAF;YACAK;YACAM;UACA;UACApB;UACAC;UACAC;UACAC;YACAC;YACAC;UACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;UACA5D;YAAA;cACAK;cACAC;YACA;UAAA;UACAyD;UACAP;UACAoB;UACAC;UACAT;UACAD;QACA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;QAEA;UACA;UACA;UAEA;YACAW;YACAC;YACAC;UACA;QACA;QAEA;UACA;YACA3E;YACAC;UACA;;UAEA;UACA;QACA;UACA;UACA;YACAD;YACAC;UACA;QACA;MACA;IACA;IAEA;IACA2E;MACA;QACA;MACA;IACA;IAEAC;MACA;QACA;MACA;IACA;IAEAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;UACA;UACA;YACA;YACA;cACAlE;gBACAC;gBACAC;cACA;cACAiE;cACA;YACA;YAEAC;UACA;YACA;YACAD;UACA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACArE;kBACAC;kBACAC;kBACAoE;gBACA;gBAAA;cAAA;gBAIAtE;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAGAK;kBACAQ;oBACAjC;oBACAmC;oBACAC;kBACA;gBACA;cAAA;gBANAsD;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACAC;kBAAA;gBAAA;gBACAC;kBAAA;gBAAA,IAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAC;kBAAA;gBAAA;gBACA1E;gBACAA;kBACAC;kBACA2C;kBACA+B;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAKAhE;gBACA;cAAA;gBAGA;gBACAiE;kBACApG;kBACAE;kBAAA;kBACAC;kBACAC;kBACAC;oBAAA;sBACA2B;sBACAE;oBACA;kBAAA;gBACA,GAEA;gBACAmE;gBACAC,iFAEA;gBACAC;kBACA7E;oBAAA;kBAAA;gBACA,IAEA;gBAAA;gBAAA,OACA8E,8CACAC;kBACAtE;kBACA;oBAAAuE;oBAAAC;kBAAA;gBACA;cAAA;gBAJA7E;gBAMA;kBACAP;oBACAC;oBACAC;kBACA;;kBAEA;kBACA;oBACAmF;kBACA;;kBAEA;kBACAlF;oBACAH;kBACA;gBACA;kBACAsF,oCAEA;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;kBAEAtF;oBACAC;oBACAC;oBACAoE;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA1D;gBACAZ;kBACAC;kBACAC;kBACAoE;gBACA;cAAA;gBAAA;gBAEAtE;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAuF;MACAvF;IACA;IAEA;IACAwF;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;;MAEA;MACAC;QACA;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA1F;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/2BA;AAAA;AAAA;AAAA;AAAyoC,CAAgB,+mCAAG,EAAC,C;;;;;;;;;;;ACA7pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/route/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/route/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=76de0246&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/route/edit.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=template&id=76de0246&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniSearchBar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar\" */ \"@/uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.formData.points.length || 0\n  var g1 = _vm.formData.points.length\n  var g2 = _vm.formData.points.length\n  var l0 =\n    g2 > 0\n      ? _vm.__map(_vm.formData.points, function (point, index) {\n          var $orig = _vm.__get_orig(point)\n          var g3 = _vm.formData.points.length\n          return {\n            $orig: $orig,\n            g3: g3,\n          }\n        })\n      : null\n  var g4 = _vm.formData.points.length\n  var g5 = _vm.availablePoints.length\n  var g6 = g5 > 0 ? _vm.tempSelectedPointIds.length : null\n  var g7 = g5 > 0 ? _vm.availablePoints.length : null\n  var l1 = _vm.__map(_vm.availablePoints, function (point, __i0__) {\n    var $orig = _vm.__get_orig(point)\n    var m0 = _vm.isPointSelected(point._id)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g8 = _vm.availablePoints.length\n  var g9 = _vm.tempSelectedPointIds.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function (e) {\n      return (_vm.formData.status = e.detail.value ? 1 : 0)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        l1: l1,\n        g8: g8,\n        g9: g9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"route-edit-container\">\n\t\t<!-- 头部标题区 -->\n\t\t<view class=\"header-section\">\n\t\t\t<view class=\"header-content\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<text class=\"header-title\">编辑线路</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<uni-forms ref=\"form\" :modelValue=\"formData\" :rules=\"rules\">\n\t\t\t<!-- 基本信息卡片 -->\n\t\t\t<view class=\"form-card\">\n\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t<text class=\"section-title\">基本信息</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-content\">\n\t\t\t\t\t<uni-forms-item label=\"线路名称\" name=\"name\" required>\n\t\t\t\t\t\t<uni-easyinput v-model=\"formData.name\" placeholder=\"请输入线路名称\" />\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t\t\n\t\t\t\t\t<uni-forms-item label=\"备注信息\" name=\"remarks\">\n\t\t\t\t\t\t<view class=\"textarea-container\">\n\t\t\t\t\t\t\t<textarea\n\t\t\t\t\t\t\t\tv-model=\"formData.remarks\"\n\t\t\t\t\t\t\t\tclass=\"remark-textarea\"\n\t\t\t\t\t\t\t\tplaceholder=\"请输入备注信息\"\n\t\t\t\t\t\t\t\tplaceholder-class=\"textarea-placeholder\"\n\t\t\t\t\t\t\t></textarea>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t\t\n\t\t\t\t\t<uni-forms-item label=\"线路状态\">\n\t\t\t\t\t\t<view class=\"switch-wrapper\">\n\t\t\t\t\t\t\t<switch :checked=\"formData.status === 1\" @change=\"(e) => formData.status = e.detail.value ? 1 : 0\" color=\"#1677FF\" style=\"transform: scale(0.8); vertical-align: middle;\" />\n\t\t\t\t\t\t\t<text class=\"switch-label\" :class=\"{'active-status': formData.status === 1, 'inactive-status': formData.status === 0}\">\n\t\t\t\t\t\t\t\t{{ formData.status === 1 ? '启用' : '停用' }}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 点位选择卡片 -->\n\t\t\t<view class=\"form-card\">\n\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<text class=\"section-title\">点位选择</text>\n\t\t\t\t\t\t<view class=\"count-badge\">{{ formData.points.length || 0 }} 个</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"header-actions\">\n\t\t\t\t\t\t<view class=\"sort-btn\" v-if=\"formData.points.length > 0\" :class=\"{'sort-btn-desc': sortOrder === 'desc'}\" @click=\"toggleSort\">\n\t\t\t\t\t\t\t<uni-icons type=\"sort\" size=\"16\" :color=\"sortOrder === 'desc' ? '#FFFFFF' : '#666666'\"></uni-icons>\n\t\t\t\t\t\t\t<text>{{ sortOrder === 'asc' ? '按时间正序' : '按时间倒序' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"add-point-btn\" @click=\"openPointSelector\">\n\t\t\t\t\t\t\t<uni-icons type=\"plusempty\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t<text>选择点位</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-content\">\n\t\t\t\t\t<!-- 已选点位列表 -->\n\t\t\t\t\t<view class=\"selected-points-list\" v-if=\"formData.points.length > 0\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"point-item\" \n\t\t\t\t\t\t\tv-for=\"(point, index) in formData.points\" \n\t\t\t\t\t\t\t:key=\"point.point_id\"\n\t\t\t\t\t\t\t:style=\"{ animationDelay: index * 0.05 + 's' }\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"point-header\">\n\t\t\t\t\t\t\t\t<view class=\"point-order\">\n\t\t\t\t\t\t\t\t\t<text class=\"order-number\">{{ index + 1 }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"point-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"point-name\">{{ point.name }}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"point-address\" v-if=\"point.address\">{{ point.address }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"point-actions\">\n\t\t\t\t\t\t\t\t<view class=\"action-move\">\n\t\t\t\t\t\t\t\t\t<view class=\"move-btn move-up\" v-if=\"index > 0\" @click=\"movePointUp(index)\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"arrow-up\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"move-btn move-down\" v-if=\"index < formData.points.length - 1\" @click=\"movePointDown(index)\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"arrow-down\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"action-delete\" @click=\"removePoint(index)\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"16\" color=\"#FF3B30\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 空点位提示 -->\n\t\t\t\t\t<view class=\"empty-points\" v-else>\n\t\t\t\t\t\t<uni-icons type=\"info\" size=\"32\" color=\"#8F959E\"></uni-icons>\n\t\t\t\t\t\t<text class=\"empty-tip\">暂无点位，请点击上方按钮选择</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 路线预览卡片 -->\n\t\t\t<view class=\"form-card\" v-if=\"formData.points.length > 0\">\n\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t<text class=\"section-title\">路线预览</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"map-container\">\n\t\t\t\t\t<map \n\t\t\t\t\t\tclass=\"route-map\" \n\t\t\t\t\t\t:latitude=\"mapCenter.latitude\" \n\t\t\t\t\t\t:longitude=\"mapCenter.longitude\" \n\t\t\t\t\t\t:markers=\"markers\"\n\t\t\t\t\t\t:polyline=\"polyline\"\n\t\t\t\t\t\t:scale=\"mapScale\"\n\t\t\t\t\t\tshow-location\n\t\t\t\t\t></map>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 地图控制按钮 -->\n\t\t\t\t\t<view class=\"map-controls\">\n\t\t\t\t\t\t<view class=\"map-control-btn\" @click=\"handleMapZoomIn\">\n\t\t\t\t\t\t\t<uni-icons type=\"plus\" size=\"18\" color=\"#333333\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"map-control-btn\" @click=\"handleMapZoomOut\">\n\t\t\t\t\t\t\t<uni-icons type=\"minus\" size=\"18\" color=\"#333333\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"map-control-btn\" @click=\"handleMapReset\">\n\t\t\t\t\t\t\t<uni-icons type=\"redo\" size=\"18\" color=\"#333333\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-forms>\n\t\t\n\t\t<!-- 底部操作区 -->\n\t\t<view class=\"action-area\">\n\t\t\t<view class=\"action-btns\">\n\t\t\t\t<view class=\"cancel-btn\" @click=\"handleCancel\">取消</view>\n\t\t\t\t<view class=\"submit-btn\" @click=\"handleSubmit\">保存</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 点位选择弹窗 -->\n\t\t<uni-popup ref=\"pointSelector\" type=\"bottom\">\n\t\t\t<view class=\"point-selector\">\n\t\t\t\t<view class=\"selector-header\">\n\t\t\t\t\t<text class=\"selector-title\">选择点位</text>\n\t\t\t\t\t<text class=\"selector-close\" @click=\"closePointSelector\">关闭</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"selection-tip\">\n\t\t\t\t\t<text class=\"tip-text\">请选择点位后点击\"确定选择\"按钮</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"selector-search\">\n\t\t\t\t\t<uni-search-bar \n\t\t\t\t\t\tv-model=\"pointKeyword\" \n\t\t\t\t\t\tplaceholder=\"搜索点位名称\" \n\t\t\t\t\t\t@confirm=\"searchPoints\"\n\t\t\t\t\t\t@cancel=\"clearPointSearch\"\n\t\t\t\t\t\t@clear=\"clearPointSearch\"\n\t\t\t\t\t\t@input=\"onPointKeywordInput\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"selector-content\">\n\t\t\t\t\t<!-- 全选区域 -->\n\t\t\t\t\t<view class=\"select-all-container\" v-if=\"availablePoints.length > 0\">\n\t\t\t\t\t\t<view class=\"select-all-checkbox\">\n\t\t\t\t\t\t\t<checkbox :checked=\"isAllSelected\" color=\"#1677FF\" @click=\"toggleSelectAll\" />\n\t\t\t\t\t\t\t<text class=\"select-all-text\" @click=\"toggleSelectAll\">全选</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"selected-count\">已选 {{ tempSelectedPointIds.length }}/{{ availablePoints.length }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"point-list\">\n\t\t\t\t\t\t<checkbox-group @change=\"handlePointsSelection\">\n\t\t\t\t\t\t\t<label class=\"point-checkbox-item\" v-for=\"point in availablePoints\" :key=\"point._id\">\n\t\t\t\t\t\t\t\t<view class=\"point-checkbox\">\n\t\t\t\t\t\t\t\t\t<checkbox :value=\"point._id\" :checked=\"isPointSelected(point._id)\" color=\"#1677FF\" />\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"point-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"point-name\">{{ point.name }}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"point-address\" v-if=\"point.location && point.location.address\">{{ point.location.address }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t</checkbox-group>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 空列表提示 -->\n\t\t\t\t\t<view class=\"empty-list\" v-if=\"availablePoints.length === 0\">\n\t\t\t\t\t\t<uni-icons type=\"info\" size=\"32\" color=\"#8F959E\"></uni-icons>\n\t\t\t\t\t\t<text class=\"empty-tip\">暂无可选点位</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"selector-footer\">\n\t\t\t\t\t<view class=\"selector-btns\">\n\t\t\t\t\t\t<view class=\"selector-cancel\" @click=\"closePointSelector\">取消</view>\n\t\t\t\t\t\t<view class=\"selector-confirm-btn\" @click=\"confirmPointSelection\">\n\t\t\t\t\t\t\t<uni-icons type=\"plusempty\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t确定选择 ({{ tempSelectedPointIds.length }}个)\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\troute_id: '', // 线路ID，编辑模式需要\n\t\t\tformData: {\n\t\t\t\tname: '',\n\t\t\t\tremarks: '',\n\t\t\t\tstatus: 1,\n\t\t\t\tpoints: []\n\t\t\t},\n\t\t\trules: {\n\t\t\t\tname: {\n\t\t\t\t\trules: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\terrorMessage: '请输入线路名称'\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t},\n\t\t\tmapCenter: {\n\t\t\t\tlatitude: 39.908823,\n\t\t\t\tlongitude: 116.397470\n\t\t\t},\n\t\t\tmapScale: 14,\n\t\t\tmarkers: [],\n\t\t\tpolyline: [],\n\t\t\t\n\t\t\t// 点位选择相关\n\t\t\tavailablePoints: [], // 可选点位列表\n\t\t\tselectedPointIds: [], // 临时选中的点位ID\n\t\t\tpointKeyword: '', // 点位搜索关键字\n\t\t\ttempSelectedPointIds: [], // 临时选中，确认前\n\t\t\tsortOrder: 'asc', // 添加排序顺序状态\n\t\t}\n\t},\n\tcomputed: {\n\t\tisAllSelected() {\n\t\t\treturn this.availablePoints.length > 0 && this.tempSelectedPointIds.length === this.availablePoints.length;\n\t\t}\n\t},\n\tonLoad(options) {\n\t\t// 如果有ID参数，表示编辑模式\n\t\tif (options.id) {\n\t\t\tthis.route_id = options.id;\n\t\t\tthis.getRouteDetail();\n\t\t} else {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '参数错误',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateBack();\n\t\t\t}, 1500);\n\t\t}\n\t\t\n\t\t// 加载所有点位\n\t\tthis.loadAvailablePoints();\n\t},\n\tmethods: {\n\t\t// 获取线路详情\n\t\tasync getRouteDetail() {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中...'\n\t\t\t});\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.callRouteFunction('getRouteDetail', {\n\t\t\t\t\troute_id: this.route_id\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\tconst routeData = res.data;\n\t\t\t\t\t\n\t\t\t\t\t// 初始化表单数据\n\t\t\t\t\tthis.formData = {\n\t\t\t\t\t\tname: routeData.name || '',\n\t\t\t\t\t\tremarks: routeData.remarks || '',\n\t\t\t\t\t\tstatus: routeData.status !== undefined ? routeData.status : 1,\n\t\t\t\t\t\tpoints: []\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 处理点位数据\n\t\t\t\t\tif (routeData.pointsDetail && routeData.pointsDetail.length > 0) {\n\t\t\t\t\t\tthis.formData.points = routeData.pointsDetail.map(point => ({\n\t\t\t\t\t\t\tpoint_id: point.point_id,\n\t\t\t\t\t\t\tname: point.name,\n\t\t\t\t\t\t\taddress: point.location?.address || point.address || '',\n\t\t\t\t\t\t\tlatitude: point.location?.latitude || point.latitude || 0,\n\t\t\t\t\t\t\tlongitude: point.location?.longitude || point.longitude || 0,\n\t\t\t\t\t\t\torder: point.order || 0\n\t\t\t\t\t\t}));\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更新选中的点位ID\n\t\t\t\t\t\tthis.selectedPointIds = this.formData.points.map(point => point.point_id);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更新地图数据\n\t\t\t\t\t\tthis.updateMapData();\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '获取线路详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1500);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('获取线路详情错误', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取线路详情出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载可选点位\n\t\tasync loadAvailablePoints() {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载点位...'\n\t\t\t});\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.callPointFunction('getPointList', {\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t\tkeyword: this.pointKeyword,\n\t\t\t\t\t\tpage: 1,\n\t\t\t\t\t\tpageSize: 100  // 假设点位不会太多\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data && res.data.list) {\n\t\t\t\t\t// 过滤掉名称以\"校准\"开头的点位\n\t\t\t\t\tthis.availablePoints = res.data.list.filter(point => \n\t\t\t\t\t\t!point.name || !point.name.startsWith('校准')\n\t\t\t\t\t);\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '加载点位失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载点位错误', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载点位出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 打开点位选择器\n\t\topenPointSelector() {\n\t\t\t// 重置临时选中状态\n\t\t\tthis.tempSelectedPointIds = [...this.selectedPointIds];\n\t\t\tthis.$refs.pointSelector.open();\n\t\t},\n\t\t\n\t\t// 关闭点位选择器\n\t\tclosePointSelector() {\n\t\t\tthis.$refs.pointSelector.close();\n\t\t},\n\t\t\n\t\t// 搜索点位\n\t\tsearchPoints() {\n\t\t\tthis.loadAvailablePoints();\n\t\t},\n\t\t\n\t\t// 监听搜索关键词输入变化\n\t\tonPointKeywordInput(e) {\n\t\t\tthis.pointKeyword = e;\n\t\t\tthis.searchPoints();\n\t\t},\n\t\t\n\t\t// 全选功能\n\t\ttoggleSelectAll() {\n\t\t\tif (this.isAllSelected) {\n\t\t\t\t// 当前已全选，需要取消全选\n\t\t\t\tthis.tempSelectedPointIds = [];\n\t\t\t} else {\n\t\t\t\t// 当前未全选，需要全选\n\t\t\t\tthis.tempSelectedPointIds = this.availablePoints.map(point => point._id);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 处理点位选择变化\n\t\thandlePointsSelection(e) {\n\t\t\tthis.tempSelectedPointIds = e.detail.value;\n\t\t},\n\t\t\n\t\t// 判断点位是否被选中（用于显示复选框状态）\n\t\tisPointSelected(pointId) {\n\t\t\treturn this.tempSelectedPointIds.includes(pointId);\n\t\t},\n\t\t\n\t\t// 确认点位选择\n\t\tconfirmPointSelection() {\n\t\t\t// 更新实际选中的点位ID列表\n\t\t\tthis.selectedPointIds = [...this.tempSelectedPointIds];\n\t\t\t\n\t\t\t// 根据选中的点位ID查找完整的点位信息\n\t\t\tconst selectedPoints = this.availablePoints.filter(point => \n\t\t\t\tthis.selectedPointIds.includes(point._id)\n\t\t\t);\n\t\t\t\n\t\t\t// 更新表单中的点位列表（需要保持原来的顺序）\n\t\t\tconst newPoints = [];\n\t\t\t\n\t\t\t// 先添加原有的点位（保持顺序）\n\t\t\tthis.formData.points.forEach(existingPoint => {\n\t\t\t\tif (this.selectedPointIds.includes(existingPoint.point_id)) {\n\t\t\t\t\tnewPoints.push(existingPoint);\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 添加新选中的点位\n\t\t\tselectedPoints.forEach(point => {\n\t\t\t\tconst existingIndex = newPoints.findIndex(p => p.point_id === point._id);\n\t\t\t\tif (existingIndex === -1) {\n\t\t\t\t\tnewPoints.push({\n\t\t\t\t\t\tpoint_id: point._id,\n\t\t\t\t\t\tname: point.name,\n\t\t\t\t\t\taddress: point.location?.address || '',\n\t\t\t\t\t\tlatitude: point.location?.latitude || 0,\n\t\t\t\t\t\tlongitude: point.location?.longitude || 0,\n\t\t\t\t\t\torder: newPoints.length + 1\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 更新点位列表\n\t\t\tthis.formData.points = newPoints;\n\t\t\t\n\t\t\t// 更新地图数据\n\t\t\tthis.updateMapData();\n\t\t\t\n\t\t\t// 关闭选择器\n\t\t\tthis.closePointSelector();\n\t\t},\n\t\t\n\t\t// 移除点位\n\t\tremovePoint(index) {\n\t\t\t// 从点位列表移除\n\t\t\tthis.formData.points.splice(index, 1);\n\t\t\t\n\t\t\t// 更新选中的点位ID\n\t\t\tthis.selectedPointIds = this.formData.points.map(point => point.point_id);\n\t\t\t\n\t\t\t// 更新地图数据\n\t\t\tthis.updateMapData();\n\t\t},\n\t\t\n\t\t// 上移点位\n\t\tmovePointUp(index) {\n\t\t\tif (index > 0) {\n\t\t\t\tconst temp = this.formData.points[index];\n\t\t\t\tthis.formData.points[index] = this.formData.points[index - 1];\n\t\t\t\tthis.formData.points[index - 1] = temp;\n\t\t\t\t\n\t\t\t\t// 更新地图数据\n\t\t\t\tthis.updateMapData();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 下移点位\n\t\tmovePointDown(index) {\n\t\t\tif (index < this.formData.points.length - 1) {\n\t\t\t\tconst temp = this.formData.points[index];\n\t\t\t\tthis.formData.points[index] = this.formData.points[index + 1];\n\t\t\t\tthis.formData.points[index + 1] = temp;\n\t\t\t\t\n\t\t\t\t// 更新地图数据\n\t\t\t\tthis.updateMapData();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 计算两点之间的距离（米）\n\t\tcalculateDistance(lat1, lng1, lat2, lng2) {\n\t\t\tconst R = 6371000; // 地球半径，单位米\n\t\t\tconst dLat = this.deg2rad(lat2 - lat1);\n\t\t\tconst dLng = this.deg2rad(lng2 - lng1);\n\t\t\tconst a = \n\t\t\t\tMath.sin(dLat/2) * Math.sin(dLat/2) +\n\t\t\t\tMath.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * \n\t\t\t\tMath.sin(dLng/2) * Math.sin(dLng/2);\n\t\t\tconst c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n\t\t\tconst distance = R * c; // 距离，单位米\n\t\t\treturn distance;\n\t\t},\n\t\t\n\t\t// 角度转弧度\n\t\tdeg2rad(deg) {\n\t\t\treturn deg * (Math.PI/180);\n\t\t},\n\t\t\n\t\t// 创建距离标记\n\t\tcreateDistanceMarkers() {\n\t\t\tif (this.formData.points.length < 2) return [];\n\t\t\t\n\t\t\tconst distanceMarkers = [];\n\t\t\t\n\t\t\tfor (let i = 0; i < this.formData.points.length - 1; i++) {\n\t\t\t\tconst point1 = this.formData.points[i];\n\t\t\t\tconst point2 = this.formData.points[i + 1];\n\t\t\t\t\n\t\t\t\tconst lat1 = parseFloat(point1.latitude) || 0;\n\t\t\t\tconst lng1 = parseFloat(point1.longitude) || 0;\n\t\t\t\tconst lat2 = parseFloat(point2.latitude) || 0;\n\t\t\t\tconst lng2 = parseFloat(point2.longitude) || 0;\n\t\t\t\t\n\t\t\t\t// 计算两点之间的距离\n\t\t\t\tconst distance = this.calculateDistance(lat1, lng1, lat2, lng2);\n\t\t\t\t\n\t\t\t\t// 计算中点位置\n\t\t\t\tconst midLat = (lat1 + lat2) / 2;\n\t\t\t\tconst midLng = (lng1 + lng2) / 2;\n\t\t\t\t\n\t\t\t\t// 创建距离标记\n\t\t\t\tdistanceMarkers.push({\n\t\t\t\t\tid: 1000 + i, // 使用数字ID，从1000开始避免与点位ID冲突\n\t\t\t\t\tlatitude: midLat,\n\t\t\t\t\tlongitude: midLng,\n\t\t\t\t\ticonPath: '/static/map/transparent.png', // 使用透明图标\n\t\t\t\t\twidth: 1,\n\t\t\t\t\theight: 1,\n\t\t\t\t\tanchor: {\n\t\t\t\t\t\tx: 0.5,\n\t\t\t\t\t\ty: 0.5\n\t\t\t\t\t},\n\t\t\t\t\tlabel: {\n\t\t\t\t\t\tcontent: distance > 1000 ? `${(distance/1000).toFixed(1)}公里` : `${Math.round(distance)}米`,\n\t\t\t\t\t\tcolor: '#1677FF',\n\t\t\t\t\t\tbgColor: '#FFFFFF',\n\t\t\t\t\t\tfontSize: 12,\n\t\t\t\t\t\tborderRadius: 4,\n\t\t\t\t\t\tborderWidth: 1,\n\t\t\t\t\t\tborderColor: '#EEEEEE',\n\t\t\t\t\t\tpadding: 6,\n\t\t\t\t\t\ttextAlign: 'center',\n\t\t\t\t\t\tanchorX: 0,\n\t\t\t\t\t\tanchorY: -30\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\treturn distanceMarkers;\n\t\t},\n\t\t\n\t\t// 更新地图数据\n\t\tupdateMapData() {\n\t\t\tif (this.formData.points.length === 0) {\n\t\t\t\tthis.markers = [];\n\t\t\t\tthis.polyline = [];\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 创建标记点\n\t\t\tconst pointMarkers = this.formData.points.map((point, index) => {\n\t\t\t\t// 确保坐标有效\n\t\t\t\tconst latitude = parseFloat(point.latitude) || 0;\n\t\t\t\tconst longitude = parseFloat(point.longitude) || 0;\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\tid: index,\n\t\t\t\t\tlatitude,\n\t\t\t\t\tlongitude,\n\t\t\t\t\ttitle: point.name,\n\t\t\t\t\tcallout: {\n\t\t\t\t\t\tcontent: `${index + 1}. ${point.name}`,\n\t\t\t\t\t\tcolor: '#FFFFFF',\n\t\t\t\t\t\tfontSize: 12,\n\t\t\t\t\t\tborderRadius: 4,\n\t\t\t\t\t\tbgColor: '#1677FF',\n\t\t\t\t\t\tpadding: 5,\n\t\t\t\t\t\tdisplay: 'ALWAYS'\n\t\t\t\t\t},\n\t\t\t\t\ticonPath: '/static/map/marker.png',\n\t\t\t\t\twidth: 32,\n\t\t\t\t\theight: 32,\n\t\t\t\t\tanchor: {\n\t\t\t\t\t\tx: 0.5,\n\t\t\t\t\t\ty: 1\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t});\n\t\t\t\n\t\t\t// 合并所有标记 - 不再显示距离标记\n\t\t\tthis.markers = pointMarkers;\n\t\t\t\n\t\t\t// 创建路线连接线\n\t\t\tif (this.formData.points.length > 1) {\n\t\t\t\tthis.polyline = [{\n\t\t\t\t\tpoints: this.formData.points.map(point => ({\n\t\t\t\t\t\tlatitude: parseFloat(point.latitude) || 0,\n\t\t\t\t\t\tlongitude: parseFloat(point.longitude) || 0\n\t\t\t\t\t})),\n\t\t\t\t\tcolor: '#1677FF',\n\t\t\t\t\twidth: 4,\n\t\t\t\t\tdottedLine: false,\n\t\t\t\t\tarrowLine: true,\n\t\t\t\t\tborderColor: '#E7F1FF',\n\t\t\t\t\tborderWidth: 1\n\t\t\t\t}];\n\t\t\t} else {\n\t\t\t\tthis.polyline = [];\n\t\t\t}\n\t\t\t\n\t\t\t// 设置地图中心点\n\t\t\tif (this.formData.points.length > 0) {\n\t\t\t\t// 计算所有点的平均位置\n\t\t\t\tlet sumLat = 0;\n\t\t\t\tlet sumLng = 0;\n\t\t\t\tlet validPoints = 0;\n\t\t\t\t\n\t\t\t\tthis.formData.points.forEach(point => {\n\t\t\t\t\tconst lat = parseFloat(point.latitude);\n\t\t\t\t\tconst lng = parseFloat(point.longitude);\n\t\t\t\t\t\n\t\t\t\t\tif (!isNaN(lat) && !isNaN(lng)) {\n\t\t\t\t\t\tsumLat += lat;\n\t\t\t\t\t\tsumLng += lng;\n\t\t\t\t\t\tvalidPoints++;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (validPoints > 0) {\n\t\t\t\t\tthis.mapCenter = {\n\t\t\t\t\t\tlatitude: sumLat / validPoints,\n\t\t\t\t\t\tlongitude: sumLng / validPoints\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 设置为最大缩放级别\n\t\t\t\t\tthis.mapScale = 18;\n\t\t\t\t} else {\n\t\t\t\t\t// 默认位置，如果没有有效点\n\t\t\t\t\tthis.mapCenter = {\n\t\t\t\t\t\tlatitude: 39.908823,\n\t\t\t\t\t\tlongitude: 116.397470\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 地图控制方法\n\t\thandleMapZoomIn() {\n\t\t\tif (this.mapScale < 20) {\n\t\t\t\tthis.mapScale += 1;\n\t\t\t}\n\t\t},\n\t\t\n\t\thandleMapZoomOut() {\n\t\t\tif (this.mapScale > 5) {\n\t\t\t\tthis.mapScale -= 1;\n\t\t\t}\n\t\t},\n\t\t\n\t\thandleMapReset() {\n\t\t\tif (this.formData.points.length > 0) {\n\t\t\t\tthis.updateMapData();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 验证表单\n\t\tvalidateForm() {\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\tthis.$refs.form.validate().then(res => {\n\t\t\t\t\t// 基本表单验证通过\n\t\t\t\t\tif (res) {\n\t\t\t\t\t\t// 验证是否选择了点位\n\t\t\t\t\t\tif (this.formData.points.length === 0) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '请至少选择一个点位',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treject('请至少选择一个点位');\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tresolve(true);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 基本表单验证不通过\n\t\t\t\t\t\treject('表单验证不通过');\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\treject(err);\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 提交表单\n\t\tasync handleSubmit() {\n\t\t\ttry {\n\t\t\t\t// 验证表单\n\t\t\t\tawait this.validateForm();\n\t\t\t\t\n\t\t\t\t// 再次确认name字段有值\n\t\t\t\tif (!this.formData.name || this.formData.name.trim() === '') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '线路名称不能为空',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在提交...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 在提交前再次验证点位是否存在\n\t\t\t\ttry {\n\t\t\t\t\t// 获取最新点位列表\n\t\t\t\t\tconst pointsRes = await PatrolApi.callPointFunction('getPointList', {\n\t\t\t\t\t\tparams: {\n\t\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t\t\tpage: 1,\n\t\t\t\t\t\t\tpageSize: 100\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (pointsRes.code === 0 && pointsRes.data && pointsRes.data.list) {\n\t\t\t\t\t\tconst existingPointIds = pointsRes.data.list.map(p => p._id);\n\t\t\t\t\t\tconst invalidPoints = this.formData.points.filter(p => !existingPointIds.includes(p.point_id));\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果存在无效点位，显示警告并停止提交\n\t\t\t\t\t\tif (invalidPoints.length > 0) {\n\t\t\t\t\t\t\tconst invalidNames = invalidPoints.map(p => p.name).join('、');\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '点位不存在',\n\t\t\t\t\t\t\t\tcontent: `选择的点位\"${invalidNames}\"可能已被删除，请重新选择有效点位。`,\n\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\tconfirmText: '知道了'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('验证点位时出错', error);\n\t\t\t\t\t// 继续提交，让服务器处理验证\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 准备提交数据，放入params对象\n\t\t\t\tconst submitData = {\n\t\t\t\t\troute_id: this.route_id,\n\t\t\t\t\tname: this.formData.name.trim(),  // 确保去除首尾空格\n\t\t\t\t\tremarks: this.formData.remarks || '',\n\t\t\t\t\tstatus: this.formData.status || 1,\n\t\t\t\t\tpoints: this.formData.points.map((point, index) => ({\n\t\t\t\t\t\tpoint_id: point.point_id,\n\t\t\t\t\t\torder: index + 1\n\t\t\t\t\t}))\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 设置提交超时\n\t\t\t\tconst timeout = 30000; // 30秒超时\n\t\t\t\tconst submitPromise = PatrolApi.callRouteFunction('updateRoute', submitData);\n\t\t\t\t\n\t\t\t\t// 创建一个超时Promise\n\t\t\t\tconst timeoutPromise = new Promise((_, reject) => {\n\t\t\t\t\tsetTimeout(() => reject(new Error('提交请求超时，请稍后重试')), timeout);\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 使用Promise.race竞争，谁先完成就用谁的结果\n\t\t\t\tconst res = await Promise.race([submitPromise, timeoutPromise])\n\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\tconsole.error('提交请求出错或超时', error);\n\t\t\t\t\t\treturn { code: -1, message: error.message || '提交请求失败，请稍后重试' };\n\t\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '更新线路成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 设置全局标记，指示列表页需要刷新\n\t\t\t\t\tif (getApp().globalData) {\n\t\t\t\t\t\tgetApp().globalData.routeListNeedRefresh = true;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 返回列表页\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1500);\n\t\t\t\t} else {\n\t\t\t\t\tlet errorMsg = res.message || '更新线路失败';\n\t\t\t\t\t\n\t\t\t\t\t// 针对特定错误提供更友好的提示\n\t\t\t\t\tif (errorMsg.includes('不存在的点位')) {\n\t\t\t\t\t\terrorMsg = '所选点位不存在，请重新选择有效点位';\n\t\t\t\t\t} else if (errorMsg.includes('线路名称已存在')) {\n\t\t\t\t\t\terrorMsg = '线路名称已存在，请使用其他名称';\n\t\t\t\t\t} else if (errorMsg.includes('线路不存在')) {\n\t\t\t\t\t\terrorMsg = '线路不存在或已被删除';\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: errorMsg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('更新线路出错', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: typeof e === 'string' ? e : (e.message || '处理请求时出错'),\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 取消\n\t\thandleCancel() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 清除点位搜索\n\t\tclearPointSearch() {\n\t\t\tthis.pointKeyword = '';\n\t\t\tthis.loadAvailablePoints();\n\t\t},\n\t\t\n\t\t// 切换排序方式\n\t\ttoggleSort() {\n\t\t\t// 先改变排序方向\n\t\t\tthis.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n\t\t\t\n\t\t\t// 创建一个新数组并进行排序\n\t\t\tconst sortedPoints = [...this.formData.points];\n\t\t\t\n\t\t\t// 根据order字段排序\n\t\t\tsortedPoints.sort((a, b) => {\n\t\t\t\t// 始终使用 a.order - b.order 的方式比较，通过 sortOrder 来决定是否反转结果\n\t\t\t\tconst result = a.order - b.order;\n\t\t\t\treturn this.sortOrder === 'asc' ? result : -result;\n\t\t\t});\n\t\t\t\n\t\t\t// 更新数据\n\t\t\tthis.formData.points = sortedPoints;\n\t\t\t\n\t\t\t// 更新地图数据\n\t\t\tthis.updateMapData();\n\t\t\t\n\t\t\t// 显示提示\n\t\t\tuni.showToast({\n\t\t\t\ttitle: this.sortOrder === 'asc' ? '已按时间正序排列' : '已按时间倒序排列',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n/* 主色调变量 */\n$primary-color: #1677FF; // 支付宝蓝\n$primary-light: #E7F1FF;\n$primary-dark: #0E5FD8;\n\n$success-color: #07C160;\n$warning-color: #FFA300;\n$danger-color: #FF3B30;\n$info-color: #8F959E;\n\n$text-primary: #2C3E50;\n$text-secondary: #666666;\n$text-tertiary: #999999;\n$border-color: #EAEAEA;\n$background: #F7F8FA;\n\n$radius-sm: 6rpx;\n$radius-md: 12rpx;\n$radius-lg: 16rpx;\n$radius-xl: 24rpx;\n$radius-full: 999rpx;\n\n$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\n/* 淡入动画 */\n@keyframes fadeIn {\n\tfrom {\n\t\topacity: 0;\n\t\ttransform: translateY(20rpx);\n\t}\n\tto {\n\t\topacity: 1;\n\t\ttransform: translateY(0);\n\t}\n}\n\n/* 脉冲动画 */\n@keyframes pulse {\n\t0% {\n\t\ttransform: scale(1);\n\t}\n\t50% {\n\t\ttransform: scale(1.05);\n\t}\n\t100% {\n\t\ttransform: scale(1);\n\t}\n}\n\n.route-edit-container {\n\tmin-height: 100vh;\n\tbackground-color: $background;\n\tpadding-bottom: 30rpx;\n}\n\n/* 头部区域样式 */\n.header-section {\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid $border-color;\n\tposition: relative;\n\tbox-shadow: $shadow-sm;\n\tz-index: 5;\n}\n\n.header-content {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.header-title {\n\tfont-size: 36rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\tposition: relative;\n\tpadding-left: 24rpx;\n\t\n\t&:before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 8rpx;\n\t\theight: 32rpx;\n\t\tbackground-color: $primary-color;\n\t\tborder-radius: $radius-sm;\n\t}\n}\n\n/* 表单卡片样式 */\n.form-card {\n\tmargin: 20rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: $radius-lg;\n\toverflow: hidden;\n\tbox-shadow: $shadow-sm;\n\tanimation: fadeIn 0.5s ease-out;\n\t\n\t&:nth-child(2) {\n\t\tanimation-delay: 0.1s;\n\t}\n\t\n\t&:nth-child(3) {\n\t\tanimation-delay: 0.2s;\n\t}\n}\n\n.card-header {\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid $border-color;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.section-header {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\tposition: relative;\n\tpadding-left: 20rpx;\n\t\n\t&:before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 6rpx;\n\t\theight: 28rpx;\n\t\tbackground-color: $primary-color;\n\t\tborder-radius: $radius-sm;\n\t}\n}\n\n.count-badge {\n\tbackground-color: $primary-light;\n\tcolor: $primary-color;\n\tfont-size: 24rpx;\n\tpadding: 4rpx 16rpx;\n\tborder-radius: $radius-full;\n\tfont-weight: 500;\n\tmargin-left: 16rpx;\n}\n\n.form-content {\n\tpadding: 20rpx 30rpx;\n}\n\n/* 表单元素样式 */\n::v-deep .uni-forms-item__label {\n\tfont-size: 28rpx;\n\tcolor: $text-secondary;\n\tfont-weight: 500;\n\tmargin-bottom: 8rpx;\n\tmin-width: 160rpx;\n\twhite-space: nowrap;\n}\n\n::v-deep .uni-easyinput__content {\n\tbackground-color: $background;\n\tborder-radius: $radius-md;\n\tborder: 1rpx solid $border-color !important;\n\theight: 80rpx;\n\tflex: 1;\n}\n\n::v-deep textarea.uni-easyinput__content {\n\theight: 160rpx;\n\tpadding: 16rpx;\n\tbox-sizing: border-box;\n}\n\n::v-deep .uni-easyinput__placeholder-class {\n\tcolor: #999999;\n}\n\n::v-deep .uni-forms-item--required .uni-forms-item__label {\n\tmin-width: 170rpx;\n}\n\n::v-deep .uni-forms-item__content {\n\tdisplay: flex;\n\twidth: 100%;\n}\n\n.switch-wrapper {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-start;\n\theight: 60rpx;\n}\n\n.switch-label {\n\tfont-size: 28rpx;\n\tcolor: $text-primary;\n\tmargin-left: 20rpx;\n\tline-height: 1;\n\tvertical-align: middle;\n\t\n\t&.active-status {\n\t\tcolor: $success-color;\n\t}\n\t\n\t&.inactive-status {\n\t\tcolor: $info-color;\n\t}\n}\n\n/* 添加点位按钮 */\n.add-point-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: $primary-light;\n\tcolor: $primary-color;\n\tfont-size: 26rpx;\n\tpadding: 8rpx 20rpx;\n\tborder-radius: $radius-full;\n\ttransition: all 0.3s ease;\n\t\n\ttext {\n\t\tmargin-left: 6rpx;\n\t}\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.8;\n\t}\n}\n\n/* 空点位提示 */\n.empty-points {\n\tpadding: 40rpx 0;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.empty-tip {\n\tfont-size: 26rpx;\n\tcolor: $text-tertiary;\n\tmargin-top: 20rpx;\n}\n\n/* 已选点位列表样式 */\n.selected-points-list {\n\tmargin-bottom: 20rpx;\n}\n\n.point-item {\n\tmargin-bottom: 30rpx;\n\tbackground-color: $background;\n\tborder-radius: $radius-md;\n\tpadding: 20rpx;\n\tborder-left: 4rpx solid $primary-color;\n\tanimation: fadeIn 0.5s ease-out forwards;\n\t\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n}\n\n.point-header {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 10rpx;\n}\n\n.point-order {\n\twidth: 48rpx;\n\theight: 48rpx;\n\tbackground-color: $primary-color;\n\tcolor: #FFFFFF;\n\tborder-radius: $radius-md;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 16rpx;\n\tflex-shrink: 0;\n}\n\n.order-number {\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n}\n\n.point-info {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.point-name {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\tmargin-bottom: 6rpx;\n}\n\n.point-address {\n\tfont-size: 24rpx;\n\tcolor: $text-tertiary;\n}\n\n.point-actions {\n\tdisplay: flex;\n\tjustify-content: flex-end;\n\talign-items: center;\n\tmargin-top: 10rpx;\n}\n\n.action-move {\n\tdisplay: flex;\n\tmargin-right: 16rpx;\n}\n\n.move-btn {\n\twidth: 56rpx;\n\theight: 56rpx;\n\tborder-radius: $radius-md;\n\tbackground-color: #FFFFFF;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder: 1rpx solid $border-color;\n\t\n\t&:first-child {\n\t\tmargin-right: 10rpx;\n\t}\n\t\n\t&:active {\n\t\tbackground-color: $primary-light;\n\t}\n}\n\n.action-delete {\n\twidth: 56rpx;\n\theight: 56rpx;\n\tborder-radius: $radius-md;\n\tbackground-color: #FFF1F0;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\t\n\t&:active {\n\t\tbackground-color: #FFE4E3;\n\t}\n}\n\n/* 地图样式 */\n.map-container {\n\theight: 480rpx;\n\tpadding: 15rpx;\n\tposition: relative;\n}\n\n.route-map {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: $radius-md;\n}\n\n.map-controls {\n\tposition: absolute;\n\tright: 30rpx;\n\tbottom: 30rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 10rpx;\n\tz-index: 10;\n}\n\n.map-control-btn {\n\twidth: 64rpx;\n\theight: 64rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: $radius-md;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: $shadow-md;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n}\n\n/* 底部操作区域 */\n.action-area {\n\tposition: relative;\n\tbackground-color: #FFFFFF;\n\tbox-shadow: $shadow-md;\n\tpadding: 20rpx 30rpx;\n\tmargin: 20rpx;\n\tborder-radius: $radius-lg;\n\tz-index: 10;\n}\n\n.action-btns {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.cancel-btn, .submit-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: $radius-full;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n}\n\n.cancel-btn {\n\tbackground-color: $background;\n\tcolor: $text-secondary;\n\tborder: 1rpx solid $border-color;\n}\n\n.submit-btn {\n\tbackground-color: $primary-color;\n\tcolor: #FFFFFF;\n}\n\n/* 点位选择弹窗样式 */\n.point-selector {\n\tbackground-color: #FFFFFF;\n\tborder-radius: $radius-xl $radius-xl 0 0;\n\tmax-height: 80vh;\n\tdisplay: flex;\n\tflex-direction: column;\n\tz-index: 999;\n}\n\n.selector-header {\n\tpadding: 30rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tborder-bottom: 1rpx solid $border-color;\n}\n\n.selector-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n}\n\n.selector-close {\n\tfont-size: 28rpx;\n\tcolor: $text-tertiary;\n}\n\n.selector-search {\n\tpadding: 10rpx 30rpx;\n\tbackground-color: #FFFFFF;\n}\n\n.selector-content {\n\tflex: 1;\n\toverflow-y: auto;\n\tpadding: 10rpx 30rpx 30rpx;\n\tmax-height: 60vh;\n}\n\n.point-list {\n\tpadding-bottom: 40rpx;\n}\n\n.point-checkbox-item {\n\tdisplay: flex;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid $border-color;\n}\n\n.point-checkbox {\n\tmargin-right: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.selector-footer {\n\tpadding: 20rpx 30rpx;\n\tborder-top: 1rpx solid $border-color;\n}\n\n.selector-btns {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.selector-cancel {\n\tflex: 1;\n\theight: 80rpx;\n\tbackground-color: $background;\n\tcolor: $text-secondary;\n\tborder: 1rpx solid $border-color;\n\tborder-radius: $radius-full;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n}\n\n.selector-confirm-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tbackground-color: $primary-color;\n\tcolor: #FFFFFF;\n\tborder-radius: $radius-full;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n}\n\n.empty-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 60rpx 0;\n}\n\n.textarea-container {\n\twidth: 100%;\n\tborder: 1rpx solid $border-color;\n\tborder-radius: $radius-md;\n\tbackground-color: #FFFFFF;\n\toverflow: hidden;\n}\n\n.remark-textarea {\n\twidth: 100%;\n\theight: 160rpx;\n\tpadding: 16rpx;\n\tfont-size: 28rpx;\n\tline-height: 1.5;\n\tcolor: $text-primary;\n\tbox-sizing: border-box;\n\tbackground-color: #FFFFFF;\n}\n\n.textarea-placeholder {\n\tfont-size: 28rpx;\n\tcolor: #999999;\n}\n\n.selection-tip {\n\tpadding: 10rpx 30rpx;\n\tbackground-color: $primary-light;\n\tmargin: 0 30rpx 10rpx;\n\tborder-radius: $radius-md;\n}\n\n.tip-text {\n\tfont-size: 26rpx;\n\tcolor: $primary-dark;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.tip-text:before {\n\tcontent: \"!\";\n\tdisplay: inline-block;\n\twidth: 28rpx;\n\theight: 28rpx;\n\tline-height: 28rpx;\n\ttext-align: center;\n\tbackground-color: $primary-color;\n\tcolor: white;\n\tborder-radius: 50%;\n\tmargin-right: 8rpx;\n\tfont-weight: bold;\n\tfont-size: 22rpx;\n}\n\n.select-all-container {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 10rpx 0;\n\tmargin-bottom: 10rpx;\n\tborder-bottom: 1rpx solid $border-color;\n}\n\n.select-all-checkbox {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.select-all-text {\n\tfont-size: 28rpx;\n\tcolor: $text-secondary;\n\tfont-weight: 500;\n\tmargin-left: 10rpx;\n}\n\n.selected-count {\n\tfont-size: 24rpx;\n\tcolor: $text-tertiary;\n}\n\n/* 头部操作区样式 */\n.header-actions {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 16rpx;\n}\n\n.sort-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: #F5F5F5;\n\tpadding: 8rpx 20rpx;\n\tborder-radius: $radius-full;\n\ttransition: all 0.3s ease;\n\t\n\ttext {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666666;\n\t\tmargin-left: 6rpx;\n\t}\n\t\n\t&.sort-btn-desc {\n\t\tbackground-color: $primary-color;\n\t\t\n\t\ttext {\n\t\t\tcolor: #FFFFFF;\n\t\t}\n\t}\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.8;\n\t}\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571661948\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
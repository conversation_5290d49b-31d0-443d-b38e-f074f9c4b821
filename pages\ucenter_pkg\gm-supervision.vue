<template>
	<view class="container">
		<!-- 页面标题 -->
		<view class="page-header">
			<text class="page-title">指派任务监督</text>
			<text class="page-subtitle">实时监控指派任务的执行情况</text>
		</view>

		<!-- 统计卡片区域 -->
		<view class="stats-section">
			<view class="stats-grid">
				<view class="stat-card" @click="filterByStatus('assigned_to_responsible')">
					<view class="stat-number assigned">{{ taskStats.assigned || 0 }}</view>
					<text class="stat-label">执行中</text>
				</view>
				<view class="stat-card" @click="filterByStatus('completed_by_responsible')">
					<view class="stat-number pending">{{ taskStats.pending || 0 }}</view>
					<text class="stat-label">待确认</text>
				</view>
				<view class="stat-card" @click="filterByStatus('final_completed')">
					<view class="stat-number completed">{{ taskStats.completed || 0 }}</view>
					<text class="stat-label">已完成</text>
				</view>
				<view class="stat-card" @click="filterByStatus('overdue')">
					<view class="stat-number overdue">{{ taskStats.overdue || 0 }}</view>
					<text class="stat-label">超时</text>
				</view>
			</view>
		</view>

		<!-- 筛选标签 -->
		<view class="filter-tabs">
			<view 
				class="filter-tab" 
				:class="{ active: currentFilter === 'all' }"
				@click="setFilter('all')">
				全部
			</view>
			<view 
				class="filter-tab" 
				:class="{ active: currentFilter === 'assigned_to_responsible' }"
				@click="setFilter('assigned_to_responsible')">
				执行中
			</view>
			<view 
				class="filter-tab" 
				:class="{ active: currentFilter === 'completed_by_responsible' }"
				@click="setFilter('completed_by_responsible')">
				待确认
			</view>
			<view 
				class="filter-tab" 
				:class="{ active: currentFilter === 'overdue' }"
				@click="setFilter('overdue')">
				超时任务
			</view>
		</view>

		<!-- 任务列表 -->
		<view class="task-list" v-if="filteredTaskList.length > 0">
			<view 
				class="task-item" 
				v-for="(task, index) in filteredTaskList" 
				:key="task._id"
				@click="goToTaskDetail(task)">
				
				<view class="task-header">
					<view class="task-title-row">
						<text class="task-name">{{ task.name }}</text>
						<view class="task-status" :class="getStatusClass(task.workflowStatus)">
							{{ getStatusText(task.workflowStatus) }}
						</view>
					</view>
					<text class="task-project">{{ task.project || '未分类' }}</text>
				</view>

				<view class="task-content">
					<text class="task-description">{{ task.description || '暂无描述' }}</text>
				</view>

				<view class="task-footer">
					<view class="responsible-info">
						<text class="responsible-label">负责人：</text>
						<text class="responsible-name">{{ getResponsibleName(task.responsibleUserId) }}</text>
					</view>
					<view class="time-info">
						<text class="time-label">{{ getTimeLabel(task) }}</text>
						<text class="time-value" :class="{ overdue: isOverdue(task) }">
							{{ getTimeValue(task) }}
						</text>
					</view>
				</view>

				<!-- 快速操作按钮 -->
				<view class="quick-actions" v-if="task.workflowStatus === 'completed_by_responsible'">
					<view class="action-btn confirm-btn" @click.stop="quickConfirm(task)">
						<uni-icons type="checkmarkempty" size="16" color="#FFFFFF"></uni-icons>
						<text>确认完成</text>
					</view>
					<view class="action-btn reject-btn" @click.stop="quickReject(task)">
						<uni-icons type="closeempty" size="16" color="#FFFFFF"></uni-icons>
						<text>退回重做</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else>
			<image class="empty-image" src="/static/empty/empty_task.png" mode="aspectFit"></image>
			<text class="empty-text">{{ getEmptyText() }}</text>
		</view>

		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<uni-load-more :status="loadingStatus"></uni-load-more>
		</view>
	</view>
</template>

<script>
	import { formatDate } from '@/utils/date.js';
	
	export default {
		data() {
			return {
				taskList: [],
				taskStats: {
					assigned: 0,
					pending: 0,
					completed: 0,
					overdue: 0
				},
				currentFilter: 'all',
				loading: false,
				loadingStatus: 'more',
				responsibleUsers: {}, // 负责人信息缓存
			}
		},
		computed: {
			filteredTaskList() {
				if (this.currentFilter === 'all') {
					return this.taskList;
				}
				if (this.currentFilter === 'overdue') {
					return this.taskList.filter(task => this.isOverdue(task));
				}
				return this.taskList.filter(task => task.workflowStatus === this.currentFilter);
			}
		},
		onLoad() {
			this.checkPermission();
			this.loadTaskData();
		},
		onShow() {
			// 页面显示时刷新数据
			this.loadTaskData();
		},
		onPullDownRefresh() {
			this.loadTaskData().then(() => {
				uni.stopPullDownRefresh();
			});
		},
		methods: {
			// 权限检查
			checkPermission() {
				if (!this.uniIDHasRole('GM') && !this.uniIDHasRole('admin')) {
					uni.showModal({
						title: '权限不足',
						content: '只有厂长才能访问此页面',
						showCancel: false,
						success: () => {
							uni.navigateBack();
						}
					});
				}
			},

			// 加载任务数据
			async loadTaskData() {
				this.loading = true;
				this.loadingStatus = 'loading';
				
				try {
					const res = await uniCloud.callFunction({
						name: 'feedback-list',
						data: {
							action: 'getGMSupervisionTasks'
						}
					});

					if (res.result && res.result.code === 0) {
						this.taskList = res.result.data.list || [];
						this.taskStats = res.result.data.stats || this.taskStats;
						this.responsibleUsers = res.result.data.responsibleUsers || {};
					} else {
						throw new Error(res.result?.message || '获取任务数据失败');
					}
				} catch (error) {
					console.error('加载任务数据失败:', error);
					uni.showToast({
						title: '加载失败',
						icon: 'error'
					});
				} finally {
					this.loading = false;
					this.loadingStatus = 'more';
				}
			},

			// 设置筛选条件
			setFilter(filter) {
				this.currentFilter = filter;
			},

			// 按状态筛选
			filterByStatus(status) {
				this.setFilter(status);
			},

			// 获取状态样式类
			getStatusClass(status) {
				const classMap = {
					'assigned_to_responsible': 'status-assigned',
					'completed_by_responsible': 'status-pending',
					'final_completed': 'status-completed'
				};
				return classMap[status] || 'status-default';
			},

			// 获取状态文本
			getStatusText(status) {
				const textMap = {
					'assigned_to_responsible': '执行中',
					'completed_by_responsible': '待确认',
					'final_completed': '已完成'
				};
				return textMap[status] || '未知状态';
			},

			// 获取负责人姓名
			getResponsibleName(userId) {
				const user = this.responsibleUsers[userId];
				return user ? (user.nickname || user.username || '未知') : '未指派';
			},

			// 获取时间标签
			getTimeLabel(task) {
				if (task.workflowStatus === 'assigned_to_responsible') {
					return '指派时间：';
				} else if (task.workflowStatus === 'completed_by_responsible') {
					return '完成时间：';
				} else if (task.workflowStatus === 'final_completed') {
					return '确认时间：';
				}
				return '创建时间：';
			},

			// 获取时间值
			getTimeValue(task) {
				let timestamp;
				if (task.workflowStatus === 'assigned_to_responsible') {
					timestamp = task.assignedTime;
				} else if (task.workflowStatus === 'completed_by_responsible') {
					timestamp = task.completedByResponsibleTime;
				} else if (task.workflowStatus === 'final_completed') {
					timestamp = task.finalCompletedTime;
				} else {
					timestamp = task.createTime;
				}
				return timestamp ? formatDate(timestamp, 'MM-DD HH:mm') : '未知';
			},

			// 判断是否超时
			isOverdue(task) {
				if (task.workflowStatus !== 'assigned_to_responsible') return false;
				if (!task.assignedTime) return false;
				
				// 超过3天未完成视为超时
				const threeDays = 3 * 24 * 60 * 60 * 1000;
				return Date.now() - task.assignedTime > threeDays;
			},

			// 跳转到任务详情
			goToTaskDetail(task) {
				uni.navigateTo({
					url: `/pages/feedback_pkg/examine?id=${task._id}`
				});
			},

			// 快速确认完成
			async quickConfirm(task) {
				try {
					const res = await uni.showModal({
						title: '确认完成',
						content: '确认该任务已完成？',
						confirmText: '确认',
						cancelText: '取消'
					});

					if (res.confirm) {
						await this.confirmTask(task._id, '厂长确认任务完成');
					}
				} catch (error) {
					console.error('快速确认失败:', error);
				}
			},

			// 快速退回重做
			async quickReject(task) {
				try {
					const res = await uni.showModal({
						title: '退回重做',
						content: '确认退回该任务重新执行？',
						confirmText: '退回',
						cancelText: '取消'
					});

					if (res.confirm) {
						// 跳转到详情页面进行详细的退回操作
						this.goToTaskDetail(task);
					}
				} catch (error) {
					console.error('快速退回失败:', error);
				}
			},

			// 确认任务完成
			async confirmTask(taskId, reason) {
				try {
					uni.showLoading({ title: '处理中...' });

					const res = await uniCloud.callFunction({
						name: 'feedback-workflow',
						data: {
							action: 'gm_final_confirm',
							id: taskId,
							reason: reason
						}
					});

					if (res.result && res.result.code === 0) {
						uni.showToast({
							title: '确认成功',
							icon: 'success'
						});
						// 刷新数据
						this.loadTaskData();
					} else {
						throw new Error(res.result?.message || '确认失败');
					}
				} catch (error) {
					console.error('确认任务失败:', error);
					uni.showToast({
						title: error.message || '确认失败',
						icon: 'error'
					});
				} finally {
					uni.hideLoading();
				}
			},

			// 获取空状态文本
			getEmptyText() {
				const textMap = {
					'all': '暂无指派任务',
					'assigned_to_responsible': '暂无执行中的任务',
					'completed_by_responsible': '暂无待确认的任务',
					'overdue': '暂无超时任务'
				};
				return textMap[this.currentFilter] || '暂无数据';
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #f8f9fc;
		padding: 20rpx;
	}

	.page-header {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		color: white;
	}

	.page-title {
		font-size: 36rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}

	.page-subtitle {
		font-size: 28rpx;
		opacity: 0.9;
	}

	.stats-section {
		margin-bottom: 30rpx;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20rpx;
	}

	.stat-card {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}

	.stat-number {
		font-size: 48rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;

		&.assigned { color: #2196f3; }
		&.pending { color: #ff9800; }
		&.completed { color: #4caf50; }
		&.overdue { color: #f44336; }
	}

	.stat-label {
		font-size: 24rpx;
		color: #666;
	}

	.filter-tabs {
		display: flex;
		background: white;
		border-radius: 16rpx;
		padding: 8rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	}

	.filter-tab {
		flex: 1;
		text-align: center;
		padding: 20rpx 10rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		color: #666;
		transition: all 0.3s ease;

		&.active {
			background: #667eea;
			color: white;
		}
	}

	.task-list {
		margin-bottom: 30rpx;
	}

	.task-item {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;

		&:active {
			transform: translateY(2rpx);
		}
	}

	.task-header {
		margin-bottom: 20rpx;
	}

	.task-title-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.task-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		flex: 1;
		margin-right: 20rpx;
	}

	.task-status {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		color: white;

		&.status-assigned { background: #2196f3; }
		&.status-pending { background: #ff9800; }
		&.status-completed { background: #4caf50; }
	}

	.task-project {
		font-size: 24rpx;
		color: #666;
	}

	.task-content {
		margin-bottom: 20rpx;
	}

	.task-description {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
	}

	.task-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.responsible-info, .time-info {
		display: flex;
		align-items: center;
	}

	.responsible-label, .time-label {
		font-size: 24rpx;
		color: #999;
		margin-right: 8rpx;
	}

	.responsible-name, .time-value {
		font-size: 24rpx;
		color: #333;

		&.overdue {
			color: #f44336;
		}
	}

	.quick-actions {
		display: flex;
		gap: 20rpx;
	}

	.action-btn {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx;
		border-radius: 12rpx;
		font-size: 26rpx;
		color: white;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}

		text {
			margin-left: 8rpx;
		}
	}

	.confirm-btn {
		background: #4caf50;
	}

	.reject-btn {
		background: #f44336;
	}

	.empty-state {
		text-align: center;
		padding: 100rpx 40rpx;
	}

	.empty-image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
		opacity: 0.6;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
	}

	.loading-state {
		padding: 40rpx;
	}
</style>

<template>
	<view class="container">
		<!-- 固定区域：不滚动的头部内容 -->
		<view class="fixed-header">
			<!-- 页面标题 -->
			<view class="page-header">
				<text class="page-title">指派任务监督</text>
				<text class="page-subtitle">实时监控指派任务的执行情况</text>
				<view class="timing-explanation">
					<text class="explanation-text">💡 超时说明：执行中任务超过7天为警告，超过14天为超时</text>
				</view>
			</view>

			<!-- 统计卡片区域 -->
			<view class="stats-section">
				<view class="stats-grid">
					<view class="stat-card" @click="filterByStatus('assigned_to_responsible')">
						<view class="stat-number assigned">{{ taskStats.assigned || 0 }}</view>
						<text class="stat-label">执行中</text>
					</view>
					<view class="stat-card" @click="filterByStatus('completed_by_responsible')">
						<view class="stat-number pending">{{ taskStats.pending || 0 }}</view>
						<text class="stat-label">待确认</text>
					</view>
					<view class="stat-card" @click="filterByStatus('final_completed')">
						<view class="stat-number completed">{{ taskStats.completed || 0 }}</view>
						<text class="stat-label">已完成</text>
					</view>
					<view class="stat-card" @click="filterByStatus('overdue')">
						<view class="stat-number overdue">{{ taskStats.overdue || 0 }}</view>
						<text class="stat-label">已超时</text>
					</view>
				</view>
			</view>

			<!-- 筛选标签 -->
			<view class="filter-tabs">
				<view
					class="filter-tab"
					:class="{ active: currentFilter === 'all' }"
					@click="setFilter('all')">
					全部
				</view>
				<view
					class="filter-tab"
					:class="{ active: currentFilter === 'assigned_to_responsible' }"
					@click="setFilter('assigned_to_responsible')">
					执行中
				</view>
				<view
					class="filter-tab"
					:class="{ active: currentFilter === 'completed_by_responsible' }"
					@click="setFilter('completed_by_responsible')">
					待确认
				</view>
				<view
					class="filter-tab"
					:class="{ active: currentFilter === 'overdue' }"
					@click="setFilter('overdue')">
					已超时
				</view>
			</view>
		</view>

		<!-- 任务列表 -->
		<scroll-view
			class="task-scroll-view"
			scroll-y
			@scrolltolower="loadMore"
			:lower-threshold="100"
			v-if="filteredTaskList.length > 0"
		>
			<view class="task-list">
				<view
					class="task-item"
					v-for="(task, index) in filteredTaskList"
					:key="task._id"
					@click="goToTaskDetail(task)">

					<view class="task-header">
						<view class="task-title-row">
							<text class="task-name">{{ task.name }}</text>
							<view class="task-status"
								:class="{
									'status-assigned': task.workflowStatus === 'assigned_to_responsible',
									'status-pending': task.workflowStatus === 'completed_by_responsible',
									'status-completed': task.workflowStatus === 'final_completed'
								}">
								{{ getStatusText(task.workflowStatus) }}
							</view>
						</view>
						<text class="task-project">{{ task.project || '未分类' }}</text>
					</view>

					<view class="task-content">
						<text class="task-description">{{ task.description || '暂无描述' }}</text>
					</view>

					<view class="task-footer">
						<view class="responsible-info">
							<text class="responsible-label">负责人：</text>
							<text class="responsible-name">{{ getResponsibleName(task.responsibleUserId) }}</text>
						</view>
						<view class="time-info">
							<text class="time-label">{{ getTimeLabel(task) }}</text>
							<text class="time-value" :class="{
								overdue: isOverdue(task),
								warning: isWarning(task)
							}">
								{{ getTimeValue(task) }}
							</text>
							<!-- 时效提醒标签 -->
							<view class="timing-badge" v-if="task.workflowStatus === 'assigned_to_responsible'">
								<text v-if="isOverdue(task)" class="timing-tag overdue-tag">超时</text>
								<text v-else-if="isWarning(task)" class="timing-tag warning-tag">警告</text>
								<text v-else class="timing-tag normal-tag">正常</text>
							</view>
						</view>
					</view>

					<!-- 快速操作按钮 -->
					<view class="quick-actions" v-if="task.workflowStatus === 'completed_by_responsible'">
						<view class="action-btn confirm-btn" @click.stop="quickConfirm(task)">
							<text>确认完成</text>
						</view>
						<view class="action-btn reject-btn" @click.stop="quickReject(task)">
							<text>退回重做</text>
						</view>
					</view>
				</view>

				<!-- 加载更多状态 -->
				<view class="load-more-state">
					<view class="loading-more" v-if="loadingMore">
						<view class="loading-icon"></view>
						<text class="loading-text">加载更多中...</text>
					</view>
					<view class="no-more" v-else-if="!pagination.hasMore && taskList.length > 0">
						<text class="no-more-text">— 没有更多数据了 —</text>
					</view>
					<view class="no-more" v-else-if="taskList.length === 0 && !loading">
						<text class="no-more-text">暂无数据</text>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 空状态 -->
		<view class="empty-state" v-else-if="!loading">
			<view class="empty-content">
				<image class="empty-image" src="/static/empty/empty_task.png" mode="aspectFit"></image>
				<text class="empty-text">{{ getEmptyText() }}</text>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<view class="custom-loading">
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 自定义弹窗 -->
		<view class="custom-modal" v-if="showModal" @click="closeModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">{{ modalData.title }}</text>
				</view>
				<view class="modal-body">
					<textarea
						class="modal-input"
						v-model="modalInput"
						:placeholder="modalData.placeholder"
						:maxlength="200"
						auto-height
						@focus="handleInputFocus"
					></textarea>
					<view class="input-counter">{{ modalInput.length }}/200</view>
				</view>
				<view class="modal-footer">
					<view class="modal-btn cancel-btn" @click="closeModal">取消</view>
					<view class="modal-btn confirm-btn" @click="confirmModal">{{ modalData.confirmText }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { formatDate } from '@/utils/date.js';

	export default {
		data() {
			return {
				taskList: [],
				taskStats: {
					assigned: 0,
					pending: 0,
					completed: 0,
					overdue: 0
				},
				currentFilter: 'all',

				// 分页数据
				pagination: {
					page: 1,
					size: 20,
					total: 0,
					hasMore: true
				},

				// 加载状态
				loading: false,
				loadingMore: false,
				loadingStatus: 'more',

				responsibleUsers: {}, // 负责人信息缓存
				currentUserId: '', // 当前用户ID
				userRole: [], // 用户角色

				// 自定义弹窗数据
				showModal: false,
				modalInput: '',
				modalData: {
					title: '',
					label: '',
					placeholder: '',
					confirmText: '确认',
					callback: null,
					taskId: null
				},
				// 跨设备更新相关
				lastRefreshTime: null
			}
		},
		computed: {
			// 由于使用服务端筛选，直接返回任务列表
			filteredTaskList() {
				return this.taskList;
			}
		},
		async onLoad() {
			await this.getUserInfo();
			if (this.checkPermission()) {
				this.loadTaskData();
				// 标记已经加载过数据
				this._hasLoadedData = true;
			}
		},
		onShow() {
			// 只有在非首次加载时才刷新数据
			if (this._hasLoadedData) {
				console.log('🔄 页面显示，刷新数据');
				this.loadTaskData();
			}

			// 监听任务状态更新事件
			uni.$on('feedback-updated', this.handleTaskUpdate);
			uni.$on('ucenter-need-refresh', this.handleTaskUpdate);
			// 监听跨设备更新事件
			uni.$on('cross-device-update-detected', this.handleCrossDeviceUpdate);
		},

		onHide() {
			// 页面隐藏时移除事件监听
			uni.$off('feedback-updated', this.handleTaskUpdate);
			uni.$off('ucenter-need-refresh', this.handleTaskUpdate);
			uni.$off('cross-device-update-detected', this.handleCrossDeviceUpdate);
		},
		onPullDownRefresh() {
			// 重置分页
			this.pagination.page = 1;
			this.loadTaskData().then(() => {
				uni.stopPullDownRefresh();
			});
		},
		methods: {
			// 权限检查
			checkPermission() {
				// 检查是否登录
				if (!this.currentUserId) {
					uni.showModal({
						title: '请先登录',
						content: '需要登录后才能访问此页面',
						showCancel: false,
						success: () => {
							uni.navigateBack();
						}
					});
					return false;
				}

				// 检查是否有厂长或管理员权限
				const hasGMPermission = this.userRole.some(role => ['GM', 'admin'].includes(role));
				if (!hasGMPermission) {
					uni.showModal({
						title: '权限不足',
						content: '只有厂长才能访问此页面',
						showCancel: false,
						success: () => {
							uni.navigateBack();
						}
					});
					return false;
				}

				return true;
			},

			// 获取用户信息
			async getUserInfo() {
				try {
					// 获取当前用户ID
					const userInfo = uniCloud.getCurrentUserInfo();
					this.currentUserId = userInfo.uid;

					if (!this.currentUserId) {
						return;
					}

					// 获取用户角色
					const db = uniCloud.database();
					const { result } = await db.collection('uni-id-users')
						.where("'_id' == $cloudEnv_uid")
						.field('role, _id')
						.get();

					if (result.data && result.data.length > 0) {
						this.userRole = result.data[0].role || [];
					} else {
						this.userRole = [];
					}
				} catch (error) {
					console.error('❌ 获取用户信息失败:', error);
					this.currentUserId = '';
					this.userRole = [];
				}
			},

			// 加载任务数据
			async loadTaskData(silent = false, loadMore = false) {
				// 设置加载状态
				if (loadMore) {
					this.loadingMore = true;
					this.loadingStatus = 'loading';
				} else if (!silent) {
					this.loading = true;
					this.loadingStatus = 'loading';
				}

				// 处理页码：如果是加载更多，页码递增；如果是刷新，重置页码
				const currentPage = loadMore ? this.pagination.page + 1 : 1;

				// 只有在非静默刷新时才更新页码
				if (!silent) {
					this.pagination.page = currentPage;
				}

				// 保留这个日志，用于跨设备更新调试
				console.log('开始加载厂长监督任务数据...',
					silent ? '(静默刷新)' : loadMore ? `(加载更多，第${currentPage}页)` : '(首次加载)');

				try {
					const res = await uniCloud.callFunction({
						name: 'feedback-list',
						data: {
							action: 'getGMSupervisionTasks',
							page: currentPage, // 使用计算后的页码
							size: this.pagination.size,
							filter: this.currentFilter
						}
					});

					if (res.result && res.result.code === 0) {
						// 检查是否返回了空数据但hasMore为true
						if (res.result.data.list?.length === 0 && res.result.data.pagination?.hasMore) {
							console.warn('⚠️ 服务器返回空数据但hasMore为true，强制设置hasMore为false');
							res.result.data.pagination.hasMore = false;
						}

						// 处理任务列表数据
						if (loadMore) {
							// 检查是否有新数据
							if (res.result.data.list?.length > 0) {
								// 加载更多时，追加数据
								this.taskList = [...this.taskList, ...(res.result.data.list || [])];
								// 更新页码为当前请求的页码
								this.pagination.page = currentPage;
								console.log('✅ 加载更多成功，页码更新为:', currentPage);
							} else {
								// 没有新数据，强制设置hasMore为false
								console.log('⚠️ 加载更多返回空数据，强制设置hasMore为false');
								this.pagination.hasMore = false;
							}
						} else {
							// 首次加载或刷新时，替换数据
							this.taskList = res.result.data.list || [];
							// 重置页码
							if (!silent) {
								this.pagination.page = 1;
							}
						}

						// 更新分页信息
						if (res.result.data.pagination) {
							this.pagination.total = res.result.data.pagination.total;
							this.pagination.hasMore = res.result.data.pagination.hasMore;
						}

						// 只在首页或刷新时更新统计信息
						if (!loadMore && res.result.data.stats) {
							this.taskStats = res.result.data.stats;
						}

						// 更新负责人信息
						if (res.result.data.responsibleUsers) {
							// 合并负责人信息，保留已有的
							this.responsibleUsers = {
								...this.responsibleUsers,
								...res.result.data.responsibleUsers
							};
						}
					} else {
						console.error('云函数返回错误:', res.result);
						throw new Error(res.result?.message || '获取任务数据失败');
					}
				} catch (error) {
					console.error('加载任务数据失败:', error);
					if (!silent) {
						uni.showToast({
							title: error.message || '加载失败',
							icon: 'error',
							duration: 3000
						});
					}
				} finally {
					// 重置加载状态
					if (loadMore) {
						this.loadingMore = false;
					} else if (!silent) {
						this.loading = false;
					}

					// 设置加载状态提示
					this.loadingStatus = this.pagination.hasMore ? 'more' : 'noMore';
				}
			},

			// 设置筛选条件
			setFilter(filter) {
				if (this.currentFilter === filter) return; // 避免重复筛选

				this.currentFilter = filter;
				this.pagination.page = 1; // 重置页码
				this.loadTaskData(); // 重新加载数据
			},

			// 按状态筛选
			filterByStatus(status) {
				this.setFilter(status);
			},

			// 获取状态文本
			getStatusText(status) {
				const textMap = {
					'assigned_to_responsible': '执行中',
					'completed_by_responsible': '待确认',
					'final_completed': '已完成'
				};
				return textMap[status] || '未知状态';
			},

			// 获取负责人姓名
			getResponsibleName(userId) {
				const user = this.responsibleUsers[userId];
				return user ? (user.nickname || user.username || '未知') : '未指派';
			},

			// 获取时间标签
			getTimeLabel(task) {
				if (task.workflowStatus === 'assigned_to_responsible') {
					return '指派时间：';
				} else if (task.workflowStatus === 'completed_by_responsible') {
					return '完成时间：';
				} else if (task.workflowStatus === 'final_completed') {
					return '确认时间：';
				}
				return '创建时间：';
			},

			// 获取时间值
			getTimeValue(task) {
				let timestamp;
				if (task.workflowStatus === 'assigned_to_responsible') {
					timestamp = task.assignedTime;
				} else if (task.workflowStatus === 'completed_by_responsible') {
					timestamp = task.completedByResponsibleTime;
				} else if (task.workflowStatus === 'final_completed') {
					timestamp = task.finalCompletedTime;
				} else {
					timestamp = task.createTime;
				}
				return timestamp ? formatDate(timestamp, 'MM-DD HH:mm') : '未知';
			},

			// 判断是否超时（与问题反馈系统保持一致）
			isOverdue(task) {
				if (task.workflowStatus !== 'assigned_to_responsible') return false;
				if (!task.assignedTime) return false;

				// 与问题反馈系统保持一致：超过14天视为超时
				const fourteenDays = 14 * 24 * 60 * 60 * 1000;
				return Date.now() - task.assignedTime > fourteenDays;
			},

			// 判断是否为警告状态（7-14天）
			isWarning(task) {
				if (task.workflowStatus !== 'assigned_to_responsible') return false;
				if (!task.assignedTime) return false;

				const sevenDays = 7 * 24 * 60 * 60 * 1000;
				const fourteenDays = 14 * 24 * 60 * 60 * 1000;
				const timePassed = Date.now() - task.assignedTime;

				return timePassed > sevenDays && timePassed <= fourteenDays;
			},

			// 处理任务更新事件
			handleTaskUpdate() {
				this.loadTaskData();
			},

			// 处理跨设备更新事件
			handleCrossDeviceUpdate(data) {
				if (data.silent && this.currentUserId) {
					// 智能判断是否需要刷新
					const shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);
					if (shouldRefresh) {
						// 保留这个日志，用于跨设备更新调试
						console.log('🏭 厂长监督页面收到跨设备更新通知，静默刷新数据');
						// 静默刷新数据，不显示提示
						this.silentRefreshData();
					}
				}
			},

			// 智能判断是否需要刷新
			shouldRefreshOnCrossDeviceUpdate(data) {
				// 检查更新类型是否与当前页面相关
				const relevantTypes = [
					'workflow_status_changed',
					'feedback_submitted',
					'gm_final_confirm',
					'task_assigned'
				];

				// 如果有相关的更新类型，则需要刷新
				const hasRelevantUpdate = data.updateTypes &&
					data.updateTypes.some(type => relevantTypes.includes(type));

				// 避免频繁刷新：如果距离上次刷新不到10秒，则跳过
				const now = Date.now();
				if (this.lastRefreshTime && (now - this.lastRefreshTime) < 10000) {
					return false;
				}

				return hasRelevantUpdate;
			},

			// 静默刷新数据
			async silentRefreshData() {
				try {
					// 记录刷新时间
					this.lastRefreshTime = Date.now();

					// 保存当前页码
					const currentPage = this.pagination.page;

					// 静默刷新，不显示loading，不改变页码
					await this.loadTaskData(true);

					// 恢复页码（确保静默刷新不影响当前分页状态）
					this.pagination.page = currentPage;
				} catch (error) {
					console.error('静默刷新失败:', error);
				}
			},

			// 加载更多数据
			async loadMore() {
				// 如果没有更多数据或正在加载中，则不执行
				if (!this.pagination.hasMore || this.loadingMore || this.loading) {
					// 只在第一次阻止时记录日志，避免重复日志
					if (!this._loadMoreBlocked) {
						console.log('📄 已到达数据末尾，没有更多数据了');
						this._loadMoreBlocked = true;
					}
					return;
				}

				// 重置阻止标记
				this._loadMoreBlocked = false;
				console.log('🔄 开始加载更多 - 当前页:', this.pagination.page, '任务数:', this.taskList.length);

				// 加载更多数据（页码在loadTaskData中处理）
				await this.loadTaskData(false, true);
			},

			// 跳转到任务详情
			goToTaskDetail(task) {
				uni.navigateTo({
					url: `/pages/feedback_pkg/examine?id=${task._id}`
				});
			},

			// 快速确认完成 - 需要输入确认理由
			quickConfirm(task) {
				this.showCustomModal({
					title: '确认完成',
					placeholder: '请输入确认意见。',
					confirmText: '确认完成',
					callback: (reason) => {
						this.confirmTask(task._id, reason);
					}
				});
			},

			// 快速退回重做 - 需要输入退回理由
			quickReject(task) {
				this.showCustomModal({
					title: '退回重做',
					placeholder: '请详细说明退回原因。',
					confirmText: '退回重做',
					callback: (reason) => {
						this.rejectTask(task._id, reason);
					}
				});
			},

			// 确认任务完成
			async confirmTask(taskId, reason) {
				try {
					uni.showLoading({ title: '确认中...' });

					const res = await uniCloud.callFunction({
						name: 'feedback-workflow',
						data: {
							action: 'gm_final_confirm',
							id: taskId,
							reason: reason
						}
					});

					if (res.result && res.result.code === 0) {
						uni.showToast({
							title: '确认成功',
							icon: 'success'
						});

						// 触发刷新事件，通知其他页面更新
						uni.$emit('feedback-updated');
						uni.$emit('ucenter-need-refresh', { id: taskId });

						// 重置分页并刷新数据
						this.pagination.page = 1;
						this.loadTaskData();
					} else {
						throw new Error(res.result?.message || '确认失败');
					}
				} catch (error) {
					console.error('确认任务失败:', error);
					uni.showToast({
						title: error.message || '确认失败',
						icon: 'error'
					});
				} finally {
					uni.hideLoading();
				}
			},

			// 退回任务重做
			async rejectTask(taskId, reason) {
				try {
					uni.showLoading({ title: '退回中...' });

					const res = await uniCloud.callFunction({
						name: 'feedback-workflow',
						data: {
							action: 'updateWorkflowStatus',
							id: taskId,
							workflowStatus: 'assigned_to_responsible',
							rejectReason: reason
						}
					});

					if (res.result && res.result.code === 0) {
						uni.showToast({
							title: '已退回重做',
							icon: 'success'
						});

						// 触发刷新事件，通知其他页面更新
						uni.$emit('feedback-updated');
						uni.$emit('ucenter-need-refresh', { id: taskId });

						// 重置分页并刷新数据
						this.pagination.page = 1;
						this.loadTaskData();
					} else {
						throw new Error(res.result?.message || '退回失败');
					}
				} catch (error) {
					console.error('退回任务失败:', error);
					uni.showToast({
						title: error.message || '退回失败',
						icon: 'error'
					});
				} finally {
					uni.hideLoading();
				}
			},

			// 获取空状态文本
			getEmptyText() {
				const textMap = {
					'all': '暂无指派任务',
					'assigned_to_responsible': '暂无执行中的任务',
					'completed_by_responsible': '暂无待确认的任务',
					'overdue': '暂无超时任务'
				};
				return textMap[this.currentFilter] || '暂无数据';
			},

			// 显示自定义弹窗
			showCustomModal(options) {
				this.modalData = {
					title: options.title || '',
					label: options.label || '',
					placeholder: options.placeholder || '',
					confirmText: options.confirmText || '确认',
					callback: options.callback || null
				};
				this.modalInput = '';
				this.showModal = true;
			},

			// 关闭弹窗
			closeModal() {
				this.showModal = false;
				this.modalInput = '';
				this.modalData = {
					title: '',
					label: '',
					placeholder: '',
					confirmText: '确认',
					callback: null
				};
			},

			// 确认弹窗
			confirmModal() {
				const reason = this.modalInput.trim();
				if (!reason) {
					uni.showToast({
						title: '请输入内容',
						icon: 'none'
					});
					return;
				}

				if (this.modalData.callback) {
					this.modalData.callback(reason);
				}
				this.closeModal();
			},

			// 输入框获得焦点时清空placeholder样式的内容
			handleInputFocus() {
				// 这里可以添加焦点处理逻辑
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f8f9fc;
	}

	/* 固定头部区域 */
	.fixed-header {
		flex-shrink: 0; /* 不允许收缩 */
		padding: 20rpx;
		background-color: #f8f9fc;
	}

	.page-header {
		background: linear-gradient(135deg, #38bdf8 0%, #0ea5e9 70%, #3b82f6 100%); /* 更轻盈的蓝色渐变 - 从浅到深 */
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		color: white;
		box-shadow: 0 6rpx 24rpx rgba(56, 189, 248, 0.25); /* 更轻的阴影 */
	}

	.page-title {
		font-size: 36rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}

	.page-subtitle {
		font-size: 28rpx;
		opacity: 0.9;
		margin-bottom: 20rpx;
	}

	.timing-explanation {
		background: rgba(255, 255, 255, 0.15);
		border-radius: 12rpx;
		padding: 16rpx 20rpx;
		margin-top: 20rpx;
	}

	.explanation-text {
		font-size: 24rpx;
		opacity: 0.95;
		line-height: 1.4;
	}

	.stats-section {
		margin-bottom: 30rpx;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20rpx;
	}

	.stat-card {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}

	.stat-number {
		font-size: 48rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;

		&.assigned { color: #38bdf8; } /* 浅蓝色 - 执行中 */
		&.pending { color: #f59e0b; } /* 橙色 - 待确认 */
		&.completed { color: #059669; } /* 绿色 - 已完成 */
		&.overdue { color: #dc2626; } /* 红色 - 超时 */
	}

	.stat-label {
		font-size: 24rpx;
		color: #666;
	}

	.filter-tabs {
		display: flex;
		background: white;
		border-radius: 16rpx;
		padding: 8rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	}

	.filter-tab {
		flex: 1;
		text-align: center;
		padding: 20rpx 10rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		color: #666;
		transition: all 0.3s ease;

		&.active {
			background: #3b82f6; /* 蓝色 - 水务主题 */
			color: white;
		}
	}

	.task-list {
		margin-bottom: 30rpx;
	}

	.task-item {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;

		&:active {
			transform: translateY(2rpx);
		}
	}

	.task-header {
		margin-bottom: 20rpx;
	}

	.task-title-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.task-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		flex: 1;
		margin-right: 20rpx;
	}

	.task-status {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		color: white;

		&.status-assigned { background: #38bdf8; } /* 浅蓝色 - 执行中 */
		&.status-pending { background: #f59e0b; } /* 橙色 - 待确认 */
		&.status-completed { background: #059669; } /* 绿色 - 已完成 */
	}

	.task-project {
		font-size: 24rpx;
		color: #666;
	}

	.task-content {
		margin-bottom: 20rpx;
	}

	.task-description {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
	}

	.task-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.responsible-info, .time-info {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 8rpx;
	}

	.responsible-label, .time-label {
		font-size: 24rpx;
		color: #999;
		margin-right: 8rpx;
	}

	.responsible-name, .time-value {
		font-size: 24rpx;
		color: #333;

		&.overdue {
			color: #dc2626; /* 红色 - 超时 */
			font-weight: bold;
		}

		&.warning {
			color: #f59e0b; /* 橙色 - 警告 */
			font-weight: bold;
		}
	}

	.timing-badge {
		margin-left: 8rpx;
	}

	.timing-tag {
		font-size: 20rpx;
		padding: 4rpx 8rpx;
		border-radius: 8rpx;
		color: white;
		font-weight: bold;
	}

	.overdue-tag {
		background: #dc2626; /* 红色 - 超时 */
	}

	.warning-tag {
		background: #f59e0b; /* 橙色 - 警告 */
	}

	.normal-tag {
		background: #059669; /* 绿色 - 正常 */
	}

	.quick-actions {
		display: flex;
		gap: 20rpx;
	}

	.action-btn {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 24rpx 20rpx;
		border-radius: 12rpx;
		color: white;
		transition: all 0.3s ease;
		font-weight: 500;

		&:active {
			transform: scale(0.95);
			opacity: 0.8;
		}

		text {
			font-size: 26rpx;
			color: white; /* 确保文字是白色 */
		}
	}

	.confirm-btn {
		background-color: #4caf50; /* 纯绿色 - 确认完成 */
	}

	.reject-btn {
		background-color: #f44336; /* 纯红色 - 退回重做 */
	}

	.empty-state {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 100rpx 40rpx;
		min-height: 400rpx;
	}

	.empty-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: center;
	}

	.empty-image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
		opacity: 0.6;
		display: block;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
		display: block;
		text-align: center;
	}

	.loading-state {
		padding: 40rpx;
	}

	.custom-loading {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #666;
	}

	/* 滚动视图样式 */
	.task-scroll-view {
		flex: 1; /* 占用剩余空间 */
		overflow: hidden; /* 防止内容溢出 */
		padding: 0 20rpx; /* 左右内边距 */
	}

	/* 加载更多状态样式 */
	.load-more-state {
		padding: 40rpx 20rpx;
		text-align: center;
	}

	.loading-more {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 20rpx;
	}

	.loading-icon {
		width: 32rpx;
		height: 32rpx;
		border: 3rpx solid #f3f3f3;
		border-top: 3rpx solid #3b82f6;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.no-more {
		padding: 20rpx;
		text-align: center;
	}

	.no-more-text {
		font-size: 26rpx;
		color: #999;
	}

	/* 自定义弹窗样式 */
	.custom-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	.modal-content {
		width: 80%;
		background-color: #fff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
		animation: modal-in 0.3s ease-out;
	}

	@keyframes modal-in {
		from {
			opacity: 0;
			transform: translateY(20rpx) scale(0.95);
		}
		to {
			opacity: 1;
			transform: translateY(0) scale(1);
		}
	}

	.modal-header {
		padding: 30rpx;
		text-align: center;
		border-bottom: 1px solid #f0f0f0;
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333; /* 深灰色 - 简洁清晰 */
	}

	.modal-body {
		padding: 30rpx;
	}

	.modal-label {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	.modal-input {
		width: 100%;
		min-height: 160rpx;
		border: 1px solid #e0e0e0;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		color: #333;
		background-color: #f9f9f9;
		box-sizing: border-box;
		transition: border-color 0.3s ease;

		&:focus {
			border-color: #3b82f6;
			background-color: #ffffff;
		}
	}

	.input-counter {
		font-size: 24rpx;
		color: #999;
		text-align: right;
		margin-top: 10rpx;
	}

	.modal-footer {
		display: flex;
		padding: 20rpx;
		gap: 20rpx;
	}

	.modal-btn {
		flex: 1;
		padding: 24rpx;
		text-align: center;
		font-size: 30rpx;
		border-radius: 8rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}

	.cancel-btn {
		color: #666;
		background-color: #f5f5f5;
		border: 1px solid #e0e0e0;
	}

	.confirm-btn {
		color: white;
		background-color: #4caf50; /* 绿色背景 - 与确认按钮保持一致 */
		font-weight: bold;
	}
</style>

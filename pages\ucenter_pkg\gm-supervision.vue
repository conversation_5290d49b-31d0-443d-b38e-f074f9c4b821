<template>
	<view class="container">
		<!-- 页面标题 -->
		<view class="page-header">
			<text class="page-title">指派任务监督</text>
			<text class="page-subtitle">实时监控指派任务的执行情况</text>
			<view class="timing-explanation">
				<text class="explanation-text">💡 超时说明：执行中任务超过7天为警告，超过14天为超时</text>
			</view>
		</view>

		<!-- 统计卡片区域 -->
		<view class="stats-section">
			<view class="stats-grid">
				<view class="stat-card" @click="filterByStatus('assigned_to_responsible')">
					<view class="stat-number assigned">{{ taskStats.assigned || 0 }}</view>
					<text class="stat-label">执行中</text>
				</view>
				<view class="stat-card" @click="filterByStatus('completed_by_responsible')">
					<view class="stat-number pending">{{ taskStats.pending || 0 }}</view>
					<text class="stat-label">待确认</text>
				</view>
				<view class="stat-card" @click="filterByStatus('final_completed')">
					<view class="stat-number completed">{{ taskStats.completed || 0 }}</view>
					<text class="stat-label">已完成</text>
				</view>
				<view class="stat-card" @click="filterByStatus('overdue')">
					<view class="stat-number overdue">{{ taskStats.overdue || 0 }}</view>
					<text class="stat-label">已超时</text>
				</view>
			</view>
		</view>

		<!-- 筛选标签 -->
		<view class="filter-tabs">
			<view 
				class="filter-tab" 
				:class="{ active: currentFilter === 'all' }"
				@click="setFilter('all')">
				全部
			</view>
			<view 
				class="filter-tab" 
				:class="{ active: currentFilter === 'assigned_to_responsible' }"
				@click="setFilter('assigned_to_responsible')">
				执行中
			</view>
			<view 
				class="filter-tab" 
				:class="{ active: currentFilter === 'completed_by_responsible' }"
				@click="setFilter('completed_by_responsible')">
				待确认
			</view>
			<view 
				class="filter-tab" 
				:class="{ active: currentFilter === 'overdue' }"
				@click="setFilter('overdue')">
				已超时
			</view>
		</view>

		<!-- 任务列表 -->
		<view class="task-list" v-if="filteredTaskList.length > 0">
			<view 
				class="task-item" 
				v-for="(task, index) in filteredTaskList" 
				:key="task._id"
				@click="goToTaskDetail(task)">
				
				<view class="task-header">
					<view class="task-title-row">
						<text class="task-name">{{ task.name }}</text>
						<view class="task-status"
							:class="{
								'status-assigned': task.workflowStatus === 'assigned_to_responsible',
								'status-pending': task.workflowStatus === 'completed_by_responsible',
								'status-completed': task.workflowStatus === 'final_completed'
							}">
							{{ getStatusText(task.workflowStatus) }}
						</view>
					</view>
					<text class="task-project">{{ task.project || '未分类' }}</text>
				</view>

				<view class="task-content">
					<text class="task-description">{{ task.description || '暂无描述' }}</text>
				</view>

				<view class="task-footer">
					<view class="responsible-info">
						<text class="responsible-label">负责人：</text>
						<text class="responsible-name">{{ getResponsibleName(task.responsibleUserId) }}</text>
					</view>
					<view class="time-info">
						<text class="time-label">{{ getTimeLabel(task) }}</text>
						<text class="time-value" :class="{
							overdue: isOverdue(task),
							warning: isWarning(task)
						}">
							{{ getTimeValue(task) }}
						</text>
						<!-- 时效提醒标签 -->
						<view class="timing-badge" v-if="task.workflowStatus === 'assigned_to_responsible'">
							<text v-if="isOverdue(task)" class="timing-tag overdue-tag">超时</text>
							<text v-else-if="isWarning(task)" class="timing-tag warning-tag">警告</text>
							<text v-else class="timing-tag normal-tag">正常</text>
						</view>
					</view>
				</view>

				<!-- 快速操作按钮 -->
				<view class="quick-actions" v-if="task.workflowStatus === 'completed_by_responsible'">
					<view class="action-btn confirm-btn" @click.stop="quickConfirm(task)">
						<uni-icons type="checkmarkempty" size="16" color="#FFFFFF"></uni-icons>
						<text>确认完成</text>
					</view>
					<view class="action-btn reject-btn" @click.stop="quickReject(task)">
						<uni-icons type="closeempty" size="16" color="#FFFFFF"></uni-icons>
						<text>退回重做</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else>
			<view class="empty-content">
				<image class="empty-image" src="/static/empty/empty_task.png" mode="aspectFit"></image>
				<text class="empty-text">{{ getEmptyText() }}</text>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<view class="custom-loading">
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 自定义弹窗 -->
		<view class="custom-modal" v-if="showModal" @click="closeModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">{{ modalData.title }}</text>
				</view>
				<view class="modal-body">
					<text class="modal-label">{{ modalData.label }}</text>
					<textarea
						class="modal-input"
						v-model="modalInput"
						:placeholder="modalData.placeholder"
						:maxlength="200"
						auto-height
						@focus="handleInputFocus"
					></textarea>
					<view class="input-counter">{{ modalInput.length }}/200</view>
				</view>
				<view class="modal-footer">
					<view class="modal-btn cancel-btn" @click="closeModal">取消</view>
					<view class="modal-btn confirm-btn" @click="confirmModal">{{ modalData.confirmText }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { formatDate } from '@/utils/date.js';

	export default {
		data() {
			return {
				taskList: [],
				taskStats: {
					assigned: 0,
					pending: 0,
					completed: 0,
					overdue: 0
				},
				currentFilter: 'all',
				loading: false,
				loadingStatus: 'more',
				responsibleUsers: {}, // 负责人信息缓存
				currentUserId: '', // 当前用户ID
				userRole: [], // 用户角色
				// 自定义弹窗数据
				showModal: false,
				modalInput: '',
				modalData: {
					title: '',
					label: '',
					placeholder: '',
					confirmText: '确认',
					callback: null,
					taskId: null
				}
			}
		},
		computed: {
			filteredTaskList() {
				if (this.currentFilter === 'all') {
					return this.taskList;
				}
				if (this.currentFilter === 'overdue') {
					return this.taskList.filter(task => this.isOverdue(task));
				}
				return this.taskList.filter(task => task.workflowStatus === this.currentFilter);
			}
		},
		computed: {
			filteredTaskList() {
				if (this.currentFilter === 'all') {
					return this.taskList;
				}
				if (this.currentFilter === 'overdue') {
					return this.taskList.filter(task => this.isOverdue(task));
				}
				return this.taskList.filter(task => task.workflowStatus === this.currentFilter);
			}
		},
		async onLoad() {
			console.log('📱 厂长监督页面加载');
			await this.getUserInfo();
			if (this.checkPermission()) {
				this.loadTaskData();
			}
		},
		onShow() {
			// 页面显示时刷新数据
			this.loadTaskData();

			// 监听任务状态更新事件
			uni.$on('feedback-updated', this.handleTaskUpdate);
			uni.$on('ucenter-need-refresh', this.handleTaskUpdate);
		},

		onHide() {
			// 页面隐藏时移除事件监听
			uni.$off('feedback-updated', this.handleTaskUpdate);
			uni.$off('ucenter-need-refresh', this.handleTaskUpdate);
		},
		onPullDownRefresh() {
			this.loadTaskData().then(() => {
				uni.stopPullDownRefresh();
			});
		},
		methods: {
			// 权限检查
			checkPermission() {
				console.log('📋 权限检查 - 当前用户信息:', {
					currentUserId: this.currentUserId,
					userRole: this.userRole
				});

				// 检查是否登录
				if (!this.currentUserId) {
					uni.showModal({
						title: '请先登录',
						content: '需要登录后才能访问此页面',
						showCancel: false,
						success: () => {
							uni.navigateBack();
						}
					});
					return false;
				}

				// 检查是否有厂长或管理员权限
				const hasGMPermission = this.userRole.some(role => ['GM', 'admin'].includes(role));
				if (!hasGMPermission) {
					uni.showModal({
						title: '权限不足',
						content: '只有厂长才能访问此页面',
						showCancel: false,
						success: () => {
							uni.navigateBack();
						}
					});
					return false;
				}

				console.log('✅ 权限检查通过');
				return true;
			},

			// 获取用户信息
			async getUserInfo() {
				try {
					// 获取当前用户ID
					const userInfo = uniCloud.getCurrentUserInfo();
					this.currentUserId = userInfo.uid;

					console.log('📋 获取到用户ID:', this.currentUserId);

					if (!this.currentUserId) {
						console.log('❌ 用户未登录');
						return;
					}

					// 获取用户角色
					const db = uniCloud.database();
					const { result } = await db.collection('uni-id-users')
						.where("'_id' == $cloudEnv_uid")
						.field('role, _id')
						.get();

					if (result.data && result.data.length > 0) {
						this.userRole = result.data[0].role || [];
						console.log('📋 获取到用户角色:', this.userRole);
					} else {
						this.userRole = [];
						console.log('❌ 未找到用户角色信息');
					}
				} catch (error) {
					console.error('❌ 获取用户信息失败:', error);
					this.currentUserId = '';
					this.userRole = [];
				}
			},

			// 加载任务数据
			async loadTaskData() {
				this.loading = true;
				this.loadingStatus = 'loading';

				console.log('开始加载厂长监督任务数据...');

				try {
					const res = await uniCloud.callFunction({
						name: 'feedback-list',
						data: {
							action: 'getGMSupervisionTasks'
						}
					});

					console.log('云函数调用结果:', res);

					if (res.result && res.result.code === 0) {
						this.taskList = res.result.data.list || [];
						this.taskStats = res.result.data.stats || this.taskStats;
						this.responsibleUsers = res.result.data.responsibleUsers || {};

						console.log('任务数据加载成功:', {
							taskCount: this.taskList.length,
							stats: this.taskStats,
							responsibleUserCount: Object.keys(this.responsibleUsers).length
						});
					} else {
						console.error('云函数返回错误:', res.result);
						throw new Error(res.result?.message || '获取任务数据失败');
					}
				} catch (error) {
					console.error('加载任务数据失败:', error);
					uni.showToast({
						title: error.message || '加载失败',
						icon: 'error',
						duration: 3000
					});
				} finally {
					this.loading = false;
					this.loadingStatus = 'more';
				}
			},

			// 设置筛选条件
			setFilter(filter) {
				this.currentFilter = filter;
			},

			// 按状态筛选
			filterByStatus(status) {
				this.setFilter(status);
			},

			// 获取状态文本
			getStatusText(status) {
				const textMap = {
					'assigned_to_responsible': '执行中',
					'completed_by_responsible': '待确认',
					'final_completed': '已完成'
				};
				return textMap[status] || '未知状态';
			},

			// 获取负责人姓名
			getResponsibleName(userId) {
				const user = this.responsibleUsers[userId];
				return user ? (user.nickname || user.username || '未知') : '未指派';
			},

			// 获取时间标签
			getTimeLabel(task) {
				if (task.workflowStatus === 'assigned_to_responsible') {
					return '指派时间：';
				} else if (task.workflowStatus === 'completed_by_responsible') {
					return '完成时间：';
				} else if (task.workflowStatus === 'final_completed') {
					return '确认时间：';
				}
				return '创建时间：';
			},

			// 获取时间值
			getTimeValue(task) {
				let timestamp;
				if (task.workflowStatus === 'assigned_to_responsible') {
					timestamp = task.assignedTime;
				} else if (task.workflowStatus === 'completed_by_responsible') {
					timestamp = task.completedByResponsibleTime;
				} else if (task.workflowStatus === 'final_completed') {
					timestamp = task.finalCompletedTime;
				} else {
					timestamp = task.createTime;
				}
				return timestamp ? formatDate(timestamp, 'MM-DD HH:mm') : '未知';
			},

			// 判断是否超时（与问题反馈系统保持一致）
			isOverdue(task) {
				if (task.workflowStatus !== 'assigned_to_responsible') return false;
				if (!task.assignedTime) return false;

				// 与问题反馈系统保持一致：超过14天视为超时
				const fourteenDays = 14 * 24 * 60 * 60 * 1000;
				return Date.now() - task.assignedTime > fourteenDays;
			},

			// 判断是否为警告状态（7-14天）
			isWarning(task) {
				if (task.workflowStatus !== 'assigned_to_responsible') return false;
				if (!task.assignedTime) return false;

				const sevenDays = 7 * 24 * 60 * 60 * 1000;
				const fourteenDays = 14 * 24 * 60 * 60 * 1000;
				const timePassed = Date.now() - task.assignedTime;

				return timePassed > sevenDays && timePassed <= fourteenDays;
			},

			// 处理任务更新事件
			handleTaskUpdate() {
				console.log('📱 收到任务更新事件，刷新厂长监督数据');
				this.loadTaskData();
			},

			// 跳转到任务详情
			goToTaskDetail(task) {
				uni.navigateTo({
					url: `/pages/feedback_pkg/examine?id=${task._id}`
				});
			},

			// 快速确认完成 - 需要输入确认理由
			quickConfirm(task) {
				this.showCustomModal({
					title: '确认完成',
					label: '请输入确认意见：',
					placeholder: '请输入确认意见，如：任务完成质量良好',
					confirmText: '确认完成',
					callback: (reason) => {
						this.confirmTask(task._id, reason);
					}
				});
			},

			// 快速退回重做 - 需要输入退回理由
			quickReject(task) {
				this.showCustomModal({
					title: '退回重做',
					label: '请输入退回理由：',
					placeholder: '请详细说明退回原因，如：完成质量不达标，需重新处理',
					confirmText: '退回重做',
					callback: (reason) => {
						this.rejectTask(task._id, reason);
					}
				});
			},

			// 确认任务完成
			async confirmTask(taskId, reason) {
				try {
					uni.showLoading({ title: '确认中...' });

					const res = await uniCloud.callFunction({
						name: 'feedback-workflow',
						data: {
							action: 'gm_final_confirm',
							id: taskId,
							reason: reason
						}
					});

					if (res.result && res.result.code === 0) {
						uni.showToast({
							title: '确认成功',
							icon: 'success'
						});

						// 触发刷新事件，通知其他页面更新
						uni.$emit('feedback-updated');
						uni.$emit('ucenter-need-refresh', { id: taskId });

						// 刷新当前页面数据
						this.loadTaskData();
					} else {
						throw new Error(res.result?.message || '确认失败');
					}
				} catch (error) {
					console.error('确认任务失败:', error);
					uni.showToast({
						title: error.message || '确认失败',
						icon: 'error'
					});
				} finally {
					uni.hideLoading();
				}
			},

			// 退回任务重做
			async rejectTask(taskId, reason) {
				try {
					uni.showLoading({ title: '退回中...' });

					const res = await uniCloud.callFunction({
						name: 'feedback-workflow',
						data: {
							action: 'updateWorkflowStatus',
							id: taskId,
							workflowStatus: 'assigned_to_responsible',
							rejectReason: reason
						}
					});

					if (res.result && res.result.code === 0) {
						uni.showToast({
							title: '已退回重做',
							icon: 'success'
						});

						// 触发刷新事件，通知其他页面更新
						uni.$emit('feedback-updated');
						uni.$emit('ucenter-need-refresh', { id: taskId });

						// 刷新当前页面数据
						this.loadTaskData();
					} else {
						throw new Error(res.result?.message || '退回失败');
					}
				} catch (error) {
					console.error('退回任务失败:', error);
					uni.showToast({
						title: error.message || '退回失败',
						icon: 'error'
					});
				} finally {
					uni.hideLoading();
				}
			},

			// 获取空状态文本
			getEmptyText() {
				const textMap = {
					'all': '暂无指派任务',
					'assigned_to_responsible': '暂无执行中的任务',
					'completed_by_responsible': '暂无待确认的任务',
					'overdue': '暂无超时任务'
				};
				return textMap[this.currentFilter] || '暂无数据';
			},

			// 显示自定义弹窗
			showCustomModal(options) {
				this.modalData = {
					title: options.title || '',
					label: options.label || '',
					placeholder: options.placeholder || '',
					confirmText: options.confirmText || '确认',
					callback: options.callback || null
				};
				this.modalInput = '';
				this.showModal = true;
			},

			// 关闭弹窗
			closeModal() {
				this.showModal = false;
				this.modalInput = '';
				this.modalData = {
					title: '',
					label: '',
					placeholder: '',
					confirmText: '确认',
					callback: null
				};
			},

			// 确认弹窗
			confirmModal() {
				const reason = this.modalInput.trim();
				if (!reason) {
					uni.showToast({
						title: '请输入内容',
						icon: 'none'
					});
					return;
				}

				if (this.modalData.callback) {
					this.modalData.callback(reason);
				}
				this.closeModal();
			},

			// 输入框获得焦点时清空placeholder样式的内容
			handleInputFocus() {
				// 这里可以添加焦点处理逻辑
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #f8f9fc;
		padding: 20rpx;
	}

	.page-header {
		background: linear-gradient(135deg, #38bdf8 0%, #0ea5e9 70%, #3b82f6 100%); /* 更轻盈的蓝色渐变 - 从浅到深 */
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		color: white;
		box-shadow: 0 6rpx 24rpx rgba(56, 189, 248, 0.25); /* 更轻的阴影 */
	}

	.page-title {
		font-size: 36rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}

	.page-subtitle {
		font-size: 28rpx;
		opacity: 0.9;
		margin-bottom: 20rpx;
	}

	.timing-explanation {
		background: rgba(255, 255, 255, 0.15);
		border-radius: 12rpx;
		padding: 16rpx 20rpx;
		margin-top: 20rpx;
	}

	.explanation-text {
		font-size: 24rpx;
		opacity: 0.95;
		line-height: 1.4;
	}

	.stats-section {
		margin-bottom: 30rpx;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20rpx;
	}

	.stat-card {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}

	.stat-number {
		font-size: 48rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;

		&.assigned { color: #38bdf8; } /* 浅蓝色 - 执行中 */
		&.pending { color: #f59e0b; } /* 橙色 - 待确认 */
		&.completed { color: #059669; } /* 绿色 - 已完成 */
		&.overdue { color: #dc2626; } /* 红色 - 超时 */
	}

	.stat-label {
		font-size: 24rpx;
		color: #666;
	}

	.filter-tabs {
		display: flex;
		background: white;
		border-radius: 16rpx;
		padding: 8rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	}

	.filter-tab {
		flex: 1;
		text-align: center;
		padding: 20rpx 10rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		color: #666;
		transition: all 0.3s ease;

		&.active {
			background: #3b82f6; /* 蓝色 - 水务主题 */
			color: white;
		}
	}

	.task-list {
		margin-bottom: 30rpx;
	}

	.task-item {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;

		&:active {
			transform: translateY(2rpx);
		}
	}

	.task-header {
		margin-bottom: 20rpx;
	}

	.task-title-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.task-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		flex: 1;
		margin-right: 20rpx;
	}

	.task-status {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		color: white;

		&.status-assigned { background: #38bdf8; } /* 浅蓝色 - 执行中 */
		&.status-pending { background: #f59e0b; } /* 橙色 - 待确认 */
		&.status-completed { background: #059669; } /* 绿色 - 已完成 */
	}

	.task-project {
		font-size: 24rpx;
		color: #666;
	}

	.task-content {
		margin-bottom: 20rpx;
	}

	.task-description {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
	}

	.task-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.responsible-info, .time-info {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 8rpx;
	}

	.responsible-label, .time-label {
		font-size: 24rpx;
		color: #999;
		margin-right: 8rpx;
	}

	.responsible-name, .time-value {
		font-size: 24rpx;
		color: #333;

		&.overdue {
			color: #dc2626; /* 红色 - 超时 */
			font-weight: bold;
		}

		&.warning {
			color: #f59e0b; /* 橙色 - 警告 */
			font-weight: bold;
		}
	}

	.timing-badge {
		margin-left: 8rpx;
	}

	.timing-tag {
		font-size: 20rpx;
		padding: 4rpx 8rpx;
		border-radius: 8rpx;
		color: white;
		font-weight: bold;
	}

	.overdue-tag {
		background: #dc2626; /* 红色 - 超时 */
	}

	.warning-tag {
		background: #f59e0b; /* 橙色 - 警告 */
	}

	.normal-tag {
		background: #059669; /* 绿色 - 正常 */
	}

	.quick-actions {
		display: flex;
		gap: 20rpx;
	}

	.action-btn {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx;
		border-radius: 12rpx;
		color: white;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}

		text {
			font-size: 26rpx;
			margin-left: 8rpx;
		}
	}

	.confirm-btn {
		background: #4caf50;
	}

	.reject-btn {
		background: #f44336;
	}

	.empty-state {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 100rpx 40rpx;
		min-height: 400rpx;
	}

	.empty-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: center;
	}

	.empty-image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
		opacity: 0.6;
		display: block;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
		display: block;
		text-align: center;
	}

	.loading-state {
		padding: 40rpx;
	}

	.custom-loading {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #666;
	}

	/* 自定义弹窗样式 */
	.custom-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	.modal-content {
		width: 80%;
		background-color: #fff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	}

	.modal-header {
		padding: 30rpx;
		text-align: center;
		border-bottom: 1px solid #f0f0f0;
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.modal-body {
		padding: 30rpx;
	}

	.modal-label {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	.modal-input {
		width: 100%;
		min-height: 160rpx;
		border: 1px solid #e0e0e0;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		color: #333;
		background-color: #f9f9f9;
		box-sizing: border-box;
	}

	.input-counter {
		font-size: 24rpx;
		color: #999;
		text-align: right;
		margin-top: 10rpx;
	}

	.modal-footer {
		display: flex;
		border-top: 1px solid #f0f0f0;
	}

	.modal-btn {
		flex: 1;
		padding: 24rpx;
		text-align: center;
		font-size: 30rpx;
	}

	.cancel-btn {
		color: #666;
		border-right: 1px solid #f0f0f0;
	}

	.confirm-btn {
		color: #3b82f6;
		font-weight: bold;
	}
</style>

(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-honor_pkg-admin-add-record"],{"0026":function(t,e,a){"use strict";var n=a("0ebb"),r=a.n(n);r.a},"0777":function(t,e,a){"use strict";var n=a("19d0"),r=a.n(n);r.a},"0ebb":function(t,e,a){var n=a("d52d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=a("967d").default;r("1aac5edc",n,!0,{sourceMap:!1,shadowMode:!1})},1732:function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("f7a5");var r=n(a("3f10")),o=a("d3b4"),i=n(a("9ef6")),s=(0,o.initVueI18n)(i.default),c=s.t,l={name:"uniPopupDialog",mixins:[r.default],emits:["confirm","close","update:modelValue","input"],props:{inputType:{type:String,default:"text"},showClose:{type:Boolean,default:!0},value:{type:[String,Number],default:""},placeholder:{type:[String,Number],default:""},type:{type:String,default:"error"},mode:{type:String,default:"base"},title:{type:String,default:""},content:{type:String,default:""},beforeClose:{type:Boolean,default:!1},cancelText:{type:String,default:""},confirmText:{type:String,default:""},maxlength:{type:Number,default:-1},focus:{type:Boolean,default:!0}},data:function(){return{dialogType:"error",val:""}},computed:{okText:function(){return this.confirmText||c("uni-popup.ok")},closeText:function(){return this.cancelText||c("uni-popup.cancel")},placeholderText:function(){return this.placeholder||c("uni-popup.placeholder")},titleText:function(){return this.title||c("uni-popup.title")}},watch:{type:function(t){this.dialogType=t},mode:function(t){"input"===t&&(this.dialogType="info")},value:function(t){-1!=this.maxlength&&"input"===this.mode?this.val=t.slice(0,this.maxlength):this.val=t},val:function(t){this.$emit("input",t)}},created:function(){this.popup.disableMask(),"input"===this.mode?(this.dialogType="info",this.val=this.value):this.dialogType=this.type},methods:{onOk:function(){"input"===this.mode?this.$emit("confirm",this.val):this.$emit("confirm"),this.beforeClose||this.popup.close()},closeDialog:function(){this.$emit("close"),this.beforeClose||this.popup.close()},close:function(){this.popup.close()}}};e.default=l},1873:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-popup-dialog"},[a("v-uni-view",{staticClass:"uni-dialog-title"},[a("v-uni-text",{staticClass:"uni-dialog-title-text",class:["uni-popup__"+t.dialogType]},[t._v(t._s(t.titleText))])],1),"base"===t.mode?a("v-uni-view",{staticClass:"uni-dialog-content"},[t._t("default",[a("v-uni-text",{staticClass:"uni-dialog-content-text"},[t._v(t._s(t.content))])])],2):a("v-uni-view",{staticClass:"uni-dialog-content"},[t._t("default",["checkbox"===t.inputType?a("v-uni-input",{staticClass:"uni-dialog-input",attrs:{maxlength:t.maxlength,placeholder:t.placeholderText,focus:t.focus,type:"checkbox"},model:{value:t.val,callback:function(e){t.val=e},expression:"val"}}):"radio"===t.inputType?a("input",{directives:[{name:"model",rawName:"v-model",value:t.val,expression:"val"}],staticClass:"uni-dialog-input",attrs:{maxlength:t.maxlength,placeholder:t.placeholderText,focus:t.focus,type:"radio"},domProps:{checked:t._q(t.val,null)},on:{change:function(e){t.val=null}}}):a("input",{directives:[{name:"model",rawName:"v-model",value:t.val,expression:"val"}],staticClass:"uni-dialog-input",attrs:{maxlength:t.maxlength,placeholder:t.placeholderText,focus:t.focus,type:t.inputType},domProps:{value:t.val},on:{input:function(e){e.target.composing||(t.val=e.target.value)}}})])],2),a("v-uni-view",{staticClass:"uni-dialog-button-group"},[t.showClose?a("v-uni-view",{staticClass:"uni-dialog-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeDialog.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"uni-dialog-button-text"},[t._v(t._s(t.closeText))])],1):t._e(),a("v-uni-view",{staticClass:"uni-dialog-button",class:t.showClose?"uni-border-left":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onOk.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"uni-dialog-button-text uni-button-color"},[t._v(t._s(t.okText))])],1)],1)],1)},r=[]},"19d0":function(t,e,a){var n=a("b72a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=a("967d").default;r("4b722938",n,!0,{sourceMap:!1,shadowMode:!1})},"1f14":function(t,e,a){"use strict";a.r(e);var n=a("1873"),r=a("cad1");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("0026");var i=a("828b"),s=Object(i["a"])(r["default"],n["b"],n["c"],!1,null,"b2d7af54",null,!1,n["a"],void 0);e["default"]=s.exports},"2c11":function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.avatar-picker-component[data-v-1c086441]{width:100%;position:relative}.avatar-picker[data-v-1c086441]{width:100%;position:relative}.avatar-card[data-v-1c086441]{background:#fff;border-radius:%?8?%;padding:%?24?%;display:flex;flex-direction:column;gap:%?24?%;box-shadow:0 %?2?% %?12?% rgba(0,0,0,.05)}.avatar-preview[data-v-1c086441]{position:relative;width:%?120?%;height:%?120?%}.avatar-preview .avatar-image[data-v-1c086441]{width:100%;height:100%;object-fit:cover;border-radius:50%;overflow:hidden;background:#fff;box-shadow:inset 0 0 0 %?1?% rgba(0,0,0,.1)}.avatar-preview .avatar-image[src*=default-avatar][data-v-1c086441]{padding:%?24?%;background:linear-gradient(135deg,#e6f3ff,#fff);box-sizing:border-box}.avatar-preview .clear-btn[data-v-1c086441]{position:absolute;top:%?-8?%;right:%?-8?%;width:%?32?%;height:%?32?%;border-radius:50%;background:#ef4444;display:flex;align-items:center;justify-content:center;padding:0;line-height:1}.avatar-preview .clear-btn[data-v-1c086441]:active{background:#dc2626}.avatar-actions[data-v-1c086441]{display:flex;gap:%?16?%}.action-btn[data-v-1c086441]{flex:1;height:%?72?%;line-height:%?72?%;font-size:%?28?%;border-radius:%?4?%;border:none;padding:0;margin:0}.action-btn[data-v-1c086441]::after{display:none}.action-btn.upload-btn[data-v-1c086441]{background:#1890ff;color:#fff}.action-btn.select-btn[data-v-1c086441]{background:#fff;color:#1890ff;border:%?1?% solid #1890ff}.history-popup[data-v-1c086441]{background:#fff;border-radius:%?12?%;width:%?640?%;min-height:%?200?%;max-height:%?720?%;display:flex;flex-direction:column;overflow:hidden}.popup-header[data-v-1c086441]{padding:%?20?% %?24?%;display:flex;align-items:center;justify-content:space-between;border-bottom:%?1?% solid #eee;flex-shrink:0}.popup-header .popup-title[data-v-1c086441]{font-size:%?28?%;font-weight:500;color:#333}.popup-header .popup-close[data-v-1c086441]{padding:%?8?%;display:flex;align-items:center;justify-content:center;border-radius:50%}.popup-header .popup-close[data-v-1c086441]:active{background:#f5f5f5}.history-list[data-v-1c086441]{flex:1;width:100%;box-sizing:border-box;overflow-y:auto;overflow-x:hidden}.history-grid[data-v-1c086441]{display:grid;grid-template-columns:repeat(3,1fr);gap:%?16?%;padding:%?16?%;box-sizing:border-box}.history-item[data-v-1c086441]{width:100%;box-sizing:border-box}.history-item .item-container[data-v-1c086441]{background:#fff;border-radius:%?12?%;border:%?1?% solid #eee;padding:%?12?%;display:flex;flex-direction:column;align-items:center;gap:%?8?%;transition:all .2s ease;width:100%;box-sizing:border-box}.history-item .item-container[data-v-1c086441]:active{background:#f5f5f5}.history-item.is-selected .item-container[data-v-1c086441]{background:#e6f7ff;border-color:#1890ff}.history-item.is-selected .item-container .history-name[data-v-1c086441]{color:#1890ff}.avatar-wrapper[data-v-1c086441]{position:relative;width:%?100?%;height:%?100?%}.avatar-wrapper .history-avatar[data-v-1c086441]{width:100%;height:100%;border-radius:50%;object-fit:cover;background:#fff;border:%?2?% solid #f0f0f0;box-sizing:border-box;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05)}.avatar-wrapper .delete-btn[data-v-1c086441]{position:absolute;top:%?-6?%;right:%?-6?%;width:%?28?%;height:%?28?%;border-radius:50%;background:#ff4d4f;display:flex;align-items:center;justify-content:center;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.2);z-index:10;border:%?2?% solid #fff;padding:0}.history-name[data-v-1c086441]{font-size:%?24?%;color:#333;width:100%;text-align:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0 %?4?%;box-sizing:border-box}.batch-upload-popup[data-v-1c086441]{background:#fff;border-radius:%?12?%;width:%?640?%;max-height:%?800?%;display:flex;flex-direction:column}.batch-upload-popup .batch-list[data-v-1c086441]{flex:1;min-height:%?400?%;max-height:%?600?%}.batch-upload-popup .batch-list .batch-table-header[data-v-1c086441],\n.batch-upload-popup .batch-list .batch-table-row[data-v-1c086441]{display:flex;align-items:center;padding:%?16?% %?24?%;border-bottom:%?1?% solid #eee}.batch-upload-popup .batch-list .batch-table-header .col-name[data-v-1c086441],\n.batch-upload-popup .batch-list .batch-table-row .col-name[data-v-1c086441]{flex:2;padding-right:%?16?%}.batch-upload-popup .batch-list .batch-table-header .col-avatar[data-v-1c086441],\n.batch-upload-popup .batch-list .batch-table-row .col-avatar[data-v-1c086441]{flex:1;padding:0 %?16?%}.batch-upload-popup .batch-list .batch-table-header .col-action[data-v-1c086441],\n.batch-upload-popup .batch-list .batch-table-row .col-action[data-v-1c086441]{flex:1;padding-left:%?16?%;text-align:center}.batch-upload-popup .batch-list .batch-table-header[data-v-1c086441]{font-size:%?28?%;color:#666;background:#f8f8f8}.batch-upload-popup .batch-list .batch-table-row .upload-area[data-v-1c086441]{width:%?70?%;height:%?70?%;background:#f8f8f8;border-radius:50%;display:flex;align-items:center;justify-content:center;overflow:hidden}.batch-upload-popup .batch-list .batch-table-row .upload-area .preview-image[data-v-1c086441]{width:100%;height:100%;object-fit:cover;border-radius:50%}.batch-upload-popup .batch-list .batch-table-row .upload-area .upload-placeholder[data-v-1c086441]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;background:#f5f5f5;border-radius:50%}.batch-upload-popup .batch-list .batch-table-row .upload-area .upload-placeholder .uni-icons[data-v-1c086441]{font-size:%?24?%}.batch-upload-popup .batch-list .batch-table-row .save-btn[data-v-1c086441]{width:%?120?%;height:%?56?%;line-height:%?56?%;font-size:%?24?%;background:#1890ff;color:#fff;border-radius:%?4?%;padding:0}.batch-upload-popup .batch-list .batch-table-row .save-btn.btn-disabled[data-v-1c086441]{background:#d9d9d9}.batch-upload-popup .popup-footer[data-v-1c086441]{padding:%?24?%;display:flex;gap:%?16?%;border-top:%?1?% solid #eee}.batch-upload-popup .popup-footer uni-button[data-v-1c086441]{flex:1;height:%?72?%;line-height:%?72?%;font-size:%?28?%;border-radius:%?4?%}.batch-upload-popup .popup-footer uni-button[data-v-1c086441]::after{display:none}.batch-upload-popup .popup-footer .add-btn[data-v-1c086441]{background:#1890ff;color:#fff;display:flex;align-items:center;justify-content:center;gap:%?8?%}.batch-upload-popup .popup-footer .close-btn[data-v-1c086441]{background:#f5f5f5;color:#666}.history-empty[data-v-1c086441]{padding:%?60?% 0}',""]),t.exports=e},"35d0":function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={uniIcons:a("6ddf").default,uniPopup:a("a2b7").default,uniPopupDialog:a("1f14").default,pEmptyState:a("9b76").default,uniEasyinput:a("6cf4").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"avatar-picker-component"},[a("v-uni-view",{staticClass:"avatar-picker"},[a("v-uni-view",{staticClass:"avatar-card"},[a("v-uni-view",{staticClass:"avatar-preview"},[a("v-uni-image",{staticClass:"avatar-image",attrs:{src:t.displayAvatar,mode:"aspectFill"}}),t.avatarUrl&&t.avatarUrl!==t.defaultAvatar&&!t.uploading?a("v-uni-view",{staticClass:"clear-btn",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.clearAvatar.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"close",size:"14",color:"#FFFFFF"}})],1):t._e()],1),a("v-uni-view",{staticClass:"avatar-actions"},[a("v-uni-button",{staticClass:"action-btn upload-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openBatchUpload.apply(void 0,arguments)}}},[t._v("上传头像")]),a("v-uni-button",{staticClass:"action-btn select-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openHistorySelect.apply(void 0,arguments)}}},[t._v("选择头像")])],1)],1),a("uni-popup",{ref:"deleteConfirm",attrs:{type:"dialog"}},[a("uni-popup-dialog",{attrs:{type:"warning",title:"删除确认",content:"确定要删除当前头像吗？删除后将恢复为默认头像。","before-close":!1},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.handleDelete.apply(void 0,arguments)},close:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.deleteConfirm.close()}}})],1),a("uni-popup",{ref:"historyPopup",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"history-popup"},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"popup-title"},[t._v("选择头像")]),a("v-uni-view",{staticClass:"popup-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeHistorySelect.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"close",size:"16",color:"#666666"}})],1)],1),a("v-uni-scroll-view",{staticClass:"history-list",attrs:{"scroll-y":!0}},[t.historyAvatars.length>0?[a("v-uni-view",{staticClass:"history-grid"},t._l(t.historyAvatars,(function(e,n){return a("v-uni-view",{key:n,staticClass:"history-item",class:{"is-selected":e.url===t.avatarUrl}},[a("v-uni-view",{staticClass:"item-container"},[a("v-uni-view",{staticClass:"avatar-wrapper"},[a("v-uni-image",{staticClass:"history-avatar",attrs:{src:e.url,mode:"aspectFill"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectHistoryAvatar(e)}}}),a("v-uni-view",{staticClass:"delete-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.confirmDeleteHistory(e)}}},[a("uni-icons",{attrs:{type:"close",size:"14",color:"#FFFFFF"}})],1)],1),a("v-uni-text",{staticClass:"history-name"},[t._v(t._s(e.userName||"未命名"))])],1)],1)})),1)]:[a("p-empty-state",{staticClass:"history-empty",attrs:{image:"/static/empty/empty_data.png",text:"暂无历史头像",tips:"上传新头像后会保存在这里"}})]],2)],1)],1),a("uni-popup",{ref:"batchUploadPopup",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"batch-upload-popup"},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"popup-title"},[t._v("上传头像")]),a("v-uni-view",{staticClass:"popup-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeBatchUpload.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"close",size:"14",color:"#666666"}})],1)],1),a("v-uni-scroll-view",{staticClass:"batch-list",attrs:{"scroll-y":!0}},[a("v-uni-view",{staticClass:"batch-table-header"},[a("v-uni-text",{staticClass:"col-name"},[t._v("姓名")]),a("v-uni-text",{staticClass:"col-avatar"},[t._v("头像")]),a("v-uni-text",{staticClass:"col-action"},[t._v("操作")])],1),t._l(t.batchList,(function(e,n){return a("v-uni-view",{key:n,staticClass:"batch-table-row"},[a("v-uni-view",{staticClass:"col-name"},[a("uni-easyinput",{attrs:{placeholder:"请输入姓名",clearable:!0},model:{value:e.userName,callback:function(a){t.$set(e,"userName",a)},expression:"item.userName"}})],1),a("v-uni-view",{staticClass:"col-avatar"},[a("v-uni-view",{staticClass:"upload-area",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectImage(n)}}},[e.url?a("v-uni-image",{staticClass:"preview-image",attrs:{src:e.url,mode:"aspectFill"}}):a("v-uni-view",{staticClass:"upload-placeholder"},[a("uni-icons",{attrs:{type:"camera-filled",size:"20",color:"#999999"}})],1)],1)],1),a("v-uni-view",{staticClass:"col-action"},[a("v-uni-button",{staticClass:"save-btn",class:{"btn-disabled":!e.userName||!e.url||e.saving},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveAvatar(n)}}},[t._v(t._s(e.saving?"保存中...":"保存"))])],1)],1)}))],2),a("v-uni-view",{staticClass:"popup-footer"},[a("v-uni-button",{staticClass:"add-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addBatchItem.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"plus",size:"14",color:"#FFFFFF"}}),a("v-uni-text",[t._v("添加一行")])],1),a("v-uni-button",{staticClass:"close-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeBatchUpload.apply(void 0,arguments)}}},[t._v("关闭")])],1)],1)],1)],1)],1)},o=[]},3935:function(t,e,a){"use strict";(function(t){a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("9004")),o=n(a("2634")),i=n(a("9b1b")),s=n(a("2fdc")),c=n(a("b7c7"));a("0c26"),a("8f71"),a("bf0f"),a("4626"),a("5ac7"),a("4100"),a("f7a5"),a("18f7"),a("de6c"),a("fd3c"),a("c223"),a("795c"),a("c9b5"),a("ab80"),a("aa77"),a("2797"),a("aa9c"),a("7a76"),a("dd2b"),a("bd06");var l=n(a("baea")),u={components:{AvatarPicker:l.default},name:"AddRecord",data:function(){return{loading:!1,saving:!1,loadingText:"加载中...",isEditMode:!1,recordId:null,formData:{userName:"",department:"",userAvatar:"",honorTypeId:"",batchId:"",reason:"",isFeatured:!1,images:[]},departmentOptions:[],honorTypeOptions:[],batchOptions:[],selectedHonorType:null,selectedBatch:null,showBatchSelector:!1,batchSearchKeyword:"",showOlderBatchCount:10,showDepartmentSelector:!1,newDepartmentName:"",showHonorTypeSelector:!1}},computed:{filteredBatchOptions:function(){if(!this.batchSearchKeyword.trim())return this.batchOptions;var t=this.batchSearchKeyword.toLowerCase();return this.batchOptions.filter((function(e){return e.text.toLowerCase().includes(t)}))},recentBatches:function(){var t=(0,c.default)(this.filteredBatchOptions).sort((function(t,e){var a=new Date(t.data.meetingDate||"1970-01-01"),n=new Date(e.data.meetingDate||"1970-01-01");return n-a}));return t.slice(0,5)},currentMonthBatches:function(){var t=new Date,e=t.getMonth()+1,a=t.getFullYear();return this.filteredBatchOptions.filter((function(t){var n=new Date(t.data.meetingDate);return n.getMonth()+1===e&&n.getFullYear()===a}))},olderBatches:function(){var t=new Date;return t.setMonth(t.getMonth()-3),this.filteredBatchOptions.filter((function(e){var a=new Date(e.data.meetingDate);return a<t})).sort((function(t,e){var a=new Date(t.data.meetingDate||"1970-01-01"),n=new Date(e.data.meetingDate||"1970-01-01");return n-a}))},filteredOlderBatches:function(){return this.olderBatches.slice(0,this.showOlderBatchCount)}},onLoad:function(t){var e=this;return(0,s.default)((0,o.default)().mark((function a(){var n;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("edit"===t.mode&&t.data){e.isEditMode=!0;try{n=JSON.parse(decodeURIComponent(t.data)),e.recordId=n.id,n.userAvatar&&(e.formData.userAvatar=n.userAvatar),e.formData=(0,i.default)((0,i.default)((0,i.default)({},e.formData),n),{},{userAvatar:n.userAvatar||""}),delete e.formData.id}catch(r){console.error("解析编辑数据失败:",r)}}return a.next=3,e.initializeData();case 3:case"end":return a.stop()}}),a)})))()},methods:{initializeData:function(){var t=this;return(0,s.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Promise.all([t.loadDepartmentList(),t.loadHonorTypeList(),t.loadBatchList()]);case 3:t.isEditMode&&(t.setSelectedHonorType(),t.setSelectedBatch()),e.next=9;break;case 6:e.prev=6,e.t0=e["catch"](0),uni.showToast({title:"初始化失败，请重试",icon:"none"});case 9:case"end":return e.stop()}}),e,null,[[0,6]])})))()},loadDepartmentList:function(){var e=this;return(0,s.default)((0,o.default)().mark((function a(){var n,r;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.callFunction({name:"honor-admin",data:{action:"getDepartments"}});case 3:n=a.sent,0===n.result.code&&(r=n.result.data,Array.isArray(r)?e.departmentOptions=r.map((function(t){return{value:t.name||t,text:t.name||t}})):e.departmentOptions=[]),a.next=11;break;case 7:a.prev=7,a.t0=a["catch"](0),console.error("加载部门列表失败:",a.t0),e.departmentOptions=[{value:"管理部门",text:"管理部门"},{value:"业务部门",text:"业务部门"},{value:"技术部门",text:"技术部门"},{value:"支持部门",text:"支持部门"}];case 11:case"end":return a.stop()}}),a,null,[[0,7]])})))()},loadHonorTypeList:function(){var e=this;return(0,s.default)((0,o.default)().mark((function a(){var n,r;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.callFunction({name:"honor-admin",data:{action:"getHonorTypes"}});case 3:n=a.sent,0===n.result.code&&(r=n.result.data,Array.isArray(r)?e.honorTypeOptions=r.filter((function(t){return!1!==t.isActive})).map((function(t){return{value:t._id||t.id,text:t.name,color:t.color||"#3a86ff",data:t}})):e.honorTypeOptions=[]),a.next=11;break;case 7:a.prev=7,a.t0=a["catch"](0),console.error("加载荣誉类型失败:",a.t0),e.honorTypeOptions=[{value:"1",text:"优秀员工",color:"#3a86ff",data:{id:"1",name:"优秀员工",color:"#3a86ff"}},{value:"2",text:"月度之星",color:"#10b981",data:{id:"2",name:"月度之星",color:"#10b981"}},{value:"3",text:"团队协作奖",color:"#f59e0b",data:{id:"3",name:"团队协作奖",color:"#f59e0b"}}];case 11:case"end":return a.stop()}}),a,null,[[0,7]])})))()},loadBatchList:function(){var e=this;return(0,s.default)((0,o.default)().mark((function a(){var n,r,i,s,c;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.callFunction({name:"honor-admin",data:{action:"getBatches"}});case 3:n=a.sent,0===n.result.code&&(r=n.result.data,Array.isArray(r)?e.batchOptions=r.map((function(t){return{value:t._id,text:t.name,meetingDate:t.meetingDate,data:t}})):e.batchOptions=[]),a.next=14;break;case 7:a.prev=7,a.t0=a["catch"](0),console.error("加载批次列表失败:",a.t0),i=new Date,s=i.getMonth()+1,c=i.getFullYear(),e.batchOptions=[{value:"default_monthly",text:"".concat(c,"年").concat(s,"月表彰"),meetingDate:"".concat(c,"-").concat(s.toString().padStart(2,"0"),"-01"),data:{_id:"default_monthly",name:"".concat(c,"年").concat(s,"月表彰"),type:"monthly",meetingDate:"".concat(c,"-").concat(s.toString().padStart(2,"0"),"-01")}}];case 14:case"end":return a.stop()}}),a,null,[[0,7]])})))()},setSelectedHonorType:function(){var t=this;if(this.formData.honorTypeId){var e=this.honorTypeOptions.find((function(e){return e.value===t.formData.honorTypeId}));e&&(this.selectedHonorType=e.data)}},setSelectedBatch:function(){var t=this;if(this.formData.batchId){var e=this.batchOptions.find((function(e){return e.value===t.formData.batchId}));e&&(this.selectedBatch=e.data)}},onHonorTypeChange:function(t){var e=this.honorTypeOptions.find((function(e){return e.value===t.detail.value}));this.selectedHonorType=e?e.data:null},onBatchChange:function(t){var e=this.batchOptions.find((function(e){return e.value===t.detail.value}));this.selectedBatch=e?e.data:null},onFeaturedChange:function(t){this.formData.isFeatured=t.detail.value},handleAvatarChange:function(t){console.log("头像变化:",t),t&&(t.cloudPath||t.url)?(this.formData.userAvatar=t.url||t.cloudPath,t.cloudPath&&(this.formData.userAvatarCloudPath=t.cloudPath),uni.showToast({title:"头像更新成功",icon:"success",duration:2e3})):t||(this.formData.userAvatar="",this.formData.userAvatarCloudPath="")},onAvatarUploadSuccess:function(t){console.log("头像上传成功:",t),t.url&&(this.formData.userAvatar=t.url,console.log("设置头像URL:",t.url),t.compressed&&console.log("压缩信息:",{size:t.size,originalSize:t.originalSize,compressionRatio:t.compressionRatio})),uni.showToast({title:"头像上传成功".concat(t.compressed?"(已压缩)":""),icon:"success"})},onAvatarUploadError:function(t){console.error("头像上传失败:",t),uni.showToast({title:"头像上传失败",icon:"none"})},chooseImages:function(){var t=this;return(0,s.default)((0,o.default)().mark((function e(){var n,i,s,c,l,u;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=5-t.formData.images.length,e.prev=1,e.next=4,t.chooseImageAsync({count:n,sizeType:["original"],sourceType:["album","camera"]});case 4:if(i=e.sent,"chooseImage:fail cancel"!==i.errMsg){e.next=7;break}return e.abrupt("return");case 7:if(!(i.tempFilePaths&&i.tempFilePaths.length>0)){e.next=24;break}return uni.showLoading({title:"处理中..."}),e.next=11,Promise.resolve().then((function(){return(0,r.default)(a("4ea0"))}));case 11:return s=e.sent.default,e.next=14,s.uploadHonorImages(i.tempFilePaths,{compress:!0,onProgress:function(t){"compress"===t.phase?uni.showLoading({title:"压缩中 ".concat(t.progress||0,"%")}):"upload"===t.phase&&uni.showLoading({title:"上传中 ".concat(t.progress||0,"%")})}});case 14:if(c=e.sent,uni.hideLoading(),!c.success){e.next=23;break}c.results.forEach((function(e,a){e.success&&t.formData.images.push({id:Date.now()+Math.random()+a,url:e.url,cloudPath:e.cloudPath})})),l=c.totalUploaded,u=c.totalFailed,u>0?uni.showToast({title:"".concat(l,"张成功，").concat(u,"张失败"),icon:"none"}):uni.showToast({title:"".concat(l,"张图片上传成功"),icon:"success"}),e.next=24;break;case 23:throw new Error("批量上传失败");case 24:e.next=31;break;case 26:e.prev=26,e.t0=e["catch"](1),uni.hideLoading(),console.error("选择图片失败:",e.t0),uni.showToast({title:e.t0.message||"图片上传失败",icon:"none"});case 31:case"end":return e.stop()}}),e,null,[[1,26]])})))()},chooseImageAsync:function(t){return new Promise((function(e,a){uni.chooseImage((0,i.default)((0,i.default)({},t),{},{success:e,fail:a}))}))},removeImage:function(e){var a=this;return(0,s.default)((0,o.default)().mark((function n(){var r,i,s;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,r=a.formData.images[e],!r||!r.cloudPath){n.next=17;break}return uni.showLoading({title:"删除中..."}),n.next=6,t.callFunction({name:"delete-file",data:{fileList:[r.cloudPath]}});case 6:if(i=n.sent,s=i.result,uni.hideLoading(),0!==s.code){n.next=14;break}a.formData.images.splice(e,1),uni.showToast({title:"删除成功",icon:"success"}),n.next=15;break;case 14:throw new Error(s.message||"删除失败");case 15:n.next=18;break;case 17:a.formData.images.splice(e,1);case 18:n.next=25;break;case 20:n.prev=20,n.t0=n["catch"](0),uni.hideLoading(),console.error("删除图片失败:",n.t0),uni.showToast({title:n.t0.message||"删除失败",icon:"none"});case 25:case"end":return n.stop()}}),n,null,[[0,20]])})))()},saveRecord:function(){var e=this;return(0,s.default)((0,o.default)().mark((function a(){var n,r,i;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.validateForm()){a.next=2;break}return a.abrupt("return");case 2:return e.saving=!0,a.prev=3,n=e.isEditMode?"updateHonor":"createHonor",r={userName:e.formData.userName,department:e.formData.department,userAvatar:e.formData.userAvatar,honorTypeId:e.formData.honorTypeId,batchId:e.formData.batchId,reason:e.formData.reason,isFeatured:e.formData.isFeatured,images:e.formData.images},e.isEditMode&&(r.honorId=e.recordId),a.next=9,t.callFunction({name:"honor-admin",data:{action:n,data:r}});case 9:if(i=a.sent,0!==i.result.code){a.next=15;break}uni.showToast({title:e.isEditMode?"表彰记录更新成功":"表彰记录创建成功",icon:"success",duration:2e3}),setTimeout((function(){uni.navigateBack()}),2e3),a.next=16;break;case 15:throw new Error(i.result.message||(e.isEditMode?"更新失败":"创建失败"));case 16:a.next=21;break;case 18:a.prev=18,a.t0=a["catch"](3),uni.showToast({title:a.t0.message||"保存失败",icon:"none"});case 21:return a.prev=21,e.saving=!1,a.finish(21);case 24:case"end":return a.stop()}}),a,null,[[3,18,21,24]])})))()},validateForm:function(){return this.formData.userName.trim()?this.formData.honorTypeId?this.formData.batchId?!!this.formData.reason.trim()||(uni.showToast({title:"请填写表彰原因",icon:"none"}),!1):(uni.showToast({title:"请选择表彰批次",icon:"none"}),!1):(uni.showToast({title:"请选择荣誉类型",icon:"none"}),!1):(uni.showToast({title:"请输入被表彰人姓名",icon:"none"}),!1)},goBack:function(){this.hasChanges()?uni.showModal({title:"确认离开",content:"您有未保存的内容，确定要离开吗？",success:function(t){t.confirm&&uni.navigateBack()}}):uni.navigateBack()},hasChanges:function(){return this.formData.userName.trim()||this.formData.reason.trim()||this.formData.honorTypeId||this.formData.batchId||this.formData.images.length>0},openDepartmentSelector:function(){this.showDepartmentSelector=!0},closeDepartmentSelector:function(){this.showDepartmentSelector=!1,this.newDepartmentName=""},selectDepartment:function(t){this.formData.department=t.value,this.closeDepartmentSelector()},addNewDepartment:function(){var t=this;return(0,s.default)((0,o.default)().mark((function e(){var a,n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=t.newDepartmentName.trim(),a){e.next=4;break}return uni.showToast({title:"请输入部门名称",icon:"none"}),e.abrupt("return");case 4:if(n=t.departmentOptions.some((function(t){return t.value===a})),!n){e.next=8;break}return uni.showToast({title:"部门已存在",icon:"none"}),e.abrupt("return");case 8:t.departmentOptions.push({value:a,text:a}),t.formData.department=a,t.newDepartmentName="",t.closeDepartmentSelector(),uni.showToast({title:"部门已添加（临时）",icon:"success",duration:2e3});case 13:case"end":return e.stop()}}),e)})))()},deleteDepartment:function(e){var a=this;this.departmentOptions.length<=1?uni.showToast({title:"至少需要保留一个部门",icon:"none"}):uni.showModal({title:"确认删除",content:'确定要删除"'.concat(e.text,'"部门吗？'),success:function(){var n=(0,s.default)((0,o.default)().mark((function n(r){var i,s;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!r.confirm){n.next=17;break}return n.prev=1,n.next=4,t.callFunction({name:"honor-admin",data:{action:"checkDepartmentUsage",data:{name:e.value}}});case 4:if(i=n.sent,0===i.result.code){n.next=8;break}return uni.showToast({title:i.result.message||"该部门正在使用中，无法删除",icon:"none",duration:3e3}),n.abrupt("return");case 8:n.next=13;break;case 10:n.prev=10,n.t0=n["catch"](1),console.log("检查部门使用情况失败，继续删除:",n.t0);case 13:s=a.departmentOptions.findIndex((function(t){return t.value===e.value})),s>-1&&a.departmentOptions.splice(s,1),a.formData.department===e.value&&(a.formData.department=""),uni.showToast({title:"部门已移除",icon:"success"});case 17:case"end":return n.stop()}}),n,null,[[1,10]])})));return function(t){return n.apply(this,arguments)}}()})},openHonorTypeSelector:function(){this.showHonorTypeSelector=!0},closeHonorTypeSelector:function(){this.showHonorTypeSelector=!1},selectHonorType:function(t){this.formData.honorTypeId=t.value,this.selectedHonorType=t.data,this.closeHonorTypeSelector()},openBatchSelector:function(){this.showBatchSelector=!0},closeBatchSelector:function(){this.showBatchSelector=!1,this.batchSearchKeyword=""},onBatchSearch:function(){this.showOlderBatchCount=10},selectBatch:function(t){this.formData.batchId=t.value,this.selectedBatch=t.data,this.closeBatchSelector()},showMoreOlderBatches:function(){this.showOlderBatchCount+=10},formatBatchDate:function(t){if(!t)return"";var e=new Date(t),a=e.getMonth()+1,n=e.getDate();return"".concat(a,"月").concat(n,"日")},getBatchTypeText:function(t){return{weekly:"周表彰",monthly:"月表彰",quarterly:"季度表彰",yearly:"年度表彰",special:"特别表彰"}[t]||"其他"},quickCreateBatch:function(){this.closeBatchSelector(),uni.navigateTo({url:"/pages/honor_pkg/admin/batch-manager?quick=true"})},previewImage:function(t){this.formData.images.length>0&&uni.previewImage({urls:this.formData.images.map((function(t){return t.url})),current:this.formData.images[t].url,indicator:"number"})}}};e.default=u}).call(this,a("861b")["uniCloud"])},"3b53":function(t,e,a){"use strict";a.r(e);var n=a("48a0"),r=a("db73");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("44eb");var i=a("828b"),s=Object(i["a"])(r["default"],n["b"],n["c"],!1,null,"269e1299",null,!1,n["a"],void 0);e["default"]=s.exports},"3f10":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{}},created:function(){this.popup=this.getParent()},methods:{getParent:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"uniPopup",e=this.$parent,a=e.$options.name;while(a!==t){if(e=e.$parent,!e)return!1;a=e.$options.name}return e}}};e.default=n},"44eb":function(t,e,a){"use strict";var n=a("6d9a"),r=a.n(n);r.a},"48a0":function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={uniIcons:a("6ddf").default,uniEasyinput:a("6cf4").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"add-record-container"},[a("v-uni-view",{staticClass:"header"},[a("v-uni-view",{staticClass:"header-content"},[a("v-uni-view",{staticClass:"back-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"left",size:"18",color:"#FFFFFF"}})],1),a("v-uni-view",{staticClass:"title-area"},[a("v-uni-text",{staticClass:"title"},[t._v(t._s(t.isEditMode?"编辑表彰记录":"添加表彰记录"))]),a("v-uni-text",{staticClass:"subtitle"},[t._v("记录管理 · 数据录入")])],1),a("v-uni-view",{staticClass:"header-actions"},[a("v-uni-button",{staticClass:"save-btn",attrs:{disabled:t.saving},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveRecord.apply(void 0,arguments)}}},[t._v(t._s(t.saving?"保存中...":t.isEditMode?"更新":"保存"))])],1)],1)],1),a("v-uni-scroll-view",{staticClass:"content-scroll",attrs:{"scroll-y":!0}},[a("v-uni-view",{staticClass:"form-container"},[a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("uni-icons",{attrs:{type:"person-filled",size:"20",color:"#3a86ff"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("被表彰人信息")])],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("姓名"),a("v-uni-text",{staticClass:"required"},[t._v("*")])],1),a("uni-easyinput",{staticClass:"custom-input",attrs:{placeholder:"请输入被表彰人姓名",clearable:!0},model:{value:t.formData.userName,callback:function(e){t.$set(t.formData,"userName",e)},expression:"formData.userName"}})],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"form-label"},[t._v("部门")]),a("v-uni-view",{staticClass:"custom-department-picker",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openDepartmentSelector.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-display"},[t.formData.department?a("v-uni-text",{staticClass:"selected-text"},[t._v(t._s(t.formData.department))]):a("v-uni-text",{staticClass:"placeholder-text"},[t._v("请选择部门")]),a("uni-icons",{attrs:{type:"bottom",size:"16",color:"#999"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"form-label"},[t._v("头像")]),a("avatar-picker",{ref:"avatarPicker",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.handleAvatarChange.apply(void 0,arguments)}},model:{value:t.formData.userAvatar,callback:function(e){t.$set(t.formData,"userAvatar",e)},expression:"formData.userAvatar"}})],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("uni-icons",{attrs:{type:"medal-filled",size:"20",color:"#3a86ff"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("表彰信息")])],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("荣誉类型"),a("v-uni-text",{staticClass:"required"},[t._v("*")])],1),a("v-uni-view",{staticClass:"custom-honor-type-picker",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openHonorTypeSelector.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-display"},[t.selectedHonorType?a("v-uni-text",{staticClass:"selected-text"},[t._v(t._s(t.selectedHonorType.name))]):a("v-uni-text",{staticClass:"placeholder-text"},[t._v("请选择荣誉类型")]),a("uni-icons",{attrs:{type:"bottom",size:"16",color:"#999"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("表彰批次"),a("v-uni-text",{staticClass:"required"},[t._v("*")])],1),a("v-uni-view",{staticClass:"custom-batch-picker",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openBatchSelector.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker-display"},[t.selectedBatch?a("v-uni-text",{staticClass:"selected-text"},[t._v(t._s(t.selectedBatch.name))]):a("v-uni-text",{staticClass:"placeholder-text"},[t._v("请选择表彰批次")]),a("uni-icons",{attrs:{type:"bottom",size:"16",color:"#999"}})],1)],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("表彰原因"),a("v-uni-text",{staticClass:"required"},[t._v("*")])],1),a("uni-easyinput",{staticClass:"custom-textarea",attrs:{type:"textarea",placeholder:"请描述表彰原因和具体事迹","auto-height":!0,maxlength:"500"},model:{value:t.formData.reason,callback:function(e){t.$set(t.formData,"reason",e)},expression:"formData.reason"}}),a("v-uni-view",{staticClass:"char-count"},[t._v(t._s(t.formData.reason.length)+"/500")])],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("uni-icons",{attrs:{type:"gear-filled",size:"20",color:"#3a86ff"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("附加设置")])],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"switch-item"},[a("v-uni-text",{staticClass:"switch-label"},[t._v("设为精选表彰")]),a("v-uni-switch",{attrs:{checked:t.formData.isFeatured,color:"#3a86ff"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onFeaturedChange.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"form-desc"},[t._v("精选表彰将在荣誉展厅中优先展示")])],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-text",{staticClass:"form-label"},[t._v("相关图片")]),a("v-uni-view",{staticClass:"image-picker"},[a("v-uni-view",{staticClass:"image-list"},[t._l(t.formData.images,(function(e,n){return a("v-uni-view",{key:n,staticClass:"image-item"},[a("v-uni-image",{staticClass:"image-preview",attrs:{src:e.url,mode:"aspectFill"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewImage(n)}}}),a("v-uni-view",{staticClass:"image-remove",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.removeImage(n)}}},[a("uni-icons",{attrs:{type:"close",size:"14",color:"#FFFFFF"}})],1)],1)})),t.formData.images.length<5?a("v-uni-view",{staticClass:"image-add",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.chooseImages.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"plus",size:"28",color:"#8a94a6"}}),a("v-uni-text",{staticClass:"add-text"},[t._v("添加图片")])],1):t._e()],2),a("v-uni-view",{staticClass:"image-tip"},[t._v("最多可上传5张图片，支持JPG/PNG格式")])],1)],1)],1),a("v-uni-view",{staticClass:"form-section"},[a("v-uni-view",{staticClass:"section-header"},[a("uni-icons",{attrs:{type:"eye",size:"20",color:"#3a86ff"}}),a("v-uni-text",{staticClass:"section-title"},[t._v("预览效果")])],1),a("v-uni-view",{staticClass:"preview-card"},[a("v-uni-view",{staticClass:"preview-header"},[a("v-uni-image",{staticClass:"preview-avatar",attrs:{src:t.formData.userAvatar||"/static/user/default-avatar.png",mode:"aspectFill"}}),a("v-uni-view",{staticClass:"preview-info"},[a("v-uni-text",{staticClass:"preview-name"},[t._v(t._s(t.formData.userName||"姓名"))]),a("v-uni-text",{staticClass:"preview-dept"},[t._v(t._s(t.formData.department||"部门"))])],1),t.formData.isFeatured?a("v-uni-view",{staticClass:"preview-badge"},[a("uni-icons",{attrs:{type:"star-filled",size:"16",color:"#f59e0b"}}),a("v-uni-text",{staticClass:"badge-text"},[t._v("精选")])],1):t._e()],1),t.selectedHonorType?a("v-uni-view",{staticClass:"preview-type"},[a("v-uni-view",{staticClass:"type-tag",style:{backgroundColor:t.selectedHonorType.color||"#3a86ff"}},[t._v(t._s(t.selectedHonorType.name))])],1):t._e(),a("v-uni-view",{staticClass:"preview-reason"},[t._v(t._s(t.formData.reason||"表彰原因将在这里显示..."))]),t.formData.images.length>0?a("v-uni-view",{staticClass:"preview-images"},[t._l(t.formData.images.slice(0,3),(function(e,n){return a("v-uni-image",{key:n,staticClass:"preview-image",attrs:{src:e.url,mode:"aspectFill"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewImage(n)}}})})),t.formData.images.length>3?a("v-uni-view",{staticClass:"more-images"},[t._v("+"+t._s(t.formData.images.length-3))]):t._e()],2):t._e(),t.selectedBatch?a("v-uni-view",{staticClass:"preview-batch"},[a("v-uni-text",{staticClass:"batch-text"},[t._v(t._s(t.selectedBatch.name))]),a("v-uni-text",{staticClass:"batch-date"},[t._v(t._s(t.selectedBatch.meetingDate))])],1):t._e()],1)],1),a("v-uni-view",{staticClass:"bottom-safe-area"})],1)],1),t.showDepartmentSelector?a("v-uni-view",{staticClass:"selector-modal-overlay",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeDepartmentSelector.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"selector-modal-content",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[a("v-uni-view",{staticClass:"selector-modal-header"},[a("v-uni-view",{staticClass:"header-left"},[a("v-uni-text",{staticClass:"selector-modal-title"},[t._v("选择部门")]),a("v-uni-text",{staticClass:"selector-modal-subtitle"},[t._v("部门数据来自已有荣誉记录")])],1),a("v-uni-view",{staticClass:"selector-modal-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeDepartmentSelector.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"close",size:"18",color:"#8a94a6"}})],1)],1),a("v-uni-view",{staticClass:"department-info-section"},[a("v-uni-view",{staticClass:"info-item"},[a("uni-icons",{attrs:{type:"info-filled",size:"16",color:"#3a86ff"}}),a("v-uni-text",{staticClass:"info-text"},[t._v("新添加的部门仅在本次会话中有效")])],1),a("v-uni-view",{staticClass:"info-item"},[a("uni-icons",{attrs:{type:"checkmarkempty",size:"16",color:"#10b981"}}),a("v-uni-text",{staticClass:"info-text"},[t._v("创建荣誉记录后，部门将永久保存")])],1)],1),a("v-uni-view",{staticClass:"add-department-section"},[a("v-uni-view",{staticClass:"add-department-input"},[a("uni-easyinput",{attrs:{placeholder:"输入新部门名称",clearable:!0},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.addNewDepartment.apply(void 0,arguments)}},model:{value:t.newDepartmentName,callback:function(e){t.newDepartmentName=e},expression:"newDepartmentName"}})],1),a("v-uni-button",{staticClass:"add-department-btn",attrs:{disabled:!t.newDepartmentName.trim()},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addNewDepartment.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"plus",size:"16",color:"#FFFFFF"}}),t._v("添加部门")],1)],1),a("v-uni-scroll-view",{staticClass:"selector-list-container",attrs:{"scroll-y":!0}},[0===t.departmentOptions.length?a("v-uni-view",{staticClass:"empty-department-hint"},[a("uni-icons",{attrs:{type:"info",size:"32",color:"#c4c4c4"}}),a("v-uni-text",{staticClass:"empty-text"},[t._v("暂无部门数据")]),a("v-uni-text",{staticClass:"empty-desc"},[t._v("请在上方输入框中添加新部门")])],1):t._e(),t._l(t.departmentOptions,(function(e){return a("v-uni-view",{key:e.value,staticClass:"selector-item",class:{selected:t.formData.department===e.value},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectDepartment(e)}}},[a("v-uni-view",{staticClass:"selector-item-left"},[t.formData.department===e.value?a("v-uni-view",{staticClass:"selected-icon"},[a("uni-icons",{attrs:{type:"checkmarkempty",size:"18",color:"#3a86ff"}})],1):t._e()],1),a("v-uni-view",{staticClass:"selector-item-content"},[a("v-uni-text",{staticClass:"selector-name"},[t._v(t._s(e.text))])],1),a("v-uni-view",{staticClass:"selector-item-right"},[t.departmentOptions.length>1?a("v-uni-view",{staticClass:"delete-department-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.deleteDepartment(e)}}},[a("uni-icons",{attrs:{type:"trash",size:"14",color:"#ef4444"}})],1):t._e()],1)],1)}))],2)],1)],1):t._e(),t.showHonorTypeSelector?a("v-uni-view",{staticClass:"selector-modal-overlay",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeHonorTypeSelector.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"selector-modal-content",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[a("v-uni-view",{staticClass:"selector-modal-header"},[a("v-uni-text",{staticClass:"selector-modal-title"},[t._v("选择荣誉类型")]),a("v-uni-view",{staticClass:"selector-modal-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeHonorTypeSelector.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"close",size:"18",color:"#8a94a6"}})],1)],1),a("v-uni-scroll-view",{staticClass:"honor-type-list-container",attrs:{"scroll-y":!0}},t._l(t.honorTypeOptions,(function(e){return a("v-uni-view",{key:e.value,staticClass:"selector-item",class:{selected:t.selectedHonorType&&t.selectedHonorType._id===e.value},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectHonorType(e)}}},[a("v-uni-view",{staticClass:"selector-item-left"},[t.selectedHonorType&&t.selectedHonorType._id===e.value?a("v-uni-view",{staticClass:"selected-icon"},[a("uni-icons",{attrs:{type:"checkmarkempty",size:"18",color:"#3a86ff"}})],1):t._e()],1),a("v-uni-view",{staticClass:"selector-item-content"},[a("v-uni-text",{staticClass:"selector-name"},[t._v(t._s(e.text))]),e.color?a("v-uni-view",{staticClass:"color-indicator",style:{backgroundColor:e.color}}):t._e()],1)],1)})),1)],1)],1):t._e(),t.showBatchSelector?a("v-uni-view",{staticClass:"batch-modal-overlay",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeBatchSelector.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"batch-modal-content",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[a("v-uni-view",{staticClass:"batch-modal-header"},[a("v-uni-text",{staticClass:"batch-modal-title"},[t._v("选择表彰批次")]),a("v-uni-view",{staticClass:"batch-modal-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeBatchSelector.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"close",size:"18",color:"#8a94a6"}})],1)],1),a("v-uni-view",{staticClass:"batch-search-section"},[a("uni-easyinput",{attrs:{placeholder:"搜索批次名称...",prefixIcon:"search",clearable:!0},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onBatchSearch.apply(void 0,arguments)}},model:{value:t.batchSearchKeyword,callback:function(e){t.batchSearchKeyword=e},expression:"batchSearchKeyword"}})],1),a("v-uni-scroll-view",{staticClass:"batch-list-container",attrs:{"scroll-y":!0}},[t.recentBatches.length>0?a("v-uni-view",{staticClass:"batch-group"},[a("v-uni-view",{staticClass:"batch-group-header"},[a("uni-icons",{attrs:{type:"calendar",size:"16",color:"#3a86ff"}}),a("v-uni-text",{staticClass:"batch-group-title"},[t._v("最近批次")])],1),t._l(t.recentBatches,(function(e){return a("v-uni-view",{key:e.value,staticClass:"batch-item",class:{selected:t.selectedBatch&&t.selectedBatch._id===e.value},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectBatch(e)}}},[a("v-uni-view",{staticClass:"batch-item-left"},[t.selectedBatch&&t.selectedBatch._id===e.value?a("v-uni-view",{staticClass:"selected-icon"},[a("uni-icons",{attrs:{type:"checkmarkempty",size:"18",color:"#3a86ff"}})],1):t._e()],1),a("v-uni-view",{staticClass:"batch-item-content"},[a("v-uni-text",{staticClass:"batch-name"},[t._v(t._s(e.text))]),a("v-uni-text",{staticClass:"batch-date"},[t._v(t._s(t.formatBatchDate(e.data.meetingDate)))])],1),a("v-uni-view",{staticClass:"batch-type-tag",class:e.data.type?"type-"+e.data.type:""},[t._v(t._s(t.getBatchTypeText(e.data.type)))])],1)}))],2):t._e(),t.currentMonthBatches.length>0?a("v-uni-view",{staticClass:"batch-group"},[a("v-uni-view",{staticClass:"batch-group-header"},[a("uni-icons",{attrs:{type:"calendar-filled",size:"16",color:"#10b981"}}),a("v-uni-text",{staticClass:"batch-group-title"},[t._v("本月批次")])],1),t._l(t.currentMonthBatches,(function(e){return a("v-uni-view",{key:e.value,staticClass:"batch-item",class:{selected:t.selectedBatch&&t.selectedBatch._id===e.value},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectBatch(e)}}},[a("v-uni-view",{staticClass:"batch-item-left"},[t.selectedBatch&&t.selectedBatch._id===e.value?a("v-uni-view",{staticClass:"selected-icon"},[a("uni-icons",{attrs:{type:"checkmarkempty",size:"18",color:"#3a86ff"}})],1):t._e()],1),a("v-uni-view",{staticClass:"batch-item-content"},[a("v-uni-text",{staticClass:"batch-name"},[t._v(t._s(e.text))]),a("v-uni-text",{staticClass:"batch-date"},[t._v(t._s(t.formatBatchDate(e.data.meetingDate)))])],1),a("v-uni-view",{staticClass:"batch-type-tag",class:e.data.type?"type-"+e.data.type:""},[t._v(t._s(t.getBatchTypeText(e.data.type)))])],1)}))],2):t._e(),t.filteredOlderBatches.length>0?a("v-uni-view",{staticClass:"batch-group"},[a("v-uni-view",{staticClass:"batch-group-header"},[a("uni-icons",{attrs:{type:"folder",size:"16",color:"#8b5cf6"}}),a("v-uni-text",{staticClass:"batch-group-title"},[t._v("历史批次")]),a("v-uni-text",{staticClass:"batch-count"},[t._v(t._s(t.olderBatches.length)+"个")])],1),t._l(t.filteredOlderBatches,(function(e){return a("v-uni-view",{key:e.value,staticClass:"batch-item",class:{selected:t.selectedBatch&&t.selectedBatch._id===e.value},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectBatch(e)}}},[a("v-uni-view",{staticClass:"batch-item-left"},[t.selectedBatch&&t.selectedBatch._id===e.value?a("v-uni-view",{staticClass:"selected-icon"},[a("uni-icons",{attrs:{type:"checkmarkempty",size:"18",color:"#3a86ff"}})],1):t._e()],1),a("v-uni-view",{staticClass:"batch-item-content"},[a("v-uni-text",{staticClass:"batch-name"},[t._v(t._s(e.text))]),a("v-uni-text",{staticClass:"batch-date"},[t._v(t._s(t.formatBatchDate(e.data.meetingDate)))])],1),a("v-uni-view",{staticClass:"batch-type-tag",class:e.data.type?"type-"+e.data.type:""},[t._v(t._s(t.getBatchTypeText(e.data.type)))])],1)})),t.olderBatches.length>t.showOlderBatchCount?a("v-uni-view",{staticClass:"show-more-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showMoreOlderBatches.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"show-more-text"},[t._v("显示更多 ("+t._s(t.olderBatches.length-t.showOlderBatchCount)+"个)")]),a("uni-icons",{attrs:{type:"bottom",size:"14",color:"#3a86ff"}})],1):t._e()],2):t._e(),0===t.filteredBatchOptions.length?a("v-uni-view",{staticClass:"batch-empty-state"},[a("uni-icons",{attrs:{type:"search",size:"40",color:"#c4c4c4"}}),a("v-uni-text",{staticClass:"empty-text"},[t._v("未找到匹配的批次")])],1):t._e()],1),a("v-uni-view",{staticClass:"batch-modal-footer"},[a("v-uni-button",{staticClass:"batch-footer-btn secondary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.quickCreateBatch.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"plus",size:"16",color:"#3a86ff"}}),t._v("快速创建")],1),a("v-uni-button",{staticClass:"batch-footer-btn primary",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeBatchSelector.apply(void 0,arguments)}}},[t._v("确定")])],1)],1)],1):t._e(),t.loading?a("v-uni-view",{staticClass:"loading-overlay"},[a("v-uni-view",{staticClass:"loading-content"},[a("uni-icons",{staticClass:"loading-spin",attrs:{type:"spinner-cycle",size:"32",color:"#3a86ff"}}),a("v-uni-text",{staticClass:"loading-text"},[t._v(t._s(t.loadingText))])],1)],1):t._e()],1)},o=[]},"4aa8":function(t){t.exports=JSON.parse('{"uni-popup.cancel":"cancel","uni-popup.ok":"ok","uni-popup.placeholder":"pleace enter","uni-popup.title":"Hint","uni-popup.shareTitle":"Share to"}')},"4b26":function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("7a76"),a("c9b5"),a("18f7"),a("de6c"),a("dc89"),a("2425"),a("4626"),a("aa9c");var r=n(a("2634")),o=n(a("2fdc")),i=n(a("9b1b")),s=n(a("80b1")),c=n(a("efe5")),l=function(){function t(){(0,s.default)(this,t),this.defaultConfig={avatar:{maxWidth:800,maxHeight:800,quality:.6,maxSize:512e3,format:"jpg"},honor:{maxWidth:1600,maxHeight:1600,quality:.8,maxSize:1048576,format:"jpg"}},this.canvas=document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),this.ctx&&(this.ctx.imageSmoothingEnabled=!0,this.ctx.imageSmoothingQuality="high")}return(0,c.default)(t,[{key:"handleError",value:function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=(null===t||void 0===t?void 0:t.message)||"".concat(e,"失败"),r=(null===t||void 0===t?void 0:t.code)||"UNKNOWN_ERROR",o=(0,i.default)({success:!1,error:{message:n,code:r,details:t,timestamp:(new Date).toISOString()}},a);return console.error("[ImageUtils] ".concat(e,"失败:"),{message:n,code:r,timestamp:o.error.timestamp}),o}},{key:"compressImage",value:function(){var t=(0,o.default)((0,r.default)().mark((function t(e){var a,n,o,s,c,l=arguments;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=l.length>1&&void 0!==l[1]?l[1]:"avatar",n=l.length>2&&void 0!==l[2]?l[2]:{},o=(0,i.default)((0,i.default)((0,i.default)({},this.defaultConfig[a]),n),{},{type:a}),s=n.originalSize,t.prev=4,t.next=7,this.compressImageH5(e,o,s);case 7:return c=t.sent,t.abrupt("return",(0,i.default)({success:!0},c));case 11:return t.prev=11,t.t0=t["catch"](4),t.abrupt("return",this.handleError(t.t0,"图片压缩",{path:e,size:s||0,width:0,height:0,compressed:!1,originalSize:s||0,compressionRatio:0}));case 14:case"end":return t.stop()}}),t,this,[[4,11]])})));return function(e){return t.apply(this,arguments)}}()},{key:"compressImageH5",value:function(){var t=(0,o.default)((0,r.default)().mark((function t(e,a,n){var o=this;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,r){var i=new Image;i.crossOrigin="anonymous",i.onload=function(){try{var e,s=null;if("avatar"===a.type){var c=Math.min(a.maxWidth,a.maxHeight);e={width:c,height:c},s=o.calculateCropSize(i.width,i.height,c,c)}else e=o.calculateSize(i.width,i.height,a.maxWidth||800,a.maxHeight||800);o.canvas.width=e.width,o.canvas.height=e.height,s?o.ctx.drawImage(i,s.offsetX,s.offsetY,s.sourceWidth-2*s.offsetX,s.sourceHeight-2*s.offsetY,0,0,e.width,e.height):o.ctx.drawImage(i,0,0,e.width,e.height),o.canvas.toBlob((function(i){if(i){var s=URL.createObjectURL(i),c=i.size;c>a.maxSize?o.canvas.toBlob((function(a){if(a){var o=URL.createObjectURL(a),i=a.size;t({path:o,size:i,width:e.width,height:e.height,compressed:!0,originalSize:n||0,compressionRatio:n>0?100*(1-i/n):0})}else r(new Error("二次压缩失败"))}),"image/jpeg",.8):t({path:s,size:c,width:e.width,height:e.height,compressed:!0,originalSize:n||0,compressionRatio:n>0?100*(1-c/n):0})}else r(new Error("图片压缩失败"))}),"image/jpeg",a.quality||.9)}catch(l){r(l)}},i.onerror=function(){r(new Error("图片加载失败"))},i.src=e})));case 1:case"end":return t.stop()}}),t)})));return function(e,a,n){return t.apply(this,arguments)}}()},{key:"calculateSize",value:function(t,e,a,n){var r=t,o=e;return t>a&&(r=a,o=Math.round(e*(a/t))),o>n&&(o=n,r=Math.round(t*(n/e))),{width:r,height:o}}},{key:"calculateCropSize",value:function(t,e,a,n){var r=Math.min(t/a,e/n),o=a*r,i=n*r,s=Math.max(0,(t-o)/2),c=Math.max(0,(e-i)/2);return{sourceWidth:t,sourceHeight:e,targetWidth:a,targetHeight:n,offsetX:Math.round(s),offsetY:Math.round(c),scale:r}}},{key:"getFileSize",value:function(){var t=(0,o.default)((0,r.default)().mark((function t(e){var a;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.getFileInfoAsync(e);case 3:return a=t.sent,t.abrupt("return",a.size);case 7:throw t.prev=7,t.t0=t["catch"](0),new Error("获取文件大小失败: "+t.t0.message);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));return function(e){return t.apply(this,arguments)}}()},{key:"formatFileSize",value:function(t){if(0===t)return"0 B";var e=Math.floor(Math.log(t)/Math.log(1024));return(t/Math.pow(1024,e)).toFixed(2)+" "+["B","KB","MB","GB"][e]}},{key:"validateImageFormat",value:function(t){var e,a=null===(e=t.split(".").pop())||void 0===e?void 0:e.toLowerCase();return a&&["jpg","jpeg","png","webp"].includes(a)?{valid:!0,format:a}:{valid:!1,message:"不支持的图片格式，请选择JPG、PNG或WebP格式"}}},{key:"generateThumbnail",value:function(){var t=(0,o.default)((0,r.default)().mark((function t(e){var a,n,o=arguments;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=o.length>1&&void 0!==o[1]?o[1]:100,t.prev=1,t.next=4,this.compressImage(e,"honor",{maxWidth:a,maxHeight:a,quality:.6,maxSize:51200});case 4:return n=t.sent,t.abrupt("return",n);case 8:throw t.prev=8,t.t0=t["catch"](1),new Error("生成缩略图失败: "+t.t0.message);case 11:case"end":return t.stop()}}),t,this,[[1,8]])})));return function(e){return t.apply(this,arguments)}}()},{key:"batchCompressImages",value:function(){var t=(0,o.default)((0,r.default)().mark((function t(e){var a,n,o,i,s,c,l=arguments;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=l.length>1&&void 0!==l[1]?l[1]:"honor",n=l.length>2?l[2]:void 0,o=e.length,i=[],s=0;case 5:if(!(s<o)){t.next=21;break}return t.prev=6,t.next=9,this.compressImage(e[s],a);case 9:c=t.sent,i.push({success:c.success,result:c.success?c:null,error:c.success?null:c.error}),n&&n({current:s+1,total:o,path:e[s],success:c.success}),t.next=18;break;case 14:t.prev=14,t.t0=t["catch"](6),i.push({success:!1,result:null,error:t.t0.message,path:e[s]}),n&&n({current:s+1,total:o,path:e[s],success:!1,error:t.t0.message});case 18:s++,t.next=5;break;case 21:return t.abrupt("return",i);case 22:case"end":return t.stop()}}),t,this,[[6,14]])})));return function(e){return t.apply(this,arguments)}}()}]),t}(),u=new l;e.default=u},"4ea0":function(t,e,a){"use strict";(function(t,n){a("6a54");var r=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7a76"),a("c9b5"),a("8f71"),a("bf0f"),a("fd3c"),a("c223"),a("9db6"),a("18f7"),a("de6c"),a("d4b5"),a("dc89"),a("2425"),a("aa9c"),a("f7a5"),a("2797"),a("bd06"),a("473f"),a("ab80");var o=r(a("b7c7")),i=r(a("2634")),s=r(a("9b1b")),c=r(a("2fdc")),l=r(a("39d8")),u=r(a("80b1")),d=r(a("efe5")),p=r(a("4b26")),f=function(){function e(){var t;(0,u.default)(this,e),this.maxRetries=3,this.retryDelay=1e3,this.chunkSize=1048576,this.errorTypes={NETWORK:"NETWORK_ERROR",TIMEOUT:"TIMEOUT_ERROR",SIZE:"SIZE_ERROR",FORMAT:"FORMAT_ERROR",COMPRESS:"COMPRESS_ERROR",UPLOAD:"UPLOAD_ERROR"},this.retryStrategies=(t={},(0,l.default)(t,this.errorTypes.NETWORK,!0),(0,l.default)(t,this.errorTypes.TIMEOUT,!0),(0,l.default)(t,this.errorTypes.UPLOAD,!0),(0,l.default)(t,this.errorTypes.COMPRESS,!1),(0,l.default)(t,this.errorTypes.FORMAT,!1),(0,l.default)(t,this.errorTypes.SIZE,!1),t)}return(0,d.default)(e,[{key:"withRetry",value:function(){var t=(0,c.default)((0,i.default)().mark((function t(e){var a,n,r,o,c,l,u,d,p,f=arguments;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=f.length>1&&void 0!==f[1]?f[1]:{},n=a.retries,r=void 0===n?0:n,o=a.errorType,c=void 0===o?this.errorTypes.UPLOAD:o,l=a.context,u=void 0===l?{}:l,t.prev=2,t.next=5,e();case 5:return t.abrupt("return",t.sent);case 8:if(t.prev=8,t.t0=t["catch"](2),d=this.retryStrategies[c]&&r<this.maxRetries,!d){t.next=17;break}return p=this.retryDelay*Math.pow(2,r),t.next=15,this.delay(p);case 15:return console.log("重试第".concat(r+1,"次:"),{errorType:c,delay:p,context:u}),t.abrupt("return",this.withRetry(e,(0,s.default)((0,s.default)({},a),{},{retries:r+1})));case 17:throw t.t0;case 18:case"end":return t.stop()}}),t,this,[[2,8]])})));return function(e){return t.apply(this,arguments)}}()},{key:"uploadAvatar",value:function(){var t=(0,c.default)((0,i.default)().mark((function t(e){var a,n,r,o,s,c,l,u,d,f,h,v,m,b,g,y,w=arguments;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=w.length>1&&void 0!==w[1]?w[1]:{},n=a.compress,r=void 0===n||n,o=a.onProgress,s=a.customPath,c=a.originalSize,t.prev=2,h=e,v=null,!r){t.next=11;break}return t.next=8,p.default.compressImage(e,"avatar",{originalSize:c});case 8:m=t.sent,h=m.path,v=m;case 11:return b=s||this.generateAvatarPath(),t.next=14,this.uploadToCloud(h,b,{onProgress:o,fileType:"avatar"});case 14:return g=t.sent,t.next=17,this.getFileInfo(g.fileID);case 17:return y=t.sent,t.abrupt("return",{success:!0,cloudPath:g.fileID,url:y.tempFileURL||g.fileID,size:g.actualSize||(null===(l=v)||void 0===l?void 0:l.size)||0,compressed:(null===(u=v)||void 0===u?void 0:u.compressed)||!1,compressionRatio:(null===(d=v)||void 0===d?void 0:d.compressionRatio)||0,originalSize:c||(null===(f=v)||void 0===f?void 0:f.originalSize)||0});case 21:throw t.prev=21,t.t0=t["catch"](2),console.error("头像上传失败:",t.t0),new Error("头像上传失败: ".concat(t.t0.message));case 25:case"end":return t.stop()}}),t,this,[[2,21]])})));return function(e){return t.apply(this,arguments)}}()},{key:"uploadHonorImages",value:function(){var t=(0,c.default)((0,i.default)().mark((function t(e){var a,n,r,c,l,u,d,f,h,v,m,b,g,y,w,x=this,k=arguments;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=k.length>1&&void 0!==k[1]?k[1]:{},n=a.compress,r=void 0===n||n,c=a.onProgress,l=a.onItemProgress,u=a.maxConcurrent,d=void 0===u?3:u,f=Array.isArray(e)?e:[e],console.log("🚀 开始上传表彰图片:",{pathCount:f.length,compress:r,maxConcurrent:d}),0!==f.length){t.next=6;break}return t.abrupt("return",{success:!0,results:[]});case 6:if(t.prev=6,m=[],!r){t.next=16;break}return console.log("🔧 开始压缩图片..."),t.next=12,p.default.batchCompressImages(f,"honor",(function(t){c&&c((0,s.default)({phase:"compress",progress:Math.round(t.current/t.total*100)},t))}));case 12:m=t.sent,console.log("✅ 压缩完成:",{total:m.length,success:m.filter((function(t){return t.success})).length}),t.next=18;break;case 16:m=f.map((function(t){return{success:!0,result:{path:t,compressed:!1,size:0,width:0,height:0,originalSize:0,compressionRatio:0}}})),console.log("⏩ 跳过压缩，直接上传");case 18:if(b=m.filter((function(t){return t.success&&t.result&&t.result.path})).map((function(t){return t.result.path})),console.log("🔍 有效路径检查:",{total:m.length,valid:b.length}),0!==b.length){t.next=22;break}throw new Error("没有可上传的图片");case 22:return console.log("📤 开始批量上传..."),t.next=25,this.batchUpload(b,{onProgress:function(t){c&&c((0,s.default)({phase:"upload",progress:Math.round(t.current/t.total*100)},t))},onItemProgress:l,maxConcurrent:d,pathGenerator:function(){return x.generateHonorImagePath()}});case 25:return g=t.sent,console.log("✅ 上传完成:",{uploaded:(null===(h=g.summary)||void 0===h?void 0:h.successful)||0,failed:(null===(v=g.summary)||void 0===v?void 0:v.failed)||0}),y=m.filter((function(t){return!t.success})),w=[].concat((0,o.default)(g.results),(0,o.default)(y.map((function(t){return{success:!1,error:t.error||"图片压缩失败",filePath:t.path}})))),t.abrupt("return",{success:!0,results:w,totalUploaded:g.results.filter((function(t){return t.success})).length,totalFailed:w.filter((function(t){return!t.success})).length});case 32:throw t.prev=32,t.t0=t["catch"](6),console.error("表彰图片上传失败:",t.t0),new Error("表彰图片上传失败: ".concat(t.t0.message));case 36:case"end":return t.stop()}}),t,this,[[6,32]])})));return function(e){return t.apply(this,arguments)}}()},{key:"uploadToCloud",value:function(){var e=(0,c.default)((0,i.default)().mark((function e(a,n){var r,o,c,l,u,d,p,f,h,v,m,b,g=this,y=arguments;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=y.length>2&&void 0!==y[2]?y[2]:{},o=r.onProgress,c=r.fileType,void 0===c?"image":c,l=r.useCache,u=void 0===l||l,d=r.cacheTime,p=void 0===d?36e5:d,f="upload_".concat(a,"_").concat(n),!u){e.next=10;break}return e.next=6,this.getCache(f);case 6:if(h=e.sent,!h){e.next=10;break}return console.log("使用上传缓存:",h),e.abrupt("return",h);case 10:return e.prev=10,m=new Promise((function(e,i){a.startsWith("data:image/")?v=g.uploadBase64ToCloud(a,n,r).then(e).catch(i):(v=t.uploadFile({filePath:a,cloudPath:n,cloudPathAsRealPath:!0,onUploadProgress:function(t){if(o){var e=Math.round(t.loaded/t.total*100);o({loaded:t.loaded,total:t.total,progress:e,speed:g.calculateSpeed(t)})}}}),v.then((function(t){if(t.success){var a=(0,s.default)((0,s.default)({},t),{},{actualSize:t.fileSize||t.size||0});u&&g.setCache(f,a,p),e(a)}else i(new Error(t.errMsg||"上传失败"))})).catch(i))})),b=new Promise((function(t,e){setTimeout((function(){e(new Error("上传超时"))}),r.timeout||3e4)})),e.next=15,this.withRetry((function(){return Promise.race([m,b])}),{errorType:this.errorTypes.UPLOAD,context:{filePath:a,cloudPath:n}});case 15:return e.abrupt("return",e.sent);case 18:throw e.prev=18,e.t0=e["catch"](10),console.error("上传失败:",e.t0),e.t0;case 22:case"end":return e.stop()}}),e,this,[[10,18]])})));return function(t,a){return e.apply(this,arguments)}}()},{key:"calculateSpeed",value:function(t){var e=Date.now();if(!this.lastProgress)return this.lastProgress={time:e,loaded:0},"0 KB/s";var a=e-this.lastProgress.time,n=t.loaded-this.lastProgress.loaded;if(a>0){var r=1e3*n/a;return this.lastProgress={time:e,loaded:t.loaded},r<1024?"".concat(r.toFixed(1)," B/s"):r<1048576?"".concat((r/1024).toFixed(1)," KB/s"):"".concat((r/1048576).toFixed(1)," MB/s")}return"计算中..."}},{key:"getCache",value:function(){var t=(0,c.default)((0,i.default)().mark((function t(e){var a,n,r,o;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,a=uni.getStorageSync("upload_cache_".concat(e)),!a){t.next=6;break}if(n=JSON.parse(a),r=n.data,o=n.expire,!(o>Date.now())){t.next=6;break}return t.abrupt("return",r);case 6:return t.abrupt("return",null);case 9:return t.prev=9,t.t0=t["catch"](0),console.warn("读取上传缓存失败:",t.t0),t.abrupt("return",null);case 13:case"end":return t.stop()}}),t,null,[[0,9]])})));return function(e){return t.apply(this,arguments)}}()},{key:"setCache",value:function(t,e,a){try{var n={data:e,expire:Date.now()+a};uni.setStorageSync("upload_cache_".concat(t),JSON.stringify(n))}catch(r){console.warn("设置上传缓存失败:",r)}}},{key:"uploadBase64ToCloud",value:function(){var e=(0,c.default)((0,i.default)().mark((function e(a,n){var r,o,s,c,l,u,d,p,f=arguments;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=f.length>2&&void 0!==f[2]?f[2]:{},o=r.onProgress,e.prev=2,console.log("开始处理Base64上传:",{cloudPath:n}),e.next=6,fetch(a);case 6:return s=e.sent,e.next=9,s.blob();case 9:if(c=e.sent,c&&0!==c.size){e.next=12;break}throw new Error("图片数据为空");case 12:return console.log("Blob信息:",{size:c.size,type:c.type}),l=new File([c],"image.jpg",{type:c.type||"image/jpeg"}),u=new FormData,u.append("file",l),d=URL.createObjectURL(l),console.log("创建临时URL:",d),e.next=20,t.uploadFile({filePath:d,cloudPath:n,cloudPathAsRealPath:!0,onUploadProgress:function(t){if(o){var e=Math.round(t.loaded/t.total*100);o({loaded:t.loaded,total:t.total,progress:e})}}});case 20:return p=e.sent,console.log("Base64上传成功:",p),URL.revokeObjectURL(d),e.abrupt("return",p);case 26:throw e.prev=26,e.t0=e["catch"](2),console.error("Base64上传失败:",e.t0),e.t0;case 30:case"end":return e.stop()}}),e,null,[[2,26]])})));return function(t,a){return e.apply(this,arguments)}}()},{key:"batchUpload",value:function(){var t=(0,c.default)((0,i.default)().mark((function t(e){var a,r,l,u,d,p,f,h,v,m,b,g,y,w,x,k,C,_,z,D,T,S,P,B,E,F,O,I,A=this,M=arguments;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:for(a=M.length>1&&void 0!==M[1]?M[1]:{},r=a.onProgress,l=a.onItemProgress,u=a.maxConcurrent,d=void 0===u?3:u,p=a.pathGenerator,f=a.chunkSize,h=void 0===f?this.chunkSize:f,v=a.autoRetry,m=void 0===v||v,b=[],g=0;g<e.length;g+=h)b.push(e.slice(g,g+h));y=[],w=e.length,x=0,k=0,C=0,_=function(){return!1},z=function(){var t=(0,c.default)((0,i.default)().mark((function t(e,a){var n,o,c;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,k++,n=p?p():A.generateImagePath(),t.next=5,A.withRetry((function(){return A.uploadToCloud(e,n,{onProgress:function(t){l&&l((0,s.default)({index:a,fileName:e},t))}})}),{errorType:A.errorTypes.UPLOAD,context:{filePath:e,index:a}});case 5:return o=t.sent,t.next=8,A.getFileInfo(o.fileID);case 8:return c=t.sent,x++,r&&r({current:x,total:w,progress:Math.round(x/w*100),activeUploads:k,memoryUsage:C}),t.abrupt("return",{success:!0,index:a,filePath:e,cloudPath:o.fileID,url:c.tempFileURL||o.fileID,size:o.actualSize});case 14:return t.prev=14,t.t0=t["catch"](0),x++,console.error("文件上传失败 [".concat(a,"]:"),t.t0),r&&r({current:x,total:w,progress:Math.round(x/w*100),activeUploads:k,memoryUsage:C}),t.abrupt("return",{success:!1,index:a,filePath:e,error:t.t0.message});case 20:return t.prev=20,k--,t.finish(20);case 23:case"end":return t.stop()}}),t,null,[[0,14,20,23]])})));return function(e,a){return t.apply(this,arguments)}}(),D=0,T=b;case 12:if(!(D<T.length)){t.next=36;break}S=T[D];case 14:if(!_()){t.next=19;break}return t.next=17,this.delay(1e3);case 17:t.next=14;break;case 19:[],P=0;case 21:if(!(P<S.length)){t.next=33;break}return B=S.slice(P,P+d),E=B.map((function(t,e){return z(t,y.length+e)})),t.next=26,Promise.all(E);case 26:F=t.sent,y.push.apply(y,(0,o.default)(F)),"undefined"!==typeof n&&n.gc&&n.gc(),F.forEach((function(t){t.success&&t.filePath&&t.filePath.startsWith("blob:")&&URL.revokeObjectURL(t.filePath)}));case 30:P+=d,t.next=21;break;case 33:D++,t.next=12;break;case 36:if(!m){t.next=44;break}if(O=y.filter((function(t){return!t.success})),!(O.length>0)){t.next=44;break}return console.log("重试".concat(O.length,"个失败的上传")),t.next=42,this.batchUpload(O.map((function(t){return t.filePath})),(0,s.default)((0,s.default)({},a),{},{autoRetry:!1}));case 42:I=t.sent,I.results.forEach((function(t){var e=y.findIndex((function(e){return e.filePath===t.filePath}));-1!==e&&(y[e]=t)}));case 44:return t.abrupt("return",{success:!0,results:y,summary:{total:w,successful:y.filter((function(t){return t.success})).length,failed:y.filter((function(t){return!t.success})).length,totalSize:y.reduce((function(t,e){return t+(e.size||0)}),0)}});case 45:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}()},{key:"generateAvatarPath",value:function(){var t=Date.now(),e=Math.random().toString(36).substring(2,8);return"avatars/".concat(t,"_").concat(e,".jpg")}},{key:"generateHonorImagePath",value:function(){var t=Date.now(),e=Math.random().toString(36).substring(2,8);return"honor-images/".concat(t,"_").concat(e,".jpg")}},{key:"generateImagePath",value:function(){var t=Date.now(),e=Math.random().toString(36).substring(2,8);return"images/".concat(t,"_").concat(e,".jpg")}},{key:"delay",value:function(t){return new Promise((function(e){return setTimeout(e,t)}))}},{key:"deleteCloudFiles",value:function(){var e=(0,c.default)((0,i.default)().mark((function e(a){var n,r;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=Array.isArray(a)?a:[a],e.prev=1,e.next=4,t.deleteFile({fileList:n});case 4:return r=e.sent,e.abrupt("return",{success:!0,deletedCount:r.fileList.filter((function(t){return 0===t.status})).length,failedCount:r.fileList.filter((function(t){return 0!==t.status})).length,details:r.fileList});case 8:throw e.prev=8,e.t0=e["catch"](1),console.error("删除云存储文件失败:",e.t0),new Error("删除文件失败: ".concat(e.t0.message));case 12:case"end":return e.stop()}}),e,null,[[1,8]])})));return function(t){return e.apply(this,arguments)}}()},{key:"getFileInfo",value:function(){var e=(0,c.default)((0,i.default)().mark((function e(a){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.getTempFileURL({fileList:[a]});case 3:if(n=e.sent,!(n.fileList&&n.fileList.length>0)){e.next=8;break}return e.abrupt("return",n.fileList[0]);case 8:throw new Error("文件不存在");case 9:e.next=15;break;case 11:return e.prev=11,e.t0=e["catch"](0),console.error("获取文件信息失败:",e.t0),e.abrupt("return",{fileID:a,tempFileURL:a,status:0});case 15:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t){return e.apply(this,arguments)}}()}]),e}(),h=new f,v=h;e.default=v}).call(this,a("861b")["uniCloud"],a("0ee4"))},"6d9a":function(t,e,a){var n=a("a061");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=a("967d").default;r("72f11910",n,!0,{sourceMap:!1,shadowMode:!1})},"72b8":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"p-empty-state",style:t.containerStyle},[a("v-uni-image",{staticClass:"p-empty-state__icon",style:t.iconStyle,attrs:{src:t.icon||t.defaultIcon,mode:"aspectFit"}}),a("v-uni-text",{staticClass:"p-empty-state__text",style:{color:t.textColor}},[t._v(t._s(t.text||"暂无数据"))]),t.showAction?a("v-uni-button",{staticClass:"p-empty-state__action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$emit("action")}}},[t._v(t._s(t.actionText))]):t._e()],1)},r=[]},"73e1":function(t,e,a){"use strict";var n=a("29d8");t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(n)},"795c":function(t,e,a){"use strict";var n=a("8bdb"),r=a("db04").start,o=a("73e1");n({target:"String",proto:!0,forced:o},{padStart:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},"854b":function(t,e,a){"use strict";a.r(e);var n=a("c01d"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"8f87":function(t){t.exports=JSON.parse('{"uni-popup.cancel":"取消","uni-popup.ok":"確定","uni-popup.placeholder":"請輸入","uni-popup.title":"提示","uni-popup.shareTitle":"分享到"}')},9004:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!==(0,n.default)(t)&&"function"!==typeof t)return{default:t};var a=r(e);if(a&&a.has(t))return a.get(t);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&Object.prototype.hasOwnProperty.call(t,s)){var c=i?Object.getOwnPropertyDescriptor(t,s):null;c&&(c.get||c.set)?Object.defineProperty(o,s,c):o[s]=t[s]}o["default"]=t,a&&a.set(t,o);return o},a("bf0f"),a("18f7"),a("d0af"),a("de6c"),a("6a54"),a("9a2c");var n=function(t){return t&&t.__esModule?t:{default:t}}(a("fcf3"));function r(t){if("function"!==typeof WeakMap)return null;var e=new WeakMap,a=new WeakMap;return(r=function(t){return t?a:e})(t)}},"9b76":function(t,e,a){"use strict";a.r(e);var n=a("72b8"),r=a("b6c2");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("0777");var i=a("828b"),s=Object(i["a"])(r["default"],n["b"],n["c"],!1,null,"bf86960a",null,!1,n["a"],void 0);e["default"]=s.exports},"9ef6":function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("4aa8")),o=n(a("9f93")),i=n(a("8f87")),s={en:r.default,"zh-Hans":o.default,"zh-Hant":i.default};e.default=s},"9f93":function(t){t.exports=JSON.parse('{"uni-popup.cancel":"取消","uni-popup.ok":"确定","uni-popup.placeholder":"请输入","uni-popup.title":"提示","uni-popup.shareTitle":"分享到"}')},a061:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.add-record-container[data-v-269e1299]{min-height:100vh;background:linear-gradient(145deg,#f8faff,#e9f0f8);position:relative}.header[data-v-269e1299]{position:fixed;top:0;left:0;right:0;z-index:1000;background:linear-gradient(180deg,#3a86ff,#2563eb);overflow:hidden}.header[data-v-269e1299]::before{content:"";position:absolute;top:-50%;right:-20%;width:200%;height:200%;background:radial-gradient(circle,hsla(0,0%,100%,.1) 0,transparent 70%);-webkit-transform:rotate(-15deg);transform:rotate(-15deg);pointer-events:none}.header .header-content[data-v-269e1299]{display:flex;align-items:center;padding:%?20?% %?40?%;padding-top:calc(0px + %?20?%);position:relative;z-index:2}.header .header-content .back-btn[data-v-269e1299]{width:%?60?%;height:%?60?%;border-radius:50%;background:hsla(0,0%,100%,.2);display:flex;align-items:center;justify-content:center;margin-right:%?30?%}.header .header-content .title-area[data-v-269e1299]{flex:1}.header .header-content .title-area .title[data-v-269e1299]{display:block;font-size:%?36?%;font-weight:600;color:#fff;text-shadow:0 %?2?% %?8?% rgba(0,0,0,.2);line-height:1.2}.header .header-content .title-area .subtitle[data-v-269e1299]{display:block;font-size:%?24?%;color:hsla(0,0%,100%,.8);margin-top:%?4?%}.header .header-content .header-actions .save-btn[data-v-269e1299]{background:hsla(0,0%,100%,.2);color:#fff;border-radius:%?8?%;padding:%?12?% %?22?%;font-size:%?24?%;font-weight:500;border:none;min-width:%?88?%;height:%?75?%;display:flex;align-items:center;justify-content:center}.header .header-content .header-actions .save-btn[data-v-269e1299]:disabled{background:hsla(0,0%,100%,.1);color:hsla(0,0%,100%,.5)}.content-scroll[data-v-269e1299]{position:fixed;top:calc(0px + %?140?%);left:0;right:0;bottom:0}.form-container[data-v-269e1299]{padding:%?40?%}.form-section[data-v-269e1299]{background:#fff;border-radius:%?20?%;padding:%?40?%;margin-bottom:%?32?%}.form-section .section-header[data-v-269e1299]{display:flex;align-items:center;margin-bottom:%?32?%}.form-section .section-header .section-title[data-v-269e1299]{font-size:%?32?%;font-weight:600;color:#1a1a1a;margin-left:%?16?%}.form-item[data-v-269e1299]{margin-bottom:%?32?%}.form-item[data-v-269e1299]:last-child{margin-bottom:0}.form-item .form-label[data-v-269e1299]{display:block;font-size:%?28?%;font-weight:500;color:#1a1a1a;margin-bottom:%?16?%}.form-item .form-label .required[data-v-269e1299]{color:#ef4444;margin-left:%?4?%}.form-item .form-desc[data-v-269e1299]{font-size:%?24?%;color:#8a94a6;margin-top:%?12?%;line-height:1.4}.form-item .char-count[data-v-269e1299]{text-align:right;font-size:%?22?%;color:#c4c4c4;margin-top:%?8?%}.switch-item[data-v-269e1299]{display:flex;align-items:center;justify-content:space-between}.switch-item .switch-label[data-v-269e1299]{font-size:%?28?%;color:#1a1a1a}.image-picker .image-list[data-v-269e1299]{display:flex;flex-wrap:wrap;gap:%?12?%;margin-bottom:%?16?%}.image-picker .image-list .image-item[data-v-269e1299]{position:relative;width:%?160?%;height:%?160?%;flex-shrink:0}.image-picker .image-list .image-item .image-preview[data-v-269e1299]{width:100%;height:100%;border-radius:%?12?%;object-fit:cover}.image-picker .image-list .image-item .image-remove[data-v-269e1299]{position:absolute;top:%?-8?%;right:%?-8?%;width:%?32?%;height:%?32?%;border-radius:50%;background:#ef4444;display:flex;align-items:center;justify-content:center;padding:0;line-height:1}.image-picker .image-list .image-item .image-remove[data-v-269e1299] :deep(.uni-icons){display:flex;align-items:center;justify-content:center;width:100%;height:100%}.image-picker .image-list .image-add[data-v-269e1299]{width:%?160?%;height:%?160?%;border:2px dashed #c4c4c4;border-radius:%?12?%;display:flex;flex-direction:column;align-items:center;justify-content:center;flex-shrink:0;transition:all .2s ease}.image-picker .image-list .image-add[data-v-269e1299]:active{background:rgba(58,134,255,.05);border-color:#3a86ff;-webkit-transform:scale(.98);transform:scale(.98)}.image-picker .image-list .image-add .add-text[data-v-269e1299]{font-size:%?24?%;color:#8a94a6;margin-top:%?8?%;font-weight:500}.image-picker .image-tip[data-v-269e1299]{font-size:%?22?%;color:#8a94a6;line-height:1.4}.preview-card[data-v-269e1299]{background:#f8f9fa;border-radius:%?16?%;padding:%?32?%;border:1px solid #e9ecef}.preview-card .preview-header[data-v-269e1299]{display:flex;align-items:center;margin-bottom:%?20?%}.preview-card .preview-header .preview-avatar[data-v-269e1299]{flex-shrink:0;width:%?80?%;height:%?80?%;border-radius:50%;margin-right:%?20?%;object-fit:cover;background-color:#f8f9fa;border:%?2?% solid #e5e7eb}.preview-card .preview-header .preview-info[data-v-269e1299]{flex:1}.preview-card .preview-header .preview-info .preview-name[data-v-269e1299]{display:block;font-size:%?28?%;font-weight:600;color:#1a1a1a;line-height:1.2}.preview-card .preview-header .preview-info .preview-dept[data-v-269e1299]{display:block;font-size:%?22?%;color:#8a94a6;margin-top:%?4?%}.preview-card .preview-header .preview-badge[data-v-269e1299]{display:flex;align-items:center;background:rgba(245,158,11,.1);border-radius:%?12?%;padding:%?8?% %?16?%}.preview-card .preview-header .preview-badge .badge-text[data-v-269e1299]{font-size:%?20?%;color:#f59e0b;margin-left:%?4?%}.preview-card .preview-type[data-v-269e1299]{margin-bottom:%?20?%}.preview-card .preview-type .type-tag[data-v-269e1299]{display:inline-block;background:#3a86ff;color:#fff;font-size:%?22?%;padding:%?8?% %?16?%;border-radius:%?12?%}.preview-card .preview-reason[data-v-269e1299]{font-size:%?26?%;color:#1a1a1a;line-height:1.5;margin-bottom:%?20?%}.preview-card .preview-images[data-v-269e1299]{display:flex;gap:%?8?%;margin-bottom:%?20?%}.preview-card .preview-images .preview-image[data-v-269e1299]{width:%?80?%;height:%?80?%;border-radius:%?8?%;object-fit:cover}.preview-card .preview-images .more-images[data-v-269e1299]{width:%?80?%;height:%?80?%;border-radius:%?8?%;background:rgba(0,0,0,.1);display:flex;align-items:center;justify-content:center;font-size:%?22?%;color:#8a94a6}.preview-card .preview-batch .batch-text[data-v-269e1299]{font-size:%?24?%;color:#3a86ff;font-weight:500}.preview-card .preview-batch .batch-date[data-v-269e1299]{font-size:%?22?%;color:#8a94a6;margin-left:%?16?%}.loading-overlay[data-v-269e1299]{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.3);z-index:600;display:flex;align-items:center;justify-content:center}.loading-overlay .loading-content[data-v-269e1299]{background:#fff;border-radius:%?20?%;padding:%?60?%;display:flex;flex-direction:column;align-items:center}.loading-overlay .loading-content .loading-spin[data-v-269e1299]{-webkit-animation:spin-data-v-269e1299 1s linear infinite;animation:spin-data-v-269e1299 1s linear infinite}.loading-overlay .loading-content .loading-text[data-v-269e1299]{font-size:%?28?%;color:#8a94a6;margin-top:%?24?%}.bottom-safe-area[data-v-269e1299]{height:env(safe-area-inset-bottom)}.custom-department-picker[data-v-269e1299],\n.custom-honor-type-picker[data-v-269e1299],\n.custom-batch-picker[data-v-269e1299]{min-height:%?88?%}.custom-department-picker .picker-display[data-v-269e1299],\n.custom-honor-type-picker .picker-display[data-v-269e1299],\n.custom-batch-picker .picker-display[data-v-269e1299]{display:flex;align-items:center;justify-content:space-between;padding:%?20?% %?20?%;border:1px solid #dcdfe6;border-radius:%?8?%;background:#fff;transition:all .2s ease}.custom-department-picker .picker-display[data-v-269e1299]:active,\n.custom-honor-type-picker .picker-display[data-v-269e1299]:active,\n.custom-batch-picker .picker-display[data-v-269e1299]:active{border-color:#3a86ff;background:rgba(58,134,255,.02)}.custom-department-picker .picker-display .selected-text[data-v-269e1299],\n.custom-honor-type-picker .picker-display .selected-text[data-v-269e1299],\n.custom-batch-picker .picker-display .selected-text[data-v-269e1299]{font-size:%?28?%;color:#606266}.custom-department-picker .picker-display .placeholder-text[data-v-269e1299],\n.custom-honor-type-picker .picker-display .placeholder-text[data-v-269e1299],\n.custom-batch-picker .picker-display .placeholder-text[data-v-269e1299]{font-size:%?28?%;color:#c0c4cc}.custom-input[data-v-269e1299] :deep(.uni-easyinput__placeholder-class){font-size:%?28?%!important;color:#606266!important}.custom-input[data-v-269e1299] :deep(.uni-easyinput__content-input){font-size:%?28?%!important}.custom-input[data-v-269e1299] :deep(.uni-easyinput__content-input)::-webkit-input-placeholder{font-size:%?28?%!important;color:#606266!important}.custom-input[data-v-269e1299] :deep(.uni-easyinput__content-input)::placeholder{font-size:%?28?%!important;color:#606266!important}.custom-input[data-v-269e1299] :deep(.uni-input-input){font-size:%?28?%!important}.custom-input[data-v-269e1299] :deep(.uni-input-input)::-webkit-input-placeholder{font-size:%?28?%!important;color:#606266!important}.custom-input[data-v-269e1299] :deep(.uni-input-input)::placeholder{font-size:%?28?%!important;color:#606266!important}.custom-textarea[data-v-269e1299] :deep(.uni-easyinput__placeholder-class){font-size:%?28?%!important;color:#606266!important}.custom-textarea[data-v-269e1299] :deep(.uni-easyinput__content-textarea){font-size:%?28?%!important}.custom-textarea[data-v-269e1299] :deep(.uni-easyinput__content-textarea)::-webkit-input-placeholder{font-size:%?28?%!important;color:#606266!important}.custom-textarea[data-v-269e1299] :deep(.uni-easyinput__content-textarea)::placeholder{font-size:%?28?%!important;color:#606266!important}.custom-textarea[data-v-269e1299] :deep(.uni-textarea-textarea){font-size:%?28?%!important}.custom-textarea[data-v-269e1299] :deep(.uni-textarea-textarea)::-webkit-input-placeholder{font-size:%?28?%!important;color:#606266!important}.custom-textarea[data-v-269e1299] :deep(.uni-textarea-textarea)::placeholder{font-size:%?28?%!important;color:#606266!important}.selector-modal-overlay[data-v-269e1299]{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.5);z-index:999;display:flex;align-items:flex-end}.selector-modal-overlay .selector-modal-content[data-v-269e1299]{width:100%;max-height:80vh;background:#fff;border-radius:%?32?% %?32?% 0 0;display:flex;flex-direction:column}.selector-modal-overlay .selector-modal-content .selector-modal-header[data-v-269e1299]{display:flex;align-items:center;justify-content:space-between;padding:%?40?% %?48?% %?32?%;border-bottom:1px solid #f0f0f0}.selector-modal-overlay .selector-modal-content .selector-modal-header .header-left[data-v-269e1299]{flex:1}.selector-modal-overlay .selector-modal-content .selector-modal-header .header-left .selector-modal-title[data-v-269e1299]{font-size:%?32?%;font-weight:600;color:#1a1a1a;display:block}.selector-modal-overlay .selector-modal-content .selector-modal-header .header-left .selector-modal-subtitle[data-v-269e1299]{font-size:%?24?%;color:#8a94a6;margin-top:%?4?%;display:block}.selector-modal-overlay .selector-modal-content .selector-modal-header .selector-modal-close[data-v-269e1299]{width:%?60?%;height:%?60?%;border-radius:50%;background:#f8f9fa;display:flex;align-items:center;justify-content:center}.selector-modal-overlay .selector-modal-content .selector-modal-header .selector-modal-close[data-v-269e1299]:active{background:#e9ecef}.selector-modal-overlay .selector-modal-content .department-info-section[data-v-269e1299]{padding:%?20?% %?48?%;background:#f8faff;border-bottom:1px solid #e5e7eb}.selector-modal-overlay .selector-modal-content .department-info-section .info-item[data-v-269e1299]{display:flex;align-items:center;margin-bottom:%?12?%}.selector-modal-overlay .selector-modal-content .department-info-section .info-item[data-v-269e1299]:last-child{margin-bottom:0}.selector-modal-overlay .selector-modal-content .department-info-section .info-item .info-text[data-v-269e1299]{font-size:%?24?%;color:#6b7280;margin-left:%?8?%}.selector-modal-overlay .selector-modal-content .add-department-section[data-v-269e1299]{padding:%?24?% %?48?%;border-bottom:1px solid #f0f0f0;background:#f8faff}.selector-modal-overlay .selector-modal-content .add-department-section .add-department-input[data-v-269e1299]{margin-bottom:%?20?%}.selector-modal-overlay .selector-modal-content .add-department-section .add-department-btn[data-v-269e1299]{width:100%;height:%?80?%;background:#3a86ff;color:#fff;border-radius:%?12?%;border:none;font-size:%?28?%;font-weight:500;display:flex;align-items:center;justify-content:center;gap:%?8?%}.selector-modal-overlay .selector-modal-content .add-department-section .add-department-btn[data-v-269e1299]:disabled{background:#d1d5db;color:#9ca3af}.selector-modal-overlay .selector-modal-content .add-department-section .add-department-btn[data-v-269e1299]:active{background:#2563eb}.selector-modal-overlay .selector-modal-content .selector-list-container[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container[data-v-269e1299]{padding:%?24?% 0}.selector-modal-overlay .selector-modal-content .selector-list-container .empty-department-hint[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .empty-department-hint[data-v-269e1299]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?80?% %?40?%}.selector-modal-overlay .selector-modal-content .selector-list-container .empty-department-hint .empty-text[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .empty-department-hint .empty-text[data-v-269e1299]{font-size:%?28?%;color:#8a94a6;margin-top:%?16?%}.selector-modal-overlay .selector-modal-content .selector-list-container .empty-department-hint .empty-desc[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .empty-department-hint .empty-desc[data-v-269e1299]{font-size:%?24?%;color:#c4c4c4;margin-top:%?8?%}.selector-modal-overlay .selector-modal-content .selector-list-container .selector-item[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .selector-item[data-v-269e1299]{display:flex;align-items:center;padding:%?24?% %?48?%;border-bottom:1px solid #f8f9fa;transition:all .2s ease}.selector-modal-overlay .selector-modal-content .selector-list-container .selector-item[data-v-269e1299]:active,\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .selector-item[data-v-269e1299]:active{background:rgba(58,134,255,.05)}.selector-modal-overlay .selector-modal-content .selector-list-container .selector-item.selected[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .selector-item.selected[data-v-269e1299]{background:rgba(58,134,255,.08);border-left:%?4?% solid #3a86ff}.selector-modal-overlay .selector-modal-content .selector-list-container .selector-item .selector-item-left[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .selector-item .selector-item-left[data-v-269e1299]{width:%?48?%;display:flex;align-items:center;justify-content:center}.selector-modal-overlay .selector-modal-content .selector-list-container .selector-item .selector-item-content[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .selector-item .selector-item-content[data-v-269e1299]{flex:1;margin-left:%?16?%;display:flex;align-items:center;justify-content:space-between}.selector-modal-overlay .selector-modal-content .selector-list-container .selector-item .selector-item-content .selector-name[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .selector-item .selector-item-content .selector-name[data-v-269e1299]{font-size:%?28?%;font-weight:500;color:#1a1a1a}.selector-modal-overlay .selector-modal-content .selector-list-container .selector-item .selector-item-content .color-indicator[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .selector-item .selector-item-content .color-indicator[data-v-269e1299]{width:%?24?%;height:%?24?%;border-radius:50%;border:%?2?% solid #fff;box-shadow:0 0 0 %?1?% rgba(0,0,0,.1)}.selector-modal-overlay .selector-modal-content .selector-list-container .selector-item .selected-icon[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .selector-item .selected-icon[data-v-269e1299]{display:flex;align-items:center;justify-content:center;width:%?32?%;height:%?32?%;border-radius:50%;background:rgba(58,134,255,.1)}.selector-modal-overlay .selector-modal-content .selector-list-container .selector-item .selector-item-right .delete-department-btn[data-v-269e1299],\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .selector-item .selector-item-right .delete-department-btn[data-v-269e1299]{width:%?48?%;height:%?48?%;border-radius:50%;background:rgba(239,68,68,.1);display:flex;align-items:center;justify-content:center;margin-left:%?16?%}.selector-modal-overlay .selector-modal-content .selector-list-container .selector-item .selector-item-right .delete-department-btn[data-v-269e1299]:active,\n.selector-modal-overlay .selector-modal-content .honor-type-list-container .selector-item .selector-item-right .delete-department-btn[data-v-269e1299]:active{background:rgba(239,68,68,.2)}.selector-modal-overlay .selector-modal-content .selector-list-container[data-v-269e1299]{height:%?550?%}.selector-modal-overlay .selector-modal-content .honor-type-list-container[data-v-269e1299]{height:%?650?%}.batch-modal-overlay[data-v-269e1299]{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.5);z-index:999;display:flex;align-items:flex-end}.batch-modal-overlay .batch-modal-content[data-v-269e1299]{width:100%;max-height:80vh;background:#fff;border-radius:%?32?% %?32?% 0 0;display:flex;flex-direction:column}.batch-modal-overlay .batch-modal-content .batch-modal-header[data-v-269e1299]{display:flex;align-items:center;justify-content:space-between;padding:%?40?% %?48?% %?32?%;border-bottom:1px solid #f0f0f0}.batch-modal-overlay .batch-modal-content .batch-modal-header .batch-modal-title[data-v-269e1299]{font-size:%?32?%;font-weight:600;color:#1a1a1a}.batch-modal-overlay .batch-modal-content .batch-modal-header .batch-modal-close[data-v-269e1299]{width:%?60?%;height:%?60?%;border-radius:50%;background:#f8f9fa;display:flex;align-items:center;justify-content:center}.batch-modal-overlay .batch-modal-content .batch-modal-header .batch-modal-close[data-v-269e1299]:active{background:#e9ecef}.batch-modal-overlay .batch-modal-content .batch-search-section[data-v-269e1299]{padding:%?32?% %?48?%;border-bottom:1px solid #f0f0f0}.batch-modal-overlay .batch-modal-content .batch-list-container[data-v-269e1299]{height:%?600?%;padding:%?24?% 0}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group[data-v-269e1299]{margin-bottom:%?40?%}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-group-header[data-v-269e1299]{display:flex;align-items:center;padding:%?16?% %?48?%;margin-bottom:%?16?%}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-group-header .batch-group-title[data-v-269e1299]{font-size:%?26?%;font-weight:600;color:#1a1a1a;margin-left:%?12?%}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-group-header .batch-count[data-v-269e1299]{font-size:%?22?%;color:#8a94a6;margin-left:auto}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item[data-v-269e1299]{display:flex;align-items:center;justify-content:space-between;padding:%?24?% %?48?%;border-bottom:1px solid #f8f9fa;transition:all .2s ease}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item[data-v-269e1299]:active{background:rgba(58,134,255,.05)}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item.selected[data-v-269e1299]{background:rgba(58,134,255,.08);border-left:%?4?% solid #3a86ff}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item .batch-item-left[data-v-269e1299]{width:%?48?%;display:flex;align-items:center;justify-content:center}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item .batch-item-content[data-v-269e1299]{flex:1;margin-left:%?16?%}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item .batch-item-content .batch-name[data-v-269e1299]{display:block;font-size:%?28?%;font-weight:500;color:#1a1a1a;line-height:1.2}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item .batch-item-content .batch-date[data-v-269e1299]{display:block;font-size:%?24?%;color:#8a94a6;margin-top:%?8?%}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item .batch-type-tag[data-v-269e1299]{padding:%?8?% %?16?%;border-radius:%?12?%;font-size:%?20?%;font-weight:500}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item .batch-type-tag.type-weekly[data-v-269e1299]{background:rgba(58,134,255,.1);color:#3a86ff}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item .batch-type-tag.type-monthly[data-v-269e1299]{background:rgba(16,185,129,.1);color:#10b981}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item .batch-type-tag.type-quarterly[data-v-269e1299]{background:rgba(139,92,246,.1);color:#8b5cf6}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item .batch-type-tag.type-yearly[data-v-269e1299]{background:rgba(245,158,11,.1);color:#f59e0b}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item .batch-type-tag.type-special[data-v-269e1299]{background:rgba(239,68,68,.1);color:#ef4444}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .batch-item .selected-icon[data-v-269e1299]{display:flex;align-items:center;justify-content:center;width:%?32?%;height:%?32?%;border-radius:50%;background:rgba(58,134,255,.1)}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .show-more-btn[data-v-269e1299]{display:flex;align-items:center;justify-content:center;padding:%?24?% %?48?%;color:#3a86ff;transition:all .2s ease}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .show-more-btn[data-v-269e1299]:active{background:rgba(58,134,255,.05)}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-group .show-more-btn .show-more-text[data-v-269e1299]{font-size:%?26?%;margin-right:%?8?%}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-empty-state[data-v-269e1299]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?80?% %?48?%}.batch-modal-overlay .batch-modal-content .batch-list-container .batch-empty-state .empty-text[data-v-269e1299]{font-size:%?26?%;color:#8a94a6;margin-top:%?24?%}.batch-modal-overlay .batch-modal-content .batch-modal-footer[data-v-269e1299]{display:flex;gap:%?24?%;padding:%?32?% %?48?%;padding-bottom:calc(%?32?% + env(safe-area-inset-bottom));border-top:1px solid #f0f0f0}.batch-modal-overlay .batch-modal-content .batch-modal-footer .batch-footer-btn[data-v-269e1299]{flex:1;height:%?80?%;border-radius:%?16?%;font-size:%?28?%;font-weight:500;border:none;display:flex;align-items:center;justify-content:center;gap:%?12?%;transition:all .2s ease}.batch-modal-overlay .batch-modal-content .batch-modal-footer .batch-footer-btn.secondary[data-v-269e1299]{background:rgba(58,134,255,.1);color:#3a86ff}.batch-modal-overlay .batch-modal-content .batch-modal-footer .batch-footer-btn.secondary[data-v-269e1299]:active{background:rgba(58,134,255,.2);-webkit-transform:scale(.98);transform:scale(.98)}.batch-modal-overlay .batch-modal-content .batch-modal-footer .batch-footer-btn.primary[data-v-269e1299]{background:linear-gradient(135deg,#3a86ff,#2563eb);color:#fff}.batch-modal-overlay .batch-modal-content .batch-modal-footer .batch-footer-btn.primary[data-v-269e1299]:active{background:linear-gradient(135deg,#2563eb,#1d4ed8);-webkit-transform:scale(.98);transform:scale(.98)}@-webkit-keyframes spin-data-v-269e1299{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes spin-data-v-269e1299{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=e},b050:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"p-empty-state",props:{icon:{type:String,default:""},text:{type:String,default:"暂无数据"},type:{type:String,default:"default"},size:{type:String,default:"medium"},textColor:{type:String,default:"#999"},containerStyle:{type:Object,default:function(){return{}}},showAction:{type:Boolean,default:!1},actionText:{type:String,default:"点击操作"}},computed:{defaultIcon:function(){var t={default:"/static/empty/empty.png",task:"/static/empty/empty_task.png",record:"/static/empty/empty_record.png",search:"/static/empty/empty-search.png",data:"/static/empty/empty_data.png",todo:"/static/empty/empty_todo.png"};return t[this.type]||t.default},iconStyle:function(){var t={small:"80rpx",medium:"120rpx",large:"180rpx"},e=t[this.size]||t.medium;return{width:e,height:e}}}};e.default=n},b6c2:function(t,e,a){"use strict";a.r(e);var n=a("b050"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},b72a:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.p-empty-state[data-v-bf86960a]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?40?%;box-sizing:border-box}.p-empty-state__icon[data-v-bf86960a]{width:%?120?%;height:%?120?%;margin-bottom:%?20?%}.p-empty-state__text[data-v-bf86960a]{font-size:%?28?%;color:#999;text-align:center;margin-bottom:%?20?%}.p-empty-state__action[data-v-bf86960a]{margin-top:%?20?%;background-color:#1677ff;color:#fff;font-size:%?28?%;border-radius:%?40?%;padding:%?10?% %?30?%;border:none}.p-empty-state__action[data-v-bf86960a]:active{opacity:.8}',""]),t.exports=e},baea:function(t,e,a){"use strict";a.r(e);var n=a("35d0"),r=a("854b");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("e5c4");var i=a("828b"),s=Object(i["a"])(r["default"],n["b"],n["c"],!1,null,"1c086441",null,!1,n["a"],void 0);e["default"]=s.exports},c01d:function(t,e,a){"use strict";(function(t){a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("39d8")),o=n(a("2634")),i=n(a("2fdc"));a("64aa"),a("c223"),a("9db6"),a("7a76"),a("c9b5"),a("4626"),a("5ac7"),a("fd3c"),a("e838"),a("795c"),a("f7a5"),a("bf0f"),a("ab80"),a("aa9c"),a("dd2b");var s,c=n(a("4ea0")),l=n(a("9b76")),u={maxSize:10485760,timeout:3e4,validTypes:["jpg","jpeg","png","webp","JPG","JPEG","PNG","WEBP"],compressQuality:.8},d={name:"AvatarPicker",components:{PEmptyState:l.default},props:{value:{type:String,default:""},label:{type:String,default:"头像"},showActions:{type:Boolean,default:!0},autoUpload:{type:Boolean,default:!0},customPath:{type:String,default:""},sizeLimit:{type:Number,default:u.maxSize},timeout:{type:Number,default:u.timeout}},data:function(){return{uploading:!1,uploadProgress:0,uploadError:!1,avatarUrl:"",uploadTimer:null,currentFile:null,uploadRes:null,historyAvatars:[],batchList:[],recentUserName:"",defaultAvatar:"/static/user/default-avatar.png"}},computed:{displayAvatar:function(){return this.avatarUrl||this.defaultAvatar}},watch:{value:{immediate:!0,handler:function(t){this.avatarUrl=t}}},created:function(){this.loadHistoryAvatars()},methods:(s={loadHistoryAvatars:function(){var e=this;return(0,i.default)((0,o.default)().mark((function a(){var n,r;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.callFunction({name:"history-avatar-get",data:{}});case 3:n=a.sent,r=n.result,0===r.code?e.historyAvatars=r.data:console.error("获取历史头像失败:",r.message),a.next=11;break;case 8:a.prev=8,a.t0=a["catch"](0),console.error("获取历史头像失败:",a.t0);case 11:case"end":return a.stop()}}),a,null,[[0,8]])})))()},onUploadSuccess:function(t){var e=this;return(0,i.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.loadHistoryAvatars();case 2:case"end":return t.stop()}}),t)})))()},handleError:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"upload",a={upload:"上传失败",compress:"压缩失败",select:"选择失败",size:"文件过大",format:"格式不支持"};this.uploadError="upload"===e,uni.showToast({title:t.message||a[e],icon:"none"}),this.$emit("error",{type:e,error:t})},handleSelectImage:function(){var t=this;return(0,i.default)((0,o.default)().mark((function e(){var a,n,r,i,s;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.uploading){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,n={count:1,sizeType:["compressed"],sourceType:["album"]},e.next=6,uni.chooseImage(n);case 6:if(r=e.sent,null===(a=r.tempFilePaths)||void 0===a||!a[0]){e.next=17;break}if(i=r.tempFiles[0],s=r.tempFilePaths[0],!(i.size>t.sizeLimit)){e.next=13;break}return t.handleError({message:"文件大小".concat(t.formatFileSize(i.size),"超过限制").concat(t.formatFileSize(t.sizeLimit))},"size"),e.abrupt("return");case 13:if(t.currentFile={path:s,size:i.size},!t.autoUpload){e.next=17;break}return e.next=17,t.uploadAvatar();case 17:e.next=23;break;case 19:e.prev=19,e.t0=e["catch"](2),console.error("选择图片失败:",e.t0),t.handleError(e.t0,"select");case 23:case"end":return e.stop()}}),e,null,[[2,19]])})))()},retryUpload:function(){var t=this;return(0,i.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.uploadError=!1,e.next=3,t.uploadAvatar();case 3:case"end":return e.stop()}}),e)})))()},previewAvatar:function(){this.avatarUrl&&uni.previewImage({urls:[this.avatarUrl]})},confirmDelete:function(){this.$refs.deleteConfirm.open()},handleDelete:function(){var e=this;return(0,i.default)((0,o.default)().mark((function a(){var n,r,i;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,!e.avatarUrl){a.next=17;break}return n=e.avatarUrl,n.startsWith("http")&&e.uploadRes&&e.uploadRes.cloudPath&&(n=e.uploadRes.cloudPath),a.prev=4,a.next=7,t.callFunction({name:"delete-file",data:{fileList:[n]}});case 7:if(r=a.sent,i=r.result,0===i.code){a.next=11;break}throw new Error(i.message||"云端文件删除失败");case 11:a.next=17;break;case 13:throw a.prev=13,a.t0=a["catch"](4),console.error("云端文件删除失败:",a.t0),new Error(a.t0.message||"云端文件删除失败");case 17:e.avatarUrl="",e.currentFile=null,e.uploadRes=null,e.$emit("input",""),e.$emit("delete"),e.$refs.deleteConfirm.close(),uni.showToast({title:"删除成功",icon:"success"}),a.next=31;break;case 26:a.prev=26,a.t1=a["catch"](0),console.error("删除失败:",a.t1),e.handleError(a.t1,"delete"),e.$refs.deleteConfirm.close();case 31:case"end":return a.stop()}}),a,null,[[0,26],[4,13]])})))()},validateImageFormat:function(t){var e;if(!t)return!1;var a=null===(e=t.split(".").pop())||void 0===e?void 0:e.toLowerCase();return!!a&&u.validTypes.map((function(t){return t.toLowerCase()})).includes(a)},formatFileSize:function(t){if(0===t)return"0 Bytes";var e=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,e)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][e]},formatDate:function(t){var e=new Date(t),a=e.getFullYear(),n=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0"),o=String(e.getHours()).padStart(2,"0"),i=String(e.getMinutes()).padStart(2,"0");return"".concat(a,"-").concat(n,"-").concat(r," ").concat(o,":").concat(i)},openHistorySelect:function(){this.$refs.historyPopup.open()},closeHistorySelect:function(){this.$refs.historyPopup.close()},selectHistoryAvatar:function(t){this.avatarUrl=t.url,this.$emit("input",t.url),this.$emit("change",{url:t.url,cloudPath:t.fileID}),this.closeHistorySelect(),uni.showToast({title:"已选择头像",icon:"success"})},uploadAvatar:function(){var e=this;return(0,i.default)((0,o.default)().mark((function a(){var n,r,i,s,c;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.currentFile){a.next=2;break}return a.abrupt("return");case 2:return a.prev=2,e.uploading=!0,e.uploadError=!1,a.next=7,t.uploadFile({filePath:e.currentFile.path,cloudPath:"avatars/".concat(Date.now(),"-").concat(Math.random().toString(36).slice(2),".").concat(e.currentFile.path.split(".").pop())});case 7:if(r=a.sent,r.fileID){a.next=10;break}throw new Error("上传失败");case 10:return a.next=12,t.getTempFileURL({fileList:[r.fileID]});case 12:if(i=a.sent,s=i.fileList,null!==s&&void 0!==s&&null!==(n=s[0])&&void 0!==n&&n.tempFileURL){a.next=16;break}throw new Error("获取临时链接失败");case 16:return c=s[0].tempFileURL,a.next=19,e.saveHistoryAvatar(c,r.fileID);case 19:e.avatarUrl=c,e.$emit("input",c),e.$emit("change",c),uni.showToast({title:"上传成功",icon:"success"}),a.next=30;break;case 25:a.prev=25,a.t0=a["catch"](2),console.error("上传头像失败:",a.t0),e.uploadError=!0,uni.showToast({title:a.t0.message||"上传失败",icon:"none"});case 30:return a.prev=30,e.uploading=!1,e.currentFile=null,a.finish(30);case 34:case"end":return a.stop()}}),a,null,[[2,25,30,34]])})))()},openBatchUpload:function(){this.batchList=[{userName:"",url:"",saving:!1}],this.$refs.batchUploadPopup.open()},closeBatchUpload:function(){this.$refs.batchUploadPopup.close()},addBatchItem:function(){this.batchList.push({userName:"",url:"",saving:!1})},removeBatchItem:function(t){this.batchList.splice(t,1),0===this.batchList.length&&this.addBatchItem()},selectImage:function(t){var e=this;return(0,i.default)((0,o.default)().mark((function a(){var n,r,i;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album"]});case 3:r=a.sent,null!==(n=r.tempFilePaths)&&void 0!==n&&n[0]&&(i=e.batchList[t],i.url=r.tempFilePaths[0],i.file=r.tempFiles[0]),a.next=10;break;case 7:a.prev=7,a.t0=a["catch"](0),console.error("选择图片失败:",a.t0);case 10:case"end":return a.stop()}}),a,null,[[0,7]])})))()},saveAvatar:function(e){var a=this;return(0,i.default)((0,o.default)().mark((function n(){var r,i,s,l;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(r=a.batchList[e],r.userName){n.next=4;break}return uni.showToast({title:"请输入姓名",icon:"none",duration:2e3}),n.abrupt("return");case 4:if(r.url){n.next=7;break}return uni.showToast({title:"请选择头像",icon:"none",duration:2e3}),n.abrupt("return");case 7:if(!r.saving){n.next=9;break}return n.abrupt("return");case 9:return r.saving=!0,n.prev=10,n.next=13,c.default.uploadAvatar(r.url);case 13:return i=n.sent,n.next=16,t.callFunction({name:"history-avatar-save",data:{url:i.cloudPath,userName:r.userName,createTime:Date.now()}});case 16:if(s=n.sent,l=s.result,0!==l.code){n.next=26;break}return uni.showToast({title:l.message||"保存成功",icon:"success"}),r.userName="",r.url="",n.next=24,a.loadHistoryAvatars();case 24:n.next=27;break;case 26:throw new Error(l.message);case 27:n.next=32;break;case 29:n.prev=29,n.t0=n["catch"](10),uni.showToast({title:n.t0.message||"保存失败",icon:"none"});case 32:return n.prev=32,r.saving=!1,n.finish(32);case 35:case"end":return n.stop()}}),n,null,[[10,29,32,35]])})))()},clearAvatar:function(){this.avatarUrl=this.defaultAvatar,this.$emit("update:modelValue",""),this.$emit("change","")}},(0,r.default)(s,"confirmDelete",(function(){var t=this;return(0,i.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:uni.showModal({title:"删除确认",content:"确定要删除当前头像吗？删除后将恢复为默认头像。",success:function(){var e=(0,i.default)((0,o.default)().mark((function e(a){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!a.confirm){e.next=3;break}return e.next=3,t.deleteAvatar();case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})))()})),(0,r.default)(s,"deleteAvatar",(function(){var e=this;return(0,i.default)((0,o.default)().mark((function a(){var n,r,i,s;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,uni.showLoading({title:"删除中...",mask:!0}),n=t.database(),r=n.collection("history-avatar"),a.next=6,r.where({url:e.avatarUrl}).get();case 6:if(i=a.sent,!(i.data&&i.data.length>0)){a.next=14;break}if(s=i.data[0],!s.fileID){a.next=12;break}return a.next=12,t.deleteFile({fileList:[s.fileID]});case 12:return a.next=14,r.doc(s._id).remove();case 14:return e.avatarUrl=e.value||"/static/user/default-avatar.png",e.$emit("input",""),a.next=18,e.loadHistoryAvatars();case 18:uni.showToast({title:"删除成功",icon:"success"}),a.next=25;break;case 21:a.prev=21,a.t0=a["catch"](0),console.error("删除头像失败:",a.t0),uni.showToast({title:"删除失败",icon:"error"});case 25:return a.prev=25,uni.hideLoading(),a.finish(25);case 28:case"end":return a.stop()}}),a,null,[[0,21,25,28]])})))()})),(0,r.default)(s,"confirmDeleteHistory",(function(t){var e=this;uni.showModal({title:"删除确认",content:"确定要删除这个头像吗？此操作不可恢复。",success:function(){var a=(0,i.default)((0,o.default)().mark((function a(n){return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!n.confirm){a.next=3;break}return a.next=3,e.deleteHistoryAvatar(t);case 3:case"end":return a.stop()}}),a)})));return function(t){return a.apply(this,arguments)}}()})})),(0,r.default)(s,"deleteHistoryAvatar",(function(e){var a=this;return(0,i.default)((0,o.default)().mark((function n(){var r,i;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e&&e._id){n.next=3;break}return uni.showToast({title:"无效的记录",icon:"none"}),n.abrupt("return");case 3:return n.prev=3,uni.showLoading({title:"删除中...",mask:!0}),n.next=7,t.callFunction({name:"history-avatar-delete",data:{_id:e._id}});case 7:if(r=n.sent,i=r.result,0!==i.code){n.next=16;break}return e.url===a.avatarUrl&&(a.avatarUrl=a.value||"/static/user/default-avatar.png",a.$emit("input","")),n.next=13,a.loadHistoryAvatars();case 13:uni.showToast({title:"删除成功",icon:"success"}),n.next=17;break;case 16:throw new Error(i.msg||"删除失败");case 17:n.next=23;break;case 19:n.prev=19,n.t0=n["catch"](3),console.error("删除历史头像失败:",n.t0),uni.showToast({title:n.t0.message||"删除失败",icon:"none"});case 23:return n.prev=23,uni.hideLoading(),n.finish(23);case 26:case"end":return n.stop()}}),n,null,[[3,19,23,26]])})))()})),(0,r.default)(s,"saveHistoryAvatar",(function(e,a){var n=this;return(0,i.default)((0,o.default)().mark((function r(){var i,s;return(0,o.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,t.callFunction({name:"history-avatar-save",data:{url:e,fileID:a,userName:n.userName||(uni.getStorageSync("uni-id-pages-userInfo")||{}).username||"未命名"}});case 3:if(i=r.sent,s=i.result,0!==s.code){r.next=10;break}return r.next=8,n.loadHistoryAvatars();case 8:r.next=11;break;case 10:uni.showToast({title:s.msg,icon:"none"});case 11:r.next=17;break;case 13:r.prev=13,r.t0=r["catch"](0),console.error("保存历史头像失败:",r.t0),uni.showToast({title:"保存历史头像失败",icon:"none"});case 17:case"end":return r.stop()}}),r,null,[[0,13]])})))()})),s)};e.default=d}).call(this,a("861b")["uniCloud"])},cad1:function(t,e,a){"use strict";a.r(e);var n=a("1732"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},d52d:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.uni-popup-dialog[data-v-b2d7af54]{width:300px;border-radius:11px;background-color:#fff}.uni-dialog-title[data-v-b2d7af54]{display:flex;flex-direction:row;justify-content:center;padding-top:25px}.uni-dialog-title-text[data-v-b2d7af54]{font-size:16px;font-weight:500}.uni-dialog-content[data-v-b2d7af54]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:20px}.uni-dialog-content-text[data-v-b2d7af54]{font-size:14px;color:#6c6c6c}.uni-dialog-button-group[data-v-b2d7af54]{display:flex;flex-direction:row;border-top-color:#f5f5f5;border-top-style:solid;border-top-width:1px}.uni-dialog-button[data-v-b2d7af54]{display:flex;flex:1;flex-direction:row;justify-content:center;align-items:center;height:45px}.uni-border-left[data-v-b2d7af54]{border-left-color:#f0f0f0;border-left-style:solid;border-left-width:1px}.uni-dialog-button-text[data-v-b2d7af54]{font-size:16px;color:#333}.uni-button-color[data-v-b2d7af54]{color:#007aff}.uni-dialog-input[data-v-b2d7af54]{flex:1;font-size:14px;border:1px #eee solid;height:40px;padding:0 10px;border-radius:5px;color:#555}.uni-popup__success[data-v-b2d7af54]{color:#4cd964}.uni-popup__warn[data-v-b2d7af54]{color:#f0ad4e}.uni-popup__error[data-v-b2d7af54]{color:#dd524d}.uni-popup__info[data-v-b2d7af54]{color:#909399}',""]),t.exports=e},db04:function(t,e,a){"use strict";var n=a("bb80"),r=a("c435"),o=a("9e70"),i=a("f298"),s=a("862c"),c=n(i),l=n("".slice),u=Math.ceil,d=function(t){return function(e,a,n){var i,d,p=o(s(e)),f=r(a),h=p.length,v=void 0===n?" ":o(n);return f<=h||""===v?p:(i=f-h,d=c(v,u(i/v.length)),d.length>i&&(d=l(d,0,i)),t?p+d:d+p)}};t.exports={start:d(!1),end:d(!0)}},db73:function(t,e,a){"use strict";a.r(e);var n=a("3935"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},e5c4:function(t,e,a){"use strict";var n=a("eb0c"),r=a.n(n);r.a},eb0c:function(t,e,a){var n=a("2c11");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=a("967d").default;r("00d967da",n,!0,{sourceMap:!1,shadowMode:!1})},f298:function(t,e,a){"use strict";var n=a("497b"),r=a("9e70"),o=a("862c"),i=RangeError;t.exports=function(t){var e=r(o(this)),a="",s=n(t);if(s<0||s===1/0)throw new i("Wrong number of repetitions");for(;s>0;(s>>>=1)&&(e+=e))1&s&&(a+=e);return a}}}]);
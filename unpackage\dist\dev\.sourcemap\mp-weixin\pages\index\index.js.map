{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/index/index.vue?e098", "webpack:///D:/Xwzc/pages/index/index.vue?60fa", "webpack:///D:/Xwzc/pages/index/index.vue?0307", "webpack:///D:/Xwzc/pages/index/index.vue?dbae", "uni-app:///pages/index/index.vue", "webpack:///D:/Xwzc/pages/index/index.vue?2965", "webpack:///D:/Xwzc/pages/index/index.vue?d63c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "name", "project", "description", "images", "uploadedFileIDs", "projectOptions", "onLoad", "onShow", "methods", "getUserInfo", "bindProjectChange", "chooseImage", "uni", "count", "sizeType", "sourceType", "success", "url", "progress", "fileID", "res", "path", "newImages", "uploadImage", "simulatedProgress", "clearInterval", "filePath", "cloudPath", "cloudPathAsRealPath", "onUploadProgress", "uploadTask", "title", "icon", "duration", "previewImages", "urls", "current", "deleteImage", "mask", "uniCloud", "fileList", "submitForm", "allUploaded", "uploadedImages", "action", "app", "id", "timestamp"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2CnnB;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IAEAC;MACA;QACA;QACA;QACA;UACA;UACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;UACA;YACA;cACAC;cACAC;cACAC;cACAnB,iDACAoB,0BACAC;YACA;UACA;UAEA;UAEAC;YACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MAEA;MACA;QACA;UACAC;QACA;UACAA;QACA;UACAA;QACA;QAEA;UACAA;UACAC;QACA;QAEA;MACA;MAGA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MAGA;QACAC;QACAC;QACAC;QACAC;UACA;YACA;YACA;cACAJ;cACA;YACA;UACA;QACA;MACA;MAEAK;QACAL;QACA;QACA;QACA;QAEAb;UACAmB;UACAC;UACAC;QACA;MACA;QACAR;QACA;QACAb;UACAmB;UACAC;QACA;MACA;IACA;IACAE;MACA;QAAA;MAAA;MACAtB;QACAuB;QACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;MAEA;QACAzB;UACAmB;UACAO;QACA;QAEAC;UACAvC;UACAF;YACA0C;UACA;QACA;UACA;UACA;YACA;UACA;UACA;UAEA5B;YACAmB;YACAC;UACA;QACA;UACA;UACApB;YACAmB;YACAC;UACA;QACA;UACApB;QACA;MACA;QACA;QACAA;UACAmB;UACAC;QACA;MACA;IACA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA7B;kBACAmB;kBACAC;gBACA;gBAAA;cAAA;gBAIApB;kBACAmB;gBACA;gBAAA;gBAGAW;kBAAA;gBAAA;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGAC;kBAAA;gBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAJ;kBACAvC;kBACAF;oBACA8C;kBAAA,GACA;oBACAzC;kBAAA;gBAEA;cAAA;gBAPAiB;gBASAR;gBAAA,MAEAQ;kBAAA;kBAAA;gBAAA;gBACAR;kBACAmB;kBACAC;gBACA;;gBAEA;gBACAa;gBACA;kBACAA;gBACA;;gBAEA;gBACAjC;kBACAkC;kBACAC;gBACA;;gBAEA;gBACA;kBACA/C;kBACAC;kBACAC;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAU;gBACAA;kBACAmB;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/SA;AAAA;AAAA;AAAA;AAAi3B,CAAgB,k3BAAG,EAAC,C;;;;;;;;;;;ACAr4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.images.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"content\">\n\t\t<view class=\"form-container\">\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">姓名：</text>\n\t\t\t\t<input class=\"input\" v-model=\"formData.name\" placeholder=\"请输入您的姓名\" />\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">找茬项目：</text>\n\t\t\t\t<picker class=\"picker\" mode=\"selector\" :range=\"projectOptions\" @change=\"bindProjectChange\">\n\t\t\t\t\t<view class=\"picker-text\">{{formData.project || '请选择找茬项目'}}</view>\n\t\t\t\t</picker>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">问题描述：</text>\n\t\t\t\t<textarea class=\"textarea\" v-model=\"formData.description\" placeholder=\"请描述您发现的问题\" />\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">上传图片（最多5张）：</text>\n\t\t\t\t<view class=\"image-uploader\">\n\t\t\t\t\t<view class=\"image-preview\" v-for=\"(image, index) in images\" :key=\"index\">\n\t\t\t\t\t\t<image class=\"preview-image\" :src=\"image.url || image\" mode=\"aspectFill\" @click=\"previewImages(index)\"></image>\n\t\t\t\t\t\t<view class=\"delete-btn\" @click=\"deleteImage(index)\">×</view>\n\t\t\t\t\t\t<!-- 上传进度显示 -->\n\t\t\t\t\t\t<view class=\"upload-progress\" v-if=\"image.progress !== undefined && image.progress < 100\">\n\t\t\t\t\t\t\t<view class=\"progress-text\">{{image.progress}}%</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"upload-btn\" @click=\"chooseImage\" v-if=\"images.length < 5\">\n\t\t\t\t\t\t<text class=\"plus-icon\">+</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<button class=\"submit-btn\" @click=\"submitForm\">提交反馈</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tformData: {\n\t\t\t\t\tname: '',\n\t\t\t\t\tproject: '',\n\t\t\t\t\tdescription: ''\n\t\t\t\t},\n\t\t\t\timages: [],\n\t\t\t\tuploadedFileIDs: [], // 存储已上传文件的fileID\n\t\t\t\tprojectOptions: ['安全找茬', '设备找茬', '其他找茬']\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t// 页面加载时获取用户信息\n\t\t\tthis.getUserInfo();\n\t\t},\n\t\tonShow() {\n\t\t\t// 页面每次显示时也获取用户信息，确保自动填充\n\t\t\tthis.getUserInfo();\n\t\t},\n\t\tmethods: {\n\t\t\t\n\t\t\tgetUserInfo() {\n\t\t\t\ttry {\n\t\t\t\t\tconst userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};\n\t\t\t\t\tconst token = uni.getStorageSync('uni_id_token') || '';\n\t\t\t\t\tif (!token) {\n\t\t\t\t\t\tthis.formData.name = '';\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tconst name = userInfo.nickname || userInfo.username || '';\n\t\t\t\t\tif (name && !name.startsWith('匿名') && userInfo.username !== 'admin') {\n\t\t\t\t\t\tthis.formData.name = name;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.formData.name = '';\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tthis.formData.name = '';\n\t\t\t\t}\n\t\t\t},\n\t\t\tbindProjectChange(e) {\n\t\t\t\tthis.formData.project = this.projectOptions[e.detail.value]\n\t\t\t},\n\t\t\tchooseImage() {\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 5 - this.images.length,\n\t\t\t\t\tsizeType: ['compressed'],\n\t\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconst newImages = res.tempFilePaths.map((path, idx) => {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\turl: path,\n\t\t\t\t\t\t\t\tprogress: 0,\n\t\t\t\t\t\t\t\tfileID: '',\n\t\t\t\t\t\t\t\tname: res.tempFiles && res.tempFiles[idx].name \n\t\t\t\t\t\t\t\t\t? res.tempFiles[idx].name \n\t\t\t\t\t\t\t\t\t: path.substring(path.lastIndexOf('/') + 1)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.images = [...this.images, ...newImages];\n\t\t\t\t\t\t\n\t\t\t\t\t\tnewImages.forEach((image, index) => {\n\t\t\t\t\t\t\tconst totalIndex = this.images.length - newImages.length + index;\n\t\t\t\t\t\t\tthis.uploadImage(image.url, totalIndex);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tuploadImage(filePath, index) {\n\t\t\t\tthis.$set(this.images[index], 'progress', 0);\n\t\t\t\t\n\t\t\t\tlet simulatedProgress = 0;\n\t\t\t\tconst progressInterval = setInterval(() => {\n\t\t\t\t\tif (simulatedProgress < 40) {\n\t\t\t\t\t\tsimulatedProgress += Math.random() * 10;\n\t\t\t\t\t} else if (simulatedProgress < 70) {\n\t\t\t\t\t\tsimulatedProgress += Math.random() * 5;\n\t\t\t\t\t} else if (simulatedProgress < 95) {\n\t\t\t\t\t\tsimulatedProgress += Math.random() * 1.5;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (simulatedProgress > 95) {\n\t\t\t\t\t\tsimulatedProgress = 95;\n\t\t\t\t\t\tclearInterval(progressInterval);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.$set(this.images[index], 'progress', Math.floor(simulatedProgress));\n\t\t\t\t}, 300);\n\t\t\t\t\n\t\t\t\t\n\t\t\t\tconst imageObj = this.images[index];\n\t\t\t\tconst fileName = imageObj.name || filePath.substring(filePath.lastIndexOf('/') + 1);\n\t\t\t\tconst fileExt = fileName.includes('.') ? fileName.substring(fileName.lastIndexOf('.')) : '.jpg';\n\t\t\t\tconst safeFileName = fileName.replace(/\\.[^/.]+$/, \"\").replace(/[^a-zA-Z0-9]/g, '_');\n\t\t\t\tconst uniqueFileName = `${Date.now()}_${safeFileName}${fileExt}`;\n\t\t\t\t\n\t\t\t\tconst now = new Date();\n\t\t\t\tconst year = now.getFullYear();\n\t\t\t\tconst month = String(now.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(now.getDate()).padStart(2, '0');\n\t\t\t\tconst dateFolder = `${year}${month}${day}`;\n\t\t\t\t\n\t\t\t\t\n\t\t\t\tconst uploadTask = uniCloud.uploadFile({\n\t\t\t\t\tfilePath: filePath,\n\t\t\t\t\tcloudPath: `feedback/${dateFolder}/${uniqueFileName}`,\n\t\t\t\t\tcloudPathAsRealPath: true,\n\t\t\t\t\tonUploadProgress: (progressEvent) => {\n\t\t\t\t\t\tif (progressEvent.totalBytesSent && progressEvent.totalBytesExpectedToSend) {\n\t\t\t\t\t\t\tconst realProgress = Math.round(progressEvent.totalBytesSent / progressEvent.totalBytesExpectedToSend * 100);\n\t\t\t\t\t\t\tif (realProgress > 95) {\n\t\t\t\t\t\t\t\tclearInterval(progressInterval);\n\t\t\t\t\t\t\t\tthis.$set(this.images[index], 'progress', realProgress);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tuploadTask.then(res => {\n\t\t\t\t\tclearInterval(progressInterval);\n\t\t\t\t\tthis.$set(this.images[index], 'fileID', res.fileID);\n\t\t\t\t\tthis.$set(this.images[index], 'progress', 100);\n\t\t\t\t\tthis.uploadedFileIDs.push(res.fileID);\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '上传成功',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tclearInterval(progressInterval);\n\t\t\t\t\tthis.$set(this.images[index], 'progress', undefined);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '图片上传失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\tpreviewImages(index) {\n\t\t\t\tconst urls = this.images.map(img => img.url || img);\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: urls,\n\t\t\t\t\tcurrent: index\n\t\t\t\t});\n\t\t\t},\n\t\t\tdeleteImage(index) {\n\t\t\t\tconst image = this.images[index];\n\t\t\t\tconst fileID = image.fileID || image;\n\t\t\t\t\n\t\t\t\tif (fileID && typeof fileID === 'string' && (fileID.startsWith('cloud://') || fileID.startsWith('https://'))) {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '删除中...',\n\t\t\t\t\t\tmask: true\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tuniCloud.callFunction({\n\t\t\t\t\t\tname: 'delete-file',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tfileList: [fileID]\n\t\t\t\t\t\t}\n\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\tconst idIndex = this.uploadedFileIDs.indexOf(fileID);\n\t\t\t\t\t\tif (idIndex !== -1) {\n\t\t\t\t\t\t\tthis.uploadedFileIDs.splice(idIndex, 1);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.images.splice(index, 1);\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\tthis.images.splice(index, 1);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '已从列表移除',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t}).finally(() => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthis.images.splice(index, 1);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '已移除',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync submitForm() {\n\t\t\t\tif (!this.formData.name || !this.formData.project || !this.formData.description) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请填写完整信息',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '提交中...'\n\t\t\t\t})\n\n\t\t\t\ttry {\n\t\t\t\t\tconst allUploaded = this.images.every(img => !img.progress || img.progress === 100);\n\t\t\t\t\tif (!allUploaded) {\n\t\t\t\t\t\tthrow new Error('请等待图片上传完成');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconst uploadedImages = this.images.map(img => img.fileID || img).filter(id => id && typeof id === 'string');\n\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-workflow',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'submit',\n\t\t\t\t\t\t\t...this.formData,\n\t\t\t\t\t\t\timages: uploadedImages\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\n\t\t\t\t\tuni.hideLoading()\n\n\t\t\t\t\tif (res.result.code === 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '提交成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\n\t\t\t\t\t\t// 更新角标\n\t\t\t\t\t\tconst app = getApp();\n\t\t\t\t\t\tif (app && app.todoBadgeManager) {\n\t\t\t\t\t\t\tapp.todoBadgeManager.updateTodoCountImmediately();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 发送全局事件通知列表页刷新数据\n\t\t\t\t\t\tuni.$emit('feedback-submitted', {\n\t\t\t\t\t\t\tid: res.result.data?.id,\n\t\t\t\t\t\t\ttimestamp: Date.now()\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 清空表单\n\t\t\t\t\t\tthis.formData = {\n\t\t\t\t\t\t\tname: '',\n\t\t\t\t\t\t\tproject: '',\n\t\t\t\t\t\t\tdescription: ''\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.images = []\n\t\t\t\t\t\tthis.uploadedFileIDs = []\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(res.result.message || '提交失败')\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: err.message || '提交失败，请重试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.content {\n    \tpadding: 20px;\n\t\tbox-sizing: border-box;\n\t\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: flex-start;\n\t\toverflow: hidden;\n\t\tmin-height: 100vh;\n  \t}\n\t\n  .form-container {\n\t\twidth: 100%;\n\t\tmax-width: 90%;\n\t\tmargin: 0 auto;\n\t\tpadding: 40rpx;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 20rpx;\n\t\tbox-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);\n\t}\n  \n\t.form-item {\n\t\tmargin-bottom: 30px;\n\t\tposition: relative;\n\t}\n\t\n\t.label {\n\t\tdisplay: block;\n\t\tmargin-bottom: 12px;\n\t\tfont-size: 16px;\n\t\tcolor: #2d3748;\n\t\tfont-weight: 600;\n\t\tposition: relative;\n\t\tpadding-left: 12px;\n\t}\n\t\n\t.label::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 4px;\n\t\theight: 16px;\n\t\tbackground: #4299e1;\n\t\tborder-radius: 2px;\n\t}\n\t\n\t.input, .textarea, .picker {\n\t\twidth: 100%;\n\t\tpadding: 14px;\n\t\tborder: 2px solid #e2e8f0;\n\t\tborder-radius: 12px;\n\t\tbox-sizing: border-box;\n\t\tbackground-color: #f8fafc;\n\t\ttransition: all 0.3s ease;\n\t\tfont-size: 15px;\n\t\tcolor: #4a5568;\n\t\tline-height: 1.5;\n\t\theight: auto;\n\t\tmin-height: 48px;\n\t\tvertical-align: middle;\n\t}\n\t\n\t.input:hover, .textarea:hover, .picker:hover {\n\t\tborder-color: #cbd5e0;\n\t\tbackground-color: #f7fafc;\n\t}\n\t\n\t.input:focus, .textarea:focus {\n\t\tborder-color: #4299e1;\n\t\tbox-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);\n\t\tbackground-color: #fff;\n\t\toutline: none;\n\t}\n\t\n\t.textarea {\n\t\theight: 120px;\n\t\tresize: none;\n\t\tline-height: 1.6;\n\t}\n\t\n\t.picker-text {\n\t\tcolor: #4a5568;\n\t\tfont-size: 15px;\n\t}\n\t\n\t.image-uploader {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 20px;\n\t}\n\t\n\t.image-preview {\n\t\tposition: relative;\n\t\twidth: 100px;\n\t\theight: 100px;\n\t\tborder-radius: 12px;\n\t\toverflow: hidden;\n\t}\n\t\n\t.preview-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\t\n\t.delete-btn {\n\t\tposition: absolute;\n\t\tright: 5px;\n\t\ttop: 5px;\n\t\twidth: 20px;\n\t\theight: 20px;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tcolor: #fff;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 16px;\n\t\tz-index: 10;\n\t}\n\t\n\t.upload-btn {\n\t\twidth: 100px;\n\t\theight: 100px;\n\t\tborder: 2px dashed #e2e8f0;\n\t\tborder-radius: 12px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: #f8fafc;\n\t}\n\t\n\t.plus-icon {\n\t\tfont-size: 60px;\n\t\tcolor: #718096;\n\t}\n\t\n\t.upload-text {\n\t\tfont-size: 12px;\n\t\tcolor: #718096;\n\t}\n\t\n\t.upload-progress {\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, 0.6);\n\t\tbackdrop-filter: blur(2px);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.progress-text {\n\t\tcolor: #fff;\n\t\tfont-size: 14px;\n\t\tfont-weight: 600;\n\t\ttext-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n\t}\n\t\n\t.submit-btn {\n\t\tbackground: linear-gradient(135deg, #67B7E6 0%, #4A9FD1 100%);\n\t\tcolor: #fff;\n\t\tfont-size: 17px;\n\t\tpadding: 16px;\n\t\tborder-radius: 12px;\n\t\tmargin-top: 30px;\n\t\twidth: 100%;\n\t\tfont-weight: 600;\n\t\tborder: none;\n\t\tbox-shadow: 0 4px 10px rgba(74, 159, 209, 0.25);\n\t\ttransition: all 0.3s ease;\n\t\tletter-spacing: 0.5px;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\t\n\t.submit-btn::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: -100%;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: linear-gradient(\n\t\t\tto right, \n\t\t\trgba(255, 255, 255, 0) 0%, \n\t\t\trgba(255, 255, 255, 0.1) 20%, \n\t\t\trgba(255, 255, 255, 0.15) 50%, \n\t\t\trgba(255, 255, 255, 0.1) 80%, \n\t\t\trgba(255, 255, 255, 0) 100%\n\t\t);\n\t\ttransform: skewX(-15deg);\n\t\tanimation: shimmer 5s ease-in-out infinite;\n\t}\n\t\n\t@keyframes shimmer {\n\t\t0% { left: -100%; }\n\t\t45% { left: 100%; }\n\t\t100% { left: 100%; }\n\t}\n\t\n\t.submit-btn:hover {\n\t\ttransform: translateY(-2px);\n\t\tbox-shadow: 0 6px 15px rgba(74, 159, 209, 0.35);\n\t\tbackground: linear-gradient(135deg, #78C3EE 0%, #5AAAD9 100%);\n\t}\n\t\n\t.submit-btn:active {\n\t\ttransform: translateY(0);\n\t\tbox-shadow: 0 4px 6px rgba(28, 124, 184, 0.25);\n\t}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571646340\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
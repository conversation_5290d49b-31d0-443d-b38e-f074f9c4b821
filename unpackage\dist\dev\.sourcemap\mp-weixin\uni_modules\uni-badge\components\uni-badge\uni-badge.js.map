{"version": 3, "sources": ["webpack:///D:/Xwzc/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?1da7", "webpack:///D:/Xwzc/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?6620", "webpack:///D:/Xwzc/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?4caf", "webpack:///D:/Xwzc/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?73df", "uni-app:///uni_modules/uni-badge/components/uni-badge/uni-badge.vue", "webpack:///D:/Xwzc/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?a610", "webpack:///D:/Xwzc/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?18f0"], "names": ["name", "emits", "props", "type", "default", "inverted", "isDot", "maxNum", "absolute", "offset", "text", "size", "customStyle", "data", "computed", "width", "classNames", "positionStyle", "h", "w", "rightTop", "right", "top", "rightBottom", "bottom", "leftBottom", "left", "leftTop", "dotStyle", "min<PERSON><PERSON><PERSON>", "height", "padding", "borderRadius", "displayValue", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;ACSvnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvBA,gBAyBA;EACAA;EACAC;EACAC;IACAC;MACAA;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;QACA;MACA;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;QACA;MACA;IACA;EACA;EACAS;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA,IACAX,WAIA,KAJAA;QACAF,OAGA,KAHAA;QACAQ,OAEA,KAFAA;QACAH,WACA,KADAA;MAEA,QACAH,oDACA,sBACA,sBACAG,sCACA;IACA;IACAS;MACA;MACA;QACAC;MACA;QACAC;QACAD;MACA;MACA;MACA;MAEA;QACAE;UACAC;UACAC;QACA;QACAC;UACAF;UACAG;QACA;QACAC;UACAC;UACAF;QACA;QACAG;UACAD;UACAJ;QACA;MACA;MACA;MACA;IACA;IACAM;MACA;MACA;QACAb;QACAc;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA,IACA3B,QAGA,KAHAA;QACAI,OAEA,KAFAA;QACAH,SACA,KADAA;MAEA;IACA;EACA;EACA2B;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC5JA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,onCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-badge/components/uni-badge/uni-badge.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-badge.vue?vue&type=template&id=7c66581c&\"\nvar renderjs\nimport script from \"./uni-badge.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-badge.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-badge.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-badge/components/uni-badge/uni-badge.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-badge.vue?vue&type=template&id=7c66581c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.text\n    ? _vm.__get_style([_vm.positionStyle, _vm.customStyle, _vm.dotStyle])\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-badge.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-badge.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-badge--x\">\r\n\t\t<slot />\r\n\t\t<text v-if=\"text\" :class=\"classNames\" :style=\"[positionStyle, customStyle, dotStyle]\"\r\n\t\t\tclass=\"uni-badge\" @click=\"onClick()\">{{displayValue}}</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * Badge 数字角标\r\n\t * @description 数字角标一般和其它控件（列表、9宫格等）配合使用，用于进行数量提示，默认为实心灰色背景\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=21\r\n\t * @property {String} text 角标内容\r\n\t * @property {String} size = [normal|small] 角标内容\r\n\t * @property {String} type = [info|primary|success|warning|error] 颜色类型\r\n\t * \t@value info 灰色\r\n\t * \t@value primary 蓝色\r\n\t * \t@value success 绿色\r\n\t * \t@value warning 黄色\r\n\t * \t@value error 红色\r\n\t * @property {String} inverted = [true|false] 是否无需背景颜色\r\n\t * @property {Number} maxNum 展示封顶的数字值，超过 99 显示 99+\r\n\t * @property {String} absolute = [rightTop|rightBottom|leftBottom|leftTop] 开启绝对定位, 角标将定位到其包裹的标签的四角上\r\n\t * \t@value rightTop 右上\r\n\t * \t@value rightBottom 右下\r\n\t * \t@value leftTop 左上\r\n\t * \t@value leftBottom 左下\r\n\t * @property {Array[number]} offset\t距定位角中心点的偏移量，只有存在 absolute 属性时有效，例如：[-10, -10] 表示向外偏移 10px，[10, 10] 表示向 absolute 指定的内偏移 10px\r\n\t * @property {String} isDot = [true|false] 是否显示为一个小点\r\n\t * @event {Function} click 点击 Badge 触发事件\r\n\t * @example <uni-badge text=\"1\"></uni-badge>\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: 'UniBadge',\r\n\t\temits: ['click'],\r\n\t\tprops: {\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'error'\r\n\t\t\t},\r\n\t\t\tinverted: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tisDot: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tmaxNum: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 99\r\n\t\t\t},\r\n\t\t\tabsolute: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\toffset: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn [0, 0]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttext: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tsize: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'small'\r\n\t\t\t},\r\n\t\t\tcustomStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\twidth() {\r\n\t\t\t\treturn String(this.text).length * 8 + 12\r\n\t\t\t},\r\n\t\t\tclassNames() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tinverted,\r\n\t\t\t\t\ttype,\r\n\t\t\t\t\tsize,\r\n\t\t\t\t\tabsolute\r\n\t\t\t\t} = this\r\n\t\t\t\treturn [\r\n\t\t\t\t\tinverted ? 'uni-badge--' + type + '-inverted' : '',\r\n\t\t\t\t\t'uni-badge--' + type,\r\n\t\t\t\t\t'uni-badge--' + size,\r\n\t\t\t\t\tabsolute ? 'uni-badge--absolute' : ''\r\n\t\t\t\t].join(' ')\r\n\t\t\t},\r\n\t\t\tpositionStyle() {\r\n\t\t\t\tif (!this.absolute) return {}\r\n\t\t\t\tlet w = this.width / 2,\r\n\t\t\t\t\th = 10\r\n\t\t\t\tif (this.isDot) {\r\n\t\t\t\t\tw = 5\r\n\t\t\t\t\th = 5\r\n\t\t\t\t}\r\n\t\t\t\tconst x = `${- w  + this.offset[0]}px`\r\n\t\t\t\tconst y = `${- h + this.offset[1]}px`\r\n\r\n\t\t\t\tconst whiteList = {\r\n\t\t\t\t\trightTop: {\r\n\t\t\t\t\t\tright: x,\r\n\t\t\t\t\t\ttop: y\r\n\t\t\t\t\t},\r\n\t\t\t\t\trightBottom: {\r\n\t\t\t\t\t\tright: x,\r\n\t\t\t\t\t\tbottom: y\r\n\t\t\t\t\t},\r\n\t\t\t\t\tleftBottom: {\r\n\t\t\t\t\t\tleft: x,\r\n\t\t\t\t\t\tbottom: y\r\n\t\t\t\t\t},\r\n\t\t\t\t\tleftTop: {\r\n\t\t\t\t\t\tleft: x,\r\n\t\t\t\t\t\ttop: y\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tconst match = whiteList[this.absolute]\r\n\t\t\t\treturn match ? match : whiteList['rightTop']\r\n\t\t\t},\r\n\t\t\tdotStyle() {\r\n\t\t\t\tif (!this.isDot) return {}\r\n\t\t\t\treturn {\r\n\t\t\t\t\twidth: '10px',\r\n\t\t\t\t\tminWidth: '0',\r\n\t\t\t\t\theight: '10px',\r\n\t\t\t\t\tpadding: '0',\r\n\t\t\t\t\tborderRadius: '10px'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdisplayValue() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tisDot,\r\n\t\t\t\t\ttext,\r\n\t\t\t\t\tmaxNum\r\n\t\t\t\t} = this\r\n\t\t\t\treturn isDot ? '' : (Number(text) > maxNum ? `${maxNum}+` : text)\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonClick() {\r\n\t\t\t\tthis.$emit('click');\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" >\r\n\t$uni-primary: #2979ff !default;\r\n\t$uni-success: #4cd964 !default;\r\n\t$uni-warning: #f0ad4e !default;\r\n\t$uni-error: #dd524d !default;\r\n\t$uni-info: #909399 !default;\r\n\r\n\r\n\t$bage-size: 12px;\r\n\t$bage-small: scale(0.8);\r\n\r\n\t.uni-badge--x {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\t// align-self: flex-start;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: inline-block;\r\n\t\t/* #endif */\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.uni-badge--absolute {\r\n\t\tposition: absolute;\r\n\t}\r\n\r\n\t.uni-badge--small {\r\n\t\ttransform: $bage-small;\r\n\t\ttransform-origin: center center;\r\n\t}\r\n\r\n\t.uni-badge {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\toverflow: hidden;\r\n\t\tbox-sizing: border-box;\n\t\tfont-feature-settings: \"tnum\";\n\t\tmin-width: 20px;\r\n\t\t/* #endif */\r\n\t\tjustify-content: center;\r\n\t\tflex-direction: row;\r\n\t\theight: 20px;\r\n\t\tpadding: 0 4px;\r\n\t\tline-height: 18px;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 100px;\r\n\t\tbackground-color: $uni-info;\r\n\t\tbackground-color: transparent;\r\n\t\tborder: 1px solid #fff;\r\n\t\ttext-align: center;\r\n\t\tfont-family: 'Helvetica Neue', Helvetica, sans-serif;\r\n\t\tfont-size: $bage-size;\r\n\t\t/* #ifdef H5 */\r\n\t\tz-index: 999;\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\r\n\t\t&--info {\r\n\t\t\tcolor: #fff;\r\n\t\t\tbackground-color: $uni-info;\r\n\t\t}\r\n\r\n\t\t&--primary {\r\n\t\t\tbackground-color: $uni-primary;\r\n\t\t}\r\n\r\n\t\t&--success {\r\n\t\t\tbackground-color: $uni-success;\r\n\t\t}\r\n\r\n\t\t&--warning {\r\n\t\t\tbackground-color: $uni-warning;\r\n\t\t}\r\n\r\n\t\t&--error {\r\n\t\t\tbackground-color: $uni-error;\r\n\t\t}\r\n\r\n\t\t&--inverted {\r\n\t\t\tpadding: 0 5px 0 0;\r\n\t\t\tcolor: $uni-info;\r\n\t\t}\r\n\r\n\t\t&--info-inverted {\r\n\t\t\tcolor: $uni-info;\r\n\t\t\tbackground-color: transparent;\r\n\t\t}\r\n\r\n\t\t&--primary-inverted {\r\n\t\t\tcolor: $uni-primary;\r\n\t\t\tbackground-color: transparent;\r\n\t\t}\r\n\r\n\t\t&--success-inverted {\r\n\t\t\tcolor: $uni-success;\r\n\t\t\tbackground-color: transparent;\r\n\t\t}\r\n\r\n\t\t&--warning-inverted {\r\n\t\t\tcolor: $uni-warning;\r\n\t\t\tbackground-color: transparent;\r\n\t\t}\r\n\r\n\t\t&--error-inverted {\r\n\t\t\tcolor: $uni-error;\r\n\t\t\tbackground-color: transparent;\r\n\t\t}\r\n\r\n\t}\r\n</style>\r\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-badge.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-badge.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558451291\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/feedback_pkg/examine.vue?f9a5", "webpack:///D:/Xwzc/pages/feedback_pkg/examine.vue?0ad5", "webpack:///D:/Xwzc/pages/feedback_pkg/examine.vue?495e", "webpack:///D:/Xwzc/pages/feedback_pkg/examine.vue?d464", "uni-app:///pages/feedback_pkg/examine.vue", "webpack:///D:/Xwzc/uni_modules/uni-icons/components/uni-icons/uni-icons.vue?7ac7", "webpack:///D:/Xwzc/uni_modules/uni-icons/components/uni-icons/uni-icons.vue?c709", "webpack:///D:/Xwzc/uni_modules/uni-icons/components/uni-icons/uni-icons.vue?ad08", "webpack:///D:/Xwzc/uni_modules/uni-icons/components/uni-icons/uni-icons.vue?ac4c", "uni-app:///uni_modules/uni-icons/components/uni-icons/uni-icons.vue", "webpack:///D:/Xwzc/uni_modules/uni-icons/components/uni-icons/uni-icons.vue?dd52", "webpack:///D:/Xwzc/uni_modules/uni-icons/components/uni-icons/uni-icons.vue?c714", "webpack:///D:/Xwzc/pages/feedback_pkg/examine.vue?6bfb", "webpack:///D:/Xwzc/pages/feedback_pkg/examine.vue?1f2e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uniIcons", "data", "formDataId", "currentStep", "_isLoading", "hasInitialData", "isPageVisible", "selectedOptions", "supervisor", "PM", "GM", "tempReasonInput", "pm", "gm", "formData", "name", "project", "description", "images", "createTime", "createUserId", "isAdopted", "isCompleted", "workflowStatus", "meetingRequired", "terminatedBy", "terminatedTime", "lastUpdateTime", "remark", "updateTrigger", "responsibleUserId", "assignedTime", "assignReason", "responsibleCompletionDescription", "responsibleCompletionEvidence", "completedByResponsibleTime", "finalCompletedTime", "rejectReason", "rejectedTime", "actionHistory", "responsibleUsers", "selectedResponsibleIndex", "selectedResponsibleUser", "completionDescription", "completionEvidence", "finalConfirmReason", "steps", "text", "computed", "effectiveSteps", "isProcessTerminated", "isProcessComplete", "isLoading", "onLoad", "option", "methods", "getRoleByStep", "getRoleTitle", "assign", "final", "getSupervisorStatus", "status", "getPMStatus", "getGMStatus", "getStepStatus", "getSupervisorStatusText", "getPMStatusText", "getGMStatusText", "getStepStatusText", "getStepTime", "filter", "sort", "formatTime", "getSupervisorReasonText", "getPMReasonText", "getGMReasonText", "getDetail", "uni", "title", "mask", "cache<PERSON>ey", "cachedData", "Object", "uniCloud", "action", "id", "res", "timestamp", "content", "showCancel", "updateCurrentStep", "updateSelectedOptions", "handleApproval", "roles", "currentRoleIndex", "confirmText", "cancelText", "confirmRes", "decision", "finalReason", "reason", "workflowAction", "tempFieldMap", "returnedData", "todoBadgeManager", "setTimeout", "icon", "duration", "handleReset", "<PERSON><PERSON>ields", "getFinalStatusText", "loadResponsibleUsers", "console", "getResponsiblePickerDisplayText", "onResponsiblePickerChange", "handleAssignResponsible", "selectedUserId", "taskId", "isCurrentUserResponsible", "getResponsibleUserName", "uploadEvidence", "count", "sizeType", "sourceType", "i", "tempFile<PERSON>ath", "now", "year", "month", "day", "dateFolder", "fileExt", "uniqueFileName", "cloudPath", "filePath", "cloudPathAsRealPath", "uploadRes", "removeImage", "confirmColor", "success", "fileID", "fileList", "previewImage", "urls", "current", "handleResponsibleComplete", "handleFinalConfirm", "handleRejectCompletion", "silentRefreshData", "shouldRefreshOnCrossDeviceUpdate", "handleRecordDeleted", "delta", "fail", "url", "created", "<PERSON><PERSON><PERSON><PERSON>", "emits", "props", "type", "default", "color", "size", "customPrefix", "fontFamily", "icons", "unicode", "iconSize", "styleObj", "_onClick"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4IAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtYA;AAAA;AAAA;AAAA;AAAimB,CAAgB,2nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6hBrnB;AAAA,eAEA;EACAC;IACAC;MAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;QACAH;QACAI;QACAC;MACA;MACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;MACA;MACA;MACAC;MACAC;MACAC;MACAV;MACAW;MACAC;MACAC;MACAR;MACAS,QACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA;IAEA;EACA;EAEAC;IACA;IACAC;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;EAEAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA,MACAC;gBAAA;gBAAA;cAAA;cACA;cACA;cAAA;cAAA,OACA;YAAA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EAEAC;IACAC;MACA;MACA;IACA;IAEAC;MACA;QACAjD;QACAC;QACAC;QACAgD;QACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;MACA,mEACAC,uDACAA,2CACAA;QACA;QACA;MACA;MACA;MACA;QACA;UACA;QACA;UACA;UACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;;MAEA;MACA;MACA;MAEA;MACA,sGACAD;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEAE;MACA;MACA;;MAEA;MACA;MACA;QACA;MACA;MAEA;MACA,uFACAF;QACA;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAG;MACA;MACA;MAEA;QACA;QACA,kEACA,+DACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;QACA;UACA;YACA;YACA;YACA;UACA;UAEA;UACA,6CACAC;YAAA;UAAA,GACAC;YAAA;UAAA;UAEA;YACA;UACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA,mDACAH;UAAA;QAAA,GACAC;UAAA;QAAA;QAEA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAG;MACA;QACA,2CACAJ;UAAA;QAAA,GACAC;UAAA;QAAA;QAEA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAI;MACA;QACA,2CACAL;UAAA;QAAA,GACAC;UAAA;QAAA;QAEA;UACA;QACA;MACA;MAEA;IACA;IAEAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBAEAC;kBACAC;kBACAC;gBACA;;gBAEA;gBACAC;gBACA;kBACAC;kBACA;oBAAA;oBACA;oBACAC;oBACA;oBACA;sBACA;oBACA;oBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA,OAEAC;kBACApE;kBACAd;oBACAmF;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACArF,wBAEA;gBACA;kBACA;kBACAc;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACA;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACA;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACA;kBACAC;gBACA;gBAEA;;gBAEA;gBACA;gBACA;;gBAEA;gBACA;kBACA;oBACA;oBACAsC;sBACAU;sBACAtF;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAGA4E;kBACAW;kBACAC;gBACA;cAAA;gBAAA;gBAEAZ;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAa;MACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA,mDACA7B;QACA;MACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA8B;MACA;MACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MAEA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MAEA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;;gBAEA;gBACA;gBAAA;gBAGA;gBACAC;gBACAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACAjB;kBACAC;kBACAU;kBACAC;kBACAM;gBACA;gBAEA;gBACA;gBACA;gBAAA;cAAA;gBAMA;kBACAX;gBACA;kBACAA;gBACA;kBACAA;gBACA;;gBAEA;gBAAA;gBAAA,OACAP;kBACAC;kBACAU;kBACAO;kBACAC;gBACA;cAAA;gBALAC;gBAAA,IAOAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAIApB;kBACAC;kBACAC;gBACA;gBAAA,MAIAmB;kBAAA;kBAAA;gBAAA;gBACA;gBACAC,iDAEA;gBACA;kBACAA;gBACA;gBAAA;gBAAA,OAEAhB;kBACApE;kBACAd;oBACAmF;oBACAC;oBACAe;kBACA;gBACA;cAAA;gBAPAd;gBAAA;gBAAA;cAAA;gBAUA;kBACAe;gBACA;kBACAA;gBACA;kBACA;oBACA;oBACAA;kBACA;oBACA;oBACAA;kBACA;gBACA;;gBAEA;gBACAC;kBACA;kBACA;kBACA;gBACA;gBAEAH,iEAEA;gBACA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;gBAAA;gBAAA,OAEAhB;kBACApE;kBACAd;oBACAmF;oBACAC;oBACAe;kBACA;gBACA;cAAA;gBAPAd;cAAA;gBAAA,MAUAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACA;kBACA;kBACA;oBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBACAiB,gCAEA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;;gBAEA;gBACAD;kBACA;kBACA;kBACA;gBACA;gBACA;kBACA;gBACA;gBAEA;;gBAEA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;oBACA;oBACAzB;sBACAU;sBACAtF;oBACA;kBACA;gBACA;gBAEA4E;;gBAEA;gBACA2B;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAA;cAAA;gBAEA;gBACAC;kBACAD;gBACA;cAAA;gBAGA;gBACA3B;gBACA;gBACAA;;gBAEA;gBACAA;kBAAAQ;gBAAA;;gBAEA;gBACAoB;kBACA;oBACAD;kBACA;gBACA;gBAEA3B;kBACAC;kBACA4B;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA9B;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAGAA;kBACAW;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGA/B;kBACAC;kBACAU;kBACAO;kBACAC;gBACA;cAAA;gBALAC;gBAAA,IAOAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIApB;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEAI;kBACApE;kBACAd;oBACAmF;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACAiB,gCAEA;kBACAM;oBACA9F;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;kBACA,GAEA;kBACA,kDACAyF;oBACA;oBACAtF;oBACAF;oBACAC;oBACAE;oBACAG;oBACAC;oBACAW;oBACA;oBACAd;oBACAC;oBACAI;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAT;kBAAA,EACA;gBACA;;gBAEA;gBACA;kBACArB;kBACAC;kBACAC;gBACA;gBAEA;;gBAEA;gBACA;;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;oBACA;oBACAmE;sBACAU;sBACAtF;oBACA;kBACA;gBACA;gBAEA4E;;gBAEA;gBACA2B;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAA;cAAA;gBAEA;gBACAC;kBACAD;gBACA;cAAA;gBAGA;gBACA3B;gBACA;gBACAA;gBAEAA;kBACAC;kBACA4B;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA9B;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAGAA;kBACAW;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAqB;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA5B;kBACApE;kBACAd;oBACAmF;kBACA;gBACA;cAAA;gBALAE;gBAOA;kBACA;kBACA;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA0B;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAtC;kBAAAC;kBAAA4B;gBAAA;gBAAA;cAAA;gBAAA;gBAKA7B;kBAAAC;kBAAAC;gBAAA;;gBAEA;gBACAqC;gBACApF;gBAAA;gBAAA,OAEAmD;kBACApE;kBACAd;oBACAmF;oBACAC;oBACAvD;oBACAsE;kBACA;gBACA;cAAA;gBARAd;gBAAA,MAUAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACAiB,gCAEA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBACA;gBACA;gBACA;gBAEA1B;gBACAA;kBAAAC;kBAAA4B;gBAAA;;gBAEA;gBACA7B;kBACAwC;kBACAvF;kBACAC;gBACA;;gBAEA;gBACA8C;;gBAEA;gBACAA;kBAAAQ;gBAAA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAR;gBACAA;kBACAW;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA6B;MAAA;MACA;MACA;IACA;IAEAC;MAAA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;;MAEA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA3C;kBACA4C;kBACAC;kBACAC;gBACA;cAAA;gBAJArC;gBAMAsC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAC,qCAEA;gBACAhD;kBACAC;kBACAC;gBACA;;gBAEA;gBACA+C;gBACAC;gBACAC;gBACAC;gBACAC,wDAEA;gBACAC;gBACAC;gBAEAC;gBAAA;gBAAA,OACAlD;kBACAmD;kBACAD;kBACAE;gBACA;cAAA;gBAJAC;gBAMA;kBACA;gBACA;cAAA;gBA7BAZ;gBAAA;gBAAA;cAAA;gBAgCA/C;kBAAAC;kBAAA4B;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA7B;kBAAAC;kBAAA4B;gBAAA;cAAA;gBAAA;gBAEA7B;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA4D;MAAA;MACA5D;QACAC;QACAU;QACAkD;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACArD;sBAAA;sBAAA;oBAAA;oBACAsD,2CAEA;oBACA/D;sBACAC;sBACAC;oBACA;oBAAA;oBAAA,MAIA6D;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OACAzD;sBACApE;sBACAd;wBACA4I;sBACA;oBACA;kBAAA;oBAGA;oBACA;oBAEAhE;sBACAC;sBACA4B;sBACAC;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEA;;oBAEA;oBACA;oBAEA9B;sBACAC;sBACA4B;sBACAC;oBACA;kBAAA;oBAAA;oBAEA9B;oBAAA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEAiE;MACAjE;QACAkE;QACAC;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACApE;kBAAAC;kBAAA4B;gBAAA;gBAAA;cAAA;gBAAA;gBAKA7B;kBAAAC;kBAAAC;gBAAA;gBAAA;gBAAA,OAEAI;kBACApE;kBACAd;oBACAmF;oBACAC;oBACA1C;oBACAC;kBACA;gBACA;cAAA;gBARA0C;gBAAA,MAUAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACAiB,gCAEA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAEA1B;gBACAA;kBAAAC;kBAAA4B;gBAAA;;gBAEA;gBACA7B;;gBAEA;gBACAA;kBAAAQ;gBAAA;;gBAEA;gBACA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAR;gBACAA;kBACAW;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAyD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACArE;kBAAAC;kBAAA4B;gBAAA;gBAAA;cAAA;gBAAA;gBAKA7B;kBAAAC;kBAAAC;gBAAA;gBAAA;gBAAA,OAEAI;kBACApE;kBACAd;oBACAmF;oBACAC;oBACAe;kBACA;gBACA;cAAA;gBAPAd;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACAiB,gCAEA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAEA1B;gBACAA;kBAAAC;kBAAA4B;gBAAA;;gBAEA;gBACA7B;;gBAEA;gBACAA;kBAAAQ;gBAAA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAR;gBACAA;kBACAW;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA0D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAtE;kBAAAC;kBAAA4B;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA7B;kBACAC;kBACAU;kBACAO;kBACAC;gBACA;cAAA;gBALAC;gBAAA,IAOAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEApB;kBAAAC;kBAAAC;gBAAA;;gBAEA;gBAAA;gBAAA,OACAI;kBACApE;kBACAd;oBACAmF;oBACAC;oBACA9D;oBACAc;kBACA;gBACA;cAAA;gBARAiD;gBAAA,MAUAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACAiB,gCAEA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;;gBAEA;gBACA;gBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBACA;gBAEA1B;gBACAA;kBAAAC;kBAAA4B;gBAAA;;gBAEA;gBACA7B;;gBAEA;gBACAA;kBAAAQ;gBAAA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAR;gBACAA;kBACAW;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;AACA;IACA2D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEAjE;kBACApE;kBACAd;oBACAmF;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAQA;kBACArF,wBAEA;kBACA;kBACA;oBACA;oBACAc;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACA;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACA;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACA;oBACAC;kBACA;;kBAEA;kBACA;kBACA;;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAyE;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAqC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACAzE;MACA;;MAEA;MACAA;QACAC;QACAU;QACAC;QACAM;QACA4C;UACA;UACA;YACA;YACA9D;UACA;;UAEA;UACAA;UACAA;UACAA;;UAEA;UACA;UACA;YACA2B;UACA;;UAEA;UACA3B;YACA0E;YACAC;cACA;cACA3E;gBACA4E;cACA;YACA;UACA;QACA;MACA;IACA;EAEA;EAEA;AACA;AACA;EACAC;IAAA;IACA;IACA7E;MACA;QACA;QACA;QACA;UACAmC;UACA;UACA;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACA2C;IACA;IACA9E;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7qEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACYvnB;;;;;;;;;;;;;AAEA;EACA;EACA;AACA;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,eAUA;EACA9D;EACA6I;EACAC;IACAC;MACAA;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACA9J;IACA;MACAmK;IACA;EACA;EACApH;IACAqH;MAAA;MACA;QAAA;MAAA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;EACAhH;IACAiH;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC3FA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,onCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAm3B,CAAgB,o3BAAG,EAAC,C;;;;;;;;;;;ACAv4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/feedback_pkg/examine.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/feedback_pkg/examine.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./examine.vue?vue&type=template&id=4afdd65d&\"\nvar renderjs\nimport script from \"./examine.vue?vue&type=script&lang=js&\"\nexport * from \"./examine.vue?vue&type=script&lang=js&\"\nimport style0 from \"./examine.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/feedback_pkg/examine.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./examine.vue?vue&type=template&id=4afdd65d&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = !!_vm.hasInitialData\n    ? _vm.__map(_vm.effectiveSteps, function (step, index) {\n        var $orig = _vm.__get_orig(step)\n        var m0 = _vm.getStepStatus(index)\n        var m1 = _vm.getStepStatus(index)\n        var m2 = _vm.getStepStatus(index)\n        var m3 = _vm.getStepStatus(index)\n        var g0 = _vm.effectiveSteps.length\n        var m4 = _vm.getStepStatus(index)\n        var m5 =\n          m4 !== \"pending\"\n            ? _vm.getStepStatus(index) === \"completed\" ||\n              _vm.getStepStatus(index) === \"approved\"\n            : null\n        var m6 = m4 !== \"pending\" ? _vm.getStepStatus(index) : null\n        var m7 = m4 !== \"pending\" ? _vm.getStepStatus(index) : null\n        var m8 = m4 !== \"pending\" ? _vm.getStepStatus(index) : null\n        var m9 =\n          m4 !== \"pending\" ? _vm.getRoleTitle(_vm.getRoleByStep(index)) : null\n        var m10 = m4 !== \"pending\" ? _vm.getStepStatusText(index) : null\n        var m11 = m4 !== \"pending\" ? _vm.getStepTime(index) : null\n        var m12 =\n          m4 !== \"pending\" && m11\n            ? _vm.formatTime(_vm.getStepTime(index))\n            : null\n        var m13 =\n          !(m4 !== \"pending\") && index === _vm.currentStep && index <= 2\n            ? _vm.getRoleTitle(_vm.getRoleByStep(index))\n            : null\n        var m14 =\n          !(m4 !== \"pending\") &&\n          index === _vm.currentStep &&\n          !(index <= 2) &&\n          index === 3\n            ? _vm.getStepStatusText(index)\n            : null\n        var m15 =\n          !(m4 !== \"pending\") &&\n          index === _vm.currentStep &&\n          !(index <= 2) &&\n          !(index === 3) &&\n          index === 4\n            ? _vm.getStepStatusText(index)\n            : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          g0: g0,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n          m9: m9,\n          m10: m10,\n          m11: m11,\n          m12: m12,\n          m13: m13,\n          m14: m14,\n          m15: m15,\n        }\n      })\n    : null\n  var m16 = !!_vm.hasInitialData\n    ? _vm.uniIDHasRole(\"supervisor\") || _vm.uniIDHasRole(\"admin\")\n    : null\n  var m17 = !!_vm.hasInitialData && m16 ? _vm.getSupervisorStatus() : null\n  var m18 = !!_vm.hasInitialData && m16 ? _vm.getSupervisorStatus() : null\n  var m19 = !!_vm.hasInitialData && m16 ? _vm.getSupervisorStatus() : null\n  var m20 = !!_vm.hasInitialData && m16 ? _vm.getSupervisorStatus() : null\n  var m21 = !!_vm.hasInitialData && m16 ? _vm.getSupervisorStatus() : null\n  var m22 = !!_vm.hasInitialData && m16 ? _vm.getSupervisorStatus() : null\n  var m23 = !!_vm.hasInitialData && m16 ? _vm.getSupervisorStatus() : null\n  var m24 = !!_vm.hasInitialData && m16 ? _vm.getSupervisorStatus() : null\n  var m25 = !!_vm.hasInitialData && m16 ? _vm.getSupervisorStatus() : null\n  var m26 = !!_vm.hasInitialData && m16 ? _vm.getSupervisorStatusText() : null\n  var m27 = !!_vm.hasInitialData && m16 ? _vm.getSupervisorStatus() : null\n  var g1 =\n    !!_vm.hasInitialData && m16 && m27 === \"pending\"\n      ? (_vm.tempReasonInput.supervisor || \"\").length\n      : null\n  var m28 =\n    !!_vm.hasInitialData && m16 && !(m27 === \"pending\")\n      ? _vm.getSupervisorReasonText()\n      : null\n  var m29 = !!_vm.hasInitialData\n    ? _vm.uniIDHasRole(\"PM\") || _vm.uniIDHasRole(\"admin\")\n    : null\n  var m30 = !!_vm.hasInitialData && m29 ? _vm.getPMStatus() : null\n  var m31 = !!_vm.hasInitialData && m29 ? _vm.getPMStatus() : null\n  var m32 = !!_vm.hasInitialData && m29 ? _vm.getPMStatus() : null\n  var m33 = !!_vm.hasInitialData && m29 ? _vm.getPMStatus() : null\n  var m34 = !!_vm.hasInitialData && m29 ? _vm.getPMStatus() : null\n  var m35 = !!_vm.hasInitialData && m29 ? _vm.getPMStatus() : null\n  var m36 = !!_vm.hasInitialData && m29 ? _vm.getPMStatus() : null\n  var m37 = !!_vm.hasInitialData && m29 ? _vm.getPMStatusText() : null\n  var m38 = !!_vm.hasInitialData && m29 ? _vm.getPMStatus() : null\n  var g2 =\n    !!_vm.hasInitialData && m29 && m38 === \"pending\"\n      ? (_vm.tempReasonInput.pm || \"\").length\n      : null\n  var m39 =\n    !!_vm.hasInitialData && m29 && !(m38 === \"pending\")\n      ? _vm.getPMReasonText()\n      : null\n  var m40 = !!_vm.hasInitialData\n    ? _vm.uniIDHasRole(\"GM\") || _vm.uniIDHasRole(\"admin\")\n    : null\n  var m41 = !!_vm.hasInitialData && m40 ? _vm.getGMStatus() : null\n  var m42 = !!_vm.hasInitialData && m40 ? _vm.getGMStatus() : null\n  var m43 = !!_vm.hasInitialData && m40 ? _vm.getGMStatus() : null\n  var m44 = !!_vm.hasInitialData && m40 ? _vm.getGMStatus() : null\n  var m45 = !!_vm.hasInitialData && m40 ? _vm.getGMStatus() : null\n  var m46 = !!_vm.hasInitialData && m40 ? _vm.getGMStatus() : null\n  var m47 = !!_vm.hasInitialData && m40 ? _vm.getGMStatus() : null\n  var m48 = !!_vm.hasInitialData && m40 ? _vm.getGMStatusText() : null\n  var m49 = !!_vm.hasInitialData && m40 ? _vm.getGMStatus() : null\n  var g3 =\n    !!_vm.hasInitialData && m40 && m49 === \"pending\"\n      ? (_vm.tempReasonInput.gm || \"\").length\n      : null\n  var m50 =\n    !!_vm.hasInitialData && m40 && !(m49 === \"pending\")\n      ? _vm.getGMReasonText()\n      : null\n  var m51 = !!_vm.hasInitialData\n    ? (_vm.uniIDHasRole(\"GM\") || _vm.uniIDHasRole(\"admin\")) &&\n      _vm.formData.workflowStatus === \"gm_approved_pending_assign\" &&\n      !_vm.formData.responsibleUserId\n    : null\n  var g4 = !!_vm.hasInitialData && m51 ? _vm.responsibleUsers.length : null\n  var m52 =\n    !!_vm.hasInitialData && m51 ? _vm.getResponsiblePickerDisplayText() : null\n  var g5 = !!_vm.hasInitialData && m51 ? _vm.assignReason.length : null\n  var m53 =\n    !!_vm.hasInitialData &&\n    _vm.formData.responsibleUserId &&\n    (_vm.formData.workflowStatus === \"assigned_to_responsible\" ||\n      _vm.formData.workflowStatus === \"completed_by_responsible\" ||\n      _vm.formData.workflowStatus === \"final_completed\")\n      ? _vm.getResponsibleUserName()\n      : null\n  var m54 =\n    !!_vm.hasInitialData &&\n    _vm.formData.responsibleUserId &&\n    (_vm.formData.workflowStatus === \"assigned_to_responsible\" ||\n      _vm.formData.workflowStatus === \"completed_by_responsible\" ||\n      _vm.formData.workflowStatus === \"final_completed\") &&\n    _vm.formData.assignedTime\n      ? _vm.formatTime(_vm.formData.assignedTime)\n      : null\n  var m55 =\n    !!_vm.hasInitialData &&\n    _vm.formData.responsibleUserId &&\n    (_vm.formData.workflowStatus === \"assigned_to_responsible\" ||\n      _vm.formData.workflowStatus === \"completed_by_responsible\" ||\n      _vm.formData.workflowStatus === \"final_completed\") &&\n    _vm.formData.completedByResponsibleTime\n      ? _vm.formatTime(_vm.formData.completedByResponsibleTime)\n      : null\n  var m56 =\n    !!_vm.hasInitialData &&\n    _vm.formData.responsibleUserId &&\n    (_vm.formData.workflowStatus === \"assigned_to_responsible\" ||\n      _vm.formData.workflowStatus === \"completed_by_responsible\" ||\n      _vm.formData.workflowStatus === \"final_completed\") &&\n    _vm.formData.rejectReason &&\n    _vm.formData.rejectedTime\n      ? _vm.formatTime(_vm.formData.rejectedTime)\n      : null\n  var g6 =\n    !!_vm.hasInitialData &&\n    _vm.formData.responsibleUserId &&\n    (_vm.formData.workflowStatus === \"assigned_to_responsible\" ||\n      _vm.formData.workflowStatus === \"completed_by_responsible\" ||\n      _vm.formData.workflowStatus === \"final_completed\") &&\n    (_vm.formData.workflowStatus === \"completed_by_responsible\" ||\n      _vm.formData.workflowStatus === \"final_completed\")\n      ? _vm.formData.responsibleCompletionEvidence &&\n        _vm.formData.responsibleCompletionEvidence.length > 0\n      : null\n  var m57 =\n    !!_vm.hasInitialData &&\n    _vm.formData.responsibleUserId &&\n    (_vm.formData.workflowStatus === \"assigned_to_responsible\" ||\n      _vm.formData.workflowStatus === \"completed_by_responsible\" ||\n      _vm.formData.workflowStatus === \"final_completed\")\n      ? _vm.formData.workflowStatus === \"assigned_to_responsible\" &&\n        (_vm.isCurrentUserResponsible() || _vm.uniIDHasRole(\"admin\"))\n      : null\n  var g7 =\n    !!_vm.hasInitialData &&\n    _vm.formData.responsibleUserId &&\n    (_vm.formData.workflowStatus === \"assigned_to_responsible\" ||\n      _vm.formData.workflowStatus === \"completed_by_responsible\" ||\n      _vm.formData.workflowStatus === \"final_completed\") &&\n    m57\n      ? _vm.completionDescription.length\n      : null\n  var g8 =\n    !!_vm.hasInitialData &&\n    _vm.formData.responsibleUserId &&\n    (_vm.formData.workflowStatus === \"assigned_to_responsible\" ||\n      _vm.formData.workflowStatus === \"completed_by_responsible\" ||\n      _vm.formData.workflowStatus === \"final_completed\") &&\n    m57\n      ? _vm.completionEvidence.length\n      : null\n  var m58 = !!_vm.hasInitialData\n    ? (_vm.uniIDHasRole(\"GM\") || _vm.uniIDHasRole(\"admin\")) &&\n      _vm.formData.workflowStatus === \"completed_by_responsible\"\n    : null\n  var m59 = !!_vm.hasInitialData && m58 ? _vm.getResponsibleUserName() : null\n  var m60 =\n    !!_vm.hasInitialData && m58 && _vm.formData.completedByResponsibleTime\n      ? _vm.formatTime(_vm.formData.completedByResponsibleTime)\n      : null\n  var g9 = !!_vm.hasInitialData && m58 ? _vm.finalConfirmReason.length : null\n  var g10 = !!_vm.hasInitialData && m58 ? _vm.rejectReason.length : null\n  var m61 =\n    !!_vm.hasInitialData &&\n    _vm.formData.workflowStatus === \"final_completed\" &&\n    _vm.formData.finalCompletedTime\n      ? _vm.formatTime(_vm.formData.finalCompletedTime)\n      : null\n  var m62 =\n    !!_vm.hasInitialData && _vm.formData.workflowStatus === \"final_completed\"\n      ? _vm.getResponsibleUserName()\n      : null\n  var m63 = !!_vm.hasInitialData ? _vm.getSupervisorStatus() : null\n  var m64 = !!_vm.hasInitialData ? _vm.getSupervisorStatus() : null\n  var m65 = !!_vm.hasInitialData ? _vm.getSupervisorStatus() : null\n  var m66 = !!_vm.hasInitialData ? _vm.getSupervisorStatus() : null\n  var m67 = !!_vm.hasInitialData ? _vm.getSupervisorStatusText() : null\n  var m68 = !!_vm.hasInitialData ? _vm.getSupervisorStatus() : null\n  var m69 = !!_vm.hasInitialData ? _vm.getSupervisorStatus() : null\n  var m70 = !!_vm.hasInitialData ? _vm.getSupervisorStatus() : null\n  var m71 = !!_vm.hasInitialData ? _vm.getSupervisorStatus() : null\n  var m72 = !!_vm.hasInitialData ? _vm.getSupervisorReasonText() : null\n  var m73 = !!_vm.hasInitialData ? _vm.getPMStatus() : null\n  var m74 = !!_vm.hasInitialData ? _vm.getPMStatus() : null\n  var m75 = !!_vm.hasInitialData ? _vm.getPMStatus() : null\n  var m76 = !!_vm.hasInitialData ? _vm.getPMStatus() : null\n  var m77 = !!_vm.hasInitialData ? _vm.getPMStatusText() : null\n  var m78 = !!_vm.hasInitialData ? _vm.getPMStatus() : null\n  var m79 = !!_vm.hasInitialData ? _vm.getPMStatus() : null\n  var m80 = !!_vm.hasInitialData ? _vm.getPMStatus() : null\n  var m81 = !!_vm.hasInitialData ? _vm.getPMReasonText() : null\n  var m82 = !!_vm.hasInitialData ? _vm.getGMStatus() : null\n  var m83 = !!_vm.hasInitialData ? _vm.getGMStatus() : null\n  var m84 = !!_vm.hasInitialData ? _vm.getGMStatus() : null\n  var m85 = !!_vm.hasInitialData ? _vm.getGMStatus() : null\n  var m86 = !!_vm.hasInitialData ? _vm.getGMStatusText() : null\n  var m87 = !!_vm.hasInitialData ? _vm.getGMStatus() : null\n  var m88 = !!_vm.hasInitialData ? _vm.getGMStatus() : null\n  var m89 = !!_vm.hasInitialData ? _vm.getGMStatus() : null\n  var m90 = !!_vm.hasInitialData ? _vm.getGMReasonText() : null\n  var m91 = !!_vm.hasInitialData\n    ? _vm.uniIDHasRole(\"GM\") || _vm.uniIDHasRole(\"admin\")\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        g1: g1,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        m35: m35,\n        m36: m36,\n        m37: m37,\n        m38: m38,\n        g2: g2,\n        m39: m39,\n        m40: m40,\n        m41: m41,\n        m42: m42,\n        m43: m43,\n        m44: m44,\n        m45: m45,\n        m46: m46,\n        m47: m47,\n        m48: m48,\n        m49: m49,\n        g3: g3,\n        m50: m50,\n        m51: m51,\n        g4: g4,\n        m52: m52,\n        g5: g5,\n        m53: m53,\n        m54: m54,\n        m55: m55,\n        m56: m56,\n        g6: g6,\n        m57: m57,\n        g7: g7,\n        g8: g8,\n        m58: m58,\n        m59: m59,\n        m60: m60,\n        g9: g9,\n        g10: g10,\n        m61: m61,\n        m62: m62,\n        m63: m63,\n        m64: m64,\n        m65: m65,\n        m66: m66,\n        m67: m67,\n        m68: m68,\n        m69: m69,\n        m70: m70,\n        m71: m71,\n        m72: m72,\n        m73: m73,\n        m74: m74,\n        m75: m75,\n        m76: m76,\n        m77: m77,\n        m78: m78,\n        m79: m79,\n        m80: m80,\n        m81: m81,\n        m82: m82,\n        m83: m83,\n        m84: m84,\n        m85: m85,\n        m86: m86,\n        m87: m87,\n        m88: m88,\n        m89: m89,\n        m90: m90,\n        m91: m91,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./examine.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./examine.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"uni-container\">\n    <view class=\"db-container\">\n      <!-- 问题描述区域 -->\n      <view class=\"description-section\" v-if=\"hasInitialData\">\n        <view class=\"description-header\">\n          <text class=\"description-title\">问题描述</text>\n        </view>\n        <view class=\"description-content\">\n          <text class=\"description-text\">{{ formData.description || '暂无问题描述' }}</text>\n        </view>\n      </view>\n\n      <!-- 骨架屏 -->\n      <template v-if=\"!hasInitialData\">\n        <view class=\"skeleton-loading\">\n          <view class=\"skeleton-steps-section\">\n            <view v-for=\"i in 5\" :key=\"i\" class=\"skeleton-step\"></view>\n          </view>\n          <view class=\"skeleton-approval-section\"></view>\n        </view>\n      </template>\n      \n      <template v-else>\n        <!-- 步骤条 - 基于新工作流状态 -->\n        <view class=\"steps-section\">\n          <view class=\"steps\">\n            <view v-for=\"(step, index) in effectiveSteps\"\n                  :key=\"index\" \n                  class=\"step\" \n                  :class=\"{ \n                    'active': index === currentStep,\n                    'completed': getStepStatus(index) === 'completed',\n                    'rejected': getStepStatus(index) === 'rejected',\n                    'meeting': getStepStatus(index) === 'meeting',\n                    'not-executed': getStepStatus(index) === 'not_executed'\n                  }\">\n              <view class=\"step-content\">\n                <view class=\"step-icon\">\n                  <text class=\"icon-number\">{{ index + 1 }}</text>\n                  <view class=\"icon-line\" v-if=\"index !== effectiveSteps.length - 1\"></view>\n                </view>\n                <view class=\"step-info\">\n                  <text class=\"step-title\">{{ step.text }}</text>\n                  <!-- 已完成状态显示 -->\n                  <view class=\"step-status-info\" v-if=\"getStepStatus(index) !== 'pending'\">\n                    <text class=\"status-text\" \n                          :class=\"{\n                            'status-text-approved': getStepStatus(index) === 'completed' || getStepStatus(index) === 'approved',\n                            'status-text-rejected': getStepStatus(index) === 'rejected',\n                            'status-text-meeting': getStepStatus(index) === 'meeting',\n                            'status-text-not-executed': getStepStatus(index) === 'not_executed'\n                          }\">\n                      {{ getRoleTitle(getRoleByStep(index)) }}{{ getStepStatusText(index) }}\n                    </text>\n                    <text class=\"time-text\" v-if=\"getStepTime(index)\">\n                      {{ formatTime(getStepTime(index)) }}\n                    </text>\n                  </view>\n                  <!-- 等待状态显示 -->\n                  <text class=\"step-waiting\" v-else-if=\"index === currentStep\">\n                    <template v-if=\"index <= 2\">等待{{ getRoleTitle(getRoleByStep(index)) }}中...</template>\n                    <template v-else-if=\"index === 3\">{{ getStepStatusText(index) }}</template>\n                    <template v-else-if=\"index === 4\">{{ getStepStatusText(index) }}</template>\n                  </text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 主管审核区域 -->\n        <view class=\"approval-section\"\n              :class=\"{ \n                'active': currentStep === 0,\n                'disabled': currentStep !== 0 || isProcessTerminated\n              }\" \n              v-if=\"uniIDHasRole('supervisor') || uniIDHasRole('admin')\">\n          <view class=\"approval-header\">\n            <text class=\"approval-title\">主管审核</text>\n            <view class=\"approval-status\" :class=\"{\n              'status-approved': getSupervisorStatus() === 'approved',\n              'status-rejected': getSupervisorStatus() === 'rejected',\n              'status-pending': getSupervisorStatus() === 'pending',\n              'status-meeting': getSupervisorStatus() === 'meeting',\n              'status-not-executed': getSupervisorStatus() === 'not_executed'\n            }\">\n              <uni-icons v-if=\"getSupervisorStatus() === 'approved'\" type=\"checkmarkempty\" size=\"16\" color=\"#4CAF50\"></uni-icons>\n              <uni-icons v-if=\"getSupervisorStatus() === 'rejected'\" type=\"closeempty\" size=\"16\" color=\"#F44336\"></uni-icons>\n              <uni-icons v-if=\"getSupervisorStatus() === 'meeting'\" type=\"calendar\" size=\"16\" color=\"#9C27B0\"></uni-icons>\n              <uni-icons v-if=\"getSupervisorStatus() === 'not_executed'\" type=\"minus\" size=\"16\" color=\"#999\"></uni-icons>\n              <text>{{ getSupervisorStatusText() }}</text>\n            </view>\n          </view>\n          <view class=\"approval-options\">\n            <label class=\"radio-item\" @click=\"handleApproval('supervisor', true)\">\n              <radio :checked=\"selectedOptions.supervisor === true\" \n                     :disabled=\"currentStep !== 0\"\n                     :name=\"`supervisor-approve`\"/>\n              <text>同意</text>\n            </label>\n            <label class=\"radio-item\" @click=\"handleApproval('supervisor', false)\">\n              <radio :checked=\"selectedOptions.supervisor === false\" \n                     :disabled=\"currentStep !== 0\"\n                     :name=\"`supervisor-reject`\"/>\n              <text>不同意</text>\n            </label>\n            <label class=\"radio-item\" @click=\"handleApproval('supervisor', 'meeting')\">\n              <radio :checked=\"selectedOptions.supervisor === 'meeting'\" \n                     :disabled=\"currentStep !== 0\"\n                     :name=\"`supervisor-meeting`\"/>\n              <text>例会讨论</text>\n            </label>\n          </view>\n          <!-- 审核理由区域：未审核时显示输入框，已审核时显示内容 -->\n          <view class=\"reason-box\">\n            <text class=\"reason-label\">审核理由：</text>\n            <template v-if=\"getSupervisorStatus() === 'pending'\">\n              <textarea class=\"reason-input\"\n                        v-model=\"tempReasonInput.supervisor\"\n                        :disabled=\"currentStep !== 0\"\n                        placeholder=\"请输入审核理由\" \n                        maxlength=\"200\"/>\n              <text class=\"character-count\">{{ (tempReasonInput.supervisor || '').length }}/200</text>\n            </template>\n            <view v-else class=\"submitted-reason\">\n              <text class=\"reason-content\">{{ getSupervisorReasonText() }}</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 副厂长审核区域 -->\n        <view class=\"approval-section\"\n              :class=\"{ \n                'active': currentStep === 1,\n                'disabled': currentStep !== 1 || isProcessTerminated\n              }\" \n              v-if=\"uniIDHasRole('PM') || uniIDHasRole('admin')\">\n          <view class=\"approval-header\">\n            <text class=\"approval-title\">副厂长审核</text>\n            <view class=\"approval-status\" :class=\"{\n              'status-approved': getPMStatus() === 'approved',\n              'status-rejected': getPMStatus() === 'rejected',\n              'status-pending': getPMStatus() === 'pending',\n              'status-not-executed': getPMStatus() === 'not_executed'\n            }\">\n              <uni-icons v-if=\"getPMStatus() === 'approved'\" type=\"checkmarkempty\" size=\"16\" color=\"#4CAF50\"></uni-icons>\n              <uni-icons v-if=\"getPMStatus() === 'rejected'\" type=\"closeempty\" size=\"16\" color=\"#F44336\"></uni-icons>\n              <uni-icons v-if=\"getPMStatus() === 'not_executed'\" type=\"minus\" size=\"16\" color=\"#999\"></uni-icons>\n              <text>{{ getPMStatusText() }}</text>\n            </view>\n          </view>\n          <view class=\"approval-options\">\n            <label class=\"radio-item\" @click=\"handleApproval('PM', true)\">\n              <radio :checked=\"selectedOptions.PM === true\" :disabled=\"currentStep !== 1\" :name=\"`PM-approve`\"/>\n              <text>同意</text>\n            </label>\n            <label class=\"radio-item\" @click=\"handleApproval('PM', false)\">\n              <radio :checked=\"selectedOptions.PM === false\" :disabled=\"currentStep !== 1\" :name=\"`PM-reject`\"/>\n              <text>不同意</text>\n            </label>\n          </view>\n          <!-- 审核理由区域：未审核时显示输入框，已审核时显示内容 -->\n          <view class=\"reason-box\">\n            <text class=\"reason-label\">审核理由：</text>\n            <template v-if=\"getPMStatus() === 'pending'\">\n              <textarea class=\"reason-input\"\n                        v-model=\"tempReasonInput.pm\"\n                        :disabled=\"currentStep !== 1\"\n                        placeholder=\"请输入审核理由\" \n                        maxlength=\"200\"/>\n              <text class=\"character-count\">{{ (tempReasonInput.pm || '').length }}/200</text>\n            </template>\n            <view v-else class=\"submitted-reason\">\n              <text class=\"reason-content\">{{ getPMReasonText() }}</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 厂长审核区域 -->\n        <view class=\"approval-section\"\n              :class=\"{ \n                'active': currentStep === 2,\n                'disabled': currentStep !== 2 || isProcessTerminated\n              }\" \n              v-if=\"uniIDHasRole('GM') || uniIDHasRole('admin')\">\n          <view class=\"approval-header\">\n            <text class=\"approval-title\">厂长审核</text>\n            <view class=\"approval-status\" :class=\"{\n              'status-approved': getGMStatus() === 'approved',\n              'status-rejected': getGMStatus() === 'rejected',\n              'status-pending': getGMStatus() === 'pending',\n              'status-not-executed': getGMStatus() === 'not_executed'\n            }\">\n              <uni-icons v-if=\"getGMStatus() === 'approved'\" type=\"checkmarkempty\" size=\"16\" color=\"#4CAF50\"></uni-icons>\n              <uni-icons v-if=\"getGMStatus() === 'rejected'\" type=\"closeempty\" size=\"16\" color=\"#F44336\"></uni-icons>\n              <uni-icons v-if=\"getGMStatus() === 'not_executed'\" type=\"minus\" size=\"16\" color=\"#999\"></uni-icons>\n              <text>{{ getGMStatusText() }}</text>\n            </view>\n          </view>\n          <view class=\"approval-options\">\n            <label class=\"radio-item\" @click=\"handleApproval('GM', true)\">\n              <radio :checked=\"selectedOptions.GM === true\" :disabled=\"currentStep !== 2\" :name=\"`GM-approve`\"/>\n              <text>同意</text>\n            </label>\n            <label class=\"radio-item\" @click=\"handleApproval('GM', false)\">\n              <radio :checked=\"selectedOptions.GM === false\" :disabled=\"currentStep !== 2\" :name=\"`GM-reject`\"/>\n              <text>不同意</text>\n            </label>\n          </view>\n          <!-- 审核理由区域：未审核时显示输入框，已审核时显示内容 -->\n          <view class=\"reason-box\">\n            <text class=\"reason-label\">审核理由：</text>\n            <template v-if=\"getGMStatus() === 'pending'\">\n              <textarea class=\"reason-input\"\n                        v-model=\"tempReasonInput.gm\"\n                        :disabled=\"currentStep !== 2\"\n                        placeholder=\"请输入审核理由\" \n                        maxlength=\"200\"/>\n              <text class=\"character-count\">{{ (tempReasonInput.gm || '').length }}/200</text>\n            </template>\n            <view v-else class=\"submitted-reason\">\n              <text class=\"reason-content\">{{ getGMReasonText() }}</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 厂长指派负责人区域 -->\n        <view class=\"approval-section\"\n              :class=\"{ \n                'active': formData.workflowStatus === 'gm_approved_pending_assign',\n                'disabled': formData.workflowStatus !== 'gm_approved_pending_assign'\n              }\" \n              v-if=\"(uniIDHasRole('GM') || uniIDHasRole('admin')) && formData.workflowStatus === 'gm_approved_pending_assign' && !formData.responsibleUserId\">\n          <view class=\"approval-header\">\n            <text class=\"approval-title\">指派负责人</text>\n            <view class=\"approval-status status-pending\">\n              <uni-icons type=\"person\" size=\"16\" color=\"#3b82f6\"></uni-icons>\n              <text>待指派负责人</text>\n            </view>\n          </view>\n          <view class=\"assign-section\">\n            <view class=\"assign-picker\">\n              <text class=\"picker-label\">选择负责人：</text>\n              <picker @change=\"onResponsiblePickerChange\" :value=\"selectedResponsibleIndex\" :range=\"responsibleUsers\" range-key=\"nickname\" :disabled=\"responsibleUsers.length === 0\">\n                <view class=\"uni-input\">{{ getResponsiblePickerDisplayText() }}</view>\n              </picker>\n            </view>\n            <view class=\"reason-box\">\n              <text class=\"reason-label\">指派说明（可选）：</text>\n              <textarea class=\"reason-input\"\n                        v-model=\"assignReason\"\n                        placeholder=\"请输入指派说明\" \n                        maxlength=\"200\"/>\n              <text class=\"character-count\">{{ assignReason.length }}/200</text>\n            </view>\n            <button class=\"assign-btn\" type=\"primary\" @click=\"handleAssignResponsible\" :disabled=\"!selectedResponsibleUser\">确认指派</button>\n          </view>\n        </view>\n\n        <!-- 负责人工作状态显示区域 -->\n        <view class=\"approval-section\"\n              :class=\"{ \n                'active': formData.workflowStatus === 'assigned_to_responsible',\n                'disabled': formData.workflowStatus !== 'assigned_to_responsible' && formData.workflowStatus !== 'completed_by_responsible' && formData.workflowStatus !== 'final_completed'\n              }\"\n              v-if=\"formData.responsibleUserId && (formData.workflowStatus === 'assigned_to_responsible' || formData.workflowStatus === 'completed_by_responsible' || formData.workflowStatus === 'final_completed')\">\n          <view class=\"approval-header\">\n            <text class=\"approval-title\">负责人执行</text>\n            <view class=\"approval-status\" :class=\"{\n              'status-pending': formData.workflowStatus === 'assigned_to_responsible',\n              'status-approved': formData.workflowStatus === 'completed_by_responsible' || formData.workflowStatus === 'final_completed'\n            }\">\n              <uni-icons v-if=\"formData.workflowStatus === 'assigned_to_responsible'\" type=\"gear\" size=\"16\" color=\"#3b82f6\"></uni-icons>\n              <uni-icons v-else type=\"checkmarkempty\" size=\"16\" color=\"#4CAF50\"></uni-icons>\n              <text v-if=\"formData.workflowStatus === 'assigned_to_responsible'\">等待负责人完成工作</text>\n              <text v-else-if=\"formData.workflowStatus === 'completed_by_responsible'\">负责人已完成，待确认</text>\n              <text v-else-if=\"formData.workflowStatus === 'final_completed'\">负责人已完成</text>\n            </view>\n          </view>\n          <view class=\"responsible-info\">\n            <text class=\"responsible-text\">已指派给：{{ getResponsibleUserName() }}</text>\n            <text class=\"assign-time\" v-if=\"formData.assignedTime\">指派时间：{{ formatTime(formData.assignedTime) }}</text>\n            <text class=\"completion-time\" v-if=\"formData.completedByResponsibleTime\">完成时间：{{ formatTime(formData.completedByResponsibleTime) }}</text>\n            <!-- 显示退回理由 -->\n            <view class=\"reject-reason-display\" v-if=\"formData.rejectReason\">\n              <view class=\"reject-reason-header\">\n                <text class=\"reject-reason-label\">厂长退回理由：</text>\n                <text class=\"reject-time\" v-if=\"formData.rejectedTime\">{{ formatTime(formData.rejectedTime) }}</text>\n              </view>\n              <view class=\"reject-reason-content\">\n                <text class=\"reject-reason-text\">{{ formData.rejectReason }}</text>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 已完成的工作显示 -->\n          <view class=\"completed-work\" v-if=\"formData.workflowStatus === 'completed_by_responsible' || formData.workflowStatus === 'final_completed'\">\n            <view class=\"completion-description-item\" v-if=\"formData.responsibleCompletionDescription\">\n              <view class=\"completion-description-header\">\n                <text class=\"description-label\">完成说明：</text>\n              </view>\n              <view class=\"completion-description-content\">\n                <text class=\"description-text\">{{ formData.responsibleCompletionDescription }}</text>\n              </view>\n            </view>\n            <!-- 完成凭证图片 -->\n            <view class=\"completion-evidence\" v-if=\"formData.responsibleCompletionEvidence && formData.responsibleCompletionEvidence.length > 0\">\n              <view class=\"evidence-images\">\n                <image v-for=\"(image, index) in formData.responsibleCompletionEvidence\" \n                       :key=\"index\" \n                       :src=\"image\" \n                       mode=\"aspectFill\" \n                       class=\"evidence-image\"\n                       @click=\"previewImage(image)\"></image>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 负责人操作区域 - 仅在待完成状态显示 -->\n          <view class=\"responsible-action\" v-if=\"formData.workflowStatus === 'assigned_to_responsible' && (isCurrentUserResponsible() || uniIDHasRole('admin'))\">\n            <view class=\"completion-form\">\n              <view class=\"reason-box\">\n                <text class=\"reason-label\">完成情况说明：</text>\n                <textarea class=\"reason-input\"\n                          v-model=\"completionDescription\"\n                          placeholder=\"请描述工作完成情况\" \n                          maxlength=\"500\"/>\n                <text class=\"character-count\">{{ completionDescription.length }}/500</text>\n              </view>\n              <view class=\"evidence-section\">\n                <text class=\"evidence-label\">完成凭证（可选）：</text>\n                <view class=\"evidence-upload\">\n                  <view class=\"image-list\">\n                    <!-- 已上传的图片 -->\n                    <view v-for=\"(image, index) in completionEvidence\" :key=\"index\" class=\"image-item\">\n                      <image :src=\"image\" mode=\"aspectFill\" @click=\"previewImage(image)\"></image>\n                      <view class=\"image-delete\" @click=\"removeImage(index)\">×</view>\n                    </view>\n                    <!-- 上传按钮 -->\n                    <view class=\"upload-item\" v-if=\"completionEvidence.length < 3\">\n                      <button class=\"upload-btn\" @click=\"uploadEvidence\">\n                        <uni-icons type=\"camera\" size=\"18\" class=\"upload-icon\"></uni-icons>\n                        <text class=\"upload-text\">上传图片</text>\n                      </button>\n                    </view>\n                  </view>\n                </view>\n              </view>\n              <button class=\"complete-btn\" type=\"primary\" @click=\"handleResponsibleComplete\" :disabled=\"!completionDescription\">提交完成</button>\n            </view>\n          </view>\n        </view>\n\n        <!-- 厂长最终确认区域 -->\n        <view class=\"approval-section\"\n              :class=\"{ \n                'active': formData.workflowStatus === 'completed_by_responsible',\n                'disabled': formData.workflowStatus !== 'completed_by_responsible'\n              }\" \n              v-if=\"(uniIDHasRole('GM') || uniIDHasRole('admin')) && formData.workflowStatus === 'completed_by_responsible'\">\n          <view class=\"approval-header\">\n            <text class=\"approval-title\">厂长最终确认</text>\n            <view class=\"approval-status status-pending\">\n              <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#3b82f6\"></uni-icons>\n              <text>待最终确认</text>\n            </view>\n          </view>\n          <view class=\"completion-review\">\n            <view class=\"completion-info\">\n              <text class=\"responsible-text\">负责人：{{ getResponsibleUserName() }}</text>\n              <text class=\"completion-time\" v-if=\"formData.completedByResponsibleTime\">完成时间：{{ formatTime(formData.completedByResponsibleTime) }}</text>\n            </view>\n            <view class=\"reason-box\">\n              <text class=\"reason-label\">确认意见：</text>\n              <textarea class=\"reason-input\"\n                        v-model=\"finalConfirmReason\"\n                        placeholder=\"请输入确认意见\" \n                        maxlength=\"200\"/>\n              <text class=\"character-count\">{{ finalConfirmReason.length }}/200</text>\n            </view>\n            <view class=\"reason-box\">\n              <text class=\"reason-label\">退回理由（如需退回）：</text>\n              <textarea class=\"reason-input\"\n                        v-model=\"rejectReason\"\n                        placeholder=\"请输入退回理由，说明需要重做的具体问题\" \n                        maxlength=\"200\"/>\n              <text class=\"character-count\">{{ rejectReason.length }}/200</text>\n            </view>\n            <view class=\"final-actions\">\n              <button class=\"reject-btn\" type=\"default\" @click=\"handleRejectCompletion\" :disabled=\"!rejectReason\">退回重做</button>\n              <button class=\"confirm-btn\" type=\"primary\" @click=\"handleFinalConfirm\" :disabled=\"!finalConfirmReason\">确认完成</button>\n            </view>\n          </view>\n        </view>\n\n        <!-- 最终完成状态显示 -->\n        <view class=\"approval-section\"\n              v-if=\"formData.workflowStatus === 'final_completed'\">\n          <view class=\"approval-header\">\n            <text class=\"approval-title\">流程完成</text>\n            <view class=\"approval-status status-approved\">\n              <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#4CAF50\"></uni-icons>\n              <text>已完成</text>\n            </view>\n          </view>\n          <view class=\"approval-content\">\n            <view class=\"completion-summary\">\n              <view class=\"summary-container\">\n                <view class=\"summary-item-container\">\n                  <view class=\"summary-header\">\n                    <text class=\"summary-label\">完成说明：</text>\n                  </view>\n                  <view class=\"summary-content\">\n                    <text class=\"summary-text\">该问题已完成整改并通过最终确认</text>\n                  </view>\n                </view>\n                \n                <view class=\"summary-item-container\" v-if=\"formData.finalCompletedTime\">\n                  <view class=\"summary-header\">\n                    <text class=\"summary-label\">完成时间：</text>\n                  </view>\n                  <view class=\"summary-content\">\n                    <text class=\"summary-text\">{{ formatTime(formData.finalCompletedTime) }}</text>\n                  </view>\n                </view>\n                \n                <view class=\"summary-item-container\">\n                  <view class=\"summary-header\">\n                    <text class=\"summary-label\">负责人：</text>\n                  </view>\n                  <view class=\"summary-content\">\n                    <text class=\"summary-text\">{{ getResponsibleUserName() }}</text>\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 审核理由汇总区域 -->\n        <view class=\"approval-summary-section\">\n          <view class=\"approval-summary-header\">\n            <text class=\"approval-summary-title\">审核理由汇总</text>\n          </view>\n          <view class=\"approval-summary-content\">\n            <!-- 主管审核理由 -->\n            <view class=\"summary-item\">\n              <view class=\"summary-item-header\">\n                <text class=\"summary-role\">主管：</text>\n                <text class=\"summary-status\" :class=\"{\n                  'status-approved': getSupervisorStatus() === 'approved',\n                  'status-rejected': getSupervisorStatus() === 'rejected',\n                  'status-pending': getSupervisorStatus() === 'pending',\n                  'status-meeting': getSupervisorStatus() === 'meeting'\n                }\">{{ getSupervisorStatusText() }}</text>\n              </view>\n              <view class=\"summary-reason\">\n                <text class=\"reason-content\" :class=\"{\n                  'reason-approved': getSupervisorStatus() === 'approved',\n                  'reason-rejected': getSupervisorStatus() === 'rejected',\n                  'reason-meeting': getSupervisorStatus() === 'meeting',\n                  'reason-not-executed': getSupervisorStatus() === 'not_executed'\n                }\">{{ getSupervisorReasonText() }}</text>\n              </view>\n            </view>\n            \n            <!-- 副厂长审核理由 -->\n            <view class=\"summary-item\">\n              <view class=\"summary-item-header\">\n                <text class=\"summary-role\">副厂长：</text>\n                <text class=\"summary-status\" :class=\"{\n                  'status-approved': getPMStatus() === 'approved',\n                  'status-rejected': getPMStatus() === 'rejected',\n                  'status-pending': getPMStatus() === 'pending',\n                  'status-not-executed': getPMStatus() === 'not_executed'\n                }\">{{ getPMStatusText() }}</text>\n              </view>\n              <view class=\"summary-reason\">\n                <text class=\"reason-content\" :class=\"{\n                  'reason-approved': getPMStatus() === 'approved',\n                  'reason-rejected': getPMStatus() === 'rejected',\n                  'reason-not-executed': getPMStatus() === 'not_executed'\n                }\">{{ getPMReasonText() }}</text>\n              </view>\n            </view>\n            \n            <!-- 厂长审核理由 -->\n            <view class=\"summary-item\">\n              <view class=\"summary-item-header\">\n                <text class=\"summary-role\">厂长：</text>\n                <text class=\"summary-status\" :class=\"{\n                  'status-approved': getGMStatus() === 'approved',\n                  'status-rejected': getGMStatus() === 'rejected',\n                  'status-pending': getGMStatus() === 'pending',\n                  'status-not-executed': getGMStatus() === 'not_executed'\n                }\">{{ getGMStatusText() }}</text>\n              </view>\n              <view class=\"summary-reason\">\n                <text class=\"reason-content\" :class=\"{\n                  'reason-approved': getGMStatus() === 'approved',\n                  'reason-rejected': getGMStatus() === 'rejected',\n                  'reason-not-executed': getGMStatus() === 'not_executed'\n                }\">{{ getGMReasonText() }}</text>\n              </view>\n            </view>\n            \n            <!-- 负责人指派说明 -->\n            <view class=\"summary-item\" v-if=\"formData.responsibleUserId\">\n              <view class=\"summary-item-header\">\n                <text class=\"summary-role\">指派说明：</text>\n                <text class=\"summary-status status-info\">已指派</text>\n              </view>\n              <view class=\"summary-reason\">\n                <text class=\"reason-content reason-info\">{{ formData.assignReason || '无' }}</text>\n              </view>\n            </view>\n            \n            <!-- 退回重做记录 -->\n            <view class=\"summary-item\" v-if=\"formData.rejectReason\">\n              <view class=\"summary-item-header\">\n                <text class=\"summary-role\">厂长退回：</text>\n                <text class=\"summary-status status-rejected\">已退回</text>\n              </view>\n              <view class=\"summary-reason\">\n                <text class=\"reason-content reason-rejected\">{{ formData.rejectReason }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 重置按钮 -->\n        <view class=\"reset-section\" v-if=\"uniIDHasRole('GM') || uniIDHasRole('admin')\">\n          <button class=\"reset-btn\" type=\"warn\" @click=\"handleReset\">重置审核流程</button>\n        </view>\n      </template>\n    </view>\n  </view>\n</template>\n\n<script>\nconst db = uniCloud.database();\n\nexport default {\n  components: {\n    uniIcons: () => import('@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue')\n  },\n  data() {\n    return {\n      formDataId: '',\n      currentStep: 0,\n      _isLoading: false,\n      hasInitialData: false,\n      isPageVisible: true,\n      // 添加选中状态跟踪\n      selectedOptions: {\n        supervisor: null,\n        PM: null,\n        GM: null\n      },\n      // 临时理由输入（用于待审核状态的理由输入）\n      tempReasonInput: {\n        supervisor: '',\n        pm: '',\n        gm: ''\n      },\n      formData: {\n        // 基础字段\n        name: '',\n        project: '',\n        description: '',\n        images: [],\n        createTime: null,\n        createUserId: '',\n        isAdopted: false,\n        isCompleted: false,\n        // 工作流字段\n        workflowStatus: 'pending_supervisor',\n        meetingRequired: false,\n        terminatedBy: '',\n        terminatedTime: null,\n        lastUpdateTime: null,\n        remark: '',\n        updateTrigger: '',\n        // 负责人字段\n        responsibleUserId: null,\n        assignedTime: null,\n        assignReason: '',\n        responsibleCompletionDescription: null,\n        responsibleCompletionEvidence: [],\n        completedByResponsibleTime: null,\n        finalCompletedTime: null,\n        rejectReason: '',\n        rejectedTime: null,\n        // 操作历史\n        actionHistory: []\n      },\n      // 新增工作流相关数据\n      responsibleUsers: [],\n      selectedResponsibleIndex: 0,\n      selectedResponsibleUser: null,\n      assignReason: '',\n      completionDescription: '',\n      completionEvidence: [],\n      finalConfirmReason: '',\n      rejectReason: '',\n      steps: [\n        { text: '主管审核' },\n        { text: '副厂长审核' },\n        { text: '厂长审核' },\n        { text: '指派负责人' },\n        { text: '执行结果' }\n      ]\n    }\n  },\n\n  computed: {\n    // 统一显示5步工作流\n    effectiveSteps() {\n      return this.steps;\n    },\n    \n    isProcessTerminated() {\n      return this.formData.workflowStatus === 'terminated';\n    },\n    isProcessComplete() {\n      return this.formData.workflowStatus === 'final_completed';\n    },\n\n    // 加载状态\n    isLoading() {\n      return this._isLoading;\n    }\n  },\n\n  async onLoad(option) {\n    if (option && option.id) {\n      this.formDataId = option.id;\n      // 先加载负责人列表，然后再加载详情，确保选择器正常显示\n      await this.loadResponsibleUsers();\n      this.getDetail(option.id);\n    }\n  },\n\n  methods: {\n    getRoleByStep(step) {\n      const roles = ['supervisor', 'PM', 'GM', 'assign', 'final'];\n      return roles[step];\n    },\n\n    getRoleTitle(role) {\n      const titles = {\n        supervisor: '主管',\n        PM: '副厂长',\n        GM: '厂长',\n        assign: '指派负责人',\n        final: '执行结果'\n      };\n      return titles[role];\n    },\n\n    // 新的状态获取方法 - 基于workflowStatus\n    getSupervisorStatus() {\n      const status = this.formData.workflowStatus;\n      const terminatedBy = this.formData.terminatedBy;\n      \n      if (status === 'pending_supervisor') return 'pending';\n      if (status === 'approved_supervisor' || status === 'pending_pm' || \n          status === 'approved_pm' || status === 'pending_gm' || \n          status === 'gm_approved_pending_assign' || \n          status === 'assigned_to_responsible' || status === 'completed_by_responsible' || status === 'final_completed') {\n        // 检查是否是例会讨论\n        return this.formData.meetingRequired ? 'meeting' : 'approved';\n      }\n      // 流程终止的情况\n      if (status === 'terminated') {\n        if (terminatedBy === 'supervisor') {\n          return 'rejected';\n        } else if (terminatedBy === 'PM' || terminatedBy === 'GM') {\n          // 后续步骤拒绝，主管应该显示已同意\n          return this.formData.meetingRequired ? 'meeting' : 'approved';\n        }\n      }\n      return 'pending';\n    },\n\n    getPMStatus() {\n      const status = this.formData.workflowStatus;\n      const terminatedBy = this.formData.terminatedBy;\n      \n      // 如果主管都没通过，副厂长显示未执行\n      if (status === 'pending_supervisor') return 'not_executed';\n      if (status === 'terminated' && terminatedBy === 'supervisor') return 'not_executed';\n      \n      if (status === 'approved_supervisor' || status === 'pending_pm') return 'pending';\n      if (status === 'approved_pm' || status === 'pending_gm' || status === 'gm_approved_pending_assign' || \n          status === 'assigned_to_responsible' || status === 'completed_by_responsible' || status === 'final_completed') {\n        return 'approved';\n      }\n      // 只有副厂长拒绝时才显示rejected\n      if (status === 'terminated' && terminatedBy === 'PM') {\n        return 'rejected';\n      }\n      // 如果厂长拒绝，但副厂长已经通过了，应该显示已同意\n      if (status === 'terminated' && terminatedBy === 'GM') {\n        return 'approved';\n      }\n      return 'not_executed';\n    },\n\n    getGMStatus() {\n      const status = this.formData.workflowStatus;\n      const terminatedBy = this.formData.terminatedBy;\n      \n      // 如果前面步骤都没通过，厂长显示未执行\n      if (status === 'pending_supervisor' || status === 'approved_supervisor' || status === 'pending_pm') return 'not_executed';\n      if (status === 'terminated' && (terminatedBy === 'supervisor' || terminatedBy === 'PM')) {\n        return 'not_executed';\n      }\n      \n      if (status === 'approved_pm' || status === 'pending_gm') return 'pending';\n      if (status === 'gm_approved_pending_assign' || status === 'assigned_to_responsible' || \n          status === 'completed_by_responsible' || status === 'final_completed') {\n        return 'approved';\n      }\n      // 只有厂长拒绝时才显示rejected\n      if (status === 'terminated' && terminatedBy === 'GM') {\n        return 'rejected';\n      }\n      return 'not_executed';\n    },\n\n    // 获取步骤状态\n    getStepStatus(stepIndex) {\n      const roles = ['supervisor', 'PM', 'GM', 'assign', 'final'];\n      const role = roles[stepIndex];\n      \n      if (role === 'assign') {\n        if (this.formData.workflowStatus === 'gm_approved_pending_assign') return 'pending';\n        if (this.formData.workflowStatus === 'assigned_to_responsible' || \n            this.formData.workflowStatus === 'completed_by_responsible' || \n            this.formData.workflowStatus === 'final_completed') return 'completed';\n        if (this.formData.workflowStatus === 'terminated') return 'not_executed';\n        return 'not_executed';\n      } else if (role === 'final') {\n        if (this.formData.workflowStatus === 'final_completed') return 'completed';\n        if (this.formData.workflowStatus === 'completed_by_responsible') return 'pending';\n        if (this.formData.workflowStatus === 'terminated') return 'rejected';\n        return 'not_executed';\n      } else {\n        const status = this[`get${role.charAt(0).toUpperCase() + role.slice(1)}Status`]();\n        if (status === 'approved') return 'completed';\n        if (status === 'meeting') return 'meeting';\n        if (status === 'rejected') return 'rejected';\n        if (status === 'not_executed') return 'not_executed';\n        return 'pending';\n      }\n    },\n\n    // 获取状态文本\n    getSupervisorStatusText() {\n      const status = this.getSupervisorStatus();\n      if (status === 'pending') return '待审核';\n      if (status === 'approved') return '已同意';\n      if (status === 'rejected') return '已拒绝';\n      if (status === 'meeting') return '例会讨论';\n      if (status === 'not_executed') return '未执行';\n      return '待审核';\n    },\n\n    getPMStatusText() {\n      const status = this.getPMStatus();\n      if (status === 'pending') return '待审核';\n      if (status === 'approved') return '已同意';\n      if (status === 'rejected') return '已拒绝';\n      if (status === 'not_executed') return '未执行';\n      return '待审核';\n    },\n\n    getGMStatusText() {\n      const status = this.getGMStatus();\n      if (status === 'pending') return '待审核';\n      if (status === 'approved') return '已同意';\n      if (status === 'rejected') return '已拒绝';\n      if (status === 'not_executed') return '未执行';\n      return '待审核';\n    },\n\n    // 获取步骤状态文本\n    getStepStatusText(stepIndex) {\n      const roles = ['supervisor', 'PM', 'GM', 'assign', 'final'];\n      const role = roles[stepIndex];\n      \n      if (role === 'assign') {\n        if (this.formData.workflowStatus === 'gm_approved_pending_assign') return '待指派负责人';\n        if (this.formData.workflowStatus === 'assigned_to_responsible') return '已指派，待完成';\n        if (this.formData.workflowStatus === 'completed_by_responsible') return '负责人已完成';\n        if (this.formData.workflowStatus === 'final_completed') return '已指派完成';\n        if (this.formData.workflowStatus === 'terminated') return '未执行';\n        return '未执行';\n      } else if (role === 'final') {\n        if (this.formData.workflowStatus === 'final_completed') return '流程完成';\n        if (this.formData.workflowStatus === 'terminated') return '流程终止';\n        if (this.formData.workflowStatus === 'assigned_to_responsible') return '负责人执行中';\n        if (this.formData.workflowStatus === 'completed_by_responsible') return '待厂长确认';\n        if (this.formData.workflowStatus === 'gm_approved_pending_assign') return '待指派负责人';\n        return '未执行';\n      } else {\n        return this[`get${role.charAt(0).toUpperCase() + role.slice(1)}StatusText`]();\n      }\n    },\n\n    // 获取步骤时间 - 重置后直接从actionHistory获取\n    getStepTime(stepIndex) {\n      const roles = ['supervisor', 'PM', 'GM', 'assign', 'final'];\n      const role = roles[stepIndex];\n      \n      if (role === 'assign') {\n        return this.formData.assignedTime;\n      } else if (role === 'final') {\n        return this.formData.finalCompletedTime || this.formData.completedByResponsibleTime || this.formData.terminatedTime;\n      } else {\n        // 从actionHistory中获取审核时间\n        if (this.formData.actionHistory && this.formData.actionHistory.length > 0) {\n          const actionMap = {\n            'supervisor': ['supervisor_approve', 'supervisor_reject', 'supervisor_meeting'],\n            'PM': ['pm_approve', 'pm_reject'],\n            'GM': ['gm_approve', 'gm_reject']\n          };\n          \n          const roleActions = actionMap[role] || [];\n          const lastAction = this.formData.actionHistory\n            .filter(action => roleActions.includes(action.action))\n            .sort((a, b) => b.timestamp - a.timestamp)[0];\n          \n          if (lastAction) {\n            return lastAction.timestamp;\n          }\n        }\n      }\n      return null;\n    },\n\n    formatTime(timestamp) {\n      if (!timestamp) return '';\n      const date = new Date(timestamp);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n    },\n\n    // 获取主管理由文本 - 从actionHistory获取\n    getSupervisorReasonText() {\n      if (this.formData.actionHistory && this.formData.actionHistory.length > 0) {\n        const supervisorAction = this.formData.actionHistory\n          .filter(action => ['supervisor_approve', 'supervisor_reject', 'supervisor_meeting'].includes(action.action))\n          .sort((a, b) => b.timestamp - a.timestamp)[0];\n        \n        if (supervisorAction && supervisorAction.reason) {\n          return supervisorAction.reason;\n      }\n      }\n      \n      return '无';\n    },\n\n    // 获取副厂长理由文本 - 从actionHistory获取\n    getPMReasonText() {\n      if (this.formData.actionHistory && this.formData.actionHistory.length > 0) {\n        const pmAction = this.formData.actionHistory\n          .filter(action => ['pm_approve', 'pm_reject'].includes(action.action))\n          .sort((a, b) => b.timestamp - a.timestamp)[0];\n        \n        if (pmAction && pmAction.reason) {\n          return pmAction.reason;\n      }\n      }\n      \n      return '无';\n    },\n\n    // 获取厂长理由文本 - 从actionHistory获取\n    getGMReasonText() {\n      if (this.formData.actionHistory && this.formData.actionHistory.length > 0) {\n        const gmAction = this.formData.actionHistory\n          .filter(action => ['gm_approve', 'gm_reject'].includes(action.action))\n          .sort((a, b) => b.timestamp - a.timestamp)[0];\n        \n        if (gmAction && gmAction.reason) {\n          return gmAction.reason;\n      }\n      }\n      \n      return '无';\n    },\n\n    async getDetail(id) {\n      try {\n        // 防止重复加载\n        if (this._isLoading) return;\n        this._isLoading = true;\n        \n        uni.showLoading({ \n          title: '加载中...',\n          mask: true \n        });\n        \n        // 使用本地缓存加速页面显示\n        const cacheKey = `approval_${id}`;\n        try {\n          const cachedData = uni.getStorageSync(cacheKey);\n          if (cachedData && Date.now() - cachedData.timestamp < 60000) { // 缓存1分钟\n            // 使用缓存数据快速显示\n            Object.assign(this.formData, cachedData.data);\n            // 确保description字段被正确恢复\n            if (cachedData.data && cachedData.data.description) {\n              this.formData.description = cachedData.data.description;\n            }\n            this.updateSelectedOptions();\n            this.updateCurrentStep();\n            this.hasInitialData = true; // 标记已有初始数据\n          }\n        } catch (e) {}\n        \n        const res = await uniCloud.callFunction({\n          name: 'feedback-workflow',\n          data: {\n            action: 'get_detail',\n            id: id\n          }\n        });\n        \n        if (res.result.code === 0 && res.result.data) {\n          const data = res.result.data;\n          \n          // 使用你的测试数据标准结构\n          this.formData = {\n            // 基础字段\n            name: data.name,\n            project: data.project,\n            description: data.description || '',\n            images: data.images || [],\n            createTime: data.createTime,\n            createUserId: data.createUserId,\n            isAdopted: data.isAdopted,\n            isCompleted: data.isCompleted,\n            // 工作流字段\n            workflowStatus: data.workflowStatus || 'pending_supervisor',\n            meetingRequired: data.meetingRequired || false,\n            terminatedBy: data.terminatedBy || '',\n            terminatedTime: data.terminatedTime,\n            lastUpdateTime: data.lastUpdateTime,\n            remark: data.remark || '',\n            updateTrigger: data.updateTrigger || '',\n            // 负责人字段\n            responsibleUserId: data.responsibleUserId,\n            assignedTime: data.assignedTime,\n            assignReason: data.assignReason || '',\n            responsibleCompletionDescription: data.responsibleCompletionDescription,\n            responsibleCompletionEvidence: data.responsibleCompletionEvidence || [],\n            completedByResponsibleTime: data.completedByResponsibleTime,\n            finalCompletedTime: data.finalCompletedTime,\n            rejectReason: data.rejectReason || '',\n            rejectedTime: data.rejectedTime,\n            // 操作历史\n            actionHistory: data.actionHistory || []\n          };\n          \n          this.hasInitialData = true; // 标记已有数据\n          \n          // 设置选中状态，包括例会讨论\n          this.updateSelectedOptions();\n          this.updateCurrentStep();\n          \n          // 保存到本地缓存（异步执行，不阻塞UI）\n          this.$nextTick(() => {\n            try {\n              const cacheKey = `approval_${this.formDataId}`;\n              uni.setStorageSync(cacheKey, {\n                timestamp: Date.now(),\n                data: this.formData\n              });\n            } catch (e) {}\n          });\n        } else {\n          // 检查是否是记录已删除的错误\n          if (res.result?.code === 404 && res.result?.errorType === 'RECORD_DELETED') {\n            this.handleRecordDeleted();\n            return;\n          }\n          throw new Error(res.result?.message || '获取详情失败');\n        }\n      } catch (err) {\n        // 检查是否是网络错误导致的记录不存在\n        if (err.message && err.message.includes('记录不存在')) {\n          this.handleRecordDeleted();\n          return;\n        }\n        uni.showModal({\n          content: err.message || '请求服务失败',\n          showCancel: false\n        });\n      } finally {\n        uni.hideLoading();\n        this._isLoading = false;\n      }\n    },\n\n    updateCurrentStep() {\n      const status = this.formData.workflowStatus;\n      const terminatedBy = this.formData.terminatedBy;\n      \n      // 基于新工作流状态确定当前步骤\n      if (status === 'pending_supervisor') {\n        this.currentStep = 0; // 主管审核\n      } else if (status === 'approved_supervisor' || status === 'pending_pm') {\n        this.currentStep = 1; // 副厂长审核\n      } else if (status === 'approved_pm' || status === 'pending_gm') {\n        this.currentStep = 2; // 厂长审核\n      } else if (status === 'gm_approved_pending_assign') {\n        this.currentStep = 3; // 指派负责人\n      } else if (status === 'assigned_to_responsible' || \n                 status === 'completed_by_responsible') {\n        this.currentStep = 4; // 工作流程结果\n      } else if (status === 'final_completed') {\n        this.currentStep = -1; // 流程完成，没有激活步骤\n      } else if (status === 'terminated') {\n        // 流程终止时，没有激活步骤\n        this.currentStep = -1;\n      } else {\n        // 默认为第一步\n        this.currentStep = 0;\n      }\n    },\n\n    updateSelectedOptions() {\n      // 根据新工作流状态设置选中状态\n      const supervisorStatus = this.getSupervisorStatus();\n      if (supervisorStatus === 'approved') {\n        this.$set(this.selectedOptions, 'supervisor', true);\n      } else if (supervisorStatus === 'rejected') {\n        this.$set(this.selectedOptions, 'supervisor', false);\n      } else if (supervisorStatus === 'meeting') {\n        this.$set(this.selectedOptions, 'supervisor', 'meeting');\n      } else {\n        this.$set(this.selectedOptions, 'supervisor', null);\n      }\n\n      const pmStatus = this.getPMStatus();\n      if (pmStatus === 'approved') {\n        this.$set(this.selectedOptions, 'PM', true);\n      } else if (pmStatus === 'rejected') {\n        this.$set(this.selectedOptions, 'PM', false);\n      } else {\n        this.$set(this.selectedOptions, 'PM', null);\n      }\n\n      const gmStatus = this.getGMStatus();\n      if (gmStatus === 'approved') {\n        this.$set(this.selectedOptions, 'GM', true);\n      } else if (gmStatus === 'rejected') {\n        this.$set(this.selectedOptions, 'GM', false);\n      } else {\n        this.$set(this.selectedOptions, 'GM', null);\n      }\n    },\n\n    \t\t/**\n\t\t * 处理审核操作 - 新工作流系统的核心审核逻辑\n\t\t * \n\t\t * 支持的审核决策：\n\t\t * - true: 同意/通过\n\t\t * - false: 不同意/拒绝  \n\t\t * - 'meeting': 提交例会讨论（仅主管可用）\n\t\t * \n\t\t * 工作流状态转换：\n\t\t * - 主管审核：pending_supervisor → pending_pm/terminated/meeting_required\n\t\t * - 副厂长审核：pending_pm → pending_gm/terminated\n\t\t * - 厂长审核：pending_gm → gm_approved_pending_assign/terminated\n\t\t * \n\t\t * @param {string} role - 审核角色 ('supervisor'/'PM'/'GM')\n\t\t * @param {boolean|string} decision - 审核决策 (true/false/'meeting')\n\t\t */\n\t\tasync handleApproval(role, decision) {\n\t\t\t// 防止重复提交，确保审核操作的原子性\n\t\t\tif (this._isProcessing) return;\n\t\t\tthis._isProcessing = true;\n      \n      // 先设置选中状态\n      this.$set(this.selectedOptions, role, decision);\n      \n      try {\n        // 理由验证已移除 - 现在所有操作都有默认理由，不需要强制验证\n        const roles = ['supervisor', 'PM', 'GM'];\n        const currentRoleIndex = roles.indexOf(role);\n        \n        if (currentRoleIndex !== this.currentStep) {\n          uni.showModal({\n            title: '提示',\n            content: `当前不是${role}审核步骤，请按流程顺序进行审核`,\n            showCancel: false,\n            confirmText: '确定'\n          });\n          \n          this._isProcessing = false;\n          // 重置选中状态\n          this.$set(this.selectedOptions, role, null);\n          return;\n        }\n\n        // 根据决策类型设置动作描述\n        let action;\n        if (decision === true) {\n          action = '同意';\n        } else if (decision === false) {\n          action = '不同意';\n        } else if (decision === 'meeting') {\n          action = '提交例会讨论';\n        }\n        \n        // 直接显示确认对话框\n        const confirmRes = await uni.showModal({\n          title: '确认操作',\n          content: `确定要${action}吗？`,\n          confirmText: '确定',\n          cancelText: '取消'\n        });\n\n        if (!confirmRes.confirm) {\n          // 用户点击取消，重置选中状态\n          this._isProcessing = false;\n          // 重置radio选中状态\n          this.$set(this.selectedOptions, role, null);\n          return;\n        }\n\n        uni.showLoading({ \n          title: '处理中...',\n          mask: true\n        });\n\n        let res;\n        \n        if (decision === 'meeting') {\n          // 例会讨论 - 使用临时输入或默认理由\n          let finalReason = this.tempReasonInput.supervisor;\n          \n          // 如果没有输入理由，使用默认理由\n          if (!finalReason || finalReason.trim() === '') {\n            finalReason = '需要例会讨论';\n          }\n          \n          res = await uniCloud.callFunction({\n            name: 'feedback-workflow',\n            data: {\n              action: 'supervisor_meeting',\n              id: this.formDataId,\n              reason: finalReason\n            }\n          });\n        } else {\n          let workflowAction;\n          if (role === 'supervisor') {\n            workflowAction = decision ? 'supervisor_approve' : 'supervisor_reject';\n          } else if (role === 'PM') {\n            workflowAction = decision ? 'pm_approve' : 'pm_reject';\n          } else if (role === 'GM') {\n            if (decision === false) {\n              // 厂长拒绝\n              workflowAction = 'gm_reject';\n            } else {\n              // 厂长同意\n              workflowAction = 'gm_approve';\n            }\n          }\n          \n          // 使用临时理由输入或默认理由\n          const tempFieldMap = {\n            'supervisor': 'supervisor',\n            'PM': 'pm',\n            'GM': 'gm'\n          };\n          \n          let finalReason = this.tempReasonInput[tempFieldMap[role]] || '';\n          \n          // 如果没有输入理由，使用默认理由\n          if (!finalReason || finalReason.trim() === '') {\n            if (decision === true) {\n              finalReason = '同意';\n            } else {\n              finalReason = '不同意';\n            }\n          }\n          \n          res = await uniCloud.callFunction({\n            name: 'feedback-workflow',\n            data: {\n              action: workflowAction,\n              id: this.formDataId,\n              reason: finalReason\n            }\n          });\n        }\n\n        if (res.result && res.result.code === 0) {\n          // 更新本地状态 - 基于云函数返回的新状态\n          if (res.result.data && res.result.data.newStatus) {\n            this.formData.workflowStatus = res.result.data.newStatus;\n            // 如果云函数返回了terminatedBy字段，直接使用\n            if (res.result.data.terminatedBy) {\n              this.$set(this.formData, 'terminatedBy', res.result.data.terminatedBy);\n            }\n          }\n          \n          // 对于例会讨论，设置标记\n          if (decision === 'meeting') {\n            this.$set(this.formData, 'meetingRequired', true);\n          }\n          \n          // 批量更新云函数返回的所有字段（确保数据一致性）\n          if (res.result.data) {\n            const returnedData = res.result.data;\n            \n            // 核心工作流字段（必须更新）\n            if (returnedData.actionHistory) {\n              this.formData.actionHistory = returnedData.actionHistory;\n            }\n            if (returnedData.lastUpdateTime) {\n              this.formData.lastUpdateTime = returnedData.lastUpdateTime;\n            }\n            if (returnedData.remark !== undefined) {\n              this.formData.remark = returnedData.remark;\n            }\n            if (returnedData.isAdopted !== undefined) {\n              this.formData.isAdopted = returnedData.isAdopted;\n            }\n            if (returnedData.isCompleted !== undefined) {\n              this.formData.isCompleted = returnedData.isCompleted;\n            }\n            if (returnedData.meetingRequired !== undefined) {\n              this.formData.meetingRequired = returnedData.meetingRequired;\n            }\n            \n            // 指派相关字段\n            if (returnedData.responsibleUserId !== undefined) {\n              this.formData.responsibleUserId = returnedData.responsibleUserId;\n            }\n            if (returnedData.assignedTime !== undefined) {\n              this.formData.assignedTime = returnedData.assignedTime;\n            }\n            if (returnedData.assignReason !== undefined) {\n              this.formData.assignReason = returnedData.assignReason;\n            }\n            \n            // 完成相关字段\n            if (returnedData.completedByResponsibleTime !== undefined) {\n              this.formData.completedByResponsibleTime = returnedData.completedByResponsibleTime;\n            }\n            if (returnedData.responsibleCompletionDescription !== undefined) {\n              this.formData.responsibleCompletionDescription = returnedData.responsibleCompletionDescription;\n            }\n            if (returnedData.responsibleCompletionEvidence !== undefined) {\n              this.formData.responsibleCompletionEvidence = returnedData.responsibleCompletionEvidence;\n            }\n            if (returnedData.finalCompletedTime !== undefined) {\n              this.formData.finalCompletedTime = returnedData.finalCompletedTime;\n            }\n            \n            // 终止相关字段\n            if (returnedData.terminatedBy !== undefined) {\n              this.formData.terminatedBy = returnedData.terminatedBy;\n            }\n            if (returnedData.terminatedTime !== undefined) {\n              this.formData.terminatedTime = returnedData.terminatedTime;\n            }\n          }\n          \n          // 清空对应的临时理由输入\n          const tempFieldMap = {\n            'supervisor': 'supervisor',\n            'PM': 'pm',\n            'GM': 'gm'\n          };\n          if (tempFieldMap[role]) {\n            this.tempReasonInput[tempFieldMap[role]] = '';\n          }\n          \n          this.updateCurrentStep();\n          \n          // 更新选中状态 - 必须在updateCurrentStep之后\n          this.updateSelectedOptions();\n          \n          // 强制触发视图更新，确保理由和时间立即显示\n          this.$forceUpdate();\n          \n          // 额外确保理由显示立即更新（防止缓存问题）\n          this.$nextTick(() => {\n            this.$forceUpdate();\n          });\n          \n          // 更新本地缓存（异步执行）\n          this.$nextTick(() => {\n            try {\n              const cacheKey = `approval_${this.formDataId}`;\n              uni.setStorageSync(cacheKey, {\n                timestamp: Date.now(),\n                data: this.formData\n              });\n            } catch (e) {}\n          });\n\n          uni.hideLoading();\n          \n          // 立即更新角标 - 审核操作后需要实时更新\n          const todoBadgeManager = require('@/utils/todo-badge.js').default;\n          if (todoBadgeManager) {\n            // 使用强制刷新确保立即更新\n            await todoBadgeManager.forceRefresh();\n            \n            // 强制同步角标状态，确保显示正确\n            setTimeout(() => {\n              todoBadgeManager.forceSyncBadge();\n            }, 300);\n          }\n          \n          // 触发刷新事件，通知其他页面更新\n          uni.$emit('refresh-todo-list');\n          // 发送feedback-updated事件，通知列表页面刷新\n          uni.$emit('feedback-updated');\n          \n          // 添加用户中心页面刷新事件，确保用户中心页面能实时更新\n          uni.$emit('ucenter-need-refresh', { id: this.formDataId });\n          \n          // 延迟更新角标，确保在返回TabBar页面时正确显示\n          setTimeout(() => {\n            if (todoBadgeManager) {\n              todoBadgeManager.updateTodoCountImmediately();\n            }\n          }, 500);\n          \n          uni.showToast({\n            title: action + '成功',\n            icon: 'success',\n            duration: 1000  // 进一步减少到1秒\n          });\n        } else {\n          // 检查是否是记录已删除的错误\n          if (res.result?.code === 404 && res.result?.errorType === 'RECORD_DELETED') {\n            this.handleRecordDeleted();\n            return;\n          }\n          throw new Error(res.result?.message || '操作失败');\n        }\n      } catch (err) {\n        uni.hideLoading();\n        // 检查是否是网络错误导致的记录不存在\n        if (err.message && err.message.includes('记录不存在')) {\n          this.handleRecordDeleted();\n          return;\n        }\n        uni.showModal({\n          content: err.message || '请求服务失败',\n          showCancel: false\n        });\n      } finally {\n        this._isProcessing = false;\n      }\n    },\n\n    async handleReset() {\n      // 防止重复提交\n      if (this._isProcessing) return;\n      this._isProcessing = true;\n      \n      try {\n        const confirmRes = await uni.showModal({\n          title: '确认重置',\n          content: '确定要重置所有审批状态吗？这将清除所有审批记录！',\n          confirmText: '确定',\n          cancelText: '取消'\n        });\n\n        if (!confirmRes.confirm) {\n          this._isProcessing = false;\n          return;\n        }\n\n        uni.showLoading({ \n          title: '处理中...',\n          mask: true\n        });\n\n        const res = await uniCloud.callFunction({\n          name: 'feedback-workflow',\n          data: {\n            action: 'reset',\n            id: this.formDataId\n          }\n        });\n\n        if (res.result && res.result.code === 0) {\n          // 批量更新云函数返回的所有字段（确保数据一致性）\n          if (res.result.data) {\n            const returnedData = res.result.data;\n            \n            // 保存基本信息\n          const preservedFields = {\n            name: this.formData.name,\n            project: this.formData.project,\n            description: this.formData.description,\n            images: this.formData.images || [],\n            createTime: this.formData.createTime,\n            createUserId: this.formData.createUserId\n          };\n          \n            // 重置为新工作流初始状态，使用云函数返回的数据\n          this.formData = {\n            ...preservedFields,\n              // 使用云函数返回的核心字段\n              workflowStatus: returnedData.workflowStatus || 'pending_supervisor',\n              isAdopted: returnedData.isAdopted || false,\n              isCompleted: returnedData.isCompleted || false,\n              meetingRequired: returnedData.meetingRequired || false,\n              lastUpdateTime: returnedData.lastUpdateTime || Date.now(),\n              remark: returnedData.remark || '',\n              actionHistory: returnedData.actionHistory || [],\n              // 重置的字段设为初始值\n              terminatedBy: '',\n              terminatedTime: null,\n            responsibleUserId: null,\n            assignedTime: null,\n              assignReason: '',\n            responsibleCompletionDescription: null,\n              responsibleCompletionEvidence: [],\n            completedByResponsibleTime: null,\n              finalCompletedTime: null,\n              rejectReason: '',\n              rejectedTime: null,\n              updateTrigger: ''\n          };\n          }\n          \n          // 重置选中状态\n          this.selectedOptions = {\n            supervisor: null,\n            PM: null,\n            GM: null\n          };\n          \n          this.updateCurrentStep();\n          \n          // 强制触发视图更新，确保重置状态立即显示\n          this.$forceUpdate();\n          \n          // 额外确保显示立即更新（防止缓存问题）\n          this.$nextTick(() => {\n            this.$forceUpdate();\n          });\n          \n          // 更新缓存（异步执行）\n          this.$nextTick(() => {\n            try {\n              const cacheKey = `approval_${this.formDataId}`;\n              uni.setStorageSync(cacheKey, {\n                timestamp: Date.now(),\n                data: this.formData\n              });\n            } catch (e) {}\n          });\n\n          uni.hideLoading();\n          \n          // 立即更新角标 - 重置后需要实时更新\n          const todoBadgeManager = require('@/utils/todo-badge.js').default;\n          if (todoBadgeManager) {\n            await todoBadgeManager.forceRefresh();\n            \n            // 强制同步角标状态\n            setTimeout(() => {\n              todoBadgeManager.forceSyncBadge();\n            }, 300);\n          }\n          \n          // 触发刷新事件\n          uni.$emit('refresh-todo-list');\n          // 发送feedback-updated事件，通知列表页面刷新\n          uni.$emit('feedback-updated');\n          \n          uni.showToast({\n            title: '重置成功',\n            icon: 'success',\n            duration: 1000  // 进一步减少到1秒\n          });\n        } else {\n          // 检查是否是记录已删除的错误\n          if (res.result?.code === 404 && res.result?.errorType === 'RECORD_DELETED') {\n            this.handleRecordDeleted();\n            return;\n          }\n          throw new Error(res.result?.message || '重置失败');\n        }\n      } catch (err) {\n        uni.hideLoading();\n        // 检查是否是网络错误导致的记录不存在\n        if (err.message && err.message.includes('记录不存在')) {\n          this.handleRecordDeleted();\n          return;\n        }\n        uni.showModal({\n          content: err.message || '重置失败',\n          showCancel: false\n        });\n      } finally {\n        this._isProcessing = false;\n      }\n    },\n\n    getFinalStatusText() {\n      if (this.formData.workflowStatus === 'final_completed') {\n        return '审核通过';\n      } else if (this.formData.workflowStatus === 'terminated') {\n        return '审核未通过';\n      } else {\n        return '审核中';\n      }\n    },\n\n    async loadResponsibleUsers() {\n      try {\n        const res = await uniCloud.callFunction({\n          name: 'feedback-workflow',\n          data: {\n            action: 'get_responsible_users'\n          }\n        });\n        \n        if (res.result && res.result.code === 0) {\n          this.responsibleUsers = res.result.data || [];\n          // 确保初始状态正确设置\n          if (this.responsibleUsers.length > 0 && this.selectedResponsibleIndex === 0) {\n            this.selectedResponsibleUser = null;\n          }\n        }\n      } catch (error) {\n        // 加载负责人列表失败，静默处理\n        console.warn('加载负责人列表失败:', error);\n      }\n    },\n\n    getResponsiblePickerDisplayText() {\n      if (this.responsibleUsers.length === 0) {\n        return '加载负责人列表中...';\n      }\n      if (this.selectedResponsibleUser) {\n        return this.selectedResponsibleUser.nickname || this.selectedResponsibleUser.username || '未知用户';\n      }\n      return '请选择负责人';\n    },\n\n    onResponsiblePickerChange(e) {\n      this.selectedResponsibleIndex = e.target.value;\n      this.selectedResponsibleUser = this.responsibleUsers[this.selectedResponsibleIndex];\n    },\n\n    async handleAssignResponsible() {\n      if (!this.selectedResponsibleUser) {\n        uni.showToast({ title: '请选择负责人', icon: 'none' });\n        return;\n      }\n\n      try {\n        uni.showLoading({ title: '指派中...', mask: true });\n\n        // 在清空表单之前保存需要的数据\n        const selectedUserId = this.selectedResponsibleUser._id;\n        const assignReason = this.assignReason || '无';\n\n        const res = await uniCloud.callFunction({\n          name: 'feedback-workflow',\n          data: {\n            action: 'gm_assign',\n            id: this.formDataId,\n            responsibleUserId: selectedUserId,\n            reason: assignReason\n          }\n        });\n\n        if (res.result && res.result.code === 0) {\n          // 批量更新云函数返回的所有字段（确保数据一致性）\n          if (res.result.data) {\n            const returnedData = res.result.data;\n            \n            // 核心工作流字段（必须更新）\n            if (returnedData.actionHistory) {\n              this.formData.actionHistory = returnedData.actionHistory;\n            }\n            if (returnedData.lastUpdateTime) {\n              this.formData.lastUpdateTime = returnedData.lastUpdateTime;\n            }\n            if (returnedData.remark !== undefined) {\n              this.formData.remark = returnedData.remark;\n            }\n            if (returnedData.workflowStatus !== undefined) {\n              this.formData.workflowStatus = returnedData.workflowStatus;\n            }\n            \n            // 指派相关字段\n            if (returnedData.responsibleUserId !== undefined) {\n              this.formData.responsibleUserId = returnedData.responsibleUserId;\n            }\n            if (returnedData.assignedTime !== undefined) {\n              this.formData.assignedTime = returnedData.assignedTime;\n            }\n            if (returnedData.assignReason !== undefined) {\n              this.formData.assignReason = returnedData.assignReason;\n            }\n          }\n          \n          // 更新当前步骤，确保界面正确显示\n          this.updateCurrentStep();\n          \n          // 强制触发视图更新，确保指派信息立即显示\n          this.$forceUpdate();\n          \n          // 额外确保显示立即更新（防止缓存问题）\n          this.$nextTick(() => {\n            this.$forceUpdate();\n          });\n          \n          // 清空指派表单\n          this.assignReason = '';\n          this.selectedResponsibleUser = null;\n          this.selectedResponsibleIndex = 0;\n          \n          uni.hideLoading();\n          uni.showToast({ title: '指派成功', icon: 'success' });\n          \n          // 发送任务指派事件，通知负责人任务页面刷新\n          uni.$emit('task-assigned', {\n            taskId: this.formDataId,\n            responsibleUserId: selectedUserId,\n            assignedTime: Date.now()\n          });\n          \n          // 发送feedback-updated事件，通知列表页面刷新\n          uni.$emit('feedback-updated');\n          \n          // 添加用户中心页面刷新事件，确保用户中心页面能实时更新\n          uni.$emit('ucenter-need-refresh', { id: this.formDataId });\n          \n          // 刷新页面数据\n          this.getDetail(this.formDataId);\n        } else {\n          throw new Error(res.result?.message || '指派失败');\n        }\n      } catch (error) {\n        uni.hideLoading();\n        uni.showModal({\n          content: error.message || '指派失败',\n          showCancel: false\n        });\n      }\n    },\n\n    isCurrentUserResponsible() {\n      const currentUserId = uni.getStorageSync('uni-id-token-payload')?.uid;\n      return this.formData.responsibleUserId === currentUserId;\n    },\n\n    getResponsibleUserName() {\n      if (!this.formData.responsibleUserId) return '';\n      \n      // 如果有responsibleUsers列表，从中查找\n      const user = this.responsibleUsers.find(u => u._id === this.formData.responsibleUserId);\n      if (user) return user.nickname || user.username || '未知用户';\n      \n      // 否则返回默认值\n      return this.formData.responsibleUserName || '负责人';\n    },\n\n    async uploadEvidence() {\n      try {\n        const res = await uni.chooseImage({\n          count: 3 - this.completionEvidence.length,\n          sizeType: ['compressed'],\n          sourceType: ['camera', 'album']\n        });\n\n        for (let i = 0; i < res.tempFilePaths.length; i++) {\n          const tempFilePath = res.tempFilePaths[i];\n          \n          // 显示当前上传进度\n          uni.showLoading({\n            title: `上传中 (${i + 1}/${res.tempFilePaths.length})`,\n            mask: true\n          });\n          \n          // 获取当前日期，创建年月日格式的目录结构\n          const now = new Date();\n          const year = now.getFullYear();\n          const month = String(now.getMonth() + 1).padStart(2, '0');\n          const day = String(now.getDate()).padStart(2, '0');\n          const dateFolder = `${year}${month}${day}`;\n          \n          // 生成唯一文件名\n          const fileExt = tempFilePath.includes('.') ? tempFilePath.substring(tempFilePath.lastIndexOf('.')) : '.jpg';\n          const uniqueFileName = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}${fileExt}`;\n          \n          const cloudPath = `feedback-evidence/${dateFolder}/${uniqueFileName}`;\n          const uploadRes = await uniCloud.uploadFile({\n            filePath: tempFilePath,\n            cloudPath: cloudPath,\n            cloudPathAsRealPath: true\n          });\n          \n          if (uploadRes.fileID) {\n            this.completionEvidence.push(uploadRes.fileID);\n          }\n        }\n        \n        uni.showToast({ title: '上传成功', icon: 'success' });\n      } catch (error) {\n        uni.showToast({ title: '上传失败', icon: 'none' });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n\n    removeImage(index) {\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这张图片吗？',\n        confirmColor: '#f44336',\n        success: async (res) => {\n          if (res.confirm) {\n            const fileID = this.completionEvidence[index];\n            \n            // 显示删除中的提示\n            uni.showLoading({\n              title: '删除中...',\n              mask: true\n            });\n            \n            try {\n              // 如果是云存储文件，调用云函数删除\n              if (fileID && typeof fileID === 'string' && (fileID.startsWith('cloud://') || fileID.startsWith('https://'))) {\n                await uniCloud.callFunction({\n                  name: 'delete-file',\n                  data: {\n                    fileList: [fileID]\n                  }\n                });\n              }\n              \n              // 从数组中移除\n              this.completionEvidence.splice(index, 1);\n              \n              uni.showToast({\n                title: '删除成功',\n                icon: 'success',\n                duration: 1000\n              });\n            } catch (error) {\n              // 删除云存储图片失败\n              \n              // 即使云存储删除失败，也从本地移除图片引用\n              this.completionEvidence.splice(index, 1);\n              \n              uni.showToast({\n                title: '已从列表移除',\n                icon: 'success',\n                duration: 1000\n              });\n            } finally {\n              uni.hideLoading();\n            }\n          }\n        }\n      });\n    },\n\n    previewImage(src) {\n      uni.previewImage({\n        urls: [src],\n        current: src\n      });\n    },\n\n    async handleResponsibleComplete() {\n      if (!this.completionDescription) {\n        uni.showToast({ title: '请输入完成情况说明', icon: 'none' });\n        return;\n      }\n\n      try {\n        uni.showLoading({ title: '提交中...', mask: true });\n\n        const res = await uniCloud.callFunction({\n          name: 'feedback-workflow',\n          data: {\n            action: 'responsible_complete',\n            id: this.formDataId,\n            completionDescription: this.completionDescription,\n            completionEvidence: this.completionEvidence\n          }\n        });\n\n        if (res.result && res.result.code === 0) {\n          // 批量更新云函数返回的所有字段（确保数据一致性）\n          if (res.result.data) {\n            const returnedData = res.result.data;\n            \n            // 核心工作流字段（必须更新）\n            if (returnedData.actionHistory) {\n              this.formData.actionHistory = returnedData.actionHistory;\n            }\n            if (returnedData.lastUpdateTime) {\n              this.formData.lastUpdateTime = returnedData.lastUpdateTime;\n            }\n            if (returnedData.remark !== undefined) {\n              this.formData.remark = returnedData.remark;\n            }\n            if (returnedData.workflowStatus !== undefined) {\n              this.formData.workflowStatus = returnedData.workflowStatus;\n            }\n            \n            // 完成相关字段\n            if (returnedData.completedByResponsibleTime !== undefined) {\n              this.formData.completedByResponsibleTime = returnedData.completedByResponsibleTime;\n            }\n            if (returnedData.responsibleCompletionDescription !== undefined) {\n              this.formData.responsibleCompletionDescription = returnedData.responsibleCompletionDescription;\n            }\n            if (returnedData.responsibleCompletionEvidence !== undefined) {\n              this.formData.responsibleCompletionEvidence = returnedData.responsibleCompletionEvidence;\n            }\n          }\n          \n          // 更新当前步骤，确保界面正确显示\n          this.updateCurrentStep();\n          \n          // 强制触发视图更新，确保完成状态立即显示\n          this.$forceUpdate();\n          \n          // 额外确保显示立即更新（防止缓存问题）\n          this.$nextTick(() => {\n            this.$forceUpdate();\n          });\n          \n          uni.hideLoading();\n          uni.showToast({ title: '提交成功', icon: 'success' });\n          \n          // 发送feedback-updated事件，通知列表页面刷新\n          uni.$emit('feedback-updated');\n          \n          // 添加用户中心页面刷新事件，确保用户中心页面能实时更新\n          uni.$emit('ucenter-need-refresh', { id: this.formDataId });\n          \n          // 清空表单\n          this.completionDescription = '';\n          this.completionEvidence = [];\n          \n          // 刷新页面数据\n          this.getDetail(this.formDataId);\n        } else {\n          throw new Error(res.result?.message || '提交失败');\n        }\n      } catch (error) {\n        uni.hideLoading();\n        uni.showModal({\n          content: error.message || '提交失败',\n          showCancel: false\n        });\n      }\n    },\n\n    async handleFinalConfirm() {\n      if (!this.finalConfirmReason) {\n        uni.showToast({ title: '请输入确认意见', icon: 'none' });\n        return;\n      }\n\n      try {\n        uni.showLoading({ title: '确认中...', mask: true });\n\n        const res = await uniCloud.callFunction({\n          name: 'feedback-workflow',\n          data: {\n            action: 'gm_final_confirm',\n            id: this.formDataId,\n            reason: this.finalConfirmReason\n          }\n        });\n\n        if (res.result && res.result.code === 0) {\n          // 批量更新云函数返回的所有字段（确保数据一致性）\n          if (res.result.data) {\n            const returnedData = res.result.data;\n            \n            // 核心工作流字段（必须更新）\n            if (returnedData.actionHistory) {\n              this.formData.actionHistory = returnedData.actionHistory;\n            }\n            if (returnedData.lastUpdateTime) {\n              this.formData.lastUpdateTime = returnedData.lastUpdateTime;\n            }\n            if (returnedData.remark !== undefined) {\n              this.formData.remark = returnedData.remark;\n            }\n            if (returnedData.workflowStatus !== undefined) {\n              this.formData.workflowStatus = returnedData.workflowStatus;\n            }\n            if (returnedData.isCompleted !== undefined) {\n              this.formData.isCompleted = returnedData.isCompleted;\n            }\n            if (returnedData.finalCompletedTime !== undefined) {\n              this.formData.finalCompletedTime = returnedData.finalCompletedTime;\n            }\n          }\n          \n          // 更新当前步骤，确保界面正确显示\n          this.updateCurrentStep();\n          \n          // 强制触发视图更新，确保最终确认状态立即显示\n          this.$forceUpdate();\n          \n          // 额外确保显示立即更新（防止缓存问题）\n          this.$nextTick(() => {\n            this.$forceUpdate();\n          });\n          \n          uni.hideLoading();\n          uni.showToast({ title: '确认完成', icon: 'success' });\n          \n          // 发送feedback-updated事件，通知列表页面刷新\n          uni.$emit('feedback-updated');\n          \n          // 添加用户中心页面刷新事件，确保用户中心页面能实时更新\n          uni.$emit('ucenter-need-refresh', { id: this.formDataId });\n          \n          // 刷新页面数据\n          this.getDetail(this.formDataId);\n        } else {\n          throw new Error(res.result?.message || '确认失败');\n        }\n      } catch (error) {\n        uni.hideLoading();\n        uni.showModal({\n          content: error.message || '确认失败',\n          showCancel: false\n        });\n      }\n    },\n\n    async handleRejectCompletion() {\n      if (!this.rejectReason) {\n        uni.showToast({ title: '请输入退回理由', icon: 'none' });\n        return;\n      }\n\n      try {\n        const confirmRes = await uni.showModal({\n          title: '确认退回',\n          content: `确定要退回重做吗？\\n退回理由：${this.rejectReason}`,\n          confirmText: '确定',\n          cancelText: '取消'\n        });\n\n        if (!confirmRes.confirm) return;\n\n        uni.showLoading({ title: '处理中...', mask: true });\n\n        // 将状态退回到assigned_to_responsible，并保存退回理由\n        const res = await uniCloud.callFunction({\n          name: 'feedback-workflow',\n          data: {\n            action: 'updateWorkflowStatus',\n            id: this.formDataId,\n            workflowStatus: 'assigned_to_responsible',\n            rejectReason: this.rejectReason\n          }\n        });\n\n        if (res.result && res.result.code === 0) {\n          // 批量更新云函数返回的所有字段（确保数据一致性）\n          if (res.result.data) {\n            const returnedData = res.result.data;\n            \n            // 核心工作流字段（必须更新）\n            if (returnedData.actionHistory) {\n              this.formData.actionHistory = returnedData.actionHistory;\n            }\n            if (returnedData.lastUpdateTime) {\n              this.formData.lastUpdateTime = returnedData.lastUpdateTime;\n            }\n            if (returnedData.remark !== undefined) {\n              this.formData.remark = returnedData.remark;\n            }\n            if (returnedData.workflowStatus !== undefined) {\n              this.formData.workflowStatus = returnedData.workflowStatus;\n            }\n            if (returnedData.rejectReason !== undefined) {\n              this.formData.rejectReason = returnedData.rejectReason;\n            }\n            if (returnedData.rejectedTime !== undefined) {\n              this.formData.rejectedTime = returnedData.rejectedTime;\n            }\n          }\n          \n          // 清除完成相关字段\n          this.formData.responsibleCompletionDescription = null;\n          this.formData.responsibleCompletionEvidence = [];\n          this.formData.completedByResponsibleTime = null;\n          \n          // 更新当前步骤，确保界面正确显示\n          this.updateCurrentStep();\n          \n          // 强制触发视图更新，确保退回状态立即显示\n          this.$forceUpdate();\n          \n          // 额外确保显示立即更新（防止缓存问题）\n          this.$nextTick(() => {\n            this.$forceUpdate();\n          });\n          \n          // 清空退回理由输入框\n          this.rejectReason = '';\n          \n          uni.hideLoading();\n          uni.showToast({ title: '已退回重做', icon: 'success' });\n          \n          // 发送feedback-updated事件，通知列表页面刷新\n          uni.$emit('feedback-updated');\n          \n          // 添加用户中心页面刷新事件，确保用户中心页面能实时更新\n          uni.$emit('ucenter-need-refresh', { id: this.formDataId });\n          \n                // 刷新页面数据\n      this.getDetail(this.formDataId);\n    } else {\n      throw new Error(res.result?.message || '退回失败');\n    }\n  } catch (error) {\n    uni.hideLoading();\n    uni.showModal({\n      content: error.message || '退回失败',\n      showCancel: false\n    });\n  }\n},\n\n    /**\n     * 静默刷新数据\n     * 不显示loading，避免影响用户操作\n     */\n    async silentRefreshData() {\n      try {\n        // 防止重复加载\n        if (this._isLoading) return;\n        \n        const res = await uniCloud.callFunction({\n          name: 'feedback-workflow',\n          data: {\n            action: 'get_detail',\n            id: this.formDataId\n          }\n        });\n        \n        if (res.result.code === 0 && res.result.data) {\n          const data = res.result.data;\n          \n          // 使用标准新工作流数据结构\n          // 使用纯净的新工作流数据结构（静默刷新用）\n          this.formData = {\n            // 基础字段\n            name: data.name,\n            project: data.project,\n            description: data.description || '',\n            images: data.images || [],\n            createTime: data.createTime,\n            createUserId: data.createUserId,\n            isAdopted: data.isAdopted,\n            isCompleted: data.isCompleted,\n            // 新工作流字段\n            workflowStatus: data.workflowStatus || 'pending_supervisor',\n            meetingRequired: data.meetingRequired || false,\n            terminatedBy: data.terminatedBy || '',\n            terminatedTime: data.terminatedTime,\n            lastUpdateTime: data.lastUpdateTime,\n            remark: data.remark || '',\n            updateTrigger: data.updateTrigger || '',\n            // 负责人字段\n            responsibleUserId: data.responsibleUserId,\n            assignedTime: data.assignedTime,\n            assignReason: data.assignReason || '',\n            responsibleCompletionDescription: data.responsibleCompletionDescription,\n            responsibleCompletionEvidence: data.responsibleCompletionEvidence || [],\n            completedByResponsibleTime: data.completedByResponsibleTime,\n            finalCompletedTime: data.finalCompletedTime,\n            rejectReason: data.rejectReason || '',\n            rejectedTime: data.rejectedTime,\n            // 操作历史（新工作流的唯一数据源）\n            actionHistory: data.actionHistory || []\n          };\n          \n          // 更新UI状态\n          this.updateSelectedOptions();\n          this.updateCurrentStep();\n          \n          // 强制更新视图\n          this.$forceUpdate();\n        }\n      } catch (error) {\n        console.log('静默刷新失败:', error);\n      }\n    },\n\n    // 智能判断是否需要刷新数据\n    shouldRefreshOnCrossDeviceUpdate(data) {\n      // 如果没有当前记录ID，不需要刷新\n      if (!this.formDataId) {\n        return false;\n      }\n      \n      // 如果页面不可见，不需要刷新\n      if (!this.isPageVisible) {\n        return false;\n      }\n      \n      // 如果距离上次刷新时间太短（小于10秒），避免频繁刷新\n      const timeSinceLastRefresh = Date.now() - this.lastRefreshTime;\n      if (timeSinceLastRefresh < 10000) {\n        return false;\n      }\n      \n      // 如果更新的反馈记录包含当前正在查看的记录，需要刷新\n      if (data.feedbackIds && data.feedbackIds.includes(this.formDataId)) {\n        return true;\n      }\n      \n      // 如果更新类型包含工作流状态变化，可能需要刷新\n      if (data.updateTypes && data.updateTypes.includes('workflow_status_changed')) {\n        return true;\n      }\n      \n      // 默认不刷新\n      return false;\n    },\n\n    /**\n     * 处理记录已被删除的情况\n     * 当其他用户删除了正在操作的记录时，优雅地处理这种边界情况\n     */\n    handleRecordDeleted() {\n      // 清除loading状态\n      uni.hideLoading();\n      this._isProcessing = false;\n\n      // 显示友好的提示信息\n      uni.showModal({\n        title: '记录已删除',\n        content: '您正在操作的反馈记录已被其他用户删除，将返回上一页面。',\n        showCancel: false,\n        confirmText: '确定',\n        success: () => {\n          // 清除本地缓存\n          try {\n            const cacheKey = `approval_${this.formDataId}`;\n            uni.removeStorageSync(cacheKey);\n          } catch (e) {}\n\n          // 触发相关页面刷新事件\n          uni.$emit('refresh-todo-list');\n          uni.$emit('feedback-updated');\n          uni.$emit('ucenter-need-refresh');\n\n          // 立即更新角标\n          const todoBadgeManager = require('@/utils/todo-badge.js').default;\n          if (todoBadgeManager) {\n            todoBadgeManager.forceRefresh();\n          }\n\n          // 返回上一页面\n          uni.navigateBack({\n            delta: 1,\n            fail: () => {\n              // 如果无法返回，跳转到首页\n              uni.switchTab({\n                url: '/pages/index/index'\n              });\n            }\n          });\n        }\n      });\n    },\n\n  },\n\n  /**\n   * 页面生命周期 - 页面创建时\n   */\n  created() {\n    // 监听角标管理器的跨设备更新事件\n    uni.$on('cross-device-update-detected', (data) => {\n      if (data.silent) {\n        // 智能判断是否需要刷新\n        const shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);\n        if (shouldRefresh) {\n                      console.log('审核页面收到跨设备更新通知，静默刷新数据');\n            // 静默刷新数据\n            this.silentRefreshData();\n        }\n      }\n    });\n  },\n\n  /**\n   * 页面生命周期 - 页面销毁时\n   */\n  beforeDestroy() {\n    // 移除事件监听\n    uni.$off('cross-device-update-detected');\n  }\n}\n</script>\n\n<style>\n.uni-container {\n  padding-top: 20px;\n  padding-right: 20px;\n  padding-left: 20px;\n  padding-bottom: 20px;\n  min-height: 100vh;\n  justify-content: flex-start;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n}\n.steps-section {\n  padding: 30px 20px;\n  background: white;\n  border-radius: 16px;\n  margin-bottom: 20px;\n}\n\n.steps {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.step {\n  position: relative;\n}\n\n.step-content {\n  display: flex;\n  align-items: flex-start;\n  gap: 20px;\n}\n\n.step-icon {\n  position: relative;\n  width: 40px;\n  flex-shrink: 0;\n}\n\n.icon-number {\n  width: 40px;\n  height: 40px;\n  line-height: 40px;\n  border-radius: 50%;\n  background: #e2e8f0;\n  color: #64748b;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  position: relative;\n  z-index: 2;\n}\n\n.icon-line {\n  position: absolute;\n  left: 50%;\n  top: 40px;\n  bottom: -60px;\n  width: 2px;\n  background: #e2e8f0;\n  transform: translateX(-50%);\n}\n\n.step.active .icon-number {\n  background: #3b82f6;\n  color: white;\n  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);\n}\n\n.step.completed .icon-number {\n  background: #10b981;\n  color: white;\n}\n\n.step.rejected .icon-number {\n  background: #ef4444;\n  color: white;\n}\n\n.step.not-executed .icon-number {\n  background: #9ca3af;\n  color: white;\n}\n\n.step.meeting .icon-number {\n  background: #9c27b0;\n  color: white;\n}\n\n.step-info {\n  flex: 1;\n  background: #f8fafc;\n  padding: 16px;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  border: 1px solid transparent;\n}\n\n.step.active .step-info {\n  background: white;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border: 1px solid #3b82f6;\n}\n\n.step.completed .step-info {\n  border-color: #10b981;\n  background-color: rgba(16, 185, 129, 0.05);\n}\n\n.step.rejected .step-info {\n  border-color: #ef4444;\n  background-color: rgba(239, 68, 68, 0.05);\n}\n\n.step.not-executed .step-info {\n  border-color: #9ca3af;\n  background-color: rgba(156, 163, 175, 0.05);\n  opacity: 0.7;\n}\n\n.step.meeting .step-info {\n  border-color: #9c27b0;\n  background-color: rgba(156, 39, 176, 0.05);\n}\n\n/* 最后一步（审核结果）特殊样式 */\n.step:last-child.completed .step-info {\n  background-color: rgba(16, 185, 129, 0.05);\n  border-color: #10b981;\n  border-width: 2px;\n}\n\n.step:last-child.rejected .step-info {\n  background-color: rgba(239, 68, 68, 0.05);\n  border-color: #ef4444;\n  border-width: 2px;\n}\n\n.step-title {\n  font-size: 14px;\n  font-weight: 500;\n  display: block;\n  color: #495057;\n}\n\n.step-status-info {\n  margin-top: 8px;\n  text-align: center;\n}\n\n.status-text {\n  font-size: 12px;\n  color: #333;\n  display: block;\n  margin-bottom: 4px;\n}\n\n.status-text-approved {\n  color: #4caf50;\n}\n\n.status-text-rejected {\n  color: #f44336;\n}\n\n.status-text-meeting {\n  color: #9c27b0;\n}\n\n.status-text-not-executed {\n  color: #999999;\n}\n\n.time-text {\n  font-size: 11px;\n  color: #666;\n  display: block;\n}\n\n.step-waiting {\n  font-size: 12px;\n  color: #3b82f6; /* 统一蓝色颜色值 */\n  display: block;\n  margin-top: 8px;\n  text-align: center;\n}\n\n.workflow-notice {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-top: 10px;\n  padding: 8px 12px;\n  background: rgba(59, 130, 246, 0.1); /* 统一蓝色 */\n  border-radius: 6px;\n  border-left: 3px solid #3b82f6; /* 统一蓝色 */\n}\n\n.workflow-notice-text {\n  font-size: 12px;\n  color: #3b82f6; /* 统一蓝色 */\n}\n\n.approval-section {\n  background: #fff;\n  padding: 20px;\n  margin: 15px 0;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  transition: all 0.3s;\n}\n\n.approval-section.active {\n  border: 2px solid #3b82f6; /* 统一蓝色 */\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1); /* 统一蓝色 */\n}\n\n.approval-section.disabled {\n  opacity: 0.7;\n  pointer-events: none;\n}\n\n.approval-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.approval-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #212529;\n}\n\n.approval-status {\n  font-size: 14px;\n  padding: 2px 8px;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.status-approved {\n  color: #4caf50;\n  background-color: rgba(76, 175, 80, 0.1);\n}\n\n.status-rejected {\n  color: #f44336;\n  background-color: rgba(244, 67, 54, 0.1);\n}\n\n.status-pending {\n  color: #3b82f6; /* 统一蓝色 */\n  background-color: rgba(59, 130, 246, 0.1); /* 统一蓝色 */\n}\n\n.status-meeting {\n  color: #9c27b0;\n  background-color: rgba(156, 39, 176, 0.1);\n}\n\n.status-not-executed {\n  color: #999999;\n  background-color: rgba(153, 153, 153, 0.1);\n}\n\n.approval-options {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n/* 小程序优化审核选项布局 */\n/* #ifdef MP-WEIXIN */\n.approval-options {\n  gap: 12rpx;\n}\n\n.radio-item {\n  flex: 1;\n  min-width: 200rpx;\n  padding: 16rpx 24rpx;\n  font-size: 28rpx;\n}\n\n.radio-item text {\n  font-size: 28rpx;\n}\n/* #endif */\n\n.radio-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 16px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.radio-item:hover {\n  background: #e9ecef;\n}\n\n.radio-item text {\n  font-size: 14px;\n  color: #495057;\n}\n\nradio {\n  transform: scale(0.9);\n}\n\n.reset-section {\n  margin-top: 30px;\n  padding: 0 20px;\n}\n\n.reset-btn {\n  width: 100%;\n  height: 44px;\n  line-height: 44px;\n  background-color: #ff4d4f;\n  color: #fff;\n  border-radius: 6px;\n  font-size: 16px;\n  transition: all 0.3s;\n}\n\n.reset-btn:active {\n  opacity: 0.8;\n}\n\n/* 最终结果的特殊样式 */\n.step:last-child .status-text {\n  font-size: 12px;\n  color: #999999; /* 默认灰色，而不是蓝色 */\n  display: block;\n  margin-top: 8px;\n  text-align: center;\n}\n\n.step.completed:last-child .status-text {\n  color: #4caf50;\n}\n\n.step.rejected:last-child .status-text {\n  color: #f44336;\n}\n\n/* 第五步激活状态的特殊样式 - 仅当状态为pending时显示蓝色 */\n.step:last-child.active .status-text {\n  color: #3b82f6; /* 激活时使用统一的蓝色 */\n}\n\n/* 第五步激活状态的数字发光效果 - 仅当状态为pending时，与其他步骤保持一致 */\n.step:last-child.active:not(.completed):not(.rejected) .icon-number {\n  background: #3b82f6;\n  color: white;\n  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);\n}\n\n/* 第五步激活状态的卡片样式 - 仅当状态为pending时 */\n.step:last-child.active:not(.completed):not(.rejected) .step-info {\n  background: white;\n  border: 2px solid #3b82f6;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n/* 第五步完成状态的特殊样式 */\n.step:last-child.completed .status-text {\n  color: #4caf50; /* 完成时显示绿色 */\n}\n\n/* 第五步拒绝状态的特殊样式 */\n.step:last-child.rejected .status-text {\n  color: #f44336; /* 拒绝时显示红色 */\n}\n\n.reason-box {\n  margin-top: 15px;\n  padding: 10px 0;\n}\n\n.reason-label {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.reason-input {\n  width: 95%;\n  height: 80px;\n  padding: 8px 12px;\n  border: 1px solid #e9ecef;\n  border-radius: 6px;\n  font-size: 14px;\n  color: #333;\n  background: #fff;\n  line-height: 1.5;\n}\n\n.reason-input:disabled {\n  background-color: #f5f5f5;\n  color: #999;\n}\n\n.submitted-reason {\n  padding: 10px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.reason-content {\n  font-size: 14px;\n  color: #333;\n  line-height: 1.5;\n}\n\n.reason-approved {\n  color: #4caf50 !important;\n}\n\n.reason-rejected {\n  color: #f44336 !important;\n}\n\n.reason-meeting {\n  color: #9c27b0 !important;\n}\n\n.reason-not-executed {\n  color: #999999 !important;\n}\n\n.reason-info {\n  color: #17a2b8 !important;\n}\n\n/* 审核理由汇总区域样式 */\n.approval-summary-section {\n  background: #fff;\n  padding: 20px;\n  margin: 15px 0;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.approval-summary-header {\n  margin-bottom: 15px;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 10px;\n}\n\n.approval-summary-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #212529;\n}\n\n.approval-summary-content {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.summary-item {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 12px;\n  border-left: 3px solid #e9ecef;\n}\n\n.summary-item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.summary-role {\n  font-size: 14px;\n  font-weight: 500;\n  color: #495057;\n}\n\n.summary-status {\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 4px;\n}\n\n.summary-reason {\n  padding: 8px;\n  background: #fff;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.status-pending {\n  color: #3b82f6;\n  background-color: rgba(59, 130, 246, 0.1);\n}\n\n.status-info {\n  color: #17a2b8;\n  background-color: rgba(23, 162, 184, 0.1);\n}\n\n.status-text-approved {\n  color: #4caf50;\n}\n\n.status-text-rejected {\n  color: #f44336;\n}\n\n.status-text-not-executed {\n  color: #999999;\n}\n\n.status-meeting {\n  color: #9c27b0;\n  background-color: rgba(156, 39, 176, 0.1);\n}\n\n.status-not-executed {\n  color: #999999;\n  background-color: rgba(153, 153, 153, 0.1);\n}\n\n/* 骨架屏样式 */\n.skeleton-loading {\n  padding: 20px;\n}\n\n.skeleton-steps-section {\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 16px;\n  padding: 30px 20px;\n  margin-bottom: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n}\n\n.skeleton-step {\n  height: 80px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 8px;\n}\n\n.skeleton-approval-section {\n  height: 200px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 12px;\n  margin-bottom: 15px;\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: 200% 0;\n  }\n  100% {\n    background-position: -200% 0;\n  }\n}\n\n/* 字符计数样式 */\n.character-count {\n  font-size: 12px;\n  color: #999;\n  text-align: right;\n  margin-top: 4px;\n}\n\n/* 新增工作流样式 */\n.assign-section {\n  padding: 15px 0;\n}\n\n.assign-picker {\n  margin-bottom: 15px;\n}\n\n.picker-label {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.uni-input {\n  padding: 12px;\n  border: 1px solid #e9ecef;\n  border-radius: 6px;\n  background: #fff;\n  font-size: 14px;\n  color: #333;\n}\n\n.assign-btn {\n  margin-top: 15px;\n  width: 100%;\n  background: #007aff;\n  color: #fff;\n  border: none;\n  border-radius: 6px;\n  height: 40px;\n  font-size: 16px;\n}\n\n.responsible-info {\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  margin-bottom: 15px;\n}\n\n.responsible-text {\n  font-size: 14px;\n  color: #333;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.assign-time {\n  font-size: 14px;\n  color: #666;\n}\n\n.responsible-action {\n  margin-top: 15px;\n}\n\n.completion-form {\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n}\n\n.evidence-section {\n  margin: 15px 0;\n}\n\n.evidence-label {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.evidence-upload {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.image-list {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.image-item {\n  position: relative;\n  width: 80px;\n  height: 80px;\n}\n\n.image-item image {\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n  border: 2px solid #fff;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.image-item image:hover {\n  transform: scale(1.05) rotate(1deg);\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\n  z-index: 10;\n}\n\n.upload-item {\n  width: 90px;\n  height: 90px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 5px;\n}\n\n.image-delete {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  width: 22px;\n  height: 22px;\n  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);\n  color: #fff;\n  border-radius: 50%;\n  text-align: center;\n  line-height: 22px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  box-shadow: 0 2px 6px rgba(255, 71, 87, 0.3);\n  border: 2px solid #fff;\n  z-index: 20;\n}\n\n.image-delete:hover {\n  transform: scale(1.1);\n  background: linear-gradient(135deg, #ff3838 0%, #ff2f2f 100%);\n  box-shadow: 0 3px 8px rgba(255, 71, 87, 0.4);\n}\n\n.upload-btn {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border: 1px dashed #ccc;\n  border-radius: 8px;\n  color: #666;\n  font-size: 10px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 2px;\n  padding: 8px 4px;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  box-sizing: border-box;\n}\n\n.upload-btn:hover {\n  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);\n  border-color: #999;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.upload-btn:active {\n  transform: translateY(0);\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);\n}\n\n.upload-icon {\n  display: block;\n  line-height: 1;\n}\n\n.upload-text {\n  font-size: 9px;\n  font-weight: 500;\n  line-height: 1.2;\n  text-align: center;\n  white-space: nowrap;\n}\n\n.complete-btn {\n  width: 100%;\n  background: #28a745;\n  color: #fff;\n  border: none;\n  border-radius: 6px;\n  height: 40px;\n  font-size: 16px;\n  margin-top: 15px;\n}\n\n.completion-review {\n  padding: 15px 0;\n}\n\n.completion-info {\n  background: #f8f9fa;\n  padding: 12px;\n  border-radius: 6px;\n  margin-bottom: 15px;\n}\n\n.completed-work {\n  margin: 15px 0;\n  padding: 15px;\n  background: #f8fdf8;\n  border: 1px solid #d4edda;\n  border-radius: 8px;\n}\n\n.completion-time {\n  font-size: 14px;\n  color: #666;\n  display: block;\n  margin-top: 5px;\n}\n\n.review-tip {\n  font-size: 12px;\n  color: #007aff;\n  display: block;\n  margin-top: 8px;\n  font-style: italic;\n}\n\n/* 完成说明样式 - 去掉灰色背景，恢复原有文字颜色 */\n.completion-description-item {\n\tmargin: 15px 0;\n}\n\n.completion-description-header {\n\tmargin-bottom: 8px;\n}\n\n.description-label {\n\tfont-size: 14px;\n\tcolor: #666;\n\tfont-weight: 500;\n}\n\n.completion-description-content {\n\tpadding: 8px;\n\tbackground: #fff;\n\tborder-radius: 6px;\n\tborder: 1px solid #e9ecef;\n}\n\n.description-text {\n\tfont-size: 14px;\n\tcolor: #28a745;\n\tline-height: 1.5;\n}\n\n.completion-evidence {\n  margin: 15px 0;\n}\n\n.evidence-images {\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n  margin-top: 8px;\n  padding: 8px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.evidence-image {\n  width: 90px;\n  height: 90px;\n  border-radius: 8px;\n  border: 2px solid #fff;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.evidence-image:hover {\n  transform: scale(1.05);\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);\n}\n\n.final-actions {\n  display: flex;\n  gap: 10px;\n  margin-top: 15px;\n}\n\n.reject-btn {\n  flex: 1;\n  background: #6c757d;\n  color: #fff;\n  border: none;\n  border-radius: 6px;\n  height: 40px;\n  font-size: 16px;\n}\n\n.confirm-btn {\n  flex: 1;\n  background: #28a745;\n  color: #fff;\n  border: none;\n  border-radius: 6px;\n  height: 40px;\n  font-size: 16px;\n}\n\n/* 退回理由显示样式 */\n.reject-reason-display {\n  margin: 15px 0;\n  padding: 12px;\n  background: #fff3cd;\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  border-left: 4px solid #f39c12;\n}\n\n.reject-reason-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.reject-reason-label {\n  font-size: 14px;\n  color: #e67e22;\n  font-weight: 500;\n}\n\n.reject-time {\n  font-size: 12px;\n  color: #666;\n}\n\n.reject-reason-content {\n  padding: 8px;\n  border-radius: 6px;\n}\n\n.reject-reason-text {\n  font-size: 14px;\n  color: #d68910;\n  line-height: 1.5;\n  font-weight: 500;\n}\n\n/* 流程完成状态样式 */\n.completion-summary {\n  margin: 15px 0;\n}\n\n.summary-container {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.summary-item-container {\n  background: #f8fdf8;\n  border: 1px solid #d4edda;\n  border-radius: 8px;\n  padding: 12px;\n}\n\n.summary-header {\n  margin-bottom: 8px;\n}\n\n.summary-label {\n  font-size: 14px;\n  color: #666;\n  font-weight: 500;\n}\n\n.summary-content {\n  /* 内容区域样式 */\n}\n\n.summary-text {\n  font-size: 14px;\n  color: #28a745;\n  line-height: 1.5;\n  font-weight: 500;\n}\n\n/* 问题描述区域样式 */\n.description-section {\n  background: #fff;\n  padding: 20px;\n  margin-bottom: 20px;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.description-header {\n  margin-bottom: 15px;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 10px;\n}\n\n.description-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #212529;\n}\n\n.description-content {\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.description-text {\n  font-size: 14px;\n  color: #333;\n  line-height: 1.5;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n</style> ", "import { render, staticRenderFns, recyclableRender, components } from \"./uni-icons.vue?vue&type=template&id=a2e81f6e&\"\nvar renderjs\nimport script from \"./uni-icons.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-icons.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-icons.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-icons.vue?vue&type=template&id=a2e81f6e&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-icons.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-icons.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- #ifdef APP-NVUE -->\r\n\t<text :style=\"styleObj\" class=\"uni-icons\" @click=\"_onClick\">{{unicode}}</text>\r\n\t<!-- #endif -->\r\n\t<!-- #ifndef APP-NVUE -->\r\n\t<text :style=\"styleObj\" class=\"uni-icons\" :class=\"['uniui-'+type,customPrefix,customPrefix?type:'']\" @click=\"_onClick\">\r\n\t\t<slot></slot>\r\n\t</text>\r\n\t<!-- #endif -->\r\n</template>\r\n\r\n<script>\r\n\timport { fontData } from './uniicons_file_vue.js';\r\n\r\n\tconst getVal = (val) => {\r\n\t\tconst reg = /^[0-9]*$/g\r\n\t\treturn (typeof val === 'number' || reg.test(val)) ? val + 'px' : val;\r\n\t}\r\n\r\n\t// #ifdef APP-NVUE\r\n\tvar domModule = weex.requireModule('dom');\r\n\timport iconUrl from './uniicons.ttf'\r\n\tdomModule.addRule('fontFace', {\r\n\t\t'fontFamily': \"uniicons\",\r\n\t\t'src': \"url('\" + iconUrl + \"')\"\r\n\t});\r\n\t// #endif\r\n\r\n\t/**\r\n\t * Icons 图标\r\n\t * @description 用于展示 icons 图标\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=28\r\n\t * @property {Number} size 图标大小\r\n\t * @property {String} type 图标图案，参考示例\r\n\t * @property {String} color 图标颜色\r\n\t * @property {String} customPrefix 自定义图标\r\n\t * @event {Function} click 点击 Icon 触发事件\r\n\t */\r\n\texport default {\r\n\t\tname: 'UniIcons',\r\n\t\temits: ['click'],\r\n\t\tprops: {\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#333333'\r\n\t\t\t},\r\n\t\t\tsize: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 16\r\n\t\t\t},\r\n\t\t\tcustomPrefix: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tfontFamily: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ticons: fontData\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tunicode() {\r\n\t\t\t\tlet code = this.icons.find(v => v.font_class === this.type)\r\n\t\t\t\tif (code) {\r\n\t\t\t\t\treturn code.unicode\r\n\t\t\t\t}\r\n\t\t\t\treturn ''\r\n\t\t\t},\r\n\t\t\ticonSize() {\r\n\t\t\t\treturn getVal(this.size)\r\n\t\t\t},\r\n\t\t\tstyleObj() {\r\n\t\t\t\tif (this.fontFamily !== '') {\r\n\t\t\t\t\treturn `color: ${this.color}; font-size: ${this.iconSize}; font-family: ${this.fontFamily};`\r\n\t\t\t\t}\r\n\t\t\t\treturn `color: ${this.color}; font-size: ${this.iconSize};`\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t_onClick() {\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t/* #ifndef APP-NVUE */\r\n\t@import './uniicons.css';\r\n\r\n\t@font-face {\r\n\t\tfont-family: uniicons;\r\n\t\tsrc: url('./uniicons.ttf');\r\n\t}\r\n\r\n\t/* #endif */\r\n\t.uni-icons {\r\n\t\tfont-family: uniicons;\r\n\t\ttext-decoration: none;\r\n\t\ttext-align: center;\r\n\t}\r\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-icons.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-icons.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571671473\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./examine.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./examine.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571646334\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
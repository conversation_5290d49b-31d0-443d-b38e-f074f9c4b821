{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?86fe", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?33d7", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?8fcb", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?5c0e", "uni-app:///uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?4b46", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?a744"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "data", "type", "phone", "focusPhone", "logo", "computed", "loginTypes", "isPhone", "imgSrc", "weixin", "apple", "hua<PERSON>", "huaweiMobile", "onLoad", "item", "uni", "onShow", "onUnload", "onReady", "methods", "showCurrentWebview", "currentWebview", "quickLogin", "options", "toSmsPage", "title", "icon", "duration", "url", "chooseArea"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,6VAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAA0mB,CAAgB,ooBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACmF9nB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFA;AAAA,eAIA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;IACA;IACAC;MAAA;MACA;IACA;IACAC;MAAA;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACAZ;cACA;cAEA;gBACA;cACA;cAEA;gBACA;gBACA;kBACA;oBAAA,OACAa;kBAAA;gBACA;cACA;cAEAC;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC,2BASA;EACAC;IACAF;EACA;EACAG;IACA;EAAA,CA0BA;EACAC;IACAC;MACA;MACAC;QACA;MACA;IACA;IACAC;MAAA;QAAA;QAAA;MACA;MACA;QACAC;MACA;MAEA;;MAEA;MACA;QACA;UAAA;QAAA;MACA;MAEA;IACA;IACAC;MACA;QACA;QACA;UACAC;UACAC;UACAC;QACA;MACA;MACA;QACA;MACA;MACA;MACAZ;QACAa;MACA;IACA;IACAC;MACAd;QACAU;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9NA;AAAA;AAAA;AAAA;AAA6qC,CAAgB,mpCAAG,EAAC,C;;;;;;;;;;;ACAjsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/pages/login/login-withoutpwd.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login-withoutpwd.vue?vue&type=template&id=3d746ff5&scoped=true&\"\nvar renderjs\nimport script from \"./login-withoutpwd.vue?vue&type=script&lang=js&\"\nexport * from \"./login-withoutpwd.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login-withoutpwd.vue?vue&type=style&index=0&id=3d746ff5&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3d746ff5\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login-withoutpwd.vue?vue&type=template&id=3d746ff5&scoped=true&\"", "var components\ntry {\n  components = {\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniIdPagesAgreements: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue\"\n      )\n    },\n    uniIdPagesFabLogin: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = [\"weixin\", \"weixinMobile\"].includes(_vm.type)\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.focusPhone = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login-withoutpwd.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login-withoutpwd.vue?vue&type=script&lang=js&\"", "<!-- 免密登录页 -->\n<template>\n\t<view class=\"login-container\">\n\t\t<!-- 登录页顶部图形 -->\n\t\t<view class=\"login-header\">\n\t\t\t<view class=\"wave-container\">\n\t\t\t\t<view class=\"wave wave1\"></view>\n\t\t\t\t<view class=\"wave wave2\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 登录内容区 -->\n\t\t<view class=\"login-content\">\n\t\t\t<!-- 应用Logo -->\n\t\t\t<view class=\"logo-container\">\n\t\t\t\t<image class=\"logo-image\" :src=\"logo\" mode=\"aspectFit\"></image>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 登录方式 -->\n\t\t\t<view class=\"login-methods\">\n\t\t\t\t<text class=\"welcome-text\">欢迎使用</text>\n\t\t\t\t\n\t\t\t\t<!-- 微信登录按钮 -->\n\t\t\t\t<template v-if=\"['weixin', 'weixinMobile'].includes(type)\">\n\t\t\t\t\t<!-- <view class=\"login-tip\">将根据微信授权获取您的信息</view> -->\n\t\t\t\t\t<view class=\"wx-login-btn-container\">\n\t\t\t\t\t\t<button v-if=\"type === 'weixinMobile'\" \n\t\t\t\t\t\t\t\ttype=\"primary\" \n\t\t\t\t\t\t\t\topen-type=\"getPhoneNumber\" \n\t\t\t\t\t\t\t\t@getphonenumber=\"quickLogin\"\n\t\t\t\t\t\t\t\tclass=\"wx-login-btn phone-btn\">\n\t\t\t\t\t\t\t<text class=\"btn-text\">微信手机号快捷登录</text>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t\t<button v-else \n\t\t\t\t\t\t\t\t@click=\"quickLogin\" \n\t\t\t\t\t\t\t\tclass=\"wx-login-btn\">\n\t\t\t\t\t\t\t<image class=\"wx-icon\" src=\"/static/user/wechat.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t<text class=\"btn-text\">微信一键登录</text>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t\t\n\t\t\t\t<!-- 手机号登录 -->\n\t\t\t\t<template v-else>\n\t\t\t\t\t<view class=\"login-tip\">未注册的账号验证通过后将自动注册</view>\n\t\t\t\t\t<view class=\"phone-input-container\">\n\t\t\t\t\t\t<view class=\"phone-area\" @click=\"chooseArea\">+86</view>\n\t\t\t\t\t\t<uni-easyinput \n\t\t\t\t\t\t\ttrim=\"both\" \n\t\t\t\t\t\t\t:focus=\"focusPhone\" \n\t\t\t\t\t\t\t@blur=\"focusPhone = false\" \n\t\t\t\t\t\t\tclass=\"phone-input\" \n\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\t:inputBorder=\"false\" \n\t\t\t\t\t\t\tv-model=\"phone\" \n\t\t\t\t\t\t\tmaxlength=\"11\" \n\t\t\t\t\t\t\tplaceholder=\"请输入手机号\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<button class=\"primary-btn\" @click=\"toSmsPage\">获取验证码</button>\n\t\t\t\t</template>\n\t\t\t\t\n\t\t\t\t<!-- 服务协议和隐私政策 -->\n\t\t\t\t<view class=\"agreement-box\">\n\t\t\t\t\t<uni-id-pages-agreements scope=\"login\" ref=\"agreements\"></uni-id-pages-agreements>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 其他登录方式 -->\n\t\t\t\t<view class=\"other-login-methods\">\n\t\t\t\t\t<view class=\"divider\">\n\t\t\t\t\t\t<view class=\"line\"></view>\n\t\t\t\t\t\t<text class=\"divider-text\">其他登录方式</text>\n\t\t\t\t\t\t<view class=\"line\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-id-pages-fab-login ref=\"uniFabLogin\"></uni-id-pages-fab-login>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\tlet currentWebview; //当前窗口对象\n\timport config from '@/uni_modules/uni-id-pages/config.js'\n\timport mixin from '@/uni_modules/uni-id-pages/common/login-page.mixin.js';\n\t\n\texport default {\n\t\tmixins: [mixin],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttype: \"\", // 快捷登录方式\n\t\t\t\tphone: \"\", // 手机号码\n\t\t\t\tfocusPhone: false,\n\t\t\t\tlogo: \"/static/brand/logo.png\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tloginTypes() { // 读取配置的登录优先级\n\t\t\t\treturn config.loginTypes\n\t\t\t},\n\t\t\tisPhone() { // 手机号码校验正则\n\t\t\t\treturn /^1\\d{10}$/.test(this.phone);\n\t\t\t},\n\t\t\timgSrc() { // 快捷登录按钮图\n\t\t\t\tconst images = {\n\t\t\t\t\tweixin: '/uni_modules/uni-id-pages/static/login/weixin.png',\n\t\t\t\t\tapple: '/uni_modules/uni-id-pages/static/app/apple.png',\n\t\t\t\t\thuawei: '/uni_modules/uni-id-pages/static/login/huawei.png',\n\t\t\t\t\thuaweiMobile: '/uni_modules/uni-id-pages/static/login/huawei-mobile.png',\n\t\t\t\t}\n\t\t\t\treturn images[this.type]\n\t\t\t}\n\t\t},\n\t\tasync onLoad(e) {\n\t\t\t// 获取通过url传递的参数type设置当前登录方式，如果没传递直接默认以配置的登录\n\t\t\tlet type = e.type || config.loginTypes[0]\n\t\t\tthis.type = type\n\t\t\t\n\t\t\tif (type != 'univerify') {\n\t\t\t\tthis.focusPhone = true\n\t\t\t}\n\t\t\t\n\t\t\tthis.$nextTick(() => {\n\t\t\t\t// 关闭重复显示的登录快捷方式\n\t\t\t\tif (['weixin', 'apple', 'huawei', 'huaweiMobile'].includes(type)) {\n\t\t\t\t\tthis.$refs.uniFabLogin.servicesList = this.$refs.uniFabLogin.servicesList.filter(item =>\n\t\t\t\t\t\titem.id != type)\n\t\t\t\t}\n\t\t\t})\n\t\t\t\n\t\t\tuni.$on('uni-id-pages-setLoginType', type => {\n\t\t\t\tthis.type = type\n\t\t\t})\n\t\t},\n\t\tonShow() {\n\t\t\t// #ifdef H5\n\t\t\tdocument.onkeydown = event => {\n\t\t\t\tvar e = event || window.event;\n\t\t\t\tif (e && e.keyCode == 13) { // 回车键的键值为13\n\t\t\t\t\tthis.toSmsPage()\n\t\t\t\t}\n\t\t\t};\n\t\t\t// #endif\n\t\t},\n\t\tonUnload() {\n\t\t\tuni.$off('uni-id-pages-setLoginType')\n\t\t},\n\t\tonReady() {\n\t\t\t// 是否优先启动一键登录。即：页面一加载就启动一键登录\n\t\t\t//#ifdef APP-PLUS\n\t\t\tif (config.loginTypes.includes('univerify') && this.type == \"univerify\") {\n\t\t\t\tuni.preLogin({\n\t\t\t\t\tprovider: 'univerify',\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\t\t\tcurrentWebview = pages[pages.length - 1].$getAppWebview();\n\t\t\t\t\t\tcurrentWebview.setStyle({\n\t\t\t\t\t\t\t\"top\": \"2000px\" // 隐藏当前页面窗体\n\t\t\t\t\t\t})\n\t\t\t\t\t\tthis.$refs.uniFabLogin.login_before('univerify')\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tif (config.loginTypes.length > 1) {\n\t\t\t\t\t\t\tthis.$refs.uniFabLogin.login_before(config.loginTypes[1])\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\tcontent: err.message,\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t\t//#endif\n\t\t},\n\t\tmethods: {\n\t\t\tshowCurrentWebview() {\n\t\t\t\t// 恢复当前页面窗体的显示 一键登录，默认不显示当前窗口\n\t\t\t\tcurrentWebview.setStyle({\n\t\t\t\t\t\"top\": 0\n\t\t\t\t})\n\t\t\t},\n\t\t\tquickLogin(e) {\n\t\t\t\tlet options = {}\n\t\t\t\tif (e.detail?.code) {\n\t\t\t\t\toptions.phoneNumberCode = e.detail.code\n\t\t\t\t}\n\n\t\t\t\tif ((this.type === 'weixinMobile' || this.type === 'huaweiMobile') && !e.detail?.code) return\n\n\t\t\t\t// 检查是否需要同意协议\n\t\t\t\tif (this.needAgreements && !this.agree) {\n\t\t\t\t\treturn this.$refs.agreements.popup(() => this.quickLogin(e))\n\t\t\t\t}\n\n\t\t\t\tthis.$refs.uniFabLogin.login_before(this.type, true, options)\n\t\t\t},\n\t\t\ttoSmsPage() {\n\t\t\t\tif (!this.isPhone) {\n\t\t\t\t\tthis.focusPhone = true\n\t\t\t\t\treturn uni.showToast({\n\t\t\t\t\t\ttitle: \"手机号码格式不正确\",\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tif (this.needAgreements && !this.agree) {\n\t\t\t\t\treturn this.$refs.agreements.popup(this.toSmsPage)\n\t\t\t\t}\n\t\t\t\t// 发送验证码\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/login/login-smscode?phoneNumber=' + this.phone\n\t\t\t\t});\n\t\t\t},\n\t\t\tchooseArea() {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '暂不支持其他国家',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 3000\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.login-container {\n\t\tmin-height: 100vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tbackground-color: #f8f9fa;\n\t}\n\t\n\t/* 顶部波浪效果 */\n\t.login-header {\n\t\tposition: relative;\n\t\theight: 220px;\n\t\toverflow: hidden;\n\t}\n\t\n\t.wave-container {\n\t\tposition: absolute;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: linear-gradient(135deg, #3688FF, #5DABFF);\n\t}\n\t\n\t.wave {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\twidth: 100%;\n\t\theight: 100px;\n\t\tbackground: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 800 88.7'%3E%3Cpath d='M800 56.9c-155.5 0-204.9-50-405.5-49.9-200 0-250 49.9-394.5 49.9v31.8h800v-31.8z' fill='%23ffffff'/%3E%3C/svg%3E\");\n\t\tbackground-position: center;\n\t\tbackground-repeat: repeat-x;\n\t\tbackground-size: 800px 100px;\n\t}\n\t\n\t.wave1 {\n\t\tanimation: wave 15s -3s linear infinite;\n\t\topacity: 1;\n\t\tz-index: 1;\n\t\tbottom: 0;\n\t}\n\t\n\t.wave2 {\n\t\tanimation: wave2 8s linear reverse infinite;\n\t\topacity: 0.5;\n\t\tz-index: 0;\n\t\tbottom: 10px;\n\t}\n\t\n\t@keyframes wave {\n\t\t0% {\n\t\t\tbackground-position-x: 0;\n\t\t}\n\t\t100% {\n\t\t\tbackground-position-x: 800px;\n\t\t}\n\t}\n\t\n\t@keyframes wave2 {\n\t\t0% {\n\t\t\tbackground-position-x: 0;\n\t\t}\n\t\t100% {\n\t\t\tbackground-position-x: 800px;\n\t\t}\n\t}\n\t\n\t/* 登录内容区 */\n\t.login-content {\n\t\tflex: 1;\n\t\tpadding: 0 30px;\n\t\tmargin-top: -50px;\n\t\tz-index: 2;\n\t}\n\t\n\t/* Logo区域 */\n\t.logo-container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tmargin-bottom: 40px;\n\t}\n\t\n\t.logo-image {\n\t\twidth: 100px;\n\t\theight: 100px;\n\t\tborder-radius: 10px;\n\t\tbox-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t/* 登录方式区域 */\n\t.login-methods {\n\t\tbackground-color: #fff;\n\t\tborder-radius: 16px;\n\t\tpadding: 30px;\n\t\tbox-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.welcome-text {\n\t\tfont-size: 22px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20px;\n\t\tdisplay: block;\n\t\ttext-align: center;\n\t}\n\t\n\t.login-tip {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t\tmargin-bottom: 25px;\n\t\ttext-align: center;\n\t}\n\t\n\t/* 微信登录按钮 */\n\t.wx-login-btn-container {\n\t\tmargin-bottom: 20px;\n\t}\n\t\n\t.wx-login-btn {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: #07C160;\n\t\tcolor: #fff;\n\t\theight: 50px;\n\t\tborder-radius: 25px;\n\t\tfont-size: 16px;\n\t\tborder: none;\n\t\tbox-shadow: 0 4px 10px rgba(7, 193, 96, 0.3);\n\t\t\n\t\t&.phone-btn {\n\t\t\tbackground-color: #07C160 !important;\n\t\t}\n\t}\n\t\n\t.wx-icon {\n\t\twidth: 24px;\n\t\theight: 24px;\n\t\tmargin-right: 8px;\n\t}\n\t\n\t.btn-text {\n\t\tfont-weight: 500;\n\t}\n\t\n\t/* 手机号输入区域 */\n\t.phone-input-container {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tbackground-color: #f5f7fa;\n\t\tborder-radius: 25px;\n\t\tpadding: 5px 15px;\n\t\tmargin-bottom: 20px;\n\t\tborder: 1px solid #e2e8f0;\n\t}\n\t\n\t.phone-area {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tfont-size: 16px;\n\t\tcolor: #333;\n\t\tpadding-right: 15px;\n\t\tborder-right: 1px solid #e2e8f0;\n\t\theight: 40px;\n\t}\n\t\n\t.phone-area::after {\n\t\tcontent: \"\";\n\t\tborder: 3px solid transparent;\n\t\tborder-top-color: #333;\n\t\tmargin-left: 5px;\n\t}\n\t\n\t.phone-input {\n\t\tflex: 1;\n\t\tpadding-left: 15px;\n\t\t\n\t\t:deep(.uni-easyinput__content) {\n\t\t\tbackground-color: transparent !important;\n\t\t\theight: 40px;\n\t\t}\n\t\t\n\t\t:deep(.uni-easyinput__placeholder-class) {\n\t\t\tfont-size: 14px;\n\t\t}\n\t}\n\t\n\t/* 主按钮样式 */\n\t.primary-btn {\n\t\tbackground-color: #3688FF;\n\t\tcolor: #fff;\n\t\theight: 50px;\n\t\tborder-radius: 25px;\n\t\tfont-size: 16px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-bottom: 20px;\n\t\tbox-shadow: 0 4px 10px rgba(54, 136, 255, 0.3);\n\t\tborder: none;\n\t}\n\t\n\t/* 协议样式 */\n\t.agreement-box {\n\t\tmargin: 20px 0;\n\t}\n\t\n\t/* 其他登录方式 */\n\t.other-login-methods {\n\t\tmargin-top: 30px;\n\t}\n\t\n\t.divider {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmargin-bottom: 20px;\n\t}\n\t\n\t.line {\n\t\tflex: 1;\n\t\theight: 1px;\n\t\tbackground-color: #e2e8f0;\n\t}\n\t\n\t.divider-text {\n\t\tpadding: 0 15px;\n\t\tfont-size: 14px;\n\t\tcolor: #999;\n\t}\n\t\n\t/* 响应式调整 */\n\t@media screen and (max-width: 375px) {\n\t\t.login-content {\n\t\t\tpadding: 0 20px;\n\t\t}\n\t\t\n\t\t.login-methods {\n\t\t\tpadding: 20px;\n\t\t}\n\t\t\n\t\t.logo-image {\n\t\t\twidth: 80px;\n\t\t\theight: 80px;\n\t\t}\n\t\t\n\t\t\n\t\t.welcome-text {\n\t\t\tfont-size: 20px;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login-withoutpwd.vue?vue&type=style&index=0&id=3d746ff5&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login-withoutpwd.vue?vue&type=style&index=0&id=3d746ff5&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558448670\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
{"version": 3, "sources": ["webpack:///D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?137e", "webpack:///D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?6da2", "webpack:///D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?5d3c", "webpack:///D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?305e", "uni-app:///pages/honor_pkg/avatar-picker/avatar-picker.vue", "webpack:///D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?e266", "webpack:///D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?db7c"], "names": ["maxSize", "timeout", "validTypes", "compressQuality", "name", "components", "PEmptyState", "props", "value", "type", "default", "label", "showActions", "autoUpload", "customPath", "sizeLimit", "data", "uploading", "uploadProgress", "uploadError", "avatarUrl", "uploadTimer", "currentFile", "uploadRes", "historyAvatars", "batchList", "recentUserName", "defaultAvatar", "computed", "displayAvatar", "watch", "immediate", "handler", "created", "methods", "loadHistoryAvatars", "uniCloud", "result", "console", "onUploadSuccess", "handleError", "upload", "compress", "select", "size", "format", "uni", "title", "icon", "error", "handleSelectImage", "options", "count", "sizeType", "sourceType", "res", "file", "filePath", "message", "path", "retryUpload", "previewAvatar", "urls", "confirmDelete", "handleDelete", "fileID", "fileList", "validateImageFormat", "formatFileSize", "formatDate", "openHistorySelect", "closeHistorySelect", "selectHistoryAvatar", "url", "cloudPath", "uploadAvatar", "uploadResult", "tempFileURL", "openBatchUpload", "userName", "saving", "closeBatchUpload", "addBatchItem", "removeBatchItem", "selectImage", "item", "saveAvat<PERSON>", "duration", "uploadUtils", "createTime", "clearAvatar", "content", "success", "mask", "db", "avatarCollection", "where", "get", "record", "avatarR<PERSON>ord", "_id"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,gWAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAAumB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC0K3nB;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAGA;AACA;EACAA;EAAA;EACAC;EAAA;EACAC;EACAC;AACA;AAAA,eAEA;EACAC;EAEAC;IACAC;EACA;EAEAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAT;MACAQ;MACAC;IACA;EACA;EAEAM;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;EACA;EAEAC;IACAtB;MACAuB;MACAC;QACA;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBACAhC;kBACAY;gBACA;cAAA;gBAAA;gBAHAqB;gBAKA;kBACA;gBACA;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MAEAC;QACAC;QACAC;MACA;MAEA;QAAAvC;QAAAwC;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGAC;kBACAC;kBACAC;kBAAA;kBACAC;gBACA;gBAAA;gBAAA,OAEAR;cAAA;gBAAAS;gBAAA,4BAEAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACAC,iCAEA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBACA;kBACAE;gBACA;gBAAA;cAAA;gBAIA;kBAAAC;kBAAAf;gBAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAN;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACAf;QACAgB;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBACAC,2BAEA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;gBAAA,OAIA7B;kBACAhC;kBACAY;oBACAkD;kBACA;gBACA;cAAA;gBAAA;gBALA7B;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBAAA,MACA;cAAA;gBAIA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEAQ;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAV;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA6B;MAAA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;QACAC;QACAC;MACA;MACA;MACA5B;QACAC;QACAC;MACA;IACA;IAEA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;;gBAEA;gBAAA;gBAAA,OACAvC;kBACAqB;kBACAiB;gBACA;cAAA;gBAHAE;gBAAA,IAKAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAIAxC;kBACA8B;gBACA;cAAA;gBAAA;gBAFAA;gBAAA,IAIAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGAW,uCAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;gBACA;gBACA;gBAEA/B;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAV;gBACA;gBACAQ;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8B;MACA;QAAAC;QAAAN;QAAAO;MAAA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QAAAH;QAAAN;QAAAO;MAAA;IACA;IAEA;IACAG;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAtC;kBACAM;kBACAC;kBACAC;gBACA;cAAA;gBAJAC;gBAMA;kBACA8B;kBACAA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA/C;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAD,gCAEA;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAvC;kBACAC;kBACAC;kBACAuC;gBACA;gBAAA;cAAA;gBAAA,IAKAF;kBAAA;kBAAA;gBAAA;gBACAvC;kBACAC;kBACAC;kBACAuC;gBACA;gBAAA;cAAA;gBAAA,KAKAF;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAA;gBAAA;gBAAA;gBAAA,OAIAG;cAAA;gBAAAZ;gBAAA;gBAAA,OAGAxC;kBACAhC;kBACAY;oBACAyD;oBACAM;oBACAU;kBACA;gBACA;cAAA;gBAAA;gBAPApD;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACAS;kBACAC;kBACAC;gBACA;gBACA;gBACAqC;gBACAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAvC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEAqC;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAK;MACA;MACA;MACA;MACA;MACA;IACA;EAAA,qFAKA;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA5C;gBACAC;gBACA4C;gBACAC;kBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA,KACArC;8BAAA;8BAAA;4BAAA;4BAAA;4BAAA,OACA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CAEA;kBAAA;oBAAA;kBAAA;kBAAA;gBAAA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA,oFAKA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEAT;gBACAC;gBACA8C;cACA;;cAEA;cACAC;cACAC;cAAA;cAAA,OACAA,iBACAC;gBACAvB;cACA,GACAwB;YAAA;cAJAC;cAAA,MAMAA;gBAAA;gBAAA;cAAA;cACAC,+BAEA;cAAA,KACAA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA/D;gBACA8B;cACA;YAAA;cAAA;cAAA,OAIA6B;YAAA;cAGA;cACA;cACA;;cAEA;cAAA;cAAA,OACA;YAAA;cAEAjD;gBACAC;gBACAC;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAV;cACAQ;gBACAC;gBACAC;cACA;YAAA;cAAA;cAEAF;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA,kGAKAuC;IAAA;IACAvC;MACAC;MACA4C;MACAC;QAAA;UAAA;YAAA;cAAA;gBAAA;kBAAA,KACArC;oBAAA;oBAAA;kBAAA;kBAAA;kBAAA,OACA;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CAEA;QAAA;UAAA;QAAA;QAAA;MAAA;IACA;EACA,gGAKA8B;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA,MACA;gBAAA;gBAAA;cAAA;cACAvC;gBACAC;gBACAC;cACA;cAAA;YAAA;cAAA;cAKAF;gBACAC;gBACA8C;cACA;;cAEA;cAAA;cAAA,OACAzD;gBACAhC;gBACAY;kBACAoF;gBACA;cACA;YAAA;cAAA;cALA/D;cAAA,MAOAA;gBAAA;gBAAA;cAAA;cACA;cACA;gBACA;gBACA;cACA;;cAEA;cAAA;cAAA,OACA;YAAA;cAEAS;gBACAC;gBACAC;cACA;cAAA;cAAA;YAAA;cAAA,MAEA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAGAV;cACAQ;gBACAC;gBACAC;cACA;YAAA;cAAA;cAEAF;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA,4FAEA2B;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEArC;gBACAhC;gBACAY;kBACAyD;kBACAR;kBACAc;gBACA;cACA;YAAA;cAAA;cAPA1C;cAAA,MASAA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAEAS;gBACAC;gBACAC;cACA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAGAV;cACAQ;gBACAC;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AAEA;AAAA,2B;;;;;;;;;;;;;ACnyBA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,gpCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/honor_pkg/avatar-picker/avatar-picker.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./avatar-picker.vue?vue&type=template&id=7979a2f4&scoped=true&\"\nvar renderjs\nimport script from \"./avatar-picker.vue?vue&type=script&lang=js&\"\nexport * from \"./avatar-picker.vue?vue&type=script&lang=js&\"\nimport style0 from \"./avatar-picker.vue?vue&type=style&index=0&id=7979a2f4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7979a2f4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/honor_pkg/avatar-picker/avatar-picker.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./avatar-picker.vue?vue&type=template&id=7979a2f4&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog\" */ \"@/uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.historyAvatars.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.$refs.deleteConfirm.close()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./avatar-picker.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./avatar-picker.vue?vue&type=script&lang=js&\"", "<!-- 头像选择器组件 -->\n<template>\n  <view class=\"avatar-picker-component\">\n  <view class=\"avatar-picker\">\n      <!-- 头像预览区域 -->\n      <view class=\"avatar-card\">\n        <view class=\"avatar-preview\">\n          <image \n            :src=\"displayAvatar\" \n            class=\"avatar-image\"\n            mode=\"aspectFill\"\n          />\n          <!-- 清除按钮 -->\n                <view \n            v-if=\"avatarUrl && avatarUrl !== defaultAvatar && !uploading\" \n            class=\"clear-btn\"\n            @click.stop=\"clearAvatar\"\n          >\n            <uni-icons type=\"close\" size=\"14\" color=\"#FFFFFF\"></uni-icons>\n              </view>\n        </view>\n        <view class=\"avatar-actions\">\n          <button \n            class=\"action-btn upload-btn\" \n            @click=\"openBatchUpload\"\n          >\n            上传头像\n          </button>\n          <button \n            class=\"action-btn select-btn\" \n            @click=\"openHistorySelect\"\n          >\n            选择头像\n          </button>\n        </view>\n            </view>\n            \n      <!-- 删除确认弹窗 -->\n      <uni-popup ref=\"deleteConfirm\" type=\"dialog\">\n        <uni-popup-dialog\n          type=\"warning\"\n          title=\"删除确认\"\n          content=\"确定要删除当前头像吗？删除后将恢复为默认头像。\"\n          :before-close=\"false\"\n          @confirm=\"handleDelete\"\n          @close=\"$refs.deleteConfirm.close()\"\n        />\n      </uni-popup>\n\n      <!-- 历史头像选择弹窗 -->\n      <uni-popup ref=\"historyPopup\" type=\"center\">\n        <view class=\"history-popup\">\n          <view class=\"popup-header\">\n            <text class=\"popup-title\">选择头像</text>\n            <view class=\"popup-close\" @click=\"closeHistorySelect\">\n              <uni-icons type=\"close\" size=\"16\" color=\"#666666\"></uni-icons>\n            </view>\n          </view>\n          \n          <scroll-view scroll-y class=\"history-list\">\n            <template v-if=\"historyAvatars.length > 0\">\n              <view class=\"history-grid\">\n          <view \n                  v-for=\"(item, index) in historyAvatars\" \n                  :key=\"index\"\n                  class=\"history-item\"\n                  :class=\"{ 'is-selected': item.url === avatarUrl }\"\n                >\n                  <view class=\"item-container\">\n                    <view class=\"avatar-wrapper\">\n                      <image \n                        :src=\"item.url\" \n                        class=\"history-avatar\"\n                        mode=\"aspectFill\"\n                        @click=\"selectHistoryAvatar(item)\"\n                      />\n                      <view \n                        class=\"delete-btn\"\n                        @click.stop=\"confirmDeleteHistory(item)\"\n                       >\n                        <uni-icons type=\"close\" size=\"14\" color=\"#FFFFFF\"></uni-icons>\n              </view>\n        </view>\n                    <text class=\"history-name\">{{ item.userName || '未命名' }}</text>\n                  </view>\n                </view>\n              </view>\n            </template>\n            <template v-else>\n              <p-empty-state\n                image=\"/static/empty/empty_data.png\"\n                text=\"暂无历史头像\"\n                tips=\"上传新头像后会保存在这里\"\n                class=\"history-empty\"\n              ></p-empty-state>\n            </template>\n          </scroll-view>\n        </view>\n      </uni-popup>\n\n      <!-- 上传头像弹窗 -->\n      <uni-popup ref=\"batchUploadPopup\" type=\"center\">\n        <view class=\"batch-upload-popup\">\n          <view class=\"popup-header\">\n            <text class=\"popup-title\">上传头像</text>\n            <view class=\"popup-close\" @click=\"closeBatchUpload\">\n              <uni-icons type=\"close\" size=\"14\" color=\"#666666\"></uni-icons>\n            </view>\n          </view>\n          \n          <scroll-view scroll-y class=\"batch-list\">\n            <!-- 表格头部 -->\n            <view class=\"batch-table-header\">\n              <text class=\"col-name\">姓名</text>\n              <text class=\"col-avatar\">头像</text>\n              <text class=\"col-action\">操作</text>\n            </view>\n            \n            <!-- 表格内容 -->\n          <view \n              v-for=\"(item, index) in batchList\" \n              :key=\"index\"\n              class=\"batch-table-row\"\n            >\n              <view class=\"col-name\">\n                <uni-easyinput\n                  v-model=\"item.userName\"\n                  placeholder=\"请输入姓名\"\n                  :clearable=\"true\"\n                />\n          </view>\n              <view class=\"col-avatar\">\n                <view class=\"upload-area\" @click=\"selectImage(index)\">\n                  <image \n                    v-if=\"item.url\"\n                    :src=\"item.url\"\n                    class=\"preview-image\"\n                    mode=\"aspectFill\"\n                  />\n                  <view v-else class=\"upload-placeholder\">\n                    <uni-icons type=\"camera-filled\" size=\"20\" color=\"#999999\"></uni-icons>\n        </view>\n      </view>\n              </view>\n              <view class=\"col-action\">\n        <button \n                  class=\"save-btn\" \n                  :class=\"{ 'btn-disabled': !item.userName || !item.url || item.saving }\"\n                  @click=\"saveAvatar(index)\"\n                >\n                  {{ item.saving ? '保存中...' : '保存' }}\n        </button>\n              </view>\n            </view>\n          </scroll-view>\n          \n          <view class=\"popup-footer\">\n            <button class=\"add-btn\" @click=\"addBatchItem\">\n              <uni-icons type=\"plus\" size=\"14\" color=\"#FFFFFF\"></uni-icons>\n              <text>添加一行</text>\n        </button>\n            <button class=\"close-btn\" @click=\"closeBatchUpload\">关闭</button>\n      </view>\n    </view>\n    </uni-popup>\n    </view>\n  </view>\n</template>\n\n<script>\nimport uploadUtils from '@/utils/upload-utils.js'\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue'\n\n// 配置常量\nconst CONFIG = {\n  maxSize: 10 * 1024 * 1024, // 10MB\n  timeout: 30000, // 30s\n  validTypes: ['jpg', 'jpeg', 'png', 'webp', 'JPG', 'JPEG', 'PNG', 'WEBP'],\n  compressQuality: 0.8\n}\n\nexport default {\n  name: 'AvatarPicker',\n  \n  components: {\n    PEmptyState\n  },\n  \n  props: {\n    value: {\n      type: String,\n      default: ''\n    },\n    label: {\n      type: String,\n      default: '头像'\n    },\n    showActions: {\n      type: Boolean,\n      default: true\n    },\n    autoUpload: {\n      type: Boolean,\n      default: true\n    },\n    customPath: {\n      type: String,\n      default: ''\n    },\n    sizeLimit: {\n      type: Number,\n      default: CONFIG.maxSize\n    },\n    timeout: {\n      type: Number,\n      default: CONFIG.timeout\n    }\n  },\n  \n  data() {\n    return {\n      uploading: false,\n      uploadProgress: 0,\n      uploadError: false,\n      avatarUrl: '',\n      uploadTimer: null,\n      currentFile: null,\n      uploadRes: null, // 添加上传结果缓存\n      historyAvatars: [], // 历史头像列表\n      batchList: [], // 批量上传列表\n      recentUserName: '', // 添加最近输入的用户名\n      defaultAvatar: '/static/user/default-avatar.png',\n    }\n  },\n  \n  computed: {\n    displayAvatar() {\n      return this.avatarUrl || this.defaultAvatar;\n    }\n  },\n  \n  watch: {\n    value: {\n      immediate: true,\n      handler(newVal) {\n        this.avatarUrl = newVal\n      }\n    }\n  },\n  \n  created() {\n    this.loadHistoryAvatars()\n  },\n  \n  methods: {\n    // 加载历史头像\n    async loadHistoryAvatars() {\n      try {\n        const { result } = await uniCloud.callFunction({\n          name: 'history-avatar-get',\n          data: {}\n        })\n        \n        if (result.code === 0) {\n          this.historyAvatars = result.data\n        } else {\n          console.error('获取历史头像失败:', result.message)\n        }\n      } catch (error) {\n        console.error('获取历史头像失败:', error)\n      }\n    },\n    \n    // 上传成功后刷新历史列表\n    async onUploadSuccess(result) {\n      await this.loadHistoryAvatars()\n    },\n    \n    // 错误处理\n    handleError(error, type = 'upload') {\n      const messages = {\n        upload: '上传失败',\n        compress: '压缩失败',\n        select: '选择失败',\n        size: '文件过大',\n        format: '格式不支持'\n      }\n      \n      this.uploadError = type === 'upload'\n      \n      uni.showToast({\n        title: error.message || messages[type],\n        icon: 'none'\n      })\n      \n      this.$emit('error', { type, error })\n    },\n    \n    // 选择图片\n    async handleSelectImage() {\n      if (this.uploading) return\n      \n      try {\n        const options = {\n          count: 1,\n          sizeType: ['compressed'], // 使用压缩\n          sourceType: ['album'], // 只使用相册选择\n        }\n        \n        const res = await uni.chooseImage(options)\n        \n        if (res.tempFilePaths?.[0]) {\n          const file = res.tempFiles[0]\n          const filePath = res.tempFilePaths[0]\n          \n          // 验证文件大小\n          if (file.size > this.sizeLimit) {\n            this.handleError({ \n              message: `文件大小${this.formatFileSize(file.size)}超过限制${this.formatFileSize(this.sizeLimit)}` \n            }, 'size')\n            return\n          }\n          \n          this.currentFile = { path: filePath, size: file.size }\n          \n          if (this.autoUpload) {\n            await this.uploadAvatar()\n          }\n        }\n      } catch (error) {\n        console.error('选择图片失败:', error)\n        this.handleError(error, 'select')\n      }\n    },\n    \n    // 重试上传\n    async retryUpload() {\n      this.uploadError = false\n      await this.uploadAvatar()\n    },\n    \n    // 预览头像\n    previewAvatar() {\n      if (!this.avatarUrl) return\n      uni.previewImage({\n        urls: [this.avatarUrl]\n      })\n    },\n    \n    // 确认删除\n    confirmDelete() {\n      this.$refs.deleteConfirm.open()\n    },\n    \n    // 删除头像\n    async handleDelete() {\n      try {\n        // 如果有云端图片URL，先删除云端文件\n        if (this.avatarUrl) {\n          let fileID = this.avatarUrl\n          \n          // 如果是http开头的临时链接，尝试从uploadRes中获取cloudPath\n          if (fileID.startsWith('http') && this.uploadRes && this.uploadRes.cloudPath) {\n            fileID = this.uploadRes.cloudPath\n          }\n          \n          try {\n            // 调用云函数删除文件\n            const { result } = await uniCloud.callFunction({\n              name: 'delete-file',\n              data: {\n                fileList: [fileID]\n              }\n            })\n            \n            if (result.code !== 0) {\n              throw new Error(result.message || '云端文件删除失败')\n            }\n          } catch (error) {\n            console.error('云端文件删除失败:', error)\n            throw new Error(error.message || '云端文件删除失败')\n          }\n        }\n\n        // 清除本地状态\n        this.avatarUrl = ''\n        this.currentFile = null\n        this.uploadRes = null // 清除上传结果缓存\n        this.$emit('input', '')\n        this.$emit('delete')\n        this.$refs.deleteConfirm.close()\n        \n        uni.showToast({\n          title: '删除成功',\n          icon: 'success'\n        })\n      } catch (error) {\n        console.error('删除失败:', error)\n        this.handleError(error, 'delete')\n        // 关闭确认弹窗\n        this.$refs.deleteConfirm.close()\n      }\n    },\n    \n    // 验证图片格式\n    validateImageFormat(filePath) {\n      if (!filePath) return false\n      \n      // 提取文件扩展名并转小写\n      const ext = filePath.split('.').pop()?.toLowerCase()\n      if (!ext) return false\n      \n      // 转小写后比较\n      return CONFIG.validTypes.map(type => type.toLowerCase()).includes(ext)\n    },\n\n    // 格式化文件大小\n    formatFileSize(bytes) {\n      if (bytes === 0) return '0 Bytes';\n      const k = 1024;\n      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    },\n\n    // 格式化日期\n    formatDate(timestamp) {\n      const date = new Date(timestamp);\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\n    },\n\n    // 打开历史头像选择弹窗\n    openHistorySelect() {\n      this.$refs.historyPopup.open();\n    },\n\n    // 关闭历史头像选择弹窗\n    closeHistorySelect() {\n      this.$refs.historyPopup.close();\n    },\n\n    // 选择历史头像\n    selectHistoryAvatar(item) {\n      this.avatarUrl = item.url;\n      // 修正事件触发，传递正确的数据结构\n      this.$emit('input', item.url);\n      this.$emit('change', {\n        url: item.url,\n        cloudPath: item.fileID\n      });\n      this.closeHistorySelect();\n      uni.showToast({\n        title: '已选择头像',\n        icon: 'success'\n      })\n    },\n\n    // 修改上传成功的处理\n    async uploadAvatar() {\n      if (!this.currentFile) return\n      \n      try {\n        this.uploading = true\n        this.uploadError = false\n        \n        // 上传文件到云存储\n        const uploadResult = await uniCloud.uploadFile({\n          filePath: this.currentFile.path,\n          cloudPath: `avatars/${Date.now()}-${Math.random().toString(36).slice(2)}.${this.currentFile.path.split('.').pop()}`\n        })\n        \n        if (!uploadResult.fileID) {\n          throw new Error('上传失败')\n        }\n        \n        // 获取临时链接\n        const { fileList } = await uniCloud.getTempFileURL({\n          fileList: [uploadResult.fileID]\n        })\n        \n        if (!fileList?.[0]?.tempFileURL) {\n          throw new Error('获取临时链接失败')\n        }\n        \n        const tempFileURL = fileList[0].tempFileURL\n        \n        // 保存头像信息\n        await this.saveHistoryAvatar(tempFileURL, uploadResult.fileID)\n        \n        // 更新当前头像\n        this.avatarUrl = tempFileURL\n        this.$emit('input', tempFileURL)\n        this.$emit('change', tempFileURL)\n        \n        uni.showToast({\n          title: '上传成功',\n          icon: 'success'\n        })\n      } catch (e) {\n        console.error('上传头像失败:', e)\n        this.uploadError = true\n        uni.showToast({\n          title: e.message || '上传失败',\n          icon: 'none'\n        })\n      } finally {\n        this.uploading = false\n        this.currentFile = null\n      }\n    },\n\n    // 打开批量上传弹窗\n    openBatchUpload() {\n      this.batchList = [{ userName: '', url: '', saving: false }]\n      this.$refs.batchUploadPopup.open()\n    },\n\n    // 关闭批量上传弹窗\n    closeBatchUpload() {\n      this.$refs.batchUploadPopup.close()\n    },\n\n    // 添加一项\n    addBatchItem() {\n      this.batchList.push({ userName: '', url: '', saving: false })\n    },\n\n    // 移除一项\n    removeBatchItem(index) {\n      this.batchList.splice(index, 1)\n      if (this.batchList.length === 0) {\n        this.addBatchItem()\n      }\n    },\n\n    // 选择图片\n    async selectImage(index) {\n      try {\n        const res = await uni.chooseImage({\n          count: 1,\n          sizeType: ['compressed'],\n          sourceType: ['album']\n        })\n        \n        if (res.tempFilePaths?.[0]) {\n          const item = this.batchList[index]\n          item.url = res.tempFilePaths[0]\n          item.file = res.tempFiles[0]\n        }\n      } catch (error) {\n        console.error('选择图片失败:', error)\n      }\n    },\n\n    // 保存头像\n    async saveAvatar(index) {\n      const item = this.batchList[index]\n      \n      // 验证名字\n      if (!item.userName) {\n        uni.showToast({\n          title: '请输入姓名',\n          icon: 'none',\n          duration: 2000\n        })\n        return\n      }\n      \n      // 验证头像\n      if (!item.url) {\n        uni.showToast({\n          title: '请选择头像',\n          icon: 'none',\n          duration: 2000\n        })\n        return\n      }\n      \n      // 避免重复保存\n      if (item.saving) return\n      \n      item.saving = true\n      \n      try {\n        // 上传图片\n        const uploadResult = await uploadUtils.uploadAvatar(item.url)\n        \n        // 保存记录\n        const { result } = await uniCloud.callFunction({\n          name: 'history-avatar-save',\n          data: {\n            url: uploadResult.cloudPath,\n            userName: item.userName,\n            createTime: Date.now()\n          }\n        })\n        \n        if (result.code === 0) {\n          uni.showToast({\n            title: result.message || '保存成功',\n            icon: 'success'\n          })\n          // 清空当前项\n          item.userName = ''\n          item.url = ''\n          // 刷新历史列表\n          await this.loadHistoryAvatars()\n        } else {\n          throw new Error(result.message)\n        }\n      } catch (error) {\n        uni.showToast({\n          title: error.message || '保存失败',\n          icon: 'none'\n        })\n      } finally {\n        item.saving = false\n      }\n    },\n\n    /**\n     * 清除当前头像\n     */\n    clearAvatar() {\n      this.avatarUrl = this.defaultAvatar;\n      // 使用正确的事件名称\n      this.$emit('update:modelValue', '');\n      // 触发change事件通知父组件\n      this.$emit('change', '');\n    },\n\n    /**\n     * 确认删除头像\n     */\n    async confirmDelete() {\n      uni.showModal({\n        title: '删除确认',\n        content: '确定要删除当前头像吗？删除后将恢复为默认头像。',\n        success: async (res) => {\n          if (res.confirm) {\n            await this.deleteAvatar();\n          }\n        }\n      });\n    },\n\n    /**\n     * 删除头像\n     */\n    async deleteAvatar() {\n      try {\n        uni.showLoading({\n          title: '删除中...',\n          mask: true\n        });\n\n        // 获取当前头像记录\n        const db = uniCloud.database();\n        const avatarCollection = db.collection('history-avatar');\n        const record = await avatarCollection\n          .where({\n            url: this.avatarUrl\n          })\n          .get();\n\n        if (record.data && record.data.length > 0) {\n          const avatarRecord = record.data[0];\n          \n          // 删除云存储文件\n          if (avatarRecord.fileID) {\n            await uniCloud.deleteFile({\n              fileList: [avatarRecord.fileID]\n            });\n          }\n\n          // 删除数据库记录\n          await avatarCollection.doc(avatarRecord._id).remove();\n        }\n\n        // 重置头像为默认头像\n        this.avatarUrl = this.value || '/static/user/default-avatar.png'; // Use value prop as fallback\n        this.$emit('input', '');\n        \n        // 刷新历史头像列表\n        await this.loadHistoryAvatars();\n\n        uni.showToast({\n          title: '删除成功',\n          icon: 'success'\n        });\n      } catch (e) {\n        console.error('删除头像失败:', e);\n        uni.showToast({\n          title: '删除失败',\n          icon: 'error'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n\n    /**\n     * 确认删除历史头像\n     */\n    confirmDeleteHistory(item) {\n      uni.showModal({\n        title: '删除确认',\n        content: '确定要删除这个头像吗？此操作不可恢复。',\n        success: async (res) => {\n          if (res.confirm) {\n            await this.deleteHistoryAvatar(item);\n          }\n        }\n      });\n    },\n\n    /**\n     * 删除历史头像\n     */\n    async deleteHistoryAvatar(item) {\n      if (!item || !item._id) {\n        uni.showToast({\n          title: '无效的记录',\n          icon: 'none'\n        });\n        return;\n      }\n\n      try {\n        uni.showLoading({\n          title: '删除中...',\n          mask: true\n        });\n\n        // 调用云函数删除历史头像\n        const { result } = await uniCloud.callFunction({\n          name: 'history-avatar-delete',\n          data: {\n            _id: item._id\n          }\n        });\n\n        if (result.code === 0) {\n          // 如果删除的是当前选中的头像，重置为默认头像\n          if (item.url === this.avatarUrl) {\n            this.avatarUrl = this.value || '/static/user/default-avatar.png';\n            this.$emit('input', '');\n          }\n\n          // 刷新历史头像列表\n          await this.loadHistoryAvatars();\n\n          uni.showToast({\n            title: '删除成功',\n            icon: 'success'\n          });\n        } else {\n          throw new Error(result.msg || '删除失败');\n        }\n      } catch (e) {\n        console.error('删除历史头像失败:', e);\n        uni.showToast({\n          title: e.message || '删除失败',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n\n    async saveHistoryAvatar(url, fileID) {\n      try {\n        const { result } = await uniCloud.callFunction({\n          name: 'history-avatar-save',\n          data: {\n            url,\n            fileID,\n            userName: this.userName || (uni.getStorageSync('uni-id-pages-userInfo') || {}).username || '未命名'\n          }\n        })\n        \n        if (result.code === 0) {\n          await this.loadHistoryAvatars()\n        } else {\n          uni.showToast({\n            title: result.msg,\n            icon: 'none'\n          })\n        }\n      } catch (e) {\n        console.error('保存历史头像失败:', e)\n        uni.showToast({\n          title: '保存历史头像失败',\n          icon: 'none'\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './_variables.scss';\n@import './_mixins.scss';\n\n.avatar-picker-component {\n  width: 100%;\n  position: relative;\n}\n\n.avatar-picker {\n  width: 100%;\n  position: relative;\n}\n\n.avatar-card {\n  background: #FFFFFF;\n  border-radius: 8rpx;\n  padding: 24rpx;\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.avatar-preview {\n  position: relative;\n  width: 120rpx;\n  height: 120rpx;\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n    border-radius: 50%;\n    overflow: hidden;\n    background: #ffffff;\n    box-shadow: inset 0 0 0 1rpx rgba(0, 0, 0, 0.1);\n    \n    &[src*=\"default-avatar\"] {\n      padding: 24rpx;\n      background: linear-gradient(135deg, #e6f3ff 0%, #ffffff 100%);\n      box-sizing: border-box;\n    }\n  }\n\n  .clear-btn {\n    position: absolute;\n        top: -8rpx;\n        right: -8rpx;\n        width: 32rpx;\n        height: 32rpx;\n        border-radius: 50%;\n        background: #ef4444;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 0;\n        line-height: 1;\n  \n  &:active {\n    background: #dc2626;\n  }\n}\n}\n\n.avatar-actions {\n  display: flex;\n  gap: 16rpx;\n}\n\n.action-btn {\n  flex: 1;\n  height: 72rpx;\n  line-height: 72rpx;\n  font-size: 28rpx;\n  border-radius: 4rpx;\n  border: none;\n  padding: 0;\n  margin: 0;\n  \n  &::after {\n    display: none;\n  }\n  \n  &.upload-btn {\n    background: #1890ff;\n    color: #FFFFFF;\n  }\n  \n  &.select-btn {\n    background: #FFFFFF;\n    color: #1890ff;\n    border: 1rpx solid #1890ff;\n  }\n}\n\n.history-popup {\n  background: #FFFFFF;\n  border-radius: 12rpx;\n  width: 640rpx;\n  min-height: 200rpx;\n  max-height: 720rpx;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.popup-header {\n  padding: 20rpx 24rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  border-bottom: 1rpx solid #eee;\n  flex-shrink: 0; // 防止头部压缩\n  \n  .popup-title {\n    font-size: 28rpx;\n    font-weight: 500;\n    color: #333;\n  }\n  \n  .popup-close {\n    padding: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n    border-radius: 50%;\n  \n  &:active {\n      background: #f5f5f5;\n    }\n  }\n}\n\n.history-list {\n  flex: 1;\n  width: 100%;\n  box-sizing: border-box;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n\n.history-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 16rpx;\n  padding: 16rpx;\n  box-sizing: border-box;\n}\n\n.history-item {\n  width: 100%;\n  box-sizing: border-box;\n  \n  .item-container {\n    background: #FFFFFF;\n    border-radius: 12rpx;\n    border: 1rpx solid #EEEEEE;\n    padding: 12rpx;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 8rpx;\n    transition: all 0.2s ease;\n    width: 100%;\n    box-sizing: border-box;\n    \n    &:active {\n      background: #f5f5f5;\n    }\n  }\n  \n  &.is-selected {\n    .item-container {\n      background: #e6f7ff;\n      border-color: #1890ff;\n      \n      .history-name {\n        color: #1890ff;\n      }\n    }\n  }\n}\n\n.avatar-wrapper {\n  position: relative;\n  width: 100rpx;\n  height: 100rpx;\n  \n  .history-avatar {\n    width: 100%;\n    height: 100%;\n    border-radius: 50%;\n    object-fit: cover;\n    background: #ffffff;\n    border: 2rpx solid #f0f0f0;\n    box-sizing: border-box;\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n  }\n  \n  .delete-btn {\n    position: absolute;\n    top: -6rpx;\n    right: -6rpx;\n    width: 28rpx;\n    height: 28rpx;\n    border-radius: 50%;\n    background: #ff4d4f;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n    z-index: 10;\n    border: 2rpx solid #ffffff;\n    padding: 0;\n  }\n}\n\n.history-name {\n  font-size: 24rpx;\n  color: #333333;\n  width: 100%;\n  text-align: center;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 0 4rpx;\n  box-sizing: border-box;\n}\n\n// 批量上传弹窗也统一宽度\n.batch-upload-popup {\n  background: #FFFFFF;\n  border-radius: 12rpx;\n  width: 640rpx;\n  max-height: 800rpx;\n  display: flex;\n  flex-direction: column;\n  \n  .batch-list {\n  flex: 1;\n    min-height: 400rpx;\n    max-height: 600rpx;\n    \n    .batch-table-header,\n    .batch-table-row {\n      display: flex;\n      align-items: center;\n      padding: 16rpx 24rpx;\n      border-bottom: 1rpx solid #eee;\n      \n      .col-name {\n        flex: 2;\n        padding-right: 16rpx;\n      }\n      \n      .col-avatar {\n        flex: 1;\n        padding: 0 16rpx;\n      }\n      \n      .col-action {\n        flex: 1;\n        padding-left: 16rpx;\n        text-align: center;\n      }\n    }\n    \n    .batch-table-header {\n      font-size: 28rpx;\n      color: #666666;\n      background: #f8f8f8;\n    }\n    \n    .batch-table-row {\n      .upload-area {\n        width: 70rpx;\n        height: 70rpx;\n        background: #f8f8f8;\n        border-radius: 50%;  // 改为圆形\n  display: flex;\n  align-items: center;\n  justify-content: center;\n        overflow: hidden;\n        \n        .preview-image {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n          border-radius: 50%;  // 确保图片也是圆形\n        }\n        \n        .upload-placeholder {\n          width: 100%;\n          height: 100%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          background: #f5f5f5;\n          border-radius: 50%;  // 确保占位符也是圆形\n    \n    .uni-icons {\n            font-size: 24rpx;\n          }\n        }\n      }\n      \n      .save-btn {\n        width: 120rpx;\n        height: 56rpx;\n        line-height: 56rpx;\n        font-size: 24rpx;\n        background: #1890ff;\n    color: #FFFFFF;\n        border-radius: 4rpx;\n        padding: 0;\n        \n        &.btn-disabled {\n          background: #d9d9d9;\n        }\n      }\n    }\n  }\n  \n  .popup-footer {\n    padding: 24rpx;\n    display: flex;\n    gap: 16rpx;\n    border-top: 1rpx solid #eee;\n    \n    button {\n      flex: 1;\n      height: 72rpx;\n      line-height: 72rpx;\n      font-size: 28rpx;\n      border-radius: 4rpx;\n      \n      &::after {\n        display: none;\n      }\n    }\n    \n    .add-btn {\n      background: #1890ff;\n      color: #FFFFFF;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 8rpx;\n    }\n    \n    .close-btn {\n      background: #f5f5f5;\n      color: #666666;\n    }\n  }\n}\n\n.history-empty {\n  padding: 60rpx 0;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./avatar-picker.vue?vue&type=style&index=0&id=7979a2f4&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./avatar-picker.vue?vue&type=style&index=0&id=7979a2f4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558453794\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
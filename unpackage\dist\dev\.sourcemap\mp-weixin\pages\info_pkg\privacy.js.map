{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/info_pkg/privacy.vue?40b4", "webpack:///D:/Xwzc/pages/info_pkg/privacy.vue?2abe", "webpack:///D:/Xwzc/pages/info_pkg/privacy.vue?d2b9", "webpack:///D:/Xwzc/pages/info_pkg/privacy.vue?67b4", "uni-app:///pages/info_pkg/privacy.vue", "webpack:///D:/Xwzc/pages/info_pkg/privacy.vue?b667", "webpack:///D:/Xwzc/pages/info_pkg/privacy.vue?29f0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "onLoad", "uni", "title", "methods"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAimB,CAAgB,2nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuGrnB;EACAC;IACA,QAEA;EACA;EACAC;IACAC;MACAC;IACA;EACA;EACAC,UAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACrHA;AAAA;AAAA;AAAA;AAAm3B,CAAgB,o3BAAG,EAAC,C;;;;;;;;;;;ACAv4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/info_pkg/privacy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/info_pkg/privacy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./privacy.vue?vue&type=template&id=10f176ad&\"\nvar renderjs\nimport script from \"./privacy.vue?vue&type=script&lang=js&\"\nexport * from \"./privacy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./privacy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/info_pkg/privacy.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./privacy.vue?vue&type=template&id=10f176ad&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./privacy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./privacy.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"privacy-container\">\n\t\t<view class=\"privacy-header\">\n\t\t\t<text class=\"header-title\">隐私政策</text>\n\t\t\t<text class=\"header-date\">更新日期：2025年5月1日</text>\n\t\t</view>\n\t\t\n\t\t<view class=\"privacy-content\">\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">引言</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t本隐私政策适用于您使用\"株水小智\"微信小程序（以下简称\"本小程序\"）所提供的服务。我们非常重视您的个人信息和隐私保护，并会尽全力保护您的个人信息安全可靠。本隐私政策旨在向您说明我们如何收集、使用、存储和共享您的个人信息，以及您享有的相关权利。\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">信息收集与使用</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-subtitle\">我们可能收集的个人信息</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t1. 位置信息：当您使用巡视打卡功能时，我们会收集您的精确位置信息，包括经纬度数据和位置精度。这些信息用于确定您是否处于巡视点位的有效范围内，计算您与巡视点位的距离，以及在地图上显示您的位置和巡视点位。\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t2. 照片：在您上传巡视记录或添加巡视点位参考图片时，我们会收集您拍摄或选择的照片。这些照片用于记录巡视情况或作为巡视点位的参考信息。\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t3. 设备信息：我们可能会收集您的设备型号、操作系统版本、设备标识符等信息，用于帮助我们优化应用性能和解决技术问题。\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">信息的存储</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t我们会将收集的信息存储在中国大陆的服务器上，并采取严格的数据安全措施保护您的个人信息。我们会在实现本政策所述目的所必需的期间内保留您的个人信息，除非法律要求或允许在更长的期间内保留这些信息。\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">信息的保护</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t我们采取各种安全技术和程序，以防信息的丢失、不当使用、未经授权阅览或披露。例如，在某些服务中，我们将利用加密技术（例如SSL）来保护您提供的个人信息。但请您理解，由于技术的限制以及可能存在的各种恶意手段，在互联网行业，即便竭尽所能加强安全措施，也不可能始终保证信息百分之百的安全。\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">您的权利</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t按照中国相关的法律、法规、标准，以及其他国家、地区的通行做法，我们保障您对自己的个人信息行使以下权利：\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t1. 访问您的个人信息\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t2. 更正不准确或不完整的信息\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t3. 删除您的个人信息\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t4. 撤回同意\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t如需行使上述权利，您可以通过小程序内提供的联系方式与我们联系。\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">隐私政策的变更</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t我们可能会不时更新本隐私政策。当我们发生重大变化时，我们会在小程序内推送通知或以其他方式通知您。请您定期查看本隐私政策，以了解我们的隐私实践及其变化。\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">联系我们</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t如果您对本隐私政策或我们的数据处理有任何疑问或投诉，请通过以下方式联系我们：\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t电子邮件：<EMAIL>\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t电话：13707335131\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tuni.setNavigationBarTitle({\n\t\t\t\ttitle: '隐私政策'\n\t\t\t});\n\t\t},\n\t\tmethods: {\n\t\t\t\n\t\t}\n\t}\n</script>\n\n<style>\n\t.privacy-container {\n\t\tpadding: 30rpx;\n\t\tbackground-color: #f8f8f8;\n\t\tmin-height: 100vh;\n\t}\n\t\n\t.privacy-header {\n\t\tmargin-bottom: 40rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.header-title {\n\t\tdisplay: block;\n\t\tfont-size: 40rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.header-date {\n\t\tdisplay: block;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.section {\n\t\tmargin-bottom: 40rpx;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 30rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.section-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t\tborder-left: 8rpx solid #1976D2;\n\t\tpadding-left: 20rpx;\n\t}\n\t\n\t.section-content {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tline-height: 1.6;\n\t}\n\t\n\t.section-subtitle {\n\t\tdisplay: block;\n\t\tfont-size: 30rpx;\n\t\tfont-weight: bold;\n\t\tmargin: 20rpx 0 10rpx;\n\t\tcolor: #444;\n\t}\n\t\n\t.section-text {\n\t\tdisplay: block;\n\t\tmargin-bottom: 16rpx;\n\t}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./privacy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./privacy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558412734\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
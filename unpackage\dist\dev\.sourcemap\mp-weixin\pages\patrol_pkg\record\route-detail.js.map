{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?d671", "webpack:///D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?01f1", "webpack:///D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?4847", "webpack:///D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?387f", "uni-app:///pages/patrol_pkg/record/route-detail.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?1130", "webpack:///D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?2ff9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "routeId", "routeName", "recordList", "loading", "refreshing", "page", "pageSize", "hasMore", "filterParams", "status", "route_id", "statusOptions", "value", "label", "routeStats", "point_count", "completion_rate", "normal_count", "missed_count", "not_checked_count", "total_checkin_time", "shift_info", "availableRounds", "currentRound", "taskDetail", "executorName", "showSortDropdown", "currentSort", "sortOptions", "currentSortOption", "isInitialLoading", "computed", "userInfo", "onLoad", "onShow", "methods", "loadTaskInfo", "PatrolApi", "name", "action", "task_id", "level", "taskDetailRes", "taskData", "id", "round", "sortedRounds", "console", "uni", "title", "icon", "calculateRouteStats", "roundsToProcess", "totalPointCount", "filter", "sort", "totalDuration", "normalCount", "missedCount", "notCheckedCount", "Math", "setCurrentRound", "refresh", "refreshRecords", "loadMore", "loadRecords", "fields", "include_basic_info", "recordRes", "allRecords", "allPoints", "roundPoints", "point_id", "order", "processedRecords", "processedIds", "selectedRound", "r", "formattedCheckTime", "_id", "point_name", "shift_name", "user_id", "user_name", "check_time", "patrol_date", "photos", "checkin_time", "location", "address", "remarks", "record_id", "lat", "lng", "filteredRecords", "statusValue", "resetFilters", "setStatus<PERSON>ilter", "getStatusText", "formatTimeDisplay", "viewRecordDetail", "duration", "url", "fail", "navigateBack", "formatTotalTime", "toggleSortDropdown", "setSortOption", "sortRecords"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACkK1nB;AACA;AAEA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QAAA;QACAC;MACA;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC,cACA;QAAAhB;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAgB;QAAAjB;QAAAC;MAAA;MAAA;MACA;MACAiB;IACA;EACA;;EACAC,4BACA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IACA;MACA;MACA;IACA;IAEA;MACA;IACA;IAEA;MACA;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAC;kBACAxC;oBACAyC;oBACA;oBACAC;kBACA;gBACA;cAAA;gBARAC;gBAUA;kBACAC;kBACA;;kBAEA;kBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA,2CACAA,2BACAA,iHACA;kBACA;;kBAEA;kBACA;oBACAL;oBACAM;kBACA;;kBAEA;kBACA;oBACA;oBACA,yBACA;sBAAAC;sBAAAP;sBAAA7B;oBAAA,EACA;;oBAEA;oBACAqC;sBAAA;oBAAA;oBAEAA;sBACA;wBACAD;wBACAP;wBACA7B;sBACA;oBACA;;oBAEA;oBACA;sBACA;oBACA;kBACA;;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAsC;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;QACAR;UAAA;QAAA;MACA;QACAA;UACA;YACAE;cACA;YACA;UACA;QACA;MACA;MACA;;MAEA;MACA;QACA;QAEA;UACA;UACAO;UACA;UACAC;QACA;UACA;UACA;YAAA;UAAA;UACA;YAAA;YACAD;YACA;YACAC;UACA;QACA;;QAEA;QACAD;UACA;YACA;YACA,iCACAE;cAAA;YAAA,GACAC;cAAA;YAAA;YAEA;cACA;cACA;cACA;cACAC;YACA;;YAEA;YACAX;cACA;gBACA;kBAAAY;kBAAA;gBACA;gBAAA;kBAAAC;kBAAA;gBACA;gBAAA;kBAAAC;kBAAA;cAAA;YAEA;UACA;QACA;MACA;;MAEA;MACA;QACA5C;QACAE;QACAC;QACAC;QACAC;QACAJ,uCACA4C;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAf;gBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAa;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAjB;kBACAC;kBACAC;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAIA1B,gCAEA;gBAAA;gBAAA,OACAa;kBACAC;kBACAC;kBACAxC;oBACAyC;oBACAnC;oBACAC;oBAAA;oBACAG;oBACA;oBACAyD,SACA,4EACA,wDACA,2CACA;oBACA;oBACAC;kBACA;gBACA;cAAA;gBAjBAC;gBAmBA;gBACAC;gBACA;kBACAA;gBACA;;gBAEA;gBACAC,gBAEA;gBACA;kBACAA;gBACA;;gBAEA;gBACA;kBAAA;gBAAA,OACA9C;kBAEA;kBACA+C;kBAEA/C;oBACA;sBACAqB;wBACA;0BACA0B;4BACAC;4BACAlC;4BACAmC;0BACA;wBACA;sBACA;oBACA;kBACA;;kBAEA;kBACA;oBACAH;kBACA;oBACA;oBACAA;sBACA;sBACA;wBACA;0BAAAhC;0BAAAmC;wBAAA;sBACA;sBACA;oBACA;kBACA;gBACA;;gBAEA;gBACAC;gBACAC;gBAEA;gBACA;kBACA;kBACAvB;kBAEA;oBACA;oBACAA;sBAAA;oBAAA;kBACA;oBACA;oBACAwB;sBAAA;oBAAA;oBACA;sBACAxB;oBACA;kBACA;;kBAEA;kBACAA;oBACA;sBACAP;wBACA;wBACA;0BAAA,OACAgC,iCACAA;wBAAA,EACA;;wBAEA;wBACA;0BAAA;wBAAA;;wBAEA;wBACA;wBAEA;0BACAF;;0BAEA;0BACA;0BACA;4BACA;8BACA;8BACA;gCACAG;8BACA;4BACA;8BACA/B;4BACA;0BACA;;0BAEA;0BACA;4BACAgC;4BACAP;4BACAQ;4BACAxC;4BACA/B;4BACAgE;4BACAQ;4BACAC;4BACAC;4BACAtC;4BACAuC;4BACAC;4BACAC;4BACA;4BACAC;4BACAC;4BACAC;4BACAC;4BACAC;4BACAC;4BACAC;0BACA;0BAEAnB;wBACA;sBACA;oBACA;kBACA;gBACA;;gBAEA;gBACAA;kBACA;kBACA;kBACA;kBACA;gBACA;;gBAEA;gBACAoB;gBACA;kBACAC;kBAEAD;oBACA;oBACA;sBACA;oBACA;oBACA;oBACA;sBACA;oBACA;oBACA;oBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA/C;gBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBACA;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8C;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;MAEA;QACA;QACA;UACA;UACA;QACA;;QAEA;QACA;MACA;QACApD;QACA;QACA;UACA;UACA;YACA;UACA;UACA;QACA;;QACA;MACA;IACA;IAEA;IACAqD;MACA;MACA;QACApD;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACAF;UACAC;UACAC;UACAmD;QACA;QACA;MACA;;MAEA;MACA;QACArD;UACAC;UACAC;UACAmD;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QAEArD;UACAsD;UACAC;YACAxD;YACAC;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAsD;MACAxD;IACA;IAEA;IACAyD;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACz1BA;AAAA;AAAA;AAAA;AAAipC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACArqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/record/route-detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/record/route-detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./route-detail.vue?vue&type=template&id=1dd827d6&\"\nvar renderjs\nimport script from \"./route-detail.vue?vue&type=script&lang=js&\"\nexport * from \"./route-detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./route-detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/record/route-detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./route-detail.vue?vue&type=template&id=1dd827d6&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.formatTotalTime(_vm.routeStats.total_checkin_time)\n  var g0 = !_vm.isInitialLoading\n    ? _vm.recordList.length === 0 && !_vm.loading\n    : null\n  var l0 =\n    !_vm.isInitialLoading && !g0\n      ? _vm.__map(_vm.recordList, function (record, __i2__) {\n          var $orig = _vm.__get_orig(record)\n          var m1 = _vm.getStatusText(record.status)\n          var g1 = record.photos && record.photos.length > 0\n          var g2 = g1 ? record.photos.length : null\n          return {\n            $orig: $orig,\n            m1: m1,\n            g1: g1,\n            g2: g2,\n          }\n        })\n      : null\n  var g3 = _vm.loading && _vm.recordList.length > 0\n  var g4 = !_vm.hasMore && _vm.recordList.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        l0: l0,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./route-detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./route-detail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"route-detail-container\">\n\t\t<!-- 固定区域：包含筛选选项卡和统计信息 -->\n\t\t<view class=\"fixed-header\">\n\t\t\t<!-- 筛选选项卡 -->\n\t\t\t<view class=\"tab-filter\">\n\t\t\t\t<view class=\"tab-group\">\n\t\t\t\t\t<view class=\"tab-options\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tv-for=\"(option, index) in statusOptions\" \n\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t:class=\"['tab-item', filterParams.status === option.value ? 'active' : '']\"\n\t\t\t\t\t\t\t@click=\"setStatusFilter(option.value)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{{ option.label }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 排序下拉菜单 -->\n\t\t\t\t\t<view class=\"sort-container\">\n\t\t\t\t\t\t<view class=\"sort-button\" @click=\"toggleSortDropdown\">\n\t\t\t\t\t\t\t<text class=\"sort-text\">{{ currentSortOption.label }}</text>\n\t\t\t\t\t\t\t<uni-icons type=\"arrow-down\" size=\"14\" color=\"#666666\" :class=\"{'rotate-icon': showSortDropdown}\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 排序选项下拉菜单 -->\n\t\t\t\t\t\t<view class=\"sort-options\" v-if=\"showSortDropdown\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tv-for=\"(option, index) in sortOptions\" \n\t\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t\tclass=\"sort-option\"\n\t\t\t\t\t\t\t\t:class=\"{'active': currentSort === option.value}\"\n\t\t\t\t\t\t\t\t@click=\"setSortOption(option)\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{{ option.label }}\n\t\t\t\t\t\t\t\t<uni-icons v-if=\"currentSort === option.value\" type=\"checkmarkempty\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 路线标题和统计信息 -->\n\t\t\t<view class=\"stats-card\">\n\t\t\t\t<view class=\"route-title\">{{ routeName || '线路记录' }}</view>\n\t\t\t\t\n\t\t\t\t<!-- 新增轮次选择器 -->\n\t\t\t\t<view class=\"round-selector\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tv-for=\"round in availableRounds\" \n\t\t\t\t\t\t:key=\"round.round\"\n\t\t\t\t\t\t:class=\"['round-item', currentRound === round.round ? 'active' : '']\"\n\t\t\t\t\t\t@click=\"setCurrentRound(round.round)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{ round.round === 0 ? '全部轮次' : `第${round.round}轮` }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"route-info\">\n\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t<text class=\"info-label\">点位数量：</text>\n\t\t\t\t\t\t<text class=\"info-value\">{{ routeStats.point_count }}个</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t<text class=\"info-label\">完成率：</text>\n\t\t\t\t\t\t<text class=\"info-value\">{{ routeStats.completion_rate }}%</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"metrics-row\">\n\t\t\t\t\t<view class=\"metric-box normal\">\n\t\t\t\t\t\t<view class=\"metric-value\">{{ routeStats.normal_count }}</view>\n\t\t\t\t\t\t<view class=\"metric-label\">已打卡</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"metric-box missed\">\n\t\t\t\t\t\t<view class=\"metric-value\">{{ routeStats.missed_count }}</view>\n\t\t\t\t\t\t<view class=\"metric-label\">缺卡</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"metric-box not-checked\">\n\t\t\t\t\t\t<view class=\"metric-value\">{{ routeStats.not_checked_count }}</view>\n\t\t\t\t\t\t<view class=\"metric-label\">未打卡</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"metric-box total-time\">\n\t\t\t\t\t\t<view class=\"metric-value\">{{ formatTotalTime(routeStats.total_checkin_time) }}</view>\n\t\t\t\t\t\t<view class=\"metric-label\">总用时</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 可滚动区域：记录列表 -->\n\t\t<scroll-view class=\"record-list\" scroll-y @scrolltolower=\"loadMore\" refresher-enabled @refresherrefresh=\"refresh\" :refresher-triggered=\"refreshing\">\n\t\t\t<!-- 初始加载骨架屏 -->\n\t\t\t<view v-if=\"isInitialLoading\" class=\"skeleton-container\">\n\t\t\t\t<view v-for=\"n in 6\" :key=\"n\" class=\"skeleton-item\">\n\t\t\t\t\t<view class=\"skeleton-header\">\n\t\t\t\t\t\t<view class=\"skeleton-point\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-status\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"skeleton-content\">\n\t\t\t\t\t\t<view class=\"skeleton-line\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-line short\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-line medium\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<p-empty-state v-else-if=\"recordList.length === 0 && !loading\" type=\"record\" text=\"暂无巡视记录\"></p-empty-state>\n\t\t\t\n\t\t\t<view v-else class=\"record-item\" v-for=\"record in recordList\" :key=\"record._id\" @click=\"viewRecordDetail(record)\">\n\t\t\t\t<view class=\"record-header\">\n\t\t\t\t\t<view class=\"record-point\">{{ record.point_name || '未知点位' }}</view>\n\t\t\t\t\t<view :class=\"['record-status', `status-${record.status}`]\">{{ getStatusText(record.status) }}</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"record-info\">\n\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t<text class=\"info-label\">班次：</text>\n\t\t\t\t\t\t<text class=\"info-value\">{{ record.shift_name || '未知班次' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t<text class=\"info-label\">轮次：</text>\n\t\t\t\t\t\t<text class=\"info-value\">{{ record.round === 0 ? '全部' : `第${record.round}轮` }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-item\" v-if=\"record.status === 3 || record.status === 4\">\n\t\t\t\t\t\t<text class=\"info-label\">状态：</text>\n\t\t\t\t\t\t<text class=\"info-value\">未打卡</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-item\" v-else-if=\"record.check_time && record.check_time !== '未打卡'\">\n\t\t\t\t\t\t<text class=\"info-label\">打卡时间：</text>\n\t\t\t\t\t\t<text class=\"info-value\">{{ record.check_time }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-item\" v-else>\n\t\t\t\t\t\t<text class=\"info-label\">详情：</text>\n\t\t\t\t\t\t<text class=\"info-value\">无详细信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-item\" v-if=\"record.photos && record.photos.length > 0\">\n\t\t\t\t\t\t<text class=\"info-label\">照片数量：</text>\n\t\t\t\t\t\t<text class=\"info-value\">{{ record.photos.length }}张</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"record-footer\">\n\t\t\t\t\t<view class=\"record-user\">{{ record.user_name || '未知人员' }}</view>\n\t\t\t\t\t<view class=\"record-time\">{{ record.patrol_date || '未知日期' }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 加载更多提示 -->\n\t\t\t<view class=\"loading-more\" v-if=\"loading && recordList.length > 0\">\n\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 全部加载完毕提示 -->\n\t\t\t<view class=\"no-more\" v-if=\"!hasMore && recordList.length > 0\">\n\t\t\t\t<text class=\"no-more-text\">没有更多记录了</text>\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport { mapState } from 'vuex';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\nimport { formatDate, getRelativeTime, safeDateFormat } from '@/utils/date.js';\n\nexport default {\n\tcomponents: {\n\t\tPEmptyState\n\t},\n\tdata() {\n\t\treturn {\n\t\t\trouteId: '',\n\t\t\trouteName: '线路记录',\n\t\t\trecordList: [],\n\t\t\tloading: false,\n\t\t\trefreshing: false,\n\t\t\tpage: 1,\n\t\t\tpageSize: 50,\n\t\t\thasMore: true,\n\t\t\tfilterParams: {\n\t\t\t\tstatus: '',  // 空字符串表示全部\n\t\t\t\troute_id: ''\n\t\t\t},\n\t\t\tstatusOptions: [\n\t\t\t\t{ value: '', label: '全部' },\n\t\t\t\t{ value: 1, label: '已打卡' },\n\t\t\t\t{ value: 3, label: '缺卡' },\n\t\t\t\t{ value: 0, label: '未打卡' }\n\t\t\t],\n\t\t\trouteStats: {\n\t\t\t\tpoint_count: 0,\n\t\t\t\tcompletion_rate: 0,\n\t\t\t\tnormal_count: 0,\n\t\t\t\tmissed_count: 0,\n\t\t\t\tnot_checked_count: 0,\n\t\t\t\ttotal_checkin_time: 0 // 添加总用时字段\n\t\t\t},\n\t\t\tshift_info: {},\n\t\t\tavailableRounds: [], // 可用的轮次列表\n\t\t\tcurrentRound: undefined,  // 当前选中的轮次，undefined表示未初始化\n\t\t\ttaskDetail: null,  // 保存完整的任务详情\n\t\t\texecutorName: '',  // 新增执行者名称\n\t\t\tshowSortDropdown: false, // 控制下拉菜单显示\n\t\t\tcurrentSort: 'point', // 默认按点位顺序\n\t\t\tsortOptions: [\n\t\t\t\t{ value: 'point', label: '点位顺序' },\n\t\t\t\t{ value: 'checkin', label: '打卡顺序' }\n\t\t\t],\n\t\t\t\t\t\t\tcurrentSortOption: { value: 'point', label: '点位顺序' }, // 当前选中的排序选项\n\t\t\t\t// 骨架屏状态\n\t\t\t\tisInitialLoading: false // 是否正在初始加载\n\t\t};\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\tuserInfo: state => state.user.userInfo\n\t\t})\n\t},\n\tonLoad(options) {\n\t\tif (options.id) {\n\t\t\tthis.routeId = options.id;\n\t\t\tthis.filterParams.route_id = options.id;\n\t\t}\n\t\t\n\t\tif (options.name) {\n\t\t\tthis.routeName = decodeURIComponent(options.name);\n\t\t}\n\t\t\n\t\tif (options.executorName) {\n\t\t\tthis.executorName = decodeURIComponent(options.executorName);\n\t\t}\n\t\t\n\t\t// 显示骨架屏并加载数据\n\t\tthis.isInitialLoading = true;\n\t\t\n\t\t// 获取任务基本信息（包含点位总数）\n\t\tthis.loadTaskInfo();\n\t\t\n\t\t// 加载真实数据\n\t\tthis.loadRecords();\n\t},\n\tonShow() {\n\t\t// 每次显示页面时只刷新记录数据，不重新加载路线信息\n\t\t// 避免覆盖URL传入的路线名称\n\t\tthis.refreshRecords();\n\t},\n\tmethods: {\n\t\t// 获取任务基本信息\n\t\tasync loadTaskInfo() {\n\t\t\tif (!this.routeId) return;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst taskDetailRes = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-task',\n\t\t\t\t\taction: 'getTaskDetail',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\ttask_id: this.routeId,\n\t\t\t\t\t\t// 🔥 优化方案：使用level='checkin'模式，保留完整points数组但简化字段\n\t\t\t\t\t\tlevel: 'checkin' // 保留points数组但只要核心字段：point_id, status, checkin_time\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (taskDetailRes.code === 0 && taskDetailRes.data) {\n\t\t\t\t\tconst taskData = taskDetailRes.data;\n\t\t\t\t\tthis.taskDetail = taskData; // 保存完整任务详情\n\t\t\t\t\t\n\t\t\t\t\t// 设置路线名称 - 修改这里，优先使用URL传入的名称\n\t\t\t\t\tif (!this.routeName || this.routeName === '线路记录') {\n\t\t\t\t\tthis.routeName = taskData.route_name || taskData.name || '线路记录';\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 设置执行者姓名\n\t\t\t\t\tif (!this.executorName) {\n\t\t\t\t\t\tthis.executorName = taskData.user_name || \n\t\t\t\t\t\t\ttaskData.executor_name || \n\t\t\t\t\t\t\t(taskData.executor ? taskData.executor.name || taskData.executor.real_name || taskData.executor.nickname : '') || \n\t\t\t\t\t\t\t'';\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 保存班次信息\n\t\t\t\t\tthis.shift_info = {\n\t\t\t\t\t\tname: taskData.shift_name || '',\n\t\t\t\t\t\tid: taskData.shift_id || ''\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 初始化可用轮次列表\n\t\t\t\t\tif (taskData.rounds_detail && taskData.rounds_detail.length > 0) {\n\t\t\t\t\t\t// 添加\"全部\"选项\n\t\t\t\t\t\tthis.availableRounds = [\n\t\t\t\t\t\t\t{ round: 0, name: '全部轮次', status: -1 }\n\t\t\t\t\t\t];\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 按轮次顺序排序并添加到列表\n\t\t\t\t\t\tconst sortedRounds = [...taskData.rounds_detail].sort((a, b) => a.round - b.round);\n\t\t\t\t\t\t\n\t\t\t\t\t\tsortedRounds.forEach(round => {\n\t\t\t\t\t\t\tthis.availableRounds.push({\n\t\t\t\t\t\t\t\tround: round.round,\n\t\t\t\t\t\t\t\tname: `第${round.round}轮`,\n\t\t\t\t\t\t\t\tstatus: round.status\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 只在首次加载时设置默认轮次\n\t\t\t\t\t\tif (this.currentRound === undefined) {\n\t\t\t\t\t\t\tthis.currentRound = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 计算点位统计信息 - 前端计算确保准确性\n\t\t\t\t\tthis.calculateRouteStats();\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载任务信息出错:', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取任务信息失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 添加前端统计计算方法\n\t\tcalculateRouteStats() {\n\t\t\tif (!this.taskDetail) return;\n\t\t\t\n\t\t\tconst taskData = this.taskDetail;\n\t\t\tlet normalCount = 0;\n\t\t\tlet missedCount = 0;\n\t\t\tlet notCheckedCount = 0;\n\t\t\tlet totalPointCount = 0;\n\t\t\tlet totalDuration = 0;\n\t\t\t\n\t\t\t// 1. 获取物理点位总数\n\t\t\tconst uniquePointIds = new Set();\n\t\t\tif (taskData.route_detail && taskData.route_detail.points) {\n\t\t\t\ttaskData.route_detail.points.forEach(p => uniquePointIds.add(p.point_id));\n\t\t\t} else if (taskData.rounds_detail && taskData.rounds_detail.length > 0) {\n\t\t\t\ttaskData.rounds_detail.forEach(round => {\n\t\t\t\t\tif (round.points && Array.isArray(round.points)) {\n\t\t\t\t\t\tround.points.forEach(point => {\n\t\t\t\t\t\t\tif (point.point_id) uniquePointIds.add(point.point_id);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\tconst physicalPointCount = uniquePointIds.size;\n\t\t\t\n\t\t\t// 2. 计算状态统计\n\t\t\tif (taskData.rounds_detail && taskData.rounds_detail.length > 0) {\n\t\t\t\tlet roundsToProcess = [];\n\t\t\t\t\n\t\t\t\tif (this.currentRound === 0) {\n\t\t\t\t\t// 全部轮次\n\t\t\t\t\troundsToProcess = taskData.rounds_detail;\n\t\t\t\t\t// 总点位数 = 物理点位数 × 轮次数\n\t\t\t\t\ttotalPointCount = physicalPointCount * taskData.rounds_detail.length;\n\t\t\t\t} else {\n\t\t\t\t\t// 单轮次\n\t\t\t\t\tconst selectedRound = taskData.rounds_detail.find(r => r.round === this.currentRound);\n\t\t\t\t\tif (selectedRound) {\n\t\t\t\t\t\troundsToProcess = [selectedRound];\n\t\t\t\t\t\t// 单轮次的总点位数就是该轮次的点位数\n\t\t\t\t\t\ttotalPointCount = selectedRound.points?.length || 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 处理每个轮次\n\t\t\t\troundsToProcess.forEach(round => {\n\t\t\t\t\tif (round.points && Array.isArray(round.points)) {\n\t\t\t\t\t\t// 计算轮次用时\n\t\t\t\t\t\tconst checkedPoints = round.points\n\t\t\t\t\t\t\t.filter(point => point.status === 1 && point.checkin_time)\n\t\t\t\t\t\t\t.sort((a, b) => new Date(a.checkin_time) - new Date(b.checkin_time));\n\n\t\t\t\t\t\tif (checkedPoints.length > 0) {\n\t\t\t\t\t\t\tconst firstCheckTime = new Date(checkedPoints[0].checkin_time);\n\t\t\t\t\t\t\tconst lastCheckTime = new Date(checkedPoints[checkedPoints.length - 1].checkin_time);\n\t\t\t\t\t\t\tconst roundDuration = Math.ceil((lastCheckTime - firstCheckTime) / (1000 * 60));\n\t\t\t\t\t\t\ttotalDuration += roundDuration;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 统计状态\n\t\t\t\t\t\tround.points.forEach(point => {\n\t\t\t\t\t\t\tswitch(Number(point.status)) {\n\t\t\t\t\t\t\t\tcase 1: normalCount++; break;\n\t\t\t\t\t\t\t\tcase 3: case 4: missedCount++; break;\n\t\t\t\t\t\t\t\tcase 0: default: notCheckedCount++; break;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 3. 更新统计数据\n\t\t\tthis.routeStats = {\n\t\t\t\tpoint_count: physicalPointCount,\n\t\t\t\tnormal_count: normalCount,\n\t\t\t\tmissed_count: missedCount,\n\t\t\t\tnot_checked_count: notCheckedCount,\n\t\t\t\ttotal_checkin_time: totalDuration,\n\t\t\t\tcompletion_rate: totalPointCount > 0 ? \n\t\t\t\t\tMath.round((normalCount / totalPointCount) * 100) : 0\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 设置当前轮次\n\t\tsetCurrentRound(round) {\n\t\t\tthis.currentRound = round;\n\t\t\t// 重新计算统计数据\n\t\t\tthis.calculateRouteStats();\n\t\t\t// 刷新记录列表\n\t\t\tthis.refresh();\n\t\t},\n\t\t\n\t\t// 完整刷新（包括任务信息和记录）\n\t\tasync refresh() {\n\t\t\tthis.refreshing = true;\n\t\t\tthis.page = 1;\n\t\t\tthis.hasMore = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 强制重新加载任务信息\n\t\t\t\tawait this.loadTaskInfo();\n\t\t\t\t// 加载记录\n\t\t\t\tawait this.loadRecords();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('刷新失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '刷新失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.refreshing = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 只刷新记录（用于页面显示时）\n\t\tasync refreshRecords() {\n\t\t\tthis.page = 1;\n\t\t\tthis.hasMore = true;\n\t\t\tawait this.loadRecords();\n\t\t},\n\t\t\n\t\t// 加载更多数据\n\t\tloadMore() {\n\t\t\tif (this.hasMore && !this.loading) {\n\t\t\t\tthis.loadRecords();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载记录列表\n\t\tasync loadRecords() {\n\t\t\tif (this.loading || !this.hasMore) return;\n\t\t\tthis.loading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 如果是刷新操作，强制重新加载任务详情\n\t\t\t\tif (this.refreshing) {\n\t\t\t\t\tawait this.loadTaskInfo();\n\t\t\t\t}\n\t\t\t\t// 如果还没有加载任务详情，先加载\n\t\t\t\telse if (!this.taskDetail) {\n\t\t\t\t\tawait this.loadTaskInfo();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.taskDetail) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取任务详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t\tthis.refreshing = false;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst taskDetail = this.taskDetail;\n\t\t\t\t\n\t\t\t\t// 获取巡视记录\n\t\t\t\tconst recordRes = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-record',\n\t\t\t\t\taction: 'getRecordList',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\ttask_id: this.routeId,\n\t\t\t\t\t\tpage: this.page,\n\t\t\t\t\t\tpageSize: 50, // 优化：减少单次数据量，从200改为50\n\t\t\t\t\t\tstatus: this.filterParams.status === '' ? undefined : Number(this.filterParams.status),\n\t\t\t\t\t\t// 优化：只获取列表显示需要的字段，减少RU消耗\n\t\t\t\t\t\tfields: [\n\t\t\t\t\t\t\t'_id', 'point_id', 'point_name', 'status', 'round', 'user_id', 'user_name',\n\t\t\t\t\t\t\t'checkin_time', 'patrol_date', 'task_id', 'shift_name',\n\t\t\t\t\t\t\t'photos', 'location', 'address', 'remarks'\n\t\t\t\t\t\t],\n\t\t\t\t\t\t// 移除冗余参数，减少数据传输\n\t\t\t\t\t\tinclude_basic_info: true\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 从记录API获取的点位\n\t\t\t\tlet allRecords = [];\n\t\t\t\tif (recordRes.code === 0 && recordRes.data && recordRes.data.records) {\n\t\t\t\t\tallRecords = recordRes.data.records || [];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 提取所有点位信息\n\t\t\t\tlet allPoints = [];\n\t\t\t\t\n\t\t\t\t// 从route_detail.points获取点位信息\n\t\t\t\tif (taskDetail.route_detail && taskDetail.route_detail.points) {\n\t\t\t\t\tallPoints = [...taskDetail.route_detail.points];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果没有route_detail，或者点位数量不足，从rounds_detail中获取\n\t\t\t\tif ((allPoints.length === 0 || allPoints.some(p => !p.name)) && \n\t\t\t\t\ttaskDetail.rounds_detail && taskDetail.rounds_detail.length > 0) {\n\t\t\t\t\t\n\t\t\t\t\t// 合并所有轮次的点位基本信息\n\t\t\t\t\tconst roundPoints = new Map();\n\t\t\t\t\t\n\t\t\t\t\ttaskDetail.rounds_detail.forEach(round => {\n\t\t\t\t\t\tif (round.points && Array.isArray(round.points)) {\n\t\t\t\t\t\t\tround.points.forEach(point => {\n\t\t\t\t\t\t\t\tif (point.point_id && (!roundPoints.has(point.point_id) || !roundPoints.get(point.point_id).name)) {\n\t\t\t\t\t\t\t\t\troundPoints.set(point.point_id, {\n\t\t\t\t\t\t\t\t\t\tpoint_id: point.point_id,\n\t\t\t\t\t\t\t\t\t\tname: point.name || '未知点位',\n\t\t\t\t\t\t\t\t\t\torder: point.order || 0\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 更新或补充点位信息\n\t\t\t\t\tif (allPoints.length === 0) {\n\t\t\t\t\t\tallPoints = Array.from(roundPoints.values());\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 补充现有点位的名称等信息\n\t\t\t\t\t\tallPoints = allPoints.map(point => {\n\t\t\t\t\t\t\tconst roundPoint = roundPoints.get(point.point_id);\n\t\t\t\t\t\t\tif (roundPoint && (!point.name || point.name === '未知点位')) {\n\t\t\t\t\t\t\t\treturn { ...point, name: roundPoint.name, order: roundPoint.order || point.order || 0 };\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn point;\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 为每轮次的每个点位创建或更新记录\n\t\t\t\tconst processedRecords = [];\n\t\t\t\tconst processedIds = new Set(); // 跟踪已处理的记录ID\n\t\t\t\t\n\t\t\t\t// 处理所选择轮次的记录\n\t\t\t\tif (taskDetail.rounds_detail && taskDetail.rounds_detail.length > 0) {\n\t\t\t\t\t// 确定要处理的轮次，确保按轮次顺序排序\n\t\t\t\t\tlet roundsToProcess = [];\n\t\t\t\t\t\n\t\t\t\t\tif (this.currentRound === 0) {\n\t\t\t\t\t\t// 处理所有轮次，按轮次号排序\n\t\t\t\t\t\troundsToProcess = [...taskDetail.rounds_detail].sort((a, b) => a.round - b.round);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 只处理选中的轮次\n\t\t\t\t\t\tconst selectedRound = taskDetail.rounds_detail.find(r => r.round === this.currentRound);\n\t\t\t\t\t\tif (selectedRound) {\n\t\t\t\t\t\t\troundsToProcess = [selectedRound];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 处理每个轮次\n\t\t\t\t\troundsToProcess.forEach(round => {\n\t\t\t\t\t\tif (round.points && Array.isArray(round.points)) {\n\t\t\t\t\t\t\tround.points.forEach(point => {\n\t\t\t\t\t\t\t\t// 查找匹配的API记录\n\t\t\t\t\t\t\t\tconst existingRecord = allRecords.find(r => \n\t\t\t\t\t\t\t\t\tr.point_id === point.point_id && \n\t\t\t\t\t\t\t\t\tr.round === round.round\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 查找点位基本信息\n\t\t\t\t\t\t\t\tconst pointInfo = allPoints.find(p => p.point_id === point.point_id) || {};\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 创建或更新记录\n\t\t\t\t\t\t\t\tconst recordId = `${point.point_id}_${round.round}`;\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tif (!processedIds.has(recordId)) {\n\t\t\t\t\t\t\t\t\tprocessedIds.add(recordId);\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 格式化打卡时间\n\t\t\t\t\t\t\t\t\tlet formattedCheckTime = '未打卡';\n\t\t\t\t\t\t\t\t\tif (existingRecord && existingRecord.checkin_time) {\n\t\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t\tconst dateObj = new Date(existingRecord.checkin_time);\n\t\t\t\t\t\t\t\t\t\t\tif (!isNaN(dateObj.getTime())) {\n\t\t\t\t\t\t\t\t\t\t\t\tformattedCheckTime = `${String(dateObj.getHours()).padStart(2, '0')}:${String(dateObj.getMinutes()).padStart(2, '0')}:${String(dateObj.getSeconds()).padStart(2, '0')}`;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('格式化时间错误:', e);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 创建完整的记录对象，确保包含必要的信息\n\t\t\t\t\t\t\t\t\tconst record = {\n\t\t\t\t\t\t\t\t\t\t_id: recordId,\n\t\t\t\t\t\t\t\t\t\tpoint_id: point.point_id,\n\t\t\t\t\t\t\t\t\t\tpoint_name: point.name || pointInfo.name || '未知点位',\n\t\t\t\t\t\t\t\t\t\ttask_id: this.routeId,\n\t\t\t\t\t\t\t\t\t\tstatus: point.status !== undefined ? Number(point.status) : 0,\n\t\t\t\t\t\t\t\t\t\torder: point.order || pointInfo.order || 0,\n\t\t\t\t\t\t\t\t\t\tshift_name: taskDetail.shift_name || this.shift_info.name || '未知班次',\n\t\t\t\t\t\t\t\t\t\tuser_id: existingRecord?.user_id || taskDetail.user_id,\n\t\t\t\t\t\t\t\t\t\tuser_name: existingRecord?.user_name || taskDetail.user_name || taskDetail.executor_name || taskDetail.creator_name || '未知人员',\n\t\t\t\t\t\t\t\t\t\tround: round.round,\n\t\t\t\t\t\t\t\t\t\tcheck_time: formattedCheckTime,\n\t\t\t\t\t\t\t\t\t\tpatrol_date: existingRecord?.patrol_date || taskDetail.patrol_date || '未知日期',\n\t\t\t\t\t\t\t\t\t\tphotos: existingRecord?.photos || [],\n\t\t\t\t\t\t\t\t\t\t// 添加记录的详细信息，确保详情页可以打开\n\t\t\t\t\t\t\t\t\t\tcheckin_time: existingRecord?.checkin_time || null,\n\t\t\t\t\t\t\t\t\t\tlocation: existingRecord?.location || null,\n\t\t\t\t\t\t\t\t\t\taddress: existingRecord?.address || null,\n\t\t\t\t\t\t\t\t\t\tremarks: existingRecord?.remarks || '',\n\t\t\t\t\t\t\t\t\t\trecord_id: existingRecord?._id || recordId,\n\t\t\t\t\t\t\t\t\t\tlat: point.lat || pointInfo.lat || existingRecord?.lat,\n\t\t\t\t\t\t\t\t\t\tlng: point.lng || pointInfo.lng || existingRecord?.lng\n\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tprocessedRecords.push(record);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 按点位顺序和轮次顺序排序\n\t\t\t\tprocessedRecords.sort((a, b) => {\n\t\t\t\t\t// 首先按轮次排序\n\t\t\t\t\tif (a.round !== b.round) return a.round - b.round;\n\t\t\t\t\t// 轮次相同，按点位顺序排序\n\t\t\t\t\treturn (a.order || 0) - (b.order || 0);\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 根据筛选条件过滤\n\t\t\t\tlet filteredRecords = processedRecords;\n\t\t\t\tif (this.filterParams.status !== '') {\n\t\t\t\t\tconst statusValue = Number(this.filterParams.status);\n\t\t\t\t\t\n\t\t\t\t\tfilteredRecords = processedRecords.filter(record => {\n\t\t\t\t\t\t// 对于缺卡状态(值为3)，同时匹配状态3和状态4\n\t\t\t\t\t\tif (statusValue === 3) {\n\t\t\t\t\t\t\treturn record.status === 3 || record.status === 4;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 对于未打卡状态(值为0)，精确匹配\n\t\t\t\t\t\tif (statusValue === 0) {\n\t\t\t\t\t\t\treturn record.status === 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 对于其他状态，精确匹配\n\t\t\t\t\t\treturn record.status === statusValue;\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 实现真正的分页逻辑\n\t\t\t\tif (this.page === 1) {\n\t\t\t\t\t// 第一页，替换现有数据\n\t\t\t\t\tthis.recordList = filteredRecords;\n\t\t\t\t} else {\n\t\t\t\t\t// 后续页面，追加数据\n\t\t\t\t\tthis.recordList = [...this.recordList, ...filteredRecords];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 应用当前的排序方式\n\t\t\t\tthis.sortRecords();\n\t\t\t\t\n\t\t\t\t// 判断是否还有更多数据\n\t\t\t\t// 如果返回的记录数少于pageSize，说明没有更多数据了\n\t\t\t\tthis.hasMore = filteredRecords.length >= this.pageSize;\n\t\t\t\t\n\t\t\t\t// 如果还有更多数据，准备下一页\n\t\t\t\tif (this.hasMore) {\n\t\t\t\t\tthis.page++;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载记录失败:', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载记录失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t\tthis.isInitialLoading = false; // 关闭骨架屏\n\t\t\t\tif (this.refreshing) {\n\t\t\t\t\tthis.refreshing = false;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 重置筛选条件\n\t\tresetFilters() {\n\t\t\tthis.filterParams.status = '';\n\t\t\tthis.refresh();\n\t\t},\n\t\t\n\t\t// 设置状态筛选\n\t\tsetStatusFilter(status) {\n\t\t\t// 将状态值转换为数字，除非是空字符串\n\t\t\tthis.filterParams.status = status === '' ? '' : Number(status);\n\t\t\tthis.refresh();\n\t\t},\n\t\t\n\t\t// 获取状态文本\n\t\tgetStatusText(status) {\n\t\t\tconst numStatus = Number(status);\n\t\t\tswitch(numStatus) {\n\t\t\t\tcase 0: \n\t\t\t\t\treturn '未打卡';\n\t\t\t\tcase 1: \n\t\t\t\t\treturn '已打卡';\n\t\t\t\tcase 2:\n\t\t\t\t\treturn '超时打卡';\n\t\t\t\tcase 3: \n\t\t\t\t\treturn '异常打卡';\n\t\t\t\tcase 4:\n\t\t\t\t\treturn '缺卡';\n\t\t\t\tdefault: \n\t\t\t\t\treturn '未知状态';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 格式化时间显示\n\t\tformatTimeDisplay(timeStr) {\n\t\t\tif (!timeStr) return '';\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 检查是否只是日期部分\n\t\t\t\tif (typeof timeStr === 'string' && timeStr.length === 10 && timeStr.includes('-')) {\n\t\t\t\t\t// 只包含日期部分 YYYY-MM-DD，直接返回\n\t\t\t\t\treturn timeStr;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 使用安全的日期格式化函数，只显示日期部分\n\t\t\t\treturn safeDateFormat(timeStr, 'YYYY-MM-DD');\n\t\t\t} catch (e) {\n\t\t\t\tconsole.warn('时间格式化错误', e);\n\t\t\t\t// 错误处理，显示原始字符串或部分字符串\n\t\t\t\tif (typeof timeStr === 'string') {\n\t\t\t\t\t// 如果是标准日期格式，只取日期部分\n\t\t\t\t\tif (timeStr.includes('T')) {\n\t\t\t\t\t\treturn timeStr.split('T')[0];\n\t\t\t\t\t}\n\t\t\t\t\treturn timeStr.split(' ')[0]; // 只显示日期部分\n\t\t\t\t}\n\t\t\t\treturn '未知时间';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 查看记录详情\n\t\tviewRecordDetail(record) {\n\t\t\t// 确保记录有必要的字段\n\t\t\tif (!record || !record.point_id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '记录数据不完整',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 获取状态\n\t\t\tconst status = record.status || 0;\n\t\t\t\n\t\t\t// 如果是未打卡(0)状态，只提示用户，不跳转\n\t\t\tif (status === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '该点位未打卡',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 缺卡状态(3或4)，只提示用户，不跳转\n\t\t\tif (status === 3 || status === 4) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '该点位缺卡',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 只有已打卡(1)和超时打卡(2)才跳转到详情页\n\t\t\tif (status === 1 || status === 2) {\n\t\t\t\t// 构建记录详情页URL\n\t\t\t\tconst url = `/pages/patrol_pkg/record/detail?id=${record.record_id || record._id}&task_id=${this.routeId}&point_id=${record.point_id}&round=${record.round}`;\n\t\t\t\t\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl,\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('打开记录详情页失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '无法打开详情',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 返回导航\n\t\tnavigateBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 添加格式化总用时的方法\n\t\tformatTotalTime(minutes) {\n\t\t\tif (!minutes || minutes <= 0) return '0分钟';\n\t\t\tif (minutes < 60) return `${minutes}分钟`;\n\t\t\tconst hours = Math.floor(minutes / 60);\n\t\t\tconst remainingMinutes = minutes % 60;\n\t\t\tif (remainingMinutes === 0) return `${hours}小时`;\n\t\t\treturn `${hours}时${remainingMinutes}分`;\n\t\t},\n\t\t\n\t\t// 切换下拉菜单显示状态\n\t\ttoggleSortDropdown() {\n\t\t\tthis.showSortDropdown = !this.showSortDropdown;\n\t\t},\n\t\t\n\t\t// 设置排序选项\n\t\tsetSortOption(option) {\n\t\t\tthis.currentSort = option.value;\n\t\t\tthis.currentSortOption = option;\n\t\t\tthis.showSortDropdown = false;\n\t\t\tthis.sortRecords();\n\t\t},\n\t\t\n\t\t// 排序记录\n\t\tsortRecords() {\n\t\t\tif (this.currentSort === 'checkin') {\n\t\t\t\t// 按打卡时间排序\n\t\t\t\tthis.recordList.sort((a, b) => {\n\t\t\t\t\t// 未打卡的记录放到最后\n\t\t\t\t\tif (!a.checkin_time && !b.checkin_time) return 0;\n\t\t\t\t\tif (!a.checkin_time) return 1;\n\t\t\t\t\tif (!b.checkin_time) return -1;\n\t\t\t\t\treturn new Date(a.checkin_time) - new Date(b.checkin_time);\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// 按点位顺序排序（默认）\n\t\t\t\tthis.recordList.sort((a, b) => {\n\t\t\t\t\t// 先按轮次排序\n\t\t\t\t\tif (a.round !== b.round) return a.round - b.round;\n\t\t\t\t\t// 轮次相同，按点位顺序排序\n\t\t\t\t\treturn (a.order || 0) - (b.order || 0);\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t}\n};\n</script>\n\n<style lang=\"scss\">\n.route-detail-container {\n\theight: 100vh;\n\tbackground-color: #F5F7FA;\n\tdisplay: flex;\n\tflex-direction: column;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.fixed-header {\n\tbackground-color: #F5F7FA;\n\twidth: 100%;\n\tz-index: 10;\n\tposition: relative;\n}\n\n.tab-filter {\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid #F0F0F0;\n\tmargin-bottom: 10rpx;\n\twidth: 100%;\n\tbox-sizing: border-box;\n}\n\n.tab-group {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin: 10rpx 0;\n\twidth: 100%;\n}\n\n.tab-options {\n\tdisplay: flex;\n\tflex-wrap: nowrap;\n\talign-items: center;\n\tflex: 1;\n\toverflow-x: auto;\n\twhite-space: nowrap;\n\t-webkit-overflow-scrolling: touch;\n\t/* 隐藏滚动条 */\n\tscrollbar-width: none;\n\t-ms-overflow-style: none;\n\t&::-webkit-scrollbar {\n\t\tdisplay: none;\n\t}\n}\n\n.tab-item {\n\tfont-size: 26rpx;\n\tcolor: #666666;\n\tpadding: 12rpx 24rpx;\n\tbackground-color: #F5F7FA;\n\tborder-radius: 30rpx;\n\tmargin-right: 20rpx;\n\tflex-shrink: 0;  /* 防止压缩 */\n\tdisplay: inline-block;  /* 适配小程序不缩放 */\n\ttransition: all 0.3s;\n\t\n\t&:last-child {\n\t\tmargin-right: 10rpx;  /* 最后一个标签右边留一点空间 */\n\t}\n\t\n\t&.active {\n\t\tbackground-color: #E6F7FF;\n\t\tcolor: #1677FF;\n\t\tborder: 1rpx solid #91D5FF;\n\t}\n}\n\n.stats-card {\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tmargin: 0 20rpx 20rpx;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(22, 119, 255, 0.15);\n}\n\n.route-title {\n\tfont-size: 34rpx;\n\tfont-weight: 600;\n\tcolor: #333333;\n\tmargin-bottom: 20rpx;\n\ttext-align: center;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\tdisplay: -webkit-box;\n\t-webkit-line-clamp: 2; /* 最多显示两行 */\n\t-webkit-box-orient: vertical;\n\tline-height: 1.4;\n}\n\n.route-info {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\tmargin-bottom: 20rpx;\n}\n\n.info-row {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.info-label {\n\tfont-size: 28rpx;\n\tcolor: #666666;\n}\n\n.info-value {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tfont-weight: 500;\n}\n\n.metrics-row {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tpadding-top: 20rpx;\n\tborder-top: 1rpx solid #F0F0F0;\n}\n\n.metric-box {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.metric-value {\n\tfont-size: 36rpx;\n\tfont-weight: 600;\n\tmargin-bottom: 8rpx;\n}\n\n.metric-label {\n\tfont-size: 24rpx;\n\tcolor: #666666;\n}\n\n.normal .metric-value {\n\tcolor: #52C41A;\n}\n\n.overtime {\n\tdisplay: none;\n}\n\n.missed .metric-value {\n\tcolor: #F5222D;\n}\n\n.not-checked .metric-value {\n\tcolor: #8C8C8C;\n}\n\n.total-time .metric-value {\n\tcolor: #1677FF;\n\tfont-size: 36rpx;\n\tfont-weight: 600;\n}\n\n.record-list {\n\tflex: 1;\n\toverflow-y: auto;\n\theight: calc(100vh - 360rpx); /* 减去固定头部的高度 */\n}\n\n.record-item {\n\tmargin-bottom: 20rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 8rpx rgba(22, 119, 255, 0.15);\n\ttransition: all 0.2s;\n\tmargin-left: 20rpx;\n\tmargin-right: 20rpx;\n}\n\n.record-item:active {\n\ttransform: scale(0.98);\n}\n\n.record-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20rpx;\n\tborder-bottom: 1rpx solid #F0F0F0;\n}\n\n.record-point {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333333;\n}\n\n.record-status {\n\tfont-size: 24rpx;\n\tpadding: 4rpx 16rpx;\n\tborder-radius: 20rpx;\n}\n\n.status-0 {\n\tbackground-color: #F5F5F5;\n\tcolor: #8C8C8C;\n}\n\n.status-1 {\n\tbackground-color: #E6F7FF;\n\tcolor: #1677FF;\n}\n\n.status-2 {\n\tbackground-color: #FFF7E6;\n\tcolor: #FAAD14;\n}\n\n.status-3, .status-4 {\n\tbackground-color: #FFF1F0;\n\tcolor: #F5222D;\n}\n\n.record-info {\n\tpadding: 20rpx;\n}\n\n.info-item {\n\tmargin-bottom: 10rpx;\n\tfont-size: 28rpx;\n\tcolor: #666666;\n}\n\n.record-footer {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tpadding: 16rpx 20rpx;\n\tborder-top: 1rpx solid #F0F0F0;\n\tfont-size: 24rpx;\n\tcolor: #8F959E;\n}\n\n.loading-more, .no-more {\n\ttext-align: center;\n\tpadding: 30rpx 0;\n}\n\n.loading-text, .no-more-text {\n\tfont-size: 24rpx;\n\tcolor: #8F959E;\n\tletter-spacing: 1rpx;\n}\n\n// 添加轮次选择器样式\n.round-selector {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tjustify-content: center;\n\tmargin: 10rpx 0 20rpx;\n}\n\n.round-item {\n\tfont-size: 24rpx;\n\tcolor: #666666;\n\tpadding: 8rpx 16rpx;\n\tbackground-color: #F5F7FA;\n\tborder-radius: 20rpx;\n\tmargin: 0 10rpx 10rpx;\n\ttransition: all 0.3s;\n\t\n\t&.active {\n\t\tbackground-color: #E6F7FF;\n\t\tcolor: #1677FF;\n\t\tfont-weight: 500;\n\t}\n}\n\n/* 添加排序相关样式 */\n.sort-container {\n\tposition: relative;\n\tmargin-left: 20rpx;\n\tflex-shrink: 0;\n}\n\n.sort-button {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: #F5F7FA;\n\tborder-radius: 30rpx;\n\tpadding: 10rpx 20rpx;\n\ttransition: all 0.3s;\n\t\n\t&:active {\n\t\tbackground-color: #E6F7FF;\n\t}\n}\n\n.sort-text {\n\tfont-size: 26rpx;\n\tcolor: #666666;\n\tmargin-right: 8rpx;\n}\n\n.sort-options {\n\tposition: absolute;\n\ttop: calc(100% + 10rpx);\n\tright: 0;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 10rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n\tpadding: 10rpx;\n\tz-index: 100;\n\tmin-width: 180rpx;\n}\n\n.sort-option {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 16rpx 20rpx;\n\tfont-size: 26rpx;\n\tcolor: #666666;\n\ttransition: all 0.2s;\n\tborder-radius: 6rpx;\n\t\n\t&:not(:last-child) {\n\t\tmargin-bottom: 6rpx;\n\t}\n\t\n\t&:active {\n\t\tbackground-color: #F5F7FA;\n\t}\n\t\n\t&.active {\n\t\tcolor: #1677FF;\n\t\tbackground-color: #E6F7FF;\n\t}\n}\n\n.rotate-icon {\n\ttransform: rotate(180deg);\n\ttransition: transform 0.3s;\n}\n\n/* 骨架屏样式 */\n.skeleton-container {\n\tpadding: 0 20rpx;\n}\n\n.skeleton-item {\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 20rpx;\n\tpadding: 20rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(22, 119, 255, 0.15);\n}\n\n.skeleton-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.skeleton-point {\n\twidth: 200rpx;\n\theight: 32rpx;\n\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n\tbackground-size: 200% 100%;\n\tanimation: skeleton-loading 1.5s infinite;\n\tborder-radius: 4rpx;\n}\n\n.skeleton-status {\n\twidth: 80rpx;\n\theight: 24rpx;\n\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n\tbackground-size: 200% 100%;\n\tanimation: skeleton-loading 1.5s infinite;\n\tborder-radius: 12rpx;\n}\n\n.skeleton-content {\n\tpadding-top: 10rpx;\n}\n\n.skeleton-line {\n\theight: 28rpx;\n\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n\tbackground-size: 200% 100%;\n\tanimation: skeleton-loading 1.5s infinite;\n\tborder-radius: 4rpx;\n\tmargin-bottom: 12rpx;\n\t\n\t&.short {\n\t\twidth: 60%;\n\t}\n\t\n\t&.medium {\n\t\twidth: 80%;\n\t}\n}\n\n@keyframes skeleton-loading {\n\t0% {\n\t\tbackground-position: 200% 0;\n\t}\n\t100% {\n\t\tbackground-position: -200% 0;\n\t}\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./route-detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./route-detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558437809\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
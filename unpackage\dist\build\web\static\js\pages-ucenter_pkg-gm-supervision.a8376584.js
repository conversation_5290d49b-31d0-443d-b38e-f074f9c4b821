(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-ucenter_pkg-gm-supervision"],{"1f16":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"fixed-header"},[a("v-uni-view",{staticClass:"page-header"},[a("v-uni-text",{staticClass:"page-title"},[t._v("指派任务监督")]),a("v-uni-text",{staticClass:"page-subtitle"},[t._v("实时监控指派任务的执行情况")]),a("v-uni-view",{staticClass:"timing-explanation"},[a("v-uni-text",{staticClass:"explanation-text"},[t._v("💡 超时说明：执行中任务超过7天为警告，超过14天为超时")])],1)],1),a("v-uni-view",{staticClass:"stats-section"},[a("v-uni-view",{staticClass:"stats-grid"},[a("v-uni-view",{staticClass:"stat-card",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.filterByStatus("assigned_to_responsible")}}},[a("v-uni-view",{staticClass:"stat-number assigned"},[t._v(t._s(t.taskStats.assigned||0))]),a("v-uni-text",{staticClass:"stat-label"},[t._v("执行中")])],1),a("v-uni-view",{staticClass:"stat-card",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.filterByStatus("completed_by_responsible")}}},[a("v-uni-view",{staticClass:"stat-number pending"},[t._v(t._s(t.taskStats.pending||0))]),a("v-uni-text",{staticClass:"stat-label"},[t._v("待确认")])],1),a("v-uni-view",{staticClass:"stat-card",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.filterByStatus("final_completed")}}},[a("v-uni-view",{staticClass:"stat-number completed"},[t._v(t._s(t.taskStats.completed||0))]),a("v-uni-text",{staticClass:"stat-label"},[t._v("已完成")])],1),a("v-uni-view",{staticClass:"stat-card",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.filterByStatus("overdue")}}},[a("v-uni-view",{staticClass:"stat-number overdue"},[t._v(t._s(t.taskStats.overdue||0))]),a("v-uni-text",{staticClass:"stat-label"},[t._v("已超时")])],1)],1)],1),a("v-uni-view",{staticClass:"filter-tabs"},[a("v-uni-view",{staticClass:"filter-tab",class:{active:"all"===t.currentFilter},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setFilter("all")}}},[t._v("全部")]),a("v-uni-view",{staticClass:"filter-tab",class:{active:"assigned_to_responsible"===t.currentFilter},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setFilter("assigned_to_responsible")}}},[t._v("执行中")]),a("v-uni-view",{staticClass:"filter-tab",class:{active:"completed_by_responsible"===t.currentFilter},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setFilter("completed_by_responsible")}}},[t._v("待确认")]),a("v-uni-view",{staticClass:"filter-tab",class:{active:"overdue"===t.currentFilter},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setFilter("overdue")}}},[t._v("已超时")])],1)],1),t.filteredTaskList.length>0?a("v-uni-scroll-view",{staticClass:"task-scroll-view",attrs:{"scroll-y":!0,"lower-threshold":100},on:{scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.loadMore.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"task-list"},[t._l(t.filteredTaskList,(function(e,n){return a("v-uni-view",{key:e._id,staticClass:"task-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goToTaskDetail(e)}}},[a("v-uni-view",{staticClass:"task-header"},[a("v-uni-view",{staticClass:"task-title-row"},[a("v-uni-text",{staticClass:"task-name"},[t._v(t._s(e.name))]),a("v-uni-view",{staticClass:"task-status",class:{"status-assigned":"assigned_to_responsible"===e.workflowStatus,"status-pending":"completed_by_responsible"===e.workflowStatus,"status-completed":"final_completed"===e.workflowStatus}},[t._v(t._s(t.getStatusText(e.workflowStatus)))])],1),a("v-uni-text",{staticClass:"task-project"},[t._v(t._s(e.project||"未分类"))])],1),a("v-uni-view",{staticClass:"task-content"},[a("v-uni-text",{staticClass:"task-description"},[t._v(t._s(e.description||"暂无描述"))])],1),a("v-uni-view",{staticClass:"task-footer"},[a("v-uni-view",{staticClass:"responsible-info"},[a("v-uni-text",{staticClass:"responsible-label"},[t._v("负责人：")]),a("v-uni-text",{staticClass:"responsible-name"},[t._v(t._s(t.getResponsibleName(e.responsibleUserId)))])],1),a("v-uni-view",{staticClass:"time-info"},[a("v-uni-text",{staticClass:"time-label"},[t._v(t._s(t.getTimeLabel(e)))]),a("v-uni-text",{staticClass:"time-value",class:{overdue:t.isOverdue(e),warning:t.isWarning(e)}},[t._v(t._s(t.getTimeValue(e)))]),"assigned_to_responsible"===e.workflowStatus?a("v-uni-view",{staticClass:"timing-badge"},[t.isOverdue(e)?a("v-uni-text",{staticClass:"timing-tag overdue-tag"},[t._v("超时")]):t.isWarning(e)?a("v-uni-text",{staticClass:"timing-tag warning-tag"},[t._v("警告")]):a("v-uni-text",{staticClass:"timing-tag normal-tag"},[t._v("正常")])],1):t._e()],1)],1),"completed_by_responsible"===e.workflowStatus?a("v-uni-view",{staticClass:"quick-actions"},[a("v-uni-view",{staticClass:"action-btn confirm-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.quickConfirm(e)}}},[a("v-uni-text",[t._v("确认完成")])],1),a("v-uni-view",{staticClass:"action-btn reject-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.quickReject(e)}}},[a("v-uni-text",[t._v("退回重做")])],1)],1):t._e()],1)})),a("v-uni-view",{staticClass:"load-more-state"},[t.loadingMore?a("v-uni-view",{staticClass:"loading-more"},[a("v-uni-view",{staticClass:"loading-icon"}),a("v-uni-text",{staticClass:"loading-text"},[t._v("加载更多中...")])],1):!t.pagination.hasMore&&t.taskList.length>0?a("v-uni-view",{staticClass:"no-more"},[a("v-uni-text",{staticClass:"no-more-text"},[t._v("— 没有更多数据了 —")])],1):0!==t.taskList.length||t.loading?t._e():a("v-uni-view",{staticClass:"no-more"},[a("v-uni-text",{staticClass:"no-more-text"},[t._v("暂无数据")])],1)],1)],2)],1):t.loading?t._e():a("v-uni-view",{staticClass:"empty-state"},[a("v-uni-view",{staticClass:"empty-content"},[a("v-uni-image",{staticClass:"empty-image",attrs:{src:"/static/empty/empty_task.png",mode:"aspectFit"}}),a("v-uni-text",{staticClass:"empty-text"},[t._v(t._s(t.getEmptyText()))])],1)],1),t.loading?a("v-uni-view",{staticClass:"loading-state"},[a("v-uni-view",{staticClass:"custom-loading"},[a("v-uni-text",{staticClass:"loading-text"},[t._v("加载中...")])],1)],1):t._e(),t.showModal?a("v-uni-view",{staticClass:"custom-modal",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeModal.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"modal-content",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[a("v-uni-view",{staticClass:"modal-header"},[a("v-uni-text",{staticClass:"modal-title"},[t._v(t._s(t.modalData.title))])],1),a("v-uni-view",{staticClass:"modal-body"},[a("v-uni-textarea",{staticClass:"modal-input",attrs:{placeholder:t.modalData.placeholder,maxlength:200,"auto-height":!0},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.handleInputFocus.apply(void 0,arguments)}},model:{value:t.modalInput,callback:function(e){t.modalInput=e},expression:"modalInput"}}),a("v-uni-view",{staticClass:"input-counter"},[t._v(t._s(t.modalInput.length)+"/200")])],1),a("v-uni-view",{staticClass:"modal-footer"},[a("v-uni-view",{staticClass:"modal-btn cancel-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeModal.apply(void 0,arguments)}}},[t._v("取消")]),a("v-uni-view",{staticClass:"modal-btn confirm-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmModal.apply(void 0,arguments)}}},[t._v(t._s(t.modalData.confirmText))])],1)],1)],1):t._e()],1)},i=[]},"47fd":function(t,e,a){"use strict";a.r(e);var n=a("ee04"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},"4c3d":function(t,e,a){"use strict";var n=a("f9bf"),i=a.n(n);i.a},"73e1":function(t,e,a){"use strict";var n=a("29d8");t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(n)},"795c":function(t,e,a){"use strict";var n=a("8bdb"),i=a("db04").start,s=a("73e1");n({target:"String",proto:!0,forced:s},{padStart:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},"7fc2":function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.calculateEndTime=v,e.calculateRoundTime=b,e.default=void 0,e.detectTimeZone=m,e.ensureCorrectTimeZone=h,e.formatDate=r,e.formatTime=g,e.getDaysDiff=u,e.getMonthFirstDay=c,e.getMonthLastDay=d,e.getRelativeTime=l,e.isTimeInRange=p,e.isToday=f,e.preprocessDates=w,e.safeDateFormat=_,e.smartFormatDate=k;var i=n(a("9b1b")),s=n(a("fcf3")),o=n(a("5de6"));function r(t){var e,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";if(!t)return"";if("string"===typeof t)if(!t.includes("T")||!t.includes("Z")&&t.includes("+")){if(t.includes(",")&&(t.includes("AM")||t.includes("PM"))){var n=t.split(",");if(n.length>=1){var i=n[0].trim(),s=i.split("/");if(3===s.length){var o=s[0].padStart(2,"0"),r=s[1].padStart(2,"0"),l=s[2];t="".concat(l,"/").concat(o,"/").concat(r)}}}e=new Date(t.replace(/-/g,"/"))}else{console.log("检测到需要调整时区的ISO日期:",t);try{var c=t.split("T")[0],d=t.includes("T")?t.split("T")[1].split(".")[0]:"00:00:00";e=new Date("".concat(c,"T").concat(d,"+08:00")),isNaN(e.getTime())&&(e=new Date(t.replace(/-/g,"/")))}catch(m){console.warn("处理日期时区出错",m),e=new Date(t.replace(/-/g,"/"))}}else if("number"===typeof t)e=new Date(t);else{if(!(t instanceof Date))return"";e=t}isNaN(e.getTime())&&(console.warn("无效的日期格式:",t),e=new Date);var u=e.getFullYear(),f=e.getMonth()+1,b=e.getDate(),v=e.getHours(),g=e.getMinutes(),p=e.getSeconds();return a.replace(/YYYY/g,u).replace(/YY/g,String(u).slice(2)).replace(/MM/g,(f<10?"0":"")+f).replace(/M/g,f).replace(/DD/g,(b<10?"0":"")+b).replace(/D/g,b).replace(/HH/g,(v<10?"0":"")+v).replace(/H/g,v).replace(/mm/g,(g<10?"0":"")+g).replace(/m/g,g).replace(/ss/g,(p<10?"0":"")+p).replace(/s/g,p)}function l(t){if(!t)return"";var e;if("string"===typeof t){var a=(new Date).getFullYear(),n=a+3,i=t.match(/^(\d{4})/);if(i&&parseInt(i[1])>=n)return console.log("检测到远期未来日期，自动转换为今天:",t),"今天";if(t.includes(",")&&(t.includes("AM")||t.includes("PM"))){var s=t.split(",");if(s.length>=1){var o=s[0].trim(),r=o.split("/");if(3===r.length){var l=r[0].padStart(2,"0"),c=r[1].padStart(2,"0"),d=r[2];t="".concat(d,"/").concat(l,"/").concat(c)}}}e=new Date(t.replace(/-/g,"/"))}else if("number"===typeof t)e=new Date(t);else{if(!(t instanceof Date))return"";e=t}if(isNaN(e.getTime()))return console.warn("无效的日期格式:",t),"未知时间";var u=new Date,f=new Date(u);if(f.setFullYear(u.getFullYear()+1),e>f)return"今天";var b=u.getTime()-e.getTime();if(b<0)return"即将";var v=864e5;return b<6e4?"刚刚":b<36e5?Math.floor(b/6e4)+"分钟前":b<v?Math.floor(b/36e5)+"小时前":b<6048e5?Math.floor(b/v)+"天前":b<2592e6?Math.floor(b/6048e5)+"周前":b<31536e6?Math.floor(b/2592e6)+"个月前":Math.floor(b/31536e6)+"年前"}function c(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,e=t.getFullYear(),a=t.getMonth();return new Date(e,a,1)}function d(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,e=t.getFullYear(),a=t.getMonth();return new Date(e,a+1,0)}function u(t,e){var a=new Date(t),n=new Date(e);a.setHours(0,0,0,0),n.setHours(0,0,0,0);var i=n.getTime()-a.getTime();return Math.round(i/864e5)}function f(t){if(!t)return!1;var e;if("string"===typeof t){if(t.includes(",")&&(t.includes("AM")||t.includes("PM"))){var a=t.split(",");if(a.length>=1){var n=a[0].trim(),i=n.split("/");if(3===i.length){var s=i[0].padStart(2,"0"),o=i[1].padStart(2,"0"),r=i[2];t="".concat(r,"/").concat(s,"/").concat(o)}}}e=new Date(t.replace(/-/g,"/"))}else if("number"===typeof t)e=new Date(t);else{if(!(t instanceof Date))return!1;e=t}if(isNaN(e.getTime()))return console.warn("无效的日期格式:",t),!1;var l=new Date;return e.getDate()===l.getDate()&&e.getMonth()===l.getMonth()&&e.getFullYear()===l.getFullYear()}function b(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(!t||!e)return console.warn("计算轮次时间：参数不完整",t,e,a),new Date;try{var n,i=e.split(":").map(Number),s=(0,o.default)(i,2),r=s[0],l=s[1];if("string"===typeof t){if(t.includes(",")&&(t.includes("AM")||t.includes("PM"))){var c=t.split(",");if(c.length>=1){var d=c[0].trim(),u=d.split("/");if(3===u.length){var f=u[0].padStart(2,"0"),b=u[1].padStart(2,"0"),v=u[2];t="".concat(v,"/").concat(f,"/").concat(b)}}}n=new Date(t.replace(/-/g,"/"))}else n=t instanceof Date?new Date(t):new Date;return isNaN(n.getTime())?(console.warn("无效的日期格式:",t),new Date):(n.setHours(r,l,0,0),a&&"number"===typeof a&&n.setDate(n.getDate()+a),n)}catch(g){return console.error("计算轮次时间出错:",g),new Date}}function v(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60;if(!t)return console.warn("计算结束时间：开始时间无效",t),new Date;try{var a=new Date(t.getTime());return a.setMinutes(a.getMinutes()+(e||60)),a}catch(n){return console.error("计算结束时间出错:",n),new Date}}function g(t){if(!t)return"";try{var e=t.getHours().toString().padStart(2,"0"),a=t.getMinutes().toString().padStart(2,"0");return"".concat(e,":").concat(a)}catch(n){return console.error("格式化时间出错:",n),""}}function p(t,e,a){var n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return!!(t&&e&&a)&&(n?t>=e&&t<=a:t>=e&&t<a)}function m(){try{var t=new Date,e=t.getTimezoneOffset(),a=-e/60,n={8:"北京时间(UTC+8)",9:"东京时间(UTC+9)",7:"雅加达时间(UTC+7)",0:"格林威治时间(UTC+0)","-5":"纽约时间(UTC-5)","-8":"洛杉矶时间(UTC-8)"}[a]||"UTC".concat(a>=0?"+":"").concat(a);return console.log("当前时区检测: ".concat(n)),console.log("- 本地时间: ".concat(t.toString())),console.log("- ISO格式: ".concat(t.toISOString())),console.log("- 时区偏移(分钟): ".concat(e)),n}catch(i){return console.error("检测时区出错:",i),"北京时间(UTC+8)"}}function h(t){if(!t)return null;try{if("string"===typeof t&&t.endsWith("Z")&&t.includes("T"))return t;var e=new Date(t);return isNaN(e.getTime())?(console.warn("无效的日期格式:",t),t):e.toISOString()}catch(a){return console.error("时区转换错误:",a,t),t}}function w(t){if(!t)return t;if("object"===(0,s.default)(t)){if(Array.isArray(t))return t.map((function(t){return w(t)}));var e=(0,i.default)({},t);for(var a in e)e.hasOwnProperty(a)&&("string"===typeof e[a]&&(a.includes("time")||a.includes("Time")||a.includes("date")||a.includes("Date"))?e[a]=h(e[a]):"object"===(0,s.default)(e[a])&&(e[a]=w(e[a])));return e}return t}function k(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"display";if(!t)return"";try{var a;if("string"===typeof t)a=new Date(t.replace(/-/g,"/"));else if("number"===typeof t)a=new Date(t);else{if(!(t instanceof Date))return"";a=t}return isNaN(a.getTime())?(console.warn("无效的日期格式:",t),""):"storage"===e?a.toISOString():"time"===e?g(a):r(a,"YYYY-MM-DD HH:mm")}catch(n){return console.error("智能格式化日期出错:",n),""}}function _(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";try{if(!t)return"未知时间";if("string"===typeof t){var a=(new Date).getFullYear(),n=a+3,i=t.match(/^(\d{4})/);if(i&&parseInt(i[1])>=n)return console.log("安全格式化检测到远期未来日期，使用当前时间:",t),r(new Date,e)}if("string"===typeof t&&t.includes("Z"))try{var s=new Date(t),o=new Date(s.getTime()+288e5);return r(o,e)}catch(l){console.warn("UTC转本地时间失败:",l)}return r(t,e)}catch(l){return console.warn("日期安全格式化失败:",l,t),"未知时间"}}a("4626"),a("5ac7"),a("c223"),a("5c47"),a("a1c1"),a("0c26"),a("795c"),a("f7a5"),a("2c10"),a("e966"),a("fd3c"),a("64aa"),a("c9b5"),a("bf0f"),a("ab80"),a("9327");var x={formatDate:r,getRelativeTime:l,getMonthFirstDay:c,getMonthLastDay:d,getDaysDiff:u,isToday:f,calculateRoundTime:b,calculateEndTime:v,formatTime:g,isTimeInRange:p,detectTimeZone:m,ensureCorrectTimeZone:h,preprocessDates:w,smartFormatDate:k,safeDateFormat:_};e.default=x},"8b9d":function(t,e,a){"use strict";a.r(e);var n=a("1f16"),i=a("47fd");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("4c3d");var o=a("828b"),r=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"b3bfce3a",null,!1,n["a"],void 0);e["default"]=r.exports},9327:function(t,e,a){"use strict";var n=a("8bdb"),i=a("9f69"),s=a("1ded").f,o=a("c435"),r=a("9e70"),l=a("b6a1"),c=a("862c"),d=a("0931"),u=a("a734"),f=i("".slice),b=Math.min,v=d("endsWith"),g=!u&&!v&&!!function(){var t=s(String.prototype,"endsWith");return t&&!t.writable}();n({target:"String",proto:!0,forced:!g&&!v},{endsWith:function(t){var e=r(c(this));l(t);var a=arguments.length>1?arguments[1]:void 0,n=e.length,i=void 0===a?n:b(o(a),n),s=r(t);return f(e,i-s.length,i)===s}})},db04:function(t,e,a){"use strict";var n=a("bb80"),i=a("c435"),s=a("9e70"),o=a("f298"),r=a("862c"),l=n(o),c=n("".slice),d=Math.ceil,u=function(t){return function(e,a,n){var o,u,f=s(r(e)),b=i(a),v=f.length,g=void 0===n?" ":s(n);return b<=v||""===g?f:(o=b-v,u=l(g,d(o/g.length)),u.length>o&&(u=c(u,0,o)),t?f+u:u+f)}};t.exports={start:u(!1),end:u(!0)}},eb3d:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.container[data-v-b3bfce3a]{display:flex;flex-direction:column;height:100vh;background-color:#f8f9fc}\n/* 固定头部区域 */.fixed-header[data-v-b3bfce3a]{flex-shrink:0;\n  /* 不允许收缩 */padding:%?20?%;background-color:#f8f9fc}.page-header[data-v-b3bfce3a]{background:linear-gradient(135deg,#38bdf8,#0ea5e9 70%,#3b82f6);\n  /* 更轻盈的蓝色渐变 - 从浅到深 */border-radius:%?20?%;padding:%?40?% %?30?%;margin-bottom:%?30?%;color:#fff;box-shadow:0 %?6?% %?24?% rgba(56,189,248,.25)\n  /* 更轻的阴影 */}.page-title[data-v-b3bfce3a]{font-size:%?36?%;font-weight:700;display:block;margin-bottom:%?10?%}.page-subtitle[data-v-b3bfce3a]{font-size:%?28?%;opacity:.9;margin-bottom:%?20?%}.timing-explanation[data-v-b3bfce3a]{background:hsla(0,0%,100%,.15);border-radius:%?12?%;padding:%?16?% %?20?%;margin-top:%?20?%}.explanation-text[data-v-b3bfce3a]{font-size:%?24?%;opacity:.95;line-height:1.4}.stats-section[data-v-b3bfce3a]{margin-bottom:%?30?%}.stats-grid[data-v-b3bfce3a]{display:grid;grid-template-columns:repeat(4,1fr);gap:%?20?%}.stat-card[data-v-b3bfce3a]{background:#fff;border-radius:%?16?%;padding:%?30?% %?20?%;text-align:center;box-shadow:0 %?4?% %?20?% rgba(0,0,0,.05);transition:all .3s ease}.stat-card[data-v-b3bfce3a]:active{-webkit-transform:scale(.95);transform:scale(.95)}.stat-number[data-v-b3bfce3a]{font-size:%?48?%;font-weight:700;display:block;margin-bottom:%?10?%\n  /* 浅蓝色 - 执行中 */\n  /* 橙色 - 待确认 */\n  /* 绿色 - 已完成 */\n  /* 红色 - 超时 */}.stat-number.assigned[data-v-b3bfce3a]{color:#38bdf8}.stat-number.pending[data-v-b3bfce3a]{color:#f59e0b}.stat-number.completed[data-v-b3bfce3a]{color:#059669}.stat-number.overdue[data-v-b3bfce3a]{color:#dc2626}.stat-label[data-v-b3bfce3a]{font-size:%?24?%;color:#666}.filter-tabs[data-v-b3bfce3a]{display:flex;background:#fff;border-radius:%?16?%;padding:%?8?%;margin-bottom:%?30?%;box-shadow:0 %?4?% %?20?% rgba(0,0,0,.05)}.filter-tab[data-v-b3bfce3a]{flex:1;text-align:center;padding:%?20?% %?10?%;border-radius:%?12?%;font-size:%?28?%;color:#666;transition:all .3s ease}.filter-tab.active[data-v-b3bfce3a]{background:#3b82f6;\n  /* 蓝色 - 水务主题 */color:#fff}.task-list[data-v-b3bfce3a]{margin-bottom:%?30?%;padding:0 %?20?%\n  /* 左右内边距，确保与固定头部区域对齐 */}.task-item[data-v-b3bfce3a]{background:#fff;border-radius:%?16?%;padding:%?30?%;margin-bottom:%?20?%;box-shadow:0 %?4?% %?20?% rgba(0,0,0,.05);transition:all .3s ease}.task-item[data-v-b3bfce3a]:active{-webkit-transform:translateY(%?2?%);transform:translateY(%?2?%)}.task-header[data-v-b3bfce3a]{margin-bottom:%?20?%}.task-title-row[data-v-b3bfce3a]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?10?%}.task-name[data-v-b3bfce3a]{font-size:%?32?%;font-weight:700;color:#333;flex:1;margin-right:%?20?%}.task-status[data-v-b3bfce3a]{padding:%?8?% %?16?%;border-radius:%?20?%;font-size:%?24?%;color:#fff\n  /* 浅蓝色 - 执行中 */\n  /* 橙色 - 待确认 */\n  /* 绿色 - 已完成 */}.task-status.status-assigned[data-v-b3bfce3a]{background:#38bdf8}.task-status.status-pending[data-v-b3bfce3a]{background:#f59e0b}.task-status.status-completed[data-v-b3bfce3a]{background:#059669}.task-project[data-v-b3bfce3a]{font-size:%?24?%;color:#666}.task-content[data-v-b3bfce3a]{margin-bottom:%?20?%}.task-description[data-v-b3bfce3a]{font-size:%?28?%;color:#666;line-height:1.6}.task-footer[data-v-b3bfce3a]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?20?%}.responsible-info[data-v-b3bfce3a], .time-info[data-v-b3bfce3a]{display:flex;align-items:center;flex-wrap:wrap;gap:%?8?%}.responsible-label[data-v-b3bfce3a], .time-label[data-v-b3bfce3a]{font-size:%?24?%;color:#999;margin-right:%?8?%}.responsible-name[data-v-b3bfce3a], .time-value[data-v-b3bfce3a]{font-size:%?24?%;color:#333}.responsible-name.overdue[data-v-b3bfce3a], .time-value.overdue[data-v-b3bfce3a]{color:#dc2626;\n  /* 红色 - 超时 */font-weight:700}.responsible-name.warning[data-v-b3bfce3a], .time-value.warning[data-v-b3bfce3a]{color:#f59e0b;\n  /* 橙色 - 警告 */font-weight:700}.timing-badge[data-v-b3bfce3a]{margin-left:%?8?%}.timing-tag[data-v-b3bfce3a]{font-size:%?20?%;padding:%?4?% %?8?%;border-radius:%?8?%;color:#fff;font-weight:700}.overdue-tag[data-v-b3bfce3a]{background:#dc2626\n  /* 红色 - 超时 */}.warning-tag[data-v-b3bfce3a]{background:#f59e0b\n  /* 橙色 - 警告 */}.normal-tag[data-v-b3bfce3a]{background:#059669\n  /* 绿色 - 正常 */}.quick-actions[data-v-b3bfce3a]{display:flex;gap:%?20?%}.action-btn[data-v-b3bfce3a]{flex:1;display:flex;align-items:center;justify-content:center;padding:%?24?% %?20?%;border-radius:%?12?%;color:#fff;transition:all .3s ease;font-weight:500}.action-btn[data-v-b3bfce3a]:active{-webkit-transform:scale(.95);transform:scale(.95);opacity:.8}.action-btn uni-text[data-v-b3bfce3a]{font-size:%?26?%;color:#fff\n  /* 确保文字是白色 */}.confirm-btn[data-v-b3bfce3a]{background-color:#4caf50\n  /* 纯绿色 - 确认完成 */}.reject-btn[data-v-b3bfce3a]{background-color:#f44336\n  /* 纯红色 - 退回重做 */}.empty-state[data-v-b3bfce3a]{display:flex;align-items:center;justify-content:center;padding:%?100?% %?40?%;min-height:%?400?%}.empty-content[data-v-b3bfce3a]{display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center}.empty-image[data-v-b3bfce3a]{width:%?200?%;height:%?200?%;margin-bottom:%?30?%;opacity:.6;display:block}.empty-text[data-v-b3bfce3a]{font-size:%?28?%;color:#999;display:block;text-align:center}.loading-state[data-v-b3bfce3a]{padding:%?40?%}.custom-loading[data-v-b3bfce3a]{display:flex;justify-content:center;align-items:center;padding:%?40?%}.loading-text[data-v-b3bfce3a]{font-size:%?28?%;color:#666}\n/* 滚动视图样式 */.task-scroll-view[data-v-b3bfce3a]{flex:1;\n  /* 占用剩余空间 */overflow:hidden\n  /* 防止内容溢出 */}\n/* 加载更多状态样式 */.load-more-state[data-v-b3bfce3a]{padding:%?40?% %?20?%;text-align:center}.loading-more[data-v-b3bfce3a]{display:flex;align-items:center;justify-content:center;gap:%?20?%}.loading-icon[data-v-b3bfce3a]{width:%?32?%;height:%?32?%;border:%?3?% solid #f3f3f3;border-top:%?3?% solid #3b82f6;border-radius:50%;-webkit-animation:spin-data-v-b3bfce3a 1s linear infinite;animation:spin-data-v-b3bfce3a 1s linear infinite}@-webkit-keyframes spin-data-v-b3bfce3a{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes spin-data-v-b3bfce3a{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.no-more[data-v-b3bfce3a]{padding:%?20?%;text-align:center}.no-more-text[data-v-b3bfce3a]{font-size:%?26?%;color:#999}\n/* 自定义弹窗样式 */.custom-modal[data-v-b3bfce3a]{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);display:flex;align-items:center;justify-content:center;z-index:9999}.modal-content[data-v-b3bfce3a]{width:80%;background-color:#fff;border-radius:%?16?%;overflow:hidden;box-shadow:0 %?8?% %?32?% rgba(0,0,0,.15);-webkit-animation:modal-in-data-v-b3bfce3a .3s ease-out;animation:modal-in-data-v-b3bfce3a .3s ease-out}@-webkit-keyframes modal-in-data-v-b3bfce3a{from{opacity:0;-webkit-transform:translateY(%?20?%) scale(.95);transform:translateY(%?20?%) scale(.95)}to{opacity:1;-webkit-transform:translateY(0) scale(1);transform:translateY(0) scale(1)}}@keyframes modal-in-data-v-b3bfce3a{from{opacity:0;-webkit-transform:translateY(%?20?%) scale(.95);transform:translateY(%?20?%) scale(.95)}to{opacity:1;-webkit-transform:translateY(0) scale(1);transform:translateY(0) scale(1)}}.modal-header[data-v-b3bfce3a]{padding:%?30?%;text-align:center;border-bottom:1px solid #f0f0f0}.modal-title[data-v-b3bfce3a]{font-size:%?32?%;font-weight:700;color:#333\n  /* 深灰色 - 简洁清晰 */}.modal-body[data-v-b3bfce3a]{padding:%?30?%}.modal-label[data-v-b3bfce3a]{font-size:%?28?%;color:#333;margin-bottom:%?20?%;display:block}.modal-input[data-v-b3bfce3a]{width:100%;min-height:%?160?%;border:1px solid #e0e0e0;border-radius:%?8?%;padding:%?20?%;font-size:%?28?%;color:#333;background-color:#f9f9f9;box-sizing:border-box;transition:border-color .3s ease}.modal-input[data-v-b3bfce3a]:focus{border-color:#3b82f6;background-color:#fff}.input-counter[data-v-b3bfce3a]{font-size:%?24?%;color:#999;text-align:right;margin-top:%?10?%}.modal-footer[data-v-b3bfce3a]{display:flex;padding:%?20?%;gap:%?20?%}.modal-btn[data-v-b3bfce3a]{flex:1;padding:%?24?%;text-align:center;font-size:%?30?%;border-radius:%?8?%;transition:all .3s ease}.modal-btn[data-v-b3bfce3a]:active{-webkit-transform:scale(.95);transform:scale(.95)}.cancel-btn[data-v-b3bfce3a]{color:#666;background-color:#f5f5f5;border:1px solid #e0e0e0}.confirm-btn[data-v-b3bfce3a]{color:#fff;background-color:#4caf50;\n  /* 绿色背景 - 与确认按钮保持一致 */font-weight:700}',""]),t.exports=e},ee04:function(t,e,a){"use strict";(function(t){a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("4626"),a("c223"),a("7a76"),a("c9b5"),a("0c26");var i=n(a("9b1b")),s=n(a("b7c7")),o=n(a("2634")),r=n(a("2fdc")),l=a("7fc2"),c={data:function(){return{taskList:[],taskStats:{assigned:0,pending:0,completed:0,overdue:0},currentFilter:"all",pagination:{page:1,size:20,total:0,hasMore:!0},loading:!1,loadingMore:!1,loadingStatus:"more",responsibleUsers:{},currentUserId:"",userRole:[],showModal:!1,modalInput:"",modalData:{title:"",label:"",placeholder:"",confirmText:"确认",callback:null,taskId:null},lastRefreshTime:null,_hasLoadedData:!1,_loadMoreBlocked:!1}},computed:{filteredTaskList:function(){return this.taskList}},onLoad:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getUserInfo();case 2:t.checkPermission()&&(t.loadTaskData(),t._hasLoadedData=!0);case 3:case"end":return e.stop()}}),e)})))()},onShow:function(){this._hasLoadedData&&(console.log("🔄 页面显示，刷新数据"),this.loadTaskData()),uni.$on("feedback-updated",this.handleTaskUpdate),uni.$on("ucenter-need-refresh",this.handleTaskUpdate),uni.$on("cross-device-update-detected",this.handleCrossDeviceUpdate)},onHide:function(){uni.$off("feedback-updated",this.handleTaskUpdate),uni.$off("ucenter-need-refresh",this.handleTaskUpdate),uni.$off("cross-device-update-detected",this.handleCrossDeviceUpdate)},onPullDownRefresh:function(){this.pagination.page=1,this.loadTaskData().then((function(){uni.stopPullDownRefresh()}))},methods:{checkPermission:function(){if(!this.currentUserId)return uni.showModal({title:"请先登录",content:"需要登录后才能访问此页面",showCancel:!1,success:function(){uni.navigateBack()}}),!1;var t=this.userRole.some((function(t){return["GM","admin"].includes(t)}));return!!t||(uni.showModal({title:"权限不足",content:"只有厂长才能访问此页面",showCancel:!1,success:function(){uni.navigateBack()}}),!1)},getUserInfo:function(){var e=this;return(0,r.default)((0,o.default)().mark((function a(){var n,i,s,r;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,n=t.getCurrentUserInfo(),e.currentUserId=n.uid,e.currentUserId){a.next=5;break}return a.abrupt("return");case 5:return i=t.database(),a.next=8,i.collection("uni-id-users").where("'_id' == $cloudEnv_uid").field("role, _id").get();case 8:s=a.sent,r=s.result,r.data&&r.data.length>0?e.userRole=r.data[0].role||[]:e.userRole=[],a.next=18;break;case 13:a.prev=13,a.t0=a["catch"](0),console.error("❌ 获取用户信息失败:",a.t0),e.currentUserId="",e.userRole=[];case 18:case"end":return a.stop()}}),a,null,[[0,13]])})))()},loadTaskData:function(){var e=arguments,a=this;return(0,r.default)((0,o.default)().mark((function n(){var r,l,c,d,u,f,b,v;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.length>0&&void 0!==e[0]&&e[0],l=e.length>1&&void 0!==e[1]&&e[1],l?(a.loadingMore=!0,a.loadingStatus="loading"):r||(a.loading=!0,a.loadingStatus="loading"),c=l?a.pagination.page+1:1,r||(a.pagination.page=c),console.log("开始加载厂长监督任务数据...",r?"(静默刷新)":l?"(加载更多，第".concat(c,"页)"):"(首次加载)"),n.prev=6,n.next=9,t.callFunction({name:"feedback-list",data:{action:"getGMSupervisionTasks",page:c,size:a.pagination.size,filter:a.currentFilter}});case 9:if(d=n.sent,!d.result||0!==d.result.code){n.next=18;break}0===(null===(u=d.result.data.list)||void 0===u?void 0:u.length)&&null!==(f=d.result.data.pagination)&&void 0!==f&&f.hasMore&&(console.warn("⚠️ 服务器返回空数据但hasMore为true，强制设置hasMore为false"),d.result.data.pagination.hasMore=!1),l?(null===(b=d.result.data.list)||void 0===b?void 0:b.length)>0?(a.taskList=[].concat((0,s.default)(a.taskList),(0,s.default)(d.result.data.list||[])),a.pagination.page=c,console.log("✅ 加载更多成功，页码更新为:",c)):(console.log("⚠️ 加载更多返回空数据，强制设置hasMore为false"),a.pagination.hasMore=!1):(a.taskList=d.result.data.list||[],r||(a.pagination.page=1)),d.result.data.pagination&&(a.pagination.total=d.result.data.pagination.total,a.pagination.hasMore=d.result.data.pagination.hasMore),!l&&d.result.data.stats&&(a.taskStats=d.result.data.stats),d.result.data.responsibleUsers&&(a.responsibleUsers=(0,i.default)((0,i.default)({},a.responsibleUsers),d.result.data.responsibleUsers)),n.next=20;break;case 18:throw console.error("云函数返回错误:",d.result),new Error((null===(v=d.result)||void 0===v?void 0:v.message)||"获取任务数据失败");case 20:n.next=26;break;case 22:n.prev=22,n.t0=n["catch"](6),console.error("加载任务数据失败:",n.t0),r||uni.showToast({title:n.t0.message||"加载失败",icon:"error",duration:3e3});case 26:return n.prev=26,l?a.loadingMore=!1:r||(a.loading=!1),a.loadingStatus=a.pagination.hasMore?"more":"noMore",n.finish(26);case 30:case"end":return n.stop()}}),n,null,[[6,22,26,30]])})))()},setFilter:function(t){this.currentFilter!==t&&(this.currentFilter=t,this.pagination.page=1,this.loadTaskData())},filterByStatus:function(t){this.setFilter(t)},getStatusText:function(t){return{assigned_to_responsible:"执行中",completed_by_responsible:"待确认",final_completed:"已完成"}[t]||"未知状态"},getResponsibleName:function(t){var e=this.responsibleUsers[t];return e?e.nickname||e.username||"未知":"未指派"},getTimeLabel:function(t){return"assigned_to_responsible"===t.workflowStatus?"指派时间：":"completed_by_responsible"===t.workflowStatus?"完成时间：":"final_completed"===t.workflowStatus?"确认时间：":"创建时间："},getTimeValue:function(t){var e;return e="assigned_to_responsible"===t.workflowStatus?t.assignedTime:"completed_by_responsible"===t.workflowStatus?t.completedByResponsibleTime:"final_completed"===t.workflowStatus?t.finalCompletedTime:t.createTime,e?(0,l.formatDate)(e,"MM-DD HH:mm"):"未知"},isOverdue:function(t){if("assigned_to_responsible"!==t.workflowStatus)return!1;if(!t.assignedTime)return!1;return Date.now()-t.assignedTime>12096e5},isWarning:function(t){if("assigned_to_responsible"!==t.workflowStatus)return!1;if(!t.assignedTime)return!1;var e=Date.now()-t.assignedTime;return e>6048e5&&e<=12096e5},handleTaskUpdate:function(){this.loadTaskData()},handleCrossDeviceUpdate:function(t){if(t.silent&&this.currentUserId){var e=this.shouldRefreshOnCrossDeviceUpdate(t);e&&(console.log("🏭 厂长监督页面收到跨设备更新通知，静默刷新数据"),this.silentRefreshData())}},shouldRefreshOnCrossDeviceUpdate:function(t){var e=["workflow_status_changed","feedback_submitted","gm_final_confirm","task_assigned"],a=t.updateTypes&&t.updateTypes.some((function(t){return e.includes(t)})),n=Date.now();return!(this.lastRefreshTime&&n-this.lastRefreshTime<1e4)&&a},silentRefreshData:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var a;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,t.lastRefreshTime=Date.now(),a=t.pagination.page,e.next=5,t.loadTaskData(!0);case 5:t.pagination.page=a,e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](0),console.error("静默刷新失败:",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})))()},loadMore:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.pagination.hasMore&&!t.loadingMore&&!t.loading){e.next=3;break}return t._loadMoreBlocked||(console.log("📄 已到达数据末尾，没有更多数据了"),t._loadMoreBlocked=!0),e.abrupt("return");case 3:return t._loadMoreBlocked=!1,console.log("🔄 开始加载更多 - 当前页:",t.pagination.page,"任务数:",t.taskList.length),e.next=7,t.loadTaskData(!1,!0);case 7:case"end":return e.stop()}}),e)})))()},goToTaskDetail:function(t){uni.navigateTo({url:"/pages/feedback_pkg/examine?id=".concat(t._id)})},quickConfirm:function(t){var e=this;this.showCustomModal({title:"确认完成",placeholder:"请输入确认意见。",confirmText:"确认完成",callback:function(a){e.confirmTask(t._id,a)}})},quickReject:function(t){var e=this;this.showCustomModal({title:"退回重做",placeholder:"请详细说明退回原因。",confirmText:"退回重做",callback:function(a){e.rejectTask(t._id,a)}})},confirmTask:function(e,a){var n=this;return(0,r.default)((0,o.default)().mark((function i(){var s,r;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,uni.showLoading({title:"确认中..."}),i.next=4,t.callFunction({name:"feedback-workflow",data:{action:"gm_final_confirm",id:e,reason:a}});case 4:if(s=i.sent,!s.result||0!==s.result.code){i.next=13;break}uni.showToast({title:"确认成功",icon:"success"}),uni.$emit("feedback-updated"),uni.$emit("ucenter-need-refresh",{id:e}),n.pagination.page=1,n.loadTaskData(),i.next=14;break;case 13:throw new Error((null===(r=s.result)||void 0===r?void 0:r.message)||"确认失败");case 14:i.next=20;break;case 16:i.prev=16,i.t0=i["catch"](0),console.error("确认任务失败:",i.t0),uni.showToast({title:i.t0.message||"确认失败",icon:"error"});case 20:return i.prev=20,uni.hideLoading(),i.finish(20);case 23:case"end":return i.stop()}}),i,null,[[0,16,20,23]])})))()},rejectTask:function(e,a){var n=this;return(0,r.default)((0,o.default)().mark((function i(){var s,r;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,uni.showLoading({title:"退回中..."}),i.next=4,t.callFunction({name:"feedback-workflow",data:{action:"updateWorkflowStatus",id:e,workflowStatus:"assigned_to_responsible",rejectReason:a}});case 4:if(s=i.sent,!s.result||0!==s.result.code){i.next=13;break}uni.showToast({title:"已退回重做",icon:"success"}),uni.$emit("feedback-updated"),uni.$emit("ucenter-need-refresh",{id:e}),n.pagination.page=1,n.loadTaskData(),i.next=14;break;case 13:throw new Error((null===(r=s.result)||void 0===r?void 0:r.message)||"退回失败");case 14:i.next=20;break;case 16:i.prev=16,i.t0=i["catch"](0),console.error("退回任务失败:",i.t0),uni.showToast({title:i.t0.message||"退回失败",icon:"error"});case 20:return i.prev=20,uni.hideLoading(),i.finish(20);case 23:case"end":return i.stop()}}),i,null,[[0,16,20,23]])})))()},getEmptyText:function(){return{all:"暂无指派任务",assigned_to_responsible:"暂无执行中的任务",completed_by_responsible:"暂无待确认的任务",overdue:"暂无超时任务"}[this.currentFilter]||"暂无数据"},showCustomModal:function(t){this.modalData={title:t.title||"",label:t.label||"",placeholder:t.placeholder||"",confirmText:t.confirmText||"确认",callback:t.callback||null},this.modalInput="",this.showModal=!0},closeModal:function(){this.showModal=!1,this.modalInput="",this.modalData={title:"",label:"",placeholder:"",confirmText:"确认",callback:null}},confirmModal:function(){var t=this.modalInput.trim();t?(this.modalData.callback&&this.modalData.callback(t),this.closeModal()):uni.showToast({title:"请输入内容",icon:"none"})},handleInputFocus:function(){}}};e.default=c}).call(this,a("861b")["uniCloud"])},f298:function(t,e,a){"use strict";var n=a("497b"),i=a("9e70"),s=a("862c"),o=RangeError;t.exports=function(t){var e=i(s(this)),a="",r=n(t);if(r<0||r===1/0)throw new o("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(e+=e))1&r&&(a+=e);return a}},f9bf:function(t,e,a){var n=a("eb3d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("452f57ac",n,!0,{sourceMap:!1,shadowMode:!1})}}]);
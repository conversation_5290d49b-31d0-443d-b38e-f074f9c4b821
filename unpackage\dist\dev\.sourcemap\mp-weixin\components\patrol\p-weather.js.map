{"version": 3, "sources": ["webpack:///D:/Xwzc/components/patrol/p-weather.vue?9ffc", "webpack:///D:/Xwzc/components/patrol/p-weather.vue?2c5a", "webpack:///D:/Xwzc/components/patrol/p-weather.vue?204c", "webpack:///D:/Xwzc/components/patrol/p-weather.vue?4227", "uni-app:///components/patrol/p-weather.vue", "webpack:///D:/Xwzc/components/patrol/p-weather.vue?fc53", "webpack:///D:/Xwzc/components/patrol/p-weather.vue?e3bf"], "names": ["name", "props", "mini", "type", "default", "data", "weather", "temperature", "weatherCode", "humidity", "windPower", "windDirection", "updateTime", "air", "address", "loading", "error", "<PERSON><PERSON><PERSON><PERSON>", "computed", "weatherIconText", "airQualityLevel", "created", "methods", "fetchData", "cache<PERSON>ey", "weatherData", "locationData", "Object", "getLocationFromIP", "uni", "url", "key", "output", "success", "resolve", "adcode", "location", "reject", "fail", "fetchWeatherByAdcode", "parseWeatherData", "result", "getWeatherCodeFromText", "getCachedData", "console", "cacheData", "timestamp", "isCacheExpired", "refresh", "clearCache"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiDvnB;AACA;AACA;AACA;AAHA,eAIA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;QACAA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;;MAEA;MACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QACA;MACA;MAEA;IACA;IACAC;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBAAA;gBAGA;gBACA;kBACAjB;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBACAY;gBACAC;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAAA;gBAAA,OAGA;cAAA;gBAAAD;gBAEA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAEAA;gBACA;gBACA;cAAA;gBAGA;gBACA;kBACAE;kBACA;kBAEA;oBACArB;oBACAO;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAc;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACAC;oBACAC;oBACAzB;sBACA0B;sBACAC;oBACA;oBACAC;sBACA;wBACA;wBACA;;wBAEA;wBACA;wBAEAC;0BACAC;0BACArB;0BACAsB;wBACA;sBACA;wBAAA;wBACAC;sBACA;oBACA;oBACAC;sBACAD;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACAV;oBACAC;oBACAzB;sBACA0B;sBACAI;sBACAH;oBACA;oBACAC;sBACA;wBACA;wBACA;wBACAC;sBACA;wBAAA;wBACAG;sBACA;oBACA;oBACAC;sBACAD;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAG;MACA;QACAlC;QACAO;MACA;MAEA;QACA;QACA;UACA;UACA;;UAEA;UACA4B;;UAEA;UACA;UACA;UACA;UAEAA;YACAnC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;QACA;UACA;UACA6B;YACAnC;YAAA;YACAC;YACAC;YAAA;YACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA;QACA;QACA6B;UACAnC;UAAA;UACAC;UACAC;UAAA;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MAEA;IACA;IAEA;IACA8B;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;QACA;MACA;QACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAhB;UACAxB;UACAyC;QACA;MACA;QACAF;MACA;IACA;IAEA;IACAG;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACApB;QACAe;MACA;QACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACldA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,onCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/patrol/p-weather.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./p-weather.vue?vue&type=template&id=5192dacf&\"\nvar renderjs\nimport script from \"./p-weather.vue?vue&type=script&lang=js&\"\nexport * from \"./p-weather.vue?vue&type=script&lang=js&\"\nimport style0 from \"./p-weather.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/patrol/p-weather.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-weather.vue?vue&type=template&id=5192dacf&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.weather.weather.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-weather.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-weather.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"p-weather\" :class=\"{'p-weather--mini': mini}\">\n    <view class=\"p-weather__content\">\n      <view class=\"p-weather__main-info\">\n        <text class=\"p-weather__icon-text\">{{ weatherIconText }}</text>\n        <view class=\"p-weather__temp-container\">\n          <text class=\"p-weather__temp\">{{ weather.temperature }}°</text>\n          <text\n            class=\"p-weather__text\"\n            :class=\"{'p-weather__text--scroll': weather.weather.length > 3}\"\n          >\n            {{ weather.weather }}\n          </text>\n        </view>\n      </view>\n      \n      <view class=\"p-weather__secondary\" v-if=\"!mini\">\n        <view class=\"p-weather__location\" v-if=\"address\">\n          <text class=\"p-weather__location-icon uni-icons-location\"></text>\n          <text class=\"p-weather__location-text\">{{ address }}</text>\n        </view>\n        \n        <view class=\"p-weather__details\">\n          <view class=\"p-weather__detail-item\">\n            <text class=\"p-weather__detail-value\">{{ weather.humidity }}</text>\n            <text class=\"p-weather__detail-label\">湿度</text>\n          </view>\n          <view class=\"p-weather__detail-item\">\n            <text class=\"p-weather__detail-value\">{{ weather.windPower }}</text>\n            <text class=\"p-weather__detail-label\">风力</text>\n          </view>\n          <view class=\"p-weather__detail-item\" v-if=\"air\">\n            <text class=\"p-weather__detail-value\" :class=\"{\n              'p-weather__air-excellent': airQualityLevel === 'excellent',\n              'p-weather__air-good': airQualityLevel === 'good',\n              'p-weather__air-moderate': airQualityLevel === 'moderate',\n              'p-weather__air-poor': airQualityLevel === 'poor',\n              'p-weather__air-bad': airQualityLevel === 'bad',\n              'p-weather__air-severe': airQualityLevel === 'severe'\n            }\">{{ air.aqi_name }}</text>\n            <text class=\"p-weather__detail-label\">空气</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n/**\n * 天气组件\n * 基于腾讯地图天气API，显示实时天气信息\n */\nexport default {\n  name: 'p-weather',\n  props: {\n    // 是否为迷你模式（只显示温度和天气状态）\n    mini: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      weather: {\n        weather: '获取中...',\n        temperature: '--',\n        weatherCode: 'default',\n        humidity: '--',\n        windPower: '--',\n        windDirection: '--',\n        updateTime: '--'\n      },\n      air: null,\n      address: '',\n      loading: false,\n      error: null,\n      apiKey: '5MPBZ-FCW63-3C43B-R7G72-UOSAO-ZWBTJ' // 腾讯地图API Key\n    };\n  },\n  computed: {\n    // 根据天气代码返回对应的表情符号\n    weatherIconText() {\n      // 如果weatherCode不存在，返回默认图标\n      if (!this.weather || !this.weather.weatherCode) return '🌈';\n      \n      // 腾讯地图天气代码映射表\n      const iconMap = {\n        '00': '☀️', // 晴\n        '01': '🌥️', // 多云\n        '02': '☁️', // 阴\n        '03': '🌦️', // 阵雨\n        '04': '⛈️', // 雷阵雨\n        '05': '⛈️', // 雷阵雨伴有冰雹\n        '06': '🌨️', // 雨夹雪\n        '07': '🌧️', // 小雨\n        '08': '🌧️', // 中雨\n        '09': '🌧️', // 大雨\n        '10': '🌧️', // 暴雨\n        '11': '🌧️', // 大暴雨\n        '12': '🌧️', // 特大暴雨\n        '13': '🌨️', // 阵雪\n        '14': '❄️', // 小雪\n        '15': '❄️', // 中雪\n        '16': '❄️', // 大雪\n        '17': '❄️', // 暴雪\n        '18': '🌫️', // 雾\n        '19': '🌧️', // 冻雨\n        '20': '🌪️', // 沙尘暴\n        '21': '🌧️', // 小到中雨\n        '22': '🌧️', // 中到大雨\n        '23': '🌧️', // 大到暴雨\n        '24': '🌧️', // 暴雨到大暴雨\n        '25': '🌧️', // 大暴雨到特大暴雨\n        '26': '❄️', // 小到中雪\n        '27': '❄️', // 中到大雪\n        '28': '❄️', // 大到暴雪\n        '29': '🌫️', // 浮尘\n        '30': '🌫️', // 扬沙\n        '31': '🌪️', // 强沙尘暴\n        '32': '🌫️', // 轻雾\n        '33': '🌫️', // 大雾\n        '34': '🌫️', // 特强浓雾\n        '35': '🌡️', // 热\n        '36': '🌡️', // 冷\n        '37': '🌪️', // 龙卷风\n        '38': '🌧️', // 雨\n        '39': '🌨️', // 雪\n        '40': '☔️', // 阵雨转晴\n        '41': '⛈️', // 雷阵雨转晴\n        '42': '🌤️', // 晴转多云\n        '43': '🌥️', // 多云转晴\n        '44': '☁️', // 阴转晴\n        '45': '🌦️', // 晴转雨\n        '46': '🌨️', // 晴转雪\n        '47': '🌫️', // 霾转晴\n        '48': '🌫️', // 晴转霾\n        '49': '🌪️', // 扬沙转晴\n        '50': '🌪️', // 晴转扬沙\n        '51': '🌫️', // 雾转晴\n        '52': '🌤️', // 晴转雾\n        '53': '🌫️', // 霾\n        '54': '💨', // 大风\n        '55': '🌪️', // 飑\n        '56': '🌡️', // 寒潮\n        '57': '🌡️', // 热浪\n        '58': '🌫️', // 轻度霾\n        '59': '🌫️', // 中度霾\n        '60': '🌫️', // 重度霾\n        // 默认图标\n        'default': '🌈'\n      };\n      \n      return iconMap[this.weather.weatherCode] || iconMap['default'];\n    },\n    airQualityLevel() {\n      if (!this.air) return '';\n      \n      // 根据AQI数值确定空气质量等级\n      const aqi = parseInt(this.air.aqi);\n      if (aqi <= 50) return 'excellent';\n      if (aqi <= 100) return 'good';\n      if (aqi <= 150) return 'moderate';\n      if (aqi <= 200) return 'poor';\n      if (aqi <= 300) return 'bad';\n      return 'severe';\n    }\n  },\n  created() {\n    // 组件创建时立即加载天气数据\n    this.fetchData();\n  },\n  methods: {\n    async fetchData() {\n      if (this.loading) return;\n      \n      this.loading = true;\n      this.error = null;\n      \n      try {\n        // 在加载前先设置显示加载状态\n        this.weather = {\n          weather: '获取中...',\n          temperature: '--',\n          weatherCode: 'default',\n          humidity: '--',\n          windPower: '--',\n          windDirection: '--',\n          updateTime: '--'\n        };\n        \n        // 尝试从缓存获取天气数据\n        const cacheKey = 'weather_data';\n        let weatherData = this.getCachedData(cacheKey);\n        \n        if (!weatherData || this.isCacheExpired(weatherData.timestamp)) {\n          // 1. 首先获取位置信息\n          const locationData = await this.getLocationFromIP();\n          \n          // 2. 使用位置信息的adcode获取天气\n          weatherData = await this.fetchWeatherByAdcode(locationData.adcode);\n          \n          // 设置地址\n          this.address = locationData.address;\n          \n          // 缓存天气数据\n          this.cacheData(cacheKey, weatherData);\n        } else {\n          weatherData = weatherData.data;\n          // 从缓存恢复地址\n          this.address = weatherData.address || '';\n        }\n        \n        // 更新天气数据到界面，只在成功获取数据后才更新\n        if (weatherData && weatherData.weather) {\n          Object.assign(this.weather, weatherData.weather);\n          this.air = weatherData.air;\n          \n          this.$emit('weather-loaded', {\n            weather: weatherData.weather,\n            air: weatherData.air,\n            address: this.address\n          });\n        }\n      } catch (error) {\n        this.error = error.message || '获取天气数据失败';\n        this.$emit('weather-error', this.error);\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    // 从IP获取位置信息（包括adcode和地址）\n    async getLocationFromIP() {\n      return new Promise((resolve, reject) => {\n        uni.request({\n          url: 'https://apis.map.qq.com/ws/location/v1/ip',\n          data: {\n            key: this.apiKey,\n            output: 'json'\n          },\n          success: (res) => {\n            if (res.statusCode === 200 && res.data && res.data.status === 0) {\n              const result = res.data.result;\n              const adInfo = result.ad_info;\n              \n              // 构建地址文本\n              const address = `${adInfo.province}${adInfo.city}${adInfo.district}`;\n              \n              resolve({\n                adcode: adInfo.adcode,\n                address: address,\n                location: result.location\n              });\n            } else {\n              reject(new Error(res.data?.message || 'IP定位请求失败'));\n            }\n          },\n          fail: (err) => {\n            reject(new Error(err.errMsg || 'IP定位请求失败'));\n          }\n        });\n      });\n    },\n    \n    // 使用adcode获取天气数据\n    async fetchWeatherByAdcode(adcode) {\n      return new Promise((resolve, reject) => {\n        uni.request({\n          url: 'https://apis.map.qq.com/ws/weather/v1/',\n          data: {\n            key: this.apiKey,\n            adcode: adcode,\n            output: 'json'\n          },\n          success: (res) => {\n            if (res.statusCode === 200 && res.data && res.data.status === 0) {\n              // 处理并标准化天气数据\n              const weatherData = this.parseWeatherData(res.data);\n              resolve(weatherData);\n            } else {\n              reject(new Error(res.data?.message || '获取天气数据失败'));\n            }\n          },\n          fail: (err) => {\n            reject(new Error(err.errMsg || '请求天气数据失败'));\n          }\n        });\n      });\n    },\n    \n    // 解析腾讯地图天气API返回的数据\n    parseWeatherData(data) {\n      const result = {\n        weather: {},\n        air: null\n      };\n      \n      try {\n        // 处理实时天气数据 - 腾讯天气API返回格式为 result.realtime[0].infos\n        if (data.result && data.result.realtime && data.result.realtime[0] && data.result.realtime[0].infos) {\n          const realtime = data.result.realtime[0];\n          const infos = realtime.infos;\n          \n          // 保存地址信息供缓存使用\n          result.address = `${realtime.province}${realtime.city}${realtime.district}`;\n          \n          // 根据文档，获取天气码 (https://lbs.qq.com/service/webService/webServiceGuide/weatherinfo)\n          // 将天气文本转换为天气码\n          const weatherText = infos.weather || '';\n          const weatherCode = this.getWeatherCodeFromText(weatherText);\n          \n          result.weather = {\n            weather: infos.weather || '未知',            // 天气文本\n            temperature: String(infos.temperature || '--'),  // 确保温度是字符串\n            weatherCode: weatherCode,                   // 天气代码\n            humidity: String(infos.humidity || '--') + '%',  // 确保湿度是字符串 \n            windPower: infos.wind_power || '--',        // 风力\n            windDirection: infos.wind_direction || '--', // 风向\n            updateTime: realtime.update_time || '--'   // 更新时间\n          };\n        } else {\n          // 设置默认值防止界面显示错误\n          result.weather = {\n            weather: '获取中...', // 使用一致的加载中文本\n            temperature: '--',\n            weatherCode: 'default', // 使用default确保显示默认天气图标\n            humidity: '--',\n            windPower: '--',\n            windDirection: '--',\n            updateTime: '--'\n          };\n        }\n      } catch (error) {\n        // 出错时设置默认值\n        result.weather = {\n          weather: '获取中...', // 使用一致的加载中文本\n          temperature: '--',\n          weatherCode: 'default', // 使用default确保显示默认天气图标\n          humidity: '--',\n          windPower: '--',\n          windDirection: '--',\n          updateTime: '--'\n        };\n      }\n      \n      return result;\n    },\n    \n    // 根据天气文本获取相应的天气代码\n    getWeatherCodeFromText(text) {\n      // 天气文本到代码的映射表\n      const weatherMapping = {\n        '晴': '00',\n        '晴天': '00',\n        '多云': '01',\n        '阴': '02',\n        '阵雨': '03',\n        '雷阵雨': '04',\n        '雷阵雨伴有冰雹': '05',\n        '雨夹雪': '06',\n        '小雨': '07',\n        '中雨': '08',\n        '大雨': '09',\n        '暴雨': '10',\n        '大暴雨': '11',\n        '特大暴雨': '12',\n        '阵雪': '13',\n        '小雪': '14',\n        '中雪': '15',\n        '大雪': '16',\n        '暴雪': '17',\n        '雾': '18',\n        '冻雨': '19',\n        '沙尘暴': '20',\n        '小到中雨': '21',\n        '中到大雨': '22',\n        '大到暴雨': '23',\n        '暴雨到大暴雨': '24',\n        '大暴雨到特大暴雨': '25',\n        '小到中雪': '26',\n        '中到大雪': '27',\n        '大到暴雪': '28',\n        '浮尘': '29',\n        '扬沙': '30',\n        '强沙尘暴': '31',\n        '轻雾': '32',\n        '大雾': '33',\n        '特强浓雾': '34',\n        '热': '35',\n        '冷': '36',\n        '龙卷风': '37',\n        '雨': '38',\n        '雪': '39',\n        '阵雨转晴': '40',\n        '雷阵雨转晴': '41',\n        '晴转多云': '42',\n        '多云转晴': '43',\n        '阴转晴': '44',\n        '晴转雨': '45',\n        '晴转雪': '46',\n        '霾转晴': '47',\n        '晴转霾': '48',\n        '扬沙转晴': '49',\n        '晴转扬沙': '50',\n        '雾转晴': '51',\n        '晴转雾': '52',\n        '霾': '53',\n        '大风': '54',\n        '飑': '55',\n        '寒潮': '56',\n        '热浪': '57',\n        '轻度霾': '58',\n        '中度霾': '59',\n        '重度霾': '60'\n      };\n      \n      return weatherMapping[text] || 'default';\n    },\n    \n    // 获取缓存数据\n    getCachedData(key) {\n      try {\n        const cached = uni.getStorageSync(key);\n        if (cached) {\n          return JSON.parse(cached);\n        }\n      } catch (e) {\n        console.error('获取缓存数据失败', e);\n      }\n      return null;\n    },\n    \n    // 缓存数据\n    cacheData(key, data) {\n      try {\n        uni.setStorageSync(key, JSON.stringify({\n          data: data,\n          timestamp: Date.now()\n        }));\n      } catch (e) {\n        console.error('缓存数据失败', e);\n      }\n    },\n    \n    // 判断缓存是否过期（1小时）\n    isCacheExpired(timestamp) {\n      const ONE_HOUR = 60 * 60 * 1000;\n      return Date.now() - timestamp > ONE_HOUR;\n    },\n    \n    // 手动刷新天气\n    refresh() {\n      return this.fetchData();\n    },\n    \n    // 清除缓存\n    clearCache(key) {\n      try {\n        uni.removeStorageSync(key);\n        console.log('已清除缓存:', key);\n      } catch (e) {\n        console.error('清除缓存失败', e);\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\">\n.p-weather {\n  background: rgba(255, 255, 255, 0.25);\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);\n  color: #333;\n  overflow: hidden;\n  padding: 0;\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1rpx solid rgba(255, 255, 255, 0.3);\n  \n  &--mini {\n    background: rgba(255, 255, 255, 0.25);\n    border-radius: 12rpx;\n    padding: 0;\n  }\n  \n  &__content {\n    padding: 12rpx 18rpx;\n  }\n  \n  &--mini &__content {\n    padding: 8rpx 14rpx;\n  }\n  \n  &__main-info {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n  }\n  \n  &__temp-container {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  &__temp {\n    font-size: 42rpx;\n    font-weight: bold;\n    line-height: 1.1;\n    text-align: center;\n  }\n  \n  &--mini &__temp {\n    font-size: 32rpx;\n  }\n  \n  &__icon-text {\n    font-size: 52rpx;\n    line-height: 1;\n    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n    margin-right: 6rpx;\n  }\n  \n  &--mini &__icon-text {\n    font-size: 44rpx;\n    margin-right: 4rpx;\n  }\n  \n  &__text {\n    font-size: 24rpx;\n    opacity: 0.85;\n    margin-top: 2rpx;\n    text-align: center;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    max-width: 80rpx;\n  }\n  \n  &--mini &__text {\n    font-size: 22rpx;\n  }\n  \n  &__text--scroll {\n    overflow-x: auto;\n    text-overflow: unset;\n    white-space: nowrap;\n    max-width: 120rpx;\n    display: inline-block;\n  }\n  \n  &__text--scroll::-webkit-scrollbar {\n    height: 4rpx;\n    background: transparent;\n  }\n  \n  &__secondary {\n    margin-top: 10rpx;\n  }\n  \n  &__location {\n    display: flex;\n    align-items: center;\n    margin-bottom: 10rpx;\n    opacity: 0.8;\n    \n    &-icon {\n      font-size: 22rpx;\n      margin-right: 6rpx;\n    }\n    \n    &-text {\n      font-size: 22rpx;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      max-width: 400rpx;\n    }\n  }\n  \n  &__details {\n    display: flex;\n    margin-top: 12rpx;\n    border-top: 1rpx solid rgba(0, 0, 0, 0.1);\n    padding-top: 12rpx;\n  }\n  \n  &__detail-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    flex: 1;\n    position: relative;\n    \n    &:not(:last-child):after {\n      content: '';\n      position: absolute;\n      right: 0;\n      top: 10%;\n      height: 80%;\n      width: 1rpx;\n      background-color: rgba(0, 0, 0, 0.1);\n    }\n  }\n  \n  &__detail-label {\n    font-size: 20rpx;\n    opacity: 0.7;\n    margin-top: 2rpx;\n  }\n  \n  &__detail-value {\n    font-size: 24rpx;\n    font-weight: 500;\n  }\n  \n  // 空气质量颜色\n  &__air {\n    &-excellent { color: #10b981; }\n    &-good { color: #22c55e; }\n    &-moderate { color: #f59e0b; }\n    &-poor { color: #f97316; }\n    &-bad { color: #ef4444; }\n    &-severe { color: #9333ea; }\n  }\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-weather.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-weather.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558436410\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
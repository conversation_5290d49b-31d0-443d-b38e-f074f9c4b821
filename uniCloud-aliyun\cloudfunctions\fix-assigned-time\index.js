'use strict';

const db = uniCloud.database();
const dbCmd = db.command;

/**
 * 修复指派时间问题的云函数
 * 
 * 问题描述：
 * 在找茬问题反馈列表页面，负责人名字下面显示的时间应该是指派时间，
 * 但发现有些数据的指派时间(assignedTime)和创建时间(createTime)相同，
 * 这是不正确的，因为指派时间应该晚于创建时间。
 * 
 * 修复策略：
 * 1. 查找所有有负责人但assignedTime等于createTime的记录
 * 2. 从actionHistory中查找实际的指派时间
 * 3. 如果找不到actionHistory，则设置为createTime + 1小时（估算）
 */
exports.main = async (event, context) => {
  const { action } = event;
  
  try {
    switch (action) {
      case 'checkData':
        return await checkAssignedTimeData();
      case 'fixData':
        return await fixAssignedTimeData();
      default:
        return {
          code: -1,
          message: '未知操作'
        };
    }
  } catch (error) {
    console.error('❌ 修复指派时间失败:', error);
    return {
      code: -1,
      message: error.message || '修复失败'
    };
  }
};

/**
 * 检查数据问题
 */
async function checkAssignedTimeData() {
  try {
    // 查找所有有负责人的记录
    const res = await db.collection('feedback')
      .where({
        responsibleUserId: dbCmd.exists(true),
        assignedTime: dbCmd.exists(true)
      })
      .field({
        _id: true,
        name: true,
        createTime: true,
        assignedTime: true,
        responsibleUserId: true,
        actionHistory: true,
        workflowStatus: true
      })
      .get();
    
    const allRecords = res.data || [];
    const problemRecords = [];
    const normalRecords = [];
    
    allRecords.forEach(record => {
      // 检查指派时间是否等于创建时间
      if (record.assignedTime === record.createTime) {
        problemRecords.push({
          _id: record._id,
          name: record.name,
          createTime: record.createTime,
          assignedTime: record.assignedTime,
          timeDiff: 0,
          hasActionHistory: !!(record.actionHistory && record.actionHistory.length > 0)
        });
      } else {
        normalRecords.push({
          _id: record._id,
          name: record.name,
          createTime: record.createTime,
          assignedTime: record.assignedTime,
          timeDiff: record.assignedTime - record.createTime,
          hasActionHistory: !!(record.actionHistory && record.actionHistory.length > 0)
        });
      }
    });
    
    return {
      code: 0,
      message: '数据检查完成',
      data: {
        total: allRecords.length,
        problemCount: problemRecords.length,
        normalCount: normalRecords.length,
        problemRecords: problemRecords.slice(0, 10), // 只返回前10条问题记录
        normalRecords: normalRecords.slice(0, 5)     // 只返回前5条正常记录作为对比
      }
    };
  } catch (error) {
    console.error('❌ 检查数据失败:', error);
    throw error;
  }
}

/**
 * 修复数据问题
 */
async function fixAssignedTimeData() {
  try {
    // 查找所有有问题的记录
    const res = await db.collection('feedback')
      .where({
        responsibleUserId: dbCmd.exists(true),
        assignedTime: dbCmd.exists(true)
      })
      .field({
        _id: true,
        name: true,
        createTime: true,
        assignedTime: true,
        actionHistory: true
      })
      .get();
    
    const allRecords = res.data || [];
    const fixedRecords = [];
    const skippedRecords = [];
    
    for (const record of allRecords) {
      // 只修复指派时间等于创建时间的记录
      if (record.assignedTime === record.createTime) {
        let newAssignedTime = null;
        
        // 尝试从actionHistory中找到实际的指派时间
        if (record.actionHistory && record.actionHistory.length > 0) {
          const assignAction = record.actionHistory.find(action => 
            action.action === 'gm_assign' || action.action === 'assign'
          );
          
          if (assignAction && assignAction.timestamp) {
            newAssignedTime = assignAction.timestamp;
          }
        }
        
        // 如果找不到actionHistory中的指派时间，估算一个合理的时间
        if (!newAssignedTime) {
          // 设置为创建时间 + 2小时（估算审核和指派的时间）
          newAssignedTime = record.createTime + (2 * 60 * 60 * 1000);
        }
        
        // 更新数据库
        await db.collection('feedback')
          .doc(record._id)
          .update({
            assignedTime: newAssignedTime
          });
        
        fixedRecords.push({
          _id: record._id,
          name: record.name,
          oldAssignedTime: record.assignedTime,
          newAssignedTime: newAssignedTime,
          timeDiff: newAssignedTime - record.createTime,
          source: newAssignedTime === record.createTime + (2 * 60 * 60 * 1000) ? 'estimated' : 'actionHistory'
        });
      } else {
        skippedRecords.push({
          _id: record._id,
          name: record.name,
          reason: 'assignedTime already different from createTime'
        });
      }
    }
    
    return {
      code: 0,
      message: `修复完成，共修复 ${fixedRecords.length} 条记录`,
      data: {
        fixedCount: fixedRecords.length,
        skippedCount: skippedRecords.length,
        fixedRecords: fixedRecords,
        skippedRecords: skippedRecords.slice(0, 5) // 只返回前5条跳过的记录
      }
    };
  } catch (error) {
    console.error('❌ 修复数据失败:', error);
    throw error;
  }
}

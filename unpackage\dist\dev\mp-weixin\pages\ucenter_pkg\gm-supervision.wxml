<view class="container data-v-1f55f9ed"><view class="page-header data-v-1f55f9ed"><text class="page-title data-v-1f55f9ed">指派任务监督</text><text class="page-subtitle data-v-1f55f9ed">实时监控指派任务的执行情况</text></view><view class="stats-section data-v-1f55f9ed"><view class="stats-grid data-v-1f55f9ed"><view data-event-opts="{{[['tap',[['filterByStatus',['assigned_to_responsible']]]]]}}" class="stat-card data-v-1f55f9ed" bindtap="__e"><view class="stat-number assigned data-v-1f55f9ed">{{taskStats.assigned||0}}</view><text class="stat-label data-v-1f55f9ed">执行中</text></view><view data-event-opts="{{[['tap',[['filterByStatus',['completed_by_responsible']]]]]}}" class="stat-card data-v-1f55f9ed" bindtap="__e"><view class="stat-number pending data-v-1f55f9ed">{{taskStats.pending||0}}</view><text class="stat-label data-v-1f55f9ed">待确认</text></view><view data-event-opts="{{[['tap',[['filterByStatus',['final_completed']]]]]}}" class="stat-card data-v-1f55f9ed" bindtap="__e"><view class="stat-number completed data-v-1f55f9ed">{{taskStats.completed||0}}</view><text class="stat-label data-v-1f55f9ed">已完成</text></view><view data-event-opts="{{[['tap',[['filterByStatus',['overdue']]]]]}}" class="stat-card data-v-1f55f9ed" bindtap="__e"><view class="stat-number overdue data-v-1f55f9ed">{{taskStats.overdue||0}}</view><text class="stat-label data-v-1f55f9ed">超时</text></view></view></view><view class="filter-tabs data-v-1f55f9ed"><view data-event-opts="{{[['tap',[['setFilter',['all']]]]]}}" class="{{['filter-tab','data-v-1f55f9ed',(currentFilter==='all')?'active':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['setFilter',['assigned_to_responsible']]]]]}}" class="{{['filter-tab','data-v-1f55f9ed',(currentFilter==='assigned_to_responsible')?'active':'']}}" bindtap="__e">执行中</view><view data-event-opts="{{[['tap',[['setFilter',['completed_by_responsible']]]]]}}" class="{{['filter-tab','data-v-1f55f9ed',(currentFilter==='completed_by_responsible')?'active':'']}}" bindtap="__e">待确认</view><view data-event-opts="{{[['tap',[['setFilter',['overdue']]]]]}}" class="{{['filter-tab','data-v-1f55f9ed',(currentFilter==='overdue')?'active':'']}}" bindtap="__e">超时任务</view></view><block wx:if="{{$root.g0>0}}"><view class="task-list data-v-1f55f9ed"><block wx:for="{{$root.l0}}" wx:for-item="task" wx:for-index="index" wx:key="_id"><view data-event-opts="{{[['tap',[['goToTaskDetail',['$0'],[[['filteredTaskList','_id',task.$orig._id]]]]]]]}}" class="task-item data-v-1f55f9ed" bindtap="__e"><view class="task-header data-v-1f55f9ed"><view class="task-title-row data-v-1f55f9ed"><text class="task-name data-v-1f55f9ed">{{task.$orig.name}}</text><view class="{{task.m0}}">{{''+task.m1+''}}</view></view><text class="task-project data-v-1f55f9ed">{{task.$orig.project||'未分类'}}</text></view><view class="task-content data-v-1f55f9ed"><text class="task-description data-v-1f55f9ed">{{task.$orig.description||'暂无描述'}}</text></view><view class="task-footer data-v-1f55f9ed"><view class="responsible-info data-v-1f55f9ed"><text class="responsible-label data-v-1f55f9ed">负责人：</text><text class="responsible-name data-v-1f55f9ed">{{task.m2}}</text></view><view class="time-info data-v-1f55f9ed"><text class="time-label data-v-1f55f9ed">{{task.m3}}</text><text class="{{['time-value','data-v-1f55f9ed',(task.m4)?'overdue':'']}}">{{''+task.m5+''}}</text></view></view><block wx:if="{{task.$orig.workflowStatus==='completed_by_responsible'}}"><view class="quick-actions data-v-1f55f9ed"><view data-event-opts="{{[['tap',[['quickConfirm',['$0'],[[['filteredTaskList','_id',task.$orig._id]]]]]]]}}" class="action-btn confirm-btn data-v-1f55f9ed" catchtap="__e"><uni-icons vue-id="{{'692c768e-1-'+index}}" type="checkmarkempty" size="16" color="#FFFFFF" class="data-v-1f55f9ed" bind:__l="__l"></uni-icons><text class="data-v-1f55f9ed">确认完成</text></view><view data-event-opts="{{[['tap',[['quickReject',['$0'],[[['filteredTaskList','_id',task.$orig._id]]]]]]]}}" class="action-btn reject-btn data-v-1f55f9ed" catchtap="__e"><uni-icons vue-id="{{'692c768e-2-'+index}}" type="closeempty" size="16" color="#FFFFFF" class="data-v-1f55f9ed" bind:__l="__l"></uni-icons><text class="data-v-1f55f9ed">退回重做</text></view></view></block></view></block></view></block><block wx:else><view class="empty-state data-v-1f55f9ed"><image class="empty-image data-v-1f55f9ed" src="/static/empty/empty_task.png" mode="aspectFit"></image><text class="empty-text data-v-1f55f9ed">{{$root.m6}}</text></view></block><block wx:if="{{loading}}"><view class="loading-state data-v-1f55f9ed"><uni-load-more vue-id="692c768e-3" status="{{loadingStatus}}" class="data-v-1f55f9ed" bind:__l="__l"></uni-load-more></view></block></view>
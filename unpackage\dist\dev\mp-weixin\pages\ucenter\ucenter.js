(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/ucenter/ucenter"],{

/***/ 85:
/*!************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fucenter%2Fucenter"} ***!
  \************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _ucenter = _interopRequireDefault(__webpack_require__(/*! ./pages/ucenter/ucenter.vue */ 86));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_ucenter.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 86:
/*!*****************************************!*\
  !*** D:/Xwzc/pages/ucenter/ucenter.vue ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _ucenter_vue_vue_type_template_id_4883731c_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ucenter.vue?vue&type=template&id=4883731c&scoped=true& */ 87);
/* harmony import */ var _ucenter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ucenter.vue?vue&type=script&lang=js& */ 89);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _ucenter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _ucenter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _ucenter_vue_vue_type_style_index_0_id_4883731c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ucenter.vue?vue&type=style&index=0&id=4883731c&lang=scss&scoped=true& */ 92);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _ucenter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _ucenter_vue_vue_type_template_id_4883731c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _ucenter_vue_vue_type_template_id_4883731c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "4883731c",
  null,
  false,
  _ucenter_vue_vue_type_template_id_4883731c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/ucenter/ucenter.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 87:
/*!************************************************************************************!*\
  !*** D:/Xwzc/pages/ucenter/ucenter.vue?vue&type=template&id=4883731c&scoped=true& ***!
  \************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_template_id_4883731c_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ucenter.vue?vue&type=template&id=4883731c&scoped=true& */ 88);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_template_id_4883731c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_template_id_4883731c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_template_id_4883731c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_template_id_4883731c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 88:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter/ucenter.vue?vue&type=template&id=4883731c&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 475))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = !!_vm.hasLogin ? _vm.roleNames && _vm.roleNames.length > 0 : null
  var m0 = _vm.hasLogin
    ? _vm.uniIDHasRole("supervisor") ||
      _vm.uniIDHasRole("PM") ||
      _vm.uniIDHasRole("GM") ||
      _vm.uniIDHasRole("admin")
    : null
  var m1 = _vm.hasLogin
    ? _vm.uniIDHasRole("supervisor") ||
      _vm.uniIDHasRole("PM") ||
      _vm.uniIDHasRole("GM") ||
      _vm.uniIDHasRole("admin")
    : null
  var m2 = _vm.hasLogin
    ? _vm.uniIDHasRole("supervisor") ||
      _vm.uniIDHasRole("PM") ||
      _vm.uniIDHasRole("GM") ||
      _vm.uniIDHasRole("admin")
    : null
  var m3 = _vm.hasLogin
    ? _vm.uniIDHasRole("reviser") ||
      _vm.uniIDHasRole("supervisor") ||
      _vm.uniIDHasRole("PM") ||
      _vm.uniIDHasRole("GM") ||
      _vm.uniIDHasRole("admin")
    : null
  var g1 =
    _vm.hasLogin && _vm.hasLogin && _vm.hasRole ? _vm.todoList.length : null
  var l0 =
    _vm.hasLogin && _vm.hasLogin && _vm.hasRole && g1 > 0
      ? _vm.__map(_vm.todoList, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m4 = _vm.getProjectName(item.project)
          var m5 = _vm.getTodoTimeText(item)
          var m6 = _vm.getTodoTypeText(item.workflowStatus)
          return {
            $orig: $orig,
            m4: m4,
            m5: m5,
            m6: m6,
          }
        })
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        m0: m0,
        m1: m1,
        m2: m2,
        m3: m3,
        g1: g1,
        l0: l0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 89:
/*!******************************************************************!*\
  !*** D:/Xwzc/pages/ucenter/ucenter.vue?vue&type=script&lang=js& ***!
  \******************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ucenter.vue?vue&type=script&lang=js& */ 90);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 90:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter/ucenter.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, uniCloud) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ 13);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _store = __webpack_require__(/*! @/uni_modules/uni-id-pages/common/store.js */ 47);
var _wxUtils = __webpack_require__(/*! @/utils/wx-utils.js */ 91);
var _cache = _interopRequireWildcard(__webpack_require__(/*! @/utils/cache.js */ 45));
var _date = __webpack_require__(/*! @/utils/date.js */ 72);
var _todoBadge = _interopRequireDefault(__webpack_require__(/*! @/utils/todo-badge.js */ 44));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var PEmptyState = function PEmptyState() {
  __webpack_require__.e(/*! require.ensure | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then((function () {
    return resolve(__webpack_require__(/*! @/components/p-empty-state/p-empty-state.vue */ 475));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
// 重新添加导入
var _default = {
  components: {
    PEmptyState: PEmptyState
  },
  data: function data() {
    // 初始化 - 先显示缓存，再异步更新
    var token = uni.getStorageSync('uni_id_token') || '';
    var hasLogin = !!token;

    // 获取用户信息 - 使用自己的缓存系统
    var cachedUserInfo = _cache.default.get(_cache.default.cacheKeys.USER_INFO) || {};
    var userInfo = {
      nickname: cachedUserInfo.nickname || '加载中...',
      username: cachedUserInfo.username || '',
      avatar_file: cachedUserInfo.avatar_file || null,
      _id: cachedUserInfo._id || ''
    };
    return {
      roleNames: [],
      todoList: [],
      todoCount: 0,
      userRole: [],
      hasRole: false,
      isWechatPlatform: false,
      isRefreshing: false,
      lastRefreshTime: 0,
      localUserInfo: userInfo,
      localHasLogin: hasLogin,
      todoUpdateTimer: null,
      isTokenValid: true,
      responsibleTaskCount: 0,
      supervisionTaskCount: 0,
      // 厂长监督任务数量
      avatarLoaded: false // 头像加载状态
    };
  },

  computed: {
    userInfo: function userInfo() {
      // 直接使用本地用户信息（已经在data()中优化了初始化逻辑）
      return this.localUserInfo || {};
    },
    hasLogin: function hasLogin() {
      return (_store.store.hasLogin || this.localHasLogin) && this.isTokenValid;
    },
    // 检查是否有管理员权限
    hasAdminPermission: function hasAdminPermission() {
      return this.userRole.some(function (role) {
        return ['admin'].includes(role);
      });
    },
    // 检查是否有负责人权限
    hasResponsiblePermission: function hasResponsiblePermission() {
      return this.userRole.some(function (role) {
        return ['responsible'].includes(role);
      });
    },
    // 检查是否有厂长权限
    hasGMPermission: function hasGMPermission() {
      return this.userRole.some(function (role) {
        return ['GM', 'admin'].includes(role);
      });
    }
  },
  created: function created() {
    var _this = this;
    // 监听登录成功事件
    uni.$on('uni-id-pages-login-success', function () {
      // 清除角色缓存
      _cache.default.remove(_cache.default.cacheKeys.USER_ROLE);
      // 立即刷新页面数据
      _this.refreshData(true);
      // 使用新的登录成功处理方法更新角标
      _todoBadge.default.onLoginSuccess();
    });

    // 监听待办数量更新事件
    uni.$on('todo-count-updated', function (count) {
      // 只有在登录状态且有角色权限时才更新
      if (_this.hasLogin && _this.hasRole) {
        _this.todoCount = count;
        // 同时更新待办列表
        _this.getTodoList();
      }
    });

    // 监听刷新待办列表事件
    uni.$on('refresh-todo-list', function () {
      if (_this.hasLogin && _this.hasRole) {
        // 直接刷新数据，不显示loading
        _this.refreshData(false);
      }
    });

    // 监听全局强制登出UI更新事件（当在用户中心页面时）
    uni.$on('force-logout-ui-update', function () {
      _this.performLogout(false); // 不显示toast，因为App.vue已经显示了
    });

    // 监听反馈更新事件 - 添加这个监听来处理审核页面的操作
    uni.$on('feedback-updated', function () {
      if (_this.hasLogin && _this.hasRole) {
        // 延迟100ms执行，确保云端数据已更新
        setTimeout(function () {
          _this.refreshData(false);
        }, 100);
      }
    });

    // 监听用户中心页面刷新事件 - 专门针对审核页面操作后的刷新
    uni.$on('ucenter-need-refresh', function (data) {
      if (_this.hasLogin && _this.hasRole) {
        console.log('收到用户中心刷新事件:', data);
        // 立即刷新数据，不等待延迟
        _this.refreshData(false);
      }
    });

    // 监听角标管理器的跨设备更新事件
    uni.$on('cross-device-update-detected', function (data) {
      if (_this.hasLogin && _this.hasRole) {
        // 智能判断是否需要刷新
        var shouldRefresh = _this.shouldRefreshOnCrossDeviceUpdate(data);
        if (shouldRefresh) {
          console.log('用户中心收到跨设备更新通知，静默刷新数据');
          // 静默刷新数据，不显示提示
          _this.refreshData(false);
        }
      }
    });

    // 检测是否为微信环境

    this.isWechatPlatform = true;

    // 启动待办事项数量定时更新
    this.startTodoUpdateTimer();

    // 添加页面可见性变化监听（针对H5端）
  },
  beforeDestroy: function beforeDestroy() {
    // 移除事件监听
    uni.$off('uni-id-pages-login-success');
    uni.$off('todo-count-updated');
    uni.$off('refresh-todo-list');
    uni.$off('force-logout-ui-update');
    uni.$off('feedback-updated'); // 移除新添加的监听
    uni.$off('ucenter-need-refresh'); // 移除用户中心刷新事件监听
    uni.$off('cross-device-update-detected'); // 移除跨设备更新事件监听

    // 清除定时器
    this.clearTodoUpdateTimer();

    // 移除页面可见性变化监听（针对H5端）
  },
  onLoad: function onLoad() {
    if (this.hasLogin) {
      // 直接刷新所有数据
      this.refreshData(true);
    }
  },
  onShow: function onShow() {
    if (this.hasLogin) {
      // 调整缓存时间为30秒，配合跨设备同步机制，避免过度刷新
      var now = Date.now();
      if (now - this.lastRefreshTime > 30000) {
        this.refreshData(false);
      }
    }

    // 检查更新

    (0, _wxUtils.checkUpdate)();

    // 每次显示页面时检查token状态
    this.checkTokenStatus();

    // 强制同步角标状态，确保角标与页面数据一致
    if (this.hasLogin && this.hasRole) {
      setTimeout(function () {
        _todoBadge.default.forceSyncBadge();
      }, 300);
    }
  },
  onPullDownRefresh: function onPullDownRefresh() {
    if (this.hasLogin) {
      // 刷新数据
      this.refreshData(true).then(function () {
        // 完成下拉刷新
        uni.stopPullDownRefresh();
        // 添加刷新成功提示
        uni.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1500
        });
      }).catch(function (e) {
        console.error('刷新数据失败:', e);
        uni.stopPullDownRefresh();
        // 添加刷新失败提示
        uni.showToast({
          title: '刷新失败',
          icon: 'error',
          duration: 1500
        });
      });
    } else {
      // 未登录状态下直接停止下拉刷新
      uni.stopPullDownRefresh();
    }
  },
  watch: {
    hasLogin: function hasLogin(newVal) {
      var _this2 = this;
      if (newVal) {
        // 用户登录状态变为已登录，立即获取用户信息和待办数量
        this.$nextTick(function () {
          _this2.refreshData(true);
        });
      }
    },
    // 监听用户头像变化，重置加载状态
    'localUserInfo.avatar_file.url': {
      handler: function handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.avatarLoaded = false;
        }
      },
      deep: true
    },
    'localUserInfo.avatar': {
      handler: function handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.avatarLoaded = false;
        }
      }
    }
  },
  methods: {
    // 头像加载成功
    onAvatarLoad: function onAvatarLoad() {
      this.avatarLoaded = true;
    },
    // 头像加载失败
    onAvatarError: function onAvatarError() {
      this.avatarLoaded = false;
      console.log('头像加载失败，显示默认头像');
    },
    // 显示功能开发中提示
    showFeatureInDevelopment: function showFeatureInDevelopment() {
      uni.showToast({
        title: '功能开发中，敬请期待',
        icon: 'none',
        duration: 1000
      });
    },
    // 添加页面可见性变化处理函数（针对H5端）
    handleVisibilityChange: function handleVisibilityChange() {},
    // 启动待办事项数量定时更新
    startTodoUpdateTimer: function startTodoUpdateTimer() {
      var _this3 = this;
      // 先清除可能存在的定时器
      this.clearTodoUpdateTimer();

      // 简化定时器：只做基础的数据同步，跨设备检查交给角标管理器处理
      this.todoUpdateTimer = setInterval(function () {
        // 只有在登录状态下才更新
        if (_this3.hasLogin && _this3.hasRole) {
          _this3.updateTodoCountFromBadge();
        }
      }, 30000); // 调整为30秒，避免与角标管理器冲突

      // 立即执行一次
      if (this.hasLogin && this.hasRole) {
        this.updateTodoCountFromBadge();
      }
    },
    // 清除待办事项更新定时器
    clearTodoUpdateTimer: function clearTodoUpdateTimer() {
      if (this.todoUpdateTimer) {
        clearInterval(this.todoUpdateTimer);
        this.todoUpdateTimer = null;
      }
    },
    // 从角标管理器更新待办数量
    updateTodoCountFromBadge: function updateTodoCountFromBadge() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var count;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                _context.next = 3;
                return _todoBadge.default.getTodoCount();
              case 3:
                count = _context.sent;
                // 更新本地待办数量
                _this4.todoCount = count;
                _context.next = 10;
                break;
              case 7:
                _context.prev = 7;
                _context.t0 = _context["catch"](0);
                console.error('更新待办数量失败:', _context.t0);
              case 10:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 7]]);
      }))();
    },
    // 统一的数据刷新方法，避免重复请求
    refreshData: function refreshData() {
      var _arguments = arguments,
        _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var showLoading;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                showLoading = _arguments.length > 0 && _arguments[0] !== undefined ? _arguments[0] : false;
                if (!(!_this5.hasLogin || _this5.isRefreshing)) {
                  _context2.next = 3;
                  break;
                }
                return _context2.abrupt("return", Promise.resolve());
              case 3:
                _this5.isRefreshing = true;
                if (showLoading) {
                  uni.showLoading({
                    title: '加载中...'
                  });
                }
                _context2.prev = 5;
                _context2.next = 8;
                return _this5.getUserInfo();
              case 8:
                if (!(!_this5.userRole || _this5.userRole.length === 0 || !_this5.roleNames || _this5.roleNames.length === 0)) {
                  _context2.next = 11;
                  break;
                }
                _context2.next = 11;
                return _this5.getUserRole();
              case 11:
                if (!_this5.hasRole) {
                  _context2.next = 16;
                  break;
                }
                _context2.next = 14;
                return _this5.getTodoList();
              case 14:
                _context2.next = 18;
                break;
              case 16:
                _this5.todoList = [];
                _this5.todoCount = 0;
              case 18:
                if (!_this5.hasResponsiblePermission) {
                  _context2.next = 23;
                  break;
                }
                _context2.next = 21;
                return _this5.getResponsibleTaskCount();
              case 21:
                _context2.next = 24;
                break;
              case 23:
                _this5.responsibleTaskCount = 0;
              case 24:
                if (!_this5.hasGMPermission) {
                  _context2.next = 29;
                  break;
                }
                _context2.next = 27;
                return _this5.getSupervisionTaskCount();
              case 27:
                _context2.next = 30;
                break;
              case 29:
                _this5.supervisionTaskCount = 0;
              case 30:
                // 更新最后刷新时间
                _this5.lastRefreshTime = Date.now();
                return _context2.abrupt("return", Promise.resolve());
              case 34:
                _context2.prev = 34;
                _context2.t0 = _context2["catch"](5);
                return _context2.abrupt("return", Promise.reject(_context2.t0));
              case 37:
                _context2.prev = 37;
                if (showLoading) {
                  uni.hideLoading();
                }
                _this5.isRefreshing = false;
                return _context2.finish(37);
              case 41:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[5, 34, 37, 41]]);
      }))();
    },
    // 获取用户角色 - 改用cacheManager统一管理缓存
    getUserRole: function getUserRole() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var cachedRole, db, _yield$db$collection$, result, roleNameMap, errorMessage;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                if (_this6.hasLogin) {
                  _context3.next = 6;
                  break;
                }
                _this6.roleNames = ['未登录'];
                _this6.userRole = [];
                _this6.hasRole = false;
                return _context3.abrupt("return");
              case 6:
                // 尝试从缓存获取角色信息
                cachedRole = _cache.default.get(_cache.default.cacheKeys.USER_ROLE);
                if (!cachedRole) {
                  _context3.next = 12;
                  break;
                }
                _this6.userRole = cachedRole.userRole;
                _this6.roleNames = cachedRole.roleNames;
                _this6.hasRole = cachedRole.hasRole;
                return _context3.abrupt("return");
              case 12:
                db = uniCloud.database();
                _context3.next = 15;
                return db.collection('uni-id-users').where("'_id' == $cloudEnv_uid").field('role').get();
              case 15:
                _yield$db$collection$ = _context3.sent;
                result = _yield$db$collection$.result;
                if (result.data && result.data.length > 0) {
                  _this6.userRole = result.data[0].role || [];

                  // 手动设置角色名称映射
                  roleNameMap = {
                    'admin': '管理员',
                    'responsible': '责任人',
                    'reviser': '发布人',
                    'supervisor': '主管',
                    'PM': '副厂长',
                    'GM': '厂长',
                    'logistics': '后勤员',
                    'dispatch': '调度员',
                    'Integrated': '综合员',
                    'operator': '设备员',
                    'technician': '工艺员',
                    'mechanic': '技术员',
                    'user': '普通员工'
                  }; // 检查用户是否有特定角色
                  _this6.hasRole = _this6.userRole.some(function (role) {
                    return ['supervisor', 'PM', 'GM', 'admin', 'responsible'].includes(role);
                  });

                  // 将角色ID转换为中文名称
                  if (_this6.userRole.length > 0) {
                    _this6.roleNames = _this6.userRole.map(function (roleId) {
                      return roleNameMap[roleId] || roleId;
                    });
                  } else {
                    // 如果没有角色，显示为普通用户
                    _this6.roleNames = ['普通用户'];
                  }

                  // 缓存角色信息，有效期60分钟
                  _cache.default.set(_cache.default.cacheKeys.USER_ROLE, {
                    userRole: _this6.userRole,
                    roleNames: _this6.roleNames,
                    hasRole: _this6.hasRole
                  }, 60);
                } else {
                  _this6.roleNames = ['普通用户'];
                  _this6.userRole = [];
                  _this6.hasRole = false;
                }
                _context3.next = 26;
                break;
              case 20:
                _context3.prev = 20;
                _context3.t0 = _context3["catch"](0);
                // 检查是否是token相关的错误
                errorMessage = _context3.t0.message || _context3.t0.toString();
                if (errorMessage.includes('token') || errorMessage.includes('unauthorized') || errorMessage.includes('expired')) {
                  console.log('检测到token相关错误，清除待办数据');
                  // 清除待办相关数据
                  _this6.todoList = [];
                  _this6.todoCount = 0;
                  _this6.hasRole = false;
                  // 显示为普通用户，但不强制退出登录
                  _this6.roleNames = ['普通用户'];
                } else {
                  // 其他错误，默认处理
                  _this6.roleNames = ['普通用户'];
                }
                _this6.userRole = [];
                _this6.hasRole = false;
              case 26:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 20]]);
      }))();
    },
    // 获取项目名称 - 简化为直接返回项目名称
    getProjectName: function getProjectName(projectId) {
      // 由于feedback表的project字段直接存储文本值，直接返回即可
      return projectId || '未分类';
    },
    // 获取待办列表 - 优化查询逻辑
    getTodoList: function getTodoList() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var db, dbCmd, whereConditions, countRes, res;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                if (!(!_this7.hasLogin || !_this7.hasRole)) {
                  _context4.next = 4;
                  break;
                }
                _this7.todoList = [];
                _this7.todoCount = 0;
                return _context4.abrupt("return");
              case 4:
                _context4.prev = 4;
                db = uniCloud.database();
                dbCmd = db.command; // 简化查询条件构建
                whereConditions = _this7.buildTodoQueryConditions(dbCmd); // 先获取总数
                _context4.next = 10;
                return db.collection('feedback').where(whereConditions).count();
              case 10:
                countRes = _context4.sent;
                if (countRes.result && countRes.result.total !== undefined) {
                  _this7.todoCount = countRes.result.total;
                }

                // 增加待办项数量限制为3条，优化加载性能
                if (!(_this7.todoCount > 0)) {
                  _context4.next = 19;
                  break;
                }
                _context4.next = 15;
                return db.collection('feedback').where(whereConditions).orderBy('createTime', 'desc').limit(3) // 只显示前3条，提高性能
                .get();
              case 15:
                res = _context4.sent;
                if (res.result && res.result.data) {
                  _this7.todoList = res.result.data;
                } else {
                  _this7.todoList = [];
                }
                _context4.next = 20;
                break;
              case 19:
                _this7.todoList = [];
              case 20:
                _context4.next = 26;
                break;
              case 22:
                _context4.prev = 22;
                _context4.t0 = _context4["catch"](4);
                _this7.todoList = [];
                _this7.todoCount = 0;
              case 26:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[4, 22]]);
      }))();
    },
    // 获取负责人任务数量
    getResponsibleTaskCount: function getResponsibleTaskCount() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var res, _res$result$data$stat;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                if (!(!_this8.hasLogin || !_this8.hasResponsiblePermission)) {
                  _context5.next = 3;
                  break;
                }
                _this8.responsibleTaskCount = 0;
                return _context5.abrupt("return");
              case 3:
                _context5.prev = 3;
                _context5.next = 6;
                return uniCloud.callFunction({
                  name: 'feedback-list',
                  data: {
                    action: 'getMyTasks',
                    status: 'assigned_to_responsible'
                  }
                });
              case 6:
                res = _context5.sent;
                if (res.result && res.result.code === 0) {
                  _this8.responsibleTaskCount = ((_res$result$data$stat = res.result.data.stats) === null || _res$result$data$stat === void 0 ? void 0 : _res$result$data$stat.assigned) || 0;
                }
                _context5.next = 14;
                break;
              case 10:
                _context5.prev = 10;
                _context5.t0 = _context5["catch"](3);
                console.error('获取负责人任务数量失败:', _context5.t0);
                _this8.responsibleTaskCount = 0;
              case 14:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[3, 10]]);
      }))();
    },
    // 获取厂长监督任务数量
    getSupervisionTaskCount: function getSupervisionTaskCount() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var res, stats;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                if (!(!_this9.hasLogin || !_this9.hasGMPermission)) {
                  _context6.next = 3;
                  break;
                }
                _this9.supervisionTaskCount = 0;
                return _context6.abrupt("return");
              case 3:
                _context6.prev = 3;
                _context6.next = 6;
                return uniCloud.callFunction({
                  name: 'feedback-list',
                  data: {
                    action: 'getGMSupervisionTasks'
                  }
                });
              case 6:
                res = _context6.sent;
                if (res.result && res.result.code === 0) {
                  stats = res.result.data.stats || {}; // 计算需要关注的任务数量：执行中 + 待确认
                  _this9.supervisionTaskCount = (stats.assigned || 0) + (stats.pending || 0);
                }
                _context6.next = 14;
                break;
              case 10:
                _context6.prev = 10;
                _context6.t0 = _context6["catch"](3);
                console.error('获取厂长监督任务数量失败:', _context6.t0);
                _this9.supervisionTaskCount = 0;
              case 14:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[3, 10]]);
      }))();
    },
    // 构建待办查询条件（统一待办版本）
    buildTodoQueryConditions: function buildTodoQueryConditions(dbCmd) {
      var conditions = [];

      // 1. 审核待办
      if (this.userRole.includes('supervisor')) {
        // supervisor看到的是待主管审核的项目
        conditions.push({
          workflowStatus: 'pending_supervisor'
        });
      }
      if (this.userRole.includes('PM')) {
        // PM看到的是待副厂长审核的项目
        conditions.push({
          workflowStatus: 'pending_pm'
        });
      }
      if (this.userRole.includes('GM')) {
        // GM看到的是待厂长审核的项目 + 待指派负责人的项目 + 最终确认的项目
        conditions.push({
          workflowStatus: 'pending_gm'
        });
        conditions.push({
          workflowStatus: 'gm_approved_pending_assign'
        });
        conditions.push({
          workflowStatus: 'completed_by_responsible'
        });
      }

      // 2. 指派任务待办（如果用户有responsible角色）
      if (this.userRole.includes('responsible')) {
        try {
          var currentUserInfo = uniCloud.getCurrentUserInfo();
          var userId = currentUserInfo === null || currentUserInfo === void 0 ? void 0 : currentUserInfo.uid;
          if (userId) {
            // 待完成的指派任务
            conditions.push({
              workflowStatus: 'assigned_to_responsible',
              responsibleUserId: userId
            });
          }
        } catch (e) {
          console.warn('获取用户ID失败:', e);
        }
      }

      // 3. 管理员可以看到所有待办，但不包括指派给负责人的任务
      if (this.userRole.includes('admin')) {
        return {
          workflowStatus: dbCmd.in(['pending_supervisor', 'pending_pm', 'pending_gm', 'gm_approved_pending_assign',
          // 移除 'assigned_to_responsible',
          'completed_by_responsible'])
        };
      }

      // 如果有条件，使用or查询
      if (conditions.length > 0) {
        return dbCmd.or(conditions);
      }

      // 默认返回空条件
      return {};
    },
    // 格式化日期
    formatDate: function formatDate(timestamp) {
      if (!timestamp) return '';
      return (0, _date.formatDate)(timestamp);
    },
    // 获取待办时间文本
    getTodoTimeText: function getTodoTimeText(item) {
      // 自定义时间格式：MM-DD HH:mm（去掉年份和秒数）
      var timeFormat = 'MM-DD HH:mm';
      if (item.workflowStatus === 'assigned_to_responsible' && item.assignedTime) {
        return "\u6307\u6D3E\u65F6\u95F4\uFF1A".concat((0, _date.formatDate)(item.assignedTime, timeFormat));
      } else if (item.workflowStatus === 'completed_by_responsible' && item.completedByResponsibleTime) {
        return "\u5B8C\u6210\u65F6\u95F4\uFF1A".concat((0, _date.formatDate)(item.completedByResponsibleTime, timeFormat));
      } else {
        return "\u63D0\u4EA4\u65F6\u95F4\uFF1A".concat((0, _date.formatDate)(item.createTime, timeFormat));
      }
    },
    // 获取待办类型文本
    getTodoTypeText: function getTodoTypeText(status) {
      var typeMap = {
        'pending_supervisor': '待主管审核',
        'pending_pm': '待副厂长审核',
        'pending_gm': '待厂长审核',
        'gm_approved_pending_assign': '待指派负责人',
        'assigned_to_responsible': '待我完成',
        'completed_by_responsible': '待最终确认'
      };
      return typeMap[status] || '待处理';
    },
    // 处理待办点击事件
    handleTodoClick: function handleTodoClick(item) {
      if (item.workflowStatus === 'assigned_to_responsible') {
        // 指派任务，跳转到任务管理页面
        uni.navigateTo({
          url: '/pages/ucenter_pkg/responsible-tasks'
        });
      } else {
        // 审核待办，跳转到审核页面
        this.goToExamine(item._id);
      }
    },
    // 跳转到审核页面
    goToExamine: function goToExamine(id) {
      var _this10 = this;
      uni.navigateTo({
        url: "/pages/feedback_pkg/examine?id=".concat(id),
        events: {
          refreshData: function refreshData() {
            _this10.refreshData(false);
          }
        }
      });
    },
    // 公告通知
    goToReadNewsLog: function goToReadNewsLog() {
      uni.navigateTo({
        url: '/pages/notice/list'
      });
    },
    // Excel导出
    exportexcel: function exportexcel() {
      uni.navigateTo({
        url: '/pages/ucenter_pkg/export-excel'
      });
    },
    // 检查导出权限
    checkExportPermission: function checkExportPermission() {
      // 检查用户是否有特定角色
      var hasExportPermission = this.userRole.some(function (role) {
        return ['reviser', 'supervisor', 'PM', 'GM', 'admin', 'dispatch'].includes(role);
      });
      if (hasExportPermission) {
        // 有权限，跳转到导出页面
        this.exportexcel();
      } else {
        // 无权限，显示提示
        uni.showToast({
          title: '权限不够,无法查看',
          icon: 'none',
          duration: 2000
        });
      }
    },
    // 跳转到负责人任务页面
    goToResponsibleTasks: function goToResponsibleTasks() {
      if (!this.hasResponsiblePermission) {
        uni.showToast({
          title: '权限不足，无法访问任务管理',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      uni.navigateTo({
        url: '/pages/ucenter_pkg/responsible-tasks',
        fail: function fail(err) {
          console.error('跳转任务管理页面失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },
    // 跳转到用户管理页面
    goToUserManagement: function goToUserManagement() {
      if (!this.hasAdminPermission) {
        uni.showToast({
          title: '权限不足，无法访问用户管理',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      uni.navigateTo({
        url: '/pages/ucenter_pkg/user-management',
        fail: function fail(err) {
          console.error('跳转用户管理页面失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },
    // 跳转到厂长监督页面
    goToGMSupervision: function goToGMSupervision() {
      if (!this.hasGMPermission) {
        uni.showToast({
          title: '权限不足，只有厂长才能访问',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      uni.navigateTo({
        url: '/pages/ucenter_pkg/gm-supervision',
        fail: function fail(err) {
          console.error('跳转厂长监督页面失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },
    // 修改昵称/用户设置
    modifyNickname: function modifyNickname() {
      // 直接跳转到uni-id-pages提供的修改页面
      uni.navigateTo({
        url: '/uni_modules/uni-id-pages/pages/userinfo/userinfo?showLoginManage=false&showUserInfo=false&showSet=false&showEdit=true'
      });
    },
    // 退出登录
    logout: function logout() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var res, uniIdCo;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _context7.prev = 0;
                _context7.next = 3;
                return uni.showModal({
                  title: '提示',
                  content: '确定要退出登录吗？',
                  confirmText: '退出',
                  cancelText: '取消'
                });
              case 3:
                res = _context7.sent;
                if (!res.confirm) {
                  _context7.next = 20;
                  break;
                }
                uni.showLoading({
                  title: '退出中...',
                  mask: true
                });

                // 先清除角标
                _todoBadge.default.forceCleanBadge();
                _context7.prev = 7;
                uniIdCo = uniCloud.importObject('uni-id-co');
                _context7.next = 11;
                return uniIdCo.logout();
              case 11:
                _context7.next = 16;
                break;
              case 13:
                _context7.prev = 13;
                _context7.t0 = _context7["catch"](7);
                // 即使云函数调用失败，也执行本地登出
                console.error('云函数退出登录失败:', _context7.t0);
              case 16:
                // 执行本地退出清理
                _this11.performLogout(false);

                // 再次确保角标被清除
                setTimeout(function () {
                  _todoBadge.default.forceCleanBadge();
                }, 100);
                uni.hideLoading();
                uni.showToast({
                  title: '已退出登录',
                  icon: 'success'
                });
              case 20:
                _context7.next = 25;
                break;
              case 22:
                _context7.prev = 22;
                _context7.t1 = _context7["catch"](0);
                uni.showToast({
                  title: '退出登录失败',
                  icon: 'none'
                });
              case 25:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[0, 22], [7, 13]]);
      }))();
    },
    // 跳转到待办列表页面
    goToTodoList: function goToTodoList() {
      uni.navigateTo({
        url: '/pages/ucenter_pkg/todo'
      });
    },
    // 根据平台自动跳转到相应的登录页面
    goToLogin: function goToLogin() {
      // 微信小程序环境

      uni.navigateTo({
        url: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd'
      });

      // 非微信小程序环境
    },
    // 获取用户信息
    getUserInfo: function getUserInfo() {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var token, cachedInfo, db, _yield$db$collection$2, result, userData;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                _context8.prev = 0;
                token = uni.getStorageSync('uni_id_token') || '';
                _this12.localHasLogin = !!token;
                if (_this12.localHasLogin) {
                  _context8.next = 6;
                  break;
                }
                _this12.localUserInfo = {};
                return _context8.abrupt("return");
              case 6:
                // 先尝试从自己的缓存获取
                cachedInfo = _cache.default.get(_cache.default.cacheKeys.USER_INFO) || {};
                if (!(cachedInfo.nickname || cachedInfo.username)) {
                  _context8.next = 10;
                  break;
                }
                _this12.localUserInfo = cachedInfo;
                return _context8.abrupt("return");
              case 10:
                // 缓存中没有完整信息，从数据库获取
                db = uniCloud.database();
                _context8.next = 13;
                return db.collection('uni-id-users').where("'_id' == $cloudEnv_uid").field('nickname, username, avatar_file').get();
              case 13:
                _yield$db$collection$2 = _context8.sent;
                result = _yield$db$collection$2.result;
                if (result.data && result.data.length > 0) {
                  userData = result.data[0];
                  _this12.localUserInfo = {
                    _id: userData._id,
                    nickname: userData.nickname || userData.username || '未设置昵称',
                    username: userData.username || userData.nickname || '未设置昵称',
                    avatar_file: userData.avatar_file || null
                  };

                  // 重置头像加载状态，确保新头像重新加载
                  _this12.avatarLoaded = false;

                  // 更新自己的缓存系统
                  _cache.default.set(_cache.default.cacheKeys.USER_INFO, _this12.localUserInfo);
                } else {
                  _this12.localUserInfo = {
                    nickname: '用户',
                    username: 'user'
                  };
                  _this12.avatarLoaded = false;
                }
                _context8.next = 23;
                break;
              case 18:
                _context8.prev = 18;
                _context8.t0 = _context8["catch"](0);
                console.error('获取用户信息失败:', _context8.t0);
                _this12.localUserInfo = {
                  nickname: '用户',
                  username: 'user'
                };
                _this12.avatarLoaded = false;
              case 23:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[0, 18]]);
      }))();
    },
    // 导航到登录页
    navToLogin: function navToLogin() {
      uni.navigateTo({
        url: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd'
      });
    },
    // 导航到指定页面
    navTo: function navTo(url) {
      // 荣誉展厅特殊处理：预加载优化
      if (url === '/pages/honor_pkg/gallery/index') {
        this.navToHonorGallery(url);
        return;
      }

      // 使用缓存优化，记录最近访问的页面
      var recentPages = _cache.default.get('recent_pages', []);
      if (!recentPages.includes(url)) {
        recentPages.unshift(url);
        // 只保留最近10个页面
        if (recentPages.length > 10) {
          recentPages.pop();
        }
        _cache.default.set('recent_pages', recentPages);
      }
      uni.navigateTo({
        url: url,
        fail: function fail(err) {
          console.error('导航失败:', err);
          uni.showToast({
            title: '页面不存在',
            icon: 'none'
          });
        }
      });
    },
    // 荣誉展厅专用导航 - 优化加载体验
    navToHonorGallery: function navToHonorGallery(url) {
      // 记录访问历史
      var recentPages = _cache.default.get('recent_pages', []);
      if (!recentPages.includes(url)) {
        recentPages.unshift(url);
        if (recentPages.length > 10) {
          recentPages.pop();
        }
        _cache.default.set('recent_pages', recentPages);
      }

      // 保持页面切换动画，但优化加载体验
      uni.navigateTo({
        url: url,
        animationType: 'slide-in-right',
        // 使用滑入动画替代默认
        animationDuration: 200,
        // 缩短动画时长
        success: function success() {
          // 跳转成功后的处理
        },
        fail: function fail(err) {
          console.error('导航失败:', err);
          // 降级到普通跳转
          uni.navigateTo({
            url: url,
            fail: function fail(err2) {
              uni.showToast({
                title: '页面不存在',
                icon: 'none'
              });
            }
          });
        }
      });
    },
    // 检查token状态
    checkTokenStatus: function checkTokenStatus() {
      var _this13 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var token, tokenValid;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                _context9.prev = 0;
                token = uni.getStorageSync('uni_id_token');
                if (token) {
                  _context9.next = 5;
                  break;
                }
                _this13.handleTokenInvalid();
                return _context9.abrupt("return");
              case 5:
                _context9.next = 7;
                return _this13.validateToken();
              case 7:
                tokenValid = _context9.sent;
                if (!tokenValid) {
                  _this13.handleTokenInvalid();
                }
                _context9.next = 15;
                break;
              case 11:
                _context9.prev = 11;
                _context9.t0 = _context9["catch"](0);
                console.error('验证登录状态失败:', _context9.t0);
                _this13.handleTokenInvalid();
              case 15:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[0, 11]]);
      }))();
    },
    // 验证token的方法
    validateToken: function validateToken() {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var token, tokenExpired;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                _context10.prev = 0;
                token = uni.getStorageSync('uni_id_token');
                if (token) {
                  _context10.next = 4;
                  break;
                }
                return _context10.abrupt("return", false);
              case 4:
                tokenExpired = uni.getStorageSync('uni_id_token_expired');
                if (!(tokenExpired < Date.now())) {
                  _context10.next = 7;
                  break;
                }
                return _context10.abrupt("return", false);
              case 7:
                return _context10.abrupt("return", true);
              case 10:
                _context10.prev = 10;
                _context10.t0 = _context10["catch"](0);
                console.error('Token validation error:', _context10.t0);
                return _context10.abrupt("return", false);
              case 14:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10, null, [[0, 10]]);
      }))();
    },
    // 统一的登出处理方法
    performLogout: function performLogout() {
      var _this14 = this;
      var showToast = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
      // 第一步：立即设置登录状态为false，确保待办区域立即消失
      this.localHasLogin = false;

      // 第二步：清除所有登录相关存储
      uni.removeStorageSync('uni_id_token');
      uni.removeStorageSync('uni_id_token_expired');
      uni.removeStorageSync('uni_id_user');
      uni.removeStorageSync('uni-id-pages-userInfo');

      // 第三步：清除缓存
      _cache.default.remove(_cache.default.cacheKeys.USER_INFO);
      _cache.default.remove(_cache.default.cacheKeys.USER_ROLE);
      _cache.default.remove(_cache.default.cacheKeys.PROJECT_OPTIONS);

      // 第四步：更新store状态
      _store.mutations.setUserInfo({}, {
        cover: true
      });

      // 第五步：清除角标
      _todoBadge.default.clearBadge();

      // 第六步：清除敏感缓存
      this.clearSensitiveCache();

      // 第七步：重置其他页面状态
      Object.assign(this, {
        roleNames: [],
        todoList: [],
        todoCount: 0,
        userRole: [],
        hasRole: false,
        localUserInfo: {},
        isTokenValid: false
      });

      // 显示提示
      if (showToast) {
        uni.showToast({
          title: '登录已过期，请重新登录',
          icon: 'none',
          duration: 2000
        });
      }

      // 更新页面状态
      this.$nextTick(function () {
        return _this14.getUserInfo();
      });
    },
    // 清除敏感缓存数据（与拦截器保持一致的逻辑）
    clearSensitiveCache: function clearSensitiveCache() {
      try {
        var storageInfo = uni.getStorageInfoSync();
        var keys = storageInfo.keys;

        // 定义需要清除的真正敏感数据（个人设备使用场景，重点保护隐私）
        var sensitivePatterns = ['user_info_',
        // 用户详细信息（可能包含个人隐私）
        'user_mgmt_' // 用户管理数据（管理员功能）
        ];

        // 定义需要明确保留的数据
        var preserveKeys = ['_DC_STAT_UUID',
        // 设备统计标识
        (0, _cache.getCacheKey)('recent_pages'),
        // 最近访问页面（动态生成键名）
        'last_app_start_time',
        // 应用启动时间
        'uni-id-pages-userInfo' // 框架用户信息（可能为空对象）
        ];

        var clearedCount = 0;
        keys.forEach(function (key) {
          // 检查是否在保留列表中
          if (preserveKeys.includes(key)) {
            return; // 跳过保留的键
          }

          // 检查是否匹配敏感数据模式
          var shouldClear = sensitivePatterns.some(function (pattern) {
            return key === pattern || key.startsWith(pattern);
          });
          if (shouldClear) {
            uni.removeStorageSync(key);
            clearedCount++;
          }
        });
      } catch (error) {
        console.error('清除敏感缓存失败:', error);
      }
    },
    // 处理token失效（兼容原有逻辑）
    handleTokenInvalid: function handleTokenInvalid() {
      this.performLogout(false);
    },
    // 智能判断是否需要刷新
    shouldRefreshOnCrossDeviceUpdate: function shouldRefreshOnCrossDeviceUpdate(data) {
      // 如果距离上次刷新时间太短（小于15秒），避免频繁刷新
      var timeSinceLastRefresh = Date.now() - this.lastRefreshTime;
      if (timeSinceLastRefresh < 15000) {
        return false;
      }

      // 如果更新类型包含待办相关的操作，需要刷新
      if (data.updateTypes) {
        var relevantTypes = ['workflow_status_changed', 'feedback_submitted'];
        var hasRelevantUpdate = data.updateTypes.some(function (type) {
          return relevantTypes.includes(type);
        });
        if (hasRelevantUpdate) {
          return true;
        }
      }

      // 如果有多个更新记录，可能需要刷新
      if (data.updateCount > 1) {
        return true;
      }

      // 默认不刷新，避免过度刷新
      return false;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"]))

/***/ }),

/***/ 92:
/*!***************************************************************************************************!*\
  !*** D:/Xwzc/pages/ucenter/ucenter.vue?vue&type=style&index=0&id=4883731c&lang=scss&scoped=true& ***!
  \***************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_style_index_0_id_4883731c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ucenter.vue?vue&type=style&index=0&id=4883731c&lang=scss&scoped=true& */ 93);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_style_index_0_id_4883731c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_style_index_0_id_4883731c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_style_index_0_id_4883731c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_style_index_0_id_4883731c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ucenter_vue_vue_type_style_index_0_id_4883731c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 93:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter/ucenter.vue?vue&type=style&index=0&id=4883731c&lang=scss&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[85,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ucenter/ucenter.js.map
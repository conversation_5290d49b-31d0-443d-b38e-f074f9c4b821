{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?195b", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?01d8", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?8fd9", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?375b", "uni-app:///pages/patrol_pkg/point/qrcode-batch.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?17de", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?d6cf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "points", "loading", "hasGeneratedCodes", "pagination", "page", "pageSize", "total", "loadMoreStatus", "isLoadingMore", "onLoad", "onReachBottom", "onPullDownRefresh", "uni", "computed", "allPointsEnabled", "methods", "goBack", "loadMore", "loadPoints", "reset", "title", "PatrolApi", "params", "keyword", "status", "res", "console", "newList", "newPoints", "point", "generated", "qrcodeContent", "newPointsCount", "totalPoints", "totalExpected", "currentPage", "hasMore", "icon", "enableQRCode", "id", "qrcode_enabled", "qrcode_version", "index", "disableQRCode", "content", "success", "qrcode_content", "qrcode_hash_key", "qrcode_generated_time", "generateQRCode", "enableRes", "point_id", "detailRes", "Object", "qr<PERSON><PERSON>nt", "includeTimestamp", "updateResult", "onQRCodeComplete", "handleBatchOperation", "saveQRCode", "qrcodeRef", "qrcode", "fail", "generateAllQRCodes", "pointsToGenerate", "confirmText", "cancelText", "confirmRes", "successCount", "failCount", "mask", "i", "showCancel", "disableAllQRCodes", "enabledPoints", "saveAllQRCodes", "pointsToSave", "totalCount", "currentProgress", "resolve"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC2H1nB;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAC;IACAC;EACA;EAEAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEA;EACAC;IACA;EACA;EAEA;EACAC;IACA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QAAA;MAAA;IACA;EACA;EAEAC;IACAC;MACAJ;IACA;IAEA;IACAK;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAGA;kBACA;kBACAP;oBAAAQ;kBAAA;gBACA;gBAAA;gBAAA,OAEAC;kBACAC;oBACAC;oBACAnB;oBACAC;oBACAmB;kBACA;gBACA;cAAA;gBAPAC;gBASAC;gBAEA;kBACAC,+BAEA;kBACAC;oBAAA,uCACAC;sBACAC;sBAAA;sBACAC;oBAAA;kBAAA,CACA,GAEA;;kBACA;oBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;oBAEA;sBACAzB;oBACA;sBACAA;oBACA;sBACAA;oBACA;sBACAA;oBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA;kBACA;oBACA;kBACA;oBACA;kBACA;kBAEAoB;oBACAM;oBACAC;oBACAC;oBACAC;oBACAC;oBACA7B;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAmB;gBACAd;kBACAQ;kBACAiB;gBACA;cAAA;gBAAA;gBAEA;kBACA;kBACAzB;gBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA1B;kBAAAQ;gBAAA;gBAAA;gBAAA,OACAC;kBACAtB;oBACAwC;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBANAhB;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAiB;kBAAA;gBAAA;gBACA;kBACA;kBACA;kBACA;kBACA;oBACA;kBACA;gBACA;gBAEA9B;kBACAQ;kBACAiB;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAX;gBACAd;kBACAQ;kBACAiB;gBACA;cAAA;gBAAA;gBAEAzB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA+B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA/B;oBACAQ;oBACAwB;oBACAC;sBAAA;wBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCAAA,KACApB;kCAAA;kCAAA;gCAAA;gCACAb;kCAAAQ;gCAAA;gCAAA;gCAAA,OACAC;kCACAtB;oCACAwC;oCACAC;oCACAM;oCAAA;oCACAL;oCAAA;oCACAM;oCAAA;oCACAC;kCACA;gCACA;8BAAA;gCATAvB;gCAAA,MAWAA;kCAAA;kCAAA;gCAAA;gCACA;gCACAiB;kCAAA;gCAAA;gCACA;kCACA;kCACA;kCACA;kCACA;kCACA;kCACA;kCACA;gCACA;gCAEA9B;kCACAQ;kCACAiB;gCACA;gCAAA;gCAAA;8BAAA;gCAAA,MAEA;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CAGA;sBAAA;wBAAA;sBAAA;sBAAA;oBAAA;kBACA;gBACA;kBACAX;kBACAd;oBACAQ;oBACAiB;kBACA;gBACA;kBACAzB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAqC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,IAGApB;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAR;kBACAtB;oBACAwC;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBANAS;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAIA7B;kBACA8B;gBACA;cAAA;gBAFAC;gBAAA,MAIAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAC;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAGA;gBACAX;kBAAA;gBAAA;gBACA;kBACA;kBACA;kBACA;gBACA;cAAA;gBAAA,IAIAb;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAR;kBACA8B;gBACA;cAAA;gBAFAC;gBAAA,MAIAA;kBAAA;kBAAA;gBAAA;gBACAvB;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAIA;gBACAyB,+EACAzB;kBACAiB;gBAAA,IACA;kBACAS;gBACA,IAEA;gBAAA;gBAAA,OACAlC;kBACAtB;oBACAwC;oBACAO;oBACAE;oBACAR;oBACAC;kBACA;gBACA;cAAA;gBARAe;gBAAA,MAUAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACAd;kBAAA;gBAAA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;;gBAEA;gBACA9B;kBACAQ;kBACAiB;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;gBACAd;kBACAQ;kBACAiB;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAoB;MACA;QACA;UAAA;QAAA;QACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA/C;kBAAAQ;gBAAA;gBAEAwC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGAC;gBAAA;gBAAA,OACAA;kBACAhB;oBACAjC;sBACAQ;sBACAiB;oBACA;kBACA;kBACAyB;oBACApC;oBACAd;sBACAQ;sBACAiB;oBACA;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;gBACAd;kBACAQ;kBACAiB;gBACA;cAAA;gBAAA;gBAEAzB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;kBAAA,OACA;gBAAA,EACA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACApD;kBACAQ;kBACAiB;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAMA;kBACAzB;oBACAQ;oBACAwB;oBACAqB;oBACAC;oBACArB;oBACAiB;kBACA;gBACA;cAAA;gBATAK;gBAAA,IAWAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIAC;gBACAC,eAEA;gBACAzD;kBACAQ;kBACAkD;gBACA;;gBAEA;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACA1C;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAuC;gBACA;gBACAxD;kBACAQ;kBACAkD;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA5C;gBACA2C;cAAA;gBAZAE;gBAAA;gBAAA;cAAA;gBAgBA;gBACA3D;;gBAEA;gBACAA;kBACAQ;kBACAwB;kBACA4B;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9C;gBACAd;gBACAA;kBACAQ;kBACAiB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;kBAAA;gBAAA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA9D;kBACAQ;kBACAiB;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKA;kBACAzB;oBACAQ;oBACAwB;oBACAC;kBACA;gBACA;cAAA;gBANAsB;gBAAA,IAQAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGAvD;kBACAQ;kBACAkD;gBACA;gBAEAF;gBACAC;gBAAA,8DAEAE;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BACA1C;0BAAA;0BAAA;0BAAA,OAEAR;4BACAtB;8BACAwC;8BACAC;8BACAM;8BAAA;8BACAL;8BAAA;8BACAM;8BAAA;8BACAC;4BACA;0BACA;wBAAA;0BATAvB;0BAAA,MAWAA;4BAAA;4BAAA;0BAAA;0BACA;0BACAiB;4BAAA;0BAAA;0BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;0BACA;0BACA0B;0BAAA;0BAAA;wBAAA;0BAAA,MAEA;wBAAA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAGA1C;0BACA2C;wBAAA;0BAGA;0BACAzD;4BACAQ;4BACAkD;0BACA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;gBAvCAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAAA;gBAAA;gBAAA;cAAA;gBA0CA3D;gBACAA;kBACAQ;kBACAwB;kBACA4B;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9C;gBACAd;kBACAQ;kBACAiB;gBACA;cAAA;gBAAA;gBAEAzB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACA+D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;kBAAA;gBAAA,IAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAhE;kBACAQ;kBACAiB;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKA;kBACAzB;oBACAQ;oBACAwB;oBACAC;kBACA;gBACA;cAAA;gBANAsB;gBAAA,IAQAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAC;gBACAC;gBACAQ,kCAEA;gBACAjE;kBACAQ;kBACAkD;gBACA;;gBAEA;gBAAA,gEACAC;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BACA1C;0BACAiD,yBAEA;0BACAlE;4BACAQ;4BACAkD;0BACA;0BAAA;0BAGA;0BACAV;0BAAA,IACAA;4BAAA;4BAAA;0BAAA;0BACAlC;0BACA2C;0BAAA;wBAAA;0BAIA;0BACAR,8DAEA;0BAAA;0BAAA,OACA;4BACAA;8BACAhB;gCACAuB;gCACAW;8BACA;8BACAjB;gCACApC;gCACA2C;gCACA;gCACAU;8BACA;4BACA;0BACA;wBAAA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAGA;0BACArD;0BACA2C;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;gBAzCAE;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAAA;gBAAA;gBAAA;cAAA;gBA6CA;gBACA3D;;gBAEA;gBACAA;kBACAQ;kBACA;kBACAwB;kBACA4B;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3xBA;AAAA;AAAA;AAAA;AAAipC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACArqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/point/qrcode-batch.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/point/qrcode-batch.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./qrcode-batch.vue?vue&type=template&id=89ff35b2&\"\nvar renderjs\nimport script from \"./qrcode-batch.vue?vue&type=script&lang=js&\"\nexport * from \"./qrcode-batch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./qrcode-batch.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/point/qrcode-batch.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qrcode-batch.vue?vue&type=template&id=89ff35b2&\"", "var components\ntry {\n  components = {\n    uqrcode: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode\" */ \"@/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.points, function (point, __i0__) {\n    var $orig = _vm.__get_orig(point)\n    var g0 = point.location ? point.location.longitude.toFixed(6) : null\n    var g1 = point.location ? point.location.latitude.toFixed(6) : null\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n    }\n  })\n  var g2 = _vm.loadMoreStatus === \"noMore\" && _vm.points.length > 0\n  var g3 = !_vm.loading && _vm.points.length === 0\n  if (!_vm._isMounted) {\n    _vm.e0 = function (res, point) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp = args[args.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        point = _temp2.point\n      var _temp, _temp2\n      return _vm.onQRCodeComplete(res, point)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qrcode-batch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qrcode-batch.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"qrcode-batch-page\">  \n    <!-- 点位列表 -->\n    <scroll-view \n      class=\"point-list\" \n      scroll-y \n      @scrolltolower=\"loadMore\"\n      :style=\"{ height: 'calc(100vh - 120rpx)' }\"\n      show-scrollbar=\"false\"\n      enable-back-to-top\n    >\n      <view class=\"point-list-content\">\n        <view class=\"point-item\" v-for=\"point in points\" :key=\"point._id\">\n          <view class=\"point-info\">\n            <text class=\"point-name\">{{point.name || '未命名点位'}}</text>\n            <view class=\"point-detail\">\n              <text class=\"point-address\">{{point.location && point.location.address || point.address || '无地址信息'}}</text>\n              <text class=\"point-coordinates\" v-if=\"point.location\">经度: {{point.location.longitude.toFixed(6)}} | 纬度: {{point.location.latitude.toFixed(6)}}</text>\n              <text class=\"point-coordinates\">ID: {{point._id}}</text>\n            </view>\n          </view>\n          \n          <view class=\"qrcode-preview\" v-if=\"point.qrcode_enabled\">\n            <view class=\"qrcode-wrapper\">\n              <uqrcode\n                v-if=\"point.qrcodeContent\"\n                :ref=\"'qrcode-' + point._id\"\n                :canvas-id=\"'qrcode-' + point._id\"\n                class=\"qrcode-canvas\"\n                :value=\"point.qrcodeContent\"\n                :options=\"{\n                  size: 200,\n                  margin: 10,\n                  backgroundColor: '#ffffff',\n                  foregroundColor: '#000000',\n                  errorCorrectLevel: 'H',\n                  type: 'image'\n                }\"\n                @complete=\"(res) => onQRCodeComplete(res, point)\"\n              />\n            </view>\n            <view class=\"qrcode-meta\">\n              <text class=\"version\">版本: {{point.qrcode_version || 1}}</text>\n              <text class=\"status\" :class=\"{'status-success': point.generated}\">\n                {{point.generated ? '已生成' : '未生成'}}\n              </text>\n            </view>\n          </view>\n          \n          <view class=\"point-actions\">\n            <button \n              class=\"btn-generate\" \n              v-if=\"!point.qrcode_enabled\" \n              @click=\"enableQRCode(point)\"\n            >\n              <uni-icons type=\"scan\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n              <text>启用二维码</text>\n            </button>\n            <button \n              class=\"btn-regenerate\" \n              v-else-if=\"!point.generated\" \n              @click=\"generateQRCode(point)\"\n            >\n              <uni-icons type=\"refresh\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n              <text>生成二维码</text>\n            </button>\n            <button \n              class=\"btn-save\" \n              v-else \n              @click=\"saveQRCode(point)\"\n            >\n              <uni-icons type=\"download\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n              <text>保存图片</text>\n            </button>\n            <button \n              class=\"btn-disable\" \n              v-if=\"point.qrcode_enabled\" \n              @click=\"disableQRCode(point)\"\n            >\n              <uni-icons type=\"closeempty\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n              <text>关闭二维码</text>\n            </button>\n          </view>\n        </view>\n        \n        <!-- 加载更多提示 -->\n        <view class=\"loading-more\" v-if=\"loadMoreStatus === 'loading'\">\n          <uni-icons type=\"spinner-cycle\" size=\"20\" color=\"#666666\"></uni-icons>\n          <text>加载中...</text>\n        </view>\n        \n        <!-- 没有更多数据提示 -->\n        <view class=\"no-more\" v-if=\"loadMoreStatus === 'noMore' && points.length > 0\">\n          <text>没有更多数据了</text>\n        </view>\n        \n        <!-- 空状态 -->\n        <p-empty-state \n          v-if=\"!loading && points.length === 0\"\n          text=\"暂无点位数据\"\n          image=\"/static/empty/empty_data.png\"\n        ></p-empty-state>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-bar\">\n      <button \n        :class=\"['btn-batch-operation', allPointsEnabled ? 'btn-disable-all' : 'btn-generate-all']\" \n        @click=\"handleBatchOperation\"\n      >\n        <uni-icons :type=\"allPointsEnabled ? 'closeempty' : 'scan'\" size=\"18\" color=\"#FFFFFF\"></uni-icons>\n        <text>{{allPointsEnabled ? '一键关闭全部' : '一键启用全部'}}</text>\n      </button>\n      <button class=\"btn-save-all\" @click=\"saveAllQRCodes\">\n        <uni-icons type=\"download\" size=\"18\" color=\"#FFFFFF\"></uni-icons>\n        <text>保存全部</text>\n      </button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport QRCodeUtil from '@/utils/qrcode-utils.js';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n\nexport default {\n  components: {\n    PEmptyState\n  },\n  \n  data() {\n    return {\n      points: [], // 点位列表\n      loading: false,\n      hasGeneratedCodes: false,\n      pagination: {\n        page: 1,\n        pageSize: 10,\n        total: 0\n      },\n      loadMoreStatus: 'more',\n      isLoadingMore: false\n    }\n  },\n  \n  onLoad() {\n    this.loadPoints(true);\n  },\n\n  // 添加页面生命周期方法\n  onReachBottom() {\n    this.loadMore();\n  },\n\n  // 添加下拉刷新\n  onPullDownRefresh() {\n    this.loadPoints(true).then(() => {\n      uni.stopPullDownRefresh();\n    });\n  },\n\n  computed: {\n    // 判断是否所有点位都已启用并生成二维码\n    allPointsEnabled() {\n      return this.points.length > 0 && this.points.every(p => p.qrcode_enabled && p.generated);\n    }\n  },\n\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    \n    // 加载更多\n    async loadMore() {\n      // 避免重复加载和已经没有更多数据的情况\n      if (this.loadMoreStatus === 'loading' || this.loadMoreStatus === 'noMore' || this.isLoadingMore) {\n        return;\n      }\n\n      this.isLoadingMore = true;\n      this.loadMoreStatus = 'loading';\n      this.pagination.page++;\n      await this.loadPoints();\n    },\n\n    // 加载点位列表\n    async loadPoints(reset = false) {\n      if (reset) {\n        this.pagination.page = 1;\n        this.points = [];\n        this.loadMoreStatus = 'more';\n      }\n\n      try {\n        if (reset) {\n          this.loading = true;\n          uni.showLoading({ title: '加载中...' });\n        }\n\n        const res = await PatrolApi.callPointFunction('getPointList', {\n          params: {\n            keyword: '',\n            page: this.pagination.page,\n            pageSize: this.pagination.pageSize,\n            status: 1\n          }\n        });\n        \n        console.log('API response:', res);\n        \n        if (res.code === 0 && res.data) {\n          const newList = res.data.list || [];\n          \n          // 处理新的点位数据\n          const newPoints = newList.map(point => ({\n            ...point,\n            generated: !!point.qrcode_content,  // 如果已有二维码内容则标记为已生成\n            qrcodeContent: point.qrcode_content || null  // 使用数据库中的二维码内容\n          }));\n          \n          // 更新列表\n          if (reset) {\n            this.points = newPoints;\n          } else {\n            this.points = [...this.points, ...newPoints];\n          }\n          \n          // 确保total是数字\n          if (reset) {\n            let total;\n            if (typeof res.data.total === 'number' && res.data.total > 0) {\n              total = res.data.total;\n            } else if (typeof res.data.total === 'string' && parseInt(res.data.total) > 0) {\n              total = parseInt(res.data.total);\n            } else if (newList.length >= this.pagination.pageSize) {\n              total = newList.length + this.pagination.pageSize;\n            } else {\n              total = newList.length;\n            }\n            this.pagination.total = total;\n          }\n          \n          // 更新加载状态\n          if (newList.length === 0) {\n            this.loadMoreStatus = 'noMore';\n          } else if (newList.length < this.pagination.pageSize) {\n            this.loadMoreStatus = 'noMore';\n          } else {\n            this.loadMoreStatus = 'more';\n          }\n          \n          console.log('Data loaded:', {\n            newPointsCount: newPoints.length,\n            totalPoints: this.points.length,\n            totalExpected: this.pagination.total,\n            currentPage: this.pagination.page,\n            hasMore: this.loadMoreStatus === 'more',\n            loadMoreStatus: this.loadMoreStatus\n          });\n        }\n      } catch (e) {\n        console.error('加载点位失败:', e);\n        uni.showToast({\n          title: '加载点位失败',\n          icon: 'none'\n        });\n      } finally {\n        if (reset) {\n          this.loading = false;\n          uni.hideLoading();\n        }\n        this.isLoadingMore = false;\n      }\n    },\n    \n    // 启用点位二维码\n    async enableQRCode(point) {\n      try {\n        uni.showLoading({ title: '启用中...' });\n        const res = await PatrolApi.callPointFunction('updatePoint', {\n          data: {\n            id: point._id,\n            qrcode_enabled: true,\n            qrcode_version: 1\n          }\n        });\n        \n        if (res.code === 0) {\n          // 更新本地数据\n          const index = this.points.findIndex(p => p._id === point._id);\n          if (index !== -1) {\n            this.$set(this.points[index], 'qrcode_enabled', true);\n            this.$set(this.points[index], 'qrcode_version', 1);\n            // 自动生成二维码\n            this.$nextTick(() => {\n              this.generateQRCode(this.points[index]);\n            });\n          }\n          \n          uni.showToast({\n            title: '启用成功',\n            icon: 'success'\n          });\n        } else {\n          throw new Error(res.message || '启用失败');\n        }\n      } catch (e) {\n        console.error('启用失败:', e);\n        uni.showToast({\n          title: e.message || '启用失败',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n    \n    // 关闭点位二维码\n    async disableQRCode(point) {\n      try {\n        uni.showModal({\n          title: '确认关闭',\n          content: '关闭二维码后，该点位的所有二维码数据将被清除，是否继续？',\n          success: async (res) => {\n            if (res.confirm) {\n              uni.showLoading({ title: '关闭中...' });\n              const res = await PatrolApi.callPointFunction('updatePoint', {\n                data: {\n                  id: point._id,\n                  qrcode_enabled: false,\n                  qrcode_content: null,        // 清除二维码内容\n                  qrcode_version: null,        // 清除版本号\n                  qrcode_hash_key: null,       // 清除哈希密钥\n                  qrcode_generated_time: null  // 清除生成时间\n                }\n              });\n              \n              if (res.code === 0) {\n                // 更新本地数据\n                const index = this.points.findIndex(p => p._id === point._id);\n                if (index !== -1) {\n                  this.$set(this.points[index], 'qrcode_enabled', false);\n                  this.$set(this.points[index], 'qrcode_content', null);\n                  this.$set(this.points[index], 'qrcode_version', null);\n                  this.$set(this.points[index], 'qrcode_hash_key', null);\n                  this.$set(this.points[index], 'qrcode_generated_time', null);\n                  this.$set(this.points[index], 'generated', false);\n                  this.$set(this.points[index], 'qrcodeContent', null);\n                }\n                \n                uni.showToast({\n                  title: '已关闭二维码',\n                  icon: 'success'\n                });\n              } else {\n                throw new Error(res.message || '关闭失败');\n              }\n            }\n          }\n        });\n      } catch (e) {\n        console.error('关闭失败:', e);\n        uni.showToast({\n          title: e.message || '关闭失败',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n    \n    // 生成单个点位的二维码\n    async generateQRCode(point) {\n      try {\n        // 如果点位未启用二维码，先启用并获取新的hash_key\n        if (!point.qrcode_enabled) {\n          const enableRes = await PatrolApi.callPointFunction('updatePoint', {\n            data: {\n              id: point._id,\n              qrcode_enabled: true,\n              qrcode_version: 1\n            }\n          });\n          \n          if (enableRes.code !== 0) {\n            throw new Error(enableRes.message || '启用二维码失败');\n          }\n          \n          // 重新获取点位信息以获取新生成的hash_key\n          const detailRes = await PatrolApi.callPointFunction('getPointDetail', {\n            point_id: point._id\n          });\n          \n          if (detailRes.code === 0 && detailRes.data) {\n            // 更新点位信息，特别是hash_key\n            Object.assign(point, detailRes.data);\n          } else {\n            throw new Error('获取点位详情失败');\n          }\n          \n          // 更新本地数据\n          const index = this.points.findIndex(p => p._id === point._id);\n          if (index !== -1) {\n            this.$set(this.points[index], 'qrcode_enabled', true);\n            this.$set(this.points[index], 'qrcode_version', 1);\n            this.$set(this.points[index], 'qrcode_hash_key', point.qrcode_hash_key);\n          }\n        }\n        \n        // 确保有hash_key\n        if (!point.qrcode_hash_key) {\n          const detailRes = await PatrolApi.callPointFunction('getPointDetail', {\n            point_id: point._id\n          });\n          \n          if (detailRes.code === 0 && detailRes.data) {\n            point.qrcode_hash_key = detailRes.data.qrcode_hash_key;\n          } else {\n            throw new Error('获取点位hash_key失败');\n          }\n        }\n        \n        // 生成新的二维码内容\n        const qrContent = QRCodeUtil.getQRCodeData({\n          ...point,\n          qrcode_content: null  // 强制生成新内容\n        }, {\n          includeTimestamp: false  // 不包含时间戳\n        });\n        \n        // 保存到数据库\n        const updateResult = await PatrolApi.callPointFunction('updatePoint', {\n          data: {\n            id: point._id,\n            qrcode_content: qrContent,\n            qrcode_generated_time: new Date().toISOString(),\n            qrcode_enabled: true,\n            qrcode_version: point.qrcode_version || 1\n          }\n        });\n        \n        if (updateResult.code !== 0) {\n          throw new Error(updateResult.message || '生成二维码失败');\n        }\n        \n        // 更新本地数据\n        const index = this.points.findIndex(p => p._id === point._id);\n        if (index !== -1) {\n          this.$set(this.points[index], 'qrcodeContent', qrContent);\n          this.$set(this.points[index], 'qrcode_content', qrContent);\n          this.$set(this.points[index], 'generated', true);\n          this.$set(this.points[index], 'qrcode_enabled', true);\n          this.$set(this.points[index], 'qrcode_version', point.qrcode_version || 1);\n          this.$set(this.points[index], 'qrcode_generated_time', new Date().toISOString());\n        }\n        \n        // 显示成功提示\n        uni.showToast({\n          title: '生成成功',\n          icon: 'success'\n        });\n      } catch (err) {\n        console.error(`生成二维码失败 (${point.name}):`, err);\n        uni.showToast({\n          title: err.message || '生成失败',\n          icon: 'none'\n        });\n        throw err;\n      }\n    },\n    \n    // 二维码生成完成回调\n    onQRCodeComplete(res, point) {\n      if (res.success) {\n        const index = this.points.findIndex(p => p._id === point._id);\n        if (index !== -1) {\n          this.$set(this.points[index], 'generated', true);\n          this.hasGeneratedCodes = true;\n        }\n      }\n    },\n    \n    // 处理批量操作（生成或关闭）\n    async handleBatchOperation() {\n      if (this.allPointsEnabled) {\n        await this.disableAllQRCodes();\n      } else {\n        await this.generateAllQRCodes();\n      }\n    },\n\n    // 保存单个二维码\n    async saveQRCode(point) {\n      try {\n        uni.showLoading({ title: '保存中...' });\n        \n        const qrcodeRef = this.$refs['qrcode-' + point._id];\n        if (!qrcodeRef) {\n          throw new Error('二维码组件未找到');\n        }\n        \n        const qrcode = Array.isArray(qrcodeRef) ? qrcodeRef[0] : qrcodeRef;\n        await qrcode.save({\n          success: () => {\n            uni.showToast({\n              title: '保存成功',\n              icon: 'success'\n            });\n          },\n          fail: (error) => {\n            console.error('保存失败:', error);\n            uni.showToast({\n              title: '保存失败',\n              icon: 'none'\n            });\n          }\n        });\n      } catch (e) {\n        console.error('保存失败:', e);\n        uni.showToast({\n          title: '保存失败',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n    \n    // 批量生成二维码\n    async generateAllQRCodes() {\n      // 过滤出需要生成二维码的点位\n      const pointsToGenerate = this.points.filter(point => \n        !point.qrcode_content || !point.generated\n      );\n\n      if (pointsToGenerate.length === 0) {\n        uni.showToast({\n          title: '没有需要生成的二维码',\n          icon: 'none'\n        });\n        return;\n      }\n\n      // 显示确认框\n      try {\n        const confirmRes = await new Promise((resolve, reject) => {\n          uni.showModal({\n            title: '确认生成',\n            content: `确定要为${pointsToGenerate.length}个点位启用并生成二维码吗？`,\n            confirmText: '确定',\n            cancelText: '取消',\n            success: resolve,\n            fail: reject\n          });\n        });\n\n        if (!confirmRes.confirm) {\n          return;\n        }\n\n        let successCount = 0;\n        let failCount = 0;\n\n        // 显示初始进度\n        uni.showLoading({\n          title: `处理中(0/${pointsToGenerate.length})`,\n          mask: true\n        });\n\n        // 逐个生成二维码\n        for (let i = 0; i < pointsToGenerate.length; i++) {\n          const point = pointsToGenerate[i];\n          try {\n            await this.generateQRCode(point);\n            successCount++;\n            // 更新进度\n            uni.showLoading({\n              title: `处理中(${successCount}/${pointsToGenerate.length})`,\n              mask: true\n            });\n          } catch (err) {\n            console.error(`生成 ${point.name} 的二维码失败:`, err);\n            failCount++;\n          }\n        }\n\n        // 隐藏加载框\n        uni.hideLoading();\n\n        // 显示最终结果\n        uni.showModal({\n          title: '处理完成',\n          content: `生成完成\\n成功：${successCount}个\\n失败：${failCount}个`,\n          showCancel: false\n        });\n      } catch (err) {\n        console.error('批量生成失败:', err);\n        uni.hideLoading();\n        uni.showToast({\n          title: '操作失败',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 一键关闭所有二维码\n    async disableAllQRCodes() {\n      // 获取所有已启用二维码的点位\n      const enabledPoints = this.points.filter(p => p.qrcode_enabled);\n      \n      if (enabledPoints.length === 0) {\n        uni.showToast({\n          title: '没有已启用的二维码',\n          icon: 'none'\n        });\n        return;\n      }\n\n      // 弹窗确认\n      const confirmRes = await new Promise(resolve => {\n        uni.showModal({\n          title: '确认关闭',\n          content: `将关闭${enabledPoints.length}个点位的二维码功能，关闭后将清除所有二维码数据，是否继续？`,\n          success: resolve\n        });\n      });\n\n      if (!confirmRes.confirm) return;\n\n      try {\n        uni.showLoading({\n          title: '处理中(0/' + enabledPoints.length + ')',\n          mask: true\n        });\n\n        let successCount = 0;\n        let failCount = 0;\n\n        for (let i = 0; i < enabledPoints.length; i++) {\n          const point = enabledPoints[i];\n          try {\n            const res = await PatrolApi.callPointFunction('updatePoint', {\n              data: {\n                id: point._id,\n                qrcode_enabled: false,\n                qrcode_content: null,        // 清除二维码内容\n                qrcode_version: null,        // 清除版本号\n                qrcode_hash_key: null,       // 清除哈希密钥\n                qrcode_generated_time: null  // 清除生成时间\n              }\n            });\n            \n            if (res.code === 0) {\n              // 更新本地数据\n              const index = this.points.findIndex(p => p._id === point._id);\n              if (index !== -1) {\n                this.$set(this.points[index], 'qrcode_enabled', false);\n                this.$set(this.points[index], 'qrcode_content', null);\n                this.$set(this.points[index], 'qrcode_version', null);\n                this.$set(this.points[index], 'qrcode_hash_key', null);\n                this.$set(this.points[index], 'qrcode_generated_time', null);\n                this.$set(this.points[index], 'generated', false);\n                this.$set(this.points[index], 'qrcodeContent', null);\n              }\n              successCount++;\n            } else {\n              throw new Error(res.message || '关闭失败');\n            }\n          } catch (e) {\n            console.error(`关闭 ${point.name} 的二维码失败:`, e);\n            failCount++;\n          }\n\n          // 更新进度\n          uni.showLoading({\n            title: `处理中(${successCount}/${enabledPoints.length})`,\n            mask: true\n          });\n        }\n\n        uni.hideLoading();\n        uni.showModal({\n          title: '处理完成',\n          content: `成功：${successCount}个\\n失败：${failCount}个\\n所有二维码数据已清除`,\n          showCancel: false\n        });\n      } catch (e) {\n        console.error('批量关闭失败:', e);\n        uni.showToast({\n          title: '批量关闭失败',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n    \n    /**\n     * @description 保存全部已生成的二维码图片\n     */\n    async saveAllQRCodes() {\n      // 筛选出已生成二维码的点位\n      const pointsToSave = this.points.filter(p => p.generated);\n      \n      // 如果没有可保存的二维码，则提示并返回\n      if (pointsToSave.length === 0) {\n        uni.showToast({\n          title: '没有已生成的二维码可保存',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 确认是否开始保存\n      const confirmRes = await new Promise(resolve => {\n        uni.showModal({\n          title: '确认保存',\n          content: `即将保存 ${pointsToSave.length} 个二维码图片到系统相册，是否继续？`,\n          success: resolve\n        });\n      });\n\n      if (!confirmRes.confirm) return;\n\n      let successCount = 0;\n      let failCount = 0;\n      const totalCount = pointsToSave.length;\n\n      // 显示初始进度\n      uni.showLoading({ \n        title: `保存中(0/${totalCount})`, \n        mask: true \n      });\n      \n      // 逐个保存二维码\n      for (let i = 0; i < totalCount; i++) {\n        const point = pointsToSave[i];\n        const currentProgress = i + 1;\n        \n        // 更新加载提示，显示当前点位名称和进度\n        uni.showLoading({ \n          title: `保存 ${currentProgress}/${totalCount}: ${point.name || '未命名点位'}...`, \n          mask: true \n        });\n\n        try {\n          // 获取对应的 uqrcode 组件实例\n          const qrcodeRef = this.$refs['qrcode-' + point._id];\n          if (!qrcodeRef) {\n            console.warn(`二维码组件 ${point.name} 未找到，跳过保存`);\n            failCount++;\n            continue; // 跳过当前点位\n          }\n          \n          // 确保获取到的是单个组件实例\n          const qrcode = Array.isArray(qrcodeRef) ? qrcodeRef[0] : qrcodeRef;\n          \n          // 调用组件的 save 方法保存图片\n          await new Promise((resolve, reject) => {\n            qrcode.save({\n              success: () => {\n                successCount++;\n                resolve();\n              },\n              fail: (err) => {\n                console.error(`保存 ${point.name} 二维码失败:`, err);\n                failCount++;\n                // 即使失败也 resolve，以便继续处理下一个\n                resolve(); \n              }\n            });\n          });\n\n        } catch (e) {\n          // 捕获预料之外的错误\n          console.error(`处理 ${point.name} 时发生意外错误:`, e);\n          failCount++;\n        }\n      }\n      \n      // 隐藏加载提示\n      uni.hideLoading();\n      \n      // 显示最终结果\n      uni.showModal({\n        title: '保存完成',\n        // 根据成功和失败数量显示不同的提示信息\n        content: `共处理 ${totalCount} 个二维码。\\n成功保存 ${successCount} 个，失败 ${failCount} 个。`,\n        showCancel: false\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.qrcode-batch-page {\n  min-height: 100vh;\n  background: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n}\n\n.point-list {\n  flex: 1;\n  position: relative;\n  padding-bottom: 120rpx; // 为底部按钮留出空间\n}\n\n.point-list-content {\n  padding: 20rpx;\n}\n\n.point-item {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\n  position: relative;\n  z-index: 1;\n  \n  &:active {\n    transform: scale(0.99);\n    transition: transform 0.2s;\n  }\n}\n\n.point-info {\n  margin-bottom: 20rpx;\n  \n  .point-name {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #333;\n    margin-bottom: 12rpx;\n    display: block;\n  }\n  \n  .point-detail {\n    .point-address {\n      font-size: 26rpx;\n      color: #666;\n      margin-bottom: 8rpx;\n      display: block;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n    \n    .point-coordinates, .point-id {\n      font-size: 24rpx;\n      color: #999;\n      display: block;\n      margin-top: 4rpx;\n    }\n  }\n}\n\n.qrcode-preview {\n  position: relative;\n  z-index: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin: 24rpx 0;\n  padding: 20rpx;\n  background: #f8f8f8;\n  border-radius: 12rpx;\n  \n  .qrcode-wrapper {\n    margin-bottom: 16rpx;\n  }\n  \n  .qrcode-canvas {\n    width: 200px !important;\n    height: 200px !important;\n    background: #fff;\n    margin-bottom: 16rpx;\n    border-radius: 8rpx;\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n  }\n  \n  .qrcode-meta {\n    display: flex;\n    align-items: center;\n    gap: 16rpx;\n    \n    text {\n      font-size: 24rpx;\n      background: #fff;\n      padding: 4rpx 12rpx;\n      border-radius: 4rpx;\n    }\n    \n    .version {\n      color: #666;\n    }\n    \n    .status {\n      color: #ff9500;\n      \n      &.status-success {\n        color: #34c759;\n      }\n    }\n  }\n}\n\n.point-actions {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-top: 20rpx;\n  \n  button {\n    flex: 1;\n    min-width: 180rpx;\n    margin: 0;\n    height: 72rpx;\n    font-size: 28rpx;\n    border-radius: 8rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8rpx;\n    color: #fff;\n    \n    text {\n      margin-left: 4rpx;\n    }\n  }\n  \n  .btn-generate {\n    background: linear-gradient(135deg, #3a86ff, #2563eb);\n  }\n  \n  .btn-regenerate {\n    background: linear-gradient(135deg, #ff9500, #ff8000);\n  }\n  \n  .btn-save {\n    background: linear-gradient(135deg, #34c759, #2fb344);\n  }\n  \n  .btn-disable {\n    background: linear-gradient(135deg, #ff3b30, #ff2d20);\n  }\n}\n\n.bottom-bar {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  padding: 24rpx;\n  background: #fff;\n  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-around;\n  gap: 20rpx;\n  z-index: 99;\n  \n  button {\n    flex: 1;\n    margin: 0;\n    font-size: 28rpx;\n    border-radius: 8rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8rpx;\n    color: #fff;\n    \n    text {\n      margin-left: 4rpx;\n    }\n  }\n  \n  .btn-batch-operation {\n    &.btn-generate-all {\n      background: linear-gradient(135deg, #3a86ff, #2563eb);\n    }\n    \n    &.btn-disable-all {\n      background: linear-gradient(135deg, #ff3b30, #ff2d20);\n    }\n  }\n  \n  .btn-save-all {\n    background: linear-gradient(135deg, #34c759, #2fb344);\n  }\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20rpx;\n  color: #666;\n  font-size: 26rpx;\n  \n  text {\n    margin-left: 8rpx;\n  }\n}\n\n.no-more {\n  text-align: center;\n  padding: 20rpx;\n  color: #999;\n  font-size: 26rpx;\n  background: #f5f5f5;\n}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qrcode-batch.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qrcode-batch.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558436507\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
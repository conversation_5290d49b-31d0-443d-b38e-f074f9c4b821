(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/notice/common/vendor"],{

/***/ 174:
/*!******************************************!*\
  !*** D:/Xwzc/js_sdk/validator/notice.js ***!
  \******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.enumConverter = void 0;
exports.filterToWhere = filterToWhere;
exports.validator = void 0;
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }
// 表单校验规则由 schema2code 生成，不建议直接修改校验规则，而建议通过 schema2code 生成, 详情: https://uniapp.dcloud.net.cn/uniCloud/schema

var validator = {
  "title": {
    "rules": [{
      "required": true,
      "errorMessage": "请填写公告标题"
    }, {
      "format": "string"
    }, {
      "maxLength": 50,
      "errorMessage": "标题不能超过50个字符"
    }]
  },
  "content": {
    "rules": [{
      "required": true,
      "errorMessage": "请填写公告内容"
    }, {
      "format": "string"
    }]
  },
  "category": {
    "rules": [{
      "required": true,
      "errorMessage": "请选择公告分类"
    }, {
      "format": "string"
    }, {
      "range": [{
        "value": "公告通知",
        "text": "公告通知"
      }, {
        "value": "重要通知",
        "text": "重要通知"
      }, {
        "value": "活动通知",
        "text": "活动通知"
      }, {
        "value": "其他通知",
        "text": "其他通知"
      }]
    }]
  },
  "isTop": {
    "rules": [{
      "format": "bool"
    }],
    "defaultValue": false
  },
  "createTime": {
    "rules": [{
      "required": true
    }, {
      "format": "timestamp"
    }],
    "defaultValue": {
      "$env": "now"
    }
  },
  "publisher": {
    "rules": [{
      "format": "string"
    }]
  },
  "publisherName": {
    "rules": [{
      "format": "string"
    }]
  },
  "readCount": {
    "rules": [{
      "format": "int"
    }],
    "defaultValue": 0
  },
  "avatar": {
    "rules": [{
      "format": "string"
    }]
  },
  "images": {
    "rules": [{
      "format": "array"
    }]
  }
};
exports.validator = validator;
var enumConverter = {
  "category_valuetotext": {
    "公告通知": "公告通知",
    "重要通知": "重要通知",
    "活动通知": "活动通知",
    "其他通知": "其他通知"
  }
};
exports.enumConverter = enumConverter;
function filterToWhere(filter, command) {
  var where = {};
  for (var field in filter) {
    var _filter$field = filter[field],
      type = _filter$field.type,
      value = _filter$field.value;
    switch (type) {
      case "search":
        if (typeof value === 'string' && value.length) {
          where[field] = new RegExp(value);
        }
        break;
      case "select":
        if (value.length) {
          var selectValue = [];
          var _iterator = _createForOfIteratorHelper(value),
            _step;
          try {
            for (_iterator.s(); !(_step = _iterator.n()).done;) {
              var s = _step.value;
              selectValue.push(command.eq(s));
            }
          } catch (err) {
            _iterator.e(err);
          } finally {
            _iterator.f();
          }
          where[field] = command.or(selectValue);
        }
        break;
      case "range":
        if (value.length) {
          var gt = value[0];
          var lt = value[1];
          where[field] = command.and([command.gte(gt), command.lte(lt)]);
        }
        break;
      case "date":
        if (value.length) {
          var _value = (0, _slicedToArray2.default)(value, 2),
            _s = _value[0],
            e = _value[1];
          var startDate = new Date(_s);
          var endDate = new Date(e);
          where[field] = command.and([command.gte(startDate), command.lte(endDate)]);
        }
        break;
      case "timestamp":
        if (value.length) {
          var _value2 = (0, _slicedToArray2.default)(value, 2),
            _startDate = _value2[0],
            _endDate = _value2[1];
          where[field] = command.and([command.gte(_startDate), command.lte(_endDate)]);
        }
        break;
    }
  }
  return where;
}

/***/ }),

/***/ 183:
/*!************************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/objectWithoutProperties.js ***!
  \************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

var objectWithoutPropertiesLoose = __webpack_require__(/*! ./objectWithoutPropertiesLoose.js */ 184);
function _objectWithoutProperties(source, excluded) {
  if (source == null) return {};
  var target = objectWithoutPropertiesLoose(source, excluded);
  var key, i;
  if (Object.getOwnPropertySymbols) {
    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
    for (i = 0; i < sourceSymbolKeys.length; i++) {
      key = sourceSymbolKeys[i];
      if (excluded.indexOf(key) >= 0) continue;
      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
      target[key] = source[key];
    }
  }
  return target;
}
module.exports = _objectWithoutProperties, module.exports.__esModule = true, module.exports["default"] = module.exports;

/***/ }),

/***/ 184:
/*!*****************************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js ***!
  \*****************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

function _objectWithoutPropertiesLoose(source, excluded) {
  if (source == null) return {};
  var target = {};
  var sourceKeys = Object.keys(source);
  var key, i;
  for (i = 0; i < sourceKeys.length; i++) {
    key = sourceKeys[i];
    if (excluded.indexOf(key) >= 0) continue;
    target[key] = source[key];
  }
  return target;
}
module.exports = _objectWithoutPropertiesLoose, module.exports.__esModule = true, module.exports["default"] = module.exports;

/***/ })

}]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/notice/common/vendor.js.map
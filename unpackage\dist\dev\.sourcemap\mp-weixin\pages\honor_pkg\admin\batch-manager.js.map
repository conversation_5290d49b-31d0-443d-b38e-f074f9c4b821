{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/honor_pkg/admin/batch-manager.vue?3886", "webpack:///D:/Xwzc/pages/honor_pkg/admin/batch-manager.vue?8adc", "webpack:///D:/Xwzc/pages/honor_pkg/admin/batch-manager.vue?961e", "webpack:///D:/Xwzc/pages/honor_pkg/admin/batch-manager.vue?af90", "uni-app:///pages/honor_pkg/admin/batch-manager.vue", "webpack:///D:/Xwzc/pages/honor_pkg/admin/batch-manager.vue?a573", "webpack:///D:/Xwzc/pages/honor_pkg/admin/batch-manager.vue?9225"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "loading", "refreshing", "creating", "loadingText", "showCreateModal", "showSmartModal", "showBatchDetailModal", "showTypePicker", "editingBatch", "selectedBatchDetail", "activeFilter", "searchKeyword", "stats", "totalBatches", "publishedBatches", "draftBatches", "thisMonthBatches", "batchList", "createForm", "type", "meetingDate", "description", "isPublished", "batchTypeOptions", "batchDetailData", "honorCount", "featuredCount", "departmentStats", "honorTypeStats", "recentHonors", "computed", "filteredBatches", "filtered", "batch", "onLoad", "methods", "initializeData", "Promise", "uni", "title", "icon", "loadBatchTypeOptions", "uniCloud", "action", "res", "value", "text", "loadBatchList", "loadStats", "now", "currentMonth", "currentYear", "createDate", "onRefresh", "duration", "changeFilter", "performSearch", "clearSearch", "openCreateBatch", "closeCreateModal", "resetCreateForm", "saveBatch", "validateCreateForm", "openSmartCreate", "closeSmartModal", "createWeeklyBatches", "content", "success", "year", "month", "lastDay", "today", "currentDay", "isCurrentMonth", "batches", "weeks", "console", "week", "targetDay", "day", "checkDate", "dayOfWeek", "meetingDateStr", "period", "week<PERSON><PERSON><PERSON>", "successCount", "batchData", "createMonthlyBatch", "createQuarterlyBatch", "quarter", "quarterStartMonth", "quarterEndMonth", "createYearlyBatch", "editBatch", "publishBatch", "batchId", "deleteBatch", "result", "openBatchDetail", "closeBatchDetail", "refreshBatchDetail", "loadBatchDetailData", "openBatchPublish", "getBatchTypeText", "formatDate", "formatRelativeTime", "date", "getTypeDisplayText", "weekly", "monthly", "quarterly", "yearly", "special", "e", "weekEnd", "weekNumber", "start", "end", "days", "currentDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,gZAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxHA;AAAA;AAAA;AAAA;AAAumB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+e3nB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MAEA;MACAC;QACApB;QACAqB;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACAC;MACA;;MAEA;MACA;QACAC;UAAA;QAAA;MACA;QACAA;UAAA;QAAA;MACA;QACAA;UAAA;QAAA;MACA;;MAEA;MACA;QACA;QACAA;UAAA,OACAC,8CACAA;QAAA,EACA;MACA;MAEA;IACA;EACA;EAEAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAIAC,aACA,+BACA,uBACA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBACA5C;kBACAC;oBACA4C;kBACA;gBACA;cAAA;gBALAC;gBAOA;kBACA7C;kBACA;oBACA;sBAAA;wBACA8C;wBACAC;sBACA;oBAAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACA,2BACA;kBAAAD;kBAAAC;gBAAA,GACA;kBAAAD;kBAAAC;gBAAA,GACA;kBAAAD;kBAAAC;gBAAA,GACA;kBAAAD;kBAAAC;gBAAA,GACA;kBAAAD;kBAAAC;gBAAA,EACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAL;kBACA5C;kBACAC;oBACA4C;kBACA;gBACA;cAAA;gBALAC;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAKA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAC;kBACAC;kBACAC;kBAEAtC;kBACAC;oBAAA;kBAAA;kBACAC,gDAEA;kBACAC;oBACA;oBACA;oBACA,mDACAoC;kBACA;kBAEA;oBACAvC;oBACAC;oBACAC;oBACAC;kBACA;gBACA;kBACA;gBAAA;cACA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAqC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAf;kBACAC;kBACAC;kBACAc;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhB;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAe;MACA;IACA;IAEA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA9D;QACAqB;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAuC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAGAlB;gBACA5C;kBACAD;kBACAqB;kBACAC;kBACAC;kBACAC;gBACA;gBAEA;kBACAvB;gBACA;gBAAA;gBAAA,OAEA2C;kBACA5C;kBACAC;oBAAA4C;oBAAA5C;kBAAA;gBACA;cAAA;gBAHA6C;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAC;gBACA;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsB;MACA;QACAxB;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAuB;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA3B;kBACAC;kBACA2B;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAvB;gCAAA;gCAAA;8BAAA;8BACA;8BACA;8BACA;8BAAA;8BAGAK;8BACAmB;8BACAC,4BAEA;8BACAC;8BACAC;8BACAC;8BACAC;8BAEAC,cAEA;8BACAC;8BAEAC;gCAAA;8BAAA;;8BAEA;8BAAA,uCACAD;8BAAA;8BAAA;4BAAA;8BAAA;gCAAA;gCAAA;8BAAA;8BAAAE;8BAAA,MAEA;gCAAA;gCAAA;8BAAA;8BAEA;8BACAC;8BAEAC;4BAAA;8BAAA;gCAAA;gCAAA;8BAAA;8BACAC;8BACAC;8BACAA;8BAAA,MAEAA;gCAAA;gCAAA;8BAAA;8BAAA;8BACAH;8BAAA;4BAAA;8BANAC;8BAAA;8BAAA;4BAAA;8BAWA;8BACAG;8BAEAR;gCACA5E;gCACAqB;gCACAgE;gCACAC;gCACAhE;gCACAC;gCACAC;8BACA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAAA;4BAAA;8BAIA;8BACA+D;8BAAA,mBACAX;4BAAA;8BAAA;gCAAA;gCAAA;8BAAA;8BAAAY;8BAAA;8BAAA;8BAAA,OAEA5C;gCACA5C;gCACAC;kCACA4C;kCACA5C;gCACA;8BACA;4BAAA;8BANA6C;8BAQA;gCACAyC;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAAA;4BAAA;8BAAA,MAMAA;gCAAA;gCAAA;8BAAA;8BACA/C;gCACAC;gCACAC;8BACA;8BAAA;8BAAA,OAEA;4BAAA;8BAAA;8BAAA,OACA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA,MAEA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAIAF;gCACAC;gCACAC;8BACA;4BAAA;8BAAA;8BAEA;8BAAA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA+C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAGAtC;gBACAmB;gBACAC;gBACAC;gBAEAgB;kBACAxF;kBACAqB;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEAoB;kBACA5C;kBACAC;oBACA4C;oBACA5C;kBACA;gBACA;cAAA;gBANA6C;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAGAvC;gBACAmB;gBACAC,4BAEA;gBACAoB;gBACAC;gBACAC,+BAEA;gBACArB;gBACAlD;gBAEAkE;kBACAxF;kBACAqB;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEAoB;kBACA5C;kBACAC;oBACA4C;oBACA5C;kBACA;gBACA;cAAA;gBANA6C;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAGA3C;gBACAmB,0BAEA;gBACAhD;gBAEAkE;kBACAxF;kBACAqB;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEAoB;kBACA5C;kBACAC;oBACA4C;oBACA5C;kBACA;gBACA;cAAA;gBANA6C;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAqD;MACA;MACA;QACA/F;QACAqB;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEAwE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEApD;kBACA5C;kBACAC;oBACA4C;oBACA5C;sBAAAgG;oBAAA;kBACA;gBACA;cAAA;gBANAnD;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAwD;MAAA;MACA1D;QACAC;QACA2B;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAvB;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA;oBAAA,OAEAF;sBACA5C;sBACAC;wBACA4C;wBACA5C;0BAAAgG;wBAAA;sBACA;oBACA;kBAAA;oBANAE;oBAAA,MAQAA;sBAAA;sBAAA;oBAAA;oBACA3D;sBACAC;sBACAC;oBACA;oBAAA;oBAAA,OAEA;kBAAA;oBAAA;oBAAA,OACA;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA,MAEA;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAGAF;sBACAC;sBACAC;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA0D;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBACA9D;kBACAC;kBACAC;kBACAc;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA+C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAIA3D;kBACA5C;kBACAC;oBACA4C;oBACA5C;sBACAgG;oBACA;kBACA;gBACA;cAAA;gBARAnD;gBAUA;kBACA;oBACAnB;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;gBACA;kBACA;kBACAS;oBACAC;oBACAC;kBACA;;kBAEA;kBACA;oBACAf;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAS;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;kBACAf;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAyE;MACAhE;QACAC;QACAC;MACA;IACA;IAEA;IAEA;IACA+D;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;QACA;UACA;UACA;UACA;UACA;QACA;QAEA;MACA;QACA5B;QACA;MACA;IACA;IAEA;IACA6B;MACA;QACA;MACA;MAEA;QACA;QACA;;QAEA;QACA;UACA;YACA;YACAC;UACA;YACA;YACAA;UACA;YACA;YACAA;UACA;QACA;UACAA;QACA;;QAEA;QACA;UACA;QACA;QAEA;;QAEA;QACA;UACA;QACA;QAEA;QACA;QACA;QAEA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA9B;QACA;MACA;IACA;IAEA+B;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;EAAA,yFAEA7F;IACA;MACAyF;MACAC;MACAC;MACAC;MACAC;IACA;IACA;EACA,8EAGAnE;IACA;IACA;EACA,oGAGAoE;IACA;EACA,4FAGA;IACA;IACA;IACAP;IACA;EACA,4FAEA;IACA;IACA;IACAA;IACA;EACA,0FAEAA;IACA;IACA;IACA;IACA;EACA,oGAGAtC;IACA;IACA;IAEA;IACA;IACA;;IAEA;IACA;MACA;MACA;;MAEA;MACA;QACA8C;MACA;;MAEA;MACA;QACAvC;UACAwC;UACAC;UACAC;UACAC;QACA;MACA;MAEAC;MACAJ;IACA;IAEA;EACA,wEAEA;IACA7E;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACj7CA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,gpCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/honor_pkg/admin/batch-manager.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/honor_pkg/admin/batch-manager.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./batch-manager.vue?vue&type=template&id=86285dce&scoped=true&\"\nvar renderjs\nimport script from \"./batch-manager.vue?vue&type=script&lang=js&\"\nexport * from \"./batch-manager.vue?vue&type=script&lang=js&\"\nimport style0 from \"./batch-manager.vue?vue&type=style&index=0&id=86285dce&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"86285dce\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/honor_pkg/admin/batch-manager.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batch-manager.vue?vue&type=template&id=86285dce&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker\" */ \"@/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.filteredBatches, function (batch, index) {\n    var $orig = _vm.__get_orig(batch)\n    var m0 = batch.type ? _vm.getTypeDisplayText(batch.type) : null\n    var m1 = batch.createTime ? _vm.formatRelativeTime(batch.createTime) : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  var g0 = _vm.filteredBatches.length === 0 && !_vm.loading\n  var m2 =\n    _vm.showCreateModal && _vm.createForm.type\n      ? _vm.getBatchTypeText(_vm.createForm.type)\n      : null\n  var m3 = _vm.showCreateModal ? _vm.getMinDateString() : null\n  var m4 = _vm.showCreateModal ? _vm.getMaxDateString() : null\n  var m5 =\n    _vm.showBatchDetailModal && _vm.selectedBatchDetail\n      ? _vm.getBatchTypeText(_vm.selectedBatchDetail.type)\n      : null\n  var m6 =\n    _vm.showBatchDetailModal && _vm.selectedBatchDetail\n      ? _vm.formatDate(_vm.selectedBatchDetail.meetingDate)\n      : null\n  var g1 = _vm.showBatchDetailModal\n    ? _vm.batchDetailData.departmentStats.length\n    : null\n  var g2 = _vm.showBatchDetailModal\n    ? _vm.batchDetailData.honorTypeStats.length\n    : null\n  var g3 = _vm.showBatchDetailModal\n    ? _vm.batchDetailData.recentHonors.length\n    : null\n  var l1 =\n    _vm.showBatchDetailModal && g3 > 0\n      ? _vm.__map(_vm.batchDetailData.recentHonors, function (honor, __i2__) {\n          var $orig = _vm.__get_orig(honor)\n          var m7 = _vm.formatRelativeTime(honor.createTime)\n          return {\n            $orig: $orig,\n            m7: m7,\n          }\n        })\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.refreshing = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showTypePicker = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showTypePicker = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showTypePicker = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batch-manager.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batch-manager.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"batch-manager-container\">\n    <!-- 头部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"18\" color=\"#FFFFFF\"></uni-icons>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"title\">智能批次管理</text>\n          <text class=\"subtitle\">批次创建 · 发布管理</text>\n        </view>\n        <view class=\"header-actions\">\n          <view class=\"add-btn\" @click=\"openCreateBatch\">\n            <uni-icons type=\"plus\" size=\"18\" color=\"#FFFFFF\"></uni-icons>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <scroll-view \n      class=\"content-scroll\" \n      scroll-y \n      refresher-enabled \n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\"\n      @refresherrestore=\"refreshing = false\"\n    >\n      <!-- 快速操作 -->\n      <view class=\"quick-actions\">\n        <view class=\"action-item\" @click=\"openSmartCreate\">\n          <view class=\"action-icon\">\n            <uni-icons type=\"gear-filled\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n          </view>\n          <view class=\"action-content\">\n            <text class=\"action-title\">智能创建</text>\n            <text class=\"action-desc\">根据周期自动生成批次</text>\n          </view>\n        </view>\n        \n        <view class=\"action-item\" @click=\"openBatchPublish\">\n          <view class=\"action-icon publish\">\n            <uni-icons type=\"calendar-filled\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n          </view>\n          <view class=\"action-content\">\n            <text class=\"action-title\">批量发布</text>\n            <text class=\"action-desc\">一键发布多个批次</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 批次统计 -->\n      <view class=\"batch-stats\">\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ stats.totalBatches }}</text>\n          <text class=\"stat-label\">总批次数</text>\n        </view>\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ stats.publishedBatches }}</text>\n          <text class=\"stat-label\">已发布</text>\n        </view>\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ stats.draftBatches }}</text>\n          <text class=\"stat-label\">草稿</text>\n        </view>\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ stats.thisMonthBatches }}</text>\n          <text class=\"stat-label\">本月新增</text>\n        </view>\n      </view>\n\n      <!-- 筛选和搜索 -->\n      <view class=\"filter-section\">\n        <view class=\"filter-tabs\">\n          <view \n            class=\"filter-tab\" \n            :class=\"{ active: activeFilter === 'all' }\"\n            @click=\"changeFilter('all')\"\n          >\n            全部\n          </view>\n          <view \n            class=\"filter-tab\" \n            :class=\"{ active: activeFilter === 'published' }\"\n            @click=\"changeFilter('published')\"\n          >\n            已发布\n          </view>\n          <view \n            class=\"filter-tab\" \n            :class=\"{ active: activeFilter === 'draft' }\"\n            @click=\"changeFilter('draft')\"\n          >\n            草稿\n          </view>\n          <view \n            class=\"filter-tab\" \n            :class=\"{ active: activeFilter === 'special' }\"\n            @click=\"changeFilter('special')\"\n          >\n            特别表彰\n          </view>\n        </view>\n        \n        <view class=\"search-box\">\n          <uni-easyinput \n            v-model=\"searchKeyword\"\n            placeholder=\"搜索批次名称...\"\n            prefixIcon=\"search\"\n            @confirm=\"performSearch\"\n            @clear=\"clearSearch\"\n            clearable\n          ></uni-easyinput>\n        </view>\n      </view>\n\n      <!-- 批次列表 -->\n      <view class=\"batch-list\">\n        <view \n          class=\"batch-item\" \n          v-for=\"(batch, index) in filteredBatches\" \n          :key=\"batch._id\"\n          @click=\"openBatchDetail(batch)\"\n        >\n          <view class=\"batch-header\">\n            <view class=\"batch-info\">\n              <text class=\"batch-name\">{{ batch.name }}</text>\n              <view class=\"batch-meta\">\n                <text class=\"batch-date\">{{ batch.meetingDate }}</text>\n                <view class=\"batch-status\" :class=\"batch.isPublished ? 'published' : 'draft'\">\n                  {{ batch.isPublished ? '已发布' : '草稿' }}\n                </view>\n              </view>\n            </view>\n            <view class=\"batch-actions\">\n              <view class=\"action-btn edit\" @click.stop=\"editBatch(batch)\">\n                <uni-icons type=\"compose\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n              </view>\n              <view class=\"action-btn publish\" v-if=\"!batch.isPublished\" @click.stop=\"publishBatch(batch)\">\n                <uni-icons type=\"upload\" size=\"16\" color=\"#10b981\"></uni-icons>\n              </view>\n              <view class=\"action-btn delete\" @click.stop=\"deleteBatch(batch)\">\n                <uni-icons type=\"trash\" size=\"16\" color=\"#ef4444\"></uni-icons>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"batch-details\">\n            <view class=\"batch-desc\" v-if=\"batch.description\">\n              {{ batch.description }}\n            </view>\n            <view class=\"batch-stats-row\">\n              <view class=\"batch-stat\">\n                <uni-icons type=\"medal-filled\" size=\"14\" color=\"#3a86ff\"></uni-icons>\n                <text class=\"stat-text\">{{ batch.honorCount || 0 }} 项表彰</text>\n              </view>\n              <view class=\"batch-stat\" v-if=\"batch.type\">\n                <uni-icons type=\"flag-filled\" size=\"14\" color=\"#f59e0b\"></uni-icons>\n                <text class=\"stat-text\">{{ getTypeDisplayText(batch.type) }}</text>\n              </view>\n              <view class=\"batch-stat\" v-if=\"batch.createTime\">\n                <uni-icons type=\"calendar\" size=\"14\" color=\"#8a94a6\"></uni-icons>\n                <text class=\"stat-text\">{{ formatRelativeTime(batch.createTime) }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 空状态 -->\n        <view v-if=\"filteredBatches.length === 0 && !loading\" class=\"empty-state\">\n          <uni-icons type=\"info\" size=\"48\" color=\"#c4c4c4\"></uni-icons>\n          <text class=\"empty-text\">暂无批次数据</text>\n          <button class=\"empty-btn\" @click=\"openCreateBatch\">创建第一个批次</button>\n        </view>\n      </view>\n\n      <!-- 底部安全区域 -->\n      <view class=\"bottom-safe-area\"></view>\n    </scroll-view>\n\n    <!-- 创建批次弹窗 -->\n    <view v-if=\"showCreateModal\" class=\"modal-overlay\" @click=\"closeCreateModal\">\n      <view class=\"modal-content create-modal\" @click.stop>\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">{{ editingBatch ? '编辑' : '创建' }}表彰批次</text>\n          <view class=\"close-btn\" @click=\"closeCreateModal\">\n            <uni-icons type=\"close\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n          </view>\n        </view>\n        \n        <view class=\"modal-body date-picker-body\">\n          <view class=\"form-section\">\n            <view class=\"form-item\">\n              <view class=\"form-label\">批次名称 <text class=\"required\">*</text></view>\n              <uni-easyinput \n                v-model=\"createForm.name\"\n                placeholder=\"如：6月第4周表彰\"\n                :clearable=\"true\"\n              ></uni-easyinput>\n            </view>\n            \n            <view class=\"form-item\">\n              <view class=\"form-label\">批次类型 <text class=\"required\">*</text></view>\n              <view class=\"custom-picker\" @click=\"showTypePicker = true\">\n                <text class=\"picker-text\" :class=\"{ placeholder: !createForm.type }\">\n                  {{ createForm.type ? getBatchTypeText(createForm.type) : '请选择批次类型' }}\n                </text>\n                <uni-icons type=\"down\" size=\"14\" color=\"#8a94a6\"></uni-icons>\n              </view>\n            </view>\n            \n            <view class=\"form-item\">\n              <view class=\"form-label\">会议日期 <text class=\"required\">*</text></view>\n              <uni-datetime-picker\n                v-model=\"createForm.meetingDate\"\n                type=\"date\"\n                :clearable=\"false\"\n                placeholder=\"请选择会议日期\"\n                :start=\"getMinDateString()\"\n                :end=\"getMaxDateString()\"\n              ></uni-datetime-picker>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label\">批次描述</text>\n              <uni-easyinput \n                v-model=\"createForm.description\"\n                type=\"textarea\"\n                placeholder=\"可选：批次描述信息\"\n                :auto-height=\"true\"\n                maxlength=\"200\"\n              ></uni-easyinput>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label\">发布状态</text>\n              <view class=\"publish-switch-wrapper\">\n                <view class=\"switch-info\">\n                  <text class=\"switch-desc\">{{ createForm.isPublished ? '发布后用户可见' : '暂存草稿，仅管理员可见' }}</text>\n                </view>\n                <switch \n                  :checked=\"createForm.isPublished\" \n                  @change=\"onPublishSwitchChange\"\n                  color=\"#10b981\"\n                  class=\"publish-switch\"\n                />\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"modal-footer\">\n          <button class=\"modal-btn cancel\" @click=\"closeCreateModal\">取消</button>\n          <button class=\"modal-btn confirm\" @click=\"saveBatch\" :disabled=\"creating\">\n            {{ creating ? '保存中...' : (editingBatch ? '更新' : '创建') }}\n          </button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 智能创建弹窗 -->\n    <view v-if=\"showSmartModal\" class=\"modal-overlay\" @click=\"closeSmartModal\">\n      <view class=\"modal-content smart-modal\" @click.stop>\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">智能批次创建</text>\n          <view class=\"close-btn\" @click=\"closeSmartModal\">\n            <uni-icons type=\"close\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n          </view>\n        </view>\n        \n        <view class=\"modal-body\">\n          <view class=\"smart-options\">\n            <view class=\"smart-option\" @click=\"createWeeklyBatches\">\n              <view class=\"option-icon\">\n                <uni-icons type=\"calendar\" size=\"24\" color=\"#3a86ff\"></uni-icons>\n              </view>\n              <view class=\"option-content\">\n                <text class=\"option-title\">创建本月周表彰</text>\n                <text class=\"option-desc\">自动创建本月所有周次的表彰批次</text>\n              </view>\n            </view>\n            \n            <view class=\"smart-option\" @click=\"createMonthlyBatch\">\n              <view class=\"option-icon monthly\">\n                <uni-icons type=\"calendar-filled\" size=\"24\" color=\"#10b981\"></uni-icons>\n              </view>\n              <view class=\"option-content\">\n                <text class=\"option-title\">创建月度表彰</text>\n                <text class=\"option-desc\">创建当月的月度表彰批次</text>\n              </view>\n            </view>\n            \n            <view class=\"smart-option\" @click=\"createQuarterlyBatch\">\n              <view class=\"option-icon quarterly\">\n                <uni-icons type=\"gift\" size=\"24\" color=\"#f59e0b\"></uni-icons>\n              </view>\n              <view class=\"option-content\">\n                <text class=\"option-title\">创建季度表彰</text>\n                <text class=\"option-desc\">创建当季度的表彰批次</text>\n              </view>\n            </view>\n            \n            <view class=\"smart-option\" @click=\"createYearlyBatch\">\n              <view class=\"option-icon yearly\">\n                <uni-icons type=\"star-filled\" size=\"24\" color=\"#e11d48\"></uni-icons>\n              </view>\n              <view class=\"option-content\">\n                <text class=\"option-title\">创建年度表彰</text>\n                <text class=\"option-desc\">创建当年的年度表彰批次</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 批次详情弹窗 -->\n    <view v-if=\"showBatchDetailModal\" class=\"modal-overlay\" @click=\"closeBatchDetail\">\n      <view class=\"modal-content detail-modal\" @click.stop>\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">批次详情</text>\n          <view class=\"header-actions\">\n            <view class=\"refresh-btn\" @click=\"refreshBatchDetail\">\n              <uni-icons type=\"refresh\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n            </view>\n            <view class=\"close-btn\" @click=\"closeBatchDetail\">\n              <uni-icons type=\"close\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"modal-body\">\n          <!-- 批次基本信息 -->\n          <view class=\"detail-section\" v-if=\"selectedBatchDetail\">\n            <view class=\"section-title\">\n              <uni-icons type=\"info-filled\" size=\"20\" color=\"#3a86ff\"></uni-icons>\n              <text>基本信息</text>\n            </view>\n            <view class=\"info-grid\">\n              <view class=\"info-item\">\n                <text class=\"info-label\">批次名称</text>\n                <text class=\"info-value\">{{ selectedBatchDetail.name }}</text>\n              </view>\n              <view class=\"info-item\">\n                <text class=\"info-label\">批次类型</text>\n                <text class=\"info-value\">{{ getBatchTypeText(selectedBatchDetail.type) }}</text>\n              </view>\n              <view class=\"info-item\">\n                <text class=\"info-label\">会议日期</text>\n                <text class=\"info-value\">{{ formatDate(selectedBatchDetail.meetingDate) }}</text>\n              </view>\n              <view class=\"info-item\">\n                <text class=\"info-label\">发布状态</text>\n                <view class=\"status-tag\" :class=\"selectedBatchDetail.isPublished ? 'published' : 'draft'\">\n                  {{ selectedBatchDetail.isPublished ? '已发布' : '草稿' }}\n                </view>\n              </view>\n            </view>\n          </view>\n\n          <!-- 统计数据 -->\n          <view class=\"detail-section\">\n            <view class=\"section-title\">\n              <uni-icons type=\"bars\" size=\"20\" color=\"#10b981\"></uni-icons>\n              <text>数据统计</text>\n            </view>\n            <view class=\"stats-grid\">\n              <view class=\"stat-card\">\n                <view class=\"stat-number\">{{ batchDetailData.honorCount }}</view>\n                <view class=\"stat-label\">表彰记录</view>\n              </view>\n              <view class=\"stat-card featured\">\n                <view class=\"stat-number\">{{ batchDetailData.featuredCount }}</view>\n                <view class=\"stat-label\">精选表彰</view>\n              </view>\n            </view>\n          </view>\n\n          <!-- 部门分布 -->\n          <view class=\"detail-section\">\n            <view class=\"section-title\">\n              <uni-icons type=\"home\" size=\"20\" color=\"#f59e0b\"></uni-icons>\n              <text>部门分布</text>\n            </view>\n            <view v-if=\"batchDetailData.departmentStats.length > 0\" class=\"department-list\">\n              <view v-for=\"dept in batchDetailData.departmentStats\" :key=\"dept.name\" class=\"department-item\">\n                <view class=\"dept-info\">\n                  <text class=\"dept-name\">{{ dept.name }}</text>\n                  <text class=\"dept-count\">{{ dept.count }}人</text>\n                </view>\n                <view class=\"dept-progress\">\n                  <view class=\"progress-bar\" :style=\"{ width: dept.percentage + '%' }\"></view>\n                </view>\n                <text class=\"dept-percentage\">{{ dept.percentage }}%</text>\n              </view>\n            </view>\n            <view v-else class=\"empty-data\">\n              <uni-icons type=\"info\" size=\"32\" color=\"#c4c4c4\"></uni-icons>\n              <text class=\"empty-text\">暂无部门数据</text>\n            </view>\n          </view>\n\n          <!-- 荣誉类型分布 -->\n          <view class=\"detail-section\">\n            <view class=\"section-title\">\n              <uni-icons type=\"star-filled\" size=\"20\" color=\"#8b5cf6\"></uni-icons>\n              <text>荣誉类型</text>\n            </view>\n            <view v-if=\"batchDetailData.honorTypeStats.length > 0\" class=\"honor-type-list\">\n              <view v-for=\"type in batchDetailData.honorTypeStats\" :key=\"type.name\" class=\"honor-type-item\">\n                <view class=\"type-dot\" :style=\"{ backgroundColor: type.color }\"></view>\n                <text class=\"type-name\">{{ type.name }}</text>\n                <text class=\"type-count\">{{ type.count }}</text>\n              </view>\n            </view>\n            <view v-else class=\"empty-data\">\n              <uni-icons type=\"info\" size=\"32\" color=\"#c4c4c4\"></uni-icons>\n              <text class=\"empty-text\">暂无荣誉类型数据</text>\n            </view>\n          </view>\n\n          <!-- 最近记录 -->\n          <view class=\"detail-section\">\n            <view class=\"section-title\">\n              <uni-icons type=\"clock\" size=\"20\" color=\"#ef4444\"></uni-icons>\n              <text>最近记录</text>\n            </view>\n            <view v-if=\"batchDetailData.recentHonors.length > 0\" class=\"recent-honors\">\n              <view v-for=\"honor in batchDetailData.recentHonors\" :key=\"honor.id\" class=\"honor-item\">\n                <view class=\"honor-left\">\n                  <text class=\"honor-user\">{{ honor.userName }}</text>\n                  <text class=\"honor-dept\">{{ honor.department }}</text>\n                </view>\n                <view class=\"honor-center\">\n                  <text class=\"honor-type\">{{ honor.honorType }}</text>\n                  <text class=\"honor-time\">{{ formatRelativeTime(honor.createTime) }}</text>\n                </view>\n                <view class=\"honor-right\">\n                  <view v-if=\"honor.isFeatured\" class=\"featured-badge\">\n                    <uni-icons type=\"star-filled\" size=\"14\" color=\"#f59e0b\"></uni-icons>\n                  </view>\n                </view>\n              </view>\n            </view>\n            <view v-else class=\"empty-data\">\n              <uni-icons type=\"info\" size=\"32\" color=\"#c4c4c4\"></uni-icons>\n              <text class=\"empty-text\">暂无荣誉记录</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- H5自定义类型选择器 -->\n    <!-- 批次类型选择器 -->\n    <view v-if=\"showTypePicker\" class=\"modal-overlay\" @click=\"showTypePicker = false\">\n      <view class=\"modal-content type-picker-modal\" @click.stop>\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">选择批次类型</text>\n          <view class=\"close-btn\" @click=\"showTypePicker = false\">\n            <uni-icons type=\"close\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n          </view>\n        </view>\n        <view class=\"modal-body\">\n          <view class=\"type-options\">\n            <view \n              v-for=\"option in batchTypeOptions\" \n              :key=\"option.value\"\n              class=\"type-option\"\n              :class=\"{ active: createForm.type === option.value }\"\n              @click=\"selectType(option.value)\"\n            >\n              <view class=\"option-content\">\n                <text class=\"option-text\">{{ option.text }}</text>\n                <view v-if=\"createForm.type === option.value\" class=\"option-check\">\n                  <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#10b981\"></uni-icons>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-overlay\">\n      <view class=\"loading-content\">\n        <uni-icons type=\"spinner-cycle\" size=\"32\" color=\"#3a86ff\" class=\"loading-spin\"></uni-icons>\n        <text class=\"loading-text\">{{ loadingText }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'BatchManager',\n  data() {\n    return {\n      loading: false,\n      refreshing: false,\n      creating: false,\n      loadingText: '加载中...',\n      \n      // 模态框状态\n      showCreateModal: false,\n      showSmartModal: false,\n      showBatchDetailModal: false,\n      showTypePicker: false,  // H5自定义类型选择器\n      editingBatch: null,\n      selectedBatchDetail: null,\n      \n      // 筛选和搜索\n      activeFilter: 'all',\n      searchKeyword: '',\n      \n      // 统计数据\n      stats: {\n        totalBatches: 0,\n        publishedBatches: 0,\n        draftBatches: 0,\n        thisMonthBatches: 0\n      },\n      \n      // 批次数据\n      batchList: [],\n      \n      // 创建表单\n      createForm: {\n        name: '',\n        type: '',\n        meetingDate: '',\n        description: '',\n        isPublished: false\n      },\n      \n      // 批次类型选项\n      batchTypeOptions: [],\n      \n      // 批次详情数据\n      batchDetailData: {\n        honorCount: 0,\n        featuredCount: 0,\n        departmentStats: [],\n        honorTypeStats: [],\n        recentHonors: []\n      }\n    }\n  },\n  \n  computed: {\n    filteredBatches() {\n      let filtered = this.batchList\n      \n      // 按状态筛选\n      if (this.activeFilter === 'published') {\n        filtered = filtered.filter(batch => batch.isPublished)\n      } else if (this.activeFilter === 'draft') {\n        filtered = filtered.filter(batch => !batch.isPublished)\n      } else if (this.activeFilter === 'special') {\n        filtered = filtered.filter(batch => batch.type === 'special')\n      }\n      \n      // 按关键词搜索\n      if (this.searchKeyword.trim()) {\n        const keyword = this.searchKeyword.toLowerCase()\n        filtered = filtered.filter(batch => \n          batch.name.toLowerCase().includes(keyword) ||\n          (batch.description && batch.description.toLowerCase().includes(keyword))\n        )\n      }\n      \n      return filtered\n    }\n  },\n  \n  async onLoad() {\n    await this.initializeData()\n  },\n  \n  methods: {\n    // 初始化数据\n    async initializeData() {\n      this.loading = true\n      this.loadingText = '加载批次数据...'\n      \n      try {\n        // 并行加载类型选项和批次列表\n        await Promise.all([\n          this.loadBatchTypeOptions(),\n          this.loadBatchList()\n        ])\n        \n        // 批次列表加载完成后再计算统计数据\n        await this.loadStats()\n      } catch (error) {\n        uni.showToast({\n          title: '数据加载失败',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 加载批次类型选项\n    async loadBatchTypeOptions() {\n      try {\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getBatchTypes'\n          }\n        })\n        \n        if (res.result.code === 0) {\n          const data = res.result.data\n          if (Array.isArray(data)) {\n            this.batchTypeOptions = data.map(type => ({\n              value: type.value,\n              text: type.text\n            }))\n          }\n        }\n      } catch (error) {\n        // 加载批次类型失败\n        // 如果云函数不支持，使用基础类型\n        this.batchTypeOptions = [\n          { value: 'weekly', text: '周表彰' },\n          { value: 'monthly', text: '月表彰' },\n          { value: 'quarterly', text: '季度表彰' },\n          { value: 'yearly', text: '年度表彰' },\n          { value: 'special', text: '特别表彰' }\n        ]\n      }\n    },\n    \n    // 加载批次列表\n    async loadBatchList() {\n      try {\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getBatches'\n          }\n        })\n        \n        if (res.result.code === 0) {\n          this.batchList = res.result.data || []\n        } else {\n          throw new Error(res.result.message || '获取批次列表失败')\n        }\n      } catch (error) {\n        throw error\n      }\n    },\n    \n    // 加载统计数据\n    async loadStats() {\n      try {\n        const now = new Date()\n        const currentMonth = now.getMonth() + 1\n        const currentYear = now.getFullYear()\n        \n        const totalBatches = this.batchList.length\n        const publishedBatches = this.batchList.filter(batch => batch.isPublished).length\n        const draftBatches = totalBatches - publishedBatches\n        \n        // 计算本月新增\n        const thisMonthBatches = this.batchList.filter(batch => {\n          if (!batch.createTime) return false\n          const createDate = new Date(batch.createTime)\n          return createDate.getFullYear() === currentYear && \n                 createDate.getMonth() + 1 === currentMonth\n        }).length\n        \n        this.stats = {\n          totalBatches,\n          publishedBatches,\n          draftBatches,\n          thisMonthBatches\n        }\n      } catch (error) {\n        // 加载统计数据失败\n      }\n    },\n    \n    // 下拉刷新\n    async onRefresh() {\n      this.refreshing = true\n      try {\n        await this.initializeData()\n        uni.showToast({\n          title: '刷新成功',\n          icon: 'success',\n          duration: 1500\n        })\n      } catch (error) {\n        uni.showToast({\n          title: '刷新失败',\n          icon: 'none'\n        })\n      } finally {\n        this.refreshing = false\n      }\n    },\n    \n    // 筛选变更\n    changeFilter(filter) {\n      this.activeFilter = filter\n    },\n    \n    // 搜索\n    performSearch() {\n      // 搜索在computed中实时进行\n    },\n    \n    // 清除搜索\n    clearSearch() {\n      this.searchKeyword = ''\n    },\n    \n    // 打开创建批次\n    openCreateBatch() {\n      this.showCreateModal = true\n      this.resetCreateForm()\n    },\n    \n    // 关闭创建批次\n    closeCreateModal() {\n      this.showCreateModal = false\n      this.editingBatch = null\n      this.resetCreateForm()\n    },\n    \n    // 重置创建表单\n    resetCreateForm() {\n      this.createForm = {\n        name: '',\n        type: '',\n        meetingDate: '',\n        description: '',\n        isPublished: false\n      }\n    },\n    \n    // 保存批次（创建或编辑）\n    async saveBatch() {\n      if (!this.validateCreateForm()) {\n        return\n      }\n      \n      this.creating = true\n      \n      try {\n        const action = this.editingBatch ? 'updateBatch' : 'createBatch'\n        const data = {\n          name: this.createForm.name,\n          type: this.createForm.type,\n          meetingDate: this.createForm.meetingDate,\n          description: this.createForm.description,\n          isPublished: this.createForm.isPublished\n        }\n        \n        if (this.editingBatch) {\n          data.batchId = this.editingBatch._id\n        }\n        \n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: { action, data }\n        })\n        \n        if (res.result.code === 0) {\n          uni.showToast({\n            title: this.editingBatch ? '批次更新成功' : '批次创建成功',\n            icon: 'success'\n          })\n          \n          this.closeCreateModal()\n          await this.loadBatchList()\n          await this.loadStats()\n        } else {\n          throw new Error(res.result.message || (this.editingBatch ? '更新失败' : '创建失败'))\n        }\n      } catch (error) {\n        uni.showToast({\n          title: error.message || '保存失败',\n          icon: 'none'\n        })\n      } finally {\n        this.creating = false\n      }\n    },\n    \n    // 验证创建表单\n    validateCreateForm() {\n      if (!this.createForm.name.trim()) {\n        uni.showToast({\n          title: '请输入批次名称',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.createForm.type) {\n        uni.showToast({\n          title: '请选择批次类型',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.createForm.meetingDate) {\n        uni.showToast({\n          title: '请选择会议日期',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      return true\n    },\n    \n    // 智能创建相关方法\n    openSmartCreate() {\n      this.showSmartModal = true\n    },\n    \n    closeSmartModal() {\n      this.showSmartModal = false\n    },\n    \n    // 创建周表彰批次\n    async createWeeklyBatches() {\n      uni.showModal({\n        title: '智能创建',\n        content: '将为本月创建所有周次的表彰批次，确认继续？',\n        success: async (res) => {\n          if (res.confirm) {\n            this.closeSmartModal()\n            this.loading = true\n            this.loadingText = '正在智能创建周表彰批次...'\n            \n            try {\n              const now = new Date()\n              const year = now.getFullYear()\n              const month = now.getMonth() + 1\n              \n              // 使用优化后的简单周次计算逻辑\n              const lastDay = new Date(year, month, 0).getDate() // 本月最后一天\n              const today = new Date()\n              const currentDay = today.getDate()\n              const isCurrentMonth = today.getFullYear() === year && today.getMonth() + 1 === month\n              \n              const batches = []\n              \n              // 优化后的周次计算逻辑：简单直接的按自然周计算\n              const weeks = this.calculateWeeksInMonth(year, month)\n              \n              console.log(`${month}月共计算出${weeks.length}周:`, weeks.map(w => `第${w.weekNumber}周(${w.start}-${w.end}号)`).join(', '))\n              \n              // 生成批次\n              for (const week of weeks) {\n                // 如果是当前月份，只创建已经过去的周或当前周\n                if (!isCurrentMonth || week.start <= currentDay) {\n                  \n                  // 寻找该周的周五，如果没有则用该周最后一天\n                  let targetDay = week.end // 默认使用该周最后一天\n                  \n                  for (let day = week.start; day <= week.end; day++) {\n                    const checkDate = new Date(year, month - 1, day)\n                    let dayOfWeek = checkDate.getDay()\n                    dayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek\n                    \n                    if (dayOfWeek === 5) { // 周五\n                      targetDay = day\n                      break\n                    }\n                  }\n                  \n                  // 使用时区安全的日期格式化\n                  const meetingDateStr = `${year}-${String(month).padStart(2, '0')}-${String(targetDay).padStart(2, '0')}`\n                  \n                  batches.push({\n                    name: `${year}年${month}月第${week.weekNumber}周表彰`,\n                    type: 'weekly',\n                    period: `${month}月第${week.weekNumber}周`,\n                    weekLabel: `第${week.weekNumber}周`,\n                    meetingDate: meetingDateStr,\n                    description: `${year}年${month}月第${week.weekNumber}周表彰批次`,\n                    isPublished: false\n                  })\n                }\n              }\n              \n              // 批量创建\n              let successCount = 0\n              for (const batchData of batches) {\n                try {\n                  const res = await uniCloud.callFunction({\n                    name: 'honor-admin',\n                    data: {\n                      action: 'createBatch',\n                      data: batchData\n                    }\n                  })\n                  \n                  if (res.result.code === 0) {\n                    successCount++\n                  }\n                } catch (error) {\n                  // 创建单个批次失败\n                }\n              }\n              \n              if (successCount > 0) {\n                uni.showToast({\n                  title: `成功创建${successCount}个周表彰批次`,\n                  icon: 'success'\n                })\n                \n                await this.loadBatchList()\n                await this.loadStats()\n              } else {\n                throw new Error('所有批次创建失败')\n              }\n              \n            } catch (error) {\n              uni.showToast({\n                title: error.message || '智能创建失败',\n                icon: 'none'\n              })\n            } finally {\n              this.loading = false\n            }\n          }\n        }\n      })\n    },\n    \n    // 创建月度批次\n    async createMonthlyBatch() {\n      this.closeSmartModal()\n      \n      try {\n        const now = new Date()\n        const year = now.getFullYear()\n        const month = now.getMonth() + 1\n        const lastDay = new Date(year, month, 0).getDate()\n        \n        const batchData = {\n          name: `${year}年${month}月月度表彰`,\n          type: 'monthly',\n          meetingDate: `${year}-${month.toString().padStart(2, '0')}-${lastDay}`,\n          description: `${year}年${month}月月度表彰批次`,\n          isPublished: false\n        }\n        \n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'createBatch',\n            data: batchData\n          }\n        })\n        \n        if (res.result.code === 0) {\n          uni.showToast({\n            title: '月度批次创建成功',\n            icon: 'success'\n          })\n          \n          await this.loadBatchList()\n          await this.loadStats()\n        } else {\n          throw new Error(res.result.message || '创建失败')\n        }\n      } catch (error) {\n        uni.showToast({\n          title: error.message || '创建失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 创建季度批次\n    async createQuarterlyBatch() {\n      this.closeSmartModal()\n      \n      try {\n        const now = new Date()\n        const year = now.getFullYear()\n        const month = now.getMonth() + 1\n        \n        // 计算当前季度\n        const quarter = Math.ceil(month / 3)\n        const quarterStartMonth = (quarter - 1) * 3 + 1\n        const quarterEndMonth = quarter * 3\n        \n        // 季度结束日期\n        const lastDay = new Date(year, quarterEndMonth, 0).getDate()\n        const meetingDate = `${year}-${quarterEndMonth.toString().padStart(2, '0')}-${lastDay}`\n        \n        const batchData = {\n          name: `${year}年第${quarter}季度表彰`,\n          type: 'quarterly',\n          meetingDate: meetingDate,\n          description: `${year}年第${quarter}季度表彰批次（${quarterStartMonth}-${quarterEndMonth}月）`,\n          isPublished: false\n        }\n        \n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'createBatch',\n            data: batchData\n          }\n        })\n        \n        if (res.result.code === 0) {\n          uni.showToast({\n            title: '季度批次创建成功',\n            icon: 'success'\n          })\n          \n          await this.loadBatchList()\n          await this.loadStats()\n        } else {\n          throw new Error(res.result.message || '创建失败')\n        }\n      } catch (error) {\n        uni.showToast({\n          title: error.message || '创建失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 🏆 创建年度批次\n    async createYearlyBatch() {\n      this.closeSmartModal()\n      \n      try {\n        const now = new Date()\n        const year = now.getFullYear()\n        \n        // 年度表彰通常在12月31日举行\n        const meetingDate = `${year}-12-31`\n        \n        const batchData = {\n          name: `${year}年度表彰大会`,\n          type: 'yearly',\n          meetingDate: meetingDate,\n          description: `${year}年度表彰大会批次，表彰全年优秀表现`,\n          isPublished: false\n        }\n        \n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'createBatch',\n            data: batchData\n          }\n        })\n        \n        if (res.result.code === 0) {\n          uni.showToast({\n            title: '年度批次创建成功',\n            icon: 'success'\n          })\n          \n          await this.loadBatchList()\n          await this.loadStats()\n        } else {\n          throw new Error(res.result.message || '创建失败')\n        }\n      } catch (error) {\n        uni.showToast({\n          title: error.message || '创建失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 批次操作方法\n    editBatch(batch) {\n      this.editingBatch = batch\n      this.createForm = {\n        name: batch.name,\n        type: batch.type,\n        meetingDate: batch.meetingDate,\n        description: batch.description || '',\n        isPublished: batch.isPublished || false\n      }\n      this.showCreateModal = true\n    },\n    \n    async publishBatch(batch) {\n      try {\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'publishBatch',\n            data: { batchId: batch._id }\n          }\n        })\n        \n        if (res.result.code === 0) {\n          uni.showToast({\n            title: '发布成功',\n            icon: 'success'\n          })\n          \n          await this.loadBatchList()\n          await this.loadStats()\n        } else {\n          throw new Error(res.result.message || '发布失败')\n        }\n      } catch (error) {\n        uni.showToast({\n          title: error.message || '发布失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    deleteBatch(batch) {\n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除批次\"${batch.name}\"吗？删除后不可恢复。`,\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const result = await uniCloud.callFunction({\n                name: 'honor-admin',\n                data: {\n                  action: 'deleteBatch',\n                  data: { batchId: batch._id }\n                }\n              })\n              \n              if (result.result.code === 0) {\n                uni.showToast({\n                  title: '删除成功',\n                  icon: 'success'\n                })\n                \n                await this.loadBatchList()\n                await this.loadStats()\n              } else {\n                throw new Error(result.result.message || '删除失败')\n              }\n            } catch (error) {\n              uni.showToast({\n                title: error.message || '删除失败',\n                icon: 'none'\n              })\n            }\n          }\n        }\n      })\n    },\n    \n    openBatchDetail(batch) {\n      // 打开批次详情弹窗\n      this.selectedBatchDetail = batch\n      this.showBatchDetailModal = true\n      this.loadBatchDetailData(batch)\n    },\n    \n    // 关闭批次详情弹窗\n    closeBatchDetail() {\n      this.showBatchDetailModal = false\n      this.selectedBatchDetail = null\n    },\n    \n    // 刷新批次详情\n    async refreshBatchDetail() {\n      if (this.selectedBatchDetail) {\n        await this.loadBatchDetailData(this.selectedBatchDetail)\n        uni.showToast({\n          title: '刷新成功',\n          icon: 'success',\n          duration: 1500\n        })\n      }\n    },\n    \n    // 加载批次详情数据\n    async loadBatchDetailData(batch) {\n      // 显示加载状态\n      this.loading = true\n      this.loadingText = '加载批次详情...'\n      \n      try {\n        // 尝试从云函数获取详细数据\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getBatchDetail',\n            data: {\n              batchId: batch._id\n            }\n          }\n        })\n        \n        if (res.result.code === 0) {\n          this.batchDetailData = res.result.data || {\n            honorCount: 0,\n            featuredCount: 0,\n            departmentStats: [],\n            honorTypeStats: [],\n            recentHonors: []\n          }\n        } else {\n          // 显示错误信息\n          uni.showToast({\n            title: res.result.message || '获取批次详情失败',\n            icon: 'none'\n          })\n          \n          // 重置为空数据\n          this.batchDetailData = {\n            honorCount: 0,\n            featuredCount: 0,\n            departmentStats: [],\n            honorTypeStats: [],\n            recentHonors: []\n          }\n        }\n      } catch (error) {\n        // 显示错误信息\n        uni.showToast({\n          title: '网络错误，请重试',\n          icon: 'none'\n        })\n        \n        // 重置为空数据\n        this.batchDetailData = {\n          honorCount: 0,\n          featuredCount: 0,\n          departmentStats: [],\n          honorTypeStats: [],\n          recentHonors: []\n        }\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    openBatchPublish() {\n      uni.showToast({\n        title: '批量发布功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 工具方法\n    \n    // 获取批次类型文本\n    getBatchTypeText(type) {\n      const typeMap = {\n        'weekly': '周表彰',\n        'monthly': '月表彰',\n        'quarterly': '季度表彰',\n        'yearly': '年度表彰',\n        'special': '特别表彰'\n      }\n      return typeMap[type] || type\n    },\n    \n    // 格式化日期\n    formatDate(date) {\n      if (!date) return ''\n      \n      try {\n        // 新数据直接是 YYYY-MM-DD 格式\n        if (typeof date === 'string' && /^\\d{4}-\\d{2}-\\d{2}$/.test(date)) {\n          return date\n        }\n        \n        // 兼容老数据的 ISO 格式 (历史遗留)\n        if (typeof date === 'string' && date.includes('T')) {\n          return date.split('T')[0]\n        }\n        \n        // 其他格式尝试标准化\n        const d = new Date(date)\n        if (!isNaN(d.getTime())) {\n          const year = d.getFullYear()\n          const month = String(d.getMonth() + 1).padStart(2, '0')\n          const day = String(d.getDate()).padStart(2, '0')\n          return `${year}-${month}-${day}`\n        }\n        \n        return date.toString()\n      } catch (error) {\n        console.error('日期格式化错误:', error, date)\n        return date.toString()\n      }\n    },\n    \n    // 格式化相对时间\n    formatRelativeTime(time) {\n      if (!time) {\n        return '未知时间'\n      }\n      \n      try {\n        const now = new Date()\n        let date\n        \n        // 处理时区问题\n        if (typeof time === 'string') {\n          if (time.includes('T') && time.includes('Z')) {\n            // UTC时间，转换为本地时间\n            date = new Date(time)\n          } else if (time.includes('T')) {\n            // 包含T但不包含Z，可能是本地时间\n            date = new Date(time)\n          } else {\n            // YYYY-MM-DD格式，按本地时间处理\n            date = new Date(time + 'T00:00:00')\n          }\n        } else {\n          date = new Date(time)\n        }\n        \n        // 检查日期是否有效\n        if (isNaN(date.getTime())) {\n          return '时间格式错误'\n        }\n        \n        const diff = now - date\n        \n        // 如果时间差为负数，说明是未来时间\n        if (diff < 0) {\n          return this.formatDate(time)\n        }\n        \n        const minutes = Math.floor(diff / (1000 * 60))\n        const hours = Math.floor(diff / (1000 * 60 * 60))\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24))\n        \n        if (minutes < 1) {\n          return '刚刚'\n        } else if (minutes < 60) {\n          return `${minutes}分钟前`\n        } else if (hours < 24) {\n          return `${hours}小时前`\n        } else if (days < 7) {\n          return `${days}天前`\n        } else {\n          return this.formatDate(time)\n        }\n      } catch (error) {\n        console.error('时间格式化错误:', error, time)\n        return '时间格式错误'\n      }\n    },\n    \n    getTypeDisplayText(type) {\n      const typeMap = {\n        weekly: '周表彰',\n        monthly: '月表彰',\n        quarterly: '季度表彰',\n        yearly: '年度表彰',\n        special: '特别表彰'\n      }\n      return typeMap[type] || type\n    },\n    \n    getBatchTypeText(type) {\n      const typeMap = {\n        weekly: '周表彰',\n        monthly: '月表彰',\n        quarterly: '季度表彰',\n        yearly: '年度表彰',\n        special: '特别表彰'\n      }\n      return typeMap[type] || type\n    },\n    \n    // H5自定义选择器方法\n    selectType(value) {\n      this.createForm.type = value\n      this.showTypePicker = false\n    },\n    \n    // 发布状态切换\n    onPublishSwitchChange(e) {\n      this.createForm.isPublished = e.detail.value\n    },\n    \n    // 日期范围方法\n    getMinDateString() {\n      // 允许选择过去一年的日期\n      const date = new Date()\n      date.setFullYear(date.getFullYear() - 1)\n      return this.formatDateString(date)\n    },\n    \n    getMaxDateString() {\n      // 允许选择未来两年的日期\n      const date = new Date()\n      date.setFullYear(date.getFullYear() + 2)\n      return this.formatDateString(date)\n    },\n    \n    formatDateString(date) {\n      const year = date.getFullYear()\n      const month = String(date.getMonth() + 1).padStart(2, '0')\n      const day = String(date.getDate()).padStart(2, '0')\n      return `${year}-${month}-${day}`\n    },\n    \n    // 优化后的周次计算方法（与云函数保持一致）\n    calculateWeeksInMonth(year, month) {\n      const firstDay = new Date(year, month - 1, 1)\n      const lastDay = new Date(year, month, 0).getDate()\n      \n      const weeks = []\n      let currentDate = 1\n      let weekNumber = 1\n      \n      // 按自然周计算，每周7天\n      while (currentDate <= lastDay && weekNumber <= 4) {\n        let weekStart = currentDate\n        let weekEnd = Math.min(currentDate + 6, lastDay)\n        \n        // 确保第一周至少有3天\n        if (weekNumber === 1 && weekEnd - weekStart + 1 < 3) {\n          weekEnd = Math.min(weekStart + 6, lastDay)\n        }\n        \n        // 只要有至少3天就算一周\n        if (weekEnd - weekStart + 1 >= 3) {\n          weeks.push({\n            weekNumber: weekNumber,\n            start: weekStart,\n            end: weekEnd,\n            days: weekEnd - weekStart + 1\n          })\n        }\n        \n        currentDate = weekEnd + 1\n        weekNumber++\n      }\n      \n      return weeks\n    },\n    \n    goBack() {\n      uni.navigateBack()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.batch-manager-container {\n  min-height: 100vh;\n  background: linear-gradient(145deg, #f8faff 0%, #e9f0f8 100%);\n  position: relative;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  background: linear-gradient(180deg, #3a86ff 0%, #2563eb 100%);\n  overflow: hidden;\n  \n  // 装饰性背景图案\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    right: -20%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);\n    transform: rotate(-15deg);\n    pointer-events: none;\n  }\n  \n  .header-content {\n    display: flex;\n    align-items: center;\n    padding: 20rpx 40rpx;\n    padding-top: calc(var(--status-bar-height, 0px) + 20rpx);\n    position: relative;\n    z-index: 2;\n    \n    .back-btn {\n      width: 60rpx;\n      height: 60rpx;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.2);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 30rpx;\n    }\n    \n    .title-area {\n      flex: 1;\n      \n      .title {\n        display: block;\n        font-size: 36rpx;\n        font-weight: 600;\n        color: #FFFFFF;\n        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n        line-height: 1.2;\n      }\n      \n      .subtitle {\n        display: block;\n        font-size: 24rpx;\n        color: rgba(255, 255, 255, 0.8);\n        margin-top: 4rpx;\n      }\n    }\n    \n    .header-actions {\n      .add-btn {\n        width: 60rpx;\n        height: 60rpx;\n        border-radius: 50%;\n        background: rgba(255, 255, 255, 0.2);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n    }\n  }\n}\n\n.content-scroll {\n  position: fixed;\n  top: calc(var(--status-bar-height, 0px) + 140rpx);\n  left: 0;\n  right: 0;\n  bottom: 0;\n}\n\n.quick-actions {\n  display: flex;\n  gap: 24rpx;\n  padding: 40rpx;\n  \n  .action-item {\n    flex: 1;\n    background: linear-gradient(135deg, #3a86ff 0%, #5c9cff 100%);\n    border-radius: 20rpx;\n    padding: 32rpx;\n    display: flex;\n    align-items: center;\n    \n    &:nth-child(2) {\n      background: linear-gradient(135deg, #10b981 0%, #34d399 100%);\n    }\n    \n    .action-icon {\n      width: 60rpx;\n      height: 60rpx;\n      border-radius: 16rpx;\n      background: rgba(255, 255, 255, 0.2);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 24rpx;\n      \n      &.publish {\n        background: rgba(255, 255, 255, 0.2);\n      }\n    }\n    \n    .action-content {\n      flex: 1;\n      \n      .action-title {\n        display: block;\n        font-size: 28rpx;\n        font-weight: 600;\n        color: #FFFFFF;\n        line-height: 1.2;\n      }\n      \n      .action-desc {\n        display: block;\n        font-size: 22rpx;\n        color: rgba(255, 255, 255, 0.8);\n        margin-top: 8rpx;\n      }\n    }\n  }\n}\n\n.batch-stats {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 1rpx;\n  background: #e9ecef;\n  margin: 0 40rpx 40rpx;\n  border-radius: 20rpx;\n  overflow: hidden;\n  \n  .stat-item {\n    background: #FFFFFF;\n    padding: 32rpx 20rpx;\n    text-align: center;\n    \n    .stat-number {\n      display: block;\n      font-size: 48rpx;\n      font-weight: 700;\n      color: #3a86ff;\n      line-height: 1;\n    }\n    \n    .stat-label {\n      display: block;\n      font-size: 22rpx;\n      color: #8a94a6;\n      margin-top: 8rpx;\n    }\n  }\n}\n\n.filter-section {\n  margin: 0 40rpx 32rpx;\n  \n  .filter-tabs {\n    display: flex;\n    background: #FFFFFF;\n    border-radius: 16rpx;\n    padding: 8rpx;\n    margin-bottom: 24rpx;\n    \n    .filter-tab {\n      flex: 1;\n      text-align: center;\n      padding: 16rpx 0;\n      font-size: 26rpx;\n      color: #8a94a6;\n      border-radius: 12rpx;\n      transition: all 0.3s ease;\n      \n      &.active {\n        background: #3a86ff;\n        color: #FFFFFF;\n        font-weight: 500;\n      }\n    }\n  }\n}\n\n.batch-list {\n  padding: 0 40rpx 40rpx;\n  \n  .batch-item {\n    background: #FFFFFF;\n    border-radius: 20rpx;\n    padding: 32rpx;\n    margin-bottom: 24rpx;\n    border: 1px solid #f0f0f0;\n    \n    .batch-header {\n      display: flex;\n      align-items: flex-start;\n      justify-content: space-between;\n      margin-bottom: 20rpx;\n      \n      .batch-info {\n        flex: 1;\n        \n        .batch-name {\n          font-size: 32rpx;\n          font-weight: 600;\n          color: #1a1a1a;\n          line-height: 1.3;\n        }\n        \n        .batch-meta {\n          display: flex;\n          align-items: center;\n          margin-top: 8rpx;\n          gap: 16rpx;\n          \n          .batch-date {\n            font-size: 24rpx;\n            color: #8a94a6;\n          }\n          \n          .batch-status {\n            font-size: 22rpx;\n            padding: 4rpx 12rpx;\n            border-radius: 8rpx;\n            \n            &.published {\n              background: rgba(16, 185, 129, 0.1);\n              color: #10b981;\n            }\n            \n            &.draft {\n              background: rgba(138, 148, 166, 0.1);\n              color: #8a94a6;\n            }\n          }\n        }\n      }\n      \n      .batch-actions {\n        display: flex;\n        gap: 12rpx;\n        \n        .action-btn {\n          width: 48rpx;\n          height: 48rpx;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          \n          &.edit {\n            background: rgba(58, 134, 255, 0.1);\n          }\n          \n          &.publish {\n            background: rgba(16, 185, 129, 0.1);\n          }\n          \n          &.delete {\n            background: rgba(239, 68, 68, 0.1);\n          }\n        }\n      }\n    }\n    \n    .batch-details {\n      .batch-desc {\n        font-size: 26rpx;\n        color: #6b7280;\n        line-height: 1.5;\n        margin-bottom: 16rpx;\n      }\n      \n      .batch-stats-row {\n        display: flex;\n        gap: 32rpx;\n        \n        .batch-stat {\n          display: flex;\n          align-items: center;\n          gap: 8rpx;\n          \n          .stat-text {\n            font-size: 24rpx;\n            color: #8a94a6;\n          }\n        }\n      }\n    }\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 120rpx 40rpx;\n  \n  .empty-text {\n    display: block;\n    font-size: 28rpx;\n    color: #c4c4c4;\n    margin: 32rpx 0;\n  }\n  \n  .empty-btn {\n    background: #3a86ff;\n    color: #FFFFFF;\n    border: none;\n    border-radius: 16rpx;\n    padding: 24rpx 48rpx;\n    font-size: 28rpx;\n  }\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n  opacity: 0;\n  animation: overlayFadeIn 0.25s ease-out forwards;\n  \n  .modal-content {\n    width: 100%;\n    max-width: 600rpx;\n    background: #FFFFFF;\n    border-radius: 24rpx !important;\n    overflow: auto;\n    transform: scale(0.95);\n    opacity: 0;\n    animation: modalEnter 0.25s cubic-bezier(0.4, 0, 0.2, 1) 0.05s forwards;\n    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);\n    \n    &.smart-modal {\n      max-width: 700rpx;\n    }\n    \n    &.detail-modal {\n      max-width: 750rpx;\n      max-height: 85vh;\n    }\n    \n    &.create-modal {\n      /* 创建/编辑弹窗特殊样式 */\n      border-radius: 24rpx !important;\n      background: #FFFFFF !important;\n      box-sizing: border-box;\n      -webkit-border-radius: 24rpx;\n      -moz-border-radius: 24rpx;\n    }\n    \n    .modal-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 40rpx;\n      border-bottom: 1px solid #f0f0f0;\n      \n      .modal-title {\n        font-size: 36rpx;\n        font-weight: 600;\n        color: #1a1a1a;\n      }\n      \n      .header-actions {\n        display: flex;\n        gap: 16rpx;\n        align-items: center;\n      }\n      \n      .refresh-btn {\n        width: 48rpx;\n        height: 48rpx;\n        border-radius: 50%;\n        background: rgba(58, 134, 255, 0.1);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.3s ease;\n        \n        &:active {\n          transform: scale(0.95);\n          background: rgba(58, 134, 255, 0.2);\n        }\n      }\n      \n      .close-btn {\n        width: 48rpx;\n        height: 48rpx;\n        border-radius: 50%;\n        background: #f5f5f5;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n    }\n    \n    .modal-body {\n      padding: 40rpx;\n      max-height: 60vh;\n      overflow-y: auto;\n      \n      /* #ifdef H5 */\n      &.date-picker-body {\n        overflow-y: visible; /* 只在日期选择器弹窗中允许溢出 */\n      }\n      /* #endif */\n    }\n    \n    .modal-footer {\n      display: flex;\n      gap: 24rpx;\n      padding: 40rpx;\n      border-top: 1px solid #f0f0f0;\n      \n      .modal-btn {\n        flex: 1;\n        height: 80rpx;\n        border-radius: 16rpx;\n        font-size: 28rpx;\n        font-weight: 500;\n        border: none;\n        \n        &.cancel {\n          background: #f5f5f5;\n          color: #8a94a6;\n        }\n        \n        &.confirm {\n          background: #3a86ff;\n          color: #FFFFFF;\n          \n          &:disabled {\n            background: #c4c4c4;\n          }\n        }\n      }\n    }\n  }\n}\n\n/* 发布状态开关样式 */\n.publish-switch-wrapper {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12rpx 0;\n}\n\n.switch-info {\n  flex: 1;\n  margin-right: 20rpx;\n}\n\n.switch-desc {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.4;\n}\n\n.publish-switch {\n  transform: scale(0.8);\n}\n\n.form-section {\n  .form-item {\n    margin-bottom: 32rpx;\n    \n    .form-label {\n      display: block;\n      font-size: 28rpx;\n      font-weight: 500;\n      color: #1a1a1a;\n      margin-bottom: 16rpx;\n      \n      .required {\n        color: #ef4444;\n        margin-left: 4rpx;\n      }\n    }\n    \n    // 统一自定义选择器样式\n    .custom-picker {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 24rpx 32rpx;\n      background: #FFFFFF;\n      border: 2rpx solid #e5e7eb;\n      border-radius: 16rpx;\n      font-size: 28rpx;\n      color: #374151;\n      transition: all 0.3s ease;\n      \n      /* #ifdef H5 */\n      cursor: pointer;\n      -webkit-tap-highlight-color: transparent; /* 禁用H5点击高亮 */\n      -webkit-user-select: none; /* 禁用文本选择 */\n      user-select: none;\n      \n      &:hover {\n        border-color: #10b981;\n        background: rgba(16, 185, 129, 0.05);\n      }\n      /* #endif */\n      \n      &:active {\n        transform: scale(0.98);\n        border-color: #10b981;\n      }\n      \n      .picker-text {\n        flex: 1;\n        \n        &.placeholder {\n          color: #9ca3af;\n        }\n      }\n    }\n  }\n}\n\n.smart-options {\n  .smart-option {\n    display: flex;\n    align-items: center;\n    padding: 32rpx;\n    background: #f8f9fa;\n    border-radius: 20rpx;\n    margin-bottom: 24rpx;\n    border: 2px solid transparent;\n    transition: all 0.3s ease;\n    \n    &:active {\n      border-color: #3a86ff;\n      transform: scale(0.98);\n    }\n    \n    .option-icon {\n      width: 80rpx;\n      height: 80rpx;\n      border-radius: 20rpx;\n      background: rgba(58, 134, 255, 0.1);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 32rpx;\n      \n      &.monthly {\n        background: rgba(16, 185, 129, 0.1);\n      }\n      \n      &.quarterly {\n        background: rgba(245, 158, 11, 0.1);\n      }\n      \n      &.yearly {\n        background: rgba(225, 29, 72, 0.1);\n      }\n    }\n    \n    .option-content {\n      flex: 1;\n      \n      .option-title {\n        display: block;\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #1a1a1a;\n        line-height: 1.2;\n      }\n      \n      .option-desc {\n        display: block;\n        font-size: 24rpx;\n        color: #8a94a6;\n        margin-top: 8rpx;\n        line-height: 1.4;\n      }\n    }\n  }\n}\n\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  .loading-content {\n    background: #FFFFFF;\n    border-radius: 20rpx;\n    padding: 60rpx;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    \n    .loading-spin {\n      animation: spin 1s linear infinite;\n    }\n    \n    .loading-text {\n      font-size: 28rpx;\n      color: #8a94a6;\n      margin-top: 24rpx;\n    }\n  }\n}\n\n.bottom-safe-area {\n  height: env(safe-area-inset-bottom);\n}\n\n// 原小程序选择器样式已移除，现在统一使用自定义选择器\n\n// H5 uni-datetime-picker 分场景优化\n/* #ifdef H5 */\n// 默认情况：保持日历原本的底部弹出效果\n// 不对全局的 uni-datetime-picker 做任何修改\n\n// 只在创建弹窗中应用特殊的日期选择器样式\n.modal-content.create-modal .date-picker-body {\n  ::v-deep .uni-datetime-picker--x {\n    position: fixed !important;\n    z-index: 1001 !important;\n  }\n  \n  ::v-deep .uni-datetime-picker__container {\n    position: fixed !important;\n    z-index: 1001 !important;\n  }\n  \n  ::v-deep .uni-popper {\n    z-index: 1001 !important;\n  }\n  \n  ::v-deep .uni-popper__content {\n    z-index: 1001 !important;\n    max-height: none !important;\n  }\n}\n\n// 确保模态框的基础层级\n.modal-content {\n  position: relative;\n  z-index: 500;\n}\n/* #endif */\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n@keyframes overlayFadeIn {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n@keyframes modalEnter {\n  0% {\n    opacity: 0;\n    transform: scale(0.95);\n    border-radius: 24rpx;\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n    border-radius: 24rpx;\n  }\n}\n\n// 统一类型选择器弹窗样式\n.type-picker-modal {\n  width: 90%;\n  max-width: 600rpx;\n  max-height: 60vh;\n  \n  .type-options {\n          .type-option {\n        padding: 32rpx 24rpx;\n        border-radius: 16rpx;\n        margin-bottom: 16rpx;\n        border: 2rpx solid #f0f0f0;\n        transition: all 0.3s ease;\n        background: #FFFFFF;\n        \n        /* #ifdef H5 */\n        cursor: pointer;\n        -webkit-tap-highlight-color: transparent; /* 禁用H5点击高亮 */\n        -webkit-user-select: none; /* 禁用文本选择 */\n        user-select: none;\n        \n        &:hover {\n          border-color: #10b981;\n          background: rgba(16, 185, 129, 0.05);\n          transform: translateY(-2rpx);\n          box-shadow: 0 8rpx 24rpx rgba(16, 185, 129, 0.1);\n        }\n        /* #endif */\n        \n        &:active {\n          transform: scale(0.98);\n        }\n      \n      &.active {\n        border-color: #10b981;\n        background: rgba(16, 185, 129, 0.1);\n        box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.15);\n      }\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n      \n      .option-content {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        \n        .option-text {\n          font-size: 30rpx;\n          font-weight: 500;\n          color: #1a1a1a;\n        }\n        \n        .option-check {\n          width: 32rpx;\n          height: 32rpx;\n          border-radius: 50%;\n          background: rgba(16, 185, 129, 0.2);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n      }\n    }\n  }\n}\n\n\n\n// 批次详情弹窗样式\n.detail-section {\n  margin-bottom: 40rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n  \n  .section-title {\n    display: flex;\n    align-items: center;\n    gap: 16rpx;\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #1a1a1a;\n    margin-bottom: 24rpx;\n  }\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 24rpx;\n  \n  .info-item {\n    .info-label {\n      display: block;\n      font-size: 24rpx;\n      color: #8a94a6;\n      margin-bottom: 8rpx;\n    }\n    \n    .info-value {\n      display: block;\n      font-size: 28rpx;\n      font-weight: 500;\n      color: #1a1a1a;\n    }\n    \n    .status-tag {\n      display: inline-block;\n      padding: 8rpx 16rpx;\n      border-radius: 8rpx;\n      font-size: 24rpx;\n      font-weight: 500;\n      \n      &.published {\n        background: rgba(16, 185, 129, 0.1);\n        color: #10b981;\n      }\n      \n      &.draft {\n        background: rgba(245, 158, 11, 0.1);\n        color: #f59e0b;\n      }\n    }\n  }\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 24rpx;\n  \n  .stat-card {\n    background: linear-gradient(135deg, #3a86ff 0%, #5b9cff 100%);\n    border-radius: 16rpx;\n    padding: 32rpx;\n    text-align: center;\n    \n    &.featured {\n      background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);\n    }\n    \n    .stat-number {\n      font-size: 48rpx;\n      font-weight: 700;\n      color: #FFFFFF;\n      line-height: 1;\n    }\n    \n    .stat-label {\n      font-size: 24rpx;\n      color: rgba(255, 255, 255, 0.8);\n      margin-top: 8rpx;\n    }\n  }\n}\n\n.department-list {\n  .department-item {\n    display: flex;\n    align-items: center;\n    gap: 24rpx;\n    margin-bottom: 24rpx;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n    \n    .dept-info {\n      width: 120rpx;\n      flex-shrink: 0;\n      \n      .dept-name {\n        display: block;\n        font-size: 28rpx;\n        font-weight: 500;\n        color: #1a1a1a;\n        line-height: 1.2;\n      }\n      \n      .dept-count {\n        display: block;\n        font-size: 24rpx;\n        color: #8a94a6;\n        margin-top: 4rpx;\n      }\n    }\n    \n    .dept-progress {\n      flex: 1;\n      height: 16rpx;\n      background: #f0f0f0;\n      border-radius: 8rpx;\n      overflow: hidden;\n      \n      .progress-bar {\n        height: 100%;\n        background: linear-gradient(135deg, #3a86ff 0%, #5b9cff 100%);\n        border-radius: 8rpx;\n        transition: width 0.3s ease;\n      }\n    }\n    \n    .dept-percentage {\n      width: 60rpx;\n      text-align: right;\n      font-size: 24rpx;\n      font-weight: 500;\n      color: #3a86ff;\n    }\n  }\n}\n\n.honor-type-list {\n  .honor-type-item {\n    display: flex;\n    align-items: center;\n    gap: 24rpx;\n    padding: 20rpx;\n    background: #f8f9fa;\n    border-radius: 12rpx;\n    margin-bottom: 16rpx;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n    \n    .type-dot {\n      width: 24rpx;\n      height: 24rpx;\n      border-radius: 50%;\n      flex-shrink: 0;\n    }\n    \n    .type-name {\n      flex: 1;\n      font-size: 28rpx;\n      font-weight: 500;\n      color: #1a1a1a;\n    }\n    \n    .type-count {\n      font-size: 24rpx;\n      font-weight: 600;\n      color: #3a86ff;\n    }\n  }\n}\n\n.recent-honors {\n  .honor-item {\n    display: flex;\n    align-items: center;\n    gap: 24rpx;\n    padding: 24rpx 20rpx;\n    background: #f8f9fa;\n    border-radius: 12rpx;\n    margin-bottom: 16rpx;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n    \n    .honor-left {\n      flex-shrink: 0;\n      \n      .honor-user {\n        display: block;\n        font-size: 28rpx;\n        font-weight: 500;\n        color: #1a1a1a;\n        line-height: 1.2;\n      }\n      \n      .honor-dept {\n        display: block;\n        font-size: 24rpx;\n        color: #8a94a6;\n        margin-top: 4rpx;\n      }\n    }\n    \n    .honor-center {\n      flex: 1;\n      \n      .honor-type {\n        display: block;\n        font-size: 26rpx;\n        font-weight: 500;\n        color: #3a86ff;\n        line-height: 1.2;\n      }\n      \n      .honor-time {\n        display: block;\n        font-size: 22rpx;\n        color: #8a94a6;\n        margin-top: 4rpx;\n      }\n    }\n    \n    .honor-right {\n      flex-shrink: 0;\n      \n      .featured-badge {\n        width: 32rpx;\n        height: 32rpx;\n        background: rgba(245, 158, 11, 0.1);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n    }\n  }\n}\n\n// 空状态样式\n.empty-data {\n  text-align: center;\n  padding: 60rpx 40rpx;\n  \n  .empty-text {\n    display: block;\n    font-size: 26rpx;\n    color: #c4c4c4;\n    margin-top: 16rpx;\n  }\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batch-manager.vue?vue&type=style&index=0&id=86285dce&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batch-manager.vue?vue&type=style&index=0&id=86285dce&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558438620\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/honor_pkg/avatar-picker/avatar-picker"],{

/***/ 747:
/*!***************************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue ***!
  \***************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _avatar_picker_vue_vue_type_template_id_7979a2f4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./avatar-picker.vue?vue&type=template&id=7979a2f4&scoped=true& */ 748);
/* harmony import */ var _avatar_picker_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./avatar-picker.vue?vue&type=script&lang=js& */ 750);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _avatar_picker_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _avatar_picker_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _avatar_picker_vue_vue_type_style_index_0_id_7979a2f4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./avatar-picker.vue?vue&type=style&index=0&id=7979a2f4&lang=scss&scoped=true& */ 752);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _avatar_picker_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _avatar_picker_vue_vue_type_template_id_7979a2f4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _avatar_picker_vue_vue_type_template_id_7979a2f4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "7979a2f4",
  null,
  false,
  _avatar_picker_vue_vue_type_template_id_7979a2f4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/honor_pkg/avatar-picker/avatar-picker.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 748:
/*!**********************************************************************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?vue&type=template&id=7979a2f4&scoped=true& ***!
  \**********************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_template_id_7979a2f4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./avatar-picker.vue?vue&type=template&id=7979a2f4&scoped=true& */ 749);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_template_id_7979a2f4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_template_id_7979a2f4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_template_id_7979a2f4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_template_id_7979a2f4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 749:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?vue&type=template&id=7979a2f4&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 490))
    },
    uniPopupDialog: function () {
      return Promise.all(/*! import() | uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue */ 729))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 483))
    },
    uniEasyinput: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput */ "uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue */ 551))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.historyAvatars.length
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      return _vm.$refs.deleteConfirm.close()
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 750:
/*!****************************************************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./avatar-picker.vue?vue&type=script&lang=js& */ 751);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 751:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uniCloud, uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _uploadUtils = _interopRequireDefault(__webpack_require__(/*! @/utils/upload-utils.js */ 258));
var _methods;
var PEmptyState = function PEmptyState() {
  __webpack_require__.e(/*! require.ensure | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then((function () {
    return resolve(__webpack_require__(/*! @/components/p-empty-state/p-empty-state.vue */ 483));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
// 配置常量
var CONFIG = {
  maxSize: 10 * 1024 * 1024,
  // 10MB
  timeout: 30000,
  // 30s
  validTypes: ['jpg', 'jpeg', 'png', 'webp', 'JPG', 'JPEG', 'PNG', 'WEBP'],
  compressQuality: 0.8
};
var _default = {
  name: 'AvatarPicker',
  components: {
    PEmptyState: PEmptyState
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: '头像'
    },
    showActions: {
      type: Boolean,
      default: true
    },
    autoUpload: {
      type: Boolean,
      default: true
    },
    customPath: {
      type: String,
      default: ''
    },
    sizeLimit: {
      type: Number,
      default: CONFIG.maxSize
    },
    timeout: {
      type: Number,
      default: CONFIG.timeout
    }
  },
  data: function data() {
    return {
      uploading: false,
      uploadProgress: 0,
      uploadError: false,
      avatarUrl: '',
      uploadTimer: null,
      currentFile: null,
      uploadRes: null,
      // 添加上传结果缓存
      historyAvatars: [],
      // 历史头像列表
      batchList: [],
      // 批量上传列表
      recentUserName: '',
      // 添加最近输入的用户名
      defaultAvatar: '/static/user/default-avatar.png'
    };
  },
  computed: {
    displayAvatar: function displayAvatar() {
      return this.avatarUrl || this.defaultAvatar;
    }
  },
  watch: {
    value: {
      immediate: true,
      handler: function handler(newVal) {
        this.avatarUrl = newVal;
      }
    }
  },
  created: function created() {
    this.loadHistoryAvatars();
  },
  methods: (_methods = {
    // 加载历史头像
    loadHistoryAvatars: function loadHistoryAvatars() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var _yield$uniCloud$callF, result;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                _context.next = 3;
                return uniCloud.callFunction({
                  name: 'history-avatar-get',
                  data: {}
                });
              case 3:
                _yield$uniCloud$callF = _context.sent;
                result = _yield$uniCloud$callF.result;
                if (result.code === 0) {
                  _this.historyAvatars = result.data;
                } else {
                  console.error('获取历史头像失败:', result.message);
                }
                _context.next = 11;
                break;
              case 8:
                _context.prev = 8;
                _context.t0 = _context["catch"](0);
                console.error('获取历史头像失败:', _context.t0);
              case 11:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 8]]);
      }))();
    },
    // 上传成功后刷新历史列表
    onUploadSuccess: function onUploadSuccess(result) {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.next = 2;
                return _this2.loadHistoryAvatars();
              case 2:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    // 错误处理
    handleError: function handleError(error) {
      var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'upload';
      var messages = {
        upload: '上传失败',
        compress: '压缩失败',
        select: '选择失败',
        size: '文件过大',
        format: '格式不支持'
      };
      this.uploadError = type === 'upload';
      uni.showToast({
        title: error.message || messages[type],
        icon: 'none'
      });
      this.$emit('error', {
        type: type,
        error: error
      });
    },
    // 选择图片
    handleSelectImage: function handleSelectImage() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var _res$tempFilePaths, options, res, file, filePath;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                if (!_this3.uploading) {
                  _context3.next = 2;
                  break;
                }
                return _context3.abrupt("return");
              case 2:
                _context3.prev = 2;
                options = {
                  count: 1,
                  sizeType: ['compressed'],
                  // 使用压缩
                  sourceType: ['album'] // 只使用相册选择
                };
                _context3.next = 6;
                return uni.chooseImage(options);
              case 6:
                res = _context3.sent;
                if (!((_res$tempFilePaths = res.tempFilePaths) !== null && _res$tempFilePaths !== void 0 && _res$tempFilePaths[0])) {
                  _context3.next = 17;
                  break;
                }
                file = res.tempFiles[0];
                filePath = res.tempFilePaths[0]; // 验证文件大小
                if (!(file.size > _this3.sizeLimit)) {
                  _context3.next = 13;
                  break;
                }
                _this3.handleError({
                  message: "\u6587\u4EF6\u5927\u5C0F".concat(_this3.formatFileSize(file.size), "\u8D85\u8FC7\u9650\u5236").concat(_this3.formatFileSize(_this3.sizeLimit))
                }, 'size');
                return _context3.abrupt("return");
              case 13:
                _this3.currentFile = {
                  path: filePath,
                  size: file.size
                };
                if (!_this3.autoUpload) {
                  _context3.next = 17;
                  break;
                }
                _context3.next = 17;
                return _this3.uploadAvatar();
              case 17:
                _context3.next = 23;
                break;
              case 19:
                _context3.prev = 19;
                _context3.t0 = _context3["catch"](2);
                console.error('选择图片失败:', _context3.t0);
                _this3.handleError(_context3.t0, 'select');
              case 23:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[2, 19]]);
      }))();
    },
    // 重试上传
    retryUpload: function retryUpload() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _this4.uploadError = false;
                _context4.next = 3;
                return _this4.uploadAvatar();
              case 3:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    // 预览头像
    previewAvatar: function previewAvatar() {
      if (!this.avatarUrl) return;
      uni.previewImage({
        urls: [this.avatarUrl]
      });
    },
    // 确认删除
    confirmDelete: function confirmDelete() {
      this.$refs.deleteConfirm.open();
    },
    // 删除头像
    handleDelete: function handleDelete() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var fileID, _yield$uniCloud$callF2, result;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                if (!_this5.avatarUrl) {
                  _context5.next = 17;
                  break;
                }
                fileID = _this5.avatarUrl; // 如果是http开头的临时链接，尝试从uploadRes中获取cloudPath
                if (fileID.startsWith('http') && _this5.uploadRes && _this5.uploadRes.cloudPath) {
                  fileID = _this5.uploadRes.cloudPath;
                }
                _context5.prev = 4;
                _context5.next = 7;
                return uniCloud.callFunction({
                  name: 'delete-file',
                  data: {
                    fileList: [fileID]
                  }
                });
              case 7:
                _yield$uniCloud$callF2 = _context5.sent;
                result = _yield$uniCloud$callF2.result;
                if (!(result.code !== 0)) {
                  _context5.next = 11;
                  break;
                }
                throw new Error(result.message || '云端文件删除失败');
              case 11:
                _context5.next = 17;
                break;
              case 13:
                _context5.prev = 13;
                _context5.t0 = _context5["catch"](4);
                console.error('云端文件删除失败:', _context5.t0);
                throw new Error(_context5.t0.message || '云端文件删除失败');
              case 17:
                // 清除本地状态
                _this5.avatarUrl = '';
                _this5.currentFile = null;
                _this5.uploadRes = null; // 清除上传结果缓存
                _this5.$emit('input', '');
                _this5.$emit('delete');
                _this5.$refs.deleteConfirm.close();
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                _context5.next = 31;
                break;
              case 26:
                _context5.prev = 26;
                _context5.t1 = _context5["catch"](0);
                console.error('删除失败:', _context5.t1);
                _this5.handleError(_context5.t1, 'delete');
                // 关闭确认弹窗
                _this5.$refs.deleteConfirm.close();
              case 31:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 26], [4, 13]]);
      }))();
    },
    // 验证图片格式
    validateImageFormat: function validateImageFormat(filePath) {
      var _filePath$split$pop;
      if (!filePath) return false;

      // 提取文件扩展名并转小写
      var ext = (_filePath$split$pop = filePath.split('.').pop()) === null || _filePath$split$pop === void 0 ? void 0 : _filePath$split$pop.toLowerCase();
      if (!ext) return false;

      // 转小写后比较
      return CONFIG.validTypes.map(function (type) {
        return type.toLowerCase();
      }).includes(ext);
    },
    // 格式化文件大小
    formatFileSize: function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      var k = 1024;
      var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
      var i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    // 格式化日期
    formatDate: function formatDate(timestamp) {
      var date = new Date(timestamp);
      var year = date.getFullYear();
      var month = String(date.getMonth() + 1).padStart(2, '0');
      var day = String(date.getDate()).padStart(2, '0');
      var hours = String(date.getHours()).padStart(2, '0');
      var minutes = String(date.getMinutes()).padStart(2, '0');
      return "".concat(year, "-").concat(month, "-").concat(day, " ").concat(hours, ":").concat(minutes);
    },
    // 打开历史头像选择弹窗
    openHistorySelect: function openHistorySelect() {
      this.$refs.historyPopup.open();
    },
    // 关闭历史头像选择弹窗
    closeHistorySelect: function closeHistorySelect() {
      this.$refs.historyPopup.close();
    },
    // 选择历史头像
    selectHistoryAvatar: function selectHistoryAvatar(item) {
      this.avatarUrl = item.url;
      // 修正事件触发，传递正确的数据结构
      this.$emit('input', item.url);
      this.$emit('change', {
        url: item.url,
        cloudPath: item.fileID
      });
      this.closeHistorySelect();
      uni.showToast({
        title: '已选择头像',
        icon: 'success'
      });
    },
    // 修改上传成功的处理
    uploadAvatar: function uploadAvatar() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var _fileList$, uploadResult, _yield$uniCloud$getTe, fileList, tempFileURL;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                if (_this6.currentFile) {
                  _context6.next = 2;
                  break;
                }
                return _context6.abrupt("return");
              case 2:
                _context6.prev = 2;
                _this6.uploading = true;
                _this6.uploadError = false;

                // 上传文件到云存储
                _context6.next = 7;
                return uniCloud.uploadFile({
                  filePath: _this6.currentFile.path,
                  cloudPath: "avatars/".concat(Date.now(), "-").concat(Math.random().toString(36).slice(2), ".").concat(_this6.currentFile.path.split('.').pop())
                });
              case 7:
                uploadResult = _context6.sent;
                if (uploadResult.fileID) {
                  _context6.next = 10;
                  break;
                }
                throw new Error('上传失败');
              case 10:
                _context6.next = 12;
                return uniCloud.getTempFileURL({
                  fileList: [uploadResult.fileID]
                });
              case 12:
                _yield$uniCloud$getTe = _context6.sent;
                fileList = _yield$uniCloud$getTe.fileList;
                if (fileList !== null && fileList !== void 0 && (_fileList$ = fileList[0]) !== null && _fileList$ !== void 0 && _fileList$.tempFileURL) {
                  _context6.next = 16;
                  break;
                }
                throw new Error('获取临时链接失败');
              case 16:
                tempFileURL = fileList[0].tempFileURL; // 保存头像信息
                _context6.next = 19;
                return _this6.saveHistoryAvatar(tempFileURL, uploadResult.fileID);
              case 19:
                // 更新当前头像
                _this6.avatarUrl = tempFileURL;
                _this6.$emit('input', tempFileURL);
                _this6.$emit('change', tempFileURL);
                uni.showToast({
                  title: '上传成功',
                  icon: 'success'
                });
                _context6.next = 30;
                break;
              case 25:
                _context6.prev = 25;
                _context6.t0 = _context6["catch"](2);
                console.error('上传头像失败:', _context6.t0);
                _this6.uploadError = true;
                uni.showToast({
                  title: _context6.t0.message || '上传失败',
                  icon: 'none'
                });
              case 30:
                _context6.prev = 30;
                _this6.uploading = false;
                _this6.currentFile = null;
                return _context6.finish(30);
              case 34:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[2, 25, 30, 34]]);
      }))();
    },
    // 打开批量上传弹窗
    openBatchUpload: function openBatchUpload() {
      this.batchList = [{
        userName: '',
        url: '',
        saving: false
      }];
      this.$refs.batchUploadPopup.open();
    },
    // 关闭批量上传弹窗
    closeBatchUpload: function closeBatchUpload() {
      this.$refs.batchUploadPopup.close();
    },
    // 添加一项
    addBatchItem: function addBatchItem() {
      this.batchList.push({
        userName: '',
        url: '',
        saving: false
      });
    },
    // 移除一项
    removeBatchItem: function removeBatchItem(index) {
      this.batchList.splice(index, 1);
      if (this.batchList.length === 0) {
        this.addBatchItem();
      }
    },
    // 选择图片
    selectImage: function selectImage(index) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var _res$tempFilePaths2, res, item;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _context7.prev = 0;
                _context7.next = 3;
                return uni.chooseImage({
                  count: 1,
                  sizeType: ['compressed'],
                  sourceType: ['album']
                });
              case 3:
                res = _context7.sent;
                if ((_res$tempFilePaths2 = res.tempFilePaths) !== null && _res$tempFilePaths2 !== void 0 && _res$tempFilePaths2[0]) {
                  item = _this7.batchList[index];
                  item.url = res.tempFilePaths[0];
                  item.file = res.tempFiles[0];
                }
                _context7.next = 10;
                break;
              case 7:
                _context7.prev = 7;
                _context7.t0 = _context7["catch"](0);
                console.error('选择图片失败:', _context7.t0);
              case 10:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[0, 7]]);
      }))();
    },
    // 保存头像
    saveAvatar: function saveAvatar(index) {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var item, uploadResult, _yield$uniCloud$callF3, result;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                item = _this8.batchList[index]; // 验证名字
                if (item.userName) {
                  _context8.next = 4;
                  break;
                }
                uni.showToast({
                  title: '请输入姓名',
                  icon: 'none',
                  duration: 2000
                });
                return _context8.abrupt("return");
              case 4:
                if (item.url) {
                  _context8.next = 7;
                  break;
                }
                uni.showToast({
                  title: '请选择头像',
                  icon: 'none',
                  duration: 2000
                });
                return _context8.abrupt("return");
              case 7:
                if (!item.saving) {
                  _context8.next = 9;
                  break;
                }
                return _context8.abrupt("return");
              case 9:
                item.saving = true;
                _context8.prev = 10;
                _context8.next = 13;
                return _uploadUtils.default.uploadAvatar(item.url);
              case 13:
                uploadResult = _context8.sent;
                _context8.next = 16;
                return uniCloud.callFunction({
                  name: 'history-avatar-save',
                  data: {
                    url: uploadResult.cloudPath,
                    userName: item.userName,
                    createTime: Date.now()
                  }
                });
              case 16:
                _yield$uniCloud$callF3 = _context8.sent;
                result = _yield$uniCloud$callF3.result;
                if (!(result.code === 0)) {
                  _context8.next = 26;
                  break;
                }
                uni.showToast({
                  title: result.message || '保存成功',
                  icon: 'success'
                });
                // 清空当前项
                item.userName = '';
                item.url = '';
                // 刷新历史列表
                _context8.next = 24;
                return _this8.loadHistoryAvatars();
              case 24:
                _context8.next = 27;
                break;
              case 26:
                throw new Error(result.message);
              case 27:
                _context8.next = 32;
                break;
              case 29:
                _context8.prev = 29;
                _context8.t0 = _context8["catch"](10);
                uni.showToast({
                  title: _context8.t0.message || '保存失败',
                  icon: 'none'
                });
              case 32:
                _context8.prev = 32;
                item.saving = false;
                return _context8.finish(32);
              case 35:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[10, 29, 32, 35]]);
      }))();
    },
    /**
     * 清除当前头像
     */
    clearAvatar: function clearAvatar() {
      this.avatarUrl = this.defaultAvatar;
      // 使用正确的事件名称
      this.$emit('update:modelValue', '');
      // 触发change事件通知父组件
      this.$emit('change', '');
    }
  }, (0, _defineProperty2.default)(_methods, "confirmDelete", function confirmDelete() {
    var _this9 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
      return _regenerator.default.wrap(function _callee10$(_context10) {
        while (1) {
          switch (_context10.prev = _context10.next) {
            case 0:
              uni.showModal({
                title: '删除确认',
                content: '确定要删除当前头像吗？删除后将恢复为默认头像。',
                success: function () {
                  var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9(res) {
                    return _regenerator.default.wrap(function _callee9$(_context9) {
                      while (1) {
                        switch (_context9.prev = _context9.next) {
                          case 0:
                            if (!res.confirm) {
                              _context9.next = 3;
                              break;
                            }
                            _context9.next = 3;
                            return _this9.deleteAvatar();
                          case 3:
                          case "end":
                            return _context9.stop();
                        }
                      }
                    }, _callee9);
                  }));
                  function success(_x) {
                    return _success.apply(this, arguments);
                  }
                  return success;
                }()
              });
            case 1:
            case "end":
              return _context10.stop();
          }
        }
      }, _callee10);
    }))();
  }), (0, _defineProperty2.default)(_methods, "deleteAvatar", function deleteAvatar() {
    var _this10 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
      var db, avatarCollection, record, avatarRecord;
      return _regenerator.default.wrap(function _callee11$(_context11) {
        while (1) {
          switch (_context11.prev = _context11.next) {
            case 0:
              _context11.prev = 0;
              uni.showLoading({
                title: '删除中...',
                mask: true
              });

              // 获取当前头像记录
              db = uniCloud.database();
              avatarCollection = db.collection('history-avatar');
              _context11.next = 6;
              return avatarCollection.where({
                url: _this10.avatarUrl
              }).get();
            case 6:
              record = _context11.sent;
              if (!(record.data && record.data.length > 0)) {
                _context11.next = 14;
                break;
              }
              avatarRecord = record.data[0]; // 删除云存储文件
              if (!avatarRecord.fileID) {
                _context11.next = 12;
                break;
              }
              _context11.next = 12;
              return uniCloud.deleteFile({
                fileList: [avatarRecord.fileID]
              });
            case 12:
              _context11.next = 14;
              return avatarCollection.doc(avatarRecord._id).remove();
            case 14:
              // 重置头像为默认头像
              _this10.avatarUrl = _this10.value || '/static/user/default-avatar.png'; // Use value prop as fallback
              _this10.$emit('input', '');

              // 刷新历史头像列表
              _context11.next = 18;
              return _this10.loadHistoryAvatars();
            case 18:
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
              _context11.next = 25;
              break;
            case 21:
              _context11.prev = 21;
              _context11.t0 = _context11["catch"](0);
              console.error('删除头像失败:', _context11.t0);
              uni.showToast({
                title: '删除失败',
                icon: 'error'
              });
            case 25:
              _context11.prev = 25;
              uni.hideLoading();
              return _context11.finish(25);
            case 28:
            case "end":
              return _context11.stop();
          }
        }
      }, _callee11, null, [[0, 21, 25, 28]]);
    }))();
  }), (0, _defineProperty2.default)(_methods, "confirmDeleteHistory", function confirmDeleteHistory(item) {
    var _this11 = this;
    uni.showModal({
      title: '删除确认',
      content: '确定要删除这个头像吗？此操作不可恢复。',
      success: function () {
        var _success2 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12(res) {
          return _regenerator.default.wrap(function _callee12$(_context12) {
            while (1) {
              switch (_context12.prev = _context12.next) {
                case 0:
                  if (!res.confirm) {
                    _context12.next = 3;
                    break;
                  }
                  _context12.next = 3;
                  return _this11.deleteHistoryAvatar(item);
                case 3:
                case "end":
                  return _context12.stop();
              }
            }
          }, _callee12);
        }));
        function success(_x2) {
          return _success2.apply(this, arguments);
        }
        return success;
      }()
    });
  }), (0, _defineProperty2.default)(_methods, "deleteHistoryAvatar", function deleteHistoryAvatar(item) {
    var _this12 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee13() {
      var _yield$uniCloud$callF4, result;
      return _regenerator.default.wrap(function _callee13$(_context13) {
        while (1) {
          switch (_context13.prev = _context13.next) {
            case 0:
              if (!(!item || !item._id)) {
                _context13.next = 3;
                break;
              }
              uni.showToast({
                title: '无效的记录',
                icon: 'none'
              });
              return _context13.abrupt("return");
            case 3:
              _context13.prev = 3;
              uni.showLoading({
                title: '删除中...',
                mask: true
              });

              // 调用云函数删除历史头像
              _context13.next = 7;
              return uniCloud.callFunction({
                name: 'history-avatar-delete',
                data: {
                  _id: item._id
                }
              });
            case 7:
              _yield$uniCloud$callF4 = _context13.sent;
              result = _yield$uniCloud$callF4.result;
              if (!(result.code === 0)) {
                _context13.next = 16;
                break;
              }
              // 如果删除的是当前选中的头像，重置为默认头像
              if (item.url === _this12.avatarUrl) {
                _this12.avatarUrl = _this12.value || '/static/user/default-avatar.png';
                _this12.$emit('input', '');
              }

              // 刷新历史头像列表
              _context13.next = 13;
              return _this12.loadHistoryAvatars();
            case 13:
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
              _context13.next = 17;
              break;
            case 16:
              throw new Error(result.msg || '删除失败');
            case 17:
              _context13.next = 23;
              break;
            case 19:
              _context13.prev = 19;
              _context13.t0 = _context13["catch"](3);
              console.error('删除历史头像失败:', _context13.t0);
              uni.showToast({
                title: _context13.t0.message || '删除失败',
                icon: 'none'
              });
            case 23:
              _context13.prev = 23;
              uni.hideLoading();
              return _context13.finish(23);
            case 26:
            case "end":
              return _context13.stop();
          }
        }
      }, _callee13, null, [[3, 19, 23, 26]]);
    }))();
  }), (0, _defineProperty2.default)(_methods, "saveHistoryAvatar", function saveHistoryAvatar(url, fileID) {
    var _this13 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee14() {
      var _yield$uniCloud$callF5, result;
      return _regenerator.default.wrap(function _callee14$(_context14) {
        while (1) {
          switch (_context14.prev = _context14.next) {
            case 0:
              _context14.prev = 0;
              _context14.next = 3;
              return uniCloud.callFunction({
                name: 'history-avatar-save',
                data: {
                  url: url,
                  fileID: fileID,
                  userName: _this13.userName || (uni.getStorageSync('uni-id-pages-userInfo') || {}).username || '未命名'
                }
              });
            case 3:
              _yield$uniCloud$callF5 = _context14.sent;
              result = _yield$uniCloud$callF5.result;
              if (!(result.code === 0)) {
                _context14.next = 10;
                break;
              }
              _context14.next = 8;
              return _this13.loadHistoryAvatars();
            case 8:
              _context14.next = 11;
              break;
            case 10:
              uni.showToast({
                title: result.msg,
                icon: 'none'
              });
            case 11:
              _context14.next = 17;
              break;
            case 13:
              _context14.prev = 13;
              _context14.t0 = _context14["catch"](0);
              console.error('保存历史头像失败:', _context14.t0);
              uni.showToast({
                title: '保存历史头像失败',
                icon: 'none'
              });
            case 17:
            case "end":
              return _context14.stop();
          }
        }
      }, _callee14, null, [[0, 13]]);
    }))();
  }), _methods)
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 752:
/*!*************************************************************************************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?vue&type=style&index=0&id=7979a2f4&lang=scss&scoped=true& ***!
  \*************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_style_index_0_id_7979a2f4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./avatar-picker.vue?vue&type=style&index=0&id=7979a2f4&lang=scss&scoped=true& */ 753);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_style_index_0_id_7979a2f4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_style_index_0_id_7979a2f4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_style_index_0_id_7979a2f4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_style_index_0_id_7979a2f4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_avatar_picker_vue_vue_type_style_index_0_id_7979a2f4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 753:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/honor_pkg/avatar-picker/avatar-picker.vue?vue&type=style&index=0&id=7979a2f4&lang=scss&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/honor_pkg/avatar-picker/avatar-picker.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages/honor_pkg/avatar-picker/avatar-picker-create-component',
    {
        'pages/honor_pkg/avatar-picker/avatar-picker-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(747))
        })
    },
    [['pages/honor_pkg/avatar-picker/avatar-picker-create-component']]
]);

(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["components/patrol/p-map"],{

/***/ 516:
/*!*******************************************!*\
  !*** D:/Xwzc/components/patrol/p-map.vue ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _p_map_vue_vue_type_template_id_0d9e2492___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./p-map.vue?vue&type=template&id=0d9e2492& */ 517);
/* harmony import */ var _p_map_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./p-map.vue?vue&type=script&lang=js& */ 519);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _p_map_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _p_map_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _p_map_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./p-map.vue?vue&type=style&index=0&lang=scss& */ 521);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _p_map_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _p_map_vue_vue_type_template_id_0d9e2492___WEBPACK_IMPORTED_MODULE_0__["render"],
  _p_map_vue_vue_type_template_id_0d9e2492___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _p_map_vue_vue_type_template_id_0d9e2492___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "components/patrol/p-map.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 517:
/*!**************************************************************************!*\
  !*** D:/Xwzc/components/patrol/p-map.vue?vue&type=template&id=0d9e2492& ***!
  \**************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_template_id_0d9e2492___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./p-map.vue?vue&type=template&id=0d9e2492& */ 518);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_template_id_0d9e2492___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_template_id_0d9e2492___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_template_id_0d9e2492___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_template_id_0d9e2492___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 518:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/components/patrol/p-map.vue?vue&type=template&id=0d9e2492& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 519:
/*!********************************************************************!*\
  !*** D:/Xwzc/components/patrol/p-map.vue?vue&type=script&lang=js& ***!
  \********************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./p-map.vue?vue&type=script&lang=js& */ 520);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 520:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/components/patrol/p-map.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/**
 * 地图组件
 * 封装微信小程序地图组件，提供更丰富的功能
 */
// 删除调试开关和调试日志函数
// const DEBUG = false;
// function debugLog(...args) {
//   if (DEBUG) {
//   }
// }
var _default2 = {
  name: 'p-map',
  data: function data() {
    return {
      // 地图相关
      map: null,
      mapCtx: null,
      // 地图数据 - 重命名避免与props冲突
      localMarkers: [],
      localCircles: [],
      localPolyline: [],
      // 显示控制
      showMap: false,
      showMarkers: true,
      showCircles: true,
      showRoute: true,
      // 缓存当前数据
      currentTask: null,
      currentPatrolPoints: null,
      currentRound: null,
      // 正在加载中
      loading: false,
      // 新增 - 缓存新数据结构的轮次详情
      rounds_detail: null
    };
  },
  props: {
    // 地图ID
    mapId: {
      type: String,
      default: 'patrolMap'
    },
    // 当前任务数据
    task: {
      type: Object,
      default: null
    },
    // 纬度（可选，指定中心点）
    latitude: {
      type: [String, Number],
      default: 30.0
    },
    // 经度（可选，指定中心点）
    longitude: {
      type: [String, Number],
      default: 120.0
    },
    // 缩放级别
    scale: {
      type: [String, Number],
      default: 16
    },
    // 显示定位按钮
    showLocationButton: {
      type: Boolean,
      default: false
    },
    // 显示指南针
    showCompass: {
      type: Boolean,
      default: true
    },
    // 允许旋转
    enableRotate: {
      type: Boolean,
      default: true
    },
    // 地图高度
    height: {
      type: String,
      default: '300px'
    },
    // 标记点数据 - 保留旧名称向后兼容
    markers: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    // 标记点数据 - 新名称
    markerData: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    // 路线数据 - 保留旧名称向后兼容
    polyline: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    // 路线数据 - 新名称
    polylineData: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    // 圆形区域数据 - 保留旧名称向后兼容
    circles: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    // 圆形区域数据 - 新名称
    circleData: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    // 是否显示地图
    show: {
      type: Boolean,
      default: true
    },
    // 是否实时刷新数据（用于追踪模式）
    realtime: {
      type: Boolean,
      default: false
    },
    // 当前轮次数据
    round: {
      type: Object,
      default: null
    },
    // 是否显示当前位置
    showLocation: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    centerLongitude: function centerLongitude() {
      return Number(this.longitude);
    },
    centerLatitude: function centerLatitude() {
      return Number(this.latitude);
    },
    formattedMarkers: function formattedMarkers() {
      // 优先使用markerData，其次使用markers prop，最后才使用本地数据
      var sourceData = this.markerData || this.markers || this.localMarkers;

      // 如果没有有效的标记点数据，返回空数组
      if (!sourceData || !Array.isArray(sourceData) || sourceData.length === 0) {
        return [];
      }

      // 确保所有marker的id都是数字类型，并保留callout属性
      return sourceData.map(function (marker, index) {
        // 创建marker的副本，避免修改原始数据
        var processedMarker = _objectSpread({}, marker);

        // 确保id为数字
        if (processedMarker.id === undefined || processedMarker.id === null || typeof processedMarker.id !== 'number') {
          // 如果id不存在或不是数字，则使用索引作为id
          processedMarker.id = index;
        } else if (typeof processedMarker.id === 'string') {
          // 如果id是字符串，尝试转换为数字
          var numId = Number(processedMarker.id);
          // 如果转换成功并且是有效数字，使用转换后的数字，否则使用索引
          processedMarker.id = !isNaN(numId) ? numId : index;
        }
        return processedMarker;
      });
    },
    formattedPolylines: function formattedPolylines() {
      // 优先使用polylineData，其次使用polyline prop，最后才使用本地数据
      var sourceData = this.polylineData || this.polyline || this.localPolyline;

      // 如果没有有效的路线数据，返回空数组
      if (!sourceData || !Array.isArray(sourceData) || sourceData.length === 0) {
        return [];
      }

      // 返回路线数据，不做格式处理
      return sourceData;
    },
    formattedCircles: function formattedCircles() {
      // 优先使用circleData，其次使用circles prop，最后才使用本地数据
      var sourceData = this.circleData || this.circles || this.localCircles;

      // 如果没有有效的圆形区域数据，返回空数组
      if (!sourceData || !Array.isArray(sourceData) || sourceData.length === 0) {
        return [];
      }

      // 返回圆形区域数据，不做格式处理
      return sourceData;
    }
  },
  methods: {
    // 初始化地图
    initMap: function initMap() {
      // 创建地图上下文
      this.mapCtx = uni.createMapContext('map', this);
    },
    // 安全地执行地图操作的辅助函数
    safeMapOperation: function safeMapOperation(operation) {
      if (!this.mapCtx) {
        console.warn('地图上下文不存在，无法执行操作');
        return false;
      }
      try {
        operation();
        return true;
      } catch (error) {
        console.error('执行地图操作时出错:', error);
        return false;
      }
    },
    // 处理任务变更
    handleTaskChange: function handleTaskChange(newTask) {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var markers, polyline, roundsData;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (newTask) {
                  _context.next = 5;
                  break;
                }
                // 如果任务为空，清空地图数据
                _this.localMarkers = [];
                _this.localCircles = [];
                _this.localPolyline = [];
                return _context.abrupt("return");
              case 5:
                _context.prev = 5;
                _this.loading = true;

                // 存储当前任务
                _this.currentTask = newTask;

                // 检查是否有点位数据
                if (!(newTask.points && Array.isArray(newTask.points) && newTask.points.length > 0)) {
                  _context.next = 14;
                  break;
                }
                // 直接使用传入的点位数据
                markers = _this.buildMarkers(newTask.points); // 更新markerData
                _this.markerData = markers;

                // 构建路线
                if (newTask.points.length > 1) {
                  polyline = _this.buildPolyline(newTask.points); // 更新polylineData
                  _this.polylineData = [polyline];
                }
                _context.next = 28;
                break;
              case 14:
                if (!(newTask.rounds_detail && newTask.rounds_detail.length > 0)) {
                  _context.next = 20;
                  break;
                }
                _this.rounds_detail = newTask.rounds_detail;

                // 处理点位数据
                _context.next = 18;
                return _this.processTaskMarkers();
              case 18:
                _context.next = 28;
                break;
              case 20:
                if (!(newTask.shift && newTask.shift.rounds)) {
                  _context.next = 28;
                  break;
                }
                _context.next = 23;
                return _this.getRoundsData(newTask);
              case 23:
                roundsData = _context.sent;
                if (!(roundsData && roundsData.points)) {
                  _context.next = 28;
                  break;
                }
                _this.currentPatrolPoints = roundsData.points;
                _context.next = 28;
                return _this.processTaskMarkers();
              case 28:
                _this.loading = false;
                _context.next = 35;
                break;
              case 31:
                _context.prev = 31;
                _context.t0 = _context["catch"](5);
                console.error('处理任务数据出错：', _context.t0);
                _this.loading = false;
              case 35:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[5, 31]]);
      }))();
    },
    // 处理任务点位标记
    processTaskMarkers: function processTaskMarkers() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var points, markers, circles, validCoordinates, i, point;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                points = []; // 使用新的数据结构
                if (_this2.rounds_detail) {
                  // 从rounds_detail中获取所有点位
                  _this2.rounds_detail.forEach(function (round) {
                    if (round.points && round.points.length) {
                      points = [].concat((0, _toConsumableArray2.default)(points), (0, _toConsumableArray2.default)(round.points));
                    }
                  });
                }
                // 兼容旧数据结构
                else if (_this2.currentPatrolPoints) {
                  points = _this2.currentPatrolPoints;
                }
                if (!(!points || points.length === 0)) {
                  _context2.next = 5;
                  break;
                }
                console.warn('无法找到任务点位数据');
                return _context2.abrupt("return");
              case 5:
                // 创建点位标记
                markers = [];
                circles = []; // 存储有效的坐标点用于路线绘制
                validCoordinates = []; // 处理每个点位
                i = 0;
              case 9:
                if (!(i < points.length)) {
                  _context2.next = 16;
                  break;
                }
                point = points[i]; // 跳过无效点位
                if (!(!point.longitude || !point.latitude)) {
                  _context2.next = 13;
                  break;
                }
                return _context2.abrupt("continue", 13);
              case 13:
                i++;
                _context2.next = 9;
                break;
              case 16:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    // 点击标记
    onMarkerTap: function onMarkerTap(e) {
      // 确保事件数据正确
      var markerId = e.markerId !== undefined ? e.markerId : e.detail && e.detail.markerId;
      if (markerId !== undefined) {
        this.$emit('marker-tap', markerId);
      }
    },
    // 点击气泡
    onCalloutTap: function onCalloutTap(e) {
      // 确保事件数据正确
      var markerId = e.markerId !== undefined ? e.markerId : e.detail && e.detail.markerId;
      if (markerId !== undefined) {
        this.$emit('callout-tap', markerId);
      }
    },
    // 地图区域变化
    onRegionChange: function onRegionChange(e) {
      if (e.type === 'end' && e.causedBy === 'drag') {
        this.$emit('region-change', {
          longitude: e.longitude,
          latitude: e.latitude
        });
      }
    },
    // 获取地图上下文
    getMapContext: function getMapContext() {
      if (!this.mapCtx) {
        this.mapCtx = uni.createMapContext('map', this);
      }
      return this.mapCtx;
    },
    // 移动到指定位置
    moveToLocation: function moveToLocation(longitude, latitude) {
      if (!this.safeMapOperation(function () {})) return;
      var mapContext = this.getMapContext();
      if (!mapContext) return;
      if (longitude && latitude) {
        mapContext.moveToLocation({
          longitude: Number(longitude),
          latitude: Number(latitude)
        });
      } else {
        this.moveToCurrentLocation();
      }
    },
    // 移动到当前位置
    moveToCurrentLocation: function moveToCurrentLocation() {
      if (!this.safeMapOperation(function () {})) return;
      var mapContext = this.getMapContext();
      if (mapContext) {
        mapContext.moveToLocation();
      }
    },
    // 包含所有点标记
    includePoints: function includePoints(points, padding) {
      if (!this.safeMapOperation(function () {})) return;
      var mapContext = this.getMapContext();
      if (mapContext && points && points.length > 0) {
        mapContext.includePoints({
          points: points,
          padding: padding || [30, 30, 30, 30]
        });
      }
    },
    // 获取当前位置
    getCurrentLocation: function getCurrentLocation() {
      return new Promise(function (resolve, reject) {
        uni.getLocation({
          type: 'gcj02',
          isHighAccuracy: true,
          highAccuracyExpireTime: 3000,
          success: function success(res) {
            resolve(res);
          },
          fail: function fail(err) {
            reject(err);
          }
        });
      });
    },
    // 计算两点之间的距离（米）
    calculateDistance: function calculateDistance(point1, point2) {
      try {
        var EARTH_RADIUS = 6378137.0; // 地球半径
        var lat1 = point1.latitude * Math.PI / 180.0;
        var lng1 = point1.longitude * Math.PI / 180.0;
        var lat2 = point2.latitude * Math.PI / 180.0;
        var lng2 = point2.longitude * Math.PI / 180.0;
        var a = lat1 - lat2;
        var b = lng1 - lng2;
        var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
        return s * EARTH_RADIUS;
      } catch (e) {
        console.error('距离计算出错:', e);
        return 0;
      }
    }
  },
  watch: {
    show: function show(val) {
      var _this3 = this;
      this.showMap = val;
      if (val) {
        this.$nextTick(function () {
          _this3.initMap();
        });
      }
    },
    // 监听任务数据变化
    task: {
      handler: function handler(newTask) {
        // 如果组件已销毁或正在销毁中，不执行handleTaskChange
        if (!this._isDestroyed && !this._isBeingDestroyed) {
          this.handleTaskChange(newTask);
        }
      },
      deep: true,
      immediate: true
    },
    // 监听当前轮次变化
    round: {
      handler: function handler(newRound) {
        // 如果组件已销毁或正在销毁中，不执行更新
        if (this._isDestroyed || this._isBeingDestroyed) return;
        this.currentRound = newRound;
        // 如果有任务数据且地图已初始化，更新地图显示
        if (this.currentTask && this.mapCtx) {
          this.processTaskMarkers();
        }
      },
      deep: true
    },
    // 外部传入的标记数据
    markerData: {
      handler: function handler(newMarkers) {
        // 检查组件是否已销毁
        if (this._isDestroyed || this._isBeingDestroyed) return;
        if (newMarkers && newMarkers.length > 0) {
          this.localMarkers = newMarkers;
        }
      },
      deep: true
    },
    // 外部传入的圆形区域数据
    circleData: {
      handler: function handler(newCircles) {
        // 检查组件是否已销毁
        if (this._isDestroyed || this._isBeingDestroyed) return;
        if (newCircles && newCircles.length > 0) {
          this.localCircles = newCircles;
        }
      },
      deep: true
    },
    // 外部传入的路线数据
    polylineData: {
      handler: function handler(newPolyline) {
        // 检查组件是否已销毁
        if (this._isDestroyed || this._isBeingDestroyed) return;
        if (newPolyline && newPolyline.length > 0) {
          // 修复bug：将polyline赋值给data中定义的polyline，而不是polylines
          this.localPolyline = newPolyline;
        }
      },
      deep: true
    }
  },
  // 组件生命周期钩子
  created: function created() {
    // 初始化
    this.showMap = this.show;
  },
  mounted: function mounted() {
    var _this4 = this;
    if (this.showMap) {
      this.$nextTick(function () {
        _this4.initMap();
      });
    }
  },
  // 销毁时清理资源
  beforeDestroy: function beforeDestroy() {
    // 清理地图实例和上下文
    this.mapCtx = null;
    this.map = null;

    // 清空数据，避免持续的异步操作
    this.localMarkers = [];
    this.localCircles = [];
    this.localPolyline = [];
    this.currentTask = null;
  }
};
exports.default = _default2;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 521:
/*!*****************************************************************************!*\
  !*** D:/Xwzc/components/patrol/p-map.vue?vue&type=style&index=0&lang=scss& ***!
  \*****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./p-map.vue?vue&type=style&index=0&lang=scss& */ 522);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_map_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 522:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/components/patrol/p-map.vue?vue&type=style&index=0&lang=scss& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/patrol/p-map.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/patrol/p-map-create-component',
    {
        'components/patrol/p-map-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(516))
        })
    },
    [['components/patrol/p-map-create-component']]
]);

{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/ucenter_pkg/complete-task.vue?ecc9", "webpack:///D:/Xwzc/pages/ucenter_pkg/complete-task.vue?9512", "webpack:///D:/Xwzc/pages/ucenter_pkg/complete-task.vue?cc6b", "webpack:///D:/Xwzc/pages/ucenter_pkg/complete-task.vue?180a", "uni-app:///pages/ucenter_pkg/complete-task.vue", "webpack:///D:/Xwzc/pages/ucenter_pkg/complete-task.vue?ad62", "webpack:///D:/Xwzc/pages/ucenter_pkg/complete-task.vue?5934"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "taskId", "taskInfo", "isLoading", "isSubmitting", "isPageVisible", "formData", "completionDescription", "completionEvidence", "rules", "required", "errorMessage", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "onLoad", "methods", "goBack", "uni", "loadTaskInfo", "uniCloud", "name", "action", "id", "res", "userInfo", "showErrorAndBack", "title", "content", "showCancel", "success", "chooseImage", "count", "sizeType", "sourceType", "fail", "icon", "uploadImages", "i", "tempFile<PERSON>ath", "mask", "now", "year", "month", "day", "dateFolder", "fileExt", "uniqueFileName", "cloudPath", "filePath", "cloudPathAsRealPath", "result", "removeImage", "confirmColor", "fileID", "fileList", "duration", "previewImage", "current", "urls", "formatTime", "submitCompletion", "setTimeout", "selector", "confirmText", "cancelText", "confirm", "confirmRes", "type", "delta", "silentRefreshTask", "console", "handleVisibilityChange", "shouldRefreshOnCrossDeviceUpdate", "handleRecordDeleted", "todoBadgeManager", "url", "onShow", "onHide", "created", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACqC;;;AAGjG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,8RAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAAumB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoJ3nB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;QACAF;UACAE,QACA;YAAAC;YAAAC;UAAA,GACA;YAAAC;YAAAD;UAAA,GACA;YAAAE;YAAAF;UAAA;QAEA;MACA;IACA;EACA;EAEAG;IACA;IACA;MACA;IACA;MACA;IACA;EACA;EAEAC;IACAC;MACAC;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEAC;kBACAC;kBACApB;oBACAqB;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACA;;gBAEA;gBACAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAGA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACAR;QACAS;QACAC;QACAC;QACAC;UACAZ;QACA;MACA;IACA;IAEAa;MAAA;MACA;MACAb;QACAc;QACAC;QACAC;QACAJ;UACA;QACA;QACAK;UACA;UACAjB;YACAS;YACAS;UACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAC,iCAEA;gBACArB;kBACAS;kBACAa;gBACA;;gBAEA;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC,wDAEA;gBACAC;gBACAC;gBAEAC;gBAAA;gBAAA,OACA5B;kBACA6B;kBACAD;kBACAE;gBACA;cAAA;gBAJAC;gBAMA;kBACA;gBACA;cAAA;gBA7BAb;gBAAA;gBAAA;cAAA;gBAgCApB;kBACAS;kBACAS;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlB;kBACAS;kBACAS;gBACA;cAAA;gBAAA;gBAEAlB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAkC;MAAA;MACAlC;QACAS;QACAC;QACAyB;QACAvB;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAN;sBAAA;sBAAA;oBAAA;oBACA8B,oDAEA;oBACApC;sBACAS;sBACAa;oBACA;oBAAA;oBAAA,MAIAc;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OACAlC;sBACAC;sBACApB;wBACAsD;sBACA;oBACA;kBAAA;oBAGA;oBACA;oBAEArC;sBACAS;sBACAS;sBACAoB;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEA;;oBAEA;oBACA;oBAEAtC;sBACAS;sBACAS;sBACAoB;oBACA;kBAAA;oBAAA;oBAEAtC;oBAAA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEAuC;MACAvC;QACAwC;QACAC;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACAC;kBACA5C;oBACA6C;oBACAP;kBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKA;kBACAtC;oBACAS;oBACAC;oBACAoC;oBACAC;oBACAZ;oBACAvB;oBACAK;sBAAA;wBAAA+B;sBAAA;oBAAA;kBACA;gBACA;cAAA;gBAVAC;gBAAA,IAYAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAAA;gBAAA,OAGA/C;kBACAC;kBACApB;oBACAqB;oBACAC;oBACAf;oBACAC;kBACA;gBACA;cAAA;gBARAe;gBAAA,MAUAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAN;kBACAS;kBACAS;kBACAoB;gBACA;;gBAEA;gBACAtC;kBACAK;kBACA6C;gBACA;gBACAlD;kBACAK;kBACAD;gBACA;gBACAJ;kBACAK;gBACA;;gBAEA;gBACAuC;kBACA5C;oBACAmD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAGA;gBACAnD;kBACAS;kBACAC;kBACAC;kBACAmC;kBACAX;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAlD;kBACAC;kBACApB;oBACAqB;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACA;;gBAEA;gBACAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACAP;kBACAS;kBACAC;kBACAC;kBACAC;oBACAZ;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBACA;kBACAA;oBACAS;oBACAC;oBACAC;oBACAC;sBACAZ;oBACA;kBACA;gBACA;kBACAA;oBACAS;oBACAC;oBACAC;oBACAC;sBACAZ;oBACA;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAqD;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAC;MACA;IAAA,CACA;IAEA;AACA;AACA;IACAC;MACA;MACA;QACAF;QACA;MACA;;MAEA;MACA;MACA;QAAA;MAAA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;MAEAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAG;MACA;MACAxD;MACA;MACA;;MAEA;MACAA;QACAS;QACAC;QACAC;QACAmC;QACAlC;UACA;UACAZ;UACAA;UACAA;;UAEA;UACA;UACA;YACAyD;UACA;;UAEA;UACAzD;YACAmD;YACAlC;cACA;cACAjB;gBACA0D;cACA;YACA;UACA;QACA;MACA;IACA;EAGA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IAAA;IACA;IACA7D;MACA;QACA;QACA;QACA;UACAqD;UACA;UACA;QACA;MACA;IACA;;IAEA;IACA;EACA;EAEAS;IACA;IACA9D;;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACppBA;AAAA;AAAA;AAAA;AAAi5B,CAAgB,k5BAAG,EAAC,C;;;;;;;;;;;ACAr6B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ucenter_pkg/complete-task.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/ucenter_pkg/complete-task.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./complete-task.vue?vue&type=template&id=6ca1bbf8&scoped=true&\"\nvar renderjs\nimport script from \"./complete-task.vue?vue&type=script&lang=js&\"\nexport * from \"./complete-task.vue?vue&type=script&lang=js&\"\nimport style0 from \"./complete-task.vue?vue&type=style&index=0&id=6ca1bbf8&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6ca1bbf8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ucenter_pkg/complete-task.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./complete-task.vue?vue&type=template&id=6ca1bbf8&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.taskInfo ? _vm.formatTime(_vm.taskInfo.assignedTime) : null\n  var g0 = _vm.formData.completionDescription.length\n  var g1 = _vm.formData.completionEvidence.length\n  var g2 = _vm.formData.completionEvidence.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./complete-task.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./complete-task.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 任务信息卡片 -->\n    <view class=\"task-card\" v-if=\"taskInfo\">\n      <view class=\"task-header\">\n        <view class=\"task-avatar\">\n          <uni-icons type=\"list\" size=\"20\" color=\"#ffffff\"></uni-icons>\n        </view>\n        <view class=\"task-main\">\n          <view class=\"task-title\">{{taskInfo.name}}</view>\n          <view class=\"task-project\">{{taskInfo.project}}</view>\n        </view>\n        <view class=\"task-status\">\n          <view class=\"status-badge assigned\">\n            <uni-icons type=\"loop\" size=\"12\" color=\"#ffffff\"></uni-icons>\n            <text>待完成</text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"task-body\">\n        <view class=\"task-description\">\n          <text class=\"desc-title\">问题描述</text>\n          <text class=\"desc-content\">{{taskInfo.description}}</text>\n        </view>\n        \n        <!-- 状态标签区 -->\n        <view class=\"task-status-tags\" v-if=\"taskInfo.rejectReason\">\n          <view class=\"status-tag reject-tag\">\n            <uni-icons type=\"loop\" size=\"12\" color=\"#ffffff\"></uni-icons>\n            <text>任务已退回</text>\n          </view>\n        </view>\n        \n        <view class=\"task-meta\">\n          <view class=\"meta-item\">\n            <uni-icons type=\"calendar\" size=\"14\" color=\"#999999\"></uni-icons>\n            <text class=\"meta-text\">指派时间：{{formatTime(taskInfo.assignedTime)}}</text>\n          </view>\n          <!-- 内联显示厂长指派说明 -->\n          <view class=\"meta-item\" v-if=\"taskInfo.assignReason && taskInfo.assignReason !== '无'\">\n            <uni-icons type=\"chat\" size=\"14\"></uni-icons>\n            <text class=\"meta-text\">厂长说明：<text class=\"assign-reason-inline\">{{taskInfo.assignReason}}</text></text>\n          </view>\n          <!-- 退回原因 -->\n          <view class=\"meta-item\" v-if=\"taskInfo.rejectReason\">\n            <uni-icons type=\"loop\" size=\"14\" color=\"#999999\"></uni-icons>\n            <text class=\"meta-text\">退回原因：<text class=\"reject-reason-inline\">{{taskInfo.rejectReason}}</text></text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 完成表单 -->\n    <view class=\"form-card\">\n      <view class=\"card-header\">\n        <uni-icons type=\"compose\" size=\"18\" color=\"#007aff\"></uni-icons>\n        <text class=\"card-title\">完成情况</text>\n      </view>\n      \n      <uni-forms ref=\"form\" :model=\"formData\" :rules=\"rules\" label-width=\"0\">\n        <!-- 完成描述 -->\n        <view class=\"form-item\">\n          <view class=\"form-label required\">完成描述</view>\n          <uni-forms-item name=\"completionDescription\">\n            <uni-easyinput \n              v-model=\"formData.completionDescription\"\n              type=\"textarea\"\n              placeholder=\"请详细描述解决方案、完成情况、注意事项等...\"\n              :maxlength=\"500\"\n              :auto-height=\"true\"\n              :min-height=\"120\"\n              class=\"custom-textarea\">\n            </uni-easyinput>\n          </uni-forms-item>\n          <view class=\"char-count\">{{formData.completionDescription.length}}/500</view>\n        </view>\n\n        <!-- 完成凭证 -->\n        <view class=\"form-item\">\n          <view class=\"form-label\">完成凭证</view>\n          <view class=\"form-tip\">\n            <uni-icons type=\"info\" size=\"14\" color=\"#ff9800\"></uni-icons>\n            <text>请上传整改前后对比图、完成证明等相关图片（选填）</text>\n          </view>\n          \n          <!-- 图片上传区域 -->\n          <view class=\"upload-area\">\n            <view class=\"image-grid\">\n              <view \n                v-for=\"(image, index) in formData.completionEvidence\" \n                :key=\"index\"\n                class=\"image-item\">\n                <image :src=\"image\" mode=\"aspectFill\" class=\"uploaded-image\" \n                       @click=\"previewImage(index)\"></image>\n                <view class=\"image-delete\" @click=\"removeImage(index)\">\n                  <uni-icons type=\"close\" size=\"16\" color=\"#ffffff\"></uni-icons>\n                </view>\n              </view>\n              \n              <!-- 添加图片按钮 -->\n              <view \n                v-if=\"formData.completionEvidence.length < 6\"\n                class=\"add-image-btn\" \n                @click=\"chooseImage\">\n                <view class=\"add-content\">\n                  <uni-icons type=\"plus\" size=\"32\" color=\"#999999\"></uni-icons>\n                  <text class=\"add-text\">添加图片</text>\n                </view>\n              </view>\n            </view>\n            <view class=\"upload-info\">\n              <text class=\"upload-count\">{{formData.completionEvidence.length}}/6</text>\n              <text class=\"upload-tip\">支持jpg、png格式，单张不超过5MB</text>\n            </view>\n          </view>\n        </view>\n      </uni-forms>\n    </view>\n\n    <!-- 提交按钮 -->\n    <view class=\"submit-section\">\n      <button \n        class=\"submit-btn\" \n        :class=\"{ 'submitting': isSubmitting }\"\n        @click=\"submitCompletion\" \n        :disabled=\"isSubmitting\">\n        <view class=\"btn-content\">\n          <text>{{isSubmitting ? '提交中...' : '提交完成'}}</text>\n        </view>\n      </button>\n      <view class=\"submit-tip\">\n        <uni-icons type=\"info-filled\" size=\"12\" color=\"#999\"></uni-icons>\n        <text>提交后将等待厂长确认，请确保信息准确无误</text>\n      </view>\n    </view>\n\n    <!-- 加载状态 -->\n    <view v-if=\"isLoading\" class=\"loading-overlay\">\n      <view class=\"loading-content\">\n        <uni-load-more status=\"loading\"></uni-load-more>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      taskId: '',\n      taskInfo: null,\n      isLoading: true,\n      isSubmitting: false,\n      // 页面状态\n      isPageVisible: true,\n      formData: {\n        completionDescription: '',\n        completionEvidence: []\n      },\n      rules: {\n        completionDescription: {\n          rules: [\n            { required: true, errorMessage: '请输入完成描述' },\n            { minLength: 2, errorMessage: '完成描述至少2个字符' },\n            { maxLength: 500, errorMessage: '完成描述不能超过500字符' }\n          ]\n        }\n      }\n    };\n  },\n  \n  onLoad(options) {\n    this.taskId = options.id;\n    if (this.taskId) {\n      this.loadTaskInfo();\n    } else {\n      this.showErrorAndBack('参数错误');\n    }\n  },\n  \n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    \n    async loadTaskInfo() {\n      this.isLoading = true;\n      try {\n        const res = await uniCloud.callFunction({\n          name: 'feedback-workflow',\n          data: {\n            action: 'get_detail',\n            id: this.taskId\n          }\n        });\n        \n        if (res.result && res.result.code === 0) {\n          this.taskInfo = res.result.data;\n          \n          // 验证是否为当前用户的任务\n          const userInfo = uniCloud.getCurrentUserInfo();\n          if (this.taskInfo.responsibleUserId !== userInfo.uid) {\n            this.showErrorAndBack('这不是您的任务');\n            return;\n          }\n          \n          // 验证任务状态\n          if (this.taskInfo.workflowStatus !== 'assigned_to_responsible') {\n            this.showErrorAndBack('任务状态不正确，无法提交完成');\n            return;\n          }\n        } else {\n          // 检查是否是记录已删除的错误\n          if (res.result?.code === 404 && res.result?.errorType === 'RECORD_DELETED') {\n            this.handleRecordDeleted();\n            return;\n          }\n          throw new Error(res.result?.message || '获取任务信息失败');\n        }\n      } catch (error) {\n        // 检查是否是网络错误导致的记录不存在\n        if (error.message && error.message.includes('记录不存在')) {\n          this.handleRecordDeleted();\n          return;\n        }\n        // 加载任务信息失败\n        this.showErrorAndBack(error.message || '加载任务失败');\n      } finally {\n        this.isLoading = false;\n      }\n    },\n    \n    showErrorAndBack(message) {\n      uni.showModal({\n        title: '提示',\n        content: message,\n        showCancel: false,\n        success: () => {\n          uni.navigateBack();\n        }\n      });\n    },\n    \n    chooseImage() {\n      const maxCount = 6 - this.formData.completionEvidence.length;\n      uni.chooseImage({\n        count: maxCount,\n        sizeType: ['compressed'],\n        sourceType: ['camera', 'album'],\n        success: (res) => {\n          this.uploadImages(res.tempFilePaths);\n        },\n        fail: (error) => {\n          // 选择图片失败\n          uni.showToast({\n            title: '选择图片失败',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    \n    async uploadImages(tempFilePaths) {\n      if (!tempFilePaths || tempFilePaths.length === 0) return;\n      \n      try {\n        for (let i = 0; i < tempFilePaths.length; i++) {\n          const tempFilePath = tempFilePaths[i];\n          \n          // 显示当前上传进度\n          uni.showLoading({\n            title: `上传中 (${i + 1}/${tempFilePaths.length})`,\n            mask: true\n          });\n          \n          // 获取当前日期，创建年月日格式的目录结构\n          const now = new Date();\n          const year = now.getFullYear();\n          const month = String(now.getMonth() + 1).padStart(2, '0');\n          const day = String(now.getDate()).padStart(2, '0');\n          const dateFolder = `${year}${month}${day}`;\n          \n          // 生成唯一文件名\n          const fileExt = tempFilePath.includes('.') ? tempFilePath.substring(tempFilePath.lastIndexOf('.')) : '.jpg';\n          const uniqueFileName = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}${fileExt}`;\n          \n          const cloudPath = `feedback-evidence/${dateFolder}/${uniqueFileName}`;\n          const result = await uniCloud.uploadFile({\n            filePath: tempFilePath,\n            cloudPath: cloudPath,\n            cloudPathAsRealPath: true\n          });\n          \n          if (result.fileID) {\n            this.formData.completionEvidence.push(result.fileID);\n          }\n        }\n        \n        uni.showToast({\n          title: '上传成功',\n          icon: 'success'\n        });\n      } catch (error) {\n        uni.showToast({\n          title: '上传失败，请重试',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n    \n    removeImage(index) {\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这张图片吗？',\n        confirmColor: '#f44336',\n        success: async (res) => {\n          if (res.confirm) {\n            const fileID = this.formData.completionEvidence[index];\n            \n            // 显示删除中的提示\n            uni.showLoading({\n              title: '删除中...',\n              mask: true\n            });\n            \n            try {\n              // 如果是云存储文件，调用云函数删除\n              if (fileID && typeof fileID === 'string' && (fileID.startsWith('cloud://') || fileID.startsWith('https://'))) {\n                await uniCloud.callFunction({\n                  name: 'delete-file',\n                  data: {\n                    fileList: [fileID]\n                  }\n                });\n              }\n              \n              // 从数组中移除\n              this.formData.completionEvidence.splice(index, 1);\n              \n              uni.showToast({\n                title: '删除成功',\n                icon: 'success',\n                duration: 1000\n              });\n            } catch (error) {\n              // 删除云存储图片失败\n              \n              // 即使云存储删除失败，也从本地移除图片引用\n              this.formData.completionEvidence.splice(index, 1);\n              \n              uni.showToast({\n                title: '已从列表移除',\n                icon: 'success',\n                duration: 1000\n              });\n            } finally {\n              uni.hideLoading();\n            }\n          }\n        }\n      });\n    },\n    \n    previewImage(index) {\n      uni.previewImage({\n        current: index,\n        urls: this.formData.completionEvidence\n      });\n    },\n    \n    formatTime(timestamp) {\n      if (!timestamp) return '-';\n      const date = new Date(timestamp);\n      const year = date.getFullYear();\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\n      const day = date.getDate().toString().padStart(2, '0');\n      const hour = date.getHours().toString().padStart(2, '0');\n      const minute = date.getMinutes().toString().padStart(2, '0');\n      return `${year}/${month}/${day} ${hour}:${minute}`;\n    },\n    \n    async submitCompletion() {\n      if (this.isSubmitting) return;\n      \n      // 表单验证\n      try {\n        await this.$refs.form.validate();\n      } catch (error) {\n        // 表单验证失败\n        // 验证失败时，滚动到第一个错误字段\n        setTimeout(() => {\n          uni.pageScrollTo({\n            selector: '.uni-forms-item-error',\n            duration: 300\n          });\n        }, 100);\n        return;\n      }\n      \n      // 确认提交对话框\n      const confirmRes = await new Promise((resolve) => {\n        uni.showModal({\n          title: '确认提交',\n          content: '确定要提交任务完成吗？提交后将等待厂长确认，无法撤回。',\n          confirmText: '确定提交',\n          cancelText: '检查一下',\n          confirmColor: '#007aff',\n          success: resolve,\n          fail: () => resolve({ confirm: false })\n        });\n      });\n      \n      if (!confirmRes.confirm) return;\n      \n      this.isSubmitting = true;\n      \n      try {\n        const res = await uniCloud.callFunction({\n          name: 'feedback-workflow',\n          data: {\n            action: 'responsible_complete',\n            id: this.taskId,\n            completionDescription: this.formData.completionDescription.trim(),\n            completionEvidence: this.formData.completionEvidence\n          }\n        });\n        \n        if (res.result && res.result.code === 0) {\n          // 提交成功\n          uni.showToast({\n            title: '提交成功',\n            icon: 'success',\n            duration: 2000\n          });\n          \n          // 发送事件通知，确保其他页面实时更新\n          uni.$emit('task-completed', { \n            id: this.taskId,\n            type: 'responsible_complete' \n          });\n          uni.$emit('feedback-updated', { \n            id: this.taskId,\n            action: 'responsible_complete' \n          });\n          uni.$emit('ucenter-need-refresh', { \n            id: this.taskId \n          });\n          \n          // 延迟返回，让用户看到成功提示\n          setTimeout(() => {\n            uni.navigateBack({\n              delta: 1\n            });\n          }, 2000);\n        } else {\n          // 检查是否是记录已删除的错误\n          if (res.result?.code === 404 && res.result?.errorType === 'RECORD_DELETED') {\n            this.handleRecordDeleted();\n            return;\n          }\n          throw new Error(res.result?.message || '提交失败');\n        }\n      } catch (error) {\n        // 检查是否是网络错误导致的记录不存在\n        if (error.message && error.message.includes('记录不存在')) {\n          this.handleRecordDeleted();\n          return;\n        }\n        // 提交失败\n        uni.showModal({\n          title: '提交失败',\n          content: error.message || '网络异常，请稍后重试',\n          showCancel: false,\n          confirmText: '我知道了',\n          confirmColor: '#007aff'\n        });\n      } finally {\n        this.isSubmitting = false;\n      }\n    },\n\n    /**\n     * 静默刷新任务数据\n     */\n    async silentRefreshTask() {\n      try {\n        const res = await uniCloud.callFunction({\n          name: 'feedback-workflow',\n          data: {\n            action: 'get_detail',\n            id: this.taskId\n          }\n        });\n        \n        if (res.result && res.result.code === 0) {\n          this.taskInfo = res.result.data;\n          \n          // 验证是否为当前用户的任务\n          const userInfo = uniCloud.getCurrentUserInfo();\n          if (this.taskInfo.responsibleUserId !== userInfo.uid) {\n            // 如果不再是当前用户的任务，可能已被重新指派，返回上级页面\n            uni.showModal({\n              title: '提示',\n              content: '任务状态已变更，即将返回上级页面',\n              showCancel: false,\n              success: () => {\n                uni.navigateBack();\n              }\n            });\n            return;\n          }\n          \n          // 检查任务状态，如果已完成则提示并返回\n          if (this.taskInfo.workflowStatus === 'final_completed') {\n            uni.showModal({\n              title: '任务已完成',\n              content: '该任务已被厂长确认完成',\n              showCancel: false,\n              success: () => {\n                uni.navigateBack();\n              }\n            });\n          } else if (this.taskInfo.workflowStatus === 'terminated') {\n            uni.showModal({\n              title: '任务已终止',\n              content: '该任务流程已被终止',\n              showCancel: false,\n              success: () => {\n                uni.navigateBack();\n              }\n            });\n          }\n        }\n      } catch (error) {\n        console.log('静默刷新任务失败:', error);\n      }\n    },\n\n    /**\n     * 处理页面可见性变化\n     */\n    handleVisibilityChange() {\n      // 统一由角标管理器处理跨设备更新\n    },\n\n    /**\n     * 智能判断是否需要刷新数据\n     */\n    shouldRefreshOnCrossDeviceUpdate(data) {\n      // 如果页面不可见，不需要刷新\n      if (!this.isPageVisible) {\n        console.log('任务完成页面不可见，跳过跨设备更新');\n        return false;\n      }\n      \n      // 任务完成页面对重要更新类型立即响应，不受时间限制\n      const importantTypes = ['workflow_status_changed', 'feedback_submitted', 'feedback_deleted'];\n      const hasImportantUpdate = data.updateTypes && data.updateTypes.some(type => importantTypes.includes(type));\n      \n      // 如果更新的反馈记录包含当前正在查看的任务，需要刷新\n      if (data.feedbackIds && data.feedbackIds.includes(this.taskId)) {\n        console.log('任务完成页面检测到当前任务更新，需要刷新');\n        return true;\n      }\n      \n      // 如果更新类型包含重要操作，立即刷新\n      if (hasImportantUpdate) {\n        console.log('任务完成页面检测到重要更新类型，需要刷新:', data.updateTypes);\n        return true;\n      }\n      \n      console.log('任务完成页面跨设备更新判断：不需要刷新');\n      return false;\n    },\n\n    /**\n     * 处理记录已被删除的情况\n     * 当其他用户删除了正在操作的任务记录时，优雅地处理这种边界情况\n     */\n    handleRecordDeleted() {\n      // 清除loading状态\n      uni.hideLoading();\n      this.isSubmitting = false;\n      this.isLoading = false;\n\n      // 显示友好的提示信息\n      uni.showModal({\n        title: '任务已删除',\n        content: '您正在操作的任务记录已被其他用户删除，将返回上一页面。',\n        showCancel: false,\n        confirmText: '确定',\n        success: () => {\n          // 触发相关页面刷新事件\n          uni.$emit('refresh-todo-list');\n          uni.$emit('feedback-updated');\n          uni.$emit('ucenter-need-refresh');\n\n          // 立即更新角标\n          const todoBadgeManager = require('@/utils/todo-badge.js').default;\n          if (todoBadgeManager) {\n            todoBadgeManager.forceRefresh();\n          }\n\n          // 返回上一页面\n          uni.navigateBack({\n            delta: 1,\n            fail: () => {\n              // 如果无法返回，跳转到首页\n              uni.switchTab({\n                url: '/pages/index/index'\n              });\n            }\n          });\n        }\n      });\n    },\n\n    \n  },\n\n  /**\n   * 页面生命周期\n   */\n  onShow() {\n    this.isPageVisible = true;\n    // 不再进行独立的跨设备检查，统一由角标管理器处理\n  },\n\n  onHide() {\n    this.isPageVisible = false;\n  },\n\n  created() {\n    // 监听角标管理器的跨设备更新事件\n    uni.$on('cross-device-update-detected', (data) => {\n      if (data.silent && this.taskId) {\n        // 智能判断是否需要刷新\n        const shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);\n        if (shouldRefresh) {\n          console.log('任务完成页面收到跨设备更新通知，静默刷新数据');\n          // 静默刷新数据\n          this.silentRefreshTask();\n        }\n      }\n    });\n    \n    // 监听页面可见性变化\n    // 统一由角标管理器处理跨设备更新\n  },\n\n  beforeDestroy() {\n    // 移除事件监听\n    uni.$off('cross-device-update-detected');\n    \n    // 统一由角标管理器处理跨设备更新\n  }\n};\n</script>\n\n<style scoped>\npage {\n  background-color: #f8f9fa;\n}\n\n.container {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);\n}\n\n/* 任务卡片 */\n.task-card {\n  margin: 24rpx;\n  background: #fff;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.08);\n}\n\n.task-header {\n  display: flex;\n  align-items: flex-start;\n  padding: 32rpx;\n  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);\n  color: #fff;\n}\n\n.task-avatar {\n  width: 56rpx;\n  height: 56rpx;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n}\n\n.task-main {\n  flex: 1;\n}\n\n.task-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  margin-bottom: 8rpx;\n  line-height: 1.4;\n}\n\n.task-project {\n  font-size: 26rpx;\n  opacity: 0.8;\n}\n\n.task-status {\n  margin-left: 16rpx;\n}\n\n.status-badge {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  padding: 12rpx 20rpx;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 20rpx;\n  font-size: 24rpx;\n}\n\n.task-body {\n  padding: 32rpx;\n}\n\n.task-description {\n  margin-bottom: 24rpx;\n}\n\n.desc-title {\n  display: block;\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 12rpx;\n}\n\n.desc-content {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.6;\n  word-break: break-word;\n}\n\n.task-meta {\n  display: flex;\n  flex-direction: column;\n  gap: 12rpx;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.meta-text {\n  font-size: 24rpx;\n  color: #666;\n}\n\n/* 状态标签区样式 */\n.task-status-tags {\n  display: flex;\n  gap: 12rpx;\n  margin-bottom: 20rpx;\n}\n\n.status-tag {\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  padding: 8rpx 12rpx;\n  border-radius: 16rpx;\n  font-size: 22rpx;\n  font-weight: 500;\n}\n\n.reject-tag {\n  background: #f39c12;\n  color: #ffffff;\n}\n\n/* 内联指派说明样式 */\n.assign-reason-inline {\n  color: #1677FF;\n  font-weight: 500;\n}\n\n/* 退回原因样式 */\n.reject-reason-inline {\n  color: #f39c12;\n  font-weight: 500;\n}\n\n/* 表单卡片 */\n.form-card {\n  margin: 24rpx;\n  background: #fff;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  padding: 32rpx 32rpx 24rpx;\n  border-bottom: 1rpx solid #f5f5f5;\n}\n\n.card-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.form-item {\n  padding: 32rpx;\n  border-bottom: 1rpx solid #f8f9fa;\n}\n\n.form-item:last-child {\n  border-bottom: none;\n}\n\n.form-label {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 16rpx;\n  display: flex;\n  align-items: center;\n}\n\n.form-label.required::after {\n  content: '*';\n  color: #f44336;\n  margin-left: 4rpx;\n}\n\n.form-tip {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  padding: 16rpx 20rpx;\n  background: #fff8e1;\n  border-radius: 12rpx;\n  margin-bottom: 24rpx;\n  font-size: 24rpx;\n  color: #f57c00;\n}\n\n.custom-textarea {\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  padding: 16rpx 20rpx;\n  font-size: 28rpx;\n  line-height: 1.6;\n}\n\n.char-count {\n  text-align: right;\n  font-size: 22rpx;\n  color: #999;\n  margin-top: 12rpx;\n}\n\n/* 图片上传 */\n.upload-area {\n  margin-top: 16rpx;\n  padding: 0;\n}\n\n.image-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 16rpx;\n  margin-bottom: 16rpx;\n  padding: 16rpx;\n  max-width: 700rpx; /* 调整最大宽度，让图片稍微大一点 */\n}\n\n@media (max-width: 750rpx) {\n  .image-grid {\n    grid-template-columns: repeat(3, 1fr);\n    gap: 14rpx;\n    padding: 12rpx;\n  }\n}\n\n@media (min-width: 751rpx) {\n  .image-grid {\n    grid-template-columns: repeat(4, 1fr);\n    gap: 18rpx;\n    padding: 16rpx;\n  }\n}\n\n.image-item {\n  position: relative;\n  width: 100%;\n  height: 0;\n  padding-bottom: 100%; /* 创建正方形容器 */\n  border-radius: 12rpx;\n  overflow: visible; /* 允许删除按钮超出容器 */\n}\n\n.uploaded-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 12rpx;\n  z-index: 1; /* 确保图片在删除按钮下方 */\n}\n\n.image-delete {\n  position: absolute;\n  top: -10rpx;\n  right: -10rpx;\n  width: 38rpx;\n  height: 38rpx;\n  background: #f44336;\n  border: 2rpx solid #fff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 3rpx 10rpx rgba(244, 67, 54, 0.4);\n  z-index: 100; /* 确保删除按钮在最上层 */\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.image-delete:active {\n  transform: scale(0.9);\n  background: #d32f2f;\n}\n\n.add-image-btn {\n  position: relative;\n  width: 100%;\n  height: 0;\n  padding-bottom: 100%; /* 创建正方形容器 */\n  border: 2rpx dashed #ddd;\n  border-radius: 12rpx;\n  background: #fafafa;\n  transition: all 0.3s ease;\n  overflow: hidden;\n  cursor: pointer;\n}\n\n.add-image-btn .add-content {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  width: 100%;\n  height: 100%;\n  transition: transform 0.2s ease;\n}\n\n.add-image-btn:active {\n  background: #f0f0f0;\n  border-color: #bbb;\n}\n\n.add-image-btn:active .add-content {\n  transform: translate(-50%, -50%) scale(0.95);\n}\n\n.add-text {\n  font-size: 24rpx;\n  color: #999;\n  font-weight: 500;\n}\n\n.upload-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 16rpx;\n  margin-top: 8rpx;\n}\n\n.upload-count {\n  font-size: 24rpx;\n  color: #666;\n  font-weight: 500;\n}\n\n.upload-tip {\n  font-size: 22rpx;\n  color: #999;\n}\n\n/* 提交区域 */\n.submit-section {\n  padding: 40rpx 24rpx 60rpx;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 96rpx;\n  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);\n  color: #fff;\n  border: none;\n  border-radius: 48rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.submit-btn:not(:disabled):active {\n  transform: translateY(2rpx);\n  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);\n}\n\n.submit-btn.submitting {\n  background: #ccc;\n  box-shadow: none;\n  color: #fff;\n}\n\n.btn-content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  width: 100%;\n  height: 100%;\n}\n\n.loading-icon {\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n.submit-tip {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  margin-top: 24rpx;\n  font-size: 22rpx;\n  color: #999;\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(4px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.loading-text {\n  font-size: 26rpx;\n  color: #666;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./complete-task.vue?vue&type=style&index=0&id=6ca1bbf8&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./complete-task.vue?vue&type=style&index=0&id=6ca1bbf8&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571664449\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
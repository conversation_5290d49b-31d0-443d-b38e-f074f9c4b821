<view class="container data-v-4883731c"><block wx:if="{{!hasLogin}}"><view data-event-opts="{{[['tap',[['goToLogin',['$event']]]]]}}" class="user-card data-v-4883731c" bindtap="__e"><view class="avatar-section data-v-4883731c"><view class="default-avatar data-v-4883731c"><uni-icons vue-id="70291d2c-1" type="person-filled" size="40" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view></view><view class="user-info data-v-4883731c"><text class="username login-prompt data-v-4883731c">点击登录</text><text class="login-tip data-v-4883731c">登录后可查看更多功能</text></view><view class="card-arrow data-v-4883731c"><uni-icons vue-id="70291d2c-2" type="right" size="16" color="#BBBBBB" class="data-v-4883731c" bind:__l="__l"></uni-icons></view></view></block><block wx:else><view class="user-card data-v-4883731c"><view class="avatar-section data-v-4883731c"><view class="avatar-container data-v-4883731c"><view class="default-avatar-bg data-v-4883731c"><uni-icons vue-id="70291d2c-3" type="person-filled" size="40" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><block wx:if="{{userInfo.avatar_file&&userInfo.avatar_file.url}}"><image class="avatar-image data-v-4883731c" style="{{'opacity:'+(avatarLoaded?1:0)+';'}}" src="{{userInfo.avatar_file.url}}" mode="aspectFill" data-event-opts="{{[['load',[['onAvatarLoad',['$event']]]],['error',[['onAvatarError',['$event']]]]]}}" bindload="__e" binderror="__e"></image></block><block wx:else><block wx:if="{{userInfo.avatar}}"><image class="avatar-image data-v-4883731c" style="{{'opacity:'+(avatarLoaded?1:0)+';'}}" src="{{userInfo.avatar}}" mode="aspectFill" data-event-opts="{{[['load',[['onAvatarLoad',['$event']]]],['error',[['onAvatarError',['$event']]]]]}}" bindload="__e" binderror="__e"></image></block></block></view></view><view class="user-info data-v-4883731c"><text class="username data-v-4883731c">{{userInfo.nickname||userInfo.username||'未设置昵称'}}</text><block wx:if="{{$root.g0}}"><view class="role-tags data-v-4883731c"><block wx:for="{{roleNames}}" wx:for-item="role" wx:for-index="index" wx:key="index"><text class="role-tag data-v-4883731c">{{role}}</text></block></view></block></view></view></block><block wx:if="{{hasLogin}}"><block class="data-v-4883731c"><view class="patrol-section data-v-4883731c"><view class="patrol-title data-v-4883731c">综合中心</view><scroll-view class="patrol-scroll data-v-4883731c" scroll-x="true" show-scrollbar="{{false}}"><view class="patrol-grid data-v-4883731c"><block wx:if="{{$root.m0}}"><view data-event-opts="{{[['tap',[['navTo',['/pages/patrol_pkg/point/index']]]]]}}" class="patrol-item data-v-4883731c" bindtap="__e"><view class="patrol-icon point-icon data-v-4883731c"><uni-icons vue-id="70291d2c-4" type="location-filled" size="24" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><text class="patrol-text data-v-4883731c">点位管理</text></view></block><block wx:if="{{$root.m1}}"><view data-event-opts="{{[['tap',[['navTo',['/pages/patrol_pkg/shift/index']]]]]}}" class="patrol-item data-v-4883731c" bindtap="__e"><view class="patrol-icon shift-icon data-v-4883731c"><uni-icons vue-id="70291d2c-5" type="calendar" size="24" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><text class="patrol-text data-v-4883731c">班次管理</text></view></block><block wx:if="{{$root.m2}}"><view data-event-opts="{{[['tap',[['navTo',['/pages/patrol_pkg/route/index']]]]]}}" class="patrol-item data-v-4883731c" bindtap="__e"><view class="patrol-icon route-icon data-v-4883731c"><uni-icons vue-id="70291d2c-6" type="map-filled" size="24" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><text class="patrol-text data-v-4883731c">线路管理</text></view></block><view data-event-opts="{{[['tap',[['navTo',['/pages/patrol_pkg/task/index']]]]]}}" class="patrol-item data-v-4883731c" bindtap="__e"><view class="patrol-icon task-icon data-v-4883731c"><uni-icons vue-id="70291d2c-7" type="flag-filled" size="24" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><text class="patrol-text data-v-4883731c">任务管理</text></view><view data-event-opts="{{[['tap',[['navTo',['/pages/patrol_pkg/record/index']]]]]}}" class="patrol-item data-v-4883731c" bindtap="__e"><view class="patrol-icon record-icon data-v-4883731c"><uni-icons vue-id="70291d2c-8" type="bars" size="24" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><text class="patrol-text data-v-4883731c">巡视记录</text></view><block wx:if="{{$root.m3}}"><view data-event-opts="{{[['tap',[['navTo',['/pages/patrol_pkg/point/qrcode-batch']]]]]}}" class="patrol-item data-v-4883731c" bindtap="__e"><view class="patrol-icon qrcode-icon data-v-4883731c"><uni-icons vue-id="70291d2c-9" type="scan" size="24" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><text class="patrol-text data-v-4883731c">二维码管理</text></view></block><view data-event-opts="{{[['tap',[['navTo',['/pages/honor_pkg/gallery/index']]]]]}}" class="patrol-item data-v-4883731c" bindtap="__e"><view class="patrol-icon gallery-icon data-v-4883731c"><uni-icons vue-id="70291d2c-10" type="star-filled" size="24" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><text class="patrol-text data-v-4883731c">荣誉展厅</text></view></view></scroll-view></view><view class="action-list data-v-4883731c"><view data-event-opts="{{[['tap',[['goToReadNewsLog',['$event']]]]]}}" class="action-item data-v-4883731c" bindtap="__e"><view class="action-icon news-icon data-v-4883731c"><uni-icons vue-id="70291d2c-11" type="notification-filled" size="22" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><view class="action-content data-v-4883731c"><text class="action-text data-v-4883731c">公告通知</text></view><uni-icons vue-id="70291d2c-12" type="right" size="16" color="#CCCCCC" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['checkExportPermission',['$event']]]]]}}" class="action-item data-v-4883731c" bindtap="__e"><view class="action-icon export-icon data-v-4883731c"><uni-icons vue-id="70291d2c-13" type="download" size="22" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><view class="action-content data-v-4883731c"><text class="action-text data-v-4883731c">文档导出</text></view><uni-icons vue-id="70291d2c-14" type="right" size="16" color="#CCCCCC" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><block wx:if="{{hasResponsiblePermission}}"><view data-event-opts="{{[['tap',[['goToResponsibleTasks',['$event']]]]]}}" class="action-item data-v-4883731c" bindtap="__e"><view class="action-icon responsible-tasks-icon data-v-4883731c"><uni-icons vue-id="70291d2c-15" type="list" size="22" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><view class="action-content data-v-4883731c"><text class="action-text data-v-4883731c">我的任务</text><block wx:if="{{responsibleTaskCount>0}}"><text class="action-badge data-v-4883731c">{{responsibleTaskCount}}</text></block></view><uni-icons vue-id="70291d2c-16" type="right" size="16" color="#CCCCCC" class="data-v-4883731c" bind:__l="__l"></uni-icons></view></block><block wx:if="{{hasAdminPermission}}"><view data-event-opts="{{[['tap',[['goToUserManagement',['$event']]]]]}}" class="action-item data-v-4883731c" bindtap="__e"><view class="action-icon user-management-icon data-v-4883731c"><uni-icons vue-id="70291d2c-17" type="person-filled" size="22" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><view class="action-content data-v-4883731c"><text class="action-text data-v-4883731c">用户管理</text></view><uni-icons vue-id="70291d2c-18" type="right" size="16" color="#CCCCCC" class="data-v-4883731c" bind:__l="__l"></uni-icons></view></block><block wx:if="{{hasGMPermission}}"><view data-event-opts="{{[['tap',[['goToGMSupervision',['$event']]]]]}}" class="action-item data-v-4883731c" bindtap="__e"><view class="action-icon gm-supervision-icon data-v-4883731c"><uni-icons vue-id="70291d2c-19" type="eye-filled" size="22" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><view class="action-content data-v-4883731c"><text class="action-text data-v-4883731c">指派监督</text><block wx:if="{{supervisionTaskCount>0}}"><text class="action-badge data-v-4883731c">{{supervisionTaskCount}}</text></block></view><uni-icons vue-id="70291d2c-20" type="right" size="16" color="#CCCCCC" class="data-v-4883731c" bind:__l="__l"></uni-icons></view></block><view data-event-opts="{{[['tap',[['modifyNickname',['$event']]]]]}}" class="action-item data-v-4883731c" bindtap="__e"><view class="action-icon settings-icon data-v-4883731c"><uni-icons vue-id="70291d2c-21" type="gear" size="22" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><view class="action-content data-v-4883731c"><text class="action-text data-v-4883731c">用户设置</text></view><uni-icons vue-id="70291d2c-22" type="right" size="16" color="#CCCCCC" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['logout',['$event']]]]]}}" class="action-item data-v-4883731c" bindtap="__e"><view class="action-icon logout-icon data-v-4883731c"><uni-icons vue-id="70291d2c-23" type="closeempty" size="22" color="#FFFFFF" class="data-v-4883731c" bind:__l="__l"></uni-icons></view><view class="action-content data-v-4883731c"><text class="action-text logout-text data-v-4883731c">退出登录</text></view><uni-icons vue-id="70291d2c-24" type="right" size="16" color="#CCCCCC" class="data-v-4883731c" bind:__l="__l"></uni-icons></view></view><block wx:if="{{hasLogin&&hasRole}}"><view class="todo-section data-v-4883731c"><view class="section-header data-v-4883731c"><text class="section-title data-v-4883731c">我的待办</text><block wx:if="{{hasRole}}"><view data-event-opts="{{[['tap',[['goToTodoList',['$event']]]]]}}" class="todo-count-wrapper data-v-4883731c" bindtap="__e"><block wx:if="{{todoCount>0}}"><text class="todo-count data-v-4883731c">{{"共"+todoCount+"项待处理"}}</text></block><block wx:else><text class="todo-count data-v-4883731c">暂无待办</text></block></view></block></view><block wx:if="{{$root.g1>0}}"><view class="todo-list data-v-4883731c"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleTodoClick',['$0'],[[['todoList','',index]]]]]]]}}" class="todo-item data-v-4883731c" bindtap="__e"><view class="todo-content data-v-4883731c"><view class="todo-title data-v-4883731c"><text class="name data-v-4883731c">{{item.$orig.name}}</text><text class="project data-v-4883731c">{{item.m4}}</text></view><text class="description data-v-4883731c">{{item.$orig.description}}</text><view class="todo-footer data-v-4883731c"><text class="time data-v-4883731c">{{item.m5}}</text><text class="todo-type data-v-4883731c">{{item.m6}}</text></view></view><uni-icons vue-id="{{'70291d2c-25-'+index}}" type="right" size="16" color="#BBBBBB" class="data-v-4883731c" bind:__l="__l"></uni-icons></view></block></view></block><block wx:else><p-empty-state vue-id="70291d2c-26" text="{{hasRole?'暂无待办事项':'权限不够无法查看待办事项'}}" image="/static/empty/empty_todo.png" class="data-v-4883731c" bind:__l="__l"></p-empty-state></block></view></block></block></block><block wx:else><p-empty-state vue-id="70291d2c-27" text="登录后查看更多功能" image="/static/empty/empty_todo.png" class="data-v-4883731c" bind:__l="__l"></p-empty-state></block></view>
{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?db4e", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?4cc1", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?ddfa", "uni-app:///pages/ucenter_pkg/gm-supervision.vue", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?8e7a", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?512b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "taskList", "taskStats", "assigned", "pending", "completed", "overdue", "currentFilter", "loading", "loadingStatus", "responsibleUsers", "currentUserId", "userRole", "computed", "filteredTaskList", "console", "uni", "checkPermission", "title", "content", "showCancel", "success", "getUserInfo", "userInfo", "db", "where", "field", "get", "result", "loadTaskData", "uniCloud", "name", "action", "res", "taskCount", "stats", "responsibleUserCount", "icon", "duration", "setFilter", "filterByStatus", "getStatusText", "getResponsibleName", "getTimeLabel", "getTimeValue", "timestamp", "isOverdue", "isWarning", "goToTaskDetail", "url", "quickConfirm", "confirmText", "cancelText", "quickReject", "confirmTask", "id", "reason", "getEmptyText"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAAwmB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC4I5nB;AAAA;AAAA;EAGAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MAAA;MACA;QACA;MACA;MACA;QACA;UAAA;QAAA;MACA;MACA;QAAA;MAAA;IACA;EACA;AAAA,oEACA;EACAA;IAAA;IACA;MACA;IACA;IACA;MACA;QAAA;MAAA;IACA;IACA;MAAA;IAAA;EACA;AACA,qFACA;EAAA;EAAA;IAAA;MAAA;QAAA;UAAA;YACAC;YAAA;YAAA,OACA;UAAA;YACA;cACA;YACA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA;AACA,qFACA;EACA;EACA;AACA,2GACA;EACA;IACAC;EACA;AACA,oEACA;EACA;EACAC;IACAF;MACAJ;MACAC;IACA;;IAEA;IACA;MACAI;QACAE;QACAC;QACAC;QACAC;UACAL;QACA;MACA;MACA;IACA;;IAEA;IACA;MAAA;IAAA;IACA;MACAA;QACAE;QACAC;QACAC;QACAC;UACAL;QACA;MACA;MACA;IACA;IAEAD;IACA;EACA;EAEA;EACAO;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cACAC;cACA;cAEAR;cAAA,IAEA;gBAAA;gBAAA;cAAA;cACAA;cAAA;YAAA;cAIA;cACAS;cAAA;cAAA,OACAA,8BACAC,gCACAC,mBACAC;YAAA;cAAA;cAHAC;cAKA;gBACA;gBACAb;cACA;gBACA;gBACAA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EAEA;EACAc;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;cAEAd;cAAA;cAAA;cAAA,OAGAe;gBACAC;gBACA/B;kBACAgC;gBACA;cACA;YAAA;cALAC;cAOAlB;cAAA,MAEAkB;gBAAA;gBAAA;cAAA;cACA;cACA;cACA;cAEAlB;gBACAmB;gBACAC;gBACAC;cACA;cAAA;cAAA;YAAA;cAEArB;cAAA,MACA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAGAA;cACAC;gBACAE;gBACAmB;gBACAC;cACA;YAAA;cAAA;cAEA;cACA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EAEA;EACAC;IACA;EACA;EAEA;EACAC;IACA;EACA;EAEA;EACAC;IACA;MACA;MACA;MACA;IACA;IACA;EACA;EAEA;EACAC;IACA;IACA;EACA;EAEA;EACAC;IACA;MACA;IACA;MACA;IACA;MACA;IACA;IACA;EACA;EAEA;EACAC;IACA;IACA;MACAC;IACA;MACAA;IACA;MACAA;IACA;MACAA;IACA;IACA;EACA;EAEA;EACAC;IACA;IACA;;IAEA;IACA;IACA;EACA;EAEA;EACAC;IACA;IACA;IAEA;IACA;IACA;IAEA;EACA;EAEA;EACAC;IACAhC;MACAiC;IACA;EACA;EAEA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEAlC;gBACAE;gBACAC;gBACAgC;gBACAC;cACA;YAAA;cALAnB;cAAA,KAOAA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAGAlB;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EAEA;EACAsC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEArC;gBACAE;gBACAC;gBACAgC;gBACAC;cACA;YAAA;cALAnB;cAOA;gBACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAlB;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EAEA;EACAuC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEAtC;gBAAAE;cAAA;cAAA;cAAA,OAEAY;gBACAC;gBACA/B;kBACAgC;kBACAuB;kBACAC;gBACA;cACA;YAAA;cAPAvB;cAAA,MASAA;gBAAA;gBAAA;cAAA;cACAjB;gBACAE;gBACAmB;cACA;cACA;cACA;cAAA;cAAA;YAAA;cAAA,MAEA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAGAtB;cACAC;gBACAE;gBACAmB;cACA;YAAA;cAAA;cAEArB;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EAEA;EACAyC;IACA;MACA;MACA;MACA;MACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACheA;AAAA;AAAA;AAAA;AAA2qC,CAAgB,ipCAAG,EAAC,C;;;;;;;;;;;ACA/rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ucenter_pkg/gm-supervision.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/ucenter_pkg/gm-supervision.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./gm-supervision.vue?vue&type=template&id=1f55f9ed&scoped=true&\"\nvar renderjs\nimport script from \"./gm-supervision.vue?vue&type=script&lang=js&\"\nexport * from \"./gm-supervision.vue?vue&type=script&lang=js&\"\nimport style0 from \"./gm-supervision.vue?vue&type=style&index=0&id=1f55f9ed&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1f55f9ed\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ucenter_pkg/gm-supervision.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=template&id=1f55f9ed&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.filteredTaskList.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.filteredTaskList, function (task, index) {\n          var $orig = _vm.__get_orig(task)\n          var m0 = _vm.getStatusText(task.workflowStatus)\n          var m1 = _vm.getResponsibleName(task.responsibleUserId)\n          var m2 = _vm.getTimeLabel(task)\n          var m3 = _vm.isOverdue(task)\n          var m4 = _vm.isWarning(task)\n          var m5 = _vm.getTimeValue(task)\n          var m6 =\n            task.workflowStatus === \"assigned_to_responsible\"\n              ? _vm.isOverdue(task)\n              : null\n          var m7 =\n            task.workflowStatus === \"assigned_to_responsible\" && !m6\n              ? _vm.isWarning(task)\n              : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n            m6: m6,\n            m7: m7,\n          }\n        })\n      : null\n  var m8 = !(g0 > 0) ? _vm.getEmptyText() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        m8: m8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 页面标题 -->\n\t\t<view class=\"page-header\">\n\t\t\t<text class=\"page-title\">指派任务监督</text>\n\t\t\t<text class=\"page-subtitle\">实时监控指派任务的执行情况</text>\n\t\t\t<view class=\"timing-explanation\">\n\t\t\t\t<text class=\"explanation-text\">💡 超时说明：执行中任务超过7天为警告，超过14天为超时</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 统计卡片区域 -->\n\t\t<view class=\"stats-section\">\n\t\t\t<view class=\"stats-grid\">\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('assigned_to_responsible')\">\n\t\t\t\t\t<view class=\"stat-number assigned\">{{ taskStats.assigned || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">执行中</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('completed_by_responsible')\">\n\t\t\t\t\t<view class=\"stat-number pending\">{{ taskStats.pending || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">待确认</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('final_completed')\">\n\t\t\t\t\t<view class=\"stat-number completed\">{{ taskStats.completed || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">已完成</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('overdue')\">\n\t\t\t\t\t<view class=\"stat-number overdue\">{{ taskStats.overdue || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">超时</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 筛选标签 -->\n\t\t<view class=\"filter-tabs\">\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'all' }\"\n\t\t\t\t@click=\"setFilter('all')\">\n\t\t\t\t全部\n\t\t\t</view>\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'assigned_to_responsible' }\"\n\t\t\t\t@click=\"setFilter('assigned_to_responsible')\">\n\t\t\t\t执行中\n\t\t\t</view>\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'completed_by_responsible' }\"\n\t\t\t\t@click=\"setFilter('completed_by_responsible')\">\n\t\t\t\t待确认\n\t\t\t</view>\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'overdue' }\"\n\t\t\t\t@click=\"setFilter('overdue')\">\n\t\t\t\t超时任务\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 任务列表 -->\n\t\t<view class=\"task-list\" v-if=\"filteredTaskList.length > 0\">\n\t\t\t<view \n\t\t\t\tclass=\"task-item\" \n\t\t\t\tv-for=\"(task, index) in filteredTaskList\" \n\t\t\t\t:key=\"task._id\"\n\t\t\t\t@click=\"goToTaskDetail(task)\">\n\t\t\t\t\n\t\t\t\t<view class=\"task-header\">\n\t\t\t\t\t<view class=\"task-title-row\">\n\t\t\t\t\t\t<text class=\"task-name\">{{ task.name }}</text>\n\t\t\t\t\t\t<view class=\"task-status\"\n\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t'status-assigned': task.workflowStatus === 'assigned_to_responsible',\n\t\t\t\t\t\t\t\t'status-pending': task.workflowStatus === 'completed_by_responsible',\n\t\t\t\t\t\t\t\t'status-completed': task.workflowStatus === 'final_completed'\n\t\t\t\t\t\t\t}\">\n\t\t\t\t\t\t\t{{ getStatusText(task.workflowStatus) }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"task-project\">{{ task.project || '未分类' }}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"task-content\">\n\t\t\t\t\t<text class=\"task-description\">{{ task.description || '暂无描述' }}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"task-footer\">\n\t\t\t\t\t<view class=\"responsible-info\">\n\t\t\t\t\t\t<text class=\"responsible-label\">负责人：</text>\n\t\t\t\t\t\t<text class=\"responsible-name\">{{ getResponsibleName(task.responsibleUserId) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"time-info\">\n\t\t\t\t\t\t<text class=\"time-label\">{{ getTimeLabel(task) }}</text>\n\t\t\t\t\t\t<text class=\"time-value\" :class=\"{\n\t\t\t\t\t\t\toverdue: isOverdue(task),\n\t\t\t\t\t\t\twarning: isWarning(task)\n\t\t\t\t\t\t}\">\n\t\t\t\t\t\t\t{{ getTimeValue(task) }}\n\t\t\t\t\t\t</text>\n\t\t\t\t\t\t<!-- 时效提醒标签 -->\n\t\t\t\t\t\t<view class=\"timing-badge\" v-if=\"task.workflowStatus === 'assigned_to_responsible'\">\n\t\t\t\t\t\t\t<text v-if=\"isOverdue(task)\" class=\"timing-tag overdue-tag\">超时</text>\n\t\t\t\t\t\t\t<text v-else-if=\"isWarning(task)\" class=\"timing-tag warning-tag\">警告</text>\n\t\t\t\t\t\t\t<text v-else class=\"timing-tag normal-tag\">正常</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 快速操作按钮 -->\n\t\t\t\t<view class=\"quick-actions\" v-if=\"task.workflowStatus === 'completed_by_responsible'\">\n\t\t\t\t\t<view class=\"action-btn confirm-btn\" @click.stop=\"quickConfirm(task)\">\n\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t<text>确认完成</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn reject-btn\" @click.stop=\"quickReject(task)\">\n\t\t\t\t\t\t<uni-icons type=\"closeempty\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t<text>退回重做</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 空状态 -->\n\t\t<view class=\"empty-state\" v-else>\n\t\t\t<image class=\"empty-image\" src=\"/static/empty/empty_task.png\" mode=\"aspectFit\"></image>\n\t\t\t<text class=\"empty-text\">{{ getEmptyText() }}</text>\n\t\t</view>\n\n\t\t<!-- 加载状态 -->\n\t\t<view class=\"loading-state\" v-if=\"loading\">\n\t\t\t<view class=\"custom-loading\">\n\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { formatDate } from '@/utils/date.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttaskList: [],\n\t\t\t\ttaskStats: {\n\t\t\t\t\tassigned: 0,\n\t\t\t\t\tpending: 0,\n\t\t\t\t\tcompleted: 0,\n\t\t\t\t\toverdue: 0\n\t\t\t\t},\n\t\t\t\tcurrentFilter: 'all',\n\t\t\t\tloading: false,\n\t\t\t\tloadingStatus: 'more',\n\t\t\t\tresponsibleUsers: {}, // 负责人信息缓存\n\t\t\t\tcurrentUserId: '', // 当前用户ID\n\t\t\t\tuserRole: [], // 用户角色\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tfilteredTaskList() {\n\t\t\t\tif (this.currentFilter === 'all') {\n\t\t\t\t\treturn this.taskList;\n\t\t\t\t}\n\t\t\t\tif (this.currentFilter === 'overdue') {\n\t\t\t\t\treturn this.taskList.filter(task => this.isOverdue(task));\n\t\t\t\t}\n\t\t\t\treturn this.taskList.filter(task => task.workflowStatus === this.currentFilter);\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tfilteredTaskList() {\n\t\t\t\tif (this.currentFilter === 'all') {\n\t\t\t\t\treturn this.taskList;\n\t\t\t\t}\n\t\t\t\tif (this.currentFilter === 'overdue') {\n\t\t\t\t\treturn this.taskList.filter(task => this.isOverdue(task));\n\t\t\t\t}\n\t\t\t\treturn this.taskList.filter(task => task.workflowStatus === this.currentFilter);\n\t\t\t}\n\t\t},\n\t\tasync onLoad() {\n\t\t\tconsole.log('📱 厂长监督页面加载');\n\t\t\tawait this.getUserInfo();\n\t\t\tif (this.checkPermission()) {\n\t\t\t\tthis.loadTaskData();\n\t\t\t}\n\t\t},\n\t\tonShow() {\n\t\t\t// 页面显示时刷新数据\n\t\t\tthis.loadTaskData();\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.loadTaskData().then(() => {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t});\n\t\t},\n\t\tmethods: {\n\t\t\t// 权限检查\n\t\t\tcheckPermission() {\n\t\t\t\tconsole.log('📋 权限检查 - 当前用户信息:', {\n\t\t\t\t\tcurrentUserId: this.currentUserId,\n\t\t\t\t\tuserRole: this.userRole\n\t\t\t\t});\n\n\t\t\t\t// 检查是否登录\n\t\t\t\tif (!this.currentUserId) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\t\tcontent: '需要登录后才能访问此页面',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\t// 检查是否有厂长或管理员权限\n\t\t\t\tconst hasGMPermission = this.userRole.some(role => ['GM', 'admin'].includes(role));\n\t\t\t\tif (!hasGMPermission) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '权限不足',\n\t\t\t\t\t\tcontent: '只有厂长才能访问此页面',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tconsole.log('✅ 权限检查通过');\n\t\t\t\treturn true;\n\t\t\t},\n\n\t\t\t// 获取用户信息\n\t\t\tasync getUserInfo() {\n\t\t\t\ttry {\n\t\t\t\t\t// 获取当前用户ID\n\t\t\t\t\tconst userInfo = uniCloud.getCurrentUserInfo();\n\t\t\t\t\tthis.currentUserId = userInfo.uid;\n\n\t\t\t\t\tconsole.log('📋 获取到用户ID:', this.currentUserId);\n\n\t\t\t\t\tif (!this.currentUserId) {\n\t\t\t\t\t\tconsole.log('❌ 用户未登录');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 获取用户角色\n\t\t\t\t\tconst db = uniCloud.database();\n\t\t\t\t\tconst { result } = await db.collection('uni-id-users')\n\t\t\t\t\t\t.where(\"'_id' == $cloudEnv_uid\")\n\t\t\t\t\t\t.field('role, _id')\n\t\t\t\t\t\t.get();\n\n\t\t\t\t\tif (result.data && result.data.length > 0) {\n\t\t\t\t\t\tthis.userRole = result.data[0].role || [];\n\t\t\t\t\t\tconsole.log('📋 获取到用户角色:', this.userRole);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.userRole = [];\n\t\t\t\t\t\tconsole.log('❌ 未找到用户角色信息');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 获取用户信息失败:', error);\n\t\t\t\t\tthis.currentUserId = '';\n\t\t\t\t\tthis.userRole = [];\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载任务数据\n\t\t\tasync loadTaskData() {\n\t\t\t\tthis.loading = true;\n\t\t\t\tthis.loadingStatus = 'loading';\n\n\t\t\t\tconsole.log('开始加载厂长监督任务数据...');\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-list',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'getGMSupervisionTasks'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tconsole.log('云函数调用结果:', res);\n\n\t\t\t\t\tif (res.result && res.result.code === 0) {\n\t\t\t\t\t\tthis.taskList = res.result.data.list || [];\n\t\t\t\t\t\tthis.taskStats = res.result.data.stats || this.taskStats;\n\t\t\t\t\t\tthis.responsibleUsers = res.result.data.responsibleUsers || {};\n\n\t\t\t\t\t\tconsole.log('任务数据加载成功:', {\n\t\t\t\t\t\t\ttaskCount: this.taskList.length,\n\t\t\t\t\t\t\tstats: this.taskStats,\n\t\t\t\t\t\t\tresponsibleUserCount: Object.keys(this.responsibleUsers).length\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('云函数返回错误:', res.result);\n\t\t\t\t\t\tthrow new Error(res.result?.message || '获取任务数据失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载任务数据失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '加载失败',\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t\tthis.loadingStatus = 'more';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 设置筛选条件\n\t\t\tsetFilter(filter) {\n\t\t\t\tthis.currentFilter = filter;\n\t\t\t},\n\n\t\t\t// 按状态筛选\n\t\t\tfilterByStatus(status) {\n\t\t\t\tthis.setFilter(status);\n\t\t\t},\n\n\t\t\t// 获取状态文本\n\t\t\tgetStatusText(status) {\n\t\t\t\tconst textMap = {\n\t\t\t\t\t'assigned_to_responsible': '执行中',\n\t\t\t\t\t'completed_by_responsible': '待确认',\n\t\t\t\t\t'final_completed': '已完成'\n\t\t\t\t};\n\t\t\t\treturn textMap[status] || '未知状态';\n\t\t\t},\n\n\t\t\t// 获取负责人姓名\n\t\t\tgetResponsibleName(userId) {\n\t\t\t\tconst user = this.responsibleUsers[userId];\n\t\t\t\treturn user ? (user.nickname || user.username || '未知') : '未指派';\n\t\t\t},\n\n\t\t\t// 获取时间标签\n\t\t\tgetTimeLabel(task) {\n\t\t\t\tif (task.workflowStatus === 'assigned_to_responsible') {\n\t\t\t\t\treturn '指派时间：';\n\t\t\t\t} else if (task.workflowStatus === 'completed_by_responsible') {\n\t\t\t\t\treturn '完成时间：';\n\t\t\t\t} else if (task.workflowStatus === 'final_completed') {\n\t\t\t\t\treturn '确认时间：';\n\t\t\t\t}\n\t\t\t\treturn '创建时间：';\n\t\t\t},\n\n\t\t\t// 获取时间值\n\t\t\tgetTimeValue(task) {\n\t\t\t\tlet timestamp;\n\t\t\t\tif (task.workflowStatus === 'assigned_to_responsible') {\n\t\t\t\t\ttimestamp = task.assignedTime;\n\t\t\t\t} else if (task.workflowStatus === 'completed_by_responsible') {\n\t\t\t\t\ttimestamp = task.completedByResponsibleTime;\n\t\t\t\t} else if (task.workflowStatus === 'final_completed') {\n\t\t\t\t\ttimestamp = task.finalCompletedTime;\n\t\t\t\t} else {\n\t\t\t\t\ttimestamp = task.createTime;\n\t\t\t\t}\n\t\t\t\treturn timestamp ? formatDate(timestamp, 'MM-DD HH:mm') : '未知';\n\t\t\t},\n\n\t\t\t// 判断是否超时（与问题反馈系统保持一致）\n\t\t\tisOverdue(task) {\n\t\t\t\tif (task.workflowStatus !== 'assigned_to_responsible') return false;\n\t\t\t\tif (!task.assignedTime) return false;\n\n\t\t\t\t// 与问题反馈系统保持一致：超过14天视为超时\n\t\t\t\tconst fourteenDays = 14 * 24 * 60 * 60 * 1000;\n\t\t\t\treturn Date.now() - task.assignedTime > fourteenDays;\n\t\t\t},\n\n\t\t\t// 判断是否为警告状态（7-14天）\n\t\t\tisWarning(task) {\n\t\t\t\tif (task.workflowStatus !== 'assigned_to_responsible') return false;\n\t\t\t\tif (!task.assignedTime) return false;\n\n\t\t\t\tconst sevenDays = 7 * 24 * 60 * 60 * 1000;\n\t\t\t\tconst fourteenDays = 14 * 24 * 60 * 60 * 1000;\n\t\t\t\tconst timePassed = Date.now() - task.assignedTime;\n\n\t\t\t\treturn timePassed > sevenDays && timePassed <= fourteenDays;\n\t\t\t},\n\n\t\t\t// 跳转到任务详情\n\t\t\tgoToTaskDetail(task) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/feedback_pkg/examine?id=${task._id}`\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 快速确认完成\n\t\t\tasync quickConfirm(task) {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await uni.showModal({\n\t\t\t\t\t\ttitle: '确认完成',\n\t\t\t\t\t\tcontent: '确认该任务已完成？',\n\t\t\t\t\t\tconfirmText: '确认',\n\t\t\t\t\t\tcancelText: '取消'\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tawait this.confirmTask(task._id, '厂长确认任务完成');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('快速确认失败:', error);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 快速退回重做\n\t\t\tasync quickReject(task) {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await uni.showModal({\n\t\t\t\t\t\ttitle: '退回重做',\n\t\t\t\t\t\tcontent: '确认退回该任务重新执行？',\n\t\t\t\t\t\tconfirmText: '退回',\n\t\t\t\t\t\tcancelText: '取消'\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// 跳转到详情页面进行详细的退回操作\n\t\t\t\t\t\tthis.goToTaskDetail(task);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('快速退回失败:', error);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 确认任务完成\n\t\t\tasync confirmTask(taskId, reason) {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '处理中...' });\n\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-workflow',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'gm_final_confirm',\n\t\t\t\t\t\t\tid: taskId,\n\t\t\t\t\t\t\treason: reason\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.result && res.result.code === 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '确认成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\t// 刷新数据\n\t\t\t\t\t\tthis.loadTaskData();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(res.result?.message || '确认失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('确认任务失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '确认失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取空状态文本\n\t\t\tgetEmptyText() {\n\t\t\t\tconst textMap = {\n\t\t\t\t\t'all': '暂无指派任务',\n\t\t\t\t\t'assigned_to_responsible': '暂无执行中的任务',\n\t\t\t\t\t'completed_by_responsible': '暂无待确认的任务',\n\t\t\t\t\t'overdue': '暂无超时任务'\n\t\t\t\t};\n\t\t\t\treturn textMap[this.currentFilter] || '暂无数据';\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f8f9fc;\n\t\tpadding: 20rpx;\n\t}\n\n\t.page-header {\n\t\tbackground: linear-gradient(135deg, #2E8B57 0%, #20B2AA 100%); /* 海绿色到浅海绿色 - 环保主题 */\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tcolor: white;\n\t\tbox-shadow: 0 8rpx 32rpx rgba(46, 139, 87, 0.3);\n\t}\n\n\t.page-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.page-subtitle {\n\t\tfont-size: 28rpx;\n\t\topacity: 0.9;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.timing-explanation {\n\t\tbackground: rgba(255, 255, 255, 0.15);\n\t\tborder-radius: 12rpx;\n\t\tpadding: 16rpx 20rpx;\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.explanation-text {\n\t\tfont-size: 24rpx;\n\t\topacity: 0.95;\n\t\tline-height: 1.4;\n\t}\n\n\t.stats-section {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.stats-grid {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(4, 1fr);\n\t\tgap: 20rpx;\n\t}\n\n\t.stat-card {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx 20rpx;\n\t\ttext-align: center;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t}\n\t}\n\n\t.stat-number {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\n\t\t&.assigned { color: #20B2AA; } /* 浅海绿色 - 执行中 */\n\t\t&.pending { color: #FF8C00; } /* 深橙色 - 待确认 */\n\t\t&.completed { color: #228B22; } /* 森林绿 - 已完成 */\n\t\t&.overdue { color: #DC143C; } /* 深红色 - 超时 */\n\t}\n\n\t.stat-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t.filter-tabs {\n\t\tdisplay: flex;\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 8rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t}\n\n\t.filter-tab {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t\tpadding: 20rpx 10rpx;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\ttransition: all 0.3s ease;\n\n\t\t&.active {\n\t\t\tbackground: #2E8B57; /* 海绿色 - 环保主题 */\n\t\t\tcolor: white;\n\t\t}\n\t}\n\n\t.task-list {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.task-item {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: translateY(2rpx);\n\t\t}\n\t}\n\n\t.task-header {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.task-title-row {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.task-name {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tflex: 1;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.task-status {\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: white;\n\n\t\t&.status-assigned { background: #20B2AA; } /* 浅海绿色 - 执行中 */\n\t\t&.status-pending { background: #FF8C00; } /* 深橙色 - 待确认 */\n\t\t&.status-completed { background: #228B22; } /* 森林绿 - 已完成 */\n\t}\n\n\t.task-project {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t.task-content {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.task-description {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tline-height: 1.6;\n\t}\n\n\t.task-footer {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.responsible-info, .time-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-wrap: wrap;\n\t\tgap: 8rpx;\n\t}\n\n\t.responsible-label, .time-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-right: 8rpx;\n\t}\n\n\t.responsible-name, .time-value {\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\n\t\t&.overdue {\n\t\t\tcolor: #DC143C; /* 深红色 - 超时 */\n\t\t\tfont-weight: bold;\n\t\t}\n\n\t\t&.warning {\n\t\t\tcolor: #FF8C00; /* 深橙色 - 警告 */\n\t\t\tfont-weight: bold;\n\t\t}\n\t}\n\n\t.timing-badge {\n\t\tmargin-left: 8rpx;\n\t}\n\n\t.timing-tag {\n\t\tfont-size: 20rpx;\n\t\tpadding: 4rpx 8rpx;\n\t\tborder-radius: 8rpx;\n\t\tcolor: white;\n\t\tfont-weight: bold;\n\t}\n\n\t.overdue-tag {\n\t\tbackground: #DC143C; /* 深红色 - 超时 */\n\t}\n\n\t.warning-tag {\n\t\tbackground: #FF8C00; /* 深橙色 - 警告 */\n\t}\n\n\t.normal-tag {\n\t\tbackground: #228B22; /* 森林绿 - 正常 */\n\t}\n\n\t.quick-actions {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t}\n\n\t.action-btn {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 20rpx;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: white;\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t}\n\n\t\ttext {\n\t\t\tmargin-left: 8rpx;\n\t\t}\n\t}\n\n\t.confirm-btn {\n\t\tbackground: #4caf50;\n\t}\n\n\t.reject-btn {\n\t\tbackground: #f44336;\n\t}\n\n\t.empty-state {\n\t\ttext-align: center;\n\t\tpadding: 100rpx 40rpx;\n\t}\n\n\t.empty-image {\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tmargin-bottom: 30rpx;\n\t\topacity: 0.6;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\n\t.loading-state {\n\t\tpadding: 40rpx;\n\t}\n\n\t.custom-loading {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 40rpx;\n\t}\n\n\t.loading-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=style&index=0&id=1f55f9ed&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=style&index=0&id=1f55f9ed&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752560778620\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?db4e", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?4cc1", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?ddfa", "uni-app:///pages/ucenter_pkg/gm-supervision.vue", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?8e7a", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?512b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "taskList", "taskStats", "assigned", "pending", "completed", "overdue", "currentFilter", "loading", "loadingStatus", "responsibleUsers", "currentUserId", "userRole", "showModal", "modalInput", "modalData", "title", "label", "placeholder", "confirmText", "callback", "taskId", "lastRefreshTime", "computed", "filteredTaskList", "onLoad", "console", "onShow", "uni", "onHide", "onPullDownRefresh", "methods", "checkPermission", "content", "showCancel", "success", "getUserInfo", "userInfo", "db", "where", "field", "get", "result", "loadTaskData", "silent", "uniCloud", "name", "action", "res", "taskCount", "stats", "responsibleUserCount", "icon", "duration", "setFilter", "filterByStatus", "getStatusText", "getResponsibleName", "getTimeLabel", "getTimeValue", "timestamp", "isOverdue", "isWarning", "handleTaskUpdate", "handleCrossDeviceUpdate", "shouldRefreshOnCrossDeviceUpdate", "silentRefreshData", "goToTaskDetail", "url", "quickConfirm", "quickReject", "confirmTask", "id", "reason", "rejectTask", "workflowStatus", "rejectReason", "getEmptyText", "showCustomModal", "closeModal", "confirmModal", "handleInputFocus"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAwmB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACoK5nB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;MACA;QACA;UAAA;QAAA;MACA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cAAA;cAAA,OACA;YAAA;cACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACA;IACA;;IAEA;IACAC;IACAA;IACA;IACAA;EACA;EAEAC;IACA;IACAD;IACAA;IACAA;EACA;EACAE;IACA;MACAF;IACA;EACA;EACAG;IACA;IACAC;MACAN;QACAf;QACAC;MACA;;MAEA;MACA;QACAgB;UACAZ;UACAiB;UACAC;UACAC;YACAP;UACA;QACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QACAA;UACAZ;UACAiB;UACAC;UACAC;YACAP;UACA;QACA;QACA;MACA;MAEAF;MACA;IACA;IAEA;IACAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBACA;gBAEAX;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA;cAAA;gBAIA;gBACAY;gBAAA;gBAAA,OACAA,8BACAC,gCACAC,mBACAC;cAAA;gBAAA;gBAHAC;gBAKA;kBACA;kBACAhB;gBACA;kBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiB;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBACA;kBACA;kBACA;gBACA;gBAEAlB;gBAAA;gBAAA;gBAAA,OAGAmB;kBACAC;kBACA9C;oBACA+C;kBACA;gBACA;cAAA;gBALAC;gBAOAtB;gBAAA,MAEAsB;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAEAtB;kBACAuB;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAEAzB;gBAAA,MACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACAE;kBACAZ;kBACAoC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;kBACA;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACArC;MACA;IACA;IAEA;IACAsC;MACA;QACA;QACA;QACA;UACAtC;UACA;UACA;QACA;MACA;IACA;IAEA;IACAuC;MACA;MACA,qBACA,2BACA,sBACA,oBACA,gBACA;;MAEA;MACA,4CACAjE;QAAA;MAAA;;MAEA;MACA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAkE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAxC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyC;MACAvC;QACAwC;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACArD;QACAE;QACAC;QACAC;UACA;QACA;MACA;IACA;IAEA;IACAkD;MAAA;MACA;QACAtD;QACAE;QACAC;QACAC;UACA;QACA;MACA;IACA;IAEA;IACAmD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA3C;kBAAAZ;gBAAA;gBAAA;gBAAA,OAEA6B;kBACAC;kBACA9C;oBACA+C;oBACAyB;oBACAC;kBACA;gBACA;cAAA;gBAPAzB;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACApB;kBACAZ;kBACAoC;gBACA;;gBAEA;gBACAxB;gBACAA;kBAAA4C;gBAAA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA9C;gBACAE;kBACAZ;kBACAoC;gBACA;cAAA;gBAAA;gBAEAxB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA9C;kBAAAZ;gBAAA;gBAAA;gBAAA,OAEA6B;kBACAC;kBACA9C;oBACA+C;oBACAyB;oBACAG;oBACAC;kBACA;gBACA;cAAA;gBARA5B;gBAAA,MAUAA;kBAAA;kBAAA;gBAAA;gBACApB;kBACAZ;kBACAoC;gBACA;;gBAEA;gBACAxB;gBACAA;kBAAA4C;gBAAA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA9C;gBACAE;kBACAZ;kBACAoC;gBACA;cAAA;gBAAA;gBAEAxB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiD;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA9D;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;IACA;IAEA;IACA2D;MACA;MACA;MACA;QACA/D;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACA4D;MACA;MACA;QACApD;UACAZ;UACAoC;QACA;QACA;MACA;MAEA;QACA;MACA;MACA;IACA;IAEA;IACA6B;MACA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACppBA;AAAA;AAAA;AAAA;AAA2qC,CAAgB,ipCAAG,EAAC,C;;;;;;;;;;;ACA/rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ucenter_pkg/gm-supervision.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/ucenter_pkg/gm-supervision.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./gm-supervision.vue?vue&type=template&id=1f55f9ed&scoped=true&\"\nvar renderjs\nimport script from \"./gm-supervision.vue?vue&type=script&lang=js&\"\nexport * from \"./gm-supervision.vue?vue&type=script&lang=js&\"\nimport style0 from \"./gm-supervision.vue?vue&type=style&index=0&id=1f55f9ed&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1f55f9ed\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ucenter_pkg/gm-supervision.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=template&id=1f55f9ed&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.filteredTaskList.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.filteredTaskList, function (task, index) {\n          var $orig = _vm.__get_orig(task)\n          var m0 = _vm.getStatusText(task.workflowStatus)\n          var m1 = _vm.getResponsibleName(task.responsibleUserId)\n          var m2 = _vm.getTimeLabel(task)\n          var m3 = _vm.isOverdue(task)\n          var m4 = _vm.isWarning(task)\n          var m5 = _vm.getTimeValue(task)\n          var m6 =\n            task.workflowStatus === \"assigned_to_responsible\"\n              ? _vm.isOverdue(task)\n              : null\n          var m7 =\n            task.workflowStatus === \"assigned_to_responsible\" && !m6\n              ? _vm.isWarning(task)\n              : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n            m6: m6,\n            m7: m7,\n          }\n        })\n      : null\n  var m8 = !(g0 > 0) ? _vm.getEmptyText() : null\n  var g1 = _vm.showModal ? _vm.modalInput.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        m8: m8,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 页面标题 -->\n\t\t<view class=\"page-header\">\n\t\t\t<text class=\"page-title\">指派任务监督</text>\n\t\t\t<text class=\"page-subtitle\">实时监控指派任务的执行情况</text>\n\t\t\t<view class=\"timing-explanation\">\n\t\t\t\t<text class=\"explanation-text\">💡 超时说明：执行中任务超过7天为警告，超过14天为超时</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 统计卡片区域 -->\n\t\t<view class=\"stats-section\">\n\t\t\t<view class=\"stats-grid\">\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('assigned_to_responsible')\">\n\t\t\t\t\t<view class=\"stat-number assigned\">{{ taskStats.assigned || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">执行中</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('completed_by_responsible')\">\n\t\t\t\t\t<view class=\"stat-number pending\">{{ taskStats.pending || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">待确认</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('final_completed')\">\n\t\t\t\t\t<view class=\"stat-number completed\">{{ taskStats.completed || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">已完成</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('overdue')\">\n\t\t\t\t\t<view class=\"stat-number overdue\">{{ taskStats.overdue || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">已超时</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 筛选标签 -->\n\t\t<view class=\"filter-tabs\">\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'all' }\"\n\t\t\t\t@click=\"setFilter('all')\">\n\t\t\t\t全部\n\t\t\t</view>\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'assigned_to_responsible' }\"\n\t\t\t\t@click=\"setFilter('assigned_to_responsible')\">\n\t\t\t\t执行中\n\t\t\t</view>\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'completed_by_responsible' }\"\n\t\t\t\t@click=\"setFilter('completed_by_responsible')\">\n\t\t\t\t待确认\n\t\t\t</view>\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'overdue' }\"\n\t\t\t\t@click=\"setFilter('overdue')\">\n\t\t\t\t已超时\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 任务列表 -->\n\t\t<view class=\"task-list\" v-if=\"filteredTaskList.length > 0\">\n\t\t\t<view \n\t\t\t\tclass=\"task-item\" \n\t\t\t\tv-for=\"(task, index) in filteredTaskList\" \n\t\t\t\t:key=\"task._id\"\n\t\t\t\t@click=\"goToTaskDetail(task)\">\n\t\t\t\t\n\t\t\t\t<view class=\"task-header\">\n\t\t\t\t\t<view class=\"task-title-row\">\n\t\t\t\t\t\t<text class=\"task-name\">{{ task.name }}</text>\n\t\t\t\t\t\t<view class=\"task-status\"\n\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t'status-assigned': task.workflowStatus === 'assigned_to_responsible',\n\t\t\t\t\t\t\t\t'status-pending': task.workflowStatus === 'completed_by_responsible',\n\t\t\t\t\t\t\t\t'status-completed': task.workflowStatus === 'final_completed'\n\t\t\t\t\t\t\t}\">\n\t\t\t\t\t\t\t{{ getStatusText(task.workflowStatus) }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"task-project\">{{ task.project || '未分类' }}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"task-content\">\n\t\t\t\t\t<text class=\"task-description\">{{ task.description || '暂无描述' }}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"task-footer\">\n\t\t\t\t\t<view class=\"responsible-info\">\n\t\t\t\t\t\t<text class=\"responsible-label\">负责人：</text>\n\t\t\t\t\t\t<text class=\"responsible-name\">{{ getResponsibleName(task.responsibleUserId) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"time-info\">\n\t\t\t\t\t\t<text class=\"time-label\">{{ getTimeLabel(task) }}</text>\n\t\t\t\t\t\t<text class=\"time-value\" :class=\"{\n\t\t\t\t\t\t\toverdue: isOverdue(task),\n\t\t\t\t\t\t\twarning: isWarning(task)\n\t\t\t\t\t\t}\">\n\t\t\t\t\t\t\t{{ getTimeValue(task) }}\n\t\t\t\t\t\t</text>\n\t\t\t\t\t\t<!-- 时效提醒标签 -->\n\t\t\t\t\t\t<view class=\"timing-badge\" v-if=\"task.workflowStatus === 'assigned_to_responsible'\">\n\t\t\t\t\t\t\t<text v-if=\"isOverdue(task)\" class=\"timing-tag overdue-tag\">超时</text>\n\t\t\t\t\t\t\t<text v-else-if=\"isWarning(task)\" class=\"timing-tag warning-tag\">警告</text>\n\t\t\t\t\t\t\t<text v-else class=\"timing-tag normal-tag\">正常</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 快速操作按钮 -->\n\t\t\t\t<view class=\"quick-actions\" v-if=\"task.workflowStatus === 'completed_by_responsible'\">\n\t\t\t\t\t<view class=\"action-btn confirm-btn\" @click.stop=\"quickConfirm(task)\">\n\t\t\t\t\t\t<text>确认完成</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn reject-btn\" @click.stop=\"quickReject(task)\">\n\t\t\t\t\t\t<text>退回重做</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 空状态 -->\n\t\t<view class=\"empty-state\" v-else>\n\t\t\t<view class=\"empty-content\">\n\t\t\t\t<image class=\"empty-image\" src=\"/static/empty/empty_task.png\" mode=\"aspectFit\"></image>\n\t\t\t\t<text class=\"empty-text\">{{ getEmptyText() }}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 加载状态 -->\n\t\t<view class=\"loading-state\" v-if=\"loading\">\n\t\t\t<view class=\"custom-loading\">\n\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 自定义弹窗 -->\n\t\t<view class=\"custom-modal\" v-if=\"showModal\" @click=\"closeModal\">\n\t\t\t<view class=\"modal-content\" @click.stop>\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">{{ modalData.title }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-body\">\n\t\t\t\t\t<textarea\n\t\t\t\t\t\tclass=\"modal-input\"\n\t\t\t\t\t\tv-model=\"modalInput\"\n\t\t\t\t\t\t:placeholder=\"modalData.placeholder\"\n\t\t\t\t\t\t:maxlength=\"200\"\n\t\t\t\t\t\tauto-height\n\t\t\t\t\t\t@focus=\"handleInputFocus\"\n\t\t\t\t\t></textarea>\n\t\t\t\t\t<view class=\"input-counter\">{{ modalInput.length }}/200</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<view class=\"modal-btn cancel-btn\" @click=\"closeModal\">取消</view>\n\t\t\t\t\t<view class=\"modal-btn confirm-btn\" @click=\"confirmModal\">{{ modalData.confirmText }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { formatDate } from '@/utils/date.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttaskList: [],\n\t\t\t\ttaskStats: {\n\t\t\t\t\tassigned: 0,\n\t\t\t\t\tpending: 0,\n\t\t\t\t\tcompleted: 0,\n\t\t\t\t\toverdue: 0\n\t\t\t\t},\n\t\t\t\tcurrentFilter: 'all',\n\t\t\t\tloading: false,\n\t\t\t\tloadingStatus: 'more',\n\t\t\t\tresponsibleUsers: {}, // 负责人信息缓存\n\t\t\t\tcurrentUserId: '', // 当前用户ID\n\t\t\t\tuserRole: [], // 用户角色\n\t\t\t\t// 自定义弹窗数据\n\t\t\t\tshowModal: false,\n\t\t\t\tmodalInput: '',\n\t\t\t\tmodalData: {\n\t\t\t\t\ttitle: '',\n\t\t\t\t\tlabel: '',\n\t\t\t\t\tplaceholder: '',\n\t\t\t\t\tconfirmText: '确认',\n\t\t\t\t\tcallback: null,\n\t\t\t\t\ttaskId: null\n\t\t\t\t},\n\t\t\t\t// 跨设备更新相关\n\t\t\t\tlastRefreshTime: null\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tfilteredTaskList() {\n\t\t\t\tif (this.currentFilter === 'all') {\n\t\t\t\t\treturn this.taskList;\n\t\t\t\t}\n\t\t\t\tif (this.currentFilter === 'overdue') {\n\t\t\t\t\treturn this.taskList.filter(task => this.isOverdue(task));\n\t\t\t\t}\n\t\t\t\treturn this.taskList.filter(task => task.workflowStatus === this.currentFilter);\n\t\t\t}\n\t\t},\n\t\tasync onLoad() {\n\t\t\tconsole.log('📱 厂长监督页面加载');\n\t\t\tawait this.getUserInfo();\n\t\t\tif (this.checkPermission()) {\n\t\t\t\tthis.loadTaskData();\n\t\t\t}\n\t\t},\n\t\tonShow() {\n\t\t\t// 页面显示时刷新数据\n\t\t\tthis.loadTaskData();\n\n\t\t\t// 监听任务状态更新事件\n\t\t\tuni.$on('feedback-updated', this.handleTaskUpdate);\n\t\t\tuni.$on('ucenter-need-refresh', this.handleTaskUpdate);\n\t\t\t// 监听跨设备更新事件\n\t\t\tuni.$on('cross-device-update-detected', this.handleCrossDeviceUpdate);\n\t\t},\n\n\t\tonHide() {\n\t\t\t// 页面隐藏时移除事件监听\n\t\t\tuni.$off('feedback-updated', this.handleTaskUpdate);\n\t\t\tuni.$off('ucenter-need-refresh', this.handleTaskUpdate);\n\t\t\tuni.$off('cross-device-update-detected', this.handleCrossDeviceUpdate);\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.loadTaskData().then(() => {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t});\n\t\t},\n\t\tmethods: {\n\t\t\t// 权限检查\n\t\t\tcheckPermission() {\n\t\t\t\tconsole.log('📋 权限检查 - 当前用户信息:', {\n\t\t\t\t\tcurrentUserId: this.currentUserId,\n\t\t\t\t\tuserRole: this.userRole\n\t\t\t\t});\n\n\t\t\t\t// 检查是否登录\n\t\t\t\tif (!this.currentUserId) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\t\tcontent: '需要登录后才能访问此页面',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\t// 检查是否有厂长或管理员权限\n\t\t\t\tconst hasGMPermission = this.userRole.some(role => ['GM', 'admin'].includes(role));\n\t\t\t\tif (!hasGMPermission) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '权限不足',\n\t\t\t\t\t\tcontent: '只有厂长才能访问此页面',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tconsole.log('✅ 权限检查通过');\n\t\t\t\treturn true;\n\t\t\t},\n\n\t\t\t// 获取用户信息\n\t\t\tasync getUserInfo() {\n\t\t\t\ttry {\n\t\t\t\t\t// 获取当前用户ID\n\t\t\t\t\tconst userInfo = uniCloud.getCurrentUserInfo();\n\t\t\t\t\tthis.currentUserId = userInfo.uid;\n\n\t\t\t\t\tconsole.log('📋 获取到用户ID:', this.currentUserId);\n\n\t\t\t\t\tif (!this.currentUserId) {\n\t\t\t\t\t\tconsole.log('❌ 用户未登录');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 获取用户角色\n\t\t\t\t\tconst db = uniCloud.database();\n\t\t\t\t\tconst { result } = await db.collection('uni-id-users')\n\t\t\t\t\t\t.where(\"'_id' == $cloudEnv_uid\")\n\t\t\t\t\t\t.field('role, _id')\n\t\t\t\t\t\t.get();\n\n\t\t\t\t\tif (result.data && result.data.length > 0) {\n\t\t\t\t\t\tthis.userRole = result.data[0].role || [];\n\t\t\t\t\t\tconsole.log('📋 获取到用户角色:', this.userRole);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.userRole = [];\n\t\t\t\t\t\tconsole.log('❌ 未找到用户角色信息');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 获取用户信息失败:', error);\n\t\t\t\t\tthis.currentUserId = '';\n\t\t\t\t\tthis.userRole = [];\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载任务数据\n\t\t\tasync loadTaskData(silent = false) {\n\t\t\t\tif (!silent) {\n\t\t\t\t\tthis.loading = true;\n\t\t\t\t\tthis.loadingStatus = 'loading';\n\t\t\t\t}\n\n\t\t\t\tconsole.log('开始加载厂长监督任务数据...', silent ? '(静默刷新)' : '');\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-list',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'getGMSupervisionTasks'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tconsole.log('云函数调用结果:', res);\n\n\t\t\t\t\tif (res.result && res.result.code === 0) {\n\t\t\t\t\t\tthis.taskList = res.result.data.list || [];\n\t\t\t\t\t\tthis.taskStats = res.result.data.stats || this.taskStats;\n\t\t\t\t\t\tthis.responsibleUsers = res.result.data.responsibleUsers || {};\n\n\t\t\t\t\t\tconsole.log('任务数据加载成功:', {\n\t\t\t\t\t\t\ttaskCount: this.taskList.length,\n\t\t\t\t\t\t\tstats: this.taskStats,\n\t\t\t\t\t\t\tresponsibleUserCount: Object.keys(this.responsibleUsers).length\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('云函数返回错误:', res.result);\n\t\t\t\t\t\tthrow new Error(res.result?.message || '获取任务数据失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载任务数据失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '加载失败',\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tif (!silent) {\n\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\tthis.loadingStatus = 'more';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 设置筛选条件\n\t\t\tsetFilter(filter) {\n\t\t\t\tthis.currentFilter = filter;\n\t\t\t},\n\n\t\t\t// 按状态筛选\n\t\t\tfilterByStatus(status) {\n\t\t\t\tthis.setFilter(status);\n\t\t\t},\n\n\t\t\t// 获取状态文本\n\t\t\tgetStatusText(status) {\n\t\t\t\tconst textMap = {\n\t\t\t\t\t'assigned_to_responsible': '执行中',\n\t\t\t\t\t'completed_by_responsible': '待确认',\n\t\t\t\t\t'final_completed': '已完成'\n\t\t\t\t};\n\t\t\t\treturn textMap[status] || '未知状态';\n\t\t\t},\n\n\t\t\t// 获取负责人姓名\n\t\t\tgetResponsibleName(userId) {\n\t\t\t\tconst user = this.responsibleUsers[userId];\n\t\t\t\treturn user ? (user.nickname || user.username || '未知') : '未指派';\n\t\t\t},\n\n\t\t\t// 获取时间标签\n\t\t\tgetTimeLabel(task) {\n\t\t\t\tif (task.workflowStatus === 'assigned_to_responsible') {\n\t\t\t\t\treturn '指派时间：';\n\t\t\t\t} else if (task.workflowStatus === 'completed_by_responsible') {\n\t\t\t\t\treturn '完成时间：';\n\t\t\t\t} else if (task.workflowStatus === 'final_completed') {\n\t\t\t\t\treturn '确认时间：';\n\t\t\t\t}\n\t\t\t\treturn '创建时间：';\n\t\t\t},\n\n\t\t\t// 获取时间值\n\t\t\tgetTimeValue(task) {\n\t\t\t\tlet timestamp;\n\t\t\t\tif (task.workflowStatus === 'assigned_to_responsible') {\n\t\t\t\t\ttimestamp = task.assignedTime;\n\t\t\t\t} else if (task.workflowStatus === 'completed_by_responsible') {\n\t\t\t\t\ttimestamp = task.completedByResponsibleTime;\n\t\t\t\t} else if (task.workflowStatus === 'final_completed') {\n\t\t\t\t\ttimestamp = task.finalCompletedTime;\n\t\t\t\t} else {\n\t\t\t\t\ttimestamp = task.createTime;\n\t\t\t\t}\n\t\t\t\treturn timestamp ? formatDate(timestamp, 'MM-DD HH:mm') : '未知';\n\t\t\t},\n\n\t\t\t// 判断是否超时（与问题反馈系统保持一致）\n\t\t\tisOverdue(task) {\n\t\t\t\tif (task.workflowStatus !== 'assigned_to_responsible') return false;\n\t\t\t\tif (!task.assignedTime) return false;\n\n\t\t\t\t// 与问题反馈系统保持一致：超过14天视为超时\n\t\t\t\tconst fourteenDays = 14 * 24 * 60 * 60 * 1000;\n\t\t\t\treturn Date.now() - task.assignedTime > fourteenDays;\n\t\t\t},\n\n\t\t\t// 判断是否为警告状态（7-14天）\n\t\t\tisWarning(task) {\n\t\t\t\tif (task.workflowStatus !== 'assigned_to_responsible') return false;\n\t\t\t\tif (!task.assignedTime) return false;\n\n\t\t\t\tconst sevenDays = 7 * 24 * 60 * 60 * 1000;\n\t\t\t\tconst fourteenDays = 14 * 24 * 60 * 60 * 1000;\n\t\t\t\tconst timePassed = Date.now() - task.assignedTime;\n\n\t\t\t\treturn timePassed > sevenDays && timePassed <= fourteenDays;\n\t\t\t},\n\n\t\t\t// 处理任务更新事件\n\t\t\thandleTaskUpdate() {\n\t\t\t\tconsole.log('📱 收到任务更新事件，刷新厂长监督数据');\n\t\t\t\tthis.loadTaskData();\n\t\t\t},\n\n\t\t\t// 处理跨设备更新事件\n\t\t\thandleCrossDeviceUpdate(data) {\n\t\t\t\tif (data.silent && this.currentUserId) {\n\t\t\t\t\t// 智能判断是否需要刷新\n\t\t\t\t\tconst shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);\n\t\t\t\t\tif (shouldRefresh) {\n\t\t\t\t\t\tconsole.log('🏭 厂长监督页面收到跨设备更新通知，静默刷新数据');\n\t\t\t\t\t\t// 静默刷新数据，不显示提示\n\t\t\t\t\t\tthis.silentRefreshData();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 智能判断是否需要刷新\n\t\t\tshouldRefreshOnCrossDeviceUpdate(data) {\n\t\t\t\t// 检查更新类型是否与当前页面相关\n\t\t\t\tconst relevantTypes = [\n\t\t\t\t\t'workflow_status_changed',\n\t\t\t\t\t'feedback_submitted',\n\t\t\t\t\t'gm_final_confirm',\n\t\t\t\t\t'task_assigned'\n\t\t\t\t];\n\n\t\t\t\t// 如果有相关的更新类型，则需要刷新\n\t\t\t\tconst hasRelevantUpdate = data.updateTypes &&\n\t\t\t\t\tdata.updateTypes.some(type => relevantTypes.includes(type));\n\n\t\t\t\t// 避免频繁刷新：如果距离上次刷新不到10秒，则跳过\n\t\t\t\tconst now = Date.now();\n\t\t\t\tif (this.lastRefreshTime && (now - this.lastRefreshTime) < 10000) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\treturn hasRelevantUpdate;\n\t\t\t},\n\n\t\t\t// 静默刷新数据\n\t\t\tasync silentRefreshData() {\n\t\t\t\ttry {\n\t\t\t\t\t// 记录刷新时间\n\t\t\t\t\tthis.lastRefreshTime = Date.now();\n\n\t\t\t\t\t// 静默刷新，不显示loading\n\t\t\t\t\tawait this.loadTaskData(true);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('静默刷新失败:', error);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 跳转到任务详情\n\t\t\tgoToTaskDetail(task) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/feedback_pkg/examine?id=${task._id}`\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 快速确认完成 - 需要输入确认理由\n\t\t\tquickConfirm(task) {\n\t\t\t\tthis.showCustomModal({\n\t\t\t\t\ttitle: '确认完成',\n\t\t\t\t\tplaceholder: '请输入确认意见。',\n\t\t\t\t\tconfirmText: '确认完成',\n\t\t\t\t\tcallback: (reason) => {\n\t\t\t\t\t\tthis.confirmTask(task._id, reason);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 快速退回重做 - 需要输入退回理由\n\t\t\tquickReject(task) {\n\t\t\t\tthis.showCustomModal({\n\t\t\t\t\ttitle: '退回重做',\n\t\t\t\t\tplaceholder: '请详细说明退回原因。',\n\t\t\t\t\tconfirmText: '退回重做',\n\t\t\t\t\tcallback: (reason) => {\n\t\t\t\t\t\tthis.rejectTask(task._id, reason);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 确认任务完成\n\t\t\tasync confirmTask(taskId, reason) {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '确认中...' });\n\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-workflow',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'gm_final_confirm',\n\t\t\t\t\t\t\tid: taskId,\n\t\t\t\t\t\t\treason: reason\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.result && res.result.code === 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '确认成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 触发刷新事件，通知其他页面更新\n\t\t\t\t\t\tuni.$emit('feedback-updated');\n\t\t\t\t\t\tuni.$emit('ucenter-need-refresh', { id: taskId });\n\n\t\t\t\t\t\t// 刷新当前页面数据\n\t\t\t\t\t\tthis.loadTaskData();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(res.result?.message || '确认失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('确认任务失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '确认失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 退回任务重做\n\t\t\tasync rejectTask(taskId, reason) {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '退回中...' });\n\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-workflow',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'updateWorkflowStatus',\n\t\t\t\t\t\t\tid: taskId,\n\t\t\t\t\t\t\tworkflowStatus: 'assigned_to_responsible',\n\t\t\t\t\t\t\trejectReason: reason\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.result && res.result.code === 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '已退回重做',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 触发刷新事件，通知其他页面更新\n\t\t\t\t\t\tuni.$emit('feedback-updated');\n\t\t\t\t\t\tuni.$emit('ucenter-need-refresh', { id: taskId });\n\n\t\t\t\t\t\t// 刷新当前页面数据\n\t\t\t\t\t\tthis.loadTaskData();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(res.result?.message || '退回失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('退回任务失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '退回失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取空状态文本\n\t\t\tgetEmptyText() {\n\t\t\t\tconst textMap = {\n\t\t\t\t\t'all': '暂无指派任务',\n\t\t\t\t\t'assigned_to_responsible': '暂无执行中的任务',\n\t\t\t\t\t'completed_by_responsible': '暂无待确认的任务',\n\t\t\t\t\t'overdue': '暂无超时任务'\n\t\t\t\t};\n\t\t\t\treturn textMap[this.currentFilter] || '暂无数据';\n\t\t\t},\n\n\t\t\t// 显示自定义弹窗\n\t\t\tshowCustomModal(options) {\n\t\t\t\tthis.modalData = {\n\t\t\t\t\ttitle: options.title || '',\n\t\t\t\t\tlabel: options.label || '',\n\t\t\t\t\tplaceholder: options.placeholder || '',\n\t\t\t\t\tconfirmText: options.confirmText || '确认',\n\t\t\t\t\tcallback: options.callback || null\n\t\t\t\t};\n\t\t\t\tthis.modalInput = '';\n\t\t\t\tthis.showModal = true;\n\t\t\t},\n\n\t\t\t// 关闭弹窗\n\t\t\tcloseModal() {\n\t\t\t\tthis.showModal = false;\n\t\t\t\tthis.modalInput = '';\n\t\t\t\tthis.modalData = {\n\t\t\t\t\ttitle: '',\n\t\t\t\t\tlabel: '',\n\t\t\t\t\tplaceholder: '',\n\t\t\t\t\tconfirmText: '确认',\n\t\t\t\t\tcallback: null\n\t\t\t\t};\n\t\t\t},\n\n\t\t\t// 确认弹窗\n\t\t\tconfirmModal() {\n\t\t\t\tconst reason = this.modalInput.trim();\n\t\t\t\tif (!reason) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入内容',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (this.modalData.callback) {\n\t\t\t\t\tthis.modalData.callback(reason);\n\t\t\t\t}\n\t\t\t\tthis.closeModal();\n\t\t\t},\n\n\t\t\t// 输入框获得焦点时清空placeholder样式的内容\n\t\t\thandleInputFocus() {\n\t\t\t\t// 这里可以添加焦点处理逻辑\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f8f9fc;\n\t\tpadding: 20rpx;\n\t}\n\n\t.page-header {\n\t\tbackground: linear-gradient(135deg, #38bdf8 0%, #0ea5e9 70%, #3b82f6 100%); /* 更轻盈的蓝色渐变 - 从浅到深 */\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tcolor: white;\n\t\tbox-shadow: 0 6rpx 24rpx rgba(56, 189, 248, 0.25); /* 更轻的阴影 */\n\t}\n\n\t.page-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.page-subtitle {\n\t\tfont-size: 28rpx;\n\t\topacity: 0.9;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.timing-explanation {\n\t\tbackground: rgba(255, 255, 255, 0.15);\n\t\tborder-radius: 12rpx;\n\t\tpadding: 16rpx 20rpx;\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.explanation-text {\n\t\tfont-size: 24rpx;\n\t\topacity: 0.95;\n\t\tline-height: 1.4;\n\t}\n\n\t.stats-section {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.stats-grid {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(4, 1fr);\n\t\tgap: 20rpx;\n\t}\n\n\t.stat-card {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx 20rpx;\n\t\ttext-align: center;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t}\n\t}\n\n\t.stat-number {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\n\t\t&.assigned { color: #38bdf8; } /* 浅蓝色 - 执行中 */\n\t\t&.pending { color: #f59e0b; } /* 橙色 - 待确认 */\n\t\t&.completed { color: #059669; } /* 绿色 - 已完成 */\n\t\t&.overdue { color: #dc2626; } /* 红色 - 超时 */\n\t}\n\n\t.stat-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t.filter-tabs {\n\t\tdisplay: flex;\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 8rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t}\n\n\t.filter-tab {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t\tpadding: 20rpx 10rpx;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\ttransition: all 0.3s ease;\n\n\t\t&.active {\n\t\t\tbackground: #3b82f6; /* 蓝色 - 水务主题 */\n\t\t\tcolor: white;\n\t\t}\n\t}\n\n\t.task-list {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.task-item {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: translateY(2rpx);\n\t\t}\n\t}\n\n\t.task-header {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.task-title-row {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.task-name {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tflex: 1;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.task-status {\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: white;\n\n\t\t&.status-assigned { background: #38bdf8; } /* 浅蓝色 - 执行中 */\n\t\t&.status-pending { background: #f59e0b; } /* 橙色 - 待确认 */\n\t\t&.status-completed { background: #059669; } /* 绿色 - 已完成 */\n\t}\n\n\t.task-project {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t.task-content {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.task-description {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tline-height: 1.6;\n\t}\n\n\t.task-footer {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.responsible-info, .time-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-wrap: wrap;\n\t\tgap: 8rpx;\n\t}\n\n\t.responsible-label, .time-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-right: 8rpx;\n\t}\n\n\t.responsible-name, .time-value {\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\n\t\t&.overdue {\n\t\t\tcolor: #dc2626; /* 红色 - 超时 */\n\t\t\tfont-weight: bold;\n\t\t}\n\n\t\t&.warning {\n\t\t\tcolor: #f59e0b; /* 橙色 - 警告 */\n\t\t\tfont-weight: bold;\n\t\t}\n\t}\n\n\t.timing-badge {\n\t\tmargin-left: 8rpx;\n\t}\n\n\t.timing-tag {\n\t\tfont-size: 20rpx;\n\t\tpadding: 4rpx 8rpx;\n\t\tborder-radius: 8rpx;\n\t\tcolor: white;\n\t\tfont-weight: bold;\n\t}\n\n\t.overdue-tag {\n\t\tbackground: #dc2626; /* 红色 - 超时 */\n\t}\n\n\t.warning-tag {\n\t\tbackground: #f59e0b; /* 橙色 - 警告 */\n\t}\n\n\t.normal-tag {\n\t\tbackground: #059669; /* 绿色 - 正常 */\n\t}\n\n\t.quick-actions {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t}\n\n\t.action-btn {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 24rpx 20rpx;\n\t\tborder-radius: 12rpx;\n\t\tcolor: white;\n\t\ttransition: all 0.3s ease;\n\t\tfont-weight: 500;\n\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t\topacity: 0.8;\n\t\t}\n\n\t\ttext {\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: white; /* 确保文字是白色 */\n\t\t}\n\t}\n\n\t.confirm-btn {\n\t\tbackground-color: #4caf50; /* 纯绿色 - 确认完成 */\n\t}\n\n\t.reject-btn {\n\t\tbackground-color: #f44336; /* 纯红色 - 退回重做 */\n\t}\n\n\t.empty-state {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 100rpx 40rpx;\n\t\tmin-height: 400rpx;\n\t}\n\n\t.empty-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttext-align: center;\n\t}\n\n\t.empty-image {\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tmargin-bottom: 30rpx;\n\t\topacity: 0.6;\n\t\tdisplay: block;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t\tdisplay: block;\n\t\ttext-align: center;\n\t}\n\n\t.loading-state {\n\t\tpadding: 40rpx;\n\t}\n\n\t.custom-loading {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 40rpx;\n\t}\n\n\t.loading-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\n\t/* 自定义弹窗样式 */\n\t.custom-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 9999;\n\t}\n\n\t.modal-content {\n\t\twidth: 80%;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 16rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);\n\t\tanimation: modal-in 0.3s ease-out;\n\t}\n\n\t@keyframes modal-in {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(20rpx) scale(0.95);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0) scale(1);\n\t\t}\n\t}\n\n\t.modal-header {\n\t\tpadding: 30rpx;\n\t\ttext-align: center;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\n\t.modal-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333; /* 深灰色 - 简洁清晰 */\n\t}\n\n\t.modal-body {\n\t\tpadding: 30rpx;\n\t}\n\n\t.modal-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t\tdisplay: block;\n\t}\n\n\t.modal-input {\n\t\twidth: 100%;\n\t\tmin-height: 160rpx;\n\t\tborder: 1px solid #e0e0e0;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 20rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tbackground-color: #f9f9f9;\n\t\tbox-sizing: border-box;\n\t\ttransition: border-color 0.3s ease;\n\n\t\t&:focus {\n\t\t\tborder-color: #3b82f6;\n\t\t\tbackground-color: #ffffff;\n\t\t}\n\t}\n\n\t.input-counter {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\ttext-align: right;\n\t\tmargin-top: 10rpx;\n\t}\n\n\t.modal-footer {\n\t\tdisplay: flex;\n\t\tpadding: 20rpx;\n\t\tgap: 20rpx;\n\t}\n\n\t.modal-btn {\n\t\tflex: 1;\n\t\tpadding: 24rpx;\n\t\ttext-align: center;\n\t\tfont-size: 30rpx;\n\t\tborder-radius: 8rpx;\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t}\n\t}\n\n\t.cancel-btn {\n\t\tcolor: #666;\n\t\tbackground-color: #f5f5f5;\n\t\tborder: 1px solid #e0e0e0;\n\t}\n\n\t.confirm-btn {\n\t\tcolor: white;\n\t\tbackground-color: #4caf50; /* 绿色背景 - 与确认按钮保持一致 */\n\t\tfont-weight: bold;\n\t}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=style&index=0&id=1f55f9ed&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=style&index=0&id=1f55f9ed&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752565164263\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
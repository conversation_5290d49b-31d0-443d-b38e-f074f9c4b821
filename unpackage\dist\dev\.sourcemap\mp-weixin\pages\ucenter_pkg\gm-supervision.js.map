{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?db4e", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?4cc1", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?ddfa", "uni-app:///pages/ucenter_pkg/gm-supervision.vue", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?8e7a", "webpack:///D:/Xwzc/pages/ucenter_pkg/gm-supervision.vue?512b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "taskList", "taskStats", "assigned", "pending", "completed", "overdue", "currentFilter", "loading", "loadingStatus", "responsibleUsers", "computed", "filteredTaskList", "onLoad", "onShow", "onPullDownRefresh", "uni", "methods", "checkPermission", "title", "content", "showCancel", "success", "loadTaskData", "uniCloud", "name", "action", "res", "console", "icon", "setFilter", "filterByStatus", "getStatusClass", "getStatusText", "getResponsibleName", "getTimeLabel", "getTimeValue", "timestamp", "isOverdue", "goToTaskDetail", "url", "quickConfirm", "confirmText", "cancelText", "quickReject", "confirmTask", "id", "reason", "getEmptyText"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAwmB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACyH5nB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACAC;MAAA;MACA;QACA;MACA;MACA;QACA;UAAA;QAAA;MACA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAF;UACAG;UACAC;UACAC;UACAC;YACAN;UACA;QACA;MACA;IACA;IAEA;IACAO;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAzB;oBACA0B;kBACA;gBACA;cAAA;gBALAC;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACAZ;kBACAG;kBACAU;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACAvB;QACAwB;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAzB;kBACAG;kBACAC;kBACAsB;kBACAC;gBACA;cAAA;gBALAhB;gBAAA,KAOAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA5B;kBACAG;kBACAC;kBACAsB;kBACAC;gBACA;cAAA;gBALAhB;gBAOA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA7B;kBAAAG;gBAAA;gBAAA;gBAAA,OAEAK;kBACAC;kBACAzB;oBACA0B;oBACAoB;oBACAC;kBACA;gBACA;cAAA;gBAPApB;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACAX;kBACAG;kBACAU;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAD;gBACAZ;kBACAG;kBACAU;gBACA;cAAA;gBAAA;gBAEAb;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrXA;AAAA;AAAA;AAAA;AAA2qC,CAAgB,ipCAAG,EAAC,C;;;;;;;;;;;ACA/rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ucenter_pkg/gm-supervision.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/ucenter_pkg/gm-supervision.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./gm-supervision.vue?vue&type=template&id=1f55f9ed&scoped=true&\"\nvar renderjs\nimport script from \"./gm-supervision.vue?vue&type=script&lang=js&\"\nexport * from \"./gm-supervision.vue?vue&type=script&lang=js&\"\nimport style0 from \"./gm-supervision.vue?vue&type=style&index=0&id=1f55f9ed&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1f55f9ed\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ucenter_pkg/gm-supervision.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=template&id=1f55f9ed&scoped=true&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 页面标题 -->\n\t\t<view class=\"page-header\">\n\t\t\t<text class=\"page-title\">指派任务监督</text>\n\t\t\t<text class=\"page-subtitle\">实时监控指派任务的执行情况</text>\n\t\t</view>\n\n\t\t<!-- 统计卡片区域 -->\n\t\t<view class=\"stats-section\">\n\t\t\t<view class=\"stats-grid\">\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('assigned_to_responsible')\">\n\t\t\t\t\t<view class=\"stat-number assigned\">{{ taskStats.assigned || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">执行中</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('completed_by_responsible')\">\n\t\t\t\t\t<view class=\"stat-number pending\">{{ taskStats.pending || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">待确认</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('final_completed')\">\n\t\t\t\t\t<view class=\"stat-number completed\">{{ taskStats.completed || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">已完成</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-card\" @click=\"filterByStatus('overdue')\">\n\t\t\t\t\t<view class=\"stat-number overdue\">{{ taskStats.overdue || 0 }}</view>\n\t\t\t\t\t<text class=\"stat-label\">超时</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 筛选标签 -->\n\t\t<view class=\"filter-tabs\">\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'all' }\"\n\t\t\t\t@click=\"setFilter('all')\">\n\t\t\t\t全部\n\t\t\t</view>\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'assigned_to_responsible' }\"\n\t\t\t\t@click=\"setFilter('assigned_to_responsible')\">\n\t\t\t\t执行中\n\t\t\t</view>\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'completed_by_responsible' }\"\n\t\t\t\t@click=\"setFilter('completed_by_responsible')\">\n\t\t\t\t待确认\n\t\t\t</view>\n\t\t\t<view \n\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t:class=\"{ active: currentFilter === 'overdue' }\"\n\t\t\t\t@click=\"setFilter('overdue')\">\n\t\t\t\t超时任务\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 任务列表 -->\n\t\t<view class=\"task-list\" v-if=\"filteredTaskList.length > 0\">\n\t\t\t<view \n\t\t\t\tclass=\"task-item\" \n\t\t\t\tv-for=\"(task, index) in filteredTaskList\" \n\t\t\t\t:key=\"task._id\"\n\t\t\t\t@click=\"goToTaskDetail(task)\">\n\t\t\t\t\n\t\t\t\t<view class=\"task-header\">\n\t\t\t\t\t<view class=\"task-title-row\">\n\t\t\t\t\t\t<text class=\"task-name\">{{ task.name }}</text>\n\t\t\t\t\t\t<view class=\"task-status\" :class=\"getStatusClass(task.workflowStatus)\">\n\t\t\t\t\t\t\t{{ getStatusText(task.workflowStatus) }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"task-project\">{{ task.project || '未分类' }}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"task-content\">\n\t\t\t\t\t<text class=\"task-description\">{{ task.description || '暂无描述' }}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"task-footer\">\n\t\t\t\t\t<view class=\"responsible-info\">\n\t\t\t\t\t\t<text class=\"responsible-label\">负责人：</text>\n\t\t\t\t\t\t<text class=\"responsible-name\">{{ getResponsibleName(task.responsibleUserId) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"time-info\">\n\t\t\t\t\t\t<text class=\"time-label\">{{ getTimeLabel(task) }}</text>\n\t\t\t\t\t\t<text class=\"time-value\" :class=\"{ overdue: isOverdue(task) }\">\n\t\t\t\t\t\t\t{{ getTimeValue(task) }}\n\t\t\t\t\t\t</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 快速操作按钮 -->\n\t\t\t\t<view class=\"quick-actions\" v-if=\"task.workflowStatus === 'completed_by_responsible'\">\n\t\t\t\t\t<view class=\"action-btn confirm-btn\" @click.stop=\"quickConfirm(task)\">\n\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t<text>确认完成</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn reject-btn\" @click.stop=\"quickReject(task)\">\n\t\t\t\t\t\t<uni-icons type=\"closeempty\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t<text>退回重做</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 空状态 -->\n\t\t<view class=\"empty-state\" v-else>\n\t\t\t<image class=\"empty-image\" src=\"/static/empty/empty_task.png\" mode=\"aspectFit\"></image>\n\t\t\t<text class=\"empty-text\">{{ getEmptyText() }}</text>\n\t\t</view>\n\n\t\t<!-- 加载状态 -->\n\t\t<view class=\"loading-state\" v-if=\"loading\">\n\t\t\t<uni-load-more :status=\"loadingStatus\"></uni-load-more>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { formatDate } from '@/utils/date.js';\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttaskList: [],\n\t\t\t\ttaskStats: {\n\t\t\t\t\tassigned: 0,\n\t\t\t\t\tpending: 0,\n\t\t\t\t\tcompleted: 0,\n\t\t\t\t\toverdue: 0\n\t\t\t\t},\n\t\t\t\tcurrentFilter: 'all',\n\t\t\t\tloading: false,\n\t\t\t\tloadingStatus: 'more',\n\t\t\t\tresponsibleUsers: {}, // 负责人信息缓存\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tfilteredTaskList() {\n\t\t\t\tif (this.currentFilter === 'all') {\n\t\t\t\t\treturn this.taskList;\n\t\t\t\t}\n\t\t\t\tif (this.currentFilter === 'overdue') {\n\t\t\t\t\treturn this.taskList.filter(task => this.isOverdue(task));\n\t\t\t\t}\n\t\t\t\treturn this.taskList.filter(task => task.workflowStatus === this.currentFilter);\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.checkPermission();\n\t\t\tthis.loadTaskData();\n\t\t},\n\t\tonShow() {\n\t\t\t// 页面显示时刷新数据\n\t\t\tthis.loadTaskData();\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.loadTaskData().then(() => {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t});\n\t\t},\n\t\tmethods: {\n\t\t\t// 权限检查\n\t\t\tcheckPermission() {\n\t\t\t\tif (!this.uniIDHasRole('GM') && !this.uniIDHasRole('admin')) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '权限不足',\n\t\t\t\t\t\tcontent: '只有厂长才能访问此页面',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载任务数据\n\t\t\tasync loadTaskData() {\n\t\t\t\tthis.loading = true;\n\t\t\t\tthis.loadingStatus = 'loading';\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-list',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'getGMSupervisionTasks'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.result && res.result.code === 0) {\n\t\t\t\t\t\tthis.taskList = res.result.data.list || [];\n\t\t\t\t\t\tthis.taskStats = res.result.data.stats || this.taskStats;\n\t\t\t\t\t\tthis.responsibleUsers = res.result.data.responsibleUsers || {};\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(res.result?.message || '获取任务数据失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载任务数据失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t\tthis.loadingStatus = 'more';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 设置筛选条件\n\t\t\tsetFilter(filter) {\n\t\t\t\tthis.currentFilter = filter;\n\t\t\t},\n\n\t\t\t// 按状态筛选\n\t\t\tfilterByStatus(status) {\n\t\t\t\tthis.setFilter(status);\n\t\t\t},\n\n\t\t\t// 获取状态样式类\n\t\t\tgetStatusClass(status) {\n\t\t\t\tconst classMap = {\n\t\t\t\t\t'assigned_to_responsible': 'status-assigned',\n\t\t\t\t\t'completed_by_responsible': 'status-pending',\n\t\t\t\t\t'final_completed': 'status-completed'\n\t\t\t\t};\n\t\t\t\treturn classMap[status] || 'status-default';\n\t\t\t},\n\n\t\t\t// 获取状态文本\n\t\t\tgetStatusText(status) {\n\t\t\t\tconst textMap = {\n\t\t\t\t\t'assigned_to_responsible': '执行中',\n\t\t\t\t\t'completed_by_responsible': '待确认',\n\t\t\t\t\t'final_completed': '已完成'\n\t\t\t\t};\n\t\t\t\treturn textMap[status] || '未知状态';\n\t\t\t},\n\n\t\t\t// 获取负责人姓名\n\t\t\tgetResponsibleName(userId) {\n\t\t\t\tconst user = this.responsibleUsers[userId];\n\t\t\t\treturn user ? (user.nickname || user.username || '未知') : '未指派';\n\t\t\t},\n\n\t\t\t// 获取时间标签\n\t\t\tgetTimeLabel(task) {\n\t\t\t\tif (task.workflowStatus === 'assigned_to_responsible') {\n\t\t\t\t\treturn '指派时间：';\n\t\t\t\t} else if (task.workflowStatus === 'completed_by_responsible') {\n\t\t\t\t\treturn '完成时间：';\n\t\t\t\t} else if (task.workflowStatus === 'final_completed') {\n\t\t\t\t\treturn '确认时间：';\n\t\t\t\t}\n\t\t\t\treturn '创建时间：';\n\t\t\t},\n\n\t\t\t// 获取时间值\n\t\t\tgetTimeValue(task) {\n\t\t\t\tlet timestamp;\n\t\t\t\tif (task.workflowStatus === 'assigned_to_responsible') {\n\t\t\t\t\ttimestamp = task.assignedTime;\n\t\t\t\t} else if (task.workflowStatus === 'completed_by_responsible') {\n\t\t\t\t\ttimestamp = task.completedByResponsibleTime;\n\t\t\t\t} else if (task.workflowStatus === 'final_completed') {\n\t\t\t\t\ttimestamp = task.finalCompletedTime;\n\t\t\t\t} else {\n\t\t\t\t\ttimestamp = task.createTime;\n\t\t\t\t}\n\t\t\t\treturn timestamp ? formatDate(timestamp, 'MM-DD HH:mm') : '未知';\n\t\t\t},\n\n\t\t\t// 判断是否超时\n\t\t\tisOverdue(task) {\n\t\t\t\tif (task.workflowStatus !== 'assigned_to_responsible') return false;\n\t\t\t\tif (!task.assignedTime) return false;\n\t\t\t\t\n\t\t\t\t// 超过3天未完成视为超时\n\t\t\t\tconst threeDays = 3 * 24 * 60 * 60 * 1000;\n\t\t\t\treturn Date.now() - task.assignedTime > threeDays;\n\t\t\t},\n\n\t\t\t// 跳转到任务详情\n\t\t\tgoToTaskDetail(task) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/feedback_pkg/examine?id=${task._id}`\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 快速确认完成\n\t\t\tasync quickConfirm(task) {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await uni.showModal({\n\t\t\t\t\t\ttitle: '确认完成',\n\t\t\t\t\t\tcontent: '确认该任务已完成？',\n\t\t\t\t\t\tconfirmText: '确认',\n\t\t\t\t\t\tcancelText: '取消'\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tawait this.confirmTask(task._id, '厂长确认任务完成');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('快速确认失败:', error);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 快速退回重做\n\t\t\tasync quickReject(task) {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await uni.showModal({\n\t\t\t\t\t\ttitle: '退回重做',\n\t\t\t\t\t\tcontent: '确认退回该任务重新执行？',\n\t\t\t\t\t\tconfirmText: '退回',\n\t\t\t\t\t\tcancelText: '取消'\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// 跳转到详情页面进行详细的退回操作\n\t\t\t\t\t\tthis.goToTaskDetail(task);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('快速退回失败:', error);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 确认任务完成\n\t\t\tasync confirmTask(taskId, reason) {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '处理中...' });\n\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-workflow',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'gm_final_confirm',\n\t\t\t\t\t\t\tid: taskId,\n\t\t\t\t\t\t\treason: reason\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.result && res.result.code === 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '确认成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\t// 刷新数据\n\t\t\t\t\t\tthis.loadTaskData();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(res.result?.message || '确认失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('确认任务失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '确认失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取空状态文本\n\t\t\tgetEmptyText() {\n\t\t\t\tconst textMap = {\n\t\t\t\t\t'all': '暂无指派任务',\n\t\t\t\t\t'assigned_to_responsible': '暂无执行中的任务',\n\t\t\t\t\t'completed_by_responsible': '暂无待确认的任务',\n\t\t\t\t\t'overdue': '暂无超时任务'\n\t\t\t\t};\n\t\t\t\treturn textMap[this.currentFilter] || '暂无数据';\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f8f9fc;\n\t\tpadding: 20rpx;\n\t}\n\n\t.page-header {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tcolor: white;\n\t}\n\n\t.page-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.page-subtitle {\n\t\tfont-size: 28rpx;\n\t\topacity: 0.9;\n\t}\n\n\t.stats-section {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.stats-grid {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(4, 1fr);\n\t\tgap: 20rpx;\n\t}\n\n\t.stat-card {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx 20rpx;\n\t\ttext-align: center;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t}\n\t}\n\n\t.stat-number {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\n\t\t&.assigned { color: #2196f3; }\n\t\t&.pending { color: #ff9800; }\n\t\t&.completed { color: #4caf50; }\n\t\t&.overdue { color: #f44336; }\n\t}\n\n\t.stat-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t.filter-tabs {\n\t\tdisplay: flex;\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 8rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t}\n\n\t.filter-tab {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t\tpadding: 20rpx 10rpx;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\ttransition: all 0.3s ease;\n\n\t\t&.active {\n\t\t\tbackground: #667eea;\n\t\t\tcolor: white;\n\t\t}\n\t}\n\n\t.task-list {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.task-item {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: translateY(2rpx);\n\t\t}\n\t}\n\n\t.task-header {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.task-title-row {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.task-name {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tflex: 1;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.task-status {\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: white;\n\n\t\t&.status-assigned { background: #2196f3; }\n\t\t&.status-pending { background: #ff9800; }\n\t\t&.status-completed { background: #4caf50; }\n\t}\n\n\t.task-project {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t.task-content {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.task-description {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tline-height: 1.6;\n\t}\n\n\t.task-footer {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.responsible-info, .time-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.responsible-label, .time-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-right: 8rpx;\n\t}\n\n\t.responsible-name, .time-value {\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\n\t\t&.overdue {\n\t\t\tcolor: #f44336;\n\t\t}\n\t}\n\n\t.quick-actions {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t}\n\n\t.action-btn {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 20rpx;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: white;\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t}\n\n\t\ttext {\n\t\t\tmargin-left: 8rpx;\n\t\t}\n\t}\n\n\t.confirm-btn {\n\t\tbackground: #4caf50;\n\t}\n\n\t.reject-btn {\n\t\tbackground: #f44336;\n\t}\n\n\t.empty-state {\n\t\ttext-align: center;\n\t\tpadding: 100rpx 40rpx;\n\t}\n\n\t.empty-image {\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tmargin-bottom: 30rpx;\n\t\topacity: 0.6;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\n\t.loading-state {\n\t\tpadding: 40rpx;\n\t}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=style&index=0&id=1f55f9ed&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./gm-supervision.vue?vue&type=style&index=0&id=1f55f9ed&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558555689\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
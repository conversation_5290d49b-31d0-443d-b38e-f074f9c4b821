require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol_pkg/record/route-detail"],{

/***/ 459:
/*!*****************************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fpatrol_pkg%2Frecord%2Froute-detail"} ***!
  \*****************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _routeDetail = _interopRequireDefault(__webpack_require__(/*! ./pages/patrol_pkg/record/route-detail.vue */ 460));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_routeDetail.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 460:
/*!********************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/record/route-detail.vue ***!
  \********************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _route_detail_vue_vue_type_template_id_1dd827d6___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./route-detail.vue?vue&type=template&id=1dd827d6& */ 461);
/* harmony import */ var _route_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./route-detail.vue?vue&type=script&lang=js& */ 463);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _route_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _route_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _route_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./route-detail.vue?vue&type=style&index=0&lang=scss& */ 465);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _route_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _route_detail_vue_vue_type_template_id_1dd827d6___WEBPACK_IMPORTED_MODULE_0__["render"],
  _route_detail_vue_vue_type_template_id_1dd827d6___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _route_detail_vue_vue_type_template_id_1dd827d6___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/patrol_pkg/record/route-detail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 461:
/*!***************************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?vue&type=template&id=1dd827d6& ***!
  \***************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_template_id_1dd827d6___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./route-detail.vue?vue&type=template&id=1dd827d6& */ 462);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_template_id_1dd827d6___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_template_id_1dd827d6___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_template_id_1dd827d6___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_template_id_1dd827d6___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 462:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?vue&type=template&id=1dd827d6& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 483))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.formatTotalTime(_vm.routeStats.total_checkin_time)
  var g0 = !_vm.isInitialLoading
    ? _vm.recordList.length === 0 && !_vm.loading
    : null
  var l0 =
    !_vm.isInitialLoading && !g0
      ? _vm.__map(_vm.recordList, function (record, __i2__) {
          var $orig = _vm.__get_orig(record)
          var m1 = _vm.getStatusText(record.status)
          var g1 = record.photos && record.photos.length > 0
          var g2 = g1 ? record.photos.length : null
          return {
            $orig: $orig,
            m1: m1,
            g1: g1,
            g2: g2,
          }
        })
      : null
  var g3 = _vm.loading && _vm.recordList.length > 0
  var g4 = !_vm.hasMore && _vm.recordList.length > 0
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        g0: g0,
        l0: l0,
        g3: g3,
        g4: g4,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 463:
/*!*********************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./route-detail.vue?vue&type=script&lang=js& */ 464);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 464:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! @/utils/patrol-api.js */ 71));
var _vuex = __webpack_require__(/*! vuex */ 53);
var _date = __webpack_require__(/*! @/utils/date.js */ 72);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var PEmptyState = function PEmptyState() {
  __webpack_require__.e(/*! require.ensure | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then((function () {
    return resolve(__webpack_require__(/*! @/components/p-empty-state/p-empty-state.vue */ 483));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    PEmptyState: PEmptyState
  },
  data: function data() {
    return {
      routeId: '',
      routeName: '线路记录',
      recordList: [],
      loading: false,
      refreshing: false,
      page: 1,
      pageSize: 50,
      hasMore: true,
      filterParams: {
        status: '',
        // 空字符串表示全部
        route_id: ''
      },
      statusOptions: [{
        value: '',
        label: '全部'
      }, {
        value: 1,
        label: '已打卡'
      }, {
        value: 3,
        label: '缺卡'
      }, {
        value: 0,
        label: '未打卡'
      }],
      routeStats: {
        point_count: 0,
        completion_rate: 0,
        normal_count: 0,
        missed_count: 0,
        not_checked_count: 0,
        total_checkin_time: 0 // 添加总用时字段
      },

      shift_info: {},
      availableRounds: [],
      // 可用的轮次列表
      currentRound: undefined,
      // 当前选中的轮次，undefined表示未初始化
      taskDetail: null,
      // 保存完整的任务详情
      executorName: '',
      // 新增执行者名称
      showSortDropdown: false,
      // 控制下拉菜单显示
      currentSort: 'point',
      // 默认按点位顺序
      sortOptions: [{
        value: 'point',
        label: '点位顺序'
      }, {
        value: 'checkin',
        label: '打卡顺序'
      }],
      currentSortOption: {
        value: 'point',
        label: '点位顺序'
      },
      // 当前选中的排序选项
      // 骨架屏状态
      isInitialLoading: false // 是否正在初始加载
    };
  },

  computed: _objectSpread({}, (0, _vuex.mapState)({
    userInfo: function userInfo(state) {
      return state.user.userInfo;
    }
  })),
  onLoad: function onLoad(options) {
    if (options.id) {
      this.routeId = options.id;
      this.filterParams.route_id = options.id;
    }
    if (options.name) {
      this.routeName = decodeURIComponent(options.name);
    }
    if (options.executorName) {
      this.executorName = decodeURIComponent(options.executorName);
    }

    // 显示骨架屏并加载数据
    this.isInitialLoading = true;

    // 获取任务基本信息（包含点位总数）
    this.loadTaskInfo();

    // 加载真实数据
    this.loadRecords();
  },
  onShow: function onShow() {
    // 每次显示页面时只刷新记录数据，不重新加载路线信息
    // 避免覆盖URL传入的路线名称
    this.refreshRecords();
  },
  methods: {
    // 获取任务基本信息
    loadTaskInfo: function loadTaskInfo() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var taskDetailRes, taskData, sortedRounds;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (_this.routeId) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return");
              case 2:
                _context.prev = 2;
                _context.next = 5;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'getTaskDetail',
                  data: {
                    task_id: _this.routeId,
                    // 🔥 优化方案：使用level='checkin'模式，保留完整points数组但简化字段
                    level: 'checkin' // 保留points数组但只要核心字段：point_id, status, checkin_time
                  }
                });
              case 5:
                taskDetailRes = _context.sent;
                if (taskDetailRes.code === 0 && taskDetailRes.data) {
                  taskData = taskDetailRes.data;
                  _this.taskDetail = taskData; // 保存完整任务详情

                  // 设置路线名称 - 修改这里，优先使用URL传入的名称
                  if (!_this.routeName || _this.routeName === '线路记录') {
                    _this.routeName = taskData.route_name || taskData.name || '线路记录';
                  }

                  // 设置执行者姓名
                  if (!_this.executorName) {
                    _this.executorName = taskData.user_name || taskData.executor_name || (taskData.executor ? taskData.executor.name || taskData.executor.real_name || taskData.executor.nickname : '') || '';
                  }

                  // 保存班次信息
                  _this.shift_info = {
                    name: taskData.shift_name || '',
                    id: taskData.shift_id || ''
                  };

                  // 初始化可用轮次列表
                  if (taskData.rounds_detail && taskData.rounds_detail.length > 0) {
                    // 添加"全部"选项
                    _this.availableRounds = [{
                      round: 0,
                      name: '全部轮次',
                      status: -1
                    }];

                    // 按轮次顺序排序并添加到列表
                    sortedRounds = (0, _toConsumableArray2.default)(taskData.rounds_detail).sort(function (a, b) {
                      return a.round - b.round;
                    });
                    sortedRounds.forEach(function (round) {
                      _this.availableRounds.push({
                        round: round.round,
                        name: "\u7B2C".concat(round.round, "\u8F6E"),
                        status: round.status
                      });
                    });

                    // 只在首次加载时设置默认轮次
                    if (_this.currentRound === undefined) {
                      _this.currentRound = 0;
                    }
                  }

                  // 计算点位统计信息 - 前端计算确保准确性
                  _this.calculateRouteStats();
                }
                _context.next = 13;
                break;
              case 9:
                _context.prev = 9;
                _context.t0 = _context["catch"](2);
                console.error('加载任务信息出错:', _context.t0);
                uni.showToast({
                  title: '获取任务信息失败',
                  icon: 'none'
                });
              case 13:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[2, 9]]);
      }))();
    },
    // 添加前端统计计算方法
    calculateRouteStats: function calculateRouteStats() {
      var _this2 = this;
      if (!this.taskDetail) return;
      var taskData = this.taskDetail;
      var normalCount = 0;
      var missedCount = 0;
      var notCheckedCount = 0;
      var totalPointCount = 0;
      var totalDuration = 0;

      // 1. 获取物理点位总数
      var uniquePointIds = new Set();
      if (taskData.route_detail && taskData.route_detail.points) {
        taskData.route_detail.points.forEach(function (p) {
          return uniquePointIds.add(p.point_id);
        });
      } else if (taskData.rounds_detail && taskData.rounds_detail.length > 0) {
        taskData.rounds_detail.forEach(function (round) {
          if (round.points && Array.isArray(round.points)) {
            round.points.forEach(function (point) {
              if (point.point_id) uniquePointIds.add(point.point_id);
            });
          }
        });
      }
      var physicalPointCount = uniquePointIds.size;

      // 2. 计算状态统计
      if (taskData.rounds_detail && taskData.rounds_detail.length > 0) {
        var roundsToProcess = [];
        if (this.currentRound === 0) {
          // 全部轮次
          roundsToProcess = taskData.rounds_detail;
          // 总点位数 = 物理点位数 × 轮次数
          totalPointCount = physicalPointCount * taskData.rounds_detail.length;
        } else {
          // 单轮次
          var selectedRound = taskData.rounds_detail.find(function (r) {
            return r.round === _this2.currentRound;
          });
          if (selectedRound) {
            var _selectedRound$points;
            roundsToProcess = [selectedRound];
            // 单轮次的总点位数就是该轮次的点位数
            totalPointCount = ((_selectedRound$points = selectedRound.points) === null || _selectedRound$points === void 0 ? void 0 : _selectedRound$points.length) || 0;
          }
        }

        // 处理每个轮次
        roundsToProcess.forEach(function (round) {
          if (round.points && Array.isArray(round.points)) {
            // 计算轮次用时
            var checkedPoints = round.points.filter(function (point) {
              return point.status === 1 && point.checkin_time;
            }).sort(function (a, b) {
              return new Date(a.checkin_time) - new Date(b.checkin_time);
            });
            if (checkedPoints.length > 0) {
              var firstCheckTime = new Date(checkedPoints[0].checkin_time);
              var lastCheckTime = new Date(checkedPoints[checkedPoints.length - 1].checkin_time);
              var roundDuration = Math.ceil((lastCheckTime - firstCheckTime) / (1000 * 60));
              totalDuration += roundDuration;
            }

            // 统计状态
            round.points.forEach(function (point) {
              switch (Number(point.status)) {
                case 1:
                  normalCount++;
                  break;
                case 3:
                case 4:
                  missedCount++;
                  break;
                case 0:
                default:
                  notCheckedCount++;
                  break;
              }
            });
          }
        });
      }

      // 3. 更新统计数据
      this.routeStats = {
        point_count: physicalPointCount,
        normal_count: normalCount,
        missed_count: missedCount,
        not_checked_count: notCheckedCount,
        total_checkin_time: totalDuration,
        completion_rate: totalPointCount > 0 ? Math.round(normalCount / totalPointCount * 100) : 0
      };
    },
    // 设置当前轮次
    setCurrentRound: function setCurrentRound(round) {
      this.currentRound = round;
      // 重新计算统计数据
      this.calculateRouteStats();
      // 刷新记录列表
      this.refresh();
    },
    // 完整刷新（包括任务信息和记录）
    refresh: function refresh() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _this3.refreshing = true;
                _this3.page = 1;
                _this3.hasMore = true;
                _context2.prev = 3;
                _context2.next = 6;
                return _this3.loadTaskInfo();
              case 6:
                _context2.next = 8;
                return _this3.loadRecords();
              case 8:
                _context2.next = 14;
                break;
              case 10:
                _context2.prev = 10;
                _context2.t0 = _context2["catch"](3);
                console.error('刷新失败:', _context2.t0);
                uni.showToast({
                  title: '刷新失败',
                  icon: 'none'
                });
              case 14:
                _context2.prev = 14;
                _this3.refreshing = false;
                return _context2.finish(14);
              case 17:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[3, 10, 14, 17]]);
      }))();
    },
    // 只刷新记录（用于页面显示时）
    refreshRecords: function refreshRecords() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _this4.page = 1;
                _this4.hasMore = true;
                _context3.next = 4;
                return _this4.loadRecords();
              case 4:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    // 加载更多数据
    loadMore: function loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadRecords();
      }
    },
    // 加载记录列表
    loadRecords: function loadRecords() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var taskDetail, recordRes, allRecords, allPoints, roundPoints, processedRecords, processedIds, roundsToProcess, selectedRound, filteredRecords, statusValue;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                if (!(_this5.loading || !_this5.hasMore)) {
                  _context4.next = 2;
                  break;
                }
                return _context4.abrupt("return");
              case 2:
                _this5.loading = true;
                _context4.prev = 3;
                if (!_this5.refreshing) {
                  _context4.next = 9;
                  break;
                }
                _context4.next = 7;
                return _this5.loadTaskInfo();
              case 7:
                _context4.next = 12;
                break;
              case 9:
                if (_this5.taskDetail) {
                  _context4.next = 12;
                  break;
                }
                _context4.next = 12;
                return _this5.loadTaskInfo();
              case 12:
                if (_this5.taskDetail) {
                  _context4.next = 17;
                  break;
                }
                uni.showToast({
                  title: '获取任务详情失败',
                  icon: 'none'
                });
                _this5.loading = false;
                _this5.refreshing = false;
                return _context4.abrupt("return");
              case 17:
                taskDetail = _this5.taskDetail; // 获取巡视记录
                _context4.next = 20;
                return _patrolApi.default.call({
                  name: 'patrol-record',
                  action: 'getRecordList',
                  data: {
                    task_id: _this5.routeId,
                    page: _this5.page,
                    pageSize: 50,
                    // 优化：减少单次数据量，从200改为50
                    status: _this5.filterParams.status === '' ? undefined : Number(_this5.filterParams.status),
                    // 优化：只获取列表显示需要的字段，减少RU消耗
                    fields: ['_id', 'point_id', 'point_name', 'status', 'round', 'user_id', 'user_name', 'checkin_time', 'patrol_date', 'task_id', 'shift_name', 'photos', 'location', 'address', 'remarks'],
                    // 移除冗余参数，减少数据传输
                    include_basic_info: true
                  }
                });
              case 20:
                recordRes = _context4.sent;
                // 从记录API获取的点位
                allRecords = [];
                if (recordRes.code === 0 && recordRes.data && recordRes.data.records) {
                  allRecords = recordRes.data.records || [];
                }

                // 提取所有点位信息
                allPoints = []; // 从route_detail.points获取点位信息
                if (taskDetail.route_detail && taskDetail.route_detail.points) {
                  allPoints = (0, _toConsumableArray2.default)(taskDetail.route_detail.points);
                }

                // 如果没有route_detail，或者点位数量不足，从rounds_detail中获取
                if ((allPoints.length === 0 || allPoints.some(function (p) {
                  return !p.name;
                })) && taskDetail.rounds_detail && taskDetail.rounds_detail.length > 0) {
                  // 合并所有轮次的点位基本信息
                  roundPoints = new Map();
                  taskDetail.rounds_detail.forEach(function (round) {
                    if (round.points && Array.isArray(round.points)) {
                      round.points.forEach(function (point) {
                        if (point.point_id && (!roundPoints.has(point.point_id) || !roundPoints.get(point.point_id).name)) {
                          roundPoints.set(point.point_id, {
                            point_id: point.point_id,
                            name: point.name || '未知点位',
                            order: point.order || 0
                          });
                        }
                      });
                    }
                  });

                  // 更新或补充点位信息
                  if (allPoints.length === 0) {
                    allPoints = Array.from(roundPoints.values());
                  } else {
                    // 补充现有点位的名称等信息
                    allPoints = allPoints.map(function (point) {
                      var roundPoint = roundPoints.get(point.point_id);
                      if (roundPoint && (!point.name || point.name === '未知点位')) {
                        return _objectSpread(_objectSpread({}, point), {}, {
                          name: roundPoint.name,
                          order: roundPoint.order || point.order || 0
                        });
                      }
                      return point;
                    });
                  }
                }

                // 为每轮次的每个点位创建或更新记录
                processedRecords = [];
                processedIds = new Set(); // 跟踪已处理的记录ID
                // 处理所选择轮次的记录
                if (taskDetail.rounds_detail && taskDetail.rounds_detail.length > 0) {
                  // 确定要处理的轮次，确保按轮次顺序排序
                  roundsToProcess = [];
                  if (_this5.currentRound === 0) {
                    // 处理所有轮次，按轮次号排序
                    roundsToProcess = (0, _toConsumableArray2.default)(taskDetail.rounds_detail).sort(function (a, b) {
                      return a.round - b.round;
                    });
                  } else {
                    // 只处理选中的轮次
                    selectedRound = taskDetail.rounds_detail.find(function (r) {
                      return r.round === _this5.currentRound;
                    });
                    if (selectedRound) {
                      roundsToProcess = [selectedRound];
                    }
                  }

                  // 处理每个轮次
                  roundsToProcess.forEach(function (round) {
                    if (round.points && Array.isArray(round.points)) {
                      round.points.forEach(function (point) {
                        // 查找匹配的API记录
                        var existingRecord = allRecords.find(function (r) {
                          return r.point_id === point.point_id && r.round === round.round;
                        });

                        // 查找点位基本信息
                        var pointInfo = allPoints.find(function (p) {
                          return p.point_id === point.point_id;
                        }) || {};

                        // 创建或更新记录
                        var recordId = "".concat(point.point_id, "_").concat(round.round);
                        if (!processedIds.has(recordId)) {
                          processedIds.add(recordId);

                          // 格式化打卡时间
                          var formattedCheckTime = '未打卡';
                          if (existingRecord && existingRecord.checkin_time) {
                            try {
                              var dateObj = new Date(existingRecord.checkin_time);
                              if (!isNaN(dateObj.getTime())) {
                                formattedCheckTime = "".concat(String(dateObj.getHours()).padStart(2, '0'), ":").concat(String(dateObj.getMinutes()).padStart(2, '0'), ":").concat(String(dateObj.getSeconds()).padStart(2, '0'));
                              }
                            } catch (e) {
                              console.error('格式化时间错误:', e);
                            }
                          }

                          // 创建完整的记录对象，确保包含必要的信息
                          var record = {
                            _id: recordId,
                            point_id: point.point_id,
                            point_name: point.name || pointInfo.name || '未知点位',
                            task_id: _this5.routeId,
                            status: point.status !== undefined ? Number(point.status) : 0,
                            order: point.order || pointInfo.order || 0,
                            shift_name: taskDetail.shift_name || _this5.shift_info.name || '未知班次',
                            user_id: (existingRecord === null || existingRecord === void 0 ? void 0 : existingRecord.user_id) || taskDetail.user_id,
                            user_name: (existingRecord === null || existingRecord === void 0 ? void 0 : existingRecord.user_name) || taskDetail.user_name || taskDetail.executor_name || taskDetail.creator_name || '未知人员',
                            round: round.round,
                            check_time: formattedCheckTime,
                            patrol_date: (existingRecord === null || existingRecord === void 0 ? void 0 : existingRecord.patrol_date) || taskDetail.patrol_date || '未知日期',
                            photos: (existingRecord === null || existingRecord === void 0 ? void 0 : existingRecord.photos) || [],
                            // 添加记录的详细信息，确保详情页可以打开
                            checkin_time: (existingRecord === null || existingRecord === void 0 ? void 0 : existingRecord.checkin_time) || null,
                            location: (existingRecord === null || existingRecord === void 0 ? void 0 : existingRecord.location) || null,
                            address: (existingRecord === null || existingRecord === void 0 ? void 0 : existingRecord.address) || null,
                            remarks: (existingRecord === null || existingRecord === void 0 ? void 0 : existingRecord.remarks) || '',
                            record_id: (existingRecord === null || existingRecord === void 0 ? void 0 : existingRecord._id) || recordId,
                            lat: point.lat || pointInfo.lat || (existingRecord === null || existingRecord === void 0 ? void 0 : existingRecord.lat),
                            lng: point.lng || pointInfo.lng || (existingRecord === null || existingRecord === void 0 ? void 0 : existingRecord.lng)
                          };
                          processedRecords.push(record);
                        }
                      });
                    }
                  });
                }

                // 按点位顺序和轮次顺序排序
                processedRecords.sort(function (a, b) {
                  // 首先按轮次排序
                  if (a.round !== b.round) return a.round - b.round;
                  // 轮次相同，按点位顺序排序
                  return (a.order || 0) - (b.order || 0);
                });

                // 根据筛选条件过滤
                filteredRecords = processedRecords;
                if (_this5.filterParams.status !== '') {
                  statusValue = Number(_this5.filterParams.status);
                  filteredRecords = processedRecords.filter(function (record) {
                    // 对于缺卡状态(值为3)，同时匹配状态3和状态4
                    if (statusValue === 3) {
                      return record.status === 3 || record.status === 4;
                    }
                    // 对于未打卡状态(值为0)，精确匹配
                    if (statusValue === 0) {
                      return record.status === 0;
                    }
                    // 对于其他状态，精确匹配
                    return record.status === statusValue;
                  });
                }

                // 实现真正的分页逻辑
                if (_this5.page === 1) {
                  // 第一页，替换现有数据
                  _this5.recordList = filteredRecords;
                } else {
                  // 后续页面，追加数据
                  _this5.recordList = [].concat((0, _toConsumableArray2.default)(_this5.recordList), (0, _toConsumableArray2.default)(filteredRecords));
                }

                // 应用当前的排序方式
                _this5.sortRecords();

                // 判断是否还有更多数据
                // 如果返回的记录数少于pageSize，说明没有更多数据了
                _this5.hasMore = filteredRecords.length >= _this5.pageSize;

                // 如果还有更多数据，准备下一页
                if (_this5.hasMore) {
                  _this5.page++;
                }
                _context4.next = 42;
                break;
              case 38:
                _context4.prev = 38;
                _context4.t0 = _context4["catch"](3);
                console.error('加载记录失败:', _context4.t0);
                uni.showToast({
                  title: '加载记录失败',
                  icon: 'none'
                });
              case 42:
                _context4.prev = 42;
                _this5.loading = false;
                _this5.isInitialLoading = false; // 关闭骨架屏
                if (_this5.refreshing) {
                  _this5.refreshing = false;
                }
                return _context4.finish(42);
              case 47:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[3, 38, 42, 47]]);
      }))();
    },
    // 重置筛选条件
    resetFilters: function resetFilters() {
      this.filterParams.status = '';
      this.refresh();
    },
    // 设置状态筛选
    setStatusFilter: function setStatusFilter(status) {
      // 将状态值转换为数字，除非是空字符串
      this.filterParams.status = status === '' ? '' : Number(status);
      this.refresh();
    },
    // 获取状态文本
    getStatusText: function getStatusText(status) {
      var numStatus = Number(status);
      switch (numStatus) {
        case 0:
          return '未打卡';
        case 1:
          return '已打卡';
        case 2:
          return '超时打卡';
        case 3:
          return '异常打卡';
        case 4:
          return '缺卡';
        default:
          return '未知状态';
      }
    },
    // 格式化时间显示
    formatTimeDisplay: function formatTimeDisplay(timeStr) {
      if (!timeStr) return '';
      try {
        // 检查是否只是日期部分
        if (typeof timeStr === 'string' && timeStr.length === 10 && timeStr.includes('-')) {
          // 只包含日期部分 YYYY-MM-DD，直接返回
          return timeStr;
        }

        // 使用安全的日期格式化函数，只显示日期部分
        return (0, _date.safeDateFormat)(timeStr, 'YYYY-MM-DD');
      } catch (e) {
        console.warn('时间格式化错误', e);
        // 错误处理，显示原始字符串或部分字符串
        if (typeof timeStr === 'string') {
          // 如果是标准日期格式，只取日期部分
          if (timeStr.includes('T')) {
            return timeStr.split('T')[0];
          }
          return timeStr.split(' ')[0]; // 只显示日期部分
        }

        return '未知时间';
      }
    },
    // 查看记录详情
    viewRecordDetail: function viewRecordDetail(record) {
      // 确保记录有必要的字段
      if (!record || !record.point_id) {
        uni.showToast({
          title: '记录数据不完整',
          icon: 'none'
        });
        return;
      }

      // 获取状态
      var status = record.status || 0;

      // 如果是未打卡(0)状态，只提示用户，不跳转
      if (status === 0) {
        uni.showToast({
          title: '该点位未打卡',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 缺卡状态(3或4)，只提示用户，不跳转
      if (status === 3 || status === 4) {
        uni.showToast({
          title: '该点位缺卡',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 只有已打卡(1)和超时打卡(2)才跳转到详情页
      if (status === 1 || status === 2) {
        // 构建记录详情页URL
        var url = "/pages/patrol_pkg/record/detail?id=".concat(record.record_id || record._id, "&task_id=").concat(this.routeId, "&point_id=").concat(record.point_id, "&round=").concat(record.round);
        uni.navigateTo({
          url: url,
          fail: function fail(err) {
            console.error('打开记录详情页失败:', err);
            uni.showToast({
              title: '无法打开详情',
              icon: 'none'
            });
          }
        });
      }
    },
    // 返回导航
    navigateBack: function navigateBack() {
      uni.navigateBack();
    },
    // 添加格式化总用时的方法
    formatTotalTime: function formatTotalTime(minutes) {
      if (!minutes || minutes <= 0) return '0分钟';
      if (minutes < 60) return "".concat(minutes, "\u5206\u949F");
      var hours = Math.floor(minutes / 60);
      var remainingMinutes = minutes % 60;
      if (remainingMinutes === 0) return "".concat(hours, "\u5C0F\u65F6");
      return "".concat(hours, "\u65F6").concat(remainingMinutes, "\u5206");
    },
    // 切换下拉菜单显示状态
    toggleSortDropdown: function toggleSortDropdown() {
      this.showSortDropdown = !this.showSortDropdown;
    },
    // 设置排序选项
    setSortOption: function setSortOption(option) {
      this.currentSort = option.value;
      this.currentSortOption = option;
      this.showSortDropdown = false;
      this.sortRecords();
    },
    // 排序记录
    sortRecords: function sortRecords() {
      if (this.currentSort === 'checkin') {
        // 按打卡时间排序
        this.recordList.sort(function (a, b) {
          // 未打卡的记录放到最后
          if (!a.checkin_time && !b.checkin_time) return 0;
          if (!a.checkin_time) return 1;
          if (!b.checkin_time) return -1;
          return new Date(a.checkin_time) - new Date(b.checkin_time);
        });
      } else {
        // 按点位顺序排序（默认）
        this.recordList.sort(function (a, b) {
          // 先按轮次排序
          if (a.round !== b.round) return a.round - b.round;
          // 轮次相同，按点位顺序排序
          return (a.order || 0) - (b.order || 0);
        });
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 465:
/*!******************************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?vue&type=style&index=0&lang=scss& ***!
  \******************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./route-detail.vue?vue&type=style&index=0&lang=scss& */ 466);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_route_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 466:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/record/route-detail.vue?vue&type=style&index=0&lang=scss& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[459,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/patrol_pkg/record/route-detail.js.map
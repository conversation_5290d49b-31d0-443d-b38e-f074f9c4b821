(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["components/patrol/p-task-card"],{

/***/ 530:
/*!*************************************************!*\
  !*** D:/Xwzc/components/patrol/p-task-card.vue ***!
  \*************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _p_task_card_vue_vue_type_template_id_23915e73___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./p-task-card.vue?vue&type=template&id=23915e73& */ 531);
/* harmony import */ var _p_task_card_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./p-task-card.vue?vue&type=script&lang=js& */ 533);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _p_task_card_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _p_task_card_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _p_task_card_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./p-task-card.vue?vue&type=style&index=0&lang=scss& */ 535);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _p_task_card_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _p_task_card_vue_vue_type_template_id_23915e73___WEBPACK_IMPORTED_MODULE_0__["render"],
  _p_task_card_vue_vue_type_template_id_23915e73___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _p_task_card_vue_vue_type_template_id_23915e73___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "components/patrol/p-task-card.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 531:
/*!********************************************************************************!*\
  !*** D:/Xwzc/components/patrol/p-task-card.vue?vue&type=template&id=23915e73& ***!
  \********************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_template_id_23915e73___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./p-task-card.vue?vue&type=template&id=23915e73& */ 532);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_template_id_23915e73___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_template_id_23915e73___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_template_id_23915e73___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_template_id_23915e73___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 532:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/components/patrol/p-task-card.vue?vue&type=template&id=23915e73& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.getUserName()
  var m1 =
    _vm.task.patrol_date || _vm.formatDate(_vm.task.create_date, "YYYY-MM-DD")
  var g0 = _vm.hasRounds
    ? _vm.task.rounds_detail && _vm.task.rounds_detail.length > 0
    : null
  var g1 = _vm.hasRounds && g0 ? _vm.task.rounds_detail.length : null
  var m2 =
    _vm.hasRounds && !(_vm.task.status === 2)
      ? _vm.getCurrentActiveRound()
      : null
  var m3 =
    _vm.hasRounds && !(_vm.task.status === 2) && m2
      ? _vm.getCurrentActiveRound().round || "?"
      : null
  var m4 =
    _vm.hasRounds && !(_vm.task.status === 2) && !m2 ? _vm.getNextRound() : null
  var m5 =
    _vm.hasRounds && !(_vm.task.status === 2) && !m2 && m4
      ? _vm.getNextRound().round || "?"
      : null
  var g2 = _vm.hasRounds
    ? !_vm.task.rounds_detail || _vm.task.rounds_detail.length === 0
    : null
  var l0 =
    _vm.hasRounds && !g2
      ? _vm.__map(_vm.sortedRounds, function (round, index) {
          var $orig = _vm.__get_orig(round)
          var m6 = _vm.isRoundSelected(round)
          var m7 = _vm.getRoundStatusText(round)
          var m8 =
            round.status === 1
              ? _vm.formatSimpleCountdown(_vm.getRoundTimeRemaining(round))
              : null
          var m9 =
            round.status === 0
              ? _vm.formatSimpleCountdown(_vm.getRoundTimeUntilStart(round))
              : null
          return {
            $orig: $orig,
            m6: m6,
            m7: m7,
            m8: m8,
            m9: m9,
          }
        })
      : null
  var m10 =
    _vm.hasRounds && _vm.task.status === 2 ? _vm.getTotalMissedPoints() : null
  var m11 =
    _vm.hasRounds && _vm.task.status === 2 && m10 > 0
      ? _vm.getTotalMissedPoints()
      : null
  var m12 =
    _vm.hasRounds &&
    !(_vm.task.status === 2) &&
    !(_vm.task.status === 4) &&
    _vm.task.status === 3
      ? _vm.getTotalMissedPoints()
      : null
  var m13 =
    _vm.hasRounds &&
    !(_vm.task.status === 2) &&
    !(_vm.task.status === 4) &&
    _vm.task.status === 3 &&
    m12 > 0
      ? _vm.getTotalMissedPoints()
      : null
  var m14 =
    _vm.hasRounds &&
    !(_vm.task.status === 2) &&
    !(_vm.task.status === 4) &&
    !(_vm.task.status === 3) &&
    _vm.task.status === 1 &&
    _vm.hasActiveRound
      ? _vm.getCurrentRoundNumber()
      : null
  var m15 =
    _vm.hasRounds &&
    !(_vm.task.status === 2) &&
    !(_vm.task.status === 4) &&
    !(_vm.task.status === 3) &&
    _vm.task.status === 1 &&
    _vm.hasActiveRound
      ? _vm.getCurrentRoundMissedPoints()
      : null
  var m16 =
    _vm.hasRounds &&
    !(_vm.task.status === 2) &&
    !(_vm.task.status === 4) &&
    !(_vm.task.status === 3) &&
    _vm.task.status === 1 &&
    _vm.hasActiveRound &&
    m15 > 0
      ? _vm.getCurrentRoundMissedPoints()
      : null
  var m17 =
    _vm.hasRounds &&
    !(_vm.task.status === 2) &&
    !(_vm.task.status === 4) &&
    !(_vm.task.status === 3) &&
    !(_vm.task.status === 1 && _vm.hasActiveRound)
      ? _vm.getNextRoundNumber()
      : null
  var m18 = _vm.formatProgress(_vm.progressRate)
  var m19 =
    _vm.canContinue && _vm.canStartPatrol ? _vm.getCurrentActiveRound() : null
  var m20 =
    !(_vm.canContinue && _vm.canStartPatrol) &&
    _vm.canContinue &&
    !_vm.canStartPatrol
      ? _vm.getCurrentActiveRound()
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        g0: g0,
        g1: g1,
        m2: m2,
        m3: m3,
        m4: m4,
        m5: m5,
        g2: g2,
        l0: l0,
        m10: m10,
        m11: m11,
        m12: m12,
        m13: m13,
        m14: m14,
        m15: m15,
        m16: m16,
        m17: m17,
        m18: m18,
        m19: m19,
        m20: m20,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 533:
/*!**************************************************************************!*\
  !*** D:/Xwzc/components/patrol/p-task-card.vue?vue&type=script&lang=js& ***!
  \**************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./p-task-card.vue?vue&type=script&lang=js& */ 534);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 534:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/components/patrol/p-task-card.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _date = __webpack_require__(/*! @/utils/date.js */ 72);
function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }
var _default2 = {
  name: 'p-task-card',
  props: {
    task: {
      type: Object,
      required: true
    },
    userMap: {
      type: Object,
      default: function _default() {
        return {};
      }
    },
    shift: {
      type: Object,
      default: null
    },
    roundInfo: {
      type: Object,
      default: null
    },
    active: {
      type: Boolean,
      default: false
    },
    // 新增：当前选中的轮次
    selectedRoundNumber: {
      type: Number,
      default: -1
    }
  },
  data: function data() {
    return {
      // 简化定时器
      countdownTimer: null,
      // 当前时间（用于计算倒计时）
      currentTime: new Date(),
      // 防抖：记录已经触发过刷新的轮次
      refreshedRounds: new Set(),
      // 缓存倒计时结果，减少闪烁
      countdownCache: {},
      // 🔥 新增：事件节流机制
      eventThrottle: {},
      lastEmitTime: {}
    };
  },
  computed: {
    // 状态样式类 - 直接使用任务状态，不再重新计算
    statusClass: function statusClass() {
      var status = this.task ? this.task.status : 0;
      if (status === 0) return 'pending';
      if (status === 1) return 'active';
      if (status === 2) return 'completed';
      if (status === 3) return 'expired';
      if (status === 4) return 'canceled';
      return 'pending';
    },
    // 状态文本 - 直接使用任务状态，不再重新计算
    statusText: function statusText() {
      var status = this.task ? this.task.status : 0;
      if (status === 0) return '未开始';
      if (status === 1) return '进行中';
      if (status === 2) return '已完成';
      if (status === 3) return '已超时';
      if (status === 4) return '已取消';
      return '未开始';
    },
    // 是否处于激活状态 - 直接使用任务状态
    isActive: function isActive() {
      return this.task && this.task.status === 1;
    },
    // 是否已超时 - 直接使用任务状态
    isExpired: function isExpired() {
      return this.task && this.task.status === 3;
    },
    // 是否已完成 - 直接使用任务状态
    isCompleted: function isCompleted() {
      return this.task && this.task.status === 2;
    },
    // 是否可以继续执行 - 简化逻辑
    canContinue: function canContinue() {
      return this.task && (this.task.status === 0 || this.task.status === 1);
    },
    // 是否可以开始巡视 - 简化逻辑，依赖父组件传入的状态
    canStartPatrol: function canStartPatrol() {
      if (!this.task) return false;
      return this.task.status === 1 || this.task.status === 0;
    },
    // 已完成点位数量
    completedPoints: function completedPoints() {
      // 优先使用overall_stats
      if (this.task && this.task.overall_stats && this.task.overall_stats.completed_points !== undefined) {
        return this.task.overall_stats.completed_points;
      }
      return 0;
    },
    // 总点位数量
    totalPoints: function totalPoints() {
      // 优先使用overall_stats
      if (this.task && this.task.overall_stats && this.task.overall_stats.total_points !== undefined) {
        return this.task.overall_stats.total_points;
      }
      return 0;
    },
    // 进度比例计算属性
    progressRate: function progressRate() {
      // 1. 优先使用overall_stats的completion_rate
      if (this.task && this.task.overall_stats && this.task.overall_stats.completion_rate !== undefined) {
        // 确保completion_rate是有效数值且在0-1范围内
        var rate = parseFloat(this.task.overall_stats.completion_rate);
        if (!isNaN(rate)) {
          return rate > 1 ? rate / 100 : rate; // 处理可能的百分比值
        }
      }

      // 2. 其次使用task的completion_rate
      if (this.task && this.task.completion_rate !== undefined) {
        // 确保completion_rate是有效数值且在0-1范围内
        var _rate = parseFloat(this.task.completion_rate);
        if (!isNaN(_rate)) {
          return _rate > 1 ? _rate / 100 : _rate; // 处理可能的百分比值
        }
      }

      // 3. 最后计算比例，确保完成点位数大于0且总点位数大于0
      if (this.completedPoints > 0 && this.totalPoints > 0) {
        return this.completedPoints / this.totalPoints;
      }
      return 0; // 默认返回0表示无进度
    },
    // 是否为通宵/跨天班次
    isAcrossDay: function isAcrossDay() {
      // 1. 首先检查task.shift_detail中的across_day属性
      if (this.task && this.task.shift_detail && this.task.shift_detail.across_day === true) {
        return true;
      }

      // 2. 如果直接从shift中获取时间
      if (this.shift && this.shift.start_time && this.shift.end_time) {
        try {
          // 如果开始时间大于结束时间，说明是跨天班次
          var startTime = this.shift.start_time;
          var endTime = this.shift.end_time;

          // 解析纯时间格式，例如 "18:00"
          var parseTimeStr = function parseTimeStr(timeStr) {
            if (!timeStr || !timeStr.includes(':')) return 0;
            var _timeStr$split$map = timeStr.split(':').map(Number),
              _timeStr$split$map2 = (0, _slicedToArray2.default)(_timeStr$split$map, 2),
              hours = _timeStr$split$map2[0],
              minutes = _timeStr$split$map2[1];
            return hours * 60 + minutes; // 转换为分钟数便于比较
          };

          var startMinutes = parseTimeStr(startTime);
          var endMinutes = parseTimeStr(endTime);

          // 如果结束时间小于开始时间，通常是跨天的情况
          return startMinutes > endMinutes;
        } catch (e) {
          return false;
        }
      }
      return false;
    },
    // 是否有轮次
    hasRounds: function hasRounds() {
      return this.task && this.task.rounds_detail && this.task.rounds_detail.length > 0;
    },
    // 排序轮次计算属性 - 直接使用传入的轮次数据，不再重新排序
    sortedRounds: function sortedRounds() {
      if (!this.task || !this.task.rounds_detail || !Array.isArray(this.task.rounds_detail)) {
        return [];
      }
      return this.task.rounds_detail;
    },
    // 是否有活跃轮次 - 直接使用轮次状态
    hasActiveRound: function hasActiveRound() {
      return this.task && this.task.rounds_detail && this.task.rounds_detail.some(function (round) {
        return round.status === 1;
      });
    }
  },
  methods: {
    // 格式化进度
    formatProgress: function formatProgress(progress) {
      // 确保progress是有效数字且在0-1之间
      if (typeof progress !== 'number' || isNaN(progress)) {
        progress = 0;
      }

      // 如果已经是百分比形式(0-100)
      if (progress > 1) {
        return "".concat(Math.min(100, Math.floor(progress)), "%");
      }

      // 如果是小数形式(0-1)
      return "".concat(Math.floor(progress * 100), "%");
    },
    // 新增：处理轮次点击事件
    onRoundClick: function onRoundClick(round, index) {
      if (!round) return;

      // 向父组件发送select-round事件，传递轮次信息
      this.$emit('select-round', {
        round: round,
        index: index,
        taskId: this.task ? this.task._id : null
      });
    },
    // 新增：检查轮次是否被选中
    isRoundSelected: function isRoundSelected(round) {
      if (!round) return false;
      return this.selectedRoundNumber === round.round;
    },
    // 获取轮次状态文本
    getRoundStatusText: function getRoundStatusText(round) {
      if (!round) return '未知';

      // 从轮次状态status获取
      var statusMap = {
        0: '未开始',
        1: '进行中',
        2: '已完成',
        3: '已超时'
      };
      return statusMap[round.status] || '未知';
    },
    // 获取当前活跃轮次 - 简化逻辑
    getCurrentActiveRound: function getCurrentActiveRound() {
      if (!this.task || !this.task.rounds_detail) return null;
      return this.task.rounds_detail.find(function (round) {
        return round.status === 1;
      });
    },
    // 获取下一个轮次 - 简化逻辑
    getNextRound: function getNextRound() {
      if (!this.task || !this.task.rounds_detail) return null;
      return this.task.rounds_detail.find(function (round) {
        return round.status === 0;
      });
    },
    // 获取总缺卡点数
    getTotalMissedPoints: function getTotalMissedPoints() {
      // 从overall_stats获取缺卡点数
      if (this.task && this.task.overall_stats && this.task.overall_stats.missed_points !== undefined) {
        return this.task.overall_stats.missed_points;
      }
      return 0;
    },
    // 点击卡片
    onCardClick: function onCardClick() {
      if (!this.task) return;

      // 点击卡片时触发select事件，传递任务ID
      this.$emit('click', this.task);
    },
    // 继续/开始巡检按钮点击事件
    onContinueTask: function onContinueTask() {
      var _this = this;
      if (!this.task) return;

      // 直接触发continue事件，让父组件处理轮次选择和状态判断
      this.$emit('continue', {
        task: this.task,
        selectedRound: this.selectedRoundNumber !== -1 ? this.task.rounds_detail.find(function (r) {
          return r.round === _this.selectedRoundNumber;
        }) : null
      });
    },
    // 查看详情按钮点击事件
    onViewDetail: function onViewDetail() {
      if (!this.task) return;
      this.$emit('view-detail', this.task);
    },
    // 获取当前轮次编号
    getCurrentRoundNumber: function getCurrentRoundNumber() {
      var currentRound = this.getCurrentActiveRound();
      return currentRound ? currentRound.round || 1 : 1;
    },
    // 获取任务状态文本
    getTaskStatusText: function getTaskStatusText() {
      if (!this.task) return '';

      // 任务状态: 0=未开始, 1=进行中, 2=已完成, 3=已超时
      switch (this.task.status) {
        case 0:
          return '等待开始';
        case 1:
          return "\u8FDB\u884C\u7B2C".concat(this.getCurrentRoundNumber(), "\u8F6E\u5DE1\u68C0");
        case 2:
          return '已完成';
        case 3:
          return '已超时';
        default:
          return '未知状态';
      }
    },
    // 获取用户名
    getUserName: function getUserName() {
      if (!this.task) return '';

      // 仅从userMap获取用户名
      if (this.userMap && this.task.user_id && this.userMap[this.task.user_id]) {
        return this.userMap[this.task.user_id].name || this.userMap[this.task.user_id].username || '未知用户';
      }
      return '未指定人员';
    },
    // 获取轮次剩余时间（毫秒）
    getRoundTimeRemaining: function getRoundTimeRemaining(round) {
      if (!round || round.status !== 1) return 0;
      var cacheKey = "remaining_".concat(round.round);
      var now = Date.now();

      // 使用缓存减少闪烁，每500ms更新一次
      if (this.countdownCache[cacheKey] && now - this.countdownCache[cacheKey].timestamp < 500) {
        return this.countdownCache[cacheKey].value;
      }
      var result = 0;
      try {
        // 方式1：直接使用ISO格式的end_time（如果可用）
        if (round.end_time) {
          var endTime = this.parseISOTime(round.end_time);
          if (endTime && this.currentTime < endTime) {
            result = Math.max(0, endTime - this.currentTime);
          }
        }
        // 方式2：使用工具函数从time和day_offset计算
        else if (round.time && this.task) {
          // 获取基准日期（优先使用任务日期，其次使用当天日期）
          var baseDate = this.task.patrol_date || (0, _date.formatDate)(new Date(), 'YYYY-MM-DD');
          // 使用工具方法计算开始时间，考虑日期偏移
          var startTime = (0, _date.calculateRoundTime)(baseDate, round.time, round.day_offset || 0);
          // 使用工具方法计算结束时间，考虑持续时间
          var _endTime = (0, _date.calculateEndTime)(startTime, round.duration || 60);
          if (this.currentTime < _endTime) {
            result = Math.max(0, _endTime - this.currentTime);
          }
        }
      } catch (e) {
        console.error('计算轮次剩余时间出错:', e, round);
      }

      // 缓存结果
      this.countdownCache[cacheKey] = {
        value: result,
        timestamp: now
      };
      return result;
    },
    // 获取轮次等待开始时间（毫秒）
    getRoundTimeUntilStart: function getRoundTimeUntilStart(round) {
      if (!round || round.status !== 0) return 0;
      var cacheKey = "waiting_".concat(round.round);
      var now = Date.now();

      // 使用缓存减少闪烁，每500ms更新一次
      if (this.countdownCache[cacheKey] && now - this.countdownCache[cacheKey].timestamp < 500) {
        return this.countdownCache[cacheKey].value;
      }
      var result = 0;
      try {
        // 方式1：直接使用ISO格式的start_time（如果可用）
        if (round.start_time) {
          var startTime = this.parseISOTime(round.start_time);
          if (startTime && this.currentTime < startTime) {
            result = Math.max(0, startTime - this.currentTime);
          }
        }
        // 方式2：使用工具函数从time和day_offset计算
        else if (round.time && this.task) {
          // 获取基准日期（优先使用任务日期，其次使用当天日期）
          var baseDate = this.task.patrol_date || (0, _date.formatDate)(new Date(), 'YYYY-MM-DD');
          // 使用工具方法计算时间，考虑日期偏移
          var _startTime = (0, _date.calculateRoundTime)(baseDate, round.time, round.day_offset || 0);
          if (this.currentTime < _startTime) {
            result = Math.max(0, _startTime - this.currentTime);
          }
        }
      } catch (e) {
        console.error('计算轮次等待时间出错:', e, round);
      }

      // 缓存结果
      this.countdownCache[cacheKey] = {
        value: result,
        timestamp: now
      };
      return result;
    },
    // 解析ISO时间字符串
    parseISOTime: function parseISOTime(isoString) {
      if (!isoString) return null;
      try {
        // 手动解析ISO格式时间字符串，如 "2025-05-27T12:00:00.000Z"
        var match = isoString.match(/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{3}))?Z?$/);
        if (match) {
          var _match = (0, _slicedToArray2.default)(match, 8),
            year = _match[1],
            month = _match[2],
            day = _match[3],
            hour = _match[4],
            minute = _match[5],
            second = _match[6],
            millisecond = _match[7];
          // 注意：JavaScript的月份是从0开始的
          return new Date(Date.UTC(parseInt(year), parseInt(month) - 1,
          // 月份需要减1
          parseInt(day), parseInt(hour), parseInt(minute), parseInt(second), parseInt(millisecond || 0)));
        }

        // 如果正则匹配失败，尝试直接解析
        var date = new Date(isoString);
        return isNaN(date.getTime()) ? null : date;
      } catch (e) {
        console.error('解析ISO时间出错:', e, isoString);
        return null;
      }
    },
    // 格式化倒计时显示
    formatSimpleCountdown: function formatSimpleCountdown(milliseconds) {
      if (!milliseconds || milliseconds <= 0) return '0秒';
      try {
        var totalSeconds = Math.floor(milliseconds / 1000);
        var hours = Math.floor(totalSeconds / 3600);
        var minutes = Math.floor(totalSeconds % 3600 / 60);
        var seconds = totalSeconds % 60;

        // 轮次时间倒计时显示
        if (totalSeconds < 60) {
          // 小于1分钟，显示秒数
          return "".concat(seconds, "\u79D2");
        } else if (totalSeconds < 3600) {
          // 小于1小时，显示分钟和秒数
          return "".concat(minutes, "\u5206").concat(seconds, "\u79D2");
        } else {
          // 大于等于1小时，显示小时和分钟
          return "".concat(hours, "\u5C0F\u65F6").concat(minutes, "\u5206\u949F");
        }
      } catch (e) {
        console.error('格式化倒计时出错:', e);
        return '0秒';
      }
    },
    // 获取当前轮次缺卡数 - 简化逻辑
    getCurrentRoundMissedPoints: function getCurrentRoundMissedPoints() {
      var currentRound = this.getCurrentActiveRound();
      if (!currentRound || !currentRound.stats) return 0;
      return currentRound.stats.missed_points || 0;
    },
    // 检查轮次是否有真实的打卡数据
    hasRealCheckInData: function hasRealCheckInData() {
      if (!this.task) return false;

      // 如果任务状态是已完成且有进度指标，那么可能已有数据
      if (this.task.status === 2 && this.progressRate > 0) {
        return true;
      }

      // 检查current_round_records是否有真实数据
      if (this.task.current_round_records && Array.isArray(this.task.current_round_records)) {
        // 过滤有效记录 - 必须有check_time
        var validRecords = this.task.current_round_records.filter(function (record) {
          return record && (0, _typeof2.default)(record) === 'object' && record.check_time;
        });
        return validRecords.length > 0;
      }

      // 检查所有轮次是否有打卡记录
      if (this.task.rounds_detail && Array.isArray(this.task.rounds_detail)) {
        var _iterator = _createForOfIteratorHelper(this.task.rounds_detail),
          _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var round = _step.value;
            if (round && round.stats) {
              // 只有当completed_points大于0时才认为有真实打卡
              if (round.stats.completed_points > 0) {
                return true;
              }
            }
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
      }

      // 没有找到有效打卡记录
      return false;
    },
    // 添加判断点位是否完成的辅助方法
    isPointCompleted: function isPointCompleted(point, round) {
      var _this$task$round_reco;
      if (!point || !round) return false;
      var pointId = point.point_id || point._id;
      if (!pointId) return false;

      // 获取轮次记录
      var roundRecord = (_this$task$round_reco = this.task.round_records) === null || _this$task$round_reco === void 0 ? void 0 : _this$task$round_reco.find(function (r) {
        return r.round === round.round;
      });
      var completedPoints = (roundRecord === null || roundRecord === void 0 ? void 0 : roundRecord.completed_points) || [];

      // 检查完成状态 - 与index.vue保持一致的判断标准
      var statusCompleted = point.status === 1 || point.status === 'completed';
      var hasValidCheckIn = !!point.checkin_time;
      var inCompletedArray = completedPoints.includes(pointId);
      return statusCompleted || hasValidCheckIn || inCompletedArray;
    },
    getNextRoundNumber: function getNextRoundNumber() {
      var nextRound = this.getNextRound();
      return nextRound ? nextRound.round : 1;
    },
    // 启动倒计时定时器
    startCountdownTimer: function startCountdownTimer() {
      var _this2 = this;
      // 先清除已有的定时器
      this.stopCountdownTimer();

      // 每秒更新一次当前时间
      this.countdownTimer = setInterval(function () {
        _this2.currentTime = new Date();

        // 检查是否有倒计时结束，需要刷新数据
        _this2.checkCountdownExpired();
      }, 1000);
    },
    // 停止倒计时定时器
    stopCountdownTimer: function stopCountdownTimer() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
      // 清理缓存
      this.countdownCache = {};
    },
    // 🔥 新增：节流发送事件，避免短时间内重复触发
    throttledEmit: function throttledEmit(eventName, data) {
      var _this3 = this;
      var throttleTime = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 5000;
      var now = Date.now();
      var key = "".concat(eventName, "_").concat(this.task._id);
      if (this.lastEmitTime[key] && now - this.lastEmitTime[key] < throttleTime) {
        console.log("\u4E8B\u4EF6".concat(eventName, "\u88AB\u8282\u6D41\uFF0C\u8DF3\u8FC7\u91CD\u590D\u89E6\u53D1"));
        return;
      }
      this.lastEmitTime[key] = now;
      this.$emit(eventName, data);

      // 只在必要时发送全局事件
      if (eventName === 'refresh-task' || eventName === 'countdown-expired') {
        // 确保全局事件不重复
        if (!this.eventThrottle[key]) {
          this.eventThrottle[key] = true;
          uni.$emit('refresh-task-list');
          setTimeout(function () {
            _this3.eventThrottle[key] = false;
          }, throttleTime);
        }
      }
    },
    // 检查倒计时是否结束，需要刷新数据
    checkCountdownExpired: function checkCountdownExpired() {
      var _this4 = this;
      if (!this.task || !this.task.rounds_detail) return;
      var needRefresh = false;
      var expiredRounds = [];
      this.task.rounds_detail.forEach(function (round) {
        if (!round) return;
        var roundKey = "".concat(_this4.task._id, "_").concat(round.round);

        // 检查进行中的轮次是否已超时
        if (round.status === 1) {
          var remaining = _this4.getRoundTimeRemaining(round);
          if (remaining <= 0 && !_this4.refreshedRounds.has(roundKey + '_expired')) {
            needRefresh = true;
            expiredRounds.push("\u8F6E\u6B21".concat(round.round, "\u5DF2\u8D85\u65F6"));
            _this4.refreshedRounds.add(roundKey + '_expired');
          }
        }

        // 检查未开始的轮次是否应该开始了
        if (round.status === 0) {
          var timeUntilStart = _this4.getRoundTimeUntilStart(round);
          if (timeUntilStart <= 0 && !_this4.refreshedRounds.has(roundKey + '_start')) {
            needRefresh = true;
            expiredRounds.push("\u8F6E\u6B21".concat(round.round, "\u5E94\u8BE5\u5F00\u59CB"));
            _this4.refreshedRounds.add(roundKey + '_start');
          }
        }
      });

      // 如果需要刷新，通知父组件
      if (needRefresh) {
        console.log('倒计时结束，需要刷新任务:', this.task._id, expiredRounds);

        // 发送事件给父组件
        this.throttledEmit('countdown-expired', {
          taskId: this.task._id,
          task: this.task,
          expiredRounds: expiredRounds
        });

        // 同时发送刷新事件（更通用的事件名）
        this.throttledEmit('refresh-task', {
          taskId: this.task._id,
          reason: 'countdown-expired',
          details: expiredRounds
        });

        // 如果父组件没有监听事件，尝试其他方式通知刷新
        this.$nextTick(function () {
          // 通过全局事件总线通知
          if (_this4.$root && _this4.$root.$emit) {
            _this4.$root.$emit('task-countdown-expired', {
              taskId: _this4.task._id,
              expiredRounds: expiredRounds
            });
          }

          // 通过uni.$emit通知（如果是uni-app环境）
          if (typeof uni !== 'undefined' && uni.$emit) {
            uni.$emit('task-countdown-expired', {
              taskId: _this4.task._id,
              expiredRounds: expiredRounds
            });

            // 发送父组件已经监听的刷新事件
            uni.$emit('refresh-task-list');
          }
        });

        // 延迟一段时间后清除防抖记录，允许再次触发
        setTimeout(function () {
          expiredRounds.forEach(function (msg) {
            var _msg$match;
            var roundNum = (_msg$match = msg.match(/轮次(\d+)/)) === null || _msg$match === void 0 ? void 0 : _msg$match[1];
            if (roundNum) {
              var roundKey = "".concat(_this4.task._id, "_").concat(roundNum);
              _this4.refreshedRounds.delete(roundKey + '_expired');
              _this4.refreshedRounds.delete(roundKey + '_start');
            }
          });
        }, 30000); // 30秒后允许再次触发
      }
    }
  },
  mounted: function mounted() {
    // 启动倒计时定时器
    this.startCountdownTimer();
  },
  beforeDestroy: function beforeDestroy() {
    // 清理定时器
    this.stopCountdownTimer();
  },
  // 添加页面生命周期管理
  onShow: function onShow() {
    this.startCountdownTimer();
  },
  onHide: function onHide() {
    this.stopCountdownTimer();
  }
};
exports.default = _default2;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 535:
/*!***********************************************************************************!*\
  !*** D:/Xwzc/components/patrol/p-task-card.vue?vue&type=style&index=0&lang=scss& ***!
  \***********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./p-task-card.vue?vue&type=style&index=0&lang=scss& */ 536);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_task_card_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 536:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/components/patrol/p-task-card.vue?vue&type=style&index=0&lang=scss& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/patrol/p-task-card.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/patrol/p-task-card-create-component',
    {
        'components/patrol/p-task-card-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(530))
        })
    },
    [['components/patrol/p-task-card-create-component']]
]);

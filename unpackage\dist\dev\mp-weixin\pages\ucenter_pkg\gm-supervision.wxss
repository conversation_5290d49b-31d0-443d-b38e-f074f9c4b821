@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-1f55f9ed {
  min-height: 100vh;
  background-color: #f8f9fc;
  padding: 20rpx;
}
.page-header.data-v-1f55f9ed {
  background: linear-gradient(135deg, #38bdf8 0%, #0ea5e9 70%, #3b82f6 100%);
  /* 更轻盈的蓝色渐变 - 从浅到深 */
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  color: white;
  box-shadow: 0 6rpx 24rpx rgba(56, 189, 248, 0.25);
  /* 更轻的阴影 */
}
.page-title.data-v-1f55f9ed {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}
.page-subtitle.data-v-1f55f9ed {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 20rpx;
}
.timing-explanation.data-v-1f55f9ed {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  margin-top: 20rpx;
}
.explanation-text.data-v-1f55f9ed {
  font-size: 24rpx;
  opacity: 0.95;
  line-height: 1.4;
}
.stats-section.data-v-1f55f9ed {
  margin-bottom: 30rpx;
}
.stats-grid.data-v-1f55f9ed {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}
.stat-card.data-v-1f55f9ed {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.stat-card.data-v-1f55f9ed:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.stat-number.data-v-1f55f9ed {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
  /* 浅蓝色 - 执行中 */
  /* 橙色 - 待确认 */
  /* 绿色 - 已完成 */
  /* 红色 - 超时 */
}
.stat-number.assigned.data-v-1f55f9ed {
  color: #38bdf8;
}
.stat-number.pending.data-v-1f55f9ed {
  color: #f59e0b;
}
.stat-number.completed.data-v-1f55f9ed {
  color: #059669;
}
.stat-number.overdue.data-v-1f55f9ed {
  color: #dc2626;
}
.stat-label.data-v-1f55f9ed {
  font-size: 24rpx;
  color: #666;
}
.filter-tabs.data-v-1f55f9ed {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.filter-tab.data-v-1f55f9ed {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}
.filter-tab.active.data-v-1f55f9ed {
  background: #3b82f6;
  /* 蓝色 - 水务主题 */
  color: white;
}
.task-list.data-v-1f55f9ed {
  margin-bottom: 30rpx;
}
.task-item.data-v-1f55f9ed {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.task-item.data-v-1f55f9ed:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
}
.task-header.data-v-1f55f9ed {
  margin-bottom: 20rpx;
}
.task-title-row.data-v-1f55f9ed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.task-name.data-v-1f55f9ed {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}
.task-status.data-v-1f55f9ed {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  /* 浅蓝色 - 执行中 */
  /* 橙色 - 待确认 */
  /* 绿色 - 已完成 */
}
.task-status.status-assigned.data-v-1f55f9ed {
  background: #38bdf8;
}
.task-status.status-pending.data-v-1f55f9ed {
  background: #f59e0b;
}
.task-status.status-completed.data-v-1f55f9ed {
  background: #059669;
}
.task-project.data-v-1f55f9ed {
  font-size: 24rpx;
  color: #666;
}
.task-content.data-v-1f55f9ed {
  margin-bottom: 20rpx;
}
.task-description.data-v-1f55f9ed {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.task-footer.data-v-1f55f9ed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.responsible-info.data-v-1f55f9ed, .time-info.data-v-1f55f9ed {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8rpx;
}
.responsible-label.data-v-1f55f9ed, .time-label.data-v-1f55f9ed {
  font-size: 24rpx;
  color: #999;
  margin-right: 8rpx;
}
.responsible-name.data-v-1f55f9ed, .time-value.data-v-1f55f9ed {
  font-size: 24rpx;
  color: #333;
}
.responsible-name.overdue.data-v-1f55f9ed, .time-value.overdue.data-v-1f55f9ed {
  color: #dc2626;
  /* 红色 - 超时 */
  font-weight: bold;
}
.responsible-name.warning.data-v-1f55f9ed, .time-value.warning.data-v-1f55f9ed {
  color: #f59e0b;
  /* 橙色 - 警告 */
  font-weight: bold;
}
.timing-badge.data-v-1f55f9ed {
  margin-left: 8rpx;
}
.timing-tag.data-v-1f55f9ed {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  color: white;
  font-weight: bold;
}
.overdue-tag.data-v-1f55f9ed {
  background: #dc2626;
  /* 红色 - 超时 */
}
.warning-tag.data-v-1f55f9ed {
  background: #f59e0b;
  /* 橙色 - 警告 */
}
.normal-tag.data-v-1f55f9ed {
  background: #059669;
  /* 绿色 - 正常 */
}
.quick-actions.data-v-1f55f9ed {
  display: flex;
  gap: 20rpx;
}
.action-btn.data-v-1f55f9ed {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 20rpx;
  border-radius: 12rpx;
  color: white;
  transition: all 0.3s ease;
  font-weight: 500;
}
.action-btn.data-v-1f55f9ed:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  opacity: 0.8;
}
.action-btn text.data-v-1f55f9ed {
  font-size: 26rpx;
  color: white;
  /* 确保文字是白色 */
}
.confirm-btn.data-v-1f55f9ed {
  background-color: #4caf50;
  /* 纯绿色 - 确认完成 */
}
.reject-btn.data-v-1f55f9ed {
  background-color: #f44336;
  /* 纯红色 - 退回重做 */
}
.empty-state.data-v-1f55f9ed {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  min-height: 400rpx;
}
.empty-content.data-v-1f55f9ed {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.empty-image.data-v-1f55f9ed {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
  display: block;
}
.empty-text.data-v-1f55f9ed {
  font-size: 28rpx;
  color: #999;
  display: block;
  text-align: center;
}
.loading-state.data-v-1f55f9ed {
  padding: 40rpx;
}
.custom-loading.data-v-1f55f9ed {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}
.loading-text.data-v-1f55f9ed {
  font-size: 28rpx;
  color: #666;
}
/* 自定义弹窗样式 */
.custom-modal.data-v-1f55f9ed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.modal-content.data-v-1f55f9ed {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  -webkit-animation: modal-in-data-v-1f55f9ed 0.3s ease-out;
          animation: modal-in-data-v-1f55f9ed 0.3s ease-out;
}
@-webkit-keyframes modal-in-data-v-1f55f9ed {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx) scale(0.95);
            transform: translateY(20rpx) scale(0.95);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0) scale(1);
            transform: translateY(0) scale(1);
}
}
@keyframes modal-in-data-v-1f55f9ed {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx) scale(0.95);
            transform: translateY(20rpx) scale(0.95);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0) scale(1);
            transform: translateY(0) scale(1);
}
}
.modal-header.data-v-1f55f9ed {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}
.modal-title.data-v-1f55f9ed {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  /* 深灰色 - 简洁清晰 */
}
.modal-body.data-v-1f55f9ed {
  padding: 30rpx;
}
.modal-label.data-v-1f55f9ed {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.modal-input.data-v-1f55f9ed {
  width: 100%;
  min-height: 160rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}
.modal-input.data-v-1f55f9ed:focus {
  border-color: #3b82f6;
  background-color: #ffffff;
}
.input-counter.data-v-1f55f9ed {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 10rpx;
}
.modal-footer.data-v-1f55f9ed {
  display: flex;
  padding: 20rpx;
  gap: 20rpx;
}
.modal-btn.data-v-1f55f9ed {
  flex: 1;
  padding: 24rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}
.modal-btn.data-v-1f55f9ed:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.cancel-btn.data-v-1f55f9ed {
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
}
.confirm-btn.data-v-1f55f9ed {
  color: white;
  background-color: #4caf50;
  /* 绿色背景 - 与确认按钮保持一致 */
  font-weight: bold;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-1f55f9ed {
  min-height: 100vh;
  background-color: #f8f9fc;
  padding: 20rpx;
}
.page-header.data-v-1f55f9ed {
  background: linear-gradient(135deg, #2E8B57 0%, #20B2AA 100%);
  /* 海绿色到浅海绿色 - 环保主题 */
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(46, 139, 87, 0.3);
}
.page-title.data-v-1f55f9ed {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}
.page-subtitle.data-v-1f55f9ed {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 20rpx;
}
.timing-explanation.data-v-1f55f9ed {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  margin-top: 20rpx;
}
.explanation-text.data-v-1f55f9ed {
  font-size: 24rpx;
  opacity: 0.95;
  line-height: 1.4;
}
.stats-section.data-v-1f55f9ed {
  margin-bottom: 30rpx;
}
.stats-grid.data-v-1f55f9ed {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}
.stat-card.data-v-1f55f9ed {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.stat-card.data-v-1f55f9ed:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.stat-number.data-v-1f55f9ed {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
  /* 浅海绿色 - 执行中 */
  /* 深橙色 - 待确认 */
  /* 森林绿 - 已完成 */
  /* 深红色 - 超时 */
}
.stat-number.assigned.data-v-1f55f9ed {
  color: #20B2AA;
}
.stat-number.pending.data-v-1f55f9ed {
  color: #FF8C00;
}
.stat-number.completed.data-v-1f55f9ed {
  color: #228B22;
}
.stat-number.overdue.data-v-1f55f9ed {
  color: #DC143C;
}
.stat-label.data-v-1f55f9ed {
  font-size: 24rpx;
  color: #666;
}
.filter-tabs.data-v-1f55f9ed {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.filter-tab.data-v-1f55f9ed {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}
.filter-tab.active.data-v-1f55f9ed {
  background: #2E8B57;
  /* 海绿色 - 环保主题 */
  color: white;
}
.task-list.data-v-1f55f9ed {
  margin-bottom: 30rpx;
}
.task-item.data-v-1f55f9ed {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.task-item.data-v-1f55f9ed:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
}
.task-header.data-v-1f55f9ed {
  margin-bottom: 20rpx;
}
.task-title-row.data-v-1f55f9ed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.task-name.data-v-1f55f9ed {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}
.task-status.data-v-1f55f9ed {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  /* 浅海绿色 - 执行中 */
  /* 深橙色 - 待确认 */
  /* 森林绿 - 已完成 */
}
.task-status.status-assigned.data-v-1f55f9ed {
  background: #20B2AA;
}
.task-status.status-pending.data-v-1f55f9ed {
  background: #FF8C00;
}
.task-status.status-completed.data-v-1f55f9ed {
  background: #228B22;
}
.task-project.data-v-1f55f9ed {
  font-size: 24rpx;
  color: #666;
}
.task-content.data-v-1f55f9ed {
  margin-bottom: 20rpx;
}
.task-description.data-v-1f55f9ed {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.task-footer.data-v-1f55f9ed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.responsible-info.data-v-1f55f9ed, .time-info.data-v-1f55f9ed {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8rpx;
}
.responsible-label.data-v-1f55f9ed, .time-label.data-v-1f55f9ed {
  font-size: 24rpx;
  color: #999;
  margin-right: 8rpx;
}
.responsible-name.data-v-1f55f9ed, .time-value.data-v-1f55f9ed {
  font-size: 24rpx;
  color: #333;
}
.responsible-name.overdue.data-v-1f55f9ed, .time-value.overdue.data-v-1f55f9ed {
  color: #DC143C;
  /* 深红色 - 超时 */
  font-weight: bold;
}
.responsible-name.warning.data-v-1f55f9ed, .time-value.warning.data-v-1f55f9ed {
  color: #FF8C00;
  /* 深橙色 - 警告 */
  font-weight: bold;
}
.timing-badge.data-v-1f55f9ed {
  margin-left: 8rpx;
}
.timing-tag.data-v-1f55f9ed {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  color: white;
  font-weight: bold;
}
.overdue-tag.data-v-1f55f9ed {
  background: #DC143C;
  /* 深红色 - 超时 */
}
.warning-tag.data-v-1f55f9ed {
  background: #FF8C00;
  /* 深橙色 - 警告 */
}
.normal-tag.data-v-1f55f9ed {
  background: #228B22;
  /* 森林绿 - 正常 */
}
.quick-actions.data-v-1f55f9ed {
  display: flex;
  gap: 20rpx;
}
.action-btn.data-v-1f55f9ed {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: white;
  transition: all 0.3s ease;
}
.action-btn.data-v-1f55f9ed:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.action-btn text.data-v-1f55f9ed {
  margin-left: 8rpx;
}
.confirm-btn.data-v-1f55f9ed {
  background: #4caf50;
}
.reject-btn.data-v-1f55f9ed {
  background: #f44336;
}
.empty-state.data-v-1f55f9ed {
  text-align: center;
  padding: 100rpx 40rpx;
}
.empty-image.data-v-1f55f9ed {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}
.empty-text.data-v-1f55f9ed {
  font-size: 28rpx;
  color: #999;
}
.loading-state.data-v-1f55f9ed {
  padding: 40rpx;
}
.custom-loading.data-v-1f55f9ed {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}
.loading-text.data-v-1f55f9ed {
  font-size: 28rpx;
  color: #666;
}

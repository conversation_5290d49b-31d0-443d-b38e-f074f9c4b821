(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/ucenter/ucenter"],{"2d76":function(e,t,n){"use strict";n.r(t);var o=n("e2b8"),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},"2e63":function(e,t,n){"use strict";var o=n("7915"),a=n.n(o);a.a},"642e":function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("357b"),n("861b");o(n("3240"));var a=o(n("ff36"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"6b68":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return o}));var o={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))}},a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.hasLogin?e.roleNames&&e.roleNames.length>0:null),o=e.hasLogin?e.uniIDHasRole("supervisor")||e.uniIDHasRole("PM")||e.uniIDHasRole("GM")||e.uniIDHasRole("admin"):null,a=e.hasLogin?e.uniIDHasRole("supervisor")||e.uniIDHasRole("PM")||e.uniIDHasRole("GM")||e.uniIDHasRole("admin"):null,r=e.hasLogin?e.uniIDHasRole("supervisor")||e.uniIDHasRole("PM")||e.uniIDHasRole("GM")||e.uniIDHasRole("admin"):null,s=e.hasLogin?e.uniIDHasRole("reviser")||e.uniIDHasRole("supervisor")||e.uniIDHasRole("PM")||e.uniIDHasRole("GM")||e.uniIDHasRole("admin"):null,i=e.hasLogin&&e.hasLogin&&e.hasRole?e.todoList.length:null,u=e.hasLogin&&e.hasLogin&&e.hasRole&&i>0?e.__map(e.todoList,(function(t,n){var o=e.__get_orig(t),a=e.getProjectName(t.project),r=e.getTodoTimeText(t),s=e.getTodoTypeText(t.workflowStatus);return{$orig:o,m4:a,m5:r,m6:s}})):null;e.$mp.data=Object.assign({},{$root:{g0:n,m0:o,m1:a,m2:r,m3:s,g1:i,l0:u}})},r=[]},7915:function(e,t,n){},e2b8:function(e,t,n){"use strict";(function(e,o){var a=n("47a9"),r=n("3b2d");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=a(n("7eb4")),i=a(n("ee10")),u=n("423e"),l=n("fb20"),c=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!==typeof e)return{default:e};var n=h(t);if(n&&n.has(e))return n.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var i=a?Object.getOwnPropertyDescriptor(e,s):null;i&&(i.get||i.set)?Object.defineProperty(o,s,i):o[s]=e[s]}o.default=e,n&&n.set(e,o);return o}(n("eddf")),d=n("7fc2"),f=a(n("7e11"));function h(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(h=function(e){return e?n:t})(e)}var p={components:{PEmptyState:function(){n.e("components/p-empty-state/p-empty-state").then(function(){return resolve(n("9b76"))}.bind(null,n)).catch(n.oe)}},data:function(){var t=e.getStorageSync("uni_id_token")||"",n=!!t,o=c.default.get(c.default.cacheKeys.USER_INFO)||{},a={nickname:o.nickname||"加载中...",username:o.username||"",avatar_file:o.avatar_file||null,_id:o._id||""};return{roleNames:[],todoList:[],todoCount:0,userRole:[],hasRole:!1,isWechatPlatform:!1,isRefreshing:!1,lastRefreshTime:0,localUserInfo:a,localHasLogin:n,todoUpdateTimer:null,isTokenValid:!0,responsibleTaskCount:0,avatarLoaded:!1}},computed:{userInfo:function(){return this.localUserInfo||{}},hasLogin:function(){return(u.store.hasLogin||this.localHasLogin)&&this.isTokenValid},hasAdminPermission:function(){return this.userRole.some((function(e){return["admin"].includes(e)}))},hasResponsiblePermission:function(){return this.userRole.some((function(e){return["responsible"].includes(e)}))}},created:function(){var t=this;e.$on("uni-id-pages-login-success",(function(){c.default.remove(c.default.cacheKeys.USER_ROLE),t.refreshData(!0),f.default.onLoginSuccess()})),e.$on("todo-count-updated",(function(e){t.hasLogin&&t.hasRole&&(t.todoCount=e,t.getTodoList())})),e.$on("refresh-todo-list",(function(){t.hasLogin&&t.hasRole&&t.refreshData(!1)})),e.$on("force-logout-ui-update",(function(){t.performLogout(!1)})),e.$on("feedback-updated",(function(){t.hasLogin&&t.hasRole&&setTimeout((function(){t.refreshData(!1)}),100)})),e.$on("ucenter-need-refresh",(function(e){t.hasLogin&&t.hasRole&&(console.log("收到用户中心刷新事件:",e),t.refreshData(!1))})),e.$on("cross-device-update-detected",(function(e){if(t.hasLogin&&t.hasRole){var n=t.shouldRefreshOnCrossDeviceUpdate(e);n&&(console.log("用户中心收到跨设备更新通知，静默刷新数据"),t.refreshData(!1))}})),this.isWechatPlatform=!0,this.startTodoUpdateTimer()},beforeDestroy:function(){e.$off("uni-id-pages-login-success"),e.$off("todo-count-updated"),e.$off("refresh-todo-list"),e.$off("force-logout-ui-update"),e.$off("feedback-updated"),e.$off("ucenter-need-refresh"),e.$off("cross-device-update-detected"),this.clearTodoUpdateTimer()},onLoad:function(){this.hasLogin&&this.refreshData(!0)},onShow:function(){if(this.hasLogin){var e=Date.now();e-this.lastRefreshTime>3e4&&this.refreshData(!1)}(0,l.checkUpdate)(),this.checkTokenStatus(),this.hasLogin&&this.hasRole&&setTimeout((function(){f.default.forceSyncBadge()}),300)},onPullDownRefresh:function(){this.hasLogin?this.refreshData(!0).then((function(){e.stopPullDownRefresh(),e.showToast({title:"刷新成功",icon:"success",duration:1500})})).catch((function(t){console.error("刷新数据失败:",t),e.stopPullDownRefresh(),e.showToast({title:"刷新失败",icon:"error",duration:1500})})):e.stopPullDownRefresh()},watch:{hasLogin:function(e){var t=this;e&&this.$nextTick((function(){t.refreshData(!0)}))},"userInfo.avatar_file.url":{handler:function(e,t){e!==t&&(this.avatarLoaded=!1)},deep:!0},"userInfo.avatar":{handler:function(e,t){e!==t&&(this.avatarLoaded=!1)}}},methods:{onAvatarLoad:function(){this.avatarLoaded=!0},onAvatarError:function(){this.avatarLoaded=!1,console.log("头像加载失败，显示默认头像")},showFeatureInDevelopment:function(){e.showToast({title:"功能开发中，敬请期待",icon:"none",duration:1e3})},handleVisibilityChange:function(){},startTodoUpdateTimer:function(){var e=this;this.clearTodoUpdateTimer(),this.todoUpdateTimer=setInterval((function(){e.hasLogin&&e.hasRole&&e.updateTodoCountFromBadge()}),3e4),this.hasLogin&&this.hasRole&&this.updateTodoCountFromBadge()},clearTodoUpdateTimer:function(){this.todoUpdateTimer&&(clearInterval(this.todoUpdateTimer),this.todoUpdateTimer=null)},updateTodoCountFromBadge:function(){var e=this;return(0,i.default)(s.default.mark((function t(){var n;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,f.default.getTodoCount();case 3:n=t.sent,e.todoCount=n,t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("更新待办数量失败:",t.t0);case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},refreshData:function(){var t=arguments,n=this;return(0,i.default)(s.default.mark((function o(){var a;return s.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(a=t.length>0&&void 0!==t[0]&&t[0],n.hasLogin&&!n.isRefreshing){o.next=3;break}return o.abrupt("return",Promise.resolve());case 3:return n.isRefreshing=!0,a&&e.showLoading({title:"加载中..."}),o.prev=5,o.next=8,n.getUserInfo();case 8:if(n.userRole&&0!==n.userRole.length&&n.roleNames&&0!==n.roleNames.length){o.next=11;break}return o.next=11,n.getUserRole();case 11:if(!n.hasRole){o.next=16;break}return o.next=14,n.getTodoList();case 14:o.next=18;break;case 16:n.todoList=[],n.todoCount=0;case 18:if(!n.hasResponsiblePermission){o.next=23;break}return o.next=21,n.getResponsibleTaskCount();case 21:o.next=24;break;case 23:n.responsibleTaskCount=0;case 24:return n.lastRefreshTime=Date.now(),o.abrupt("return",Promise.resolve());case 28:return o.prev=28,o.t0=o["catch"](5),o.abrupt("return",Promise.reject(o.t0));case 31:return o.prev=31,a&&e.hideLoading(),n.isRefreshing=!1,o.finish(31);case 35:case"end":return o.stop()}}),o,null,[[5,28,31,35]])})))()},getUserRole:function(){var e=this;return(0,i.default)(s.default.mark((function t(){var n,a,r,i,u,l;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.hasLogin){t.next=6;break}return e.roleNames=["未登录"],e.userRole=[],e.hasRole=!1,t.abrupt("return");case 6:if(n=c.default.get(c.default.cacheKeys.USER_ROLE),!n){t.next=12;break}return e.userRole=n.userRole,e.roleNames=n.roleNames,e.hasRole=n.hasRole,t.abrupt("return");case 12:return a=o.database(),t.next=15,a.collection("uni-id-users").where("'_id' == $cloudEnv_uid").field("role").get();case 15:r=t.sent,i=r.result,i.data&&i.data.length>0?(e.userRole=i.data[0].role||[],u={admin:"管理员",responsible:"责任人",reviser:"发布人",supervisor:"主管",PM:"副厂长",GM:"厂长",logistics:"后勤员",dispatch:"调度员",Integrated:"综合员",operator:"设备员",technician:"工艺员",mechanic:"技术员",user:"普通员工"},e.hasRole=e.userRole.some((function(e){return["supervisor","PM","GM","admin","responsible"].includes(e)})),e.userRole.length>0?e.roleNames=e.userRole.map((function(e){return u[e]||e})):e.roleNames=["普通用户"],c.default.set(c.default.cacheKeys.USER_ROLE,{userRole:e.userRole,roleNames:e.roleNames,hasRole:e.hasRole},60)):(e.roleNames=["普通用户"],e.userRole=[],e.hasRole=!1),t.next=26;break;case 20:t.prev=20,t.t0=t["catch"](0),l=t.t0.message||t.t0.toString(),l.includes("token")||l.includes("unauthorized")||l.includes("expired")?(console.log("检测到token相关错误，清除待办数据"),e.todoList=[],e.todoCount=0,e.hasRole=!1,e.roleNames=["普通用户"]):e.roleNames=["普通用户"],e.userRole=[],e.hasRole=!1;case 26:case"end":return t.stop()}}),t,null,[[0,20]])})))()},getProjectName:function(e){return e||"未分类"},getTodoList:function(){var e=this;return(0,i.default)(s.default.mark((function t(){var n,a,r,i,u;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.hasLogin&&e.hasRole){t.next=4;break}return e.todoList=[],e.todoCount=0,t.abrupt("return");case 4:return t.prev=4,n=o.database(),a=n.command,r=e.buildTodoQueryConditions(a),t.next=10,n.collection("feedback").where(r).count();case 10:if(i=t.sent,i.result&&void 0!==i.result.total&&(e.todoCount=i.result.total),!(e.todoCount>0)){t.next=19;break}return t.next=15,n.collection("feedback").where(r).orderBy("createTime","desc").limit(3).get();case 15:u=t.sent,u.result&&u.result.data?e.todoList=u.result.data:e.todoList=[],t.next=20;break;case 19:e.todoList=[];case 20:t.next=26;break;case 22:t.prev=22,t.t0=t["catch"](4),e.todoList=[],e.todoCount=0;case 26:case"end":return t.stop()}}),t,null,[[4,22]])})))()},getResponsibleTaskCount:function(){var e=this;return(0,i.default)(s.default.mark((function t(){var n,a;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.hasLogin&&e.hasResponsiblePermission){t.next=3;break}return e.responsibleTaskCount=0,t.abrupt("return");case 3:return t.prev=3,t.next=6,o.callFunction({name:"feedback-list",data:{action:"getMyTasks",status:"assigned_to_responsible"}});case 6:n=t.sent,n.result&&0===n.result.code&&(e.responsibleTaskCount=(null===(a=n.result.data.stats)||void 0===a?void 0:a.assigned)||0),t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](3),console.error("获取负责人任务数量失败:",t.t0),e.responsibleTaskCount=0;case 14:case"end":return t.stop()}}),t,null,[[3,10]])})))()},buildTodoQueryConditions:function(e){var t=[];if(this.userRole.includes("supervisor")&&t.push({workflowStatus:"pending_supervisor"}),this.userRole.includes("PM")&&t.push({workflowStatus:"pending_pm"}),this.userRole.includes("GM")&&(t.push({workflowStatus:"pending_gm"}),t.push({workflowStatus:"gm_approved_pending_assign"}),t.push({workflowStatus:"completed_by_responsible"})),this.userRole.includes("responsible"))try{var n=o.getCurrentUserInfo(),a=null===n||void 0===n?void 0:n.uid;a&&t.push({workflowStatus:"assigned_to_responsible",responsibleUserId:a})}catch(r){console.warn("获取用户ID失败:",r)}return this.userRole.includes("admin")?{workflowStatus:e.in(["pending_supervisor","pending_pm","pending_gm","gm_approved_pending_assign","completed_by_responsible"])}:t.length>0?e.or(t):{}},formatDate:function(e){return e?(0,d.formatDate)(e):""},getTodoTimeText:function(e){return"assigned_to_responsible"===e.workflowStatus&&e.assignedTime?"指派时间：".concat((0,d.formatDate)(e.assignedTime,"MM-DD HH:mm")):"completed_by_responsible"===e.workflowStatus&&e.completedByResponsibleTime?"完成时间：".concat((0,d.formatDate)(e.completedByResponsibleTime,"MM-DD HH:mm")):"提交时间：".concat((0,d.formatDate)(e.createTime,"MM-DD HH:mm"))},getTodoTypeText:function(e){return{pending_supervisor:"待主管审核",pending_pm:"待副厂长审核",pending_gm:"待厂长审核",gm_approved_pending_assign:"待指派负责人",assigned_to_responsible:"待我完成",completed_by_responsible:"待最终确认"}[e]||"待处理"},handleTodoClick:function(t){"assigned_to_responsible"===t.workflowStatus?e.navigateTo({url:"/pages/ucenter_pkg/responsible-tasks"}):this.goToExamine(t._id)},goToExamine:function(t){var n=this;e.navigateTo({url:"/pages/feedback_pkg/examine?id=".concat(t),events:{refreshData:function(){n.refreshData(!1)}}})},goToReadNewsLog:function(){e.navigateTo({url:"/pages/notice/list"})},exportexcel:function(){e.navigateTo({url:"/pages/ucenter_pkg/export-excel"})},checkExportPermission:function(){var t=this.userRole.some((function(e){return["reviser","supervisor","PM","GM","admin","dispatch"].includes(e)}));t?this.exportexcel():e.showToast({title:"权限不够,无法查看",icon:"none",duration:2e3})},goToResponsibleTasks:function(){this.hasResponsiblePermission?e.navigateTo({url:"/pages/ucenter_pkg/responsible-tasks",fail:function(t){console.error("跳转任务管理页面失败:",t),e.showToast({title:"页面跳转失败",icon:"none"})}}):e.showToast({title:"权限不足，无法访问任务管理",icon:"none",duration:2e3})},goToUserManagement:function(){this.hasAdminPermission?e.navigateTo({url:"/pages/ucenter_pkg/user-management",fail:function(t){console.error("跳转用户管理页面失败:",t),e.showToast({title:"页面跳转失败",icon:"none"})}}):e.showToast({title:"权限不足，无法访问用户管理",icon:"none",duration:2e3})},modifyNickname:function(){e.navigateTo({url:"/uni_modules/uni-id-pages/pages/userinfo/userinfo?showLoginManage=false&showUserInfo=false&showSet=false&showEdit=true"})},logout:function(){var t=this;return(0,i.default)(s.default.mark((function n(){var a,r;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,e.showModal({title:"提示",content:"确定要退出登录吗？",confirmText:"退出",cancelText:"取消"});case 3:if(a=n.sent,!a.confirm){n.next=20;break}return e.showLoading({title:"退出中...",mask:!0}),f.default.forceCleanBadge(),n.prev=7,r=o.importObject("uni-id-co"),n.next=11,r.logout();case 11:n.next=16;break;case 13:n.prev=13,n.t0=n["catch"](7),console.error("云函数退出登录失败:",n.t0);case 16:t.performLogout(!1),setTimeout((function(){f.default.forceCleanBadge()}),100),e.hideLoading(),e.showToast({title:"已退出登录",icon:"success"});case 20:n.next=25;break;case 22:n.prev=22,n.t1=n["catch"](0),e.showToast({title:"退出登录失败",icon:"none"});case 25:case"end":return n.stop()}}),n,null,[[0,22],[7,13]])})))()},goToTodoList:function(){e.navigateTo({url:"/pages/ucenter_pkg/todo"})},goToLogin:function(){e.navigateTo({url:"/uni_modules/uni-id-pages/pages/login/login-withoutpwd"})},getUserInfo:function(){var t=this;return(0,i.default)(s.default.mark((function n(){var a,r,i,u,l,d;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,a=e.getStorageSync("uni_id_token")||"",t.localHasLogin=!!a,t.localHasLogin){n.next=6;break}return t.localUserInfo={},n.abrupt("return");case 6:if(r=c.default.get(c.default.cacheKeys.USER_INFO)||{},!r.nickname&&!r.username){n.next=10;break}return t.localUserInfo=r,n.abrupt("return");case 10:return i=o.database(),n.next=13,i.collection("uni-id-users").where("'_id' == $cloudEnv_uid").field("nickname, username, avatar_file").get();case 13:u=n.sent,l=u.result,l.data&&l.data.length>0?(d=l.data[0],t.localUserInfo={_id:d._id,nickname:d.nickname||d.username||"未设置昵称",username:d.username||d.nickname||"未设置昵称",avatar_file:d.avatar_file||null},t.avatarLoaded=!1,c.default.set(c.default.cacheKeys.USER_INFO,t.localUserInfo)):(t.localUserInfo={nickname:"用户",username:"user"},t.avatarLoaded=!1),n.next=22;break;case 18:n.prev=18,n.t0=n["catch"](0),console.error("获取用户信息失败:",n.t0),t.localUserInfo={nickname:"用户",username:"user"};case 22:case"end":return n.stop()}}),n,null,[[0,18]])})))()},navToLogin:function(){e.navigateTo({url:"/uni_modules/uni-id-pages/pages/login/login-withoutpwd"})},navTo:function(t){if("/pages/honor_pkg/gallery/index"!==t){var n=c.default.get("recent_pages",[]);n.includes(t)||(n.unshift(t),n.length>10&&n.pop(),c.default.set("recent_pages",n)),e.navigateTo({url:t,fail:function(t){console.error("导航失败:",t),e.showToast({title:"页面不存在",icon:"none"})}})}else this.navToHonorGallery(t)},navToHonorGallery:function(t){var n=c.default.get("recent_pages",[]);n.includes(t)||(n.unshift(t),n.length>10&&n.pop(),c.default.set("recent_pages",n)),e.navigateTo({url:t,animationType:"slide-in-right",animationDuration:200,success:function(){},fail:function(n){console.error("导航失败:",n),e.navigateTo({url:t,fail:function(t){e.showToast({title:"页面不存在",icon:"none"})}})}})},checkTokenStatus:function(){var t=this;return(0,i.default)(s.default.mark((function n(){var o,a;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,o=e.getStorageSync("uni_id_token"),o){n.next=5;break}return t.handleTokenInvalid(),n.abrupt("return");case 5:return n.next=7,t.validateToken();case 7:a=n.sent,a||t.handleTokenInvalid(),n.next=15;break;case 11:n.prev=11,n.t0=n["catch"](0),console.error("验证登录状态失败:",n.t0),t.handleTokenInvalid();case 15:case"end":return n.stop()}}),n,null,[[0,11]])})))()},validateToken:function(){return(0,i.default)(s.default.mark((function t(){var n,o;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=e.getStorageSync("uni_id_token"),n){t.next=4;break}return t.abrupt("return",!1);case 4:if(o=e.getStorageSync("uni_id_token_expired"),!(o<Date.now())){t.next=7;break}return t.abrupt("return",!1);case 7:return t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t["catch"](0),console.error("Token validation error:",t.t0),t.abrupt("return",!1);case 14:case"end":return t.stop()}}),t,null,[[0,10]])})))()},performLogout:function(){var t=this,n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.localHasLogin=!1,e.removeStorageSync("uni_id_token"),e.removeStorageSync("uni_id_token_expired"),e.removeStorageSync("uni_id_user"),e.removeStorageSync("uni-id-pages-userInfo"),c.default.remove(c.default.cacheKeys.USER_INFO),c.default.remove(c.default.cacheKeys.USER_ROLE),c.default.remove(c.default.cacheKeys.PROJECT_OPTIONS),u.mutations.setUserInfo({},{cover:!0}),f.default.clearBadge(),this.clearSensitiveCache(),Object.assign(this,{roleNames:[],todoList:[],todoCount:0,userRole:[],hasRole:!1,localUserInfo:{},isTokenValid:!1}),n&&e.showToast({title:"登录已过期，请重新登录",icon:"none",duration:2e3}),this.$nextTick((function(){return t.getUserInfo()}))},clearSensitiveCache:function(){try{var t=e.getStorageInfoSync(),n=t.keys,o=["user_info_","user_mgmt_"],a=["_DC_STAT_UUID",(0,c.getCacheKey)("recent_pages"),"last_app_start_time","uni-id-pages-userInfo"];n.forEach((function(t){if(!a.includes(t)){var n=o.some((function(e){return t===e||t.startsWith(e)}));n&&(e.removeStorageSync(t))}}))}catch(r){console.error("清除敏感缓存失败:",r)}},handleTokenInvalid:function(){this.performLogout(!1)},shouldRefreshOnCrossDeviceUpdate:function(e){var t=Date.now()-this.lastRefreshTime;if(t<15e3)return!1;if(e.updateTypes){var n=["workflow_status_changed","feedback_submitted"],o=e.updateTypes.some((function(e){return n.includes(e)}));if(o)return!0}return e.updateCount>1}}};t.default=p}).call(this,n("df3c")["default"],n("861b")["uniCloud"])},ff36:function(e,t,n){"use strict";n.r(t);var o=n("6b68"),a=n("2d76");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("2e63");var s=n("828b"),i=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,"4bb0b824",null,!1,o["a"],void 0);t["default"]=i.exports}},[["642e","common/runtime","common/vendor"]]]);
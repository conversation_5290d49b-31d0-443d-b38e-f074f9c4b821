{
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "株水小智",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/patrol/index",
			"style": {
				"navigationBarTitleText": "巡视打卡",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/feedback/list",
			"style": {
				"navigationBarTitleText": "问题反馈",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/ucenter/ucenter",
			"style": {
				"navigationBarTitleText": "用户中心",
				"enablePullDownRefresh": true
			}
		}
	],
	"subPackages": [
		{
			"root": "pages/feedback_pkg",
			"pages": [
				{
					"path": "edit",
					"style": {
						"navigationBarTitleText": "找茬编辑"
					}
				},
				{
					"path": "examine",
					"style": {
						"navigationBarTitleText": "审核流程"
					}
				}
			]
		},
		{
			"root": "pages/ucenter_pkg",
			"pages": [
				{
					"path": "todo",
					"style": {
						"navigationBarTitleText": "我的待办",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "export-excel",
					"style": {
						"navigationBarTitleText": "导出Excel"
					}
				},
				{
					"path": "user-management",
					"style": {
						"navigationBarTitleText": "用户管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "responsible-tasks",
					"style": {
						"navigationBarTitleText": "我的任务",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "gm-supervision",
					"style": {
						"navigationBarTitleText": "任务监督",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "complete-task",
					"style": {
						"navigationBarTitleText": "完成任务",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/notice",
			"pages": [
				{
					"path": "add",
					"style": {
						"navigationBarTitleText": "公告新增",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "edit",
					"style": {
						"navigationBarTitleText": "公告编辑",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "list",
					"style": {
						"navigationBarTitleText": "公告通知",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "detail",
					"style": {
						"navigationBarTitleText": "公告详细"
					}
				}
			]
		},
		{
			"root": "uni_modules/uni-id-pages/pages",
			"pages": [
				{
					"path": "login/login-withoutpwd",
					"style": {
						"navigationBarTitleText": "微信一键登录"
					}
				},
				{
					"path": "login/login-withpwd",
					"style": {
						"navigationBarTitleText": "账号密码登录"
					}
				},
				{
					"path": "userinfo/change_pwd/change_pwd",
					"style": {
						"navigationBarTitleText": "密码修改"
					}
				},
				{
					"path": "userinfo/userinfo",
					"style": {
						"navigationBarTitleText": "个人资料"
					}
				}
			]
		},
		{
			"root": "pages/honor_pkg",
			"pages": [
				{
					"path": "gallery/index",
					"style": {
						"navigationBarTitleText": "荣誉展厅",
						"navigationStyle": "custom",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "admin/index",
					"style": {
						"navigationBarTitleText": "荣誉管理",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "admin/add-record",
					"style": {
						"navigationBarTitleText": "添加表彰记录",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "admin/batch-manager",
					"style": {
						"navigationBarTitleText": "智能批次管理",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "admin/type-manager",
					"style": {
						"navigationBarTitleText": "荣誉类型管理",
						"navigationStyle": "custom"
					}
				}
			]
		},
		{
			"root": "pages/info_pkg",
			"pages": [
				{
					"path": "user-guide",
					"style": {
						"navigationBarTitleText": "用户指南",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "privacy",
					"style": {
						"navigationBarTitleText": "隐私政策",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/patrol_pkg",
			"pages": [
				{
					"path": "checkin/index",
					"style": {
						"navigationBarTitleText": "打卡签到",
						"navigationStyle": "custom",
						"enablePullDownRefresh": false,
						"disableScroll": true
					}
				},
				{
					"path": "point/index",
					"style": {
						"navigationBarTitleText": "点位管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "point/add",
					"style": {
						"navigationBarTitleText": "添加点位",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "point/edit",
					"style": {
						"navigationBarTitleText": "编辑点位",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "point/detail",
					"style": {
						"navigationBarTitleText": "点位详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "route/index",
					"style": {
						"navigationBarTitleText": "线路管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "route/add",
					"style": {
						"navigationBarTitleText": "添加线路",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "route/edit",
					"style": {
						"navigationBarTitleText": "编辑线路",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "route/detail",
					"style": {
						"navigationBarTitleText": "线路详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shift/index",
					"style": {
						"navigationBarTitleText": "班次管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shift/add",
					"style": {
						"navigationBarTitleText": "添加班次",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shift/edit",
					"style": {
						"navigationBarTitleText": "编辑班次",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shift/detail",
					"style": {
						"navigationBarTitleText": "班次详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "task/index",
					"style": {
						"navigationBarTitleText": "任务管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "task/add",
					"style": {
						"navigationBarTitleText": "添加任务",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "task/batch-add",
					"style": {
						"navigationBarTitleText": "批量添加任务",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "task/detail",
					"style": {
						"navigationBarTitleText": "任务详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "task/edit",
					"style": {
						"navigationBarTitleText": "编辑任务",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "record/index",
					"style": {
						"navigationBarTitleText": "巡视记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "record/detail",
					"style": {
						"navigationBarTitleText": "记录详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "record/route-detail",
					"style": {
						"navigationBarTitleText": "线路记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "point/qrcode",
					"style": {
						"navigationBarTitleText": "二维码管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "point/qrcode-batch",
					"style": {
						"navigationBarTitleText": "批量二维码管理",
						"enablePullDownRefresh": false
					}
				}
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "株水小智",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "#7A7E83",
		"selectedColor": "#1677FF",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"list": [
			{
				"pagePath": "pages/index/index",
				"iconPath": "static/tabbar/home.png",
				"selectedIconPath": "static/tabbar/home_active.png",
				"text": "找茬"
			},
			{
				"pagePath": "pages/feedback/list",
				"iconPath": "static/tabbar/feedback.png",
				"selectedIconPath": "static/tabbar/feedback_active.png",
				"text": "反馈"
			},
			{
				"pagePath": "pages/patrol/index",
				"iconPath": "static/tabbar/patrol.png",
				"selectedIconPath": "static/tabbar/patrol_active.png",
				"text": "巡视"
			},
			{
				"pagePath": "pages/ucenter/ucenter",
				"iconPath": "static/tabbar/user.png",
				"selectedIconPath": "static/tabbar/user_active.png",
				"text": "我的"
			}
		]
	},
	"uniIdRouter": {
		// #ifdef MP-WEIXIN 
		"loginPage": "uni_modules/uni-id-pages/pages/login/login-withoutpwd",
		// #endif 
		
		// #ifndef MP-WEIXIN 
		"loginPage": "uni_modules/uni-id-pages/pages/login/login-withpwd",
		// #endif 
		"needLogin": [
			"pages/notice/.*",
			"pages/feedback_pkg/.*",
			"pages/ucenter_pkg/.*",
			"pages/patrol_pkg/.*",
			"pages/honor_pkg/.*",
			"uni_modules/uni-id-pages/pages/userinfo/.*"
		],
		"resToLogin": false
	}
}

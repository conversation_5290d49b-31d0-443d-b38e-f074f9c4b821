<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能导出系统 - 株水小智</title>
    <script src="libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="libs/all.min.css">
    <script src="libs/echarts.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #1e40af 100%);
        }
        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .animate-pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .img-zoom {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: zoom-in;
        }
        .img-zoom:hover {
            transform: scale(1.08);
            box-shadow: 0 0 32px 8px #6ed0fa, 0 2px 32px 0 #4f8ef7;
            z-index: 10;
        }
        .modal-bg {
            background: rgba(0,0,0,0.7);
            backdrop-filter: blur(6px);
        }
        .subtitle-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 0.5rem;
            margin-bottom: 2rem;
        }
        .subtitle-bar:before, .subtitle-bar:after {
            content: "";
            flex: 1;
            height: 2px;
            background: linear-gradient(90deg, #38bdf8 0%, #a7f3d0 100%);
            margin: 0 1rem;
            border-radius: 1px;
            box-shadow: 0 0 8px rgba(56, 189, 248, 0.6);
        }
        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .glass-effect:hover {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .bubble {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.13);
            pointer-events: none;
            animation: floatBubble 12s linear infinite;
        }
        @keyframes floatBubble {
            0% { transform: translateY(0) scale(1); opacity: 0.7; }
            50% { opacity: 1; }
            100% { transform: translateY(-120vh) scale(1.2); opacity: 0; }
        }
        @keyframes orbit {
            0% { transform: rotate(0deg) translateX(60px) rotate(0deg);}
            100% { transform: rotate(360deg) translateX(60px) rotate(-360deg);}
        }
        .bubble-orbit { animation: orbit 12s linear infinite; }
    </style>
</head>
<body class="gradient-bg min-h-screen flex flex-col">
    <!-- 进度条 -->
    <div class="fixed top-0 left-0 w-full h-1 bg-white/20 z-50">
        <div class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-1000" style="width: 0%"></div>
    </div>
    <!-- 气泡粒子动画 -->
    <div class="absolute inset-0 z-0 overflow-hidden">
        <div class="bubble" style="width:60px;height:60px;left:10vw;bottom:10vh;animation-delay:0s;"></div>
        <div class="bubble" style="width:40px;height:40px;left:80vw;bottom:20vh;animation-delay:2s;"></div>
        <div class="bubble" style="width:80px;height:80px;left:30vw;bottom:5vh;animation-delay:4s;"></div>
        <div class="bubble" style="width:30px;height:30px;left:60vw;bottom:15vh;animation-delay:6s;"></div>
        <div class="bubble" style="width:50px;height:50px;left:50vw;bottom:8vh;animation-delay:1s;"></div>
    </div>
    

    <div class="relative z-10 flex-1 flex flex-col justify-center">
        <!-- 头部 -->
        <header class="text-center py-8">
            <div class="animate-pulse-slow">
                <h1 class="text-5xl font-bold text-white mb-4 flex items-center justify-center">
                    <i class="fas fa-download mr-4 text-blue-200"></i>
                    智能导出系统
                </h1>
                <div class="subtitle-bar">
                    <span class="text-xl text-blue-100 font-light">数据管理优化</span>
                </div>
            </div>
        </header>
        <!-- 主要内容 -->
        <main class="flex-1 px-8 pb-8">
            <div class="max-w-7xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-8 h-full">
                    <!-- 左侧问题与解决方案 -->
                    <div class="space-y-6">
                        <!-- 管理痛点 -->
                        <div class="glass-effect rounded-2xl p-8 transform hover:scale-105 transition-all duration-300">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center mr-4">
                                    <i class="fas fa-exclamation-triangle text-white text-xl"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-white">管理痛点</h3>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center p-4 bg-red-500/40 rounded-xl">
                                    <i class="fas fa-times-circle text-red-400 mr-3 text-lg"></i>
                                    <span class="text-white">传统打卡器数据需手动录入，格式混乱</span>
                                </div>
                                <div class="flex items-center p-4 bg-red-500/40 rounded-xl">
                                    <i class="fas fa-times-circle text-red-400 mr-3 text-lg"></i>
                                    <span class="text-white">无法按人员/时间筛选，统计耗时</span>
                                </div>
                                <div class="flex items-center p-4 bg-red-500/40 rounded-xl">
                                    <i class="fas fa-times-circle text-red-400 mr-3 text-lg"></i>
                                    <span class="text-white">报表格式不统一，影响管理决策</span>
                                </div>
                            </div>
                        </div>

                        <!-- 解决方案 -->
                        <div class="glass-effect rounded-2xl p-8 transform hover:scale-105 transition-all duration-300">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mr-4">
                                    <i class="fas fa-check-circle text-white text-xl"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-white">解决方案</h3>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center p-4 bg-green-500/40 rounded-xl">
                                    <i class="fas fa-mobile-alt text-green-400 mr-3 text-lg"></i>
                                    <span class="text-white">多种记录智能导出（找茬数据，巡视数据）</span>
                                </div>
                                <div class="flex items-center p-4 bg-green-500/40 rounded-xl">
                                    <i class="fas fa-file-alt text-green-400 mr-3 text-lg"></i>
                                    <span class="text-white">标准化格式（内置Excel/Word模板，数据自动校验）</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧图表和效果 -->
                    <div class="space-y-6">
                        <!-- 导出对比图表 -->
                        <div class="glass-effect rounded-2xl p-8 h-80">
                            <h3 class="text-2xl font-bold text-white mb-6 flex items-center">
                                <i class="fas fa-chart-line mr-3 text-purple-300"></i>
                                导出方式对比
                            </h3>
                            <div id="exportChart" class="w-full h-64"></div>
                        </div>

                        <!-- 实际效果 -->
                        <div class="glass-effect rounded-2xl p-8">
                            <h3 class="text-2xl font-bold text-white mb-6 flex items-center">
                                <i class="fas fa-trophy mr-3 text-yellow-300"></i>
                                实际效果
                            </h3>
                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-center p-4 bg-white/10 rounded-xl">
                                    <div class="text-2xl font-bold text-green-400 mb-2">8h→1h</div>
                                    <div class="text-white/80 text-sm">数据整理时间</div>
                                </div>
                                <div class="text-center p-4 bg-white/10 rounded-xl">
                                    <div class="text-2xl font-bold text-blue-400 mb-2">500%</div>
                                    <div class="text-white/80 text-sm">效率提升</div>
                                </div>
                                <div class="text-center p-4 bg-white/10 rounded-xl">
                                    <div class="text-2xl font-bold text-purple-400 mb-2">15%→0</div>
                                    <div class="text-white/80 text-sm">报表错误率</div>
                                </div>
                            </div>
                        </div>

                        <!-- 系统截图 -->
                        <div class="glass-effect rounded-2xl p-6">
                            <h3 class="text-xl font-bold text-white mb-4 flex items-center">
                                <i class="fas fa-image mr-3 text-blue-300"></i>
                                系统界面
                            </h3>
                            <div class="grid grid-cols-2 gap-4">
                                <img src="images/微信图片_20250712225346.jpg" alt="导出界面1" class="img-zoom w-full h-32 object-cover rounded-lg shadow-lg" onclick="showBig(this.src)">
                                <img src="images/微信图片_20250712225350.jpg" alt="导出界面2" class="img-zoom w-full h-32 object-cover rounded-lg shadow-lg" onclick="showBig(this.src)">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    <footer class="text-center py-6 text-blue-200 flex justify-center space-x-8 relative z-20 flex-shrink-0">
        <button onclick="prevSlide()" class="px-6 py-2 rounded-xl bg-white/20 hover:bg-white/30 transition text-lg font-semibold"><i class="fas fa-arrow-left mr-2"></i>上一页</button>
        <button onclick="nextSlide()" class="px-6 py-2 rounded-xl bg-white/20 hover:bg-white/30 transition text-lg font-semibold">下一页<i class="fas fa-arrow-right ml-2"></i></button>
    </footer>
    <!-- 图片放大 Modal -->
    <div id="img-modal" class="fixed inset-0 hidden items-center justify-center z-50">
        <div class="modal-bg absolute inset-0" onclick="closeBig()"></div>
        <button id="img-prev" class="absolute left-8 top-1/2 -translate-y-1/2 bg-white/30 hover:bg-white/50 text-2xl rounded-full w-12 h-12 flex items-center justify-center z-20" style="outline:none;border:none;" onclick="showPrevImg(event)"><i class="fas fa-chevron-left"></i></button>
        <img id="modal-img" src="" class="max-w-full max-h-[95vh] rounded-xl shadow-2xl border-4 border-white relative z-10" style="transition: transform 0.2s; cursor: grab;">
        <button id="img-next" class="absolute right-8 top-1/2 -translate-y-1/2 bg-white/30 hover:bg-white/50 text-2xl rounded-full w-12 h-12 flex items-center justify-center z-20" style="outline:none;border:none;" onclick="showNextImg(event)"><i class="fas fa-chevron-right"></i></button>
    </div>
    <script>
        // 初始化图表
        function initCharts() {
            // 导出能力雷达图
            const exportChart = echarts.init(document.getElementById('exportChart'));
            const exportOption = {
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    data: ['传统导出', '智能导出'],
                    textStyle: { color: '#fff' }
                },
                radar: {
                    indicator: [
                        { name: '操作便捷性', max: 100 },
                        { name: '数据准确性', max: 100 },
                        { name: '格式统一性', max: 100 },
                        { name: '筛选功能', max: 100 },
                        { name: '处理速度', max: 100 },
                        { name: '错误率', max: 100 }
                    ],
                    axisName: {
                        color: '#fff'
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(255, 255, 255, 0.3)'
                        }
                    },
                    splitArea: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(255, 255, 255, 0.3)'
                        }
                    }
                },
                series: [
                    {
                        name: '导出能力对比',
                        type: 'radar',
                        data: [
                            {
                                value: [30, 60, 40, 20, 25, 80],
                                name: '传统导出',
                                itemStyle: { color: '#EF4444' },
                                areaStyle: {
                                    color: 'rgba(239, 68, 68, 0.2)'
                                }
                            },
                            {
                                value: [95, 99, 100, 95, 90, 5],
                                name: '智能导出',
                                itemStyle: { color: '#10B981' },
                                areaStyle: {
                                    color: 'rgba(16, 185, 129, 0.2)'
                                }
                            }
                        ]
                    }
                ]
            };
            exportChart.setOption(exportOption);

            // 响应式处理
            window.addEventListener('resize', () => {
                exportChart.resize();
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            
            // 进度条动画
            setTimeout(() => {
                document.querySelector('.bg-gradient-to-r').style.width = '100%';
            }, 500);
            imgList = Array.from(document.querySelectorAll('.img-zoom')).map(img => img.src);
        });

        // 导航函数
        function prevSlide() {
            window.location.href = 'slide4.html';
        }

        function nextSlide() {
            window.location.href = 'slide6.html';
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (document.getElementById('img-modal').classList.contains('flex')) {
                // 图片放大模式下，阻止页面切换
                e.preventDefault();
                e.stopPropagation();
                if (e.key === 'ArrowRight') showNextImg();
                else if (e.key === 'ArrowLeft') showPrevImg();
                else if (e.key === 'Escape') closeBig();
                return false;
            } else {
                // 正常模式下，允许页面切换
                if (e.key === 'ArrowRight' || e.key === ' ') nextSlide();
                else if (e.key === 'ArrowLeft') prevSlide();
            }
        });

        // 多图放大切换
        let imgList = [];
        let imgIndex = 0;
        // 缩放和拖拽功能
        let scale = 1, originX = 0, originY = 0, startX = 0, startY = 0, dragging = false;
        const modalImg = document.getElementById('modal-img');
        function updateTransform() {
            modalImg.style.transform = `scale(${scale}) translate(${originX}px,${originY}px)`;
        }
        modalImg.addEventListener('wheel', function(e) {
            e.preventDefault();
            const delta = e.deltaY > 0 ? -0.1 : 0.1;
            scale = Math.min(Math.max(0.2, scale + delta), 5);
            updateTransform();
        });
        modalImg.addEventListener('mousedown', function(e) {
            if (scale === 1) return;
            dragging = true;
            startX = e.clientX - originX;
            startY = e.clientY - originY;
            modalImg.style.cursor = 'grabbing';
        });
        document.addEventListener('mousemove', function(e) {
            if (!dragging) return;
            originX = e.clientX - startX;
            originY = e.clientY - startY;
            updateTransform();
        });
        document.addEventListener('mouseup', function() {
            dragging = false;
            modalImg.style.cursor = 'grab';
        });
        // 触摸拖拽
        modalImg.addEventListener('touchstart', function(e) {
            if (scale === 1) return;
            dragging = true;
            const t = e.touches[0];
            startX = t.clientX - originX;
            startY = t.clientY - originY;
        });
        document.addEventListener('touchmove', function(e) {
            if (!dragging) return;
            const t = e.touches[0];
            originX = t.clientX - startX;
            originY = t.clientY - startY;
            updateTransform();
        });
        document.addEventListener('touchend', function() { dragging = false; });
        // 阻止原生拖拽，防止禁止符号和图片被拖出页面
        modalImg.addEventListener('dragstart', function(e) { e.preventDefault(); });
        // 切换图片/关闭时重置
        function showBig(src) {
            imgIndex = imgList.indexOf(src);
            modalImg.src = src;
            document.getElementById('img-modal').classList.remove('hidden');
            document.getElementById('img-modal').classList.add('flex');
            scale = 1; originX = 0; originY = 0; updateTransform();
        }
        function showPrevImg(e) {
            e && e.stopPropagation();
            imgIndex = (imgIndex - 1 + imgList.length) % imgList.length;
            modalImg.src = imgList[imgIndex];
            scale = 1; originX = 0; originY = 0; updateTransform();
        }
        function showNextImg(e) {
            e && e.stopPropagation();
            imgIndex = (imgIndex + 1) % imgList.length;
            modalImg.src = imgList[imgIndex];
            scale = 1; originX = 0; originY = 0; updateTransform();
        }
        function closeBig() {
            document.getElementById('img-modal').classList.add('hidden');
            document.getElementById('img-modal').classList.remove('flex');
            scale = 1; originX = 0; originY = 0; updateTransform();
        }
        // 删除重复的keydown监听器，已在上面统一处理
        // 鼠标移动视差效果
        document.addEventListener('mousemove', function(e) {
            const moveX = (e.clientX - window.innerWidth / 2) * 0.01;
            const moveY = (e.clientY - window.innerHeight / 2) * 0.01;
            document.querySelectorAll('.bubble-orbit').forEach(element => {
                element.style.transform = `rotate(${moveX * 2}deg) translateX(60px) rotate(${-moveX * 2}deg)`;
            });
        });
    </script>
</body>
</html> 
{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/task/detail.vue?bfd2", "webpack:///D:/Xwzc/pages/patrol_pkg/task/detail.vue?37d3", "webpack:///D:/Xwzc/pages/patrol_pkg/task/detail.vue?2e77", "webpack:///D:/Xwzc/pages/patrol_pkg/task/detail.vue?dca9", "uni-app:///pages/patrol_pkg/task/detail.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/task/detail.vue?8046", "webpack:///D:/Xwzc/pages/patrol_pkg/task/detail.vue?83e7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "setCache", "data", "timestamp", "uni", "console", "getCache", "clearCache", "components", "PEmptyState", "taskId", "taskDetail", "userInfo", "loading", "refreshing", "shiftInfo", "showRoundDetails", "round", "retryCount", "maxRetries", "roundStatusClasses", "computed", "shiftTimeText", "taskTitle", "executorName", "shiftDisplayName", "executionDate", "roundsCount", "missedPointsCount", "missedCount", "onLoad", "onUnload", "methods", "loadTaskDetail", "title", "icon", "currentUserId", "PatrolApi", "name", "action", "task_id", "userId", "level", "fields", "res", "promises", "Promise", "setTimeout", "loadShiftInfo", "shiftId", "cache<PERSON>ey", "cachedShift", "startTime", "endTime", "params", "shift_id", "shiftData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "processRoundsData", "processedRound", "total", "checked", "hasUpcomingRound", "allRoundsCompleted", "hasActiveRound", "activeRounds", "completedRounds", "parseTimeString", "hours", "minutes", "getCurrentUserId", "loadUserInfo", "cachedUser", "userid", "pageSize", "user", "refresh", "getStatusText", "getRoundStatusText", "getStatusClassForRound", "calculateCompletionRate", "formatDate", "formatDateTime", "formatTime", "date", "formatTimeRange", "navigateToEdit", "url", "toggleTaskStatus", "newStatus", "statusText", "status", "toggleRoundDetails", "loadRoundPoints", "round_number", "formatTimeStr", "getRoundCompletion", "calculateRemainingTime", "navigateBack", "getShiftTime", "task", "getProgressClass", "getPointStatusClass", "ensureOverallStats", "totalPoints", "completedPoints", "missedPoints", "completionRate"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChGA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACqNpnB;AAEA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AACA;;AAEA;EACAC;IACA;MACA;QACAC;QACAC;MACA;MACAC;IACA;MACAC;IACA;EACA;EAEAC;IACA;MACA;MACA;MAEA;MACA;;MAEA;MACA;QACAF;QACA;MACA;MAEA;IACA;MACAC;MACA;IACA;EACA;EAEAE;IACA;MACAH;IACA;MACAC;IACA;EACA;AACA;AAAA,eAEA;EACAG;IACAC;EACA;EACAP;IACA;MACAQ;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;QACA;QACA;QACA;QACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA,uBACA,4EACA;IACA;IAEA;IACAC;MACA;MACA,qCACA,uDACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;QACA;UACAC;YAAA;UAAA;QACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IAEA;IACA;MACA;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA7B;kBACA8B;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAGAC,0CAEA;gBACA;gBAAA;gBAAA,OACAC;kBACAC;kBACAC;kBACArC;oBACAsC;oBACAC;oBACAC;oBAAA;oBACA;oBACAC,SACA,2EACA,mEACA;oBACA;oBACA,qEACA,gFACA,yEACA;kBAEA;gBACA;cAAA;gBAnBAC;gBAAA,MAqBAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAEA;kBACA;gBACA;gBAEA;kBACA;gBACA;gBAEA;kBACA;gBACA;;gBAEA;gBACA;gBACA;;gBAEA;gBACA;kBACAvC;kBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;;gBAEA;gBACAwC;gBAEA;kBACAA;gBACA;gBAEA;kBACAA;gBACA;;gBAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAzC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACA;gBACA0C;kBACA;gBACA;gBAAA;cAAA;gBAIA3C;kBACA8B;kBACAC;gBACA;gBACAY;kBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA1C;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA0C;kBACA;gBACA;gBAAA;cAAA;gBAIA3C;kBACA8B;kBACAC;gBACA;gBACAY;kBAAA;gBAAA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAC;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;;gBAEA;gBACA;kBACA;oBACA;kBACA;kBAEAC;kBACAC;kBAEA,iEACAD;gBACA;gBAEA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAMAf;kBACAC;kBACAC;kBACArC;oBACAoD;sBAAAC;oBAAA;kBACA;gBACA;cAAA;gBANAX;gBAQA;kBACA;kBACAY,8DAEA;kBACAC;;kBAEA;kBACA;;kBAEA;kBACA;oBACA;sBACA;oBACA;;oBAEA;oBACAL;oBACAC,0DAEA;oBACA,mEACAD;kBACA;;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA/C;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAqD;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;QACArD;QACA;QACA;MACA;;MAEA;MACA;MACA;MACA;MAEA;QACA;QACA;;QAEA;QACAsD;QACAA;QACAA;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;UACAA;YACAC;YACAC;UACA;QACA;UACA;UACAF;YACAC;YACAC;UACA;QACA;;QAEA;QACA;UACA;UACA;YACA;YACA;YACA;;YAEA;YACAF;YACAA;;YAEA;YACAA;YACAA;;YAEA;YACA;cACAA;cACAG;cACAC;YACA;cACA;cACA,kCACAJ,wCACAA;gBACAA;cACA;gBACAA;gBACAI;cACA;YACA;cACA;cACA,kCACAJ,wCACAA;gBACAA;cACA;gBACAA;gBACAK;gBACAD;cACA;YACA;UACA;YACA;YACA;YACA;;YAEA;YACAJ;YACAA;;YAEA;YACA;cACAA;cACAG;cACAC;YACA;cACA,kCACAJ,wCACAA;gBACAA;cACA;gBACAA;cACA;YACA;cACA,kCACAA,wCACAA;gBACAA;cACA;gBACAA;gBACAK;gBACAD;cACA;YACA;UACA;YACA;YACA,kCACAJ,wCACAA;cACAA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;UACAtD;;UAEA;UACA,kCACAsD,wCACAA;YACAA;UACA;YACAA;UACA;YACAA;UACA;QACA;;QAEA;QACAA;;QAEA;QACAA;QAEA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;;MAEA;MACA,wDACA;QAAA;MAAA,EACA;MACA,2DACA;QAAA;MAAA,EACA;MAEAM;QAAA;MAAA;MACAC;QAAA;MAAA;MAEA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;UACA;YACAC;YACAC;UACA;QACA;;QAEA;QACA;QACA;UACAD;UACAC;QACA;MACA;QACA;QACA;UAAAD;UAAAC;QAAA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA9B;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAS;gBACAsB;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAKApC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAC;kBACArC;oBACAuE;oBACAC;kBACA;gBACA;cAAA;gBAPA9B;gBASA;kBACA+B;oBAAA;kBAAA;kBACA;oBACA;oBACA;oBACAlB;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEApD;gBACAD;kBACA8B;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;QACAC;QAEA;UACA/E;UACA;QACA;QAEA;QACA;QAEA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAgF;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;UACA;UACA;UACA;QACA;UACAhF;QACA;MACA;MAEA;IACA;IAEA;IACAiF;MACAlF;QACAmF;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACApF;kBACA8B;kBACAC;gBACA;gBAAA;cAAA;gBAIAsD;gBACAC;gBAAA;gBAGA;gBAAA;gBAAA,OAEArD;kBACAC;kBACAC;kBACArC;oBACAsC;oBACAmD;kBACA;gBACA;cAAA;gBAPA/C;gBASA;kBACAxC;oBACA8B;oBACAC;kBACA;kBACA;gBACA;kBACA/B;oBACA8B;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9B;gBACAD;kBACA8B;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyD;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA3E;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACA;oBACA;sBACA;oBACA;kBACA;gBACA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;gBACAD;kBACA8B;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAGA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAxD;kBACAC;kBACAC;kBACArC;oBACAsC;oBACAsD;oBACA;oBACAnD,SACA,mEACA,0DACA;kBAEA;gBACA;cAAA;gBAbAC;gBAeA;kBACA;kBACA;;kBAEA;kBACA;oBACA3B;sBACA2C;sBACAC;wBAAA;sBAAA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAxD;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACA0F;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MAEA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MAEA;MAEA;MACA;MAEA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA9F;IACA;IAEA;IACA+F;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;UACAC;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;UACAA;UACA;QACA;MACA;;MAEA;MACA;MACA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;QACA;UACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;;MAEA;MACA;MACA;MACA;MAEA;QACA;UACA;YACAC;UACA;UACA;YACAC;UACA;UACA;UACA;UACA;YACAC;UACA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;QACAC;MACA;MACA;;MAEA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpuCA;AAAA;AAAA;AAAA;AAA2oC,CAAgB,inCAAG,EAAC,C;;;;;;;;;;;ACA/pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/task/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/task/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=1189b4c4&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/task/detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=1189b4c4&\"", "var components\ntry {\n  components = {\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.taskDetail ? _vm.getStatusText(_vm.taskDetail.status || 0) : null\n  var m1 = _vm.taskDetail\n    ? _vm.formatDateTime(_vm.taskDetail.create_date)\n    : null\n  var g0 = _vm.taskDetail\n    ? _vm.taskDetail.rounds_detail && _vm.taskDetail.rounds_detail.length > 0\n    : null\n  var l1 =\n    _vm.taskDetail && g0\n      ? _vm.__map(_vm.taskDetail.rounds_detail, function (round, index) {\n          var $orig = _vm.__get_orig(round)\n          var m2 = _vm.formatTimeStr(round.time)\n          var m3 = round.status === 1 ? _vm.calculateRemainingTime(round) : null\n          var m4 = round.point_stats ? _vm.getRoundCompletion(round) : null\n          var m5 = _vm.getRoundStatusText(round.status)\n          var g1 =\n            round.expanded && !round.loading\n              ? round.points && round.points.length > 0\n              : null\n          var g2 =\n            round.expanded && !round.loading && g1 ? round.points.length : null\n          var l0 =\n            round.expanded && !round.loading && g1\n              ? _vm.__map(round.points, function (point, pIndex) {\n                  var $orig = _vm.__get_orig(point)\n                  var m6 =\n                    point.status === 1 || point.status === 2\n                      ? _vm.formatDateTime(point.checkin_time)\n                      : null\n                  return {\n                    $orig: $orig,\n                    m6: m6,\n                  }\n                })\n              : null\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n            g1: g1,\n            g2: g2,\n            l0: l0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"detail-container\">\n\t\t<scroll-view class=\"content\" scroll-y refresher-enabled :refresher-triggered=\"refreshing\" @refresherrefresh=\"refresh\">\n\t\t\t<!-- 加载中 -->\n\t\t\t<view class=\"loading-container\" v-if=\"loading\">\n\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 数据为空 -->\n\t\t\t<view class=\"empty-container\" v-if=\"!loading && !taskDetail\">\n\t\t\t\t<p-empty-state type=\"data\" text=\"任务不存在或已被删除\"></p-empty-state>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 任务详情内容 -->\n\t\t\t<view class=\"content-wrapper\" v-if=\"taskDetail\">\n\t\t\t\t<!-- 任务基本信息 -->\n\t\t\t\t<view class=\"section-block info-block\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<view class=\"section-title-bar\"></view>\n\t\t\t\t\t\t<text class=\"section-title\">{{ taskTitle }}</text>\n\t\t\t\t\t\t<view :class=\"['status-tag', 'status-' + (taskDetail.status || 0)]\">\n\t\t\t\t\t\t\t{{ getStatusText(taskDetail.status || 0) }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t\t<text class=\"info-label\">线路区域</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ taskDetail.area || taskDetail.route_name || '未指定区域' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t\t<text class=\"info-label\">执行人员</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ executorName }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t\t<text class=\"info-label\">执行班次</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">\n\t\t\t\t\t\t\t\t{{ shiftDisplayName }}\n\t\t\t\t\t\t\t\t<text class=\"shift-time\" v-if=\"shiftTimeText\">\n\t\t\t\t\t\t\t\t\t{{ shiftTimeText }}\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t\t<text class=\"info-label\">执行日期</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ executionDate }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t\t<text class=\"info-label\">创建时间</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ formatDateTime(taskDetail.create_date) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 轮次卡片 -->\n\t\t\t\t<view class=\"rounds-section\" v-if=\"taskDetail.rounds_detail && taskDetail.rounds_detail.length > 0\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<view class=\"section-title-bar\"></view>\n\t\t\t\t\t\t<text class=\"section-title\">轮次信息</text>\n\t\t\t\t\t\t<text class=\"round-count\">{{ roundsCount }}个轮次</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"round-list\">\n\t\t\t\t\t\t<view class=\"round-item\" v-for=\"(round, index) in taskDetail.rounds_detail\" :key=\"index\" @click=\"toggleRoundDetails(round)\">\n\t\t\t\t\t\t\t<view class=\"round-header\">\n\t\t\t\t\t\t\t\t<view class=\"round-title\">\n\t\t\t\t\t\t\t\t\t<text>{{ round.name || '轮次' + (round.round || (index + 1)) }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"round-time\">\n\t\t\t\t\t\t\t\t\t{{ formatTimeStr(round.time) }}\n\t\t\t\t\t\t\t\t\t<text v-if=\"round.day_offset > 0\" class=\"next-day-badge\">\n\t\t\t\t\t\t\t\t\t\t次日\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t<text class=\"effective-duration\">有效时长: {{ round.duration || 60 }}分钟</text>\n\t\t\t\t\t\t\t\t\t<text class=\"remaining-time\" v-if=\"round.status === 1\">\n\t\t\t\t\t\t\t\t\t\t({{ calculateRemainingTime(round) }})\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"round-info\">\n\t\t\t\t\t\t\t\t<view class=\"round-progress\" v-if=\"round.point_stats\">\n\t\t\t\t\t\t\t\t\t<view class=\"progress-label\">\n\t\t\t\t\t\t\t\t\t\t<text>点位打卡</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"progress-value\">{{ round.point_stats.checked || 0 }}/{{ round.point_stats.total || 0 }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"progress-bg\"></view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"'width:' + getRoundCompletion(round) + '%'\"></view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"round-badge\" :class=\"roundStatusClasses[round.status] || 'status-waiting'\">\n\t\t\t\t\t\t\t\t\t{{ getRoundStatusText(round.status) }}\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 轮次点位列表 -->\n\t\t\t\t\t\t\t<view class=\"point-list\" v-if=\"round.expanded\">\n\t\t\t\t\t\t\t\t<!-- 🔥 新增：点位加载状态 -->\n\t\t\t\t\t\t\t\t<view class=\"point-loading\" v-if=\"round.loading\">\n\t\t\t\t\t\t\t\t\t<view class=\"loading-spinner small\"></view>\n\t\t\t\t\t\t\t\t\t<text class=\"loading-text\">加载点位信息...</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 🔥 优化：只有在有点位数据时才显示 -->\n\t\t\t\t\t\t\t\t<view v-else-if=\"round.points && round.points.length > 0\">\n\t\t\t\t\t\t\t\t\t<view class=\"point-title\">\n\t\t\t\t\t\t\t\t\t\t<text>轮次点位 ({{ round.points.length }}个)</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"point-items\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"point-item\" v-for=\"(point, pIndex) in round.points\" :key=\"pIndex\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"point-header\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"point-name-section\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"point-index\">{{ pIndex + 1 }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"point-name\">{{ point.name }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"point-range\" v-if=\"point.range\">{{ point.range || 10 }}米</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"point-status-badge\" :class=\"{ \n\t\t\t\t\t\t\t\t\t\t\t\t\t'success': point.status === 1 || point.status === 2, \n\t\t\t\t\t\t\t\t\t\t\t\t\t'warning': point.status === 0 && round.status === 1,\n\t\t\t\t\t\t\t\t\t\t\t\t\t'danger': round.status === 3 && point.status === 0\n\t\t\t\t\t\t\t\t\t\t\t\t}\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{ point.status === 1 || point.status === 2 ? '已打卡' : '未打卡' }}\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"point-address\" v-if=\"point.location && point.location.address\">\n\t\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"location\" size=\"14\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t\t<text>{{ point.location.address }}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"point-checkin-info\" v-if=\"point.status === 1 || point.status === 2\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"checkin-time\">打卡时间: {{ formatDateTime(point.checkin_time) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"checkin-status\" v-if=\"point.abnormal\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"warn\" size=\"14\" color=\"#FF9500\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{ point.abnormal_reason || '异常打卡' }}\n\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 🔥 新增：无点位数据状态 -->\n\t\t\t\t\t\t\t\t<view class=\"point-empty\" v-else>\n\t\t\t\t\t\t\t\t\t<text>该轮次暂无点位信息</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 总体进度展示 -->\n\t\t\t\t<view class=\"section-block\" v-if=\"taskDetail.overall_stats\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<view class=\"section-title-bar\"></view>\n\t\t\t\t\t\t<text class=\"section-title\">总体进度</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t\t<view class=\"overall-stats\">\n\t\t\t\t\t\t\t<view class=\"stats-row\">\n\t\t\t\t\t\t\t\t<text class=\"stats-label\">总点位数</text>\n\t\t\t\t\t\t\t\t<text class=\"stats-value\">{{ taskDetail.overall_stats.total_points }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"stats-row\">\n\t\t\t\t\t\t\t\t<text class=\"stats-label\">已完成点位</text>\n\t\t\t\t\t\t\t\t<text class=\"stats-value\">{{ taskDetail.overall_stats.completed_points }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"stats-row\">\n\t\t\t\t\t\t\t\t<text class=\"stats-label\">缺卡点位</text>\n\t\t\t\t\t\t\t\t<text class=\"stats-value\">{{ missedPointsCount }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"stats-row\">\n\t\t\t\t\t\t\t\t<text class=\"stats-label\">总体完成率</text>\n\t\t\t\t\t\t\t\t<text class=\"stats-value\">{{ taskDetail.overall_stats.completion_rate }}%</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"progress-bar-wrapper overall-progress\">\n\t\t\t\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\t:class=\"['progress-inner', \n\t\t\t\t\t\t\t\t\t\t\ttaskDetail.overall_stats.completion_rate === 100 ? 'completed' : \n\t\t\t\t\t\t\t\t\t\t\t(taskDetail.overall_stats.completion_rate > 0 ? 'partial' : '')\n\t\t\t\t\t\t\t\t\t\t]\" \n\t\t\t\t\t\t\t\t\t\t:style=\"'width:' + taskDetail.overall_stats.completion_rate + '%'\"\n\t\t\t\t\t\t\t\t\t></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 操作按钮：集成到内容中 -->\n\t\t\t\t<!-- <view class=\"button-section\" v-if=\"taskDetail\">\n\t\t\t\t\t<view class=\"action-buttons\">\n\t\t\t\t\t\t<view class=\"cancel-btn\" @click=\"navigateBack\">取消</view>\n\t\t\t\t\t\t<view class=\"primary-btn\" @click=\"navigateToEdit\" v-if=\"taskDetail.status !== 2 && taskDetail.status !== 4\">编辑任务</view>\n\t\t\t\t\t\t<view class=\"status-btn\" @click=\"toggleTaskStatus\" v-if=\"taskDetail.status === 1 || taskDetail.status === 3\">\n\t\t\t\t\t\t\t{{ taskDetail.status === 1 ? '暂停任务' : '启用任务' }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view> -->\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\nimport { formatDate, formatTime, calculateRoundTime, calculateEndTime } from '@/utils/date.js';\n\n// 添加缓存工具方法\nconst CACHE_DURATION = 30 * 60 * 1000; // 30分钟缓存\n\nconst CacheUtils = {\n\tsetCache(key, data) {\n\t\ttry {\n\t\t\tconst cacheData = {\n\t\t\t\tdata: data,\n\t\t\t\ttimestamp: Date.now()\n\t\t\t};\n\t\t\tuni.setStorageSync(key, JSON.stringify(cacheData));\n\t\t} catch (e) {\n\t\t\tconsole.warn('设置缓存失败:', e);\n\t\t}\n\t},\n\t\n\tgetCache(key) {\n\t\ttry {\n\t\t\tconst cacheStr = uni.getStorageSync(key);\n\t\t\tif (!cacheStr) return null;\n\t\t\t\n\t\t\tconst cacheData = JSON.parse(cacheStr);\n\t\t\tconst now = Date.now();\n\t\t\t\n\t\t\t// 检查缓存是否过期\n\t\t\tif (now - cacheData.timestamp > CACHE_DURATION) {\n\t\t\t\tuni.removeStorageSync(key);\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\t\n\t\t\treturn cacheData.data;\n\t\t} catch (e) {\n\t\t\tconsole.warn('获取缓存失败:', e);\n\t\t\treturn null;\n\t\t}\n\t},\n\t\n\tclearCache(key) {\n\t\ttry {\n\t\t\tuni.removeStorageSync(key);\n\t\t} catch (e) {\n\t\t\tconsole.warn('清除缓存失败:', e);\n\t\t}\n\t}\n};\n\nexport default {\n\tcomponents: {\n\t\tPEmptyState\n\t},\n\tdata() {\n\t\treturn {\n\t\t\ttaskId: '',\n\t\t\ttaskDetail: null,\n\t\t\tuserInfo: null,\n\t\t\tloading: true,\n\t\t\trefreshing: false,\n\t\t\tshiftInfo: {}, // 班次信息缓存\n\t\t\tshowRoundDetails: false,\n\t\t\tround: {}, // 轮次信息缓存\n\t\t\tretryCount: 0, // 重试次数\n\t\t\tmaxRetries: 3, // 最大重试次数\n\t\t\t// 轮次状态样式映射\n\t\t\troundStatusClasses: {\n\t\t\t\t0: 'status-waiting',\n\t\t\t\t1: 'status-active',\n\t\t\t\t2: 'status-completed',\n\t\t\t\t3: 'status-expired'\n\t\t\t}\n\t\t};\n\t},\n\t\n\tcomputed: {\n\t\t// 班次时间文本计算属性\n\t\tshiftTimeText() {\n\t\t\tif (!this.taskDetail) return '';\n\t\t\treturn this.getShiftTime(this.taskDetail);\n\t\t},\n\t\t\n\t\t// 任务标题计算属性\n\t\ttaskTitle() {\n\t\t\tif (!this.taskDetail) return '未命名任务';\n\t\t\treturn this.taskDetail.name || this.taskDetail.area || this.taskDetail.route_name || '未命名任务';\n\t\t},\n\t\t\n\t\t// 执行人员名称计算属性\n\t\texecutorName() {\n\t\t\tif (!this.taskDetail) return '加载中...';\n\t\t\treturn this.userInfo ? \n\t\t\t\t(this.userInfo.nickname || this.userInfo.name || this.taskDetail.user_name) : \n\t\t\t\t(this.taskDetail.user_name || '加载中...');\n\t\t},\n\t\t\n\t\t// 班次名称计算属性\n\t\tshiftDisplayName() {\n\t\t\tif (!this.taskDetail) return '未指定班次';\n\t\t\treturn this.taskDetail.shift_name || \n\t\t\t\t(this.taskDetail.shift && this.taskDetail.shift.name) || \n\t\t\t\t'未指定班次';\n\t\t},\n\t\t\n\t\t// 执行日期计算属性\n\t\texecutionDate() {\n\t\t\tif (!this.taskDetail) return '';\n\t\t\treturn this.taskDetail.patrol_date || this.formatDate(this.taskDetail.create_date);\n\t\t},\n\t\t\n\t\t// 轮次数量计算属性\n\t\troundsCount() {\n\t\t\tif (!this.taskDetail || !this.taskDetail.rounds_detail) return 0;\n\t\t\treturn this.taskDetail.rounds_detail.length;\n\t\t},\n\t\t\n\t\t// 缺卡点位数计算属性\n\t\tmissedPointsCount() {\n\t\t\tif (!this.taskDetail || !this.taskDetail.rounds_detail) return 0;\n\t\t\t\n\t\t\tlet missedCount = 0;\n\t\t\tthis.taskDetail.rounds_detail.forEach(round => {\n\t\t\t\tif (round.points) {\n\t\t\t\t\tmissedCount += round.points.filter(p => p.status === 4).length;\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn missedCount;\n\t\t}\n\t},\n\t\n\tonLoad(options) {\n\t\tconst startTime = Date.now();\n\t\t\n\t\tthis.taskId = options.id;\n\t\tthis.loadTaskDetail().then(() => {\n\t\t\tconst loadTime = Date.now() - startTime;\n\t\t});\n\t},\n\t\n\tonUnload() {\n\t\t// 清理大对象，释放内存\n\t\tthis.taskDetail = null;\n\t\tthis.userInfo = null;\n\t\tthis.shiftInfo = {};\n\t},\n\t\n\tmethods: {\n\t\t// 加载任务详情\n\t\tasync loadTaskDetail() {\n\t\t\tif (!this.taskId) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '任务ID无效',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.loading = true;\n\t\t\tthis.taskDetail = null;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst currentUserId = this.getCurrentUserId();\n\t\t\t\t\n\t\t\t\t// 🔥 优化：分层加载策略\n\t\t\t\t// 第一阶段：加载轻量级基本信息（快速显示）\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-task',\n\t\t\t\t\taction: 'getTaskDetail',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\ttask_id: this.taskId,\n\t\t\t\t\t\tuserId: currentUserId,\n\t\t\t\t\t\tlevel: 'checkin', // 🔥 轻量级：不包含points数组，适用于详情查看\n\t\t\t\t\t\t// 🔥 优化：只获取详情页必需的基础字段\n\t\t\t\t\t\tfields: [\n\t\t\t\t\t\t\t'_id', 'name', 'status', 'user_id', 'user_name', 'shift_id', 'shift_name',\n\t\t\t\t\t\t\t'area', 'route_name', 'patrol_date', 'create_date', 'update_date',\n\t\t\t\t\t\t\t'overall_stats', 'shift', 'route',\n\t\t\t\t\t\t\t// 🔥 只获取轮次摘要，不包含points数组\n\t\t\t\t\t\t\t'rounds_detail.round', 'rounds_detail.name', 'rounds_detail.status',\n\t\t\t\t\t\t\t'rounds_detail.start_time', 'rounds_detail.end_time', 'rounds_detail.duration',\n\t\t\t\t\t\t\t'rounds_detail.day_offset', 'rounds_detail.time', 'rounds_detail.stats',\n\t\t\t\t\t\t\t'rounds_detail.point_stats'\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t// 重置重试次数\n\t\t\t\t\tthis.retryCount = 0;\n\t\t\t\t\t\n\t\t\t\t\t// 打印原始任务数据，用于调试\n\t\t\t\t\t// console.log('原始任务数据:', JSON.stringify(res.data));\n\t\t\t\t\t\n\t\t\t\t\t// 深拷贝数据，避免引用问题\n\t\t\t\t\tthis.taskDetail = JSON.parse(JSON.stringify(res.data));\n\t\t\t\t\t\n\t\t\t\t\t// 处理可能的数据结构不一致问题\n\t\t\t\t\tif (!this.taskDetail.shift_id && this.taskDetail.shift && this.taskDetail.shift._id) {\n\t\t\t\t\t\tthis.taskDetail.shift_id = this.taskDetail.shift._id;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (!this.taskDetail.shift_name && this.taskDetail.shift && this.taskDetail.shift.name) {\n\t\t\t\t\t\tthis.taskDetail.shift_name = this.taskDetail.shift.name;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (!this.taskDetail.route_name && this.taskDetail.route && this.taskDetail.route.name) {\n\t\t\t\t\t\tthis.taskDetail.route_name = this.taskDetail.route.name;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (!this.taskDetail.area && this.taskDetail.route && this.taskDetail.route.area) {\n\t\t\t\t\t\tthis.taskDetail.area = this.taskDetail.route.area;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 确保基本属性都有值\n\t\t\t\t\tthis.taskDetail.status = this.taskDetail.status !== undefined ? Number(this.taskDetail.status) : 0;\n\t\t\t\t\tthis.taskDetail.name = this.taskDetail.name || '';\n\t\t\t\t\t\n\t\t\t\t\t// 确保轮次数据存在\n\t\t\t\t\tif (!this.taskDetail.rounds_detail || !Array.isArray(this.taskDetail.rounds_detail)) {\n\t\t\t\t\t\tconsole.warn('任务数据中没有轮次信息或格式不正确');\n\t\t\t\t\t\tthis.taskDetail.rounds_detail = [];\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 处理轮次数据\n\t\t\t\t\tthis.processRoundsData();\n\t\t\t\t\t\n\t\t\t\t\t// 打印处理后的任务数据，用于调试 - 注意：保留完整数据结构\n\t\t\t\t\t// console.log('处理后的任务数据:', {\n\t\t\t\t\t// \tname: this.taskDetail.name,\n\t\t\t\t\t// \tstatus: this.taskDetail.status,\n\t\t\t\t\t// \tshift_name: this.taskDetail.shift_name,\n\t\t\t\t\t// \t// 只显示轮次数量，保留原数组\n\t\t\t\t\t// \trounds_count: this.taskDetail.rounds_detail ? this.taskDetail.rounds_detail.length : 0\n\t\t\t\t\t// });\n\t\t\t\t\t\n\t\t\t\t\t// 并行加载用户信息和班次信息\n\t\t\t\t\tconst promises = [];\n\t\t\t\t\t\n\t\t\t\t\tif (this.taskDetail.user_id) {\n\t\t\t\t\t\tpromises.push(this.loadUserInfo(this.taskDetail.user_id));\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (this.taskDetail.shift_id && !this.shiftInfo[this.taskDetail.shift_id]) {\n\t\t\t\t\t\tpromises.push(this.loadShiftInfo(this.taskDetail.shift_id));\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 等待所有并行请求完成，但不阻塞主流程\n\t\t\t\t\tif (promises.length > 0) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tawait Promise.all(promises);\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.warn('加载附加信息时出错:', e);\n\t\t\t\t\t\t\t// 不影响主流程，继续显示页面\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 检查是否需要重试\n\t\t\t\t\tif (this.retryCount < this.maxRetries && (res.code === -1 || !res.code)) {\n\t\t\t\t\t\tthis.retryCount++;\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.loadTaskDetail();\n\t\t\t\t\t\t}, 1000 * this.retryCount); // 递增延迟重试\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '任务不存在',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(() => uni.navigateBack(), 1500);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载任务详情出错:', e);\n\t\t\t\t\n\t\t\t\t// 检查是否需要重试\n\t\t\t\tif (this.retryCount < this.maxRetries) {\n\t\t\t\t\tthis.retryCount++;\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.loadTaskDetail();\n\t\t\t\t\t}, 1000 * this.retryCount); // 递增延迟重试\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载任务详情出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => uni.navigateBack(), 1500);\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t\tthis.refreshing = false;\n\t\t\t}\n\t\t},\n\n\t\t// 加载班次信息\n\t\tasync loadShiftInfo(shiftId) {\n\t\t\tif (!shiftId) return;\n\t\t\t\n\t\t\t// 先检查缓存\n\t\t\tconst cacheKey = `shift_info_${shiftId}`;\n\t\t\tconst cachedShift = CacheUtils.getCache(cacheKey);\n\t\t\tif (cachedShift) {\n\t\t\t\tthis.$set(this.shiftInfo, shiftId, cachedShift);\n\t\t\t\t\n\t\t\t\t// 更新任务班次信息\n\t\t\t\tif (this.taskDetail && this.taskDetail.shift_id === shiftId) {\n\t\t\t\t\tif (!this.taskDetail.shift) {\n\t\t\t\t\t\tthis.taskDetail.shift = {};\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconst startTime = cachedShift.start_time || cachedShift.startTime || '';\n\t\t\t\t\tconst endTime = cachedShift.end_time || cachedShift.endTime || '';\n\t\t\t\t\t\n\t\t\t\t\tthis.taskDetail.shift_time = startTime && endTime ? \n\t\t\t\t\t\t`(${startTime} - ${endTime})` : '';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.$forceUpdate();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 加载班次详情\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-shift',\n\t\t\t\t\taction: 'getShiftDetail',\n\t\t\t\t\tdata: { \n\t\t\t\t\t\tparams: { shift_id: shiftId }\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t// 处理可能是数组的情况\n\t\t\t\t\tconst shiftData = Array.isArray(res.data) ? res.data[0] : res.data;\n\t\t\t\t\t\n\t\t\t\t\t// 缓存班次信息\n\t\t\t\t\tCacheUtils.setCache(cacheKey, shiftData);\n\t\t\t\t\t\n\t\t\t\t\t// 使用Vue的响应式API更新数据\n\t\t\t\t\tthis.$set(this.shiftInfo, shiftId, shiftData);\n\t\t\t\t\t\n\t\t\t\t\t// 如果当前任务使用此班次，直接更新任务班次信息\n\t\t\t\t\tif (this.taskDetail && this.taskDetail.shift_id === shiftId) {\n\t\t\t\t\t\tif (!this.taskDetail.shift) {\n\t\t\t\t\t\t\tthis.taskDetail.shift = {};\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 兼容不同字段名\n\t\t\t\t\t\tconst startTime = shiftData.start_time || shiftData.startTime || '';\n\t\t\t\t\t\tconst endTime = shiftData.end_time || shiftData.endTime || '';\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 直接设置shift_time字段简化后续处理\n\t\t\t\t\t\tthis.taskDetail.shift_time = startTime && endTime ? \n\t\t\t\t\t\t\t`(${startTime} - ${endTime})` : '';\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 强制更新视图\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载班次信息出错:', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 处理轮次数据\n\t\tprocessRoundsData() {\n\t\t\tif (!this.taskDetail || !this.taskDetail.rounds_detail) return;\n\t\t\t\n\t\t\t// 输出原始轮次数据\n\t\t\t// console.log('处理轮次数据前:', JSON.stringify(this.taskDetail.rounds_detail));\n\t\t\t\n\t\t\t// 获取任务基准日期\n\t\t\tconst taskDate = this.taskDetail.patrol_date ? new Date(this.taskDetail.patrol_date.replace(/-/g, '/')) : new Date(this.taskDetail.create_date);\n\t\t\tconst taskDateStr = formatDate(taskDate, 'YYYY-MM-DD');\n\t\t\tconst now = new Date(); // 当前时间，用于判断轮次状态\n\t\t\t\n\t\t\t// 确保rounds_detail是数组\n\t\t\tif (!Array.isArray(this.taskDetail.rounds_detail)) {\n\t\t\t\tconsole.warn('轮次数据不是数组，重置为空数组');\n\t\t\t\tthis.taskDetail.rounds_detail = [];\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 处理每个轮次的时间信息和状态\n\t\t\tlet hasActiveRound = false;\n\t\t\tlet hasUpcomingRound = false;\n\t\t\tlet allRoundsCompleted = true;\n\t\t\t\n\t\t\tthis.taskDetail.rounds_detail = this.taskDetail.rounds_detail.map(round => {\n\t\t\t\t// 创建轮次对象的副本，避免修改原始对象\n\t\t\t\tconst processedRound = {...round};\n\t\t\t\t\n\t\t\t\t// 确保轮次中有day_offset和duration字段\n\t\t\t\tprocessedRound.day_offset = processedRound.day_offset !== undefined ? Number(processedRound.day_offset) : 0;\n\t\t\t\tprocessedRound.duration = processedRound.duration !== undefined ? Number(processedRound.duration) : 60; // 默认60分钟\n\t\t\t\tprocessedRound.round = processedRound.round !== undefined ? Number(processedRound.round) : 1; // 默认轮次号为1\n\t\t\t\t\n\t\t\t\t// 确保points字段存在并且是数组\n\t\t\t\tif (!processedRound.points || !Array.isArray(processedRound.points)) {\n\t\t\t\t\tprocessedRound.points = [];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 确保point_stats字段存在\n\t\t\t\tif (!processedRound.point_stats) {\n\t\t\t\t\tprocessedRound.point_stats = {\n\t\t\t\t\t\ttotal: processedRound.points.length,\n\t\t\t\t\t\tchecked: 0\n\t\t\t\t\t};\n\t\t\t\t} else if (processedRound.stats && !processedRound.point_stats) {\n\t\t\t\t\t// 兼容stats字段\n\t\t\t\t\tprocessedRound.point_stats = {\n\t\t\t\t\t\ttotal: processedRound.stats.total_points || processedRound.points.length,\n\t\t\t\t\t\tchecked: processedRound.stats.completed_points || 0\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 处理时间字段\n\t\t\t\ttry {\n\t\t\t\t\t// 使用calculateRoundTime计算轮次开始时间，考虑day_offset\n\t\t\t\t\tif (processedRound.time) {\n\t\t\t\t\t\tconst roundStartTime = calculateRoundTime(taskDateStr, processedRound.time, processedRound.day_offset);\n\t\t\t\t\t\t// 使用calculateEndTime计算结束时间，基于开始时间和持续时间\n\t\t\t\t\t\tconst roundEndTime = calculateEndTime(roundStartTime, processedRound.duration);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 保存实际开始时间和结束时间\n\t\t\t\t\t\tprocessedRound.actualStartTime = roundStartTime;\n\t\t\t\t\t\tprocessedRound.actualEndTime = roundEndTime;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 为方便UI显示，保存ISO格式的开始和结束时间\n\t\t\t\t\t\tprocessedRound.start_time = roundStartTime.toISOString();\n\t\t\t\t\t\tprocessedRound.end_time = roundEndTime.toISOString();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 计算轮次状态\n\t\t\t\t\t\tif (now < roundStartTime) {\n\t\t\t\t\t\t\tprocessedRound.status = 0; // 未开始\n\t\t\t\t\t\t\thasUpcomingRound = true;\n\t\t\t\t\t\t\tallRoundsCompleted = false;\n\t\t\t\t\t\t} else if (now > roundEndTime) {\n\t\t\t\t\t\t\t// 已超时，但要检查点位是否全部完成\n\t\t\t\t\t\t\tif (processedRound.point_stats && \n\t\t\t\t\t\t\t\tprocessedRound.point_stats.total > 0 && \n\t\t\t\t\t\t\t\tprocessedRound.point_stats.checked >= processedRound.point_stats.total) {\n\t\t\t\t\t\t\t\tprocessedRound.status = 2; // 虽然超时但已完成\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tprocessedRound.status = 3; // 超时且未完成\n\t\t\t\t\t\t\t\tallRoundsCompleted = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 在时间范围内\n\t\t\t\t\t\t\tif (processedRound.point_stats && \n\t\t\t\t\t\t\t\tprocessedRound.point_stats.total > 0 && \n\t\t\t\t\t\t\t\tprocessedRound.point_stats.checked >= processedRound.point_stats.total) {\n\t\t\t\t\t\t\t\tprocessedRound.status = 2; // 已完成\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tprocessedRound.status = 1; // 进行中\n\t\t\t\t\t\t\t\thasActiveRound = true;\n\t\t\t\t\t\t\t\tallRoundsCompleted = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (processedRound.start_time) {\n\t\t\t\t\t\t// 直接从ISO时间格式解析日期时间\n\t\t\t\t\t\tconst roundStartTime = new Date(processedRound.start_time);\n\t\t\t\t\t\tconst roundEndTime = processedRound.end_time ? new Date(processedRound.end_time) : calculateEndTime(roundStartTime, processedRound.duration);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 保存实际开始时间和结束时间\n\t\t\t\t\t\tprocessedRound.actualStartTime = roundStartTime;\n\t\t\t\t\t\tprocessedRound.actualEndTime = roundEndTime;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 其余状态计算与上方相同\n\t\t\t\t\t\tif (now < roundStartTime) {\n\t\t\t\t\t\t\tprocessedRound.status = 0; // 未开始\n\t\t\t\t\t\t\thasUpcomingRound = true;\n\t\t\t\t\t\t\tallRoundsCompleted = false;\n\t\t\t\t\t\t} else if (now > roundEndTime) {\n\t\t\t\t\t\t\tif (processedRound.point_stats && \n\t\t\t\t\t\t\t\tprocessedRound.point_stats.total > 0 && \n\t\t\t\t\t\t\t\tprocessedRound.point_stats.checked >= processedRound.point_stats.total) {\n\t\t\t\t\t\t\t\tprocessedRound.status = 2; // 虽然超时但已完成\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tprocessedRound.status = 3; // 超时且未完成\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (processedRound.point_stats && \n\t\t\t\t\t\t\t\tprocessedRound.point_stats.total > 0 && \n\t\t\t\t\t\t\t\tprocessedRound.point_stats.checked >= processedRound.point_stats.total) {\n\t\t\t\t\t\t\t\tprocessedRound.status = 2; // 已完成\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tprocessedRound.status = 1; // 进行中\n\t\t\t\t\t\t\t\thasActiveRound = true;\n\t\t\t\t\t\t\t\tallRoundsCompleted = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 无时间信息时，根据点位完成情况判断\n\t\t\t\t\t\tif (processedRound.point_stats && \n\t\t\t\t\t\t\tprocessedRound.point_stats.total > 0 && \n\t\t\t\t\t\t\tprocessedRound.point_stats.checked >= processedRound.point_stats.total) {\n\t\t\t\t\t\t\tprocessedRound.status = 2; // 已完成\n\t\t\t\t\t\t} else if (processedRound.point_stats && processedRound.point_stats.checked > 0) {\n\t\t\t\t\t\t\tprocessedRound.status = 1; // 进行中\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tprocessedRound.status = 0; // 默认未开始\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('处理轮次时间出错:', e);\n\t\t\t\t\t\n\t\t\t\t\t// 出错时使用备用方案：根据点位完成情况判断\n\t\t\t\t\tif (processedRound.point_stats && \n\t\t\t\t\t\tprocessedRound.point_stats.total > 0 && \n\t\t\t\t\t\tprocessedRound.point_stats.checked >= processedRound.point_stats.total) {\n\t\t\t\t\t\tprocessedRound.status = 2; // 已完成\n\t\t\t\t\t} else if (processedRound.point_stats && processedRound.point_stats.checked > 0) {\n\t\t\t\t\t\tprocessedRound.status = 1; // 进行中\n\t\t\t\t\t} else {\n\t\t\t\t\t\tprocessedRound.status = 0; // 默认未开始\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 确保状态是数字\n\t\t\t\tprocessedRound.status = parseInt(processedRound.status || 0);\n\t\t\t\t\n\t\t\t\t// 添加expanded属性用于控制展开/折叠\n\t\t\t\tprocessedRound.expanded = false;\n\t\t\t\t\n\t\t\t\treturn processedRound;\n\t\t\t});\n\t\t\t\n\t\t\t// 更新任务整体状态\n\t\t\tif (allRoundsCompleted) {\n\t\t\t\tthis.taskDetail.status = 2; // 已完成\n\t\t\t} else if (hasActiveRound) {\n\t\t\t\tthis.taskDetail.status = 1; // 进行中\n\t\t\t} else if (hasUpcomingRound) {\n\t\t\t\tthis.taskDetail.status = 0; // 未开始\n\t\t\t} else {\n\t\t\t\tthis.taskDetail.status = 3; // 已超时\n\t\t\t}\n\t\t\t\n\t\t\t// 轮次排序\n\t\t\tconst activeRounds = this.taskDetail.rounds_detail.filter(\n\t\t\t\tround => round.status === 0 || round.status === 1\n\t\t\t);\n\t\t\tconst completedRounds = this.taskDetail.rounds_detail.filter(\n\t\t\t\tround => round.status === 2 || round.status === 3\n\t\t\t);\n\t\t\t\n\t\t\tactiveRounds.sort((a, b) => a.round - b.round);\n\t\t\tcompletedRounds.sort((a, b) => b.round - a.round);\n\t\t\t\n\t\t\tthis.taskDetail.rounds_detail = [...activeRounds, ...completedRounds];\n\t\t\t\n\t\t\t// 更新总体统计\n\t\t\tthis.ensureOverallStats();\n\t\t},\n\t\t\n\t\t// 解析时间字符串\n\t\tparseTimeString(timeStr) {\n\t\t\ttry {\n\t\t\t\t// 检查是否是完整的ISO格式日期时间\n\t\t\t\tif (timeStr.includes('T') || timeStr.includes('-')) {\n\t\t\t\t\tconst date = new Date(timeStr);\n\t\t\t\t\treturn {\n\t\t\t\t\t\thours: date.getHours(),\n\t\t\t\t\t\tminutes: date.getMinutes()\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 否则假设是HH:MM格式\n\t\t\t\tconst parts = timeStr.split(':');\n\t\t\t\treturn {\n\t\t\t\t\thours: parseInt(parts[0], 10),\n\t\t\t\t\tminutes: parseInt(parts[1], 10)\n\t\t\t\t};\n\t\t\t} catch (e) {\n\t\t\t\t// 发生错误时返回默认值\n\t\t\t\treturn { hours: 0, minutes: 0 };\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取当前用户ID\n\t\tgetCurrentUserId() {\n\t\t\ttry {\n\t\t\t\tconst userInfo = uni.getStorageSync('uni-id-pages-userInfo');\n\t\t\t\treturn userInfo ? userInfo._id || '' : '';\n\t\t\t} catch (e) {\n\t\t\t\treturn '';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载用户信息\n\t\tasync loadUserInfo(userId) {\n\t\t\tif (!userId) return;\n\t\t\t\n\t\t\t// 先检查缓存\n\t\t\tconst cacheKey = `user_info_${userId}`;\n\t\t\tconst cachedUser = CacheUtils.getCache(cacheKey);\n\t\t\tif (cachedUser) {\n\t\t\t\tthis.userInfo = cachedUser;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst currentUserId = this.getCurrentUserId();\n\t\t\t\tif (!currentUserId) return;\n\t\t\t\t\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-user',\n\t\t\t\t\taction: 'getUsers',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tuserid: currentUserId,\n\t\t\t\t\t\tpageSize: 100\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data && res.data.list) {\n\t\t\t\t\tconst user = res.data.list.find(u => u._id === userId);\n\t\t\t\t\tif (user) {\n\t\t\t\t\t\tthis.userInfo = user;\n\t\t\t\t\t\t// 缓存用户信息\n\t\t\t\t\t\tCacheUtils.setCache(cacheKey, user);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载用户信息失败:', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载用户信息失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 刷新\n\t\trefresh() {\n\t\t\tthis.refreshing = true;\n\t\t\tthis.retryCount = 0; // 重置重试次数\n\t\t\tthis.loadTaskDetail();\n\t\t},\n\t\t\n\t\t// 获取状态文本 - 优化为静态映射\n\t\tgetStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t0: '未开始',\n\t\t\t\t1: '进行中', \n\t\t\t\t2: '已完成',\n\t\t\t\t3: '已超时',\n\t\t\t\t4: '已取消'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未知状态';\n\t\t},\n\t\t\n\t\t// 获取轮次状态文本 - 优化为静态映射\n\t\tgetRoundStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t0: '未开始',\n\t\t\t\t1: '进行中',\n\t\t\t\t2: '已完成', \n\t\t\t\t3: '已超时'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未开始';\n\t\t},\n\t\t\n\t\t// 获取轮次状态样式类 - 不使用函数而是使用对象方式\n\t\tgetStatusClassForRound(status) {\n\t\t\treturn this.roundStatusClasses[status] || 'status-waiting';\n\t\t},\n\t\t\n\t\t// 计算完成率\n\t\tcalculateCompletionRate(round) {\n\t\t\tif (!round.point_stats || !round.point_stats.total || round.point_stats.total === 0) {\n\t\t\t\treturn 0;\n\t\t\t}\n\t\t\treturn Math.round((round.point_stats.checked / round.point_stats.total) * 100);\n\t\t},\n\t\t\n\t\t// 格式化日期\n\t\tformatDate(dateStr) {\n\t\t\tif (!dateStr) return '';\n\t\t\ttry {\n\t\t\t\tconst date = new Date(dateStr);\n\t\t\t\treturn formatDate(date, 'YYYY-MM-DD');\n\t\t\t} catch (e) {\n\t\t\t\treturn dateStr.split(' ')[0] || '';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 格式化日期时间\n\t\tformatDateTime(dateStr) {\n\t\t\tif (!dateStr) return '';\n\t\t\ttry {\n\t\t\t\tconst date = new Date(dateStr);\n\t\t\t\treturn formatDate(date, 'YYYY-MM-DD HH:mm');\n\t\t\t} catch (e) {\n\t\t\t\treturn dateStr;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 格式化时间\n\t\tformatTime(dateStr) {\n\t\t\tif (!dateStr) return '';\n\t\t\tlet date;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tdate = typeof dateStr === 'string' ? new Date(dateStr.replace(/-/g, '/')) : dateStr;\n\t\t\t\t\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\tconsole.warn('无效的日期格式:', dateStr);\n\t\t\t\t\treturn '--:--';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst hours = date.getHours().toString().padStart(2, '0');\n\t\t\t\tconst minutes = date.getMinutes().toString().padStart(2, '0');\n\t\t\t\t\n\t\t\t\treturn `${hours}:${minutes}`;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('格式化时间出错:', e);\n\t\t\t\treturn '--:--';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 格式化时间范围\n\t\tformatTimeRange(round) {\n\t\t\tif (!round) return '时间未定义';\n\t\t\t\n\t\t\t// 使用解析后的实际时间\n\t\t\tif (round.actualStartTime && round.actualEndTime) {\n\t\t\t\tconst startDate = round.actualStartTime;\n\t\t\t\treturn formatTime(startDate);\n\t\t\t}\n\t\t\t\n\t\t\t// 回退到原始时间\n\t\t\tif (round.start_time) {\n\t\t\t\ttry {\n\t\t\t\t\t// 尝试解析ISO时间字符串\n\t\t\t\t\tconst startDate = new Date(round.start_time);\n\t\t\t\t\treturn formatTime(startDate);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('格式化时间出错:', e);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\treturn round.time || '时间未定义';\n\t\t},\n\t\t\n\t\t// 导航到编辑页面\n\t\tnavigateToEdit() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/patrol_pkg/task/edit?id=${this.taskId}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 切换任务状态\n\t\tasync toggleTaskStatus() {\n\t\t\tif (this.taskDetail.status !== 1 && this.taskDetail.status !== 3) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '当前状态无法操作',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tconst newStatus = this.taskDetail.status === 1 ? 3 : 1;\n\t\t\tconst statusText = newStatus === 1 ? '启用' : '暂停';\n\t\t\t\n\t\t\ttry {\n\t\t\t\tthis.loading = true;\n\t\t\t\t\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-task',\n\t\t\t\t\taction: 'updateTask',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\ttask_id: this.taskId,\n\t\t\t\t\t\tstatus: newStatus\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: `${statusText}成功`,\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.taskDetail.status = newStatus;\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || `${statusText}失败`,\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('切换任务状态错误:', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `${statusText}任务出错`,\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 切换轮次详情显示状态\n\t\tasync toggleRoundDetails(round) {\n\t\t\t// 🔥 优化：按需加载轮次点位数据\n\t\t\tif (!round.expanded) {\n\t\t\t\t// 先关闭所有其他轮次的详情\n\t\t\t\tif (this.taskDetail && this.taskDetail.rounds_detail) {\n\t\t\t\t\tthis.taskDetail.rounds_detail.forEach(r => {\n\t\t\t\t\t\tif (r !== round) {\n\t\t\t\t\t\t\tthis.$set(r, 'expanded', false);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 🔥 检查是否已加载点位数据\n\t\t\t\tif (!round.points || round.points.length === 0) {\n\t\t\t\t\t// 显示加载状态\n\t\t\t\t\tthis.$set(round, 'loading', true);\n\t\t\t\t\t\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// 🔥 按需加载该轮次的完整点位数据\n\t\t\t\t\t\tawait this.loadRoundPoints(round);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('加载轮次点位失败:', e);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '加载点位信息失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t} finally {\n\t\t\t\t\t\tthis.$set(round, 'loading', false);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 打开当前轮次的详情\n\t\t\t\tthis.$set(round, 'expanded', true);\n\t\t\t} else {\n\t\t\t\t// 关闭当前轮次的详情\n\t\t\t\tthis.$set(round, 'expanded', false);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 🔥 新增：按需加载轮次点位数据\n\t\tasync loadRoundPoints(round) {\n\t\t\tif (!this.taskId || !round) return;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-task',\n\t\t\t\t\taction: 'getRoundPoints',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\ttask_id: this.taskId,\n\t\t\t\t\t\tround_number: round.round,\n\t\t\t\t\t\t// 只获取点位相关字段\n\t\t\t\t\t\tfields: [\n\t\t\t\t\t\t\t'points.point_id', 'points.name', 'points.order', 'points.status',\n\t\t\t\t\t\t\t'points.location', 'points.range', 'points.checkin_time', \n\t\t\t\t\t\t\t'points.record_id', 'points.qrcode_enabled', 'points.abnormal'\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data && res.data.points) {\n\t\t\t\t\t// 🔥 只更新该轮次的点位数据\n\t\t\t\t\tthis.$set(round, 'points', res.data.points);\n\t\t\t\t\t\n\t\t\t\t\t// 更新点位统计\n\t\t\t\t\tif (!round.point_stats) {\n\t\t\t\t\t\tround.point_stats = {\n\t\t\t\t\t\t\ttotal: res.data.points.length,\n\t\t\t\t\t\t\tchecked: res.data.points.filter(p => p.status === 1).length\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载轮次点位数据失败:', e);\n\t\t\t\tthrow e;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 格式化时间字符串\n\t\tformatTimeStr(timeStr) {\n\t\t\tif (!timeStr) return '--:--';\n\t\t\treturn timeStr;\n\t\t},\n\t\t\n\t\t// 获取轮次完成率\n\t\tgetRoundCompletion(round) {\n\t\t\tif (!round || !round.point_stats) return 0;\n\t\t\t\n\t\t\tconst total = round.point_stats.total || 0;\n\t\t\tconst checked = round.point_stats.checked || 0;\n\t\t\t\n\t\t\tif (total === 0) return 0;\n\t\t\treturn Math.round((checked / total) * 100);\n\t\t},\n\t\t\n\t\t// 计算剩余时间\n\t\tcalculateRemainingTime(round) {\n\t\t\tif (!round || !round.end_time) return '';\n\t\t\t\n\t\t\tconst now = new Date();\n\t\t\tconst endTime = new Date(round.end_time);\n\t\t\t\n\t\t\tif (now >= endTime) return '已结束';\n\t\t\t\n\t\t\tconst diff = endTime - now;\n\t\t\tconst minutes = Math.floor(diff / 1000 / 60);\n\t\t\t\n\t\t\tif (minutes < 60) {\n\t\t\t\treturn `剩余${minutes}分钟`;\n\t\t\t} else {\n\t\t\t\tconst hours = Math.floor(minutes / 60);\n\t\t\t\tconst remainingMinutes = minutes % 60;\n\t\t\t\treturn `剩余${hours}小时${remainingMinutes > 0 ? remainingMinutes + '分钟' : ''}`;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 返回上一页\n\t\tnavigateBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 获取班次时间 - 精简版本\n\t\tgetShiftTime(task) {\n\t\t\tif (!task) return '';\n\t\t\t\n\t\t\t// 1. 优先使用已处理的shift_time\n\t\t\tif (task.shift_time) {\n\t\t\t\treturn task.shift_time;\n\t\t\t}\n\t\t\t\n\t\t\t// 2. 使用班次对象中的时间\n\t\t\tif (task.shift) {\n\t\t\t\tconst start = task.shift.start_time || task.shift.startTime;\n\t\t\t\tconst end = task.shift.end_time || task.shift.endTime;\n\t\t\t\tif (start && end) {\n\t\t\t\t\ttask.shift_time = `(${start} - ${end})`;\n\t\t\t\t\treturn task.shift_time;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 3. 从加载的班次信息中获取\n\t\t\tif (task.shift_id && this.shiftInfo[task.shift_id]) {\n\t\t\t\tconst shift = this.shiftInfo[task.shift_id];\n\t\t\t\tconst start = shift.start_time || shift.startTime;\n\t\t\t\tconst end = shift.end_time || shift.endTime;\n\t\t\t\tif (start && end) {\n\t\t\t\t\ttask.shift_time = `(${start} - ${end})`;\n\t\t\t\t\treturn task.shift_time;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 4. 使用任务自身的时间字段\n\t\t\tconst startTime = task.startTime || task.start_time;\n\t\t\tconst endTime = task.endTime || task.end_time;\n\t\t\tif (startTime && endTime) {\n\t\t\t\ttask.shift_time = `(${startTime} - ${endTime})`;\n\t\t\t\treturn task.shift_time;\n\t\t\t}\n\t\t\t\n\t\t\t// 5. 使用轮次信息\n\t\t\tif (this.taskDetail && this.taskDetail.rounds_detail && this.taskDetail.rounds_detail.length > 0) {\n\t\t\t\tconst firstRound = this.taskDetail.rounds_detail[0];\n\t\t\t\tif (firstRound.time) {\n\t\t\t\t\treturn `(首轮:${firstRound.time}${firstRound.day_offset > 0 ? ' 次日' : ''})`;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 6. 返回班次名称\n\t\t\treturn task.shift_name ? `(${task.shift_name})` : '';\n\t\t},\n\t\t\n\t\t// 获取进度条样式类\n\t\tgetProgressClass(rate) {\n\t\t\tif (rate === 100) {\n\t\t\t\treturn 'completed';\n\t\t\t} else if (rate > 0 && rate < 100) {\n\t\t\t\treturn 'partial';\n\t\t\t}\n\t\t\treturn '';\n\t\t},\n\t\t\n\t\t// 获取点位状态样式类\n\t\tgetPointStatusClass(point, round) {\n\t\t\tif (point.status === 1 || point.status === 2) {\n\t\t\t\treturn 'success';\n\t\t\t} else if (point.status === 0 && round.status === 1) {\n\t\t\t\treturn 'warning';\n\t\t\t} else if (round.status === 3 && point.status === 0) {\n\t\t\t\treturn 'danger';\n\t\t\t}\n\t\t\treturn '';\n\t\t},\n\t\t\n\t\t// 确保overall_stats存在\n\t\tensureOverallStats() {\n\t\t\tif (!this.taskDetail.overall_stats) {\n\t\t\t\tthis.taskDetail.overall_stats = {};\n\t\t\t}\n\t\t\t\n\t\t\t// 计算总点位和已完成点位\n\t\t\tlet totalPoints = 0;\n\t\t\tlet completedPoints = 0;\n\t\t\tlet missedPoints = 0;\n\t\t\t\n\t\t\tthis.taskDetail.rounds_detail.forEach(round => {\n\t\t\t\tif (round.point_stats) {\n\t\t\t\t\tif (round.point_stats.total) {\n\t\t\t\t\t\ttotalPoints += round.point_stats.total;\n\t\t\t\t\t}\n\t\t\t\t\tif (round.point_stats.checked) {\n\t\t\t\t\t\tcompletedPoints += round.point_stats.checked;\n\t\t\t\t\t}\n\t\t\t\t\t// 计算缺卡点位\n\t\t\t\t\tconst roundMissed = (round.point_stats.total || 0) - (round.point_stats.checked || 0);\n\t\t\t\t\tif (roundMissed > 0) {\n\t\t\t\t\t\tmissedPoints += roundMissed;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 更新总体统计数据\n\t\t\tthis.taskDetail.overall_stats.total_points = totalPoints;\n\t\t\tthis.taskDetail.overall_stats.completed_points = completedPoints;\n\t\t\tthis.taskDetail.overall_stats.missed_points = missedPoints;\n\t\t\t\n\t\t\t// 计算总体完成率\n\t\t\tlet completionRate = 0;\n\t\t\tif (totalPoints > 0) {\n\t\t\t\tcompletionRate = Math.round((completedPoints / totalPoints) * 100);\n\t\t\t}\n\t\t\tthis.taskDetail.overall_stats.completion_rate = completionRate;\n\t\t\t\n\t\t\t// 确保异常记录数存在\n\t\t\tif (this.taskDetail.overall_stats.abnormal_count === undefined) {\n\t\t\t\tthis.taskDetail.overall_stats.abnormal_count = 0;\n\t\t\t}\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\">\n.detail-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\theight: 100vh;\n\tbackground-color: #F5F5F5;\n\tposition: relative;\n}\n\n.content {\n\tflex: 1;\n\tposition: relative;\n\toverflow-y: auto;\n}\n\n.content-wrapper {\n\tpadding: 20rpx;\n\tpadding-bottom: 140rpx; /* 给底部按钮留出空间 */\n}\n\n.loading-container, .empty-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 100rpx 0;\n}\n\n.loading-spinner {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder: 4rpx solid rgba(22, 119, 255, 0.1);\n\tborder-top: 4rpx solid #1677FF;\n\tborder-radius: 50%;\n\tanimation: spin 1s linear infinite;\n\tmargin-bottom: 20rpx;\n}\n\n@keyframes spin {\n\t0% { transform: rotate(0deg); }\n\t100% { transform: rotate(360deg); }\n}\n\n.loading-text, .empty-text {\n\tfont-size: 28rpx;\n\tcolor: #999999;\n}\n\n.empty-image {\n\twidth: 200rpx;\n\theight: 200rpx;\n\tmargin-bottom: 20rpx;\n\topacity: 0.8;\n\tfilter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));\n}\n\n.section-block {\n\tbackground-color: #FFFFFF;\n\twidth: 100%;\n\tmargin-bottom: 20rpx;\n\toverflow: hidden;\n\tborder-radius: 8rpx;\n\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\n\tbox-sizing: border-box;\n}\n\n.section-header {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 16rpx;\n\tposition: relative;\n\tborder-bottom: 1px solid #EEEEEE;\n}\n\n.section-title-bar {\n\twidth: 4rpx;\n\theight: 28rpx;\n\tbackground-color: #1677FF;\n\tmargin-right: 12rpx;\n\tborder-radius: 2rpx;\n}\n\n.section-title {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333333;\n\tflex: 1;\n}\n\n.section-content {\n\tpadding: 16rpx;\n\tbackground-color: #FFFFFF;\n}\n\n.info-row {\n\tdisplay: flex;\n\tpadding: 16rpx;\n\tline-height: 40rpx;\n\tborder-bottom: 1px solid #EEEEEE;\n\t\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n}\n\n.info-label {\n\twidth: 140rpx;\n\tfont-size: 28rpx;\n\tcolor: #666666;\n}\n\n.info-value {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tword-break: break-all;\n}\n\n.status-tag {\n\tfont-size: 24rpx;\n\tpadding: 4rpx 16rpx;\n\tborder-radius: 6rpx;\n\tmargin-left: auto;\n\t\n\t&.status-0 {\n\t\tbackground-color: #F5F5F5;\n\t\tcolor: #666666;\n\t}\n\t\n\t&.status-1 {\n\t\tbackground-color: #E6F7FF;\n\t\tcolor: #1677FF;\n\t}\n\t\n\t&.status-2 {\n\t\tbackground-color: #F6FFED;\n\t\tcolor: #52C41A;\n\t}\n\t\n\t&.status-3 {\n\t\tbackground-color: #FFF7E6;\n\t\tcolor: #FA8C16;\n\t}\n\t\n\t&.status-4 {\n\t\tbackground-color: #FFF1F0;\n\t\tcolor: #F5222D;\n\t}\n}\n\n/* 轮次部分样式 */\n.rounds-section {\n\tbackground-color: #FFFFFF;\n\tborder-radius: 8rpx;\n\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\n\tmargin-bottom: 20rpx;\n\toverflow: hidden;\n}\n\n.round-count {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tmargin-left: 10rpx;\n}\n\n.round-list {\n\tbackground-color: #FFFFFF;\n}\n\n.round-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\tpadding: 20rpx;\n\tborder-bottom: 1px solid #EEEEEE;\n\t\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n}\n\n.round-header {\n\tdisplay: flex;\n\tflex-direction: column;\n\twidth: 100%;\n}\n\n.round-title {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tfont-weight: bold;\n\tmargin-bottom: 8rpx;\n}\n\n.round-time {\n\tcolor: #666666;\n\tfont-size: 24rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tflex-wrap: wrap;\n\tmargin-bottom: 12rpx;\n}\n\n.round-info {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-top: 10rpx;\n}\n\n.round-progress {\n\tflex: 1;\n\tmargin-right: 16rpx;\n}\n\n.effective-duration {\n\tmargin-left: 10rpx;\n\tcolor: #1677FF;\n}\n\n.remaining-time {\n\tmargin-left: 10rpx;\n\tcolor: #FF9500;\n}\n\n.next-day-badge {\n\tdisplay: inline-block;\n\tfont-size: 22rpx;\n\tpadding: 2rpx 8rpx;\n\tbackground-color: #FF9500;\n\tcolor: #FFFFFF;\n\tborder-radius: 4rpx;\n\tmargin-left: 8rpx;\n}\n\n.progress-label {\n\tfont-size: 24rpx;\n\tcolor: #666666;\n\tmargin-bottom: 4rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n}\n\n.progress-value {\n\tfont-size: 24rpx;\n\tcolor: #333333;\n}\n\n.progress-bar {\n\twidth: 100%;\n\theight: 16rpx;\n\tbackground-color: #F5F5F5;\n\tborder-radius: 8rpx;\n\toverflow: hidden;\n\tposition: relative;\n}\n\n.progress-bg {\n\twidth: 100%;\n\theight: 100%;\n\tposition: absolute;\n\tbackground-color: #F5F5F5;\n}\n\n.progress-fill {\n\theight: 100%;\n\tbackground-color: #52C41A;\n\tborder-radius: 8rpx;\n\tposition: relative;\n\tz-index: 1;\n\ttransition: width 0.5s ease;\n\t\n\t.round-item--completed & {\n\t\tbackground-color: #52C41A;\n\t}\n\t\n\t.round-item--active & {\n\t\tbackground-color: #1677FF;\n\t}\n\t\n\t.round-item--expired & {\n\t\tbackground-color: #FA8C16;\n\t}\n}\n\n.round-badge {\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 4rpx;\n\tfont-size: 24rpx;\n\t\n\t&.status-waiting {\n\t\tbackground-color: #F5F5F5;\n\t\tcolor: #666666;\n\t}\n\t\n\t&.status-active {\n\t\tbackground-color: #E6F7FF;\n\t\tcolor: #1677FF;\n\t}\n\t\n\t&.status-completed {\n\t\tbackground-color: #F6FFED;\n\t\tcolor: #52C41A;\n\t}\n\t\n\t&.status-expired {\n\t\tbackground-color: #FFF7E6;\n\t\tcolor: #FA8C16;\n\t}\n}\n\n/* 点位列表样式 */\n.point-list {\n\tmargin-top: 16rpx;\n\tborder-top: 1px dashed #EEEEEE;\n\tpadding-top: 16rpx;\n}\n\n.point-title {\n\tmargin-bottom: 12rpx;\n\tfont-size: 26rpx;\n\tcolor: #666666;\n}\n\n.point-items {\n\tbackground-color: #F9F9F9;\n\tborder-radius: 8rpx;\n\toverflow: hidden;\n}\n\n.point-item {\n\tpadding: 16rpx;\n\tborder-bottom: 1px solid #EEEEEE;\n\t\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n}\n\n.point-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 8rpx;\n}\n\n.point-name-section {\n\tdisplay: flex;\n\talign-items: center;\n\tflex: 1;\n\toverflow: hidden;\n}\n\n.point-index {\n\twidth: 36rpx;\n\theight: 36rpx;\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n\tfont-size: 24rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 12rpx;\n\tflex-shrink: 0;\n}\n\n.point-name {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tflex: 1;\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n.point-range {\n\tfont-size: 24rpx;\n\tcolor: #1677FF;\n\tbackground-color: #E6F7FF;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 999rpx;\n\tmargin-left: 10rpx;\n\tflex-shrink: 0;\n}\n\n.point-status-badge {\n\tfont-size: 24rpx;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 4rpx;\n\tbackground-color: #F5F5F5;\n\tcolor: #999999;\n\t\n\t&.success {\n\t\tbackground-color: #F6FFED;\n\t\tcolor: #52C41A;\n\t}\n\t\n\t&.warning {\n\t\tbackground-color: #FFFBE6;\n\t\tcolor: #FAAD14;\n\t}\n\t\n\t&.danger {\n\t\tbackground-color: #FFF1F0;\n\t\tcolor: #FF4D4F;\n\t}\n}\n\n.point-address {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 6rpx;\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tmargin-bottom: 8rpx;\n}\n\n.point-checkin-info {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tfont-size: 24rpx;\n\tcolor: #666666;\n}\n\n.checkin-time {\n\tcolor: #1677FF;\n}\n\n.checkin-status {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 4rpx;\n\tcolor: #FF9500;\n}\n\n.shift-time {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tmargin-left: 8rpx;\n}\n\n/* 按钮区域样式 */\n.button-section {\n\tmargin: 20rpx 0;\n\tpadding: 0;\n}\n\n.action-buttons {\n\tdisplay: flex;\n\tgap: 20rpx;\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\n}\n\n.cancel-btn, .primary-btn, .status-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n}\n\n.cancel-btn {\n\tbackground-color: #F5F5F5;\n\tcolor: #666666;\n\tborder: 1rpx solid #EEEEEE;\n}\n\n.primary-btn {\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n}\n\n.status-btn {\n\tbackground-color: #FFFFFF;\n\tcolor: #FA8C16;\n\tborder: 1px solid #FA8C16;\n}\n\n.overall-stats {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.stats-row {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tpadding: 16rpx;\n\tborder-bottom: 1px solid #EEEEEE;\n\t\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n}\n\n.stats-label {\n\tfont-size: 28rpx;\n\tcolor: #666666;\n}\n\n.stats-value {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n}\n\n.progress-bar-wrapper {\n\twidth: 100%;\n\theight: 20rpx;\n\tbackground-color: #F5F5F5;\n\tborder-radius: 10rpx;\n\toverflow: hidden;\n\tmargin-top: 16rpx;\n}\n\n.progress-inner {\n\theight: 100%;\n\tbackground-color: #1677FF;\n\tborder-radius: 10rpx;\n\ttransition: width 0.5s ease;\n\t\n\t&.completed {\n\t\tbackground-color: #52C41A;\n\t}\n\t\n\t&.partial {\n\t\tbackground-color: #1677FF;\n\t}\n}\n\n.progress-bar-wrapper.overall-progress {\n\tmargin-top: 12rpx;\n}\n\n.time-remaining {\n\tfont-size: 22rpx;\n\tcolor: #1677FF;\n\tmargin-left: 8rpx;\n}\n\n/* 新增样式 */\n.point-loading {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 20rpx;\n}\n\n.loading-spinner.small {\n\twidth: 30rpx;\n\theight: 30rpx;\n\tborder: 2rpx solid rgba(22, 119, 255, 0.1);\n\tborder-top: 2rpx solid #1677FF;\n\tborder-radius: 50%;\n\tanimation: spin 1s linear infinite;\n\tmargin-bottom: 10rpx;\n}\n\n@keyframes spin {\n\t0% { transform: rotate(0deg); }\n\t100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n}\n\n.point-empty {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 20rpx;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558437839\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
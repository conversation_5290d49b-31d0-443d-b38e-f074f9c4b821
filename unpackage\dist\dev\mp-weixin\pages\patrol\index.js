(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol/index"],{

/***/ 63:
/*!*********************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fpatrol%2Findex"} ***!
  \*********************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index2 = _interopRequireDefault(__webpack_require__(/*! ./pages/patrol/index.vue */ 64));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index2.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 64:
/*!**************************************!*\
  !*** D:/Xwzc/pages/patrol/index.vue ***!
  \**************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_4321fcc8___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=4321fcc8& */ 65);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 67);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&lang=scss& */ 73);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_4321fcc8___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_4321fcc8___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _index_vue_vue_type_template_id_4321fcc8___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/patrol/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 65:
/*!*********************************************************************!*\
  !*** D:/Xwzc/pages/patrol/index.vue?vue&type=template&id=4321fcc8& ***!
  \*********************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4321fcc8___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4321fcc8& */ 66);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4321fcc8___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4321fcc8___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4321fcc8___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4321fcc8___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 66:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol/index.vue?vue&type=template&id=4321fcc8& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 483))
    },
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 490))
    },
    uniLoadMore: function () {
      return Promise.all(/*! import() | uni_modules/uni-load-more/components/uni-load-more/uni-load-more */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-load-more/components/uni-load-more/uni-load-more")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue */ 497))
    },
    uniPopupMessage: function () {
      return Promise.all(/*! import() | uni_modules/uni-popup/components/uni-popup-message/uni-popup-message */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-popup/components/uni-popup-message/uni-popup-message")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup-message/uni-popup-message.vue */ 508))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.getAccuracyColor()
  var g0 = _vm.currentLocation.accuracy
    ? _vm.currentLocation.accuracy.toFixed(1)
    : null
  var g1 = _vm.taskList.length
  var l0 = !(g1 === 0)
    ? _vm.__map(_vm.taskList, function (task, index) {
        var $orig = _vm.__get_orig(task)
        var m1 = _vm.getTaskShift(task)
        var m2 = _vm.getTaskRoundInfo(task)
        return {
          $orig: $orig,
          m1: m1,
          m2: m2,
        }
      })
    : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        g0: g0,
        g1: g1,
        l0: l0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 67:
/*!***************************************************************!*\
  !*** D:/Xwzc/pages/patrol/index.vue?vue&type=script&lang=js& ***!
  \***************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 68);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 68:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol/index.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, wx) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _locationServices = __webpack_require__(/*! @/utils/location-services.js */ 69);
var _geocoder = _interopRequireDefault(__webpack_require__(/*! @/utils/geocoder.js */ 70));
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! @/utils/patrol-api.js */ 71));
var _date = __webpack_require__(/*! @/utils/date.js */ 72);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var PMap = function PMap() {
  __webpack_require__.e(/*! require.ensure | components/patrol/p-map */ "components/patrol/p-map").then((function () {
    return resolve(__webpack_require__(/*! @/components/patrol/p-map.vue */ 516));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var PWeather = function PWeather() {
  __webpack_require__.e(/*! require.ensure | components/patrol/p-weather */ "components/patrol/p-weather").then((function () {
    return resolve(__webpack_require__(/*! @/components/patrol/p-weather.vue */ 523));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var PTaskCard = function PTaskCard() {
  __webpack_require__.e(/*! require.ensure | components/patrol/p-task-card */ "components/patrol/p-task-card").then((function () {
    return resolve(__webpack_require__(/*! @/components/patrol/p-task-card.vue */ 530));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var PEmptyState = function PEmptyState() {
  __webpack_require__.e(/*! require.ensure | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then((function () {
    return resolve(__webpack_require__(/*! @/components/p-empty-state/p-empty-state.vue */ 483));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
// 统一的轮次排序函数，与任务列表页保持一致
function sortRounds(rounds) {
  if (!rounds || !Array.isArray(rounds) || rounds.length === 0) {
    return [];
  }

  // 使用与任务列表页相同的排序逻辑
  // 按状态分组：未开始和进行中的轮次，已完成和已超时的轮次
  var notStartedOrActive = rounds.filter(function (round) {
    return round.status === 0 || round.status === 1;
  });
  var completedOrExpired = rounds.filter(function (round) {
    return round.status === 2 || round.status === 3;
  });

  // 分别排序
  notStartedOrActive.sort(function (a, b) {
    return a.round - b.round;
  }); // 按轮次号升序
  completedOrExpired.sort(function (a, b) {
    return b.round - a.round;
  }); // 按轮次号降序

  // 合并排序后的数组
  return [].concat((0, _toConsumableArray2.default)(notStartedOrActive), (0, _toConsumableArray2.default)(completedOrExpired));
}
var _default = {
  components: {
    PMap: PMap,
    PWeather: PWeather,
    PTaskCard: PTaskCard,
    PEmptyState: PEmptyState
  },
  data: function data() {
    return {
      // 用户位置
      userLocation: null,
      // 当前激活的任务ID
      activeTaskId: '',
      // 当前选中的任务
      currentTask: null,
      // 当前选择的轮次索引，-1表示自动选择（当前/最后一轮）
      selectedRoundIndex: -1,
      // 当前选择的轮次，null表示自动选择
      selectedRound: null,
      // 任务列表
      taskList: [],
      // 任务轮次信息
      taskRoundInfos: {},
      // 任务班次信息
      taskShifts: {},
      // 路线缓存
      routeCache: {},
      // 地图标记点
      markers: [],
      // 地图路线
      polylines: [],
      // 地图圆形区域
      circles: [],
      // 地图中心点
      mapCenter: {
        latitude: 30.0,
        longitude: 120.0
      },
      // 天气信息
      weatherInfo: null,
      // 加载状态
      isLoading: false,
      // 初始加载标志，防止状态跳变
      isInitialLoading: false,
      // 下拉刷新状态
      isRefreshing: false,
      // 空状态
      isEmpty: false,
      // 过滤状态
      filterStatus: 'all',
      // 'all', 'active', 'completed'

      // 显示地图
      showMap: true,
      // 位置信息
      currentLocation: {
        latitude: 0,
        longitude: 0,
        accuracy: 0,
        altitude: 0,
        speed: 0,
        address: '',
        isLocating: false,
        lastUpdated: 0
      },
      // UI状态
      showFilterPanel: false,
      showTaskDetail: false,
      showWeather: true,
      // 轮次倒计时更新间隔（毫秒）
      roundUpdateInterval: 3000,
      // 从5秒减少到3秒，进一步提高状态更新响应速度

      // 用户数据映射
      userMap: {},
      // 控制任务选择器是否展开
      isTaskSelectorExpanded: false,
      // 控制侧边抽屉是否打开
      isDrawerOpen: false,
      // 标志，防止重复显示登录提示
      isShowingLoginTip: false,
      touchStartY: 0,
      touchMoveY: 0,
      drawerHeight: 0,
      isDragging: false,
      currentTranslateY: 0,
      drawerStyle: {},
      maskStyle: {},
      lastTouchTime: 0,
      lastDeltaY: 0,
      // 🔥 新增：防抖机制相关变量
      lastLoadTime: 0,
      loadingInProgress: false,
      hasInitialLoaded: false
    };
  },
  computed: {
    // 当前日期 YYYY-MM-DD
    currentDate: function currentDate() {
      // 使用formatDate工具函数替代手动格式化
      return (0, _date.formatDate)(new Date(), 'YYYY-MM-DD');
    },
    // 地图高度
    mapHeight: function mapHeight() {
      // 使用固定高度，不再根据底部区域变化
      return '100vh';
    },
    // 当前任务的点位
    currentRoutePoints: function currentRoutePoints() {
      if (!this.currentTask) return [];

      // 获取当前任务的点位信息 - 优先使用rounds_detail
      if (this.currentTask.rounds_detail && Array.isArray(this.currentTask.rounds_detail)) {
        // 获取当前轮次
        var taskId = this.currentTask._id;
        var round = null;

        // 如果用户选择了特定轮次，使用选中的轮次
        if (this.selectedRound && this.selectedRound.taskId === taskId) {
          round = this.selectedRound.round;
        }
        // 否则使用系统自动判断的轮次
        else {
          var roundInfo = this.taskRoundInfos[taskId];
          if (roundInfo && roundInfo.status === 'active' && roundInfo.currentRound) {
            round = roundInfo.currentRound;
          } else if (roundInfo && roundInfo.status === 'completed' && roundInfo.lastRound) {
            round = roundInfo.lastRound;
          } else if (roundInfo && roundInfo.status === 'waiting' && roundInfo.nextRound) {
            round = roundInfo.nextRound;
          } else {
            // 如果没有特定轮次，使用第一个轮次
            var rounds = sortRounds(this.currentTask.rounds_detail);
            round = rounds.length > 0 ? rounds[0] : null;
          }
        }

        // 使用轮次中的points数据，确保包含完整的状态信息
        if (round && round.points && Array.isArray(round.points)) {
          return round.points;
        }
      }

      // 如果没有轮次数据，尝试从route_detail获取点位
      if (this.currentTask.route_detail && this.currentTask.route_detail.points) {
        return this.currentTask.route_detail.points;
      }
      return [];
    },
    // 当前任务的轮次列表
    availableRounds: function availableRounds() {
      if (!this.currentTask || !this.currentTask.rounds_detail || !Array.isArray(this.currentTask.rounds_detail)) {
        return [];
      }
      return sortRounds(this.currentTask.rounds_detail);
    },
    // 当前轮次信息
    currentRoundInfo: function currentRoundInfo() {
      if (!this.currentTask) {
        return {
          status: 'no_task'
        };
      }
      var task = this.currentTask;
      return this.getTaskRoundInfo(task);
    }
  },
  onLoad: function onLoad(options) {
    // 初始化位置和任务
    this.initLocationAndTasks();

    // 开始更新轮次倒计时
    this.startRoundUpdateTimer();

    // 添加页面事件监听 - 确保事件名称一致性
    uni.$on('refresh-task-list', this.refreshTaskList);

    // 注册多种可能的任务更新事件，确保能接收到更新消息
    uni.$on('task-updated', this.handleTaskUpdated);
    uni.$on('patrol-task-updated', this.handleTaskUpdated);
    uni.$on('check-in-completed', this.handleTaskUpdated);
  },
  onUnload: function onUnload() {
    // 取消页面事件监听
    uni.$off('refresh-task-list', this.refreshTaskList);
    uni.$off('task-updated', this.handleTaskUpdated);
    uni.$off('patrol-task-updated', this.handleTaskUpdated);
    uni.$off('check-in-completed', this.handleTaskUpdated);

    // 清除轮次更新定时器
    this.clearRoundUpdateTimer();

    // 停止位置监听
    this.stopLocationWatch();
  },
  onShow: function onShow() {
    var _this = this;
    if (this.isOnboarding) {
      // 如果正在进行引导，则不执行刷新任务
      this.isOnboarding = false;
    } else if (!this.hasInitialLoaded) {
      // 🔥 首次显示时不刷新，因为 onLoad 已经加载了
      this.hasInitialLoaded = true;
      console.log('首次显示页面，跳过刷新（onLoad已处理）');
    } else {
      // 🔥 只有从其他页面返回时才刷新
      console.log('从其他页面返回，执行刷新');
      // 设置缓冲标记，防止状态快速跳变 - 保持这个标记
      this.isInitialLoading = true;

      // 每次显示页面时都刷新任务列表，确保数据最新
      this.refreshTaskList(false); // 使用静默刷新，避免每次都显示加载提示

      // 检查是否从打卡页面返回并有需要刷新的任务ID
      if (getApp().globalData && getApp().globalData.checkedInTaskId) {
        var refreshTaskId = getApp().globalData.checkedInTaskId;
        // 清除标记
        getApp().globalData.checkedInTaskId = null;
        console.log('检测到打卡页面返回，刷新任务:', refreshTaskId);

        // 标记任务需要刷新，并延迟执行刷新，避免过于频繁的操作
        // 简单的刷新列表通常能覆盖大部分场景
        // 如果问题依然存在，再考虑更精细化的处理

        // 延迟清除缓冲标记，给列表刷新留出时间
        setTimeout(function () {
          _this.isInitialLoading = false;
        }, 800); // 稍微延长缓冲时间
      } else {
        // 没有特定任务需要刷新，延迟清除缓冲标记
        setTimeout(function () {
          _this.isInitialLoading = false;
        }, 800); // 稍微延长缓冲时间
      }
    }

    // 重要：每次页面显示都重新启动位置监听
    // 这样可以确保从其他页面返回(特别是使用手机手势返回)时位置监听能被正确启动
    console.log('页面显示，重新启动位置监听');
    setTimeout(function () {
      _this.restartLocationWatch();
    }, 300); // 稍微延迟启动，避免与其他操作冲突
  },

  methods: {
    // GPS精度颜色判断方法
    getAccuracyColor: function getAccuracyColor() {
      var accuracy = this.currentLocation.accuracy;
      if (!accuracy) return '#999999';
      if (accuracy <= 5) return '#34C759'; // 绿色 - 精度极好
      if (accuracy <= 10) return '#00C58E'; // 青色 - 精度良好
      if (accuracy <= 15) return '#FFD60A'; // 黄色 - 精度一般
      if (accuracy <= 20) return '#FF9500'; // 橙色 - 精度较差
      if (accuracy <= 25) return '#FF6B2C'; // 深橙色 - 精度很差
      return '#FF3B30'; // 红色 - 精度极差
    },
    // 初始化位置和任务
    initLocationAndTasks: function initLocationAndTasks() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                // 立即设置加载状态
                _this2.isLoading = true;

                // 获取位置信息
                _context.next = 4;
                return _this2.initLocation();
              case 4:
                _context.next = 6;
                return _this2.loadUsers();
              case 6:
                _context.next = 8;
                return _this2.loadTaskList(true);
              case 8:
                _context.next = 14;
                break;
              case 10:
                _context.prev = 10;
                _context.t0 = _context["catch"](0);
                console.error('初始化位置和任务出错：', _context.t0);
                uni.showToast({
                  title: '加载任务失败，请重试',
                  icon: 'none'
                });
              case 14:
                _context.prev = 14;
                _this2.isLoading = false;
                return _context.finish(14);
              case 17:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 10, 14, 17]]);
      }))();
    },
    // 初始化位置信息
    initLocation: function initLocation() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var location, _geocodeResult$addres, geocodeResult, district;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _this3.isLoading = true;

                // 获取当前位置
                _context2.next = 4;
                return (0, _locationServices.getCurrentLocation)({
                  isHighAccuracy: true,
                  // 请求高精度定位
                  highAccuracyExpireTime: 4000 // 高精度定位超时时间
                });
              case 4:
                location = _context2.sent;
                // 更新用户位置和当前位置
                _this3.userLocation = {
                  latitude: location.latitude,
                  longitude: location.longitude
                };

                // 确保包含完整的位置信息
                _this3.currentLocation = {
                  latitude: location.latitude,
                  longitude: location.longitude,
                  accuracy: location.accuracy || 0,
                  altitude: location.altitude || 0,
                  speed: location.speed || 0,
                  address: '',
                  isLocating: false,
                  lastUpdated: Date.now()
                };

                // 设置地图中心
                _this3.mapCenter = {
                  latitude: location.latitude,
                  longitude: location.longitude
                };

                // 先更新当前位置的范围圈
                _this3.updateCurrentLocationMarker();

                // 如果有任务，重新构建任务标记
                if (_this3.currentTask) {
                  _this3.buildTaskMarkers();
                }

                // 获取地址信息
                _context2.prev = 10;
                _context2.next = 13;
                return _geocoder.default.reverseGeocoder(_this3.currentLocation);
              case 13:
                geocodeResult = _context2.sent;
                _this3.currentLocation.address = geocodeResult.address || '';
                district = ((_geocodeResult$addres = geocodeResult.address_component) === null || _geocodeResult$addres === void 0 ? void 0 : _geocodeResult$addres.district) || '';
                uni.setNavigationBarTitle({
                  title: "\u5DE1\u89C6\u6253\u5361 - ".concat(district)
                });
                _context2.next = 22;
                break;
              case 19:
                _context2.prev = 19;
                _context2.t0 = _context2["catch"](10);
                console.error('获取地址信息失败', _context2.t0);
              case 22:
                // 开始监听位置变化
                _this3.startLocationWatch();
                _context2.next = 29;
                break;
              case 25:
                _context2.prev = 25;
                _context2.t1 = _context2["catch"](0);
                console.error('初始化位置失败', _context2.t1);
                uni.showToast({
                  title: '获取位置失败，请检查定位权限',
                  icon: 'none'
                });
              case 29:
                _context2.prev = 29;
                setTimeout(function () {
                  _this3.isLoading = false;
                }, 200);
                return _context2.finish(29);
              case 32:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 25, 29, 32], [10, 19]]);
      }))();
    },
    // 开始监听位置变化
    startLocationWatch: function startLocationWatch() {
      var _this4 = this;
      console.log('开始位置监听');
      if (this.locationChangeListener) {
        console.log('位置监听已存在，不需要重新启动');
        return;
      }
      try {
        // 使用微信原生API直接监听位置变化
        wx.startLocationUpdate({
          type: 'gcj02',
          success: function success() {
            console.log('位置监听启动成功');

            // 设置位置变化回调
            wx.onLocationChange(function (res) {
              // 保存当前的circles数组
              var currentCircles = (0, _toConsumableArray2.default)(_this4.circles);

              // 更新当前位置信息
              _this4.currentLocation = {
                latitude: res.latitude,
                longitude: res.longitude,
                accuracy: res.accuracy || 0,
                altitude: res.altitude || 0,
                speed: res.speed || 0,
                isLocating: false,
                lastUpdated: Date.now()
              };

              // 更新用户位置
              _this4.userLocation = {
                latitude: res.latitude,
                longitude: res.longitude
              };

              // 恢复circles数组并更新位置标记
              _this4.circles = currentCircles;
              _this4.updateCurrentLocationMarker();
            });

            // 标记位置监听已启动
            _this4.locationChangeListener = true;
          },
          fail: function fail(err) {
            console.error('启动位置监听失败:', err);
            _this4.locationChangeListener = false;
          }
        });
      } catch (err) {
        console.error('启动位置监听出错:', err);
        this.locationChangeListener = false;
      }
    },
    // 停止位置监听
    stopLocationWatch: function stopLocationWatch() {
      var _this5 = this;
      console.log('停止位置监听');
      if (!this.locationChangeListener) {
        console.log('没有活跃的位置监听，无需停止');
        return;
      }
      try {
        // 使用微信原生API停止位置监听
        wx.stopLocationUpdate({
          success: function success() {
            console.log('停止位置监听成功');
          },
          fail: function fail(err) {
            console.error('停止位置监听失败:', err);
          },
          complete: function complete() {
            try {
              // 无论成功失败，都尝试解绑监听器
              wx.offLocationChange();
            } catch (e) {
              console.error('解除位置监听绑定失败:', e);
            }
            // 重置监听标志
            _this5.locationChangeListener = false;
          }
        });
      } catch (err) {
        console.error('停止位置监听出错:', err);
        // 确保标志被重置
        this.locationChangeListener = false;
      }
    },
    // 重新启动位置监听（先停止再启动）
    restartLocationWatch: function restartLocationWatch() {
      var _this6 = this;
      try {
        // 无论当前状态如何，先尝试停止现有监听
        this.forceStopLocationWatch();

        // 短暂延迟后启动新的监听
        setTimeout(function () {
          _this6.startLocationWatch();
        }, 200);
      } catch (err) {
        console.error('重启位置监听出错:', err);
      }
    },
    // 强制停止位置监听（不检查状态）
    forceStopLocationWatch: function forceStopLocationWatch() {
      try {
        // 使用微信原生API停止和解绑，无论当前状态如何
        wx.stopLocationUpdate();
        wx.offLocationChange();
        this.locationChangeListener = false;
      } catch (err) {
        console.error('强制停止位置监听出错:', err);
        this.locationChangeListener = false;
      }
    },
    // 移动到当前位置
    moveToCurrentLocation: function moveToCurrentLocation() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var res;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                // 显示加载中提示
                uni.showLoading({
                  title: '获取位置中...',
                  mask: true
                });

                // 使用微信原生API获取高精度位置
                _context3.next = 4;
                return new Promise(function (resolve, reject) {
                  wx.getLocation({
                    type: 'gcj02',
                    isHighAccuracy: true,
                    highAccuracyExpireTime: 5000,
                    success: resolve,
                    fail: reject
                  });
                });
              case 4:
                res = _context3.sent;
                // 更新用户位置
                _this7.userLocation = {
                  latitude: res.latitude,
                  longitude: res.longitude
                };

                // 更新地图中心点
                _this7.mapCenter = {
                  latitude: res.latitude,
                  longitude: res.longitude
                };

                // 如果地图组件存在，移动到新位置
                if (_this7.$refs.patrolMap) {
                  _this7.$refs.patrolMap.moveToLocation(_this7.userLocation);
                }

                // 隐藏加载提示
                uni.hideLoading();
                _context3.next = 16;
                break;
              case 11:
                _context3.prev = 11;
                _context3.t0 = _context3["catch"](0);
                console.error('获取位置失败:', _context3.t0);

                // 如果获取位置失败，尝试使用现有位置
                if (_this7.userLocation && _this7.userLocation.latitude && _this7.userLocation.longitude) {
                  // 更新地图中心点
                  _this7.mapCenter = {
                    latitude: _this7.userLocation.latitude,
                    longitude: _this7.userLocation.longitude
                  };

                  // 如果地图组件存在，移动到现有位置
                  if (_this7.$refs.patrolMap) {
                    _this7.$refs.patrolMap.moveToLocation(_this7.userLocation);
                  }
                } else {
                  // 如果没有位置信息，重新初始化位置
                  _this7.initLocation();
                }

                // 隐藏加载提示
                uni.hideLoading();
              case 16:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 11]]);
      }))();
    },
    // 加载任务列表
    loadTaskList: function loadTaskList() {
      var _arguments = arguments,
        _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var showLoading, userId, res, rawTaskList, taskList, newTaskList;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                showLoading = _arguments.length > 0 && _arguments[0] !== undefined ? _arguments[0] : false;
                if (_this8.checkLoginState()) {
                  _context4.next = 3;
                  break;
                }
                return _context4.abrupt("return");
              case 3:
                _context4.prev = 3;
                // 设置初始加载标志，防止状态跳变
                _this8.isInitialLoading = true;
                if (showLoading) {
                  _this8.showLoading(true);
                }

                // 获取当前用户ID
                userId = _this8.getCurrentUserId();
                if (userId) {
                  _context4.next = 11;
                  break;
                }
                _this8.showLoginTip();
                if (showLoading) {
                  _this8.showLoading(false);
                }
                return _context4.abrupt("return");
              case 11:
                _context4.next = 13;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'getTaskList',
                  data: {
                    level: 'list',
                    // 🔥 启用轻量级模式，减少RU消耗
                    fields: '_id,name,status,area,patrol_date,shift_id,shift_name,user_id,user_name,route_name,create_date,overall_stats,rounds_detail.round,rounds_detail.name,rounds_detail.status,rounds_detail.start_time,rounds_detail.end_time,rounds_detail.duration,rounds_detail.stats',
                    // 🔥 字段过滤，排除points数组
                    // 🔥 新增：强制个人模式，巡视首页只显示自己的任务
                    viewScope: 'personal',
                    params: {
                      patrolDate: _this8.currentDate,
                      pageSize: 100,
                      status: -1 // 全部状态
                      // 🔥 移除硬编码userId，改为使用viewScope控制
                    }
                  }
                });
              case 13:
                res = _context4.sent;
                if (!(res.code === 0 && res.data)) {
                  _context4.next = 27;
                  break;
                }
                // 获取原始任务列表
                rawTaskList = res.data.list || []; // 处理任务数据，与任务列表页相同的处理逻辑
                taskList = _this8.processTasks(rawTaskList); // 明确过滤只显示分配给当前用户的任务
                if (userId) {
                  taskList = taskList.filter(function (task) {
                    return task.user_id === userId;
                  });
                }

                // 预加载任务关联的班次信息
                _context4.next = 20;
                return _this8.preloadTaskShifts(taskList);
              case 20:
                // 更新每个任务的轮次信息
                _this8.updateTasksRoundInfo(taskList);

                // 处理空状态
                _this8.isEmpty = taskList.length === 0;

                // 暂存新任务列表，但不立即更新视图
                // 这个延迟将防止任务状态在初始加载时跳变
                newTaskList = (0, _toConsumableArray2.default)(taskList); // 如果已选中任务不在新列表中，清除选择
                if (_this8.activeTaskId && !newTaskList.some(function (task) {
                  return task._id === _this8.activeTaskId;
                })) {
                  _this8.activeTaskId = '';
                  _this8.currentTask = null;
                }

                // 在完成所有准备工作后，一次性更新taskList
                _this8.$nextTick(function () {
                  // 使用整体替换方式更新任务列表，避免Vue部分更新导致的视图问题
                  _this8.taskList = newTaskList;

                  // 如果任务列表不为空，且没有选中任务，自动选中第一个任务
                  if (_this8.taskList.length > 0 && !_this8.activeTaskId) {
                    _this8.activeTaskId = _this8.taskList[0]._id;
                    _this8.loadTaskDetail(_this8.activeTaskId);
                  }
                  // 如果有活动任务ID但未加载任务详情
                  else if (_this8.activeTaskId && !_this8.currentTask) {
                    _this8.loadTaskDetail(_this8.activeTaskId);
                  }

                  // 清除初始加载标志
                  _this8.isInitialLoading = false;
                });
                _context4.next = 28;
                break;
              case 27:
                if (res.code === 401) {
                  // 未登录状态
                  _this8.taskList = [];
                  _this8.isEmpty = true;
                  _this8.showLoginTip();
                  _this8.isInitialLoading = false;
                } else {
                  _this8.showError(res.message || '获取任务列表失败');
                  _this8.isInitialLoading = false;
                }
              case 28:
                _context4.next = 35;
                break;
              case 30:
                _context4.prev = 30;
                _context4.t0 = _context4["catch"](3);
                console.error('加载任务列表失败', _context4.t0);
                _this8.showError('加载任务列表失败');
                _this8.isInitialLoading = false;
              case 35:
                _context4.prev = 35;
                _this8.showLoading(false);
                return _context4.finish(35);
              case 38:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[3, 30, 35, 38]]);
      }))();
    },
    // 获取当前用户ID方法
    getCurrentUserId: function getCurrentUserId() {
      try {
        // 1. 首先尝试从uni-id-pages-userInfo获取
        var userInfo = uni.getStorageSync('uni-id-pages-userInfo');
        if (userInfo) {
          if ((0, _typeof2.default)(userInfo) === 'object') {
            return userInfo._id;
          }
          if (typeof userInfo === 'string') {
            try {
              var parsed = JSON.parse(userInfo);
              return parsed._id;
            } catch (e) {
              console.error('解析用户信息失败:', e);
            }
          }
        }

        // 2. 尝试从token获取
        var tokenInfo = uni.getStorageSync('uni_id_token');
        if (tokenInfo) {
          if ((0, _typeof2.default)(tokenInfo) === 'object' && tokenInfo.uid) {
            return tokenInfo.uid;
          }
          if (typeof tokenInfo === 'string') {
            try {
              var _parsed = JSON.parse(tokenInfo);
              return _parsed.uid;
            } catch (e) {
              // 错误处理已简化
            }
          }
        }

        // 3. 尝试从其他存储位置获取
        return uni.getStorageSync('uni_id_user_id') || uni.getStorageSync('uid');
      } catch (e) {
        console.error('获取用户ID失败');
        return null;
      }
    },
    // 修改预加载任务关联的班次信息方法
    preloadTaskShifts: function preloadTaskShifts(tasks) {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var shiftPromises, loadedShiftIds;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                shiftPromises = [];
                loadedShiftIds = []; // 收集所有需要加载的班次ID
                tasks.forEach(function (task) {
                  if (task.shift_id && !_this9.taskShifts[task.shift_id] && !loadedShiftIds.includes(task.shift_id)) {
                    loadedShiftIds.push(task.shift_id);

                    // 加载班次详情，使用PatrolApi.call标准格式
                    var promise = _patrolApi.default.call({
                      name: 'patrol-shift',
                      action: 'getShiftDetail',
                      data: {
                        shift_id: task.shift_id,
                        userId: _this9.getCurrentUserId()
                      }
                    }).then(function (res) {
                      if (res.code === 0 && res.data) {
                        _this9.taskShifts[task.shift_id] = res.data;

                        // 更新任务轮次信息
                        _this9.updateTaskRoundInfo(task);
                      }
                    }).catch(function (err) {
                      console.error("\u52A0\u8F7D\u73ED\u6B21\u4FE1\u606F\u5931\u8D25: ".concat(task.shift_id), err);
                    });
                    shiftPromises.push(promise);
                  } else if (task.shift_id && _this9.taskShifts[task.shift_id]) {
                    // 已加载过的班次，只更新轮次信息
                    _this9.updateTaskRoundInfo(task);
                  }
                });

                // 等待所有班次加载完成
                if (!(shiftPromises.length > 0)) {
                  _context5.next = 6;
                  break;
                }
                _context5.next = 6;
                return Promise.all(shiftPromises);
              case 6:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    // 更新多个任务的轮次信息
    updateTasksRoundInfo: function updateTasksRoundInfo(tasks) {
      var _this10 = this;
      if (!Array.isArray(tasks)) return;
      var now = new Date(); // 获取当前时间一次，避免多次获取造成轻微差异

      // 遍历每个任务，更新轮次信息
      tasks.forEach(function (task) {
        _this10.updateTaskRoundInfo(task, now);
      });
    },
    // 更新任务轮次信息
    updateTaskRoundInfo: function updateTaskRoundInfo(task, currentTime) {
      if (!task || !task.shift_id || !this.taskShifts[task.shift_id]) return;

      // 使用传入的当前时间或获取新的当前时间
      currentTime = currentTime || new Date();
      var shift = this.taskShifts[task.shift_id];

      // 获取当前轮次信息
      try {
        var roundInfo = this.calculateCurrentRound(task, shift, currentTime);

        // 直接使用当前时间更新timeElapsed，确保数据是最新的
        if (roundInfo.status === 'active') {
          var currentRound = roundInfo.currentRound;
          if (currentRound && currentRound.start_time) {
            var startTime = new Date(currentRound.start_time.replace(/-/g, '/'));
            roundInfo.timeElapsed = currentTime.getTime() - startTime.getTime();
          }
        }

        // 保留之前的状态，方便比较变化
        var prevRoundInfo = this.taskRoundInfos[task._id];

        // 保存当前计算的状态
        this.taskRoundInfos[task._id] = roundInfo;

        // 如果状态发生变化，记录日志
        if (prevRoundInfo && prevRoundInfo.status !== roundInfo.status) {
          console.log("\u4EFB\u52A1[".concat(task._id, "]\u8F6E\u6B21\u72B6\u6001\u4ECE ").concat(prevRoundInfo.status, " \u53D8\u4E3A ").concat(roundInfo.status));
        }

        // 如果任务有rounds_detail，按照状态进行排序
        if (task.rounds_detail && Array.isArray(task.rounds_detail) && task.rounds_detail.length > 0) {
          // 先深拷贝rounds_detail以避免直接修改原始数据
          var rounds = task.rounds_detail.map(function (round) {
            return _objectSpread({}, round);
          });

          // 确保每个轮次都有status字段
          for (var i = 0; i < rounds.length; i++) {
            // 保留已确定的状态，避免不必要的状态变化
            if (rounds[i].status === 2 || rounds[i].status === 3) {
              // 保持已完成或已超时状态
              continue;
            }
            if (rounds[i].status === undefined) {
              // 如果没有status，尝试根据时间计算
              var roundStartTime = new Date(rounds[i].start_time.replace(/-/g, '/'));
              var roundEndTime = new Date(rounds[i].end_time.replace(/-/g, '/'));
              var now = new Date();
              if (now < roundStartTime) {
                rounds[i].status = 0; // 未开始
              } else if (now > roundEndTime) {
                // 检查点位完成情况
                var allPointsCompleted = this.areAllPointsCompleted(task, rounds[i]);
                rounds[i].status = allPointsCompleted ? 2 : 3; // 已完成或已超时
              } else {
                // 检查点位完成情况
                var _allPointsCompleted = this.areAllPointsCompleted(task, rounds[i]);
                rounds[i].status = _allPointsCompleted ? 2 : 1; // 已完成或进行中
              }
            }
          }

          // 分组：未开始和进行中按升序，已完成和超时按降序
          var notStartedOrActive = rounds.filter(function (round) {
            return round.status === 0 || round.status === 1;
          });
          var completedOrExpired = rounds.filter(function (round) {
            return round.status === 2 || round.status === 3;
          });
          notStartedOrActive.sort(function (a, b) {
            return a.round - b.round;
          });
          completedOrExpired.sort(function (a, b) {
            return b.round - a.round;
          });

          // 更新任务的rounds_detail
          task.rounds_detail = [].concat((0, _toConsumableArray2.default)(notStartedOrActive), (0, _toConsumableArray2.default)(completedOrExpired));
        }
      } catch (error) {
        console.error('更新任务轮次信息失败', error);
      }
    },
    // 获取任务关联的班次信息
    getTaskShift: function getTaskShift(task) {
      if (!task || !task.shift_id) return null;
      return this.taskShifts[task.shift_id] || null;
    },
    // 获取任务的轮次信息
    getTaskRoundInfo: function getTaskRoundInfo(task) {
      // 检查缓存
      if (this.taskRoundInfos[task._id]) {
        return this.taskRoundInfos[task._id];
      }
      var currentTime = new Date();
      var roundInfo = {};

      // 使用新数据结构
      if (task.rounds_detail && Array.isArray(task.rounds_detail) && task.rounds_detail.length > 0) {
        roundInfo = this.calculateCurrentRound(task, null, currentTime);
      } else {
        // 获取任务关联的班次信息
        var shift = this.getTaskShift(task);
        if (!shift) {
          return null;
        }

        // 计算当前轮次状态
        roundInfo = this.calculateCurrentRound(task, shift, currentTime);
      }

      // 添加到缓存
      this.taskRoundInfos[task._id] = roundInfo;
      return roundInfo;
    },
    // 判断是否为当前激活任务
    isActiveTask: function isActiveTask(task) {
      return this.activeTaskId === task._id;
    },
    // 计算当前轮次状态
    calculateCurrentRound: function calculateCurrentRound(task, shift) {
      var currentTime = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : new Date();
      // 使用新数据结构
      if (task.rounds_detail && Array.isArray(task.rounds_detail) && task.rounds_detail.length > 0) {
        // 使用排序函数对轮次进行排序
        var sortedRounds = sortRounds(task.rounds_detail);

        // 查找当前时间所在的轮次
        for (var i = 0; i < sortedRounds.length; i++) {
          var round = sortedRounds[i];

          // 获取任务基准日期
          var _taskDate = task.patrol_date || task.create_date || new Date();

          // 使用工具函数正确处理轮次时间，包括时区和day_offset
          var startTime = (0, _date.calculateRoundTime)(_taskDate, round.start_time, round.day_offset || 0);
          // 计算结束时间，使用轮次持续时间或默认60分钟
          var endTime = (0, _date.calculateEndTime)(startTime, round.duration || 60);

          // 轮次为毫秒时间戳
          var startMs = startTime.getTime();
          var endMs = endTime.getTime();
          var _currentMs = currentTime.getTime();

          // 判断当前时间与轮次时间的关系
          if (_currentMs < startMs) {
            // 当前时间早于轮次开始时间，表示正在等待该轮次
            return {
              status: 'waiting',
              nextRound: round,
              nextRoundIndex: i,
              timeUntilNext: startMs - _currentMs,
              isCountdown: true // 标记为倒计时模式
            };
          } else if (_currentMs >= startMs && _currentMs <= endMs) {
            // 当前时间在轮次的时间范围内，表示这个轮次正在进行中
            // 增强：检查点位完成情况
            var allPointsCompleted = this.areAllPointsCompleted(task, round);

            // 如果所有点位已完成，虽然在时间范围内，但状态应该是已完成
            if (allPointsCompleted) {
              return {
                status: 'completed',
                currentRound: round,
                currentRoundIndex: i,
                isEarlyCompletion: true,
                // 提前完成标记
                timeRemaining: endMs - _currentMs,
                isCountdown: false,
                // 标记为非倒计时模式
                totalTime: endMs - startMs // 总时长
              };
            }

            return {
              status: 'active',
              currentRound: round,
              currentRoundIndex: i,
              timeRemaining: endMs - _currentMs,
              timeElapsed: _currentMs - startMs,
              totalTime: endMs - startMs,
              completionPercentage: (_currentMs - startMs) / (endMs - startMs),
              isCountdown: false // 标记为非倒计时模式
            };
          }
        }

        // 如果所有轮次都已经过去，需要检查最后一个轮次是否完成
        var lastRound = sortedRounds[sortedRounds.length - 1];
        var allPointsCompletedInLastRound = this.areAllPointsCompleted(task, lastRound);
        return {
          status: allPointsCompletedInLastRound ? 'completed' : 'expired',
          // 增强：区分完成和超时
          lastRound: lastRound,
          lastRoundIndex: sortedRounds.length - 1,
          isCompleted: allPointsCompletedInLastRound
        };
      }

      // 如果没有新数据结构，回退到使用旧数据结构
      if (!shift || !shift.rounds || shift.rounds.length === 0) {
        return {
          status: 'no_rounds'
        };
      }
      var enabledRounds = shift.rounds;
      var currentMs = currentTime.getTime();

      // 按时间排序
      enabledRounds.sort(function (a, b) {
        var aTimeStr = a.start_time || a.time;
        var bTimeStr = b.start_time || b.time;

        // 转换为天数中的分钟数
        var _aTimeStr$split$map = aTimeStr.split(':').map(Number),
          _aTimeStr$split$map2 = (0, _slicedToArray2.default)(_aTimeStr$split$map, 2),
          aHour = _aTimeStr$split$map2[0],
          aMinute = _aTimeStr$split$map2[1];
        var _bTimeStr$split$map = bTimeStr.split(':').map(Number),
          _bTimeStr$split$map2 = (0, _slicedToArray2.default)(_bTimeStr$split$map, 2),
          bHour = _bTimeStr$split$map2[0],
          bMinute = _bTimeStr$split$map2[1];

        // 按时间排序
        return aHour * 60 + aMinute - (bHour * 60 + bMinute);
      });

      // 获取任务日期（YYYY-MM-DD格式）
      var taskDate = task.patrol_date || task.date;

      // 判断是否是跨天班次
      var isShiftAcrossDay = shift.across_day || shift.end_time < shift.start_time;

      // 遍历轮次
      for (var _i = 0; _i < enabledRounds.length; _i++) {
        var _round = enabledRounds[_i];

        // 获取轮次时间
        var roundTimeStr = _round.start_time || _round.time;
        var roundEndTimeStr = _round.end_time || (_i < enabledRounds.length - 1 ? enabledRounds[_i + 1].start_time || enabledRounds[_i + 1].time : shift.end_time);

        // 使用工具函数正确处理轮次时间
        var _startTime = (0, _date.calculateRoundTime)(taskDate, roundTimeStr);

        // 计算结束时间，使用轮次持续时间或默认60分钟
        var duration = _round.duration || 60;
        var _endTime = (0, _date.calculateEndTime)(_startTime, duration);

        // 处理跨天情况
        if (isShiftAcrossDay) {
          // 获取开始和结束时间的小时数
          var startHour = _startTime.getHours();
          var endHour = _endTime.getHours();

          // 如果结束时间小于开始时间，说明跨天
          if (endHour < startHour || roundEndTimeStr < roundTimeStr) {
            _endTime.setDate(_endTime.getDate() + 1);
          }
        }

        // 轮次为毫秒时间戳
        var _startMs = _startTime.getTime();
        var _endMs = _endTime.getTime();

        // 判断当前时间与轮次时间的关系
        if (currentMs < _startMs) {
          // 当前时间早于轮次开始时间，表示正在等待该轮次
          return {
            status: 'waiting',
            nextRound: _round,
            nextRoundIndex: _i,
            timeUntilNext: _startMs - currentMs,
            isCountdown: true // 标记为倒计时模式
          };
        } else if (currentMs >= _startMs && currentMs <= _endMs) {
          // 当前时间在轮次的时间范围内，表示这个轮次正在进行中
          return {
            status: 'active',
            currentRound: _round,
            currentRoundIndex: _i,
            timeRemaining: _endMs - currentMs,
            timeElapsed: currentMs - _startMs,
            totalTime: _endMs - _startMs,
            completionPercentage: (currentMs - _startMs) / (_endMs - _startMs),
            isCountdown: false // 标记为非倒计时模式
          };
        }
      }

      // 如果所有轮次都已经过去，则表示所有轮次已完成
      return {
        status: 'completed',
        lastRound: enabledRounds[enabledRounds.length - 1],
        lastRoundIndex: enabledRounds.length - 1
      };
    },
    // 添加判断轮次所有点位是否完成的辅助方法
    areAllPointsCompleted: function areAllPointsCompleted(task, round) {
      if (!round || !round.point_stats) return false;

      // 如果point_stats中有完成信息，使用它判断
      if (round.point_stats.total && round.point_stats.checked) {
        return round.point_stats.checked >= round.point_stats.total;
      }

      // 如果没有point_stats或数据不完整，尝试使用round_records判断
      if (task.round_records && Array.isArray(task.round_records)) {
        // 查找对应轮次的记录
        var roundRecord = task.round_records.find(function (r) {
          return r && r.round === round.round;
        });
        if (roundRecord) {
          // 确保completed_points是数组
          var completedPoints = Array.isArray(roundRecord.completed_points) ? roundRecord.completed_points : [];

          // 确保points是数组
          var points = Array.isArray(round.points) ? round.points : [];

          // 如果没有点位，返回false
          if (points.length === 0) return false;

          // 判断是否所有点位都已完成
          return completedPoints.length >= points.length;
        }
      }

      // 如果以上判断都无法执行，返回false
      return false;
    },
    // 构建地图标记点
    buildTaskMarkers: function buildTaskMarkers() {
      var _this11 = this;
      // 保存当前位置的范围圈
      var locationCircle = this.circles.find(function (circle) {
        return circle.strokeColor && (circle.strokeColor === '#34C759' ||
        // 精度极好 - 绿色
        circle.strokeColor === '#00C58E' ||
        // 精度良好 - 青色
        circle.strokeColor === '#FFD60A' ||
        // 精度一般 - 黄色
        circle.strokeColor === '#FF9500' ||
        // 精度较差 - 橙色
        circle.strokeColor === '#FF6B2C' ||
        // 精度很差 - 深橙色
        circle.strokeColor === '#FF3B30' // 精度极差 - 红色
        );
      });

      // 清空当前标记点数组
      this.markers = [];
      this.polylines = [];
      this.circles = []; // 清空范围圈数组

      var task = this.currentTask;
      if (!task) {
        // 如果没有任务，但有位置范围圈，则保留位置范围圈
        if (locationCircle) {
          this.circles = [locationCircle];
        }
        console.warn('没有当前任务，无法构建标记点');
        return;
      }

      // 获取当前任务的轮次信息
      var taskId = task._id;
      var roundInfo = this.taskRoundInfos[taskId];
      if (!roundInfo) {
        console.warn('没有轮次信息，无法构建标记点');
        return;
      }

      // 获取当前轮次
      var currentRound = null;

      // 1. 如果有选中的轮次，优先使用
      if (this.selectedRound && this.selectedRound.taskId === taskId) {
        currentRound = task.rounds_detail.find(function (r) {
          return r.round === _this11.selectedRound.round.round;
        });
      }

      // 2. 如果没有选中的轮次，查找进行中的轮次
      if (!currentRound) {
        currentRound = task.rounds_detail.find(function (r) {
          return r.status === 1;
        });
      }

      // 3. 如果没有进行中的轮次，查找未开始的轮次
      if (!currentRound) {
        currentRound = task.rounds_detail.find(function (r) {
          return r.status === 0;
        });
      }

      // 4. 如果还是没找到，使用最后一个轮次
      if (!currentRound && task.rounds_detail && task.rounds_detail.length > 0) {
        // 任务结束时，直接使用排序后的第一个轮次（最新完成的轮次）
        currentRound = task.rounds_detail[0];
      }
      if (!currentRound) {
        console.warn('没有找到可用的轮次');
        return;
      }

      // 获取当前轮次的点位
      var points = [];
      if (currentRound.points && Array.isArray(currentRound.points)) {
        points = currentRound.points;
      } else if (task.route_detail && task.route_detail.points) {
        points = task.route_detail.points;
      }
      if (points.length === 0) {
        console.warn('没有可用的点位数据');
        return;
      }

      // 确保点位按照顺序排序
      var sortedPoints = (0, _toConsumableArray2.default)(points).sort(function (a, b) {
        if (a.order !== undefined && b.order !== undefined) {
          return a.order - b.order;
        }
        return 0;
      });

      // 获取当前轮次记录
      var roundRecord = (task.round_records || []).find(function (r) {
        return r.round === currentRound.round;
      });
      var completedPointIds = roundRecord ? roundRecord.completed_points || [] : [];

      // 创建标记点和路线
      var markers = [];
      var coordinates = [];

      // 创建所有点位的标记点
      sortedPoints.forEach(function (point, index) {
        if (!point) {
          console.error("\u70B9\u4F4D\u6570\u636E\u4E0D\u5B8C\u6574\uFF0C\u7D22\u5F15: ".concat(index));
          return;
        }

        // 提取点位ID
        var pointId = point.point_id || point.id || point._id;
        if (!pointId) {
          console.error("\u70B9\u4F4D\u6CA1\u6709\u6709\u6548\u7684ID\uFF0C\u7D22\u5F15: ".concat(index), point);
          pointId = "point_".concat(index);
        }

        // 提取经纬度
        var latitude, longitude;
        if (point.latitude && point.longitude) {
          latitude = parseFloat(point.latitude);
          longitude = parseFloat(point.longitude);
        } else if (point.location && point.location.latitude && point.location.longitude) {
          latitude = parseFloat(point.location.latitude);
          longitude = parseFloat(point.location.longitude);
        } else if (point.coordinates && Array.isArray(point.coordinates) && point.coordinates.length >= 2) {
          longitude = parseFloat(point.coordinates[0]);
          latitude = parseFloat(point.coordinates[1]);
        } else if (point.position || point.lnglat) {
          var posObj = point.position || point.lnglat;
          if (posObj.lat || posObj.latitude) {
            latitude = parseFloat(posObj.lat || posObj.latitude);
          }
          if (posObj.lng || posObj.lon || posObj.longitude) {
            longitude = parseFloat(posObj.lng || posObj.lon || posObj.longitude);
          }
        }
        if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) {
          console.error("\u70B9\u4F4D\u6CA1\u6709\u6709\u6548\u7684\u7ECF\u7EAC\u5EA6\u5750\u6807\uFF0C\u70B9\u4F4DID: ".concat(pointId), point);
          return;
        }

        // 检查坐标是否在有效范围内
        if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
          console.error("\u70B9\u4F4D\u7684\u7ECF\u7EAC\u5EA6\u5750\u6807\u8D85\u51FA\u6709\u6548\u8303\u56F4\uFF0C\u70B9\u4F4DID: ".concat(pointId, ", \u5750\u6807: [").concat(latitude, ", ").concat(longitude, "]"));
          return;
        }

        // 简化点位完成状态判断 - 直接使用点位自身属性
        // 不再查询completedPointIds，除非必要
        var isCompleted = false;

        // 修改判断逻辑，确保状态为4的点位不会被标记为已完成
        if (point.status === 4) {
          // 状态为4的点位始终视为未完成，即使有record_id
          isCompleted = false;
        } else {
          // 其他情况下使用常规判断
          // 修复布尔表达式可能返回undefined的问题
          isCompleted = !!(point.status === 1 || point.status === 'completed' || point.checkin_time || point.record_id);
        }

        // 打印调试信息，帮助排查问题
        // console.log(`点位状态检查：${point.name || `点位${index + 1}`}`, {
        //   status: point.status,
        //   hasCheckInTime: !!point.checkin_time,
        //   hasRecordId: !!point.record_id,
        //   isCompleted: isCompleted
        // });

        // 获取点位顺序号
        var pointOrder = point.order || index + 1;

        // 设置点位属性 - 使用不同图标区分完成状态
        // 未打卡使用红色marker.png，已打卡使用map-pin.png
        var iconPath = isCompleted ? '/static/map/map-pin.png' : '/static/map/marker.png';

        // 创建标记点
        var marker = {
          id: index,
          pointId: pointId,
          latitude: latitude,
          longitude: longitude,
          iconPath: iconPath,
          width: 32,
          height: 32,
          title: point.name || "\u70B9\u4F4D".concat(index + 1),
          isCompleted: isCompleted,
          callout: {
            content: (point.name ? "".concat(pointOrder, ". ").concat(point.name) : "".concat(pointOrder, ". \u70B9\u4F4D").concat(index + 1)) + (isCompleted ? ' ✓' : ''),
            color: '#FFFFFF',
            fontSize: 12,
            borderWidth: 0,
            bgColor: isCompleted ? '#4CAF50' : '#3688FF',
            padding: 5,
            display: 'ALWAYS',
            borderRadius: 4,
            textAlign: 'center'
          }
        };
        markers.push(marker);

        // 添加坐标到路线数组
        coordinates.push({
          longitude: longitude,
          latitude: latitude,
          pointId: pointId,
          // 添加点位ID用于关联
          order: index // 使用循环索引作为顺序
        });

        // 添加点位范围圈 - 使用数据库的范围值或默认50米半径
        // 确保从点位数据读取正确的range值
        var pointRange = 50; // 默认值

        // 首先尝试从point对象获取range
        if (point.range && !isNaN(parseFloat(point.range))) {
          pointRange = parseFloat(point.range);
        }
        // 如果没有range属性，尝试从location对象内获取
        else if (point.location && point.location.range && !isNaN(parseFloat(point.location.range))) {
          pointRange = parseFloat(point.location.range);
        }
        // 如果没有range属性，尝试从其他可能的属性获取
        else if (point.radius && !isNaN(parseFloat(point.radius))) {
          pointRange = parseFloat(point.radius);
        }

        // 确保范围值是有效的正数
        pointRange = pointRange > 0 ? pointRange : 50;
        _this11.circles.push({
          latitude: latitude,
          longitude: longitude,
          color: isCompleted ? '#52c41a88' : '#1677FF88',
          // 点位范围圈颜色（带透明度）
          fillColor: isCompleted ? '#52c41a33' : '#1677FF33',
          // 点位范围填充色（更透明）
          radius: pointRange,
          strokeWidth: 2
        });
      });

      // 创建路线 - 如果至少有两个点位
      if (coordinates.length >= 2) {
        // 确保路线坐标按顺序排列
        var routeCoordinates = [].concat(coordinates).sort(function (a, b) {
          return a.order - b.order;
        }).map(function (coord) {
          return {
            latitude: coord.latitude,
            longitude: coord.longitude
          };
        });

        // 创建路线 - 改为实线，不再添加距离计算
        var polylinePoints = routeCoordinates;
        var polyline = {
          points: routeCoordinates,
          color: '#1677FF',
          width: 4,
          // 粗一点的实线
          arrowLine: true,
          dottedLine: false // 确保是实线
        };

        this.polylines = [polyline];
      }

      // 更新标记点 - 不包含距离标签
      this.markers = markers;

      // 设置地图中心点 - 使用第一个标记点或用户位置
      if (markers.length > 0) {
        this.mapCenter = {
          latitude: markers[0].latitude,
          longitude: markers[0].longitude
        };
      } else if (this.userLocation && this.userLocation.latitude && this.userLocation.longitude) {
        this.mapCenter = {
          latitude: this.userLocation.latitude,
          longitude: this.userLocation.longitude
        };
      }

      // 在最后添加位置范围圈
      if (locationCircle) {
        this.circles.push(locationCircle);
      }

      // 确保更新当前位置标记
      this.updateCurrentLocationMarker();
    },
    // 更新当前位置标记
    updateCurrentLocationMarker: function updateCurrentLocationMarker() {
      if (!this.currentLocation || !this.currentLocation.latitude || !this.currentLocation.longitude) {
        return;
      }

      // 获取精度值
      var accuracy = this.currentLocation.accuracy || 0;

      // 根据精度决定圈的颜色
      var circleColor, fillColor;
      if (accuracy <= 5) {
        circleColor = '#34C75988'; // 绿色 - 精度极好
        fillColor = '#34C75933';
      } else if (accuracy <= 10) {
        circleColor = '#00C58E88'; // 青色 - 精度良好
        fillColor = '#00C58E33';
      } else if (accuracy <= 15) {
        circleColor = '#FFD60A88'; // 黄色 - 精度一般
        fillColor = '#FFD60A33';
      } else if (accuracy <= 20) {
        circleColor = '#FF950088'; // 橙色 - 精度较差
        fillColor = '#FF950033';
      } else if (accuracy <= 25) {
        circleColor = '#FF6B2C88'; // 深橙色 - 精度很差
        fillColor = '#FF6B2C33';
      } else {
        circleColor = '#FF3B3088'; // 红色 - 精度极差
        fillColor = '#FF3B3033';
      }

      // 当前位置精度圈
      var accuracyCircle = {
        latitude: this.currentLocation.latitude,
        longitude: this.currentLocation.longitude,
        radius: 3,
        // 固定3米精度圈
        color: circleColor,
        fillColor: fillColor,
        strokeWidth: 2,
        strokeColor: circleColor.slice(0, 7) // 去掉透明度
      };

      // 更新圆形区域，保留任务点位范围圈
      var circles = (0, _toConsumableArray2.default)(this.circles);

      // 查找并更新或添加当前位置精度圈
      var accuracyCircleIndex = circles.findIndex(function (circle) {
        return circle.strokeColor === '#34C759' ||
        // 精度极好 - 绿色
        circle.strokeColor === '#00C58E' ||
        // 精度良好 - 青色
        circle.strokeColor === '#FFD60A' ||
        // 精度一般 - 黄色
        circle.strokeColor === '#FF9500' ||
        // 精度较差 - 橙色
        circle.strokeColor === '#FF6B2C' ||
        // 精度很差 - 深橙色
        circle.strokeColor === '#FF3B30';
      } // 精度极差 - 红色
      );

      if (accuracyCircleIndex >= 0) {
        circles[accuracyCircleIndex] = accuracyCircle;
      } else {
        circles.push(accuracyCircle);
      }
      this.circles = circles;
    },
    // 点击点位标记
    onPointMarkerTap: function onPointMarkerTap(markerId) {
      var _this12 = this;
      console.log('onPointMarkerTap triggered with markerId:', markerId); // 添加这行日志用于调试点击事件
      // 通过markerId找到对应索引
      var markerIndex = parseInt(markerId);

      // 找到对应的点位，通过索引匹配
      if (isNaN(markerIndex) || markerIndex < 0 || !this.currentRoutePoints || markerIndex >= this.currentRoutePoints.length) {
        return;
      }
      var point = this.currentRoutePoints[markerIndex];
      if (!point) return;

      // 检查是否有当前任务
      if (!this.currentTask || !this.currentTask._id) {
        uni.showToast({
          title: '请先选择一个任务',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 获取当前任务的轮次信息
      var roundInfo = this.taskRoundInfos[this.currentTask._id];

      // 打印调试信息，了解roundInfo的实际内容
      console.log('任务轮次信息：', roundInfo);

      // 检查任务是否有活跃或即将开始的轮次
      if (!roundInfo) {
        console.log('没有找到任务轮次信息');
        // 尝试重新计算轮次信息
        var shift = this.getTaskShift(this.currentTask);
        if (shift) {
          var calculatedRoundInfo = this.calculateCurrentRound(this.currentTask, shift, new Date());
          console.log('重新计算的轮次信息：', calculatedRoundInfo);

          // 如果仍然没有有效轮次，提示用户
          if (!calculatedRoundInfo || calculatedRoundInfo.status !== 'active' && calculatedRoundInfo.status !== 'waiting') {
            uni.showToast({
              title: '当前没有可打卡的轮次',
              icon: 'none',
              duration: 2000
            });
            return;
          }

          // 如果有即将开始的轮次也允许打卡
          this.taskRoundInfos[this.currentTask._id] = calculatedRoundInfo;
        } else {
          uni.showToast({
            title: '任务未关联班次信息',
            icon: 'none',
            duration: 2000
          });
          return;
        }
      }

      // 轮次检查：如果有选中轮次则使用选中轮次，否则使用当前活跃轮次或等待的轮次
      var roundToUse = null;

      // 验证当前选中的轮次状态
      if (this.selectedRound && this.selectedRound.round) {
        var roundData = this.currentTask.rounds_detail.find(function (r) {
          return r.round === _this12.selectedRound.round.round;
        });
        if (roundData && (roundData.status === 0 || roundData.status === 2 || roundData.status === 3)) {
          // 轮次未开始、已完成或已超时，显示提示并阻止操作
          var message = '';
          if (roundData.status === 3) {
            message = '所选轮次已超时';
          } else if (roundData.status === 2) {
            message = '所选轮次已完成';
          } else if (roundData.status === 0) {
            message = '所选轮次尚未开始';
          }
          uni.showToast({
            title: message,
            icon: 'none',
            duration: 2000
          });
          return;
        }
        roundToUse = roundData;
      } else {
        // 没有选中轮次，使用当前活跃轮次或等待的轮次
        var activeRound = this.getCurrentActiveRound(this.currentTask);
        if (activeRound) {
          roundToUse = activeRound;
          console.log('使用当前活跃轮次：', activeRound);
        } else {
          console.log('没有找到可用的活跃轮次');
          uni.showToast({
            title: '当前没有可打卡的轮次',
            icon: 'none',
            duration: 2000
          });
          return;
        }
      }

      // 直接跳转到打卡页面
      this.navigateToCheckIn(this.currentTask, point);
    },
    // 获取当前活跃的轮次（状态为1-进行中的轮次）
    getCurrentActiveRound: function getCurrentActiveRound(task) {
      if (!task || !task.rounds_detail || !Array.isArray(task.rounds_detail)) {
        return null;
      }

      // 查找状态为1的轮次（进行中）
      var activeRound = task.rounds_detail.find(function (r) {
        return r.status === 1;
      });

      // 如果没有进行中的轮次，查找状态为0的轮次（未开始）
      if (!activeRound) {
        activeRound = task.rounds_detail.find(function (r) {
          return r.status === 0;
        });
      }
      return activeRound;
    },
    // 天气数据加载完成
    onWeatherLoaded: function onWeatherLoaded(weatherInfo) {
      this.weatherInfo = weatherInfo;
    },
    // 点击任务卡片
    onTaskClick: function onTaskClick(task) {
      // 如果切换到不同的任务，清除轮次选择状态
      if (this.activeTaskId !== task._id) {
        this.selectedRound = null;
      }

      // 设置当前激活的任务ID
      this.activeTaskId = task._id;

      // 加载任务详情
      this.loadTaskDetail(task._id);
    },
    // 点击继续/开始巡检按钮
    onTaskContinue: function onTaskContinue(eventData) {
      // 检查是否有传递完整的事件数据对象（包含task和selectedRound）
      if (eventData && eventData.task) {
        // 保存选中的轮次信息
        if (eventData.selectedRound) {
          this.selectedRound = {
            round: eventData.selectedRound,
            taskId: eventData.task._id
          };
        } else {
          // 如果没有选中轮次，清除之前的选择
          this.selectedRound = null;
        }

        // 加载任务详情并开始巡检
        this.loadTaskDetail(eventData.task._id, true);
      } else {
        // 旧版本的处理方式 - 直接传递任务对象
        var task = eventData;

        // 加载任务详情并开始巡检
        this.loadTaskDetail(task._id, true);
      }
    },
    // 点击查看详情按钮
    onTaskViewDetail: function onTaskViewDetail(task) {
      // 跳转到任务详情页面
      uni.navigateTo({
        url: "/pages/patrol_pkg/task/detail?id=".concat(task._id)
      });
    },
    // 修改加载任务详情方法
    loadTaskDetail: function loadTaskDetail(taskId) {
      var _arguments2 = arguments,
        _this13 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var startPatrol, forceRefresh, res, newTaskData, processedTasks, shiftRes, taskIndex, updatedTaskList;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                startPatrol = _arguments2.length > 1 && _arguments2[1] !== undefined ? _arguments2[1] : false;
                forceRefresh = _arguments2.length > 2 && _arguments2[2] !== undefined ? _arguments2[2] : false;
                if (taskId) {
                  _context6.next = 6;
                  break;
                }
                console.error('任务ID为空');
                _this13.showError('任务ID为空');
                return _context6.abrupt("return");
              case 6:
                _context6.prev = 6;
                // 显示加载中
                _this13.showLoading(true);

                // 使用PatrolApi.call获取任务详情，与任务列表页保持一致
                _context6.next = 10;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'getTaskDetail',
                  data: {
                    task_id: taskId,
                    // 🔥 使用优化后的参数格式
                    level: 'map',
                    // 🔥 启用地图专用模式，保留位置信息，减少约30%数据传输量
                    forceRefresh: forceRefresh ? true : undefined // 添加强制刷新参数
                  }
                });
              case 10:
                res = _context6.sent;
                if (!(res.code === 0 && res.data)) {
                  _context6.next = 37;
                  break;
                }
                // 处理任务详情
                newTaskData = res.data; // 同步处理轮次信息
                if (newTaskData.rounds_detail && newTaskData.rounds_detail.length > 0) {
                  processedTasks = _this13.processTasks([newTaskData]);
                  if (processedTasks.length > 0) {
                    newTaskData = processedTasks[0]; // 使用处理后的数据
                  }
                }

                // 确保任务有round_records字段
                if (!newTaskData.round_records) {
                  newTaskData.round_records = [];
                }

                // 更新轮次信息 (如果需要加载班次，确保 await 完成)
                if (!(newTaskData.shift_id && !_this13.taskShifts[newTaskData.shift_id])) {
                  _context6.next = 28;
                  break;
                }
                _context6.prev = 16;
                _context6.next = 19;
                return _patrolApi.default.call({
                  // 使用 await
                  name: 'patrol-shift',
                  action: 'getShiftDetail',
                  data: {
                    params: {
                      // 确认参数结构
                      shift_id: newTaskData.shift_id
                    }
                  }
                });
              case 19:
                shiftRes = _context6.sent;
                if (shiftRes.code === 0 && shiftRes.data) {
                  _this13.taskShifts[newTaskData.shift_id] = shiftRes.data;
                  _this13.updateTaskRoundInfo(newTaskData); // 使用新数据更新
                }
                _context6.next = 26;
                break;
              case 23:
                _context6.prev = 23;
                _context6.t0 = _context6["catch"](16);
                console.error('加载班次信息失败', _context6.t0);
              case 26:
                _context6.next = 29;
                break;
              case 28:
                _this13.updateTaskRoundInfo(newTaskData); // 使用新数据更新
              case 29:
                // 更新当前任务 - 使用新数据创建新引用
                _this13.currentTask = _objectSpread({}, newTaskData);

                // 更新taskList中相应的任务对象，确保引用变化
                taskIndex = _this13.taskList.findIndex(function (t) {
                  return t._id === taskId;
                });
                if (taskIndex >= 0) {
                  // 创建全新的对象引用以触发Vue的检测
                  updatedTaskList = (0, _toConsumableArray2.default)(_this13.taskList); // 使用深拷贝或扩展运算符替换对象，确保引用变化
                  updatedTaskList[taskIndex] = _objectSpread({}, newTaskData); // 使用扩展运算符创建新对象
                  // 替换整个数组以确保Vue检测到变化
                  _this13.taskList = updatedTaskList;
                  console.log('loadTaskDetail: 已更新taskList中的任务对象:', taskId);
                }

                // 构建标记点 - 确保在数据更新后执行
                _this13.$nextTick(function () {
                  _this13.buildTaskMarkers();

                  // 检查是否要开始巡检
                  if (startPatrol === true) {
                    // 延迟执行startPatrol，确保地图已渲染
                    setTimeout(function () {
                      _this13.startPatrol(_this13.currentTask); // 传递更新后的currentTask
                    }, 300);
                  }
                });

                // 更新激活的任务ID
                _this13.activeTaskId = taskId;
                return _context6.abrupt("return", _this13.currentTask);
              case 37:
                _this13.showError(res.message || '获取任务详情失败');
                return _context6.abrupt("return", null);
              case 39:
                _context6.next = 46;
                break;
              case 41:
                _context6.prev = 41;
                _context6.t1 = _context6["catch"](6);
                console.error('加载任务详情失败', _context6.t1);
                _this13.showError('加载任务详情失败');
                return _context6.abrupt("return", null);
              case 46:
                _context6.prev = 46;
                // 添加延时和安全检查，避免多个hideLoading调用冲突
                setTimeout(function () {
                  _this13.showLoading(false);
                }, 200);
                return _context6.finish(46);
              case 49:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[6, 41, 46, 49], [16, 23]]);
      }))();
    },
    // 开始巡检
    startPatrol: function startPatrol(taskData) {
      var _this14 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var res, _processPatrolTask;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                if (taskData) {
                  _context7.next = 4;
                  break;
                }
                console.error('任务数据为空');
                _this14.showError('任务数据丢失');
                return _context7.abrupt("return");
              case 4:
                _context7.prev = 4;
                // 显示加载中
                _this14.showLoading(true);

                // 先从服务器获取最新的任务数据
                _context7.next = 8;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'getTaskDetail',
                  data: {
                    task_id: taskData._id,
                    // 🔥 使用优化后的参数格式
                    level: 'map',
                    // 🔥 启用地图专用模式
                    forceRefresh: true // 强制刷新
                  }
                });
              case 8:
                res = _context7.sent;
                // 创建处理任务的函数
                _processPatrolTask = function _processPatrolTask(task) {
                  if (!task || !task.rounds_detail || !Array.isArray(task.rounds_detail)) {
                    return;
                  }
                  var now = new Date();

                  // 更新所有轮次的状态
                  task.rounds_detail.forEach(function (round) {
                    // 只有当轮次状态不是已完成(2)和超时(3)时才重新计算状态
                    if (round.status !== 2 && round.status !== 3) {
                      round.status = _this14.getRoundStatus(round, now);
                    }
                  });

                  // 获取当前选中的轮次或进行中的轮次
                  var currentRound = null;

                  // 检查是否所有轮次都已结束（完成或超时）
                  var allRoundsEnded = task.rounds_detail.every(function (round) {
                    return round.status === 2 || round.status === 3;
                  });
                  if (allRoundsEnded) {
                    // 如果所有轮次都已结束，直接使用最后一轮
                    currentRound = task.rounds_detail[task.rounds_detail.length - 1];
                    console.log('所有轮次已结束，使用最后一轮:', currentRound.round);
                  } else {
                    // 1. 如果有选中的轮次，验证其状态
                    if (_this14.selectedRound && _this14.selectedRound.taskId === task._id) {
                      var selectedRound = task.rounds_detail.find(function (r) {
                        return r.round === _this14.selectedRound.round.round;
                      });
                      // 只有当轮次状态为进行中(1)或未开始(0)时才使用
                      if (selectedRound && (selectedRound.status === 1 || selectedRound.status === 0)) {
                        currentRound = selectedRound;
                      }
                    }

                    // 2. 如果没有有效的选中轮次，查找进行中的轮次
                    if (!currentRound) {
                      var activeRounds = task.rounds_detail.filter(function (round) {
                        return round.status === 1;
                      });
                      if (activeRounds.length > 0) {
                        // 如果有多个进行中的轮次，选择时间范围最长的
                        currentRound = activeRounds.reduce(function (prev, curr) {
                          var prevDuration = new Date(prev.end_time) - new Date(prev.start_time);
                          var currDuration = new Date(curr.end_time) - new Date(curr.start_time);
                          return currDuration > prevDuration ? curr : prev;
                        });
                      }
                    }

                    // 3. 如果没有进行中的轮次，查找未开始的轮次
                    if (!currentRound) {
                      var notStartedRounds = task.rounds_detail.filter(function (round) {
                        return round.status === 0;
                      });
                      if (notStartedRounds.length > 0) {
                        // 如果有多个未开始的轮次，选择时间范围最长的
                        currentRound = notStartedRounds.reduce(function (prev, curr) {
                          var prevDuration = new Date(prev.end_time) - new Date(prev.start_time);
                          var currDuration = new Date(curr.end_time) - new Date(curr.start_time);
                          return currDuration > prevDuration ? curr : prev;
                        });
                      }
                    }
                  }

                  // 如果找到了轮次，继续处理
                  if (currentRound) {
                    console.log('处理巡视任务，当前轮次:', currentRound.round, '状态:', currentRound.status);

                    // 获取当前轮次的点位
                    var taskPoints = [];
                    if (currentRound.points && Array.isArray(currentRound.points)) {
                      console.log('从当前轮次获取点位信息:', currentRound.round);
                      taskPoints = currentRound.points;
                    } else if (task.route_detail && task.route_detail.points) {
                      console.log('从route_detail获取点位信息');
                      taskPoints = task.route_detail.points;
                    }

                    // 检查是否有可用的巡视点位
                    if (taskPoints.length === 0) {
                      console.error('未找到有效的点位信息');
                      _this14.showError('任务没有巡视点位');
                      return;
                    }

                    // 按照顺序对点位进行排序
                    var sortedPoints = (0, _toConsumableArray2.default)(taskPoints).sort(function (a, b) {
                      var orderA = a.order !== undefined ? a.order : 0;
                      var orderB = b.order !== undefined ? b.order : 0;
                      return orderA - orderB;
                    });
                    console.log('排序后的点位:', sortedPoints);

                    // 如果所有轮次已结束，不再查找未完成的点位
                    if (!allRoundsEnded) {
                      // 🔥 修复：查找第一个未完成的点位（按顺序）
                      var incompletePoint = sortedPoints.find(function (point) {
                        // 修复状态判断逻辑，与buildTaskMarkers保持一致
                        if (point.status === 4) {
                          return true; // 状态为4的点位视为未完成
                        }
                        // 优先使用status字段判断
                        return !(point.status === 1 || point.checkin_time || point.record_id);
                      });
                      if (incompletePoint) {
                        console.log("\uD83C\uDFAF \u7EE7\u7EED\u5DE1\u89C6: ".concat(incompletePoint.name || '未命名点位', " (order: ").concat(incompletePoint.order, ")"));
                        // 跳转到打卡页面
                        _this14.navigateToCheckIn(task, incompletePoint);
                        return;
                      }
                      console.log('✅ 所有点位已完成');
                    }

                    // 更新地图上的点位显示
                    _this14.buildTaskMarkers(task, currentRound, sortedPoints);
                  }
                };
                if (res.code === 0 && res.data) {
                  console.log('获取最新任务数据成功');
                  // 使用最新的任务数据继续处理
                  _processPatrolTask(res.data);
                } else {
                  console.warn('获取最新任务数据失败，使用缓存数据继续');
                  // 使用当前缓存的任务数据继续处理
                  _processPatrolTask(taskData);
                }
                _context7.next = 17;
                break;
              case 13:
                _context7.prev = 13;
                _context7.t0 = _context7["catch"](4);
                console.error('获取最新任务数据出错:', _context7.t0);
                // 出错时使用缓存数据
                processPatrolTask(taskData);
              case 17:
                _context7.prev = 17;
                _this14.showLoading(false);
                return _context7.finish(17);
              case 20:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[4, 13, 17, 20]]);
      }))();
    },
    // 跳转到打卡页面
    navigateToCheckIn: function navigateToCheckIn(task, point) {
      var _this15 = this;
      if (!task) {
        console.error('任务数据为空');
        this.showError('任务数据丢失');
        return;
      }
      if (!point) {
        console.error('点位数据为空');
        this.showError('点位数据丢失');
        return;
      }

      // 检查任务状态，允许已完成任务继续打卡
      if (task.status === 4) {
        // 只有已取消的任务(status=4)不允许打卡
        uni.showModal({
          title: '提示',
          content: '该任务已取消，无法打卡',
          showCancel: false
        });
        return;
      }

      // 获取点位ID
      var pointId = point.point_id || point._id;
      if (!pointId) {
        console.error('点位没有有效的ID');
        this.showError('点位数据异常');
        return;
      }

      // 获取所有可用的轮次（按顺序：进行中 -> 未开始 -> 已完成）
      var availableRounds = task.rounds_detail.filter(function (round) {
        return round.status !== 3 && round.status !== 4;
      }) // 排除超时和取消的轮次
      .sort(function (a, b) {
        // 优先级：进行中 > 未开始 > 已完成
        var getWeight = function getWeight(status) {
          if (status === 1) return 0; // 进行中
          if (status === 0) return 1; // 未开始
          if (status === 2) return 2; // 已完成
          return 3; // 其他状态
        };

        return getWeight(a.status) - getWeight(b.status);
      });

      // 如果有选中的轮次，验证其状态
      var targetRound = null;
      if (this.selectedRound && this.selectedRound.taskId === task._id) {
        var selectedRound = task.rounds_detail.find(function (r) {
          return r.round === _this15.selectedRound.round.round;
        });
        if (selectedRound && (selectedRound.status === 1 || selectedRound.status === 0)) {
          targetRound = selectedRound;
        }
      }

      // 如果没有选中的轮次或选中的轮次无效，查找下一个可用的轮次
      if (!targetRound) {
        // 找到第一个未完成的轮次
        targetRound = availableRounds.find(function (round) {
          var _task$round_records;
          // 检查该轮次中的点位是否已完成
          var roundRecord = (_task$round_records = task.round_records) === null || _task$round_records === void 0 ? void 0 : _task$round_records.find(function (record) {
            return record.round === round.round;
          });
          var completedPoints = (roundRecord === null || roundRecord === void 0 ? void 0 : roundRecord.completed_points) || [];
          return !completedPoints.includes(pointId);
        });
      }

      // 如果没有找到可用轮次，说明所有轮次都已完成该点位
      if (!targetRound) {
        uni.showToast({
          icon: 'none',
          title: '当前没有可打卡的轮次',
          duration: 2000
        });
        return;
      }

      // 构建跳转URL，传递当前位置信息作为初始值
      var url = "/pages/patrol_pkg/checkin/index?task_id=".concat(task._id, "&point_id=").concat(pointId, "&round=").concat(targetRound.round);

      // 如果有当前位置信息，传递给打卡页面作为初始定位
      if (this.currentLocation && this.currentLocation.latitude && this.currentLocation.longitude) {
        url += "&lat=".concat(this.currentLocation.latitude, "&lng=").concat(this.currentLocation.longitude, "&accuracy=").concat(this.currentLocation.accuracy || 0);
      }

      // 跳转到打卡页面
      uni.navigateTo({
        url: url
      });
    },
    // 显示加载中
    showLoading: function showLoading() {
      var show = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
      this.isLoading = show;
      if (show) {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
      } else {
        // 添加延时和检查，确保loading存在再隐藏
        setTimeout(function () {
          try {
            // 检查当前是否有loading显示中
            uni.hideLoading();
          } catch (e) {}
        }, 150); // 延迟150ms执行，给loading显示和关闭之间留出缓冲时间
      }
    },
    // 显示错误消息
    showError: function showError(message) {
      uni.showToast({
        icon: 'none',
        title: message || '操作失败',
        duration: 2000
      });
    },
    // 轮次状态更新定时器
    startRoundUpdateTimer: function startRoundUpdateTimer() {
      var _this16 = this;
      // 先清除之前的定时器
      this.clearRoundUpdateTimer();

      // 创建新的定时器，定期更新轮次状态
      this.roundUpdateTimer = setInterval(function () {
        _this16.updateAllTaskRounds();
      }, this.roundUpdateInterval);
    },
    // 清除轮次更新定时器
    clearRoundUpdateTimer: function clearRoundUpdateTimer() {
      if (this.roundUpdateTimer) {
        clearInterval(this.roundUpdateTimer);
        this.roundUpdateTimer = null;
      }
    },
    // 更新所有任务的轮次状态
    updateAllTaskRounds: function updateAllTaskRounds() {
      var _this17 = this;
      if (!this.taskList || this.taskList.length === 0 || this.isInitialLoading) return;
      var currentTime = new Date();
      var needFullRefresh = false;
      this.taskList.forEach(function (task) {
        if (!task.shift_id || !_this17.taskShifts[task.shift_id]) return;

        // 获取当前任务的轮次信息
        var oldRoundInfo = _this17.taskRoundInfos[task._id];
        var oldStatus = oldRoundInfo ? oldRoundInfo.status : null;

        // 计算最新的轮次状态
        var newRoundInfo = _this17.calculateCurrentRound(task, _this17.taskShifts[task.shift_id], currentTime);

        // 检查轮次状态变化边界情况 - 特别是倒计时接近0的情况
        if (oldRoundInfo && oldRoundInfo.status === 'waiting' && oldRoundInfo.timeUntilNext && oldRoundInfo.timeUntilNext < 60000) {
          // 当等待时间少于1分钟时，更频繁地检查状态
          console.log('轮次即将开始，更频繁检查');
          // 立即强制重新计算状态
          setTimeout(function () {
            _this17.updateTaskRoundInfo(task);
            _this17.$forceUpdate();
          }, 2000); // 2秒后重新检查
        }

        _this17.taskRoundInfos[task._id] = newRoundInfo;

        // 状态变化增强监测：特别关注从waiting到active的变化
        if (oldStatus && newRoundInfo.status !== oldStatus) {
          // 从等待到当前轮次，重要的状态变化，需要立即刷新UI
          if (oldStatus === 'waiting' && newRoundInfo.status === 'active') {
            console.log('重要状态变化: 从waiting到active');
            // 立即强制刷新页面UI
            _this17.$forceUpdate();
            // 如果当前选中的是这个任务，立即更新地图等信息
            if (_this17.currentTask && _this17.currentTask._id === task._id) {
              _this17.buildTaskMarkers();
            }
            needFullRefresh = true;
          }

          // 其他重要状态变化
          if (oldStatus === 'active' && newRoundInfo.status === 'completed' || oldStatus === 'active' && newRoundInfo.status === 'between_rounds' || oldStatus === 'between_rounds' && newRoundInfo.status === 'active') {
            needFullRefresh = true;

            // 显示轮次变化提示
            _this17.showRoundChangeNotification(task, oldRoundInfo, newRoundInfo);
          }
        }
      });

      // 如果有关键轮次状态变化，执行完整刷新
      if (needFullRefresh) {
        // 重要状态变化时先设置缓冲标记
        this.isInitialLoading = true;
        this.refreshTaskList(false);

        // 延迟清除缓冲标记
        setTimeout(function () {
          _this17.isInitialLoading = false;
        }, 800);
      } else {
        // 触发页面更新
        this.$forceUpdate();
      }
    },
    // 显示轮次变化通知
    showRoundChangeNotification: function showRoundChangeNotification(task, oldRound, newRound) {
      var _this18 = this;
      var title = '';
      var content = '';
      if (oldRound.status === 'waiting' && newRound.status === 'active') {
        var _newRound$currentRoun;
        // 从等待到当前轮次激活
        title = '新轮次开始';
        content = "\u4EFB\u52A1\"".concat(task.route_name, "\"\u7684\u7B2C").concat(((_newRound$currentRoun = newRound.currentRound) === null || _newRound$currentRoun === void 0 ? void 0 : _newRound$currentRoun.round) || '?', "\u8F6E\u5DE1\u89C6\u5DF2\u7ECF\u5F00\u59CB");
      } else if (oldRound.status === 'active' && newRound.status === 'between_rounds') {
        var _oldRound$currentRoun;
        // 从当前轮次到轮次间隔
        title = '当前轮次结束';
        content = "\u4EFB\u52A1\"".concat(task.route_name, "\"\u7684\u7B2C").concat(((_oldRound$currentRoun = oldRound.currentRound) === null || _oldRound$currentRoun === void 0 ? void 0 : _oldRound$currentRoun.round) || '?', "\u8F6E\u5DE1\u89C6\u5DF2\u7ECF\u7ED3\u675F\uFF0C\u8BF7\u7B49\u5F85\u4E0B\u4E00\u8F6E\u5F00\u59CB");
      } else if (oldRound.status === 'between_rounds' && newRound.status === 'active') {
        var _newRound$currentRoun2;
        // 从轮次间隔到新轮次激活
        title = '新轮次开始';
        content = "\u4EFB\u52A1\"".concat(task.route_name, "\"\u7684\u7B2C").concat(((_newRound$currentRoun2 = newRound.currentRound) === null || _newRound$currentRoun2 === void 0 ? void 0 : _newRound$currentRoun2.round) || '?', "\u8F6E\u5DE1\u89C6\u5DF2\u7ECF\u5F00\u59CB");
      } else if (oldRound.status === 'active' && newRound.status === 'completed') {
        // 从当前轮次到全部完成
        title = '所有轮次完成';
        content = "\u4EFB\u52A1\"".concat(task.route_name, "\"\u7684\u6240\u6709\u5DE1\u89C6\u8F6E\u6B21\u5DF2\u7ECF\u7ED3\u675F");
      }

      // 显示通知
      if (title && content) {
        uni.showModal({
          title: title,
          content: content,
          showCancel: false,
          success: function success() {
            // 用户确认后，如果是新轮次开始，自动选中该任务
            if (oldRound.status === 'waiting' && newRound.status === 'active' || oldRound.status === 'between_rounds' && newRound.status === 'active') {
              _this18.onTaskClick(task);
            }
          }
        });
      }
    },
    // 计算两点间距离（米）
    calculateDistance: function calculateDistance(lat1, lon1, lat2, lon2) {
      var R = 6371000; // 地球半径，单位米
      var dLat = this.deg2rad(lat2 - lat1);
      var dLon = this.deg2rad(lon2 - lon1);
      var a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
      var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      var distance = R * c;
      return distance; // 返回米为单位的距离
    },
    // 角度转弧度
    deg2rad: function deg2rad(deg) {
      return deg * (Math.PI / 180);
    },
    // 检查用户登录状态
    checkLoginState: function checkLoginState() {
      try {
        // 如果已经显示了登录提示，不再重复显示
        if (this.isShowingLoginTip) {
          return false;
        }
        var userInfo = uni.getStorageSync('uni-id-pages-userInfo');
        if (!userInfo || !userInfo._id) {
          // 显示登录提示
          this.showLoginTip();
          return false;
        }
        return true;
      } catch (e) {
        console.error('检查登录状态出错', e);
        if (!this.isShowingLoginTip) {
          this.showLoginTip();
        }
        return false;
      }
    },
    // 显示登录提示
    showLoginTip: function showLoginTip() {
      var _this19 = this;
      // 设置标志，防止重复显示
      this.isShowingLoginTip = true;

      // 设置页面为空状态
      this.isEmpty = true;
      this.taskList = [];

      // 显示提示框
      uni.showModal({
        title: '需要登录',
        content: '巡视打卡功能需要登录后才能使用',
        confirmText: '去登录',
        cancelText: '返回首页',
        success: function success(res) {
          if (res.confirm) {
            // 根据平台跳转到不同的登录页

            uni.navigateTo({
              url: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd'
            });
          } else {
            // 返回首页
            uni.switchTab({
              url: '/pages/index/index'
            });
          }
          // 清除标志
          setTimeout(function () {
            _this19.isShowingLoginTip = false;
          }, 1000);
        }
      });
    },
    // 添加加载用户数据的方法
    loadUsers: function loadUsers() {
      var _this20 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var userInfo, userId, result, users;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                _context8.prev = 0;
                // 获取当前用户ID
                userInfo = uni.getStorageSync('uni-id-pages-userInfo');
                userId = userInfo ? typeof userInfo === 'string' ? JSON.parse(userInfo)._id : userInfo._id : '';
                if (userId) {
                  _context8.next = 6;
                  break;
                }
                console.error('未获取到用户ID，可能未登录');
                return _context8.abrupt("return");
              case 6:
                _context8.next = 8;
                return _patrolApi.default.call({
                  name: 'patrol-user',
                  action: 'getUsers',
                  data: {
                    userid: userId,
                    pageSize: 100
                  }
                });
              case 8:
                result = _context8.sent;
                if (!(result.code === 0)) {
                  _context8.next = 16;
                  break;
                }
                users = result.data.list || []; // 清空之前的用户映射
                _this20.userMap = {};
                users.forEach(function (user) {
                  // 确保用户对象有name属性
                  var processedUser = _objectSpread(_objectSpread({}, user), {}, {
                    // 按优先级选择用户显示名称
                    name: user.real_name || user.nickname || user.username || '未命名用户'
                  });
                  _this20.userMap[user._id] = processedUser;
                });
                return _context8.abrupt("return", users);
              case 16:
                console.error('加载用户数据失败:', result.message);
              case 17:
                _context8.next = 22;
                break;
              case 19:
                _context8.prev = 19;
                _context8.t0 = _context8["catch"](0);
                console.error('加载用户错误:', _context8.t0);
              case 22:
                return _context8.abrupt("return", []);
              case 23:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[0, 19]]);
      }))();
    },
    // 添加获取用户名称的方法
    getUserName: function getUserName(userId) {
      if (userId && this.userMap[userId]) {
        return this.userMap[userId].name || '未知用户';
      }
      return '未分配';
    },
    // 添加格式化时间区间的方法
    formatTimeRange: function formatTimeRange(task, round) {
      if (!round) return '--:-- - --:--';
      try {
        // 处理不同格式的时间字符串
        var startTime = round.start_time;
        var endTime = round.end_time;

        // 如果没有明确的开始或结束时间，返回占位符
        if (!startTime || !endTime) return '--:-- - --:--';

        // 使用formatDate工具函数格式化时间
        var formatTimeOnly = function formatTimeOnly(dateStr) {
          if (!dateStr) return '--:--';
          try {
            // 使用replace确保跨平台兼容性
            var d = new Date(dateStr.replace(/-/g, '/'));
            if (isNaN(d.getTime())) return '--:--';

            // 使用formatDate格式化时间
            return (0, _date.formatDate)(d, 'HH:mm');
          } catch (e) {
            console.error('时间格式化错误:', e);
            return '--:--';
          }
        };
        return "".concat(formatTimeOnly(startTime), " - ").concat(formatTimeOnly(endTime));
      } catch (e) {
        console.error('格式化时间区间出错:', e);
        return '--:-- - --:--';
      }
    },
    // 格式化倒计时
    formatCountdown: function formatCountdown(milliseconds) {
      if (!milliseconds || milliseconds <= 0) return '00:00';
      try {
        // 转换毫秒为小时、分钟和秒
        var totalSeconds = Math.floor(milliseconds / 1000);
        var hours = Math.floor(totalSeconds / 3600);
        var minutes = Math.floor(totalSeconds % 3600 / 60);
        var seconds = totalSeconds % 60;

        // 格式化显示，使用padStart确保两位数显示
        if (hours > 0) {
          return "".concat(String(hours).padStart(2, '0'), ":").concat(String(minutes).padStart(2, '0'), ":").concat(String(seconds).padStart(2, '0'));
        } else {
          return "".concat(String(minutes).padStart(2, '0'), ":").concat(String(seconds).padStart(2, '0'));
        }
      } catch (e) {
        console.error('格式化倒计时出错:', e);
        return '00:00';
      }
    },
    // 添加格式化有效时长方法
    formatValidTime: function formatValidTime(milliseconds, totalTime) {
      if (!milliseconds || milliseconds <= 0) return '00:00';
      try {
        // 如果是倒计时（等待开始），使用原有的倒计时格式
        if (milliseconds < 0) {
          return this.formatCountdown(Math.abs(milliseconds));
        }

        // 如果是已开始的有效时长，使用新格式
        var totalSeconds = Math.floor(milliseconds / 1000);
        var hours = Math.floor(totalSeconds / 3600);
        var minutes = Math.floor(totalSeconds % 3600 / 60);

        // 格式化总时长（仅显示小时和分钟）
        var totalTimeStr = '';
        if (totalTime) {
          var totalSec = Math.floor(totalTime / 1000);
          var totalHours = Math.floor(totalSec / 3600);
          var totalMinutes = Math.floor(totalSec % 3600 / 60);
          if (totalHours > 0) {
            totalTimeStr = "".concat(totalHours, "\u5C0F\u65F6").concat(totalMinutes, "\u5206\u949F");
          } else {
            totalTimeStr = "".concat(totalMinutes, "\u5206\u949F");
          }
        }

        // 返回格式化的有效时长
        if (hours > 0) {
          return "\u6709\u6548\u65F6\u957F: ".concat(totalTimeStr || "".concat(hours, "\u5C0F\u65F6").concat(minutes, "\u5206\u949F"));
        } else {
          return "\u6709\u6548\u65F6\u957F: ".concat(totalTimeStr || "".concat(minutes, "\u5206\u949F"));
        }
      } catch (e) {
        console.error('格式化有效时长出错:', e);
        return '有效时长: 0分钟';
      }
    },
    // 判断时间是否紧急（小于10分钟）
    isTimeUrgent: function isTimeUrgent(milliseconds) {
      if (!milliseconds) return false;
      return milliseconds < 10 * 60 * 1000; // 小于10分钟
    },
    // 添加判断任务是否为今天的方法
    isTaskToday: function isTaskToday(task) {
      if (!task || !task.patrol_date) return false;
      try {
        // 使用isToday工具函数判断任务日期是否为今天
        return (0, _date.isToday)(task.patrol_date);
      } catch (e) {
        console.error('判断任务日期出错:', e);
        return false;
      }
    },
    // 刷新任务列表
    refreshTaskList: function refreshTaskList() {
      var _arguments3 = arguments,
        _this21 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var showLoading, now, timeSinceLastLoad, savedActiveTaskId, savedMapCenter, taskExists;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                showLoading = _arguments3.length > 0 && _arguments3[0] !== undefined ? _arguments3[0] : true;
                // 🔥 防抖机制：避免短时间内重复请求
                now = Date.now();
                timeSinceLastLoad = now - _this21.lastLoadTime; // 如果正在加载中，跳过重复请求
                if (!_this21.loadingInProgress) {
                  _context9.next = 6;
                  break;
                }
                console.log('任务加载中，跳过重复请求');
                return _context9.abrupt("return");
              case 6:
                if (!(timeSinceLastLoad < 3000)) {
                  _context9.next = 9;
                  break;
                }
                console.log('防抖阻止重复加载，距离上次加载', timeSinceLastLoad, 'ms');
                return _context9.abrupt("return");
              case 9:
                // 🔥 设置加载状态和时间戳
                _this21.loadingInProgress = true;
                _this21.lastLoadTime = now;
                if (showLoading) {
                  uni.showToast({
                    title: '正在刷新任务...',
                    icon: 'loading',
                    mask: true,
                    duration: 2000
                  });
                }

                // 保存当前激活的任务ID和地图中心点
                savedActiveTaskId = _this21.activeTaskId;
                savedMapCenter = _objectSpread({}, _this21.mapCenter); // 设置初始加载标记
                _this21.isInitialLoading = true;
                _context9.prev = 15;
                _context9.next = 18;
                return _this21.loadTaskList(false);
              case 18:
                // 使用静默加载
                // 检查之前选中的任务是否仍然存在于新列表中
                taskExists = _this21.taskList.some(function (task) {
                  return task._id === savedActiveTaskId;
                });
                if (!(savedActiveTaskId && taskExists)) {
                  _context9.next = 24;
                  break;
                }
                _context9.next = 22;
                return _this21.loadTaskDetail(savedActiveTaskId, false, true);
              case 22:
                _context9.next = 31;
                break;
              case 24:
                if (!(_this21.taskList.length > 0 && !_this21.activeTaskId)) {
                  _context9.next = 30;
                  break;
                }
                // 如果之前没有选中任务，或者选中的任务消失了，自动选中第一个
                _this21.activeTaskId = _this21.taskList[0]._id;
                _context9.next = 28;
                return _this21.loadTaskDetail(_this21.activeTaskId);
              case 28:
                _context9.next = 31;
                break;
              case 30:
                if (!taskExists) {
                  // 如果选中的任务消失了，清空状态
                  _this21.activeTaskId = '';
                  _this21.currentTask = null;
                  _this21.markers = [];
                  _this21.polylines = [];
                  _this21.circles = [];
                }
              case 31:
                _context9.next = 37;
                break;
              case 33:
                _context9.prev = 33;
                _context9.t0 = _context9["catch"](15);
                console.error('刷新任务列表过程中出错:', _context9.t0);
                _this21.showError('刷新任务失败');
              case 37:
                _context9.prev = 37;
                // 🔥 确保清除加载状态
                _this21.loadingInProgress = false;

                // 清除初始加载标记，并给予一定延迟
                setTimeout(function () {
                  _this21.isInitialLoading = false;
                }, 500); // 稍长延迟确保渲染稳定

                if (showLoading) {
                  // 确保隐藏加载提示
                  setTimeout(function () {
                    uni.hideLoading();
                  }, 150);
                }
                return _context9.finish(37);
              case 42:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[15, 33, 37, 42]]);
      }))();
    },
    // 处理任务更新事件 - 优化处理逻辑
    handleTaskUpdated: function handleTaskUpdated(data) {
      var _this22 = this;
      console.log('收到任务更新事件:', data);
      var task_id = data.task_id,
        point_id = data.point_id,
        round = data.round;

      // 如果收到了轮次信息，先更新当前任务的轮次
      if (round && this.currentTask && this.currentTask._id === task_id) {
        console.log('检查轮次更新:', round);
        // 确保rounds_detail存在
        if (this.currentTask.rounds_detail) {
          var existingRound = this.currentTask.rounds_detail.find(function (r) {
            return r.round === round;
          });
          if (!existingRound) {
            console.log('发现新轮次，更新轮次信息:', round);
            // 更新选中的轮次
            this.selectedRound = {
              taskId: task_id,
              round: {
                round: round,
                status: 1
              }
            };
          }
        }
      }

      // 标记需要刷新，并延迟执行刷新，避免过于频繁的操作
      if (!this.refreshTimeout) {
        this.refreshTimeout = setTimeout( /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
          return _regenerator.default.wrap(function _callee10$(_context10) {
            while (1) {
              switch (_context10.prev = _context10.next) {
                case 0:
                  console.log('执行延迟刷新...');
                  _context10.prev = 1;
                  if (!(_this22.activeTaskId === task_id)) {
                    _context10.next = 7;
                    break;
                  }
                  _context10.next = 5;
                  return _this22.loadTaskDetail(task_id, false, true);
                case 5:
                  _context10.next = 9;
                  break;
                case 7:
                  _context10.next = 9;
                  return _this22.refreshTaskList(false);
                case 9:
                  _context10.next = 14;
                  break;
                case 11:
                  _context10.prev = 11;
                  _context10.t0 = _context10["catch"](1);
                  console.error('刷新任务数据失败:', _context10.t0);
                case 14:
                  _context10.prev = 14;
                  _this22.refreshTimeout = null;
                  return _context10.finish(14);
                case 17:
                case "end":
                  return _context10.stop();
              }
            }
          }, _callee10, null, [[1, 11, 14, 17]]);
        })), 1500); // 延长间隔时间，减少刷新频率
      }
    },
    // 处理任务列表数据 - 从任务列表页复制的方法
    processTasks: function processTasks(list) {
      var _this23 = this;
      if (!list || !Array.isArray(list)) return [];
      var now = new Date(); // 当前时间，用于判断轮次状态

      // 处理任务数据
      var processedTasks = list.map(function (task) {
        // 基础任务数据处理
        var processedTask = _objectSpread(_objectSpread({}, task), {}, {
          points: task.points || [],
          completed_points: task.completed_points || [],
          status: parseInt(task.status || 0),
          area: task.area || task.route_name || '未指定区域',
          patrol_date: task.patrol_date || _this23.formatDateForCompare(new Date()),
          shift_id: task.shift_id || '',
          name: task.name || task.route_name || '未命名任务'
        });

        // 保存服务器返回的原始状态
        var originalStatus = processedTask.status;

        // 初始加载期间或对于已有明确状态的任务，保持服务器状态不变
        if (_this23.isInitialLoading || originalStatus === 2 || originalStatus === 4) {
          return processedTask;
        }

        // 如果任务已取消或已完成，直接返回
        if (originalStatus === 4 || originalStatus === 2) {
          return processedTask;
        }

        // 处理轮次详情，确保状态正确
        if (processedTask.rounds_detail && processedTask.rounds_detail.length > 0) {
          var allRoundsCompleted = true;
          var hasActiveRound = false;
          var hasUpcomingRound = false;
          var allRoundsExpired = true;

          // 遍历所有轮次，检查状态
          processedTask.rounds_detail.forEach(function (round) {
            if (!round) return;

            // 确保轮次中有day_offset和duration字段
            round.day_offset = round.day_offset !== undefined ? Number(round.day_offset) : 0;
            round.duration = round.duration !== undefined ? Number(round.duration) : 60;
            try {
              // 解析轮次时间
              var roundStartTime = new Date(round.start_time);
              var roundEndTime = new Date(round.end_time);

              // 检查点位完成情况
              var isAllPointsCompleted = round.stats && round.stats.total_points > 0 && round.stats.completed_points >= round.stats.total_points;

              // 增加状态稳定性，保留已有的完成和超时状态
              if (round.status === 2 || round.status === 3) {
                // 保持已完成或已超时状态
                if (round.status === 2) allRoundsExpired = false;
                if (round.status === 3) allRoundsCompleted = false;
              } else {
                // 确定轮次状态
                if (isAllPointsCompleted) {
                  round.status = 2; // 已完成
                  allRoundsExpired = false;
                } else if (now < roundStartTime) {
                  round.status = 0; // 未开始
                  hasUpcomingRound = true;
                  allRoundsCompleted = false;
                  allRoundsExpired = false;
                } else if (now > roundEndTime) {
                  round.status = 3; // 已超时
                  allRoundsCompleted = false;
                } else {
                  round.status = 1; // 进行中
                  hasActiveRound = true;
                  allRoundsCompleted = false;
                  allRoundsExpired = false;
                }
              }

              // 记录状态变化日志，帮助诊断问题
              if (originalStatus !== processedTask.status) {
                console.log("\u8F6E\u6B21[".concat(round.round, "]\u72B6\u6001: ").concat(round.status));
              }
            } catch (error) {
              console.error("\u89E3\u6790\u8F6E\u6B21[".concat(round.round, "]\u65F6\u95F4\u51FA\u9519:"), error);
            }
          });

          // 记录原来的状态
          var prevStatus = processedTask.status;

          // 根据轮次状态确定任务状态
          if (allRoundsCompleted || processedTask.overall_stats && processedTask.overall_stats.completion_rate === 1) {
            // 如果所有轮次已完成或总体完成率为100%，任务状态为已完成
            processedTask.status = 2;
          } else if (hasActiveRound) {
            // 如果有进行中的轮次，任务状态为进行中
            processedTask.status = 1;
          } else if (hasUpcomingRound) {
            // 如果有未开始的轮次，任务状态为未开始
            processedTask.status = 0;
          } else if (allRoundsExpired) {
            // 如果所有轮次都已超时，任务状态为已超时
            processedTask.status = 3;
          }

          // 记录状态变化
          if (prevStatus !== processedTask.status) {
            console.log("\u4EFB\u52A1\u72B6\u6001\u5DF2\u66F4\u65B0: ".concat(prevStatus, " \u2192 ").concat(processedTask.status));
          }

          // 根据轮次状态排序
          var notStartedOrActive = processedTask.rounds_detail.filter(function (round) {
            return round.status === 0 || round.status === 1;
          });
          var completedOrExpired = processedTask.rounds_detail.filter(function (round) {
            return round.status === 2 || round.status === 3;
          });
          notStartedOrActive.sort(function (a, b) {
            return a.round - b.round;
          });
          completedOrExpired.sort(function (a, b) {
            return b.round - a.round;
          });
          processedTask.rounds_detail = [].concat((0, _toConsumableArray2.default)(notStartedOrActive), (0, _toConsumableArray2.default)(completedOrExpired));
        }
        return processedTask;
      });
      return processedTasks;
    },
    // 格式化日期用于比较
    formatDateForCompare: function formatDateForCompare(date) {
      if (!date) {
        return '';
      }
      try {
        // 确保date是Date对象
        if (!(date instanceof Date)) {
          // 尝试解析日期字符串
          var parsedDate = new Date(date);
          if (isNaN(parsedDate.getTime())) {
            console.error('无效的日期字符串:', date);
            return '';
          }
          date = parsedDate;
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          console.error('无效的日期:', date);
          return '';
        }

        // 获取本地时区的年月日
        var year = date.getFullYear();
        // 月份范围是0-11，需要+1并补零
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        var formattedDate = "".concat(year, "-").concat(month, "-").concat(day);
        return formattedDate;
      } catch (e) {
        console.error('格式化日期错误:', e, '原始日期:', date);
        return '';
      }
    },
    // 处理轮次选择事件
    onRoundSelected: function onRoundSelected(data) {
      // 性能优化：只在实际发生变化时更新和重建标记点
      var isChanging = !this.selectedRound || this.selectedRound.taskId !== data.taskId || this.selectedRound.round.round !== data.round.round;

      // 如果点击的是当前选中的轮次，取消选择（恢复自动模式）
      if (this.selectedRound && this.selectedRound.taskId === data.taskId && this.selectedRound.round.round === data.round.round) {
        this.selectedRound = null;
      } else {
        this.selectedRound = data;
      }

      // 如果选择的轮次属于当前激活的任务，且轮次发生了变化，重新构建标记点
      if (isChanging && this.currentTask && this.currentTask._id === data.taskId) {
        this.buildTaskMarkers();
      }
    },
    // 切换抽屉菜单
    toggleDrawer: function toggleDrawer() {
      this.isDrawerOpen = !this.isDrawerOpen;
    },
    // 关闭抽屉菜单
    closeDrawer: function closeDrawer() {
      this.drawerStyle = {
        transform: 'translateY(100%)',
        transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
      };
      this.isDrawerOpen = false;
    },
    // 打开抽屉菜单
    openDrawer: function openDrawer() {
      this.isDrawerOpen = true;
    },
    // 强制更新任务列表中的任务状态
    forceUpdateTaskInList: function forceUpdateTaskInList(taskId) {
      var _this24 = this;
      if (!taskId || !this.taskList || this.taskList.length === 0) return;
      console.log('强制更新任务列表中的任务:', taskId);

      // 获取任务最新数据
      _patrolApi.default.call({
        name: 'patrol-task',
        action: 'getTaskDetail',
        data: {
          task_id: taskId,
          // 🔥 使用优化后的参数格式
          level: 'map' // 🔥 启用地图专用模式
        }
      }).then(function (res) {
        if (res.code === 0 && res.data) {
          // 找到并替换任务
          var taskIndex = _this24.taskList.findIndex(function (t) {
            return t._id === taskId;
          });
          if (taskIndex >= 0) {
            // 使用深拷贝创建新引用
            var newTaskList = (0, _toConsumableArray2.default)(_this24.taskList);
            newTaskList[taskIndex] = JSON.parse(JSON.stringify(res.data));

            // 确保处理轮次信息
            if (newTaskList[taskIndex].rounds_detail && newTaskList[taskIndex].rounds_detail.length > 0) {
              var processedTasks = _this24.processTasks([newTaskList[taskIndex]]);
              if (processedTasks.length > 0) {
                newTaskList[taskIndex] = processedTasks[0];
              }
            }

            // 替换整个数组以确保视图更新
            _this24.taskList = newTaskList;
            console.log('已更新任务列表中的任务:', taskId);
          }
        }
      }).catch(function (error) {
        console.error('获取任务详情失败:', error);
      });
    },
    handleTouchStart: function handleTouchStart(event) {
      var _this25 = this;
      if (!this.isDrawerOpen) return;

      // iOS设备上禁用拖拽手势，避免事件穿透问题
      if (uni.getSystemInfoSync().platform === 'ios') {
        return;
      }
      this.touchStartY = event.touches[0].clientY;
      this.isDragging = true;
      this.currentTranslateY = 0;
      this.lastTouchTime = Date.now();
      this.lastDeltaY = 0;

      // 获取抽屉高度
      var query = uni.createSelectorQuery().in(this);
      query.select('.task-drawer').boundingClientRect(function (data) {
        if (data) {
          _this25.drawerHeight = data.height;
        }
      }).exec();
    },
    handleTouchMove: function handleTouchMove(event) {
      if (!this.isDragging || !this.isDrawerOpen) return;

      // iOS设备上禁用拖拽手势
      if (uni.getSystemInfoSync().platform === 'ios') {
        return;
      }
      var currentY = event.touches[0].clientY;
      var deltaY = currentY - this.touchStartY;
      var currentTime = Date.now();
      var timeDiff = currentTime - this.lastTouchTime;

      // 计算滑动速度 (像素/毫秒)
      var velocity = Math.abs((deltaY - this.lastDeltaY) / timeDiff);
      if (deltaY < 0) return;

      // 根据滑动速度和距离动态调整阻尼效果
      var damping = 0.9; // 基础阻尼系数
      if (velocity > 0.5) {
        // 如果滑动速度较快
        damping = 1; // 减小阻尼
      } else if (deltaY < this.drawerHeight * 0.2) {
        // 在开始阶段
        damping = 0.95; // 稍微减小阻尼，使初始滑动更容易
      }

      this.currentTranslateY = Math.min(deltaY * damping, this.drawerHeight);

      // 应用变换
      this.drawerStyle = {
        transform: "translateY(".concat(this.currentTranslateY, "px)"),
        transition: 'none'
      };

      // 更新遮罩层透明度
      var maskOpacity = Math.max(0.4 - deltaY / this.drawerHeight * 0.4, 0);
      this.maskStyle = {
        backgroundColor: "rgba(0, 0, 0, ".concat(maskOpacity, ")")
      };

      // 更新状态
      this.lastTouchTime = currentTime;
      this.lastDeltaY = deltaY;
    },
    handleTouchEnd: function handleTouchEnd() {
      if (!this.isDragging || !this.isDrawerOpen) return;

      // iOS设备上禁用拖拽手势
      if (uni.getSystemInfoSync().platform === 'ios') {
        return;
      }
      this.isDragging = false;

      // 计算最终速度
      var endTime = Date.now();
      var timeDiff = endTime - this.lastTouchTime;
      var velocity = Math.abs((this.currentTranslateY - this.lastDeltaY) / timeDiff);

      // 动态关闭阈值：根据速度和距离综合判断
      var closeThreshold = this.drawerHeight * 0.2; // 默认20%
      if (velocity > 0.5) {
        // 如果速度较快
        closeThreshold = this.drawerHeight * 0.1; // 降低到10%
      }

      // 恢复过渡动画
      this.drawerStyle = {
        transform: this.currentTranslateY > closeThreshold ? 'translateY(100%)' : 'translateY(0)',
        transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
      };

      // 如果拖动距离超过阈值，关闭抽屉
      if (this.currentTranslateY > closeThreshold) {
        this.closeDrawer();
      } else {
        // 否则恢复原位
        this.maskStyle = {
          backgroundColor: 'rgba(0, 0, 0, 0.4)'
        };
      }
      this.currentTranslateY = 0;
      this.lastDeltaY = 0;
    },
    // 添加getRoundStatus方法
    getRoundStatus: function getRoundStatus(round) {
      var now = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : new Date();
      if (!round || !round.start_time || !round.end_time) {
        return 0; // 默认未开始
      }

      try {
        // 如果轮次已经有状态且为已完成，保持完成状态
        if (round.status === 2) {
          return 2;
        }
        var startTime = new Date(round.start_time.replace(/-/g, '/'));
        var endTime = new Date(round.end_time.replace(/-/g, '/'));

        // 检查点位完成情况
        var isAllPointsCompleted = round.stats && round.stats.total_points > 0 && round.stats.completed_points >= round.stats.total_points;

        // 如果所有点位已完成，则轮次状态为已完成
        if (isAllPointsCompleted) {
          return 2;
        }

        // 根据时间判断状态
        if (now < startTime) {
          return 0; // 未开始
        } else if (now > endTime) {
          return 3; // 已超时
        } else {
          return 1; // 进行中
        }
      } catch (error) {
        console.error('计算轮次状态出错:', error);
        return 0; // 出错时默认未开始
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"]))

/***/ }),

/***/ 73:
/*!************************************************************************!*\
  !*** D:/Xwzc/pages/patrol/index.vue?vue&type=style&index=0&lang=scss& ***!
  \************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss& */ 74);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 74:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol/index.vue?vue&type=style&index=0&lang=scss& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[63,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/patrol/index.js.map
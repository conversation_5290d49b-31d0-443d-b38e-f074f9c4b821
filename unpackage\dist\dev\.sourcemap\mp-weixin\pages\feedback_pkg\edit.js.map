{"version": 3, "sources": ["webpack:///D:/Xwzc/pages/feedback_pkg/edit.vue?dd61", "webpack:///D:/Xwzc/pages/feedback_pkg/edit.vue?90ec", "uni-app:///main.js", "webpack:///D:/Xwzc/pages/feedback_pkg/edit.vue?9a33", "webpack:///D:/Xwzc/pages/feedback_pkg/edit.vue?0cae", "webpack:///D:/Xwzc/pages/feedback_pkg/edit.vue?a86e", "webpack:///D:/Xwzc/pages/feedback_pkg/edit.vue?7242", "uni-app:///pages/feedback_pkg/edit.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "result", "data", "formData", "formOptions", "responsible_localdata", "rules", "getValidator", "onLoad", "onReady", "methods", "loadResponsibleMap", "db", "where", "role", "field", "get", "res", "value", "text", "console", "uni", "title", "icon", "submit", "mask", "submitForm", "setTimeout", "description", "showCancel", "getDetail", "content", "chooseImage", "count", "success", "target", "files", "deleteImage", "uniCloud", "name", "fileList", "previewImage", "urls", "current", "uploadImage", "fileName", "fileExt", "safeFileName", "uniqueFileName", "now", "year", "month", "day", "dateFolder", "filePath", "cloudPath", "cloudPathAsRealPath", "handleImageUpload", "uploadedImages", "file", "fileID", "navigateBack"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,i3BAAG,EAAC,C;;;;;;;;;;;ACAp4B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,8RAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,wUAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,gZAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACuDlnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;EACA;EACA;IACA;MACAC;IACA;EACA;EACA;AACA;AAAA,eAEA;EACAC;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;MACAC;MACAC;QACA,sBACA;UACA;UACA;QACA,GACA;UACA;UACA;QACA,GACA;UACA;UACA;QACA,EACA;QACA,wBACA;UACA;UACA;QACA,GACA;UACA;UACA;QACA,EACA;QACAC;MACA;MACAC,yBACAC;IAEA;EACA;EACAC;IACA;MACA;MACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC,8BACAC;kBACAC;gBACA,GACAC,iCACAC;cAAA;gBALAC;gBAOA;kBAAA;oBACAC;oBACAC;kBACA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;AACA;AACA;IACAC;MAAA;MACAH;QACAI;MACA;MACA;QACA;MACA,sBACA;QACAJ;MACA;IACA;IAEA;AACA;AACA;IACAK;MAAA;MACA;MACA;QACAL;UACAC;QACA;QACA;QACAK;UAAA;QAAA;MACA;QACAN;UACAO;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACAT;QACAI;MACA;MACAb;QACA;QACA;UACA;QACA;MACA;QACAS;UACAU;UACAF;QACA;MACA;QACAR;MACA;IACA;IACAW;MAAA;MACAX;QACAY;QACAC;UACA;YAAAC;cAAAC;YAAA;UAAA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;;MAEA;MACAhB;QACAC;QACAG;MACA;;MAEA;MACAa;QACAC;QACArC;UACAsC;QACA;MACA;QAEA;UACA;UACA;UAEAnB;YACAC;YACAC;UACA;QACA;UACA;UACA;QACA;MACA;QACAF;UACAC;UACAS;UACAF;QACA;MACA;QACAR;MACA;IACA;IACAoB;MACApB;QACAqB;QACAC;MACA;IACA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC,gEAEA;gBACAC,2FAEA;gBACAC,gFAEA;gBACAC,kFAEA;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC,wDAGA;gBAAA;gBAAA,OACAf;kBACAgB;kBAAA;kBACAC;kBAAA;kBACAC;gBACA;cAAA;gBAJAvC;gBAAA,kCAOAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACAwC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACArB;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAsB;gBAAA,uCACAtB;gBAAA;gBAAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAAuB;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAF;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAKA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAG;MACAxC;IACA;EACA;AACA;AAAA,2B", "file": "pages/feedback_pkg/edit.js", "sourcesContent": ["import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571646328\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/feedback_pkg/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=517a113e&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/feedback_pkg/edit.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=template&id=517a113e&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniDataCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox\" */ \"@/uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker\" */ \"@/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue\"\n      )\n    },\n    uniDataSelect: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-data-select/components/uni-data-select/uni-data-select\" */ \"@/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.formData.images.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"uni-container\">\n    <uni-forms ref=\"form\" :model=\"formData\" validateTrigger=\"bind\">\n      <uni-forms-item name=\"name\" label=\"姓名\" required>\n        <uni-easyinput placeholder=\"用户的姓名\" v-model=\"formData.name\"></uni-easyinput>\n      </uni-forms-item>\n      <uni-forms-item name=\"project\" label=\"找茬项目\">\n        <uni-data-checkbox v-model=\"formData.project\" :localdata=\"formOptions.project_localdata\"></uni-data-checkbox>\n      </uni-forms-item>\n      <uni-forms-item name=\"description\" label=\"问题描述\">\n        <uni-easyinput type=\"textarea\" placeholder=\"问题的详细描述\" v-model=\"formData.description\"></uni-easyinput>\n      </uni-forms-item>\n      <uni-forms-item name=\"images\" label=\"图片\">\n        <view class=\"image-container\">\n          <view v-if=\"formData.images.length < 5\" class=\"upload-btn\" @click=\"chooseImage\">\n            <uni-icons type=\"plusempty\" size=\"24\" color=\"#999\" />\n          </view>\n          <view class=\"image-item\" v-for=\"(img, index) in formData.images\" :key=\"index\">\n            <image\n              :src=\"img\"\n              mode=\"aspectFit\"\n              class=\"image-hover\"\n              @click=\"previewImage(formData.images, index)\"\n            />\n            <view class=\"delete-icon\" @click=\"deleteImage(index)\">×</view>\n          </view>\n        </view>\n      </uni-forms-item>\n\n      <uni-forms-item name=\"createTime\" label=\"创建时间\">\n        <uni-datetime-picker type=\"date\" return-type=\"timestamp\" v-model=\"formData.createTime\"></uni-datetime-picker>\n      </uni-forms-item>\n      <uni-forms-item name=\"responsibleUserId\" label=\"负责人\">\n        <uni-data-select\n          v-model=\"formData.responsibleUserId\"\n          :localdata=\"formOptions.responsible_localdata\"\n          placeholder=\"请选择负责人\"\n        ></uni-data-select>\n      </uni-forms-item>\n      <uni-forms-item name=\"isAdopted\" label=\"是否采纳\">\n        <switch @change=\"binddata('isAdopted', $event.detail.value)\" :checked=\"formData.isAdopted\" color=\"#2979ff\"></switch>\n      </uni-forms-item>\n\n      <uni-forms-item name=\"remark\" label=\"备注\">\n        <uni-easyinput type=\"textarea\" placeholder=\"备注信息\" autoHeight v-model=\"formData.remark\"></uni-easyinput>\n      </uni-forms-item>\n      <view class=\"uni-button-group\">\n        <button class=\"submit-btn\" @click=\"submit\">提交</button>\n        <button class=\"cancel-btn\" @click=\"navigateBack\">返回</button>\n      </view>\n    </uni-forms>\n  </view>\n</template>\n\n<script>\n  import { validator } from '../../js_sdk/validator/feedback.js';\n\n  const db = uniCloud.database();\n  const dbCmd = db.command;\n  const dbCollectionName = 'feedback';\n\n  function getValidator(fields) {\n    let result = {}\n    for (let key in validator) {\n      if (fields.includes(key)) {\n        result[key] = validator[key]\n      }\n    }\n    return result\n  }\n\n  export default {\n    data() {\n      let formData = {\n        \"name\": \"\",\n        \"project\": \"\",\n        \"description\": \"\",\n        \"images\": [],\n        \"createTime\": null,\n        \"responsibleUserId\": \"\",\n        \"isAdopted\": \"\",\n        \"remark\": \"\"\n      }\n      return {\n        formData,\n        formOptions: {\n          \"project_localdata\": [\n            {\n              \"value\": \"安全找茬\",\n              \"text\": \"安全找茬\"\n            },\n            {\n              \"value\": \"设备找茬\",\n              \"text\": \"设备找茬\"\n            },\n            {\n              \"value\": \"其他找茬\",\n              \"text\": \"其他找茬\"\n            }\n          ],\n          \"isAdopted_localdata\": [\n            {\n              \"value\": \"是\",\n              \"text\": \"是\"\n            },\n            {\n              \"value\": \"否\",\n              \"text\": \"否\"\n            },\n          ],\n          responsible_localdata: []\n        },\n        rules: {\n          ...getValidator(Object.keys(formData))\n        }\n      }\n    },\n    onLoad(e) {\n      if (e.id) {\n        const id = e.id\n        this.formDataId = id\n        this.getDetail(id)\n      }\n      this.loadResponsibleMap();\n    },\n    onReady() {\n      this.$refs.form.setRules(this.rules)\n    },\n    methods: {\n      async loadResponsibleMap() {\n        try {\n          const res = await db.collection('uni-id-users')\n            .where({\n              role: db.command.in(['responsible'])\n            })\n            .field('_id, nickname, username')\n            .get();\n          \n          this.formOptions.responsible_localdata = res.result.data.map(user => ({\n            value: user._id,\n            text: user.nickname || user.username || '未命名用户'\n          }));\n        } catch (err) {\n          console.error('加载负责人列表失败:', err);\n          uni.showToast({\n            title: '加载负责人列表失败',\n            icon: 'none'\n          });\n        }\n      },\n      /**\n       * 验证表单并提交\n       */\n      submit() {\n        uni.showLoading({\n          mask: true\n        })\n        this.$refs.form.validate().then((res) => {\n          return this.submitForm(res)\n        }).catch(() => {\n        }).finally(() => {\n          uni.hideLoading()\n        })\n      },\n\n      /**\n       * 提交表单\n       */\n      submitForm(value) {\n        // 使用 clientDB 提交数据\n        return db.collection(dbCollectionName).doc(this.formDataId).update(value).then((res) => {\n          uni.showToast({\n            title: '修改成功'\n          })\n          this.getOpenerEventChannel().emit('refreshData')\n          setTimeout(() => uni.navigateBack(), 500)\n        }).catch((err) => {\n          uni.showModal({\n            description: err.message || '请求服务失败',\n            showCancel: false\n          })\n        })\n      },\n\n      /**\n       * 获取表单数据\n       * @param {Object} id\n       */\n      getDetail(id) {\n        uni.showLoading({\n          mask: true\n        })\n        db.collection(dbCollectionName).doc(id).field(\"name,project,description,images,createTime,responsibleUserId,isAdopted,remark\").get().then((res) => {\n          const data = res.result.data[0]\n          if (data) {\n            this.formData = data\n          }\n        }).catch((err) => {\n          uni.showModal({\n            content: err.message || '请求服务失败',\n            showCancel: false\n          })\n        }).finally(() => {\n          uni.hideLoading()\n        })\n      },\n      chooseImage() {\n        uni.chooseImage({\n          count: 5 - this.formData.images.length,\n          success: (res) => {\n            this.handleImageUpload({ target: { files: res.tempFiles } });\n          }\n        });\n      },\n      deleteImage(index) {\n        // 获取要删除的图片fileID\n        const fileID = this.formData.images[index];\n        \n        // 显示加载提示\n        uni.showLoading({\n          title: '删除中...',\n          mask: true\n        });\n        \n        // 调用云函数删除云存储中的文件\n        uniCloud.callFunction({\n          name: 'delete-file',\n          data: {\n            fileList: [fileID]\n          }\n        }).then(res => {\n          \n          if (res.result && res.result.code === 0) {\n            // 从表单中移除图片引用\n            this.formData.images.splice(index, 1);\n            \n            uni.showToast({\n              title: '删除成功',\n              icon: 'success'\n            });\n          } else {\n            const errorMsg = res.result ? res.result.message : '未知错误';\n            throw new Error(`删除失败: ${errorMsg}`);\n          }\n        }).catch(err => {\n          uni.showModal({\n            title: '删除失败',\n            content: err.message || '删除云存储图片失败',\n            showCancel: false\n          });\n        }).finally(() => {\n          uni.hideLoading();\n        });\n      },\n      previewImage(images, index) {\n        uni.previewImage({\n          urls: images,\n          current: index\n        });\n      },\n      async uploadImage(file) {\n        try {\n          // 从文件路径中提取文件名\n          const fileName = file.path.substring(file.path.lastIndexOf('/') + 1);\n          \n          // 提取文件扩展名\n          const fileExt = fileName.includes('.') ? fileName.substring(fileName.lastIndexOf('.')) : '.jpg';\n          \n          // 移除文件扩展名并替换特殊字符\n          const safeFileName = fileName.replace(/\\.[^/.]+$/, \"\").replace(/[^a-zA-Z0-9]/g, '_');\n          \n          // 生成一个包含原始文件名和扩展名的唯一文件名\n          const uniqueFileName = `${Date.now()}_${safeFileName}${fileExt}`;\n          \n          // 获取当前日期，创建年月日格式的目录结构\n          const now = new Date();\n          const year = now.getFullYear();\n          const month = String(now.getMonth() + 1).padStart(2, '0');\n          const day = String(now.getDate()).padStart(2, '0');\n          const dateFolder = `${year}${month}${day}`;\n          \n          \n          // 使用 uniCloud.uploadFile 上传图片到阿里云存储\n          const res = await uniCloud.uploadFile({\n            filePath: file.path, // 本地文件路径\n            cloudPath: `feedback/${dateFolder}/${uniqueFileName}`, // 按日期组织的云存储路径\n            cloudPathAsRealPath: true // 启用真实目录支持\n          });\n\n          // 返回上传后的文件地址\n          return res.fileID;\n        } catch (err) {\n          throw err;\n        }\n      },\n      async handleImageUpload(event) {\n        const files = event.target.files;\n        if (files && files.length > 0) {\n          const uploadedImages = [];\n          for (const file of files) {\n            try {\n              const fileID = await this.uploadImage(file);\n              uploadedImages.push(fileID);\n            } catch (err) {\n              // 上传失败，跳过该文件\n            }\n          }\n          this.formData.images = [...this.formData.images, ...uploadedImages];\n        }\n      },\n      navigateBack() {\n        uni.navigateBack();\n      },\n    }\n  }\n</script>\n\n<style>\n  .uni-container {\n    padding-top: 20px;\n    padding-right: 20px;\n    padding-left: 20px;\n    padding-bottom: 20px;\n    min-height: 100vh;\n    justify-content: flex-start;\n    background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n  }\n\n  .uni-forms {\n    width: 100%;\n    max-width: 90%;\n    margin: 0 auto;\n    padding: 40rpx;\n    background-color: #fff;\n    border-radius: 20rpx;\n    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);\n  }\n\n  .uni-forms-item {\n    margin-bottom: 30px;\n    position: relative;\n  }\n\n  .uni-forms-item__label {\n    display: block;\n    margin-bottom: 12px;\n    font-size: 16px;\n    color: #2d3748;\n    font-weight: 600;\n  }\n\n .uni-data-checkbox, .uni-data-select, .uni-datetime-picker {\n    width: 100%;\n    padding: 14px;\n    border: 2px solid #e2e8f0;\n    border-radius: 12px;\n    box-sizing: border-box;\n    background-color: #f8fafc;\n    transition: all 0.3s ease;\n    font-size: 15px;\n    color: #4a5568;\n    line-height: 1.5;\n    height: auto;\n    min-height: 48px;\n    vertical-align: middle;\n  }\n\n  .uni-easyinput::placeholder {\n    color: #a0aec0;\n  }\n\n  .uni-easyinput:focus, .uni-data-checkbox:focus, .uni-data-select:focus, .uni-datetime-picker:focus {\n    border-color: #4299e1;\n    box-shadow: 0 0 0 4px rgba(66, 153, 225, 0.15);\n    outline: none;\n    background-color: #fff;\n  }\n\n  .image-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 20px;\n  }\n\n  .image-item {\n    position: relative;\n    width: 100px;\n    height: 100px;\n    border-radius: 12px;\n    overflow: hidden;\n    border: 2px dashed #e2e8f0;\n    background-color: #f8fafc;\n    transition: all 0.3s ease;\n  }\n\n  .image-item:hover {\n    border-color: #4299e1;\n    background-color: rgba(66, 153, 225, 0.1);\n  }\n\n  .image-hover {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n  }\n\n  .delete-icon {\n    position: absolute;\n    right: 10px;\n    top: 10px;\n    width: 20px;\n    height: 20px;\n    background-color: rgba(0, 0, 0, 0.5);\n    color: #fff;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 12px;\n    cursor: pointer;\n  }\n\n  .upload-btn {\n    width: 100px;\n    height: 100px;\n    border: 2px dashed #e2e8f0;\n    border-radius: 12px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background-color: #f8fafc;\n    color: #718096;\n    cursor: pointer;\n    transition: all 0.3s ease;\n  }\n\n  .upload-btn:hover {\n    border-color: #4299e1;\n    background-color: rgba(66, 153, 225, 0.1);\n  }\n\n  .upload-text {\n    font-size: 12px;\n    margin-top: 4px;\n    color: #718096;\n  }\n\n  .uni-button-group {\n    display: flex;\n    justify-content: center;\n    gap: 15px;\n    margin-top: 40px;\n    margin-bottom: 20px;\n  }\n\n  .submit-btn, .cancel-btn {\n    padding: 0 30px;\n    height: 44px;\n    font-size: 16px;\n    font-weight: 500;\n    border-radius: 22px;\n    border: none;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    transition: all 0.3s ease;\n    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);\n  }\n  \n  .submit-btn {\n    background: #1677FF;\n    color: #FFFFFF;\n  }\n  \n  .submit-btn:active {\n    background: #0E5FD8;\n    transform: scale(0.98);\n  }\n  \n  .cancel-btn {\n    background: #F0F0F0;\n    color: #666666;\n  }\n  \n  .cancel-btn:active {\n    background: #E0E0E0;\n    transform: scale(0.98);\n  }\n</style> "], "sourceRoot": ""}
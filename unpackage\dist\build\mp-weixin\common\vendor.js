(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["common/vendor"],{"011a":function(e,t){function n(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],(function(){})))}catch(t){}return(e.exports=n=function(){return!!t},e.exports.__esModule=!0,e.exports["default"]=e.exports)()}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"0bdb":function(e,t,n){var r=n("d551");function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,r(o.key),o)}}e.exports=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"0c73":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pages:[{path:"pages/index/index",style:{navigationBarTitleText:"株水小智",enablePullDownRefresh:!1}},{path:"pages/patrol/index",style:{navigationBarTitleText:"巡视打卡",enablePullDownRefresh:!1}},{path:"pages/feedback/list",style:{navigationBarTitleText:"问题反馈",enablePullDownRefresh:!0}},{path:"pages/ucenter/ucenter",style:{navigationBarTitleText:"用户中心",enablePullDownRefresh:!0}}],subPackages:[{root:"pages/feedback_pkg",pages:[{path:"edit",style:{navigationBarTitleText:"找茬编辑"}},{path:"examine",style:{navigationBarTitleText:"审核流程"}}]},{root:"pages/ucenter_pkg",pages:[{path:"todo",style:{navigationBarTitleText:"我的待办",enablePullDownRefresh:!0}},{path:"export-excel",style:{navigationBarTitleText:"导出Excel"}},{path:"user-management",style:{navigationBarTitleText:"用户管理",enablePullDownRefresh:!0}},{path:"responsible-tasks",style:{navigationBarTitleText:"我的任务",enablePullDownRefresh:!0}},{path:"complete-task",style:{navigationBarTitleText:"完成任务",enablePullDownRefresh:!1}}]},{root:"pages/notice",pages:[{path:"add",style:{navigationBarTitleText:"公告新增",enablePullDownRefresh:!1}},{path:"edit",style:{navigationBarTitleText:"公告编辑",enablePullDownRefresh:!1}},{path:"list",style:{navigationBarTitleText:"公告通知",enablePullDownRefresh:!0}},{path:"detail",style:{navigationBarTitleText:"公告详细"}}]},{root:"uni_modules/uni-id-pages/pages",pages:[{path:"login/login-withoutpwd",style:{navigationBarTitleText:"微信一键登录"}},{path:"login/login-withpwd",style:{navigationBarTitleText:"账号密码登录"}},{path:"userinfo/change_pwd/change_pwd",style:{navigationBarTitleText:"密码修改"}},{path:"userinfo/userinfo",style:{navigationBarTitleText:"个人资料"}}]},{root:"pages/honor_pkg",pages:[{path:"gallery/index",style:{navigationBarTitleText:"荣誉展厅",navigationStyle:"custom",enablePullDownRefresh:!0}},{path:"admin/index",style:{navigationBarTitleText:"荣誉管理",navigationStyle:"custom"}},{path:"admin/add-record",style:{navigationBarTitleText:"添加表彰记录",navigationStyle:"custom"}},{path:"admin/batch-manager",style:{navigationBarTitleText:"智能批次管理",navigationStyle:"custom"}},{path:"admin/type-manager",style:{navigationBarTitleText:"荣誉类型管理",navigationStyle:"custom"}}]},{root:"pages/info_pkg",pages:[{path:"user-guide",style:{navigationBarTitleText:"用户指南",enablePullDownRefresh:!1}},{path:"privacy",style:{navigationBarTitleText:"隐私政策",enablePullDownRefresh:!1}}]},{root:"pages/patrol_pkg",pages:[{path:"checkin/index",style:{navigationBarTitleText:"打卡签到",navigationStyle:"custom",enablePullDownRefresh:!1,disableScroll:!0}},{path:"point/index",style:{navigationBarTitleText:"点位管理",enablePullDownRefresh:!1}},{path:"point/add",style:{navigationBarTitleText:"添加点位",enablePullDownRefresh:!1}},{path:"point/edit",style:{navigationBarTitleText:"编辑点位",enablePullDownRefresh:!1}},{path:"point/detail",style:{navigationBarTitleText:"点位详情",enablePullDownRefresh:!1}},{path:"route/index",style:{navigationBarTitleText:"线路管理",enablePullDownRefresh:!1}},{path:"route/add",style:{navigationBarTitleText:"添加线路",enablePullDownRefresh:!1}},{path:"route/edit",style:{navigationBarTitleText:"编辑线路",enablePullDownRefresh:!1}},{path:"route/detail",style:{navigationBarTitleText:"线路详情",enablePullDownRefresh:!1}},{path:"shift/index",style:{navigationBarTitleText:"班次管理",enablePullDownRefresh:!1}},{path:"shift/add",style:{navigationBarTitleText:"添加班次",enablePullDownRefresh:!1}},{path:"shift/edit",style:{navigationBarTitleText:"编辑班次",enablePullDownRefresh:!1}},{path:"shift/detail",style:{navigationBarTitleText:"班次详情",enablePullDownRefresh:!1}},{path:"task/index",style:{navigationBarTitleText:"任务管理",enablePullDownRefresh:!0}},{path:"task/add",style:{navigationBarTitleText:"添加任务",enablePullDownRefresh:!1}},{path:"task/batch-add",style:{navigationBarTitleText:"批量添加任务",enablePullDownRefresh:!1}},{path:"task/detail",style:{navigationBarTitleText:"任务详情",enablePullDownRefresh:!1}},{path:"task/edit",style:{navigationBarTitleText:"编辑任务",enablePullDownRefresh:!1}},{path:"record/index",style:{navigationBarTitleText:"巡视记录",enablePullDownRefresh:!1}},{path:"record/detail",style:{navigationBarTitleText:"记录详情",enablePullDownRefresh:!1}},{path:"record/route-detail",style:{navigationBarTitleText:"线路记录",enablePullDownRefresh:!1}},{path:"point/qrcode",style:{navigationBarTitleText:"二维码管理",enablePullDownRefresh:!1}},{path:"point/qrcode-batch",style:{navigationBarTitleText:"批量二维码管理",enablePullDownRefresh:!1}}]}],globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"株水小智",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"},tabBar:{color:"#7A7E83",selectedColor:"#1677FF",borderStyle:"black",backgroundColor:"#ffffff",list:[{pagePath:"pages/index/index",iconPath:"static/tabbar/home.png",selectedIconPath:"static/tabbar/home_active.png",text:"找茬"},{pagePath:"pages/feedback/list",iconPath:"static/tabbar/feedback.png",selectedIconPath:"static/tabbar/feedback_active.png",text:"反馈"},{pagePath:"pages/patrol/index",iconPath:"static/tabbar/patrol.png",selectedIconPath:"static/tabbar/patrol_active.png",text:"巡视"},{pagePath:"pages/ucenter/ucenter",iconPath:"static/tabbar/user.png",selectedIconPath:"static/tabbar/user_active.png",text:"我的"}]},uniIdRouter:{loginPage:"uni_modules/uni-id-pages/pages/login/login-withoutpwd",needLogin:["pages/notice/.*","pages/feedback_pkg/.*","pages/ucenter_pkg/.*","pages/patrol_pkg/.*","pages/honor_pkg/.*","uni_modules/uni-id-pages/pages/userinfo/.*"],resToLogin:!1}}},"0ee4":function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(r){"object"===typeof window&&(n=window)}e.exports=n},"206a":function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.createAnimation=function(e,t){if(!t)return;return clearTimeout(t.timer),new c(e,t)};var o=r(n("7ca3")),i=r(n("67ad")),a=r(n("0bdb"));function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,o.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var c=function(){function t(n,r){(0,i.default)(this,t),this.options=n,this.animation=e.createAnimation(u({},n)),this.currentStepAnimates={},this.next=0,this.$=r}return(0,a.default)(t,[{key:"_nvuePushAnimates",value:function(e,t){var n=this.currentStepAnimates[this.next],r={};if(r=n||{styles:{},config:{}},l.includes(e)){r.styles.transform||(r.styles.transform="");var o="";"rotate"===e&&(o="deg"),r.styles.transform+="".concat(e,"(").concat(t+o,") ")}else r.styles[e]="".concat(t);this.currentStepAnimates[this.next]=r}},{key:"_animateRun",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.$.$refs["ani"].ref;if(n)return new Promise((function(r,o){nvueAnimation.transition(n,u({styles:e},t),(function(e){r()}))}))}},{key:"_nvueNextAnimate",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2?arguments[2]:void 0,o=e[n];if(o){var i=o.styles,a=o.config;this._animateRun(i,a).then((function(){n+=1,t._nvueNextAnimate(e,n,r)}))}else this.currentStepAnimates={},"function"===typeof r&&r(),this.isEnd=!0}},{key:"step",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.animation.step(e),this}},{key:"run",value:function(e){this.$.animationData=this.animation.export(),this.$.timer=setTimeout((function(){"function"===typeof e&&e()}),this.$.durationTime)}}]),t}(),l=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"];l.concat(["opacity","backgroundColor"],["width","height","left","right","top","bottom"]).forEach((function(e){c.prototype[e]=function(){var t;return(t=this.animation)[e].apply(t,arguments),this}}))}).call(this,n("df3c")["default"])},"20d8":function(e,t,n){"use strict";function r(){var e=this.waitingQueue=[],t=this.isRunning=!1;this.exec=function(n){return new Promise((function(r,o){t?e.push({task:n,resolve:r,reject:o}):(t=!0,function n(r,o,i){r().then((function(e){o(e)})).catch((function(e){i(e)})).finally((function(){if(e.length){var r=e.shift();n(r.task,r.resolve,r.reject)}else t=!1}))}(n,r,o))}))}}Object.defineProperty(t,"__esModule",{value:!0}),t.queueLoadImage=t.queueDraw=void 0;var o=new r;t.queueDraw=o;var i=new r;t.queueLoadImage=i},"24ff":function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("7eb4")),i=r(n("3b2d")),a=r(n("ee10")),s={props:{localdata:{type:[Array,Object],default:function(){return[]}},spaceInfo:{type:Object,default:function(){return{}}},collection:{type:String,default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:500},getcount:{type:[Boolean,String],default:!1},getone:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},manual:{type:Boolean,default:!1},value:{type:[Array,String,Number],default:function(){return[]}},modelValue:{type:[Array,String,Number],default:function(){return[]}},preload:{type:Boolean,default:!1},stepSearh:{type:Boolean,default:!0},selfField:{type:String,default:""},parentField:{type:String,default:""},multiple:{type:Boolean,default:!1},map:{type:Object,default:function(){return{text:"text",value:"value"}}}},data:function(){return{loading:!1,errorMessage:"",loadMore:{contentdown:"",contentrefresh:"",contentnomore:""},dataList:[],selected:[],selectedIndex:0,page:{current:this.pageCurrent,size:this.pageSize,count:0}}},computed:{isLocalData:function(){return!this.collection.length},isCloudData:function(){return this.collection.length>0},isCloudDataList:function(){return this.isCloudData&&!this.parentField&&!this.selfField},isCloudDataTree:function(){return this.isCloudData&&this.parentField&&this.selfField},dataValue:function(){var e=Array.isArray(this.modelValue)?this.modelValue.length>0:null!==this.modelValue||void 0!==this.modelValue;return e?this.modelValue:this.value},hasValue:function(){return"number"===typeof this.dataValue||null!=this.dataValue&&this.dataValue.length>0}},created:function(){var e=this;this.$watch((function(){var t=[];return["pageCurrent","pageSize","spaceInfo","value","modelValue","localdata","collection","action","field","orderby","where","getont","getcount","gettree"].forEach((function(n){t.push(e[n])})),t}),(function(t,n){for(var r=2;r<t.length;r++)if(t[r]!=n[r]){!0;break}t[0]!=n[0]&&(e.page.current=e.pageCurrent),e.page.size=e.pageSize,e.onPropsChange()})),this._treeData=[]},methods:{onPropsChange:function(){this._treeData=[]},loadData:function(){var e=this;return(0,a.default)(o.default.mark((function t(){return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.isLocalData?e.loadLocalData():e.isCloudDataList?e.loadCloudDataList():e.isCloudDataTree&&e.loadCloudDataTree();case 1:case"end":return t.stop()}}),t)})))()},loadLocalData:function(){var e=this;return(0,a.default)(o.default.mark((function t(){var n;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e._treeData=[],e._extractTree(e.localdata,e._treeData),n=e.dataValue,void 0!==n){t.next=5;break}return t.abrupt("return");case 5:Array.isArray(n)&&(n=n[n.length-1],"object"===(0,i.default)(n)&&n[e.map.value]&&(n=n[e.map.value])),e.selected=e._findNodePath(n,e.localdata);case 7:case"end":return t.stop()}}),t)})))()},loadCloudDataList:function(){var e=this;return(0,a.default)(o.default.mark((function t(){var n,r;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.loading){t.next=2;break}return t.abrupt("return");case 2:return e.loading=!0,t.prev=3,t.next=6,e.getCommand();case 6:n=t.sent,r=n.result.data,e._treeData=r,e._updateBindData(),e._updateSelected(),e.onDataChange(),t.next=17;break;case 14:t.prev=14,t.t0=t["catch"](3),e.errorMessage=t.t0;case 17:return t.prev=17,e.loading=!1,t.finish(17);case 20:case"end":return t.stop()}}),t,null,[[3,14,17,20]])})))()},loadCloudDataTree:function(){var e=this;return(0,a.default)(o.default.mark((function t(){var n,r,i;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.loading){t.next=2;break}return t.abrupt("return");case 2:return e.loading=!0,t.prev=3,n={field:e._cloudDataPostField(),where:e._cloudDataTreeWhere()},e.gettree&&(n.startwith="".concat(e.selfField,"=='").concat(e.dataValue,"'")),t.next=8,e.getCommand(n);case 8:r=t.sent,i=r.result.data,e._treeData=i,e._updateBindData(),e._updateSelected(),e.onDataChange(),t.next=19;break;case 16:t.prev=16,t.t0=t["catch"](3),e.errorMessage=t.t0;case 19:return t.prev=19,e.loading=!1,t.finish(19);case 22:case"end":return t.stop()}}),t,null,[[3,16,19,22]])})))()},loadCloudDataNode:function(e){var t=this;return(0,a.default)(o.default.mark((function n(){var r,i,a;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!t.loading){n.next=2;break}return n.abrupt("return");case 2:return t.loading=!0,n.prev=3,r={field:t._cloudDataPostField(),where:t._cloudDataNodeWhere()},n.next=7,t.getCommand(r);case 7:i=n.sent,a=i.result.data,e(a),n.next=15;break;case 12:n.prev=12,n.t0=n["catch"](3),t.errorMessage=n.t0;case 15:return n.prev=15,t.loading=!1,n.finish(15);case 18:case"end":return n.stop()}}),n,null,[[3,12,15,18]])})))()},getCloudDataValue:function(){return this.isCloudDataList?this.getCloudDataListValue():this.isCloudDataTree?this.getCloudDataTreeValue():void 0},getCloudDataListValue:function(){var e=this,t=[],n=this._getForeignKeyByField();return n&&t.push("".concat(n," == '").concat(this.dataValue,"'")),t=t.join(" || "),this.where&&(t="(".concat(this.where,") && (").concat(t,")")),this.getCommand({field:this._cloudDataPostField(),where:t}).then((function(t){return e.selected=t.result.data,t.result.data}))},getCloudDataTreeValue:function(){var e=this;return this.getCommand({field:this._cloudDataPostField(),getTreePath:{startWith:"".concat(this.selfField,"=='").concat(this.dataValue,"'")}}).then((function(t){var n=[];return e._extractTreePath(t.result.data,n),e.selected=n,n}))},getCommand:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.database(this.spaceInfo),r=t.action||this.action;r&&(n=n.action(r));var o=t.collection||this.collection;n=n.collection(o);var i=t.where||this.where;i&&Object.keys(i).length&&(n=n.where(i));var a=t.field||this.field;a&&(n=n.field(a));var s=t.orderby||this.orderby;s&&(n=n.orderBy(s));var u=void 0!==t.pageCurrent?t.pageCurrent:this.page.current,c=void 0!==t.pageSize?t.pageSize:this.page.size,l=void 0!==t.getcount?t.getcount:this.getcount,f=void 0!==t.gettree?t.gettree:this.gettree,d={getCount:l,getTree:f};return t.getTreePath&&(d.getTreePath=t.getTreePath),n=n.skip(c*(u-1)).limit(c).get(d),n},_cloudDataPostField:function(){var e=[this.field];return this.parentField&&e.push("".concat(this.parentField," as parent_value")),e.join(",")},_cloudDataTreeWhere:function(){var e=[],t=this.selected,n=this.parentField;if(n&&e.push("".concat(n," == null || ").concat(n,' == ""')),t.length)for(var r=0;r<t.length-1;r++)e.push("".concat(n," == '").concat(t[r].value,"'"));var o=[];return this.where&&o.push("(".concat(this.where,")")),e.length&&o.push("(".concat(e.join(" || "),")")),o.join(" && ")},_cloudDataNodeWhere:function(){var e=[],t=this.selected;return t.length&&e.push("".concat(this.parentField," == '").concat(t[t.length-1].value,"'")),e=e.join(" || "),this.where?"(".concat(this.where,") && (").concat(e,")"):e},_getWhereByForeignKey:function(){var e=[],t=this._getForeignKeyByField();return t&&e.push("".concat(t," == '").concat(this.dataValue,"'")),this.where?"(".concat(this.where,") && (").concat(e.join(" || "),")"):e.join(" || ")},_getForeignKeyByField:function(){for(var e=this.field.split(","),t=null,n=0;n<e.length;n++){var r=e[n].split("as");if(!(r.length<2)&&"value"===r[1].trim()){t=r[0].trim();break}}return t},_updateBindData:function(e){var t=this._filterData(this._treeData,this.selected),n=t.dataList,r=t.hasNodes,o=!1===this._stepSearh&&!r;return e&&(e.isleaf=o),this.dataList=n,this.selectedIndex=n.length-1,!o&&this.selected.length<n.length&&this.selected.push({value:null,text:"请选择"}),{isleaf:o,hasNodes:r}},_updateSelected:function(){for(var e=this.dataList,t=this.selected,n=this.map.text,r=this.map.value,o=0;o<t.length;o++)for(var i=t[o].value,a=e[o],s=0;s<a.length;s++){var u=a[s];if(u[r]===i){t[o].text=u[n];break}}},_filterData:function(e,t){var n=[],r=!0;n.push(e.filter((function(e){return null===e.parent_value||void 0===e.parent_value||""===e.parent_value})));for(var o=function(o){var i=t[o].value,a=e.filter((function(e){return e.parent_value===i}));a.length?n.push(a):r=!1},i=0;i<t.length;i++)o(i);return{dataList:n,hasNodes:r}},_extractTree:function(e,t,n){for(var r=this.map.value,o=0;o<e.length;o++){var i=e[o],a={};for(var s in i)"children"!==s&&(a[s]=i[s]);null!==n&&void 0!==n&&""!==n&&(a.parent_value=n),t.push(a);var u=i.children;u&&this._extractTree(u,t,i[r])}},_extractTreePath:function(e,t){for(var n=0;n<e.length;n++){var r=e[n],o={};for(var i in r)"children"!==i&&(o[i]=r[i]);t.push(o);var a=r.children;a&&this._extractTreePath(a,t)}},_findNodePath:function(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=this.map.text,o=this.map.value,i=0;i<t.length;i++){var a=t[i],s=a.children,u=a[r],c=a[o];if(n.push({value:c,text:u}),c===e)return n;if(s){var l=this._findNodePath(e,s,n);if(l.length)return l}n.pop()}return[]}}};t.default=s}).call(this,n("861b")["uniCloud"])},2889:function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.Calendar=void 0,t.addZero=l,t.checkDate=function(e){return e.match(/((19|20)\d{2})(-|\/)\d{1,2}(-|\/)\d{1,2}/g)},t.dateCompare=f,t.fixIosDateFormat=h,t.getDate=u,t.getDateTime=function(e,t){return"".concat(u(e)," ").concat(c(e,t))},t.getDefaultSecond=function(e){return e?"00:00":"00:00:00"},t.getTime=c;var o=r(n("af34")),i=r(n("67ad")),a=r(n("0bdb")),s=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.selected,r=t.startDate,o=t.endDate,a=t.range;(0,i.default)(this,e),this.date=this.getDateObj(new Date),this.selected=n||[],this.startDate=r,this.endDate=o,this.range=a,this.cleanMultipleStatus(),this.weeks={},this.lastHover=!1}return(0,a.default)(e,[{key:"setDate",value:function(e){var t=this.getDateObj(e);this.getWeeks(t.fullDate)}},{key:"cleanMultipleStatus",value:function(){this.multipleStatus={before:"",after:"",data:[]}}},{key:"setStartDate",value:function(e){this.startDate=e}},{key:"setEndDate",value:function(e){this.endDate=e}},{key:"getPreMonthObj",value:function(e){e=h(e),e=new Date(e);var t=e.getMonth();e.setMonth(t-1);var n=e.getMonth();return 0!==t&&n-t===0&&e.setMonth(n-1),this.getDateObj(e)}},{key:"getNextMonthObj",value:function(e){e=h(e),e=new Date(e);var t=e.getMonth();e.setMonth(t+1);var n=e.getMonth();return n-t>1&&e.setMonth(n-1),this.getDateObj(e)}},{key:"getDateObj",value:function(e){return e=h(e),e=new Date(e),{fullDate:u(e),year:e.getFullYear(),month:l(e.getMonth()+1),date:l(e.getDate()),day:e.getDay()}}},{key:"getPreMonthDays",value:function(e,t){for(var n=[],r=e-1;r>=0;r--){var o=t.month-1;n.push({date:new Date(t.year,o,-r).getDate(),month:o,disable:!0})}return n}},{key:"getCurrentMonthDays",value:function(e,t){for(var n=this,r=[],o=this.date.fullDate,i=function(e){var i="".concat(t.year,"-").concat(t.month,"-").concat(l(e)),a=o===i,s=n.selected&&n.selected.find((function(e){if(n.dateEqual(i,e.date))return e}));n.startDate&&f(n.startDate,i),n.endDate&&f(i,n.endDate);var u=n.multipleStatus.data,c=-1;n.range&&u&&(c=u.findIndex((function(e){return n.dateEqual(e,i)})));var d=-1!==c;r.push({fullDate:i,year:t.year,date:e,multiple:!!n.range&&d,beforeMultiple:n.isLogicBefore(i,n.multipleStatus.before,n.multipleStatus.after),afterMultiple:n.isLogicAfter(i,n.multipleStatus.before,n.multipleStatus.after),month:t.month,disable:n.startDate&&!f(n.startDate,i)||n.endDate&&!f(i,n.endDate),isToday:a,userChecked:!1,extraInfo:s})},a=1;a<=e;a++)i(a);return r}},{key:"_getNextMonthDays",value:function(e,t){for(var n=[],r=t.month+1,o=1;o<=e;o++)n.push({date:o,month:r,disable:!0});return n}},{key:"getInfo",value:function(e){var t=this;e||(e=new Date);var n=this.calendar.find((function(n){return n.fullDate===t.getDateObj(e).fullDate}));return n||this.getDateObj(e)}},{key:"dateEqual",value:function(e,t){return e=new Date(h(e)),t=new Date(h(t)),e.valueOf()===t.valueOf()}},{key:"isLogicBefore",value:function(e,t,n){var r=t;return t&&n&&(r=f(t,n)?t:n),this.dateEqual(r,e)}},{key:"isLogicAfter",value:function(e,t,n){var r=n;return t&&n&&(r=f(t,n)?n:t),this.dateEqual(r,e)}},{key:"geDateAll",value:function(e,t){var n=[],r=e.split("-"),o=t.split("-"),i=new Date;i.setFullYear(r[0],r[1]-1,r[2]);var a=new Date;a.setFullYear(o[0],o[1]-1,o[2]);for(var s=i.getTime()-864e5,u=a.getTime()-864e5,c=s;c<=u;)c+=864e5,n.push(this.getDateObj(new Date(parseInt(c))).fullDate);return n}},{key:"setMultiple",value:function(e){if(this.range){var t=this.multipleStatus,n=t.before,r=t.after;if(n&&r){if(!this.lastHover)return void(this.lastHover=!0);this.multipleStatus.before=e,this.multipleStatus.after="",this.multipleStatus.data=[],this.multipleStatus.fulldate="",this.lastHover=!1}else n?(this.multipleStatus.after=e,f(this.multipleStatus.before,this.multipleStatus.after)?this.multipleStatus.data=this.geDateAll(this.multipleStatus.before,this.multipleStatus.after):this.multipleStatus.data=this.geDateAll(this.multipleStatus.after,this.multipleStatus.before),this.lastHover=!0):(this.multipleStatus.before=e,this.multipleStatus.after=void 0,this.lastHover=!1);this.getWeeks(e)}}},{key:"setHoverMultiple",value:function(e){if(this.range&&!this.lastHover){var t=this.multipleStatus.before;t?(this.multipleStatus.after=e,f(this.multipleStatus.before,this.multipleStatus.after)?this.multipleStatus.data=this.geDateAll(this.multipleStatus.before,this.multipleStatus.after):this.multipleStatus.data=this.geDateAll(this.multipleStatus.after,this.multipleStatus.before)):this.multipleStatus.before=e,this.getWeeks(e)}}},{key:"setDefaultMultiple",value:function(e,t){this.multipleStatus.before=e,this.multipleStatus.after=t,e&&t&&(f(e,t)?(this.multipleStatus.data=this.geDateAll(e,t),this.getWeeks(t)):(this.multipleStatus.data=this.geDateAll(t,e),this.getWeeks(e)))}},{key:"getWeeks",value:function(e){for(var t=this.getDateObj(e),n=t.year,r=t.month,i=new Date(n,r-1,1).getDay(),a=this.getPreMonthDays(i,this.getDateObj(e)),s=new Date(n,r,0).getDate(),u=this.getCurrentMonthDays(s,this.getDateObj(e)),c=42-i-s,l=this._getNextMonthDays(c,this.getDateObj(e)),f=[].concat((0,o.default)(a),(0,o.default)(u),(0,o.default)(l)),d=new Array(6),h=0;h<f.length;h++){var p=Math.floor(h/7);d[p]||(d[p]=new Array(7)),d[p][h%7]=f[h]}this.calendar=f,this.weeks=d}}]),e}();function u(e){e=h(e),e=new Date(e);var t=e.getFullYear(),n=e.getMonth()+1,r=e.getDate();return"".concat(t,"-").concat(l(n),"-").concat(l(r))}function c(e,t){e=h(e),e=new Date(e);var n=e.getHours(),r=e.getMinutes(),o=e.getSeconds();return t?"".concat(l(n),":").concat(l(r)):"".concat(l(n),":").concat(l(r),":").concat(l(o))}function l(e){return e<10&&(e="0".concat(e)),e}function f(e,t){return e=new Date(h(e)),t=new Date(h(t)),e<=t}t.Calendar=s;var d=/^\d{4}-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])( [0-5]?[0-9]:[0-5]?[0-9](:[0-5]?[0-9])?)?$/;function h(e){return"string"===typeof e&&d.test(e)&&(e=e.replace(/-/g,"/")),e}},"2a11":function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.cacheGeocoderData=u,t.default=void 0,t.formatAddress=s,t.geocoder=a,t.getCachedGeocoderData=c,t.getNearbyPOI=i,t.reverseGeocoder=o;var n="5MPBZ-FCW63-3C43B-R7G72-UOSAO-ZWBTJ",r="https://apis.map.qq.com/ws";function o(t){return new Promise((function(o,i){if(!t||!t.latitude||!t.longitude)return i(new Error("位置信息不完整"));e.request({url:"".concat(r,"/geocoder/v1/"),data:{key:n,location:"".concat(t.latitude,",").concat(t.longitude),get_poi:0},success:function(e){var t;e.data&&0===e.data.status?o(e.data.result):i(new Error((null===(t=e.data)||void 0===t?void 0:t.message)||"逆地址解析失败"))},fail:function(e){i(e)}})}))}function i(t){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(i,a){if(!t||!t.latitude||!t.longitude)return a(new Error("位置信息不完整"));var s=o.radius,u=void 0===s?500:s,c=o.keyword,l=void 0===c?"":c,f=o.page_size,d=void 0===f?20:f,h=o.page_index,p=void 0===h?1:h;e.request({url:"".concat(r,"/place/v1/search"),data:{key:n,location:"".concat(t.latitude,",").concat(t.longitude),radius:u,keyword:l,page_size:d,page_index:p},success:function(e){var t;e.data&&0===e.data.status?i(e.data.data):a(new Error((null===(t=e.data)||void 0===t?void 0:t.message)||"获取POI信息失败"))},fail:function(e){a(e)}})}))}function a(t){return new Promise((function(o,i){if(!t)return i(new Error("地址不能为空"));e.request({url:"".concat(r,"/geocoder/v1/"),data:{key:n,address:t},success:function(e){var t;e.data&&0===e.data.status?o(e.data.result):i(new Error((null===(t=e.data)||void 0===t?void 0:t.message)||"地址解析失败"))},fail:function(e){i(e)}})}))}function s(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e||!e.address_component)return"";var r=e.address_component,o=r.province,i=r.city,a=r.district,s=r.street,u=r.street_number,c="";return t&&o&&(c+=o),n&&i&&(c+=i),a&&(c+=a),s&&(c+=s),u&&(c+=u),c}function u(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:36e5;try{e.setStorageSync(t,{data:n,timestamp:Date.now(),expire:r})}catch(o){console.error("缓存地理编码数据失败",o)}}function c(t){try{var n=e.getStorageSync(t);if(!n)return null;var r=n.data,o=n.timestamp,i=n.expire;return Date.now()-o>i?null:r}catch(a){return console.error("获取缓存地理编码数据失败",a),null}}var l={reverseGeocoder:o,getNearbyPOI:i,geocoder:a,formatAddress:s,cacheGeocoderData:u,getCachedGeocoderData:c};t.default=l}).call(this,n("df3c")["default"])},"2d0a":function(e,t,n){"use strict";(function(e,n){function r(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return o(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){u=!0,a=e},f:function(){try{s||null==n.return||n.return()}finally{if(u)throw a}}}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i,a,s={trustTags:d("a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,ruby,rt,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video"),blockTags:d("address,article,aside,body,caption,center,cite,footer,header,html,nav,pre,section"),ignoreTags:d("area,base,canvas,embed,frame,head,iframe,input,link,map,meta,param,rp,script,source,style,textarea,title,track,wbr"),voidTags:d("area,base,br,col,circle,ellipse,embed,frame,hr,img,input,line,link,meta,param,path,polygon,rect,source,track,use,wbr"),entities:{lt:"<",gt:">",quot:'"',apos:"'",ensp:" ",emsp:" ",nbsp:" ",semi:";",ndash:"–",mdash:"—",middot:"·",lsquo:"‘",rsquo:"’",ldquo:"“",rdquo:"”",bull:"•",hellip:"…",larr:"←",uarr:"↑",rarr:"→",darr:"↓"},tagStyle:{address:"font-style:italic",big:"display:inline;font-size:1.2em",caption:"display:table-caption;text-align:center",center:"text-align:center",cite:"font-style:italic",dd:"margin-left:40px",mark:"background-color:yellow",pre:"font-family:monospace;white-space:pre",s:"text-decoration:line-through",small:"display:inline;font-size:0.8em",strike:"text-decoration:line-through",u:"text-decoration:underline"},svgDict:{animatetransform:"animateTransform",lineargradient:"linearGradient",viewbox:"viewBox",attributename:"attributeName",repeatcount:"repeatCount",repeatdur:"repeatDur",foreignobject:"foreignObject"}},u={};if(e.canIUse("getWindowInfo"))i=e.getWindowInfo().windowWidth,a=e.getDeviceInfo().system;else{var c=e.getSystemInfoSync();i=c.windowWidth,a=c.system}var l=d(" ,\r,\n,\t,\f"),f=0;function d(e){for(var t=Object.create(null),n=e.split(","),r=n.length;r--;)t[n[r]]=!0;return t}function h(e,t){var n=e.indexOf("&");while(-1!==n){var r=e.indexOf(";",n+3),o=void 0;if(-1===r)break;"#"===e[n+1]?(o=parseInt(("x"===e[n+2]?"0":"")+e.substring(n+2,r)),isNaN(o)||(e=e.substr(0,n)+String.fromCharCode(o)+e.substr(r+1))):(o=e.substring(n+1,r),(s.entities[o]||"amp"===o&&t)&&(e=e.substr(0,n)+(s.entities[o]||"&")+e.substr(r+1))),n=e.indexOf("&",n+1)}return e}function p(e){for(var t=e.length-1,n=t;n>=-1;n--)(-1===n||e[n].c||!e[n].name||"div"!==e[n].name&&"p"!==e[n].name&&"h"!==e[n].name[0]||(e[n].attrs.style||"").includes("inline"))&&(t-n>=5&&e.splice(n+1,t-n,{name:"div",attrs:{},children:e.slice(n+1,t+1)}),t=n-1)}function g(e){this.options=e||{},this.tagStyle=Object.assign({},s.tagStyle,this.options.tagStyle),this.imgList=e.imgList||[],this.imgList._unloadimgs=0,this.plugins=e.plugins||[],this.attrs=Object.create(null),this.stack=[],this.nodes=[],this.pre=(this.options.containerStyle||"").includes("white-space")&&this.options.containerStyle.includes("pre")?2:0}function v(e){this.handler=e}g.prototype.parse=function(e){for(var t=this.plugins.length;t--;)this.plugins[t].onUpdate&&(e=this.plugins[t].onUpdate(e,s)||e);new v(this).parse(e);while(this.stack.length)this.popNode();return this.nodes.length>50&&p(this.nodes),this.nodes},g.prototype.expose=function(){for(var e=this.stack.length;e--;){var t=this.stack[e];if(t.c||"a"===t.name||"video"===t.name||"audio"===t.name)return;t.c=1}},g.prototype.hook=function(e){for(var t=this.plugins.length;t--;)if(this.plugins[t].onParse&&!1===this.plugins[t].onParse(e,this))return!1;return!0},g.prototype.getUrl=function(e){var t=this.options.domain;return"/"===e[0]?"/"===e[1]?e=(t?t.split("://")[0]:"http")+":"+e:t&&(e=t+e):e.includes("data:")||e.includes("://")||t&&(e=t+"/"+e),e},g.prototype.parseStyle=function(e){var t=e.attrs,n=(this.tagStyle[e.name]||"").split(";").concat((t.style||"").split(";")),r={},o="";t.id&&!this.xml&&(this.options.useAnchor?this.expose():"img"!==e.name&&"a"!==e.name&&"video"!==e.name&&"audio"!==e.name&&(t.id=void 0)),t.width&&(r.width=parseFloat(t.width)+(t.width.includes("%")?"%":"px"),t.width=void 0),t.height&&(r.height=parseFloat(t.height)+(t.height.includes("%")?"%":"px"),t.height=void 0);for(var a=0,s=n.length;a<s;a++){var u=n[a].split(":");if(!(u.length<2)){var c=u.shift().trim().toLowerCase(),f=u.join(":").trim();if("-"===f[0]&&f.lastIndexOf("-")>0||f.includes("safe"))o+=";".concat(c,":").concat(f);else if(!r[c]||f.includes("import")||!r[c].includes("import")){if(f.includes("url")){var d=f.indexOf("(")+1;if(d){while('"'===f[d]||"'"===f[d]||l[f[d]])d++;f=f.substr(0,d)+this.getUrl(f.substr(d))}}else f.includes("rpx")&&(f=f.replace(/[0-9.]+\s*rpx/g,(function(e){return parseFloat(e)*i/750+"px"})));r[c]=f}}}return e.attrs.style=o,r},g.prototype.onTagName=function(e){this.tagName=this.xml?e:e.toLowerCase(),"svg"===this.tagName&&(this.xml=(this.xml||0)+1,s.ignoreTags.style=void 0)},g.prototype.onAttrName=function(e){e=this.xml?e:e.toLowerCase(),"data-"===e.substr(0,5)?"data-src"!==e||this.attrs.src?"img"===this.tagName||"a"===this.tagName?this.attrName=e:this.attrName=void 0:this.attrName="src":(this.attrName=e,this.attrs[e]="T")},g.prototype.onAttrVal=function(e){var t=this.attrName||"";"style"===t||"href"===t?this.attrs[t]=h(e,!0):t.includes("src")?this.attrs[t]=this.getUrl(h(e,!0)):t&&(this.attrs[t]=e)},g.prototype.onOpenTag=function(e){var t=Object.create(null);t.name=this.tagName,t.attrs=this.attrs,this.options.nodes.length&&(t.type="node"),this.attrs=Object.create(null);var n=t.attrs,r=this.stack[this.stack.length-1],o=r?r.children:this.nodes,a=this.xml?e:s.voidTags[t.name];if(u[t.name]&&(n.class=u[t.name]+(n.class?" "+n.class:"")),"embed"===t.name){var c=n.src||"";c.includes(".mp4")||c.includes(".3gp")||c.includes(".m3u8")||(n.type||"").includes("video")?t.name="video":(c.includes(".mp3")||c.includes(".wav")||c.includes(".aac")||c.includes(".m4a")||(n.type||"").includes("audio"))&&(t.name="audio"),n.autostart&&(n.autoplay="T"),n.controls="T"}if("video"!==t.name&&"audio"!==t.name||("video"!==t.name||n.id||(n.id="v"+f++),n.controls||n.autoplay||(n.controls="T"),t.src=[],n.src&&(t.src.push(n.src),n.src=void 0),this.expose()),a){if(!this.hook(t)||s.ignoreTags[t.name])return void("base"!==t.name||this.options.domain?"source"===t.name&&r&&("video"===r.name||"audio"===r.name)&&n.src&&r.src.push(n.src):this.options.domain=n.href);var l=this.parseStyle(t);if("img"===t.name){if(n.src&&(n.src.includes("webp")&&(t.webp="T"),n.src.includes("data:")&&"all"!==this.options.previewImg&&!n["original-src"]&&(n.ignore="T"),!n.ignore||t.webp||n.src.includes("cloud://"))){for(var d=this.stack.length;d--;){var h=this.stack[d];"a"===h.name&&(t.a=h.attrs),"table"!==h.name||t.webp||n.src.includes("cloud://")||(!l.display||l.display.includes("inline")?t.t="inline-block":t.t=l.display,l.display=void 0);var p=h.attrs.style||"";if(!p.includes("flex:")||p.includes("flex:0")||p.includes("flex: 0")||l.width&&!(parseInt(l.width)>100))if(p.includes("flex")&&"100%"===l.width)for(var g=d+1;g<this.stack.length;g++){var v=this.stack[g].attrs.style||"";if(!v.includes(";width")&&!v.includes(" width")&&0!==v.indexOf("width")){l.width="";break}}else p.includes("inline-block")&&(l.width&&"%"===l.width[l.width.length-1]?(h.attrs.style+=";max-width:"+l.width,l.width=""):h.attrs.style+=";max-width:100%");else{l.width="100% !important",l.height="";for(var m=d+1;m<this.stack.length;m++)this.stack[m].attrs.style=(this.stack[m].attrs.style||"").replace("inline-","")}h.c=1}n.i=this.imgList.length.toString();var y=n["original-src"]||n.src;if(this.imgList.includes(y)){var _=y.indexOf("://");if(-1!==_){_+=3;for(var b=y.substr(0,_);_<y.length;_++){if("/"===y[_])break;b+=Math.random()>.5?y[_].toUpperCase():y[_]}b+=y.substr(_),y=b}}this.imgList.push(y),t.t||(this.imgList._unloadimgs+=1)}"inline"===l.display&&(l.display=""),n.ignore&&(l["max-width"]=l["max-width"]||"100%",n.style+=";-webkit-touch-callout:none"),parseInt(l.width)>i&&(l.height=void 0),isNaN(parseInt(l.width))||(t.w="T"),!isNaN(parseInt(l.height))&&(!l.height.includes("%")||r&&(r.attrs.style||"").includes("height"))&&(t.h="T"),t.w&&t.h&&l["object-fit"]&&("contain"===l["object-fit"]?t.m="aspectFit":"cover"===l["object-fit"]&&(t.m="aspectFill"))}else if("svg"===t.name)return o.push(t),this.stack.push(t),void this.popNode();for(var w in l)l[w]&&(n.style+=";".concat(w,":").concat(l[w].replace(" !important","")));n.style=n.style.substr(1)||void 0}else("pre"===t.name||(n.style||"").includes("white-space")&&n.style.includes("pre"))&&2!==this.pre&&(this.pre=t.pre=1),t.children=[],this.stack.push(t);o.push(t)},g.prototype.onCloseTag=function(e){var t;for(e=this.xml?e:e.toLowerCase(),t=this.stack.length;t--;)if(this.stack[t].name===e)break;if(-1!==t)while(this.stack.length>t)this.popNode();else if("p"===e||"br"===e){var n=this.stack.length?this.stack[this.stack.length-1].children:this.nodes;n.push({name:e,attrs:{class:u[e]||"",style:this.tagStyle[e]||""}})}},g.prototype.popNode=function(){var t=this.stack.pop(),o=t.attrs,a=t.children,u=this.stack[this.stack.length-1],c=u?u.children:this.nodes;if(!this.hook(t)||s.ignoreTags[t.name])return"title"===t.name&&a.length&&"text"===a[0].type&&this.options.setTitle&&e.setNavigationBarTitle({title:a[0].text}),void c.pop();if(t.pre&&2!==this.pre){this.pre=t.pre=void 0;for(var l=this.stack.length;l--;)this.stack[l].pre&&(this.pre=1)}var f={};if("svg"===t.name){if(this.xml>1)return void this.xml--;var d="",h=o.style;return o.style="",o.xmlns="http://www.w3.org/2000/svg",function e(t){if("text"!==t.type){var n=s.svgDict[t.name]||t.name;if("foreignObject"===n){var o,i=r(t.children||[]);try{for(i.s();!(o=i.n()).done;){var a=o.value;if(a.attrs&&!a.attrs.xmlns){a.attrs.xmlns="http://www.w3.org/1999/xhtml";break}}}catch(f){i.e(f)}finally{i.f()}}for(var u in d+="<"+n,t.attrs){var c=t.attrs[u];c&&(d+=" ".concat(s.svgDict[u]||u,'="').concat(c.replace(/"/g,""),'"'))}if(t.children){d+=">";for(var l=0;l<t.children.length;l++)e(t.children[l]);d+="</"+n+">"}else d+="/>"}else d+=t.text}(t),t.name="img",t.attrs={src:"data:image/svg+xml;utf8,"+d.replace(/#/g,"%23"),style:h,ignore:"T"},t.children=void 0,this.xml=!1,void(s.ignoreTags.style=!0)}if(o.align&&("table"===t.name?"center"===o.align?f["margin-inline-start"]=f["margin-inline-end"]="auto":f.float=o.align:f["text-align"]=o.align,o.align=void 0),o.dir&&(f.direction=o.dir,o.dir=void 0),"font"===t.name&&(o.color&&(f.color=o.color,o.color=void 0),o.face&&(f["font-family"]=o.face,o.face=void 0),o.size)){var g=parseInt(o.size);isNaN(g)||(g<1?g=1:g>7&&(g=7),f["font-size"]=["x-small","small","medium","large","x-large","xx-large","xxx-large"][g-1]),o.size=void 0}if((o.class||"").includes("align-center")&&(f["text-align"]="center"),Object.assign(f,this.parseStyle(t)),"table"!==t.name&&parseInt(f.width)>i&&(f["max-width"]="100%",f["box-sizing"]="border-box"),s.blockTags[t.name]?t.name="div":s.trustTags[t.name]||this.xml||(t.name="span"),"a"===t.name||"ad"===t.name)this.expose();else if("video"===t.name)(f.height||"").includes("auto")&&(f.height=void 0);else if("ul"!==t.name&&"ol"!==t.name||!t.c)if("table"===t.name){var v=parseFloat(o.cellpadding),m=parseFloat(o.cellspacing),y=parseFloat(o.border),_=f["border-color"],b=f["border-style"];if(t.c&&(isNaN(v)&&(v=2),isNaN(m)&&(m=2)),y&&(o.style+=";border:".concat(y,"px ").concat(b||"solid"," ").concat(_||"gray")),t.flag&&t.c){f.display="grid","collapse"===f["border-collapse"]&&(f["border-collapse"]=void 0,m=0),m?(f["grid-gap"]=m+"px",f.padding=m+"px"):y&&(o.style+=";border-left:0;border-top:0");var w=[],k=[],x=[],S={};(function e(t){for(var n=0;n<t.length;n++)if("tr"===t[n].name)k.push(t[n]);else if("colgroup"===t[n].name){var o,i=1,a=r(t[n].children||[]);try{for(a.s();!(o=a.n()).done;){var s=o.value;if("col"===s.name){var u=s.attrs.style||"",c=u.indexOf("width")?u.indexOf(";width"):0;if(-1!==c){var l=u.indexOf(";",c+6);-1===l&&(l=u.length),w[i]=u.substring(c?c+7:6,l)}i+=1}}}catch(f){a.e(f)}finally{a.f()}}else e(t[n].children||[])})(a);for(var O=1;O<=k.length;O++){for(var T=1,P=0;P<k[O-1].children.length;P++){var C=k[O-1].children[P];if("td"===C.name||"th"===C.name){while(S[O+"."+T])T++;var A=C.attrs.style||"",I=A.indexOf("width")?A.indexOf(";width"):0;if(-1!==I){var E=A.indexOf(";",I+6);-1===E&&(E=A.length),C.attrs.colspan||(w[T]=A.substring(I?I+7:6,E)),A=A.substr(0,I)+A.substr(E)}if(A+=";display:flex",I=A.indexOf("vertical-align"),-1!==I){var D=A.substr(I+15,10);D.includes("middle")?A+=";align-items:center":D.includes("bottom")&&(A+=";align-items:flex-end")}else A+=";align-items:center";if(I=A.indexOf("text-align"),-1!==I){var j=A.substr(I+11,10);j.includes("center")?A+=";justify-content: center":j.includes("right")&&(A+=";justify-content: right")}if(A=(y?";border:".concat(y,"px ").concat(b||"solid"," ").concat(_||"gray")+(m?"":";border-right:0;border-bottom:0"):"")+(v?";padding:".concat(v,"px"):"")+";"+A,C.attrs.colspan&&(A+=";grid-column-start:".concat(T,";grid-column-end:").concat(T+parseInt(C.attrs.colspan)),C.attrs.rowspan||(A+=";grid-row-start:".concat(O,";grid-row-end:").concat(O+1)),T+=parseInt(C.attrs.colspan)-1),C.attrs.rowspan){A+=";grid-row-start:".concat(O,";grid-row-end:").concat(O+parseInt(C.attrs.rowspan)),C.attrs.colspan||(A+=";grid-column-start:".concat(T,";grid-column-end:").concat(T+1));for(var L=1;L<C.attrs.rowspan;L++)for(var N=0;N<(C.attrs.colspan||1);N++)S[O+L+"."+(T-N)]=1}A&&(C.attrs.style=A),x.push(C),T++}}if(1===O){for(var M="",R=1;R<T;R++)M+=(w[R]?w[R]:"auto")+" ";f["grid-template-columns"]=M}}t.children=x}else t.c&&(f.display="table"),isNaN(m)||(f["border-spacing"]=m+"px"),(y||v)&&function e(t){for(var n=0;n<t.length;n++){var r=t[n];"th"===r.name||"td"===r.name?(y&&(r.attrs.style="border:".concat(y,"px ").concat(b||"solid"," ").concat(_||"gray",";").concat(r.attrs.style||"")),v&&(r.attrs.style="padding:".concat(v,"px;").concat(r.attrs.style||""))):r.children&&e(r.children)}}(a);if(this.options.scrollTable&&!(o.style||"").includes("inline")){var B=Object.assign({},t);t.name="div",t.attrs={style:"overflow:auto"},t.children=[B],o=B.attrs}}else if(("tbody"===t.name||"tr"===t.name)&&t.flag&&t.c)t.flag=void 0,function e(t){for(var n=0;n<t.length;n++)if("td"===t[n].name)for(var r=0,o=["color","background","background-color"];r<o.length;r++){var i=o[r];f[i]&&(t[n].attrs.style=i+":"+f[i]+";"+(t[n].attrs.style||""))}else e(t[n].children||[])}(a);else if("td"!==t.name&&"th"!==t.name||!o.colspan&&!o.rowspan)if("ruby"===t.name){t.name="span";for(var $=0;$<a.length-1;$++)"text"===a[$].type&&"rt"===a[$+1].name&&(a[$]={name:"div",attrs:{style:"display:inline-block;text-align:center"},children:[{name:"div",attrs:{style:"font-size:50%;"+(a[$+1].attrs.style||"")},children:a[$+1].children},a[$]]},a.splice($+1,1))}else t.c&&function(e){e.c=2;for(var t=e.children.length;t--;){var n=e.children[t];n.c&&"table"!==n.name||(e.c=1)}}(t);else for(var U=this.stack.length;U--;)"table"!==this.stack[U].name&&"tbody"!==this.stack[U].name&&"tr"!==this.stack[U].name||(this.stack[U].flag=1);else{var F={a:"lower-alpha",A:"upper-alpha",i:"lower-roman",I:"upper-roman"};F[o.type]&&(o.style+=";list-style-type:"+F[o.type],o.type=void 0);for(var q=a.length;q--;)"li"===a[q].name&&(a[q].c=1)}if((f.display||"").includes("flex")&&!t.c)for(var H=a.length;H--;){var z=a[H];z.f&&(z.attrs.style=(z.attrs.style||"")+z.f,z.f=void 0)}var V=u&&((u.attrs.style||"").includes("flex")||(u.attrs.style||"").includes("grid"))&&!(t.c&&n.getNFCAdapter);for(var K in V&&(t.f=";max-width:100%"),a.length>=50&&t.c&&!(f.display||"").includes("flex")&&p(a),f)if(f[K]){var W=";".concat(K,":").concat(f[K].replace(" !important",""));V&&(K.includes("flex")&&"flex-direction"!==K||"align-self"===K||K.includes("grid")||"-"===f[K][0]||K.includes("width")&&W.includes("%"))?(t.f+=W,"width"===K&&(o.style+=";width:100%")):o.style+=W}o.style=o.style.substr(1)||void 0},g.prototype.onText=function(t){if(!this.pre){for(var n,r="",o=0,i=t.length;o<i;o++)l[t[o]]?(" "!==r[r.length-1]&&(r+=" "),"\n"!==t[o]||n||(n=!0)):r+=t[o];if(" "===r&&n)return;t=r}var s=Object.create(null);if(s.type="text",s.text=h(t),this.hook(s)){"force"===this.options.selectable&&a.includes("iOS")&&!e.canIUse("rich-text.user-select")&&this.expose();var u=this.stack.length?this.stack[this.stack.length-1].children:this.nodes;u.push(s)}},v.prototype.parse=function(e){this.content=e||"",this.i=0,this.start=0,this.state=this.text;for(var t=this.content.length;-1!==this.i&&this.i<t;)this.state()},v.prototype.checkClose=function(e){var t="/"===this.content[this.i];return!!(">"===this.content[this.i]||t&&">"===this.content[this.i+1])&&(e&&this.handler[e](this.content.substring(this.start,this.i)),this.i+=t?2:1,this.start=this.i,this.handler.onOpenTag(t),"script"===this.handler.tagName?(this.i=this.content.indexOf("</",this.i),-1!==this.i&&(this.i+=2,this.start=this.i),this.state=this.endTag):this.state=this.text,!0)},v.prototype.text=function(){if(this.i=this.content.indexOf("<",this.i),-1!==this.i){var e=this.content[this.i+1];if(e>="a"&&e<="z"||e>="A"&&e<="Z")this.start!==this.i&&this.handler.onText(this.content.substring(this.start,this.i)),this.start=++this.i,this.state=this.tagName;else if("/"===e||"!"===e||"?"===e){this.start!==this.i&&this.handler.onText(this.content.substring(this.start,this.i));var t=this.content[this.i+2];if("/"===e&&(t>="a"&&t<="z"||t>="A"&&t<="Z"))return this.i+=2,this.start=this.i,void(this.state=this.endTag);var n="--\x3e";"!"===e&&"-"===this.content[this.i+2]&&"-"===this.content[this.i+3]||(n=">"),this.i=this.content.indexOf(n,this.i),-1!==this.i&&(this.i+=n.length,this.start=this.i)}else this.i++}else this.start<this.content.length&&this.handler.onText(this.content.substring(this.start,this.content.length))},v.prototype.tagName=function(){if(l[this.content[this.i]]){this.handler.onTagName(this.content.substring(this.start,this.i));while(l[this.content[++this.i]]);this.i<this.content.length&&!this.checkClose()&&(this.start=this.i,this.state=this.attrName)}else this.checkClose("onTagName")||this.i++},v.prototype.attrName=function(){var e=this.content[this.i];if(l[e]||"="===e){this.handler.onAttrName(this.content.substring(this.start,this.i));var t="="===e,n=this.content.length;while(++this.i<n)if(e=this.content[this.i],!l[e]){if(this.checkClose())return;if(t)return this.start=this.i,void(this.state=this.attrVal);if("="!==this.content[this.i])return this.start=this.i,void(this.state=this.attrName);t=!0}}else this.checkClose("onAttrName")||this.i++},v.prototype.attrVal=function(){var e=this.content[this.i],t=this.content.length;if('"'===e||"'"===e){if(this.start=++this.i,this.i=this.content.indexOf(e,this.i),-1===this.i)return;this.handler.onAttrVal(this.content.substring(this.start,this.i))}else for(;this.i<t;this.i++){if(l[this.content[this.i]]){this.handler.onAttrVal(this.content.substring(this.start,this.i));break}if(this.checkClose("onAttrVal"))return}while(l[this.content[++this.i]]);this.i<t&&!this.checkClose()&&(this.start=this.i,this.state=this.attrName)},v.prototype.endTag=function(){var e=this.content[this.i];if(l[e]||">"===e||"/"===e){if(this.handler.onCloseTag(this.content.substring(this.start,this.i)),">"!==e&&(this.i=this.content.indexOf(">",this.i),-1===this.i))return;this.start=++this.i,this.state=this.text}else this.i++};var m=g;t.default=m}).call(this,n("df3c")["default"],n("3223")["default"])},"30eb":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={debug:!1,isAdmin:!1,loginTypes:["weixin","username"],agreements:{serviceUrl:"/pages/info_pkg/user-guide",privacyUrl:"/pages/info_pkg/privacy",scope:["register","login","realNameVerify"]},appid:{weixin:{h5:"xxxxxx",web:"xxxxxx"}},passwordStrength:"weak",setPasswordAfterLogin:!1,loginSuccess:{type:"toast",options:{title:"登录成功",icon:"success",duration:2e3,background:"none"}}}},3223:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],o=["lanDebug","router","worklet"],i="undefined"!==typeof globalThis?globalThis:function(){return this}(),a=["w","x"].join(""),s=i[a],u=s.getLaunchOptionsSync?s.getLaunchOptionsSync():null;function c(e){return(!u||1154!==u.scene||!o.includes(e))&&(r.indexOf(e)>-1||"function"===typeof s[e])}i[a]=function(){var e={};for(var t in s)c(t)&&(e[t]=s[t]);return e}(),i[a].canIUse("getAppBaseInfo")||(i[a].getAppBaseInfo=i[a].getSystemInfoSync),i[a].canIUse("getWindowInfo")||(i[a].getWindowInfo=i[a].getSystemInfoSync),i[a].canIUse("getDeviceInfo")||(i[a].getDeviceInfo=i[a].getSystemInfoSync);var l=i[a];t.default=l},3240:function(e,t,n){"use strict";n.r(t),function(e){
/*!
 * Vue.js v2.6.11
 * (c) 2014-2024 Evan You
 * Released under the MIT License.
 */
var n=Object.freeze({});function r(e){return void 0===e||null===e}function o(e){return void 0!==e&&null!==e}function i(e){return!0===e}function a(e){return"string"===typeof e||"number"===typeof e||"symbol"===typeof e||"boolean"===typeof e}function s(e){return null!==e&&"object"===typeof e}var u=Object.prototype.toString;function c(e){return"[object Object]"===u.call(e)}function l(e){var t=parseFloat(String(e));return t>=0&&Math.floor(t)===t&&isFinite(e)}function f(e){return o(e)&&"function"===typeof e.then&&"function"===typeof e.catch}function d(e){return null==e?"":Array.isArray(e)||c(e)&&e.toString===u?JSON.stringify(e,null,2):String(e)}function h(e){var t=parseFloat(e);return isNaN(t)?e:t}function p(e,t){for(var n=Object.create(null),r=e.split(","),o=0;o<r.length;o++)n[r[o]]=!0;return t?function(e){return n[e.toLowerCase()]}:function(e){return n[e]}}p("slot,component",!0);var g=p("key,ref,slot,slot-scope,is");function v(e,t){if(e.length){var n=e.indexOf(t);if(n>-1)return e.splice(n,1)}}var m=Object.prototype.hasOwnProperty;function y(e,t){return m.call(e,t)}function _(e){var t=Object.create(null);return function(n){var r=t[n];return r||(t[n]=e(n))}}var b=/-(\w)/g,w=_((function(e){return e.replace(b,(function(e,t){return t?t.toUpperCase():""}))})),k=_((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})),x=/\B([A-Z])/g,S=_((function(e){return e.replace(x,"-$1").toLowerCase()}));var O=Function.prototype.bind?function(e,t){return e.bind(t)}:function(e,t){function n(n){var r=arguments.length;return r?r>1?e.apply(t,arguments):e.call(t,n):e.call(t)}return n._length=e.length,n};function T(e,t){t=t||0;var n=e.length-t,r=new Array(n);while(n--)r[n]=e[n+t];return r}function P(e,t){for(var n in t)e[n]=t[n];return e}function C(e){for(var t={},n=0;n<e.length;n++)e[n]&&P(t,e[n]);return t}function A(e,t,n){}var I=function(e,t,n){return!1},E=function(e){return e};function D(e,t){if(e===t)return!0;var n=s(e),r=s(t);if(!n||!r)return!n&&!r&&String(e)===String(t);try{var o=Array.isArray(e),i=Array.isArray(t);if(o&&i)return e.length===t.length&&e.every((function(e,n){return D(e,t[n])}));if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(o||i)return!1;var a=Object.keys(e),u=Object.keys(t);return a.length===u.length&&a.every((function(n){return D(e[n],t[n])}))}catch(c){return!1}}function j(e,t){for(var n=0;n<e.length;n++)if(D(e[n],t))return n;return-1}function L(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}var N=["component","directive","filter"],M=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch"],R={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:I,isReservedAttr:I,isUnknownElement:I,getTagNamespace:A,parsePlatformTagName:E,mustUseProp:I,async:!0,_lifecycleHooks:M},B=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function $(e){var t=(e+"").charCodeAt(0);return 36===t||95===t}function U(e,t,n,r){Object.defineProperty(e,t,{value:n,enumerable:!!r,writable:!0,configurable:!0})}var F=new RegExp("[^"+B.source+".$_\\d]");var q,H="__proto__"in{},z="undefined"!==typeof window,V="undefined"!==typeof WXEnvironment&&!!WXEnvironment.platform,K=V&&WXEnvironment.platform.toLowerCase(),W=z&&window.navigator&&window.navigator.userAgent.toLowerCase(),J=W&&/msie|trident/.test(W),G=(W&&W.indexOf("msie 9.0"),W&&W.indexOf("edge/")>0),Y=(W&&W.indexOf("android"),W&&/iphone|ipad|ipod|ios/.test(W)||"ios"===K),X=(W&&/chrome\/\d+/.test(W),W&&/phantomjs/.test(W),W&&W.match(/firefox\/(\d+)/),{}.watch);if(z)try{var Q={};Object.defineProperty(Q,"passive",{get:function(){}}),window.addEventListener("test-passive",null,Q)}catch(Rn){}var Z=function(){return void 0===q&&(q=!z&&!V&&"undefined"!==typeof e&&(e["process"]&&"server"===e["process"].env.VUE_ENV)),q},ee=z&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function te(e){return"function"===typeof e&&/native code/.test(e.toString())}var ne,re="undefined"!==typeof Symbol&&te(Symbol)&&"undefined"!==typeof Reflect&&te(Reflect.ownKeys);ne="undefined"!==typeof Set&&te(Set)?Set:function(){function e(){this.set=Object.create(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var oe=A,ie=0,ae=function(){this.id=ie++,this.subs=[]};function se(e){ae.SharedObject.targetStack.push(e),ae.SharedObject.target=e,ae.target=e}function ue(){ae.SharedObject.targetStack.pop(),ae.SharedObject.target=ae.SharedObject.targetStack[ae.SharedObject.targetStack.length-1],ae.target=ae.SharedObject.target}ae.prototype.addSub=function(e){this.subs.push(e)},ae.prototype.removeSub=function(e){v(this.subs,e)},ae.prototype.depend=function(){ae.SharedObject.target&&ae.SharedObject.target.addDep(this)},ae.prototype.notify=function(){var e=this.subs.slice();for(var t=0,n=e.length;t<n;t++)e[t].update()},ae.SharedObject={},ae.SharedObject.target=null,ae.SharedObject.targetStack=[];var ce=function(e,t,n,r,o,i,a,s){this.tag=e,this.data=t,this.children=n,this.text=r,this.elm=o,this.ns=void 0,this.context=i,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=t&&t.key,this.componentOptions=a,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=s,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1},le={child:{configurable:!0}};le.child.get=function(){return this.componentInstance},Object.defineProperties(ce.prototype,le);var fe=function(e){void 0===e&&(e="");var t=new ce;return t.text=e,t.isComment=!0,t};function de(e){return new ce(void 0,void 0,void 0,String(e))}var he=Array.prototype,pe=Object.create(he);["push","pop","shift","unshift","splice","sort","reverse"].forEach((function(e){var t=he[e];U(pe,e,(function(){var n=[],r=arguments.length;while(r--)n[r]=arguments[r];var o,i=t.apply(this,n),a=this.__ob__;switch(e){case"push":case"unshift":o=n;break;case"splice":o=n.slice(2);break}return o&&a.observeArray(o),a.dep.notify(),i}))}));var ge=Object.getOwnPropertyNames(pe),ve=!0;function me(e){ve=e}var ye=function(e){this.value=e,this.dep=new ae,this.vmCount=0,U(e,"__ob__",this),Array.isArray(e)?(H?e.push!==e.__proto__.push?_e(e,pe,ge):function(e,t){e.__proto__=t}(e,pe):_e(e,pe,ge),this.observeArray(e)):this.walk(e)};function _e(e,t,n){for(var r=0,o=n.length;r<o;r++){var i=n[r];U(e,i,t[i])}}function be(e,t){var n;if(s(e)&&!(e instanceof ce))return y(e,"__ob__")&&e.__ob__ instanceof ye?n=e.__ob__:!ve||Z()||!Array.isArray(e)&&!c(e)||!Object.isExtensible(e)||e._isVue||e.__v_isMPComponent||(n=new ye(e)),t&&n&&n.vmCount++,n}function we(e,t,n,r,o){var i=new ae,a=Object.getOwnPropertyDescriptor(e,t);if(!a||!1!==a.configurable){var s=a&&a.get,u=a&&a.set;s&&!u||2!==arguments.length||(n=e[t]);var c=!o&&be(n);Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){var t=s?s.call(e):n;return ae.SharedObject.target&&(i.depend(),c&&(c.dep.depend(),Array.isArray(t)&&Se(t))),t},set:function(t){var r=s?s.call(e):n;t===r||t!==t&&r!==r||s&&!u||(u?u.call(e,t):n=t,c=!o&&be(t),i.notify())}})}}function ke(e,t,n){if(Array.isArray(e)&&l(t))return e.length=Math.max(e.length,t),e.splice(t,1,n),n;if(t in e&&!(t in Object.prototype))return e[t]=n,n;var r=e.__ob__;return e._isVue||r&&r.vmCount?n:r?(we(r.value,t,n),r.dep.notify(),n):(e[t]=n,n)}function xe(e,t){if(Array.isArray(e)&&l(t))e.splice(t,1);else{var n=e.__ob__;e._isVue||n&&n.vmCount||y(e,t)&&(delete e[t],n&&n.dep.notify())}}function Se(e){for(var t=void 0,n=0,r=e.length;n<r;n++)t=e[n],t&&t.__ob__&&t.__ob__.dep.depend(),Array.isArray(t)&&Se(t)}ye.prototype.walk=function(e){for(var t=Object.keys(e),n=0;n<t.length;n++)we(e,t[n])},ye.prototype.observeArray=function(e){for(var t=0,n=e.length;t<n;t++)be(e[t])};var Oe=R.optionMergeStrategies;function Te(e,t){if(!t)return e;for(var n,r,o,i=re?Reflect.ownKeys(t):Object.keys(t),a=0;a<i.length;a++)n=i[a],"__ob__"!==n&&(r=e[n],o=t[n],y(e,n)?r!==o&&c(r)&&c(o)&&Te(r,o):ke(e,n,o));return e}function Pe(e,t,n){return n?function(){var r="function"===typeof t?t.call(n,n):t,o="function"===typeof e?e.call(n,n):e;return r?Te(r,o):o}:t?e?function(){return Te("function"===typeof t?t.call(this,this):t,"function"===typeof e?e.call(this,this):e)}:t:e}function Ce(e,t){var n=t?e?e.concat(t):Array.isArray(t)?t:[t]:e;return n?function(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}function Ae(e,t,n,r){var o=Object.create(e||null);return t?P(o,t):o}Oe.data=function(e,t,n){return n?Pe(e,t,n):t&&"function"!==typeof t?e:Pe(e,t)},M.forEach((function(e){Oe[e]=Ce})),N.forEach((function(e){Oe[e+"s"]=Ae})),Oe.watch=function(e,t,n,r){if(e===X&&(e=void 0),t===X&&(t=void 0),!t)return Object.create(e||null);if(!e)return t;var o={};for(var i in P(o,e),t){var a=o[i],s=t[i];a&&!Array.isArray(a)&&(a=[a]),o[i]=a?a.concat(s):Array.isArray(s)?s:[s]}return o},Oe.props=Oe.methods=Oe.inject=Oe.computed=function(e,t,n,r){if(!e)return t;var o=Object.create(null);return P(o,e),t&&P(o,t),o},Oe.provide=Pe;var Ie=function(e,t){return void 0===t?e:t};function Ee(e,t,n){if("function"===typeof t&&(t=t.options),function(e,t){var n=e.props;if(n){var r,o,i,a={};if(Array.isArray(n)){r=n.length;while(r--)o=n[r],"string"===typeof o&&(i=w(o),a[i]={type:null})}else if(c(n))for(var s in n)o=n[s],i=w(s),a[i]=c(o)?o:{type:o};else 0;e.props=a}}(t),function(e,t){var n=e.inject;if(n){var r=e.inject={};if(Array.isArray(n))for(var o=0;o<n.length;o++)r[n[o]]={from:n[o]};else if(c(n))for(var i in n){var a=n[i];r[i]=c(a)?P({from:i},a):{from:a}}else 0}}(t),function(e){var t=e.directives;if(t)for(var n in t){var r=t[n];"function"===typeof r&&(t[n]={bind:r,update:r})}}(t),!t._base&&(t.extends&&(e=Ee(e,t.extends,n)),t.mixins))for(var r=0,o=t.mixins.length;r<o;r++)e=Ee(e,t.mixins[r],n);var i,a={};for(i in e)s(i);for(i in t)y(e,i)||s(i);function s(r){var o=Oe[r]||Ie;a[r]=o(e[r],t[r],n,r)}return a}function De(e,t,n,r){if("string"===typeof n){var o=e[t];if(y(o,n))return o[n];var i=w(n);if(y(o,i))return o[i];var a=k(i);if(y(o,a))return o[a];var s=o[n]||o[i]||o[a];return s}}function je(e,t,n,r){var o=t[e],i=!y(n,e),a=n[e],s=Me(Boolean,o.type);if(s>-1)if(i&&!y(o,"default"))a=!1;else if(""===a||a===S(e)){var u=Me(String,o.type);(u<0||s<u)&&(a=!0)}if(void 0===a){a=function(e,t,n){if(!y(t,"default"))return;var r=t.default;0;if(e&&e.$options.propsData&&void 0===e.$options.propsData[n]&&void 0!==e._props[n])return e._props[n];return"function"===typeof r&&"Function"!==Le(t.type)?r.call(e):r}(r,o,e);var c=ve;me(!0),be(a),me(c)}return a}function Le(e){var t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:""}function Ne(e,t){return Le(e)===Le(t)}function Me(e,t){if(!Array.isArray(t))return Ne(t,e)?0:-1;for(var n=0,r=t.length;n<r;n++)if(Ne(t[n],e))return n;return-1}function Re(e,t,n){se();try{if(t){var r=t;while(r=r.$parent){var o=r.$options.errorCaptured;if(o)for(var i=0;i<o.length;i++)try{var a=!1===o[i].call(r,e,t,n);if(a)return}catch(Rn){$e(Rn,r,"errorCaptured hook")}}}$e(e,t,n)}finally{ue()}}function Be(e,t,n,r,o){var i;try{i=n?e.apply(t,n):e.call(t),i&&!i._isVue&&f(i)&&!i._handled&&(i.catch((function(e){return Re(e,r,o+" (Promise/async)")})),i._handled=!0)}catch(Rn){Re(Rn,r,o)}return i}function $e(e,t,n){if(R.errorHandler)try{return R.errorHandler.call(null,e,t,n)}catch(Rn){Rn!==e&&Ue(Rn,null,"config.errorHandler")}Ue(e,t,n)}function Ue(e,t,n){if(!z&&!V||"undefined"===typeof console)throw e;console.error(e)}var Fe,qe=[],He=!1;function ze(){He=!1;var e=qe.slice(0);qe.length=0;for(var t=0;t<e.length;t++)e[t]()}if("undefined"!==typeof Promise&&te(Promise)){var Ve=Promise.resolve();Fe=function(){Ve.then(ze),Y&&setTimeout(A)}}else if(J||"undefined"===typeof MutationObserver||!te(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())Fe="undefined"!==typeof setImmediate&&te(setImmediate)?function(){setImmediate(ze)}:function(){setTimeout(ze,0)};else{var Ke=1,We=new MutationObserver(ze),Je=document.createTextNode(String(Ke));We.observe(Je,{characterData:!0}),Fe=function(){Ke=(Ke+1)%2,Je.data=String(Ke)}}function Ge(e,t){var n;if(qe.push((function(){if(e)try{e.call(t)}catch(Rn){Re(Rn,t,"nextTick")}else n&&n(t)})),He||(He=!0,Fe()),!e&&"undefined"!==typeof Promise)return new Promise((function(e){n=e}))}var Ye=new ne;function Xe(e){(function e(t,n){var r,o,i=Array.isArray(t);if(!i&&!s(t)||Object.isFrozen(t)||t instanceof ce)return;if(t.__ob__){var a=t.__ob__.dep.id;if(n.has(a))return;n.add(a)}if(i){r=t.length;while(r--)e(t[r],n)}else{o=Object.keys(t),r=o.length;while(r--)e(t[o[r]],n)}})(e,Ye),Ye.clear()}var Qe=_((function(e){var t="&"===e.charAt(0);e=t?e.slice(1):e;var n="~"===e.charAt(0);e=n?e.slice(1):e;var r="!"===e.charAt(0);return e=r?e.slice(1):e,{name:e,once:n,capture:r,passive:t}}));function Ze(e,t){function n(){var e=arguments,r=n.fns;if(!Array.isArray(r))return Be(r,null,arguments,t,"v-on handler");for(var o=r.slice(),i=0;i<o.length;i++)Be(o[i],null,e,t,"v-on handler")}return n.fns=e,n}function et(e,t,n,i){var a=t.options.mpOptions&&t.options.mpOptions.properties;if(r(a))return n;var s=t.options.mpOptions.externalClasses||[],u=e.attrs,c=e.props;if(o(u)||o(c))for(var l in a){var f=S(l),d=tt(n,c,l,f,!0)||tt(n,u,l,f,!1);d&&n[l]&&-1!==s.indexOf(f)&&i[w(n[l])]&&(n[l]=i[w(n[l])])}return n}function tt(e,t,n,r,i){if(o(t)){if(y(t,n))return e[n]=t[n],i||delete t[n],!0;if(y(t,r))return e[n]=t[r],i||delete t[r],!0}return!1}function nt(e){return a(e)?[de(e)]:Array.isArray(e)?function e(t,n){var s,u,c,l,f=[];for(s=0;s<t.length;s++)u=t[s],r(u)||"boolean"===typeof u||(c=f.length-1,l=f[c],Array.isArray(u)?u.length>0&&(u=e(u,(n||"")+"_"+s),rt(u[0])&&rt(l)&&(f[c]=de(l.text+u[0].text),u.shift()),f.push.apply(f,u)):a(u)?rt(l)?f[c]=de(l.text+u):""!==u&&f.push(de(u)):rt(u)&&rt(l)?f[c]=de(l.text+u.text):(i(t._isVList)&&o(u.tag)&&r(u.key)&&o(n)&&(u.key="__vlist"+n+"_"+s+"__"),f.push(u)));return f}(e):void 0}function rt(e){return o(e)&&o(e.text)&&function(e){return!1===e}(e.isComment)}function ot(e){var t=e.$options.provide;t&&(e._provided="function"===typeof t?t.call(e):t)}function it(e){var t=at(e.$options.inject,e);t&&(me(!1),Object.keys(t).forEach((function(n){we(e,n,t[n])})),me(!0))}function at(e,t){if(e){for(var n=Object.create(null),r=re?Reflect.ownKeys(e):Object.keys(e),o=0;o<r.length;o++){var i=r[o];if("__ob__"!==i){var a=e[i].from,s=t;while(s){if(s._provided&&y(s._provided,a)){n[i]=s._provided[a];break}s=s.$parent}if(!s)if("default"in e[i]){var u=e[i].default;n[i]="function"===typeof u?u.call(t):u}else 0}}return n}}function st(e,t){if(!e||!e.length)return{};for(var n={},r=0,o=e.length;r<o;r++){var i=e[r],a=i.data;if(a&&a.attrs&&a.attrs.slot&&delete a.attrs.slot,i.context!==t&&i.fnContext!==t||!a||null==a.slot)i.asyncMeta&&i.asyncMeta.data&&"page"===i.asyncMeta.data.slot?(n["page"]||(n["page"]=[])).push(i):(n.default||(n.default=[])).push(i);else{var s=a.slot,u=n[s]||(n[s]=[]);"template"===i.tag?u.push.apply(u,i.children||[]):u.push(i)}}for(var c in n)n[c].every(ut)&&delete n[c];return n}function ut(e){return e.isComment&&!e.asyncFactory||" "===e.text}function ct(e,t,r){var o,i=Object.keys(t).length>0,a=e?!!e.$stable:!i,s=e&&e.$key;if(e){if(e._normalized)return e._normalized;if(a&&r&&r!==n&&s===r.$key&&!i&&!r.$hasNormal)return r;for(var u in o={},e)e[u]&&"$"!==u[0]&&(o[u]=lt(t,u,e[u]))}else o={};for(var c in t)c in o||(o[c]=ft(t,c));return e&&Object.isExtensible(e)&&(e._normalized=o),U(o,"$stable",a),U(o,"$key",s),U(o,"$hasNormal",i),o}function lt(e,t,n){var r=function(){var e=arguments.length?n.apply(null,arguments):n({});return e=e&&"object"===typeof e&&!Array.isArray(e)?[e]:nt(e),e&&(0===e.length||1===e.length&&e[0].isComment)?void 0:e};return n.proxy&&Object.defineProperty(e,t,{get:r,enumerable:!0,configurable:!0}),r}function ft(e,t){return function(){return e[t]}}function dt(e,t){var n,r,i,a,u;if(Array.isArray(e)||"string"===typeof e)for(n=new Array(e.length),r=0,i=e.length;r<i;r++)n[r]=t(e[r],r,r,r);else if("number"===typeof e)for(n=new Array(e),r=0;r<e;r++)n[r]=t(r+1,r,r,r);else if(s(e))if(re&&e[Symbol.iterator]){n=[];var c=e[Symbol.iterator](),l=c.next();while(!l.done)n.push(t(l.value,n.length,r,r++)),l=c.next()}else for(a=Object.keys(e),n=new Array(a.length),r=0,i=a.length;r<i;r++)u=a[r],n[r]=t(e[u],u,r,r);return o(n)||(n=[]),n._isVList=!0,n}function ht(e,t,n,r){var o,i=this.$scopedSlots[e];i?(n=n||{},r&&(n=P(P({},r),n)),o=i(n,this,n._i)||t):o=this.$slots[e]||t;var a=n&&n.slot;return a?this.$createElement("template",{slot:a},o):o}function pt(e){return De(this.$options,"filters",e)||E}function gt(e,t){return Array.isArray(e)?-1===e.indexOf(t):e!==t}function vt(e,t,n,r,o){var i=R.keyCodes[t]||n;return o&&r&&!R.keyCodes[t]?gt(o,r):i?gt(i,e):r?S(r)!==t:void 0}function mt(e,t,n,r,o){if(n)if(s(n)){var i;Array.isArray(n)&&(n=C(n));var a=function(a){if("class"===a||"style"===a||g(a))i=e;else{var s=e.attrs&&e.attrs.type;i=r||R.mustUseProp(t,s,a)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}var u=w(a),c=S(a);if(!(u in i)&&!(c in i)&&(i[a]=n[a],o)){var l=e.on||(e.on={});l["update:"+a]=function(e){n[a]=e}}};for(var u in n)a(u)}else;return e}function yt(e,t){var n=this._staticTrees||(this._staticTrees=[]),r=n[e];return r&&!t||(r=n[e]=this.$options.staticRenderFns[e].call(this._renderProxy,null,this),bt(r,"__static__"+e,!1)),r}function _t(e,t,n){return bt(e,"__once__"+t+(n?"_"+n:""),!0),e}function bt(e,t,n){if(Array.isArray(e))for(var r=0;r<e.length;r++)e[r]&&"string"!==typeof e[r]&&wt(e[r],t+"_"+r,n);else wt(e,t,n)}function wt(e,t,n){e.isStatic=!0,e.key=t,e.isOnce=n}function kt(e,t){if(t)if(c(t)){var n=e.on=e.on?P({},e.on):{};for(var r in t){var o=n[r],i=t[r];n[r]=o?[].concat(o,i):i}}else;return e}function xt(e,t,n,r){t=t||{$stable:!n};for(var o=0;o<e.length;o++){var i=e[o];Array.isArray(i)?xt(i,t,n):i&&(i.proxy&&(i.fn.proxy=!0),t[i.key]=i.fn)}return r&&(t.$key=r),t}function St(e,t){for(var n=0;n<t.length;n+=2){var r=t[n];"string"===typeof r&&r&&(e[t[n]]=t[n+1])}return e}function Ot(e,t){return"string"===typeof e?t+e:e}function Tt(e){e._o=_t,e._n=h,e._s=d,e._l=dt,e._t=ht,e._q=D,e._i=j,e._m=yt,e._f=pt,e._k=vt,e._b=mt,e._v=de,e._e=fe,e._u=xt,e._g=kt,e._d=St,e._p=Ot}function Pt(e,t,r,o,a){var s,u=this,c=a.options;y(o,"_uid")?(s=Object.create(o),s._original=o):(s=o,o=o._original);var l=i(c._compiled),f=!l;this.data=e,this.props=t,this.children=r,this.parent=o,this.listeners=e.on||n,this.injections=at(c.inject,o),this.slots=function(){return u.$slots||ct(e.scopedSlots,u.$slots=st(r,o)),u.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return ct(e.scopedSlots,this.slots())}}),l&&(this.$options=c,this.$slots=this.slots(),this.$scopedSlots=ct(e.scopedSlots,this.$slots)),c._scopeId?this._c=function(e,t,n,r){var i=Lt(s,e,t,n,r,f);return i&&!Array.isArray(i)&&(i.fnScopeId=c._scopeId,i.fnContext=o),i}:this._c=function(e,t,n,r){return Lt(s,e,t,n,r,f)}}function Ct(e,t,n,r,o){var i=function(e){var t=new ce(e.tag,e.data,e.children&&e.children.slice(),e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return t.ns=e.ns,t.isStatic=e.isStatic,t.key=e.key,t.isComment=e.isComment,t.fnContext=e.fnContext,t.fnOptions=e.fnOptions,t.fnScopeId=e.fnScopeId,t.asyncMeta=e.asyncMeta,t.isCloned=!0,t}(e);return i.fnContext=n,i.fnOptions=r,t.slot&&((i.data||(i.data={})).slot=t.slot),i}function At(e,t){for(var n in t)e[w(n)]=t[n]}Tt(Pt.prototype);var It={init:function(e,t){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){var n=e;It.prepatch(n,n)}else{var r=e.componentInstance=function(e,t){var n={_isComponent:!0,_parentVnode:e,parent:t},r=e.data.inlineTemplate;o(r)&&(n.render=r.render,n.staticRenderFns=r.staticRenderFns);return new e.componentOptions.Ctor(n)}(e,Ht);r.$mount(t?e.elm:void 0,t)}},prepatch:function(e,t){var r=t.componentOptions,o=t.componentInstance=e.componentInstance;(function(e,t,r,o,i){0;var a=o.data.scopedSlots,s=e.$scopedSlots,u=!!(a&&!a.$stable||s!==n&&!s.$stable||a&&e.$scopedSlots.$key!==a.$key),c=!!(i||e.$options._renderChildren||u);e.$options._parentVnode=o,e.$vnode=o,e._vnode&&(e._vnode.parent=o);if(e.$options._renderChildren=i,e.$attrs=o.data.attrs||n,e.$listeners=r||n,t&&e.$options.props){me(!1);for(var l=e._props,f=e.$options._propKeys||[],d=0;d<f.length;d++){var h=f[d],p=e.$options.props;l[h]=je(h,p,t,e)}me(!0),e.$options.propsData=t}e._$updateProperties&&e._$updateProperties(e),r=r||n;var g=e.$options._parentListeners;e.$options._parentListeners=r,qt(e,r,g),c&&(e.$slots=st(i,o.context),e.$forceUpdate());0})(o,r.propsData,r.listeners,t,r.children)},insert:function(e){var t=e.context,n=e.componentInstance;n._isMounted||(Kt(n,"onServiceCreated"),Kt(n,"onServiceAttached"),n._isMounted=!0,Kt(n,"mounted")),e.data.keepAlive&&(t._isMounted?function(e){e._inactive=!1,Jt.push(e)}(n):Vt(n,!0))},destroy:function(e){var t=e.componentInstance;t._isDestroyed||(e.data.keepAlive?function e(t,n){if(n&&(t._directInactive=!0,zt(t)))return;if(!t._inactive){t._inactive=!0;for(var r=0;r<t.$children.length;r++)e(t.$children[r]);Kt(t,"deactivated")}}(t,!0):t.$destroy())}},Et=Object.keys(It);function Dt(e,t,a,u,c){if(!r(e)){var l=a.$options._base;if(s(e)&&(e=l.extend(e)),"function"===typeof e){var d;if(r(e.cid)&&(d=e,e=function(e,t){if(i(e.error)&&o(e.errorComp))return e.errorComp;if(o(e.resolved))return e.resolved;var n=Mt;n&&o(e.owners)&&-1===e.owners.indexOf(n)&&e.owners.push(n);if(i(e.loading)&&o(e.loadingComp))return e.loadingComp;if(n&&!o(e.owners)){var a=e.owners=[n],u=!0,c=null,l=null;n.$on("hook:destroyed",(function(){return v(a,n)}));var d=function(e){for(var t=0,n=a.length;t<n;t++)a[t].$forceUpdate();e&&(a.length=0,null!==c&&(clearTimeout(c),c=null),null!==l&&(clearTimeout(l),l=null))},h=L((function(n){e.resolved=Rt(n,t),u?a.length=0:d(!0)})),p=L((function(t){o(e.errorComp)&&(e.error=!0,d(!0))})),g=e(h,p);return s(g)&&(f(g)?r(e.resolved)&&g.then(h,p):f(g.component)&&(g.component.then(h,p),o(g.error)&&(e.errorComp=Rt(g.error,t)),o(g.loading)&&(e.loadingComp=Rt(g.loading,t),0===g.delay?e.loading=!0:c=setTimeout((function(){c=null,r(e.resolved)&&r(e.error)&&(e.loading=!0,d(!1))}),g.delay||200)),o(g.timeout)&&(l=setTimeout((function(){l=null,r(e.resolved)&&p(null)}),g.timeout)))),u=!1,e.loading?e.loadingComp:e.resolved}}(d,l),void 0===e))return function(e,t,n,r,o){var i=fe();return i.asyncFactory=e,i.asyncMeta={data:t,context:n,children:r,tag:o},i}(d,t,a,u,c);t=t||{},pn(e),o(t.model)&&function(e,t){var n=e.model&&e.model.prop||"value",r=e.model&&e.model.event||"input";(t.attrs||(t.attrs={}))[n]=t.model.value;var i=t.on||(t.on={}),a=i[r],s=t.model.callback;o(a)?(Array.isArray(a)?-1===a.indexOf(s):a!==s)&&(i[r]=[s].concat(a)):i[r]=s}(e.options,t);var h=function(e,t,n,i){var a=t.options.props;if(r(a))return et(e,t,{},i);var s={},u=e.attrs,c=e.props;if(o(u)||o(c))for(var l in a){var f=S(l);tt(s,c,l,f,!0)||tt(s,u,l,f,!1)}return et(e,t,s,i)}(t,e,0,a);if(i(e.options.functional))return function(e,t,r,i,a){var s=e.options,u={},c=s.props;if(o(c))for(var l in c)u[l]=je(l,c,t||n);else o(r.attrs)&&At(u,r.attrs),o(r.props)&&At(u,r.props);var f=new Pt(r,u,a,i,e),d=s.render.call(null,f._c,f);if(d instanceof ce)return Ct(d,r,f.parent,s,f);if(Array.isArray(d)){for(var h=nt(d)||[],p=new Array(h.length),g=0;g<h.length;g++)p[g]=Ct(h[g],r,f.parent,s,f);return p}}(e,h,t,a,u);var p=t.on;if(t.on=t.nativeOn,i(e.options.abstract)){var g=t.slot;t={},g&&(t.slot=g)}(function(e){for(var t=e.hook||(e.hook={}),n=0;n<Et.length;n++){var r=Et[n],o=t[r],i=It[r];o===i||o&&o._merged||(t[r]=o?jt(i,o):i)}})(t);var m=e.options.name||c,y=new ce("vue-component-"+e.cid+(m?"-"+m:""),t,void 0,void 0,void 0,a,{Ctor:e,propsData:h,listeners:p,tag:c,children:u},d);return y}}}function jt(e,t){var n=function(n,r){e(n,r),t(n,r)};return n._merged=!0,n}function Lt(e,t,n,u,c,l){return(Array.isArray(n)||a(n))&&(c=u,u=n,n=void 0),i(l)&&(c=2),function(e,t,n,a,u){if(o(n)&&o(n.__ob__))return fe();o(n)&&o(n.is)&&(t=n.is);if(!t)return fe();0;Array.isArray(a)&&"function"===typeof a[0]&&(n=n||{},n.scopedSlots={default:a[0]},a.length=0);2===u?a=nt(a):1===u&&(a=function(e){for(var t=0;t<e.length;t++)if(Array.isArray(e[t]))return Array.prototype.concat.apply([],e);return e}(a));var c,l;if("string"===typeof t){var f;l=e.$vnode&&e.$vnode.ns||R.getTagNamespace(t),c=R.isReservedTag(t)?new ce(R.parsePlatformTagName(t),n,a,void 0,void 0,e):n&&n.pre||!o(f=De(e.$options,"components",t))?new ce(t,n,a,void 0,void 0,e):Dt(f,n,e,a,t)}else c=Dt(t,n,e,a);return Array.isArray(c)?c:o(c)?(o(l)&&function e(t,n,a){t.ns=n,"foreignObject"===t.tag&&(n=void 0,a=!0);if(o(t.children))for(var s=0,u=t.children.length;s<u;s++){var c=t.children[s];o(c.tag)&&(r(c.ns)||i(a)&&"svg"!==c.tag)&&e(c,n,a)}}(c,l),o(n)&&function(e){s(e.style)&&Xe(e.style);s(e.class)&&Xe(e.class)}(n),c):fe()}(e,t,n,u,c)}var Nt,Mt=null;function Rt(e,t){return(e.__esModule||re&&"Module"===e[Symbol.toStringTag])&&(e=e.default),s(e)?t.extend(e):e}function Bt(e){return e.isComment&&e.asyncFactory}function $t(e,t){Nt.$on(e,t)}function Ut(e,t){Nt.$off(e,t)}function Ft(e,t){var n=Nt;return function r(){var o=t.apply(null,arguments);null!==o&&n.$off(e,r)}}function qt(e,t,n){Nt=e,function(e,t,n,o,a,s){var u,c,l,f;for(u in e)c=e[u],l=t[u],f=Qe(u),r(c)||(r(l)?(r(c.fns)&&(c=e[u]=Ze(c,s)),i(f.once)&&(c=e[u]=a(f.name,c,f.capture)),n(f.name,c,f.capture,f.passive,f.params)):c!==l&&(l.fns=c,e[u]=l));for(u in t)r(e[u])&&(f=Qe(u),o(f.name,t[u],f.capture))}(t,n||{},$t,Ut,Ft,e),Nt=void 0}var Ht=null;function zt(e){while(e&&(e=e.$parent))if(e._inactive)return!0;return!1}function Vt(e,t){if(t){if(e._directInactive=!1,zt(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inactive=!1;for(var n=0;n<e.$children.length;n++)Vt(e.$children[n]);Kt(e,"activated")}}function Kt(e,t){se();var n=e.$options[t],r=t+" hook";if(n)for(var o=0,i=n.length;o<i;o++)Be(n[o],e,null,e,r);e._hasHookEvent&&e.$emit("hook:"+t),ue()}var Wt=[],Jt=[],Gt={},Yt=!1,Xt=!1,Qt=0;var Zt=Date.now;if(z&&!J){var en=window.performance;en&&"function"===typeof en.now&&Zt()>document.createEvent("Event").timeStamp&&(Zt=function(){return en.now()})}function tn(){var e,t;for(Zt(),Xt=!0,Wt.sort((function(e,t){return e.id-t.id})),Qt=0;Qt<Wt.length;Qt++)e=Wt[Qt],e.before&&e.before(),t=e.id,Gt[t]=null,e.run();var n=Jt.slice(),r=Wt.slice();(function(){Qt=Wt.length=Jt.length=0,Gt={},Yt=Xt=!1})(),function(e){for(var t=0;t<e.length;t++)e[t]._inactive=!0,Vt(e[t],!0)}(n),function(e){var t=e.length;while(t--){var n=e[t],r=n.vm;r._watcher===n&&r._isMounted&&!r._isDestroyed&&Kt(r,"updated")}}(r),ee&&R.devtools&&ee.emit("flush")}var nn=0,rn=function(e,t,n,r,o){this.vm=e,o&&(e._watcher=this),e._watchers.push(this),r?(this.deep=!!r.deep,this.user=!!r.user,this.lazy=!!r.lazy,this.sync=!!r.sync,this.before=r.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=n,this.id=++nn,this.active=!0,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new ne,this.newDepIds=new ne,this.expression="","function"===typeof t?this.getter=t:(this.getter=function(e){if(!F.test(e)){var t=e.split(".");return function(e){for(var n=0;n<t.length;n++){if(!e)return;e=e[t[n]]}return e}}}(t),this.getter||(this.getter=A)),this.value=this.lazy?void 0:this.get()};rn.prototype.get=function(){var e;se(this);var t=this.vm;try{e=this.getter.call(t,t)}catch(Rn){if(!this.user)throw Rn;Re(Rn,t,'getter for watcher "'+this.expression+'"')}finally{this.deep&&Xe(e),ue(),this.cleanupDeps()}return e},rn.prototype.addDep=function(e){var t=e.id;this.newDepIds.has(t)||(this.newDepIds.add(t),this.newDeps.push(e),this.depIds.has(t)||e.addSub(this))},rn.prototype.cleanupDeps=function(){var e=this.deps.length;while(e--){var t=this.deps[e];this.newDepIds.has(t.id)||t.removeSub(this)}var n=this.depIds;this.depIds=this.newDepIds,this.newDepIds=n,this.newDepIds.clear(),n=this.deps,this.deps=this.newDeps,this.newDeps=n,this.newDeps.length=0},rn.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():function(e){var t=e.id;if(null==Gt[t]){if(Gt[t]=!0,Xt){var n=Wt.length-1;while(n>Qt&&Wt[n].id>e.id)n--;Wt.splice(n+1,0,e)}else Wt.push(e);Yt||(Yt=!0,Ge(tn))}}(this)},rn.prototype.run=function(){if(this.active){var e=this.get();if(e!==this.value||s(e)||this.deep){var t=this.value;if(this.value=e,this.user)try{this.cb.call(this.vm,e,t)}catch(Rn){Re(Rn,this.vm,'callback for watcher "'+this.expression+'"')}else this.cb.call(this.vm,e,t)}}},rn.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},rn.prototype.depend=function(){var e=this.deps.length;while(e--)this.deps[e].depend()},rn.prototype.teardown=function(){if(this.active){this.vm._isBeingDestroyed||v(this.vm._watchers,this);var e=this.deps.length;while(e--)this.deps[e].removeSub(this);this.active=!1}};var on={enumerable:!0,configurable:!0,get:A,set:A};function an(e,t,n){on.get=function(){return this[t][n]},on.set=function(e){this[t][n]=e},Object.defineProperty(e,n,on)}function sn(e){e._watchers=[];var t=e.$options;t.props&&function(e,t){var n=e.$options.propsData||{},r=e._props={},o=e.$options._propKeys=[],i=!e.$parent;i||me(!1);var a=function(i){o.push(i);var a=je(i,t,n,e);we(r,i,a),i in e||an(e,"_props",i)};for(var s in t)a(s);me(!0)}(e,t.props),t.methods&&function(e,t){e.$options.props;for(var n in t)e[n]="function"!==typeof t[n]?A:O(t[n],e)}(e,t.methods),t.data?function(e){var t=e.$options.data;t=e._data="function"===typeof t?function(e,t){se();try{return e.call(t,t)}catch(Rn){return Re(Rn,t,"data()"),{}}finally{ue()}}(t,e):t||{},c(t)||(t={});var n=Object.keys(t),r=e.$options.props,o=(e.$options.methods,n.length);while(o--){var i=n[o];0,r&&y(r,i)||$(i)||an(e,"_data",i)}be(t,!0)}(e):be(e._data={},!0),t.computed&&function(e,t){var n=e._computedWatchers=Object.create(null),r=Z();for(var o in t){var i=t[o],a="function"===typeof i?i:i.get;0,r||(n[o]=new rn(e,a||A,A,un)),o in e||cn(e,o,i)}}(e,t.computed),t.watch&&t.watch!==X&&function(e,t){for(var n in t){var r=t[n];if(Array.isArray(r))for(var o=0;o<r.length;o++)dn(e,n,r[o]);else dn(e,n,r)}}(e,t.watch)}var un={lazy:!0};function cn(e,t,n){var r=!Z();"function"===typeof n?(on.get=r?ln(t):fn(n),on.set=A):(on.get=n.get?r&&!1!==n.cache?ln(t):fn(n.get):A,on.set=n.set||A),Object.defineProperty(e,t,on)}function ln(e){return function(){var t=this._computedWatchers&&this._computedWatchers[e];if(t)return t.dirty&&t.evaluate(),ae.SharedObject.target&&t.depend(),t.value}}function fn(e){return function(){return e.call(this,this)}}function dn(e,t,n,r){return c(n)&&(r=n,n=n.handler),"string"===typeof n&&(n=e[n]),e.$watch(t,n,r)}var hn=0;function pn(e){var t=e.options;if(e.super){var n=pn(e.super),r=e.superOptions;if(n!==r){e.superOptions=n;var o=function(e){var t,n=e.options,r=e.sealedOptions;for(var o in n)n[o]!==r[o]&&(t||(t={}),t[o]=n[o]);return t}(e);o&&P(e.extendOptions,o),t=e.options=Ee(n,e.extendOptions),t.name&&(t.components[t.name]=e)}}return t}function gn(e){this._init(e)}function vn(e){e.cid=0;var t=1;e.extend=function(e){e=e||{};var n=this,r=n.cid,o=e._Ctor||(e._Ctor={});if(o[r])return o[r];var i=e.name||n.options.name;var a=function(e){this._init(e)};return a.prototype=Object.create(n.prototype),a.prototype.constructor=a,a.cid=t++,a.options=Ee(n.options,e),a["super"]=n,a.options.props&&function(e){var t=e.options.props;for(var n in t)an(e.prototype,"_props",n)}(a),a.options.computed&&function(e){var t=e.options.computed;for(var n in t)cn(e.prototype,n,t[n])}(a),a.extend=n.extend,a.mixin=n.mixin,a.use=n.use,N.forEach((function(e){a[e]=n[e]})),i&&(a.options.components[i]=a),a.superOptions=n.options,a.extendOptions=e,a.sealedOptions=P({},a.options),o[r]=a,a}}function mn(e){return e&&(e.Ctor.options.name||e.tag)}function yn(e,t){return Array.isArray(e)?e.indexOf(t)>-1:"string"===typeof e?e.split(",").indexOf(t)>-1:!!function(e){return"[object RegExp]"===u.call(e)}(e)&&e.test(t)}function _n(e,t){var n=e.cache,r=e.keys,o=e._vnode;for(var i in n){var a=n[i];if(a){var s=mn(a.componentOptions);s&&!t(s)&&bn(n,i,r,o)}}}function bn(e,t,n,r){var o=e[t];!o||r&&o.tag===r.tag||o.componentInstance.$destroy(),e[t]=null,v(n,t)}(function(e){e.prototype._init=function(e){var t=this;t._uid=hn++,t._isVue=!0,e&&e._isComponent?function(e,t){var n=e.$options=Object.create(e.constructor.options),r=t._parentVnode;n.parent=t.parent,n._parentVnode=r;var o=r.componentOptions;n.propsData=o.propsData,n._parentListeners=o.listeners,n._renderChildren=o.children,n._componentTag=o.tag,t.render&&(n.render=t.render,n.staticRenderFns=t.staticRenderFns)}(t,e):t.$options=Ee(pn(t.constructor),e||{},t),t._renderProxy=t,t._self=t,function(e){var t=e.$options,n=t.parent;if(n&&!t.abstract){while(n.$options.abstract&&n.$parent)n=n.$parent;n.$children.push(e)}e.$parent=n,e.$root=n?n.$root:e,e.$children=[],e.$refs={},e._watcher=null,e._inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}(t),function(e){e._events=Object.create(null),e._hasHookEvent=!1;var t=e.$options._parentListeners;t&&qt(e,t)}(t),function(e){e._vnode=null,e._staticTrees=null;var t=e.$options,r=e.$vnode=t._parentVnode,o=r&&r.context;e.$slots=st(t._renderChildren,o),e.$scopedSlots=n,e._c=function(t,n,r,o){return Lt(e,t,n,r,o,!1)},e.$createElement=function(t,n,r,o){return Lt(e,t,n,r,o,!0)};var i=r&&r.data;we(e,"$attrs",i&&i.attrs||n,null,!0),we(e,"$listeners",t._parentListeners||n,null,!0)}(t),Kt(t,"beforeCreate"),!t._$fallback&&it(t),sn(t),!t._$fallback&&ot(t),!t._$fallback&&Kt(t,"created"),t.$options.el&&t.$mount(t.$options.el)}})(gn),function(e){var t={get:function(){return this._data}},n={get:function(){return this._props}};Object.defineProperty(e.prototype,"$data",t),Object.defineProperty(e.prototype,"$props",n),e.prototype.$set=ke,e.prototype.$delete=xe,e.prototype.$watch=function(e,t,n){if(c(t))return dn(this,e,t,n);n=n||{},n.user=!0;var r=new rn(this,e,t,n);if(n.immediate)try{t.call(this,r.value)}catch(o){Re(o,this,'callback for immediate watcher "'+r.expression+'"')}return function(){r.teardown()}}}(gn),function(e){var t=/^hook:/;e.prototype.$on=function(e,n){var r=this;if(Array.isArray(e))for(var o=0,i=e.length;o<i;o++)r.$on(e[o],n);else(r._events[e]||(r._events[e]=[])).push(n),t.test(e)&&(r._hasHookEvent=!0);return r},e.prototype.$once=function(e,t){var n=this;function r(){n.$off(e,r),t.apply(n,arguments)}return r.fn=t,n.$on(e,r),n},e.prototype.$off=function(e,t){var n=this;if(!arguments.length)return n._events=Object.create(null),n;if(Array.isArray(e)){for(var r=0,o=e.length;r<o;r++)n.$off(e[r],t);return n}var i,a=n._events[e];if(!a)return n;if(!t)return n._events[e]=null,n;var s=a.length;while(s--)if(i=a[s],i===t||i.fn===t){a.splice(s,1);break}return n},e.prototype.$emit=function(e){var t=this,n=t._events[e];if(n){n=n.length>1?T(n):n;for(var r=T(arguments,1),o='event handler for "'+e+'"',i=0,a=n.length;i<a;i++)Be(n[i],t,r,t,o)}return t}}(gn),function(e){e.prototype._update=function(e,t){var n=this,r=n.$el,o=n._vnode,i=function(e){var t=Ht;return Ht=e,function(){Ht=t}}(n);n._vnode=e,n.$el=o?n.__patch__(o,e):n.__patch__(n.$el,e,t,!1),i(),r&&(r.__vue__=null),n.$el&&(n.$el.__vue__=n),n.$vnode&&n.$parent&&n.$vnode===n.$parent._vnode&&(n.$parent.$el=n.$el)},e.prototype.$forceUpdate=function(){this._watcher&&this._watcher.update()},e.prototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){Kt(e,"beforeDestroy"),e._isBeingDestroyed=!0;var t=e.$parent;!t||t._isBeingDestroyed||e.$options.abstract||v(t.$children,e),e._watcher&&e._watcher.teardown();var n=e._watchers.length;while(n--)e._watchers[n].teardown();e._data.__ob__&&e._data.__ob__.vmCount--,e._isDestroyed=!0,e.__patch__(e._vnode,null),Kt(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&&(e.$vnode.parent=null)}}}(gn),function(e){Tt(e.prototype),e.prototype.$nextTick=function(e){return Ge(e,this)},e.prototype._render=function(){var e,t=this,n=t.$options,r=n.render,o=n._parentVnode;o&&(t.$scopedSlots=ct(o.data.scopedSlots,t.$slots,t.$scopedSlots)),t.$vnode=o;try{Mt=t,e=r.call(t._renderProxy,t.$createElement)}catch(Rn){Re(Rn,t,"render"),e=t._vnode}finally{Mt=null}return Array.isArray(e)&&1===e.length&&(e=e[0]),e instanceof ce||(e=fe()),e.parent=o,e}}(gn);var wn=[String,RegExp,Array],kn={name:"keep-alive",abstract:!0,props:{include:wn,exclude:wn,max:[String,Number]},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var e in this.cache)bn(this.cache,e,this.keys)},mounted:function(){var e=this;this.$watch("include",(function(t){_n(e,(function(e){return yn(t,e)}))})),this.$watch("exclude",(function(t){_n(e,(function(e){return!yn(t,e)}))}))},render:function(){var e=this.$slots.default,t=function(e){if(Array.isArray(e))for(var t=0;t<e.length;t++){var n=e[t];if(o(n)&&(o(n.componentOptions)||Bt(n)))return n}}(e),n=t&&t.componentOptions;if(n){var r=mn(n),i=this.include,a=this.exclude;if(i&&(!r||!yn(i,r))||a&&r&&yn(a,r))return t;var s=this.cache,u=this.keys,c=null==t.key?n.Ctor.cid+(n.tag?"::"+n.tag:""):t.key;s[c]?(t.componentInstance=s[c].componentInstance,v(u,c),u.push(c)):(s[c]=t,u.push(c),this.max&&u.length>parseInt(this.max)&&bn(s,u[0],u,this._vnode)),t.data.keepAlive=!0}return t||e&&e[0]}},xn={KeepAlive:kn};(function(e){var t={get:function(){return R}};Object.defineProperty(e,"config",t),e.util={warn:oe,extend:P,mergeOptions:Ee,defineReactive:we},e.set=ke,e.delete=xe,e.nextTick=Ge,e.observable=function(e){return be(e),e},e.options=Object.create(null),N.forEach((function(t){e.options[t+"s"]=Object.create(null)})),e.options._base=e,P(e.options.components,xn),function(e){e.use=function(e){var t=this._installedPlugins||(this._installedPlugins=[]);if(t.indexOf(e)>-1)return this;var n=T(arguments,1);return n.unshift(this),"function"===typeof e.install?e.install.apply(e,n):"function"===typeof e&&e.apply(null,n),t.push(e),this}}(e),function(e){e.mixin=function(e){return this.options=Ee(this.options,e),this}}(e),vn(e),function(e){N.forEach((function(t){e[t]=function(e,n){return n?("component"===t&&c(n)&&(n.name=n.name||e,n=this.options._base.extend(n)),"directive"===t&&"function"===typeof n&&(n={bind:n,update:n}),this.options[t+"s"][e]=n,n):this.options[t+"s"][e]}}))}(e)})(gn),Object.defineProperty(gn.prototype,"$isServer",{get:Z}),Object.defineProperty(gn.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(gn,"FunctionalRenderContext",{value:Pt}),gn.version="2.6.11";var Sn="[object Array]",On="[object Object]";function Tn(e,t){var n={};return function e(t,n){if(t===n)return;var r=Cn(t),o=Cn(n);if(r==On&&o==On){if(Object.keys(t).length>=Object.keys(n).length)for(var i in n){var a=t[i];void 0===a?t[i]=null:e(a,n[i])}}else r==Sn&&o==Sn&&t.length>=n.length&&n.forEach((function(n,r){e(t[r],n)}))}(e,t),function e(t,n,r,o){if(t===n)return;var i=Cn(t),a=Cn(n);if(i==On)if(a!=On||Object.keys(t).length<Object.keys(n).length)Pn(o,r,t);else{var s=function(i){var a=t[i],s=n[i],u=Cn(a),c=Cn(s);if(u!=Sn&&u!=On)a!==n[i]&&function(e,t){if(("[object Null]"===e||"[object Undefined]"===e)&&("[object Null]"===t||"[object Undefined]"===t))return!1;return!0}(u,c)&&Pn(o,(""==r?"":r+".")+i,a);else if(u==Sn)c!=Sn||a.length<s.length?Pn(o,(""==r?"":r+".")+i,a):a.forEach((function(t,n){e(t,s[n],(""==r?"":r+".")+i+"["+n+"]",o)}));else if(u==On)if(c!=On||Object.keys(a).length<Object.keys(s).length)Pn(o,(""==r?"":r+".")+i,a);else for(var l in a)e(a[l],s[l],(""==r?"":r+".")+i+"."+l,o)};for(var u in t)s(u)}else i==Sn?a!=Sn||t.length<n.length?Pn(o,r,t):t.forEach((function(t,i){e(t,n[i],r+"["+i+"]",o)})):Pn(o,r,t)}(e,t,"",n),n}function Pn(e,t,n){e[t]=n}function Cn(e){return Object.prototype.toString.call(e)}function An(e){if(e.__next_tick_callbacks&&e.__next_tick_callbacks.length){if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"株水小智",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var t=e.$scope;console.log("["+ +new Date+"]["+(t.is||t.route)+"]["+e._uid+"]:flushCallbacks["+e.__next_tick_callbacks.length+"]")}var n=e.__next_tick_callbacks.slice(0);e.__next_tick_callbacks.length=0;for(var r=0;r<n.length;r++)n[r]()}}function In(e,t){if(!e.__next_tick_pending&&!function(e){return Wt.find((function(t){return e._watcher===t}))}(e)){if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"株水小智",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var n=e.$scope;console.log("["+ +new Date+"]["+(n.is||n.route)+"]["+e._uid+"]:nextVueTick")}return Ge(t,e)}if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"株水小智",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var r=e.$scope;console.log("["+ +new Date+"]["+(r.is||r.route)+"]["+e._uid+"]:nextMPTick")}var o;if(e.__next_tick_callbacks||(e.__next_tick_callbacks=[]),e.__next_tick_callbacks.push((function(){if(t)try{t.call(e)}catch(Rn){Re(Rn,e,"nextTick")}else o&&o(e)})),!t&&"undefined"!==typeof Promise)return new Promise((function(e){o=e}))}function En(e,t){return t&&(t._isVue||t.__v_isMPComponent)?{}:t}function Dn(){}function jn(e){return Array.isArray(e)?function(e){for(var t,n="",r=0,i=e.length;r<i;r++)o(t=jn(e[r]))&&""!==t&&(n&&(n+=" "),n+=t);return n}(e):s(e)?function(e){var t="";for(var n in e)e[n]&&(t&&(t+=" "),t+=n);return t}(e):"string"===typeof e?e:""}var Ln=_((function(e){var t={},n=/:(.+)/;return e.split(/;(?![^(]*\))/g).forEach((function(e){if(e){var r=e.split(n);r.length>1&&(t[r[0].trim()]=r[1].trim())}})),t}));var Nn=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];var Mn=["onLaunch","onShow","onHide","onUniNViewMessage","onPageNotFound","onThemeChange","onError","onUnhandledRejection","onInit","onLoad","onReady","onUnload","onPullDownRefresh","onReachBottom","onTabItemTap","onAddToFavorites","onShareTimeline","onShareAppMessage","onResize","onPageScroll","onNavigationBarButtonTap","onBackPress","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputClicked","onUploadDouyinVideo","onNFCReadMessage","onPageShow","onPageHide","onPageResize"];gn.prototype.__patch__=function(e,t){var n=this;if(null!==t&&("page"===this.mpType||"component"===this.mpType)){var r=this.$scope,o=Object.create(null);try{o=function(e){var t=Object.create(null),n=[].concat(Object.keys(e._data||{}),Object.keys(e._computedWatchers||{}));n.reduce((function(t,n){return t[n]=e[n],t}),t);var r=e.__composition_api_state__||e.__secret_vfa_state__,o=r&&r.rawBindings;return o&&Object.keys(o).forEach((function(n){t[n]=e[n]})),Object.assign(t,e.$mp.data||{}),Array.isArray(e.$options.behaviors)&&-1!==e.$options.behaviors.indexOf("uni://form-field")&&(t["name"]=e.name,t["value"]=e.value),JSON.parse(JSON.stringify(t,En))}(this)}catch(s){console.error(s)}o.__webviewId__=r.data.__webviewId__;var i=Object.create(null);Object.keys(o).forEach((function(e){i[e]=r.data[e]}));var a=!1===this.$shouldDiffData?o:Tn(o,i);Object.keys(a).length?(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"株水小智",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG&&console.log("["+ +new Date+"]["+(r.is||r.route)+"]["+this._uid+"]差量更新",JSON.stringify(a)),this.__next_tick_pending=!0,r.setData(a,(function(){n.__next_tick_pending=!1,An(n)}))):An(this)}},gn.prototype.$mount=function(e,t){return function(e,t,n){return e.mpType?("app"===e.mpType&&(e.$options.render=Dn),e.$options.render||(e.$options.render=Dn),!e._$fallback&&Kt(e,"beforeMount"),new rn(e,(function(){e._update(e._render(),n)}),A,{before:function(){e._isMounted&&!e._isDestroyed&&Kt(e,"beforeUpdate")}},!0),n=!1,e):e}(this,0,t)},function(e){var t=e.extend;e.extend=function(e){e=e||{};var n=e.methods;return n&&Object.keys(n).forEach((function(t){-1!==Mn.indexOf(t)&&(e[t]=n[t],delete n[t])})),t.call(this,e)};var n=e.config.optionMergeStrategies,r=n.created;Mn.forEach((function(e){n[e]=r})),e.prototype.__lifecycle_hooks__=Mn}(gn),function(e){e.config.errorHandler=function(t,n,r){e.util.warn("Error in "+r+': "'+t.toString()+'"',n),console.error(t);var o="function"===typeof getApp&&getApp();o&&o.onError&&o.onError(t)};var t=e.prototype.$emit;e.prototype.$emit=function(e){if(this.$scope&&e){var n=this.$scope["_triggerEvent"]||this.$scope["triggerEvent"];if(n)try{n.call(this.$scope,e,{__args__:T(arguments,1)})}catch(r){}}return t.apply(this,arguments)},e.prototype.$nextTick=function(e){return In(this,e)},Nn.forEach((function(t){e.prototype[t]=function(e){return this.$scope&&this.$scope[t]?this.$scope[t](e):"undefined"!==typeof my?"createSelectorQuery"===t?my.createSelectorQuery(e):"createIntersectionObserver"===t?my.createIntersectionObserver(e):void 0:void 0}})),e.prototype.__init_provide=ot,e.prototype.__init_injections=it,e.prototype.__call_hook=function(e,t){var n=this;se();var r,o=n.$options[e],i=e+" hook";if(o)for(var a=0,s=o.length;a<s;a++)r=Be(o[a],n,t?[t]:null,n,i);return n._hasHookEvent&&n.$emit("hook:"+e,t),ue(),r},e.prototype.__set_model=function(t,n,r,o){Array.isArray(o)&&(-1!==o.indexOf("trim")&&(r=r.trim()),-1!==o.indexOf("number")&&(r=this._n(r))),t||(t=this),e.set(t,n,r)},e.prototype.__set_sync=function(t,n,r){t||(t=this),e.set(t,n,r)},e.prototype.__get_orig=function(e){return c(e)&&e["$orig"]||e},e.prototype.__get_value=function(e,t){return function e(t,n){var r=n.split("."),o=r[0];return 0===o.indexOf("__$n")&&(o=parseInt(o.replace("__$n",""))),1===r.length?t[o]:e(t[o],r.slice(1).join("."))}(t||this,e)},e.prototype.__get_class=function(e,t){return function(e,t){return o(e)||o(t)?function(e,t){return e?t?e+" "+t:e:t||""}(e,jn(t)):""}(t,e)},e.prototype.__get_style=function(e,t){if(!e&&!t)return"";var n=function(e){return Array.isArray(e)?C(e):"string"===typeof e?Ln(e):e}(e),r=t?P(t,n):n;return Object.keys(r).map((function(e){return S(e)+":"+r[e]})).join(";")},e.prototype.__map=function(e,t){var n,r,o,i,a;if(Array.isArray(e)){for(n=new Array(e.length),r=0,o=e.length;r<o;r++)n[r]=t(e[r],r);return n}if(s(e)){for(i=Object.keys(e),n=Object.create(null),r=0,o=i.length;r<o;r++)a=i[r],n[a]=t(e[a],a,r);return n}if("number"===typeof e){for(n=new Array(e),r=0,o=e;r<o;r++)n[r]=t(r,r);return n}return[]}}(gn),t["default"]=gn}.call(this,n("0ee4"))},3352:function(e,t){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"34b0":function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("a08b")),i=r(n("b0d3")),a=r(n("75cc")),s={en:o.default,"zh-Hans":i.default,"zh-Hant":a.default};t.default=s},"34cf":function(e,t,n){var r=n("ed45"),o=n("7172"),i=n("6382"),a=n("dd3e");e.exports=function(e,t){return r(e)||o(e,t)||i(e,t)||a()},e.exports.__esModule=!0,e.exports["default"]=e.exports},"357b":function(e,t){},"3b2d":function(e,t){function n(t){return e.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports["default"]=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"3bbd":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.cacheImageList=void 0;t.cacheImageList=[]},"3e22":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fontData=void 0;t.fontData=[{font_class:"arrow-down",unicode:""},{font_class:"arrow-left",unicode:""},{font_class:"arrow-right",unicode:""},{font_class:"arrow-up",unicode:""},{font_class:"auth",unicode:""},{font_class:"auth-filled",unicode:""},{font_class:"back",unicode:""},{font_class:"bars",unicode:""},{font_class:"calendar",unicode:""},{font_class:"calendar-filled",unicode:""},{font_class:"camera",unicode:""},{font_class:"camera-filled",unicode:""},{font_class:"cart",unicode:""},{font_class:"cart-filled",unicode:""},{font_class:"chat",unicode:""},{font_class:"chat-filled",unicode:""},{font_class:"chatboxes",unicode:""},{font_class:"chatboxes-filled",unicode:""},{font_class:"chatbubble",unicode:""},{font_class:"chatbubble-filled",unicode:""},{font_class:"checkbox",unicode:""},{font_class:"checkbox-filled",unicode:""},{font_class:"checkmarkempty",unicode:""},{font_class:"circle",unicode:""},{font_class:"circle-filled",unicode:""},{font_class:"clear",unicode:""},{font_class:"close",unicode:""},{font_class:"closeempty",unicode:""},{font_class:"cloud-download",unicode:""},{font_class:"cloud-download-filled",unicode:""},{font_class:"cloud-upload",unicode:""},{font_class:"cloud-upload-filled",unicode:""},{font_class:"color",unicode:""},{font_class:"color-filled",unicode:""},{font_class:"compose",unicode:""},{font_class:"contact",unicode:""},{font_class:"contact-filled",unicode:""},{font_class:"down",unicode:""},{font_class:"bottom",unicode:""},{font_class:"download",unicode:""},{font_class:"download-filled",unicode:""},{font_class:"email",unicode:""},{font_class:"email-filled",unicode:""},{font_class:"eye",unicode:""},{font_class:"eye-filled",unicode:""},{font_class:"eye-slash",unicode:""},{font_class:"eye-slash-filled",unicode:""},{font_class:"fire",unicode:""},{font_class:"fire-filled",unicode:""},{font_class:"flag",unicode:""},{font_class:"flag-filled",unicode:""},{font_class:"folder-add",unicode:""},{font_class:"folder-add-filled",unicode:""},{font_class:"font",unicode:""},{font_class:"forward",unicode:""},{font_class:"gear",unicode:""},{font_class:"gear-filled",unicode:""},{font_class:"gift",unicode:""},{font_class:"gift-filled",unicode:""},{font_class:"hand-down",unicode:""},{font_class:"hand-down-filled",unicode:""},{font_class:"hand-up",unicode:""},{font_class:"hand-up-filled",unicode:""},{font_class:"headphones",unicode:""},{font_class:"heart",unicode:""},{font_class:"heart-filled",unicode:""},{font_class:"help",unicode:""},{font_class:"help-filled",unicode:""},{font_class:"home",unicode:""},{font_class:"home-filled",unicode:""},{font_class:"image",unicode:""},{font_class:"image-filled",unicode:""},{font_class:"images",unicode:""},{font_class:"images-filled",unicode:""},{font_class:"info",unicode:""},{font_class:"info-filled",unicode:""},{font_class:"left",unicode:""},{font_class:"link",unicode:""},{font_class:"list",unicode:""},{font_class:"location",unicode:""},{font_class:"location-filled",unicode:""},{font_class:"locked",unicode:""},{font_class:"locked-filled",unicode:""},{font_class:"loop",unicode:""},{font_class:"mail-open",unicode:""},{font_class:"mail-open-filled",unicode:""},{font_class:"map",unicode:""},{font_class:"map-filled",unicode:""},{font_class:"map-pin",unicode:""},{font_class:"map-pin-ellipse",unicode:""},{font_class:"medal",unicode:""},{font_class:"medal-filled",unicode:""},{font_class:"mic",unicode:""},{font_class:"mic-filled",unicode:""},{font_class:"micoff",unicode:""},{font_class:"micoff-filled",unicode:""},{font_class:"minus",unicode:""},{font_class:"minus-filled",unicode:""},{font_class:"more",unicode:""},{font_class:"more-filled",unicode:""},{font_class:"navigate",unicode:""},{font_class:"navigate-filled",unicode:""},{font_class:"notification",unicode:""},{font_class:"notification-filled",unicode:""},{font_class:"paperclip",unicode:""},{font_class:"paperplane",unicode:""},{font_class:"paperplane-filled",unicode:""},{font_class:"person",unicode:""},{font_class:"person-filled",unicode:""},{font_class:"personadd",unicode:""},{font_class:"personadd-filled",unicode:""},{font_class:"personadd-filled-copy",unicode:""},{font_class:"phone",unicode:""},{font_class:"phone-filled",unicode:""},{font_class:"plus",unicode:""},{font_class:"plus-filled",unicode:""},{font_class:"plusempty",unicode:""},{font_class:"pulldown",unicode:""},{font_class:"pyq",unicode:""},{font_class:"qq",unicode:""},{font_class:"redo",unicode:""},{font_class:"redo-filled",unicode:""},{font_class:"refresh",unicode:""},{font_class:"refresh-filled",unicode:""},{font_class:"refreshempty",unicode:""},{font_class:"reload",unicode:""},{font_class:"right",unicode:""},{font_class:"scan",unicode:""},{font_class:"search",unicode:""},{font_class:"settings",unicode:""},{font_class:"settings-filled",unicode:""},{font_class:"shop",unicode:""},{font_class:"shop-filled",unicode:""},{font_class:"smallcircle",unicode:""},{font_class:"smallcircle-filled",unicode:""},{font_class:"sound",unicode:""},{font_class:"sound-filled",unicode:""},{font_class:"spinner-cycle",unicode:""},{font_class:"staff",unicode:""},{font_class:"staff-filled",unicode:""},{font_class:"star",unicode:""},{font_class:"star-filled",unicode:""},{font_class:"starhalf",unicode:""},{font_class:"trash",unicode:""},{font_class:"trash-filled",unicode:""},{font_class:"tune",unicode:""},{font_class:"tune-filled",unicode:""},{font_class:"undo",unicode:""},{font_class:"undo-filled",unicode:""},{font_class:"up",unicode:""},{font_class:"top",unicode:""},{font_class:"upload",unicode:""},{font_class:"upload-filled",unicode:""},{font_class:"videocam",unicode:""},{font_class:"videocam-filled",unicode:""},{font_class:"vip",unicode:""},{font_class:"vip-filled",unicode:""},{font_class:"wallet",unicode:""},{font_class:"wallet-filled",unicode:""},{font_class:"weibo",unicode:""},{font_class:"weixin",unicode:""}]},"3f10":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r={data:function(){return{}},created:function(){this.popup=this.getParent()},methods:{getParent:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"uniPopup",t=this.$parent,n=t.$options.name;while(n!==e){if(t=t.$parent,!t)return!1;n=t.$options.name}return t}}};t.default=r},"423e":function(e,t,n){"use strict";(function(e,r){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.store=t.mutations=void 0;var i=o(n("7eb4")),a=o(n("7ca3")),s=o(n("ee10")),u=o(n("0c73")),c=o(n("30eb")),l=o(n("3240"));function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=e.importObject("uni-id-co"),p=e.database(),g=p.collection("uni-id-users"),v=r.getStorageSync("uni-id-pages-userInfo")||{},m={userInfo:v,hasLogin:0!=Object.keys(v).length},y={updateUserInfo:function(){var t=arguments,n=this;return(0,s.default)(i.default.mark((function o(){var a,s,u,c,l;return i.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(a=t.length>0&&void 0!==t[0]&&t[0],!a){o.next=5;break}g.where("_id==$env.uid").update(a).then((function(e){e.result.updated?(r.showToast({title:"更新成功",icon:"none",duration:3e3}),n.setUserInfo(a)):r.showToast({title:"没有改变",icon:"none",duration:3e3})})),o.next=22;break;case 5:return s=e.getCurrentUserInfo().uid,n.setUserInfo({_id:s},{cover:!0}),u=e.importObject("uni-id-co",{customUI:!0}),o.prev=8,o.next=11,g.where("'_id' == $cloudEnv_uid").field("mobile,nickname,username,email,avatar_file").get();case 11:return c=o.sent,o.next=14,u.getRealNameInfo();case 14:l=o.sent,n.setUserInfo(d(d({},c.result.data[0]),{},{realNameAuth:l})),o.next=22;break;case 18:o.prev=18,o.t0=o["catch"](8),n.setUserInfo({},{cover:!0}),console.error(o.t0.message,o.t0.errCode);case 22:case"end":return o.stop()}}),o,null,[[8,18]])})))()},setUserInfo:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{cover:!1},n=t.cover,o=n?e:Object.assign(_.userInfo,e);return _.userInfo=Object.assign({},o),_.hasLogin=0!=Object.keys(_.userInfo).length,r.setStorageSync("uni-id-pages-userInfo",_.userInfo),e},logout:function(){var t=this;return(0,s.default)(i.default.mark((function n(){return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!(e.getCurrentUserInfo().tokenExpired>Date.now())){n.next=9;break}return n.prev=1,n.next=4,h.logout();case 4:n.next=9;break;case 6:n.prev=6,n.t0=n["catch"](1),console.error(n.t0);case 9:r.removeStorageSync("uni_id_token"),r.setStorageSync("uni_id_token_expired",0),t.setUserInfo({},{cover:!0}),r.$emit("uni-id-pages-logout"),r.redirectTo({url:"/".concat(u.default.uniIdRouter&&u.default.uniIdRouter.loginPage?u.default.uniIdRouter.loginPage:"uni_modules/uni-id-pages/pages/login/login-withoutpwd")});case 14:case"end":return n.stop()}}),n,null,[[1,6]])})))()},loginBack:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.uniIdRedirectUrl,n=void 0===t?"":t,o=0,i=getCurrentPages();if(i.forEach((function(e,t){"login"==i[i.length-t-1].route.split("/")[3]&&o++})),n)return r.redirectTo({url:n,fail:function(e){r.switchTab({url:n,fail:function(t){console.log(e,t)}})}});if(o){var a=u.default.pages[0];return r.reLaunch({url:"/".concat(a.path)})}r.navigateBack({delta:o})},loginSuccess:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.showToast,o=void 0===n||n,i=t.toastText,a=void 0===i?"登录成功":i,s=t.autoBack,u=void 0===s||s,l=t.uniIdRedirectUrl,f=void 0===l?"":l,d=t.passwordConfirmed;if(o&&r.showToast({title:a,icon:"none",duration:3e3}),this.updateUserInfo(),r.$emit("uni-id-pages-login-success"),c.default.setPasswordAfterLogin&&!d)return r.redirectTo({url:f?"/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd?uniIdRedirectUrl=".concat(f,"&loginType=").concat(t.loginType):"/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd?loginType=".concat(t.loginType),fail:function(e){console.log(e)}});r.reLaunch({url:"/pages/ucenter/ucenter",fail:function(t){console.error("跳转到用户中心失败:",t),u&&e.loginBack({uniIdRedirectUrl:f})}})}};t.mutations=y;var _=l.default.observable(m);t.store=_}).call(this,n("861b")["uniCloud"],n("df3c")["default"])},"47a9":function(e,t){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports["default"]=e.exports},4825:function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.addLink=function(e,t,n){e.insertText({text:a}),e.getContents({success:function(r){var s=r.delta.ops,u=s.findIndex((function(e){var t;return e.insert&&"object"!==(0,i.default)(e.insert)&&-1!==(null===(t=e.insert)||void 0===t?void 0:t.indexOf(a))}));if(u>-1){var c=s[u],l=c.attributes,f=c.insert.split(a),d=(0,o.default)(f,2),h=d[0],p=d[1],g=[];if(h){var v=l?{insert:h,attributes:l}:{insert:h};g.push(v)}var m={insert:t.text,attributes:{link:t.href,textDecoration:t.textDecoration||"none",color:t.color||"#007aff"}};if(g.push(m),p){var y=l?{insert:p,attributes:l}:{insert:p};g.push(y)}s.splice(u,1),s.splice.apply(s,[u,0].concat(g)),e.setContents({delta:{ops:s}}),e.blur(),n&&n()}}})},t.convertImgStylesToAttributes=function(e){return e.replace(/<img\s+([^>]+)\s*>/g,(function(e,t){var n=t.split(/\s+/),r=n.findIndex((function(e){return e.startsWith("style=")}));if(-1===r)return e;var i=n.splice(r,1)[0],a=i.match(/"([^"]*)"/)[1],s={};a.split(";").forEach((function(e){if(e){var t=e.split(":"),n=(0,o.default)(t,2),r=n[0],i=n[1];s[r.trim()]=i.trim()}}));var u="<img";return s.width&&(u+=' width="'.concat(s.width,'"')),s.height&&(u+=' height="'.concat(s.height,'"')),u+=" ".concat(i," ").concat(n.join(" ")),u+=">",u}))},t.handleHtmlWithVideo=function(e){return e.replace(/<img\s+src="[^"]*"\s+alt="([^"]*)"[^>]*>/g,(function(e,t){return'<video width="80%" controls><source src="'.concat(t,'" type="video/mp4"></video>')}))},t.linkFlag=void 0;var o=r(n("34cf")),i=r(n("3b2d")),a="#-*=*-*=*-*=*@-link超链接标识link-@*=*-*=*-*=*-#";t.linkFlag=a},"48b1":function(e,t,n){"use strict";n.r(t);var r=n("a8ce"),o=n("9469"),i=n("affd"),a=n("6aea"),s=n("e7f2");t["default"]={en:r,es:o,fr:i,"zh-Hans":a,"zh-Hant":s}},4965:function(e,t){e.exports=function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"===typeof e}},e.exports.__esModule=!0,e.exports["default"]=e.exports},"4aa8":function(e){e.exports=JSON.parse('{"uni-popup.cancel":"cancel","uni-popup.ok":"ok","uni-popup.placeholder":"pleace enter","uni-popup.title":"Hint","uni-popup.shareTitle":"Share to"}')},"4ffb":function(e,t,n){var r=n("3b2d")["default"],o=n("3352");e.exports=function(e,t){if(t&&("object"===r(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},"56a1":function(e){e.exports=JSON.parse('{"uni-pagination.prevText":"anterior","uni-pagination.nextText":"prxima","uni-pagination.piecePerPage":"Art��culo/P��gina"}')},"5bc7":function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=b;var o=r(n("7eb4")),i=r(n("ee10")),a=r(n("7ca3"));function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e){this.mode=f.MODE_8BIT_BYTE,this.data=e}function l(e,t){this.typeNumber=e,this.errorCorrectLevel=t,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=new Array}c.prototype={getLength:function(e){return this.data.length},write:function(e){for(var t=0;t<this.data.length;t++)e.put(this.data.charCodeAt(t),8)}},l.prototype={addData:function(e){var t=new c(e);this.dataList.push(t),this.dataCache=null},isDark:function(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(e+","+t);return this.modules[e][t]},getModuleCount:function(){return this.moduleCount},make:function(){if(this.typeNumber<1){var e=1;for(e=1;e<40;e++){for(var t=m.getRSBlocks(e,this.errorCorrectLevel),n=new y,r=0,o=0;o<t.length;o++)r+=t[o].dataCount;for(o=0;o<this.dataList.length;o++){var i=this.dataList[o];n.put(i.mode,4),n.put(i.getLength(),h.getLengthInBits(i.mode,e)),i.write(n)}if(n.getLengthInBits()<=8*r)break}this.typeNumber=e}this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(e,t){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++){this.modules[n]=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++)this.modules[n][r]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=l.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)},setupPositionProbePattern:function(e,t){for(var n=-1;n<=7;n++)if(!(e+n<=-1||this.moduleCount<=e+n))for(var r=-1;r<=7;r++)t+r<=-1||this.moduleCount<=t+r||(this.modules[e+n][t+r]=0<=n&&n<=6&&(0==r||6==r)||0<=r&&r<=6&&(0==n||6==n)||2<=n&&n<=4&&2<=r&&r<=4)},getBestMaskPattern:function(){for(var e=0,t=0,n=0;n<8;n++){this.makeImpl(!0,n);var r=h.getLostPoint(this);(0==n||e>r)&&(e=r,t=n)}return t},createMovieClip:function(e,t,n){var r=e.createEmptyMovieClip(t,n);this.make();for(var o=0;o<this.modules.length;o++)for(var i=1*o,a=0;a<this.modules[o].length;a++){var s=1*a;this.modules[o][a]&&(r.beginFill(0,100),r.moveTo(s,i),r.lineTo(s+1,i),r.lineTo(s+1,i+1),r.lineTo(s,i+1),r.endFill())}return r},setupTimingPattern:function(){for(var e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=e%2==0);for(var t=8;t<this.moduleCount-8;t++)null==this.modules[6][t]&&(this.modules[6][t]=t%2==0)},setupPositionAdjustPattern:function(){for(var e=h.getPatternPosition(this.typeNumber),t=0;t<e.length;t++)for(var n=0;n<e.length;n++){var r=e[t],o=e[n];if(null==this.modules[r][o])for(var i=-2;i<=2;i++)for(var a=-2;a<=2;a++)this.modules[r+i][o+a]=-2==i||2==i||-2==a||2==a||0==i&&0==a}},setupTypeNumber:function(e){for(var t=h.getBCHTypeNumber(this.typeNumber),n=0;n<18;n++){var r=!e&&1==(t>>n&1);this.modules[Math.floor(n/3)][n%3+this.moduleCount-8-3]=r}for(n=0;n<18;n++)r=!e&&1==(t>>n&1),this.modules[n%3+this.moduleCount-8-3][Math.floor(n/3)]=r},setupTypeInfo:function(e,t){for(var n=this.errorCorrectLevel<<3|t,r=h.getBCHTypeInfo(n),o=0;o<15;o++){var i=!e&&1==(r>>o&1);o<6?this.modules[o][8]=i:o<8?this.modules[o+1][8]=i:this.modules[this.moduleCount-15+o][8]=i}for(o=0;o<15;o++)i=!e&&1==(r>>o&1),o<8?this.modules[8][this.moduleCount-o-1]=i:o<9?this.modules[8][15-o-1+1]=i:this.modules[8][15-o-1]=i;this.modules[this.moduleCount-8][8]=!e},mapData:function(e,t){for(var n=-1,r=this.moduleCount-1,o=7,i=0,a=this.moduleCount-1;a>0;a-=2)for(6==a&&a--;;){for(var s=0;s<2;s++)if(null==this.modules[r][a-s]){var u=!1;i<e.length&&(u=1==(e[i]>>>o&1)),h.getMask(t,r,a-s)&&(u=!u),this.modules[r][a-s]=u,-1==--o&&(i++,o=7)}if((r+=n)<0||this.moduleCount<=r){r-=n,n=-n;break}}}},l.PAD0=236,l.PAD1=17,l.createData=function(e,t,n){for(var r=m.getRSBlocks(e,t),o=new y,i=0;i<n.length;i++){var a=n[i];o.put(a.mode,4),o.put(a.getLength(),h.getLengthInBits(a.mode,e)),a.write(o)}var s=0;for(i=0;i<r.length;i++)s+=r[i].dataCount;if(o.getLengthInBits()>8*s)throw new Error("code length overflow. ("+o.getLengthInBits()+">"+8*s+")");for(o.getLengthInBits()+4<=8*s&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(!1);for(;!(o.getLengthInBits()>=8*s||(o.put(l.PAD0,8),o.getLengthInBits()>=8*s));)o.put(l.PAD1,8);return l.createBytes(o,r)},l.createBytes=function(e,t){for(var n=0,r=0,o=0,i=new Array(t.length),a=new Array(t.length),s=0;s<t.length;s++){var u=t[s].dataCount,c=t[s].totalCount-u;r=Math.max(r,u),o=Math.max(o,c),i[s]=new Array(u);for(var l=0;l<i[s].length;l++)i[s][l]=255&e.buffer[l+n];n+=u;var f=h.getErrorCorrectPolynomial(c),d=new v(i[s],f.getLength()-1).mod(f);for(a[s]=new Array(f.getLength()-1),l=0;l<a[s].length;l++){var p=l+d.getLength()-a[s].length;a[s][l]=p>=0?d.get(p):0}}var g=0;for(l=0;l<t.length;l++)g+=t[l].totalCount;var m=new Array(g),y=0;for(l=0;l<r;l++)for(s=0;s<t.length;s++)l<i[s].length&&(m[y++]=i[s][l]);for(l=0;l<o;l++)for(s=0;s<t.length;s++)l<a[s].length&&(m[y++]=a[s][l]);return m};for(var f={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},d={L:1,M:0,Q:3,H:2},h={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(e){for(var t=e<<10;h.getBCHDigit(t)-h.getBCHDigit(h.G15)>=0;)t^=h.G15<<h.getBCHDigit(t)-h.getBCHDigit(h.G15);return(e<<10|t)^h.G15_MASK},getBCHTypeNumber:function(e){for(var t=e<<12;h.getBCHDigit(t)-h.getBCHDigit(h.G18)>=0;)t^=h.G18<<h.getBCHDigit(t)-h.getBCHDigit(h.G18);return e<<12|t},getBCHDigit:function(e){for(var t=0;0!=e;)t++,e>>>=1;return t},getPatternPosition:function(e){return h.PATTERN_POSITION_TABLE[e-1]},getMask:function(e,t,n){switch(e){case 0:return(t+n)%2==0;case 1:return t%2==0;case 2:return n%3==0;case 3:return(t+n)%3==0;case 4:return(Math.floor(t/2)+Math.floor(n/3))%2==0;case 5:return t*n%2+t*n%3==0;case 6:return(t*n%2+t*n%3)%2==0;case 7:return(t*n%3+(t+n)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial:function(e){for(var t=new v([1],0),n=0;n<e;n++)t=t.multiply(new v([1,p.gexp(n)],0));return t},getLengthInBits:function(e,t){if(1<=t&&t<10)switch(e){case f.MODE_NUMBER:return 10;case f.MODE_ALPHA_NUM:return 9;case f.MODE_8BIT_BYTE:case f.MODE_KANJI:return 8;default:throw new Error("mode:"+e)}else if(t<27)switch(e){case f.MODE_NUMBER:return 12;case f.MODE_ALPHA_NUM:return 11;case f.MODE_8BIT_BYTE:return 16;case f.MODE_KANJI:return 10;default:throw new Error("mode:"+e)}else{if(!(t<41))throw new Error("type:"+t);switch(e){case f.MODE_NUMBER:return 14;case f.MODE_ALPHA_NUM:return 13;case f.MODE_8BIT_BYTE:return 16;case f.MODE_KANJI:return 12;default:throw new Error("mode:"+e)}}},getLostPoint:function(e){for(var t=e.getModuleCount(),n=0,r=0;r<t;r++)for(var o=0;o<t;o++){for(var i=0,a=e.isDark(r,o),s=-1;s<=1;s++)if(!(r+s<0||t<=r+s))for(var u=-1;u<=1;u++)o+u<0||t<=o+u||0==s&&0==u||a==e.isDark(r+s,o+u)&&i++;i>5&&(n+=3+i-5)}for(r=0;r<t-1;r++)for(o=0;o<t-1;o++){var c=0;e.isDark(r,o)&&c++,e.isDark(r+1,o)&&c++,e.isDark(r,o+1)&&c++,e.isDark(r+1,o+1)&&c++,0!=c&&4!=c||(n+=3)}for(r=0;r<t;r++)for(o=0;o<t-6;o++)e.isDark(r,o)&&!e.isDark(r,o+1)&&e.isDark(r,o+2)&&e.isDark(r,o+3)&&e.isDark(r,o+4)&&!e.isDark(r,o+5)&&e.isDark(r,o+6)&&(n+=40);for(o=0;o<t;o++)for(r=0;r<t-6;r++)e.isDark(r,o)&&!e.isDark(r+1,o)&&e.isDark(r+2,o)&&e.isDark(r+3,o)&&e.isDark(r+4,o)&&!e.isDark(r+5,o)&&e.isDark(r+6,o)&&(n+=40);var l=0;for(o=0;o<t;o++)for(r=0;r<t;r++)e.isDark(r,o)&&l++;return n+Math.abs(100*l/t/t-50)/5*10}},p={glog:function(e){if(e<1)throw new Error("glog("+e+")");return p.LOG_TABLE[e]},gexp:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return p.EXP_TABLE[e]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},g=0;g<8;g++)p.EXP_TABLE[g]=1<<g;for(g=8;g<256;g++)p.EXP_TABLE[g]=p.EXP_TABLE[g-4]^p.EXP_TABLE[g-5]^p.EXP_TABLE[g-6]^p.EXP_TABLE[g-8];for(g=0;g<255;g++)p.LOG_TABLE[p.EXP_TABLE[g]]=g;function v(e,t){if(null==e.length)throw new Error(e.length+"/"+t);for(var n=0;n<e.length&&0==e[n];)n++;this.num=new Array(e.length-n+t);for(var r=0;r<e.length-n;r++)this.num[r]=e[r+n]}function m(e,t){this.totalCount=e,this.dataCount=t}function y(){this.buffer=new Array,this.length=0}function _(e){return e.setFillStyle=e.setFillStyle||function(t){e.fillStyle=t},e.setFontSize=e.setFontSize||function(t){e.font="".concat(t,"px")},e.setTextAlign=e.setTextAlign||function(t){e.textAlign=t},e.setTextBaseline=e.setTextBaseline||function(t){e.textBaseline=t},e.setGlobalAlpha=e.setGlobalAlpha||function(t){e.globalAlpha=t},e.setStrokeStyle=e.setStrokeStyle||function(t){e.strokeStyle=t},e.setShadow=e.setShadow||function(t,n,r,o){e.shadowOffsetX=t,e.shadowOffsetY=n,e.shadowBlur=r,e.shadowColor=o},e.draw=e.draw||function(e,t){t&&t()},e.clearRect=e.clearRect||function(t,n,r,o){e.draw(!1)},e}function b(e,t){var n=this,r=this.data="",o=this.size=200;this.useDynamicSize=!1,this.dynamicSize=o;var i=this.typeNumber=-1;this.errorCorrectLevel=b.errorCorrectLevel.H;var a=this.margin=0;this.areaColor="#FFFFFF",this.backgroundColor="rgba(255,255,255,0)",this.backgroundImageSrc=void 0;var s=this.backgroundImageWidth=void 0,u=this.backgroundImageHeight=void 0,c=this.backgroundImageX=void 0,l=this.backgroundImageY=void 0;this.backgroundImageAlpha=1,this.backgroundImageBorderRadius=0;var f=this.backgroundPadding=0;this.foregroundColor="#000000",this.foregroundImageSrc=void 0;var d=this.foregroundImageWidth=void 0,h=this.foregroundImageHeight=void 0,p=this.foregroundImageX=void 0,g=this.foregroundImageY=void 0,v=this.foregroundImagePadding=0;this.foregroundImageBackgroundColor="#FFFFFF";var m=this.foregroundImageBorderRadius=0,y=this.foregroundImageShadowOffsetX=0,w=this.foregroundImageShadowOffsetY=0,k=this.foregroundImageShadowBlur=0;this.foregroundImageShadowColor="#808080";var x=this.foregroundPadding=0,S=this.positionProbeBackgroundColor=void 0,O=this.positionProbeForegroundColor=void 0,T=this.separatorColor=void 0,P=this.positionAdjustBackgroundColor=void 0,C=this.positionAdjustForegroundColor=void 0,A=this.timingBackgroundColor=void 0,I=this.timingForegroundColor=void 0,E=this.typeNumberBackgroundColor=void 0,D=this.typeNumberForegroundColor=void 0,j=this.darkBlockColor=void 0;this.base=void 0,this.modules=[],this.moduleCount=0,this.drawModules=[];var L=this.canvasContext=void 0;this.loadImage,this.drawReserve=!1,this.isMaked=!1,Object.defineProperties(this,{data:{get:function(){if(""===r||void 0===r)throw console.error("[uQRCode]: data must be set!"),new b.Error("data must be set!");return r},set:function(e){r=String(e)}},size:{get:function(){return o},set:function(e){o=Number(e)}},typeNumber:{get:function(){return i},set:function(e){i=Number(e)}},margin:{get:function(){return a},set:function(e){a=Number(e)}},backgroundImageWidth:{get:function(){return void 0===s?this.dynamicSize:this.useDynamicSize?this.dynamicSize/this.size*s:s},set:function(e){s=Number(e)}},backgroundImageHeight:{get:function(){return void 0===u?this.dynamicSize:this.useDynamicSize?this.dynamicSize/this.size*u:u},set:function(e){u=Number(e)}},backgroundImageX:{get:function(){return void 0===c?0:this.useDynamicSize?this.dynamicSize/this.size*c:c},set:function(e){c=Number(e)}},backgroundImageY:{get:function(){return void 0===l?0:this.useDynamicSize?this.dynamicSize/this.size*l:l},set:function(e){l=Number(e)}},backgroundPadding:{get:function(){return f},set:function(e){f=e>1?1:e<0?0:e}},foregroundImageWidth:{get:function(){return void 0===d?(this.dynamicSize-2*this.margin)/4:this.useDynamicSize?this.dynamicSize/this.size*d:d},set:function(e){d=Number(e)}},foregroundImageHeight:{get:function(){return void 0===h?(this.dynamicSize-2*this.margin)/4:this.useDynamicSize?this.dynamicSize/this.size*h:h},set:function(e){h=Number(e)}},foregroundImageX:{get:function(){return void 0===p?this.dynamicSize/2-this.foregroundImageWidth/2:this.useDynamicSize?this.dynamicSize/this.size*p:p},set:function(e){p=Number(e)}},foregroundImageY:{get:function(){return void 0===g?this.dynamicSize/2-this.foregroundImageHeight/2:this.useDynamicSize?this.dynamicSize/this.size*g:g},set:function(e){g=Number(e)}},foregroundImagePadding:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*v:v},set:function(e){v=Number(e)}},foregroundImageBorderRadius:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*m:m},set:function(e){m=Number(e)}},foregroundImageShadowOffsetX:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*y:y},set:function(e){y=Number(e)}},foregroundImageShadowOffsetY:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*w:w},set:function(e){w=Number(e)}},foregroundImageShadowBlur:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*k:k},set:function(e){k=Number(e)}},foregroundPadding:{get:function(){return x},set:function(e){x=e>1?1:e<0?0:e}},positionProbeBackgroundColor:{get:function(){return S||this.backgroundColor},set:function(e){S=e}},positionProbeForegroundColor:{get:function(){return O||this.foregroundColor},set:function(e){O=e}},separatorColor:{get:function(){return T||this.backgroundColor},set:function(e){T=e}},positionAdjustBackgroundColor:{get:function(){return P||this.backgroundColor},set:function(e){P=e}},positionAdjustForegroundColor:{get:function(){return C||this.foregroundColor},set:function(e){C=e}},timingBackgroundColor:{get:function(){return A||this.backgroundColor},set:function(e){A=e}},timingForegroundColor:{get:function(){return I||this.foregroundColor},set:function(e){I=e}},typeNumberBackgroundColor:{get:function(){return E||this.backgroundColor},set:function(e){E=e}},typeNumberForegroundColor:{get:function(){return D||this.foregroundColor},set:function(e){D=e}},darkBlockColor:{get:function(){return j||this.foregroundColor},set:function(e){j=e}},canvasContext:{get:function(){if(void 0===L)throw console.error("[uQRCode]: use drawCanvas, you need to set the canvasContext!"),new b.Error("use drawCanvas, you need to set the canvasContext!");return L},set:function(e){L=_(e)}}}),b.plugins.forEach((function(e){return e(b,n,!1)})),e&&this.setOptions(e),t&&(this.canvasContext=_(t))}v.prototype={get:function(e){return this.num[e]},getLength:function(){return this.num.length},multiply:function(e){for(var t=new Array(this.getLength()+e.getLength()-1),n=0;n<this.getLength();n++)for(var r=0;r<e.getLength();r++)t[n+r]^=p.gexp(p.glog(this.get(n))+p.glog(e.get(r)));return new v(t,0)},mod:function(e){if(this.getLength()-e.getLength()<0)return this;for(var t=p.glog(this.get(0))-p.glog(e.get(0)),n=new Array(this.getLength()),r=0;r<this.getLength();r++)n[r]=this.get(r);for(r=0;r<e.getLength();r++)n[r]^=p.gexp(p.glog(e.get(r))+t);return new v(n,0).mod(e)}},m.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],m.getRSBlocks=function(e,t){var n=m.getRsBlockTable(e,t);if(null==n)throw new Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);for(var r=n.length/3,o=new Array,i=0;i<r;i++)for(var a=n[3*i+0],s=n[3*i+1],u=n[3*i+2],c=0;c<a;c++)o.push(new m(s,u));return o},m.getRsBlockTable=function(e,t){switch(t){case d.L:return m.RS_BLOCK_TABLE[4*(e-1)+0];case d.M:return m.RS_BLOCK_TABLE[4*(e-1)+1];case d.Q:return m.RS_BLOCK_TABLE[4*(e-1)+2];case d.H:return m.RS_BLOCK_TABLE[4*(e-1)+3];default:return}},y.prototype={get:function(e){var t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)},put:function(e,t){for(var n=0;n<t;n++)this.putBit(1==(e>>>t-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}},l.errorCorrectLevel=d,b.errorCorrectLevel=l.errorCorrectLevel,b.Error=function(e){this.errMsg="[uQRCode]: "+e},b.plugins=[],b.use=function(e){"function"==typeof e&&b.plugins.push(e)},b.prototype.loadImage=function(e){return Promise.resolve(e)},b.prototype.setOptions=function(e){var t,n,r,o,i,a,s,c,l,f,d,h,p,g,v,m,y,_,b,w,k,x,S,O,T,P,C,A,I,E,D,j,L,N,M,R,B,$,U,F,q,H,z,V,K,W,J,G,Y,X,Q,Z,ee,te,ne,re,oe=this;e&&(Object.keys(e).forEach((function(t){oe[t]=e[t]})),function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(var o in e=r?t:u({},t),n){var i=n[o];null!=i&&(i.constructor==Object?e[o]=this.deepReplace(e[o],i):i.constructor!=String||i?e[o]=i:e[o]=e[o])}}(this,{data:e.data||e.text,size:e.size,useDynamicSize:e.useDynamicSize,typeNumber:e.typeNumber,errorCorrectLevel:e.errorCorrectLevel,margin:e.margin,areaColor:e.areaColor,backgroundColor:e.backgroundColor||(null===(t=e.background)||void 0===t?void 0:t.color),backgroundImageSrc:e.backgroundImageSrc||(null===(n=e.background)||void 0===n||null===(r=n.image)||void 0===r?void 0:r.src),backgroundImageWidth:e.backgroundImageWidth||(null===(o=e.background)||void 0===o||null===(i=o.image)||void 0===i?void 0:i.width),backgroundImageHeight:e.backgroundImageHeight||(null===(a=e.background)||void 0===a||null===(s=a.image)||void 0===s?void 0:s.height),backgroundImageX:e.backgroundImageX||(null===(c=e.background)||void 0===c||null===(l=c.image)||void 0===l?void 0:l.x),backgroundImageY:e.backgroundImageY||(null===(f=e.background)||void 0===f||null===(d=f.image)||void 0===d?void 0:d.y),backgroundImageAlpha:e.backgroundImageAlpha||(null===(h=e.background)||void 0===h||null===(p=h.image)||void 0===p?void 0:p.alpha),backgroundImageBorderRadius:e.backgroundImageBorderRadius||(null===(g=e.background)||void 0===g||null===(v=g.image)||void 0===v?void 0:v.borderRadius),backgroundPadding:e.backgroundPadding,foregroundColor:e.foregroundColor||(null===(m=e.foreground)||void 0===m?void 0:m.color),foregroundImageSrc:e.foregroundImageSrc||(null===(y=e.foreground)||void 0===y||null===(_=y.image)||void 0===_?void 0:_.src),foregroundImageWidth:e.foregroundImageWidth||(null===(b=e.foreground)||void 0===b||null===(w=b.image)||void 0===w?void 0:w.width),foregroundImageHeight:e.foregroundImageHeight||(null===(k=e.foreground)||void 0===k||null===(x=k.image)||void 0===x?void 0:x.height),foregroundImageX:e.foregroundImageX||(null===(S=e.foreground)||void 0===S||null===(O=S.image)||void 0===O?void 0:O.x),foregroundImageY:e.foregroundImageY||(null===(T=e.foreground)||void 0===T||null===(P=T.image)||void 0===P?void 0:P.y),foregroundImagePadding:e.foregroundImagePadding||(null===(C=e.foreground)||void 0===C||null===(A=C.image)||void 0===A?void 0:A.padding),foregroundImageBackgroundColor:e.foregroundImageBackgroundColor||(null===(I=e.foreground)||void 0===I||null===(E=I.image)||void 0===E?void 0:E.backgroundColor),foregroundImageBorderRadius:e.foregroundImageBorderRadius||(null===(D=e.foreground)||void 0===D||null===(j=D.image)||void 0===j?void 0:j.borderRadius),foregroundImageShadowOffsetX:e.foregroundImageShadowOffsetX||(null===(L=e.foreground)||void 0===L||null===(N=L.image)||void 0===N?void 0:N.shadowOffsetX),foregroundImageShadowOffsetY:e.foregroundImageShadowOffsetY||(null===(M=e.foreground)||void 0===M||null===(R=M.image)||void 0===R?void 0:R.shadowOffsetY),foregroundImageShadowBlur:e.foregroundImageShadowBlur||(null===(B=e.foreground)||void 0===B||null===($=B.image)||void 0===$?void 0:$.shadowBlur),foregroundImageShadowColor:e.foregroundImageShadowColor||(null===(U=e.foreground)||void 0===U||null===(F=U.image)||void 0===F?void 0:F.shadowColor),foregroundPadding:e.foregroundPadding,positionProbeBackgroundColor:e.positionProbeBackgroundColor||(null===(q=e.positionProbe)||void 0===q?void 0:q.backgroundColor)||(null===(H=e.positionDetection)||void 0===H?void 0:H.backgroundColor),positionProbeForegroundColor:e.positionProbeForegroundColor||(null===(z=e.positionProbe)||void 0===z?void 0:z.foregroundColor)||(null===(V=e.positionDetection)||void 0===V?void 0:V.foregroundColor),separatorColor:e.separatorColor||(null===(K=e.separator)||void 0===K?void 0:K.color),positionAdjustBackgroundColor:e.positionAdjustBackgroundColor||(null===(W=e.positionAdjust)||void 0===W?void 0:W.backgroundColor)||(null===(J=e.alignment)||void 0===J?void 0:J.backgroundColor),positionAdjustForegroundColor:e.positionAdjustForegroundColor||(null===(G=e.positionAdjust)||void 0===G?void 0:G.foregroundColor)||(null===(Y=e.alignment)||void 0===Y?void 0:Y.foregroundColor),timingBackgroundColor:e.timingBackgroundColor||(null===(X=e.timing)||void 0===X?void 0:X.backgroundColor),timingForegroundColor:e.timingForegroundColor||(null===(Q=e.timing)||void 0===Q?void 0:Q.foregroundColor),typeNumberBackgroundColor:e.typeNumberBackgroundColor||(null===(Z=e.typeNumber)||void 0===Z?void 0:Z.backgroundColor)||(null===(ee=e.versionInformation)||void 0===ee?void 0:ee.backgroundColor),typeNumberForegroundColor:e.typeNumberForegroundColor||(null===(te=e.typeNumber)||void 0===te?void 0:te.foregroundColor)||(null===(ne=e.versionInformation)||void 0===ne?void 0:ne.foregroundColor),darkBlockColor:e.darkBlockColor||(null===(re=e.darkBlock)||void 0===re?void 0:re.color)},!0))},b.prototype.make=function(){var e=this.foregroundColor,t=this.backgroundColor,n=this.typeNumber,r=this.errorCorrectLevel,o=this.data,i=this.size,a=this.margin,s=this.useDynamicSize;if(e===t)throw console.error("[uQRCode]: foregroundColor and backgroundColor cannot be the same!"),new b.Error("foregroundColor and backgroundColor cannot be the same!");var u=new l(n,r);u.addData(function(e){e=e.toString();for(var t,n="",r=0;r<e.length;r++)(t=e.charCodeAt(r))>=1&&t<=127?n+=e.charAt(r):t>2047?(n+=String.fromCharCode(224|t>>12&15),n+=String.fromCharCode(128|t>>6&63),n+=String.fromCharCode(128|t>>0&63)):(n+=String.fromCharCode(192|t>>6&31),n+=String.fromCharCode(128|t>>0&63));return n}(o)),u.make(),this.base=u,this.typeNumber=u.typeNumber,this.modules=u.modules,this.moduleCount=u.moduleCount,this.dynamicSize=s?Math.ceil((i-2*a)/u.moduleCount)*u.moduleCount+2*a:i,function(e){var t=e.dynamicSize,n=e.margin,r=e.backgroundColor,o=e.backgroundPadding,i=e.foregroundColor,a=e.foregroundPadding,s=e.modules,u=e.moduleCount,c=(t-2*n)/u,l=c,f=0;o>0&&(f=l*o/2,l-=2*f);var d=c,h=0;a>0&&(h=d*a/2,d-=2*h);for(var p=0;p<u;p++)for(var g=0;g<u;g++){var v=g*c+n,m=p*c+n;if(s[p][g]){var y=h,_=v+h,b=m+h,w=d,k=d;s[p][g]={type:["foreground"],color:i,isBlack:!0,isDrawn:!1,destX:v,destY:m,destWidth:c,destHeight:c,x:_,y:b,width:w,height:k,paddingTop:y,paddingRight:y,paddingBottom:y,paddingLeft:y}}else y=f,_=v+f,b=m+f,w=l,k=l,s[p][g]={type:["background"],color:r,isBlack:!1,isDrawn:!1,destX:v,destY:m,destWidth:c,destHeight:c,x:_,y:b,width:w,height:k,paddingTop:y,paddingRight:y,paddingBottom:y,paddingLeft:y}}}(this),function(e){var t=e.modules,n=e.moduleCount,r=e.positionProbeBackgroundColor,o=e.positionProbeForegroundColor,i=n-7;[[0,0,1],[1,0,1],[2,0,1],[3,0,1],[4,0,1],[5,0,1],[6,0,1],[0,1,1],[1,1,0],[2,1,0],[3,1,0],[4,1,0],[5,1,0],[6,1,1],[0,2,1],[1,2,0],[2,2,1],[3,2,1],[4,2,1],[5,2,0],[6,2,1],[0,3,1],[1,3,0],[2,3,1],[3,3,1],[4,3,1],[5,3,0],[6,3,1],[0,4,1],[1,4,0],[2,4,1],[3,4,1],[4,4,1],[5,4,0],[6,4,1],[0,5,1],[1,5,0],[2,5,0],[3,5,0],[4,5,0],[5,5,0],[6,5,1],[0,6,1],[1,6,1],[2,6,1],[3,6,1],[4,6,1],[5,6,1],[6,6,1]].forEach((function(e){var n=t[e[0]][e[1]],a=t[e[0]+i][e[1]],s=t[e[0]][e[1]+i];s.type.push("positionProbe"),a.type.push("positionProbe"),n.type.push("positionProbe"),n.color=1==e[2]?o:r,a.color=1==e[2]?o:r,s.color=1==e[2]?o:r}))}(this),function(e){var t=e.modules,n=e.moduleCount,r=e.separatorColor;[[7,0],[7,1],[7,2],[7,3],[7,4],[7,5],[7,6],[7,7],[0,7],[1,7],[2,7],[3,7],[4,7],[5,7],[6,7]].forEach((function(e){var o=t[e[0]][e[1]],i=t[n-e[0]-1][e[1]],a=t[e[0]][n-e[1]-1];a.type.push("separator"),i.type.push("separator"),o.type.push("separator"),o.color=r,i.color=r,a.color=r}))}(this),function(e){var t=e.typeNumber,n=e.modules,r=e.moduleCount,o=e.foregroundColor,i=e.backgroundColor,a=e.positionAdjustForegroundColor,s=e.positionAdjustBackgroundColor,u=e.timingForegroundColor,c=e.timingBackgroundColor,l=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]][t-1];if(l)for(var f=[[-2,-2,1],[-1,-2,1],[0,-2,1],[1,-2,1],[2,-2,1],[-2,-1,1],[-1,-1,0],[0,-1,0],[1,-1,0],[2,-1,1],[-2,0,1],[-1,0,0],[0,0,1],[1,0,0],[2,0,1],[-2,1,1],[-1,1,0],[0,1,0],[1,1,0],[2,1,1],[-2,2,1],[-1,2,1],[0,2,1],[1,2,1],[2,2,1]],d=l.length,h=0;h<d;h++)for(var p=function(e){var t={x:l[h],y:l[e]},d=t.x,p=t.y;d<9&&p<9||d>r-9-1&&p<9||p>r-9-1&&d<9||f.forEach((function(e){var t=n[d+e[0]][p+e[1]];t.type.push("positionAdjust"),t.type.includes("timing")?1==e[2]?t.color=a==o?u:a:t.color=a==o&&s==i?c:s:t.color=1==e[2]?a:s}))},g=0;g<d;g++)p(g)}(this),function(e){for(var t=e.modules,n=e.moduleCount,r=e.timingForegroundColor,o=e.timingBackgroundColor,i=n-16,a=0;a<i;a++){var s=t[6][8+a],u=t[8+a][6];s.type.push("timing"),u.type.push("timing"),s.color=1&a^1?r:o,u.color=1&a^1?r:o}}(this),function(e){var t=e.modules,n=e.moduleCount,r=e.darkBlockColor,o=t[n-7-1][8];o.type.push("darkBlock"),o.color=r}(this),function(e){var t=e.typeNumber,n=e.modules,r=e.moduleCount,o=e.typeNumberBackgroundColor,i=e.typeNumberForegroundColor;if(t<7)return n;var a=[0,0,0,0,0,0,0,"000111110010010100","001000010110111100","001001101010011001","001010010011010011","001011101111110110","001100011101100010","001101100001000111","001110011000001101","001111100100101000","010000101101111000","010001010001011101","010010101000010111","010011010100110010","010100100110100110","010101011010000011","010110100011001001","010111011111101100","011000111011000100","011001000111100001","011010111110101011","011011000010001110","011100110000011010","011101001100111111","011110110101110101","011111001001010000","100000100111010101","100001011011110000","100010100010111010","100011011110011111","100100101100001011","100101010000101110","100110101001100100","100111010101000001","101000110001101001"],s=a[t]+a[t],u=[r-11,r-10,r-9];[[5,u[2]],[5,u[1]],[5,u[0]],[4,u[2]],[4,u[1]],[4,u[0]],[3,u[2]],[3,u[1]],[3,u[0]],[2,u[2]],[2,u[1]],[2,u[0]],[1,u[2]],[1,u[1]],[1,u[0]],[0,u[2]],[0,u[1]],[0,u[0]],[u[2],5],[u[1],5],[u[0],5],[u[2],4],[u[1],4],[u[0],4],[u[2],3],[u[1],3],[u[0],3],[u[2],2],[u[1],2],[u[0],2],[u[2],1],[u[1],1],[u[0],1],[u[2],0],[u[1],0],[u[0],0]].forEach((function(e,t){var r=n[e[0]][e[1]];r.type.push("typeNumber"),r.color="1"==s[t]?i:o}))}(this),this.isMaked=!0,this.drawModules=[]},b.prototype.getDrawModules=function(){if(this.drawModules&&this.drawModules.length>0)return this.drawModules;var e=this.drawModules=[],t=this.modules,n=this.moduleCount,r=this.dynamicSize,o=this.areaColor,i=this.backgroundImageSrc,a=this.backgroundImageX,s=this.backgroundImageY,u=this.backgroundImageWidth,c=this.backgroundImageHeight,l=this.backgroundImageAlpha,f=this.backgroundImageBorderRadius,d=this.foregroundImageSrc,h=this.foregroundImageX,p=this.foregroundImageY,g=this.foregroundImageWidth,v=this.foregroundImageHeight,m=this.foregroundImagePadding,y=this.foregroundImageBackgroundColor,_=this.foregroundImageBorderRadius,b=this.foregroundImageShadowOffsetX,w=this.foregroundImageShadowOffsetY,k=this.foregroundImageShadowBlur,x=this.foregroundImageShadowColor;o&&e.push({name:"area",type:"area",color:o,x:0,y:0,width:r,height:r}),i&&e.push({name:"backgroundImage",type:"image",imageSrc:i,mappingName:"backgroundImageSrc",x:a,y:s,width:u,height:c,alpha:l,borderRadius:f});for(var S=0;S<n;S++)for(var O=0;O<n;O++){var T=t[S][O];T.isDrawn||(T.type.includes("foreground")?e.push({name:"foreground",type:"tile",color:T.color,destX:T.destX,destY:T.destY,destWidth:T.destWidth,destHeight:T.destHeight,x:T.x,y:T.y,width:T.width,height:T.height,paddingTop:T.paddingTop,paddingRight:T.paddingRight,paddingBottom:T.paddingBottom,paddingLeft:T.paddingLeft,rowIndex:S,colIndex:O}):e.push({name:"background",type:"tile",color:T.color,destX:T.destX,destY:T.destY,destWidth:T.destWidth,destHeight:T.destHeight,x:T.x,y:T.y,width:T.width,height:T.height,paddingTop:T.paddingTop,paddingRight:T.paddingRight,paddingBottom:T.paddingBottom,paddingLeft:T.paddingLeft,rowIndex:S,colIndex:O}),T.isDrawn=!0)}return d&&e.push({name:"foregroundImage",type:"image",imageSrc:d,mappingName:"foregroundImageSrc",x:h,y:p,width:g,height:v,padding:m,backgroundColor:y,borderRadius:_,shadowOffsetX:b,shadowOffsetY:w,shadowBlur:k,shadowColor:x}),e},b.prototype.isBlack=function(e,t){var n=this.moduleCount;return!(0>e||0>t||e>=n||t>=n)&&this.modules[e][t].isBlack},b.prototype.drawCanvas=function(){var e=this,t=this.isMaked,n=this.canvasContext,r=(this.useDynamicSize,this.dynamicSize),a=(this.foregroundColor,this.foregroundPadding,this.backgroundColor,this.backgroundPadding,this.drawReserve);this.margin;if(!t)return console.error("[uQRCode]: please execute the make method first!"),Promise.reject(new b.Error("please execute the make method first!"));var s=this.getDrawModules(),u=function(){var t=(0,i.default)(o.default.mark((function t(i,u){var c,l,f,d,h,p,g,v,m,y,_,w,k,x;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:t.prev=0,n.clearRect(0,0,r,r),n.draw(!1),c=0;case 3:if(!(c<s.length)){t.next=48;break}l=s[c],t.t0=(n.save(),l.type),t.next="area"===t.t0?8:"tile"===t.t0?10:"image"===t.t0?13:44;break;case 8:return n.setFillStyle(l.color),n.fillRect(l.x,l.y,l.width,l.height),t.abrupt("break",44);case 10:return f=l.x,d=l.y,h=l.width,p=l.height,n.setFillStyle(l.color),n.fillRect(f,d,h,p),t.abrupt("break",44);case 13:if("backgroundImage"!==l.name){t.next=28;break}return f=Math.round(l.x),d=Math.round(l.y),h=Math.round(l.width),p=Math.round(l.height),h<2*(v=Math.round(l.borderRadius))&&(v=h/2),p<2*v&&(v=p/2),n.setGlobalAlpha(l.alpha),v>0&&(n.beginPath(),n.moveTo(f+v,d),n.arcTo(f+h,d,f+h,d+p,v),n.arcTo(f+h,d+p,f,d+p,v),n.arcTo(f,d+p,f,d,v),n.arcTo(f,d,f+h,d,v),n.closePath(),n.setStrokeStyle("rgba(0,0,0,0)"),n.stroke(),n.clip()),t.prev=16,t.next=19,e.loadImage(l.imageSrc);case 19:g=t.sent,n.drawImage(g,f,d,h,p),t.next=26;break;case 23:throw t.prev=23,t.t1=t["catch"](16),console.error("[uQRCode]: ".concat(l.mappingName," invalid!")),new b.Error("".concat(l.mappingName," invalid!"));case 26:t.next=44;break;case 28:if("foregroundImage"!==l.name){t.next=44;break}return f=Math.round(l.x),d=Math.round(l.y),h=Math.round(l.width),p=Math.round(l.height),m=Math.round(l.padding),h<2*(v=Math.round(l.borderRadius))&&(v=h/2),p<2*v&&(v=p/2),y=f-m,_=d-m,w=h+2*m,k=p+2*m,x=Math.round(w/h*v),w<2*x&&(x=w/2),k<2*x&&(x=k/2),n.save(),n.setShadow(l.shadowOffsetX,l.shadowOffsetY,l.shadowBlur,l.shadowColor),x>0?(n.beginPath(),n.moveTo(y+x,_),n.arcTo(y+w,_,y+w,_+k,x),n.arcTo(y+w,_+k,y,_+k,x),n.arcTo(y,_+k,y,_,x),n.arcTo(y,_,y+w,_,x),n.closePath(),n.setFillStyle(l.backgroundColor),n.fill()):(n.setFillStyle(l.backgroundColor),n.fillRect(y,_,w,k)),n.restore(),n.save(),x>0?(n.beginPath(),n.moveTo(y+x,_),n.arcTo(y+w,_,y+w,_+k,x),n.arcTo(y+w,_+k,y,_+k,x),n.arcTo(y,_+k,y,_,x),n.arcTo(y,_,y+w,_,x),n.closePath(),n.setFillStyle(m>0?l.backgroundColor:"rgba(0,0,0,0)"),n.fill()):(n.setFillStyle(m>0?l.backgroundColor:"rgba(0,0,0,0)"),n.fillRect(y,_,w,k)),n.restore(),v>0&&(n.beginPath(),n.moveTo(f+v,d),n.arcTo(f+h,d,f+h,d+p,v),n.arcTo(f+h,d+p,f,d+p,v),n.arcTo(f,d+p,f,d,v),n.arcTo(f,d,f+h,d,v),n.closePath(),n.setStrokeStyle("rgba(0,0,0,0)"),n.stroke(),n.clip()),t.prev=34,t.next=37,e.loadImage(l.imageSrc);case 37:g=t.sent,n.drawImage(g,f,d,h,p),t.next=44;break;case 41:throw t.prev=41,t.t2=t["catch"](34),console.error("[uQRCode]: ".concat(l.mappingName," invalid!")),new b.Error("".concat(l.mappingName," invalid!"));case 44:a&&n.draw(!0),n.restore();case 45:c++,t.next=3;break;case 48:n.draw(!0),setTimeout(i,150),t.next=56;break;case 51:if(t.prev=51,t.t3=t["catch"](0),t.t3 instanceof b.Error){t.next=55;break}throw t.t3;case 55:u(t.t3);case 56:case"end":return t.stop()}}),t,null,[[0,51],[16,23],[34,41]])})));return function(e,n){return t.apply(this,arguments)}}();return new Promise((function(e,t){u(e,t)}))},b.prototype.draw=function(){return this.drawCanvas()},b.prototype.register=function(e){e&&e(b,this,!0)}},"5d29":function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("3240")),i=r(n("8f59")),a=r(n("df01"));o.default.use(i.default);var s=new i.default.Store({modules:{user:a.default},strict:!1});t.default=s},6382:function(e,t,n){var r=n("6454");e.exports=function(e,t){if(e){if("string"===typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}},e.exports.__esModule=!0,e.exports["default"]=e.exports},6454:function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports["default"]=e.exports},6492:function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("8f3c")),i=r(n("ab20")),a=r(n("6caf")),s={en:o.default,"zh-Hans":i.default,"zh-Hant":a.default};t.default=s},"67ad":function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports["default"]=e.exports},"696a":function(e,t,n){"use strict";(function(e,r){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.calculateDistance=m,t.checkGPSEnabled=S,t.checkLocationPermission=k,t.chooseLocation=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n={latitude:0,longitude:0,keyword:""},o=c(c({},n),t);return new Promise((function(t,n){var i={};o.latitude&&o.longitude&&(i.latitude=o.latitude,i.longitude=o.longitude),o.keyword&&(i.keyword=o.keyword),r.chooseLocation?r.chooseLocation(c(c({},i),{},{success:function(e){t({name:e.name||"",address:e.address||"",latitude:e.latitude,longitude:e.longitude,formatted_address:e.address||""})},fail:function(e){e.errMsg&&e.errMsg.includes("cancel")?n(Object.assign(e,{code:"USER_CANCEL"})):n(e)}})):e.chooseLocation(c(c({},i),{},{success:function(e){t({name:e.name||"",address:e.address||"",latitude:e.latitude,longitude:e.longitude,formatted_address:e.address||""})},fail:function(e){e.errMsg&&e.errMsg.includes("cancel")?n(Object.assign(e,{code:"USER_CANCEL"})):n(e)}}))}))},t.choosePoi=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n={latitude:0,longitude:0,radius:5e3,category:"",keyword:""},r=c(c({},n),t);return new Promise((function(t,n){var o={};r.latitude&&r.longitude&&(o.latitude=r.latitude,o.longitude=r.longitude),r.radius&&(o.radius=r.radius),r.category&&(o.category=r.category),r.keyword&&(o.keyword=r.keyword),e.choosePoi(c(c({},o),{},{success:function(e){t(c(c({},e),{},{pois:Array.isArray(e.pois)?e.pois.map((function(e){return{name:e.name||"",address:e.address||"",latitude:e.latitude,longitude:e.longitude,category:e.category||"",type:e.type||0}})):[]}))},fail:function(e){e.errMsg&&e.errMsg.includes("cancel")?n(Object.assign(e,{code:"USER_CANCEL"})):n(e)}}))}))},t.formatDistance=function(e){return e<1?"小于1米":e<1e3?"".concat(Math.round(e),"米"):"".concat((e/1e3).toFixed(1),"公里")},t.formatLocation=function(e){if(!e||!e.longitude||!e.latitude)return"未知位置";var t=Number(e.longitude).toFixed(6),n=Number(e.latitude).toFixed(6);return"".concat(t,",").concat(n)},t.getCurrentLocation=h,t.getEnhancedLocation=function(){return v(2,{isHighAccuracy:!0}).then((function(e){return y(e).then((function(t){return c(c({},e),{},{addressInfo:t,accuracy:e.accuracy,enhancedSource:"GPS+GEO"})})).catch((function(t){return console.warn("逆地理编码失败，返回原始位置信息:",t),c(c({},e),{},{enhancedSource:"GPS"})}))})).catch((function(e){return console.warn("高精度定位失败，尝试模糊定位:",e),p().then((function(e){return c(c({},e),{},{enhancedSource:"FUZZY",isFuzzy:!0})}))}))},t.getFuzzyLocation=p,t.getLocation=d,t.getLocationWithChecks=function(){return O.apply(this,arguments)},t.getLocationWithRetry=v,t.getLoggedinUserInfo=function(){try{var t=e.getStorageSync("userInfo");return t?"string"===typeof t?JSON.parse(t):t:null}catch(n){return console.error("获取用户信息失败:",n),null}},t.getSignalQuality=function(e){return e<=10?{level:"excellent",text:"极好",color:"#34C759",value:5}:e<=20?{level:"good",text:"良好",color:"#00C58E",value:4}:e<=35?{level:"moderate",text:"一般",color:"#FFD60A",value:3}:e<=50?{level:"poor",text:"较差",color:"#FF9500",value:2}:e<=80?{level:"very-poor",text:"很差",color:"#FF6B2C",value:1}:{level:"extremely-poor",text:"极差",color:"#FF3B30",value:0}},t.isInRange=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:50;if(!e||!t)return console.warn("isInRange: 位置信息不完整",e,t),!1;var r="number"===typeof n&&n>0?n:50,o=m(e,t);return console.log("位置范围检查:",{current:"".concat(e.latitude,",").concat(e.longitude),checkpoint:"".concat(t.latitude,",").concat(t.longitude),distance:Math.round(o),range:r,inRange:o>=0&&o<=r}),o>=0&&o<=r},t.isLocationAccuracyAcceptable=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;if(!e||"number"!==typeof e.accuracy)return!1;return e.accuracy<=t},t.parseLocation=l,t.requestLocationPermission=x,t.reverseGeocoding=y,t.startLocationUpdate=g,t.startLocationUpdateBackground=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(o,i){var a=c(c({},{type:"gcj02",isHighAccuracy:!0,highAccuracyExpireTime:3e3,allowBackgroundUpdates:!0,interval:5e3}),n);if(!r.startLocationUpdateBackground)return console.warn("当前环境不支持后台定位，将使用普通定位"),g(t,n);r.startLocationUpdateBackground(c(c({},a),{},{success:function(){var n=e.onLocationChange((function(e){try{if("function"===typeof t){var n=l(e);n.receivedAt=Date.now(),t(n)}}catch(r){console.error("位置变化处理错误:",r)}}));o({success:!0,watchId:n})},fail:function(e){console.error("开启后台位置监听失败:",e),console.warn("后台定位失败，降级到普通定位"),g(t,n).then((function(e){o({success:!0,watchId:e,isFallback:!0})})).catch(i)}}))}))},t.stopLocationUpdate=function(t){try{e.stopLocationUpdate({complete:function(){try{e.offLocationChange()}catch(t){console.error("清理位置监听失败:",t)}}})}catch(n){console.error("停止位置监听失败:",n)}},t.throttleLocationCallback=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2e3,n=0,r=null;return function(o){var i=Date.now(),a=i-n;if(!r||a>=t)return n=i,r=o,void e(o);if(r&&o){var s=m(r,o),u=s>10;(u||r.accuracy&&o.accuracy&&r.accuracy>1.5*o.accuracy)&&(n=i,r=o,e(o))}}};var i=o(n("7eb4")),a=o(n("ee10")),s=o(n("7ca3"));function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){(0,s.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function l(e){return e?{latitude:e.latitude||0,longitude:e.longitude||0,accuracy:e.accuracy||e.horizontalAccuracy||0,altitude:e.altitude||0,speed:e.speed||0,verticalAccuracy:e.verticalAccuracy||0,horizontalAccuracy:e.horizontalAccuracy||e.accuracy||0,timestamp:e.timestamp||Date.now()}:null}var f={type:"gcj02",isHighAccuracy:!0,highAccuracyExpireTime:5e3,timeout:1e4};function d(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=c(c({},f),t);return new Promise((function(t,r){e.getLocation(c(c({},n),{},{success:function(e){t(e)},fail:function(e){r(e)}}))}))}function h(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n={type:"gcj02",isHighAccuracy:!0,highAccuracyExpireTime:3e3,timeout:8e3,maxRetries:3,retryDelay:1e3,fallbackToLowAccuracy:!0},r=c(c({},n),t),o=0;function s(){return new Promise((function(t,n){e.getLocation({type:r.type,isHighAccuracy:r.isHighAccuracy,highAccuracyExpireTime:r.highAccuracyExpireTime,timeout:r.timeout,success:function(e){var o=l(e);o&&o.latitude&&o.longitude?(r.isHighAccuracy&&o.accuracy>100&&console.warn("位置精度较低:",o.accuracy),t(o)):n(new Error("获取到的位置数据无效"))},fail:function(){var e=(0,a.default)(i.default.mark((function e(a){var u;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.error("获取位置失败(第".concat(o+1,"次):"),a),!r.isHighAccuracy||!r.fallbackToLowAccuracy){e.next=13;break}return e.prev=2,e.next=5,_();case 5:return u=e.sent,t(u),e.abrupt("return");case 10:e.prev=10,e.t0=e["catch"](2),console.error("降级定位也失败:",e.t0);case 13:o<r.maxRetries?(o++,console.log("将在".concat(r.retryDelay,"ms后重试(").concat(o,"/").concat(r.maxRetries,")")),setTimeout((function(){s().then(t).catch(n)}),r.retryDelay)):n(new Error("获取位置失败，已重试".concat(r.maxRetries,"次: ").concat(a.errMsg||a.message)));case 14:case"end":return e.stop()}}),e,null,[[2,10]])})));return function(t){return e.apply(this,arguments)}}()})}))}return s()}function p(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={type:"gcj02",maxRetries:2,retryDelay:1e3},n=c(c({},t),e),o=0;function i(){return new Promise((function(e,t){if(!r.getFuzzyLocation)return console.warn("当前环境不支持模糊定位，将使用普通定位"),void d(c({isHighAccuracy:!1},n)).then(e).catch(t);r.getFuzzyLocation({type:n.type,success:function(t){e(l(t))},fail:function(r){console.error("获取模糊位置失败(第".concat(o+1,"次):"),r),o<n.maxRetries?(o++,setTimeout((function(){i().then(e).catch(t)}),n.retryDelay)):(console.warn("模糊定位失败，降级到普通定位"),d(c({isHighAccuracy:!1},n)).then((function(t){e(c(c({},t),{},{isFallback:!0,accuracy:Math.max(t.accuracy,1e3)}))})).catch(t))}})}))}return i()}function g(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(r,o){var i=c(c({},{type:"gcj02",isHighAccuracy:!0,highAccuracyExpireTime:3e3,allowBackgroundUpdates:!0}),n);e.startLocationUpdate(c(c({},i),{},{success:function(){var n=e.onLocationChange((function(e){try{if("function"===typeof t){var n=l(e);t(n)}}catch(r){console.error("位置变化处理错误:",r)}}));r(n)},fail:function(e){console.error("开启位置监听失败:",e),o(e)}}))}))}function v(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=0;function r(){return d(t).catch((function(t){if(n++,n>=e)throw new Error("位置获取失败，已重试".concat(e,"次: ").concat(t.errMsg||t.message));return new Promise((function(e){return setTimeout(e,1e3)})).then(r)}))}return r()}function m(e,t){if(!e||!t)return 0;var n=e.latitude,r=e.longitude,o=t.latitude,i=t.longitude,a=n*Math.PI/180,s=o*Math.PI/180,u=a-s,c=r*Math.PI/180-i*Math.PI/180,l=2*Math.asin(Math.sqrt(Math.pow(Math.sin(u/2),2)+Math.cos(a)*Math.cos(s)*Math.pow(Math.sin(c/2),2)));return 6378137*l}function y(e){return new Promise((function(t,n){e&&e.latitude&&e.longitude?r.request({url:"https://apis.map.qq.com/ws/geocoder/v1/",data:{location:"".concat(e.latitude,",").concat(e.longitude),key:"5MPBZ-FCW63-3C43B-R7G72-UOSAO-ZWBTJ",get_poi:1},success:function(e){if(e.data&&0===e.data.status)t(e.data.result);else{var r=new Error("逆地理编码失败: ".concat(e.data.message||"未知错误"));n(r)}},fail:function(e){n(e)}}):n(new Error("无效的位置信息"))}))}function _(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new Promise((function(n,o){var i=Object.assign({},t);i.isHighAccuracy=!1,r.getLocation?r.getLocation(c(c({},i),{},{success:function(e){n(e)},fail:function(e){o(e)}})):e.getLocation(c(c({},i),{},{success:function(e){n(e)},fail:function(e){o(e)}}))}))}var b={aggressive:{cacheTimeout:36e5,description:"1小时缓存，最大减少API调用，适合稳定环境"},balanced:{cacheTimeout:12e4,description:"2分钟缓存，性能与实时性平衡，推荐使用"},conservative:{cacheTimeout:3e4,description:"30秒缓存，高实时性，权限变更响应快"},debug:{cacheTimeout:1e4,description:"10秒缓存，便于开发测试"}}.aggressive,w={hasPermission:null,lastCheck:0,cacheTimeout:b.cacheTimeout};function k(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return new Promise((function(n){var r=Date.now();if(!t&&null!==w.hasPermission&&r-w.lastCheck<w.cacheTimeout)return console.log("🗂️ 使用权限缓存:",w.hasPermission),void n(w.hasPermission);console.log("🔍 检查位置权限..."),e.getSetting({success:function(e){var t=!!e.authSetting["scope.userLocation"];w.hasPermission=t,w.lastCheck=r,console.log("✅ 位置权限检查完成:",t),n(t)},fail:function(){w.hasPermission=!1,w.lastCheck=r-w.cacheTimeout+1e4,console.log("❌ 位置权限检查失败"),n(!1)}})}))}function x(){return new Promise((function(t){e.authorize({scope:"scope.userLocation",success:function(){return t(!0)},fail:function(){return t(!1)}})}))}function S(){return new Promise((function(t){"android"===e.getSystemInfoSync().platform?e.getSystemInfo({success:function(e){t(e.locationEnabled||!1)},fail:function(){t(!1)}}):t(!0)}))}function O(){return O=(0,a.default)(i.default.mark((function e(){var t,n,r,o,a=arguments;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=a.length>0&&void 0!==a[0]?a[0]:{},e.next=3,k();case 3:if(n=e.sent,n){e.next=10;break}return e.next=7,x();case 7:if(r=e.sent,r){e.next=10;break}throw new Error("未获得位置权限");case 10:return e.next=12,S();case 12:if(o=e.sent,o){e.next=15;break}throw new Error("请开启GPS定位");case 15:return e.abrupt("return",h(t));case 16:case"end":return e.stop()}}),e)}))),O.apply(this,arguments)}}).call(this,n("df3c")["default"],n("3223")["default"])},"6aea":function(e){e.exports=JSON.parse('{"uniCloud.component.add.success":"新增成功","uniCloud.component.update.success":"修改成功","uniCloud.component.update.showModal.title":"提示","uniCloud.component.update.showModal.content":"是否更新该数据","uniCloud.component.remove.showModal.title":"提示","uniCloud.component.remove.showModal.content":"是否删除该数据"}')},"6caf":function(e){e.exports=JSON.parse('{"uni-search-bar.cancel":"取消","uni-search-bar.placeholder":"請輸入搜索內容"}')},"6f50":function(e){e.exports=JSON.parse('{"uni-pagination.prevText":"上一頁","uni-pagination.nextText":"下一頁","uni-pagination.piecePerPage":"條/頁"}')},7172:function(e,t){e.exports=function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],u=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);u=!0);}catch(e){c=!0,o=e}finally{try{if(!u&&null!=n["return"]&&(a=n["return"](),Object(a)!==a))return}finally{if(c)throw o}}return s}},e.exports.__esModule=!0,e.exports["default"]=e.exports},7456:function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("7eb4")),i=r(n("8ffa")),a=r(n("4ffb")),s=r(n("b4d2")),u=r(n("ee10")),c=r(n("67ad")),l=r(n("0bdb")),f=r(n("3b2d"));function d(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=(0,s.default)(e);if(t){var o=(0,s.default)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return(0,a.default)(this,n)}}var h={email:/^\S+?@\S+?\.\S+?$/,idcard:/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,url:new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i")},p={int:"integer",bool:"boolean",double:"number",long:"number",password:"string"};function g(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=["label"];n.forEach((function(t){void 0===e[t]&&(e[t]="")}));var r=t;for(var o in e){var i=new RegExp("{"+o+"}");r=r.replace(i,e[o])}return r}var v={integer:function(e){return v.number(e)&&parseInt(e,10)===e},string:function(e){return"string"===typeof e},number:function(e){return!isNaN(e)&&"number"===typeof e},boolean:function(e){return"boolean"===typeof e},float:function(e){return v.number(e)&&!v.integer(e)},array:function(e){return Array.isArray(e)},object:function(e){return"object"===(0,f.default)(e)&&!v.array(e)},date:function(e){return e instanceof Date},timestamp:function(e){return!(!this.integer(e)||Math.abs(e).toString().length>16)},file:function(e){return"string"===typeof e.url},email:function(e){return"string"===typeof e&&!!e.match(h.email)&&e.length<255},url:function(e){return"string"===typeof e&&!!e.match(h.url)},pattern:function(e,t){try{return new RegExp(e).test(t)}catch(n){return!1}},method:function(e){return"function"===typeof e},idcard:function(e){return"string"===typeof e&&!!e.match(h.idcard)},"url-https":function(e){return this.url(e)&&e.startsWith("https://")},"url-scheme":function(e){return e.startsWith("://")},"url-web":function(e){return!1}},m=function(){function e(t){(0,c.default)(this,e),this._message=t}return(0,l.default)(e,[{key:"validateRule",value:function(){var e=(0,u.default)(o.default.mark((function e(t,n,r,i,a){var s,u,c,l,f,d,h,p,g;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(s=null,u=n.rules,c=u.findIndex((function(e){return e.required})),!(c<0)){e.next=8;break}if(null!==r&&void 0!==r){e.next=6;break}return e.abrupt("return",s);case 6:if("string"!==typeof r||r.length){e.next=8;break}return e.abrupt("return",s);case 8:if(l=this._message,void 0!==u){e.next=11;break}return e.abrupt("return",l["default"]);case 11:f=0;case 12:if(!(f<u.length)){e.next=35;break}if(d=u[f],h=this._getValidateType(d),Object.assign(d,{label:n.label||'["'.concat(t,'"]')}),!y[h]){e.next=20;break}if(s=y[h](d,r,l),null==s){e.next=20;break}return e.abrupt("break",35);case 20:if(!d.validateExpr){e.next=26;break}if(p=Date.now(),g=d.validateExpr(r,a,p),!1!==g){e.next=26;break}return s=this._getMessage(d,d.errorMessage||this._message["default"]),e.abrupt("break",35);case 26:if(!d.validateFunction){e.next=32;break}return e.next=29,this.validateFunction(d,r,i,a,h);case 29:if(s=e.sent,null===s){e.next=32;break}return e.abrupt("break",35);case 32:f++,e.next=12;break;case 35:return null!==s&&(s=l.TAG+s),e.abrupt("return",s);case 37:case"end":return e.stop()}}),e,this)})));return function(t,n,r,o,i){return e.apply(this,arguments)}}()},{key:"validateFunction",value:function(){var e=(0,u.default)(o.default.mark((function e(t,n,r,i,a){var s,u,c;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return s=null,e.prev=1,u=null,e.next=5,t.validateFunction(t,n,i||r,(function(e){u=e}));case 5:c=e.sent,(u||"string"===typeof c&&c||!1===c)&&(s=this._getMessage(t,u||c,a)),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](1),s=this._getMessage(t,e.t0.message,a);case 12:return e.abrupt("return",s);case 13:case"end":return e.stop()}}),e,this,[[1,9]])})));return function(t,n,r,o,i){return e.apply(this,arguments)}}()},{key:"_getMessage",value:function(e,t,n){return g(e,t||e.errorMessage||this._message[n]||t["default"])}},{key:"_getValidateType",value:function(e){var t="";return e.required?t="required":e.format?t="format":e.arrayType?t="arrayTypeFormat":e.range?t="range":void 0!==e.maximum||void 0!==e.minimum?t="rangeNumber":void 0!==e.maxLength||void 0!==e.minLength?t="rangeLength":e.pattern?t="pattern":e.validateFunction&&(t="validateFunction"),t}}]),e}(),y={required:function(e,t,n){return e.required&&function(e,t){return void 0===e||null===e||("string"===typeof e&&!e||(!(!Array.isArray(e)||e.length)||"object"===t&&!Object.keys(e).length))}(t,e.format||(0,f.default)(t))?g(e,e.errorMessage||n.required):null},range:function(e,t,n){for(var r=e.range,o=e.errorMessage,i=new Array(r.length),a=0;a<r.length;a++){var s=r[a];v.object(s)&&void 0!==s.value?i[a]=s.value:i[a]=s}var u=!1;return Array.isArray(t)?u=new Set(t.concat(i)).size===i.length:i.indexOf(t)>-1&&(u=!0),u?null:g(e,o||n["enum"])},rangeNumber:function(e,t,n){if(!v.number(t))return g(e,e.errorMessage||n.pattern.mismatch);var r=e.minimum,o=e.maximum,i=e.exclusiveMinimum,a=e.exclusiveMaximum,s=i?t<=r:t<r,u=a?t>=o:t>o;return void 0!==r&&s?g(e,e.errorMessage||n["number"][i?"exclusiveMinimum":"minimum"]):void 0!==o&&u?g(e,e.errorMessage||n["number"][a?"exclusiveMaximum":"maximum"]):void 0!==r&&void 0!==o&&(s||u)?g(e,e.errorMessage||n["number"].range):null},rangeLength:function(e,t,n){if(!v.string(t)&&!v.array(t))return g(e,e.errorMessage||n.pattern.mismatch);var r=e.minLength,o=e.maxLength,i=t.length;return void 0!==r&&i<r?g(e,e.errorMessage||n["length"].minLength):void 0!==o&&i>o?g(e,e.errorMessage||n["length"].maxLength):void 0!==r&&void 0!==o&&(i<r||i>o)?g(e,e.errorMessage||n["length"].range):null},pattern:function(e,t,n){return v["pattern"](e.pattern,t)?null:g(e,e.errorMessage||n.pattern.mismatch)},format:function(e,t,n){var r=Object.keys(v),o=p[e.format]?p[e.format]:e.format||e.arrayType;return r.indexOf(o)>-1&&!v[o](t)?g(e,e.errorMessage||n.typeError):null},arrayTypeFormat:function(e,t,n){if(!Array.isArray(t))return g(e,e.errorMessage||n.typeError);for(var r=0;r<t.length;r++){var o=t[r],i=this.format(e,o,n);if(null!==i)return i}return null}},_=function(e){(0,i.default)(n,e);var t=d(n);function n(e,r){var o;return(0,c.default)(this,n),o=t.call(this,n.message),o._schema=e,o._options=r||null,o}return(0,l.default)(n,[{key:"updateSchema",value:function(e){this._schema=e}},{key:"validate",value:function(){var e=(0,u.default)(o.default.mark((function e(t,n){var r;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=this._checkFieldInSchema(t),r){e.next=5;break}return e.next=4,this.invokeValidate(t,!1,n);case 4:r=e.sent;case 5:return e.abrupt("return",r.length?r[0]:null);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"validateAll",value:function(){var e=(0,u.default)(o.default.mark((function e(t,n){var r;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=this._checkFieldInSchema(t),r){e.next=5;break}return e.next=4,this.invokeValidate(t,!0,n);case 4:r=e.sent;case 5:return e.abrupt("return",r);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"validateUpdate",value:function(){var e=(0,u.default)(o.default.mark((function e(t,n){var r;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=this._checkFieldInSchema(t),r){e.next=5;break}return e.next=4,this.invokeValidateUpdate(t,!1,n);case 4:r=e.sent;case 5:return e.abrupt("return",r.length?r[0]:null);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"invokeValidate",value:function(){var e=(0,u.default)(o.default.mark((function e(t,n,r){var i,a,s,u,c;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=[],a=this._schema,e.t0=o.default.keys(a);case 3:if((e.t1=e.t0()).done){e.next=15;break}return s=e.t1.value,u=a[s],e.next=8,this.validateRule(s,u,t[s],t,r);case 8:if(c=e.sent,null==c){e.next=13;break}if(i.push({key:s,errorMessage:c}),n){e.next=13;break}return e.abrupt("break",15);case 13:e.next=3;break;case 15:return e.abrupt("return",i);case 16:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}()},{key:"invokeValidateUpdate",value:function(){var e=(0,u.default)(o.default.mark((function e(t,n,r){var i,a,s;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=[],e.t0=o.default.keys(t);case 2:if((e.t1=e.t0()).done){e.next=13;break}return a=e.t1.value,e.next=6,this.validateRule(a,this._schema[a],t[a],t,r);case 6:if(s=e.sent,null==s){e.next=11;break}if(i.push({key:a,errorMessage:s}),n){e.next=11;break}return e.abrupt("break",13);case 11:e.next=2;break;case 13:return e.abrupt("return",i);case 14:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}()},{key:"_checkFieldInSchema",value:function(e){var t=Object.keys(e),r=Object.keys(this._schema);if(new Set(t.concat(r)).size===r.length)return"";var o=t.filter((function(e){return r.indexOf(e)<0})),i=g({field:JSON.stringify(o)},n.message.TAG+n.message["defaultInvalid"]);return[{key:"invalid",errorMessage:i}]}}]),n}(m);_.message=new function(){return{TAG:"",default:"验证错误",defaultInvalid:"提交的字段{field}在数据库中并不存在",validateFunction:"验证无效",required:"{label}必填",enum:"{label}超出范围",timestamp:"{label}格式无效",whitespace:"{label}不能为空",typeError:"{label}类型无效",date:{format:"{label}日期{value}格式无效",parse:"{label}日期无法解析,{value}无效",invalid:"{label}日期{value}无效"},length:{minLength:"{label}长度不能少于{minLength}",maxLength:"{label}长度不能超过{maxLength}",range:"{label}必须介于{minLength}和{maxLength}之间"},number:{minimum:"{label}不能小于{minimum}",maximum:"{label}不能大于{maximum}",exclusiveMinimum:"{label}不能小于等于{minimum}",exclusiveMaximum:"{label}不能大于等于{maximum}",range:"{label}必须介于{minimum}and{maximum}之间"},pattern:{mismatch:"{label}格式不匹配"}}};var b=_;t.default=b},"75cc":function(e){e.exports=JSON.parse('{"uni-datetime-picker.selectDate":"選擇日期","uni-datetime-picker.selectTime":"選擇時間","uni-datetime-picker.selectDateTime":"選擇日期時間","uni-datetime-picker.startDate":"開始日期","uni-datetime-picker.endDate":"結束日期","uni-datetime-picker.startTime":"開始时间","uni-datetime-picker.endTime":"結束时间","uni-datetime-picker.ok":"確定","uni-datetime-picker.clear":"清除","uni-datetime-picker.cancel":"取消","uni-datetime-picker.year":"年","uni-datetime-picker.month":"月","uni-calender.SUN":"日","uni-calender.MON":"一","uni-calender.TUE":"二","uni-calender.WED":"三","uni-calender.THU":"四","uni-calender.FRI":"五","uni-calender.SAT":"六","uni-calender.confirm":"確認"}')},7647:function(e,t){function n(t,r){return e.exports=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports["default"]=e.exports,n(t,r)}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"7ad1":function(e,t,n){(function(e){var t=n("3b2d");e.addInterceptor({returnValue:function(e){return!e||"object"!==t(e)&&"function"!==typeof e||"function"!==typeof e.then?e:new Promise((function(t,n){e.then((function(e){return e?e[0]?n(e[0]):t(e[1]):t(e)}))}))}})}).call(this,n("df3c")["default"])},"7b39":function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("c718")),i=r(n("e2bd")),a=r(n("a1f2")),s={en:o.default,"zh-Hans":i.default,"zh-Hant":a.default};t.default=s},"7ca3":function(e,t,n){var r=n("d551");e.exports=function(e,t,n){return t=r(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"7ce1":function(e,t,n){var r=n("b4d2"),o=n("7647"),i=n("4965"),a=n("931d");function s(t){var n="function"===typeof Map?new Map:void 0;return e.exports=s=function(e){if(null===e||!i(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof n){if(n.has(e))return n.get(e);n.set(e,t)}function t(){return a(e,arguments,r(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),o(t,e)},e.exports.__esModule=!0,e.exports["default"]=e.exports,s(t)}e.exports=s,e.exports.__esModule=!0,e.exports["default"]=e.exports},"7e11":function(e,t,n){"use strict";(function(e,r){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n("7eb4")),a=o(n("af34")),s=o(n("ee10")),u=o(n("67ad")),c=o(n("0bdb")),l=n("eddf"),f=function(){function t(){(0,u.default)(this,t),this.todoCount=0,this.initialized=!1,this.userRoles=[],this.debounceTimer=null,this.debounceDelay=100,this.isProcessing=!1,this.checkInterval=null,this.isClearing=!1,this.lastTabBarCheck=0,this.lastSyncTime=0,this.checkIntervalMs=15e3,this.setupAppStateListener()}return(0,c.default)(t,[{key:"setupAppStateListener",value:function(){var t=this;try{e.$on("feedback-updated",(function(){setTimeout((function(){t.forceRefresh()}),100)}))}catch(n){console.error("设置应用状态监听失败:",n)}}},{key:"onAppForeground",value:function(){this.forceRefresh()}},{key:"isTokenLikelyExpired",value:function(){try{var t=e.getStorageSync("uni_id_token_expired");return!!(t&&Date.now()>t)&&(this.handleTokenExpired(),!0)}catch(n){return!1}}},{key:"handleTokenExpired",value:function(){this.clearBadge(),e.removeStorageSync("uni-id-pages-userInfo"),e.removeStorageSync((0,l.getCacheKey)(l.CACHE_KEYS.USER_ROLE)),e.$emit("token-expired")}},{key:"hasSpecificRole",value:function(){try{if(this.isTokenLikelyExpired())return this.userRoles=[],!1;var e=r.getCurrentUserInfo();return null!==e&&void 0!==e&&e.role?(this.userRoles=e.role,this.userRoles.some((function(e){return["supervisor","PM","GM","admin","responsible"].includes(e)}))):(this.userRoles=[],!1)}catch(t){return this.userRoles=[],this.clearBadge(),!1}}},{key:"hasRole",value:function(e){return this.userRoles.length>0&&this.userRoles.includes(e)}},{key:"buildTodoQueryConditions",value:function(e){var t=[];if(this.userRoles.includes("supervisor")&&t.push({workflowStatus:"pending_supervisor"}),this.userRoles.includes("PM")&&t.push({workflowStatus:"pending_pm"}),this.userRoles.includes("GM")&&(t.push({workflowStatus:"pending_gm"}),t.push({workflowStatus:"gm_approved_pending_assign"})),this.userRoles.includes("responsible"))try{var n=r.getCurrentUserInfo(),o=null===n||void 0===n?void 0:n.uid;o&&t.push({workflowStatus:"assigned_to_responsible",responsibleUserId:o})}catch(i){console.warn("获取用户ID失败:",i)}return this.userRoles.includes("GM")&&t.push({workflowStatus:"completed_by_responsible"}),this.userRoles.includes("admin")?{workflowStatus:e.in(["pending_supervisor","pending_pm","pending_gm","gm_approved_pending_assign","completed_by_responsible"])}:t.length>0?e.or(t):null}},{key:"getTodoCount",value:function(){var t=(0,s.default)(i.default.mark((function t(){var n,o,a,s,u,c,l;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,!this.isProcessing){t.next=3;break}return t.abrupt("return",this.todoCount);case 3:if(this.isProcessing=!0,n=e.getStorageSync("uni_id_token"),n){t.next=9;break}return this.clearBadge(),this.isProcessing=!1,t.abrupt("return",0);case 9:if(!this.isTokenLikelyExpired()){t.next=12;break}return this.isProcessing=!1,t.abrupt("return",0);case 12:if(o=this.hasSpecificRole(),o){t.next=17;break}return this.clearBadge(),this.isProcessing=!1,t.abrupt("return",0);case 17:if(a=r.database(),s=a.command,u=this.buildTodoQueryConditions(s),u){t.next=24;break}return this.clearBadge(),this.isProcessing=!1,t.abrupt("return",0);case 24:return t.next=26,a.collection("feedback").where(u).count();case 26:if(c=t.sent,!c.result||void 0===c.result.total){t.next=32;break}return l=c.result.total,this.todoCount!==l&&(this.todoCount=l,this.lastUpdateTime=Date.now(),this.updateBadge()),this.isProcessing=!1,t.abrupt("return",this.todoCount);case 32:return this.clearBadge(),this.isProcessing=!1,t.abrupt("return",0);case 37:return t.prev=37,t.t0=t["catch"](0),this.isProcessing=!1,t.abrupt("return",0);case 41:case"end":return t.stop()}}),t,this,[[0,37]])})));return function(){return t.apply(this,arguments)}}()},{key:"updateTodoCountImmediately",value:function(){var e=(0,s.default)(i.default.mark((function e(){var t;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null),!this.isProcessing){e.next=10;break}t=0;case 3:if(!(this.isProcessing&&t<50)){e.next=9;break}return e.next=6,new Promise((function(e){return setTimeout(e,100)}));case 6:t++,e.next=3;break;case 9:this.isProcessing&&(this.isProcessing=!1);case 10:return e.next=12,this.getTodoCount();case 12:return e.abrupt("return",e.sent);case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"debouncedGetTodoCount",value:function(){var e=this;return this.debounceTimer&&clearTimeout(this.debounceTimer),this.debounceTimer=setTimeout((function(){e.getTodoCount()}),this.debounceDelay),Promise.resolve()}},{key:"updateBadge",value:function(){var t=this;(function n(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,o=t.isCurrentPageTabBar();o?t.todoCount>0?e.setTabBarBadge({index:3,text:t.todoCount.toString()}):t.clearBadgeInternal():r<3&&setTimeout((function(){n(r+1)}),300)})(),e.$emit("todo-count-updated",this.todoCount)}},{key:"clearBadgeInternal",value:function(){if(this.isCurrentPageTabBar())try{e.removeTabBarBadge({index:3})}catch(t){console.warn("清除TabBar徽章失败(可能不在TabBar页面):",t.errMsg||t.message)}}},{key:"clearBadge",value:function(){this.isClearing||(this.isClearing=!0,this.clearBadgeInternal(),this.todoCount=0,this.userRoles=[],this.isClearing=!1,e.$emit("todo-count-updated",0))}},{key:"forceCleanBadge",value:function(){if(this.isClearing=!1,this.isProcessing=!1,this.todoCount=0,this.userRoles=[],this.isCurrentPageTabBar()){(function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;try{e.removeTabBarBadge({index:3,complete:function(e){e.errMsg&&e.errMsg.includes("fail")&&n<3&&setTimeout((function(){t(n+1)}),100)}})}catch(r){console.warn("清除TabBar徽章失败(重试".concat(n+1,"/3):"),r.errMsg||r.message),n<3&&setTimeout((function(){t(n+1)}),100)}})()}e.$emit("todo-count-updated",0)}},{key:"init",value:function(){var t=(0,s.default)(i.default.mark((function t(){var n,r=this;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!this.initialized){t.next=2;break}return t.abrupt("return");case 2:if(n=e.getStorageSync("uni_id_token"),!n){t.next=8;break}return t.next=6,this.updateTodoCountImmediately();case 6:t.next=9;break;case 8:this.clearBadge();case 9:this.checkInterval&&clearInterval(this.checkInterval),this.checkInterval=setInterval((0,s.default)(i.default.mark((function t(){var n;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=e.getStorageSync("uni_id_token"),!n){t.next=12;break}if(!r.isTokenLikelyExpired()){t.next=6;break}return r.clearBadge(),t.abrupt("return");case 6:return t.next=8,r.checkCrossDeviceUpdates();case 8:return t.next=10,r.updateTodoCountImmediately();case 10:t.next=13;break;case 12:r.clearBadge();case 13:t.next=18;break;case 15:t.prev=15,t.t0=t["catch"](0),r.clearBadge();case 18:case"end":return t.stop()}}),t,null,[[0,15]])}))),this.checkIntervalMs),this.initialized=!0;case 12:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}()},{key:"checkCrossDeviceUpdates",value:function(){var t=(0,s.default)(i.default.mark((function t(){var n,o,s,u,c;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,this.lastSyncTime){t.next=4;break}return this.lastSyncTime=Date.now(),t.abrupt("return");case 4:return n=r.database(),t.next=7,n.collection("feedback-sync-status").where({updateTime:n.command.gt(this.lastSyncTime)}).orderBy("updateTime","desc").limit(5).get();case 7:if(o=t.sent,!(o.result&&o.result.data&&o.result.data.length>0)){t.next=19;break}return console.log("角标管理器检测到跨设备更新，静默强制刷新"),this.isProcessing=!1,this.userRoles=[],this.lastSyncTime=o.result.data[0].updateTime,s=o.result.data,u=(0,a.default)(new Set(s.map((function(e){return e.updateType})))),c=(0,a.default)(new Set(s.map((function(e){return e.feedbackId})))),e.$emit("cross-device-update-detected",{updateTime:this.lastSyncTime,updateTypes:u,feedbackIds:c,updateCount:s.length,silent:!0}),t.next=19,this.updateTodoCountImmediately();case 19:t.next=24;break;case 21:t.prev=21,t.t0=t["catch"](0),console.log("角标跨设备更新检查失败:",t.t0);case 24:case"end":return t.stop()}}),t,this,[[0,21]])})));return function(){return t.apply(this,arguments)}}()},{key:"forceRefresh",value:function(){var e=(0,s.default)(i.default.mark((function e(){return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.userRoles=[],this.isProcessing=!1,this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null),e.next=5,this.updateTodoCountImmediately();case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"forceSyncBadge",value:function(){if(this.isCurrentPageTabBar())if(this.todoCount>0)e.setTabBarBadge({index:3,text:this.todoCount.toString()});else for(var t=function(t){setTimeout((function(){try{e.removeTabBarBadge({index:3})}catch(n){console.warn("强制清除TabBar徽章失败(尝试".concat(t+1,"/3):"),n.errMsg||n.message)}}),100*t)},n=0;n<3;n++)t(n);else e.$emit("todo-count-updated",this.todoCount)}},{key:"onLoginSuccess",value:function(){var e=(0,s.default)(i.default.mark((function e(){return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.userRoles=[],this.isProcessing=!1,this.todoCount=0,this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null),e.next=7,this.updateTodoCountImmediately();case 7:this.forceSyncBadge(),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](0),this.clearBadge();case 13:case"end":return e.stop()}}),e,this,[[0,10]])})));return function(){return e.apply(this,arguments)}}()},{key:"isCurrentPageTabBar",value:function(){try{var e=Date.now();if(e-this.lastTabBarCheck<100)return this._lastTabBarResult||!1;this.lastTabBarCheck=e;var t=getCurrentPages();if(0===t.length)return this._lastTabBarResult=!1,!1;var n=t[t.length-1],r=n.route||"";if(r.includes("_pkg"))return this._lastTabBarResult=!1,!1;var o=["pages/index/index","pages/feedback/list","pages/patrol/index","pages/ucenter/ucenter"].includes(r);return this._lastTabBarResult=o,o}catch(i){return console.warn("检测TabBar页面失败:",i),this._lastTabBarResult=!1,!1}}},{key:"destroy",value:function(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null),this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null),e.$off("feedback-updated"),this.todoCount=0,this.userRoles=[],this.isProcessing=!1,this.isClearing=!1,this.initialized=!1}}]),t}(),d=new f;d.init();var h=d;t.default=h}).call(this,n("df3c")["default"],n("861b")["uniCloud"])},"7eb4":function(e,t,n){var r=n("9fc1")();e.exports=r},"7fc2":function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.calculateEndTime=g,t.calculateRoundTime=p,t.default=void 0,t.detectTimeZone=y,t.ensureCorrectTimeZone=_,t.formatDate=u,t.formatTime=v,t.getDaysDiff=d,t.getMonthFirstDay=l,t.getMonthLastDay=f,t.getRelativeTime=c,t.isTimeInRange=m,t.isToday=h,t.preprocessDates=b,t.safeDateFormat=k,t.smartFormatDate=w;var o=r(n("7ca3")),i=r(n("3b2d")),a=r(n("34cf"));function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";if(!e)return"";if("string"===typeof e)if(!e.includes("T")||!e.includes("Z")&&e.includes("+")){if(e.includes(",")&&(e.includes("AM")||e.includes("PM"))){var r=e.split(",");if(r.length>=1){var o=r[0].trim(),i=o.split("/");if(3===i.length){var a=i[0].padStart(2,"0"),s=i[1].padStart(2,"0"),u=i[2];e="".concat(u,"/").concat(a,"/").concat(s)}}}t=new Date(e.replace(/-/g,"/"))}else{console.log("检测到需要调整时区的ISO日期:",e);try{var c=e.split("T")[0],l=e.includes("T")?e.split("T")[1].split(".")[0]:"00:00:00";t=new Date("".concat(c,"T").concat(l,"+08:00")),isNaN(t.getTime())&&(t=new Date(e.replace(/-/g,"/")))}catch(m){console.warn("处理日期时区出错",m),t=new Date(e.replace(/-/g,"/"))}}else if("number"===typeof e)t=new Date(e);else{if(!(e instanceof Date))return"";t=e}isNaN(t.getTime())&&(console.warn("无效的日期格式:",e),t=new Date);var f=t.getFullYear(),d=t.getMonth()+1,h=t.getDate(),p=t.getHours(),g=t.getMinutes(),v=t.getSeconds();return n.replace(/YYYY/g,f).replace(/YY/g,String(f).slice(2)).replace(/MM/g,(d<10?"0":"")+d).replace(/M/g,d).replace(/DD/g,(h<10?"0":"")+h).replace(/D/g,h).replace(/HH/g,(p<10?"0":"")+p).replace(/H/g,p).replace(/mm/g,(g<10?"0":"")+g).replace(/m/g,g).replace(/ss/g,(v<10?"0":"")+v).replace(/s/g,v)}function c(e){if(!e)return"";var t;if("string"===typeof e){var n=(new Date).getFullYear(),r=n+3,o=e.match(/^(\d{4})/);if(o&&parseInt(o[1])>=r)return console.log("检测到远期未来日期，自动转换为今天:",e),"今天";if(e.includes(",")&&(e.includes("AM")||e.includes("PM"))){var i=e.split(",");if(i.length>=1){var a=i[0].trim(),s=a.split("/");if(3===s.length){var u=s[0].padStart(2,"0"),c=s[1].padStart(2,"0"),l=s[2];e="".concat(l,"/").concat(u,"/").concat(c)}}}t=new Date(e.replace(/-/g,"/"))}else if("number"===typeof e)t=new Date(e);else{if(!(e instanceof Date))return"";t=e}if(isNaN(t.getTime()))return console.warn("无效的日期格式:",e),"未知时间";var f=new Date,d=new Date(f);if(d.setFullYear(f.getFullYear()+1),t>d)return"今天";var h=f.getTime()-t.getTime();if(h<0)return"即将";var p=864e5;return h<6e4?"刚刚":h<36e5?Math.floor(h/6e4)+"分钟前":h<p?Math.floor(h/36e5)+"小时前":h<6048e5?Math.floor(h/p)+"天前":h<2592e6?Math.floor(h/6048e5)+"周前":h<31536e6?Math.floor(h/2592e6)+"个月前":Math.floor(h/31536e6)+"年前"}function l(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,t=e.getFullYear(),n=e.getMonth();return new Date(t,n,1)}function f(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,t=e.getFullYear(),n=e.getMonth();return new Date(t,n+1,0)}function d(e,t){var n=new Date(e),r=new Date(t);n.setHours(0,0,0,0),r.setHours(0,0,0,0);var o=r.getTime()-n.getTime();return Math.round(o/864e5)}function h(e){if(!e)return!1;var t;if("string"===typeof e){if(e.includes(",")&&(e.includes("AM")||e.includes("PM"))){var n=e.split(",");if(n.length>=1){var r=n[0].trim(),o=r.split("/");if(3===o.length){var i=o[0].padStart(2,"0"),a=o[1].padStart(2,"0"),s=o[2];e="".concat(s,"/").concat(i,"/").concat(a)}}}t=new Date(e.replace(/-/g,"/"))}else if("number"===typeof e)t=new Date(e);else{if(!(e instanceof Date))return!1;t=e}if(isNaN(t.getTime()))return console.warn("无效的日期格式:",e),!1;var u=new Date;return t.getDate()===u.getDate()&&t.getMonth()===u.getMonth()&&t.getFullYear()===u.getFullYear()}function p(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(!e||!t)return console.warn("计算轮次时间：参数不完整",e,t,n),new Date;try{var r,o=t.split(":").map(Number),i=(0,a.default)(o,2),s=i[0],u=i[1];if("string"===typeof e){if(e.includes(",")&&(e.includes("AM")||e.includes("PM"))){var c=e.split(",");if(c.length>=1){var l=c[0].trim(),f=l.split("/");if(3===f.length){var d=f[0].padStart(2,"0"),h=f[1].padStart(2,"0"),p=f[2];e="".concat(p,"/").concat(d,"/").concat(h)}}}r=new Date(e.replace(/-/g,"/"))}else r=e instanceof Date?new Date(e):new Date;return isNaN(r.getTime())?(console.warn("无效的日期格式:",e),new Date):(r.setHours(s,u,0,0),n&&"number"===typeof n&&r.setDate(r.getDate()+n),r)}catch(g){return console.error("计算轮次时间出错:",g),new Date}}function g(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60;if(!e)return console.warn("计算结束时间：开始时间无效",e),new Date;try{var n=new Date(e.getTime());return n.setMinutes(n.getMinutes()+(t||60)),n}catch(r){return console.error("计算结束时间出错:",r),new Date}}function v(e){if(!e)return"";try{var t=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0");return"".concat(t,":").concat(n)}catch(r){return console.error("格式化时间出错:",r),""}}function m(e,t,n){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return!!(e&&t&&n)&&(r?e>=t&&e<=n:e>=t&&e<n)}function y(){try{var e=new Date,t=e.getTimezoneOffset(),n=-t/60,r={8:"北京时间(UTC+8)",9:"东京时间(UTC+9)",7:"雅加达时间(UTC+7)",0:"格林威治时间(UTC+0)","-5":"纽约时间(UTC-5)","-8":"洛杉矶时间(UTC-8)"}[n]||"UTC".concat(n>=0?"+":"").concat(n);return console.log("当前时区检测: ".concat(r)),console.log("- 本地时间: ".concat(e.toString())),console.log("- ISO格式: ".concat(e.toISOString())),console.log("- 时区偏移(分钟): ".concat(t)),r}catch(o){return console.error("检测时区出错:",o),"北京时间(UTC+8)"}}function _(e){if(!e)return null;try{if("string"===typeof e&&e.endsWith("Z")&&e.includes("T"))return e;var t=new Date(e);return isNaN(t.getTime())?(console.warn("无效的日期格式:",e),e):t.toISOString()}catch(n){return console.error("时区转换错误:",n,e),e}}function b(e){if(!e)return e;if("object"===(0,i.default)(e)){if(Array.isArray(e))return e.map((function(e){return b(e)}));var t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,o.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},e);for(var n in t)t.hasOwnProperty(n)&&("string"===typeof t[n]&&(n.includes("time")||n.includes("Time")||n.includes("date")||n.includes("Date"))?t[n]=_(t[n]):"object"===(0,i.default)(t[n])&&(t[n]=b(t[n])));return t}return e}function w(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"display";if(!e)return"";try{var n;if("string"===typeof e)n=new Date(e.replace(/-/g,"/"));else if("number"===typeof e)n=new Date(e);else{if(!(e instanceof Date))return"";n=e}return isNaN(n.getTime())?(console.warn("无效的日期格式:",e),""):"storage"===t?n.toISOString():"time"===t?v(n):u(n,"YYYY-MM-DD HH:mm")}catch(r){return console.error("智能格式化日期出错:",r),""}}function k(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";try{if(!e)return"未知时间";if("string"===typeof e){var n=(new Date).getFullYear(),r=n+3,o=e.match(/^(\d{4})/);if(o&&parseInt(o[1])>=r)return console.log("安全格式化检测到远期未来日期，使用当前时间:",e),u(new Date,t)}if("string"===typeof e&&e.includes("Z"))try{var i=new Date(e),a=new Date(i.getTime()+288e5);return u(a,t)}catch(s){console.warn("UTC转本地时间失败:",s)}return u(e,t)}catch(s){return console.warn("日期安全格式化失败:",s,e),"未知时间"}}var x={formatDate:u,getRelativeTime:c,getMonthFirstDay:l,getMonthLastDay:f,getDaysDiff:d,isToday:h,calculateRoundTime:p,calculateEndTime:g,formatTime:v,isTimeInRange:m,detectTimeZone:y,ensureCorrectTimeZone:_,preprocessDates:b,smartFormatDate:w,safeDateFormat:k};t.default=x},"812d":function(e,t,n){"use strict";(function(e,r){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return f.apply(this,arguments)};var i=o(n("7eb4")),a=o(n("ee10")),s=o(n("30eb")),u=e.importObject("uni-id-co",{customUI:!0}),c=s.default.loginTypes,l=s.default.debug;function f(){return f=(0,a.default)(i.default.mark((function t(){var n,o,s,f,d,h;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(h=function(e){e.code,e.message},!l){t.next=10;break}return t.next=4,u.getSupportedLoginType();case 4:n=t.sent,o=n.supportedLoginType,console.log("supportedLoginType: "+JSON.stringify(o)),s={smsCode:"mobile-code",univerify:"univerify",username:"username-password",weixin:"weixin",qq:"qq",xiaomi:"xiaomi",sinaweibo:"sinaweibo",taobao:"taobao",facebook:"facebook",google:"google",alipay:"alipay",apple:"apple",weixinMobile:"weixin"},f=c.filter((function(e){return!o.includes(s[e])})),f.length&&console.error("错误：前端启用的登录方式:".concat(f.join("，"),';没有在服务端完成配置。配置文件路径："/uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center/uni-id/config.json"'));case 10:d=e.database(),d.on("error",h),e.onRefreshToken&&e.onRefreshToken((function(){r.getPushClientId&&r.getPushClientId({success:function(){var e=(0,a.default)(i.default.mark((function e(t){var n;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.cid,e.next=3,u.setPushCid({pushClientId:n});case 3:e.sent;case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),fail:function(e){}})}));case 13:case"end":return t.stop()}}),t)}))),f.apply(this,arguments)}}).call(this,n("861b")["uniCloud"],n("df3c")["default"])},"828b":function(e,t,n){"use strict";function r(e,t,n,r,o,i,a,s,u,c){var l,f="function"===typeof e?e.options:e;if(u){f.components||(f.components={});var d=Object.prototype.hasOwnProperty;for(var h in u)d.call(u,h)&&!d.call(f.components,h)&&(f.components[h]=u[h])}if(c&&("function"===typeof c.beforeCreate&&(c.beforeCreate=[c.beforeCreate]),(c.beforeCreate||(c.beforeCreate=[])).unshift((function(){this[c.__module]=this})),(f.mixins||(f.mixins=[])).push(c)),t&&(f.render=t,f.staticRenderFns=n,f._compiled=!0),r&&(f.functional=!0),i&&(f._scopeId="data-v-"+i),a?(l=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),o&&o.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(a)},f._ssrRegister=l):o&&(l=s?function(){o.call(this,this.$root.$options.shadowRoot)}:o),l)if(f.functional){f._injectStyles=l;var p=f.render;f.render=function(e,t){return l.call(t),p(e,t)}}else{var g=f.beforeCreate;f.beforeCreate=g?[].concat(g,l):[l]}return{exports:e,options:f}}n.d(t,"a",(function(){return r}))},8546:function(e){e.exports=JSON.parse('{"uni-pagination.prevText":"prev","uni-pagination.nextText":"next","uni-pagination.piecePerPage":"piece/page"}')},"861b":function(e,t,n){"use strict";(function(e,r,o){var i=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.uniCloud=t.default=t.UniCloudError=void 0;var a=i(n("7eb4")),s=i(n("3352")),u=i(n("34cf")),c=i(n("3b2d")),l=i(n("af34")),f=i(n("ee10")),d=i(n("7ca3")),h=i(n("8ffa")),p=i(n("4ffb")),g=i(n("b4d2")),v=i(n("7ce1")),m=i(n("67ad")),y=i(n("0bdb")),_=i(n("0c73"));function b(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return w(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return w(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw i}}}}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){(0,d.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function S(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=(0,g.default)(e);if(t){var o=(0,g.default)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return(0,p.default)(this,n)}}function O(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof e||"undefined"!=typeof self&&self;var T=O((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),r={},o=r.lib={},i=o.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},a=o.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,o=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<o;i++){var a=n[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=a<<24-(r+i)%4*8}else for(i=0;i<o;i+=4)t[r+i>>>2]=n[i>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,r=[],o=function(t){var n=987654321,r=4294967295;return function(){var o=((n=36969*(65535&n)+(n>>16)&r)<<16)+(t=18e3*(65535&t)+(t>>16)&r)&r;return o/=4294967296,(o+=.5)*(e.random()>.5?1:-1)}},i=0;i<t;i+=4){var s=o(4294967296*(n||e.random()));n=987654071*s(),r.push(4294967296*s()|0)}return new a.init(r,t)}}),s=r.enc={},u=s.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new a.init(n,t/2)}},c=s.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new a.init(n,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},f=o.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new a.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,o=n.sigBytes,i=this.blockSize,s=o/(4*i),u=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*i,c=e.min(4*u,o);if(u){for(var l=0;l<u;l+=i)this._doProcessBlock(r,l);var f=r.splice(0,u);n.sigBytes-=c}return new a.init(f,c)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=f.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new d.HMAC.init(e,n).finalize(t)}}});var d=r.algo={};return r}(Math),n)})),P=T,C=(O((function(e,t){var n;e.exports=(n=P,function(e){var t=n,r=t.lib,o=r.WordArray,i=r.Hasher,a=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var u=a.MD5=i.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,o=e[r];e[r]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var i=this._hash.words,a=e[t+0],u=e[t+1],h=e[t+2],p=e[t+3],g=e[t+4],v=e[t+5],m=e[t+6],y=e[t+7],_=e[t+8],b=e[t+9],w=e[t+10],k=e[t+11],x=e[t+12],S=e[t+13],O=e[t+14],T=e[t+15],P=i[0],C=i[1],A=i[2],I=i[3];P=c(P,C,A,I,a,7,s[0]),I=c(I,P,C,A,u,12,s[1]),A=c(A,I,P,C,h,17,s[2]),C=c(C,A,I,P,p,22,s[3]),P=c(P,C,A,I,g,7,s[4]),I=c(I,P,C,A,v,12,s[5]),A=c(A,I,P,C,m,17,s[6]),C=c(C,A,I,P,y,22,s[7]),P=c(P,C,A,I,_,7,s[8]),I=c(I,P,C,A,b,12,s[9]),A=c(A,I,P,C,w,17,s[10]),C=c(C,A,I,P,k,22,s[11]),P=c(P,C,A,I,x,7,s[12]),I=c(I,P,C,A,S,12,s[13]),A=c(A,I,P,C,O,17,s[14]),P=l(P,C=c(C,A,I,P,T,22,s[15]),A,I,u,5,s[16]),I=l(I,P,C,A,m,9,s[17]),A=l(A,I,P,C,k,14,s[18]),C=l(C,A,I,P,a,20,s[19]),P=l(P,C,A,I,v,5,s[20]),I=l(I,P,C,A,w,9,s[21]),A=l(A,I,P,C,T,14,s[22]),C=l(C,A,I,P,g,20,s[23]),P=l(P,C,A,I,b,5,s[24]),I=l(I,P,C,A,O,9,s[25]),A=l(A,I,P,C,p,14,s[26]),C=l(C,A,I,P,_,20,s[27]),P=l(P,C,A,I,S,5,s[28]),I=l(I,P,C,A,h,9,s[29]),A=l(A,I,P,C,y,14,s[30]),P=f(P,C=l(C,A,I,P,x,20,s[31]),A,I,v,4,s[32]),I=f(I,P,C,A,_,11,s[33]),A=f(A,I,P,C,k,16,s[34]),C=f(C,A,I,P,O,23,s[35]),P=f(P,C,A,I,u,4,s[36]),I=f(I,P,C,A,g,11,s[37]),A=f(A,I,P,C,y,16,s[38]),C=f(C,A,I,P,w,23,s[39]),P=f(P,C,A,I,S,4,s[40]),I=f(I,P,C,A,a,11,s[41]),A=f(A,I,P,C,p,16,s[42]),C=f(C,A,I,P,m,23,s[43]),P=f(P,C,A,I,b,4,s[44]),I=f(I,P,C,A,x,11,s[45]),A=f(A,I,P,C,T,16,s[46]),P=d(P,C=f(C,A,I,P,h,23,s[47]),A,I,a,6,s[48]),I=d(I,P,C,A,y,10,s[49]),A=d(A,I,P,C,O,15,s[50]),C=d(C,A,I,P,v,21,s[51]),P=d(P,C,A,I,x,6,s[52]),I=d(I,P,C,A,p,10,s[53]),A=d(A,I,P,C,w,15,s[54]),C=d(C,A,I,P,u,21,s[55]),P=d(P,C,A,I,_,6,s[56]),I=d(I,P,C,A,T,10,s[57]),A=d(A,I,P,C,m,15,s[58]),C=d(C,A,I,P,S,21,s[59]),P=d(P,C,A,I,g,6,s[60]),I=d(I,P,C,A,k,10,s[61]),A=d(A,I,P,C,h,15,s[62]),C=d(C,A,I,P,b,21,s[63]),i[0]=i[0]+P|0,i[1]=i[1]+C|0,i[2]=i[2]+A|0,i[3]=i[3]+I|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;n[o>>>5]|=128<<24-o%32;var i=e.floor(r/4294967296),a=r;n[15+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(o+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,u=s.words,c=0;c<4;c++){var l=u[c];u[c]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,n,r,o,i,a){var s=e+(t&n|~t&r)+o+a;return(s<<i|s>>>32-i)+t}function l(e,t,n,r,o,i,a){var s=e+(t&r|n&~r)+o+a;return(s<<i|s>>>32-i)+t}function f(e,t,n,r,o,i,a){var s=e+(t^n^r)+o+a;return(s<<i|s>>>32-i)+t}function d(e,t,n,r,o,i,a){var s=e+(n^(t|~r))+o+a;return(s<<i|s>>>32-i)+t}t.MD5=i._createHelper(u),t.HmacMD5=i._createHmacHelper(u)}(Math),n.MD5)})),O((function(e,t){var n;e.exports=(n=P,void function(){var e=n,t=e.lib.Base,r=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,o=4*n;t.sigBytes>o&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),a=this._iKey=t.clone(),s=i.words,u=a.words,c=0;c<n;c++)s[c]^=1549556828,u[c]^=909522486;i.sigBytes=a.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}())})),O((function(e,t){e.exports=P.HmacMD5}))),A=O((function(e,t){e.exports=P.enc.Utf8})),I=O((function(e,t){var n;e.exports=(n=P,function(){var e=n,t=e.lib.WordArray;function r(e,n,r){for(var o=[],i=0,a=0;a<n;a++)if(a%4){var s=r[e.charCodeAt(a-1)]<<a%4*2,u=r[e.charCodeAt(a)]>>>6-a%4*2;o[i>>>2]|=(s|u)<<24-i%4*8,i++}return t.create(o,i)}e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var o=[],i=0;i<n;i+=3)for(var a=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<n;s++)o.push(r.charAt(a>>>6*(3-s)&63));var u=r.charAt(64);if(u)for(;o.length%4;)o.push(u);return o.join("")},parse:function(e){var t=e.length,n=this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var i=0;i<n.length;i++)o[n.charCodeAt(i)]=i}var a=n.charAt(64);if(a){var s=e.indexOf(a);-1!==s&&(t=s)}return r(e,t,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),n.enc.Base64)})),E="uni_id_token",D="uni_id_token_expired",j={DEFAULT:"FUNCTION",FUNCTION:"FUNCTION",OBJECT:"OBJECT",CLIENT_DB:"CLIENT_DB"},L="pending",N="fulfilled",M="rejected";function R(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function B(e){return"object"===R(e)}function $(e){return"function"==typeof e}function U(e){return function(){try{return e.apply(e,arguments)}catch(e){console.error(e)}}}var F="REJECTED",q="NOT_PENDING",H=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.createPromise,r=t.retryRule,o=void 0===r?F:r;(0,m.default)(this,e),this.createPromise=n,this.status=null,this.promise=null,this.retryRule=o}return(0,y.default)(e,[{key:"needRetry",get:function(){if(!this.status)return!0;switch(this.retryRule){case F:return this.status===M;case q:return this.status!==L}}},{key:"exec",value:function(){var e=this;return this.needRetry?(this.status=L,this.promise=this.createPromise().then((function(t){return e.status=N,Promise.resolve(t)}),(function(t){return e.status=M,Promise.reject(t)})),this.promise):this.promise}}]),e}(),z=function(){function e(){(0,m.default)(this,e),this._callback={}}return(0,y.default)(e,[{key:"addListener",value:function(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}},{key:"on",value:function(e,t){return this.addListener(e,t)}},{key:"removeListener",value:function(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');var n=this._callback[e];if(n){var r=function(e,t){for(var n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(r,1)}}},{key:"off",value:function(e,t){return this.removeListener(e,t)}},{key:"removeAllListener",value:function(e){delete this._callback[e]}},{key:"emit",value:function(e){for(var t=this._callback[e],n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];if(t)for(var i=0;i<t.length;i++)t[i].apply(t,r)}}]),e}();function V(e){return e&&"string"==typeof e?JSON.parse(e):e}var K="mp-weixin",W=V([]),J=K,G=(V(void 0),V([{provider:"aliyun",spaceName:"xxzc",spaceId:"mp-e0eed894-67bf-40c8-b468-a69d3af56f68",clientSecret:"GJRuiFv38aUnUfoUdrEX3w==",endpoint:"https://api.next.bspapp.com"}])||[]);try{(n("db30").default||n("db30")).appid}catch(Pr){}var Y,X={};function Q(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=X,n=e,Object.prototype.hasOwnProperty.call(t,n)||(X[e]=r),X[e]}"app"===J&&(X=r._globalUniCloudObj?r._globalUniCloudObj:r._globalUniCloudObj={});var Z=["invoke","success","fail","complete"],ee=Q("_globalUniCloudInterceptor");function te(e,t){ee[e]||(ee[e]={}),B(t)&&Object.keys(t).forEach((function(n){Z.indexOf(n)>-1&&function(e,t,n){var r=ee[e][t];r||(r=ee[e][t]=[]),-1===r.indexOf(n)&&$(n)&&r.push(n)}(e,n,t[n])}))}function ne(e,t){ee[e]||(ee[e]={}),B(t)?Object.keys(t).forEach((function(n){Z.indexOf(n)>-1&&function(e,t,n){var r=ee[e][t];if(r){var o=r.indexOf(n);o>-1&&r.splice(o,1)}}(e,n,t[n])})):delete ee[e]}function re(e,t){return e&&0!==e.length?e.reduce((function(e,n){return e.then((function(){return n(t)}))}),Promise.resolve()):Promise.resolve()}function oe(e,t){return ee[e]&&ee[e][t]||[]}function ie(e){te("callObject",e)}var ae=Q("_globalUniCloudListener"),se={RESPONSE:"response",NEED_LOGIN:"needLogin",REFRESH_TOKEN:"refreshToken"},ue={CLIENT_DB:"clientdb",CLOUD_FUNCTION:"cloudfunction",CLOUD_OBJECT:"cloudobject"};function ce(e){return ae[e]||(ae[e]=[]),ae[e]}function le(e,t){var n=ce(e);n.includes(t)||n.push(t)}function fe(e,t){var n=ce(e),r=n.indexOf(t);-1!==r&&n.splice(r,1)}function de(e,t){for(var n=ce(e),r=0;r<n.length;r++)(0,n[r])(t)}var he,pe=!1;function ge(){return he||(he=new Promise((function(e){pe&&e(),function t(){if("function"==typeof getCurrentPages){var n=getCurrentPages();n&&n[0]&&(pe=!0,e())}pe||setTimeout((function(){t()}),30)}()})),he)}function ve(e){var t={};for(var n in e){var r=e[n];$(r)&&(t[n]=U(r))}return t}var me=function(e){(0,h.default)(n,e);var t=S(n);function n(e){var r;(0,m.default)(this,n);var o=e.message||e.errMsg||"unknown system error";return r=t.call(this,o),r.errMsg=o,r.code=r.errCode=e.code||e.errCode||"SYSTEM_ERROR",r.errSubject=r.subject=e.subject||e.errSubject,r.cause=e.cause,r.requestId=e.requestId,r}return(0,y.default)(n,[{key:"toJson",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}]),n}((0,v.default)(Error));t.UniCloudError=me;var ye,_e,be={request:function(e){return r.request(e)},uploadFile:function(e){return r.uploadFile(e)},setStorageSync:function(e,t){return r.setStorageSync(e,t)},getStorageSync:function(e){return r.getStorageSync(e)},removeStorageSync:function(e){return r.removeStorageSync(e)},clearStorageSync:function(){return r.clearStorageSync()},connectSocket:function(e){return r.connectSocket(e)}};function we(){return{token:be.getStorageSync(E)||be.getStorageSync("uniIdToken"),tokenExpired:be.getStorageSync(D)}}function ke(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.token,n=e.tokenExpired;t&&be.setStorageSync(E,t),n&&be.setStorageSync(D,n)}function xe(){return ye||(ye="mp-weixin"===J&&o.canIUse("getAppBaseInfo")&&o.canIUse("getDeviceInfo")?x(x({},r.getAppBaseInfo()),r.getDeviceInfo()):r.getSystemInfoSync()),ye}var Se={};function Oe(){var e=r.getLocale&&r.getLocale()||"en";if(_e)return x(x(x({},Se),_e),{},{locale:e,LOCALE:e});var t=xe(),n=t.deviceId,o=t.osName,i=t.uniPlatform,a=t.appId,s=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(var u in t)Object.hasOwnProperty.call(t,u)&&-1===s.indexOf(u)&&delete t[u];return _e=x(x({PLATFORM:i,OS:o,APPID:a,DEVICEID:n},function(){var e,t;try{if(r.getLaunchOptionsSync){if(r.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;var n=r.getLaunchOptionsSync(),o=n.scene,i=n.channel;e=i,t=o}}catch(e){}return{channel:e,scene:t}}()),t),x(x(x({},Se),_e),{},{locale:e,LOCALE:e})}var Te,Pe={sign:function(e,t){var n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),C(n,t).toString()},wrappedRequest:function(e,t){return new Promise((function(n,r){t(Object.assign(e,{complete:function(e){e||(e={});var t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){var o=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",i=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return r(new me({code:o,message:i,requestId:t}))}var a=e.data;if(a.error)return r(new me({code:a.error.code,message:a.error.message,requestId:t}));a.result=a.data,a.requestId=t,delete a.data,n(a)}}))}))},toBase64:function(e){return I.stringify(A.parse(e))}},Ce=function(){function e(t){var n=this;(0,m.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),this.config=Object.assign({},{endpoint:0===t.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},t),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=be,this._getAccessTokenPromiseHub=new H({createPromise:function(){return n.requestAuth(n.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((function(e){if(!e.result||!e.result.accessToken)throw new me({code:"AUTH_FAILED",message:"获取accessToken失败"});n.setAccessToken(e.result.accessToken)}))},retryRule:q})}return(0,y.default)(e,[{key:"hasAccessToken",get:function(){return!!this.accessToken}},{key:"setAccessToken",value:function(e){this.accessToken=e}},{key:"requestWrapped",value:function(e){return Pe.wrappedRequest(e,this.adapter.request)}},{key:"requestAuth",value:function(e){return this.requestWrapped(e)}},{key:"request",value:function(e,t){var n=this;return Promise.resolve().then((function(){return n.hasAccessToken?t?n.requestWrapped(e):n.requestWrapped(e).catch((function(t){return new Promise((function(e,n){!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((function(){return n.getAccessToken()})).then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))})):n.getAccessToken().then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))}))}},{key:"rebuildRequest",value:function(e){var t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=Pe.sign(t.data,this.config.clientSecret),t}},{key:"setupRequest",value:function(e,t){var n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),r={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,r["x-basement-token"]=this.accessToken),r["x-serverless-sign"]=Pe.sign(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:r}}},{key:"getAccessToken",value:function(){return this._getAccessTokenPromiseHub.exec()}},{key:"authorize",value:function(){var e=(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getAccessToken();case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(x(x({},this.setupRequest(t)),{},{timeout:e.timeout}))}},{key:"getOSSUploadOptionsFromPath",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFileToOSS",value:function(e){var t=this,n=e.url,r=e.formData,o=e.name,i=e.filePath,a=e.fileType,s=e.onUploadProgress;return new Promise((function(e,u){var c=t.adapter.uploadFile({url:n,formData:r,name:o,filePath:i,fileType:a,header:{"X-OSS-server-side-encrpytion":"AES256"},success:function(t){t&&t.statusCode<400?e(t):u(new me({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){u(new me({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"reportOSSUpload",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFile",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,o,i,s,u,c,l,f,d,h,p,g,v,m,y,_,b,w,k,x,S;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,o=t.fileType,i=void 0===o?"image":o,s=t.cloudPathAsRealPath,u=void 0!==s&&s,c=t.onUploadProgress,l=t.config,"string"===R(r)){e.next=3;break}throw new me({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(r=r.trim()){e.next=5;break}throw new me({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(r)){e.next=7;break}throw new me({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:if(f=l&&l.envType||this.config.envType,!(u&&("/"!==r[0]&&(r="/"+r),r.indexOf("\\")>-1))){e.next=10;break}throw new me({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});case 10:return e.next=12,this.getOSSUploadOptionsFromPath({env:f,filename:u?r.split("/").pop():r,fileId:u?r:void 0});case 12:return d=e.sent.result,h="https://"+d.cdnDomain+"/"+d.ossPath,p=d.securityToken,g=d.accessKeyId,v=d.signature,m=d.host,y=d.ossPath,_=d.id,b=d.policy,w=d.ossCallbackUrl,k={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:g,Signature:v,host:m,id:_,key:y,policy:b,success_action_status:200},p&&(k["x-oss-security-token"]=p),w&&(x=JSON.stringify({callbackUrl:w,callbackBody:JSON.stringify({fileId:_,spaceId:this.config.spaceId}),callbackBodyType:"application/json"}),k.callback=Pe.toBase64(x)),S={url:"https://"+d.host,formData:k,fileName:"file",name:"file",filePath:n,fileType:i},e.next=27,this.uploadFileToOSS(Object.assign({},S,{onUploadProgress:c}));case 27:if(!w){e.next=29;break}return e.abrupt("return",{success:!0,filePath:n,fileID:h});case 29:return e.next=31,this.reportOSSUpload({id:_});case 31:if(!e.sent.success){e.next=33;break}return e.abrupt("return",{success:!0,filePath:n,fileID:h});case 33:throw new me({code:"UPLOAD_FAILED",message:"文件上传失败"});case 34:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.fileList;return new Promise((function(t,r){Array.isArray(n)&&0!==n.length||r(new me({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),e.getFileInfo({fileList:n}).then((function(e){t({fileList:n.map((function(t,n){var r=e.fileList[n];return{fileID:t,tempFileURL:r&&r.url||t}}))})}))}))}},{key:"getFileInfo",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,o=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=o.length>0&&void 0!==o[0]?o[0]:{},n=t.fileList,Array.isArray(n)&&0!==n.length){e.next=3;break}throw new me({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});case 3:return r={method:"serverless.file.resource.info",params:JSON.stringify({id:n.map((function(e){return e.split("?")[0]})).join(",")})},e.next=6,this.request(this.setupRequest(r));case 6:return e.t0=e.sent.result,e.abrupt("return",{fileList:e.t0});case 8:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),Ae={init:function(e){var t=new Ce(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Ie="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";!function(e){e.local="local",e.none="none",e.session="session"}(Te||(Te={}));var Ee,De=function(){},je=O((function(e,t){var n;e.exports=(n=P,function(e){var t=n,r=t.lib,o=r.WordArray,i=r.Hasher,a=t.algo,s=[],u=[];!function(){function t(t){for(var n=e.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,o=0;o<64;)t(r)&&(o<8&&(s[o]=n(e.pow(r,.5))),u[o]=n(e.pow(r,1/3)),o++),r++}();var c=[],l=a.SHA256=i.extend({_doReset:function(){this._hash=new o.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],i=n[2],a=n[3],s=n[4],l=n[5],f=n[6],d=n[7],h=0;h<64;h++){if(h<16)c[h]=0|e[t+h];else{var p=c[h-15],g=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,v=c[h-2],m=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;c[h]=g+c[h-7]+m+c[h-16]}var y=r&o^r&i^o&i,_=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),b=d+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&f)+u[h]+c[h];d=f,f=l,l=s,s=a+b|0,a=i,i=o,o=r,r=b+(_+y)|0}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+i|0,n[3]=n[3]+a|0,n[4]=n[4]+s|0,n[5]=n[5]+l|0,n[6]=n[6]+f|0,n[7]=n[7]+d|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;return n[o>>>5]|=128<<24-o%32,n[14+(o+64>>>9<<4)]=e.floor(r/4294967296),n[15+(o+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=i._createHelper(l),t.HmacSHA256=i._createHmacHelper(l)}(Math),n.SHA256)})),Le=je,Ne=O((function(e,t){e.exports=P.HmacSHA256})),Me=function(){var e;if(!Promise){e=function(){},e.promise={};var t=function(){throw new me({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var n=new Promise((function(t,n){e=function(e,r){return e?n(e):t(r)}}));return e.promise=n,e};function Re(e){return void 0===e}function Be(e){return"[object Null]"===Object.prototype.toString.call(e)}function $e(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function Ue(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:32,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n="",r=0;r<e;r++)n+=t.charAt(Math.floor(62*Math.random()));return n}!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Ee||(Ee={}));var Fe={adapter:null,runtime:void 0},qe=["anonymousUuidKey"],He=function(e){(0,h.default)(n,e);var t=S(n);function n(){var e;return(0,m.default)(this,n),e=t.call(this),Fe.adapter.root.tcbObject||(Fe.adapter.root.tcbObject={}),e}return(0,y.default)(n,[{key:"setItem",value:function(e,t){Fe.adapter.root.tcbObject[e]=t}},{key:"getItem",value:function(e){return Fe.adapter.root.tcbObject[e]}},{key:"removeItem",value:function(e){delete Fe.adapter.root.tcbObject[e]}},{key:"clear",value:function(){delete Fe.adapter.root.tcbObject}}]),n}(De);function ze(e,t){switch(e){case"local":return t.localStorage||new He;case"none":return new He;default:return t.sessionStorage||new He}}var Ve=function(){function e(t){if((0,m.default)(this,e),!this._storage){this._persistence=Fe.adapter.primaryStorage||t.persistence,this._storage=ze(this._persistence,Fe.adapter);var n="access_token_".concat(t.env),r="access_token_expire_".concat(t.env),o="refresh_token_".concat(t.env),i="anonymous_uuid_".concat(t.env),a="login_type_".concat(t.env),s="token_type_".concat(t.env),u="user_info_".concat(t.env);this.keys={accessTokenKey:n,accessTokenExpireKey:r,refreshTokenKey:o,anonymousUuidKey:i,loginTypeKey:a,userInfoKey:u,deviceIdKey:"device_id",tokenTypeKey:s}}}return(0,y.default)(e,[{key:"updatePersistence",value:function(e){if(e!==this._persistence){var t="local"===this._persistence;this._persistence=e;var n=ze(e,Fe.adapter);for(var r in this.keys){var o=this.keys[r];if(!t||!qe.includes(r)){var i=this._storage.getItem(o);Re(i)||Be(i)||(n.setItem(o,i),this._storage.removeItem(o))}}this._storage=n}}},{key:"setStore",value:function(e,t,n){if(this._storage){var r={version:n||"localCachev1",content:t},o=JSON.stringify(r);try{this._storage.setItem(e,o)}catch(e){throw e}}}},{key:"getStore",value:function(e,t){try{if(!this._storage)return}catch(e){return""}t=t||"localCachev1";var n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}},{key:"removeStore",value:function(e){this._storage.removeItem(e)}}]),e}(),Ke={},We={};function Je(e){return Ke[e]}var Ge=(0,y.default)((function e(t,n){(0,m.default)(this,e),this.data=n||null,this.name=t})),Ye=function(e){(0,h.default)(n,e);var t=S(n);function n(e,r){var o;return(0,m.default)(this,n),o=t.call(this,"error",{error:e,data:r}),o.error=e,o}return(0,y.default)(n)}(Ge),Xe=new(function(){function e(){(0,m.default)(this,e),this._listeners={}}return(0,y.default)(e,[{key:"on",value:function(e,t){return function(e,t,n){n[e]=n[e]||[],n[e].push(t)}(e,t,this._listeners),this}},{key:"off",value:function(e,t){return function(e,t,n){if(n&&n[e]){var r=n[e].indexOf(t);-1!==r&&n[e].splice(r,1)}}(e,t,this._listeners),this}},{key:"fire",value:function(e,t){if(e instanceof Ye)return console.error(e.error),this;var n="string"==typeof e?new Ge(e,t||{}):e,r=n.name;if(this._listens(r)){n.target=this;var o,i=this._listeners[r]?(0,l.default)(this._listeners[r]):[],a=b(i);try{for(a.s();!(o=a.n()).done;){var s=o.value;s.call(this,n)}}catch(u){a.e(u)}finally{a.f()}}return this}},{key:"_listens",value:function(e){return this._listeners[e]&&this._listeners[e].length>0}}]),e}());function Qe(e,t){Xe.on(e,t)}function Ze(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Xe.fire(e,t)}function et(e,t){Xe.off(e,t)}var tt,nt="loginStateChanged",rt="loginStateExpire",ot="loginTypeChanged",it="anonymousConverted",at="refreshAccessToken";!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(tt||(tt={}));var st=function(){function e(){(0,m.default)(this,e),this._fnPromiseMap=new Map}return(0,y.default)(e,[{key:"run",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r,o=this;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=this._fnPromiseMap.get(t),e.abrupt("return",(r||(r=new Promise(function(){var e=(0,f.default)(a.default.mark((function e(r,i){var s;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,o._runIdlePromise();case 3:return s=n(),e.t0=r,e.next=7,s;case 7:e.t1=e.sent,(0,e.t0)(e.t1),e.next=14;break;case 11:e.prev=11,e.t2=e["catch"](0),i(e.t2);case 14:return e.prev=14,o._fnPromiseMap.delete(t),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[0,11,14,17]])})));return function(t,n){return e.apply(this,arguments)}}()),this._fnPromiseMap.set(t,r)),r));case 2:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_runIdlePromise",value:function(){return Promise.resolve()}}]),e}(),ut=function(){function e(t){(0,m.default)(this,e),this._singlePromise=new st,this._cache=Je(t.env),this._baseURL="https://".concat(t.env,".ap-shanghai.tcb-api.tencentcloudapi.com"),this._reqClass=new Fe.adapter.reqClass({timeout:t.timeout,timeoutMsg:"请求在".concat(t.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]})}return(0,y.default)(e,[{key:"_getDeviceId",value:function(){if(this._deviceID)return this._deviceID;var e=this._cache.keys.deviceIdKey,t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=Ue(),this._cache.setStore(e,t)),this._deviceID=t,t}},{key:"_request",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r,o,i,s,u,c=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=c.length>2&&void 0!==c[2]?c[2]:{},o={"x-request-id":Ue(),"x-device-id":this._getDeviceId()},!r.withAccessToken){e.next=9;break}return i=this._cache.keys.tokenTypeKey,e.next=6,this.getAccessToken();case 6:s=e.sent,u=this._cache.getStore(i),o.authorization="".concat(u," ").concat(s);case 9:return e.abrupt("return",this._reqClass["get"===r.method?"get":"post"]({url:"".concat(this._baseURL).concat(t),data:n,headers:o}));case 10:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_fetchAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,o,i,s,u,c,l,d,h=this;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.loginTypeKey,r=t.accessTokenKey,o=t.accessTokenExpireKey,i=t.tokenTypeKey,s=this._cache.getStore(n),!s||s===tt.ANONYMOUS){e.next=3;break}throw new me({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});case 3:return e.next=5,this._singlePromise.run("fetchAccessToken",(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,h._request("/auth/v1/signin/anonymously",{},{method:"post"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)}))));case 5:return u=e.sent,c=u.access_token,l=u.expires_in,d=u.token_type,e.abrupt("return",(this._cache.setStore(i,d),this._cache.setStore(r,c),this._cache.setStore(o,Date.now()+1e3*l),c));case 10:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"isAccessTokenExpired",value:function(e,t){var n=!0;return e&&t&&(n=t<Date.now()),n}},{key:"getAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,o,i;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,o=this._cache.getStore(n),i=this._cache.getStore(r),e.abrupt("return",this.isAccessTokenExpired(o,i)?this._fetchAccessToken():o);case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,o;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,o=t.loginTypeKey,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(o,tt.ANONYMOUS),this.getAccessToken()));case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getUserInfo",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t=this;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._singlePromise.run("getUserInfo",(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)})))));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),ct=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],lt={"X-SDK-Version":"1.3.5"};function ft(e,t,n){var r=e[t];e[t]=function(t){var o={},i={};n.forEach((function(n){var r=n.call(e,t),a=r.data,s=r.headers;Object.assign(o,a),Object.assign(i,s)}));var a=t.data;return a&&function(){var e;if(e=a,"[object FormData]"!==Object.prototype.toString.call(e))t.data=x(x({},a),o);else for(var n in o)a.append(n,o[n])}(),t.headers=x(x({},t.headers||{}),i),r.call(e,t)}}function dt(){var e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:x(x({},lt),{},{"x-seqid":e})}}var ht=function(){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,m.default)(this,e),this.config=n,this._reqClass=new Fe.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:"请求在".concat(this.config.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]}),this._cache=Je(this.config.env),this._localCache=(t=this.config.env,We[t]),this.oauth=new ut(this.config),ft(this._reqClass,"post",[dt]),ft(this._reqClass,"upload",[dt]),ft(this._reqClass,"download",[dt])}return(0,y.default)(e,[{key:"post",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.post(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"upload",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.upload(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"download",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.download(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken()),e.prev=1,e.next=4,this._refreshAccessTokenPromise;case 4:t=e.sent,e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](1),n=e.t0;case 10:if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,!n){e.next=12;break}throw n;case 12:return e.abrupt("return",t);case 13:case"end":return e.stop()}}),e,this,[[1,7]])})));return function(){return e.apply(this,arguments)}}()},{key:"_refreshAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,o,i,s,u,c,l,f,d,h,p;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,o=t.refreshTokenKey,i=t.loginTypeKey,s=t.anonymousUuidKey,this._cache.removeStore(n),this._cache.removeStore(r),u=this._cache.getStore(o),u){e.next=5;break}throw new me({message:"未登录CloudBase"});case 5:return c={refresh_token:u},e.next=8,this.request("auth.fetchAccessTokenWithRefreshToken",c);case 8:if(l=e.sent,!l.data.code){e.next=21;break}if(f=l.data.code,"SIGN_PARAM_INVALID"!==f&&"REFRESH_TOKEN_EXPIRED"!==f&&"INVALID_REFRESH_TOKEN"!==f){e.next=20;break}if(this._cache.getStore(i)!==tt.ANONYMOUS||"INVALID_REFRESH_TOKEN"!==f){e.next=19;break}return d=this._cache.getStore(s),h=this._cache.getStore(o),e.next=17,this.send("auth.signInAnonymously",{anonymous_uuid:d,refresh_token:h});case 17:return p=e.sent,e.abrupt("return",(this.setRefreshToken(p.refresh_token),this._refreshAccessToken()));case 19:Ze(rt),this._cache.removeStore(o);case 20:throw new me({code:l.data.code,message:"刷新access token失败：".concat(l.data.code)});case 21:if(!l.data.access_token){e.next=23;break}return e.abrupt("return",(Ze(at),this._cache.setStore(n,l.data.access_token),this._cache.setStore(r,l.data.access_token_expire+Date.now()),{accessToken:l.data.access_token,accessTokenExpire:l.data.access_token_expire}));case 23:l.data.refresh_token&&(this._cache.removeStore(o),this._cache.setStore(o,l.data.refresh_token),this._refreshAccessToken());case 24:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,o,i,s,u;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,o=t.refreshTokenKey,this._cache.getStore(o)){e.next=3;break}throw new me({message:"refresh token不存在，登录状态异常"});case 3:if(i=this._cache.getStore(n),s=this._cache.getStore(r),u=!0,e.t0=this._shouldRefreshAccessTokenHook,!e.t0){e.next=9;break}return e.next=8,this._shouldRefreshAccessTokenHook(i,s);case 8:e.t0=!e.sent;case 9:if(e.t1=e.t0,!e.t1){e.next=12;break}u=!1;case 12:return e.abrupt("return",(!i||!s||s<Date.now())&&u?this.refreshAccessToken():{accessToken:i,accessTokenExpire:s});case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"request",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n,r){var o,i,s,u,c,l,f,d,h,p,g,v,m,y,_;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(o="x-tcb-trace_".concat(this.config.env),i="application/x-www-form-urlencoded",s=x({action:t,env:this.config.env,dataVersion:"2019-08-16"},n),e.t0=-1===ct.indexOf(t),!e.t0){e.next=9;break}return this._cache.keys,e.next=8,this.oauth.getAccessToken();case 8:s.access_token=e.sent;case 9:if("storage.uploadFile"!==t){e.next=15;break}for(c in u=new FormData,u)u.hasOwnProperty(c)&&void 0!==u[c]&&u.append(c,s[c]);i="multipart/form-data",e.next=17;break;case 15:for(l in i="application/json",u={},s)void 0!==s[l]&&(u[l]=s[l]);case 17:return f={headers:{"content-type":i}},r&&r.timeout&&(f.timeout=r.timeout),r&&r.onUploadProgress&&(f.onUploadProgress=r.onUploadProgress),d=this._localCache.getStore(o),d&&(f.headers["X-TCB-Trace"]=d),h=n.parse,p=n.inQuery,g=n.search,v={env:this.config.env},h&&(v.parse=!0),p&&(v=x(x({},p),v)),m=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=/\?/.test(t),o="";for(var i in n)""===o?!r&&(t+="?"):o+="&",o+="".concat(i,"=").concat(encodeURIComponent(n[i]));return/^http(s)?\:\/\//.test(t+=o)?t:"".concat(e).concat(t)}(Ie,"//tcb-api.tencentcloudapi.com/web",v),g&&(m+=g),e.next=28,this.post(x({url:m,data:u},f));case 28:if(y=e.sent,_=y.header&&y.header["x-tcb-trace"],_&&this._localCache.setStore(o,_),(200===Number(y.status)||200===Number(y.statusCode))&&y.data){e.next=32;break}throw new me({code:"NETWORK_ERROR",message:"network request error"});case 32:return e.abrupt("return",y);case 33:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}()},{key:"send",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,o,i,s=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=s.length>1&&void 0!==s[1]?s[1]:{},r=s.length>2&&void 0!==s[2]?s[2]:{},e.next=4,this.request(t,n,x(x({},r),{},{onUploadProgress:n.onUploadProgress}));case 4:if(o=e.sent,"ACCESS_TOKEN_DISABLED"!==o.data.code&&"ACCESS_TOKEN_EXPIRED"!==o.data.code||-1!==ct.indexOf(t)){e.next=14;break}return e.next=8,this.oauth.refreshAccessToken();case 8:return e.next=10,this.request(t,n,x(x({},r),{},{onUploadProgress:n.onUploadProgress}));case 10:if(i=e.sent,!i.data.code){e.next=13;break}throw new me({code:i.data.code,message:$e(i.data.message)});case 13:return e.abrupt("return",i.data);case 14:if(!o.data.code){e.next=16;break}throw new me({code:o.data.code,message:$e(o.data.message)});case 16:return e.abrupt("return",o.data);case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,o=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(o,e)}}]),e}(),pt={};function gt(e){return pt[e]}var vt=function(){function e(t){(0,m.default)(this,e),this.config=t,this._cache=Je(t.env),this._request=gt(t.env)}return(0,y.default)(e,[{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,o=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(o,e)}},{key:"setAccessToken",value:function(e,t){var n=this._cache.keys,r=n.accessTokenKey,o=n.accessTokenExpireKey;this._cache.setStore(r,e),this._cache.setStore(o,t)}},{key:"refreshUserInfo",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getUserInfo",{});case 2:return t=e.sent,n=t.data,e.abrupt("return",(this.setLocalUserInfo(n),n));case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e)}}]),e}(),mt=function(){function e(t){if((0,m.default)(this,e),!t)throw new me({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=t,this._cache=Je(this._envId),this._request=gt(this._envId),this.setUserInfo()}return(0,y.default)(e,[{key:"linkWithTicket",value:function(e){if("string"!=typeof e)throw new me({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}},{key:"linkWithRedirect",value:function(e){e.signInWithRedirect()}},{key:"updatePassword",value:function(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}},{key:"updateEmail",value:function(e){return this._request.send("auth.updateEmail",{newEmail:e})}},{key:"updateUsername",value:function(e){if("string"!=typeof e)throw new me({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}},{key:"getLinkedUidList",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,o;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getLinkedUidList",{});case 2:return t=e.sent,n=t.data,r=!1,o=n.users,e.abrupt("return",(o.forEach((function(e){e.wxOpenId&&e.wxPublicId&&(r=!0)})),{users:o,hasPrimaryUid:r}));case 7:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setPrimaryUid",value:function(e){return this._request.send("auth.setPrimaryUid",{uid:e})}},{key:"unlink",value:function(e){return this._request.send("auth.unlink",{platform:e})}},{key:"update",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,o,i,s,u,c,l;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.nickName,r=t.gender,o=t.avatarUrl,i=t.province,s=t.country,u=t.city,e.next=8,this._request.send("auth.updateUserInfo",{nickName:n,gender:r,avatarUrl:o,province:i,country:s,city:u});case 8:c=e.sent,l=c.data,this.setLocalUserInfo(l);case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refresh",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.oauth.getUserInfo();case 2:return t=e.sent,e.abrupt("return",(this.setLocalUserInfo(t),t));case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setUserInfo",value:function(){var e=this,t=this._cache.keys.userInfoKey,n=this._cache.getStore(t);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((function(t){e[t]=n[t]})),this.location={country:n.country,province:n.province,city:n.city}}},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e),this.setUserInfo()}}]),e}(),yt=function(){function e(t){if((0,m.default)(this,e),!t)throw new me({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=Je(t);var n=this._cache.keys,r=n.refreshTokenKey,o=n.accessTokenKey,i=n.accessTokenExpireKey,a=this._cache.getStore(r),s=this._cache.getStore(o),u=this._cache.getStore(i);this.credential={refreshToken:a,accessToken:s,accessTokenExpire:u},this.user=new mt(t)}return(0,y.default)(e,[{key:"isAnonymousAuth",get:function(){return this.loginType===tt.ANONYMOUS}},{key:"isCustomAuth",get:function(){return this.loginType===tt.CUSTOM}},{key:"isWeixinAuth",get:function(){return this.loginType===tt.WECHAT||this.loginType===tt.WECHAT_OPEN||this.loginType===tt.WECHAT_PUBLIC}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}]),e}(),_t=function(e){(0,h.default)(n,e);var t=S(n);function n(){return(0,m.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._cache.updatePersistence("local"),e.next=3,this._request.oauth.getAccessToken();case 3:return Ze(nt),Ze(ot,{env:this.config.env,loginType:tt.ANONYMOUS,persistence:"local"}),t=new yt(this.config.env),e.next=8,t.user.refresh();case 8:return e.abrupt("return",t);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,o,i,s,u;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=this._cache.keys,r=n.anonymousUuidKey,o=n.refreshTokenKey,i=this._cache.getStore(r),s=this._cache.getStore(o),e.next=7,this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:i,refresh_token:s,ticket:t});case 7:if(u=e.sent,!u.refresh_token){e.next=16;break}return this._clearAnonymousUUID(),this.setRefreshToken(u.refresh_token),e.next=13,this._request.refreshAccessToken();case 13:return Ze(it,{env:this.config.env}),Ze(ot,{loginType:tt.CUSTOM,persistence:"local"}),e.abrupt("return",{credential:{refreshToken:u.refresh_token}});case 16:throw new me({message:"匿名转化失败"});case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"_setAnonymousUUID",value:function(e){var t=this._cache.keys,n=t.anonymousUuidKey,r=t.loginTypeKey;this._cache.removeStore(n),this._cache.setStore(n,e),this._cache.setStore(r,tt.ANONYMOUS)}},{key:"_clearAnonymousUUID",value:function(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}]),n}(vt),bt=function(e){(0,h.default)(n,e);var t=S(n);function n(){return(0,m.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new me({code:"PARAM_ERROR",message:"ticket must be a string"});case 2:return n=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signInWithTicket",{ticket:t,refresh_token:this._cache.getStore(n)||""});case 5:if(r=e.sent,!r.refresh_token){e.next=15;break}return this.setRefreshToken(r.refresh_token),e.next=10,this._request.refreshAccessToken();case 10:return Ze(nt),Ze(ot,{env:this.config.env,loginType:tt.CUSTOM,persistence:this.config.persistence}),e.next=14,this.refreshUserInfo();case 14:return e.abrupt("return",new yt(this.config.env));case 15:throw new me({message:"自定义登录失败"});case 16:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),n}(vt),wt=function(e){(0,h.default)(n,e);var t=S(n);function n(){return(0,m.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r,o,i,s,u;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new me({code:"PARAM_ERROR",message:"email must be a string"});case 2:return r=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signIn",{loginType:"EMAIL",email:t,password:n,refresh_token:this._cache.getStore(r)||""});case 5:if(o=e.sent,i=o.refresh_token,s=o.access_token,u=o.access_token_expire,!i){e.next=22;break}if(this.setRefreshToken(i),!s||!u){e.next=15;break}this.setAccessToken(s,u),e.next=17;break;case 15:return e.next=17,this._request.refreshAccessToken();case 17:return e.next=19,this.refreshUserInfo();case 19:return Ze(nt),Ze(ot,{env:this.config.env,loginType:tt.EMAIL,persistence:this.config.persistence}),e.abrupt("return",new yt(this.config.env));case 22:throw o.code?new me({code:o.code,message:"邮箱登录失败: ".concat(o.message)}):new me({message:"邮箱登录失败"});case 23:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"activate",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.activateEndUserMail",{token:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"resetPasswordWithToken",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.resetPasswordWithToken",{token:t,newPassword:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(vt),kt=function(e){(0,h.default)(n,e);var t=S(n);function n(){return(0,m.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r,o,i,s,u;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new me({code:"PARAM_ERROR",message:"username must be a string"});case 2:return"string"!=typeof n&&(n="",console.warn("password is empty")),r=this._cache.keys.refreshTokenKey,e.next=6,this._request.send("auth.signIn",{loginType:tt.USERNAME,username:t,password:n,refresh_token:this._cache.getStore(r)||""});case 6:if(o=e.sent,i=o.refresh_token,s=o.access_token_expire,u=o.access_token,!i){e.next=23;break}if(this.setRefreshToken(i),!u||!s){e.next=16;break}this.setAccessToken(u,s),e.next=18;break;case 16:return e.next=18,this._request.refreshAccessToken();case 18:return e.next=20,this.refreshUserInfo();case 20:return Ze(nt),Ze(ot,{env:this.config.env,loginType:tt.USERNAME,persistence:this.config.persistence}),e.abrupt("return",new yt(this.config.env));case 23:throw o.code?new me({code:o.code,message:"用户名密码登录失败: ".concat(o.message)}):new me({message:"用户名密码登录失败"});case 24:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(vt),xt=function(){function e(t){(0,m.default)(this,e),this.config=t,this._cache=Je(t.env),this._request=gt(t.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Qe(ot,this._onLoginTypeChanged)}return(0,y.default)(e,[{key:"currentUser",get:function(){var e=this.hasLoginState();return e&&e.user||null}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}},{key:"anonymousAuthProvider",value:function(){return new _t(this.config)}},{key:"customAuthProvider",value:function(){return new bt(this.config)}},{key:"emailAuthProvider",value:function(){return new wt(this.config)}},{key:"usernameAuthProvider",value:function(){return new kt(this.config)}},{key:"signInAnonymously",value:function(){var e=(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new _t(this.config).signIn());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signInWithEmailAndPassword",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new wt(this.config).signIn(t,n));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"signInWithUsernameAndPassword",value:function(e,t){return new kt(this.config).signIn(e,t)}},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._anonymousAuthProvider||(this._anonymousAuthProvider=new _t(this.config)),Qe(it,this._onAnonymousConverted),e.next=3,this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(t);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"signOut",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,o,i,s;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.loginType!==tt.ANONYMOUS){e.next=2;break}throw new me({message:"匿名用户不支持登出操作"});case 2:if(t=this._cache.keys,n=t.refreshTokenKey,r=t.accessTokenKey,o=t.accessTokenExpireKey,i=this._cache.getStore(n),i){e.next=5;break}return e.abrupt("return");case 5:return e.next=7,this._request.send("auth.logout",{refresh_token:i});case 7:return s=e.sent,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.removeStore(o),Ze(nt),Ze(ot,{env:this.config.env,loginType:tt.NULL,persistence:this.config.persistence}),s));case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signUpWithEmailAndPassword",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.signUpWithEmailAndPassword",{email:t,password:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"sendPasswordResetEmail",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.sendPasswordResetEmail",{email:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"onLoginStateChanged",value:function(e){var t=this;Qe(nt,(function(){var n=t.hasLoginState();e.call(t,n)}));var n=this.hasLoginState();e.call(this,n)}},{key:"onLoginStateExpired",value:function(e){Qe(rt,e.bind(this))}},{key:"onAccessTokenRefreshed",value:function(e){Qe(at,e.bind(this))}},{key:"onAnonymousConverted",value:function(e){Qe(it,e.bind(this))}},{key:"onLoginTypeChanged",value:function(e){var t=this;Qe(ot,(function(){var n=t.hasLoginState();e.call(t,n)}))}},{key:"getAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.getAccessToken();case 2:return e.t0=e.sent.accessToken,e.t1=this.config.env,e.abrupt("return",{accessToken:e.t0,env:e.t1});case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"hasLoginState",value:function(){var e=this._cache.keys,t=e.accessTokenKey,n=e.accessTokenExpireKey,r=this._cache.getStore(t),o=this._cache.getStore(n);return this._request.oauth.isAccessTokenExpired(r,o)?null:new yt(this.config.env)}},{key:"isUsernameRegistered",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new me({code:"PARAM_ERROR",message:"username must be a string"});case 2:return e.next=4,this._request.send("auth.isUsernameRegistered",{username:t});case 4:return n=e.sent,r=n.data,e.abrupt("return",r&&r.isRegistered);case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getLoginState",value:function(){return Promise.resolve(this.hasLoginState())}},{key:"signInWithTicket",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new bt(this.config).signIn(t));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"shouldRefreshAccessToken",value:function(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}},{key:"getUserInfo",value:function(){return this._request.send("auth.getUserInfo",{}).then((function(e){return e.code?e:x(x({},e.data),{},{requestId:e.seqId})}))}},{key:"getAuthHeader",value:function(){var e=this._cache.keys,t=e.refreshTokenKey,n=e.accessTokenKey,r=this._cache.getStore(t);return{"x-cloudbase-credentials":this._cache.getStore(n)+"/@@/"+r}}},{key:"_onAnonymousConverted",value:function(e){var t=e.data.env;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}},{key:"_onLoginTypeChanged",value:function(e){var t=e.data,n=t.loginType,r=t.persistence,o=t.env;o===this.config.env&&(this._cache.updatePersistence(r),this._cache.setStore(this._cache.keys.loginTypeKey,n))}}]),e}(),St=function(e,t){t=t||Me();var n=gt(this.config.env),r=e.cloudPath,o=e.filePath,i=e.onUploadProgress,a=e.fileType,s=void 0===a?"image":a;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){var a=e.data,u=a.url,c=a.authorization,l=a.token,f=a.fileId,d=a.cosFileId,h=e.requestId,p={key:r,signature:c,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":l};n.upload({url:u,data:p,file:o,name:r,fileType:s,onUploadProgress:i}).then((function(e){201===e.statusCode?t(null,{fileID:f,requestId:h}):t(new me({code:"STORAGE_REQUEST_FAIL",message:"STORAGE_REQUEST_FAIL: ".concat(e.data)}))})).catch((function(e){t(e)}))})).catch((function(e){t(e)})),t.promise},Ot=function(e,t){t=t||Me();var n=gt(this.config.env),r=e.cloudPath;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){t(null,e)})).catch((function(e){t(e)})),t.promise},Tt=function(e,t){var n=e.fileList;if(t=t||Me(),!n||!Array.isArray(n))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};var r,o=b(n);try{for(o.s();!(r=o.n()).done;){var i=r.value;if(!i||"string"!=typeof i)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"}}}catch(s){o.e(s)}finally{o.f()}var a={fileid_list:n};return gt(this.config.env).send("storage.batchDeleteFile",a).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},Pt=function(e,t){var n=e.fileList;t=t||Me(),n&&Array.isArray(n)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});var r,o=[],i=b(n);try{for(i.s();!(r=i.n()).done;){var a=r.value;"object"==(0,c.default)(a)?(a.hasOwnProperty("fileID")&&a.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),o.push({fileid:a.fileID,max_age:a.maxAge})):"string"==typeof a?o.push({fileid:a}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"})}}catch(u){i.e(u)}finally{i.f()}var s={file_list:o};return gt(this.config.env).send("storage.batchGetDownloadUrl",s).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},Ct=function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r,o,i,s;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=t.fileID,e.next=3,Pt.call(this,{fileList:[{fileID:r,maxAge:600}]});case 3:if(o=e.sent.fileList[0],"SUCCESS"===o.code){e.next=6;break}return e.abrupt("return",n?n(o):new Promise((function(e){e(o)})));case 6:if(i=gt(this.config.env),s=o.download_url,s=encodeURI(s),n){e.next=10;break}return e.abrupt("return",i.download({url:s}));case 10:return e.t0=n,e.next=13,i.download({url:s});case 13:e.t1=e.sent,(0,e.t0)(e.t1);case 15:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),At=function(e,t){var n,r=e.name,o=e.data,i=e.query,a=e.parse,s=e.search,u=e.timeout,c=t||Me();try{n=o?JSON.stringify(o):""}catch(r){return Promise.reject(r)}if(!r)return Promise.reject(new me({code:"PARAM_ERROR",message:"函数名不能为空"}));var l={inQuery:i,parse:a,search:s,function_name:r,request_data:n};return gt(this.config.env).send("functions.invokeFunction",l,{timeout:u}).then((function(e){if(e.code)c(null,e);else{var t=e.data.response_data;if(a)c(null,{result:t,requestId:e.requestId});else try{t=JSON.parse(e.data.response_data),c(null,{result:t,requestId:e.requestId})}catch(e){c(new me({message:"response data must be json"}))}}return c.promise})).catch((function(e){c(e)})),c.promise},It={timeout:15e3,persistence:"session"},Et={},Dt=function(){function e(t){(0,m.default)(this,e),this.config=t||this.config,this.authObj=void 0}return(0,y.default)(e,[{key:"init",value:function(t){switch(Fe.adapter||(this.requestClient=new Fe.adapter.reqClass({timeout:t.timeout||5e3,timeoutMsg:"请求在".concat((t.timeout||5e3)/1e3,"s内未完成，已中断")})),this.config=x(x({},It),t),!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new e(this.config)}},{key:"auth",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.persistence;if(this.authObj)return this.authObj;var n,r=t||Fe.adapter.primaryStorage||It.persistence;return r!==this.config.persistence&&(this.config.persistence=r),function(e){var t=e.env;Ke[t]=new Ve(e),We[t]=new Ve(x(x({},e),{},{persistence:"local"}))}(this.config),n=this.config,pt[n.env]=new ht(n),this.authObj=new xt(this.config),this.authObj}},{key:"on",value:function(e,t){return Qe.apply(this,[e,t])}},{key:"off",value:function(e,t){return et.apply(this,[e,t])}},{key:"callFunction",value:function(e,t){return At.apply(this,[e,t])}},{key:"deleteFile",value:function(e,t){return Tt.apply(this,[e,t])}},{key:"getTempFileURL",value:function(e,t){return Pt.apply(this,[e,t])}},{key:"downloadFile",value:function(e,t){return Ct.apply(this,[e,t])}},{key:"uploadFile",value:function(e,t){return St.apply(this,[e,t])}},{key:"getUploadMetadata",value:function(e,t){return Ot.apply(this,[e,t])}},{key:"registerExtension",value:function(e){Et[e.name]=e}},{key:"invokeExtension",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=Et[t],r){e.next=3;break}throw new me({message:"扩展".concat(t," 必须先注册")});case 3:return e.next=5,r.invoke(n,this);case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"useAdapters",value:function(e){var t=function(e){var t,n,r=(t=e,"[object Array]"===Object.prototype.toString.call(t)?e:[e]),o=b(r);try{for(o.s();!(n=o.n()).done;){var i=n.value,a=i.isMatch,s=i.genAdapter,u=i.runtime;if(a())return{adapter:s(),runtime:u}}}catch(c){o.e(c)}finally{o.f()}}(e)||{},n=t.adapter,r=t.runtime;n&&(Fe.adapter=n),r&&(Fe.runtime=r)}}]),e}(),jt=new Dt;function Lt(e,t,n){void 0===n&&(n={});var r=/\?/.test(t),o="";for(var i in n)""===o?!r&&(t+="?"):o+="&",o+=i+"="+encodeURIComponent(n[i]);return/^http(s)?:\/\//.test(t+=o)?t:""+e+t}var Nt=function(){function e(){(0,m.default)(this,e)}return(0,y.default)(e,[{key:"get",value:function(e){var t=e.url,n=e.data,r=e.headers,o=e.timeout;return new Promise((function(e,i){be.request({url:Lt("https:",t),data:n,method:"GET",header:r,timeout:o,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"post",value:function(e){var t=e.url,n=e.data,r=e.headers,o=e.timeout;return new Promise((function(e,i){be.request({url:Lt("https:",t),data:n,method:"POST",header:r,timeout:o,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"upload",value:function(e){return new Promise((function(t,n){var r=e.url,o=e.file,i=e.data,a=e.headers,s=e.fileType,u=be.uploadFile({url:Lt("https:",r),name:"file",formData:Object.assign({},i),filePath:o,fileType:s,header:a,success:function(e){var n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(n.statusCode=parseInt(i.success_action_status,10)),t(n)},fail:function(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&u&&"function"==typeof u.onProgressUpdate&&u.onProgressUpdate((function(t){e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}]),e}(),Mt={setItem:function(e,t){be.setStorageSync(e,t)},getItem:function(e){return be.getStorageSync(e)},removeItem:function(e){be.removeStorageSync(e)},clear:function(){be.clearStorageSync()}},Rt={genAdapter:function(){return{root:{},reqClass:Nt,localStorage:Mt,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};jt.useAdapters(Rt);var Bt=jt,$t=Bt.init;Bt.init=function(e){e.env=e.spaceId;var t=$t.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;var n=t.auth;return t.auth=function(e){var t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((function(e){var n;t[e]=(n=t[e],function(e){e=e||{};var t=ve(e),r=t.success,o=t.fail,i=t.complete;if(!(r||o||i))return n.call(this,e);n.call(this,e).then((function(e){r&&r(e),i&&i(e)}),(function(e){o&&o(e),i&&i(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var Ut=Bt;function Ft(e,t){return qt.apply(this,arguments)}function qt(){return qt=(0,f.default)(a.default.mark((function e(t,n){var r,o,i;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r="http://".concat(t,":").concat(n,"/system/ping"),e.prev=1,e.next=4,i={url:r,timeout:500},new Promise((function(e,t){be.request(x(x({},i),{},{success:function(t){e(t)},fail:function(e){t(e)}}))}));case 4:return o=e.sent,e.abrupt("return",!(!o.data||0!==o.data.code));case 8:return e.prev=8,e.t0=e["catch"](1),e.abrupt("return",!1);case 11:case"end":return e.stop()}}),e,null,[[1,8]])}))),qt.apply(this,arguments)}function Ht(e,t){return zt.apply(this,arguments)}function zt(){return zt=(0,f.default)(a.default.mark((function e(t,n){var r,o,i;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:o=0;case 1:if(!(o<t.length)){e.next=11;break}return i=t[o],e.next=5,Ft(i,n);case 5:if(!e.sent){e.next=8;break}return r=i,e.abrupt("break",11);case 8:o++,e.next=1;break;case 11:return e.abrupt("return",{address:r,port:n});case 12:case"end":return e.stop()}}),e)}))),zt.apply(this,arguments)}var Vt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"},Kt=function(){function e(t){if((0,m.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),!t.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},t),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=be}return(0,y.default)(e,[{key:"request",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r=this,o=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(o.length>1&&void 0!==o[1])||o[1],n=!1,!n){e.next=8;break}return e.next=5,this.setupLocalRequest(t);case 5:e.t0=e.sent,e.next=9;break;case 8:e.t0=this.setupRequest(t);case 9:return t=e.t0,e.abrupt("return",Promise.resolve().then((function(){return n?r.requestLocal(t):Pe.wrappedRequest(t,r.adapter.request)})));case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"requestLocal",value:function(e){var t=this;return new Promise((function(n,r){t.adapter.request(Object.assign(e,{complete:function(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){var t=e.data&&e.data.code||"SYS_ERR",o=e.data&&e.data.message||"request:fail";return r(new me({code:t,message:o}))}n({success:!0,result:e.data})}}))}))}},{key:"setupRequest",value:function(e){var t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=Pe.sign(t,this.config.clientSecret);var r=Oe();n["x-client-info"]=encodeURIComponent(JSON.stringify(r));var o=we(),i=o.token;return n["x-client-token"]=i,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}},{key:"setupLocalRequest",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,o,i,s,u,c,l,f;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=Oe(),r=we(),o=r.token,i=Object.assign({},t,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:n,token:o}),s=this.__dev__&&this.__dev__.debugInfo||{},u=s.address,c=s.servePort,e.next=9,Ht(u,c);case 9:return l=e.sent,f=l.address,e.abrupt("return",{url:"http://".concat(f,":").concat(c,"/").concat(Vt[t.method]),method:"POST",data:i,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))});case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}},{key:"getUploadFileOptions",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}},{key:"reportUploadFile",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}},{key:"uploadFile",value:function(e){var t,n=this,r=e.filePath,o=e.cloudPath,i=e.fileType,a=void 0===i?"image":i,s=e.onUploadProgress;if(!o)throw new me({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});return this.getUploadFileOptions({cloudPath:o}).then((function(e){var o=e.result,i=o.url,u=o.formData,c=o.name;return t=e.result.fileUrl,new Promise((function(e,t){var o=n.adapter.uploadFile({url:i,formData:u,name:c,filePath:r,fileType:a,success:function(n){n&&n.statusCode<400?e(n):t(new me({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){t(new me({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&o&&"function"==typeof o.onProgressUpdate&&o.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((function(){return n.reportUploadFile({cloudPath:o})})).then((function(e){return new Promise((function(n,o){e.success?n({success:!0,filePath:r,fileID:t}):o(new me({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))}))}},{key:"deleteFile",value:function(e){var t=e.fileList,n={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:t})};return this.request(n).then((function(e){if(e.success)return e.result;throw new me({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList,n=e.maxAge;if(!Array.isArray(t)||0===t.length)throw new me({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});var r={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:t,maxAge:n})};return this.request(r).then((function(e){if(e.success)return{fileList:e.result.fileList.map((function(e){return{fileID:e.fileID,tempFileURL:e.tempFileURL}}))};throw new me({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}]),e}(),Wt={init:function(e){var t=new Kt(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Jt=O((function(e,t){e.exports=P.enc.Hex}));function Gt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Yt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.data,r=t.functionName,o=t.method,i=t.headers,a=t.signHeaderKeys,s=void 0===a?[]:a,c=t.config,l=String(Date.now()),f=Gt(),d=Object.assign({},i,{"x-from-app-id":c.spaceAppId,"x-from-env-id":c.spaceId,"x-to-env-id":c.spaceId,"x-from-instance-id":l,"x-from-function-name":r,"x-client-timestamp":l,"x-alipay-source":"client","x-request-id":f,"x-alipay-callid":f,"x-trace-id":f}),h=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(s),p=e.split("?")||[],g=(0,u.default)(p,2),v=g[0],m=void 0===v?"":v,y=g[1],_=void 0===y?"":y,b=function(e){var t="HMAC-SHA256",n=e.signedHeaders.join(";"),r=e.signedHeaders.map((function(t){return"".concat(t.toLowerCase(),":").concat(e.headers[t],"\n")})).join(""),o=Le(e.body).toString(Jt),i="".concat(e.method.toUpperCase(),"\n").concat(e.path,"\n").concat(e.query,"\n").concat(r,"\n").concat(n,"\n").concat(o,"\n"),a=Le(i).toString(Jt),s="".concat(t,"\n").concat(e.timestamp,"\n").concat(a,"\n"),u=Ne(s,e.secretKey).toString(Jt);return"".concat(t," Credential=").concat(e.secretId,", SignedHeaders=").concat(n,", Signature=").concat(u)}({path:m,query:_,method:o,headers:d,timestamp:l,body:JSON.stringify(n),secretId:c.accessKey,secretKey:c.secretKey,signedHeaders:h.sort()});return{url:"".concat(c.endpoint).concat(e),headers:Object.assign({},d,{Authorization:b})}}function Xt(e){var t=e.url,n=e.data,r=e.method,o=void 0===r?"POST":r,i=e.headers,a=void 0===i?{}:i,s=e.timeout;return new Promise((function(e,r){be.request({url:t,method:o,data:"object"==(0,c.default)(n)?JSON.stringify(n):n,header:a,dataType:"json",timeout:s,complete:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=a["x-trace-id"]||"";if(!t.statusCode||t.statusCode>=400){var o=t.data||{},i=o.message,s=o.errMsg,u=o.trace_id;return r(new me({code:"SYS_ERR",message:i||s||"request:fail",requestId:u||n}))}e({status:t.statusCode,data:t.data,headers:t.header,requestId:n})}})}))}function Qt(e,t){var n=e.path,r=e.data,o=e.method,i=void 0===o?"GET":o,a=Yt(n,{functionName:"",data:r,method:i,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t}),s=a.url,u=a.headers;return Xt({url:s,data:r,method:i,headers:u}).then((function(e){var t=e.data||{};if(!t.success)throw new me({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((function(e){throw new me({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Zt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new me({code:"INVALID_PARAM",message:"fileID不合法"});var r=t.substring(0,n),o=t.substring(n+1);return r!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),o}function en(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}var tn=function(){function e(t){(0,m.default)(this,e),this.config=t}return(0,y.default)(e,[{key:"signedURL",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="/ws/function/".concat(e),r=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),o=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Gt(),timestamp:""+Date.now()}),i=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return o[e]?"".concat(e,"=").concat(o[e]):null})).filter(Boolean).join("&"),"host:".concat(r)].join("\n"),a=["HMAC-SHA256",Le(i).toString(Jt)].join("\n"),s=Ne(a,this.config.secretKey).toString(Jt),u=Object.keys(o).map((function(e){return"".concat(e,"=").concat(encodeURIComponent(o[e]))})).join("&");return"".concat(this.config.wsEndpoint).concat(n,"?").concat(u,"&signature=").concat(s)}}]),e}(),nn=function(){function e(t){if((0,m.default)(this,e),["spaceId","spaceAppId","accessKey","secretKey"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),t.endpoint){if("string"!=typeof t.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(t.endpoint))throw new Error("endpoint must start with https://");t.endpoint=t.endpoint.replace(/\/$/,"")}this.config=Object.assign({},t,{endpoint:t.endpoint||"https://".concat(t.spaceId,".api-hz.cloudbasefunction.cn"),wsEndpoint:t.wsEndpoint||"wss://".concat(t.spaceId,".api-hz.cloudbasefunction.cn")}),this._websocket=new tn(this.config)}return(0,y.default)(e,[{key:"callFunction",value:function(e){return function(e,t){var n=e.name,r=e.data,o=e.async,i=void 0!==o&&o,a=e.timeout,s="POST",u={"x-to-function-name":n};i&&(u["x-function-invoke-type"]="async");var c=Yt("/functions/invokeFunction",{functionName:n,data:r,method:s,headers:u,signHeaderKeys:["x-to-function-name"],config:t}),l=c.url,f=c.headers;return Xt({url:l,data:r,method:s,headers:f,timeout:a}).then((function(e){var t=0;if(i){var n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new me({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((function(e){throw new me({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}},{key:"uploadFileToOSS",value:function(e){var t=e.url,n=e.filePath,r=e.fileType,o=e.formData,i=e.onUploadProgress;return new Promise((function(e,a){var s=be.uploadFile({url:t,filePath:n,fileType:r,formData:o,name:"file",success:function(t){t&&t.statusCode<400?e(t):a(new me({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){a(new me({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof i&&s&&"function"==typeof s.onProgressUpdate&&s.onProgressUpdate((function(e){i({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"uploadFile",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,o,i,s,u,c,l,f,d,h;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,o=void 0===r?"":r,i=t.fileType,s=void 0===i?"image":i,u=t.onUploadProgress,"string"===R(o)){e.next=3;break}throw new me({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(o=o.trim()){e.next=5;break}throw new me({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(o)){e.next=7;break}throw new me({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:return e.next=9,Qt({path:"/".concat(o.replace(/^\//,""),"?post_url")},this.config);case 9:return c=e.sent,l=c.file_id,f=c.upload_url,d=c.form_data,h=d&&d.reduce((function(e,t){return e[t.key]=t.value,e}),{}),e.abrupt("return",this.uploadFileToOSS({url:f,filePath:n,fileType:s,formData:h,onUploadProgress:u}).then((function(){return{fileID:l}})));case 15:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r=this;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.fileList,e.abrupt("return",new Promise((function(e,t){(!n||n.length<0)&&e({code:"INVALID_PARAM",message:"fileList不能为空数组"}),n.length>50&&e({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});var o,i=[],a=b(n);try{for(a.s();!(o=a.n()).done;){var s=o.value,u=void 0;"string"!==R(s)&&e({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{u=Zt.call(r,s)}catch(e){console.warn(e.errCode,e.errMsg),u=s}i.push({file_id:u,expire:600})}}catch(c){a.e(c)}finally{a.f()}Qt({path:"/?download_url",data:{file_list:i},method:"POST"},r.config).then((function(t){var n=t.file_list,o=void 0===n?[]:n;e({fileList:o.map((function(e){return{fileID:en.call(r,e.file_id),tempFileURL:e.download_url}}))})})).catch((function(e){return t(e)}))})));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},{key:"connectWebSocket",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.name,r=t.query,e.abrupt("return",be.connectSocket({url:this._websocket.signedURL(n,r),complete:function(){}}));case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),rn={init:function(e){e.provider="alipay";var t=new nn(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function on(e){var t,n=e.data;t=Oe();var r=JSON.parse(JSON.stringify(n||{}));if(Object.assign(r,{clientInfo:t}),!r.uniIdToken){var o=we(),i=o.token;i&&(r.uniIdToken=i)}return r}var an=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}],sn=/[\\^$.*+?()[\]{}|]/g,un=RegExp(sn.source);function cn(e,t,n){return e.replace(new RegExp((r=t)&&un.test(r)?r.replace(sn,"\\$&"):r,"g"),n);var r}var ln={NONE:"none",REQUEST:"request",RESPONSE:"response",BOTH:"both"},fn="_globalUniCloudStatus",dn="_globalUniCloudSecureNetworkCache__{spaceId}",hn="uni-secure-network",pn={SYSTEM_ERROR:{code:2e4,message:"System error"},APP_INFO_INVALID:{code:20101,message:"Invalid client"},GET_ENCRYPT_KEY_FAILED:{code:20102,message:"Get encrypt key failed"}};function gn(e){var t=e||{},n=t.errSubject,r=t.subject,o=t.errCode,i=t.errMsg,a=t.code,s=t.message,u=t.cause;return new me({subject:n||r||hn,code:o||a||pn.SYSTEM_ERROR.code,message:i||s,cause:u})}var vn;vn="0123456789abcdef";var mn;function yn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.secretType;return t===ln.REQUEST||t===ln.RESPONSE||t===ln.BOTH}function _n(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name,n=e.data,r=void 0===n?{}:n;return"app"===J&&"DCloud-clientDB"===t&&"encryption"===r.redirectTo&&"getAppClientKey"===r.action}function bn(e){e.functionName,e.result,e.logPvd}function wn(e){var t=e.callFunction,n=function(n){var r=this,o=n.name;n.data=on.call(e,{data:n.data});var i={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],a=yn(n),s=_n(n),u=a||s;return t.call(this,n).then((function(e){return e.errCode=0,!u&&bn.call(r,{functionName:o,result:e,logPvd:i}),Promise.resolve(e)}),(function(e){return!u&&bn.call(r,{functionName:o,result:e,logPvd:i}),e&&e.message&&(e.message=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.message,n=void 0===t?"":t,r=e.extraInfo,o=void 0===r?{}:r,i=e.formatter,a=void 0===i?[]:i,s=0;s<a.length;s++){var u=a[s],c=u.rule,l=u.content,f=u.mode,d=n.match(c);if(d){for(var h=l,p=1;p<d.length;p++)h=cn(h,"{$".concat(p,"}"),d[p]);for(var g in o)h=cn(h,"{".concat(g,"}"),o[g]);return"replace"===f?h:n+h}}return n}({message:"[".concat(n.name,"]: ").concat(e.message),formatter:an,extraInfo:{functionName:o}})),Promise.reject(e)}))};e.callFunction=function(t){var r,o,i=e.config,a=i.provider,s=i.spaceId,u=t.name;return t.data=t.data||{},r=n,r=r.bind(e),o=_n(t)?n.call(e,t):function(e){var t=e.name,n=e.data,r=void 0===n?{}:n;return"mp-weixin"===J&&"uni-id-co"===t&&"secureNetworkHandshakeByWeixin"===r.method}(t)?r.call(e,t):yn(t)?new mn({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,r=e.functionName,o=xe(),i=o.appId,a=o.uniPlatform,s=o.osName,u=a;"app"===a&&(u=s);var c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,r=W;if(!r)return{};t=function(e){return"tencent"===e?"tcb":e}(t);var o=r.find((function(e){return e.provider===t&&e.spaceId===n}));return o&&o.config}({provider:t,spaceId:n});if(!c||!c.accessControl||!c.accessControl.enable)return!1;var l=c.accessControl.function||{},f=Object.keys(l);if(0===f.length)return!0;var d=function(e,t){for(var n,r,o,i=0;i<e.length;i++){var a=e[i];a!==t?"*"!==a?a.split(",").map((function(e){return e.trim()})).indexOf(t)>-1&&(r=a):o=a:n=a}return n||r||o}(f,r);if(!d)return!1;if((l[d]||[]).find((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.appId===i&&(e.platform||"").toLowerCase()===u.toLowerCase()})))return!0;throw console.error("此应用[appId: ".concat(i,", platform: ").concat(u,"]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client")),gn(pn.APP_INFO_INVALID)}({provider:a,spaceId:s,functionName:u})?new mn({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):r(t),Object.defineProperty(o,"result",{get:function(){return console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{}}}),o.then((function(e){return e}))}}mn="mp-weixin"!==J&&"app"!==J?function(){return(0,y.default)((function e(){throw(0,m.default)(this,e),gn({message:"Platform ".concat(J," is not supported by secure network")})}))}():function(){return(0,y.default)((function e(){throw(0,m.default)(this,e),gn({message:"Platform ".concat(J," is not enabled, please check whether secure network module is enabled in your manifest.json")})}))}();var kn=Symbol("CLIENT_DB_INTERNAL");function xn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=kn,e.inspect=null,e.__ob__=void 0,new Proxy(e,{get:function(e,n,r){if("_uniClient"===n)return null;if("symbol"==(0,c.default)(n))return e[n];if(n in e||"string"!=typeof n){var o=e[n];return"function"==typeof o?o.bind(e):o}return t.get(e,n,r)}})}function Sn(e){return{on:function(t,n){e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:function(t,n){e[t]=e[t]||[];var r=e[t].indexOf(n);-1!==r&&e[t].splice(r,1)}}}var On=["db.Geo","db.command","command.aggregate"];function Tn(e,t){return On.indexOf("".concat(e,".").concat(t))>-1}function Pn(e){switch(R(e)){case"array":return e.map((function(e){return Pn(e)}));case"object":return e._internalType===kn||Object.keys(e).forEach((function(t){e[t]=Pn(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function Cn(e){return e&&e.content&&e.content.$method}var An=function(){function e(t,n,r){(0,m.default)(this,e),this.content=t,this.prevStage=n||null,this.udb=null,this._database=r}return(0,y.default)(e,[{key:"toJSON",value:function(){for(var e=this,t=[e.content];e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((function(e){return{$method:e.$method,$param:Pn(e.$param)}}))}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}},{key:"getAction",value:function(){var e=this.toJSON().$db.find((function(e){return"action"===e.$method}));return e&&e.$param&&e.$param[0]}},{key:"getCommand",value:function(){return{$db:this.toJSON().$db.filter((function(e){return"action"!==e.$method}))}}},{key:"isAggregate",get:function(){for(var e=this;e;){var t=Cn(e),n=Cn(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}},{key:"isCommand",get:function(){for(var e=this;e;){if("command"===Cn(e))return!0;e=e.prevStage}return!1}},{key:"isAggregateCommand",get:function(){for(var e=this;e;){var t=Cn(e),n=Cn(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}},{key:"getNextStageFn",value:function(e){var t=this;return function(){return In({$method:e,$param:Pn(Array.from(arguments))},t,t._database)}}},{key:"count",get:function(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}},{key:"remove",get:function(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}},{key:"get",value:function(){return this._send("get",Array.from(arguments))}},{key:"add",get:function(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}},{key:"update",value:function(){return this._send("update",Array.from(arguments))}},{key:"end",value:function(){return this._send("end",Array.from(arguments))}},{key:"set",get:function(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}},{key:"_send",value:function(e,t){var n=this.getAction(),r=this.getCommand();return r.$db.push({$method:e,$param:Pn(t)}),this._database._callCloudFunction({action:n,command:r})}}]),e}();function In(e,t,n){return xn(new An(e,t,n),{get:function(e,t){var r="db";return e&&e.content&&(r=e.content.$method),Tn(r,t)?In({$method:t},e,n):function(){return In({$method:t,$param:Pn(Array.from(arguments))},e,n)}}})}function En(e){var t=e.path,n=e.method;return function(){function e(){(0,m.default)(this,e),this.param=Array.from(arguments)}return(0,y.default)(e,[{key:"toJSON",value:function(){return{$newDb:[].concat((0,l.default)(t.map((function(e){return{$method:e}}))),[{$method:n,$param:this.param}])}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}}]),e}()}var Dn=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.uniClient,r=void 0===n?{}:n,o=t.isJQL,i=void 0!==o&&o;(0,m.default)(this,e),this._uniClient=r,this._authCallBacks={},this._dbCallBacks={},r._isDefault&&(this._dbCallBacks=Q("_globalUniCloudDatabaseCallback")),i||(this.auth=Sn(this._authCallBacks)),this._isJQL=i,Object.assign(this,Sn(this._dbCallBacks)),this.env=xn({},{get:function(e,t){return{$env:t}}}),this.Geo=xn({},{get:function(e,t){return En({path:["Geo"],method:t})}}),this.serverDate=En({path:[],method:"serverDate"}),this.RegExp=En({path:[],method:"RegExp"})}return(0,y.default)(e,[{key:"getCloudEnv",value:function(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}},{key:"_callback",value:function(e,t){var n=this._dbCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,l.default)(t))}))}},{key:"_callbackAuth",value:function(e,t){var n=this._authCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,l.default)(t))}))}},{key:"multiSend",value:function(){var e=Array.from(arguments),t=e.map((function(e){var t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}]),e}();function jn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return xn(new e(t),{get:function(e,t){return Tn("db",t)?In({$method:t},null,e):function(){return In({$method:t,$param:Pn(Array.from(arguments))},null,e)}}})}var Ln=function(e){(0,h.default)(n,e);var t=S(n);function n(){return(0,m.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"_parseResult",value:function(e){return this._isJQL?e.result:e}},{key:"_callCloudFunction",value:function(e){var t=this,n=e.action,r=e.command,o=e.multiCommand,i=e.queryList;function a(e,t){if(o&&i)for(var n=0;n<i.length;n++){var r=i[n];r.udb&&"function"==typeof r.udb.setResult&&(t?r.udb.setResult(t):r.udb.setResult(e.result.dataList[n]))}}var s=this,u=this._isJQL?"databaseForJQL":"database";function c(e){return s._callback("error",[e]),re(oe(u,"fail"),e).then((function(){return re(oe(u,"complete"),e)})).then((function(){return a(null,e),de(se.RESPONSE,{type:ue.CLIENT_DB,content:e}),Promise.reject(e)}))}var l=re(oe(u,"invoke")),f=this._uniClient;return l.then((function(){return f.callFunction({name:"DCloud-clientDB",type:j.CLIENT_DB,data:{action:n,command:r,multiCommand:o}})})).then((function(e){var n=e.result,r=n.code,o=n.message,i=n.token,l=n.tokenExpired,f=n.systemInfo,d=void 0===f?[]:f;if(d)for(var h=0;h<d.length;h++){var p=d[h],g=p.level,v=p.message,m=p.detail,y="[System Info]"+v;m&&(y="".concat(y,"\n详细信息：").concat(m)),(console["app"===J&&"warn"===g?"error":g]||console.log)(y)}if(r)return c(new me({code:r,message:o,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,i&&l&&(ke({token:i,tokenExpired:l}),t._callbackAuth("refreshToken",[{token:i,tokenExpired:l}]),t._callback("refreshToken",[{token:i,tokenExpired:l}]),de(se.REFRESH_TOKEN,{token:i,tokenExpired:l}));for(var _=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}],b=function(t){var n=_[t],r=n.prop,o=n.tips;if(r in e.result){var i=e.result[r];Object.defineProperty(e.result,r,{get:function(){return console.warn(o),i}})}},w=0;w<_.length;w++)b(w);return function(e){return re(oe(u,"success"),e).then((function(){return re(oe(u,"complete"),e)})).then((function(){a(e,null);var t=s._parseResult(e);return de(se.RESPONSE,{type:ue.CLIENT_DB,content:t}),Promise.resolve(t)}))}(e)}),(function(e){return/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),c(new me({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId}))}))}}]),n}(Dn),Nn="token无效，跳转登录页面",Mn="token过期，跳转登录页面",Rn={TOKEN_INVALID_TOKEN_EXPIRED:Mn,TOKEN_INVALID_INVALID_CLIENTID:Nn,TOKEN_INVALID:Nn,TOKEN_INVALID_WRONG_TOKEN:Nn,TOKEN_INVALID_ANONYMOUS_USER:Nn},Bn={"uni-id-token-expired":Mn,"uni-id-check-token-failed":Nn,"uni-id-token-not-exist":Nn,"uni-id-check-device-feature-failed":Nn},$n=x(x(x({},Rn),Bn),{},{default:"用户未登录或登录状态过期，自动跳转登录页面"});function Un(e,t){var n="";return n=e?"".concat(e,"/").concat(t):t,n.replace(/^\//,"")}function Fn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=[],r=[];return e.forEach((function(e){!0===e.needLogin?n.push(Un(t,e.path)):!1===e.needLogin&&r.push(Un(t,e.path))})),{needLoginPage:n,notNeedLoginPage:r}}function qn(e){return e.split("?")[0].replace(/^\//,"")}function Hn(){return function(e){var t=e&&e.$page&&e.$page.fullPath;return t?("/"!==t.charAt(0)&&(t="/"+t),t):""}(function(){var e=getCurrentPages();return e[e.length-1]}())}function zn(){return qn(Hn())}function Vn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;var n=t.list,r=qn(e);return n.some((function(e){return e.pagePath===r}))}var Kn,Wn=!!_.default.uniIdRouter,Jn=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_.default,t=e.pages,n=void 0===t?[]:t,r=e.subPackages,o=void 0===r?[]:r,i=e.uniIdRouter,a=void 0===i?{}:i,s=e.tabBar,u=void 0===s?{}:s,c=a.loginPage,f=a.needLogin,d=void 0===f?[]:f,h=a.resToLogin,p=void 0===h||h,g=Fn(n),v=g.needLoginPage,m=g.notNeedLoginPage,y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[],n=[];return e.forEach((function(e){var r=e.root,o=e.pages,i=void 0===o?[]:o,a=Fn(i,r),s=a.needLoginPage,u=a.notNeedLoginPage;t.push.apply(t,(0,l.default)(s)),n.push.apply(n,(0,l.default)(u))})),{needLoginPage:t,notNeedLoginPage:n}}(o),b=y.needLoginPage,w=y.notNeedLoginPage;return{loginPage:c,routerNeedLogin:d,resToLogin:p,needLoginPage:[].concat((0,l.default)(v),(0,l.default)(b)),notNeedLoginPage:[].concat((0,l.default)(m),(0,l.default)(w)),loginPageInTabBar:Vn(c,u)}}(),Gn=Jn.loginPage,Yn=Jn.routerNeedLogin,Xn=Jn.resToLogin,Qn=Jn.needLoginPage,Zn=Jn.notNeedLoginPage,er=Jn.loginPageInTabBar;if(Qn.indexOf(Gn)>-1)throw new Error("Login page [".concat(Gn,'] should not be "needLogin", please check your pages.json'));function tr(e){var t=zn();if("/"===e.charAt(0))return e;var n=e.split("?"),r=(0,u.default)(n,2),o=r[0],i=r[1],a=o.replace(/^\//,"").split("/"),s=t.split("/");s.pop();for(var c=0;c<a.length;c++){var l=a[c];".."===l?s.pop():"."!==l&&s.push(l)}return""===s[0]&&s.shift(),"/"+s.join("/")+(i?"?"+i:"")}function nr(e){var t=qn(tr(e));return!(Zn.indexOf(t)>-1)&&(Qn.indexOf(t)>-1||Yn.some((function(t){return function(e,t){return new RegExp(t).test(e)}(e,t)})))}function rr(e){var t=e.redirect,n=qn(t),r=qn(Gn);return zn()!==r&&n!==r}function or(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.api,n=e.redirect;if(n&&rr({redirect:n})){var o=function(e,t){return"/"!==e.charAt(0)&&(e="/"+e),t?e.indexOf("?")>-1?e+"&uniIdRedirectUrl=".concat(encodeURIComponent(t)):e+"?uniIdRedirectUrl=".concat(encodeURIComponent(t)):e}(Gn,n);er?"navigateTo"!==t&&"redirectTo"!==t||(t="switchTab"):"switchTab"===t&&(t="navigateTo");var i={navigateTo:r.navigateTo,redirectTo:r.redirectTo,switchTab:r.switchTab,reLaunch:r.reLaunch};setTimeout((function(){i[t]({url:o})}),0)}}function ir(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.url,n={abortLoginPageJump:!1,autoToLoginPage:!1},r=function(){var e,t=we(),n=t.token,r=t.tokenExpired;if(n){if(r<Date.now()){var o="uni-id-token-expired";e={errCode:o,errMsg:$n[o]}}}else{var i="uni-id-check-token-failed";e={errCode:i,errMsg:$n[i]}}return e}();if(nr(t)&&r){if(r.uniIdRedirectUrl=t,ce(se.NEED_LOGIN).length>0)return setTimeout((function(){de(se.NEED_LOGIN,r)}),0),n.abortLoginPageJump=!0,n;n.autoToLoginPage=!0}return n}function ar(){!function(){var e=Hn(),t=ir({url:e}),n=t.abortLoginPageJump,r=t.autoToLoginPage;n||r&&or({api:"redirectTo",redirect:e})}();for(var e=["navigateTo","redirectTo","reLaunch","switchTab"],t=function(t){var n=e[t];r.addInterceptor(n,{invoke:function(e){var t=ir({url:e.url}),r=t.abortLoginPageJump,o=t.autoToLoginPage;return r?e:o?(or({api:n,redirect:tr(e.url)}),!1):e}})},n=0;n<e.length;n++)t(n)}function sr(){this.onResponse((function(e){var t=e.type,n=e.content,r=!1;switch(t){case"cloudobject":r=function(e){if("object"!=(0,c.default)(e))return!1;var t=e||{},n=t.errCode;return n in $n}(n);break;case"clientdb":r=function(e){if("object"!=(0,c.default)(e))return!1;var t=e||{},n=t.errCode;return n in Rn}(n)}r&&function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=ce(se.NEED_LOGIN);ge().then((function(){var n=Hn();if(n&&rr({redirect:n}))return t.length>0?de(se.NEED_LOGIN,Object.assign({uniIdRedirectUrl:n},e)):void(Gn&&or({api:"navigateTo",redirect:n}))}))}(n)}))}function ur(e){!function(e){e.onResponse=function(e){le(se.RESPONSE,e)},e.offResponse=function(e){fe(se.RESPONSE,e)}}(e),function(e){e.onNeedLogin=function(e){le(se.NEED_LOGIN,e)},e.offNeedLogin=function(e){fe(se.NEED_LOGIN,e)},Wn&&(Q(fn).needLoginInit||(Q(fn).needLoginInit=!0,ge().then((function(){ar.call(e)})),Xn&&sr.call(e)))}(e),function(e){e.onRefreshToken=function(e){le(se.REFRESH_TOKEN,e)},e.offRefreshToken=function(e){fe(se.REFRESH_TOKEN,e)}}(e)}var cr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",lr=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function fr(){var e,t,n=we().token||"",r=n.split(".");if(!n||3!==r.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{e=JSON.parse((t=r[1],decodeURIComponent(Kn(t).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(n){throw new Error("获取当前用户信息出错，详细错误信息为："+n.message)}return e.tokenExpired=1e3*e.exp,delete e.exp,delete e.iat,e}Kn="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!lr.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,o="",i=0;i<e.length;)t=cr.indexOf(e.charAt(i++))<<18|cr.indexOf(e.charAt(i++))<<12|(n=cr.indexOf(e.charAt(i++)))<<6|(r=cr.indexOf(e.charAt(i++))),o+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o}:atob;var dr=O((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n="chooseAndUploadFile:ok",i="chooseAndUploadFile:fail";function a(e,t){return e.tempFiles.forEach((function(e,n){e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((function(e){return e.path}))),e}function s(e,t,r){var o=r.onChooseFile,i=r.onUploadProgress;return t.then((function(e){if(o){var t=o(e);if(void 0!==t)return Promise.resolve(t).then((function(t){return void 0===t?e:t}))}return e})).then((function(t){return!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,o=arguments.length>3?arguments[3]:void 0;(t=Object.assign({},t)).errMsg=n;var i=t.tempFiles,a=i.length,s=0;return new Promise((function(n){for(;s<r;)u();function u(){var r=s++;if(r>=a)!i.find((function(e){return!e.url&&!e.errMsg}))&&n(t);else{var c=i[r];e.uploadFile({provider:c.provider,filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,cloudPathAsRealPath:c.cloudPathAsRealPath,onUploadProgress:function(e){e.index=r,e.tempFile=c,e.tempFilePath=c.path,o&&o(e)}}).then((function(e){c.url=e.fileID,r<a&&u()})).catch((function(e){c.errMsg=e.errMsg||e.message,r<a&&u()}))}}}))}(e,t,5,i)}))}t.initChooseAndUploadFile=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"all"};return"image"===t.type?s(e,function(e){var t=e.count,n=e.sizeType,o=e.sourceType,s=void 0===o?["album","camera"]:o,u=e.extension;return new Promise((function(e,o){r.chooseImage({count:t,sizeType:n,sourceType:s,extension:u,success:function(t){e(a(t,"image"))},fail:function(e){o({errMsg:e.errMsg.replace("chooseImage:fail",i)})}})}))}(t),t):"video"===t.type?s(e,function(e){var t=e.camera,n=e.compressed,o=e.maxDuration,s=e.sourceType,u=void 0===s?["album","camera"]:s,c=e.extension;return new Promise((function(e,s){r.chooseVideo({camera:t,compressed:n,maxDuration:o,sourceType:u,extension:c,success:function(t){var n=t.tempFilePath,r=t.duration,o=t.size,i=t.height,s=t.width;e(a({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:o,type:t.tempFile&&t.tempFile.type||"",width:s,height:i,duration:r,fileType:"video",cloudPath:""}]},"video"))},fail:function(e){s({errMsg:e.errMsg.replace("chooseVideo:fail",i)})}})}))}(t),t):s(e,function(e){var t=e.count,n=e.extension;return new Promise((function(e,s){var u=r.chooseFile;if("undefined"!=typeof o&&"function"==typeof o.chooseMessageFile&&(u=o.chooseMessageFile),"function"!=typeof u)return s({errMsg:i+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});u({type:"all",count:t,extension:n,success:function(t){e(a(t))},fail:function(e){s({errMsg:e.errMsg.replace("chooseFile:fail",i)})}})}))}(t),t)}}})),hr=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(dr),pr={auto:"auto",onready:"onready",manual:"manual"};function gr(e){return{props:{localdata:{type:Array,default:function(){return[]}},options:{type:[Object,Array],default:function(){return{}}},spaceInfo:{type:Object,default:function(){return{}}},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:function(){return{mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}},created:function(){var e=this;this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((function(){var t=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((function(n){t.push(e[n])})),t}),(function(t,n){if(e.loadtime!==pr.manual){for(var r=!1,o=[],i=2;i<t.length;i++)t[i]!==n[i]&&(o.push(t[i]),r=!0);t[0]!==n[0]&&(e.mixinDatacomPage.current=e.pageCurrent),e.mixinDatacomPage.size=e.pageSize,e.onMixinDatacomPropsChange(r,o)}}))},methods:{onMixinDatacomPropsChange:function(e,t){},mixinDatacomEasyGet:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.getone,r=void 0!==n&&n,o=t.success,i=t.fail;this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((function(t){e.mixinDatacomLoading=!1;var n=t.result,i=n.data,a=n.count;e.getcount&&(e.mixinDatacomPage.count=a),e.mixinDatacomHasMore=i.length<e.pageSize;var s=r?i.length?i[0]:void 0:i;e.mixinDatacomResData=s,o&&o(s)})).catch((function(t){e.mixinDatacomLoading=!1,e.mixinDatacomErrorMessage=t,e.mixinDatacomError=t,i&&i(t)})))},mixinDatacomGet:function(){var t,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};r=r||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);var o=r.action||this.action;o&&(n=n.action(o));var i=r.collection||this.collection;n=Array.isArray(i)?(t=n).collection.apply(t,(0,l.default)(i)):n.collection(i);var a=r.where||this.where;a&&Object.keys(a).length&&(n=n.where(a));var s=r.field||this.field;s&&(n=n.field(s));var u=r.foreignKey||this.foreignKey;u&&(n=n.foreignKey(u));var c=r.groupby||this.groupby;c&&(n=n.groupBy(c));var f=r.groupField||this.groupField;f&&(n=n.groupField(f)),!0===(void 0!==r.distinct?r.distinct:this.distinct)&&(n=n.distinct());var d=r.orderby||this.orderby;d&&(n=n.orderBy(d));var h=void 0!==r.pageCurrent?r.pageCurrent:this.mixinDatacomPage.current,p=void 0!==r.pageSize?r.pageSize:this.mixinDatacomPage.size,g=void 0!==r.getcount?r.getcount:this.getcount,v=void 0!==r.gettree?r.gettree:this.gettree,m=void 0!==r.gettreepath?r.gettreepath:this.gettreepath,y={getCount:g},_={limitLevel:void 0!==r.limitlevel?r.limitlevel:this.limitlevel,startWith:void 0!==r.startwith?r.startwith:this.startwith};return v&&(y.getTree=_),m&&(y.getTreePath=_),n=n.skip(p*(h-1)).limit(p).get(y),n}}}}function vr(e){return Q(dn.replace("{spaceId}",e.config.spaceId))}function mr(){return yr.apply(this,arguments)}function yr(){return yr=(0,f.default)(a.default.mark((function e(){var t,n,o,i,s,u,c,l=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=l.length>0&&void 0!==l[0]?l[0]:{},n=t.openid,o=t.callLoginByWeixin,i=void 0!==o&&o,s=vr(this),"mp-weixin"===J){e.next=4;break}throw new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `".concat(J,"`"));case 4:if(!n||!i){e.next=6;break}throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");case 6:if(!n){e.next=8;break}return e.abrupt("return",(s.mpWeixinOpenid=n,{}));case 8:return e.next=10,new Promise((function(e,t){r.login({success:function(t){e(t.code)},fail:function(e){t(new Error(e.errMsg))}})}));case 10:return u=e.sent,c=this.importObject("uni-id-co",{customUI:!0}),e.next=14,c.secureNetworkHandshakeByWeixin({code:u,callLoginByWeixin:i});case 14:return s.mpWeixinCode=u,e.abrupt("return",{code:u});case 16:case"end":return e.stop()}}),e,this)}))),yr.apply(this,arguments)}function _r(e){return br.apply(this,arguments)}function br(){return br=(0,f.default)(a.default.mark((function e(t){var n;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=vr(this),e.abrupt("return",(n.initPromise||(n.initPromise=mr.call(this,t).then((function(e){return e})).catch((function(e){throw delete n.initPromise,e}))),n.initPromise));case 2:case"end":return e.stop()}}),e,this)}))),br.apply(this,arguments)}function wr(e){!function(e){Se=e}(e)}function kr(e){var t="mp-weixin"===J&&o.canIUse("getAppBaseInfo"),n={getAppBaseInfo:t?r.getAppBaseInfo:r.getSystemInfo,getPushClientId:r.getPushClientId};return function(r){return new Promise((function(o,i){t&&"getAppBaseInfo"===e?o(n[e]()):n[e](x(x({},r),{},{success:function(e){o(e)},fail:function(e){i(e)}}))}))}}var xr=function(e){(0,h.default)(n,e);var t=S(n);function n(){var e;return(0,m.default)(this,n),e=t.call(this),e._uniPushMessageCallback=e._receivePushMessage.bind((0,s.default)(e)),e._currentMessageId=-1,e._payloadQueue=[],e}return(0,y.default)(n,[{key:"init",value:function(){var e=this;return Promise.all([kr("getAppBaseInfo")(),kr("getPushClientId")()]).then((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=(0,u.default)(t,2),r=n[0];r=void 0===r?{}:r;var o=r.appId,i=n[1];i=void 0===i?{}:i;var a=i.cid;if(!o)throw new Error("Invalid appId, please check the manifest.json file");if(!a)throw new Error("Invalid push client id");e._appId=o,e._pushClientId=a,e._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),e.emit("open"),e._initMessageListener()}),(function(t){throw e.emit("error",t),e.close(),t}))}},{key:"open",value:function(){var e=(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.init());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"_isUniCloudSSE",value:function(e){if("receive"!==e.type)return!1;var t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}},{key:"_receivePushMessage",value:function(e){if(this._isUniCloudSSE(e)){var t=e&&e.data&&e.data.payload,n=t.action,r=t.messageId,o=t.message;this._payloadQueue.push({action:n,messageId:r,message:o}),this._consumMessage()}}},{key:"_consumMessage",value:function(){for(var e=this;;){var t=this._payloadQueue.find((function(t){return t.messageId===e._currentMessageId+1}));if(!t)break;this._currentMessageId++,this._parseMessagePayload(t)}}},{key:"_parseMessagePayload",value:function(e){var t=e.action,n=e.messageId,r=e.message;"end"===t?this._end({messageId:n,message:r}):"message"===t&&this._appendMessage({messageId:n,message:r})}},{key:"_appendMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("message",t)}},{key:"_end",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("end",t),this.close()}},{key:"_initMessageListener",value:function(){r.onPushMessage(this._uniPushMessageCallback)}},{key:"_destroy",value:function(){r.offPushMessage(this._uniPushMessageCallback)}},{key:"toJSON",value:function(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}},{key:"close",value:function(){this._destroy(),this.emit("close")}}]),n}(z);var Sr={tcb:Ut,tencent:Ut,aliyun:Ae,private:Wt,dcloud:Wt,alipay:rn},Or=new(function(){function e(){(0,m.default)(this,e)}return(0,y.default)(e,[{key:"init",value:function(e){var t={},n=Sr[e.provider];if(!n)throw new Error("未提供正确的provider参数");return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new H({createPromise:function(){var t=Promise.resolve();t=new Promise((function(e){setTimeout((function(){e()}),1)}));var n=e.auth();return t.then((function(){return n.getLoginState()})).then((function(e){return e?Promise.resolve():n.signInAnonymously()}))}}))}(t),wn(t),function(e){var t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),function(e){e.database=function(t){if(t&&Object.keys(t).length>0)return e.init(t).database();if(this._database)return this._database;var n=jn(Ln,{uniClient:e});return this._database=n,n},e.databaseForJQL=function(t){if(t&&Object.keys(t).length>0)return e.init(t).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;var n=jn(Ln,{uniClient:e,isJQL:!0});return this._databaseForJQL=n,n}}(t),function(e){e.getCurrentUserInfo=fr,e.chooseAndUploadFile=hr.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return gr(e)}}),e.SSEChannel=xr,e.initSecureNetworkByWeixin=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.openid,r=t.callLoginByWeixin,o=void 0!==r&&r;return _r.call(e,{openid:n,callLoginByWeixin:o})}}(e),e.setCustomClientInfo=wr,e.importObject=function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==(0,c.default)(t.secretMethods)&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},n);var o=n,i=o.customUI,s=o.loadingOptions,u=o.errorOptions,l=o.parseSystemError,d=!i;return new Proxy({},{get:function(o,i){switch(i){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fn,n=e.interceptorName,r=e.getCallbackArgs;return(0,f.default)(a.default.mark((function e(){var o,i,s,u,c,l,f=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(o=f.length,i=new Array(o),s=0;s<o;s++)i[s]=f[s];return u=r?r({params:i}):{},e.prev=2,e.next=5,re(oe(n,"invoke"),x({},u));case 5:return e.next=7,t.apply(void 0,i);case 7:return c=e.sent,e.next=10,re(oe(n,"success"),x(x({},u),{},{result:c}));case 10:return e.abrupt("return",c);case 13:return e.prev=13,e.t0=e["catch"](2),l=e.t0,e.next=18,re(oe(n,"fail"),x(x({},u),{},{error:l}));case 18:throw l;case 19:return e.prev=19,e.next=22,re(oe(n,"complete"),x(x({},u),{},l?{error:l}:{result:c}));case 22:return e.finish(19);case 23:case"end":return e.stop()}}),e,null,[[2,13,19,23]])})))}({fn:function(){var o=(0,f.default)(a.default.mark((function o(){var p,g,v,m,y,_,b,w,k,S,O,T,P,C,A,I=arguments;return a.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:for(d&&r.showLoading({title:s.title,mask:s.mask}),g=I.length,v=new Array(g),m=0;m<g;m++)v[m]=I[m];return y={name:t,type:j.OBJECT,data:{method:i,params:v}},"object"==(0,c.default)(n.secretMethods)&&function(e,t){var n=t.data.method,r=e.secretMethods||{},o=r[n]||r["*"];o&&(t.secretType=o)}(n,y),_=!1,o.prev=5,o.next=8,e.callFunction(y);case 8:p=o.sent,o.next=14;break;case 11:o.prev=11,o.t0=o["catch"](5),_=!0,p={result:new me(o.t0)};case 14:if(b=p.result||{},w=b.errSubject,k=b.errCode,S=b.errMsg,O=b.newToken,d&&r.hideLoading(),O&&O.token&&O.tokenExpired&&(ke(O),de(se.REFRESH_TOKEN,x({},O))),!k){o.next=39;break}if(T=S,!_||!l){o.next=24;break}return o.next=20,l({objectName:t,methodName:i,params:v,errSubject:w,errCode:k,errMsg:S});case 20:if(o.t1=o.sent.errMsg,o.t1){o.next=23;break}o.t1=S;case 23:T=o.t1;case 24:if(!d){o.next=37;break}if("toast"!==u.type){o.next=29;break}r.showToast({title:T,icon:"none"}),o.next=37;break;case 29:if("modal"===u.type){o.next=31;break}throw new Error("Invalid errorOptions.type: ".concat(u.type));case 31:return o.next=33,(0,f.default)(a.default.mark((function e(){var t,n,o,i,s,u,c=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.title,o=t.content,i=t.showCancel,s=t.cancelText,u=t.confirmText,e.abrupt("return",new Promise((function(e,t){r.showModal({title:n,content:o,showCancel:i,cancelText:s,confirmText:u,success:function(t){e(t)},fail:function(){e({confirm:!1,cancel:!0})}})})));case 2:case"end":return e.stop()}}),e)})))({title:"提示",content:T,showCancel:u.retry,cancelText:"取消",confirmText:u.retry?"重试":"确定"});case 33:if(P=o.sent,C=P.confirm,!u.retry||!C){o.next=37;break}return o.abrupt("return",h.apply(void 0,v));case 37:throw A=new me({subject:w,code:k,message:S,requestId:p.requestId}),A.detail=p.result,de(se.RESPONSE,{type:ue.CLOUD_OBJECT,content:A}),A;case 39:return o.abrupt("return",(de(se.RESPONSE,{type:ue.CLOUD_OBJECT,content:p.result}),p.result));case 40:case"end":return o.stop()}}),o,null,[[5,11]])})));function h(){return o.apply(this,arguments)}return h}(),interceptorName:"callObject",getCallbackArgs:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.params;return{objectName:t,methodName:i,params:n}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((function(e){if(t[e]){var n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){var r=this,o=!1;if("callFunction"===t){var i=n&&n.type||j.DEFAULT;o=i!==j.DEFAULT}var a="callFunction"===t&&!o,s=this._initPromiseHub.exec();n=n||{};var u=ve(n),c=u.success,l=u.fail,f=u.complete,d=s.then((function(){return o?Promise.resolve():re(oe(t,"invoke"),n)})).then((function(){return e.call(r,n)})).then((function(e){return o?Promise.resolve(e):re(oe(t,"success"),e).then((function(){return re(oe(t,"complete"),e)})).then((function(){return a&&de(se.RESPONSE,{type:ue.CLOUD_FUNCTION,content:e}),Promise.resolve(e)}))}),(function(e){return o?Promise.reject(e):re(oe(t,"fail"),e).then((function(){return re(oe(t,"complete"),e)})).then((function(){return de(se.RESPONSE,{type:ue.CLOUD_FUNCTION,content:e}),Promise.reject(e)}))}));if(!(c||l||f))return d;d.then((function(e){c&&c(e),f&&f(e),a&&de(se.RESPONSE,{type:ue.CLOUD_FUNCTION,content:e})}),(function(e){l&&l(e),f&&f(e),a&&de(se.RESPONSE,{type:ue.CLOUD_FUNCTION,content:e})}))}}(t[e],e).bind(t)}})),t.init=this.init,t}}]),e}());t.uniCloud=Or,function(){var e=G,n={};if(e&&1===e.length)n=e[0],t.uniCloud=Or=Or.init(n),Or._isDefault=!0;else{var o,i=["database","getCurrentUserInfo","importObject"];o=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",[].concat(["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile"],i).forEach((function(e){Or[e]=function(){if(console.error(o),-1===i.indexOf(e))return Promise.reject(new me({code:"SYS_ERR",message:o}));console.error(o)}}))}if(Object.assign(Or,{get mixinDatacom(){return gr(Or)}}),ur(Or),Or.addInterceptor=te,Or.removeInterceptor=ne,Or.interceptObject=ie,"app"===J&&(r.__uniCloud=Or),"app"===J||"web"===J){var a=function(){return Y||(Y=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),Y)}();a.uniCloud=Or,a.UniCloudError=me}}();var Tr=Or;t.default=Tr}).call(this,n("0ee4"),n("df3c")["default"],n("3223")["default"])},"86a7":function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.typeFilter=t.type=t.setDataValue=t.realName=t.rawData=t.objSet=t.objGet=t.name2arr=t.isRequiredField=t.isRealName=t.isNumber=t.isEqual=t.isBoolean=t.getValue=t.getDataValueType=t.getDataValue=t.deepCopy=void 0;var o=r(n("3b2d"));t.deepCopy=function(e){return JSON.parse(JSON.stringify(e))};var i=function(e){return"int"===e||"double"===e||"number"===e||"timestamp"===e};t.typeFilter=i;t.getValue=function(e,t,n){var r=n.find((function(e){return e.format&&i(e.format)})),o=n.find((function(e){return e.format&&"boolean"===e.format||"bool"===e.format}));return r&&(t=t||0===t?f(Number(t))?Number(t):t:null),o&&(t=!!d(t)&&t),t};t.setDataValue=function(e,t,n){return t[e]=n,n||""};var a=function(e,t){return l(t,e)};t.getDataValue=a;t.getDataValueType=function(e,t){var n=a(e,t);return{type:h(n),value:n}};t.realName=function(e){var t=c(e);if("object"===(0,o.default)(t)&&Array.isArray(t)&&t.length>1){var n=t.reduce((function(e,t){return e+"#".concat(t)}),"_formdata_");return n}return t[0]||e};t.isRealName=function(e){return/^_formdata_#*/.test(e)};t.rawData=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=JSON.parse(JSON.stringify(e)),n={};for(var r in t){var o=s(r);u(n,o,t[r])}return n};var s=function(e){var t=e.replace("_formdata_#","");return t=t.split("#").map((function(e){return f(e)?Number(e):e})),t};t.name2arr=s;var u=function(e,t,n){return"object"!==(0,o.default)(e)||c(t).reduce((function(e,t,r,o){return r===o.length-1?(e[t]=n,null):(t in e||(e[t]=/^[0-9]{1,}$/.test(o[r+1])?[]:{}),e[t])}),e),e};function c(e){return Array.isArray(e)?e:e.replace(/\[/g,".").replace(/\]/g,"").split(".")}t.objSet=u;var l=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"undefined",r=c(t),o=r.reduce((function(e,t){return(e||{})[t]}),e);return o&&void 0===o?n:o};t.objGet=l;var f=function(e){return!isNaN(Number(e))};t.isNumber=f;var d=function(e){return"boolean"===typeof e};t.isBoolean=d;t.isRequiredField=function(e){for(var t=!1,n=0;n<e.length;n++){var r=e[n];if(r.required){t=!0;break}}return t};var h=function(e){var t={};return"Boolean Number String Function Array Date RegExp Object Error".split(" ").map((function(e,n){t["[object "+e+"]"]=e.toLowerCase()})),null==e?e+"":"object"===(0,o.default)(e)||"function"===typeof e?t[Object.prototype.toString.call(e)]||"object":(0,o.default)(e)};t.type=h;t.isEqual=function(e,t){if(e===t)return 0!==e||1/e===1/t;if(null==e||null==t)return e===t;var n=toString.call(e),r=toString.call(t);if(n!==r)return!1;switch(n){case"[object RegExp]":case"[object String]":return""+e===""+t;case"[object Number]":return+e!==+e?+t!==+t:0===+e?1/+e===1/t:+e===+t;case"[object Date]":case"[object Boolean]":return+e===+t}if("[object Object]"==n){var o=Object.getOwnPropertyNames(e),i=Object.getOwnPropertyNames(t);if(o.length!=i.length)return!1;for(var a=0;a<o.length;a++){var s=o[a];if(e[s]!==t[s])return!1}return!0}return"[object Array]"==n?e.toString()==t.toString():void 0}},"8f3c":function(e){e.exports=JSON.parse('{"uni-search-bar.cancel":"cancel","uni-search-bar.placeholder":"Search enter content"}')},"8f59":function(e,t,n){"use strict";(function(t){var n="undefined"!==typeof window?window:"undefined"!==typeof t?t:{},r=n.__VUE_DEVTOOLS_GLOBAL_HOOK__;function o(e,t){if(void 0===t&&(t=[]),null===e||"object"!==typeof e)return e;var n=function(e,t){return e.filter(t)[0]}(t,(function(t){return t.original===e}));if(n)return n.copy;var r=Array.isArray(e)?[]:{};return t.push({original:e,copy:r}),Object.keys(e).forEach((function(n){r[n]=o(e[n],t)})),r}function i(e,t){Object.keys(e).forEach((function(n){return t(e[n],n)}))}function a(e){return null!==e&&"object"===typeof e}var s=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var n=e.state;this.state=("function"===typeof n?n():n)||{}},u={namespaced:{configurable:!0}};u.namespaced.get=function(){return!!this._rawModule.namespaced},s.prototype.addChild=function(e,t){this._children[e]=t},s.prototype.removeChild=function(e){delete this._children[e]},s.prototype.getChild=function(e){return this._children[e]},s.prototype.hasChild=function(e){return e in this._children},s.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},s.prototype.forEachChild=function(e){i(this._children,e)},s.prototype.forEachGetter=function(e){this._rawModule.getters&&i(this._rawModule.getters,e)},s.prototype.forEachAction=function(e){this._rawModule.actions&&i(this._rawModule.actions,e)},s.prototype.forEachMutation=function(e){this._rawModule.mutations&&i(this._rawModule.mutations,e)},Object.defineProperties(s.prototype,u);var c=function(e){this.register([],e,!1)};c.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},c.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,n){return t=t.getChild(n),e+(t.namespaced?n+"/":"")}),"")},c.prototype.update=function(e){(function e(t,n,r){0;if(n.update(r),r.modules)for(var o in r.modules){if(!n.getChild(o))return void 0;e(t.concat(o),n.getChild(o),r.modules[o])}})([],this.root,e)},c.prototype.register=function(e,t,n){var r=this;void 0===n&&(n=!0);var o=new s(t,n);if(0===e.length)this.root=o;else{var a=this.get(e.slice(0,-1));a.addChild(e[e.length-1],o)}t.modules&&i(t.modules,(function(t,o){r.register(e.concat(o),t,n)}))},c.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1],r=t.getChild(n);r&&r.runtime&&t.removeChild(n)},c.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1];return!!t&&t.hasChild(n)};var l;var f=function(e){var t=this;void 0===e&&(e={}),!l&&"undefined"!==typeof window&&window.Vue&&_(window.Vue);var n=e.plugins;void 0===n&&(n=[]);var o=e.strict;void 0===o&&(o=!1),this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new c(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._watcherVM=new l,this._makeLocalGettersCache=Object.create(null);var i=this,a=this.dispatch,s=this.commit;this.dispatch=function(e,t){return a.call(i,e,t)},this.commit=function(e,t,n){return s.call(i,e,t,n)},this.strict=o;var u=this._modules.root.state;v(this,u,[],this._modules.root),g(this,u),n.forEach((function(e){return e(t)}));var f=void 0!==e.devtools?e.devtools:l.config.devtools;f&&function(e){r&&(e._devtoolHook=r,r.emit("vuex:init",e),r.on("vuex:travel-to-state",(function(t){e.replaceState(t)})),e.subscribe((function(e,t){r.emit("vuex:mutation",e,t)}),{prepend:!0}),e.subscribeAction((function(e,t){r.emit("vuex:action",e,t)}),{prepend:!0}))}(this)},d={state:{configurable:!0}};function h(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var n=t.indexOf(e);n>-1&&t.splice(n,1)}}function p(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;v(e,n,[],e._modules.root,!0),g(e,n,t)}function g(e,t,n){var r=e._vm;e.getters={},e._makeLocalGettersCache=Object.create(null);var o=e._wrappedGetters,a={};i(o,(function(t,n){a[n]=function(e,t){return function(){return e(t)}}(t,e),Object.defineProperty(e.getters,n,{get:function(){return e._vm[n]},enumerable:!0})}));var s=l.config.silent;l.config.silent=!0,e._vm=new l({data:{$$state:t},computed:a}),l.config.silent=s,e.strict&&function(e){e._vm.$watch((function(){return this._data.$$state}),(function(){0}),{deep:!0,sync:!0})}(e),r&&(n&&e._withCommit((function(){r._data.$$state=null})),l.nextTick((function(){return r.$destroy()})))}function v(e,t,n,r,o){var i=!n.length,a=e._modules.getNamespace(n);if(r.namespaced&&(e._modulesNamespaceMap[a],e._modulesNamespaceMap[a]=r),!i&&!o){var s=m(t,n.slice(0,-1)),u=n[n.length-1];e._withCommit((function(){l.set(s,u,r.state)}))}var c=r.context=function(e,t,n){var r=""===t,o={dispatch:r?e.dispatch:function(n,r,o){var i=y(n,r,o),a=i.payload,s=i.options,u=i.type;return s&&s.root||(u=t+u),e.dispatch(u,a)},commit:r?e.commit:function(n,r,o){var i=y(n,r,o),a=i.payload,s=i.options,u=i.type;s&&s.root||(u=t+u),e.commit(u,a,s)}};return Object.defineProperties(o,{getters:{get:r?function(){return e.getters}:function(){return function(e,t){if(!e._makeLocalGettersCache[t]){var n={},r=t.length;Object.keys(e.getters).forEach((function(o){if(o.slice(0,r)===t){var i=o.slice(r);Object.defineProperty(n,i,{get:function(){return e.getters[o]},enumerable:!0})}})),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}(e,t)}},state:{get:function(){return m(e.state,n)}}}),o}(e,a,n);r.forEachMutation((function(t,n){var r=a+n;(function(e,t,n,r){var o=e._mutations[t]||(e._mutations[t]=[]);o.push((function(t){n.call(e,r.state,t)}))})(e,r,t,c)})),r.forEachAction((function(t,n){var r=t.root?n:a+n,o=t.handler||t;(function(e,t,n,r){var o=e._actions[t]||(e._actions[t]=[]);o.push((function(t){var o=n.call(e,{dispatch:r.dispatch,commit:r.commit,getters:r.getters,state:r.state,rootGetters:e.getters,rootState:e.state},t);return function(e){return e&&"function"===typeof e.then}(o)||(o=Promise.resolve(o)),e._devtoolHook?o.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):o}))})(e,r,o,c)})),r.forEachGetter((function(t,n){var r=a+n;(function(e,t,n,r){if(e._wrappedGetters[t])return void 0;e._wrappedGetters[t]=function(e){return n(r.state,r.getters,e.state,e.getters)}})(e,r,t,c)})),r.forEachChild((function(r,i){v(e,t,n.concat(i),r,o)}))}function m(e,t){return t.reduce((function(e,t){return e[t]}),e)}function y(e,t,n){return a(e)&&e.type&&(n=t,t=e,e=e.type),{type:e,payload:t,options:n}}function _(e){l&&e===l||(l=e,
/*!
 * vuex v3.6.2
 * (c) 2021 Evan You
 * @license MIT
 */
function(e){var t=Number(e.version.split(".")[0]);if(t>=2)e.mixin({beforeCreate:r});else{var n=e.prototype._init;e.prototype._init=function(e){void 0===e&&(e={}),e.init=e.init?[r].concat(e.init):r,n.call(this,e)}}function r(){var e=this.$options;e.store?this.$store="function"===typeof e.store?e.store():e.store:e.parent&&e.parent.$store&&(this.$store=e.parent.$store)}}(l))}d.state.get=function(){return this._vm._data.$$state},d.state.set=function(e){0},f.prototype.commit=function(e,t,n){var r=this,o=y(e,t,n),i=o.type,a=o.payload,s=(o.options,{type:i,payload:a}),u=this._mutations[i];u&&(this._withCommit((function(){u.forEach((function(e){e(a)}))})),this._subscribers.slice().forEach((function(e){return e(s,r.state)})))},f.prototype.dispatch=function(e,t){var n=this,r=y(e,t),o=r.type,i=r.payload,a={type:o,payload:i},s=this._actions[o];if(s){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(a,n.state)}))}catch(c){0}var u=s.length>1?Promise.all(s.map((function(e){return e(i)}))):s[0](i);return new Promise((function(e,t){u.then((function(t){try{n._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(a,n.state)}))}catch(c){0}e(t)}),(function(e){try{n._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(a,n.state,e)}))}catch(c){0}t(e)}))}))}},f.prototype.subscribe=function(e,t){return h(e,this._subscribers,t)},f.prototype.subscribeAction=function(e,t){var n="function"===typeof e?{before:e}:e;return h(n,this._actionSubscribers,t)},f.prototype.watch=function(e,t,n){var r=this;return this._watcherVM.$watch((function(){return e(r.state,r.getters)}),t,n)},f.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._vm._data.$$state=e}))},f.prototype.registerModule=function(e,t,n){void 0===n&&(n={}),"string"===typeof e&&(e=[e]),this._modules.register(e,t),v(this,this.state,e,this._modules.get(e),n.preserveState),g(this,this.state)},f.prototype.unregisterModule=function(e){var t=this;"string"===typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){var n=m(t.state,e.slice(0,-1));l.delete(n,e[e.length-1])})),p(this)},f.prototype.hasModule=function(e){return"string"===typeof e&&(e=[e]),this._modules.isRegistered(e)},f.prototype[[104,111,116,85,112,100,97,116,101].map((function(e){return String.fromCharCode(e)})).join("")]=function(e){this._modules.update(e),p(this,!0)},f.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(f.prototype,d);var b=O((function(e,t){var n={};return S(t).forEach((function(t){var r=t.key,o=t.val;n[r]=function(){var t=this.$store.state,n=this.$store.getters;if(e){var r=T(this.$store,"mapState",e);if(!r)return;t=r.context.state,n=r.context.getters}return"function"===typeof o?o.call(this,t,n):t[o]},n[r].vuex=!0})),n})),w=O((function(e,t){var n={};return S(t).forEach((function(t){var r=t.key,o=t.val;n[r]=function(){var t=[],n=arguments.length;while(n--)t[n]=arguments[n];var r=this.$store.commit;if(e){var i=T(this.$store,"mapMutations",e);if(!i)return;r=i.context.commit}return"function"===typeof o?o.apply(this,[r].concat(t)):r.apply(this.$store,[o].concat(t))}})),n})),k=O((function(e,t){var n={};return S(t).forEach((function(t){var r=t.key,o=t.val;o=e+o,n[r]=function(){if(!e||T(this.$store,"mapGetters",e))return this.$store.getters[o]},n[r].vuex=!0})),n})),x=O((function(e,t){var n={};return S(t).forEach((function(t){var r=t.key,o=t.val;n[r]=function(){var t=[],n=arguments.length;while(n--)t[n]=arguments[n];var r=this.$store.dispatch;if(e){var i=T(this.$store,"mapActions",e);if(!i)return;r=i.context.dispatch}return"function"===typeof o?o.apply(this,[r].concat(t)):r.apply(this.$store,[o].concat(t))}})),n}));function S(e){return function(e){return Array.isArray(e)||a(e)}(e)?Array.isArray(e)?e.map((function(e){return{key:e,val:e}})):Object.keys(e).map((function(t){return{key:t,val:e[t]}})):[]}function O(e){return function(t,n){return"string"!==typeof t?(n=t,t=""):"/"!==t.charAt(t.length-1)&&(t+="/"),e(t,n)}}function T(e,t,n){var r=e._modulesNamespaceMap[n];return r}function P(e,t,n){var r=n?e.groupCollapsed:e.group;try{r.call(e,t)}catch(o){e.log(t)}}function C(e){try{e.groupEnd()}catch(t){e.log("—— log end ——")}}function A(){var e=new Date;return" @ "+I(e.getHours(),2)+":"+I(e.getMinutes(),2)+":"+I(e.getSeconds(),2)+"."+I(e.getMilliseconds(),3)}function I(e,t){return function(e,t){return new Array(t+1).join(e)}("0",t-e.toString().length)+e}var E={Store:f,install:_,version:"3.6.2",mapState:b,mapMutations:w,mapGetters:k,mapActions:x,createNamespacedHelpers:function(e){return{mapState:b.bind(null,e),mapGetters:k.bind(null,e),mapMutations:w.bind(null,e),mapActions:x.bind(null,e)}},createLogger:function(e){void 0===e&&(e={});var t=e.collapsed;void 0===t&&(t=!0);var n=e.filter;void 0===n&&(n=function(e,t,n){return!0});var r=e.transformer;void 0===r&&(r=function(e){return e});var i=e.mutationTransformer;void 0===i&&(i=function(e){return e});var a=e.actionFilter;void 0===a&&(a=function(e,t){return!0});var s=e.actionTransformer;void 0===s&&(s=function(e){return e});var u=e.logMutations;void 0===u&&(u=!0);var c=e.logActions;void 0===c&&(c=!0);var l=e.logger;return void 0===l&&(l=console),function(e){var f=o(e.state);"undefined"!==typeof l&&(u&&e.subscribe((function(e,a){var s=o(a);if(n(e,f,s)){var u=A(),c=i(e),d="mutation "+e.type+u;P(l,d,t),l.log("%c prev state","color: #9E9E9E; font-weight: bold",r(f)),l.log("%c mutation","color: #03A9F4; font-weight: bold",c),l.log("%c next state","color: #4CAF50; font-weight: bold",r(s)),C(l)}f=s})),c&&e.subscribeAction((function(e,n){if(a(e,n)){var r=A(),o=s(e),i="action "+e.type+r;P(l,i,t),l.log("%c action","color: #03A9F4; font-weight: bold",o),C(l)}})))}}};e.exports=E}).call(this,n("0ee4"))},"8f87":function(e){e.exports=JSON.parse('{"uni-popup.cancel":"取消","uni-popup.ok":"確定","uni-popup.placeholder":"請輸入","uni-popup.title":"提示","uni-popup.shareTitle":"分享到"}')},"8ffa":function(e,t,n){var r=n("7647");e.exports=function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports["default"]=e.exports},9008:function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports["default"]=e.exports},"931d":function(e,t,n){var r=n("7647"),o=n("011a");e.exports=function(e,t,n){if(o())return Reflect.construct.apply(null,arguments);var i=[null];i.push.apply(i,t);var a=new(e.bind.apply(e,i));return n&&r(a,n.prototype),a},e.exports.__esModule=!0,e.exports["default"]=e.exports},"93dd":function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("30eb")),i=o.default.passwordStrength,a={super:/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/])[0-9a-zA-Z~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/]{8,16}$/,strong:/^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/])[0-9a-zA-Z~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/]{8,16}$/,medium:/^(?![0-9]+$)(?![a-zA-Z]+$)(?![~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/]+$)[0-9a-zA-Z~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/]{8,16}$/,weak:/^(?=.*[0-9])(?=.*[a-zA-Z])[0-9a-zA-Z~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/]{6,16}$/},s={normal:{noPwd:"请输入密码",noRePwd:"再次输入密码",rePwdErr:"两次输入密码不一致"},passwordStrengthError:{super:"密码必须包含大小写字母、数字和特殊符号，密码长度必须在8-16位之间",strong:"密码必须包含字母、数字和特殊符号，密码长度必须在8-16位之间",medium:"密码必须为字母、数字和特殊符号任意两种的组合，密码长度必须在8-16位之间",weak:"密码必须包含字母，密码长度必须在6-16位之间"}};function u(e){return!(i&&a[i]&&!new RegExp(a[i]).test(e))||s.passwordStrengthError[i]}var c={ERROR:s,validPwd:u,getPwdRules:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"password",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"password2",n={};return n[e]={rules:[{required:!0,errorMessage:s.normal.noPwd},{validateFunction:function(e,t,n,r){var o=u(t);return!0!==o&&r(o),!0}}]},t&&(n[t]={rules:[{required:!0,errorMessage:s.normal.noRePwd},{validateFunction:function(t,n,r,o){return n!=r[e]&&o(s.normal.rePwdErr),!0}}]}),n}};t.default=c},9469:function(e){e.exports=JSON.parse('{"uniCloud.component.add.success":"新增成功","uniCloud.component.update.success":"修改成功","uniCloud.component.update.showModal.title":"提示","uniCloud.component.update.showModal.content":"是否更新该数据","uniCloud.component.remove.showModal.title":"提示","uniCloud.component.remove.showModal.content":"是否删除该数据"}')},"9ef6":function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("4aa8")),i=r(n("9f93")),a=r(n("8f87")),s={en:o.default,"zh-Hans":i.default,"zh-Hant":a.default};t.default=s},"9f93":function(e){e.exports=JSON.parse('{"uni-popup.cancel":"取消","uni-popup.ok":"确定","uni-popup.placeholder":"请输入","uni-popup.title":"提示","uni-popup.shareTitle":"分享到"}')},"9fc1":function(e,t,n){var r=n("3b2d")["default"];function o(){"use strict";
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e.exports=o=function(){return n},e.exports.__esModule=!0,e.exports["default"]=e.exports;var t,n={},i=Object.prototype,a=i.hasOwnProperty,s=Object.defineProperty||function(e,t,n){e[t]=n.value},u="function"==typeof Symbol?Symbol:{},c=u.iterator||"@@iterator",l=u.asyncIterator||"@@asyncIterator",f=u.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(t){d=function(e,t,n){return e[t]=n}}function h(e,t,n,r){var o=t&&t.prototype instanceof _?t:_,i=Object.create(o.prototype),a=new D(r||[]);return s(i,"_invoke",{value:C(e,n,a)}),i}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=h;var g="suspendedStart",v="executing",m="completed",y={};function _(){}function b(){}function w(){}var k={};d(k,c,(function(){return this}));var x=Object.getPrototypeOf,S=x&&x(x(j([])));S&&S!==i&&a.call(S,c)&&(k=S);var O=w.prototype=_.prototype=Object.create(k);function T(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function n(o,i,s,u){var c=p(e[o],e,i);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==r(f)&&a.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,s,u)}),(function(e){n("throw",e,s,u)})):t.resolve(f).then((function(e){l.value=e,s(l)}),(function(e){return n("throw",e,s,u)}))}u(c.arg)}var o;s(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(i,i):i()}})}function C(e,n,r){var o=g;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===m){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var s=r.delegate;if(s){var u=A(s,r);if(u){if(u===y)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===g)throw o=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=v;var c=p(e,n,r);if("normal"===c.type){if(o=r.done?m:"suspendedYield",c.arg===y)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=m,r.method="throw",r.arg=c.arg)}}}function A(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator["return"]&&(n.method="return",n.arg=t,A(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=p(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function I(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function D(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(I,this),this.reset(!0)}function j(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(a.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(r(e)+" is not iterable")}return b.prototype=w,s(O,"constructor",{value:w,configurable:!0}),s(w,"constructor",{value:b,configurable:!0}),b.displayName=d(w,f,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,d(e,f,"GeneratorFunction")),e.prototype=Object.create(O),e},n.awrap=function(e){return{__await:e}},T(P.prototype),d(P.prototype,l,(function(){return this})),n.AsyncIterator=P,n.async=function(e,t,r,o,i){void 0===i&&(i=Promise);var a=new P(h(e,t,r,o),i);return n.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},T(O),d(O,f,"Generator"),d(O,c,(function(){return this})),d(O,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},n.values=j,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,o){return s.type="throw",s.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var u=a.call(i,"catchLoc"),c=a.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:j(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),y}},n}e.exports=o,e.exports.__esModule=!0,e.exports["default"]=e.exports},a08b:function(e){e.exports=JSON.parse('{"uni-datetime-picker.selectDate":"select date","uni-datetime-picker.selectTime":"select time","uni-datetime-picker.selectDateTime":"select date and time","uni-datetime-picker.startDate":"start date","uni-datetime-picker.endDate":"end date","uni-datetime-picker.startTime":"start time","uni-datetime-picker.endTime":"end time","uni-datetime-picker.ok":"ok","uni-datetime-picker.clear":"clear","uni-datetime-picker.cancel":"cancel","uni-datetime-picker.year":"-","uni-datetime-picker.month":"","uni-calender.MON":"MON","uni-calender.TUE":"TUE","uni-calender.WED":"WED","uni-calender.THU":"THU","uni-calender.FRI":"FRI","uni-calender.SAT":"SAT","uni-calender.SUN":"SUN","uni-calender.confirm":"confirm"}')},a1f2:function(e){e.exports=JSON.parse('{"uni-load-more.contentdown":"上拉顯示更多","uni-load-more.contentrefresh":"正在加載...","uni-load-more.contentnomore":"沒有更多數據了"}')},a708:function(e,t,n){var r=n("6454");e.exports=function(e){if(Array.isArray(e))return r(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},a8ce:function(e){e.exports=JSON.parse('{"uniCloud.component.add.success":"Success","uniCloud.component.update.success":"Success","uniCloud.component.update.showModal.title":"Tips","uniCloud.component.update.showModal.content":"是否更新该数据","uniCloud.component.remove.showModal.title":"Tips","uniCloud.component.remove.showModal.content":"是否删除该数据"}')},ab20:function(e){e.exports=JSON.parse('{"uni-search-bar.cancel":"取消","uni-search-bar.placeholder":"请输入搜索内容"}')},af34:function(e,t,n){var r=n("a708"),o=n("b893"),i=n("6382"),a=n("9008");e.exports=function(e){return r(e)||o(e)||i(e)||a()},e.exports.__esModule=!0,e.exports["default"]=e.exports},affd:function(e){e.exports=JSON.parse('{"uniCloud.component.add.success":"新增成功","uniCloud.component.update.success":"修改成功","uniCloud.component.update.showModal.title":"提示","uniCloud.component.update.showModal.content":"是否更新该数据","uniCloud.component.remove.showModal.title":"提示","uniCloud.component.remove.showModal.content":"是否删除该数据"}')},b0d3:function(e){e.exports=JSON.parse('{"uni-datetime-picker.selectDate":"选择日期","uni-datetime-picker.selectTime":"选择时间","uni-datetime-picker.selectDateTime":"选择日期时间","uni-datetime-picker.startDate":"开始日期","uni-datetime-picker.endDate":"结束日期","uni-datetime-picker.startTime":"开始时间","uni-datetime-picker.endTime":"结束时间","uni-datetime-picker.ok":"确定","uni-datetime-picker.clear":"清除","uni-datetime-picker.cancel":"取消","uni-datetime-picker.year":"年","uni-datetime-picker.month":"月","uni-calender.SUN":"日","uni-calender.MON":"一","uni-calender.TUE":"二","uni-calender.WED":"三","uni-calender.THU":"四","uni-calender.FRI":"五","uni-calender.SAT":"六","uni-calender.confirm":"确认"}')},b4d2:function(e,t){function n(t){return e.exports=n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},b893:function(e,t){e.exports=function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},b96b:function(e,t,n){"use strict";(function(e,r){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n("3b2d")),a=o(n("7ca3"));function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e){if(!e)return e;if(e instanceof Date){var t=e.getFullYear(),n=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0");return"".concat(t,"/").concat(n,"/").concat(r)}return e.replace(/-/g,"/")}function l(t){var n=t.name,r=t.action,o=t.data,i=void 0===o?{}:o;if("patrol-task-today"===n&&"getTodayTasks"===r){var a=i.userId,s=i.date,l=i.includeAcrossDayTasks,f=void 0!==l&&l,d={userId:a,date:s||c(new Date)};if(f){d.includeAcrossDayTasks=!0;var h=new Date;if(s)try{h.setTime(new Date(s.replace?s.replace(/-/g,"/"):s).getTime())}catch(p){console.error("解析日期出错:",p)}h.setDate(h.getDate()-1),d.prevDate=c(h)}return _(d)}return e.callFunction({name:n,data:u({action:r},i)}).then((function(e){return e.result})).catch((function(e){return{code:-1,message:"调用云函数 ".concat(n,".").concat(r," 出错"),error:e}}))}function f(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.callFunction({name:"patrol-task",data:u({action:t},n)}).then((function(e){return e.result})).catch((function(e){return{code:-1,message:"调用任务云函数出错",error:e}}))}function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.callFunction({name:"patrol-point",data:u({action:t},n)}).then((function(e){return e.result})).catch((function(e){return{code:-1,message:"调用点位云函数出错",error:e}}))}function h(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.callFunction({name:"patrol-route",data:{action:t,params:n}}).then((function(e){return e.result})).catch((function(e){return{code:-1,message:"调用路线云函数出错",error:e}}))}function p(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.callFunction({name:"patrol-shift",data:u({action:t},n)}).then((function(e){return e.result})).catch((function(e){return{code:-1,message:"调用班次云函数出错",error:e}}))}function g(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.callFunction({name:"patrol-config",data:u({action:t},n)}).then((function(e){return e.result})).catch((function(e){return{code:-1,message:"调用配置云函数出错",error:e}}))}function v(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=u({},n);return"getTaskRecords"===t&&n.taskId&&(r={task_id:n.taskId},n.params&&(r=u(u({},r),n.params))),"getRecordDetail"===t&&n.id&&(r={record_id:n.id}),e.callFunction({name:"patrol-record",data:u({action:t},r)}).then((function(e){return e.result})).catch((function(e){return{code:-1,message:"调用记录云函数出错",error:e}}))}function m(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.callFunction({name:"patrol-stats",data:u({action:t},n)}).then((function(e){return e.result})).catch((function(e){return{code:-1,message:"调用统计云函数出错",error:e}}))}function y(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.callFunction({name:"patrol-user",data:u({action:t},n)}).then((function(e){return e.result})).catch((function(e){return{code:-1,message:"调用用户云函数出错",error:e}}))}function _(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.userId||null;if(t.hasOwnProperty("userId")&&null===t.userId)return Promise.resolve({code:401,message:"未登录，无法获取任务列表",data:{tasks:[]}});if(!n)try{var o=r.getStorageSync("uni-id-pages-userInfo");if(o)if("object"===(0,i.default)(o))n=o._id;else if("string"===typeof o)try{o=JSON.parse(o),n=o._id}catch(s){console.error("解析userInfo字符串失败:",s.message)}if(!n){var a=r.getStorageSync("uni_id_token");if(a)if("object"===(0,i.default)(a))n=a.uid;else if("string"===typeof a)try{a=JSON.parse(a),n=a.uid}catch(s){console.error("解析token字符串失败:",s.message)}}n||(n=r.getStorageSync("uid")||r.getStorageSync("uni_id_user_id")||null)}catch(c){console.error("获取用户ID出错:",c)}return n?e.callFunction({name:"patrol-task-today",data:u({action:"getTodayTasks",userId:n},t)}).then((function(e){return e.result})).catch((function(e){return{code:-1,message:"调用今日任务云函数出错",error:e}})):Promise.resolve({code:401,message:"未登录，无法获取任务列表",data:{tasks:[]}})}function b(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.callFunction({name:"patrol-sync",data:t}).then((function(e){return e.result})).catch((function(e){return{code:-1,message:"调用同步云函数出错",error:e}}))}var w={getTaskList:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return f("getTaskList",{params:e})},getTaskDetail:function(e){return e?f("getTaskDetail",{task_id:e}):Promise.reject({code:1,message:"任务ID不能为空"})},updateTaskStatus:function(e,t){return e?f("updateTaskStatus",{task_id:e,status:t}):Promise.reject({code:1,message:"任务ID不能为空"})},addTask:function(e){return f("addTask",e)},updateTask:function(e,t){return e?f("updateTask",{params:u({task_id:e},t)}):Promise.reject({code:1,message:"任务ID不能为空"})},deleteTask:function(e){return f("deleteTask",{task_id:e})},getPointList:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return d("getPointList",{params:e})},getPointDetail:function(e){return e?d("getPointDetail",{point_id:e}):Promise.reject({code:1,message:"点位ID不能为空"})},addPoint:function(e){return d("addPoint",{data:e})},updatePoint:function(e){return d("updatePoint",{data:e})},deletePoint:function(e){return e?d("deletePoint",{point_id:e}):Promise.reject({code:1,message:"点位ID不能为空"})},getShiftList:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.timestamp||(e.timestamp=Date.now()),p("getShiftList",{params:e})},getShiftDetail:function(e){return p("getShiftDetail",{params:{shift_id:e}})},addShift:function(e){return e.rounds&&Array.isArray(e.rounds)&&(e.rounds=e.rounds.map((function(e){return u(u({},e),{},{day_offset:void 0!==e.day_offset?parseInt(e.day_offset):0,duration:void 0!==e.duration?parseInt(e.duration):60})}))),p("addShift",{params:e})},updateShift:function(e){return e.rounds&&Array.isArray(e.rounds)&&(e.rounds=e.rounds.map((function(e){return u(u({},e),{},{day_offset:void 0!==e.day_offset?parseInt(e.day_offset):0,duration:void 0!==e.duration?parseInt(e.duration):60})}))),p("updateShift",{params:e})},deleteShift:function(e){return p("deleteShift",{params:{shift_id:e}})},getRouteList:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h("getRouteList",{params:e})},getRouteDetail:function(e){return e?h("getRouteDetail",{route_id:e}):Promise.reject({code:1,message:"线路ID不能为空"})},addRoute:function(e){return h("addRoute",{data:e})},updateRoute:function(e){return h("updateRoute",{data:e})},deleteRoute:function(e){return e?h("deleteRoute",{route_id:e}):Promise.reject({code:1,message:"线路ID不能为空"})},submitCheckIn:function(e){var t={task_id:e.task_id,point_id:e.point_id,round:e.round,location:e.location,photos:e.photos||[],remark:e.remark||"",abnormal:!!e.abnormal,status:e.status||1};return l({name:"patrol-record",action:"submitCheckIn",data:t})},getRecordList:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return v("getRecordList",{params:e})},getRecordDetail:function(e){return e?v("getRecordDetail",{record_id:e}):Promise.reject({code:1,message:"记录ID不能为空"})},getUserList:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return y("getUsers",{params:e})},getUserDetail:function(e){return e?y("getUserDetail",{params:{id:e}}):Promise.reject({code:1,message:"用户ID不能为空"})},getRoleList:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return y("getRoleList",{params:e})},getUsers:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return f("getUsers",{params:e})},getConfig:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("getConfigList",{params:e})},getConfigDetail:function(e){return e?g("getConfigDetail",{key:e}):Promise.reject({code:1,message:"配置键名不能为空"})},updateConfig:function(e){return e.key?g("updateConfig",{params:e}):Promise.reject({code:1,message:"配置键名不能为空"})},batchUpdateConfig:function(e){return Array.isArray(e)&&0!==e.length?g("batchUpdateConfig",{configs:e}):Promise.reject({code:1,message:"配置列表不能为空"})},deleteConfig:function(e){return e?g("deleteConfig",{key:e}):Promise.reject({code:1,message:"配置键名不能为空"})},initDefaultConfig:function(){return g("initDefaultConfig",{})},getConfigValue:function(e,t){return e?g("getConfigValue",{key:e,defaultValue:t}).then((function(e){return 0===e.code?e.data:t})).catch((function(e){return t})):Promise.resolve(t)},getPatrolStats:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m("getPatrolStats",{params:e})},getUserStats:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m("getUserStats",{params:e})},getPointStats:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m("getPointStats",{params:e})},getTaskCompletionTrend:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m("getTaskCompletionTrend",{params:e})},getTaskStatusDistribution:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m("getTaskStatusDistribution",{params:e})},getUserCompletionRank:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m("getUserCompletionRank",{params:e})},getRoundCompletionRates:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m("getRoundCompletionRates",{params:e})},getPointCompletionRates:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m("getPointCompletionRates",{params:e})},getCheckinTimeDistribution:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m("getCheckinTimeDistribution",{params:e})},call:l,callTaskFunction:f,callPointFunction:d,callRouteFunction:h,callShiftFunction:p,callConfigFunction:g,callRecordFunction:v,callStatsFunction:m,callSyncFunction:b,syncOfflineRecords:function(e){return Array.isArray(e)&&0!==e.length?b({records:e,userId:r.getStorageSync("uni_id_token_expired")||""}):Promise.resolve({code:0,message:"没有需要同步的记录",data:{success:0,failed:0}})},getTodayTasks:_,formatDate:c,calculatePrevDay:function(e){try{if(e instanceof Date){var t=new Date(e);return t.setDate(t.getDate()-1),c(t)}var n=new Date(e.replace?e.replace(/-/g,"/"):e);return n.setDate(n.getDate()-1),c(n)}catch(r){return console.error("计算前一天日期出错:",r),e instanceof Date?c(e):e}}};t.default=w}).call(this,n("861b")["uniCloud"],n("df3c")["default"])},c718:function(e){e.exports=JSON.parse('{"uni-load-more.contentdown":"Pull up to show more","uni-load-more.contentrefresh":"loading...","uni-load-more.contentnomore":"No more data"}')},ca42:function(e){e.exports=JSON.parse('{"uni-pagination.prevText":"上一页","uni-pagination.nextText":"下一页","uni-pagination.piecePerPage":"条/页"}')},cdc9:function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("8546")),i=r(n("56a1")),a=r(n("e38a")),s=r(n("ca42")),u=r(n("6f50")),c={en:o.default,es:i.default,fr:a.default,"zh-Hans":s.default,"zh-Hant":u.default};t.default=c},d3b4:function(e,t,n){"use strict";(function(e,r){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.LOCALE_ZH_HANT=t.LOCALE_ZH_HANS=t.LOCALE_FR=t.LOCALE_ES=t.LOCALE_EN=t.I18n=t.Formatter=void 0,t.compileI18nJsonStr=function(e,t){var n=t.locale,r=t.locales,o=t.delimiters;if(!O(e,o))return e;x||(x=new f);var i=[];Object.keys(r).forEach((function(e){e!==n&&i.push({locale:e,values:r[e]})})),i.unshift({locale:n,values:r[n]});try{return JSON.stringify(P(JSON.parse(e),i,o),null,2)}catch(a){}return e},t.hasI18nJson=function e(t,n){x||(x=new f);return C(t,(function(t,r){var o=t[r];return S(o)?!!O(o,n)||void 0:e(o,n)}))},t.initVueI18n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;if("string"!==typeof e){var o=[t,e];e=o[0],t=o[1]}"string"!==typeof e&&(e=k());"string"!==typeof n&&(n="undefined"!==typeof __uniConfig&&__uniConfig.fallbackLocale||"en");var i=new b({locale:e,fallbackLocale:n,messages:t,watcher:r}),a=function(e,t){if("function"!==typeof getApp)a=function(e,t){return i.t(e,t)};else{var n=!1;a=function(e,t){var r=getApp().$vm;return r&&(r.$locale,n||(n=!0,w(r,i))),i.t(e,t)}}return a(e,t)};return{i18n:i,f:function(e,t,n){return i.f(e,t,n)},t:function(e,t){return a(e,t)},add:function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return i.add(e,t,n)},watch:function(e){return i.watchLocale(e)},getLocale:function(){return i.getLocale()},setLocale:function(e){return i.setLocale(e)}}},t.isI18nStr=O,t.isString=void 0,t.normalizeLocale=_,t.parseI18nJson=function e(t,n,r){x||(x=new f);return C(t,(function(t,o){var i=t[o];S(i)?O(i,r)&&(t[o]=T(i,n,r)):e(i,n,r)})),t},t.resolveLocale=function(e){return function(t){return t?(t=_(t)||t,function(e){var t=[],n=e.split("-");while(n.length)t.push(n.join("-")),n.pop();return t}(t).find((function(t){return e.indexOf(t)>-1}))):t}};var i=o(n("34cf")),a=o(n("67ad")),s=o(n("0bdb")),u=o(n("3b2d")),c=function(e){return null!==e&&"object"===(0,u.default)(e)},l=["{","}"],f=function(){function e(){(0,a.default)(this,e),this._caches=Object.create(null)}return(0,s.default)(e,[{key:"interpolate",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:l;if(!t)return[e];var r=this._caches[e];return r||(r=p(e,n),this._caches[e]=r),g(r,t)}}]),e}();t.Formatter=f;var d=/^(?:\d)+/,h=/^(?:\w)+/;function p(e,t){var n=(0,i.default)(t,2),r=n[0],o=n[1],a=[],s=0,u="";while(s<e.length){var c=e[s++];if(c===r){u&&a.push({type:"text",value:u}),u="";var l="";c=e[s++];while(void 0!==c&&c!==o)l+=c,c=e[s++];var f=c===o,p=d.test(l)?"list":f&&h.test(l)?"named":"unknown";a.push({value:l,type:p})}else u+=c}return u&&a.push({type:"text",value:u}),a}function g(e,t){var n=[],r=0,o=Array.isArray(t)?"list":c(t)?"named":"unknown";if("unknown"===o)return n;while(r<e.length){var i=e[r];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===o&&n.push(t[i.value]);break;case"unknown":0;break}r++}return n}t.LOCALE_ZH_HANS="zh-Hans";t.LOCALE_ZH_HANT="zh-Hant";t.LOCALE_EN="en";t.LOCALE_FR="fr";t.LOCALE_ES="es";var v=Object.prototype.hasOwnProperty,m=function(e,t){return v.call(e,t)},y=new f;function _(e,t){if(e){if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if(e=e.toLowerCase(),"chinese"===e)return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1||function(e,t){return!!t.find((function(t){return-1!==e.indexOf(t)}))}(e,["-tw","-hk","-mo","-cht"])?"zh-Hant":"zh-Hans";var n=["en","fr","es"];t&&Object.keys(t).length>0&&(n=Object.keys(t));var r=function(e,t){return t.find((function(t){return 0===e.indexOf(t)}))}(e,n);return r||void 0}}var b=function(){function e(t){var n=t.locale,r=t.fallbackLocale,o=t.messages,i=t.watcher,s=t.formater;(0,a.default)(this,e),this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],r&&(this.fallbackLocale=r),this.formater=s||y,this.messages=o||{},this.setLocale(n||"en"),i&&this.watchLocale(i)}return(0,s.default)(e,[{key:"setLocale",value:function(e){var t=this,n=this.locale;this.locale=_(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],n!==this.locale&&this.watchers.forEach((function(e){e(t.locale,n)}))}},{key:"getLocale",value:function(){return this.locale}},{key:"watchLocale",value:function(e){var t=this,n=this.watchers.push(e)-1;return function(){t.watchers.splice(n,1)}}},{key:"add",value:function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=this.messages[e];r?n?Object.assign(r,t):Object.keys(t).forEach((function(e){m(r,e)||(r[e]=t[e])})):this.messages[e]=t}},{key:"f",value:function(e,t,n){return this.formater.interpolate(e,t,n).join("")}},{key:"t",value:function(e,t,n){var r=this.message;return"string"===typeof t?(t=_(t,this.messages),t&&(r=this.messages[t])):n=t,m(r,e)?this.formater.interpolate(r[e],n).join(""):(console.warn("Cannot translate the value of keypath ".concat(e,". Use the value of keypath as default.")),e)}}]),e}();function w(e,t){e.$watchLocale?e.$watchLocale((function(e){t.setLocale(e)})):e.$watch((function(){return e.$locale}),(function(e){t.setLocale(e)}))}function k(){return"undefined"!==typeof e&&e.getLocale?e.getLocale():"undefined"!==typeof r&&r.getLocale?r.getLocale():"en"}t.I18n=b;var x,S=function(e){return"string"===typeof e};function O(e,t){return e.indexOf(t[0])>-1}function T(e,t,n){return x.interpolate(e,t,n).join("")}function P(e,t,n){return C(e,(function(e,r){(function(e,t,n,r){var o=e[t];if(S(o)){if(O(o,r)&&(e[t]=T(o,n[0].values,r),n.length>1)){var i=e[t+"Locales"]={};n.forEach((function(e){i[e.locale]=T(o,e.values,r)}))}}else P(o,n,r)})(e,r,t,n)})),e}function C(e,t){if(Array.isArray(e)){for(var n=0;n<e.length;n++)if(t(e,n))return!0}else if(c(e))for(var r in e)if(t(e,r))return!0;return!1}t.isString=S}).call(this,n("df3c")["default"],n("0ee4"))},d551:function(e,t,n){var r=n("3b2d")["default"],o=n("e6db");e.exports=function(e){var t=o(e,"string");return"symbol"==r(t)?t:t+""},e.exports.__esModule=!0,e.exports["default"]=e.exports},db30:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={appid:"__UNI__2339AC9"}},dd3e:function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports["default"]=e.exports},df01:function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={SET_USER_INFO:function(e,t){e.userInfo=t},SET_TOKEN:function(t,n){t.token=n,e.setStorageSync("uni_id_token",n)},SET_LOGIN_STATE:function(e,t){e.isLoggedIn=t},SET_PERMISSIONS:function(e,t){e.permissions=t},CLEAR_USER_INFO:function(t){t.userInfo=null,t.token=null,t.isLoggedIn=!1,t.permissions=[],e.removeStorageSync("uni_id_token")}},r={namespaced:!0,state:{userInfo:null,token:null,isLoggedIn:!1,permissions:[]},getters:{userInfo:function(e){return e.userInfo},isLoggedIn:function(e){return e.isLoggedIn},permissions:function(e){return e.permissions},hasPermission:function(e){return function(t){return e.permissions.includes(t)}},hasManagePermission:function(e){if(!e.userInfo||!e.userInfo.role)return!1;var t=e.userInfo.role;return["admin","supervisor","PM","GM","reviser"].includes(t)}},mutations:n,actions:{loginSuccess:function(e,t){var n=e.commit,r=t.userInfo,o=t.token,i=t.permissions,a=void 0===i?[]:i;n("SET_USER_INFO",r),n("SET_TOKEN",o),n("SET_LOGIN_STATE",!0),n("SET_PERMISSIONS",a)},logout:function(e){var t=e.commit;t("CLEAR_USER_INFO")},updateUserInfo:function(e,t){var n=e.commit;n("SET_USER_INFO",t)}}};t.default=r}).call(this,n("df3c")["default"])},df3c:function(e,t,n){"use strict";(function(e,r){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.createApp=Lt,t.createComponent=zt,t.createPage=Ht,t.createPlugin=Kt,t.createSubpackageApp=Vt,t.default=void 0;var i,a=o(n("34cf")),s=o(n("7ca3")),u=o(n("931d")),c=o(n("af34")),l=o(n("3b2d")),f=n("d3b4"),d=o(n("3240"));function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){(0,s.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",v=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function m(){var t,n=e.getStorageSync("uni_id_token")||"",r=n.split(".");if(!n||3!==r.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{t=JSON.parse(function(e){return decodeURIComponent(i(e).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))}(r[1]))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}return t.tokenExpired=1e3*t.exp,delete t.exp,delete t.iat,t}i="function"!==typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!v.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,o="",i=0;i<e.length;)t=g.indexOf(e.charAt(i++))<<18|g.indexOf(e.charAt(i++))<<12|(n=g.indexOf(e.charAt(i++)))<<6|(r=g.indexOf(e.charAt(i++))),o+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o}:atob;var y=Object.prototype.toString,_=Object.prototype.hasOwnProperty;function b(e){return"function"===typeof e}function w(e){return"string"===typeof e}function k(e){return"[object Object]"===y.call(e)}function x(e,t){return _.call(e,t)}function S(){}function O(e){var t=Object.create(null);return function(n){var r=t[n];return r||(t[n]=e(n))}}var T=/-(\w)/g,P=O((function(e){return e.replace(T,(function(e,t){return t?t.toUpperCase():""}))}));function C(e){var t={};return k(e)&&Object.keys(e).sort().forEach((function(n){t[n]=e[n]})),Object.keys(t)?t:e}var A=["invoke","success","fail","complete","returnValue"],I={},E={};function D(e,t){Object.keys(t).forEach((function(n){-1!==A.indexOf(n)&&b(t[n])&&(e[n]=function(e,t){var n=t?e?e.concat(t):Array.isArray(t)?t:[t]:e;return n?function(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}function j(e,t){e&&t&&Object.keys(t).forEach((function(n){-1!==A.indexOf(n)&&b(t[n])&&function(e,t){var n=e.indexOf(t);-1!==n&&e.splice(n,1)}(e[n],t[n])}))}function L(e,t){return function(n){return e(n,t)||n}}function N(e){return!!e&&("object"===(0,l.default)(e)||"function"===typeof e)&&"function"===typeof e.then}function M(e,t,n){for(var r=!1,o=0;o<e.length;o++){var i=e[o];if(r)r=Promise.resolve(L(i,n));else{var a=i(t,n);if(N(a)&&(r=Promise.resolve(a)),!1===a)return{then:function(){}}}}return r||{then:function(e){return e(t)}}}function R(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return["success","fail","complete"].forEach((function(n){if(Array.isArray(e[n])){var r=t[n];t[n]=function(o){M(e[n],o,t).then((function(e){return b(r)&&r(e)||e}))}}})),t}function B(e,t){var n=[];Array.isArray(I.returnValue)&&n.push.apply(n,(0,c.default)(I.returnValue));var r=E[e];return r&&Array.isArray(r.returnValue)&&n.push.apply(n,(0,c.default)(r.returnValue)),n.forEach((function(e){t=e(t)||t})),t}function $(e){var t=Object.create(null);Object.keys(I).forEach((function(e){"returnValue"!==e&&(t[e]=I[e].slice())}));var n=E[e];return n&&Object.keys(n).forEach((function(e){"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function U(e,t,n){for(var r=arguments.length,o=new Array(r>3?r-3:0),i=3;i<r;i++)o[i-3]=arguments[i];var a=$(e);if(a&&Object.keys(a).length){if(Array.isArray(a.invoke)){var s=M(a.invoke,n);return s.then((function(n){return t.apply(void 0,[R($(e),n)].concat(o))}))}return t.apply(void 0,[R(a,n)].concat(o))}return t.apply(void 0,[n].concat(o))}var F={returnValue:function(e){return N(e)?new Promise((function(t,n){e.then((function(e){e?e[0]?n(e[0]):t(e[1]):t(e)}))})):e}},q=/^\$|__f__|Window$|WindowStyle$|sendHostEvent|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|rpx2px|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale|invokePushCallback|getWindowInfo|getDeviceInfo|getAppBaseInfo|getSystemSetting|getAppAuthorizeSetting|initUTS|requireUTS|registerUTS/,H=/^create|Manager$/,z=["createBLEConnection"],V=["createBLEConnection","createPushMessage"],K=/^on|^off/;function W(e){return H.test(e)&&-1===z.indexOf(e)}function J(e){return q.test(e)&&-1===V.indexOf(e)}function G(e){return e.then((function(e){return[null,e]})).catch((function(e){return[e]}))}function Y(e){return!(W(e)||J(e)||function(e){return K.test(e)&&"onPush"!==e}(e))}function X(e,t){return Y(e)&&b(t)?function(){for(var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length,o=new Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];return b(n.success)||b(n.fail)||b(n.complete)?B(e,U.apply(void 0,[e,t,Object.assign({},n)].concat(o))):B(e,G(new Promise((function(r,i){U.apply(void 0,[e,t,Object.assign({},n,{success:r,fail:i})].concat(o))}))))}:t}Promise.prototype.finally||(Promise.prototype.finally=function(e){var t=this.constructor;return this.then((function(n){return t.resolve(e()).then((function(){return n}))}),(function(n){return t.resolve(e()).then((function(){throw n}))}))});var Q=!1,Z=0,ee=0;function te(t,n){if(0===Z&&function(){var t,n,r,o="function"===typeof e.getWindowInfo&&e.getWindowInfo()?e.getWindowInfo():e.getSystemInfoSync(),i="function"===typeof e.getDeviceInfo&&e.getDeviceInfo()?e.getDeviceInfo():e.getSystemInfoSync();t=o.windowWidth,n=o.pixelRatio,r=i.platform,Z=t,ee=n,Q="ios"===r}(),t=Number(t),0===t)return 0;var r=t/750*(n||Z);return r<0&&(r=-r),r=Math.floor(r+1e-4),0===r&&(r=1!==ee&&Q?.5:1),t<0?-r:r}var ne,re={};function oe(){var t,n="function"===typeof e.getAppBaseInfo&&e.getAppBaseInfo()?e.getAppBaseInfo():e.getSystemInfoSync(),r=n&&n.language?n.language:"en";return t=se(r)||"en",t}ne=oe(),function(){if(function(){return"undefined"!==typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length}()){var e=Object.keys(__uniConfig.locales);e.length&&e.forEach((function(e){var t=re[e],n=__uniConfig.locales[e];t?Object.assign(t,n):re[e]=n}))}}();var ie=(0,f.initVueI18n)(ne,{}),ae=ie.t;ie.mixin={beforeCreate:function(){var e=this,t=ie.i18n.watchLocale((function(){e.$forceUpdate()}));this.$once("hook:beforeDestroy",(function(){t()}))},methods:{$$t:function(e,t){return ae(e,t)}}},ie.setLocale,ie.getLocale;function se(e,t){if(e){if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if(e=e.toLowerCase(),"chinese"===e)return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1||function(e,t){return!!t.find((function(t){return-1!==e.indexOf(t)}))}(e,["-tw","-hk","-mo","-cht"])?"zh-Hant":"zh-Hans";var n=function(e,t){return t.find((function(t){return 0===e.indexOf(t)}))}(e,["en","fr","es"]);return n||void 0}}function ue(){if(b(getApp)){var e=getApp({allowDefault:!0});if(e&&e.$vm)return e.$vm.$locale}return oe()}var ce=[];"undefined"!==typeof r&&(r.getLocale=ue);var le={promiseInterceptor:F},fe=Object.freeze({__proto__:null,upx2px:te,rpx2px:te,getLocale:ue,setLocale:function(e){var t=!!b(getApp)&&getApp();if(!t)return!1;var n=t.$vm.$locale;return n!==e&&(t.$vm.$locale=e,ce.forEach((function(t){return t({locale:e})})),!0)},onLocaleChange:function(e){-1===ce.indexOf(e)&&ce.push(e)},addInterceptor:function(e,t){"string"===typeof e&&k(t)?D(E[e]||(E[e]={}),t):k(e)&&D(I,e)},removeInterceptor:function(e,t){"string"===typeof e?k(t)?j(E[e],t):delete E[e]:k(e)&&j(I,e)},interceptors:le});var de,he={name:function(e){return"back"===e.exists&&e.delta?"navigateBack":"redirectTo"},args:function(e){if("back"===e.exists&&e.url){var t=function(e){var t=getCurrentPages(),n=t.length;while(n--){var r=t[n];if(r.$page&&r.$page.fullPath===e)return n}return-1}(e.url);if(-1!==t){var n=getCurrentPages().length-1-t;n>0&&(e.delta=n)}}}},pe={args:function(e){var t=parseInt(e.current);if(!isNaN(t)){var n=e.urls;if(Array.isArray(n)){var r=n.length;if(r)return t<0?t=0:t>=r&&(t=r-1),t>0?(e.current=n[t],e.urls=n.filter((function(e,r){return!(r<t)||e!==n[t]}))):e.current=n[0],{indicator:!1,loop:!1}}}}};function ge(t){de=de||e.getStorageSync("__DC_STAT_UUID"),de||(de=Date.now()+""+Math.floor(1e7*Math.random()),e.setStorage({key:"__DC_STAT_UUID",data:de})),t.deviceId=de}function ve(e){if(e.safeArea){var t=e.safeArea;e.safeAreaInsets={top:t.top,left:t.left,right:e.windowWidth-t.right,bottom:e.screenHeight-t.bottom}}}function me(e,t){var n="",r="";switch(n=e.split(" ")[0]||t,r=e.split(" ")[1]||"",n=n.toLocaleLowerCase(),n){case"harmony":case"ohos":case"openharmony":n="harmonyos";break;case"iphone os":n="ios";break;case"mac":case"darwin":n="macos";break;case"windows_nt":n="windows";break}return{osName:n,osVersion:r}}function ye(e,t){for(var n=e.deviceType||"phone",r={ipad:"pad",windows:"pc",mac:"pc"},o=Object.keys(r),i=t.toLocaleLowerCase(),a=0;a<o.length;a++){var s=o[a];if(-1!==i.indexOf(s)){n=r[s];break}}return n}function _e(e){var t=e;return t&&(t=e.toLocaleLowerCase()),t}function be(e){return ue?ue():e}function we(e){var t=e.hostName||"WeChat";return e.environment?t=e.environment:e.host&&e.host.env&&(t=e.host.env),t}var ke={returnValue:function(e){ge(e),ve(e),function(e){var t=e.brand,n=void 0===t?"":t,r=e.model,o=void 0===r?"":r,i=e.system,a=void 0===i?"":i,s=e.language,u=void 0===s?"":s,c=e.theme,l=e.version,f=e.platform,d=e.fontSizeSetting,h=e.SDKVersion,p=e.pixelRatio,g=e.deviceOrientation,v=me(a,f),m=v.osName,y=v.osVersion,_=l,b=ye(e,o),w=_e(n),k=we(e),x=g,S=p,O=h,T=(u||"").replace(/_/g,"-"),P={appId:"__UNI__2339AC9",appName:"株水小智",appVersion:"3.3.3",appVersionCode:"333",appLanguage:be(T),uniCompileVersion:"4.75",uniCompilerVersion:"4.75",uniRuntimeVersion:"4.75",uniPlatform:"mp-weixin",deviceBrand:w,deviceModel:o,deviceType:b,devicePixelRatio:S,deviceOrientation:x,osName:m.toLocaleLowerCase(),osVersion:y,hostTheme:c,hostVersion:_,hostLanguage:T,hostName:k,hostSDKVersion:O,hostFontSizeSetting:d,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0,isUniAppX:!1};Object.assign(e,P,{})}(e)}},xe={args:function(e){"object"===(0,l.default)(e)&&(e.alertText=e.title)}},Se={returnValue:function(e){var t=e,n=t.version,r=t.language,o=t.SDKVersion,i=t.theme,a=we(e),s=(r||"").replace("_","-");e=C(Object.assign(e,{appId:"__UNI__2339AC9",appName:"株水小智",appVersion:"3.3.3",appVersionCode:"333",appLanguage:be(s),hostVersion:n,hostLanguage:s,hostName:a,hostSDKVersion:o,hostTheme:i,isUniAppX:!1,uniPlatform:"mp-weixin",uniCompileVersion:"4.75",uniCompilerVersion:"4.75",uniRuntimeVersion:"4.75"}))}},Oe={returnValue:function(e){var t=e,n=t.brand,r=t.model,o=t.system,i=void 0===o?"":o,a=t.platform,s=void 0===a?"":a,u=ye(e,r),c=_e(n);ge(e);var l=me(i,s),f=l.osName,d=l.osVersion;e=C(Object.assign(e,{deviceType:u,deviceBrand:c,deviceModel:r,osName:f,osVersion:d}))}},Te={returnValue:function(e){ve(e),e=C(Object.assign(e,{windowTop:0,windowBottom:0}))}},Pe={redirectTo:he,previewImage:pe,getSystemInfo:ke,getSystemInfoSync:ke,showActionSheet:xe,getAppBaseInfo:Se,getDeviceInfo:Oe,getWindowInfo:Te,getAppAuthorizeSetting:{returnValue:function(e){var t=e.locationReducedAccuracy;e.locationAccuracy="unsupported",!0===t?e.locationAccuracy="reduced":!1===t&&(e.locationAccuracy="full")}},compressImage:{args:function(e){e.compressedHeight&&!e.compressHeight&&(e.compressHeight=e.compressedHeight),e.compressedWidth&&!e.compressWidth&&(e.compressWidth=e.compressedWidth)}}},Ce=["success","fail","cancel","complete"];function Ae(e,t,n){return function(r){return t(Ee(e,r,n))}}function Ie(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(k(t)){var i=!0===o?t:{};for(var a in b(n)&&(n=n(t,i)||{}),t)if(x(n,a)){var s=n[a];b(s)&&(s=s(t[a],t,i)),s?w(s)?i[s]=t[a]:k(s)&&(i[s.name?s.name:a]=s.value):console.warn("The '".concat(e,"' method of platform '微信小程序' does not support option '").concat(a,"'"))}else-1!==Ce.indexOf(a)?b(t[a])&&(i[a]=Ae(e,t[a],r)):o||(i[a]=t[a]);return i}return b(t)&&(t=Ae(e,t,r)),t}function Ee(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return b(Pe.returnValue)&&(t=Pe.returnValue(e,t)),Ie(e,t,n,{},r)}function De(t,n){if(x(Pe,t)){var r=Pe[t];return r?function(n,o){var i=r;b(r)&&(i=r(n)),n=Ie(t,n,i.args,i.returnValue);var a=[n];"undefined"!==typeof o&&a.push(o),b(i.name)?t=i.name(n):w(i.name)&&(t=i.name);var s=e[t].apply(e,a);return J(t)?Ee(t,s,i.returnValue,W(t)):s}:function(){console.error("Platform '微信小程序' does not support '".concat(t,"'."))}}return n}var je=Object.create(null);["onTabBarMidButtonTap","subscribePush","unsubscribePush","onPush","offPush","share"].forEach((function(e){je[e]=function(e){return function(t){var n=t.fail,r=t.complete,o={errMsg:"".concat(e,":fail method '").concat(e,"' not supported")};b(n)&&n(o),b(r)&&r(o)}}(e)}));var Le={oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]};var Ne=Object.freeze({__proto__:null,getProvider:function(e){var t=e.service,n=e.success,r=e.fail,o=e.complete,i=!1;Le[t]?(i={errMsg:"getProvider:ok",service:t,provider:Le[t]},b(n)&&n(i)):(i={errMsg:"getProvider:fail service not found"},b(r)&&r(i)),b(o)&&o(i)}}),Me=function(){var e;return function(){return e||(e=new d.default),e}}();function Re(e,t,n){return e[t].apply(e,n)}var Be,$e,Ue,Fe=Object.freeze({__proto__:null,$on:function(){return Re(Me(),"$on",Array.prototype.slice.call(arguments))},$off:function(){return Re(Me(),"$off",Array.prototype.slice.call(arguments))},$once:function(){return Re(Me(),"$once",Array.prototype.slice.call(arguments))},$emit:function(){return Re(Me(),"$emit",Array.prototype.slice.call(arguments))}});function qe(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}function He(e){try{return JSON.parse(e)}catch(t){}return e}var ze=[];function Ve(e,t){ze.forEach((function(n){n(e,t)})),ze.length=0}var Ke=[];var We=e.getAppBaseInfo&&e.getAppBaseInfo();We||(We=e.getSystemInfoSync());var Je=We?We.host:null,Ge=Je&&"SAAASDK"===Je.env?e.miniapp.shareVideoMessage:e.shareVideoMessage,Ye=Object.freeze({__proto__:null,shareVideoMessage:Ge,getPushClientId:function(e){k(e)||(e={});var t=function(e){var t={};for(var n in e){var r=e[n];b(r)&&(t[n]=qe(r),delete e[n])}return t}(e),n=t.success,r=t.fail,o=t.complete,i=b(n),a=b(r),s=b(o);Promise.resolve().then((function(){"undefined"===typeof Ue&&(Ue=!1,Be="",$e="uniPush is not enabled"),ze.push((function(e,t){var u;e?(u={errMsg:"getPushClientId:ok",cid:e},i&&n(u)):(u={errMsg:"getPushClientId:fail"+(t?" "+t:"")},a&&r(u)),s&&o(u)})),"undefined"!==typeof Be&&Ve(Be,$e)}))},onPushMessage:function(e){-1===Ke.indexOf(e)&&Ke.push(e)},offPushMessage:function(e){if(e){var t=Ke.indexOf(e);t>-1&&Ke.splice(t,1)}else Ke.length=0},invokePushCallback:function(e){if("enabled"===e.type)Ue=!0;else if("clientId"===e.type)Be=e.cid,$e=e.errMsg,Ve(Be,e.errMsg);else if("pushMsg"===e.type)for(var t={type:"receive",data:He(e.message)},n=0;n<Ke.length;n++){var r=Ke[n];if(r(t),t.stopped)break}else"click"===e.type&&Ke.forEach((function(t){t({type:"click",data:He(e.message)})}))},__f__:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];console[e].apply(console,n)}}),Xe=["__route__","__wxExparserNodeId__","__wxWebviewId__"];function Qe(e){return Behavior(e)}function Ze(){return!!this.route}function et(e){this.triggerEvent("__l",e)}function tt(e){var t=e.$scope,n={};Object.defineProperty(e,"$refs",{get:function(){var e={};(function e(t,n,r){var o=t.selectAllComponents(n)||[];o.forEach((function(t){var o=t.dataset.ref;r[o]=t.$vm||ot(t),"scoped"===t.dataset.vueGeneric&&t.selectAllComponents(".scoped-ref").forEach((function(t){e(t,n,r)}))}))})(t,".vue-ref",e);var r=t.selectAllComponents(".vue-ref-in-for")||[];return r.forEach((function(t){var n=t.dataset.ref;e[n]||(e[n]=[]),e[n].push(t.$vm||ot(t))})),function(e,t){var n=(0,u.default)(Set,(0,c.default)(Object.keys(e))),r=Object.keys(t);return r.forEach((function(r){var o=e[r],i=t[r];Array.isArray(o)&&Array.isArray(i)&&o.length===i.length&&i.every((function(e){return o.includes(e)}))||(e[r]=i,n.delete(r))})),n.forEach((function(t){delete e[t]})),e}(n,e)}})}function nt(e){var t,n=e.detail||e.value,r=n.vuePid,o=n.vueOptions;r&&(t=function e(t,n){for(var r,o=t.$children,i=o.length-1;i>=0;i--){var a=o[i];if(a.$scope._$vueId===n)return a}for(var s=o.length-1;s>=0;s--)if(r=e(o[s],n),r)return r}(this.$vm,r)),t||(t=this.$vm),o.parent=t}function rt(e){return Object.defineProperty(e,"__v_isMPComponent",{configurable:!0,enumerable:!1,value:!0}),e}function ot(e){return function(e){return null!==e&&"object"===(0,l.default)(e)}(e)&&Object.isExtensible(e)&&Object.defineProperty(e,"__ob__",{configurable:!0,enumerable:!1,value:(0,s.default)({},"__v_skip",!0)}),e}var it=/_(.*)_worklet_factory_/;var at=Page,st=Component,ut=/:/g,ct=O((function(e){return P(e.replace(ut,"-"))}));function lt(e){var t=e.triggerEvent,n=function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];if(this.$vm||this.dataset&&this.dataset.comType)e=ct(e);else{var i=ct(e);i!==e&&t.apply(this,[i].concat(r))}return t.apply(this,[e].concat(r))};try{e.triggerEvent=n}catch(r){e._triggerEvent=n}}function ft(e,t,n){var r=t[e];t[e]=function(){if(rt(this),lt(this),r){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r.apply(this,t)}}}at.__$wrappered||(at.__$wrappered=!0,Page=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return ft("onLoad",e),at(e)},Page.after=at.after,Component=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return ft("created",e),st(e)});function dt(e,t,n){t.forEach((function(t){(function e(t,n){if(!n)return!0;if(d.default.options&&Array.isArray(d.default.options[t]))return!0;if(n=n.default||n,b(n))return!!b(n.extendOptions[t])||!!(n.super&&n.super.options&&Array.isArray(n.super.options[t]));if(b(n[t])||Array.isArray(n[t]))return!0;var r=n.mixins;return Array.isArray(r)?!!r.find((function(n){return e(t,n)})):void 0})(t,n)&&(e[t]=function(e){return this.$vm&&this.$vm.__call_hook(t,e)})}))}function ht(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];pt(t).forEach((function(t){return gt(e,t,n)}))}function pt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e&&Object.keys(e).forEach((function(n){0===n.indexOf("on")&&b(e[n])&&t.push(n)})),t}function gt(e,t,n){-1!==n.indexOf(t)||x(e,t)||(e[t]=function(e){return this.$vm&&this.$vm.__call_hook(t,e)})}function vt(e,t){var n;return t=t.default||t,n=b(t)?t:e.extend(t),t=n.options,[n,t]}function mt(e,t){if(Array.isArray(t)&&t.length){var n=Object.create(null);t.forEach((function(e){n[e]=!0})),e.$scopedSlots=e.$slots=n}}function yt(e,t){e=(e||"").split(",");var n=e.length;1===n?t._$vueId=e[0]:2===n&&(t._$vueId=e[0],t._$vuePid=e[1])}function _t(e,t){var n=e.data||{},r=e.methods||{};if("function"===typeof n)try{n=n.call(t)}catch(o){Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"株水小智",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG&&console.warn("根据 Vue 的 data 函数初始化小程序 data 失败，请尽量确保 data 函数中不访问 vm 对象，否则可能影响首次数据渲染速度。",n)}else try{n=JSON.parse(JSON.stringify(n))}catch(o){}return k(n)||(n={}),Object.keys(r).forEach((function(e){-1!==t.__lifecycle_hooks__.indexOf(e)||x(n,e)||(n[e]=r[e])})),n}var bt=[String,Number,Boolean,Object,Array,null];function wt(e){return function(t,n){this.$vm&&(this.$vm[e]=t)}}function kt(e,t){var n=e.behaviors,r=e.extends,o=e.mixins,i=e.props;i||(e.props=i=[]);var a=[];return Array.isArray(n)&&n.forEach((function(e){a.push(e.replace("uni://","wx".concat("://"))),"uni://form-field"===e&&(Array.isArray(i)?(i.push("name"),i.push("value")):(i.name={type:String,default:""},i.value={type:[String,Number,Boolean,Array,Object,Date],default:""}))})),k(r)&&r.props&&a.push(t({properties:St(r.props,!0)})),Array.isArray(o)&&o.forEach((function(e){k(e)&&e.props&&a.push(t({properties:St(e.props,!0)}))})),a}function xt(e,t,n,r){return Array.isArray(t)&&1===t.length?t[0]:t}function St(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>3?arguments[3]:void 0,r={};return t||(r.vueId={type:String,value:""},n.virtualHost&&(r.virtualHostStyle={type:null,value:""},r.virtualHostClass={type:null,value:""}),r.scopedSlotsCompiler={type:String,value:""},r.vueSlots={type:null,value:[],observer:function(e,t){var n=Object.create(null);e.forEach((function(e){n[e]=!0})),this.setData({$slots:n})}}),Array.isArray(e)?e.forEach((function(e){r[e]={type:null,observer:wt(e)}})):k(e)&&Object.keys(e).forEach((function(t){var n=e[t];if(k(n)){var o=n.default;b(o)&&(o=o()),n.type=xt(0,n.type),r[t]={type:-1!==bt.indexOf(n.type)?n.type:null,value:o,observer:wt(t)}}else{var i=xt(0,n);r[t]={type:-1!==bt.indexOf(i)?i:null,observer:wt(t)}}})),r}function Ot(e,t,n,r){var o={};return Array.isArray(t)&&t.length&&t.forEach((function(t,i){"string"===typeof t?t?"$event"===t?o["$"+i]=n:"arguments"===t?o["$"+i]=n.detail&&n.detail.__args__||r:0===t.indexOf("$event.")?o["$"+i]=e.__get_value(t.replace("$event.",""),n):o["$"+i]=e.__get_value(t):o["$"+i]=e:o["$"+i]=function(e,t){var n=e;return t.forEach((function(t){var r=t[0],o=t[2];if(r||"undefined"!==typeof o){var i,a=t[1],s=t[3];Number.isInteger(r)?i=r:r?"string"===typeof r&&r&&(i=0===r.indexOf("#s#")?r.substr(3):e.__get_value(r,n)):i=n,Number.isInteger(i)?n=o:a?Array.isArray(i)?n=i.find((function(t){return e.__get_value(a,t)===o})):k(i)?n=Object.keys(i).find((function(t){return e.__get_value(a,i[t])===o})):console.error("v-for 暂不支持循环数据：",i):n=i[o],s&&(n=e.__get_value(s,n))}})),n}(e,t)})),o}function Tt(e){for(var t={},n=1;n<e.length;n++){var r=e[n];t[r[0]]=r[1]}return t}function Pt(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],o=arguments.length>4?arguments[4]:void 0,i=arguments.length>5?arguments[5]:void 0,a=!1,s=k(t.detail)&&t.detail.__args__||[t.detail];if(o&&(a=t.currentTarget&&t.currentTarget.dataset&&"wx"===t.currentTarget.dataset.comType,!n.length))return a?[t]:s;var u=Ot(e,r,t,s),c=[];return n.forEach((function(e){"$event"===e?"__set_model"!==i||o?o&&!a?c.push(s[0]):c.push(t):c.push(t.target.value):Array.isArray(e)&&"o"===e[0]?c.push(Tt(e)):"string"===typeof e&&x(u,e)?c.push(u[e]):c.push(e)})),c}function Ct(e){var t=this;e=function(e){try{e.mp=JSON.parse(JSON.stringify(e))}catch(t){}return e.stopPropagation=S,e.preventDefault=S,e.target=e.target||{},x(e,"detail")||(e.detail={}),x(e,"markerId")&&(e.detail="object"===(0,l.default)(e.detail)?e.detail:{},e.detail.markerId=e.markerId),k(e.detail)&&(e.target=Object.assign({},e.target,e.detail)),e}(e);var n=(e.currentTarget||e.target).dataset;if(!n)return console.warn("事件信息不存在");var r=n.eventOpts||n["event-opts"];if(!r)return console.warn("事件信息不存在");var o=e.type,i=[];return r.forEach((function(n){var r=n[0],a=n[1],s="^"===r.charAt(0);r=s?r.slice(1):r;var u="~"===r.charAt(0);r=u?r.slice(1):r,a&&function(e,t){return e===t||"regionchange"===t&&("begin"===e||"end"===e)}(o,r)&&a.forEach((function(n){var r=n[0];if(r){var o=t.$vm;if(o.$options.generic&&(o=function(e){var t=e.$parent;while(t&&t.$parent&&(t.$options.generic||t.$parent.$options.generic||t.$scope._$vuePid))t=t.$parent;return t&&t.$parent}(o)||o),"$emit"===r)return void o.$emit.apply(o,Pt(t.$vm,e,n[1],n[2],s,r));var a=o[r];if(!b(a)){var c="page"===t.$vm.mpType?"Page":"Component",l=t.route||t.is;throw new Error("".concat(c,' "').concat(l,'" does not have a method "').concat(r,'"'))}if(u){if(a.once)return;a.once=!0}var f=Pt(t.$vm,e,n[1],n[2],s,r);f=Array.isArray(f)?f:[],/=\s*\S+\.eventParams\s*\|\|\s*\S+\[['"]event-params['"]\]/.test(a.toString())&&(f=f.concat([,,,,,,,,,,e])),i.push(a.apply(o,f))}}))})),"input"===o&&1===i.length&&"undefined"!==typeof i[0]?i[0]:void 0}var At={};var It=["onShow","onHide","onError","onPageNotFound","onThemeChange","onUnhandledRejection"];function Et(){d.default.prototype.getOpenerEventChannel=function(){return this.$scope.getOpenerEventChannel()};var e=d.default.prototype.__call_hook;d.default.prototype.__call_hook=function(t,n){return"onLoad"===t&&n&&n.__id__&&(this.__eventChannel__=function(e){var t=At[e];return delete At[e],t}(n.__id__),delete n.__id__),e.call(this,t,n)}}function Dt(t,n){var r=n.mocks,o=n.initRefs;Et(),function(){var e={},t={};function n(e){var t=this.$options.propsData.vueId;if(t){var n=t.split(",")[0];e(n)}}d.default.prototype.$hasSSP=function(n){var r=e[n];return r||(t[n]=this,this.$on("hook:destroyed",(function(){delete t[n]}))),r},d.default.prototype.$getSSP=function(t,n,r){var o=e[t];if(o){var i=o[n]||[];return r?i:i[0]}},d.default.prototype.$setSSP=function(t,r){var o=0;return n.call(this,(function(n){var i=e[n],a=i[t]=i[t]||[];a.push(r),o=a.length-1})),o},d.default.prototype.$initSSP=function(){n.call(this,(function(t){e[t]={}}))},d.default.prototype.$callSSP=function(){n.call(this,(function(e){t[e]&&t[e].$forceUpdate()}))},d.default.mixin({destroyed:function(){var n=this.$options.propsData,r=n&&n.vueId;r&&(delete e[r],delete t[r])}})}(),t.$options.store&&(d.default.prototype.$store=t.$options.store),function(e){e.prototype.uniIDHasRole=function(e){var t=m(),n=t.role;return n.indexOf(e)>-1},e.prototype.uniIDHasPermission=function(e){var t=m(),n=t.permission;return this.uniIDHasRole("admin")||n.indexOf(e)>-1},e.prototype.uniIDTokenValid=function(){var e=m(),t=e.tokenExpired;return t>Date.now()}}(d.default),d.default.prototype.mpHost="mp-weixin",d.default.mixin({beforeCreate:function(){if(this.$options.mpType){if(this.mpType=this.$options.mpType,this.$mp=(0,s.default)({data:{}},this.mpType,this.$options.mpInstance),this.$scope=this.$options.mpInstance,delete this.$options.mpType,delete this.$options.mpInstance,"page"===this.mpType&&"function"===typeof getApp){var e=getApp();e.$vm&&e.$vm.$i18n&&(this._i18n=e.$vm.$i18n)}"app"!==this.mpType&&(o(this),function(e,t){var n=e.$mp[e.mpType];t.forEach((function(t){x(n,t)&&(e[t]=n[t])}))}(this,r))}}});var i={onLaunch:function(n){this.$vm||(e.canIUse&&!e.canIUse("nextTick")&&console.error("当前微信基础库版本过低，请将 微信开发者工具-详情-项目设置-调试基础库版本 更换为`2.3.0`以上"),this.$vm=t,this.$vm.$mp={app:this},this.$vm.$scope=this,this.$vm.globalData=this.globalData,this.$vm._isMounted=!0,this.$vm.__call_hook("mounted",n),this.$vm.__call_hook("onLaunch",n))}};i.globalData=t.$options.globalData||{};var a=t.$options.methods;return a&&Object.keys(a).forEach((function(e){i[e]=a[e]})),function(e,t,n){var r=e.observable({locale:n||ie.getLocale()}),o=[];t.$watchLocale=function(e){o.push(e)},Object.defineProperty(t,"$locale",{get:function(){return r.locale},set:function(e){r.locale=e,o.forEach((function(t){return t(e)}))}})}(d.default,t,function(){var t,n=e.getAppBaseInfo(),r=n&&n.language?n.language:"en";return t=se(r)||"en",t}()),dt(i,It),ht(i,t.$options),i}function jt(e){return Dt(e,{mocks:Xe,initRefs:tt})}function Lt(e){return App(jt(e)),e}var Nt=/[!'()*]/g,Mt=function(e){return"%"+e.charCodeAt(0).toString(16)},Rt=/%2C/g,Bt=function(e){return encodeURIComponent(e).replace(Nt,Mt).replace(Rt,",")};function $t(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Bt,n=e?Object.keys(e).map((function(n){var r=e[n];if(void 0===r)return"";if(null===r)return t(n);if(Array.isArray(r)){var o=[];return r.forEach((function(e){void 0!==e&&(null===e?o.push(t(n)):o.push(t(n)+"="+t(e)))})),o.join("&")}return t(n)+"="+t(r)})).filter((function(e){return e.length>0})).join("&"):null;return n?"?".concat(n):""}function Ut(e,t){return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.isPage,r=t.initRelation,o=arguments.length>2?arguments[2]:void 0,i=vt(d.default,e),s=(0,a.default)(i,2),u=s[0],c=s[1],l=p({multipleSlots:!0,addGlobalClass:!0},c.options||{});c["mp-weixin"]&&c["mp-weixin"].options&&Object.assign(l,c["mp-weixin"].options);var f={options:l,data:_t(c,d.default.prototype),behaviors:kt(c,Qe),properties:St(c.props,!1,c.__file,l),lifetimes:{attached:function(){var e=this.properties,t={mpType:n.call(this)?"page":"component",mpInstance:this,propsData:e};yt(e.vueId,this),r.call(this,{vuePid:this._$vuePid,vueOptions:t}),this.$vm=new u(t),mt(this.$vm,e.vueSlots),this.$vm.$mount()},ready:function(){this.$vm&&(this.$vm._isMounted=!0,this.$vm.__call_hook("mounted"),this.$vm.__call_hook("onReady"))},detached:function(){this.$vm&&this.$vm.$destroy()}},pageLifetimes:{show:function(e){this.$vm&&this.$vm.__call_hook("onPageShow",e)},hide:function(){this.$vm&&this.$vm.__call_hook("onPageHide")},resize:function(e){this.$vm&&this.$vm.__call_hook("onPageResize",e)}},methods:{__l:nt,__e:Ct}};return c.externalClasses&&(f.externalClasses=c.externalClasses),Array.isArray(c.wxsCallMethods)&&c.wxsCallMethods.forEach((function(e){f.methods[e]=function(t){return this.$vm[e](t)}})),o?[f,c,u]:n?f:[f,u]}(e,{isPage:Ze,initRelation:et},t)}var Ft=["onShow","onHide","onUnload"];function qt(e){var t=Ut(e,!0),n=(0,a.default)(t,2),r=n[0],o=n[1];return dt(r.methods,Ft,o),r.methods.onLoad=function(e){this.options=e;var t=Object.assign({},e);delete t.__id__,this.$page={fullPath:"/"+(this.route||this.is)+$t(t)},this.$vm.$mp.query=e,this.$vm.__call_hook("onLoad",e)},ht(r.methods,e,["onReady"]),function(e,t){t&&Object.keys(t).forEach((function(n){var r=n.match(it);if(r){var o=r[1];e[n]=t[n],e[o]=t[o]}}))}(r.methods,o.methods),r}function Ht(e){return Component(function(e){return qt(e)}(e))}function zt(e){return Component(Ut(e))}function Vt(t){var n=jt(t),r=getApp({allowDefault:!0});t.$scope=r;var o=r.globalData;if(o&&Object.keys(n.globalData).forEach((function(e){x(o,e)||(o[e]=n.globalData[e])})),Object.keys(n).forEach((function(e){x(r,e)||(r[e]=n[e])})),b(n.onShow)&&e.onAppShow&&e.onAppShow((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.__call_hook("onShow",n)})),b(n.onHide)&&e.onAppHide&&e.onAppHide((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.__call_hook("onHide",n)})),b(n.onLaunch)){var i=e.getLaunchOptionsSync&&e.getLaunchOptionsSync();t.__call_hook("onLaunch",i)}return t}function Kt(t){var n=jt(t);if(b(n.onShow)&&e.onAppShow&&e.onAppShow((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.__call_hook("onShow",n)})),b(n.onHide)&&e.onAppHide&&e.onAppHide((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.__call_hook("onHide",n)})),b(n.onLaunch)){var r=e.getLaunchOptionsSync&&e.getLaunchOptionsSync();t.__call_hook("onLaunch",r)}return t}Ft.push.apply(Ft,["onPullDownRefresh","onReachBottom","onAddToFavorites","onShareTimeline","onShareAppMessage","onPageScroll","onResize","onTabItemTap"]),["vibrate","preloadPage","unPreloadPage","loadSubPackage"].forEach((function(e){Pe[e]=!1})),[].forEach((function(t){var n=Pe[t]&&Pe[t].name?Pe[t].name:t;e.canIUse(n)||(Pe[t]=!1)}));var Wt={};"undefined"!==typeof Proxy?Wt=new Proxy({},{get:function(t,n){return x(t,n)?t[n]:fe[n]?fe[n]:Ye[n]?X(n,Ye[n]):Ne[n]?X(n,Ne[n]):je[n]?X(n,je[n]):Fe[n]?Fe[n]:X(n,De(n,e[n]))},set:function(e,t,n){return e[t]=n,!0}}):(Object.keys(fe).forEach((function(e){Wt[e]=fe[e]})),Object.keys(je).forEach((function(e){Wt[e]=X(e,je[e])})),Object.keys(Ne).forEach((function(e){Wt[e]=X(e,Ne[e])})),Object.keys(Fe).forEach((function(e){Wt[e]=Fe[e]})),Object.keys(Ye).forEach((function(e){Wt[e]=X(e,Ye[e])})),Object.keys(e).forEach((function(t){(x(e,t)||x(Pe,t))&&(Wt[t]=X(t,De(t,e[t])))}))),e.createApp=Lt,e.createPage=Ht,e.createComponent=zt,e.createSubpackageApp=Vt,e.createPlugin=Kt;var Jt=Wt,Gt=Jt;t.default=Gt}).call(this,n("3223")["default"],n("0ee4"))},e2bd:function(e){e.exports=JSON.parse('{"uni-load-more.contentdown":"上拉显示更多","uni-load-more.contentrefresh":"正在加载...","uni-load-more.contentnomore":"没有更多数据了"}')},e38a:function(e){e.exports=JSON.parse('{"uni-pagination.prevText":"précédente","uni-pagination.nextText":"suivante","uni-pagination.piecePerPage":"Articles/Pages"}')},e6db:function(e,t,n){var r=n("3b2d")["default"];e.exports=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},e7f2:function(e){e.exports=JSON.parse('{"uniCloud.component.add.success":"新增成功","uniCloud.component.update.success":"修改成功","uniCloud.component.update.showModal.title":"提示","uniCloud.component.update.showModal.content":"是否更新该数据","uniCloud.component.remove.showModal.title":"提示","uniCloud.component.remove.showModal.content":"是否刪除數據"}')},ed45:function(e,t){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports["default"]=e.exports},eddf:function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.setCache=t.removeCache=t.hasValidCache=t.getCacheKey=t.getCache=t.default=t.clearAllCache=t.cacheManager=t.CACHE_KEYS=void 0;var o=r(n("7eb4")),i=r(n("ee10")),a=r(n("7ca3")),s=r(n("67ad")),u=r(n("0bdb"));function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f="offline_checkin_records",d={USER_ROLE:"userRole",USER_INFO:"userInfo",PROJECT_OPTIONS:"project_options",STATUS_OPTIONS:"status_options",URGENCY_OPTIONS:"urgency_options",RESPONSIBLE_LIST:"responsible_list",RESPONSIBLE_MAP:"responsible_map"};t.CACHE_KEYS=d;var h={USER_ROLE:18e5,USER_INFO:18e5,PROJECT_OPTIONS:864e5,STATUS_OPTIONS:864e5,URGENCY_OPTIONS:864e5,RESPONSIBLE_LIST:6e5,RESPONSIBLE_MAP:6e5},p=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"zzps_",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6048e5;(0,s.default)(this,t),this.prefix=e,this.defaultCacheTime=n,this.cacheKeys=d}return(0,u.default)(t,[{key:"_getFullKey",value:function(e){return"".concat(this.prefix).concat(e)}},{key:"set",value:function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultCacheTime,o=this._getFullKey(t),i={value:n,expire:null!==r?Date.now()+r:null};try{e.setStorageSync(o,JSON.stringify(i))}catch(a){console.error("缓存设置失败:",a)}}},{key:"get",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=this._getFullKey(t);try{var o=e.getStorageSync(r);if(!o)return n;var i="string"===typeof o?JSON.parse(o):o,a=i.value,s=i.expire;return null!==s&&s<Date.now()?(this.remove(t),n):a}catch(u){return console.error("缓存获取失败:",u),n}}},{key:"remove",value:function(t){var n=this._getFullKey(t);try{e.removeStorageSync(n)}catch(r){console.error("缓存移除失败:",r)}}},{key:"clear",value:function(){var t=this,n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{if(n)e.clearStorageSync();else{var r=e.getStorageInfoSync(),o=r.keys;o.forEach((function(n){n.startsWith(t.prefix)&&e.removeStorageSync(n)}))}}catch(i){console.error("缓存清空失败:",i)}}},{key:"getInfo",value:function(){var t=this;try{var n=e.getStorageInfoSync(),r=n.keys.filter((function(e){return e.startsWith(t.prefix)}));return{size:n.currentSize,limit:n.limitSize,totalKeys:n.keys.length,prefixKeys:r.length,keys:r}}catch(o){return console.error("获取缓存信息失败:",o),{size:0,limit:0,totalKeys:0,prefixKeys:0,keys:[]}}}},{key:"has",value:function(t){var n=this._getFullKey(t);try{var r=e.getStorageSync(n);if(!r)return!1;var o="string"===typeof r?JSON.parse(r):r,i=o.expire;return!(null!==i&&i<Date.now())||(this.remove(t),!1)}catch(a){return console.error("缓存检查失败:",a),!1}}},{key:"setPermanent",value:function(e,t){this.set(e,t,null)}},{key:"keys",value:function(){var t=this;try{var n=e.getStorageInfoSync();return n.keys.filter((function(e){return e.startsWith(t.prefix)})).map((function(e){return e.slice(t.prefix.length)}))}catch(r){return console.error("获取缓存键失败:",r),[]}}},{key:"setMultiple",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.defaultCacheTime;for(var n in e)this.set(n,e[n],t)}},{key:"getMultiple",value:function(e){var t=this,n={};return e.forEach((function(e){n[e]=t.get(e)})),n}},{key:"removeMultiple",value:function(e){var t=this;e.forEach((function(e){t.remove(e)}))}},{key:"clearExpired",value:function(){var e=this;try{var t=this.keys();t.forEach((function(t){e.get(t)}))}catch(n){console.error("清理过期缓存失败:",n)}}},{key:"saveOfflineCheckin",value:function(e){if(!e||!e.task_id||!e.point_id||!e.round)return Promise.reject(new Error("打卡记录缺少必要字段"));try{var t=this.get(f,[]),n=l(l({},e),{},{offline_time:(new Date).toISOString(),sync_status:"pending"}),r=t.some((function(e){return e.task_id===n.task_id&&e.point_id===n.point_id&&e.round===n.round}));return r?Promise.reject(new Error("已存在相同的离线打卡记录")):(t.push(n),this.set(f,t),Promise.resolve({success:!0,message:"离线打卡记录已保存",id:t.length-1}))}catch(o){return Promise.reject(o)}}},{key:"getOfflineCheckins",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"all";try{var t=this.get(f,[]);return"all"===e?t:t.filter((function(t){return t.sync_status===e}))}catch(n){return console.error("获取离线打卡记录失败:",n),[]}}},{key:"updateOfflineCheckinStatus",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{var r=this.get(f,[]);return!!r[e]&&(r[e].sync_status=t,n&&(r[e].record_id=n),"success"!==t&&"failed"!==t||(r[e].sync_time=(new Date).toISOString()),this.set(f,r),!0)}catch(o){return console.error("更新离线打卡记录状态失败:",o),!1}}},{key:"clearSyncedOfflineCheckins",value:function(){try{var e=this.get(f,[]),t=e.filter((function(e){return"pending"===e.sync_status}));return this.set(f,t),e.length-t.length}catch(n){return console.error("清除已同步的离线打卡记录失败:",n),0}}},{key:"getUserRoles",value:function(){var e=(0,i.default)(o.default.mark((function e(t,n){var r,i;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=this._getFullKey(this.cacheKeys.USER_ROLE)+(t?"_".concat(t):""),i=this.get(r),i||!n){e.next=8;break}return console.log("🔄 用户角色缓存未命中，从服务器获取..."),e.next=6,n();case 6:i=e.sent,i&&this.set(r,i);case 8:return e.abrupt("return",i||[]);case 9:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"getResponsibleList",value:function(){var e=(0,i.default)(o.default.mark((function e(t){var n,r;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=this.get(this.cacheKeys.RESPONSIBLE_LIST),n||!t){e.next=7;break}return console.log("🔄 负责人列表缓存未命中，从服务器获取..."),e.next=5,t();case 5:n=e.sent,n&&(this.set(this.cacheKeys.RESPONSIBLE_LIST,n),r=n.reduce((function(e,t){return e[t._id]=t.nickname||t.username||"-",e}),{}),this.set(this.cacheKeys.RESPONSIBLE_MAP,r));case 7:return e.abrupt("return",n||[]);case 8:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getResponsibleMap",value:function(){return this.get(this.cacheKeys.RESPONSIBLE_MAP)||{}}},{key:"getProjectOptions",value:function(){var e=(0,i.default)(o.default.mark((function e(t){var n;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=this.get(this.cacheKeys.PROJECT_OPTIONS),n||!t){e.next=7;break}return console.log("🔄 项目选项缓存未命中，使用默认数据..."),e.next=5,t();case 5:n=e.sent,n&&this.set(this.cacheKeys.PROJECT_OPTIONS,n);case 7:return e.abrupt("return",n||[{text:"全部",value:""},{text:"安全找茬",value:"安全找茬"},{text:"设备找茬",value:"设备找茬"},{text:"其他找茬",value:"其他找茬"}]);case 8:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getStatusOptions",value:function(){var e=this.get(this.cacheKeys.STATUS_OPTIONS);return e||(console.log("🔄 状态选项缓存未命中，使用默认数据..."),e=[{text:"全部",value:""},{text:"待主管审核",value:"pending_supervisor"},{text:"主管已通过",value:"approved_supervisor"},{text:"需要例会讨论",value:"meeting_required"},{text:"待副厂长审核",value:"pending_pm"},{text:"副厂长已通过",value:"approved_pm"},{text:"待厂长审核",value:"pending_gm"},{text:"待指派负责人",value:"gm_approved_pending_assign"},{text:"已指派负责人",value:"assigned_to_responsible"},{text:"待厂长确认",value:"completed_by_responsible"},{text:"已完成",value:"final_completed"},{text:"已终止",value:"terminated"}],this.set(this.cacheKeys.STATUS_OPTIONS,e)),e}},{key:"getUrgencyOptions",value:function(){var e=this.get(this.cacheKeys.URGENCY_OPTIONS);return e||(console.log("🔄 紧急程度选项缓存未命中，使用默认数据..."),e=[{text:"全部",value:""},{text:"正常",value:"normal"},{text:"警告",value:"warning"},{text:"紧急",value:"urgent"}],this.set(this.cacheKeys.URGENCY_OPTIONS,e)),e}},{key:"clearUserRelatedCache",value:function(){console.log("🧹 清除用户相关缓存..."),this.remove(this.cacheKeys.USER_ROLE),this.remove(this.cacheKeys.USER_INFO)}},{key:"clearResponsibleCache",value:function(){console.log("🧹 清除负责人相关缓存..."),this.remove(this.cacheKeys.RESPONSIBLE_LIST),this.remove(this.cacheKeys.RESPONSIBLE_MAP)}}]),t}(),g=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return t?"".concat("zzps_").concat(e,"_").concat(t):"".concat("zzps_").concat(e)};t.getCacheKey=g;t.setCache=function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{var o=r||h[t]||18e5,i={data:n,timestamp:Date.now(),expiry:Date.now()+o};return e.setStorageSync(g(t),i),console.log("✅ 缓存设置成功: ".concat(t,", 过期时间: ").concat(new Date(i.expiry).toLocaleString())),!0}catch(a){return console.error("❌ 缓存设置失败: ".concat(t),a),!1}};t.getCache=function(t){try{var n=e.getStorageSync(g(t));return n?Date.now()>n.expiry?(console.log("⏰ 缓存已过期: ".concat(t)),v(t),null):(console.log("✅ 缓存命中: ".concat(t)),n.data):(console.log("📭 缓存未找到: ".concat(t)),null)}catch(r){return console.error("❌ 缓存获取失败: ".concat(t),r),null}};var v=function(t){try{return e.removeStorageSync(g(t)),console.log("🗑️ 缓存已清除: ".concat(t)),!0}catch(n){return console.error("❌ 缓存清除失败: ".concat(t),n),!1}};t.removeCache=v;t.hasValidCache=function(t){try{var n=e.getStorageSync(g(t));return n&&Date.now()<=n.expiry}catch(r){return!1}};t.clearAllCache=function(){try{return Object.values(d).forEach((function(e){v(e)})),console.log("🧹 所有缓存已清除"),!0}catch(e){return console.error("❌ 清除所有缓存失败",e),!1}};var m=new p;t.cacheManager=m;var y=m;t.default=y}).call(this,n("df3c")["default"])},ee10:function(e,t){function n(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(c){return void n(c)}s.done?t(u):Promise.resolve(u).then(r,o)}e.exports=function(e){return function(){var t=this,r=arguments;return new Promise((function(o,i){var a=e.apply(t,r);function s(e){n(a,o,i,s,u,"next",e)}function u(e){n(a,o,i,s,u,"throw",e)}s(void 0)}))}},e.exports.__esModule=!0,e.exports["default"]=e.exports},f991:function(e,t,n){"use strict";(function(e,r){var o=n("47a9"),i=n("3b2d");Object.defineProperty(t,"__esModule",{value:!0}),t.database=void 0,t.handleTokenInvalid=f;var a=n("423e"),s=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!==typeof e)return{default:e};var n=c(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(r,a,s):r[a]=e[a]}r.default=e,n&&n.set(e,r);return r}(n("eddf")),u=o(n("7e11"));function c(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(c=function(e){return e?n:t})(e)}var l=["TOKEN_INVALID","TOKEN_EXPIRED","INVALID_TOKEN","uni-id-token-expired","uni-id-check-token-failed"];function f(){e.removeStorageSync("uni_id_token"),e.removeStorageSync("uni_id_token_expired"),e.removeStorageSync("uni_id_user"),e.removeStorageSync("uni-id-pages-userInfo"),e.removeStorageSync((0,s.getCacheKey)(s.CACHE_KEYS.USER_ROLE)),a.mutations.setUserInfo({},{cover:!0}),s.default.remove("userRole"),s.default.remove("user_info"),s.default.remove("projectOptions"),u.default.clearBadge(),function(){try{var t=e.getStorageInfoSync(),n=t.keys,r=["user_info_","user_mgmt_"],o=["_DC_STAT_UUID",(0,s.getCacheKey)("recent_pages"),"last_app_start_time","uni-id-pages-userInfo"];n.forEach((function(t){if(!o.includes(t)){var n=r.some((function(e){return t===e||t.startsWith(e)}));n&&(e.removeStorageSync(t))}}))}catch(i){console.error("拦截器清除敏感缓存失败:",i)}}(),e.$emit("token-invalid")}var d=r.database();d.interceptorAdd("callFunction",{invoke:function(e){console.log("发起请求:",e)},success:function(e){return console.log("请求成功:",e),e.result&&(l.includes(e.result.code)||l.includes(e.result.errCode))&&(console.log("检测到token失效，开始处理..."),f()),e},fail:function(e){return console.error("请求失败:",e),e.message&&(e.message.includes("token")||e.message.includes("TOKEN"))&&(console.log("检测到token相关错误，开始处理..."),f()),e},complete:function(e){return console.log("请求完成"),e}});var h=d;t.database=h}).call(this,n("df3c")["default"],n("861b")["uniCloud"])},fb20:function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.checkUpdate=s,t.chooseLocation=v,t.default=void 0,t.getLaunchOptions=u,t.getLocation=p,t.getSetting=b,t.getUserProfile=f,t.login=m,t.openLocation=g,t.openSetting=w,t.requestPayment=h,t.saveImageToPhotosAlbum=d,t.shareAppMessage=c,t.subscribeMessage=l;var o=r(n("7ca3"));function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){(0,o.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(){if(e.canIUse("getUpdateManager")){var t=e.getUpdateManager();t.onCheckForUpdate((function(e){e.hasUpdate})),t.onUpdateReady((function(){e.showModal({title:"更新提示",content:"新版本已经准备好，是否重启应用？",success:function(e){e.confirm&&t.applyUpdate()}})})),t.onUpdateFailed((function(){e.showToast({title:"新版本下载失败，请检查网络",icon:"none"})}))}else e.showToast({title:"当前微信版本过低，无法使用该功能，请升级到最新微信版本",icon:"none"})}function u(){return e.getLaunchOptionsSync()}function c(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={title:"株水小智",path:"/pages/index/index",imageUrl:"/static/logo.png"};return a(a({},t),e)}function l(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.length?new Promise((function(n,r){e.requestSubscribeMessage({tmplIds:t,success:function(e){n(e)},fail:function(e){r(e)}})})):Promise.reject(new Error("模板ID列表不能为空"))}function f(){return new Promise((function(t,n){e.getUserProfile({desc:"用于完善用户资料",success:function(e){t(e)},fail:function(e){n(e)}})}))}function d(t){return new Promise((function(n,r){e.saveImageToPhotosAlbum({filePath:t,success:function(e){n(e)},fail:function(e){r(e)}})}))}function h(t){return new Promise((function(n,r){e.requestPayment(a(a({},t),{},{success:function(e){n(e)},fail:function(e){r(e)}}))}))}function p(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n={type:"gcj02",isHighAccuracy:!0,highAccuracyExpireTime:3e3},r=a(a({},n),t);return new Promise((function(t,n){e.getLocation(a(a({},r),{},{success:function(e){t(e)},fail:function(e){n(e)}}))}))}function g(t){return new Promise((function(n,r){e.openLocation(a(a({},t),{},{success:function(e){n(e)},fail:function(e){r(e)}}))}))}function v(){return new Promise((function(t,n){e.chooseLocation({success:function(e){t(e)},fail:function(e){n(e)}})}))}function m(){return new Promise((function(t,n){e.login({success:function(e){t(e)},fail:function(e){n(e)}})}))}var y={aggressive:{cacheTimeout:36e5,description:"1小时缓存，最大减少API调用"},balanced:{cacheTimeout:12e4,description:"2分钟缓存，性能与实时性平衡，推荐使用"},conservative:{cacheTimeout:2e4,description:"20秒缓存，高实时性"},debug:{cacheTimeout:5e3,description:"5秒缓存，便于开发测试"}}.aggressive,_={data:null,lastCheck:0,cacheTimeout:y.cacheTimeout};function b(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r,o){var i=Date.now();if(!n&&null!==_.data&&i-_.lastCheck<_.cacheTimeout)return console.log("🗂️ 使用授权设置缓存"),void r(t?_.data[t]||!1:_.data);console.log("🔍 获取授权设置..."),e.getSetting({success:function(e){_.data=e.authSetting,_.lastCheck=i,console.log("✅ 授权设置获取完成"),r(t?e.authSetting[t]||!1:e.authSetting)},fail:function(e){console.log("❌ 授权设置获取失败"),o(e)}})}))}function w(){return new Promise((function(t,n){e.openSetting({success:function(e){t(e.authSetting)},fail:function(e){n(e)}})}))}var k={checkUpdate:s,getLaunchOptions:u,shareAppMessage:c,subscribeMessage:l,getUserProfile:f,saveImageToPhotosAlbum:d,requestPayment:h,getLocation:p,openLocation:g,chooseLocation:v,login:m,getSetting:b,openSetting:w};t.default=k}).call(this,n("df3c")["default"])}}]);
(function(e){function n(n){for(var t,r,s=n[0],p=n[1],g=n[2],c=0,u=[];c<s.length;c++)r=s[c],Object.prototype.hasOwnProperty.call(i,r)&&i[r]&&u.push(i[r][0]),i[r]=0;for(t in p)Object.prototype.hasOwnProperty.call(p,t)&&(e[t]=p[t]);d&&d(n);while(u.length)u.shift()();return o.push.apply(o,g||[]),a()}function a(){for(var e,n=0;n<o.length;n++){for(var a=o[n],t=!0,s=1;s<a.length;s++){var p=a[s];0!==i[p]&&(t=!1)}t&&(o.splice(n--,1),e=r(r.s=a[0]))}return e}var t={},i={index:0},o=[];function r(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,r),a.l=!0,a.exports}r.e=function(e){var n=[],a=i[e];if(0!==a)if(a)n.push(a[2]);else{var t=new Promise((function(n,t){a=i[e]=[n,t]}));n.push(a[2]=t);var o,s=document.createElement("script");s.charset="utf-8",s.timeout=120,r.nc&&s.setAttribute("nonce",r.nc),s.src=function(e){return r.p+"static/js/"+({"pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013":"pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013","pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68":"pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68","pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-batch-manager~pages-patrol_pkg-record-index":"pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-batch-manager~pages-patrol_pkg-record-index","pages-feedback-list~pages-ucenter_pkg-user-management":"pages-feedback-list~pages-ucenter_pkg-user-management","pages-feedback-list":"pages-feedback-list","pages-honor_pkg-admin-batch-manager":"pages-honor_pkg-admin-batch-manager","pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978":"pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978","pages-feedback_pkg-edit":"pages-feedback_pkg-edit","pages-notice-add~pages-notice-detail~pages-notice-edit":"pages-notice-add~pages-notice-detail~pages-notice-edit","pages-notice-add":"pages-notice-add","pages-notice-edit":"pages-notice-edit","pages-ucenter_pkg-complete-task":"pages-ucenter_pkg-complete-task","uni_modules-uni-id-pages-pages-userinfo-change_pwd-change_pwd":"uni_modules-uni-id-pages-pages-userinfo-change_pwd-change_pwd","pages-honor_pkg-admin-add-record~pages-patrol-index~pages-patrol_pkg-task-index~pages-ucenter_pkg-us~cfd09d99":"pages-honor_pkg-admin-add-record~pages-patrol-index~pages-patrol_pkg-task-index~pages-ucenter_pkg-us~cfd09d99","pages-honor_pkg-admin-add-record":"pages-honor_pkg-admin-add-record","uni_modules-uni-id-pages-pages-login-login-withoutpwd~uni_modules-uni-id-pages-pages-login-login-withpwd":"uni_modules-uni-id-pages-pages-login-login-withoutpwd~uni_modules-uni-id-pages-pages-login-login-withpwd","uni_modules-uni-id-pages-pages-login-login-withoutpwd":"uni_modules-uni-id-pages-pages-login-login-withoutpwd","uni_modules-uni-id-pages-pages-login-login-withpwd":"uni_modules-uni-id-pages-pages-login-login-withpwd","pages-honor_pkg-admin-index":"pages-honor_pkg-admin-index","pages-honor_pkg-admin-type-manager":"pages-honor_pkg-admin-type-manager","pages-feedback_pkg-examine":"pages-feedback_pkg-examine","pages-ucenter_pkg-user-management":"pages-ucenter_pkg-user-management","uni_modules-uni-id-pages-pages-userinfo-userinfo":"uni_modules-uni-id-pages-pages-userinfo-userinfo","pages-honor_pkg-gallery-index":"pages-honor_pkg-gallery-index","pages-notice-detail~pages-notice-list":"pages-notice-detail~pages-notice-list","pages-notice-detail":"pages-notice-detail","pages-notice-list":"pages-notice-list","pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164":"pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164","pages-patrol_pkg-point-add":"pages-patrol_pkg-point-add","pages-patrol_pkg-point-edit":"pages-patrol_pkg-point-edit","pages-patrol_pkg-route-add":"pages-patrol_pkg-route-add","pages-patrol_pkg-route-edit":"pages-patrol_pkg-route-edit","pages-patrol_pkg-shift-add":"pages-patrol_pkg-shift-add","pages-patrol_pkg-shift-edit":"pages-patrol_pkg-shift-edit","pages-patrol_pkg-record-index":"pages-patrol_pkg-record-index","pages-patrol-index~pages-patrol_pkg-checkin-index":"pages-patrol-index~pages-patrol_pkg-checkin-index","pages-patrol-index":"pages-patrol-index","pages-patrol_pkg-task-index":"pages-patrol_pkg-task-index","pages-patrol_pkg-checkin-index":"pages-patrol_pkg-checkin-index","pages-patrol_pkg-point-detail":"pages-patrol_pkg-point-detail","pages-patrol_pkg-point-index":"pages-patrol_pkg-point-index","pages-patrol_pkg-point-qrcode~pages-patrol_pkg-point-qrcode-batch":"pages-patrol_pkg-point-qrcode~pages-patrol_pkg-point-qrcode-batch","pages-patrol_pkg-point-qrcode-batch":"pages-patrol_pkg-point-qrcode-batch","pages-patrol_pkg-record-detail":"pages-patrol_pkg-record-detail","pages-patrol_pkg-record-route-detail":"pages-patrol_pkg-record-route-detail","pages-patrol_pkg-route-detail":"pages-patrol_pkg-route-detail","pages-patrol_pkg-route-index":"pages-patrol_pkg-route-index","pages-patrol_pkg-shift-detail":"pages-patrol_pkg-shift-detail","pages-patrol_pkg-shift-index":"pages-patrol_pkg-shift-index","pages-patrol_pkg-task-add":"pages-patrol_pkg-task-add","pages-patrol_pkg-task-batch-add":"pages-patrol_pkg-task-batch-add","pages-patrol_pkg-task-detail":"pages-patrol_pkg-task-detail","pages-patrol_pkg-task-edit":"pages-patrol_pkg-task-edit","pages-ucenter-ucenter":"pages-ucenter-ucenter","pages-ucenter_pkg-export-excel":"pages-ucenter_pkg-export-excel","pages-ucenter_pkg-responsible-tasks":"pages-ucenter_pkg-responsible-tasks","pages-ucenter_pkg-todo":"pages-ucenter_pkg-todo","pages-index-index":"pages-index-index","pages-info_pkg-privacy":"pages-info_pkg-privacy","pages-info_pkg-user-guide":"pages-info_pkg-user-guide","pages-patrol_pkg-point-qrcode":"pages-patrol_pkg-point-qrcode","pages-ucenter_pkg-gm-supervision":"pages-ucenter_pkg-gm-supervision"}[e]||e)+"."+{"pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013":"08e660d3","pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68":"0de74a75","pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-batch-manager~pages-patrol_pkg-record-index":"c769e536","pages-feedback-list~pages-ucenter_pkg-user-management":"feaf9f08","pages-feedback-list":"973a7c0a","pages-honor_pkg-admin-batch-manager":"2b9ead71","pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978":"1aa33314","pages-feedback_pkg-edit":"dd18c56d","pages-notice-add~pages-notice-detail~pages-notice-edit":"92c24a06","pages-notice-add":"449741bf","pages-notice-edit":"ce4bb1c8","pages-ucenter_pkg-complete-task":"40d16434","uni_modules-uni-id-pages-pages-userinfo-change_pwd-change_pwd":"72ab324f","pages-honor_pkg-admin-add-record~pages-patrol-index~pages-patrol_pkg-task-index~pages-ucenter_pkg-us~cfd09d99":"50e5764e","pages-honor_pkg-admin-add-record":"fded48ec","uni_modules-uni-id-pages-pages-login-login-withoutpwd~uni_modules-uni-id-pages-pages-login-login-withpwd":"cf8785be","uni_modules-uni-id-pages-pages-login-login-withoutpwd":"3c2434cd","uni_modules-uni-id-pages-pages-login-login-withpwd":"2d90b720","pages-honor_pkg-admin-index":"dd4d34f1","pages-honor_pkg-admin-type-manager":"175405ab","pages-feedback_pkg-examine":"46764cb0","pages-ucenter_pkg-user-management":"dea9ed95","uni_modules-uni-id-pages-pages-userinfo-userinfo":"f71e84b6","pages-honor_pkg-gallery-index":"446a2882","pages-notice-detail~pages-notice-list":"1c106bb4","pages-notice-detail":"f1bcb5d7","pages-notice-list":"ee3159ae","pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164":"8ac477cf","pages-patrol_pkg-point-add":"89d22643","pages-patrol_pkg-point-edit":"c86088d3","pages-patrol_pkg-route-add":"224c7d32","pages-patrol_pkg-route-edit":"aa40e61a","pages-patrol_pkg-shift-add":"9e398deb","pages-patrol_pkg-shift-edit":"d783af24","pages-patrol_pkg-record-index":"5d21e872","pages-patrol-index~pages-patrol_pkg-checkin-index":"edd0f54a","pages-patrol-index":"5b6ef6be","pages-patrol_pkg-task-index":"6f31f8bb","pages-patrol_pkg-checkin-index":"5edcb8a8","pages-patrol_pkg-point-detail":"287c3e45","pages-patrol_pkg-point-index":"bb72fe1d","pages-patrol_pkg-point-qrcode~pages-patrol_pkg-point-qrcode-batch":"342f80bd","pages-patrol_pkg-point-qrcode-batch":"5878598f","pages-patrol_pkg-record-detail":"1b449242","pages-patrol_pkg-record-route-detail":"89899324","pages-patrol_pkg-route-detail":"f4b85d2d","pages-patrol_pkg-route-index":"3a9e59e4","pages-patrol_pkg-shift-detail":"2396c77e","pages-patrol_pkg-shift-index":"0fa4411b","pages-patrol_pkg-task-add":"4b02600e","pages-patrol_pkg-task-batch-add":"6f6a0582","pages-patrol_pkg-task-detail":"e867873e","pages-patrol_pkg-task-edit":"8f5dd9da","pages-ucenter-ucenter":"2c757962","pages-ucenter_pkg-export-excel":"daf3f377","pages-ucenter_pkg-responsible-tasks":"058090a6","pages-ucenter_pkg-todo":"1203bdbc","pages-index-index":"02bc5ba3","pages-info_pkg-privacy":"e1b0a509","pages-info_pkg-user-guide":"78e0750c","pages-patrol_pkg-point-qrcode":"c6091261","pages-ucenter_pkg-gm-supervision":"a8376584"}[e]+".js"}(e);var p=new Error;o=function(n){s.onerror=s.onload=null,clearTimeout(g);var a=i[e];if(0!==a){if(a){var t=n&&("load"===n.type?"missing":n.type),o=n&&n.target&&n.target.src;p.message="Loading chunk "+e+" failed.\n("+t+": "+o+")",p.name="ChunkLoadError",p.type=t,p.request=o,a[1](p)}i[e]=void 0}};var g=setTimeout((function(){o({type:"timeout",target:s})}),12e4);s.onerror=s.onload=o,document.head.appendChild(s)}return Promise.all(n)},r.m=e,r.c=t,r.d=function(e,n,a){r.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:a})},r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,n){if(1&n&&(e=r(e)),8&n)return e;if(4&n&&"object"===typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(r.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var t in e)r.d(a,t,function(n){return e[n]}.bind(null,t));return a},r.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(n,"a",n),n},r.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},r.p="/",r.oe=function(e){throw console.error(e),e};var s=window["webpackJsonp"]=window["webpackJsonp"]||[],p=s.push.bind(s);s.push=n,s=s.slice();for(var g=0;g<s.length;g++)n(s[g]);var d=p;o.push([0,"chunk-vendors"]),a()})({0:function(e,n,a){e.exports=a("95b1")},"0c73":function(e,n,a){"use strict";a("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={pages:[{path:"pages/index/index",style:{navigationBarTitleText:"株水小智",enablePullDownRefresh:!1}},{path:"pages/patrol/index",style:{navigationBarTitleText:"巡视打卡",enablePullDownRefresh:!1}},{path:"pages/feedback/list",style:{navigationBarTitleText:"问题反馈",enablePullDownRefresh:!0}},{path:"pages/ucenter/ucenter",style:{navigationBarTitleText:"用户中心",enablePullDownRefresh:!0}}],subPackages:[{root:"pages/feedback_pkg",pages:[{path:"edit",style:{navigationBarTitleText:"找茬编辑"}},{path:"examine",style:{navigationBarTitleText:"审核流程"}}]},{root:"pages/ucenter_pkg",pages:[{path:"todo",style:{navigationBarTitleText:"我的待办",enablePullDownRefresh:!0}},{path:"export-excel",style:{navigationBarTitleText:"导出Excel"}},{path:"user-management",style:{navigationBarTitleText:"用户管理",enablePullDownRefresh:!0}},{path:"responsible-tasks",style:{navigationBarTitleText:"我的任务",enablePullDownRefresh:!0}},{path:"gm-supervision",style:{navigationBarTitleText:"任务监督",enablePullDownRefresh:!0}},{path:"complete-task",style:{navigationBarTitleText:"完成任务",enablePullDownRefresh:!1}}]},{root:"pages/notice",pages:[{path:"add",style:{navigationBarTitleText:"公告新增",enablePullDownRefresh:!1}},{path:"edit",style:{navigationBarTitleText:"公告编辑",enablePullDownRefresh:!1}},{path:"list",style:{navigationBarTitleText:"公告通知",enablePullDownRefresh:!0}},{path:"detail",style:{navigationBarTitleText:"公告详细"}}]},{root:"uni_modules/uni-id-pages/pages",pages:[{path:"login/login-withoutpwd",style:{navigationBarTitleText:"微信一键登录"}},{path:"login/login-withpwd",style:{navigationBarTitleText:"账号密码登录"}},{path:"userinfo/change_pwd/change_pwd",style:{navigationBarTitleText:"密码修改"}},{path:"userinfo/userinfo",style:{navigationBarTitleText:"个人资料"}}]},{root:"pages/honor_pkg",pages:[{path:"gallery/index",style:{navigationBarTitleText:"荣誉展厅",navigationStyle:"custom",enablePullDownRefresh:!0}},{path:"admin/index",style:{navigationBarTitleText:"荣誉管理",navigationStyle:"custom"}},{path:"admin/add-record",style:{navigationBarTitleText:"添加表彰记录",navigationStyle:"custom"}},{path:"admin/batch-manager",style:{navigationBarTitleText:"智能批次管理",navigationStyle:"custom"}},{path:"admin/type-manager",style:{navigationBarTitleText:"荣誉类型管理",navigationStyle:"custom"}}]},{root:"pages/info_pkg",pages:[{path:"user-guide",style:{navigationBarTitleText:"用户指南",enablePullDownRefresh:!1}},{path:"privacy",style:{navigationBarTitleText:"隐私政策",enablePullDownRefresh:!1}}]},{root:"pages/patrol_pkg",pages:[{path:"checkin/index",style:{navigationBarTitleText:"打卡签到",navigationStyle:"custom",enablePullDownRefresh:!1,disableScroll:!0}},{path:"point/index",style:{navigationBarTitleText:"点位管理",enablePullDownRefresh:!1}},{path:"point/add",style:{navigationBarTitleText:"添加点位",enablePullDownRefresh:!1}},{path:"point/edit",style:{navigationBarTitleText:"编辑点位",enablePullDownRefresh:!1}},{path:"point/detail",style:{navigationBarTitleText:"点位详情",enablePullDownRefresh:!1}},{path:"route/index",style:{navigationBarTitleText:"线路管理",enablePullDownRefresh:!1}},{path:"route/add",style:{navigationBarTitleText:"添加线路",enablePullDownRefresh:!1}},{path:"route/edit",style:{navigationBarTitleText:"编辑线路",enablePullDownRefresh:!1}},{path:"route/detail",style:{navigationBarTitleText:"线路详情",enablePullDownRefresh:!1}},{path:"shift/index",style:{navigationBarTitleText:"班次管理",enablePullDownRefresh:!1}},{path:"shift/add",style:{navigationBarTitleText:"添加班次",enablePullDownRefresh:!1}},{path:"shift/edit",style:{navigationBarTitleText:"编辑班次",enablePullDownRefresh:!1}},{path:"shift/detail",style:{navigationBarTitleText:"班次详情",enablePullDownRefresh:!1}},{path:"task/index",style:{navigationBarTitleText:"任务管理",enablePullDownRefresh:!0}},{path:"task/add",style:{navigationBarTitleText:"添加任务",enablePullDownRefresh:!1}},{path:"task/batch-add",style:{navigationBarTitleText:"批量添加任务",enablePullDownRefresh:!1}},{path:"task/detail",style:{navigationBarTitleText:"任务详情",enablePullDownRefresh:!1}},{path:"task/edit",style:{navigationBarTitleText:"编辑任务",enablePullDownRefresh:!1}},{path:"record/index",style:{navigationBarTitleText:"巡视记录",enablePullDownRefresh:!1}},{path:"record/detail",style:{navigationBarTitleText:"记录详情",enablePullDownRefresh:!1}},{path:"record/route-detail",style:{navigationBarTitleText:"线路记录",enablePullDownRefresh:!1}},{path:"point/qrcode",style:{navigationBarTitleText:"二维码管理",enablePullDownRefresh:!1}},{path:"point/qrcode-batch",style:{navigationBarTitleText:"批量二维码管理",enablePullDownRefresh:!1}}]}],globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"株水小智",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"},tabBar:{color:"#7A7E83",selectedColor:"#1677FF",borderStyle:"black",backgroundColor:"#ffffff",list:[{pagePath:"pages/index/index",iconPath:"static/tabbar/home.png",selectedIconPath:"static/tabbar/home_active.png",text:"找茬"},{pagePath:"pages/feedback/list",iconPath:"static/tabbar/feedback.png",selectedIconPath:"static/tabbar/feedback_active.png",text:"反馈"},{pagePath:"pages/patrol/index",iconPath:"static/tabbar/patrol.png",selectedIconPath:"static/tabbar/patrol_active.png",text:"巡视"},{pagePath:"pages/ucenter/ucenter",iconPath:"static/tabbar/user.png",selectedIconPath:"static/tabbar/user_active.png",text:"我的"}]},uniIdRouter:{loginPage:"uni_modules/uni-id-pages/pages/login/login-withpwd",needLogin:["pages/notice/.*","pages/feedback_pkg/.*","pages/ucenter_pkg/.*","pages/patrol_pkg/.*","pages/honor_pkg/.*","uni_modules/uni-id-pages/pages/userinfo/.*"],resToLogin:!1}}},"2e52":function(e,n,a){"use strict";a.r(n);var t=a("bd7e"),i=a("a267");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(n,e,(function(){return i[e]}))}(o);a("8300");var r=a("828b"),s=Object(r["a"])(i["default"],t["b"],t["c"],!1,null,null,null,!1,t["a"],void 0);n["default"]=s.exports},"30eb":function(e,n,a){"use strict";a("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={debug:!1,isAdmin:!1,loginTypes:["username"],agreements:{serviceUrl:"/pages/info_pkg/user-guide",privacyUrl:"/pages/info_pkg/privacy",scope:["register","login","realNameVerify"]},appid:{weixin:{h5:"xxxxxx",web:"xxxxxx"}},passwordStrength:"weak",setPasswordAfterLogin:!1,loginSuccess:{type:"toast",options:{title:"登录成功",icon:"success",duration:2e3,background:"none"}}}},"357b":function(e,n,a){"use strict";(function(e){var n=a("f5bd").default;a("473f"),a("bf0f"),a("de6c"),a("5c47"),a("a1c1");var t=n(a("9b8e")),i={keys:function(){return[]}};e["____2339AC9____"]=!0,delete e["____2339AC9____"],e.__uniConfig={globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"株水小智",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"},tabBar:{color:"#7A7E83",selectedColor:"#1677FF",borderStyle:"black",backgroundColor:"#ffffff",list:[{pagePath:"pages/index/index",iconPath:"static/tabbar/home.png",selectedIconPath:"static/tabbar/home_active.png",text:"找茬",redDot:!1,badge:""},{pagePath:"pages/feedback/list",iconPath:"static/tabbar/feedback.png",selectedIconPath:"static/tabbar/feedback_active.png",text:"反馈",redDot:!1,badge:""},{pagePath:"pages/patrol/index",iconPath:"static/tabbar/patrol.png",selectedIconPath:"static/tabbar/patrol_active.png",text:"巡视",redDot:!1,badge:""},{pagePath:"pages/ucenter/ucenter",iconPath:"static/tabbar/user.png",selectedIconPath:"static/tabbar/user_active.png",text:"我的",redDot:!1,badge:""}]},uniIdRouter:{loginPage:"uni_modules/uni-id-pages/pages/login/login-withpwd",needLogin:["pages/notice/.*","pages/feedback_pkg/.*","pages/ucenter_pkg/.*","pages/patrol_pkg/.*","pages/honor_pkg/.*","uni_modules/uni-id-pages/pages/userinfo/.*"],resToLogin:!1}},e.__uniConfig.compilerVersion="4.75",e.__uniConfig.darkmode=!1,e.__uniConfig.themeConfig={},e.__uniConfig.uniPlatform="h5",e.__uniConfig.appId="__UNI__2339AC9",e.__uniConfig.appName="株水小智",e.__uniConfig.appVersion="3.3.3",e.__uniConfig.appVersionCode="333",e.__uniConfig.router={mode:"hash",base:"/"},e.__uniConfig.publicPath="/",e.__uniConfig["async"]={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4},e.__uniConfig.debug=!1,e.__uniConfig.networkTimeout={request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},e.__uniConfig.sdkConfigs={maps:{amap:{key:"0bfa2c6ea6d25dfa695a64d61ff76e42",securityJsCode:"",serviceHost:""}}},e.__uniConfig.qqMapKey=void 0,e.__uniConfig.googleMapKey=void 0,e.__uniConfig.aMapKey="0bfa2c6ea6d25dfa695a64d61ff76e42",e.__uniConfig.aMapSecurityJsCode="",e.__uniConfig.aMapServiceHost="",e.__uniConfig.locale="",e.__uniConfig.fallbackLocale=void 0,e.__uniConfig.locales=i.keys().reduce((function(e,n){var a=n.replace(/\.\/(uni-app.)?(.*).json/,"$2"),t=i(n);return Object.assign(e[a]||(e[a]={}),t.common||t),e}),{}),e.__uniConfig.nvue={"flex-direction":"column"},e.__uniConfig.__webpack_chunk_load__=a.e,t.default.component("pages-index-index",(function(e){var n={component:a.e("pages-index-index").then(function(){return e(a("2d87"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol-index",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-honor_pkg-admin-add-record~pages-patrol-index~pages-patrol_pkg-task-index~pages-ucenter_pkg-us~cfd09d99"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index"),a.e("pages-patrol-index")]).then(function(){return e(a("127f"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-feedback-list",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-batch-manager~pages-patrol_pkg-record-index"),a.e("pages-feedback-list~pages-ucenter_pkg-user-management"),a.e("pages-feedback-list")]).then(function(){return e(a("726d"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-ucenter-ucenter",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-ucenter-ucenter")]).then(function(){return e(a("ff36"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-feedback_pkg-edit",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-batch-manager~pages-patrol_pkg-record-index"),a.e("pages-feedback_pkg-edit")]).then(function(){return e(a("e812"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-feedback_pkg-examine",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback_pkg-examine")]).then(function(){return e(a("a720"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-ucenter_pkg-todo",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-ucenter_pkg-todo")]).then(function(){return e(a("3af0"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-ucenter_pkg-export-excel",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-ucenter_pkg-export-excel")]).then(function(){return e(a("0bb9"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-ucenter_pkg-user-management",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-honor_pkg-admin-add-record~pages-patrol-index~pages-patrol_pkg-task-index~pages-ucenter_pkg-us~cfd09d99"),a.e("pages-feedback-list~pages-ucenter_pkg-user-management"),a.e("pages-ucenter_pkg-user-management")]).then(function(){return e(a("4bd8"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-ucenter_pkg-responsible-tasks",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-ucenter_pkg-responsible-tasks")]).then(function(){return e(a("1325"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-ucenter_pkg-gm-supervision",(function(e){var n={component:a.e("pages-ucenter_pkg-gm-supervision").then(function(){return e(a("8b9d"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-ucenter_pkg-complete-task",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978"),a.e("pages-ucenter_pkg-complete-task")]).then(function(){return e(a("0538"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-notice-add",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978"),a.e("pages-notice-add~pages-notice-detail~pages-notice-edit"),a.e("pages-notice-add")]).then(function(){return e(a("ac41"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-notice-edit",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978"),a.e("pages-notice-add~pages-notice-detail~pages-notice-edit"),a.e("pages-notice-edit")]).then(function(){return e(a("ec21"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-notice-list",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-notice-detail~pages-notice-list"),a.e("pages-notice-list")]).then(function(){return e(a("1e0c"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-notice-detail",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-notice-add~pages-notice-detail~pages-notice-edit"),a.e("pages-notice-detail~pages-notice-list"),a.e("pages-notice-detail")]).then(function(){return e(a("321b"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("uni_modules-uni-id-pages-pages-login-login-withoutpwd",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-honor_pkg-admin-add-record~pages-patrol-index~pages-patrol_pkg-task-index~pages-ucenter_pkg-us~cfd09d99"),a.e("uni_modules-uni-id-pages-pages-login-login-withoutpwd~uni_modules-uni-id-pages-pages-login-login-withpwd"),a.e("uni_modules-uni-id-pages-pages-login-login-withoutpwd")]).then(function(){return e(a("8dd5"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("uni_modules-uni-id-pages-pages-login-login-withpwd",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-honor_pkg-admin-add-record~pages-patrol-index~pages-patrol_pkg-task-index~pages-ucenter_pkg-us~cfd09d99"),a.e("uni_modules-uni-id-pages-pages-login-login-withoutpwd~uni_modules-uni-id-pages-pages-login-login-withpwd"),a.e("uni_modules-uni-id-pages-pages-login-login-withpwd")]).then(function(){return e(a("a5e8"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("uni_modules-uni-id-pages-pages-userinfo-change_pwd-change_pwd",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978"),a.e("uni_modules-uni-id-pages-pages-userinfo-change_pwd-change_pwd")]).then(function(){return e(a("4e75"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("uni_modules-uni-id-pages-pages-userinfo-userinfo",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-honor_pkg-admin-add-record~pages-patrol-index~pages-patrol_pkg-task-index~pages-ucenter_pkg-us~cfd09d99"),a.e("uni_modules-uni-id-pages-pages-userinfo-userinfo")]).then(function(){return e(a("73ac"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-honor_pkg-gallery-index",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-honor_pkg-gallery-index")]).then(function(){return e(a("b13d"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-honor_pkg-admin-index",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-honor_pkg-admin-index")]).then(function(){return e(a("e7c8"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-honor_pkg-admin-add-record",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-honor_pkg-admin-add-record~pages-patrol-index~pages-patrol_pkg-task-index~pages-ucenter_pkg-us~cfd09d99"),a.e("pages-honor_pkg-admin-add-record")]).then(function(){return e(a("3b53"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-honor_pkg-admin-batch-manager",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-batch-manager~pages-patrol_pkg-record-index"),a.e("pages-honor_pkg-admin-batch-manager")]).then(function(){return e(a("91f9"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-honor_pkg-admin-type-manager",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-honor_pkg-admin-type-manager")]).then(function(){return e(a("5a96"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-info_pkg-user-guide",(function(e){var n={component:a.e("pages-info_pkg-user-guide").then(function(){return e(a("5ec0"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-info_pkg-privacy",(function(e){var n={component:a.e("pages-info_pkg-privacy").then(function(){return e(a("c975"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-checkin-index",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index"),a.e("pages-patrol_pkg-checkin-index")]).then(function(){return e(a("f2cd"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-point-index",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-point-index")]).then(function(){return e(a("d3a8"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-point-add",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978"),a.e("pages-patrol_pkg-point-add")]).then(function(){return e(a("6f9c"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-point-edit",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978"),a.e("pages-patrol_pkg-point-edit")]).then(function(){return e(a("6357"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-point-detail",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-point-detail")]).then(function(){return e(a("819e"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-route-index",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-route-index")]).then(function(){return e(a("4c2c"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-route-add",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978"),a.e("pages-patrol_pkg-route-add")]).then(function(){return e(a("5d59"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-route-edit",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978"),a.e("pages-patrol_pkg-route-edit")]).then(function(){return e(a("88d4"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-route-detail",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-route-detail")]).then(function(){return e(a("e653"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-shift-index",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-shift-index")]).then(function(){return e(a("d34d"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-shift-add",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978"),a.e("pages-patrol_pkg-shift-add")]).then(function(){return e(a("0562"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-shift-edit",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-add-record~pages-honor_pkg-admin-b~e93cbf68"),a.e("pages-feedback_pkg-edit~pages-notice-add~pages-notice-edit~pages-patrol_pkg-point-add~pages-patrol_p~08be2978"),a.e("pages-patrol_pkg-shift-edit")]).then(function(){return e(a("a9bf"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-shift-detail",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-shift-detail")]).then(function(){return e(a("f381"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-task-index",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-honor_pkg-admin-add-record~pages-patrol-index~pages-patrol_pkg-task-index~pages-ucenter_pkg-us~cfd09d99"),a.e("pages-patrol_pkg-task-index")]).then(function(){return e(a("f5af"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-task-add",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-task-add")]).then(function(){return e(a("9cee"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-task-batch-add",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-task-batch-add")]).then(function(){return e(a("fbf3"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-task-detail",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-task-detail")]).then(function(){return e(a("fa04"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-task-edit",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-task-edit")]).then(function(){return e(a("1e15"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-record-index",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-honor_pkg-admin-batch-manager~pages-patrol_pkg-record-index"),a.e("pages-patrol_pkg-record-index")]).then(function(){return e(a("9819"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-record-detail",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-record-detail")]).then(function(){return e(a("7116"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-record-route-detail",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-record-route-detail")]).then(function(){return e(a("3129"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-point-qrcode",(function(e){var n={component:Promise.all([a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-point-qrcode~pages-patrol_pkg-point-qrcode-batch"),a.e("pages-patrol_pkg-point-qrcode")]).then(function(){return e(a("bdcb"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),t.default.component("pages-patrol_pkg-point-qrcode-batch",(function(e){var n={component:Promise.all([a.e("pages-feedback-list~pages-feedback_pkg-edit~pages-feedback_pkg-examine~pages-honor_pkg-admin-add-rec~0dcd1013"),a.e("pages-patrol-index~pages-patrol_pkg-checkin-index~pages-patrol_pkg-point-add~pages-patrol_pkg-point-~dc86a164"),a.e("pages-patrol_pkg-point-qrcode~pages-patrol_pkg-point-qrcode-batch"),a.e("pages-patrol_pkg-point-qrcode-batch")]).then(function(){return e(a("88a5"))}.bind(null,a)).catch(a.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(n.loading={name:"SystemAsyncLoading",render:function(e){return e(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(n.error={name:"SystemAsyncError",render:function(e){return e(__uniConfig["async"]["error"])}}),n})),e.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{render:function(e){return e("Page",{props:Object.assign({isQuit:!0,isEntry:!0,isTabBar:!0,tabBarIndex:0},__uniConfig.globalStyle,{navigationBarTitleText:"株水小智",enablePullDownRefresh:!1})},[e("pages-index-index",{slot:"page"})])}},meta:{id:1,name:"pages-index-index",isNVue:!1,maxWidth:0,pagePath:"pages/index/index",isQuit:!0,isEntry:!0,isTabBar:!0,tabBarIndex:0,windowTop:44}},{path:"/pages/patrol/index",component:{render:function(e){return e("Page",{props:Object.assign({isQuit:!0,isTabBar:!0,tabBarIndex:2},__uniConfig.globalStyle,{navigationBarTitleText:"巡视打卡",enablePullDownRefresh:!1})},[e("pages-patrol-index",{slot:"page"})])}},meta:{id:2,name:"pages-patrol-index",isNVue:!1,maxWidth:0,pagePath:"pages/patrol/index",isQuit:!0,isTabBar:!0,tabBarIndex:2,windowTop:44}},{path:"/pages/feedback/list",component:{render:function(e){return e("Page",{props:Object.assign({isQuit:!0,isTabBar:!0,tabBarIndex:1},__uniConfig.globalStyle,{navigationBarTitleText:"问题反馈",enablePullDownRefresh:!0})},[e("pages-feedback-list",{slot:"page"})])}},meta:{id:3,name:"pages-feedback-list",isNVue:!1,maxWidth:0,pagePath:"pages/feedback/list",isQuit:!0,isTabBar:!0,tabBarIndex:1,windowTop:44}},{path:"/pages/ucenter/ucenter",component:{render:function(e){return e("Page",{props:Object.assign({isQuit:!0,isTabBar:!0,tabBarIndex:3},__uniConfig.globalStyle,{navigationBarTitleText:"用户中心",enablePullDownRefresh:!0})},[e("pages-ucenter-ucenter",{slot:"page"})])}},meta:{id:4,name:"pages-ucenter-ucenter",isNVue:!1,maxWidth:0,pagePath:"pages/ucenter/ucenter",isQuit:!0,isTabBar:!0,tabBarIndex:3,windowTop:44}},{path:"/pages/feedback_pkg/edit",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"找茬编辑"})},[e("pages-feedback_pkg-edit",{slot:"page"})])}},meta:{name:"pages-feedback_pkg-edit",isNVue:!1,maxWidth:0,pagePath:"pages/feedback_pkg/edit",windowTop:44}},{path:"/pages/feedback_pkg/examine",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"审核流程"})},[e("pages-feedback_pkg-examine",{slot:"page"})])}},meta:{name:"pages-feedback_pkg-examine",isNVue:!1,maxWidth:0,pagePath:"pages/feedback_pkg/examine",windowTop:44}},{path:"/pages/ucenter_pkg/todo",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"我的待办",enablePullDownRefresh:!0})},[e("pages-ucenter_pkg-todo",{slot:"page"})])}},meta:{name:"pages-ucenter_pkg-todo",isNVue:!1,maxWidth:0,pagePath:"pages/ucenter_pkg/todo",windowTop:44}},{path:"/pages/ucenter_pkg/export-excel",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"导出Excel"})},[e("pages-ucenter_pkg-export-excel",{slot:"page"})])}},meta:{name:"pages-ucenter_pkg-export-excel",isNVue:!1,maxWidth:0,pagePath:"pages/ucenter_pkg/export-excel",windowTop:44}},{path:"/pages/ucenter_pkg/user-management",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"用户管理",enablePullDownRefresh:!0})},[e("pages-ucenter_pkg-user-management",{slot:"page"})])}},meta:{name:"pages-ucenter_pkg-user-management",isNVue:!1,maxWidth:0,pagePath:"pages/ucenter_pkg/user-management",windowTop:44}},{path:"/pages/ucenter_pkg/responsible-tasks",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"我的任务",enablePullDownRefresh:!0})},[e("pages-ucenter_pkg-responsible-tasks",{slot:"page"})])}},meta:{name:"pages-ucenter_pkg-responsible-tasks",isNVue:!1,maxWidth:0,pagePath:"pages/ucenter_pkg/responsible-tasks",windowTop:44}},{path:"/pages/ucenter_pkg/gm-supervision",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"任务监督",enablePullDownRefresh:!0})},[e("pages-ucenter_pkg-gm-supervision",{slot:"page"})])}},meta:{name:"pages-ucenter_pkg-gm-supervision",isNVue:!1,maxWidth:0,pagePath:"pages/ucenter_pkg/gm-supervision",windowTop:44}},{path:"/pages/ucenter_pkg/complete-task",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"完成任务",enablePullDownRefresh:!1})},[e("pages-ucenter_pkg-complete-task",{slot:"page"})])}},meta:{name:"pages-ucenter_pkg-complete-task",isNVue:!1,maxWidth:0,pagePath:"pages/ucenter_pkg/complete-task",windowTop:44}},{path:"/pages/notice/add",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"公告新增",enablePullDownRefresh:!1})},[e("pages-notice-add",{slot:"page"})])}},meta:{name:"pages-notice-add",isNVue:!1,maxWidth:0,pagePath:"pages/notice/add",windowTop:44}},{path:"/pages/notice/edit",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"公告编辑",enablePullDownRefresh:!1})},[e("pages-notice-edit",{slot:"page"})])}},meta:{name:"pages-notice-edit",isNVue:!1,maxWidth:0,pagePath:"pages/notice/edit",windowTop:44}},{path:"/pages/notice/list",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"公告通知",enablePullDownRefresh:!0})},[e("pages-notice-list",{slot:"page"})])}},meta:{name:"pages-notice-list",isNVue:!1,maxWidth:0,pagePath:"pages/notice/list",windowTop:44}},{path:"/pages/notice/detail",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"公告详细"})},[e("pages-notice-detail",{slot:"page"})])}},meta:{name:"pages-notice-detail",isNVue:!1,maxWidth:0,pagePath:"pages/notice/detail",windowTop:44}},{path:"/uni_modules/uni-id-pages/pages/login/login-withoutpwd",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"微信一键登录"})},[e("uni_modules-uni-id-pages-pages-login-login-withoutpwd",{slot:"page"})])}},meta:{name:"uni_modules-uni-id-pages-pages-login-login-withoutpwd",isNVue:!1,maxWidth:0,pagePath:"uni_modules/uni-id-pages/pages/login/login-withoutpwd",windowTop:44}},{path:"/uni_modules/uni-id-pages/pages/login/login-withpwd",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"账号密码登录"})},[e("uni_modules-uni-id-pages-pages-login-login-withpwd",{slot:"page"})])}},meta:{name:"uni_modules-uni-id-pages-pages-login-login-withpwd",isNVue:!1,maxWidth:0,pagePath:"uni_modules/uni-id-pages/pages/login/login-withpwd",windowTop:44}},{path:"/uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"密码修改"})},[e("uni_modules-uni-id-pages-pages-userinfo-change_pwd-change_pwd",{slot:"page"})])}},meta:{name:"uni_modules-uni-id-pages-pages-userinfo-change_pwd-change_pwd",isNVue:!1,maxWidth:0,pagePath:"uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd",windowTop:44}},{path:"/uni_modules/uni-id-pages/pages/userinfo/userinfo",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"个人资料"})},[e("uni_modules-uni-id-pages-pages-userinfo-userinfo",{slot:"page"})])}},meta:{name:"uni_modules-uni-id-pages-pages-userinfo-userinfo",isNVue:!1,maxWidth:0,pagePath:"uni_modules/uni-id-pages/pages/userinfo/userinfo",windowTop:44}},{path:"/pages/honor_pkg/gallery/index",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"荣誉展厅",navigationStyle:"custom",enablePullDownRefresh:!0})},[e("pages-honor_pkg-gallery-index",{slot:"page"})])}},meta:{name:"pages-honor_pkg-gallery-index",isNVue:!1,maxWidth:0,pagePath:"pages/honor_pkg/gallery/index",windowTop:0}},{path:"/pages/honor_pkg/admin/index",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"荣誉管理",navigationStyle:"custom"})},[e("pages-honor_pkg-admin-index",{slot:"page"})])}},meta:{name:"pages-honor_pkg-admin-index",isNVue:!1,maxWidth:0,pagePath:"pages/honor_pkg/admin/index",windowTop:0}},{path:"/pages/honor_pkg/admin/add-record",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"添加表彰记录",navigationStyle:"custom"})},[e("pages-honor_pkg-admin-add-record",{slot:"page"})])}},meta:{name:"pages-honor_pkg-admin-add-record",isNVue:!1,maxWidth:0,pagePath:"pages/honor_pkg/admin/add-record",windowTop:0}},{path:"/pages/honor_pkg/admin/batch-manager",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"智能批次管理",navigationStyle:"custom"})},[e("pages-honor_pkg-admin-batch-manager",{slot:"page"})])}},meta:{name:"pages-honor_pkg-admin-batch-manager",isNVue:!1,maxWidth:0,pagePath:"pages/honor_pkg/admin/batch-manager",windowTop:0}},{path:"/pages/honor_pkg/admin/type-manager",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"荣誉类型管理",navigationStyle:"custom"})},[e("pages-honor_pkg-admin-type-manager",{slot:"page"})])}},meta:{name:"pages-honor_pkg-admin-type-manager",isNVue:!1,maxWidth:0,pagePath:"pages/honor_pkg/admin/type-manager",windowTop:0}},{path:"/pages/info_pkg/user-guide",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"用户指南",enablePullDownRefresh:!1})},[e("pages-info_pkg-user-guide",{slot:"page"})])}},meta:{name:"pages-info_pkg-user-guide",isNVue:!1,maxWidth:0,pagePath:"pages/info_pkg/user-guide",windowTop:44}},{path:"/pages/info_pkg/privacy",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"隐私政策",enablePullDownRefresh:!1})},[e("pages-info_pkg-privacy",{slot:"page"})])}},meta:{name:"pages-info_pkg-privacy",isNVue:!1,maxWidth:0,pagePath:"pages/info_pkg/privacy",windowTop:44}},{path:"/pages/patrol_pkg/checkin/index",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"打卡签到",navigationStyle:"custom",enablePullDownRefresh:!1,disableScroll:!0})},[e("pages-patrol_pkg-checkin-index",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-checkin-index",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/checkin/index",windowTop:0}},{path:"/pages/patrol_pkg/point/index",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"点位管理",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-point-index",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-point-index",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/point/index",windowTop:44}},{path:"/pages/patrol_pkg/point/add",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"添加点位",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-point-add",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-point-add",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/point/add",windowTop:44}},{path:"/pages/patrol_pkg/point/edit",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"编辑点位",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-point-edit",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-point-edit",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/point/edit",windowTop:44}},{path:"/pages/patrol_pkg/point/detail",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"点位详情",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-point-detail",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-point-detail",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/point/detail",windowTop:44}},{path:"/pages/patrol_pkg/route/index",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"线路管理",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-route-index",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-route-index",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/route/index",windowTop:44}},{path:"/pages/patrol_pkg/route/add",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"添加线路",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-route-add",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-route-add",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/route/add",windowTop:44}},{path:"/pages/patrol_pkg/route/edit",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"编辑线路",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-route-edit",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-route-edit",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/route/edit",windowTop:44}},{path:"/pages/patrol_pkg/route/detail",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"线路详情",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-route-detail",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-route-detail",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/route/detail",windowTop:44}},{path:"/pages/patrol_pkg/shift/index",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"班次管理",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-shift-index",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-shift-index",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/shift/index",windowTop:44}},{path:"/pages/patrol_pkg/shift/add",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"添加班次",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-shift-add",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-shift-add",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/shift/add",windowTop:44}},{path:"/pages/patrol_pkg/shift/edit",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"编辑班次",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-shift-edit",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-shift-edit",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/shift/edit",windowTop:44}},{path:"/pages/patrol_pkg/shift/detail",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"班次详情",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-shift-detail",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-shift-detail",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/shift/detail",windowTop:44}},{path:"/pages/patrol_pkg/task/index",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"任务管理",enablePullDownRefresh:!0})},[e("pages-patrol_pkg-task-index",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-task-index",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/task/index",windowTop:44}},{path:"/pages/patrol_pkg/task/add",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"添加任务",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-task-add",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-task-add",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/task/add",windowTop:44}},{path:"/pages/patrol_pkg/task/batch-add",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"批量添加任务",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-task-batch-add",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-task-batch-add",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/task/batch-add",windowTop:44}},{path:"/pages/patrol_pkg/task/detail",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"任务详情",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-task-detail",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-task-detail",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/task/detail",windowTop:44}},{path:"/pages/patrol_pkg/task/edit",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"编辑任务",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-task-edit",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-task-edit",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/task/edit",windowTop:44}},{path:"/pages/patrol_pkg/record/index",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"巡视记录",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-record-index",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-record-index",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/record/index",windowTop:44}},{path:"/pages/patrol_pkg/record/detail",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"记录详情",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-record-detail",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-record-detail",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/record/detail",windowTop:44}},{path:"/pages/patrol_pkg/record/route-detail",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"线路记录",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-record-route-detail",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-record-route-detail",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/record/route-detail",windowTop:44}},{path:"/pages/patrol_pkg/point/qrcode",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"二维码管理",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-point-qrcode",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-point-qrcode",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/point/qrcode",windowTop:44}},{path:"/pages/patrol_pkg/point/qrcode-batch",component:{render:function(e){return e("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"批量二维码管理",enablePullDownRefresh:!1})},[e("pages-patrol_pkg-point-qrcode-batch",{slot:"page"})])}},meta:{name:"pages-patrol_pkg-point-qrcode-batch",isNVue:!1,maxWidth:0,pagePath:"pages/patrol_pkg/point/qrcode-batch",windowTop:44}},{path:"/choose-location",component:{render:function(e){return e("Page",{props:{navigationStyle:"custom"}},[e("system-choose-location",{slot:"page"})])}},meta:{name:"choose-location",pagePath:"/choose-location"}},{path:"/open-location",component:{render:function(e){return e("Page",{props:{navigationStyle:"custom"}},[e("system-open-location",{slot:"page"})])}},meta:{name:"open-location",pagePath:"/open-location"}}],e.UniApp&&new e.UniApp}).call(this,a("0ee4"))},"423e":function(e,n,a){"use strict";(function(e){a("6a54");var t=a("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.store=n.mutations=void 0;var i=t(a("2634")),o=t(a("9b1b")),r=t(a("2fdc"));a("dc8a"),a("bf0f"),a("2797"),a("c223");var s=t(a("0c73")),p=t(a("30eb")),g=t(a("9b8e")),d=e.importObject("uni-id-co"),c=e.database(),u=c.collection("uni-id-users"),l=uni.getStorageSync("uni-id-pages-userInfo")||{},f={userInfo:l,hasLogin:0!=Object.keys(l).length},_={updateUserInfo:function(){var n=arguments,a=this;return(0,r.default)((0,i.default)().mark((function t(){var r,s,p,g,d;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=n.length>0&&void 0!==n[0]&&n[0],!r){t.next=5;break}u.where("_id==$env.uid").update(r).then((function(e){e.result.updated?(uni.showToast({title:"更新成功",icon:"none",duration:3e3}),a.setUserInfo(r)):uni.showToast({title:"没有改变",icon:"none",duration:3e3})})),t.next=22;break;case 5:return s=e.getCurrentUserInfo().uid,a.setUserInfo({_id:s},{cover:!0}),p=e.importObject("uni-id-co",{customUI:!0}),t.prev=8,t.next=11,u.where("'_id' == $cloudEnv_uid").field("mobile,nickname,username,email,avatar_file").get();case 11:return g=t.sent,t.next=14,p.getRealNameInfo();case 14:d=t.sent,a.setUserInfo((0,o.default)((0,o.default)({},g.result.data[0]),{},{realNameAuth:d})),t.next=22;break;case 18:t.prev=18,t.t0=t["catch"](8),a.setUserInfo({},{cover:!0}),console.error(t.t0.message,t.t0.errCode);case 22:case"end":return t.stop()}}),t,null,[[8,18]])})))()},setUserInfo:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{cover:!1},a=n.cover,t=a?e:Object.assign(k.userInfo,e);return k.userInfo=Object.assign({},t),k.hasLogin=0!=Object.keys(k.userInfo).length,uni.setStorageSync("uni-id-pages-userInfo",k.userInfo),e},logout:function(){var n=this;return(0,r.default)((0,i.default)().mark((function a(){return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!(e.getCurrentUserInfo().tokenExpired>Date.now())){a.next=9;break}return a.prev=1,a.next=4,d.logout();case 4:a.next=9;break;case 6:a.prev=6,a.t0=a["catch"](1),console.error(a.t0);case 9:uni.removeStorageSync("uni_id_token"),uni.setStorageSync("uni_id_token_expired",0),n.setUserInfo({},{cover:!0}),uni.$emit("uni-id-pages-logout"),uni.redirectTo({url:"/".concat(s.default.uniIdRouter&&s.default.uniIdRouter.loginPage?s.default.uniIdRouter.loginPage:"uni_modules/uni-id-pages/pages/login/login-withoutpwd")});case 14:case"end":return a.stop()}}),a,null,[[1,6]])})))()},loginBack:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.uniIdRedirectUrl,a=void 0===n?"":n,t=0,i=getCurrentPages();if(i.forEach((function(e,n){"login"==i[i.length-n-1].route.split("/")[3]&&t++})),a)return uni.redirectTo({url:a,fail:function(e){uni.switchTab({url:a,fail:function(n){console.log(e,n)}})}});if("weixin"==e.loginType)return window.history.go(-3);if(t){var o=s.default.pages[0];return uni.reLaunch({url:"/".concat(o.path)})}uni.navigateBack({delta:t})},loginSuccess:function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=n.showToast,t=void 0===a||a,i=n.toastText,o=void 0===i?"登录成功":i,r=n.autoBack,s=void 0===r||r,g=n.uniIdRedirectUrl,d=void 0===g?"":g,c=n.passwordConfirmed;if(t&&uni.showToast({title:o,icon:"none",duration:3e3}),this.updateUserInfo(),uni.$emit("uni-id-pages-login-success"),p.default.setPasswordAfterLogin&&!c)return uni.redirectTo({url:d?"/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd?uniIdRedirectUrl=".concat(d,"&loginType=").concat(n.loginType):"/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd?loginType=".concat(n.loginType),fail:function(e){console.log(e)}});uni.reLaunch({url:"/pages/ucenter/ucenter",fail:function(n){console.error("跳转到用户中心失败:",n),s&&e.loginBack({uniIdRedirectUrl:d})}})}};n.mutations=_;var k=g.default.observable(f);n.store=k}).call(this,a("861b")["uniCloud"])},"4bea":function(e,n,a){"use strict";(function(e){a("6a54");var t=a("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,a("4626"),a("bf0f");var i=t(a("812d")),o=t(a("7e11")),r=a("eddf");a("f991f");var s=["pages/index/index","pages/feedback/list","pages/patrol/index","pages/ucenter/ucenter"],p={data:function(){return{launchInitialized:!1}},onLaunch:function(){var e=this;this.launchTime=Date.now(),uni.$on("token-invalid",(function(){e.handleTokenExpired("token-invalid")})),uni.$on("token-expired",(function(){e.handleTokenExpired("token-expired")})),setTimeout((function(){(0,i.default)().then((function(){var n=uni.getStorageSync("uni_id_token");n?o.default.updateTodoCountImmediately().then((function(n){e.launchInitialized=!0})):(o.default.clearBadge(),e.launchInitialized=!0)})).catch((function(n){o.default.clearBadge(),e.launchInitialized=!0}))}),0),uni.$on("uni-id-pages-login-success",(function(){o.default.clearBadge(),setTimeout((function(){o.default.getTodoCount()}),1e3)})),uni.$on("uni-id-pages-logout",(function(){o.default.forceCleanBadge()})),this.setupPageChangeListener()},onShow:function(){var e=this,n=uni.getStorageSync("uni_id_token");if(n){if(!this.launchInitialized)return void setTimeout((function(){e.performOnShowUpdate()}),1e3);this.performOnShowUpdate()}},onHide:function(){},beforeDestroy:function(){uni.$off("token-invalid"),uni.$off("token-expired"),clearInterval(this.pageChangeInterval)},onLaunchFromBGFetch:function(e){try{if(e&&"location"===e.fetchType){var n=e.fetchedData||{};uni.setStorageSync("lastBackgroundLocation",n)}}catch(a){console.error("处理后台数据错误:",a)}},methods:{isTabBarPage:function(e){return s.includes(e)},handleTokenExpired:function(){uni.removeStorageSync("uni_id_token"),uni.removeStorageSync("uni_id_token_expired"),uni.removeStorageSync("uni_id_user"),uni.removeStorageSync("uni-id-pages-userInfo"),uni.removeStorageSync((0,r.getCacheKey)(r.CACHE_KEYS.USER_ROLE)),o.default.clearBadge();var e=getCurrentPages(),n=e[e.length-1],a=n?n.route:"",t=!a||0===e.length,i=t||this.isTabBarPage(a);i?("pages/ucenter/ucenter"===a&&uni.$emit("force-logout-ui-update"),uni.showToast({title:"登录已过期，请重新登录",icon:"none",duration:2e3})):(uni.showToast({title:"登录已过期，即将跳转登录页",icon:"none",duration:2e3}),setTimeout((function(){uni.reLaunch({url:"/uni_modules/uni-id-pages/pages/login/login-withpwd"})}),2e3))},initializeBadge:function(){var e=uni.getStorageSync("uni_id_token");e?(o.default.updateTodoCountImmediately(),this.startSmartRetry()):o.default.clearBadge()},startSmartRetry:function(){setTimeout((function(){var n=uni.getStorageSync("uni_id_token");if(n&&0===o.default.todoCount&&!o.default.isProcessing)try{var a=e.getCurrentUserInfo();null!==a&&void 0!==a&&a.role&&a.role.some((function(e){return["supervisor","PM","GM","admin"].includes(e)}))&&o.default.forceRefresh()}catch(t){setTimeout((function(){n&&0===o.default.todoCount&&o.default.forceRefresh()}),3e3)}}),3e3)},setupPageChangeListener:function(){var e=this,n="",a=setInterval((function(){try{var a=getCurrentPages();if(a.length>0){var t=a[a.length-1],i=t.route||"";if(i&&i!==n&&(n=i,e.isTabBarPage(i))){var r=uni.getStorageSync("uni_id_token");r&&setTimeout((function(){o.default.updateTodoCountImmediately(),setTimeout((function(){o.default.forceSyncBadge()}),100)}),100)}}}catch(s){}}),1e3);this.pageChangeInterval=a},performOnShowUpdate:function(){o.default.updateTodoCountImmediately()}},globalData:{lazyLoadComponents:!1,needRefreshTaskList:!1,refreshTaskDate:null,refreshShiftList:!1,lastTaskOperation:{type:null,timestamp:0,taskId:null}}};n.default=p}).call(this,a("861b")["uniCloud"])},"5d29":function(e,n,a){"use strict";a("6a54");var t=a("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=t(a("9b8e")),o=t(a("8f59")),r=t(a("df01"));i.default.use(o.default);var s=new o.default.Store({modules:{user:r.default},strict:!1});n.default=s},"7ad1":function(e,n,a){var t=a("bdbb").default;a("bf0f"),uni.addInterceptor({returnValue:function(e){return!e||"object"!==t(e)&&"function"!==typeof e||"function"!==typeof e.then?e:new Promise((function(n,a){e.then((function(e){return e?e[0]?a(e[0]):n(e[1]):n(e)}))}))}})},"7e11":function(e,n,a){"use strict";(function(e){a("6a54");var t=a("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,a("bf0f"),a("4626"),a("5ac7"),a("aa9c"),a("c9b5"),a("ab80"),a("f3f7"),a("18f7"),a("de6c"),a("fd3c");var i=t(a("b7c7")),o=t(a("2634")),r=t(a("2fdc")),s=t(a("80b1")),p=t(a("efe5")),g=a("eddf"),d=function(){function n(){(0,s.default)(this,n),this.todoCount=0,this.initialized=!1,this.userRoles=[],this.debounceTimer=null,this.debounceDelay=100,this.isProcessing=!1,this.checkInterval=null,this.isClearing=!1,this.lastTabBarCheck=0,this.lastSyncTime=0,this.checkIntervalMs=15e3,this.setupAppStateListener()}return(0,p.default)(n,[{key:"setupAppStateListener",value:function(){var e=this;try{"undefined"!==typeof document&&document.addEventListener("visibilitychange",(function(){"visible"===document.visibilityState&&e.onAppForeground()})),uni.$on("feedback-updated",(function(){setTimeout((function(){e.forceRefresh()}),100)}))}catch(n){console.error("设置应用状态监听失败:",n)}}},{key:"onAppForeground",value:function(){this.forceRefresh()}},{key:"isTokenLikelyExpired",value:function(){try{var e=uni.getStorageSync("uni_id_token_expired");return!!(e&&Date.now()>e)&&(this.handleTokenExpired(),!0)}catch(n){return!1}}},{key:"handleTokenExpired",value:function(){this.clearBadge(),uni.removeStorageSync("uni-id-pages-userInfo"),uni.removeStorageSync((0,g.getCacheKey)(g.CACHE_KEYS.USER_ROLE)),uni.$emit("token-expired")}},{key:"hasSpecificRole",value:function(){try{if(this.isTokenLikelyExpired())return this.userRoles=[],!1;var n=e.getCurrentUserInfo();return null!==n&&void 0!==n&&n.role?(this.userRoles=n.role,this.userRoles.some((function(e){return["supervisor","PM","GM","admin","responsible"].includes(e)}))):(this.userRoles=[],!1)}catch(a){return this.userRoles=[],this.clearBadge(),!1}}},{key:"hasRole",value:function(e){return this.userRoles.length>0&&this.userRoles.includes(e)}},{key:"buildTodoQueryConditions",value:function(n){var a=[];if(this.userRoles.includes("supervisor")&&a.push({workflowStatus:"pending_supervisor"}),this.userRoles.includes("PM")&&a.push({workflowStatus:"pending_pm"}),this.userRoles.includes("GM")&&(a.push({workflowStatus:"pending_gm"}),a.push({workflowStatus:"gm_approved_pending_assign"})),this.userRoles.includes("responsible"))try{var t=e.getCurrentUserInfo(),i=null===t||void 0===t?void 0:t.uid;i&&a.push({workflowStatus:"assigned_to_responsible",responsibleUserId:i})}catch(o){console.warn("获取用户ID失败:",o)}return this.userRoles.includes("GM")&&a.push({workflowStatus:"completed_by_responsible"}),this.userRoles.includes("admin")?{workflowStatus:n.in(["pending_supervisor","pending_pm","pending_gm","gm_approved_pending_assign","completed_by_responsible"])}:a.length>0?n.or(a):null}},{key:"getTodoCount",value:function(){var n=(0,r.default)((0,o.default)().mark((function n(){var a,t,i,r,s,p,g;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,!this.isProcessing){n.next=3;break}return n.abrupt("return",this.todoCount);case 3:if(this.isProcessing=!0,a=uni.getStorageSync("uni_id_token"),a){n.next=9;break}return this.clearBadge(),this.isProcessing=!1,n.abrupt("return",0);case 9:if(!this.isTokenLikelyExpired()){n.next=12;break}return this.isProcessing=!1,n.abrupt("return",0);case 12:if(t=this.hasSpecificRole(),t){n.next=17;break}return this.clearBadge(),this.isProcessing=!1,n.abrupt("return",0);case 17:if(i=e.database(),r=i.command,s=this.buildTodoQueryConditions(r),s){n.next=24;break}return this.clearBadge(),this.isProcessing=!1,n.abrupt("return",0);case 24:return n.next=26,i.collection("feedback").where(s).count();case 26:if(p=n.sent,!p.result||void 0===p.result.total){n.next=32;break}return g=p.result.total,this.todoCount!==g&&(this.todoCount=g,this.lastUpdateTime=Date.now(),this.updateBadge()),this.isProcessing=!1,n.abrupt("return",this.todoCount);case 32:return this.clearBadge(),this.isProcessing=!1,n.abrupt("return",0);case 37:return n.prev=37,n.t0=n["catch"](0),this.isProcessing=!1,n.abrupt("return",0);case 41:case"end":return n.stop()}}),n,this,[[0,37]])})));return function(){return n.apply(this,arguments)}}()},{key:"updateTodoCountImmediately",value:function(){var e=(0,r.default)((0,o.default)().mark((function e(){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null),!this.isProcessing){e.next=10;break}n=0;case 3:if(!(this.isProcessing&&n<50)){e.next=9;break}return e.next=6,new Promise((function(e){return setTimeout(e,100)}));case 6:n++,e.next=3;break;case 9:this.isProcessing&&(this.isProcessing=!1);case 10:return e.next=12,this.getTodoCount();case 12:return e.abrupt("return",e.sent);case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"debouncedGetTodoCount",value:function(){var e=this;return this.debounceTimer&&clearTimeout(this.debounceTimer),this.debounceTimer=setTimeout((function(){e.getTodoCount()}),this.debounceDelay),Promise.resolve()}},{key:"updateBadge",value:function(){var e=this;(function n(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=e.isCurrentPageTabBar();t?e.todoCount>0?uni.setTabBarBadge({index:3,text:e.todoCount.toString()}):e.clearBadgeInternal():a<3&&setTimeout((function(){n(a+1)}),300)})(),uni.$emit("todo-count-updated",this.todoCount)}},{key:"clearBadgeInternal",value:function(){if(this.isCurrentPageTabBar())try{uni.removeTabBarBadge({index:3})}catch(e){console.warn("清除TabBar徽章失败(可能不在TabBar页面):",e.errMsg||e.message)}}},{key:"clearBadge",value:function(){this.isClearing||(this.isClearing=!0,this.clearBadgeInternal(),this.todoCount=0,this.userRoles=[],this.isClearing=!1,uni.$emit("todo-count-updated",0))}},{key:"forceCleanBadge",value:function(){if(this.isClearing=!1,this.isProcessing=!1,this.todoCount=0,this.userRoles=[],this.isCurrentPageTabBar()){(function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;try{uni.removeTabBarBadge({index:3,complete:function(a){a.errMsg&&a.errMsg.includes("fail")&&n<3&&setTimeout((function(){e(n+1)}),100)}})}catch(a){console.warn("清除TabBar徽章失败(重试".concat(n+1,"/3):"),a.errMsg||a.message),n<3&&setTimeout((function(){e(n+1)}),100)}})()}uni.$emit("todo-count-updated",0)}},{key:"init",value:function(){var e=(0,r.default)((0,o.default)().mark((function e(){var n,a=this;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!this.initialized){e.next=2;break}return e.abrupt("return");case 2:if(n=uni.getStorageSync("uni_id_token"),!n){e.next=8;break}return e.next=6,this.updateTodoCountImmediately();case 6:e.next=9;break;case 8:this.clearBadge();case 9:this.checkInterval&&clearInterval(this.checkInterval),this.checkInterval=setInterval((0,r.default)((0,o.default)().mark((function e(){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,n=uni.getStorageSync("uni_id_token"),!n){e.next=12;break}if(!a.isTokenLikelyExpired()){e.next=6;break}return a.clearBadge(),e.abrupt("return");case 6:return e.next=8,a.checkCrossDeviceUpdates();case 8:return e.next=10,a.updateTodoCountImmediately();case 10:e.next=13;break;case 12:a.clearBadge();case 13:e.next=18;break;case 15:e.prev=15,e.t0=e["catch"](0),a.clearBadge();case 18:case"end":return e.stop()}}),e,null,[[0,15]])}))),this.checkIntervalMs),this.initialized=!0;case 12:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"checkCrossDeviceUpdates",value:function(){var n=(0,r.default)((0,o.default)().mark((function n(){var a,t,r,s,p;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,this.lastSyncTime){n.next=4;break}return this.lastSyncTime=Date.now(),n.abrupt("return");case 4:return a=e.database(),n.next=7,a.collection("feedback-sync-status").where({updateTime:a.command.gt(this.lastSyncTime)}).orderBy("updateTime","desc").limit(5).get();case 7:if(t=n.sent,!(t.result&&t.result.data&&t.result.data.length>0)){n.next=19;break}return console.log("角标管理器检测到跨设备更新，静默强制刷新"),this.isProcessing=!1,this.userRoles=[],this.lastSyncTime=t.result.data[0].updateTime,r=t.result.data,s=(0,i.default)(new Set(r.map((function(e){return e.updateType})))),p=(0,i.default)(new Set(r.map((function(e){return e.feedbackId})))),uni.$emit("cross-device-update-detected",{updateTime:this.lastSyncTime,updateTypes:s,feedbackIds:p,updateCount:r.length,silent:!0}),n.next=19,this.updateTodoCountImmediately();case 19:n.next=24;break;case 21:n.prev=21,n.t0=n["catch"](0),console.log("角标跨设备更新检查失败:",n.t0);case 24:case"end":return n.stop()}}),n,this,[[0,21]])})));return function(){return n.apply(this,arguments)}}()},{key:"forceRefresh",value:function(){var e=(0,r.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.userRoles=[],this.isProcessing=!1,this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null),e.next=5,this.updateTodoCountImmediately();case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"forceSyncBadge",value:function(){if(this.isCurrentPageTabBar())if(this.todoCount>0)uni.setTabBarBadge({index:3,text:this.todoCount.toString()});else for(var e=function(e){setTimeout((function(){try{uni.removeTabBarBadge({index:3})}catch(n){console.warn("强制清除TabBar徽章失败(尝试".concat(e+1,"/3):"),n.errMsg||n.message)}}),100*e)},n=0;n<3;n++)e(n);else uni.$emit("todo-count-updated",this.todoCount)}},{key:"onLoginSuccess",value:function(){var e=(0,r.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.userRoles=[],this.isProcessing=!1,this.todoCount=0,this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null),e.next=7,this.updateTodoCountImmediately();case 7:this.forceSyncBadge(),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](0),this.clearBadge();case 13:case"end":return e.stop()}}),e,this,[[0,10]])})));return function(){return e.apply(this,arguments)}}()},{key:"isCurrentPageTabBar",value:function(){try{var e=Date.now();if(e-this.lastTabBarCheck<100)return this._lastTabBarResult||!1;this.lastTabBarCheck=e;var n=getCurrentPages();if(0===n.length)return this._lastTabBarResult=!1,!1;var a=n[n.length-1],t=a.route||"";if(t.includes("_pkg"))return this._lastTabBarResult=!1,!1;var i=["pages/index/index","pages/feedback/list","pages/patrol/index","pages/ucenter/ucenter"].includes(t);return this._lastTabBarResult=i,i}catch(o){return console.warn("检测TabBar页面失败:",o),this._lastTabBarResult=!1,!1}}},{key:"destroy",value:function(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null),this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null),uni.$off("feedback-updated");try{"undefined"!==typeof document&&document.removeEventListener("visibilitychange")}catch(e){}this.todoCount=0,this.userRoles=[],this.isProcessing=!1,this.isClearing=!1,this.initialized=!1}}]),n}(),c=new d;c.init();var u=c;n.default=u}).call(this,a("861b")["uniCloud"])},"812d":function(e,n,a){"use strict";(function(e){a("6a54");var t=a("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(){return d.apply(this,arguments)},a("d4b5"),a("8f71"),a("bf0f"),a("4626"),a("5ac7");var i=t(a("2634")),o=t(a("2fdc")),r=t(a("30eb")),s=e.importObject("uni-id-co",{customUI:!0}),p=r.default.loginTypes,g=r.default.debug;function d(){return d=(0,o.default)((0,i.default)().mark((function n(){var a,t,r,d,c,u;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(u=function(e){e.code,e.message},!g){n.next=10;break}return n.next=4,s.getSupportedLoginType();case 4:a=n.sent,t=a.supportedLoginType,console.log("supportedLoginType: "+JSON.stringify(t)),r={smsCode:"mobile-code",univerify:"univerify",username:"username-password",weixin:"weixin",qq:"qq",xiaomi:"xiaomi",sinaweibo:"sinaweibo",taobao:"taobao",facebook:"facebook",google:"google",alipay:"alipay",apple:"apple",weixinMobile:"weixin"},d=p.filter((function(e){return!t.includes(r[e])})),d.length&&console.error("错误：前端启用的登录方式:".concat(d.join("，"),';没有在服务端完成配置。配置文件路径："/uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center/uni-id/config.json"'));case 10:c=e.database(),c.on("error",u),e.onRefreshToken&&e.onRefreshToken((function(){uni.getPushClientId&&uni.getPushClientId({success:function(){var e=(0,o.default)((0,i.default)().mark((function e(n){var a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=n.cid,e.next=3,s.setPushCid({pushClientId:a});case 3:e.sent;case 4:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),fail:function(e){}})}));case 13:case"end":return n.stop()}}),n)}))),d.apply(this,arguments)}}).call(this,a("861b")["uniCloud"])},8300:function(e,n,a){"use strict";var t=a("d57b"),i=a.n(t);i.a},"95b1":function(e,n,a){"use strict";var t=a("f5bd").default,i=t(a("9b1b"));a("3dde"),a("a8b2"),a("1480"),a("6e4a"),a("357b"),a("9337"),a("861b");var o=t(a("2e52")),r=t(a("9b8e"));a("7ad1");var s=t(a("5d29"));r.default.config.productionTip=!1,o.default.mpType="app";var p=new r.default((0,i.default)((0,i.default)({},o.default),{},{store:s.default}));p.$mount()},a0b5:function(e,n,a){var t=a("c86c");n=t(!1),n.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */\n/* 全局样式 */@font-face{font-family:iconfont;src:url(/static/fonts/iconfont.ttf) format("truetype");font-weight:400;font-style:normal}.iconfont{font-family:iconfont!important;font-size:16px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}',""]),e.exports=n},a267:function(e,n,a){"use strict";a.r(n);var t=a("4bea"),i=a.n(t);for(var o in t)["default"].indexOf(o)<0&&function(e){a.d(n,e,(function(){return t[e]}))}(o);n["default"]=i.a},bd7e:function(e,n,a){"use strict";a.d(n,"b",(function(){return t})),a.d(n,"c",(function(){return i})),a.d(n,"a",(function(){}));var t=function(){var e=this.$createElement,n=this._self._c||e;return n("App",{attrs:{keepAliveInclude:this.keepAliveInclude}})},i=[]},d57b:function(e,n,a){var t=a("a0b5");t.__esModule&&(t=t.default),"string"===typeof t&&(t=[[e.i,t,""]]),t.locals&&(e.exports=t.locals);var i=a("967d").default;i("e9759928",t,!0,{sourceMap:!1,shadowMode:!1})},db30:function(e,n,a){"use strict";a("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={appid:"__UNI__2339AC9"}},df01:function(e,n,a){"use strict";a("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,a("4626"),a("5ac7");var t={SET_USER_INFO:function(e,n){e.userInfo=n},SET_TOKEN:function(e,n){e.token=n,uni.setStorageSync("uni_id_token",n)},SET_LOGIN_STATE:function(e,n){e.isLoggedIn=n},SET_PERMISSIONS:function(e,n){e.permissions=n},CLEAR_USER_INFO:function(e){e.userInfo=null,e.token=null,e.isLoggedIn=!1,e.permissions=[],uni.removeStorageSync("uni_id_token")}},i={namespaced:!0,state:{userInfo:null,token:null,isLoggedIn:!1,permissions:[]},getters:{userInfo:function(e){return e.userInfo},isLoggedIn:function(e){return e.isLoggedIn},permissions:function(e){return e.permissions},hasPermission:function(e){return function(n){return e.permissions.includes(n)}},hasManagePermission:function(e){if(!e.userInfo||!e.userInfo.role)return!1;var n=e.userInfo.role;return["admin","supervisor","PM","GM","reviser"].includes(n)}},mutations:t,actions:{loginSuccess:function(e,n){var a=e.commit,t=n.userInfo,i=n.token,o=n.permissions,r=void 0===o?[]:o;a("SET_USER_INFO",t),a("SET_TOKEN",i),a("SET_LOGIN_STATE",!0),a("SET_PERMISSIONS",r)},logout:function(e){var n=e.commit;n("CLEAR_USER_INFO")},updateUserInfo:function(e,n){var a=e.commit;a("SET_USER_INFO",n)}}};n.default=i},eddf:function(e,n,a){"use strict";a("6a54");var t=a("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.setCache=n.removeCache=n.hasValidCache=n.getCacheKey=n.getCache=n.default=n.clearAllCache=n.cacheManager=n.CACHE_KEYS=void 0,a("c223"),a("d4b5"),a("bf0f"),a("de6c"),a("2797"),a("9db6"),a("8f71"),a("fd3c"),a("f7a5"),a("7a76"),a("c9b5"),a("aa9c"),a("473f"),a("22b6");var i=t(a("2634")),o=t(a("2fdc")),r=t(a("9b1b")),s=t(a("80b1")),p=t(a("efe5")),g="offline_checkin_records",d={USER_ROLE:"userRole",USER_INFO:"userInfo",PROJECT_OPTIONS:"project_options",STATUS_OPTIONS:"status_options",URGENCY_OPTIONS:"urgency_options",RESPONSIBLE_LIST:"responsible_list",RESPONSIBLE_MAP:"responsible_map"};n.CACHE_KEYS=d;var c={USER_ROLE:18e5,USER_INFO:18e5,PROJECT_OPTIONS:864e5,STATUS_OPTIONS:864e5,URGENCY_OPTIONS:864e5,RESPONSIBLE_LIST:6e5,RESPONSIBLE_MAP:6e5},u=function(){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"zzps_",a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6048e5;(0,s.default)(this,e),this.prefix=n,this.defaultCacheTime=a,this.cacheKeys=d}return(0,p.default)(e,[{key:"_getFullKey",value:function(e){return"".concat(this.prefix).concat(e)}},{key:"set",value:function(e,n){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultCacheTime,t=this._getFullKey(e),i={value:n,expire:null!==a?Date.now()+a:null};try{uni.setStorageSync(t,JSON.stringify(i))}catch(o){console.error("缓存设置失败:",o)}}},{key:"get",value:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=this._getFullKey(e);try{var t=uni.getStorageSync(a);if(!t)return n;var i="string"===typeof t?JSON.parse(t):t,o=i.value,r=i.expire;return null!==r&&r<Date.now()?(this.remove(e),n):o}catch(s){return console.error("缓存获取失败:",s),n}}},{key:"remove",value:function(e){var n=this._getFullKey(e);try{uni.removeStorageSync(n)}catch(a){console.error("缓存移除失败:",a)}}},{key:"clear",value:function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{if(n)uni.clearStorageSync();else{var a=uni.getStorageInfoSync(),t=a.keys;t.forEach((function(n){n.startsWith(e.prefix)&&uni.removeStorageSync(n)}))}}catch(i){console.error("缓存清空失败:",i)}}},{key:"getInfo",value:function(){var e=this;try{var n=uni.getStorageInfoSync(),a=n.keys.filter((function(n){return n.startsWith(e.prefix)}));return{size:n.currentSize,limit:n.limitSize,totalKeys:n.keys.length,prefixKeys:a.length,keys:a}}catch(t){return console.error("获取缓存信息失败:",t),{size:0,limit:0,totalKeys:0,prefixKeys:0,keys:[]}}}},{key:"has",value:function(e){var n=this._getFullKey(e);try{var a=uni.getStorageSync(n);if(!a)return!1;var t="string"===typeof a?JSON.parse(a):a,i=t.expire;return!(null!==i&&i<Date.now())||(this.remove(e),!1)}catch(o){return console.error("缓存检查失败:",o),!1}}},{key:"setPermanent",value:function(e,n){this.set(e,n,null)}},{key:"keys",value:function(){var e=this;try{var n=uni.getStorageInfoSync();return n.keys.filter((function(n){return n.startsWith(e.prefix)})).map((function(n){return n.slice(e.prefix.length)}))}catch(a){return console.error("获取缓存键失败:",a),[]}}},{key:"setMultiple",value:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.defaultCacheTime;for(var a in e)this.set(a,e[a],n)}},{key:"getMultiple",value:function(e){var n=this,a={};return e.forEach((function(e){a[e]=n.get(e)})),a}},{key:"removeMultiple",value:function(e){var n=this;e.forEach((function(e){n.remove(e)}))}},{key:"clearExpired",value:function(){var e=this;try{var n=this.keys();n.forEach((function(n){e.get(n)}))}catch(a){console.error("清理过期缓存失败:",a)}}},{key:"saveOfflineCheckin",value:function(e){if(!e||!e.task_id||!e.point_id||!e.round)return Promise.reject(new Error("打卡记录缺少必要字段"));try{var n=this.get(g,[]),a=(0,r.default)((0,r.default)({},e),{},{offline_time:(new Date).toISOString(),sync_status:"pending"}),t=n.some((function(e){return e.task_id===a.task_id&&e.point_id===a.point_id&&e.round===a.round}));return t?Promise.reject(new Error("已存在相同的离线打卡记录")):(n.push(a),this.set(g,n),Promise.resolve({success:!0,message:"离线打卡记录已保存",id:n.length-1}))}catch(i){return Promise.reject(i)}}},{key:"getOfflineCheckins",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"all";try{var n=this.get(g,[]);return"all"===e?n:n.filter((function(n){return n.sync_status===e}))}catch(a){return console.error("获取离线打卡记录失败:",a),[]}}},{key:"updateOfflineCheckinStatus",value:function(e,n){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{var t=this.get(g,[]);return!!t[e]&&(t[e].sync_status=n,a&&(t[e].record_id=a),"success"!==n&&"failed"!==n||(t[e].sync_time=(new Date).toISOString()),this.set(g,t),!0)}catch(i){return console.error("更新离线打卡记录状态失败:",i),!1}}},{key:"clearSyncedOfflineCheckins",value:function(){try{var e=this.get(g,[]),n=e.filter((function(e){return"pending"===e.sync_status}));return this.set(g,n),e.length-n.length}catch(a){return console.error("清除已同步的离线打卡记录失败:",a),0}}},{key:"getUserRoles",value:function(){var e=(0,o.default)((0,i.default)().mark((function e(n,a){var t,o;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._getFullKey(this.cacheKeys.USER_ROLE)+(n?"_".concat(n):""),o=this.get(t),o||!a){e.next=8;break}return console.log("🔄 用户角色缓存未命中，从服务器获取..."),e.next=6,a();case 6:o=e.sent,o&&this.set(t,o);case 8:return e.abrupt("return",o||[]);case 9:case"end":return e.stop()}}),e,this)})));return function(n,a){return e.apply(this,arguments)}}()},{key:"getResponsibleList",value:function(){var e=(0,o.default)((0,i.default)().mark((function e(n){var a,t;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=this.get(this.cacheKeys.RESPONSIBLE_LIST),a||!n){e.next=7;break}return console.log("🔄 负责人列表缓存未命中，从服务器获取..."),e.next=5,n();case 5:a=e.sent,a&&(this.set(this.cacheKeys.RESPONSIBLE_LIST,a),t=a.reduce((function(e,n){return e[n._id]=n.nickname||n.username||"-",e}),{}),this.set(this.cacheKeys.RESPONSIBLE_MAP,t));case 7:return e.abrupt("return",a||[]);case 8:case"end":return e.stop()}}),e,this)})));return function(n){return e.apply(this,arguments)}}()},{key:"getResponsibleMap",value:function(){return this.get(this.cacheKeys.RESPONSIBLE_MAP)||{}}},{key:"getProjectOptions",value:function(){var e=(0,o.default)((0,i.default)().mark((function e(n){var a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=this.get(this.cacheKeys.PROJECT_OPTIONS),a||!n){e.next=7;break}return console.log("🔄 项目选项缓存未命中，使用默认数据..."),e.next=5,n();case 5:a=e.sent,a&&this.set(this.cacheKeys.PROJECT_OPTIONS,a);case 7:return e.abrupt("return",a||[{text:"全部",value:""},{text:"安全找茬",value:"安全找茬"},{text:"设备找茬",value:"设备找茬"},{text:"其他找茬",value:"其他找茬"}]);case 8:case"end":return e.stop()}}),e,this)})));return function(n){return e.apply(this,arguments)}}()},{key:"getStatusOptions",value:function(){var e=this.get(this.cacheKeys.STATUS_OPTIONS);return e||(console.log("🔄 状态选项缓存未命中，使用默认数据..."),e=[{text:"全部",value:""},{text:"待主管审核",value:"pending_supervisor"},{text:"主管已通过",value:"approved_supervisor"},{text:"需要例会讨论",value:"meeting_required"},{text:"待副厂长审核",value:"pending_pm"},{text:"副厂长已通过",value:"approved_pm"},{text:"待厂长审核",value:"pending_gm"},{text:"待指派负责人",value:"gm_approved_pending_assign"},{text:"已指派负责人",value:"assigned_to_responsible"},{text:"待厂长确认",value:"completed_by_responsible"},{text:"已完成",value:"final_completed"},{text:"已终止",value:"terminated"}],this.set(this.cacheKeys.STATUS_OPTIONS,e)),e}},{key:"getUrgencyOptions",value:function(){var e=this.get(this.cacheKeys.URGENCY_OPTIONS);return e||(console.log("🔄 紧急程度选项缓存未命中，使用默认数据..."),e=[{text:"全部",value:""},{text:"正常",value:"normal"},{text:"警告",value:"warning"},{text:"紧急",value:"urgent"}],this.set(this.cacheKeys.URGENCY_OPTIONS,e)),e}},{key:"clearUserRelatedCache",value:function(){console.log("🧹 清除用户相关缓存..."),this.remove(this.cacheKeys.USER_ROLE),this.remove(this.cacheKeys.USER_INFO)}},{key:"clearResponsibleCache",value:function(){console.log("🧹 清除负责人相关缓存..."),this.remove(this.cacheKeys.RESPONSIBLE_LIST),this.remove(this.cacheKeys.RESPONSIBLE_MAP)}}]),e}(),l=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return n?"".concat("zzps_").concat(e,"_").concat(n):"".concat("zzps_").concat(e)};n.getCacheKey=l;n.setCache=function(e,n){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{var t=a||c[e]||18e5,i={data:n,timestamp:Date.now(),expiry:Date.now()+t};return uni.setStorageSync(l(e),i),console.log("✅ 缓存设置成功: ".concat(e,", 过期时间: ").concat(new Date(i.expiry).toLocaleString())),!0}catch(o){return console.error("❌ 缓存设置失败: ".concat(e),o),!1}};n.getCache=function(e){try{var n=uni.getStorageSync(l(e));return n?Date.now()>n.expiry?(console.log("⏰ 缓存已过期: ".concat(e)),f(e),null):(console.log("✅ 缓存命中: ".concat(e)),n.data):(console.log("📭 缓存未找到: ".concat(e)),null)}catch(a){return console.error("❌ 缓存获取失败: ".concat(e),a),null}};var f=function(e){try{return uni.removeStorageSync(l(e)),console.log("🗑️ 缓存已清除: ".concat(e)),!0}catch(n){return console.error("❌ 缓存清除失败: ".concat(e),n),!1}};n.removeCache=f;n.hasValidCache=function(e){try{var n=uni.getStorageSync(l(e));return n&&Date.now()<=n.expiry}catch(a){return!1}};n.clearAllCache=function(){try{return Object.values(d).forEach((function(e){f(e)})),console.log("🧹 所有缓存已清除"),!0}catch(e){return console.error("❌ 清除所有缓存失败",e),!1}};var _=new u;n.cacheManager=_;var k=_;n.default=k},f991f:function(e,n,a){"use strict";(function(e){a("6a54");var t=a("f5bd").default,i=a("3639").default;Object.defineProperty(n,"__esModule",{value:!0}),n.database=void 0,n.handleTokenInvalid=g,a("bf0f"),a("de6c"),a("2797"),a("4626"),a("5ac7"),a("9db6");var o=a("423e"),r=i(a("eddf")),s=t(a("7e11")),p=["TOKEN_INVALID","TOKEN_EXPIRED","INVALID_TOKEN","uni-id-token-expired","uni-id-check-token-failed"];function g(){uni.removeStorageSync("uni_id_token"),uni.removeStorageSync("uni_id_token_expired"),uni.removeStorageSync("uni_id_user"),uni.removeStorageSync("uni-id-pages-userInfo"),uni.removeStorageSync((0,r.getCacheKey)(r.CACHE_KEYS.USER_ROLE)),o.mutations.setUserInfo({},{cover:!0}),r.default.remove("userRole"),r.default.remove("user_info"),r.default.remove("projectOptions"),s.default.clearBadge(),function(){try{var e=uni.getStorageInfoSync(),n=e.keys,a=["user_info_","user_mgmt_"],t=["_DC_STAT_UUID",(0,r.getCacheKey)("recent_pages"),"last_app_start_time","uni-id-pages-userInfo"];n.forEach((function(e){if(!t.includes(e)){var n=a.some((function(n){return e===n||e.startsWith(n)}));n&&(uni.removeStorageSync(e))}}))}catch(i){console.error("拦截器清除敏感缓存失败:",i)}}(),uni.$emit("token-invalid")}var d=e.database();d.interceptorAdd("callFunction",{invoke:function(e){console.log("发起请求:",e)},success:function(e){return console.log("请求成功:",e),e.result&&(p.includes(e.result.code)||p.includes(e.result.errCode))&&(console.log("检测到token失效，开始处理..."),g()),e},fail:function(e){return console.error("请求失败:",e),e.message&&(e.message.includes("token")||e.message.includes("TOKEN"))&&(console.log("检测到token相关错误，开始处理..."),g()),e},complete:function(e){return console.log("请求完成"),e}});var c=d;n.database=c}).call(this,a("861b")["uniCloud"])}});
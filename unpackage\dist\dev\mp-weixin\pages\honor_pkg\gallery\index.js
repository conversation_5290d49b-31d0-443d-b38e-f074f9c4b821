require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/honor_pkg/gallery/index"],{

/***/ 236:
/*!**********************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fhonor_pkg%2Fgallery%2Findex"} ***!
  \**********************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index2 = _interopRequireDefault(__webpack_require__(/*! ./pages/honor_pkg/gallery/index.vue */ 237));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index2.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 237:
/*!*************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/gallery/index.vue ***!
  \*************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_4213b958___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=4213b958& */ 238);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 240);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&lang=scss& */ 242);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_4213b958___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_4213b958___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _index_vue_vue_type_template_id_4213b958___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/honor_pkg/gallery/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 238:
/*!********************************************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/gallery/index.vue?vue&type=template&id=4213b958& ***!
  \********************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4213b958___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4213b958& */ 239);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4213b958___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4213b958___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4213b958___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4213b958___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 239:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/honor_pkg/gallery/index.vue?vue&type=template&id=4213b958& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    uniLoadMore: function () {
      return Promise.all(/*! import() | uni_modules/uni-load-more/components/uni-load-more/uni-load-more */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-load-more/components/uni-load-more/uni-load-more")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue */ 497))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 483))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 =
    _vm.currentView === "grid" && _vm.currentPageData && !_vm.loading
      ? _vm.currentPageData.honors.length
      : null
  var g1 =
    _vm.currentView === "grid" && !_vm.loading
      ? _vm.currentPageData && _vm.currentPageData.honors.length > 0
      : null
  var g2 =
    _vm.currentView === "grid"
      ? _vm.totalPages > 1 && _vm.gridPagedData.length > 0
      : null
  var g3 =
    _vm.currentView === "grid"
      ? _vm.totalPages > 1 && _vm.gridPagedData.length > 0
      : null
  var g4 = _vm.currentView === "list" ? _vm.displayListHonors.length : null
  var g5 =
    _vm.currentView === "list" && _vm.listViewMode === "grouped"
      ? _vm.groupedListHonors.length
      : null
  var g6 =
    _vm.currentView === "list" && !_vm.loading
      ? _vm.displayListHonors.length
      : null
  var g7 =
    _vm.currentView === "ranking" && !_vm.loading
      ? _vm.sortedHonorsWithRank.length
      : null
  var g8 = _vm.currentView === "grid" ? _vm.displayHonors.length : null
  var g9 =
    _vm.currentView === "grid" && !_vm.loading ? _vm.displayHonors.length : null
  var g10 = _vm.selectedHonor
    ? _vm.selectedHonor.images && _vm.selectedHonor.images.length > 0
    : null
  var g11 = _vm.selectedHonor && g10 ? _vm.selectedHonor.images.length : null
  var g12 = _vm.selectedHonor && g10 ? _vm.selectedHonor.images.length : null
  var l0 =
    _vm.selectedHonor && g10 && !(g12 === 1)
      ? _vm.__map(
          _vm.selectedHonor.images.slice(0, 5),
          function (image, index) {
            var $orig = _vm.__get_orig(image)
            var g13 = index === 4 && _vm.selectedHonor.images.length > 5
            var g14 = g13 ? _vm.selectedHonor.images.length : null
            return {
              $orig: $orig,
              g13: g13,
              g14: g14,
            }
          }
        )
      : null
  if (!_vm._isMounted) {
    _vm.e0 = function ($event, image) {
      var _temp = arguments[arguments.length - 1].currentTarget.dataset,
        _temp2 = _temp.eventParams || _temp["event-params"],
        image = _temp2.image
      var _temp, _temp2
      return _vm.previewImage(image.url, _vm.selectedHonor.images)
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        g2: g2,
        g3: g3,
        g4: g4,
        g5: g5,
        g6: g6,
        g7: g7,
        g8: g8,
        g9: g9,
        g10: g10,
        g11: g11,
        g12: g12,
        l0: l0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 240:
/*!**************************************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/gallery/index.vue?vue&type=script&lang=js& ***!
  \**************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 241);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 241:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/honor_pkg/gallery/index.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, uniCloud, wx) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ 13);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _vuex = __webpack_require__(/*! vuex */ 53);
var _methods;
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var PEmptyState = function PEmptyState() {
  __webpack_require__.e(/*! require.ensure | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then((function () {
    return resolve(__webpack_require__(/*! @/components/p-empty-state/p-empty-state.vue */ 483));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  name: 'HonorGallery',
  components: {
    PEmptyState: PEmptyState
  },
  data: function data() {
    return {
      loading: true,
      currentView: 'grid',
      selectedHonor: null,
      currentMonth: new Date().getMonth() + 1,
      currentYear: new Date().getFullYear(),
      scrollTop: 0,
      refreshing: false,
      currentPage: 1,
      pageSize: 9,
      autoPlayTimer: null,
      autoPlayInterval: 8000,
      honors: [],
      allHonorsData: [],
      lastUserId: '',
      // 添加用户ID追踪
      pendingHonorId: null,
      // 设备性能等级：'low', 'medium', 'high'
      devicePerformance: 'medium',
      // 手势相关
      touchStartX: 0,
      touchStartY: 0,
      touchStartTime: 0,
      touchMoved: false,
      minSwipeDistance: 50,
      maxSwipeTime: 500,
      swipeThreshold: 0.3,
      // 防抖相关
      debounceTimers: {},
      isProcessing: false,
      // 排行榜筛选
      currentRankingPeriod: 'month',
      currentRankingMetric: 'likes',
      // 智能时间选择器
      currentWeek: 1,
      // 当前选择的周数
      currentQuarter: 1,
      // 当前选择的季度
      currentWeekOfMonth: 1,
      // 当前月份的第几周（1-4周）

      // 列表视图模式
      listViewMode: 'all',
      // 'all' 或 'grouped'
      expandedUsers: {},
      // 记录展开状态的用户

      // 排行榜时间范围选项
      rankingPeriods: [{
        value: 'week',
        label: '本周',
        icon: 'checkbox-filled'
      }, {
        value: 'month',
        label: '本月',
        icon: 'calendar-filled'
      }, {
        value: 'quarter',
        label: '本季度',
        icon: 'bars'
      }, {
        value: 'year',
        label: '本年度',
        icon: 'medal-filled'
      }],
      // 排行榜指标选项
      rankingMetrics: [{
        value: 'likes',
        label: '点赞',
        icon: 'heart-filled'
      }, {
        value: 'views',
        label: '浏览',
        icon: 'eye-filled'
      }, {
        value: 'awards',
        label: '获奖',
        icon: 'medal-filled'
      }, {
        value: 'featured',
        label: '精选',
        icon: 'fire-filled'
      }, {
        value: 'comprehensive',
        label: '综合',
        icon: 'star-filled'
      }],
      // 缓存相关
      honorsCache: {},
      rankingCache: {},
      // 排行榜数据缓存，按时间范围分别缓存
      lastCacheTime: 0,
      cacheExpireTime: 5 * 60 * 1000,
      // 缓存过期时间：5分钟
      maxCacheSize: 10,
      // 最大缓存条目数（控制内存占用）
      maxCacheItemSize: 100,
      // 单个缓存项最大记录数

      // 用户点赞统计
      userLikeStats: null,
      // 点赞操作状态
      likeProcessing: {},
      // 记录正在处理的点赞操作 { honorId: true }

      // 自定义toast
      customToast: {
        show: false,
        message: '',
        type: 'success',
        // success, error, warning
        timer: null
      }
    };
  },
  computed: _objectSpread(_objectSpread({}, (0, _vuex.mapGetters)({
    userInfo: 'user/userInfo'
  })), {}, {
    // 判断是否为管理员 - 使用uniIDHasRole方法
    isAdmin: function isAdmin() {
      return this.uniIDHasRole('admin') || this.uniIDHasRole('supervisor') || this.uniIDHasRole('PM') || this.uniIDHasRole('GM') || this.uniIDHasRole('reviser');
    },
    // 显示的荣誉数据
    displayHonors: function displayHonors() {
      var _this$userInfo,
        _this = this;
      var currentUserId = ((_this$userInfo = this.userInfo) === null || _this$userInfo === void 0 ? void 0 : _this$userInfo._id) || '';
      var cacheKey = "".concat(currentUserId, "_").concat(this.currentYear, "-").concat(this.currentMonth);
      var now = Date.now();

      // 检查缓存是否有效
      if (this.honorsCache[cacheKey] && now - this.lastCacheTime < this.cacheExpireTime) {
        return this.honorsCache[cacheKey].data;
      }

      // 过滤当前月份的数据
      var filteredHonors = this.allHonorsData.filter(function (honor) {
        // 检查用户ID是否匹配
        if (honor._userId !== currentUserId) {
          return false;
        }
        var honorDate = new Date(honor.createTime);
        var honorYear = honorDate.getFullYear();
        var honorMonth = honorDate.getMonth() + 1;
        return honorYear === _this.currentYear && honorMonth === _this.currentMonth;
      });

      // 更新缓存
      this.honorsCache[cacheKey] = {
        data: filteredHonors,
        timestamp: now
      };
      this.lastCacheTime = now;

      // 执行缓存管理
      this.manageCacheSize();
      return filteredHonors;
    },
    // 当前月份的荣誉数据
    getCurrentMonthHonors: function getCurrentMonthHonors() {
      return this.displayHonors || [];
    },
    // 按批次分组的荣誉数据
    honorsByBatch: function honorsByBatch() {
      if (!this.displayHonors) return [];
      var grouped = {};
      this.displayHonors.forEach(function (honor) {
        var batchKey = honor.batch.name || honor.batch.id;
        if (!grouped[batchKey]) {
          grouped[batchKey] = {
            batch: honor.batch,
            honors: []
          };
        }
        grouped[batchKey].honors.push(honor);
      });
      return Object.values(grouped).sort(function (a, b) {
        var dateA = new Date(a.batch.meetingDate || '1970-01-01');
        var dateB = new Date(b.batch.meetingDate || '1970-01-01');
        if (dateB.getTime() !== dateA.getTime()) {
          return dateB - dateA;
        }
        return (a.batch.name || '').localeCompare(b.batch.name || '');
      });
    },
    // 宫格视图分页数据
    gridPagedData: function gridPagedData() {
      var batches = this.honorsByBatch;
      var totalPages = Math.ceil(batches.length / 1);
      var pages = [];
      for (var i = 0; i < totalPages; i++) {
        var batch = batches[i];
        if (batch) {
          pages.push({
            pageIndex: i,
            batch: batch.batch,
            honors: batch.honors.slice(0, 9),
            hasMore: batch.honors.length > 9
          });
        }
      }
      return pages;
    },
    // 统计数据
    featuredCount: function featuredCount() {
      return this.getCurrentMonthHonors.filter(function (honor) {
        return honor.isFeatured;
      }).length;
    },
    totalViews: function totalViews() {
      var currentMonthData = this.getCurrentMonthHonors;
      var total = currentMonthData.reduce(function (sum, honor) {
        var viewCount = typeof honor.viewCount === 'number' && honor.viewCount >= 0 ? honor.viewCount : 0;
        return sum + viewCount;
      }, 0);
      return total;
    },
    totalLikes: function totalLikes() {
      return this.getCurrentMonthHonors.reduce(function (sum, honor) {
        return sum + (honor.likeCount || 0);
      }, 0);
    },
    // 总页数
    totalPages: function totalPages() {
      return this.gridPagedData.length || 1;
    },
    // 当前页的批次数据
    currentPageData: function currentPageData() {
      var pageIndex = this.currentPage - 1;
      var pages = this.gridPagedData;
      if (pageIndex >= pages.length) {
        var fallback = pages.length > 0 ? pages[0] : null;
        return fallback;
      }
      var result = pages[pageIndex] || null;
      return result;
    },
    // 排行榜数据
    sortedHonorsWithRank: function sortedHonorsWithRank() {
      var _this2 = this;
      var metric = this.currentRankingMetric;
      var period = this.currentRankingPeriod;

      // 生成排行榜缓存键
      var cacheKey = "ranking-".concat(period, "-").concat(metric, "-").concat(this.currentYear, "-").concat(this.currentMonth, "-").concat(this.currentWeekOfMonth, "-").concat(this.currentQuarter);

      // 检查缓存是否有效
      if (this.rankingCache && this.rankingCache[cacheKey]) {
        var cachedData = this.rankingCache[cacheKey];
        // 检查数据时效性（5分钟缓存）
        if (Date.now() - cachedData.timestamp < 5 * 60 * 1000) {
          return cachedData.data;
        }
      }
      var filtered = this.getFilteredHonorsByPeriod();

      // 按用户聚合所有数据
      var userAggregatedData = {};
      filtered.forEach(function (honor) {
        if (!userAggregatedData[honor.userName]) {
          userAggregatedData[honor.userName] = {
            userName: honor.userName,
            userAvatar: honor.userAvatar,
            department: honor.department,
            // 使用最新的一条记录作为显示基础
            latestHonor: honor,
            // 累计统计数据
            totalAwards: 0,
            totalFeatured: 0,
            totalLikes: 0,
            totalViews: 0,
            // 荣誉类型统计（显示最常获得的奖项）
            honorTypes: {}
          };
        }
        var userData = userAggregatedData[honor.userName];

        // 更新最新记录（用于显示）
        if (new Date(honor.createTime) > new Date(userData.latestHonor.createTime)) {
          userData.latestHonor = honor;
        }

        // 累计统计
        userData.totalAwards++;
        if (honor.isFeatured) {
          userData.totalFeatured++;
        }
        userData.totalLikes += honor.likeCount || 0;
        userData.totalViews += honor.viewCount || 0;

        // 统计荣誉类型
        var typeName = honor.honorType.name;
        userData.honorTypes[typeName] = (userData.honorTypes[typeName] || 0) + 1;
      });

      // 转换为数组并计算分数
      var userRankingData = Object.values(userAggregatedData).map(function (userData) {
        // 找出最常获得的荣誉类型
        var mostFrequentType = Object.keys(userData.honorTypes).reduce(function (a, b) {
          return userData.honorTypes[a] > userData.honorTypes[b] ? a : b;
        }, Object.keys(userData.honorTypes)[0] || '未知');
        return {
          // 基础显示信息（来自最新记录）
          id: userData.latestHonor.id,
          userName: userData.userName,
          userAvatar: userData.userAvatar,
          department: userData.department,
          createTime: userData.latestHonor.createTime,
          // 显示最常获得的荣誉类型
          honorType: {
            id: userData.latestHonor.honorType.id,
            name: mostFrequentType
          },
          reason: userData.latestHonor.reason,
          batch: userData.latestHonor.batch,
          images: userData.latestHonor.images,
          isFeatured: userData.totalFeatured > 0,
          // 只要有精选就显示为精选用户
          // 累计统计数据
          totalAwards: userData.totalAwards,
          totalFeatured: userData.totalFeatured,
          totalLikes: userData.totalLikes,
          totalViews: userData.totalViews,
          // 为了兼容现有显示逻辑，设置这些字段
          viewCount: userData.totalViews,
          likeCount: userData.totalLikes
        };
      });

      // 排序
      var sorted = userRankingData.sort(function (a, b) {
        var aScore = _this2.calculateRankingScore(a, metric);
        var bScore = _this2.calculateRankingScore(b, metric);
        if (bScore === aScore) {
          // 相同分数时的排序规则
          switch (metric) {
            case 'likes':
              if (b.totalViews !== a.totalViews) return b.totalViews - a.totalViews;
              return b.totalAwards - a.totalAwards;
            case 'views':
              if (b.totalLikes !== a.totalLikes) return b.totalLikes - a.totalLikes;
              return b.totalAwards - a.totalAwards;
            case 'awards':
              if (b.totalFeatured !== a.totalFeatured) return b.totalFeatured - a.totalFeatured;
              return b.totalLikes - a.totalLikes;
            case 'featured':
              if (b.totalAwards !== a.totalAwards) return b.totalAwards - a.totalAwards;
              return b.totalLikes - a.totalLikes;
            case 'comprehensive':
              if (b.totalFeatured !== a.totalFeatured) return b.totalFeatured - a.totalFeatured;
              if (b.totalAwards !== a.totalAwards) return b.totalAwards - a.totalAwards;
              return b.totalLikes - a.totalLikes;
            default:
              return b.totalLikes - a.totalLikes;
          }
        }
        return bScore - aScore;
      });
      var finalRanking = sorted.map(function (userData, index) {
        var displayScore = _this2.calculateRankingScore(userData, metric);
        var scoreLabel = _this2.getScoreLabel(metric);
        return _objectSpread(_objectSpread({}, userData), {}, {
          rankClass: index === 0 ? 'rank-1' : index === 1 ? 'rank-2' : index === 2 ? 'rank-3' : '',
          displayScore: displayScore,
          scoreLabel: scoreLabel,
          _rankKey: "".concat(metric, "-").concat(period, "-").concat(index, "-").concat(userData.userName)
        });
      });

      // 缓存结果
      if (!this.rankingCache) {
        this.rankingCache = {};
      }
      this.rankingCache[cacheKey] = {
        data: finalRanking,
        timestamp: Date.now()
      };

      // 排行榜数据准备完成

      return finalRanking;
    },
    // 图片网格样式类
    imageGridClass: function imageGridClass() {
      if (!this.selectedHonor || !this.selectedHonor.images) {
        return 'grid-single';
      }
      var imageCount = this.selectedHonor.images.length;
      if (imageCount === 1) return 'grid-single';
      if (imageCount === 2) return 'grid-double';
      if (imageCount === 3) return 'grid-triple';
      if (imageCount === 4) return 'grid-quad';
      if (imageCount >= 5) return 'grid-five';
      return 'grid-single';
    },
    // 列表视图显示的数据
    displayListHonors: function displayListHonors() {
      return this.getCurrentMonthHonors;
    },
    // 按用户分组的列表数据
    groupedListHonors: function groupedListHonors() {
      var _this3 = this;
      var grouped = {};

      // 按用户名分组
      this.getCurrentMonthHonors.forEach(function (honor) {
        if (!grouped[honor.userName]) {
          grouped[honor.userName] = {
            userName: honor.userName,
            userAvatar: honor.userAvatar,
            honors: [],
            totalCount: 0,
            featuredCount: 0,
            totalViews: 0,
            totalLikes: 0,
            expanded: _this3.expandedUsers[honor.userName] || false,
            hasLiked: false // 初始化为false
          };
        }

        grouped[honor.userName].honors.push(honor);
        grouped[honor.userName].totalCount++;
        if (honor.isFeatured) {
          grouped[honor.userName].featuredCount++;
        }
        grouped[honor.userName].totalViews += honor.viewCount || 0;
        grouped[honor.userName].totalLikes += honor.likeCount || 0;

        // 如果有任何一条荣誉被点赞，设置hasLiked为true
        if (honor.isLiked) {
          grouped[honor.userName].hasLiked = true;
        }
      });

      // 转换为数组并排序（按总获奖次数降序，然后按精选次数降序）
      return Object.values(grouped).sort(function (a, b) {
        if (b.featuredCount !== a.featuredCount) {
          return b.featuredCount - a.featuredCount; // 精选次数优先
        }

        if (b.totalCount !== a.totalCount) {
          return b.totalCount - a.totalCount; // 总获奖次数其次
        }

        return b.totalLikes - a.totalLikes; // 最后按点赞数
      }).map(function (group) {
        // 对每个用户的奖项按时间倒序排列
        group.honors.sort(function (a, b) {
          return new Date(b.createTime) - new Date(a.createTime);
        });
        return group;
      });
    },
    // 智能时间显示 - 根据排行榜筛选维度动态显示
    currentTimeDisplay: function currentTimeDisplay() {
      if (this.currentView === 'ranking') {
        switch (this.currentRankingPeriod) {
          case 'week':
            return "".concat(this.currentYear, "\u5E74").concat(this.currentMonth, "\u6708\u7B2C").concat(this.currentWeekOfMonth, "\u5468");
          case 'month':
            return "".concat(this.currentYear, "\u5E74").concat(this.currentMonth, "\u6708");
          case 'quarter':
            return "".concat(this.currentYear, "\u5E74\u7B2C").concat(this.currentQuarter, "\u5B63\u5EA6");
          case 'year':
            return "".concat(this.currentYear, "\u5E74");
          default:
            return "".concat(this.currentYear, "\u5E74").concat(this.currentMonth, "\u6708");
        }
      }
      // 非排行榜视图显示传统的年月格式
      return "".concat(this.currentYear, "\u5E74").concat(this.currentMonth, "\u6708");
    }
  }),
  watch: {
    userInfo: {
      immediate: true,
      handler: function handler(newUserInfo) {
        var newUserId = (newUserInfo === null || newUserInfo === void 0 ? void 0 : newUserInfo._id) || '';
        if (this.lastUserId !== newUserId) {
          this.lastUserId = newUserId;
          this.honors = [];
          this.allHonorsData = [];
          this.honorsCache = {};
          this.loadAllHonorData();
        }
      }
    }
  },
  onLoad: function onLoad(options) {
    var _this4 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      var initPromises, honor;
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _this4.loading = true;

              // 并行初始化基础数据，减少等待时间
              initPromises = [_this4.initUserInfo(), _this4.loadAllHonorData() // 与用户信息并行加载
              ]; // 同步执行不耗时的操作

              _this4.initTimeData();
              _this4.detectDevicePerformance();

              // 等待并行任务完成
              _context.next = 7;
              return Promise.all(initPromises);
            case 7:
              // 如果有传入的honorId，在allHonorsData中查找
              if (options.honorId) {
                honor = _this4.allHonorsData.find(function (h) {
                  return h.id === options.honorId;
                }) || _this4.honors.find(function (h) {
                  return h.id === options.honorId;
                });
                if (honor) {
                  _this4.selectedHonor = honor;
                  _this4.incrementViewCount(honor.id);
                }
              }

              // 优化后的展厅初始化（已移除内部延迟）
              _context.next = 10;
              return _this4.initGallery();
            case 10:
              // 强制触发displayHonors重新计算
              _this4.$forceUpdate();

              // 异步清理存储空间（不阻塞页面显示）
              setTimeout(function () {
                _this4.clearStorageSpace();
              }, 2000);
              _context.next = 18;
              break;
            case 14:
              _context.prev = 14;
              _context.t0 = _context["catch"](0);
              _this4.showCustomToast('页面加载失败', 'error');
              _this4.loading = false;
            case 18:
            case "end":
              return _context.stop();
          }
        }
      }, _callee, null, [[0, 14]]);
    }))();
  },
  onShow: function onShow() {
    this.initUserInfo();
    if (this.currentView === 'grid' && this.totalPages > 1) {
      this.startAutoPlay();
    }
  },
  onHide: function onHide() {
    this.stopAutoPlay();
  },
  beforeDestroy: function beforeDestroy() {
    var _this5 = this;
    this.stopAutoPlay();
    Object.keys(this.debounceTimers).forEach(function (key) {
      if (typeof _this5.debounceTimers[key] === 'number') {
        clearTimeout(_this5.debounceTimers[key]);
      }
    });
    this.debounceTimers = {};

    // 清理自定义toast定时器
    if (this.customToast.timer) {
      clearTimeout(this.customToast.timer);
    }
  },
  methods: (_methods = {
    // 初始化用户信息
    initUserInfo: function initUserInfo() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var token, cachedUserInfo, userInfo, _yield$import, getCacheKey, CACHE_KEYS, userRoleStr, userRole, fallbackUserInfo, _userInfo;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                token = uni.getStorageSync('uni_id_token');
                cachedUserInfo = uni.getStorageSync('uni-id-pages-userInfo');
                if (!(cachedUserInfo && token)) {
                  _context2.next = 31;
                  break;
                }
                _context2.prev = 4;
                userInfo = typeof cachedUserInfo === 'string' ? JSON.parse(cachedUserInfo) : cachedUserInfo; // 确保用户信息包含必要的ID字段
                if (!(userInfo._id || userInfo.uid)) {
                  _context2.next = 25;
                  break;
                }
                _context2.prev = 7;
                _context2.next = 10;
                return Promise.resolve().then(function () {
                  return _interopRequireWildcard(__webpack_require__(/*! @/utils/cache.js */ 45));
                });
              case 10:
                _yield$import = _context2.sent;
                getCacheKey = _yield$import.getCacheKey;
                CACHE_KEYS = _yield$import.CACHE_KEYS;
                userRoleStr = uni.getStorageSync(getCacheKey(CACHE_KEYS.USER_ROLE));
                if (userRoleStr) {
                  userRole = typeof userRoleStr === 'string' ? JSON.parse(userRoleStr) : userRoleStr;
                  if (userRole && userRole.value && userRole.value.userRole) {
                    userInfo.role = userRole.value.userRole;
                  }
                }
                _context2.next = 19;
                break;
              case 17:
                _context2.prev = 17;
                _context2.t0 = _context2["catch"](7);
              case 19:
                // 设置用户ID和token
                userInfo.uid = userInfo._id || userInfo.uid;
                userInfo.token = token;
                _this6.$store.dispatch('user/loginSuccess', {
                  userInfo: userInfo,
                  token: token,
                  permissions: userInfo.permissions || []
                });
                return _context2.abrupt("return");
              case 25:
                // 用户信息缺少ID字段，删除无效缓存
                uni.removeStorageSync('uni-id-pages-userInfo');
              case 26:
                _context2.next = 31;
                break;
              case 28:
                _context2.prev = 28;
                _context2.t1 = _context2["catch"](4);
                // 解析本地存储的用户信息失败，忽略错误
                uni.removeStorageSync('uni-id-pages-userInfo');
              case 31:
                // 如果本地用户信息无效，尝试获取当前用户信息
                // 这里可以调用 uniCloud 获取用户信息，但对于荣誉展厅，用户应该已经在其他地方登录过了
                // 所以如果走到这里，说明用户状态有问题
                fallbackUserInfo = uni.getStorageSync('uni_id_user_info');
                if (!(fallbackUserInfo && token)) {
                  _context2.next = 39;
                  break;
                }
                _userInfo = typeof fallbackUserInfo === 'string' ? JSON.parse(fallbackUserInfo) : fallbackUserInfo;
                _userInfo.uid = _userInfo._id || _userInfo.uid;
                _userInfo.token = token;
                if (!_userInfo.uid) {
                  _context2.next = 39;
                  break;
                }
                _this6.$store.dispatch('user/loginSuccess', {
                  userInfo: _userInfo,
                  token: token,
                  permissions: _userInfo.permissions || []
                });
                return _context2.abrupt("return");
              case 39:
                _context2.next = 43;
                break;
              case 41:
                _context2.prev = 41;
                _context2.t2 = _context2["catch"](0);
              case 43:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 41], [4, 28], [7, 17]]);
      }))();
    },
    // 返回上一页
    goBack: function goBack() {
      uni.navigateBack({
        delta: 1,
        fail: function fail() {
          uni.switchTab({
            url: '/pages/ucenter/ucenter'
          });
        }
      });
    },
    // 跳转到管理员页面
    goToAdmin: function goToAdmin() {
      if (!this.isAdmin) {
        this.showCustomToast('无管理权限', 'error');
        return;
      }
      uni.navigateTo({
        url: '/pages/honor_pkg/admin/index'
      });
    },
    // 防抖工具方法
    debounce: function debounce(key, fn) {
      var _this7 = this;
      var delay = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 300;
      if (this.debounceTimers[key]) {
        clearTimeout(this.debounceTimers[key]);
      }
      this.debounceTimers[key] = setTimeout(function () {
        fn.call(_this7);
        delete _this7.debounceTimers[key];
      }, delay);
    },
    debounceImmediate: function debounceImmediate(key, fn) {
      var _this8 = this;
      var delay = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 100;
      // 为滚动相关的操作使用更小的延迟
      var scrollKeys = ['rankingPeriod', 'rankingMetric', 'listViewMode'];
      var optimizedDelay = scrollKeys.includes(key) ? 30 : delay;
      if (this.debounceTimers[key]) {
        clearTimeout(this.debounceTimers[key]);
      }

      // 立即执行一次，然后设置延迟
      if (scrollKeys.includes(key)) {
        fn();
        return;
      }
      this.debounceTimers[key] = setTimeout(function () {
        fn();
        delete _this8.debounceTimers[key];
      }, optimizedDelay);
    },
    // 初始化展厅
    initGallery: function initGallery() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var _this9$userInfo, _this9$userInfo2;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                if (!(_this9.allHonorsData.length === 0)) {
                  _context3.next = 4;
                  break;
                }
                _context3.next = 4;
                return _this9.loadHonorData();
              case 4:
                // 立即隐藏loading，避免骨架屏显示过久
                _this9.loading = false;

                // 异步加载用户点赞统计（不阻塞页面显示）
                if ((_this9$userInfo = _this9.userInfo) !== null && _this9$userInfo !== void 0 && _this9$userInfo.uid || (_this9$userInfo2 = _this9.userInfo) !== null && _this9$userInfo2 !== void 0 && _this9$userInfo2._id) {
                  setTimeout(function () {
                    _this9.loadUserLikeStats();
                  }, 1000);
                }
                _context3.next = 12;
                break;
              case 8:
                _context3.prev = 8;
                _context3.t0 = _context3["catch"](0);
                // 展厅初始化失败
                _this9.showCustomToast('加载失败', 'error');
                _this9.loading = false;
              case 12:
                _context3.prev = 12;
                // 使用immediate模式启动自动轮播
                if (_this9.currentView === 'grid' && _this9.totalPages > 1) {
                  _this9.startAutoPlay();
                }
                return _context3.finish(12);
              case 15:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 8, 12, 15]]);
      }))();
    },
    // 加载完整荣誉数据（用于排行榜）
    loadAllHonorData: function loadAllHonorData() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var _this10$userInfo, _this10$userInfo2, currentUserId, requestData, result, _this10$userInfo3, rawData;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.prev = 0;
                currentUserId = ((_this10$userInfo = _this10.userInfo) === null || _this10$userInfo === void 0 ? void 0 : _this10$userInfo._id) || '';
                requestData = {
                  action: 'getHonorList',
                  data: {
                    page: 1,
                    size: 500,
                    status: 'published'
                  },
                  uniIdToken: ((_this10$userInfo2 = _this10.userInfo) === null || _this10$userInfo2 === void 0 ? void 0 : _this10$userInfo2.token) || uni.getStorageSync('uni_id_token')
                };
                _context4.next = 5;
                return uniCloud.callFunction({
                  name: 'honor-gallery',
                  data: requestData
                });
              case 5:
                result = _context4.sent;
                if (result.result.code === 0) {
                  // 确保用户ID没有改变
                  if (currentUserId === (((_this10$userInfo3 = _this10.userInfo) === null || _this10$userInfo3 === void 0 ? void 0 : _this10$userInfo3._id) || '')) {
                    rawData = result.result.data.list || [];
                    _this10.allHonorsData = _this10.convertToPageFormat(rawData);

                    // 清除缓存
                    _this10.honorsCache = {};
                    _this10.clearRankingCache();
                  }
                }
                _context4.next = 13;
                break;
              case 9:
                _context4.prev = 9;
                _context4.t0 = _context4["catch"](0);
                console.error('加载荣誉数据失败:', _context4.t0);
                _this10.allHonorsData = [];
              case 13:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[0, 9]]);
      }))();
    },
    // 加载荣誉数据
    loadHonorData: function loadHonorData() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var _this11$userInfo, requestData, result, rawData, honor;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                requestData = {
                  action: 'getHonorList',
                  data: {
                    year: _this11.currentYear,
                    month: _this11.currentMonth,
                    page: 1,
                    size: 100,
                    status: 'published'
                  },
                  uniIdToken: ((_this11$userInfo = _this11.userInfo) === null || _this11$userInfo === void 0 ? void 0 : _this11$userInfo.token) || uni.getStorageSync('uni_id_token') // 传递用户token
                };
                _context5.next = 4;
                return uniCloud.callFunction({
                  name: 'honor-gallery',
                  data: requestData
                });
              case 4:
                result = _context5.sent;
                if (!(result.result.code === 0)) {
                  _context5.next = 11;
                  break;
                }
                rawData = result.result.data.list || [];
                _this11.honors = _this11.convertToPageFormat(rawData);
                if (_this11.pendingHonorId) {
                  honor = _this11.honors.find(function (h) {
                    return h.id === _this11.pendingHonorId;
                  });
                  if (honor) {
                    _this11.selectedHonor = honor;
                    _this11.incrementViewCount(honor.id);
                  }
                  _this11.pendingHonorId = null;
                }
                _context5.next = 12;
                break;
              case 11:
                throw new Error(result.result.message || '数据加载失败');
              case 12:
                _context5.next = 17;
                break;
              case 14:
                _context5.prev = 14;
                _context5.t0 = _context5["catch"](0);
                // 荣誉数据加载失败
                _this11.honors = [];
              case 17:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 14]]);
      }))();
    },
    // 计算周次标签（优化版本）
    calculateWeekLabel: function calculateWeekLabel(batchName, meetingDate) {
      // 首先尝试从批次名称中提取
      if (batchName) {
        var weekMatch = batchName.match(/第(\d+)周/);
        if (weekMatch) {
          return "\u7B2C".concat(weekMatch[1], "\u5468");
        }
      }

      // 如果没有找到，尝试从日期计算（使用优化后的简单算法）
      if (meetingDate) {
        try {
          var date = new Date(meetingDate.replace(/\//g, '-'));
          if (!isNaN(date.getTime())) {
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();

            // 使用简单的周次计算：每7天为一周
            var week = Math.ceil(day / 7);
            return "\u7B2C".concat(Math.min(week, 4), "\u5468"); // 最多4周
          }
        } catch (e) {
          // 计算周次失败，使用默认值
        }
      }
      return '第0周'; // 默认值
    },
    // 将云数据库数据转换为页面所需格式
    convertToPageFormat: function convertToPageFormat(cloudData) {
      var _this$userInfo2,
        _this12 = this;
      if (!Array.isArray(cloudData)) {
        return [];
      }
      var currentUserId = ((_this$userInfo2 = this.userInfo) === null || _this$userInfo2 === void 0 ? void 0 : _this$userInfo2._id) || '';
      return cloudData.map(function (item) {
        var _item$honorType, _item$batch, _item$batch2, _item$batch3, _item$batch4, _item$batch5, _item$batch6, _item$batch7;
        var viewCount = typeof item.viewCount === 'number' ? item.viewCount : 0;
        return {
          id: item._id,
          userId: item.userId,
          userName: item.userName,
          userAvatar: item.userAvatar || '/static/user/default-avatar.png',
          department: item.department,
          honorType: {
            id: item.honorTypeId,
            name: ((_item$honorType = item.honorType) === null || _item$honorType === void 0 ? void 0 : _item$honorType.name) || '未知类型'
          },
          reason: item.reason,
          images: item.images || [],
          createTime: item.createTime,
          batch: {
            id: item.batchId,
            name: ((_item$batch = item.batch) === null || _item$batch === void 0 ? void 0 : _item$batch.name) || '未知批次',
            type: ((_item$batch2 = item.batch) === null || _item$batch2 === void 0 ? void 0 : _item$batch2.type) || 'weekly',
            period: ((_item$batch3 = item.batch) === null || _item$batch3 === void 0 ? void 0 : _item$batch3.period) || '',
            meetingDate: ((_item$batch4 = item.batch) === null || _item$batch4 === void 0 ? void 0 : _item$batch4.meetingDate) || '',
            weekLabel: ((_item$batch5 = item.batch) === null || _item$batch5 === void 0 ? void 0 : _item$batch5.weekLabel) || _this12.calculateWeekLabel((_item$batch6 = item.batch) === null || _item$batch6 === void 0 ? void 0 : _item$batch6.name, (_item$batch7 = item.batch) === null || _item$batch7 === void 0 ? void 0 : _item$batch7.meetingDate)
          },
          viewCount: viewCount,
          likeCount: item.likeCount || 0,
          userLikeCount: item.userLikeCount || 0,
          todayLikeCount: item.todayLikeCount || 0,
          isFeatured: item.isFeatured || false,
          isLiked: item.isLiked || false,
          // 添加用户标识
          _userId: currentUserId
        };
      });
    },
    // 选择荣誉查看详情
    selectHonor: function selectHonor(honor) {
      var _this13 = this;
      // 检查honor参数是否有效
      if (!honor || !honor.id) {
        console.warn('selectHonor: honor参数无效', honor);
        return;
      }

      // 使用nextTick确保DOM更新流畅
      this.$nextTick(function () {
        _this13.selectedHonor = honor;

        // 禁用页面滚动
        _this13.disableScroll();

        // 异步增加浏览量，不阻塞弹窗显示
        setTimeout(function () {
          _this13.incrementViewCount(honor.id);
        }, 100);
      });
    },
    // 关闭详情
    closeDetail: function closeDetail() {
      this.selectedHonor = null;
      // 恢复页面滚动
      this.enableScroll();
    },
    // 禁止页面滚动
    disableScroll: function disableScroll() {},
    // 恢复页面滚动
    enableScroll: function enableScroll() {},
    // 切换视图模式
    switchView: function switchView(mode) {
      var _this14 = this;
      if (this.currentView === mode) return;
      this.debounceImmediate('switchView', function () {
        // 显示短暂加载状态提升体验
        _this14.loading = true;
        _this14.currentView = mode;
        _this14.selectedHonor = null;

        // 切换到排行榜时，同步时间数据
        if (mode === 'ranking') {
          _this14.syncTimeDataForPeriod(_this14.currentRankingPeriod);
        }
        setTimeout(function () {
          _this14.loading = false;
          if (mode === 'grid') {
            _this14.$nextTick(function () {
              if (_this14.currentPage > _this14.totalPages) {
                _this14.currentPage = 1;
              }
              if (_this14.totalPages > 1) {
                _this14.startAutoPlay();
              }
            });
          } else {
            _this14.stopAutoPlay();
          }
        }, 200);
      }, 250);
    },
    // 自动轮播相关方法
    startAutoPlay: function startAutoPlay() {
      var _this15 = this;
      this.stopAutoPlay();
      if (this.totalPages <= 1) return;
      this.autoPlayTimer = setInterval(function () {
        _this15.nextPage();
      }, this.autoPlayInterval);
    },
    stopAutoPlay: function stopAutoPlay() {
      if (this.autoPlayTimer) {
        clearInterval(this.autoPlayTimer);
        this.autoPlayTimer = null;
      }
    },
    // 分页相关方法
    prevPage: function prevPage() {
      var _this16 = this;
      this.debounceImmediate('prevPage', function () {
        if (_this16.totalPages <= 1) return;
        if (_this16.currentPage > 1) {
          _this16.currentPage--;
        } else {
          _this16.currentPage = _this16.totalPages;
        }
        _this16.resetAutoPlay();
      }, 200);
    },
    nextPage: function nextPage() {
      var _this17 = this;
      this.debounceImmediate('nextPage', function () {
        if (_this17.totalPages <= 1) return;
        if (_this17.currentPage < _this17.totalPages) {
          _this17.currentPage++;
        } else {
          _this17.currentPage = 1;
        }
        _this17.resetAutoPlay();
      }, 200);
    },
    goToPage: function goToPage(page) {
      var _this18 = this;
      this.debounceImmediate('goToPage', function () {
        if (page >= 1 && page <= _this18.totalPages) {
          _this18.currentPage = page;
          _this18.resetAutoPlay();
        }
      }, 300);
    },
    resetAutoPlay: function resetAutoPlay() {
      if (this.currentView === 'grid' && this.totalPages > 1) {
        this.startAutoPlay();
      }
    },
    // 按钮触摸事件
    onBtnTouchStart: function onBtnTouchStart(e) {
      e.stopPropagation();
    },
    onBtnTouchEnd: function onBtnTouchEnd(e) {
      e.stopPropagation();
    },
    // 工具方法
    sleep: function sleep(ms) {
      return new Promise(function (resolve) {
        return setTimeout(resolve, ms);
      });
    },
    // 图片预览
    previewImage: function previewImage(currentUrl, imageList) {
      var urls = imageList.map(function (img) {
        return img.url;
      });
      this.selectedHonor = null;
      setTimeout(function () {
        uni.previewImage({
          urls: urls,
          current: currentUrl,
          indicator: 'number',
          loop: true,
          fail: function fail(err) {
            // 图片预览失败
          }
        });
      }, 100);
    },
    // 图片加载错误处理
    onImageError: function onImageError(e) {
      // 图片加载失败
    },
    // 月份相关方法
    loadMonthData: function loadMonthData() {
      var _this19 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _context6.prev = 0;
                // 显示骨架屏
                _this19.loading = true;
                _this19.honors = [];
                _this19.currentPage = 1;
                _context6.next = 6;
                return _this19.loadHonorData();
              case 6:
                _this19.stopAutoPlay();
                _this19.$nextTick(function () {
                  if (_this19.currentView === 'grid' && _this19.totalPages > 1) {
                    _this19.startAutoPlay();
                  }
                });
                _context6.next = 12;
                break;
              case 10:
                _context6.prev = 10;
                _context6.t0 = _context6["catch"](0);
              case 12:
                _context6.prev = 12;
                _this19.loading = false;
                return _context6.finish(12);
              case 15:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[0, 10, 12, 15]]);
      }))();
    },
    // 给荣誉增加1个点赞
    addLikeToHonor: function addLikeToHonor(honor) {
      var _this20 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var likeKey;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                likeKey = "addLike_".concat(honor.id);
                _this20.debounceImmediate(likeKey, /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
                  var _this20$userInfo, _this20$userInfo2, _this20$userInfo3, userId, _originalLikeCount, _originalUserLikeCount, result, _ref2, likeCount, userLikeCount, todayLikeCount, todayUsedLikes, remainingLikes;
                  return _regenerator.default.wrap(function _callee7$(_context7) {
                    while (1) {
                      switch (_context7.prev = _context7.next) {
                        case 0:
                          _context7.prev = 0;
                          // 检查用户登录状态
                          userId = ((_this20$userInfo = _this20.userInfo) === null || _this20$userInfo === void 0 ? void 0 : _this20$userInfo.uid) || ((_this20$userInfo2 = _this20.userInfo) === null || _this20$userInfo2 === void 0 ? void 0 : _this20$userInfo2._id);
                          if (!(!_this20.userInfo || !userId)) {
                            _context7.next = 5;
                            break;
                          }
                          uni.showModal({
                            title: '用户信息异常',
                            content: '用户登录状态异常\n请退出页面重新进入',
                            showCancel: false,
                            confirmText: '知道了',
                            confirmColor: '#3a86ff'
                          });
                          return _context7.abrupt("return");
                        case 5:
                          if (!(!_this20.userLikeStats || _this20.userLikeStats.remainingLikes <= 0)) {
                            _context7.next = 8;
                            break;
                          }
                          uni.showModal({
                            title: '点赞次数已用完',
                            content: '今日点赞次数已用完\n每天最多可点赞3次\n明天可继续点赞',
                            showCancel: false,
                            confirmText: '知道了',
                            confirmColor: '#3a86ff'
                          });
                          return _context7.abrupt("return");
                        case 8:
                          // 保存原始值用于回滚
                          _originalLikeCount = honor.likeCount || 0;
                          _originalUserLikeCount = honor.userLikeCount || 0; // 设置处理状态，禁用按钮避免重复操作
                          _this20.$set(_this20.likeProcessing, honor.id, true);

                          // 乐观更新UI
                          honor.likeCount = _originalLikeCount + 1;
                          honor.userLikeCount = _originalUserLikeCount + 1;
                          honor.isLiked = true;
                          _context7.next = 16;
                          return uniCloud.callFunction({
                            name: 'honor-gallery',
                            data: {
                              action: 'likeHonor',
                              data: {
                                honorId: honor.id
                              },
                              uniIdToken: ((_this20$userInfo3 = _this20.userInfo) === null || _this20$userInfo3 === void 0 ? void 0 : _this20$userInfo3.token) || uni.getStorageSync('uni_id_token')
                            }
                          });
                        case 16:
                          result = _context7.sent;
                          if (!(result.result.code === 0)) {
                            _context7.next = 29;
                            break;
                          }
                          _ref2 = result.result.data || {}, likeCount = _ref2.likeCount, userLikeCount = _ref2.userLikeCount, todayLikeCount = _ref2.todayLikeCount, todayUsedLikes = _ref2.todayUsedLikes, remainingLikes = _ref2.remainingLikes; // 使用服务器返回的准确数据
                          honor.likeCount = likeCount || honor.likeCount;
                          honor.userLikeCount = userLikeCount || honor.userLikeCount;
                          honor.todayLikeCount = todayLikeCount || 0;

                          // 同步更新所有位置的数据
                          _this20.updateHonorInAllArrays(honor.id, {
                            likeCount: honor.likeCount,
                            userLikeCount: honor.userLikeCount,
                            todayLikeCount: honor.todayLikeCount,
                            isLiked: honor.userLikeCount > 0
                          });

                          // 更新用户统计
                          if (_this20.userLikeStats) {
                            _this20.userLikeStats.todayUsedLikes = todayUsedLikes;
                            _this20.userLikeStats.remainingLikes = remainingLikes;
                            _this20.userLikeStats.canLike = remainingLikes > 0;
                          }

                          // 先清除处理状态，确保按钮立即可用
                          _this20.$delete(_this20.likeProcessing, honor.id);

                          // 使用nextTick确保DOM更新完成后再清除缓存
                          _this20.$nextTick(function () {
                            _this20.clearAllCaches();
                          });

                          // 显示成功提示，保持弹窗打开
                          _this20.showCustomToast("\u70B9\u8D5E\u6210\u529F\uFF01\u5269\u4F59".concat(remainingLikes, "\u6B21"), 'success');
                          _context7.next = 30;
                          break;
                        case 29:
                          throw new Error(result.result.message || '点赞失败');
                        case 30:
                          _context7.next = 39;
                          break;
                        case 32:
                          _context7.prev = 32;
                          _context7.t0 = _context7["catch"](0);
                          // 点赞失败

                          // 回滚UI状态
                          honor.likeCount = originalLikeCount;
                          honor.userLikeCount = originalUserLikeCount;
                          honor.isLiked = originalUserLikeCount > 0;

                          // 清除处理状态
                          _this20.$delete(_this20.likeProcessing, honor.id);

                          // 显示错误提示，保持弹窗打开
                          if (_context7.t0.message && _context7.t0.message.includes('今日点赞次数已用完')) {
                            _this20.showCustomToast('今日点赞已用完', 'error');
                          } else {
                            _this20.showCustomToast(_context7.t0.message || '点赞失败', 'error');
                          }
                        case 39:
                        case "end":
                          return _context7.stop();
                      }
                    }
                  }, _callee7, null, [[0, 32]]);
                })), 300);
              case 2:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8);
      }))();
    },
    // 取消对荣誉的所有点赞
    removeLikeFromHonor: function removeLikeFromHonor(honor) {
      var _this21 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var likeKey;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                likeKey = "removeLike_".concat(honor.id);
                _this21.debounceImmediate(likeKey, /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
                  var _this21$userInfo, _this21$userInfo2, _this21$userInfo3, userId, message, _originalLikeCount2, _originalUserLikeCount2, result, _ref4, likeCount, userLikeCount, todayLikeCount, canceledCount, updateData, _message;
                  return _regenerator.default.wrap(function _callee9$(_context9) {
                    while (1) {
                      switch (_context9.prev = _context9.next) {
                        case 0:
                          _context9.prev = 0;
                          // 检查用户登录状态
                          userId = ((_this21$userInfo = _this21.userInfo) === null || _this21$userInfo === void 0 ? void 0 : _this21$userInfo.uid) || ((_this21$userInfo2 = _this21.userInfo) === null || _this21$userInfo2 === void 0 ? void 0 : _this21$userInfo2._id);
                          if (!(!_this21.userInfo || !userId)) {
                            _context9.next = 5;
                            break;
                          }
                          uni.showModal({
                            title: '用户信息异常',
                            content: '用户登录状态异常\n请退出页面重新进入',
                            showCancel: false,
                            confirmText: '知道了',
                            confirmColor: '#3a86ff'
                          });
                          return _context9.abrupt("return");
                        case 5:
                          if (!(!honor.todayLikeCount || honor.todayLikeCount <= 0)) {
                            _context9.next = 9;
                            break;
                          }
                          message = honor.userLikeCount > 0 ? '只能取消今日的点赞' : '还没有点赞记录'; // 显示提示，保持弹窗打开
                          _this21.showCustomToast(message, 'warning');
                          return _context9.abrupt("return");
                        case 9:
                          // 保存原始值用于回滚
                          _originalLikeCount2 = honor.likeCount || 0;
                          _originalUserLikeCount2 = honor.userLikeCount || 0; // 设置处理状态，禁用按钮避免重复操作
                          _this21.$set(_this21.likeProcessing, honor.id, true);
                          _context9.next = 14;
                          return uniCloud.callFunction({
                            name: 'honor-gallery',
                            data: {
                              action: 'unlikeHonor',
                              data: {
                                honorId: honor.id
                              },
                              uniIdToken: ((_this21$userInfo3 = _this21.userInfo) === null || _this21$userInfo3 === void 0 ? void 0 : _this21$userInfo3.token) || uni.getStorageSync('uni_id_token')
                            }
                          });
                        case 14:
                          result = _context9.sent;
                          if (!(result.result.code === 0)) {
                            _context9.next = 31;
                            break;
                          }
                          _ref4 = result.result.data || {}, likeCount = _ref4.likeCount, userLikeCount = _ref4.userLikeCount, todayLikeCount = _ref4.todayLikeCount, canceledCount = _ref4.canceledCount; // 使用服务器返回的准确数据
                          honor.likeCount = likeCount || honor.likeCount;
                          honor.userLikeCount = userLikeCount || 0;
                          honor.todayLikeCount = todayLikeCount || 0;
                          honor.isLiked = (userLikeCount || 0) > 0;

                          // 同步更新所有位置的数据
                          updateData = {
                            likeCount: honor.likeCount,
                            userLikeCount: honor.userLikeCount,
                            todayLikeCount: honor.todayLikeCount,
                            isLiked: honor.isLiked
                          };
                          _this21.updateHonorInAllArrays(honor.id, updateData);

                          // 更新用户统计（取消点赞会增加剩余次数）
                          if (_this21.userLikeStats && canceledCount) {
                            _this21.userLikeStats.todayUsedLikes = Math.max((_this21.userLikeStats.todayUsedLikes || 0) - 1, 0);
                            _this21.userLikeStats.remainingLikes = Math.min((_this21.userLikeStats.remainingLikes || 0) + 1, 3);
                            _this21.userLikeStats.canLike = _this21.userLikeStats.remainingLikes > 0;
                          }

                          // 先清除处理状态，确保按钮立即可用
                          _this21.$delete(_this21.likeProcessing, honor.id);

                          // 使用nextTick确保DOM更新完成后再清除缓存
                          _this21.$nextTick(function () {
                            _this21.clearAllCaches();
                          });

                          // 显示成功提示，保持弹窗打开
                          _message = '取消点赞成功';
                          if (todayLikeCount > 0) {
                            _message += "\uFF0C\u4ECA\u65E5\u8FD8\u53EF\u53D6\u6D88".concat(todayLikeCount, "\u6B21");
                          }
                          _this21.showCustomToast(_message, 'success');
                          _context9.next = 32;
                          break;
                        case 31:
                          throw new Error(result.result.message || '取消点赞失败');
                        case 32:
                          _context9.next = 41;
                          break;
                        case 34:
                          _context9.prev = 34;
                          _context9.t0 = _context9["catch"](0);
                          // 取消点赞失败

                          // 回滚UI状态
                          honor.likeCount = originalLikeCount;
                          honor.userLikeCount = originalUserLikeCount;
                          honor.isLiked = originalUserLikeCount > 0;

                          // 清除处理状态
                          _this21.$delete(_this21.likeProcessing, honor.id);

                          // 显示错误提示，保持弹窗打开
                          if (_context9.t0.message && _context9.t0.message.includes('只能取消今日的点赞')) {
                            _this21.showCustomToast('只能取消今日点赞', 'error');
                          } else {
                            _this21.showCustomToast(_context9.t0.message || '取消失败', 'error');
                          }
                        case 41:
                        case "end":
                          return _context9.stop();
                      }
                    }
                  }, _callee9, null, [[0, 34]]);
                })), 300);
              case 2:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10);
      }))();
    },
    // 统一更新荣誉数据的辅助方法
    updateHonorInAllArrays: function updateHonorInAllArrays(honorId, updateData) {
      var updateRecord = function updateRecord(record) {
        if (record.id === honorId) {
          Object.assign(record, updateData);
        }
      };
      this.allHonorsData.forEach(updateRecord);
      this.honors.forEach(updateRecord);

      // 如果当前选中的荣誉就是这个，也要更新
      if (this.selectedHonor && this.selectedHonor.id === honorId) {
        Object.assign(this.selectedHonor, updateData);
      }
    },
    // 清除所有缓存的辅助方法
    clearAllCaches: function clearAllCaches() {
      this.clearHonorsCache();
      this.clearRankingCache();
    },
    // 分享荣誉
    shareHonor: function shareHonor(honor) {
      var _this22 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12() {
        var shareKey;
        return _regenerator.default.wrap(function _callee12$(_context12) {
          while (1) {
            switch (_context12.prev = _context12.next) {
              case 0:
                shareKey = "share_".concat(honor.id);
                _this22.debounceImmediate(shareKey, /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
                  return _regenerator.default.wrap(function _callee11$(_context11) {
                    while (1) {
                      switch (_context11.prev = _context11.next) {
                        case 0:
                          try {
                            // 尝试使用系统分享

                            uni.share({
                              provider: 'weixin',
                              scene: 'WXSceneSession',
                              type: 0,
                              href: '',
                              title: "".concat(honor.userName, "\u83B7\u5F97").concat(honor.honorType.name),
                              summary: honor.reason || '查看表彰详情',
                              imageUrl: honor.images && honor.images.length > 0 ? honor.images[0].url : '',
                              success: function success() {
                                _this22.showCustomToast('分享成功', 'success');
                              },
                              fail: function fail() {
                                _this22.fallbackShare(honor);
                              }
                            });
                          } catch (error) {
                            // 分享失败
                            _this22.fallbackShare(honor);
                          }
                        case 1:
                        case "end":
                          return _context11.stop();
                      }
                    }
                  }, _callee11);
                })), 300);
              case 2:
              case "end":
                return _context12.stop();
            }
          }
        }, _callee12);
      }))();
    },
    // 备用分享方案
    fallbackShare: function fallbackShare(honor) {
      var shareText = "".concat(honor.userName, "\u83B7\u5F97").concat(honor.honorType.name, ": ").concat(honor.reason || '查看表彰详情');
      this.copyToClipboard(shareText);
    },
    // 复制到剪贴板
    copyToClipboard: function copyToClipboard(text) {
      // 微信小程序：使用系统默认提示
      wx.setClipboardData({
        data: text,
        success: function success() {
          // 系统会自动显示"内容已复制"提示，无需额外处理
        },
        fail: function fail() {
          uni.showModal({
            title: '分享内容',
            content: text,
            showCancel: false,
            confirmText: '知道了'
          });
        }
      });
    },
    // 加载用户点赞统计
    loadUserLikeStats: function loadUserLikeStats() {
      var _this23 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee13() {
        var _this23$userInfo, _this23$userInfo2, _this23$userInfo3, userId, result, stats;
        return _regenerator.default.wrap(function _callee13$(_context13) {
          while (1) {
            switch (_context13.prev = _context13.next) {
              case 0:
                _context13.prev = 0;
                userId = ((_this23$userInfo = _this23.userInfo) === null || _this23$userInfo === void 0 ? void 0 : _this23$userInfo.uid) || ((_this23$userInfo2 = _this23.userInfo) === null || _this23$userInfo2 === void 0 ? void 0 : _this23$userInfo2._id);
                if (userId) {
                  _context13.next = 4;
                  break;
                }
                return _context13.abrupt("return");
              case 4:
                _context13.next = 6;
                return uniCloud.callFunction({
                  name: 'honor-gallery',
                  data: {
                    action: 'getUserLikeStats',
                    uniIdToken: ((_this23$userInfo3 = _this23.userInfo) === null || _this23$userInfo3 === void 0 ? void 0 : _this23$userInfo3.token) || uni.getStorageSync('uni_id_token')
                  }
                });
              case 6:
                result = _context13.sent;
                if (result.result.code === 0) {
                  stats = result.result.data; // 存储到data中，可以在界面上显示
                  _this23.userLikeStats = stats;

                  // 移除页面进入时的提醒，只在点赞后提醒
                }
                _context13.next = 12;
                break;
              case 10:
                _context13.prev = 10;
                _context13.t0 = _context13["catch"](0);
              case 12:
              case "end":
                return _context13.stop();
            }
          }
        }, _callee13, null, [[0, 10]]);
      }))();
    },
    // 增加浏览量
    incrementViewCount: function incrementViewCount(honorId) {
      var _this24 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee14() {
        var targetHonor, _oldCount, newCount, _honorInAllData, _honorInArray, result, _result$result$data, serverCount, rollbackCount;
        return _regenerator.default.wrap(function _callee14$(_context14) {
          while (1) {
            switch (_context14.prev = _context14.next) {
              case 0:
                // 先在allHonorsData中查找，再在honors中查找
                targetHonor = _this24.allHonorsData.find(function (h) {
                  return h.id === honorId;
                });
                if (!targetHonor) {
                  targetHonor = _this24.honors.find(function (h) {
                    return h.id === honorId;
                  });
                }
                if (targetHonor) {
                  _context14.next = 4;
                  break;
                }
                return _context14.abrupt("return");
              case 4:
                _context14.prev = 4;
                // 乐观更新 - 同时更新allHonorsData和honors中的数据
                _oldCount = targetHonor.viewCount || 0;
                newCount = _oldCount + 1; // 更新allHonorsData中的数据
                _honorInAllData = _this24.allHonorsData.find(function (h) {
                  return h.id === honorId;
                });
                if (_honorInAllData) {
                  _honorInAllData.viewCount = newCount;
                }

                // 更新honors数组中的数据
                _honorInArray = _this24.honors.find(function (h) {
                  return h.id === honorId;
                });
                if (_honorInArray) {
                  _honorInArray.viewCount = newCount;
                }

                // 更新selectedHonor中的数据（如果当前正在查看这个荣誉）
                if (_this24.selectedHonor && _this24.selectedHonor.id === honorId) {
                  _this24.selectedHonor.viewCount = newCount;
                }

                // 清除相关缓存
                _this24.clearHonorsCache();
                _this24.clearRankingCache(); // 同时清除排行榜缓存

                // 调用云函数更新数据库
                _context14.next = 16;
                return uniCloud.callFunction({
                  name: 'honor-gallery',
                  data: {
                    action: 'incrementViewCount',
                    data: {
                      honorId: honorId
                    }
                  }
                });
              case 16:
                result = _context14.sent;
                if (result.result.code === 0) {
                  serverCount = (_result$result$data = result.result.data) === null || _result$result$data === void 0 ? void 0 : _result$result$data.viewCount;
                  if (typeof serverCount === 'number' && serverCount >= 0) {
                    // 使用服务器返回的准确数据同步更新所有位置
                    if (_honorInAllData) {
                      _honorInAllData.viewCount = serverCount;
                    }
                    if (_honorInArray) {
                      _honorInArray.viewCount = serverCount;
                    }
                    if (_this24.selectedHonor && _this24.selectedHonor.id === honorId) {
                      _this24.selectedHonor.viewCount = serverCount;
                    }

                    // 强制重新计算排行榜数据
                    _this24.$forceUpdate();
                  }
                } else {
                  // 云函数返回错误
                }
                _context14.next = 26;
                break;
              case 20:
                _context14.prev = 20;
                _context14.t0 = _context14["catch"](4);
                // 增加浏览量失败
                // 回滚浏览量 - 需要回滚所有位置的数据
                rollbackCount = Math.max(oldCount, 0);
                if (honorInAllData) {
                  honorInAllData.viewCount = rollbackCount;
                }
                if (honorInArray) {
                  honorInArray.viewCount = rollbackCount;
                }
                if (_this24.selectedHonor && _this24.selectedHonor.id === honorId) {
                  _this24.selectedHonor.viewCount = rollbackCount;
                }
              case 26:
              case "end":
                return _context14.stop();
            }
          }
        }, _callee14, null, [[4, 20]]);
      }))();
    },
    // 清除荣誉缓存
    clearHonorsCache: function clearHonorsCache() {
      this.honorsCache = {};
      this.lastCacheTime = 0;
    },
    // 清除排行榜缓存
    clearRankingCache: function clearRankingCache() {
      this.rankingCache = {};
    },
    // 智能缓存管理
    manageCacheSize: function manageCacheSize() {
      var _this25 = this;
      var cacheKeys = Object.keys(this.honorsCache);

      // 超出最大缓存条目数时，删除最旧的
      if (cacheKeys.length > this.maxCacheSize) {
        var sortedKeys = cacheKeys.map(function (key) {
          return {
            key: key,
            timestamp: _this25.honorsCache[key].timestamp
          };
        }).sort(function (a, b) {
          return a.timestamp - b.timestamp;
        });
        var keysToDelete = sortedKeys.slice(0, cacheKeys.length - this.maxCacheSize);
        keysToDelete.forEach(function (item) {
          delete _this25.honorsCache[item.key];
        });
      }

      // 检查单个缓存项大小，防止内存溢出
      Object.keys(this.honorsCache).forEach(function (key) {
        var cacheItem = _this25.honorsCache[key];
        if (cacheItem.data && cacheItem.data.length > _this25.maxCacheItemSize) {
          cacheItem.data = cacheItem.data.slice(0, _this25.maxCacheItemSize);
        }
      });
    },
    // 格式化相关方法
    formatDate: function formatDate(dateStr) {
      if (!dateStr) return '';
      try {
        var date = new Date(dateStr);
        return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5");
      } catch (e) {
        return dateStr;
      }
    },
    formatBatchDate: function formatBatchDate(dateStr) {
      if (!dateStr) return '';
      try {
        var date = new Date(dateStr);
        var month = date.getMonth() + 1;
        var day = date.getDate();
        var weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        var weekday = weekdays[date.getDay()];
        return "".concat(month, "\u6708").concat(day, "\u65E5 ").concat(weekday, " \u5468\u4F1A");
      } catch (e) {
        return dateStr;
      }
    },
    // 列表视图相关方法
    setListViewMode: function setListViewMode(mode) {
      var _this26 = this;
      if (this.listViewMode === mode) return;
      this.debounceImmediate('listViewMode', function () {
        _this26.listViewMode = mode;

        // 如果切换到分组模式，重新初始化所有用户的展开状态
        if (mode === 'grouped') {
          // 清空现有状态，确保重新初始化
          _this26.expandedUsers = {};

          // 使用nextTick确保数据更新后再设置展开状态
          _this26.$nextTick(function () {
            _this26.groupedListHonors.forEach(function (group) {
              // 所有用户都默认为收起状态，用户可以手动点击展开
              // 这样可以确保第一个用户也能正常点击
              _this26.$set(_this26.expandedUsers, group.userName, false);
            });
          });
        }
      }, 100);
    },
    toggleUserGroup: function toggleUserGroup(userName) {
      var _this27 = this;
      this.debounceImmediate("toggleUser_".concat(userName), function () {
        // 确保expandedUsers对象存在该用户的属性
        if (!_this27.expandedUsers.hasOwnProperty(userName)) {
          _this27.$set(_this27.expandedUsers, userName, false);
        }
        // 切换展开状态
        _this27.$set(_this27.expandedUsers, userName, !_this27.expandedUsers[userName]);
      }, 150); // 减少防抖延迟，提升响应速度
    },
    // 排行榜相关方法
    setRankingPeriod: function setRankingPeriod(period) {
      var _this28 = this;
      if (this.currentRankingPeriod === period) return;
      this.debounceImmediate('rankingPeriod', function () {
        // 显示短暂加载状态
        _this28.loading = true;

        // 清空排行榜缓存，确保数据更新
        _this28.rankingCache = {};
        _this28.currentRankingPeriod = period;

        // 根据新的时间维度同步时间数据
        _this28.syncTimeDataForPeriod(period);
        setTimeout(function () {
          _this28.loading = false;
          _this28.$nextTick(function () {
            _this28.$forceUpdate();
          });
        }, 200); // 减少加载时间，提升用户体验
      }, 50); // 减少防抖延迟
    },
    // 同步时间数据（基于排行榜期间）
    syncTimeDataForPeriod: function syncTimeDataForPeriod(period) {
      var currentDate = new Date();

      // 只在首次切换到对应模式时初始化为当前时间，不覆盖用户的选择
      if (period === 'week') {
        // 如果当前不在当前周，则初始化为当前周
        var nowYear = currentDate.getFullYear();
        var nowMonth = currentDate.getMonth() + 1;
        var nowWeekOfMonth = this.getWeekOfMonth(currentDate);
        if (this.currentYear !== nowYear || this.currentMonth !== nowMonth) {
          this.currentYear = nowYear;
          this.currentMonth = nowMonth;
          this.currentWeekOfMonth = nowWeekOfMonth;
        }
      } else if (period === 'quarter') {
        // 如果当前不在当前季度，则初始化为当前季度
        var _nowYear = currentDate.getFullYear();
        var nowQuarter = Math.ceil((currentDate.getMonth() + 1) / 3);
        if (this.currentYear !== _nowYear || this.currentQuarter !== nowQuarter) {
          this.currentYear = _nowYear;
          this.currentQuarter = nowQuarter;
          // 同步更新月份到季度的第一个月
          this.currentMonth = (nowQuarter - 1) * 3 + 1;
        }
      } else if (period === 'year') {
        // 如果当前不在当前年，则初始化为当前年
        var _nowYear2 = currentDate.getFullYear();
        if (this.currentYear !== _nowYear2) {
          this.currentYear = _nowYear2;
        }
      }
      // month 模式保持原有月份选择器的值不变
    },
    setRankingMetric: function setRankingMetric(metric) {
      var _this29 = this;
      if (this.currentRankingMetric === metric) return;
      this.debounceImmediate('rankingMetric', function () {
        // 显示短暂加载状态
        _this29.loading = true;
        _this29.currentRankingMetric = metric;
        setTimeout(function () {
          _this29.loading = false;
          _this29.$nextTick(function () {
            _this29.$forceUpdate();
          });
        }, 150); // 减少加载时间
      }, 50); // 减少防抖延迟
    },
    // 根据排行榜时间维度过滤荣誉数据
    getFilteredHonorsByPeriod: function getFilteredHonorsByPeriod() {
      var _this30 = this;
      // 生成缓存键，包含时间范围和具体时间参数
      var period = this.currentRankingPeriod;
      var cacheKey = "ranking-".concat(period);
      var currentYear = new Date().getFullYear();
      var currentMonth = new Date().getMonth() + 1;
      var selectedYear = this.currentYear;
      var selectedMonth = this.currentMonth;

      // 根据不同时间范围生成不同的缓存键
      switch (period) {
        case 'week':
          var selectedWeekOfMonth = this.currentWeekOfMonth;
          cacheKey += "-".concat(selectedYear, "-").concat(selectedMonth, "-W").concat(selectedWeekOfMonth);
          break;
        case 'month':
          cacheKey += "-".concat(selectedYear, "-").concat(selectedMonth);
          break;
        case 'quarter':
          var selectedQuarter = this.currentQuarter;
          cacheKey += "-".concat(selectedYear, "-Q").concat(selectedQuarter);
          break;
        case 'year':
          cacheKey += "-".concat(selectedYear);
          break;
        default:
          cacheKey += "-".concat(selectedYear, "-").concat(selectedMonth);
      }

      // 检查缓存
      if (this.rankingCache && this.rankingCache[cacheKey]) {
        return this.rankingCache[cacheKey];
      }

      // 使用allHonorsData作为数据源，支持跨时间段查询
      var dataSource = this.allHonorsData.length > 0 ? this.allHonorsData : this.getCurrentMonthHonors;
      var filtered = dataSource.filter(function (honor) {
        var honorDate = new Date(honor.createTime);
        var honorYear = honorDate.getFullYear();
        var honorMonth = honorDate.getMonth() + 1;
        switch (period) {
          case 'week':
            // 本周：使用用户选择的周（通过左右箭头切换）
            var _selectedWeekOfMonth = _this30.currentWeekOfMonth;
            var honorWeekOfMonth = _this30.getWeekOfMonth(honorDate);
            return honorYear === selectedYear && honorMonth === selectedMonth && honorWeekOfMonth === _selectedWeekOfMonth;
          case 'month':
            // 本月：跟随月份选择器，可以选择任意月份
            return honorYear === selectedYear && honorMonth === selectedMonth;
          case 'quarter':
            // 本季度：使用用户选择的季度（通过左右箭头切换）
            var _selectedQuarter = _this30.currentQuarter;
            var honorQuarter = Math.ceil(honorMonth / 3);
            return honorYear === selectedYear && honorQuarter === _selectedQuarter;
          case 'year':
            // 本年度：使用用户选择的年份（通过左右箭头切换）
            return honorYear === selectedYear;
          default:
            return honorYear === selectedYear && honorMonth === selectedMonth;
        }
      });

      // 缓存结果
      if (!this.rankingCache) {
        this.rankingCache = {};
      }
      this.rankingCache[cacheKey] = filtered;
      return filtered;
    },
    // 计算排行榜分数（基于聚合数据）
    calculateRankingScore: function calculateRankingScore(userData, metric) {
      switch (metric) {
        case 'likes':
          return userData.totalLikes || 0;
        case 'views':
          return userData.totalViews || 0;
        case 'awards':
          return userData.totalAwards || 0;
        case 'featured':
          return userData.totalFeatured || 0;
        case 'comprehensive':
          // 综合分数：精选*50 + 获奖*10 + 点赞*1 + 浏览*0.1
          var featuredScore = (userData.totalFeatured || 0) * 50;
          var awardScore = (userData.totalAwards || 0) * 10;
          var likeScore = (userData.totalLikes || 0) * 1;
          var viewScore = (userData.totalViews || 0) * 0.1;
          return Math.round(featuredScore + awardScore + likeScore + viewScore);
        default:
          return userData.totalLikes || 0;
      }
    },
    // 获取分数标签
    getScoreLabel: function getScoreLabel() {
      var metric = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
      var targetMetric = metric || this.currentRankingMetric;
      switch (targetMetric) {
        case 'likes':
          return '点赞数';
        case 'views':
          return '浏览数';
        case 'awards':
          return '获奖数';
        case 'featured':
          return '精选数';
        case 'comprehensive':
          return '综合分';
        default:
          return '点赞数';
      }
    },
    // 获取用户获奖次数
    getAwardCount: function getAwardCount(userName) {
      try {
        var filtered = this.getFilteredHonorsByPeriod().filter(function (honor) {
          return honor.userName === userName;
        });
        return filtered.length;
      } catch (error) {
        // 获取获奖次数失败
        return 0;
      }
    },
    // 月份切换（优化版 - 使用缓存避免重新加载）
    prevMonth: function prevMonth() {
      var _this31 = this;
      this.debounce('prevMonth', function () {
        if (_this31.currentMonth > 1) {
          _this31.currentMonth--;
        } else {
          _this31.currentMonth = 12;
          _this31.currentYear--;
        }
        // 优化：只有在排行榜视图或者缓存中没有数据时才重新加载
        _this31.smartLoadMonthData();
      }, 400);
    },
    nextMonth: function nextMonth() {
      var _this32 = this;
      this.debounce('nextMonth', function () {
        if (_this32.currentMonth < 12) {
          _this32.currentMonth++;
        } else {
          _this32.currentMonth = 1;
          _this32.currentYear++;
        }
        // 优化：只有在排行榜视图或者缓存中没有数据时才重新加载
        _this32.smartLoadMonthData();
      }, 400);
    },
    // 智能切换月份 - 优先使用缓存和allHonorsData
    smartLoadMonthData: function smartLoadMonthData() {
      var _this33 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee15() {
        var cacheKey, now, filtered;
        return _regenerator.default.wrap(function _callee15$(_context15) {
          while (1) {
            switch (_context15.prev = _context15.next) {
              case 0:
                cacheKey = "".concat(_this33.currentYear, "-").concat(_this33.currentMonth);
                now = Date.now();
                _context15.prev = 2;
                if (!(_this33.currentView === 'ranking')) {
                  _context15.next = 6;
                  break;
                }
                if (_this33.currentRankingPeriod === 'month') {
                  // 清空月度相关的排行榜缓存
                  Object.keys(_this33.rankingCache).forEach(function (key) {
                    if (key.includes('-month-')) {
                      delete _this33.rankingCache[key];
                    }
                  });
                }
                return _context15.abrupt("return");
              case 6:
                if (!(_this33.honorsCache[cacheKey] && now - _this33.honorsCache[cacheKey].timestamp < _this33.cacheExpireTime)) {
                  _context15.next = 9;
                  break;
                }
                // 缓存有效，瞬间切换
                _this33.loading = false;
                return _context15.abrupt("return");
              case 9:
                if (!(_this33.allHonorsData.length > 0)) {
                  _context15.next = 15;
                  break;
                }
                filtered = _this33.allHonorsData.filter(function (honor) {
                  var honorDate = new Date(honor.createTime);
                  return honorDate.getFullYear() === _this33.currentYear && honorDate.getMonth() + 1 === _this33.currentMonth;
                });
                if (!(filtered.length > 0)) {
                  _context15.next = 15;
                  break;
                }
                // 更新缓存
                _this33.honorsCache[cacheKey] = {
                  data: filtered,
                  timestamp: now,
                  recordCount: filtered.length
                };
                _this33.loading = false;
                return _context15.abrupt("return");
              case 15:
                // 需要从服务器加载
                _this33.loading = true;
                _context15.next = 18;
                return _this33.loadHonorData();
              case 18:
                _context15.next = 22;
                break;
              case 20:
                _context15.prev = 20;
                _context15.t0 = _context15["catch"](2);
              case 22:
                _context15.prev = 22;
                _this33.loading = false;
                return _context15.finish(22);
              case 25:
              case "end":
                return _context15.stop();
            }
          }
        }, _callee15, null, [[2, 20, 22, 25]]);
      }))();
    },
    // 下拉刷新
    onPullRefresh: function onPullRefresh() {
      var _this34 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee17() {
        return _regenerator.default.wrap(function _callee17$(_context17) {
          while (1) {
            switch (_context17.prev = _context17.next) {
              case 0:
                if (!_this34.isProcessing) {
                  _context17.next = 2;
                  break;
                }
                return _context17.abrupt("return");
              case 2:
                _this34.debounce('pullRefresh', /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee16() {
                  return _regenerator.default.wrap(function _callee16$(_context16) {
                    while (1) {
                      switch (_context16.prev = _context16.next) {
                        case 0:
                          _this34.refreshing = true;
                          _this34.isProcessing = true;
                          _context16.prev = 2;
                          _context16.next = 5;
                          return _this34.refreshData();
                        case 5:
                          _context16.prev = 5;
                          _this34.refreshing = false;
                          _this34.isProcessing = false;
                          uni.stopPullDownRefresh();
                          return _context16.finish(5);
                        case 10:
                        case "end":
                          return _context16.stop();
                      }
                    }
                  }, _callee16, null, [[2,, 5, 10]]);
                })), 500);
              case 3:
              case "end":
                return _context17.stop();
            }
          }
        }, _callee17);
      }))();
    },
    onRefreshRestore: function onRefreshRestore() {
      this.refreshing = false;
    },
    // 刷新数据
    refreshData: function refreshData() {
      var _this35 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee18() {
        return _regenerator.default.wrap(function _callee18$(_context18) {
          while (1) {
            switch (_context18.prev = _context18.next) {
              case 0:
                _context18.prev = 0;
                // 下拉刷新时不需要额外的loading，scroll-view有自己的刷新指示器
                if (!_this35.refreshing) {
                  uni.showLoading({
                    title: '刷新中...'
                  });
                }

                // 下拉刷新时需要显示loading状态（宫格中心的转圈）
                _this35.loading = true;
                _this35.honors = [];
                _this35.allHonorsData = [];
                _this35.clearHonorsCache();

                // 并行加载月份数据和完整数据
                _context18.next = 8;
                return Promise.all([_this35.loadHonorData(), _this35.loadAllHonorData()]);
              case 8:
                _this35.loading = false;
                if (!_this35.refreshing) {
                  _this35.showCustomToast('刷新成功', 'success');
                }
                _context18.next = 15;
                break;
              case 12:
                _context18.prev = 12;
                _context18.t0 = _context18["catch"](0);
                // 刷新数据失败
                _this35.showCustomToast('刷新失败', 'error');
              case 15:
                _context18.prev = 15;
                if (!_this35.refreshing) {
                  uni.hideLoading();
                }
                return _context18.finish(15);
              case 18:
              case "end":
                return _context18.stop();
            }
          }
        }, _callee18, null, [[0, 12, 15, 18]]);
      }))();
    },
    // 手势检测方法
    onTouchStart: function onTouchStart(e) {
      if (this.currentView !== 'grid' || this.totalPages <= 1) return;
      this.touchMoved = false;
      this.touchStartTime = Date.now();
      if (e.touches && e.touches.length > 0) {
        this.touchStartX = e.touches[0].clientX;
        this.touchStartY = e.touches[0].clientY;
      }
    },
    onTouchMove: function onTouchMove(e) {
      if (this.currentView !== 'grid' || this.totalPages <= 1) return;
      this.touchMoved = true;
      if (e.touches && e.touches.length > 0) {
        var deltaX = Math.abs(e.touches[0].clientX - this.touchStartX);
        var deltaY = Math.abs(e.touches[0].clientY - this.touchStartY);
        if (deltaX > deltaY && deltaX > 10) {
          e.preventDefault();
        }
      }
    },
    onTouchEnd: function onTouchEnd(e) {
      var _this36 = this;
      if (this.currentView !== 'grid' || this.totalPages <= 1) return;
      var touchEndTime = Date.now();
      var touchDuration = touchEndTime - this.touchStartTime;
      if (!this.touchMoved || touchDuration > this.maxSwipeTime) {
        return;
      }
      if (e.changedTouches && e.changedTouches.length > 0) {
        var touchEndX = e.changedTouches[0].clientX;
        var touchEndY = e.changedTouches[0].clientY;
        var deltaX = touchEndX - this.touchStartX;
        var deltaY = touchEndY - this.touchStartY;
        var distanceX = Math.abs(deltaX);
        var distanceY = Math.abs(deltaY);
        if (distanceX > distanceY && distanceX > this.minSwipeDistance) {
          this.stopAutoPlay();
          if (deltaX > 0) {
            this.prevPage();
          } else {
            this.nextPage();
          }
          setTimeout(function () {
            if (_this36.currentView === 'grid' && _this36.totalPages > 1) {
              _this36.startAutoPlay();
            }
          }, 1500);
        }
      }
    },
    onTouchCancel: function onTouchCancel(e) {
      this.touchMoved = false;
      this.touchStartX = 0;
      this.touchStartY = 0;
      this.touchStartTime = 0;
    },
    // 检测设备性能
    detectDevicePerformance: function detectDevicePerformance() {
      try {
        var systemInfo = uni.getSystemInfoSync();
        var totalMemory = systemInfo.memSize || 0;
        var platform = systemInfo.platform || 'unknown';

        // 根据内存和平台判断设备性能
        var performanceLevel = 'medium';
        if (totalMemory > 6000 || platform === 'ios') {
          performanceLevel = 'high';
        } else if (totalMemory < 3000) {
          performanceLevel = 'low';
        }
        this.devicePerformance = performanceLevel;

        // 根据性能调整缓存策略
        switch (performanceLevel) {
          case 'high':
            this.maxCacheSize = 15;
            this.maxCacheItemSize = 150;
            break;
          case 'medium':
            this.maxCacheSize = 10;
            this.maxCacheItemSize = 100;
            break;
          case 'low':
            this.maxCacheSize = 5;
            this.maxCacheItemSize = 50;
            break;
        }
      } catch (error) {
        // 设备性能检测失败
        // 默认中等性能设置
        this.devicePerformance = 'medium';
        this.maxCacheSize = 10;
        this.maxCacheItemSize = 100;
      }
    },
    // 清理存储空间
    clearStorageSpace: function clearStorageSpace() {
      try {
        var storageInfo = uni.getStorageInfoSync();
        if (storageInfo.currentSize && storageInfo.limitSize) {
          var usagePercent = storageInfo.currentSize / storageInfo.limitSize * 100;
          if (usagePercent > 80) {
            var cacheKeys = storageInfo.keys.filter(function (key) {
              return key.startsWith('honor_cache_') || key.startsWith('temp_') || key.includes('qrcode_temp');
            });
            cacheKeys.forEach(function (key) {
              try {
                uni.removeStorageSync(key);
              } catch (e) {
                // 静默处理清理失败
              }
            });
          }
        }
      } catch (error) {
        // 静默处理错误，不影响页面加载
      }
    },
    // 智能时间切换 - 根据当前视图和排行榜筛选维度决定切换方式
    prevTimePeriod: function prevTimePeriod() {
      var _this37 = this;
      this.debounce('prevTimePeriod', function () {
        if (_this37.currentView === 'ranking') {
          switch (_this37.currentRankingPeriod) {
            case 'week':
              _this37.prevWeek();
              break;
            case 'month':
              _this37.prevMonth();
              break;
            case 'quarter':
              _this37.prevQuarter();
              break;
            case 'year':
              _this37.prevYear();
              break;
            default:
              _this37.prevMonth();
          }
        } else {
          // 非排行榜视图使用传统月份切换
          _this37.prevMonth();
        }
      }, 400);
    },
    nextTimePeriod: function nextTimePeriod() {
      var _this38 = this;
      this.debounce('nextTimePeriod', function () {
        if (_this38.currentView === 'ranking') {
          switch (_this38.currentRankingPeriod) {
            case 'week':
              _this38.nextWeek();
              break;
            case 'month':
              _this38.nextMonth();
              break;
            case 'quarter':
              _this38.nextQuarter();
              break;
            case 'year':
              _this38.nextYear();
              break;
            default:
              _this38.nextMonth();
          }
        } else {
          // 非排行榜视图使用传统月份切换
          _this38.nextMonth();
        }
      }, 400);
    },
    // 周切换方法
    prevWeek: function prevWeek() {
      if (this.currentWeekOfMonth > 1) {
        this.currentWeekOfMonth--;
      } else {
        // 切换到上个月的第4周
        this.prevMonth();
        this.currentWeekOfMonth = 4;
      }
      this.loadRankingData();
    },
    nextWeek: function nextWeek() {
      if (this.currentWeekOfMonth < 4) {
        this.currentWeekOfMonth++;
      } else {
        // 切换到下个月的第1周
        this.nextMonth();
        this.currentWeekOfMonth = 1;
      }
      this.loadRankingData();
    },
    // 季度切换方法
    prevQuarter: function prevQuarter() {
      if (this.currentQuarter > 1) {
        this.currentQuarter--;
      } else {
        this.currentQuarter = 4;
        this.currentYear--;
      }
      // 同步更新月份到季度的第一个月
      this.currentMonth = (this.currentQuarter - 1) * 3 + 1;
      this.loadRankingData();
    },
    nextQuarter: function nextQuarter() {
      if (this.currentQuarter < 4) {
        this.currentQuarter++;
      } else {
        this.currentQuarter = 1;
        this.currentYear++;
      }
      // 同步更新月份到季度的第一个月
      this.currentMonth = (this.currentQuarter - 1) * 3 + 1;
      this.loadRankingData();
    },
    // 年份切换方法
    prevYear: function prevYear() {
      this.currentYear--;
      this.loadRankingData();
    },
    nextYear: function nextYear() {
      this.currentYear++;
      this.loadRankingData();
    }
  }, (0, _defineProperty2.default)(_methods, "prevMonth", function prevMonth() {
    if (this.currentMonth > 1) {
      this.currentMonth--;
    } else {
      this.currentMonth = 12;
      this.currentYear--;
    }
    this.refreshCurrentMonthData();
  }), (0, _defineProperty2.default)(_methods, "nextMonth", function nextMonth() {
    if (this.currentMonth < 12) {
      this.currentMonth++;
    } else {
      this.currentMonth = 1;
      this.currentYear++;
    }
    this.refreshCurrentMonthData();
  }), (0, _defineProperty2.default)(_methods, "refreshCurrentMonthData", function refreshCurrentMonthData() {
    var _this39 = this;
    // 清除当前月份的缓存，强制displayHonors重新计算
    var cacheKey = "".concat(this.currentYear, "-").concat(this.currentMonth);
    if (this.honorsCache[cacheKey]) {
      delete this.honorsCache[cacheKey];
    }

    // 强制重新计算所有计算属性
    this.$forceUpdate();

    // 确保统计数据立即更新
    this.$nextTick(function () {
      // 触发统计计算属性的重新计算
      var temp = _this39.featuredCount + _this39.totalViews + _this39.totalLikes;
      // 用temp变量确保计算被执行，但不做其他操作
    });
  }), (0, _defineProperty2.default)(_methods, "loadRankingData", function loadRankingData() {
    var _this40 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee19() {
      return _regenerator.default.wrap(function _callee19$(_context19) {
        while (1) {
          switch (_context19.prev = _context19.next) {
            case 0:
              _context19.prev = 0;
              if (!(_this40.allHonorsData.length === 0)) {
                _context19.next = 4;
                break;
              }
              _context19.next = 4;
              return _this40.loadAllHonorData();
            case 4:
              // 强制重新计算排行榜
              _this40.$forceUpdate();
              _context19.next = 9;
              break;
            case 7:
              _context19.prev = 7;
              _context19.t0 = _context19["catch"](0);
            case 9:
            case "end":
              return _context19.stop();
          }
        }
      }, _callee19, null, [[0, 7]]);
    }))();
  }), (0, _defineProperty2.default)(_methods, "initTimeData", function initTimeData() {
    var now = new Date();
    this.currentYear = now.getFullYear();
    this.currentMonth = now.getMonth() + 1;

    // 计算当前周数（简化版本：一年中的第几周）
    var startOfYear = new Date(now.getFullYear(), 0, 1);
    var dayOfYear = Math.floor((now - startOfYear) / (24 * 60 * 60 * 1000)) + 1;
    this.currentWeek = Math.ceil(dayOfYear / 7);

    // 计算当前月份的第几周（1-4周）
    this.currentWeekOfMonth = this.getWeekOfMonth(now);

    // 计算当前季度
    this.currentQuarter = Math.ceil(this.currentMonth / 3);
  }), (0, _defineProperty2.default)(_methods, "getWeekOfMonth", function getWeekOfMonth(date) {
    var firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
    var dayOfMonth = date.getDate();

    // 计算月初是周几（0=周日，1=周一...）
    var firstDayWeekday = firstDayOfMonth.getDay();

    // 计算当前日期是当月第几周
    // 如果月初不是周一，第一周可能不完整
    var weekOfMonth = Math.ceil((dayOfMonth + firstDayWeekday) / 7);

    // 限制在1-4周范围内
    return Math.min(weekOfMonth, 4);
  }), (0, _defineProperty2.default)(_methods, "showCustomToast", function showCustomToast(message) {
    var _this41 = this;
    var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'success';
    var duration = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 2000;
    // 清除之前的定时器
    if (this.customToast.timer) {
      clearTimeout(this.customToast.timer);
    }

    // 设置toast内容
    this.customToast.message = message;
    this.customToast.type = type;
    this.customToast.show = true;

    // 自动隐藏
    this.customToast.timer = setTimeout(function () {
      _this41.customToast.show = false;
      _this41.customToast.timer = null;
    }, duration);
  }), (0, _defineProperty2.default)(_methods, "onPullDownRefresh", function onPullDownRefresh() {
    var _this42 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee20() {
      var cacheKey;
      return _regenerator.default.wrap(function _callee20$(_context20) {
        while (1) {
          switch (_context20.prev = _context20.next) {
            case 0:
              _context20.prev = 0;
              // 清除缓存
              cacheKey = "".concat(_this42.currentYear, "-").concat(_this42.currentMonth);
              if (_this42.honorsCache[cacheKey]) {
                delete _this42.honorsCache[cacheKey];
              }

              // 重新加载数据
              _context20.next = 5;
              return _this42.loadAllHonorData();
            case 5:
              // 刷新当前视图
              _this42.refreshCurrentMonthData();

              // 显示刷新成功提示
              _this42.showCustomToast('刷新成功');
              _context20.next = 12;
              break;
            case 9:
              _context20.prev = 9;
              _context20.t0 = _context20["catch"](0);
              _this42.showCustomToast('刷新失败', 'error');
            case 12:
              _context20.prev = 12;
              // 停止下拉刷新动画
              uni.stopPullDownRefresh();
              return _context20.finish(12);
            case 15:
            case "end":
              return _context20.stop();
          }
        }
      }, _callee20, null, [[0, 9, 12, 15]]);
    }))();
  }), _methods)
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"]))

/***/ }),

/***/ 242:
/*!***********************************************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/gallery/index.vue?vue&type=style&index=0&lang=scss& ***!
  \***********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss& */ 243);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 243:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/honor_pkg/gallery/index.vue?vue&type=style&index=0&lang=scss& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[236,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/honor_pkg/gallery/index.js.map
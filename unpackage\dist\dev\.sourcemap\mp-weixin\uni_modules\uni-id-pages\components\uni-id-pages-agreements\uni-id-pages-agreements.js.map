{"version": 3, "sources": ["webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?d121", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?1223", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?4acd", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?fd32", "uni-app:///uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?9aed", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?f451"], "names": ["name", "computed", "agreements", "serviceUrl", "privacyUrl", "url", "title", "props", "scope", "type", "default", "methods", "popupConfirm", "popup", "navigateTo", "uni", "success", "fail", "console", "icon", "hasAnd", "setAgree", "created", "data", "isAgree", "needAgreements", "needPopupAgreements", "retryFun"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgJ;AAChJ;AAC2E;AACL;AACsC;;;AAG5G;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,6FAAM;AACR,EAAE,8GAAM;AACR,EAAE,uHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kHAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,gWAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAAinB,CAAgB,2oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgCroB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AAJA,gBAKA;EACAA;EACAC;IACAC;MACA;QACA;MACA;MACA;QAAAC;QAAAC;MACA,QACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;;MAEA;MACA;MAEA;;MAGA;MACA;;MAEA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;;MAEA;;MAQA;QACA;UACA;QACA;QACA;MACA;IAEA;IACAC,sCAGA;MAAA,IAFAT;QACAC;MAEA;MACA;QACA;QACAS;UACAV;UACAW,gCACA;UACAC;YACAC;YACA;YACAH;cACAV;cACAY;gBACAF;kBACAT;kBACAa;gBACA;cACA;YACA;UACA;QACA;MACA;QACA;QACAJ;UACAV;UACAY;YACAF;cACAT;cACAa;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IAAA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AClKA;AAAA;AAAA;AAAA;AAAorC,CAAgB,0pCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-id-pages-agreements.vue?vue&type=template&id=6abcbb91&scoped=true&\"\nvar renderjs\nimport script from \"./uni-id-pages-agreements.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-id-pages-agreements.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-id-pages-agreements.vue?vue&type=style&index=0&id=6abcbb91&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6abcbb91\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-agreements.vue?vue&type=template&id=6abcbb91&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog\" */ \"@/uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.agreements.length\n  var l0 =\n    g0 && _vm.needAgreements\n      ? _vm.__map(_vm.agreements, function (agreement, index) {\n          var $orig = _vm.__get_orig(agreement)\n          var m0 = _vm.hasAnd(_vm.agreements, index)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var l1 =\n    g0 && (_vm.needAgreements || _vm.needPopupAgreements)\n      ? _vm.__map(_vm.agreements, function (agreement, index) {\n          var $orig = _vm.__get_orig(agreement)\n          var m1 = _vm.hasAnd(_vm.agreements, index)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-agreements.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-agreements.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"root\" v-if=\"agreements.length\">\n\t\t<template v-if=\"needAgreements\">\n\t\t\t<checkbox-group @change=\"setAgree\">\n\t\t\t\t<label class=\"checkbox-box\">\n\t\t\t\t\t<checkbox :checked=\"isAgree\" style=\"transform: scale(0.5);margin-right: -6px;\" />\n\t\t\t\t\t<text class=\"text\">同意</text>\n\t\t\t\t</label>\n\t\t\t</checkbox-group>\n\t\t\t<view class=\"content\">\n\t\t\t\t<view class=\"item\" v-for=\"(agreement,index) in agreements\" :key=\"index\">\n\t\t\t\t\t<text class=\"agreement text\" @click=\"navigateTo(agreement)\">{{agreement.title}}</text>\n\t\t\t\t\t<text class=\"text and\" v-if=\"hasAnd(agreements,index)\" space=\"nbsp\"> 和 </text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t\t<!-- 弹出式 -->\n\t\t<uni-popup v-if=\"needAgreements||needPopupAgreements\" ref=\"popupAgreement\" type=\"center\">\n\t\t\t<uni-popup-dialog confirmText=\"同意\" @confirm=\"popupConfirm\">\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<text class=\"text\">请先阅读并同意</text>\n\t\t\t\t\t<view class=\"item\" v-for=\"(agreement,index) in agreements\" :key=\"index\">\n\t\t\t\t\t\t<text class=\"agreement text\" @click=\"navigateTo(agreement)\">{{agreement.title}}</text>\n\t\t\t\t\t\t<text class=\"text and\" v-if=\"hasAnd(agreements,index)\" space=\"nbsp\"> 和 </text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</uni-popup-dialog>\n\t\t</uni-popup>\n\t</view>\n</template>\n\n<script>\n\timport config from '@/uni_modules/uni-id-pages/config.js'\n\t/**\n\t\t* uni-id-pages-agreements\n\t\t* @description 用户服务协议和隐私政策条款组件\n\t\t* @property {String,Boolean} scope = [register|login]\t作用于哪种场景如：register 注册（包括登录并注册，如：微信登录、苹果登录、短信验证码登录）、login 登录。默认值为：register\n\t*/\n\texport default {\n\t\tname: \"uni-agreements\",\n\t\tcomputed: {\n\t\t\tagreements() {\n\t\t\t\tif(!config.agreements){\n\t\t\t\t\treturn []\n\t\t\t\t}\n\t\t\t\tlet {serviceUrl,privacyUrl} = config.agreements\n\t\t\t\treturn [\n\t\t\t\t\t{\n\t\t\t\t\t\turl:serviceUrl,\n\t\t\t\t\t\ttitle:\"用户服务协议\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\turl:privacyUrl,\n\t\t\t\t\t\ttitle:\"隐私政策条款\"\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t}\n\t\t},\n\t\tprops: {\n\t\t\tscope: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault(){\n\t\t\t\t\treturn 'register'\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tmethods: {\n\t\t\tpopupConfirm(){\n\t\t\t\t// 设置协议已同意\n\t\t\t\tthis.isAgree = true\n\t\t\t\t\n\t\t\t\t// 关闭弹窗\n\t\t\t\tthis.needPopupAgreements = false\n\t\t\t\t// #ifndef MP-HARMONY\n\t\t\t\tthis.$refs.popupAgreement.close()\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// 通知父组件协议状态已更新\n\t\t\t\tthis.$emit('setAgree', this.isAgree)\n\t\t\t\t\n\t\t\t\t// 调用回调函数继续登录流程\n\t\t\t\tif(this.retryFun){\n\t\t\t\t\tthis.retryFun()\n\t\t\t\t}\n\t\t\t},\n\t\t\tpopup(Fun){\n\t\t\t\tthis.needPopupAgreements = true\n\t\t\t\t// this.needAgreements = true\n\n\t\t\t\t//::TODO 鸿蒙元服务暂不支持 createAnimation，等支持后再打开\n\t\t\t\t// #ifdef MP-HARMONY\n\t\t\t\t\treturn uni.showModal({\n\t\t\t\t\t\ttitle: \"提示\",\n\t\t\t\t\t\tcontent: `请先阅读并同意${this.agreements.map(item=>`\"${item.title}\"`).join('和')}`,\n\t\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef MP-HARMONY\n\t\t\t\tthis.$nextTick(()=>{\n\t\t\t\t\tif(Fun){\n\t\t\t\t\t\tthis.retryFun = Fun\n\t\t\t\t\t}\n\t\t\t\t\tthis.$refs.popupAgreement.open()\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tnavigateTo({\n\t\t\t\turl,\n\t\t\t\ttitle\n\t\t\t}) {\n\t\t\t\t// 检查URL是否是相对路径（以/开头）\n\t\t\t\tif (url.startsWith('/')) {\n\t\t\t\t\t// 直接使用相对路径导航\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: url,\n\t\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('跳转失败:', err);\n\t\t\t\t\t\t\t// 如果跳转失败，尝试使用webview\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/common/webview/webview?url=' + encodeURIComponent(url) + '&title=' + encodeURIComponent(title),\n\t\t\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 外部URL使用webview打开\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/common/webview/webview?url=' + encodeURIComponent(url) + '&title=' + encodeURIComponent(title),\n\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\thasAnd(agreements, index) {\n\t\t\t\treturn agreements.length - 1 > index\n\t\t\t},\n\t\t\tsetAgree(e) {\n\t\t\t\tthis.isAgree = !this.isAgree\n\t\t\t\tthis.$emit('setAgree', this.isAgree)\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.needAgreements = (config?.agreements?.scope || []).includes(this.scope)\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisAgree: false,\n\t\t\t\tneedAgreements:true,\n\t\t\t\tneedPopupAgreements:false,\n\t\t\t\tretryFun: null\n\t\t\t};\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t/* #ifndef APP-NVUE */\n\tview {\n\t\tdisplay: flex;\n\t\tbox-sizing: border-box;\n\t\tflex-direction: column;\n\t}\n\n\t/* #endif */\n\t.root {\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tfont-size: 12px;\n\t\tcolor: #8a8f8b;\n\t}\n\n\t.checkbox-box ,.uni-label-pointer{\n\t\talign-items: center;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t}\n\n\t.item {\n\t\tflex-direction: row;\n\t}\n\t.text{\n\t\tline-height: 26px;\n\t}\n\t.agreement {\n\t\tcolor: #04498c;\n\t\tcursor: pointer;\n\t}\n\n\t.checkbox-box ::v-deep .uni-checkbox-input{\n\t\tborder-radius: 100%;\n\t}\n\n\t.checkbox-box ::v-deep .uni-checkbox-input.uni-checkbox-input-checked{\n\t\tborder-color: $uni-color-primary;\n\t\tcolor: #FFFFFF !important;\n\t\tbackground-color: $uni-color-primary;\n\t}\n\n\t.content{\n\t\tflex-wrap: wrap;\n\t\tflex-direction: row;\n\t}\n\n\t.root ::v-deep .uni-popup__error{\n\t\tcolor: #333333;\n\t}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-agreements.vue?vue&type=style&index=0&id=6abcbb91&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-agreements.vue?vue&type=style&index=0&id=6abcbb91&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558450543\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
{"version": 3, "sources": ["webpack:///D:/Xwzc/components/patrol/p-map.vue?f56a", "webpack:///D:/Xwzc/components/patrol/p-map.vue?8839", "webpack:///D:/Xwzc/components/patrol/p-map.vue?9763", "webpack:///D:/Xwzc/components/patrol/p-map.vue?60a5", "uni-app:///components/patrol/p-map.vue", "webpack:///D:/Xwzc/components/patrol/p-map.vue?626d", "webpack:///D:/Xwzc/components/patrol/p-map.vue?4559"], "names": ["name", "data", "map", "mapCtx", "localMarkers", "localCircles", "localPolyline", "showMap", "showMarkers", "showCircles", "showRoute", "currentTask", "currentPatrolPoints", "currentRound", "loading", "rounds_detail", "props", "mapId", "type", "default", "task", "latitude", "longitude", "scale", "showLocationButton", "showCompass", "enableRotate", "height", "markers", "markerData", "polyline", "polylineData", "circles", "circleData", "show", "realtime", "round", "showLocation", "computed", "centerLongitude", "centerLatitude", "formattedMarkers", "processedMarker", "formattedPolylines", "formattedCircles", "methods", "initMap", "safeMapOperation", "console", "operation", "handleTaskChange", "newTask", "roundsData", "processTaskMarkers", "points", "validCoordinates", "i", "point", "onMarkerTap", "onCalloutTap", "onRegionChange", "getMapContext", "moveToLocation", "mapContext", "moveToCurrentLocation", "includePoints", "padding", "getCurrentLocation", "uni", "isHighAccuracy", "highAccuracyExpireTime", "success", "resolve", "fail", "reject", "calculateDistance", "Math", "watch", "handler", "deep", "immediate", "created", "mounted", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqBnnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,gBAEA;EACAA;EACAC;IACA;MACA;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;QAAA;MAAA;IACA;IACA;IACAU;MACAX;MACAC;QAAA;MAAA;IACA;IACA;IACAW;MACAZ;MACAC;QAAA;MAAA;IACA;IACA;IACAY;MACAb;MACAC;QAAA;MAAA;IACA;IACA;IACAa;MACAd;MACAC;QAAA;MAAA;IACA;IACA;IACAc;MACAf;MACAC;QAAA;MAAA;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;EACA;EACAmB;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAEA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;;QAEA;QACA;UACA;UACAC;QACA;UACA;UACA;UACA;UACAA;QACA;QAEA;MACA;IACA;IACAC;MAEA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IACAC;MAEA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACA;MACA;MAEA;QACAC;QACA;MACA;QACAD;QACA;MACA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAKA;;gBAEA;gBACA;;gBAGA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAvB,8CACA;gBACA;;gBAEA;gBACA;kBACAE,gDACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGAqB;kBAAA;kBAAA;gBAAA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAIA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC,aAEA;gBACA;kBACA;kBACA;oBACA;sBACAA;oBACA;kBACA;gBACA;gBACA;gBAAA,KACA;kBACAA;gBACA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAN;gBAAA;cAAA;gBAIA;gBACApB;gBACAI,cAEA;gBACAuB,uBAEA;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAC,mBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAJAD;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IASA;IACA;IACAE;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;UACAtC;UACAD;QACA;MACA;IACA;IACA;IACAwC;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MAEA;MACA;MAEA;QACAC;UACAzC;UACAD;QACA;MACA;QACA;MACA;IACA;IACA;IACA2C;MACA;MAEA;MACA;QACAD;MACA;IACA;IACA;IACAE;MACA;MAEA;MACA;QACAF;UACAT;UACAY;QACA;MACA;IACA;IACA;IACAC;MACA;QACAC;UACAlD;UACAmD;UACAC;UACAC;YACAC;UACA;UACAC;YACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QAEA,+DACAC;QAEA;MACA;QACA5B;QACA;MACA;IACA;EACA;EACA6B;IACA3C;MAAA;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAd;MACA0D;QACA;QACA;UACA;QACA;MACA;MACAC;MACAC;IACA;IACA;IACA5C;MACA0C;QACA;QACA;QAEA;QACA;QACA;UACA;QACA;MACA;MACAC;IACA;IACA;IACAlD;MACAiD;QACA;QACA;QAEA;UACA;QACA;MACA;MACAC;IACA;IACA;IACA9C;MACA6C;QACA;QACA;QAEA;UACA;QACA;MACA;MACAC;IACA;IACA;IACAhD;MACA+C;QACA;QACA;QAEA;UACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACA;EACAE;IACA;IACA;EACA;EACAC;IAAA;IACA;MACA;QACA;MACA;IACA;EACA;EACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC5hBA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/patrol/p-map.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./p-map.vue?vue&type=template&id=0d9e2492&\"\nvar renderjs\nimport script from \"./p-map.vue?vue&type=script&lang=js&\"\nexport * from \"./p-map.vue?vue&type=script&lang=js&\"\nimport style0 from \"./p-map.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/patrol/p-map.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-map.vue?vue&type=template&id=0d9e2492&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-map.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-map.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"p-map-container\" :style=\"{ height: height }\">\n    <map\n      id=\"map\"\n      class=\"map\"\n      :longitude=\"centerLongitude\"\n      :latitude=\"centerLatitude\"\n      :markers=\"formattedMarkers\"\n      :polyline=\"formattedPolylines\"\n      :circles=\"formattedCircles\"\n      :scale=\"scale\"\n      :show-location=\"showLocation\"\n      @markertap=\"onMarkerTap\"\n      @callouttap=\"onCalloutTap\"\n      @regionchange=\"onRegionChange\"\n    ></map>\n    <slot></slot>\n  </view>\n</template>\n\n<script>\n/**\n * 地图组件\n * 封装微信小程序地图组件，提供更丰富的功能\n */\n// 删除调试开关和调试日志函数\n// const DEBUG = false;\n// function debugLog(...args) {\n//   if (DEBUG) {\n//   }\n// }\n\nexport default {\n  name: 'p-map',\n  data() {\n    return {\n      // 地图相关\n      map: null,\n      mapCtx: null,\n      // 地图数据 - 重命名避免与props冲突\n      localMarkers: [],\n      localCircles: [],\n      localPolyline: [],\n      // 显示控制\n      showMap: false,\n      showMarkers: true,\n      showCircles: true,\n      showRoute: true,\n      // 缓存当前数据\n      currentTask: null,\n      currentPatrolPoints: null,\n      currentRound: null,\n      // 正在加载中\n      loading: false,\n      // 新增 - 缓存新数据结构的轮次详情\n      rounds_detail: null,\n    }\n  },\n  props: {\n    // 地图ID\n    mapId: {\n      type: String,\n      default: 'patrolMap'\n    },\n    // 当前任务数据\n    task: {\n      type: Object,\n      default: null\n    },\n    // 纬度（可选，指定中心点）\n    latitude: {\n      type: [String, Number],\n      default: 30.0\n    },\n    // 经度（可选，指定中心点）\n    longitude: {\n      type: [String, Number],\n      default: 120.0\n    },\n    // 缩放级别\n    scale: {\n      type: [String, Number],\n      default: 16\n    },\n    // 显示定位按钮\n    showLocationButton: {\n      type: Boolean,\n      default: false\n    },\n    // 显示指南针\n    showCompass: {\n      type: Boolean,\n      default: true\n    },\n    // 允许旋转\n    enableRotate: {\n      type: Boolean,\n      default: true\n    },\n    // 地图高度\n    height: {\n      type: String,\n      default: '300px'\n    },\n    // 标记点数据 - 保留旧名称向后兼容\n    markers: {\n      type: Array,\n      default: () => []\n    },\n    // 标记点数据 - 新名称\n    markerData: {\n      type: Array,\n      default: () => []\n    },\n    // 路线数据 - 保留旧名称向后兼容\n    polyline: {\n      type: Array,\n      default: () => []\n    },\n    // 路线数据 - 新名称\n    polylineData: {\n      type: Array,\n      default: () => []\n    },\n    // 圆形区域数据 - 保留旧名称向后兼容\n    circles: {\n      type: Array,\n      default: () => []\n    },\n    // 圆形区域数据 - 新名称\n    circleData: {\n      type: Array,\n      default: () => []\n    },\n    // 是否显示地图\n    show: {\n      type: Boolean,\n      default: true\n    },\n    // 是否实时刷新数据（用于追踪模式）\n    realtime: {\n      type: Boolean,\n      default: false\n    },\n    // 当前轮次数据\n    round: {\n      type: Object,\n      default: null\n    },\n    // 是否显示当前位置\n    showLocation: {\n      type: Boolean,\n      default: true\n    },\n  },\n  computed: {\n    centerLongitude() {\n      return Number(this.longitude);\n    },\n    centerLatitude() {\n      return Number(this.latitude);\n    },\n    formattedMarkers() {\n      \n      // 优先使用markerData，其次使用markers prop，最后才使用本地数据\n      const sourceData = this.markerData || this.markers || this.localMarkers;\n      \n      // 如果没有有效的标记点数据，返回空数组\n      if (!sourceData || !Array.isArray(sourceData) || sourceData.length === 0) {\n        return [];\n      }\n      \n      // 确保所有marker的id都是数字类型，并保留callout属性\n      return sourceData.map((marker, index) => {\n        // 创建marker的副本，避免修改原始数据\n        const processedMarker = {...marker};\n        \n        // 确保id为数字\n        if (processedMarker.id === undefined || processedMarker.id === null || typeof processedMarker.id !== 'number') {\n          // 如果id不存在或不是数字，则使用索引作为id\n          processedMarker.id = index;\n        } else if (typeof processedMarker.id === 'string') {\n          // 如果id是字符串，尝试转换为数字\n          const numId = Number(processedMarker.id);\n          // 如果转换成功并且是有效数字，使用转换后的数字，否则使用索引\n          processedMarker.id = !isNaN(numId) ? numId : index;\n        }\n        \n        return processedMarker;\n      });\n    },\n    formattedPolylines() {\n      \n      // 优先使用polylineData，其次使用polyline prop，最后才使用本地数据\n      const sourceData = this.polylineData || this.polyline || this.localPolyline;\n      \n      // 如果没有有效的路线数据，返回空数组\n      if (!sourceData || !Array.isArray(sourceData) || sourceData.length === 0) {\n        return [];\n      }\n      \n      // 返回路线数据，不做格式处理\n      return sourceData;\n    },\n    formattedCircles() {\n      \n      // 优先使用circleData，其次使用circles prop，最后才使用本地数据\n      const sourceData = this.circleData || this.circles || this.localCircles;\n      \n      // 如果没有有效的圆形区域数据，返回空数组\n      if (!sourceData || !Array.isArray(sourceData) || sourceData.length === 0) {\n        return [];\n      }\n      \n      // 返回圆形区域数据，不做格式处理\n      return sourceData;\n    }\n  },\n  methods: {\n    // 初始化地图\n    initMap() {\n      // 创建地图上下文\n      this.mapCtx = uni.createMapContext('map', this);\n    },\n    \n    // 安全地执行地图操作的辅助函数\n    safeMapOperation(operation) {\n      if (!this.mapCtx) {\n        console.warn('地图上下文不存在，无法执行操作');\n        return false;\n      }\n      \n      try {\n        operation();\n        return true;\n      } catch (error) {\n        console.error('执行地图操作时出错:', error);\n        return false;\n      }\n    },\n    \n    // 处理任务变更\n    async handleTaskChange(newTask) {\n      if (!newTask) {\n        // 如果任务为空，清空地图数据\n        this.localMarkers = [];\n        this.localCircles = [];\n        this.localPolyline = [];\n        return;\n      }\n      \n      try {\n        this.loading = true;\n        \n        // 存储当前任务\n        this.currentTask = newTask;\n        \n        \n        // 检查是否有点位数据\n        if (newTask.points && Array.isArray(newTask.points) && newTask.points.length > 0) {\n          // 直接使用传入的点位数据\n          const markers = this.buildMarkers(newTask.points);\n          // 更新markerData\n          this.markerData = markers;\n          \n          // 构建路线\n          if (newTask.points.length > 1) {\n            const polyline = this.buildPolyline(newTask.points);\n            // 更新polylineData\n            this.polylineData = [polyline];\n          }\n        }\n        // 优先使用新的数据结构 rounds_detail\n        else if (newTask.rounds_detail && newTask.rounds_detail.length > 0) {\n          this.rounds_detail = newTask.rounds_detail;\n          \n          // 处理点位数据\n          await this.processTaskMarkers();\n        } \n        // 兼容旧数据结构\n        else if (newTask.shift && newTask.shift.rounds) {\n          // 获取轮次和点位信息\n          const roundsData = await this.getRoundsData(newTask);\n          if (roundsData && roundsData.points) {\n            this.currentPatrolPoints = roundsData.points;\n            await this.processTaskMarkers();\n          }\n        }\n        \n        this.loading = false;\n      } catch (error) {\n        console.error('处理任务数据出错：', error);\n        this.loading = false;\n      }\n    },\n    \n    // 处理任务点位标记\n    async processTaskMarkers() {\n      let points = [];\n      \n      // 使用新的数据结构\n      if (this.rounds_detail) {\n        // 从rounds_detail中获取所有点位\n        this.rounds_detail.forEach(round => {\n          if (round.points && round.points.length) {\n            points = [...points, ...round.points];\n          }\n        });\n      } \n      // 兼容旧数据结构\n      else if (this.currentPatrolPoints) {\n        points = this.currentPatrolPoints;\n      }\n      \n      if (!points || points.length === 0) {\n        console.warn('无法找到任务点位数据');\n        return;\n      }\n      \n      // 创建点位标记\n      const markers = [];\n      const circles = [];\n      \n      // 存储有效的坐标点用于路线绘制\n      let validCoordinates = [];\n      \n      // 处理每个点位\n      for (let i = 0; i < points.length; i++) {\n        const point = points[i];\n        \n        // 跳过无效点位\n        if (!point.longitude || !point.latitude) {\n          continue;\n        }\n        // ... existing code ...\n      }\n    },\n    // 点击标记\n    onMarkerTap(e) {\n      // 确保事件数据正确\n      const markerId = e.markerId !== undefined ? e.markerId : (e.detail && e.detail.markerId);\n      if (markerId !== undefined) {\n        this.$emit('marker-tap', markerId);\n      }\n    },\n    // 点击气泡\n    onCalloutTap(e) {\n      // 确保事件数据正确\n      const markerId = e.markerId !== undefined ? e.markerId : (e.detail && e.detail.markerId);\n      if (markerId !== undefined) {\n        this.$emit('callout-tap', markerId);\n      }\n    },\n    // 地图区域变化\n    onRegionChange(e) {\n      if (e.type === 'end' && e.causedBy === 'drag') {\n        this.$emit('region-change', {\n          longitude: e.longitude,\n          latitude: e.latitude\n        });\n      }\n    },\n    // 获取地图上下文\n    getMapContext() {\n      if (!this.mapCtx) {\n        this.mapCtx = uni.createMapContext('map', this);\n      }\n      return this.mapCtx;\n    },\n    // 移动到指定位置\n    moveToLocation(longitude, latitude) {\n      if (!this.safeMapOperation(() => {})) return;\n      \n      const mapContext = this.getMapContext();\n      if (!mapContext) return;\n      \n      if (longitude && latitude) {\n        mapContext.moveToLocation({\n          longitude: Number(longitude),\n          latitude: Number(latitude)\n        });\n      } else {\n        this.moveToCurrentLocation();\n      }\n    },\n    // 移动到当前位置\n    moveToCurrentLocation() {\n      if (!this.safeMapOperation(() => {})) return;\n      \n      const mapContext = this.getMapContext();\n      if (mapContext) {\n        mapContext.moveToLocation();\n      }\n    },\n    // 包含所有点标记\n    includePoints(points, padding) {\n      if (!this.safeMapOperation(() => {})) return;\n      \n      const mapContext = this.getMapContext();\n      if (mapContext && points && points.length > 0) {\n        mapContext.includePoints({\n          points,\n          padding: padding || [30, 30, 30, 30]\n        });\n      }\n    },\n    // 获取当前位置\n    getCurrentLocation() {\n      return new Promise((resolve, reject) => {\n        uni.getLocation({\n          type: 'gcj02',\n          isHighAccuracy: true,\n          highAccuracyExpireTime: 3000,\n          success: res => {\n            resolve(res);\n          },\n          fail: err => {\n            reject(err);\n          }\n        });\n      });\n    },\n    // 计算两点之间的距离（米）\n    calculateDistance(point1, point2) {\n      try {\n        const EARTH_RADIUS = 6378137.0; // 地球半径\n        const lat1 = (point1.latitude * Math.PI) / 180.0;\n        const lng1 = (point1.longitude * Math.PI) / 180.0;\n        const lat2 = (point2.latitude * Math.PI) / 180.0;\n        const lng2 = (point2.longitude * Math.PI) / 180.0;\n        \n        const a = lat1 - lat2;\n        const b = lng1 - lng2;\n        \n        const s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + \n            Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));\n        \n        return s * EARTH_RADIUS;\n      } catch (e) {\n        console.error('距离计算出错:', e);\n        return 0;\n      }\n    }\n  },\n  watch: {\n    show(val) {\n      this.showMap = val;\n      if (val) {\n        this.$nextTick(() => {\n          this.initMap();\n        });\n      }\n    },\n    // 监听任务数据变化\n    task: {\n      handler(newTask) {\n        // 如果组件已销毁或正在销毁中，不执行handleTaskChange\n        if (!this._isDestroyed && !this._isBeingDestroyed) {\n          this.handleTaskChange(newTask);\n        }\n      },\n      deep: true,\n      immediate: true\n    },\n    // 监听当前轮次变化\n    round: {\n      handler(newRound) {\n        // 如果组件已销毁或正在销毁中，不执行更新\n        if (this._isDestroyed || this._isBeingDestroyed) return;\n        \n        this.currentRound = newRound;\n        // 如果有任务数据且地图已初始化，更新地图显示\n        if (this.currentTask && this.mapCtx) {\n          this.processTaskMarkers();\n        }\n      },\n      deep: true\n    },\n    // 外部传入的标记数据\n    markerData: {\n      handler(newMarkers) {\n        // 检查组件是否已销毁\n        if (this._isDestroyed || this._isBeingDestroyed) return;\n        \n        if (newMarkers && newMarkers.length > 0) {\n          this.localMarkers = newMarkers;\n        }\n      },\n      deep: true\n    },\n    // 外部传入的圆形区域数据\n    circleData: {\n      handler(newCircles) {\n        // 检查组件是否已销毁\n        if (this._isDestroyed || this._isBeingDestroyed) return;\n        \n        if (newCircles && newCircles.length > 0) {\n          this.localCircles = newCircles;\n        }\n      },\n      deep: true\n    },\n    // 外部传入的路线数据\n    polylineData: {\n      handler(newPolyline) {\n        // 检查组件是否已销毁\n        if (this._isDestroyed || this._isBeingDestroyed) return;\n        \n        if (newPolyline && newPolyline.length > 0) {\n          // 修复bug：将polyline赋值给data中定义的polyline，而不是polylines\n          this.localPolyline = newPolyline;\n        }\n      },\n      deep: true\n    }\n  },\n  // 组件生命周期钩子\n  created() {\n    // 初始化\n    this.showMap = this.show;\n  },\n  mounted() {\n    if (this.showMap) {\n      this.$nextTick(() => {\n        this.initMap();\n      });\n    }\n  },\n  // 销毁时清理资源\n  beforeDestroy() {\n    // 清理地图实例和上下文\n    this.mapCtx = null;\n    this.map = null;\n    \n    // 清空数据，避免持续的异步操作\n    this.localMarkers = [];\n    this.localCircles = [];\n    this.localPolyline = [];\n    this.currentTask = null;\n  },\n};\n</script>\n\n<style lang=\"scss\">\n.p-map-container {\n  position: relative;\n  width: 100%;\n  height: 300px;\n  background-color: rgba(255, 255, 255, 0.85); /* 白色半透明 */\n  \n  .map {\n    width: 100%;\n    height: 100%;\n  }\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-map.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-map.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558435364\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
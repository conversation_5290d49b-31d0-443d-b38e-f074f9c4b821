<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户体验提升 - 株水小智</title>
    <script src="libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="libs/all.min.css">
    <script src="libs/echarts.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #1e40af 100%);
        }
        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .glass-effect:hover {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .animate-pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .subtitle-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 0.5rem;
            margin-bottom: 2rem;
        }
        .subtitle-bar:before, .subtitle-bar:after {
            content: "";
            flex: 1;
            height: 2px;
            background: linear-gradient(90deg, #38bdf8 0%, #a7f3d0 100%);
            margin: 0 1rem;
            border-radius: 1px;
            box-shadow: 0 0 8px rgba(56, 189, 248, 0.6);
        }
        .bubble {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.13);
            pointer-events: none;
            animation: floatBubble 12s linear infinite;
        }
        @keyframes floatBubble {
            0% { transform: translateY(0) scale(1); opacity: 0.7; }
            50% { opacity: 1; }
            100% { transform: translateY(-120vh) scale(1.2); opacity: 0; }
        }
        .icon-contrast {
            color: #38bdf8 !important;
            filter: drop-shadow(0 2px 4px rgba(30,64,175,0.12));
            background: #1e40af;
            border-radius: 0.6rem;
            padding: 0.18rem 0.32rem;
            margin-right: 0.7rem;
            font-size: 1.25rem;
            border: 1.5px solid #38bdf8;
        }
        .icon-contrast-green {
            color: #4ade80 !important;
            border-color: #bbf7d0;
            background: #166534;
        }
        .icon-contrast-orange {
            color: #fbbf24 !important;
            border-color: #fde68a;
            background: #78350f;
        }
        .highlight-blue { color: #38bdf8; font-weight: bold; }
        .highlight-green { color: #4ade80; font-weight: bold; }
        .highlight-yellow { color: #fbbf24; font-weight: bold; }
        .highlight-white { color: #fff; font-weight: bold; }
        .text-main { color: #fff; }
        .text-sub { color: #bae6fd; }
    </style>
</head>
<body class="gradient-bg min-h-screen flex flex-col">
    <!-- 进度条 -->
    <div class="fixed top-0 left-0 w-full h-1 bg-white/20 z-50">
        <div class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-1000" style="width: 90%"></div>
    </div>

    <!-- 气泡粒子动画 -->
    <div class="absolute inset-0 z-0 overflow-hidden">
        <div class="bubble" style="width:60px;height:60px;left:10vw;bottom:10vh;animation-delay:0s;"></div>
        <div class="bubble" style="width:40px;height:40px;left:80vw;bottom:20vh;animation-delay:2s;"></div>
        <div class="bubble" style="width:80px;height:80px;left:30vw;bottom:5vh;animation-delay:4s;"></div>
        <div class="bubble" style="width:30px;height:30px;left:60vw;bottom:15vh;animation-delay:6s;"></div>
        <div class="bubble" style="width:50px;height:50px;left:50vw;bottom:8vh;animation-delay:1s;"></div>
    </div>

    <div class="relative z-10 flex-1 flex flex-col justify-center">
        <!-- 头部 -->
        <header class="text-center py-8">
            <div class="animate-pulse-slow">
                <h1 class="text-5xl font-bold text-white mb-4 flex items-center justify-center">
                    <i class="fas fa-star mr-4 text-blue-200"></i>
                    用户体验提升
                </h1>
                <div class="subtitle-bar">
                    <span class="text-xl text-blue-100 font-light">操作便捷协作顺畅</span>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="flex-1 px-8 pb-8">
            <div class="max-w-7xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-8 mb-8">
                    <!-- 工作效率提升 -->
                    <div class="glass-effect rounded-2xl p-8 transform hover:scale-105 transition-all duration-300">
                        <div class="flex items-center mb-6">
                            <i class="fas fa-rocket text-blue-300 mr-4 text-2xl"></i>
                            <h3 class="text-2xl font-bold text-white">工作效率提升</h3>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center p-4 bg-blue-500/40 rounded-xl">
                                <i class="fas fa-bolt text-blue-300 mr-3 text-lg"></i>
                                <div>
                                    <span class="text-white font-semibold">问题处理从"被动等"变为"主动推"</span>
                                    <p class="text-blue-100 text-sm mt-1">响应更快，处理更及时</p>
                                </div>
                            </div>
                            <div class="flex items-center p-4 bg-blue-500/40 rounded-xl">
                                <i class="fas fa-sync-alt text-blue-300 mr-3 text-lg"></i>
                                <div>
                                    <span class="text-white font-semibold">跨部门协作通过系统直达</span>
                                    <p class="text-blue-100 text-sm mt-1">减少中间环节，沟通更高效</p>
                                </div>
                            </div>
                            <div class="flex items-center p-4 bg-blue-500/40 rounded-xl">
                                <i class="fas fa-bullseye text-blue-300 mr-3 text-lg"></i>
                                <div>
                                    <span class="text-white font-semibold">标准流程固化</span>
                                    <p class="text-blue-100 text-sm mt-1">新人也能快速上手，降低培训成本</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作体验优化 -->
                    <div class="glass-effect rounded-2xl p-8 transform hover:scale-105 transition-all duration-300">
                        <div class="flex items-center mb-6">
                            <i class="fas fa-mobile-alt text-green-300 mr-4 text-2xl"></i>
                            <h3 class="text-2xl font-bold text-white">操作体验优化</h3>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center p-4 bg-green-500/40 rounded-xl">
                                <i class="fas fa-list text-green-300 mr-3 text-lg"></i>
                                <div>
                                    <span class="text-white font-semibold">工作更有序</span>
                                    <p class="text-green-100 text-sm mt-1">任务清单化，进度可视化</p>
                                </div>
                            </div>
                            <div class="flex items-center p-4 bg-green-500/40 rounded-xl">
                                <i class="fas fa-mobile-alt text-green-300 mr-3 text-lg"></i>
                                <div>
                                    <span class="text-white font-semibold">操作更便捷</span>
                                    <p class="text-green-100 text-sm mt-1">手机随手用，随时随地处理</p>
                                </div>
                            </div>
                            <div class="flex items-center p-4 bg-green-500/40 rounded-xl">
                                <i class="fas fa-handshake text-green-300 mr-3 text-lg"></i>
                                <div>
                                    <span class="text-white font-semibold">配合更默契</span>
                                    <p class="text-green-100 text-sm mt-1">数据互通，信息共享</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户体验数据展示 -->
                <div class="glass-effect rounded-2xl p-8 mb-8">
                    <h2 class="text-3xl font-bold mb-6 text-center text-yellow-300 flex items-center justify-center">
                        <i class="fas fa-chart-line mr-3"></i>
                        用户体验数据
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center p-6 bg-green-500/20 rounded-xl">
                            <div class="text-4xl font-bold text-green-400 mb-2">90%</div>
                            <div class="text-lg text-white font-semibold">员工上手时间减少</div>
                            <div class="text-sm text-green-200">从1小时→5分钟</div>
                        </div>
                        <div class="text-center p-6 bg-blue-500/20 rounded-xl">
                            <div class="text-4xl font-bold text-blue-400 mb-2">100%</div>
                            <div class="text-lg text-white font-semibold">消息查看率</div>
                            <div class="text-sm text-blue-200">无遗漏，实时推送</div>
                        </div>
                        <div class="text-center p-6 bg-purple-500/20 rounded-xl">
                            <div class="text-4xl font-bold text-purple-400 mb-2">多场景</div>
                            <div class="text-lg text-white font-semibold">适配能力</div>
                            <div class="text-sm text-purple-200">现场、办公室、外出均可操作</div>
                        </div>
                    </div>
                </div>

                <!-- 用户反馈展示 -->
                <div class="glass-effect rounded-2xl p-8">
                    <h2 class="text-3xl font-bold mb-6 text-center text-green-300 flex items-center justify-center">
                        <i class="fas fa-comments mr-3"></i>
                        用户真实反馈
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-xl p-6 border border-blue-500/30">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div>
                                    <div class="font-semibold text-blue-200">刘师傅</div>
                                    <div class="text-sm text-blue-300">运行班组长</div>
                                </div>
                            </div>
                            <p class="text-gray-300 italic">"现在发现问题直接拍照上报，处理结果还能实时跟踪，比以前方便太多了！"</p>
                        </div>
                        <div class="bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl p-6 border border-green-500/30">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div>
                                    <div class="font-semibold text-green-200">谢调度</div>
                                    <div class="text-sm text-green-300">调度员</div>
                                </div>
                            </div>
                            <p class="text-gray-300 italic">"数据统计自动化了，报表从生成到整理缩短到只要几分钟，效率大幅提升！"</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 底部导航 -->
    <footer class="text-center py-6 text-blue-200 flex justify-center space-x-8 relative z-20">
        <button onclick="goPrev()" class="px-6 py-2 rounded-xl bg-white/20 hover:bg-white/30 transition text-lg font-semibold"><i class="fas fa-arrow-left mr-2"></i>上一页</button>
        <button onclick="goNext()" class="px-6 py-2 rounded-xl bg-white/20 hover:bg-white/30 transition text-lg font-semibold">下一页<i class="fas fa-arrow-right ml-2"></i></button>
    </footer>

    <script>
        // 导航
        function goPrev() { window.location.href = 'slide8.html'; }
        function goNext() { window.location.href = 'slide10.html'; }
        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                goPrev();
            } else if (e.key === 'ArrowRight' || e.key === ' ') {
                goNext();
            }
        });
    </script>
</body>
</html> 
{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/ucenter_pkg/todo.vue?8f95", "webpack:///D:/Xwzc/pages/ucenter_pkg/todo.vue?c8c4", "webpack:///D:/Xwzc/pages/ucenter_pkg/todo.vue?4047", "webpack:///D:/Xwzc/pages/ucenter_pkg/todo.vue?fdeb", "uni-app:///pages/ucenter_pkg/todo.vue", "webpack:///D:/Xwzc/pages/ucenter_pkg/todo.vue?278d", "webpack:///D:/Xwzc/pages/ucenter_pkg/todo.vue?4017"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "todoList", "userRole", "currentUserId", "page", "pageSize", "total", "reviewCount", "taskCount", "currentTab", "loadMoreStatus", "expandedItems", "isLoading", "isRefreshing", "lastRequestTime", "cachedQueries", "hasInitialized", "roleConfig", "reviewRoles", "responsibleRoles", "isPageVisible", "lastUpdateCheck", "computed", "showLoadMore", "allowedReview<PERSON>oles", "hasResponsiblePermission", "onLoad", "uni", "methods", "initRoleConfig", "console", "getUserInfo", "db", "where", "field", "get", "result", "showError", "title", "content", "showCancel", "confirmText", "switchTab", "checkUserPermission", "success", "fail", "url", "debounceGetTodoList", "generate<PERSON>ache<PERSON>ey", "clearCache", "clearExpiredCache", "key", "value", "getTodoList", "refresh", "cache<PERSON>ey", "cachedData", "uniCloud", "name", "action", "res", "applyDataFromCache", "updateDataFromResponse", "list", "timestamp", "updateLoadMoreStatus", "handleGetTodoListError", "icon", "duration", "finalizeTodoListLoading", "handleCrossDeviceUpdate", "shouldRefreshOnCrossDeviceUpdate", "silentRefresh", "refreshList", "handleFeedbackUpdated", "setTimeout", "handleTaskCompleted", "goToDetail", "getItemIcon", "getItemColor", "getItemTypeText", "getItemUserText", "formatDate", "toggleExpand", "Object", "isTextOverflow", "onPullDownRefresh", "onReachBottom", "onShow", "onHide", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrFA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;AC6FlnB;AAEA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAMA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QAAA,OACA;MAAA,EACA;IACA;EACA;EAEAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;cACA;cAAA;cAAA,OACA;YAAA;cAEA;cACAC;;cAEA;cACAA;cACAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EAEAC;IACA;AACA;AACA;AACA;IACAC;MACA;MACAC;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC,8BACAC,gCACAC,mBACAC;cAAA;gBAAA;gBAHAC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAN;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAO;MACAV;QACAW;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QAAA;MAAA;MACA;MAEA;QACAhB;UACAW;UACAC;UACAC;UACAC;UACAG;YACAjB;cACAkB;gBACAlB;kBAAAmB;gBAAA;cACA;YACA;UACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;QACA;MACA;MACA;MAEA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MAAA,2CAEA;QAAA;MAAA;QAAA;UAAA;YAAAC;YAAAC;UACA;YACA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;IACA;IAEAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBAEA;kBACA;kBACA;kBACA;gBACA;gBAEA;gBAAA;gBAGA;gBACAC,kEAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKAC;kBACAC;kBACA1D;oBACA2D;oBACAlD;oBACAL;oBACAC;kBACA;gBACA;cAAA;gBARAuD;gBAAA,MAUAA;kBAAA;kBAAA;gBAAA;gBACA5D;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIA8B;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACA+B;MACA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;MAEA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACA;MAEA;;MAEA;MACA;QACAxD;QACAC;QACAC;QACAuD;QACAC;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;MAEA;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACAvC;QACAW;QACA6B;QACAC;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;;MAEA;MACA;QACA1C;QACA;MACA;IACA;IAEA;AACA;AACA;IACA2C;MACA;QACA;QACA;UACAxC;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAyC;MAAA;MACA;MACA;QACAzC;QACA;MACA;;MAEA;MACA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;QACA;UAAA;QAAA;QACA;UACAA;UACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;MAEA;IACA;IAEA;AACA;AACA;IACA0C;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA1C;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACA2C;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACAD;QACA;MACA;IACA;IAEAE;MACA;MACA;QACA;QACAlD;UACAmB;UACAD;YACAlB;cACAW;cACA6B;YACA;UACA;QACA;MACA;QACA;QACAxC;UACAmB;UACAD;YACAlB;cACAW;cACA6B;YACA;UACA;QACA;MACA;IACA;IAEA;IACAW;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MAEA;MACA;MACA;MACA;MAEA;IACA;IAEAC;MAAA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;QACAC;UACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;;MAEA;MACA;;MAEA;;MAcA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;MACA;MACA;IACA;EACA;EAEAC;IAAA;IACA;;IAEA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA,6DACA,4BACA,mBACA;IAEA;MACAb;QACA;QACA;UACA7C;UACA;QACA;MACA;IACA;EACA;EAEA2D;IACA;IACA;IACA;EACA;EAEAC;IACA;IACA/D;IACAA;IACAA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjrBA;AAAA;AAAA;AAAA;AAAiqC,CAAgB,uoCAAG,EAAC,C;;;;;;;;;;;ACArrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ucenter_pkg/todo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/ucenter_pkg/todo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./todo.vue?vue&type=template&id=6972cfb7&scoped=true&\"\nvar renderjs\nimport script from \"./todo.vue?vue&type=script&lang=js&\"\nexport * from \"./todo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./todo.vue?vue&type=style&index=0&id=6972cfb7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6972cfb7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ucenter_pkg/todo.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./todo.vue?vue&type=template&id=6972cfb7&scoped=true&\"", "var components\ntry {\n  components = {\n    uniList: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-list/components/uni-list/uni-list\" */ \"@/uni_modules/uni-list/components/uni-list/uni-list.vue\"\n      )\n    },\n    uniListItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-list/components/uni-list-item/uni-list-item\" */ \"@/uni_modules/uni-list/components/uni-list-item/uni-list-item.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.todoList.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.todoList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.getItemIcon(item)\n          var m1 = _vm.getItemColor(item)\n          var m2 = _vm.getItemTypeText(item)\n          var m3 =\n            !_vm.expandedItems[index] && _vm.isTextOverflow(item.description)\n          var m4 = _vm.formatDate(item.createTime || item.assignedTime)\n          var m5 = _vm.getItemUserText(item)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./todo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./todo.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<view class=\"page-header\">\n\t\t\t<text class=\"page-title\">待处理事项 ({{total}})</text>\n\t\t</view>\n\t\t\n\t\t<!-- 分类选择 -->\n\t\t<view class=\"category-tabs\">\n\t\t\t<view class=\"tab-item\" \n\t\t\t\t:class=\"{ 'active': currentTab === 'all' }\" \n\t\t\t\t@click=\"switchTab('all')\">\n\t\t\t\t<text>全部 ({{total}})</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" \n\t\t\t\t:class=\"{ 'active': currentTab === 'review' }\" \n\t\t\t\t@click=\"switchTab('review')\">\n\t\t\t\t<text>审核待办 ({{reviewCount}})</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" \n\t\t\t\t:class=\"{ 'active': currentTab === 'task' }\" \n\t\t\t\t@click=\"switchTab('task')\" \n\t\t\t\tv-if=\"hasResponsiblePermission\">\n\t\t\t\t<text>我的任务 ({{taskCount}})</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 数据显示区域 -->\n\t\t<view class=\"data-area\" v-if=\"todoList.length > 0\">\n\t\t\t<view class=\"todo-list\">\t\t\t\n\t\t\t\t<uni-list class=\"list-wrapper\">\n\t\t\t\t\t<uni-list-item v-for=\"(item, index) in todoList\" \n\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\t:title=\"item.project || item.name || '未命名项目'\" \n\t\t\t\t\t\t:note=\"item.description || '无描述内容'\"\n\t\t\t\t\t\tclickable\n\t\t\t\t\t\t@click=\"goToDetail(item)\">\n\t\t\t\t\t\t<template v-slot:header>\n\t\t\t\t\t\t\t<view class=\"item-icon\">\n\t\t\t\t\t\t\t\t<uni-icons :type=\"getItemIcon(item)\" size=\"22\" :color=\"getItemColor(item)\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<template v-slot:body>\n\t\t\t\t\t\t\t<view class=\"item-body\">\n\t\t\t\t\t\t\t\t<view class=\"item-title-row\">\n\t\t\t\t\t\t\t\t\t<text class=\"item-title\">{{item.project || item.name || '未命名项目'}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"item-type-badge\" :class=\"item.type === 'task' ? 'task-badge' : 'review-badge'\">{{getItemTypeText(item)}}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"item-description-container\">\n\t\t\t\t\t\t\t\t\t<text class=\"item-description\" :class=\"{'expanded': expandedItems[index]}\" @click.stop=\"toggleExpand(index)\">{{item.description || '无描述内容'}}</text>\n\t\t\t\t\t\t\t\t\t<text v-if=\"!expandedItems[index] && isTextOverflow(item.description)\" class=\"expand-btn\" @click.stop=\"toggleExpand(index)\">展开</text>\n\t\t\t\t\t\t\t\t\t<text v-if=\"expandedItems[index]\" class=\"expand-btn\" @click.stop=\"toggleExpand(index)\">收起</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<template v-slot:footer>\n\t\t\t\t\t\t\t<view class=\"item-footer\">\n\t\t\t\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"item-time\">{{formatDate(item.createTime || item.assignedTime)}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"item-dot\">·</text>\n\t\t\t\t\t\t\t\t\t<text class=\"item-user\">{{getItemUserText(item)}}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#BBBBBB\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</uni-list-item>\n\t\t\t\t</uni-list>\n\t\t\t</view>\n\t\t\t\n\t\t\t<uni-load-more :status=\"loadMoreStatus\" v-if=\"showLoadMore\"></uni-load-more>\n\t\t</view>\n\t\t\n\t\t<!-- 没有数据时的状态处理 -->\n\t\t<view v-else class=\"data-area\">\n\t\t\t<!-- 初次加载中 -->\n\t\t\t<view v-if=\"!hasInitialized\" class=\"data-loading\">\n\t\t\t\t<uni-load-more status=\"loading\" :content-text=\"{ contentdown: '正在加载待办事项...' }\"></uni-load-more>\n\t\t\t</view>\n\t\t\t<!-- 搜索加载中 -->\n\t\t\t<view v-else-if=\"isLoading\" class=\"data-loading\">\n\t\t\t\t<uni-load-more status=\"loading\" :content-text=\"{ contentdown: '加载中...' }\"></uni-load-more>\n\t\t\t</view>\n\t\t\t<!-- 数据为空 -->\n\t\t\t<p-empty-state v-else \n\t\t\t\ttype=\"data\" \n\t\t\t\ttext=\"暂无待办事项\"\n\t\t\t\tsize=\"medium\">\n\t\t\t</p-empty-state>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\tconst db = uniCloud.database();\n\timport {\n\t\tstore\n\t} from '@/uni_modules/uni-id-pages/common/store.js'\n\timport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n\t\n\texport default {\n\t\tcomponents: {\n\t\t\tPEmptyState\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttodoList: [],\n\t\t\t\tuserRole: [],\n\t\t\t\tcurrentUserId: null,\n\t\t\t\tpage: 1,\n\t\t\t\tpageSize: 10,\n\t\t\t\ttotal: 0,\n\t\t\t\treviewCount: 0,\n\t\t\t\ttaskCount: 0,\n\t\t\t\tcurrentTab: 'all', // 当前选中的标签页\n\t\t\t\tloadMoreStatus: 'more',\n\t\t\t\texpandedItems: {}, // 用于跟踪哪些项目的描述被展开\n\t\t\t\tisLoading: false, // 添加加载状态\n\t\t\t\tisRefreshing: false, // 刷新状态\n\t\t\t\tlastRequestTime: 0, // 上次请求时间戳\n\t\t\t\tcachedQueries: new Map(), // 使用Map替代普通对象作为缓存，性能更好\n\t\t\t\thasInitialized: false, // 是否已初始化\n\t\t\t\t\n\t\t\t\t// 角色权限配置 - 从数据库动态获取\n\t\t\t\troleConfig: {\n\t\t\t\t\treviewRoles: ['supervisor', 'PM', 'GM', 'admin'],\n\t\t\t\t\tresponsibleRoles: ['logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'responsible', 'admin']\n\t\t\t\t},\n\t\t\t\t\n\t\t\t\t// 跨设备更新相关\n\t\t\t\tisPageVisible: true,\n\t\t\t\tlastUpdateCheck: 0\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 用于判断是否显示加载更多\n\t\t\tshowLoadMore() {\n\t\t\t\treturn this.todoList.length > 0 && !this.isLoading;\n\t\t\t},\n\t\t\t// 允许查看审核待办的角色列表 - 使用动态配置\n\t\t\tallowedReviewRoles() {\n\t\t\t\treturn this.roleConfig.reviewRoles;\n\t\t\t},\n\t\t\t// 是否有负责人权限（可以查看我的任务）- 使用动态配置\n\t\t\thasResponsiblePermission() {\n\t\t\t\treturn this.userRole.some(role => \n\t\t\t\t\tthis.roleConfig.responsibleRoles.includes(role)\n\t\t\t\t);\n\t\t\t}\n\t\t},\n\t\t\n\t\tasync onLoad() {\n\t\t\t// 初始化角色配置（使用默认配置）\n\t\t\tthis.initRoleConfig();\n\t\t\t// 加载用户信息\n\t\t\tawait this.getUserInfo();\n\t\t\t\n\t\t\t// 监听角标管理器的跨设备更新事件\n\t\t\tuni.$on('cross-device-update-detected', this.handleCrossDeviceUpdate);\n\t\t\t\n\t\t\t// 监听相关事件\n\t\t\tuni.$on('feedback-updated', this.handleFeedbackUpdated);\n\t\t\tuni.$on('task-completed', this.handleTaskCompleted);\n\t\t},\n\t\t\n\t\tmethods: {\n\t\t\t/**\n\t\t\t * 初始化角色配置\n\t\t\t * 使用默认配置，避免权限问题\n\t\t\t */\n\t\t\tinitRoleConfig() {\n\t\t\t\t// 使用可靠的默认配置\n\t\t\t\tconsole.log('✅ 初始化默认角色配置');\n\t\t\t\t// 角色配置已在data中定义，无需额外操作\n\t\t\t},\n\t\t\t\n\t\t\tasync getUserInfo() {\n\t\t\t\ttry {\n\t\t\t\t\tconst { result } = await db.collection('uni-id-users')\n\t\t\t\t\t\t.where(\"'_id' == $cloudEnv_uid\")\n\t\t\t\t\t\t.field('role, _id')\n\t\t\t\t\t\t.get();\n\t\t\t\t\t\n\t\t\t\t\tif (result.data && result.data.length > 0) {\n\t\t\t\t\t\tthis.userRole = result.data[0].role || [];\n\t\t\t\t\t\tthis.currentUserId = result.data[0]._id;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 获取待办列表\n\t\t\t\t\t\tawait this.getTodoList();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.showError('获取用户信息失败', '请重新登录');\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('❌ 获取用户信息失败:', e);\n\t\t\t\t\tthis.showError('获取用户信息失败', '网络异常，请稍后重试');\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 显示错误信息的统一方法\n\t\t\t */\n\t\t\tshowError(title, content) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle,\n\t\t\t\t\tcontent,\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: '确定'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 切换标签页\n\t\t\tswitchTab(tab) {\n\t\t\t\tif (this.currentTab === tab) return;\n\t\t\t\t\n\t\t\t\tthis.currentTab = tab;\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.todoList = [];\n\t\t\t\tthis.clearCache(); // 清空缓存\n\t\t\t\tthis.getTodoList(true);\n\t\t\t},\n\t\t\t\n\t\t\t// 检查用户权限\n\t\t\tcheckUserPermission() {\n\t\t\t\t// 检查用户是否有查看待办的权限\n\t\t\t\tconst hasReviewRole = this.userRole.some(role => this.allowedReviewRoles.includes(role));\n\t\t\t\tconst hasResponsibleRole = this.hasResponsiblePermission;\n\t\t\t\t\n\t\t\t\tif (!hasReviewRole && !hasResponsibleRole) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '权限不足',\n\t\t\t\t\t\tcontent: '您没有查看待办的权限，请联系管理员',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: '返回',\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tuni.navigateBack({\n\t\t\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\t\t\tuni.switchTab({ url: '/pages/index/index' });\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\t\n\t\t\t// 使用防抖处理获取数据\n\t\t\tdebounceGetTodoList(refresh = false) {\n\t\t\t\t// 防抖处理 - 避免短时间内多次请求\n\t\t\t\tconst now = Date.now();\n\t\t\t\tif (now - this.lastRequestTime < 300 && !refresh) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.lastRequestTime = now;\n\t\t\t\t\n\t\t\t\tthis.getTodoList(refresh);\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 生成缓存键 - 优化版本\n\t\t\t * 使用更简洁的键生成策略，提高缓存效率\n\t\t\t */\n\t\t\tgenerateCacheKey(page, pageSize) {\n\t\t\t\tconst roleKey = this.userRole.sort().join('_');\n\t\t\t\treturn `${this.currentTab}_${roleKey}_${page}_${pageSize}`;\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 清除所有缓存\n\t\t\t */\n\t\t\tclearCache() {\n\t\t\t\tthis.cachedQueries.clear();\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 清除过期缓存 - 优化版本\n\t\t\t */\n\t\t\tclearExpiredCache() {\n\t\t\t\tconst now = Date.now();\n\t\t\t\tconst expireTime = 5 * 60 * 1000; // 5分钟过期\n\t\t\t\t\n\t\t\t\tfor (const [key, value] of this.cachedQueries.entries()) {\n\t\t\t\t\tif (now - value.timestamp > expireTime) {\n\t\t\t\t\t\tthis.cachedQueries.delete(key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tasync getTodoList(refresh = false) {\n\t\t\t\t// 检查权限\n\t\t\t\tif (!this.checkUserPermission()) return;\n\t\t\t\t\n\t\t\t\tif (this.isLoading) return;\n\t\t\t\tthis.isLoading = true;\n\t\t\t\t\n\t\t\t\tif (refresh) {\n\t\t\t\t\tthis.page = 1;\n\t\t\t\t\tthis.todoList = [];\n\t\t\t\t\tthis.isRefreshing = true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.loadMoreStatus = 'loading';\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 生成缓存键\n\t\t\t\t\tconst cacheKey = this.generateCacheKey(this.page, this.pageSize);\n\t\t\t\t\t\n\t\t\t\t\t// 检查缓存 - 使用Map的has和get方法\n\t\t\t\t\tif (!refresh && this.cachedQueries.has(cacheKey)) {\n\t\t\t\t\t\tconst cachedData = this.cachedQueries.get(cacheKey);\n\t\t\t\t\t\tthis.applyDataFromCache(cachedData);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 调用优化后的云函数接口\n\t\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'feedback-workflow',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'get_todo_list',\n\t\t\t\t\t\t\tcurrentTab: this.currentTab,\n\t\t\t\t\t\t\tpage: this.page,\n\t\t\t\t\t\t\tpageSize: this.pageSize\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (res.result && res.result.code === 0) {\n\t\t\t\t\t\tconst data = res.result.data;\n\t\t\t\t\t\tthis.updateDataFromResponse(data, cacheKey);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(res.result?.message || '获取待办列表失败');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('❌ 获取待办列表失败:', e);\n\t\t\t\t\tthis.handleGetTodoListError(e);\n\t\t\t\t} finally {\n\t\t\t\t\tthis.finalizeTodoListLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 从缓存应用数据\n\t\t\t */\n\t\t\tapplyDataFromCache(cachedData) {\n\t\t\t\tthis.total = cachedData.total;\n\t\t\t\tthis.reviewCount = cachedData.reviewCount || 0;\n\t\t\t\tthis.taskCount = cachedData.taskCount || 0;\n\t\t\t\t\n\t\t\t\tif (this.page === 1) {\n\t\t\t\t\tthis.todoList = cachedData.list;\n\t\t\t\t} else {\n\t\t\t\t\tthis.todoList = [...this.todoList, ...cachedData.list];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.updateLoadMoreStatus();\n\t\t\t\tthis.finalizeTodoListLoading();\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 从云函数响应更新数据\n\t\t\t */\n\t\t\tupdateDataFromResponse(data, cacheKey) {\n\t\t\t\tthis.total = data.total || 0;\n\t\t\t\tthis.reviewCount = data.reviewCount || 0;\n\t\t\t\tthis.taskCount = data.taskCount || 0;\n\t\t\t\t\n\t\t\t\tconst newList = data.list || [];\n\t\t\t\t\n\t\t\t\t// 缓存结果 - 使用Map的set方法\n\t\t\t\tthis.cachedQueries.set(cacheKey, {\n\t\t\t\t\ttotal: this.total,\n\t\t\t\t\treviewCount: this.reviewCount,\n\t\t\t\t\ttaskCount: this.taskCount,\n\t\t\t\t\tlist: newList,\n\t\t\t\t\ttimestamp: Date.now()\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 更新列表\n\t\t\t\tif (this.page === 1) {\n\t\t\t\t\tthis.todoList = newList;\n\t\t\t\t} else {\n\t\t\t\t\tthis.todoList = [...this.todoList, ...newList];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.updateLoadMoreStatus();\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 更新加载更多状态\n\t\t\t */\n\t\t\tupdateLoadMoreStatus() {\n\t\t\t\tthis.loadMoreStatus = this.todoList.length >= this.total ? 'noMore' : 'more';\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 处理获取待办列表错误\n\t\t\t */\n\t\t\thandleGetTodoListError(error) {\n\t\t\t\tconst errorMessage = error.message || '获取待办列表失败';\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: errorMessage,\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\tthis.loadMoreStatus = 'more';\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 完成待办列表加载的最终处理\n\t\t\t */\n\t\t\tfinalizeTodoListLoading() {\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tthis.hasInitialized = true;\n\t\t\t\t\n\t\t\t\t// 如果是下拉刷新，停止刷新动画\n\t\t\t\tif (this.isRefreshing) {\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\tthis.isRefreshing = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 跨设备更新事件处理\n\t\t\t */\n\t\t\thandleCrossDeviceUpdate(data) {\n\t\t\t\tif (data.silent) {\n\t\t\t\t\tconst shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);\n\t\t\t\t\tif (shouldRefresh) {\n\t\t\t\t\t\tconsole.log('待办页面收到跨设备更新通知，静默刷新数据');\n\t\t\t\t\t\tthis.silentRefresh();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 智能判断是否需要刷新数据 - 优化版本\n\t\t\t */\n\t\t\tshouldRefreshOnCrossDeviceUpdate(data) {\n\t\t\t\t// 如果页面不可见，不需要刷新\n\t\t\t\tif (!this.isPageVisible) {\n\t\t\t\t\tconsole.log('待办页面不可见，跳过跨设备更新');\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果距离上次刷新时间太短（小于10秒），避免频繁刷新\n\t\t\t\tconst timeSinceLastRefresh = Date.now() - (this.lastRequestTime || 0);\n\t\t\t\tif (timeSinceLastRefresh < 10000) {\n\t\t\t\t\tconsole.log('待办页面距离上次刷新时间太短，跳过跨设备更新');\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 删除操作立即刷新\n\t\t\t\tif (data.updateTypes?.includes('feedback_deleted')) {\n\t\t\t\t\tconsole.log('待办页面检测到删除操作，需要立即刷新');\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 相关更新类型判断\n\t\t\t\tif (data.updateTypes) {\n\t\t\t\t\tconst relevantTypes = ['workflow_status_changed', 'feedback_submitted', 'feedback_deleted'];\n\t\t\t\t\tconst hasRelevantUpdate = data.updateTypes.some(type => relevantTypes.includes(type));\n\t\t\t\t\tif (hasRelevantUpdate) {\n\t\t\t\t\t\tconsole.log('待办页面检测到相关更新类型，需要刷新:', data.updateTypes);\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 有具体反馈ID更新\n\t\t\t\tif (data.feedbackIds?.length > 0) {\n\t\t\t\t\tconsole.log('待办页面检测到反馈更新，需要刷新:', data.feedbackIds);\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 保守策略：有任何更新记录都刷新\n\t\t\t\tif (data.updateCount > 0) {\n\t\t\t\t\tconsole.log('待办页面检测到更新记录，采用保守策略刷新:', data.updateCount);\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 静默刷新 - 优化版本\n\t\t\t */\n\t\t\tasync silentRefresh() {\n\t\t\t\ttry {\n\t\t\t\t\t// 清空缓存，强制从服务器获取最新数据\n\t\t\t\t\tthis.clearCache();\n\t\t\t\t\t// 静默刷新当前页面数据\n\t\t\t\t\tawait this.getTodoList(true);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 待办页面静默刷新失败:', error);\n\t\t\t\t\t// 静默失败，不显示错误提示\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 手动刷新方法\n\t\t\t */\n\t\t\trefreshList() {\n\t\t\t\tthis.clearExpiredCache(); // 清除过期缓存\n\t\t\t\tthis.getTodoList(true);\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 事件处理：反馈更新\n\t\t\t */\n\t\t\thandleFeedbackUpdated() {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.silentRefresh();\n\t\t\t\t}, 1000);\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 事件处理：任务完成\n\t\t\t */\n\t\t\thandleTaskCompleted() {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.silentRefresh();\n\t\t\t\t}, 1000);\n\t\t\t},\n\t\t\t\n\t\t\tgoToDetail(item) {\n\t\t\t\t// 根据不同的待办类型跳转到不同的页面\n\t\t\t\tif (item.type === 'task') {\n\t\t\t\t\t// 我的任务 - 跳转到完成任务页面\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/ucenter_pkg/complete-task?id=' + item._id,\n\t\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 审核待办 - 跳转到审核页面\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/feedback_pkg/examine?id=' + item._id,\n\t\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取项目图标\n\t\t\tgetItemIcon(item) {\n\t\t\t\tif (item.type === 'task') {\n\t\t\t\t\treturn 'gear-filled';\n\t\t\t\t} else {\n\t\t\t\t\treturn 'checkbox';\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取项目颜色\n\t\t\tgetItemColor(item) {\n\t\t\t\tif (item.type === 'task') {\n\t\t\t\t\treturn '#FF6B35';\n\t\t\t\t} else {\n\t\t\t\t\treturn '#3688FF';\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取项目类型文本\n\t\t\tgetItemTypeText(item) {\n\t\t\t\tif (item.type === 'task') {\n\t\t\t\t\treturn '我的任务';\n\t\t\t\t} else {\n\t\t\t\t\treturn '审核待办';\n\t\t\t\t}\n\t\t\t},\t\t\t\n\t\t\t\n\t\t\t// 获取项目用户文本\n\t\t\tgetItemUserText(item) {\n\t\t\t\tif (item.type === 'task') {\n\t\t\t\t\treturn item.assignedByName || '未知分配人';\n\t\t\t\t} else {\n\t\t\t\t\treturn item.name || '未知用户';\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tformatDate(dateString) {\n\t\t\t\tif (!dateString) return '未知时间';\n\t\t\t\t\n\t\t\t\tconst date = new Date(dateString);\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\t\n\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t},\n\t\t\t\n\t\t\ttoggleExpand(index) {\n\t\t\t\t// 优化展开/收起状态管理\n\t\t\t\tif (this.expandedItems[index]) {\n\t\t\t\t\t// 已展开，收起\n\t\t\t\t\tthis.$set(this.expandedItems, index, false);\n\t\t\t\t} else {\n\t\t\t\t\t// 展开当前项，可选择是否收起其他项\n\t\t\t\t\t// 对于移动端，建议同时只展开一项，提升性能\n\t\t\t\t\tconst wasExpanded = { ...this.expandedItems };\n\t\t\t\t\tObject.keys(wasExpanded).forEach(key => {\n\t\t\t\t\t\tthis.$set(this.expandedItems, key, false);\n\t\t\t\t\t});\n\t\t\t\t\tthis.$set(this.expandedItems, index, true);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tisTextOverflow(text) {\n\t\t\t\t// 优化文本溢出判断\n\t\t\t\tif (!text) return false;\n\t\t\t\t\n\t\t\t\t// 根据设备宽度动态调整判断标准\n\t\t\t\tlet maxLength = 50; // 默认值\n\t\t\t\t\n\t\t\t\t// 获取屏幕宽度，根据不同宽度调整最大长度\n\t\t\t\t// #ifdef H5 || APP-PLUS\n\t\t\t\ttry {\n\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\tif (systemInfo.windowWidth < 375) {\n\t\t\t\t\t\tmaxLength = 40; // 小屏幕设备\n\t\t\t\t\t} else if (systemInfo.windowWidth > 768) {\n\t\t\t\t\t\tmaxLength = 80; // 大屏幕设备\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\t// 获取系统信息失败，使用默认值\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\treturn text.length > maxLength;\n\t\t\t}\n\t\t},\n\t\t\n\t\tonPullDownRefresh() {\n\t\t\tthis.isRefreshing = true;\n\t\t\tthis.refreshList();\n\t\t},\n\t\t\n\t\tonReachBottom() {\n\t\t\tif (this.loadMoreStatus === 'more' && !this.isLoading) {\n\t\t\t\tthis.page++;\n\t\t\t\tthis.getTodoList();\n\t\t\t}\n\t\t},\n\t\t\n\t\tonShow() {\n\t\t\tthis.isPageVisible = true;\n\t\t\t\n\t\t\t// 页面显示时检查是否需要刷新数据\n\t\t\t// 增加更严格的刷新条件，避免从审核页面返回时立即刷新\n\t\t\tconst now = Date.now();\n\t\t\tconst timeSinceLastRequest = now - this.lastRequestTime;\n\t\t\t\n\t\t\t// 只有在以下条件同时满足时才自动刷新：\n\t\t\t// 1. 距离上次加载时间超过30分钟（避免频繁刷新）\n\t\t\t// 2. 已有数据\n\t\t\t// 3. 不在加载中\n\t\t\t// 4. 已经初始化过\n\t\t\tconst shouldRefresh = timeSinceLastRequest > 30 * 60 * 1000 && \n\t\t\t\t\t\t\t\t  this.todoList.length > 0 && \n\t\t\t\t\t\t\t\t  !this.isLoading &&\n\t\t\t\t\t\t\t\t  this.hasInitialized;\n\t\t\t\n\t\t\tif (shouldRefresh) {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t// 二次确认，避免用户快速切换页面时的误触发\n\t\t\t\t\tif (!this.isLoading && Date.now() - this.lastRequestTime > 30 * 60 * 1000) {\n\t\t\t\t\t\tconsole.log('Todo页面自动刷新');\n\t\t\t\t\t\tthis.refreshList();\n\t\t\t\t\t}\n\t\t\t\t}, 2000); // 延迟2秒执行\n\t\t\t}\n\t\t},\n\t\t\n\t\tonHide() {\n\t\t\tthis.isPageVisible = false;\n\t\t\t// 页面隐藏时清理过期缓存\n\t\t\tthis.clearExpiredCache();\n\t\t},\n\t\t\n\t\tbeforeDestroy() {\n\t\t\t// 移除事件监听\n\t\t\tuni.$off('cross-device-update-detected', this.handleCrossDeviceUpdate);\n\t\t\tuni.$off('feedback-updated', this.handleFeedbackUpdated);\n\t\t\tuni.$off('task-completed', this.handleTaskCompleted);\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t/* #ifndef APP-NVUE */\n\tview {\n\t\tdisplay: flex;\n\t\tbox-sizing: border-box;\n\t\tflex-direction: column;\n\t}\n\n\tpage {\n\t\tbackground-color: #f0f4f8;\n\t}\n\t/* #endif*/\n\t\n\t.container {\n\t\tflex: 1;\n\t\tflex-direction: column;\n\t\tbackground-color: #f0f4f8;\n\t\tmin-height: 100vh;\n\t\tpadding: 30rpx;\n\t}\n\t\n\t.page-header {\n\t\tpadding: 40rpx;\n\t\tbackground: linear-gradient(145deg, #3688FF, #5A9FFF);\n\t\tmargin-bottom: 40rpx;\n\t\tborder-radius: 24rpx;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(54, 136, 255, 0.15);\n\t}\n\t\n\t.page-title {\n\t\tfont-size: 40rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #FFFFFF;\n\t\tletter-spacing: 1rpx;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.category-tabs {\n\t\tflex-direction: row;\n\t\tjustify-content: space-around;\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 24rpx;\n\t\tpadding: 20rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.tab-item {\n\t\tflex: 1;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 20rpx 30rpx;\n\t\tborder-radius: 16rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.tab-item.active {\n\t\tbackground: linear-gradient(145deg, #3688FF, #5A9FFF);\n\t\tbox-shadow: 0 4rpx 15rpx rgba(54, 136, 255, 0.3);\n\t}\n\t\n\t.tab-item text {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #666666;\n\t\ttransition: color 0.3s ease;\n\t}\n\t\n\t.tab-item.active text {\n\t\tcolor: #FFFFFF;\n\t\tfont-weight: 600;\n\t}\n\t\n\t/* 加载状态 */\n\t.loading-state {\n\t\tpadding: 100rpx 0;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\t\n\t.loading-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t}\n\t\n\t.data-loading {\n\t\tpadding: 100rpx 0;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t\n\t/* 空状态 */\n\t.empty-state {\n\t\tpadding: 80rpx 40rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmin-height: 400rpx;\n\t\tflex: 1;\n\t}\n\t\n\t.empty-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmax-width: 360rpx;\n\t\twidth: 100%;\n\t}\n\t\n\t.empty-icon {\n\t\twidth: 100rpx;\n\t\theight: 100rpx;\n\t\tborder-radius: 50rpx;\n\t\tbackground: #f5f5f5;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-bottom: 24rpx;\n\t}\n\t\n\t.empty-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tmargin-bottom: 12rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.empty-desc {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\ttext-align: center;\n\t\tline-height: 1.5;\n\t}\n\t\n\t.todo-list {\n\t\tflex-direction: column;\n\t}\n\t\n\t.list-header {\n\t\tpadding: 20rpx 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.list-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333333;\n\t\tletter-spacing: 0.5rpx;\n\t}\n\t\n\t.list-wrapper {\n\t\tborder-radius: 24rpx;\n\t\toverflow: hidden;\n\t\tbackground-color: #FFFFFF;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.item-icon {\n\t\tmargin-right: 20rpx;\n\t}\n\t\n\t.item-body {\n\t\tflex: 1;\n\t\tflex-direction: column;\n\t\tpadding-right: 20rpx;\n\t}\n\t\n\t.item-title-row {\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.item-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333333;\n\t\tletter-spacing: 0.5rpx;\n\t\tflex: 1;\n\t}\n\t\n\t.item-type-badge {\n\t\tfont-size: 22rpx;\n\t\tfont-weight: 500;\n\t\tpadding: 6rpx 12rpx;\n\t\tborder-radius: 12rpx;\n\t\tmargin-left: 20rpx;\n\t}\n\t\n\t.review-badge {\n\t\tbackground-color: rgba(54, 136, 255, 0.1);\n\t\tcolor: #3688FF;\n\t}\n\t\n\t.task-badge {\n\t\tbackground-color: rgba(255, 107, 53, 0.1);\n\t\tcolor: #FF6B35;\n\t}\n\t\n\t.item-description-container {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t}\n\t\n\t.item-description {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tline-height: 1.5;\n\t\t/* 显示两行，超出部分显示省略号 */\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tdisplay: -webkit-box;\n\t\t-webkit-line-clamp: 2;\n\t\t-webkit-box-orient: vertical;\n\t}\n\t\n\t.item-description.expanded {\n\t\t-webkit-line-clamp: unset;\n\t}\n\t\n\t.expand-btn {\n\t\tfont-size: 26rpx;\n\t\tcolor: #3688FF;\n\t\tmargin-top: 4rpx;\n        display: flex;\n        justify-content: flex-end;\n\t}\n\t\n\t.item-footer {\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin-top: 10rpx;\n\t}\n\t\n\t.item-info {\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\t\n\t.item-time, .item-user {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t}\n\t\n\t.item-dot {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\tmargin: 0 10rpx;\n\t}\n\t\n\t/* 修改uni-list样式 */\n\t:deep(.uni-list--border-top),\n\t:deep(.uni-list--border-bottom) {\n\t\theight: 0;\n\t\tbackground-color: transparent;\n\t}\n\t\n\t:deep(.uni-list-item__container) {\n\t\tpadding: 30rpx 40rpx;\n\t}\n\t\n\t:deep(.uni-list-item__content-title) {\n\t\tdisplay: none; /* 隐藏默认标题，使用自定义标题 */\n\t}\n\t\n\t:deep(.uni-list-item__content-note) {\n\t\tdisplay: none; /* 隐藏默认描述，使用自定义描述 */\n\t}\n\t\n\t:deep(.uni-list-item--hover) {\n\t\tbackground-color: rgba(54, 136, 255, 0.05);\n\t}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./todo.vue?vue&type=style&index=0&id=6972cfb7&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./todo.vue?vue&type=style&index=0&id=6972cfb7&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558436904\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
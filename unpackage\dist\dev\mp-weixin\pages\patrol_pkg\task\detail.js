require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol_pkg/task/detail"],{

/***/ 427:
/*!*********************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fpatrol_pkg%2Ftask%2Fdetail"} ***!
  \*********************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _detail = _interopRequireDefault(__webpack_require__(/*! ./pages/patrol_pkg/task/detail.vue */ 428));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_detail.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 428:
/*!************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/detail.vue ***!
  \************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _detail_vue_vue_type_template_id_1189b4c4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./detail.vue?vue&type=template&id=1189b4c4& */ 429);
/* harmony import */ var _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./detail.vue?vue&type=script&lang=js& */ 431);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./detail.vue?vue&type=style&index=0&lang=scss& */ 433);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _detail_vue_vue_type_template_id_1189b4c4___WEBPACK_IMPORTED_MODULE_0__["render"],
  _detail_vue_vue_type_template_id_1189b4c4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _detail_vue_vue_type_template_id_1189b4c4___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/patrol_pkg/task/detail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 429:
/*!*******************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/detail.vue?vue&type=template&id=1189b4c4& ***!
  \*******************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_1189b4c4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=1189b4c4& */ 430);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_1189b4c4___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_1189b4c4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_1189b4c4___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_1189b4c4___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 430:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/detail.vue?vue&type=template&id=1189b4c4& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 483))
    },
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.taskDetail ? _vm.getStatusText(_vm.taskDetail.status || 0) : null
  var m1 = _vm.taskDetail
    ? _vm.formatDateTime(_vm.taskDetail.create_date)
    : null
  var g0 = _vm.taskDetail
    ? _vm.taskDetail.rounds_detail && _vm.taskDetail.rounds_detail.length > 0
    : null
  var l1 =
    _vm.taskDetail && g0
      ? _vm.__map(_vm.taskDetail.rounds_detail, function (round, index) {
          var $orig = _vm.__get_orig(round)
          var m2 = _vm.formatTimeStr(round.time)
          var m3 = round.status === 1 ? _vm.calculateRemainingTime(round) : null
          var m4 = round.point_stats ? _vm.getRoundCompletion(round) : null
          var m5 = _vm.getRoundStatusText(round.status)
          var g1 =
            round.expanded && !round.loading
              ? round.points && round.points.length > 0
              : null
          var g2 =
            round.expanded && !round.loading && g1 ? round.points.length : null
          var l0 =
            round.expanded && !round.loading && g1
              ? _vm.__map(round.points, function (point, pIndex) {
                  var $orig = _vm.__get_orig(point)
                  var m6 =
                    point.status === 1 || point.status === 2
                      ? _vm.formatDateTime(point.checkin_time)
                      : null
                  return {
                    $orig: $orig,
                    m6: m6,
                  }
                })
              : null
          return {
            $orig: $orig,
            m2: m2,
            m3: m3,
            m4: m4,
            m5: m5,
            g1: g1,
            g2: g2,
            l0: l0,
          }
        })
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        g0: g0,
        l1: l1,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 431:
/*!*************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/detail.vue?vue&type=script&lang=js& ***!
  \*************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js& */ 432);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 432:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/detail.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! @/utils/patrol-api.js */ 71));
var _date = __webpack_require__(/*! @/utils/date.js */ 72);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var PEmptyState = function PEmptyState() {
  __webpack_require__.e(/*! require.ensure | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then((function () {
    return resolve(__webpack_require__(/*! @/components/p-empty-state/p-empty-state.vue */ 483));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
// 添加缓存工具方法
var CACHE_DURATION = 30 * 60 * 1000; // 30分钟缓存

var CacheUtils = {
  setCache: function setCache(key, data) {
    try {
      var cacheData = {
        data: data,
        timestamp: Date.now()
      };
      uni.setStorageSync(key, JSON.stringify(cacheData));
    } catch (e) {
      console.warn('设置缓存失败:', e);
    }
  },
  getCache: function getCache(key) {
    try {
      var cacheStr = uni.getStorageSync(key);
      if (!cacheStr) return null;
      var cacheData = JSON.parse(cacheStr);
      var now = Date.now();

      // 检查缓存是否过期
      if (now - cacheData.timestamp > CACHE_DURATION) {
        uni.removeStorageSync(key);
        return null;
      }
      return cacheData.data;
    } catch (e) {
      console.warn('获取缓存失败:', e);
      return null;
    }
  },
  clearCache: function clearCache(key) {
    try {
      uni.removeStorageSync(key);
    } catch (e) {
      console.warn('清除缓存失败:', e);
    }
  }
};
var _default = {
  components: {
    PEmptyState: PEmptyState
  },
  data: function data() {
    return {
      taskId: '',
      taskDetail: null,
      userInfo: null,
      loading: true,
      refreshing: false,
      shiftInfo: {},
      // 班次信息缓存
      showRoundDetails: false,
      round: {},
      // 轮次信息缓存
      retryCount: 0,
      // 重试次数
      maxRetries: 3,
      // 最大重试次数
      // 轮次状态样式映射
      roundStatusClasses: {
        0: 'status-waiting',
        1: 'status-active',
        2: 'status-completed',
        3: 'status-expired'
      }
    };
  },
  computed: {
    // 班次时间文本计算属性
    shiftTimeText: function shiftTimeText() {
      if (!this.taskDetail) return '';
      return this.getShiftTime(this.taskDetail);
    },
    // 任务标题计算属性
    taskTitle: function taskTitle() {
      if (!this.taskDetail) return '未命名任务';
      return this.taskDetail.name || this.taskDetail.area || this.taskDetail.route_name || '未命名任务';
    },
    // 执行人员名称计算属性
    executorName: function executorName() {
      if (!this.taskDetail) return '加载中...';
      return this.userInfo ? this.userInfo.nickname || this.userInfo.name || this.taskDetail.user_name : this.taskDetail.user_name || '加载中...';
    },
    // 班次名称计算属性
    shiftDisplayName: function shiftDisplayName() {
      if (!this.taskDetail) return '未指定班次';
      return this.taskDetail.shift_name || this.taskDetail.shift && this.taskDetail.shift.name || '未指定班次';
    },
    // 执行日期计算属性
    executionDate: function executionDate() {
      if (!this.taskDetail) return '';
      return this.taskDetail.patrol_date || this.formatDate(this.taskDetail.create_date);
    },
    // 轮次数量计算属性
    roundsCount: function roundsCount() {
      if (!this.taskDetail || !this.taskDetail.rounds_detail) return 0;
      return this.taskDetail.rounds_detail.length;
    },
    // 缺卡点位数计算属性
    missedPointsCount: function missedPointsCount() {
      if (!this.taskDetail || !this.taskDetail.rounds_detail) return 0;
      var missedCount = 0;
      this.taskDetail.rounds_detail.forEach(function (round) {
        if (round.points) {
          missedCount += round.points.filter(function (p) {
            return p.status === 4;
          }).length;
        }
      });
      return missedCount;
    }
  },
  onLoad: function onLoad(options) {
    var startTime = Date.now();
    this.taskId = options.id;
    this.loadTaskDetail().then(function () {
      var loadTime = Date.now() - startTime;
    });
  },
  onUnload: function onUnload() {
    // 清理大对象，释放内存
    this.taskDetail = null;
    this.userInfo = null;
    this.shiftInfo = {};
  },
  methods: {
    // 加载任务详情
    loadTaskDetail: function loadTaskDetail() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var currentUserId, res, promises;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (_this.taskId) {
                  _context.next = 3;
                  break;
                }
                uni.showToast({
                  title: '任务ID无效',
                  icon: 'none'
                });
                return _context.abrupt("return");
              case 3:
                _this.loading = true;
                _this.taskDetail = null;
                _context.prev = 5;
                currentUserId = _this.getCurrentUserId(); // 🔥 优化：分层加载策略
                // 第一阶段：加载轻量级基本信息（快速显示）
                _context.next = 9;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'getTaskDetail',
                  data: {
                    task_id: _this.taskId,
                    userId: currentUserId,
                    level: 'checkin',
                    // 🔥 轻量级：不包含points数组，适用于详情查看
                    // 🔥 优化：只获取详情页必需的基础字段
                    fields: ['_id', 'name', 'status', 'user_id', 'user_name', 'shift_id', 'shift_name', 'area', 'route_name', 'patrol_date', 'create_date', 'update_date', 'overall_stats', 'shift', 'route',
                    // 🔥 只获取轮次摘要，不包含points数组
                    'rounds_detail.round', 'rounds_detail.name', 'rounds_detail.status', 'rounds_detail.start_time', 'rounds_detail.end_time', 'rounds_detail.duration', 'rounds_detail.day_offset', 'rounds_detail.time', 'rounds_detail.stats', 'rounds_detail.point_stats']
                  }
                });
              case 9:
                res = _context.sent;
                if (!(res.code === 0 && res.data)) {
                  _context.next = 35;
                  break;
                }
                // 重置重试次数
                _this.retryCount = 0;

                // 打印原始任务数据，用于调试
                // console.log('原始任务数据:', JSON.stringify(res.data));

                // 深拷贝数据，避免引用问题
                _this.taskDetail = JSON.parse(JSON.stringify(res.data));

                // 处理可能的数据结构不一致问题
                if (!_this.taskDetail.shift_id && _this.taskDetail.shift && _this.taskDetail.shift._id) {
                  _this.taskDetail.shift_id = _this.taskDetail.shift._id;
                }
                if (!_this.taskDetail.shift_name && _this.taskDetail.shift && _this.taskDetail.shift.name) {
                  _this.taskDetail.shift_name = _this.taskDetail.shift.name;
                }
                if (!_this.taskDetail.route_name && _this.taskDetail.route && _this.taskDetail.route.name) {
                  _this.taskDetail.route_name = _this.taskDetail.route.name;
                }
                if (!_this.taskDetail.area && _this.taskDetail.route && _this.taskDetail.route.area) {
                  _this.taskDetail.area = _this.taskDetail.route.area;
                }

                // 确保基本属性都有值
                _this.taskDetail.status = _this.taskDetail.status !== undefined ? Number(_this.taskDetail.status) : 0;
                _this.taskDetail.name = _this.taskDetail.name || '';

                // 确保轮次数据存在
                if (!_this.taskDetail.rounds_detail || !Array.isArray(_this.taskDetail.rounds_detail)) {
                  console.warn('任务数据中没有轮次信息或格式不正确');
                  _this.taskDetail.rounds_detail = [];
                }

                // 处理轮次数据
                _this.processRoundsData();

                // 打印处理后的任务数据，用于调试 - 注意：保留完整数据结构
                // console.log('处理后的任务数据:', {
                // 	name: this.taskDetail.name,
                // 	status: this.taskDetail.status,
                // 	shift_name: this.taskDetail.shift_name,
                // 	// 只显示轮次数量，保留原数组
                // 	rounds_count: this.taskDetail.rounds_detail ? this.taskDetail.rounds_detail.length : 0
                // });

                // 并行加载用户信息和班次信息
                promises = [];
                if (_this.taskDetail.user_id) {
                  promises.push(_this.loadUserInfo(_this.taskDetail.user_id));
                }
                if (_this.taskDetail.shift_id && !_this.shiftInfo[_this.taskDetail.shift_id]) {
                  promises.push(_this.loadShiftInfo(_this.taskDetail.shift_id));
                }

                // 等待所有并行请求完成，但不阻塞主流程
                if (!(promises.length > 0)) {
                  _context.next = 33;
                  break;
                }
                _context.prev = 25;
                _context.next = 28;
                return Promise.all(promises);
              case 28:
                _context.next = 33;
                break;
              case 30:
                _context.prev = 30;
                _context.t0 = _context["catch"](25);
                console.warn('加载附加信息时出错:', _context.t0);
                // 不影响主流程，继续显示页面
              case 33:
                _context.next = 41;
                break;
              case 35:
                if (!(_this.retryCount < _this.maxRetries && (res.code === -1 || !res.code))) {
                  _context.next = 39;
                  break;
                }
                _this.retryCount++;
                setTimeout(function () {
                  _this.loadTaskDetail();
                }, 1000 * _this.retryCount); // 递增延迟重试
                return _context.abrupt("return");
              case 39:
                uni.showToast({
                  title: res.message || '任务不存在',
                  icon: 'none'
                });
                setTimeout(function () {
                  return uni.navigateBack();
                }, 1500);
              case 41:
                _context.next = 52;
                break;
              case 43:
                _context.prev = 43;
                _context.t1 = _context["catch"](5);
                console.error('加载任务详情出错:', _context.t1);

                // 检查是否需要重试
                if (!(_this.retryCount < _this.maxRetries)) {
                  _context.next = 50;
                  break;
                }
                _this.retryCount++;
                setTimeout(function () {
                  _this.loadTaskDetail();
                }, 1000 * _this.retryCount); // 递增延迟重试
                return _context.abrupt("return");
              case 50:
                uni.showToast({
                  title: '加载任务详情出错',
                  icon: 'none'
                });
                setTimeout(function () {
                  return uni.navigateBack();
                }, 1500);
              case 52:
                _context.prev = 52;
                _this.loading = false;
                _this.refreshing = false;
                return _context.finish(52);
              case 56:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[5, 43, 52, 56], [25, 30]]);
      }))();
    },
    // 加载班次信息
    loadShiftInfo: function loadShiftInfo(shiftId) {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var cacheKey, cachedShift, startTime, endTime, res, shiftData, _startTime, _endTime;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                if (shiftId) {
                  _context2.next = 2;
                  break;
                }
                return _context2.abrupt("return");
              case 2:
                // 先检查缓存
                cacheKey = "shift_info_".concat(shiftId);
                cachedShift = CacheUtils.getCache(cacheKey);
                if (!cachedShift) {
                  _context2.next = 9;
                  break;
                }
                _this2.$set(_this2.shiftInfo, shiftId, cachedShift);

                // 更新任务班次信息
                if (_this2.taskDetail && _this2.taskDetail.shift_id === shiftId) {
                  if (!_this2.taskDetail.shift) {
                    _this2.taskDetail.shift = {};
                  }
                  startTime = cachedShift.start_time || cachedShift.startTime || '';
                  endTime = cachedShift.end_time || cachedShift.endTime || '';
                  _this2.taskDetail.shift_time = startTime && endTime ? "(".concat(startTime, " - ").concat(endTime, ")") : '';
                }
                _this2.$forceUpdate();
                return _context2.abrupt("return");
              case 9:
                _context2.prev = 9;
                _context2.next = 12;
                return _patrolApi.default.call({
                  name: 'patrol-shift',
                  action: 'getShiftDetail',
                  data: {
                    params: {
                      shift_id: shiftId
                    }
                  }
                });
              case 12:
                res = _context2.sent;
                if (res.code === 0 && res.data) {
                  // 处理可能是数组的情况
                  shiftData = Array.isArray(res.data) ? res.data[0] : res.data; // 缓存班次信息
                  CacheUtils.setCache(cacheKey, shiftData);

                  // 使用Vue的响应式API更新数据
                  _this2.$set(_this2.shiftInfo, shiftId, shiftData);

                  // 如果当前任务使用此班次，直接更新任务班次信息
                  if (_this2.taskDetail && _this2.taskDetail.shift_id === shiftId) {
                    if (!_this2.taskDetail.shift) {
                      _this2.taskDetail.shift = {};
                    }

                    // 兼容不同字段名
                    _startTime = shiftData.start_time || shiftData.startTime || '';
                    _endTime = shiftData.end_time || shiftData.endTime || ''; // 直接设置shift_time字段简化后续处理
                    _this2.taskDetail.shift_time = _startTime && _endTime ? "(".concat(_startTime, " - ").concat(_endTime, ")") : '';
                  }

                  // 强制更新视图
                  _this2.$forceUpdate();
                }
                _context2.next = 19;
                break;
              case 16:
                _context2.prev = 16;
                _context2.t0 = _context2["catch"](9);
                console.error('加载班次信息出错:', _context2.t0);
              case 19:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[9, 16]]);
      }))();
    },
    // 处理轮次数据
    processRoundsData: function processRoundsData() {
      if (!this.taskDetail || !this.taskDetail.rounds_detail) return;

      // 输出原始轮次数据
      // console.log('处理轮次数据前:', JSON.stringify(this.taskDetail.rounds_detail));

      // 获取任务基准日期
      var taskDate = this.taskDetail.patrol_date ? new Date(this.taskDetail.patrol_date.replace(/-/g, '/')) : new Date(this.taskDetail.create_date);
      var taskDateStr = (0, _date.formatDate)(taskDate, 'YYYY-MM-DD');
      var now = new Date(); // 当前时间，用于判断轮次状态

      // 确保rounds_detail是数组
      if (!Array.isArray(this.taskDetail.rounds_detail)) {
        console.warn('轮次数据不是数组，重置为空数组');
        this.taskDetail.rounds_detail = [];
        return;
      }

      // 处理每个轮次的时间信息和状态
      var hasActiveRound = false;
      var hasUpcomingRound = false;
      var allRoundsCompleted = true;
      this.taskDetail.rounds_detail = this.taskDetail.rounds_detail.map(function (round) {
        // 创建轮次对象的副本，避免修改原始对象
        var processedRound = _objectSpread({}, round);

        // 确保轮次中有day_offset和duration字段
        processedRound.day_offset = processedRound.day_offset !== undefined ? Number(processedRound.day_offset) : 0;
        processedRound.duration = processedRound.duration !== undefined ? Number(processedRound.duration) : 60; // 默认60分钟
        processedRound.round = processedRound.round !== undefined ? Number(processedRound.round) : 1; // 默认轮次号为1

        // 确保points字段存在并且是数组
        if (!processedRound.points || !Array.isArray(processedRound.points)) {
          processedRound.points = [];
        }

        // 确保point_stats字段存在
        if (!processedRound.point_stats) {
          processedRound.point_stats = {
            total: processedRound.points.length,
            checked: 0
          };
        } else if (processedRound.stats && !processedRound.point_stats) {
          // 兼容stats字段
          processedRound.point_stats = {
            total: processedRound.stats.total_points || processedRound.points.length,
            checked: processedRound.stats.completed_points || 0
          };
        }

        // 处理时间字段
        try {
          // 使用calculateRoundTime计算轮次开始时间，考虑day_offset
          if (processedRound.time) {
            var roundStartTime = (0, _date.calculateRoundTime)(taskDateStr, processedRound.time, processedRound.day_offset);
            // 使用calculateEndTime计算结束时间，基于开始时间和持续时间
            var roundEndTime = (0, _date.calculateEndTime)(roundStartTime, processedRound.duration);

            // 保存实际开始时间和结束时间
            processedRound.actualStartTime = roundStartTime;
            processedRound.actualEndTime = roundEndTime;

            // 为方便UI显示，保存ISO格式的开始和结束时间
            processedRound.start_time = roundStartTime.toISOString();
            processedRound.end_time = roundEndTime.toISOString();

            // 计算轮次状态
            if (now < roundStartTime) {
              processedRound.status = 0; // 未开始
              hasUpcomingRound = true;
              allRoundsCompleted = false;
            } else if (now > roundEndTime) {
              // 已超时，但要检查点位是否全部完成
              if (processedRound.point_stats && processedRound.point_stats.total > 0 && processedRound.point_stats.checked >= processedRound.point_stats.total) {
                processedRound.status = 2; // 虽然超时但已完成
              } else {
                processedRound.status = 3; // 超时且未完成
                allRoundsCompleted = false;
              }
            } else {
              // 在时间范围内
              if (processedRound.point_stats && processedRound.point_stats.total > 0 && processedRound.point_stats.checked >= processedRound.point_stats.total) {
                processedRound.status = 2; // 已完成
              } else {
                processedRound.status = 1; // 进行中
                hasActiveRound = true;
                allRoundsCompleted = false;
              }
            }
          } else if (processedRound.start_time) {
            // 直接从ISO时间格式解析日期时间
            var _roundStartTime = new Date(processedRound.start_time);
            var _roundEndTime = processedRound.end_time ? new Date(processedRound.end_time) : (0, _date.calculateEndTime)(_roundStartTime, processedRound.duration);

            // 保存实际开始时间和结束时间
            processedRound.actualStartTime = _roundStartTime;
            processedRound.actualEndTime = _roundEndTime;

            // 其余状态计算与上方相同
            if (now < _roundStartTime) {
              processedRound.status = 0; // 未开始
              hasUpcomingRound = true;
              allRoundsCompleted = false;
            } else if (now > _roundEndTime) {
              if (processedRound.point_stats && processedRound.point_stats.total > 0 && processedRound.point_stats.checked >= processedRound.point_stats.total) {
                processedRound.status = 2; // 虽然超时但已完成
              } else {
                processedRound.status = 3; // 超时且未完成
              }
            } else {
              if (processedRound.point_stats && processedRound.point_stats.total > 0 && processedRound.point_stats.checked >= processedRound.point_stats.total) {
                processedRound.status = 2; // 已完成
              } else {
                processedRound.status = 1; // 进行中
                hasActiveRound = true;
                allRoundsCompleted = false;
              }
            }
          } else {
            // 无时间信息时，根据点位完成情况判断
            if (processedRound.point_stats && processedRound.point_stats.total > 0 && processedRound.point_stats.checked >= processedRound.point_stats.total) {
              processedRound.status = 2; // 已完成
            } else if (processedRound.point_stats && processedRound.point_stats.checked > 0) {
              processedRound.status = 1; // 进行中
            } else {
              processedRound.status = 0; // 默认未开始
            }
          }
        } catch (e) {
          console.error('处理轮次时间出错:', e);

          // 出错时使用备用方案：根据点位完成情况判断
          if (processedRound.point_stats && processedRound.point_stats.total > 0 && processedRound.point_stats.checked >= processedRound.point_stats.total) {
            processedRound.status = 2; // 已完成
          } else if (processedRound.point_stats && processedRound.point_stats.checked > 0) {
            processedRound.status = 1; // 进行中
          } else {
            processedRound.status = 0; // 默认未开始
          }
        }

        // 确保状态是数字
        processedRound.status = parseInt(processedRound.status || 0);

        // 添加expanded属性用于控制展开/折叠
        processedRound.expanded = false;
        return processedRound;
      });

      // 更新任务整体状态
      if (allRoundsCompleted) {
        this.taskDetail.status = 2; // 已完成
      } else if (hasActiveRound) {
        this.taskDetail.status = 1; // 进行中
      } else if (hasUpcomingRound) {
        this.taskDetail.status = 0; // 未开始
      } else {
        this.taskDetail.status = 3; // 已超时
      }

      // 轮次排序
      var activeRounds = this.taskDetail.rounds_detail.filter(function (round) {
        return round.status === 0 || round.status === 1;
      });
      var completedRounds = this.taskDetail.rounds_detail.filter(function (round) {
        return round.status === 2 || round.status === 3;
      });
      activeRounds.sort(function (a, b) {
        return a.round - b.round;
      });
      completedRounds.sort(function (a, b) {
        return b.round - a.round;
      });
      this.taskDetail.rounds_detail = [].concat((0, _toConsumableArray2.default)(activeRounds), (0, _toConsumableArray2.default)(completedRounds));

      // 更新总体统计
      this.ensureOverallStats();
    },
    // 解析时间字符串
    parseTimeString: function parseTimeString(timeStr) {
      try {
        // 检查是否是完整的ISO格式日期时间
        if (timeStr.includes('T') || timeStr.includes('-')) {
          var date = new Date(timeStr);
          return {
            hours: date.getHours(),
            minutes: date.getMinutes()
          };
        }

        // 否则假设是HH:MM格式
        var parts = timeStr.split(':');
        return {
          hours: parseInt(parts[0], 10),
          minutes: parseInt(parts[1], 10)
        };
      } catch (e) {
        // 发生错误时返回默认值
        return {
          hours: 0,
          minutes: 0
        };
      }
    },
    // 获取当前用户ID
    getCurrentUserId: function getCurrentUserId() {
      try {
        var userInfo = uni.getStorageSync('uni-id-pages-userInfo');
        return userInfo ? userInfo._id || '' : '';
      } catch (e) {
        return '';
      }
    },
    // 加载用户信息
    loadUserInfo: function loadUserInfo(userId) {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var cacheKey, cachedUser, currentUserId, res, user;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                if (userId) {
                  _context3.next = 2;
                  break;
                }
                return _context3.abrupt("return");
              case 2:
                // 先检查缓存
                cacheKey = "user_info_".concat(userId);
                cachedUser = CacheUtils.getCache(cacheKey);
                if (!cachedUser) {
                  _context3.next = 7;
                  break;
                }
                _this3.userInfo = cachedUser;
                return _context3.abrupt("return");
              case 7:
                _context3.prev = 7;
                currentUserId = _this3.getCurrentUserId();
                if (currentUserId) {
                  _context3.next = 11;
                  break;
                }
                return _context3.abrupt("return");
              case 11:
                _context3.next = 13;
                return _patrolApi.default.call({
                  name: 'patrol-user',
                  action: 'getUsers',
                  data: {
                    userid: currentUserId,
                    pageSize: 100
                  }
                });
              case 13:
                res = _context3.sent;
                if (res.code === 0 && res.data && res.data.list) {
                  user = res.data.list.find(function (u) {
                    return u._id === userId;
                  });
                  if (user) {
                    _this3.userInfo = user;
                    // 缓存用户信息
                    CacheUtils.setCache(cacheKey, user);
                  }
                }
                _context3.next = 21;
                break;
              case 17:
                _context3.prev = 17;
                _context3.t0 = _context3["catch"](7);
                console.error('加载用户信息失败:', _context3.t0);
                uni.showToast({
                  title: '加载用户信息失败',
                  icon: 'none'
                });
              case 21:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[7, 17]]);
      }))();
    },
    // 刷新
    refresh: function refresh() {
      this.refreshing = true;
      this.retryCount = 0; // 重置重试次数
      this.loadTaskDetail();
    },
    // 获取状态文本 - 优化为静态映射
    getStatusText: function getStatusText(status) {
      var statusMap = {
        0: '未开始',
        1: '进行中',
        2: '已完成',
        3: '已超时',
        4: '已取消'
      };
      return statusMap[status] || '未知状态';
    },
    // 获取轮次状态文本 - 优化为静态映射
    getRoundStatusText: function getRoundStatusText(status) {
      var statusMap = {
        0: '未开始',
        1: '进行中',
        2: '已完成',
        3: '已超时'
      };
      return statusMap[status] || '未开始';
    },
    // 获取轮次状态样式类 - 不使用函数而是使用对象方式
    getStatusClassForRound: function getStatusClassForRound(status) {
      return this.roundStatusClasses[status] || 'status-waiting';
    },
    // 计算完成率
    calculateCompletionRate: function calculateCompletionRate(round) {
      if (!round.point_stats || !round.point_stats.total || round.point_stats.total === 0) {
        return 0;
      }
      return Math.round(round.point_stats.checked / round.point_stats.total * 100);
    },
    // 格式化日期
    formatDate: function formatDate(dateStr) {
      if (!dateStr) return '';
      try {
        var date = new Date(dateStr);
        return (0, _date.formatDate)(date, 'YYYY-MM-DD');
      } catch (e) {
        return dateStr.split(' ')[0] || '';
      }
    },
    // 格式化日期时间
    formatDateTime: function formatDateTime(dateStr) {
      if (!dateStr) return '';
      try {
        var date = new Date(dateStr);
        return (0, _date.formatDate)(date, 'YYYY-MM-DD HH:mm');
      } catch (e) {
        return dateStr;
      }
    },
    // 格式化时间
    formatTime: function formatTime(dateStr) {
      if (!dateStr) return '';
      var date;
      try {
        date = typeof dateStr === 'string' ? new Date(dateStr.replace(/-/g, '/')) : dateStr;
        if (isNaN(date.getTime())) {
          console.warn('无效的日期格式:', dateStr);
          return '--:--';
        }
        var hours = date.getHours().toString().padStart(2, '0');
        var minutes = date.getMinutes().toString().padStart(2, '0');
        return "".concat(hours, ":").concat(minutes);
      } catch (e) {
        console.error('格式化时间出错:', e);
        return '--:--';
      }
    },
    // 格式化时间范围
    formatTimeRange: function formatTimeRange(round) {
      if (!round) return '时间未定义';

      // 使用解析后的实际时间
      if (round.actualStartTime && round.actualEndTime) {
        var startDate = round.actualStartTime;
        return (0, _date.formatTime)(startDate);
      }

      // 回退到原始时间
      if (round.start_time) {
        try {
          // 尝试解析ISO时间字符串
          var _startDate = new Date(round.start_time);
          return (0, _date.formatTime)(_startDate);
        } catch (e) {
          console.error('格式化时间出错:', e);
        }
      }
      return round.time || '时间未定义';
    },
    // 导航到编辑页面
    navigateToEdit: function navigateToEdit() {
      uni.navigateTo({
        url: "/pages/patrol_pkg/task/edit?id=".concat(this.taskId)
      });
    },
    // 切换任务状态
    toggleTaskStatus: function toggleTaskStatus() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var newStatus, statusText, res;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                if (!(_this4.taskDetail.status !== 1 && _this4.taskDetail.status !== 3)) {
                  _context4.next = 3;
                  break;
                }
                uni.showToast({
                  title: '当前状态无法操作',
                  icon: 'none'
                });
                return _context4.abrupt("return");
              case 3:
                newStatus = _this4.taskDetail.status === 1 ? 3 : 1;
                statusText = newStatus === 1 ? '启用' : '暂停';
                _context4.prev = 5;
                _this4.loading = true;
                _context4.next = 9;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'updateTask',
                  data: {
                    task_id: _this4.taskId,
                    status: newStatus
                  }
                });
              case 9:
                res = _context4.sent;
                if (res.code === 0) {
                  uni.showToast({
                    title: "".concat(statusText, "\u6210\u529F"),
                    icon: 'success'
                  });
                  _this4.taskDetail.status = newStatus;
                } else {
                  uni.showToast({
                    title: res.message || "".concat(statusText, "\u5931\u8D25"),
                    icon: 'none'
                  });
                }
                _context4.next = 17;
                break;
              case 13:
                _context4.prev = 13;
                _context4.t0 = _context4["catch"](5);
                console.error('切换任务状态错误:', _context4.t0);
                uni.showToast({
                  title: "".concat(statusText, "\u4EFB\u52A1\u51FA\u9519"),
                  icon: 'none'
                });
              case 17:
                _context4.prev = 17;
                _this4.loading = false;
                return _context4.finish(17);
              case 20:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[5, 13, 17, 20]]);
      }))();
    },
    // 切换轮次详情显示状态
    toggleRoundDetails: function toggleRoundDetails(round) {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                if (round.expanded) {
                  _context5.next = 20;
                  break;
                }
                // 先关闭所有其他轮次的详情
                if (_this5.taskDetail && _this5.taskDetail.rounds_detail) {
                  _this5.taskDetail.rounds_detail.forEach(function (r) {
                    if (r !== round) {
                      _this5.$set(r, 'expanded', false);
                    }
                  });
                }

                // 🔥 检查是否已加载点位数据
                if (!(!round.points || round.points.length === 0)) {
                  _context5.next = 17;
                  break;
                }
                // 显示加载状态
                _this5.$set(round, 'loading', true);
                _context5.prev = 4;
                _context5.next = 7;
                return _this5.loadRoundPoints(round);
              case 7:
                _context5.next = 14;
                break;
              case 9:
                _context5.prev = 9;
                _context5.t0 = _context5["catch"](4);
                console.error('加载轮次点位失败:', _context5.t0);
                uni.showToast({
                  title: '加载点位信息失败',
                  icon: 'none'
                });
                return _context5.abrupt("return");
              case 14:
                _context5.prev = 14;
                _this5.$set(round, 'loading', false);
                return _context5.finish(14);
              case 17:
                // 打开当前轮次的详情
                _this5.$set(round, 'expanded', true);
                _context5.next = 21;
                break;
              case 20:
                // 关闭当前轮次的详情
                _this5.$set(round, 'expanded', false);
              case 21:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[4, 9, 14, 17]]);
      }))();
    },
    // 🔥 新增：按需加载轮次点位数据
    loadRoundPoints: function loadRoundPoints(round) {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var res;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                if (!(!_this6.taskId || !round)) {
                  _context6.next = 2;
                  break;
                }
                return _context6.abrupt("return");
              case 2:
                _context6.prev = 2;
                _context6.next = 5;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'getRoundPoints',
                  data: {
                    task_id: _this6.taskId,
                    round_number: round.round,
                    // 只获取点位相关字段
                    fields: ['points.point_id', 'points.name', 'points.order', 'points.status', 'points.location', 'points.range', 'points.checkin_time', 'points.record_id', 'points.qrcode_enabled', 'points.abnormal']
                  }
                });
              case 5:
                res = _context6.sent;
                if (res.code === 0 && res.data && res.data.points) {
                  // 🔥 只更新该轮次的点位数据
                  _this6.$set(round, 'points', res.data.points);

                  // 更新点位统计
                  if (!round.point_stats) {
                    round.point_stats = {
                      total: res.data.points.length,
                      checked: res.data.points.filter(function (p) {
                        return p.status === 1;
                      }).length
                    };
                  }
                }
                _context6.next = 13;
                break;
              case 9:
                _context6.prev = 9;
                _context6.t0 = _context6["catch"](2);
                console.error('加载轮次点位数据失败:', _context6.t0);
                throw _context6.t0;
              case 13:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[2, 9]]);
      }))();
    },
    // 格式化时间字符串
    formatTimeStr: function formatTimeStr(timeStr) {
      if (!timeStr) return '--:--';
      return timeStr;
    },
    // 获取轮次完成率
    getRoundCompletion: function getRoundCompletion(round) {
      if (!round || !round.point_stats) return 0;
      var total = round.point_stats.total || 0;
      var checked = round.point_stats.checked || 0;
      if (total === 0) return 0;
      return Math.round(checked / total * 100);
    },
    // 计算剩余时间
    calculateRemainingTime: function calculateRemainingTime(round) {
      if (!round || !round.end_time) return '';
      var now = new Date();
      var endTime = new Date(round.end_time);
      if (now >= endTime) return '已结束';
      var diff = endTime - now;
      var minutes = Math.floor(diff / 1000 / 60);
      if (minutes < 60) {
        return "\u5269\u4F59".concat(minutes, "\u5206\u949F");
      } else {
        var hours = Math.floor(minutes / 60);
        var remainingMinutes = minutes % 60;
        return "\u5269\u4F59".concat(hours, "\u5C0F\u65F6").concat(remainingMinutes > 0 ? remainingMinutes + '分钟' : '');
      }
    },
    // 返回上一页
    navigateBack: function navigateBack() {
      uni.navigateBack();
    },
    // 获取班次时间 - 精简版本
    getShiftTime: function getShiftTime(task) {
      if (!task) return '';

      // 1. 优先使用已处理的shift_time
      if (task.shift_time) {
        return task.shift_time;
      }

      // 2. 使用班次对象中的时间
      if (task.shift) {
        var start = task.shift.start_time || task.shift.startTime;
        var end = task.shift.end_time || task.shift.endTime;
        if (start && end) {
          task.shift_time = "(".concat(start, " - ").concat(end, ")");
          return task.shift_time;
        }
      }

      // 3. 从加载的班次信息中获取
      if (task.shift_id && this.shiftInfo[task.shift_id]) {
        var shift = this.shiftInfo[task.shift_id];
        var _start = shift.start_time || shift.startTime;
        var _end = shift.end_time || shift.endTime;
        if (_start && _end) {
          task.shift_time = "(".concat(_start, " - ").concat(_end, ")");
          return task.shift_time;
        }
      }

      // 4. 使用任务自身的时间字段
      var startTime = task.startTime || task.start_time;
      var endTime = task.endTime || task.end_time;
      if (startTime && endTime) {
        task.shift_time = "(".concat(startTime, " - ").concat(endTime, ")");
        return task.shift_time;
      }

      // 5. 使用轮次信息
      if (this.taskDetail && this.taskDetail.rounds_detail && this.taskDetail.rounds_detail.length > 0) {
        var firstRound = this.taskDetail.rounds_detail[0];
        if (firstRound.time) {
          return "(\u9996\u8F6E:".concat(firstRound.time).concat(firstRound.day_offset > 0 ? ' 次日' : '', ")");
        }
      }

      // 6. 返回班次名称
      return task.shift_name ? "(".concat(task.shift_name, ")") : '';
    },
    // 获取进度条样式类
    getProgressClass: function getProgressClass(rate) {
      if (rate === 100) {
        return 'completed';
      } else if (rate > 0 && rate < 100) {
        return 'partial';
      }
      return '';
    },
    // 获取点位状态样式类
    getPointStatusClass: function getPointStatusClass(point, round) {
      if (point.status === 1 || point.status === 2) {
        return 'success';
      } else if (point.status === 0 && round.status === 1) {
        return 'warning';
      } else if (round.status === 3 && point.status === 0) {
        return 'danger';
      }
      return '';
    },
    // 确保overall_stats存在
    ensureOverallStats: function ensureOverallStats() {
      if (!this.taskDetail.overall_stats) {
        this.taskDetail.overall_stats = {};
      }

      // 计算总点位和已完成点位
      var totalPoints = 0;
      var completedPoints = 0;
      var missedPoints = 0;
      this.taskDetail.rounds_detail.forEach(function (round) {
        if (round.point_stats) {
          if (round.point_stats.total) {
            totalPoints += round.point_stats.total;
          }
          if (round.point_stats.checked) {
            completedPoints += round.point_stats.checked;
          }
          // 计算缺卡点位
          var roundMissed = (round.point_stats.total || 0) - (round.point_stats.checked || 0);
          if (roundMissed > 0) {
            missedPoints += roundMissed;
          }
        }
      });

      // 更新总体统计数据
      this.taskDetail.overall_stats.total_points = totalPoints;
      this.taskDetail.overall_stats.completed_points = completedPoints;
      this.taskDetail.overall_stats.missed_points = missedPoints;

      // 计算总体完成率
      var completionRate = 0;
      if (totalPoints > 0) {
        completionRate = Math.round(completedPoints / totalPoints * 100);
      }
      this.taskDetail.overall_stats.completion_rate = completionRate;

      // 确保异常记录数存在
      if (this.taskDetail.overall_stats.abnormal_count === undefined) {
        this.taskDetail.overall_stats.abnormal_count = 0;
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 433:
/*!**********************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/detail.vue?vue&type=style&index=0&lang=scss& ***!
  \**********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss& */ 434);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 434:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/detail.vue?vue&type=style&index=0&lang=scss& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[427,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/patrol_pkg/task/detail.js.map
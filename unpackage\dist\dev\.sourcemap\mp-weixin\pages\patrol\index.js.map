{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol/index.vue?d924", "webpack:///D:/Xwzc/pages/patrol/index.vue?d890", "webpack:///D:/Xwzc/pages/patrol/index.vue?371d", "webpack:///D:/Xwzc/pages/patrol/index.vue?c9d9", "uni-app:///pages/patrol/index.vue", "webpack:///D:/Xwzc/pages/patrol/index.vue?82eb", "webpack:///D:/Xwzc/pages/patrol/index.vue?a766"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "notStartedOrActive", "completedOrExpired", "components", "PMap", "<PERSON><PERSON><PERSON><PERSON>", "PTaskCard", "PEmptyState", "data", "userLocation", "activeTaskId", "currentTask", "selectedRoundIndex", "selectedRound", "taskList", "taskRoundInfos", "taskShifts", "routeCache", "markers", "polylines", "circles", "mapCenter", "latitude", "longitude", "weatherInfo", "isLoading", "isInitialLoading", "isRefreshing", "isEmpty", "filterStatus", "showMap", "currentLocation", "accuracy", "altitude", "speed", "address", "isLocating", "lastUpdated", "showFilterPanel", "showTaskDetail", "<PERSON><PERSON><PERSON><PERSON>", "roundUpdateInterval", "userMap", "isTaskSelectorExpanded", "isDrawerOpen", "isShowingLoginTip", "touchStartY", "touchMoveY", "drawerHeight", "isDragging", "currentTranslateY", "drawerStyle", "maskStyle", "lastTouchTime", "lastDeltaY", "lastLoadTime", "loadingInProgress", "hasInitialLoaded", "computed", "currentDate", "mapHeight", "currentRoutePoints", "round", "availableRounds", "currentRoundInfo", "status", "onLoad", "uni", "onUnload", "onShow", "console", "getApp", "setTimeout", "methods", "getAccuracyColor", "initLocationAndTasks", "title", "icon", "initLocation", "isHighAccuracy", "highAccuracyExpireTime", "location", "geocoderService", "geocodeResult", "district", "startLocationWatch", "type", "success", "fail", "stopLocationWatch", "complete", "restartLocationWatch", "forceStopLocationWatch", "moveToCurrentLocation", "mask", "res", "loadTaskList", "showLoading", "userId", "patrolApi", "name", "action", "level", "fields", "viewScope", "params", "patrolDate", "pageSize", "rawTaskList", "newTaskList", "getCurrentUserId", "preloadTaskShifts", "shiftPromises", "loadedShiftIds", "tasks", "shift_id", "then", "catch", "Promise", "updateTasksRoundInfo", "updateTaskRoundInfo", "currentTime", "roundInfo", "rounds", "task", "getTaskShift", "getTaskRoundInfo", "isActiveTask", "calculateCurrentRound", "nextRound", "nextRoundIndex", "timeUntilNext", "isCountdown", "currentRound", "currentRoundIndex", "isEarlyCompletion", "timeRemaining", "totalTime", "timeElapsed", "completionPercentage", "lastRound", "lastRoundIndex", "isCompleted", "enabledRounds", "aHour", "aMinute", "bHour", "bMinute", "shift", "endTime", "areAllPointsCompleted", "roundRecord", "buildTaskMarkers", "circle", "points", "sortedPoints", "pointId", "point", "id", "iconPath", "width", "height", "callout", "content", "color", "fontSize", "borderWidth", "bgColor", "padding", "display", "borderRadius", "textAlign", "coordinates", "order", "pointRange", "fillColor", "radius", "strokeWidth", "arrowLine", "dottedLine", "updateCurrentLocationMarker", "circleColor", "strokeColor", "onPointMarkerTap", "duration", "message", "roundToUse", "getCurrentActiveRound", "activeRound", "onWeatherLoaded", "onTaskClick", "onTaskContinue", "taskId", "onTaskViewDetail", "url", "loadTaskDetail", "startPatrol", "forceRefresh", "task_id", "newTaskData", "processedTasks", "shiftRes", "taskIndex", "updatedTaskList", "taskData", "processPatrolTask", "taskPoints", "navigateToCheckIn", "showCancel", "filter", "sort", "targetRound", "showError", "startRoundUpdateTimer", "clearRoundUpdateTimer", "clearInterval", "updateAllTaskRounds", "oldRoundInfo", "needFullRefresh", "oldStatus", "showRoundChangeNotification", "oldRound", "calculateDistance", "Math", "deg2rad", "checkLoginState", "showLoginTip", "confirmText", "cancelText", "loadUsers", "userInfo", "userid", "result", "users", "user", "getUserName", "formatTimeRange", "formatCountdown", "formatValidTime", "totalTimeStr", "isTimeUrgent", "isTaskToday", "refreshTaskList", "now", "timeSinceLastLoad", "savedActiveTaskId", "savedMapCenter", "taskExists", "handleTaskUpdated", "point_id", "processTasks", "completed_points", "area", "patrol_date", "processedTask", "allRoundsExpired", "hasUpcomingRound", "allRoundsCompleted", "hasActiveRound", "formatDateForCompare", "date", "onRoundSelected", "toggle<PERSON>rawer", "closeDrawer", "transform", "transition", "openDrawer", "forceUpdateTaskInList", "handleTouchStart", "query", "handleTouchMove", "damping", "backgroundColor", "handleTouchEnd", "closeT<PERSON><PERSON>old", "getRoundStatus"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,sWAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjFA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;AC6InnB;AACA;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AACA;EACA;IACA;EACA;;EAEA;EACA;EACA,uCACA;IAAA;EAAA,EACA;EACA,uCACA;IAAA;EAAA,EACA;;EAEA;EACAC;IAAA;EAAA;EACAC;IAAA;EAAA;;EAEA;EACA;AACA;AAAA,eAEA;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;QACAC;QACAC;MACA;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAAA;;MAEA;MACAC;MAEA;MACAC;QACAT;QACAC;QACAS;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MAAA;;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;;MAEA;MACA;QACA;QACA;QACA;;QAEA;QACA;UACAC;QACA;QACA;QAAA,KACA;UACA;UAEA;YACAA;UACA;YACAA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;;QAEA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IACA;IACAC;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;UAAAC;QAAA;MACA;MAEA;MACA;IACA;EACA;EACAC;IACA;IACA;;IAEA;IACA;;IAEA;IACAC;;IAEA;IACAA;IACAA;IACAA;EACA;EACAC;IACA;IACAD;IACAA;IACAA;IACAA;;IAEA;IACA;;IAEA;IACA;EACA;EACAE;IAAA;IACA;MACA;MACA;IACA;MACA;MACA;MACAC;IACA;MACA;MACAA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;QACAC;QAEAD;;QAEA;QACA;QACA;;QAEA;QACAE;UACA;QACA;MAEA;QACA;QACAA;UACA;QACA;MACA;IACA;;IAEA;IACA;IACAF;IACAE;MACA;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAL;gBACAH;kBACAS;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;;gBAEA;gBAAA;gBAAA,OACA;kBACAC;kBAAA;kBACAC;gBACA;cAAA;gBAHAC;gBAKA;gBACA;kBACA3D;kBACAC;gBACA;;gBAEA;gBACA;kBACAD;kBACAC;kBACAS;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;kBACAf;kBACAC;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAEA2D;cAAA;gBAAAC;gBACA;gBACAC;gBACAjB;kBACAS;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAN;cAAA;gBAGA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAH;kBACAS;kBACAC;gBACA;cAAA;gBAAA;gBAEAL;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAa;MAAA;MACAf;MAEA;QACAA;QACA;MACA;MAEA;QACA;QACA1E;UACA0F;UACAC;YACAjB;;YAEA;YACA1E;cACA;cACA;;cAEA;cACA;gBACA0B;gBACAC;gBACAS;gBACAC;gBACAC;gBACAE;gBACAC;cACA;;cAEA;cACA;gBACAf;gBACAC;cACA;;cAEA;cACA;cACA;YACA;;YAEA;YACA;UACA;UACAiE;YACAlB;YACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAmB;MAAA;MACAnB;MAEA;QACAA;QACA;MACA;MAEA;QACA;QACA1E;UACA2F;YACAjB;UACA;UACAkB;YACAlB;UACA;UACAoB;YACA;cACA;cACA9F;YACA;cACA0E;YACA;YACA;YACA;UACA;QACA;MACA;QACAA;QACA;QACA;MACA;IACA;IAEA;IACAqB;MAAA;MACA;QACA;QACA;;QAEA;QACAnB;UACA;QACA;MACA;QACAF;MACA;IACA;IAEA;IACAsB;MACA;QACA;QACAhG;QACAA;QACA;MACA;QACA0E;QACA;MACA;IACA;IAEA;IACAuB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA1B;kBACAS;kBACAkB;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBACAlG;oBACA0F;oBACAP;oBACAC;oBACAO;oBACAC;kBACA;gBACA;cAAA;gBARAO;gBAUA;gBACA;kBACAzE;kBACAC;gBACA;;gBAEA;gBACA;kBACAD;kBACAC;gBACA;;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBACA4C;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAG;;gBAEA;gBACA;kBACA;kBACA;oBACAhD;oBACAC;kBACA;;kBAEA;kBACA;oBACA;kBACA;gBACA;kBACA;kBACA;gBACA;;gBAEA;gBACA4C;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA6B;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAKA;gBACA;gBAEA;kBACA;gBACA;;gBAEA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKAC;kBACAC;kBACAC;kBACA7F;oBACA8F;oBAAA;oBACAC;oBAAA;oBACA;oBACAC;oBACAC;sBACAC;sBACAC;sBACA1C;sBACA;oBACA;kBACA;gBACA;cAAA;gBAfA8B;gBAAA,MAiBAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAa,mCAEA;gBACA9F,6CAEA;gBACA;kBACAA;oBAAA;kBAAA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;gBACA+F,0DAEA;gBACA;kBAAA;gBAAA;kBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;kBACA;;kBAEA;kBACA;oBACA;oBACA;kBACA;kBACA;kBAAA,KACA;oBACA;kBACA;;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvC;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwC;MACA;QACA;QACA;QACA;UACA;YACA;UACA;UAEA;YACA;cACA;cACA;YACA;cACAxC;YACA;UACA;QACA;;QAEA;QACA;QACA;UACA;YACA;UACA;UAEA;YACA;cACA;cACA;YACA;cACA;YAAA;UAEA;QACA;;QAEA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAyC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC,qBAEA;gBACAC;kBACA;oBACAD;;oBAEA;oBACA;sBACAb;sBACAC;sBACA7F;wBACA2G;wBACAjB;sBACA;oBACA,GACAkB;sBACA;wBACA;;wBAEA;wBACA;sBACA;oBACA,GACAC;sBACA/C;oBACA;oBAEA0C;kBACA;oBACA;oBACA;kBACA;gBACA;;gBAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAM;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA;MAEA;;MAEA;MACAL;QACA;MACA;IACA;IAEA;IACAM;MACA;;MAEA;MACAC;MAEA;;MAEA;MACA;QACA;;QAEA;QACA;UACA;UACA;YACA;YACAC;UACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;UACApD;QACA;;QAEA;QACA;UACA;UACA;YAAA;UAAA;;UAEA;UACA;YACA;YACA;cACA;cACA;YACA;YAEA;cACA;cACA;cACA;cACA;cAEA;gBACAqD;cACA;gBACA;gBACA;gBACAA;cACA;gBACA;gBACA;gBACAA;cACA;YACA;UACA;;UAEA;UACA;YAAA;UAAA;UACA;YAAA;UAAA;UAEA1H;YAAA;UAAA;UACAC;YAAA;UAAA;;UAEA;UACA0H;QACA;MACA;QACAtD;MACA;IACA;IAEA;IACAuD;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;QACAJ;MACA;QACA;QACA;QACA;UACA;QACA;;QAEA;QACAA;MACA;;MAEA;MACA;MAEA;IACA;IAEA;IACAK;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;QACA;;QAEA;QACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;UACA;;UAEA;UACA;UACA;UACA;;UAEA;UACA;YACA;YACA;cACA/D;cACAgE;cACAC;cACAC;cACAC;YACA;UACA;YACA;YACA;YACA;;YAEA;YACA;cACA;gBACAnE;gBACAoE;gBACAC;gBACAC;gBAAA;gBACAC;gBACAJ;gBAAA;gBACAK;cACA;YACA;;YAEA;cACAxE;cACAoE;cACAC;cACAE;cACAE;cACAD;cACAE;cACAP;YACA;UACA;QACA;;QAEA;QACA;QACA;QAEA;UACAnE;UAAA;UACA2E;UACAC;UACAC;QACA;MACA;;MAEA;MACA;QACA;UAAA7E;QAAA;MACA;MAEA;MACA;;MAEA;MACA8E;QACA;QACA;;QAEA;QACA;UAAA;UAAAC;UAAAC;QACA;UAAA;UAAAC;UAAAC;;QAEA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;;QAEA;QACA;QACA,0EACAJ,iEACAK;;QAEA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;UACA;UACA;UACA;;UAEA;UACA;YACAC;UACA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;UACA;UACA;YACApF;YACAgE;YACAC;YACAC;YACAC;UACA;QACA;UACA;UACA;YACAnE;YACAoE;YACAC;YACAE;YACAE;YACAD;YACAE;YACAP;UACA;QACA;MACA;;MAEA;MACA;QACAnE;QACA2E;QACAC;MACA;IACA;IAEA;IACAS;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;UAAA;QAAA;QAEA;UACA;UACA,oEACAC;;UAEA;UACA;;UAEA;UACA;;UAEA;UACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QAAA,OACAC,uBACAA;QAAA;QACAA;QAAA;QACAA;QAAA;QACAA;QAAA;QACAA;QAAA;QACAA;QAAA,CACA;MAAA,EACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;QACA;QACA;UACA;QACA;QACAnF;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACAA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA+D;UAAA;QAAA;MACA;;MAEA;MACA;QACAA;UAAA;QAAA;MACA;;MAEA;MACA;QACAA;UAAA;QAAA;MACA;;MAEA;MACA;QACA;QACAA;MACA;MAEA;QACA/D;QACA;MACA;;MAEA;MACA;MACA;QACAoF;MACA;QACAA;MACA;MAEA;QACApF;QACA;MACA;;MAEA;MACA;QACA;UACA;QACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;;MAEA;MACA;MACA;;MAEA;MACAqF;QACA;UACArF;UACA;QACA;;QAEA;QACA;QACA;UACAA;UACAsF;QACA;;QAEA;QACA;QAEA;UACAtI;UACAC;QACA;UACAD;UACAC;QACA;UACAA;UACAD;QACA;UACA;UACA;YACAA;UACA;UACA;YACAC;UACA;QACA;QAEA;UACA+C;UACA;QACA;;QAEA;QACA;UACAA;UACA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;UACA;UACAwE;QACA;UACA;UACA;UACAA,uCACAe,gCACAA,sBACAA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;UACAC;UACAF;UACAtI;UACAC;UACAwI;UACAC;UACAC;UACArF;UACAkE;UACAoB;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;QAEAzJ;;QAEA;QACA0J;UACArJ;UACAD;UACAsI;UAAA;UACAiB;QACA;;QAEA;QACA;QACA;;QAEA;QACA;UACAC;QACA;QACA;QAAA,KACA;UACAA;QACA;QACA;QAAA,KACA;UACAA;QACA;;QAEA;QACAA;QAEA;UACAxJ;UACAC;UACA6I;UAAA;UACAW;UAAA;UACAC;UACAC;QACA;MACA;;MAEA;MACA;QACA;QACA;UAAA;QAAA;UAAA;YACA3J;YACAC;UACA;QAAA;;QAEA;QACA;QAEA;UACAmI;UACAU;UACAJ;UAAA;UACAkB;UACAC;QACA;;QAEA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;UACA7J;UACAC;QACA;MACA;QACA;UACAD;UACAC;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACA6J;MACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACAC;QACAN;MACA;QACAM;QACAN;MACA;QACAM;QACAN;MACA;QACAM;QACAN;MACA;QACAM;QACAN;MACA;QACAM;QACAN;MACA;;MAEA;MACA;QACAzJ;QACAC;QACAyJ;QAAA;QACAZ;QACAW;QACAE;QACAK;MACA;;MAEA;MACA;;MAEA;MACA;QAAA,OACA7B;QAAA;QACAA;QAAA;QACAA;QAAA;QACAA;QAAA;QACAA;QAAA;QACAA;MAAA;MAAA,CACA;;MAEA;QACArI;MACA;QACAA;MACA;MAEA;IACA;IAEA;IACAmK;MAAA;MACAjH;MACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;QACAH;UACAS;UACAC;UACA2G;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACAlH;;MAEA;MACA;QACAA;QACA;QACA;QACA;UACA;UACAA;;UAEA;UACA;YACAH;cACAS;cACAC;cACA2G;YACA;YACA;UACA;;UAEA;UACA;QACA;UACArH;YACAS;YACAC;YACA2G;UACA;UACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;UAAA;QAAA;QACA;UACA;UACA;UACA;YACAC;UACA;YACAA;UACA;YACAA;UACA;UAEAtH;YACAS;YACAC;YACA2G;UACA;UACA;QACA;QACAE;MACA;QACA;QACA;QACA;UACAA;UACApH;QACA;UACAA;UACAH;YACAS;YACAC;YACA2G;UACA;UACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAG;MACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;QACAC;UAAA;QAAA;MACA;MAEA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;UACA;YACAjI;YACAkI;UACA;QACA;UACA;UACA;QACA;;QAEA;QACA;MACA;QACA;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA9H;QACA+H;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAAC;gBAAA,IACAL;kBAAA;kBAAA;gBAAA;gBACA1H;gBACA;gBAAA;cAAA;gBAAA;gBAKA;gBACA;;gBAEA;gBAAA;gBAAA,OACA6B;kBACAC;kBACAC;kBACA7F;oBACA8L;oBAAA;oBACAhG;oBAAA;oBACA+F;kBACA;gBACA;cAAA;gBARAtG;gBAAA,MAUAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAwG,wBAEA;gBACA;kBACAC;kBACA;oBACAD;kBACA;gBACA;;gBAEA;gBACA;kBACAA;gBACA;;gBAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEApG;kBAAA;kBACAC;kBACAC;kBACA7F;oBACAiG;sBAAA;sBACAU;oBACA;kBACA;gBACA;cAAA;gBARAsF;gBASA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnI;cAAA;gBAAA;gBAAA;cAAA;gBAGA;cAAA;gBAGA;gBACA;;gBAEA;gBACAoI;kBAAA;gBAAA;gBACA;kBACA;kBACAC,sEACA;kBACAA;kBACA;kBACA;kBAEArI;gBACA;;gBAEA;gBACA;kBACA;;kBAEA;kBACA;oBACA;oBACAE;sBACA;oBACA;kBACA;gBACA;;gBAEA;gBACA;gBAAA,kCAEA;cAAA;gBAEA;gBAAA,kCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;gBACA;gBAAA,kCACA;cAAA;gBAAA;gBAEA;gBACAE;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4H;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEAQ;kBAAA;kBAAA;gBAAA;gBACAtI;gBACA;gBAAA;cAAA;gBAAA;gBAKA;gBACA;;gBAEA;gBAAA;gBAAA,OACA6B;kBACAC;kBACAC;kBACA7F;oBACA8L;oBAAA;oBACAhG;oBAAA;oBACA+F;kBACA;gBACA;cAAA;gBARAtG;gBAUA;gBACA8G;kBACA;oBACA;kBACA;kBAEA;;kBAEA;kBACAjF;oBACA;oBACA;sBACA9D;oBACA;kBACA;;kBAEA;kBACA;;kBAEA;kBACA;oBAAA;kBAAA;kBAEA;oBACA;oBACAuE;oBACA/D;kBACA;oBACA;oBACA;sBACA;wBAAA;sBAAA;sBACA;sBACA;wBACA+D;sBACA;oBACA;;oBAEA;oBACA;sBACA;wBAAA;sBAAA;sBACA;wBACA;wBACAA;0BACA;0BACA;0BACA;wBACA;sBACA;oBACA;;oBAEA;oBACA;sBACA;wBAAA;sBAAA;sBACA;wBACA;wBACAA;0BACA;0BACA;0BACA;wBACA;sBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA/D;;oBAEA;oBACA;oBACA;sBACAA;sBACAwI;oBACA;sBACAxI;sBACAwI;oBACA;;oBAEA;oBACA;sBACAxI;sBACA;sBACA;oBACA;;oBAEA;oBACA;sBACA;sBACA;sBACA;oBACA;oBAEAA;;oBAEA;oBACA;sBACA;sBACA;wBACA;wBACA;0BACA;wBACA;wBACA;wBACA;sBACA;sBAEA;wBACAA;wBACA;wBACA;wBACA;sBACA;sBAEAA;oBACA;;oBAEA;oBACA;kBACA;gBACA;gBAEA;kBACAA;kBACA;kBACAuI;gBACA;kBACAvI;kBACA;kBACAuI;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvI;gBACA;gBACAuI;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;MACA;QACAzI;QACA;QACA;MACA;MAEA;QACAA;QACA;QACA;MACA;;MAEA;MACA;QAAA;QACAH;UACAS;UACAuF;UACA6C;QACA;QACA;MACA;;MAEA;MACA;MACA;QACA1I;QACA;QACA;MACA;;MAEA;MACA,yCACA2I;QAAA;MAAA;MAAA,CACAC;QACA;QACA;UACA;UACA;UACA;UACA;QACA;;QACA;MACA;;MAEA;MACA;MACA;QACA;UAAA;QAAA;QACA;UACAC;QACA;MACA;;MAEA;MACA;QACA;QACAA;UAAA;UACA;UACA;YAAA;UAAA;UACA;UACA;QACA;MACA;;MAEA;MACA;QACAhJ;UACAU;UACAD;UACA4G;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACAU;MACA;;MAEA;MACA/H;QACA+H;MACA;IACA;IAEA;IACAjG;MAAA;MACA;MAEA;QACA9B;UACAS;UACAkB;QACA;MACA;QACA;QACAtB;UACA;YACA;YACAL;UACA,aACA;QACA;MACA;IACA;IAEA;IACAiJ;MACAjJ;QACAU;QACAD;QACA4G;MACA;IACA;IAEA;IACA6B;MAAA;MACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;MACA;MAEA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;;QAEA;QACA,yDACAC;UACA;UACAnJ;UACA;UACAE;YACA;YACA;UACA;QACA;;QAEA;;QAEA;QACA;UACA;UACA;YACAF;YACA;YACA;YACA;YACA;cACA;YACA;YACAoJ;UACA;;UAEA;UACA,IACAC,iEACAA,sEACAA,oEACA;YACAD;;YAEA;YACA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QAEA;;QAEA;QACAlJ;UACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAoJ;MAAA;MACA;MACA;MAEA;QAAA;QACA;QACAhJ;QACAuF;MACA;QAAA;QACA;QACAvF;QACAuF;MACA;QAAA;QACA;QACAvF;QACAuF;MACA;QACA;QACAvF;QACAuF;MACA;;MAEA;MACA;QACAhG;UACAS;UACAuF;UACA6C;UACAzH;YACA;YACA,IACAsI,iEACAA,sEACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA,QACAC,0CACAA,8DACAA;MAEA;MACA;MAEA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;QACA;QAEA;QACA;UACA;UACA;UACA;QACA;QACA;MACA;QACA3J;QACA;UACA;QACA;QACA;MACA;IACA;IAEA;IACA4J;MAAA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA/J;QACAS;QACAuF;QACAgE;QACAC;QACA7I;UACA;YACA;;YAEApB;cACA+H;YACA;UAQA;YACA;YACA/H;cACA+H;YACA;UACA;UACA;UACA1H;YACA;UACA;QACA;MACA;IACA;IAEA;IACA6J;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBACApI;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBACA5B;gBAAA;cAAA;gBAAA;gBAAA,OAIA6B;kBACAC;kBACAC;kBACA7F;oBACA+N;oBACA5H;kBACA;gBACA;cAAA;gBAPA6H;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACAC,gCAEA;gBACA;gBAEAA;kBACA;kBACA,oDACAC;oBACA;oBACAtI;kBAAA,EACA;kBACA;gBACA;gBAAA,kCAEAqI;cAAA;gBAEAnK;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;cAAA;gBAAA,kCAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAqK;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;UAEA;YACA;YACA;YACA;;YAEA;YACA;UACA;YACAtK;YACA;UACA;QACA;QAEA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAuK;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;UACA;QACA;MACA;QACAvK;QACA;MACA;IACA;IAEA;IACAwK;MACA;MAEA;QACA;QACA;UACA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;QACA;UACA;UACA;UACA;UAEA;YACAC;UACA;YACAA;UACA;QACA;;QAEA;QACA;UACA;QACA;UACA;QACA;MACA;QACAzK;QACA;MACA;IACA;IAEA;IACA0K;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;MACA;QACA3K;QACA;MACA;IACA;IAEA;IACA4K;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAjJ;gBACA;gBACAkJ;gBACAC,gDAEA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBACA9K;gBAAA;cAAA;gBAAA,MAKA8K;kBAAA;kBAAA;gBAAA;gBACA9K;gBAAA;cAAA;gBAIA;gBACA;gBACA;gBAEA;kBACAH;oBACAS;oBACAC;oBACAiB;oBACA0F;kBACA;gBACA;;gBAEA;gBACA6D;gBACAC,uDAEA;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAEA;gBACAC;kBAAA;gBAAA;gBAAA,MAEAF;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIA/K;gBACA;cAAA;gBAAA;gBAEA;gBACA;;gBAEA;gBACAE;kBACA;gBACA;;gBAEA;kBACA;kBACAA;oBACAL;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAqL;MAAA;MACAlL;MACA;QAAAmL;QAAA3L;;MAEA;MACA;QACAQ;QACA;QACA;UACA;YAAA;UAAA;UACA;YACAA;YACA;YACA;cACA0H;cACAlI;gBACAA;gBACAG;cACA;YACA;UACA;QACA;MACA;;MAEA;MACA;QACA;UAAA;YAAA;cAAA;gBAAA;kBACAK;kBAAA;kBAAA,MAIA;oBAAA;oBAAA;kBAAA;kBAAA;kBAAA,OAEA;gBAAA;kBAAA;kBAAA;gBAAA;kBAAA;kBAAA,OAGA;gBAAA;kBAAA;kBAAA;gBAAA;kBAAA;kBAAA;kBAGAA;gBAAA;kBAAA;kBAEA;kBAAA;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CAEA;MACA;IACA;IAEA;IACAoL;MAAA;MACA;MAEA;;MAEA;MACA;QACA;QACA,oDACA9H;UACA8B;UACAiG;UACA1L;UACA2L;UACAC;UACA1I;UACAf;QAAA,EACA;;QAEA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACA;UACA;UACA;UACA;;UAEA;UACA0J;YACA;;YAEA;YACAhM;YACAA;YAEA;cACA;cACA;cACA;;cAEA;cACA,0CACAA,gCACAA;;cAEA;cACA;gBACA;gBACA;gBACA;cACA;gBACA;gBACA;kBACAA;kBACAiM;gBACA;kBACAjM;kBACAkM;kBACAC;kBACAF;gBACA;kBACAjM;kBACAmM;gBACA;kBACAnM;kBACAoM;kBACAD;kBACAF;gBACA;cACA;;cAEA;cACA;gBACAzL;cACA;YACA;cACAA;YACA;UACA;;UAEA;UACA;;UAEA;UACA,0BACAwL,+BACAA;YACA;YACAA;UACA;YACA;YACAA;UACA;YACA;YACAA;UACA;YACA;YACAA;UACA;;UAEA;UACA;YACAxL;UACA;;UAEA;UACA,4DACA;YAAA;UAAA,EACA;UACA,4DACA;YAAA;UAAA,EACA;UAEArE;YAAA;UAAA;UACAC;YAAA;UAAA;UAEA4P;QACA;QAEA;MACA;MAEA;IACA;IAEA;IACAK;MACA;QACA;MACA;MAEA;QACA;QACA;UACA;UACA;UACA;YACA7L;YACA;UACA;UACA8L;QACA;;QAEA;QACA;UACA9L;UACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QAEA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACA+L;MACA;MACA,wCACA,6CACA;;MAEA;MACA,0BACA,6CACA;QACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEArM;;MAEA;MACA6B;QACAC;QACAC;QACA7F;UACA8L;UAAA;UACAhG;QACA;MACA;QACA;UACA;UACA;YAAA;UAAA;UACA;YACA;YACA;YACAO;;YAEA;YACA;cACA;cACA;gBACAA;cACA;YACA;;YAEA;YACA;YACAvC;UACA;QACA;MACA;QACAA;MACA;IACA;IAEAsM;MAAA;MACA;;MAEA;MACA;QACA;MACA;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IAEAC;MACA;;MAEA;MACA;QACA;MACA;MAEA;MACA;MACA;MACA;;MAEA;MACA;MAEA;;MAEA;MACA;MACA;QAAA;QACAC;MACA;QAAA;QACAA;MACA;;MAEA;;MAEA;MACA;QACAP;QACAC;MACA;;MAEA;MACA;MACA;QACAO;MACA;;MAEA;MACA;MACA;IACA;IAEAC;MACA;;MAEA;MACA;QACA;MACA;MAEA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;QAAA;QACAC;MACA;;MAEA;MACA;QACAV;QACAC;MACA;;MAEA;MACA;QACA;MACA;QACA;QACA;UACAO;QACA;MACA;MAEA;MACA;IACA;IAEA;IACAG;MAAA;MACA;QACA;MACA;;MAEA;QACA;QACA;UACA;QACA;QAEA;QACA;;QAEA;QACA,0CACArN,gCACAA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACAQ;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5jGA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4321fcc8&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=4321fcc8&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    uniPopupMessage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup-message/uni-popup-message\" */ \"@/uni_modules/uni-popup/components/uni-popup-message/uni-popup-message.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getAccuracyColor()\n  var g0 = _vm.currentLocation.accuracy\n    ? _vm.currentLocation.accuracy.toFixed(1)\n    : null\n  var g1 = _vm.taskList.length\n  var l0 = !(g1 === 0)\n    ? _vm.__map(_vm.taskList, function (task, index) {\n        var $orig = _vm.__get_orig(task)\n        var m1 = _vm.getTaskShift(task)\n        var m2 = _vm.getTaskRoundInfo(task)\n        return {\n          $orig: $orig,\n          m1: m1,\n          m2: m2,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        g1: g1,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"patrol-index\">\n    <!-- 地图区域 -->\n    <p-map\n      ref=\"patrolMap\"\n      :longitude=\"mapCenter.longitude\"\n      :latitude=\"mapCenter.latitude\"\n      :marker-data=\"markers\"\n      :polyline-data=\"polylines\"\n      :circle-data=\"circles\"\n      :scale=\"19\"\n      :height=\"mapHeight || '100vh'\"\n      @marker-tap=\"onPointMarkerTap\"\n      @callout-tap=\"onPointMarkerTap\"\n    >\n      <!-- 地图顶部控件区域 -->\n      <view class=\"map-controls\">\n        <!-- 控制按钮区域 -->\n        <view class=\"control-buttons\">\n          <!-- 定位按钮 -->\n          <button class=\"control-btn\" @tap=\"moveToCurrentLocation\">\n            <uni-icons type=\"location\" size=\"24\" color=\"#1677FF\"></uni-icons>\n          </button>\n        </view>\n      </view>\n\n      <!-- GPS精度显示 -->\n      <view class=\"location-accuracy\">\n        <view class=\"status-dot\" :style=\"{ background: getAccuracyColor() }\"></view>\n        <text class=\"accuracy-text\">GPS精度: {{ currentLocation.accuracy ? currentLocation.accuracy.toFixed(1) : '0' }}米</text>\n      </view>\n    </p-map>\n\n    <!-- 右下角菜单按钮 -->\n    <view class=\"float-menu-btn\" @tap=\"toggleDrawer\">\n      <view class=\"float-menu-btn__inner\">\n        <uni-icons type=\"bars\" size=\"22\" color=\"#FFFFFF\"></uni-icons>\n        <text class=\"float-menu-btn__text\">任务</text>\n      </view>\n    </view>\n\n    <!-- 天气信息简洁显示 -->\n    <view class=\"weather-compact\">\n      <p-weather\n        :location=\"currentLocation\"\n        :mini=\"true\"\n        @weather-loaded=\"onWeatherLoaded\"\n      ></p-weather>\n    </view>\n\n    <!-- 抽屉菜单 -->\n    <view \n      class=\"drawer-mask\" \n      :class=\"{'drawer-mask--active': isDrawerOpen}\" \n      :style=\"maskStyle\" \n      @click=\"closeDrawer\"\n      @touchmove.stop.prevent\n    >\n      <view\n        class=\"task-drawer\"\n        :class=\"{'task-drawer--active': isDrawerOpen}\"\n        :style=\"drawerStyle\"\n        @touchstart=\"handleTouchStart\"\n        @touchmove=\"handleTouchMove\"\n        @touchend=\"handleTouchEnd\"\n        @click.stop\n      >\n        <!-- 手势区域指示器 -->\n        <view class=\"drawer-handle\"></view>\n\n        <!-- 内容区域 -->\n        <scroll-view\n          class=\"drawer-content\"\n          scroll-y\n          :scroll-top=\"currentTranslateY === 0 ? undefined : 0\"\n        >\n          <!-- 抽屉头部区域 -->\n          <view class=\"task-drawer__header\">\n            <view class=\"task-drawer__title-container\">\n              <uni-icons type=\"calendar\" size=\"20\" color=\"#1677FF\"></uni-icons>\n              <text class=\"task-drawer__date\">{{ currentDate }}</text>\n              <text class=\"task-drawer__title-text\">巡视任务</text>\n            </view>\n            <view class=\"task-drawer__refresh-btn\" @click=\"refreshTaskList\">\n              <uni-icons type=\"refresh\" size=\"18\" color=\"#1677FF\"></uni-icons>\n            </view>\n            <view class=\"task-drawer__close-btn\" @click=\"closeDrawer\">\n              <uni-icons type=\"closeempty\" size=\"20\" color=\"#666\"></uni-icons>\n            </view>\n          </view>\n\n          <!-- 暂无任务显示 -->\n          <p-empty-state\n            v-if=\"taskList.length === 0\"\n            type=\"task\"\n            text=\"今日暂无巡视任务\"\n            size=\"medium\"\n            :containerStyle=\"{ padding: '30rpx 0', backgroundColor: '#fff' }\"\n          ></p-empty-state>\n\n          <!-- 任务列表 -->\n          <scroll-view v-else scroll-y class=\"task-drawer__list\">\n            <view class=\"task-drawer__list-content\">\n              <p-task-card\n                v-for=\"(task, index) in taskList\"\n                :key=\"index\"\n                :task=\"task\"\n                :shift=\"getTaskShift(task)\"\n                :round-info=\"getTaskRoundInfo(task)\"\n                :active=\"activeTaskId === task._id\"\n                :user-map=\"userMap\"\n                :selected-round-number=\"selectedRound && selectedRound.taskId === task._id ? selectedRound.round.round : -1\"\n                @click=\"onTaskClick(task)\"\n                @continue=\"onTaskContinue(task)\"\n                @view-detail=\"onTaskViewDetail(task)\"\n                @select-round=\"onRoundSelected\"\n              >\n              </p-task-card>\n            </view>\n          </scroll-view>\n        </scroll-view>\n      </view>\n    </view>\n\n    <!-- 加载中 -->\n    <uni-popup ref=\"loadingPopup\" type=\"center\" mask-click=\"false\">\n      <uni-load-more status=\"loading\" :content-text=\"{ contentdown: '加载中' }\"></uni-load-more>\n    </uni-popup>\n\n    <!-- 消息提示 -->\n    <uni-popup ref=\"messagePopup\" type=\"message\">\n      <uni-popup-message type=\"error\" :message=\"errorMessage\" :duration=\"2000\"></uni-popup-message>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport PMap from '@/components/patrol/p-map.vue';\nimport PWeather from '@/components/patrol/p-weather.vue';\nimport PTaskCard from '@/components/patrol/p-task-card.vue';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\nimport { getCurrentLocation } from '@/utils/location-services.js';\nimport geocoderService from '@/utils/geocoder.js';\nimport patrolApi from '@/utils/patrol-api.js'; // 需要创建这个API工具文件\nimport { formatDate, calculateRoundTime, calculateEndTime, isToday } from '@/utils/date.js';\n\n// 统一的轮次排序函数，与任务列表页保持一致\nfunction sortRounds(rounds) {\n  if (!rounds || !Array.isArray(rounds) || rounds.length === 0) {\n    return [];\n  }\n\n  // 使用与任务列表页相同的排序逻辑\n  // 按状态分组：未开始和进行中的轮次，已完成和已超时的轮次\n  const notStartedOrActive = rounds.filter(\n    round => round.status === 0 || round.status === 1\n  );\n  const completedOrExpired = rounds.filter(\n    round => round.status === 2 || round.status === 3\n  );\n\n  // 分别排序\n  notStartedOrActive.sort((a, b) => a.round - b.round); // 按轮次号升序\n  completedOrExpired.sort((a, b) => b.round - a.round); // 按轮次号降序\n\n  // 合并排序后的数组\n  return [...notStartedOrActive, ...completedOrExpired];\n}\n\nexport default {\n  components: {\n    PMap,\n    PWeather,\n    PTaskCard,\n    PEmptyState\n  },\n  data() {\n    return {\n      // 用户位置\n      userLocation: null,\n\n      // 当前激活的任务ID\n      activeTaskId: '',\n\n      // 当前选中的任务\n      currentTask: null,\n\n      // 当前选择的轮次索引，-1表示自动选择（当前/最后一轮）\n      selectedRoundIndex: -1,\n\n      // 当前选择的轮次，null表示自动选择\n      selectedRound: null,\n\n      // 任务列表\n      taskList: [],\n\n      // 任务轮次信息\n      taskRoundInfos: {},\n\n      // 任务班次信息\n      taskShifts: {},\n\n      // 路线缓存\n      routeCache: {},\n\n      // 地图标记点\n      markers: [],\n\n      // 地图路线\n      polylines: [],\n\n      // 地图圆形区域\n      circles: [],\n\n      // 地图中心点\n      mapCenter: {\n        latitude: 30.0,\n        longitude: 120.0\n      },\n\n      // 天气信息\n      weatherInfo: null,\n\n      // 加载状态\n      isLoading: false,\n\n      // 初始加载标志，防止状态跳变\n      isInitialLoading: false,\n\n      // 下拉刷新状态\n      isRefreshing: false,\n\n      // 空状态\n      isEmpty: false,\n\n      // 过滤状态\n      filterStatus: 'all', // 'all', 'active', 'completed'\n\n      // 显示地图\n      showMap: true,\n\n      // 位置信息\n      currentLocation: {\n        latitude: 0,\n        longitude: 0,\n        accuracy: 0,\n        altitude: 0,\n        speed: 0,\n        address: '',\n        isLocating: false,\n        lastUpdated: 0\n      },\n\n      // UI状态\n      showFilterPanel: false,\n      showTaskDetail: false,\n      showWeather: true,\n\n      // 轮次倒计时更新间隔（毫秒）\n      roundUpdateInterval: 3000, // 从5秒减少到3秒，进一步提高状态更新响应速度\n\n      // 用户数据映射\n      userMap: {},\n\n      // 控制任务选择器是否展开\n      isTaskSelectorExpanded: false,\n\n      // 控制侧边抽屉是否打开\n      isDrawerOpen: false,\n\n      // 标志，防止重复显示登录提示\n      isShowingLoginTip: false,\n\n      touchStartY: 0,\n      touchMoveY: 0,\n      drawerHeight: 0,\n      isDragging: false,\n      currentTranslateY: 0,\n      drawerStyle: {},\n      maskStyle: {},\n      lastTouchTime: 0,\n      lastDeltaY: 0,\n\n      // 🔥 新增：防抖机制相关变量\n      lastLoadTime: 0,\n      loadingInProgress: false,\n      hasInitialLoaded: false,\n    };\n  },\n  computed: {\n    // 当前日期 YYYY-MM-DD\n    currentDate() {\n      // 使用formatDate工具函数替代手动格式化\n      return formatDate(new Date(), 'YYYY-MM-DD');\n    },\n    // 地图高度\n    mapHeight() {\n      // 使用固定高度，不再根据底部区域变化\n      return '100vh';\n    },\n    // 当前任务的点位\n    currentRoutePoints() {\n      if (!this.currentTask) return [];\n\n      // 获取当前任务的点位信息 - 优先使用rounds_detail\n      if (this.currentTask.rounds_detail && Array.isArray(this.currentTask.rounds_detail)) {\n        // 获取当前轮次\n        const taskId = this.currentTask._id;\n        let round = null;\n\n        // 如果用户选择了特定轮次，使用选中的轮次\n        if (this.selectedRound && this.selectedRound.taskId === taskId) {\n          round = this.selectedRound.round;\n        }\n        // 否则使用系统自动判断的轮次\n        else {\n          const roundInfo = this.taskRoundInfos[taskId];\n\n          if (roundInfo && roundInfo.status === 'active' && roundInfo.currentRound) {\n            round = roundInfo.currentRound;\n          } else if (roundInfo && roundInfo.status === 'completed' && roundInfo.lastRound) {\n            round = roundInfo.lastRound;\n          } else if (roundInfo && roundInfo.status === 'waiting' && roundInfo.nextRound) {\n            round = roundInfo.nextRound;\n          } else {\n            // 如果没有特定轮次，使用第一个轮次\n            const rounds = sortRounds(this.currentTask.rounds_detail);\n            round = rounds.length > 0 ? rounds[0] : null;\n          }\n        }\n\n        // 使用轮次中的points数据，确保包含完整的状态信息\n        if (round && round.points && Array.isArray(round.points)) {\n          return round.points;\n        }\n      }\n\n      // 如果没有轮次数据，尝试从route_detail获取点位\n      if (this.currentTask.route_detail && this.currentTask.route_detail.points) {\n        return this.currentTask.route_detail.points;\n      }\n\n      return [];\n    },\n    // 当前任务的轮次列表\n    availableRounds() {\n      if (!this.currentTask || !this.currentTask.rounds_detail || !Array.isArray(this.currentTask.rounds_detail)) {\n        return [];\n      }\n\n      return sortRounds(this.currentTask.rounds_detail);\n    },\n\n    // 当前轮次信息\n    currentRoundInfo() {\n      if (!this.currentTask) {\n        return { status: 'no_task' };\n      }\n\n      const task = this.currentTask;\n      return this.getTaskRoundInfo(task);\n    },\n  },\n  onLoad(options) {\n    // 初始化位置和任务\n    this.initLocationAndTasks();\n\n    // 开始更新轮次倒计时\n    this.startRoundUpdateTimer();\n\n    // 添加页面事件监听 - 确保事件名称一致性\n    uni.$on('refresh-task-list', this.refreshTaskList);\n\n    // 注册多种可能的任务更新事件，确保能接收到更新消息\n    uni.$on('task-updated', this.handleTaskUpdated);\n    uni.$on('patrol-task-updated', this.handleTaskUpdated);\n    uni.$on('check-in-completed', this.handleTaskUpdated);\n  },\n  onUnload() {\n    // 取消页面事件监听\n    uni.$off('refresh-task-list', this.refreshTaskList);\n    uni.$off('task-updated', this.handleTaskUpdated);\n    uni.$off('patrol-task-updated', this.handleTaskUpdated);\n    uni.$off('check-in-completed', this.handleTaskUpdated);\n\n    // 清除轮次更新定时器\n    this.clearRoundUpdateTimer();\n\n    // 停止位置监听\n    this.stopLocationWatch();\n  },\n  onShow() {\n    if (this.isOnboarding) {\n      // 如果正在进行引导，则不执行刷新任务\n      this.isOnboarding = false;\n    } else if (!this.hasInitialLoaded) {\n      // 🔥 首次显示时不刷新，因为 onLoad 已经加载了\n      this.hasInitialLoaded = true;\n      console.log('首次显示页面，跳过刷新（onLoad已处理）');\n    } else {\n      // 🔥 只有从其他页面返回时才刷新\n      console.log('从其他页面返回，执行刷新');\n      // 设置缓冲标记，防止状态快速跳变 - 保持这个标记\n      this.isInitialLoading = true;\n\n      // 每次显示页面时都刷新任务列表，确保数据最新\n      this.refreshTaskList(false); // 使用静默刷新，避免每次都显示加载提示\n\n      // 检查是否从打卡页面返回并有需要刷新的任务ID\n      if (getApp().globalData && getApp().globalData.checkedInTaskId) {\n        const refreshTaskId = getApp().globalData.checkedInTaskId;\n        // 清除标记\n        getApp().globalData.checkedInTaskId = null;\n\n        console.log('检测到打卡页面返回，刷新任务:', refreshTaskId);\n\n        // 标记任务需要刷新，并延迟执行刷新，避免过于频繁的操作\n        // 简单的刷新列表通常能覆盖大部分场景\n        // 如果问题依然存在，再考虑更精细化的处理\n\n        // 延迟清除缓冲标记，给列表刷新留出时间\n        setTimeout(() => {\n          this.isInitialLoading = false;\n        }, 800); // 稍微延长缓冲时间\n\n      } else {\n        // 没有特定任务需要刷新，延迟清除缓冲标记\n        setTimeout(() => {\n          this.isInitialLoading = false;\n        }, 800); // 稍微延长缓冲时间\n      }\n    }\n\n    // 重要：每次页面显示都重新启动位置监听\n    // 这样可以确保从其他页面返回(特别是使用手机手势返回)时位置监听能被正确启动\n    console.log('页面显示，重新启动位置监听');\n    setTimeout(() => {\n      this.restartLocationWatch();\n    }, 300); // 稍微延迟启动，避免与其他操作冲突\n  },\n  methods: {\n    // GPS精度颜色判断方法\n    getAccuracyColor() {\n        const accuracy = this.currentLocation.accuracy;\n        if (!accuracy) return '#999999';\n        if (accuracy <= 5) return '#34C759';    // 绿色 - 精度极好\n        if (accuracy <= 10) return '#00C58E';    // 青色 - 精度良好\n        if (accuracy <= 15) return '#FFD60A';   // 黄色 - 精度一般\n        if (accuracy <= 20) return '#FF9500';   // 橙色 - 精度较差\n        if (accuracy <= 25) return '#FF6B2C';   // 深橙色 - 精度很差\n        return '#FF3B30';                       // 红色 - 精度极差\n    },\n\n    // 初始化位置和任务\n    async initLocationAndTasks() {\n      try {\n        // 立即设置加载状态\n        this.isLoading = true;\n\n        // 获取位置信息\n        await this.initLocation();\n\n        // 加载用户数据\n        await this.loadUsers();\n\n        // 加载任务列表\n        await this.loadTaskList(true);\n      } catch (error) {\n        console.error('初始化位置和任务出错：', error);\n        uni.showToast({\n          title: '加载任务失败，请重试',\n          icon: 'none'\n        });\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    // 初始化位置信息\n    async initLocation() {\n      try {\n        this.isLoading = true;\n\n        // 获取当前位置\n        const location = await getCurrentLocation({\n          isHighAccuracy: true,  // 请求高精度定位\n          highAccuracyExpireTime: 4000  // 高精度定位超时时间\n        });\n\n        // 更新用户位置和当前位置\n        this.userLocation = {\n          latitude: location.latitude,\n          longitude: location.longitude\n        };\n\n        // 确保包含完整的位置信息\n        this.currentLocation = {\n          latitude: location.latitude,\n          longitude: location.longitude,\n          accuracy: location.accuracy || 0,\n          altitude: location.altitude || 0,\n          speed: location.speed || 0,\n          address: '',\n          isLocating: false,\n          lastUpdated: Date.now()\n        };\n\n        // 设置地图中心\n        this.mapCenter = {\n          latitude: location.latitude,\n          longitude: location.longitude\n        };\n\n        // 先更新当前位置的范围圈\n        this.updateCurrentLocationMarker();\n\n        // 如果有任务，重新构建任务标记\n        if (this.currentTask) {\n          this.buildTaskMarkers();\n        }\n\n        // 获取地址信息\n        try {\n          const geocodeResult = await geocoderService.reverseGeocoder(this.currentLocation);\n          this.currentLocation.address = geocodeResult.address || '';\n          const district = geocodeResult.address_component?.district || '';\n          uni.setNavigationBarTitle({\n            title: `巡视打卡 - ${district}`\n          });\n        } catch (error) {\n          console.error('获取地址信息失败', error);\n        }\n\n        // 开始监听位置变化\n        this.startLocationWatch();\n      } catch (error) {\n        console.error('初始化位置失败', error);\n        uni.showToast({\n          title: '获取位置失败，请检查定位权限',\n          icon: 'none'\n        });\n      } finally {\n        setTimeout(() => {\n          this.isLoading = false;\n        }, 200);\n      }\n    },\n\n    // 开始监听位置变化\n    startLocationWatch() {\n      console.log('开始位置监听');\n\n      if (this.locationChangeListener) {\n        console.log('位置监听已存在，不需要重新启动');\n        return;\n      }\n\n      try {\n        // 使用微信原生API直接监听位置变化\n        wx.startLocationUpdate({\n          type: 'gcj02',\n          success: () => {\n            console.log('位置监听启动成功');\n\n            // 设置位置变化回调\n            wx.onLocationChange((res) => {\n              // 保存当前的circles数组\n              const currentCircles = [...this.circles];\n\n              // 更新当前位置信息\n              this.currentLocation = {\n                latitude: res.latitude,\n                longitude: res.longitude,\n                accuracy: res.accuracy || 0,\n                altitude: res.altitude || 0,\n                speed: res.speed || 0,\n                isLocating: false,\n                lastUpdated: Date.now()\n              };\n\n              // 更新用户位置\n              this.userLocation = {\n                latitude: res.latitude,\n                longitude: res.longitude\n              };\n\n              // 恢复circles数组并更新位置标记\n              this.circles = currentCircles;\n              this.updateCurrentLocationMarker();\n            });\n\n            // 标记位置监听已启动\n            this.locationChangeListener = true;\n          },\n          fail: (err) => {\n            console.error('启动位置监听失败:', err);\n            this.locationChangeListener = false;\n          }\n        });\n      } catch (err) {\n        console.error('启动位置监听出错:', err);\n        this.locationChangeListener = false;\n      }\n    },\n\n    // 停止位置监听\n    stopLocationWatch() {\n      console.log('停止位置监听');\n\n      if (!this.locationChangeListener) {\n        console.log('没有活跃的位置监听，无需停止');\n        return;\n      }\n\n      try {\n        // 使用微信原生API停止位置监听\n        wx.stopLocationUpdate({\n          success: () => {\n            console.log('停止位置监听成功');\n          },\n          fail: (err) => {\n            console.error('停止位置监听失败:', err);\n          },\n          complete: () => {\n            try {\n              // 无论成功失败，都尝试解绑监听器\n              wx.offLocationChange();\n            } catch (e) {\n              console.error('解除位置监听绑定失败:', e);\n            }\n            // 重置监听标志\n            this.locationChangeListener = false;\n          }\n        });\n      } catch (err) {\n        console.error('停止位置监听出错:', err);\n        // 确保标志被重置\n        this.locationChangeListener = false;\n      }\n    },\n\n    // 重新启动位置监听（先停止再启动）\n    restartLocationWatch() {\n      try {\n        // 无论当前状态如何，先尝试停止现有监听\n        this.forceStopLocationWatch();\n\n        // 短暂延迟后启动新的监听\n        setTimeout(() => {\n          this.startLocationWatch();\n        }, 200);\n      } catch (err) {\n        console.error('重启位置监听出错:', err);\n      }\n    },\n\n    // 强制停止位置监听（不检查状态）\n    forceStopLocationWatch() {\n      try {\n        // 使用微信原生API停止和解绑，无论当前状态如何\n        wx.stopLocationUpdate();\n        wx.offLocationChange();\n        this.locationChangeListener = false;\n      } catch (err) {\n        console.error('强制停止位置监听出错:', err);\n        this.locationChangeListener = false;\n      }\n    },\n\n    // 移动到当前位置\n    async moveToCurrentLocation() {\n      try {\n        // 显示加载中提示\n        uni.showLoading({\n          title: '获取位置中...',\n          mask: true\n        });\n\n        // 使用微信原生API获取高精度位置\n        const res = await new Promise((resolve, reject) => {\n          wx.getLocation({\n            type: 'gcj02',\n            isHighAccuracy: true,\n            highAccuracyExpireTime: 5000,\n            success: resolve,\n            fail: reject\n          });\n        });\n\n        // 更新用户位置\n        this.userLocation = {\n          latitude: res.latitude,\n          longitude: res.longitude\n        };\n\n        // 更新地图中心点\n        this.mapCenter = {\n          latitude: res.latitude,\n          longitude: res.longitude\n        };\n\n        // 如果地图组件存在，移动到新位置\n        if (this.$refs.patrolMap) {\n          this.$refs.patrolMap.moveToLocation(this.userLocation);\n        }\n\n        // 隐藏加载提示\n        uni.hideLoading();\n      } catch (error) {\n        console.error('获取位置失败:', error);\n\n        // 如果获取位置失败，尝试使用现有位置\n        if (this.userLocation && this.userLocation.latitude && this.userLocation.longitude) {\n          // 更新地图中心点\n          this.mapCenter = {\n            latitude: this.userLocation.latitude,\n            longitude: this.userLocation.longitude\n          };\n\n          // 如果地图组件存在，移动到现有位置\n          if (this.$refs.patrolMap) {\n            this.$refs.patrolMap.moveToLocation(this.userLocation);\n          }\n        } else {\n          // 如果没有位置信息，重新初始化位置\n          this.initLocation();\n        }\n\n        // 隐藏加载提示\n        uni.hideLoading();\n      }\n    },\n\n    // 加载任务列表\n    async loadTaskList(showLoading = false) {\n      // 再次检查登录状态\n      if (!this.checkLoginState()) {\n        return;\n      }\n\n      try {\n        // 设置初始加载标志，防止状态跳变\n        this.isInitialLoading = true;\n\n        if (showLoading) {\n          this.showLoading(true);\n        }\n\n        // 获取当前用户ID\n        let userId = this.getCurrentUserId();\n        if (!userId) {\n          this.showLoginTip();\n          if (showLoading) {\n            this.showLoading(false);\n          }\n          return;\n        }\n\n        // 统一使用PatrolApi.call标准格式获取今日任务，与任务列表页保持一致\n        const res = await patrolApi.call({\n          name: 'patrol-task',\n          action: 'getTaskList',\n          data: {\n            level: 'list',  // 🔥 启用轻量级模式，减少RU消耗\n            fields: '_id,name,status,area,patrol_date,shift_id,shift_name,user_id,user_name,route_name,create_date,overall_stats,rounds_detail.round,rounds_detail.name,rounds_detail.status,rounds_detail.start_time,rounds_detail.end_time,rounds_detail.duration,rounds_detail.stats',  // 🔥 字段过滤，排除points数组\n            // 🔥 新增：强制个人模式，巡视首页只显示自己的任务\n            viewScope: 'personal',\n            params: {\n              patrolDate: this.currentDate,\n              pageSize: 100,\n              status: -1  // 全部状态\n              // 🔥 移除硬编码userId，改为使用viewScope控制\n            }\n          }\n        });\n\n        if (res.code === 0 && res.data) {\n          // 获取原始任务列表\n          let rawTaskList = res.data.list || [];\n\n          // 处理任务数据，与任务列表页相同的处理逻辑\n          let taskList = this.processTasks(rawTaskList);\n\n          // 明确过滤只显示分配给当前用户的任务\n          if (userId) {\n            taskList = taskList.filter(task => task.user_id === userId);\n          }\n\n          // 预加载任务关联的班次信息\n          await this.preloadTaskShifts(taskList);\n\n          // 更新每个任务的轮次信息\n          this.updateTasksRoundInfo(taskList);\n\n          // 处理空状态\n          this.isEmpty = taskList.length === 0;\n\n          // 暂存新任务列表，但不立即更新视图\n          // 这个延迟将防止任务状态在初始加载时跳变\n          const newTaskList = [...taskList];\n\n          // 如果已选中任务不在新列表中，清除选择\n          if (this.activeTaskId && !newTaskList.some(task => task._id === this.activeTaskId)) {\n            this.activeTaskId = '';\n            this.currentTask = null;\n          }\n\n          // 在完成所有准备工作后，一次性更新taskList\n          this.$nextTick(() => {\n            // 使用整体替换方式更新任务列表，避免Vue部分更新导致的视图问题\n            this.taskList = newTaskList;\n\n            // 如果任务列表不为空，且没有选中任务，自动选中第一个任务\n            if (this.taskList.length > 0 && !this.activeTaskId) {\n              this.activeTaskId = this.taskList[0]._id;\n              this.loadTaskDetail(this.activeTaskId);\n            }\n            // 如果有活动任务ID但未加载任务详情\n            else if (this.activeTaskId && !this.currentTask) {\n              this.loadTaskDetail(this.activeTaskId);\n            }\n\n            // 清除初始加载标志\n            this.isInitialLoading = false;\n          });\n        } else if (res.code === 401) {\n          // 未登录状态\n          this.taskList = [];\n          this.isEmpty = true;\n          this.showLoginTip();\n          this.isInitialLoading = false;\n        } else {\n          this.showError(res.message || '获取任务列表失败');\n          this.isInitialLoading = false;\n        }\n      } catch (error) {\n        console.error('加载任务列表失败', error);\n        this.showError('加载任务列表失败');\n        this.isInitialLoading = false;\n      } finally {\n        this.showLoading(false);\n      }\n    },\n\n    // 获取当前用户ID方法\n    getCurrentUserId() {\n      try {\n        // 1. 首先尝试从uni-id-pages-userInfo获取\n        const userInfo = uni.getStorageSync('uni-id-pages-userInfo');\n        if (userInfo) {\n          if (typeof userInfo === 'object') {\n            return userInfo._id;\n          }\n\n          if (typeof userInfo === 'string') {\n            try {\n              const parsed = JSON.parse(userInfo);\n              return parsed._id;\n            } catch (e) {\n              console.error('解析用户信息失败:', e);\n            }\n          }\n        }\n\n        // 2. 尝试从token获取\n        const tokenInfo = uni.getStorageSync('uni_id_token');\n        if (tokenInfo) {\n          if (typeof tokenInfo === 'object' && tokenInfo.uid) {\n            return tokenInfo.uid;\n          }\n\n          if (typeof tokenInfo === 'string') {\n            try {\n              const parsed = JSON.parse(tokenInfo);\n              return parsed.uid;\n            } catch (e) {\n              // 错误处理已简化\n            }\n          }\n        }\n\n        // 3. 尝试从其他存储位置获取\n        return uni.getStorageSync('uni_id_user_id') || uni.getStorageSync('uid');\n      } catch (e) {\n        console.error('获取用户ID失败');\n        return null;\n      }\n    },\n\n    // 修改预加载任务关联的班次信息方法\n    async preloadTaskShifts(tasks) {\n      const shiftPromises = [];\n      const loadedShiftIds = [];\n\n      // 收集所有需要加载的班次ID\n      tasks.forEach(task => {\n        if (task.shift_id && !this.taskShifts[task.shift_id] && !loadedShiftIds.includes(task.shift_id)) {\n          loadedShiftIds.push(task.shift_id);\n\n          // 加载班次详情，使用PatrolApi.call标准格式\n          const promise = patrolApi.call({\n            name: 'patrol-shift',\n            action: 'getShiftDetail',\n            data: {\n              shift_id: task.shift_id,\n              userId: this.getCurrentUserId()\n            }\n          })\n            .then(res => {\n              if (res.code === 0 && res.data) {\n                this.taskShifts[task.shift_id] = res.data;\n\n                // 更新任务轮次信息\n                this.updateTaskRoundInfo(task);\n              }\n            })\n            .catch(err => {\n              console.error(`加载班次信息失败: ${task.shift_id}`, err);\n            });\n\n          shiftPromises.push(promise);\n        } else if (task.shift_id && this.taskShifts[task.shift_id]) {\n          // 已加载过的班次，只更新轮次信息\n          this.updateTaskRoundInfo(task);\n        }\n      });\n\n      // 等待所有班次加载完成\n      if (shiftPromises.length > 0) {\n        await Promise.all(shiftPromises);\n      }\n    },\n\n    // 更新多个任务的轮次信息\n    updateTasksRoundInfo(tasks) {\n      if (!Array.isArray(tasks)) return;\n\n      const now = new Date(); // 获取当前时间一次，避免多次获取造成轻微差异\n\n      // 遍历每个任务，更新轮次信息\n      tasks.forEach(task => {\n        this.updateTaskRoundInfo(task, now);\n      });\n    },\n\n    // 更新任务轮次信息\n    updateTaskRoundInfo(task, currentTime) {\n      if (!task || !task.shift_id || !this.taskShifts[task.shift_id]) return;\n\n      // 使用传入的当前时间或获取新的当前时间\n      currentTime = currentTime || new Date();\n\n      const shift = this.taskShifts[task.shift_id];\n\n      // 获取当前轮次信息\n      try {\n        const roundInfo = this.calculateCurrentRound(task, shift, currentTime);\n\n        // 直接使用当前时间更新timeElapsed，确保数据是最新的\n        if (roundInfo.status === 'active') {\n          const currentRound = roundInfo.currentRound;\n          if (currentRound && currentRound.start_time) {\n            const startTime = new Date(currentRound.start_time.replace(/-/g, '/'));\n            roundInfo.timeElapsed = currentTime.getTime() - startTime.getTime();\n          }\n        }\n\n        // 保留之前的状态，方便比较变化\n        const prevRoundInfo = this.taskRoundInfos[task._id];\n\n        // 保存当前计算的状态\n        this.taskRoundInfos[task._id] = roundInfo;\n\n        // 如果状态发生变化，记录日志\n        if (prevRoundInfo && prevRoundInfo.status !== roundInfo.status) {\n          console.log(`任务[${task._id}]轮次状态从 ${prevRoundInfo.status} 变为 ${roundInfo.status}`);\n        }\n\n        // 如果任务有rounds_detail，按照状态进行排序\n        if (task.rounds_detail && Array.isArray(task.rounds_detail) && task.rounds_detail.length > 0) {\n          // 先深拷贝rounds_detail以避免直接修改原始数据\n          const rounds = task.rounds_detail.map(round => ({...round}));\n\n          // 确保每个轮次都有status字段\n          for (let i = 0; i < rounds.length; i++) {\n            // 保留已确定的状态，避免不必要的状态变化\n            if (rounds[i].status === 2 || rounds[i].status === 3) {\n              // 保持已完成或已超时状态\n              continue;\n            }\n\n            if (rounds[i].status === undefined) {\n              // 如果没有status，尝试根据时间计算\n              const roundStartTime = new Date(rounds[i].start_time.replace(/-/g, '/'));\n              const roundEndTime = new Date(rounds[i].end_time.replace(/-/g, '/'));\n              const now = new Date();\n\n              if (now < roundStartTime) {\n                rounds[i].status = 0; // 未开始\n              } else if (now > roundEndTime) {\n                // 检查点位完成情况\n                const allPointsCompleted = this.areAllPointsCompleted(task, rounds[i]);\n                rounds[i].status = allPointsCompleted ? 2 : 3; // 已完成或已超时\n              } else {\n                // 检查点位完成情况\n                const allPointsCompleted = this.areAllPointsCompleted(task, rounds[i]);\n                rounds[i].status = allPointsCompleted ? 2 : 1; // 已完成或进行中\n              }\n            }\n          }\n\n          // 分组：未开始和进行中按升序，已完成和超时按降序\n          const notStartedOrActive = rounds.filter(round => round.status === 0 || round.status === 1);\n          const completedOrExpired = rounds.filter(round => round.status === 2 || round.status === 3);\n\n          notStartedOrActive.sort((a, b) => a.round - b.round);\n          completedOrExpired.sort((a, b) => b.round - a.round);\n\n          // 更新任务的rounds_detail\n          task.rounds_detail = [...notStartedOrActive, ...completedOrExpired];\n        }\n      } catch (error) {\n        console.error('更新任务轮次信息失败', error);\n      }\n    },\n\n    // 获取任务关联的班次信息\n    getTaskShift(task) {\n      if (!task || !task.shift_id) return null;\n      return this.taskShifts[task.shift_id] || null;\n    },\n\n    // 获取任务的轮次信息\n    getTaskRoundInfo(task) {\n      // 检查缓存\n      if (this.taskRoundInfos[task._id]) {\n        return this.taskRoundInfos[task._id];\n      }\n\n      const currentTime = new Date();\n      let roundInfo = {};\n\n      // 使用新数据结构\n      if (task.rounds_detail && Array.isArray(task.rounds_detail) && task.rounds_detail.length > 0) {\n        roundInfo = this.calculateCurrentRound(task, null, currentTime);\n      } else {\n        // 获取任务关联的班次信息\n        const shift = this.getTaskShift(task);\n        if (!shift) {\n          return null;\n        }\n\n        // 计算当前轮次状态\n        roundInfo = this.calculateCurrentRound(task, shift, currentTime);\n      }\n\n      // 添加到缓存\n      this.taskRoundInfos[task._id] = roundInfo;\n\n      return roundInfo;\n    },\n\n    // 判断是否为当前激活任务\n    isActiveTask(task) {\n      return this.activeTaskId === task._id;\n    },\n\n    // 计算当前轮次状态\n    calculateCurrentRound(task, shift, currentTime = new Date()) {\n      // 使用新数据结构\n      if (task.rounds_detail && Array.isArray(task.rounds_detail) && task.rounds_detail.length > 0) {\n        // 使用排序函数对轮次进行排序\n        const sortedRounds = sortRounds(task.rounds_detail);\n\n        // 查找当前时间所在的轮次\n        for (let i = 0; i < sortedRounds.length; i++) {\n          const round = sortedRounds[i];\n\n          // 获取任务基准日期\n          const taskDate = task.patrol_date || task.create_date || new Date();\n\n          // 使用工具函数正确处理轮次时间，包括时区和day_offset\n          const startTime = calculateRoundTime(taskDate, round.start_time, round.day_offset || 0);\n          // 计算结束时间，使用轮次持续时间或默认60分钟\n          const endTime = calculateEndTime(startTime, round.duration || 60);\n\n          // 轮次为毫秒时间戳\n          const startMs = startTime.getTime();\n          const endMs = endTime.getTime();\n          const currentMs = currentTime.getTime();\n\n          // 判断当前时间与轮次时间的关系\n          if (currentMs < startMs) {\n            // 当前时间早于轮次开始时间，表示正在等待该轮次\n            return {\n              status: 'waiting',\n              nextRound: round,\n              nextRoundIndex: i,\n              timeUntilNext: startMs - currentMs,\n              isCountdown: true // 标记为倒计时模式\n            };\n          } else if (currentMs >= startMs && currentMs <= endMs) {\n            // 当前时间在轮次的时间范围内，表示这个轮次正在进行中\n            // 增强：检查点位完成情况\n            const allPointsCompleted = this.areAllPointsCompleted(task, round);\n\n            // 如果所有点位已完成，虽然在时间范围内，但状态应该是已完成\n            if (allPointsCompleted) {\n              return {\n                status: 'completed',\n                currentRound: round,\n                currentRoundIndex: i,\n                isEarlyCompletion: true, // 提前完成标记\n                timeRemaining: endMs - currentMs,\n                isCountdown: false, // 标记为非倒计时模式\n                totalTime: endMs - startMs // 总时长\n              };\n            }\n\n            return {\n              status: 'active',\n              currentRound: round,\n              currentRoundIndex: i,\n              timeRemaining: endMs - currentMs,\n              timeElapsed: currentMs - startMs,\n              totalTime: endMs - startMs,\n              completionPercentage: (currentMs - startMs) / (endMs - startMs),\n              isCountdown: false // 标记为非倒计时模式\n            };\n          }\n        }\n\n        // 如果所有轮次都已经过去，需要检查最后一个轮次是否完成\n        const lastRound = sortedRounds[sortedRounds.length - 1];\n        const allPointsCompletedInLastRound = this.areAllPointsCompleted(task, lastRound);\n\n        return {\n          status: allPointsCompletedInLastRound ? 'completed' : 'expired', // 增强：区分完成和超时\n          lastRound: lastRound,\n          lastRoundIndex: sortedRounds.length - 1,\n          isCompleted: allPointsCompletedInLastRound\n        };\n      }\n\n      // 如果没有新数据结构，回退到使用旧数据结构\n      if (!shift || !shift.rounds || shift.rounds.length === 0) {\n        return { status: 'no_rounds' };\n      }\n\n      const enabledRounds = shift.rounds;\n      const currentMs = currentTime.getTime();\n\n      // 按时间排序\n      enabledRounds.sort((a, b) => {\n        const aTimeStr = a.start_time || a.time;\n        const bTimeStr = b.start_time || b.time;\n\n        // 转换为天数中的分钟数\n        const [aHour, aMinute] = aTimeStr.split(':').map(Number);\n        const [bHour, bMinute] = bTimeStr.split(':').map(Number);\n\n        // 按时间排序\n        return (aHour * 60 + aMinute) - (bHour * 60 + bMinute);\n      });\n\n      // 获取任务日期（YYYY-MM-DD格式）\n      const taskDate = task.patrol_date || task.date;\n\n      // 判断是否是跨天班次\n      const isShiftAcrossDay = shift.across_day || shift.end_time < shift.start_time;\n\n      // 遍历轮次\n      for (let i = 0; i < enabledRounds.length; i++) {\n        const round = enabledRounds[i];\n\n        // 获取轮次时间\n        const roundTimeStr = round.start_time || round.time;\n        const roundEndTimeStr = round.end_time || (i < enabledRounds.length - 1 ?\n                             (enabledRounds[i+1].start_time || enabledRounds[i+1].time) :\n                             shift.end_time);\n\n        // 使用工具函数正确处理轮次时间\n        const startTime = calculateRoundTime(taskDate, roundTimeStr);\n\n        // 计算结束时间，使用轮次持续时间或默认60分钟\n        const duration = round.duration || 60;\n        const endTime = calculateEndTime(startTime, duration);\n\n        // 处理跨天情况\n        if (isShiftAcrossDay) {\n          // 获取开始和结束时间的小时数\n          const startHour = startTime.getHours();\n          const endHour = endTime.getHours();\n\n          // 如果结束时间小于开始时间，说明跨天\n          if (endHour < startHour || roundEndTimeStr < roundTimeStr) {\n            endTime.setDate(endTime.getDate() + 1);\n          }\n        }\n\n        // 轮次为毫秒时间戳\n        const startMs = startTime.getTime();\n        const endMs = endTime.getTime();\n\n        // 判断当前时间与轮次时间的关系\n        if (currentMs < startMs) {\n          // 当前时间早于轮次开始时间，表示正在等待该轮次\n          return {\n            status: 'waiting',\n            nextRound: round,\n            nextRoundIndex: i,\n            timeUntilNext: startMs - currentMs,\n            isCountdown: true // 标记为倒计时模式\n          };\n        } else if (currentMs >= startMs && currentMs <= endMs) {\n          // 当前时间在轮次的时间范围内，表示这个轮次正在进行中\n          return {\n            status: 'active',\n            currentRound: round,\n            currentRoundIndex: i,\n            timeRemaining: endMs - currentMs,\n            timeElapsed: currentMs - startMs,\n            totalTime: endMs - startMs,\n            completionPercentage: (currentMs - startMs) / (endMs - startMs),\n            isCountdown: false // 标记为非倒计时模式\n          };\n        }\n      }\n\n      // 如果所有轮次都已经过去，则表示所有轮次已完成\n      return {\n        status: 'completed',\n        lastRound: enabledRounds[enabledRounds.length - 1],\n        lastRoundIndex: enabledRounds.length - 1\n      };\n    },\n\n    // 添加判断轮次所有点位是否完成的辅助方法\n    areAllPointsCompleted(task, round) {\n      if (!round || !round.point_stats) return false;\n\n      // 如果point_stats中有完成信息，使用它判断\n      if (round.point_stats.total && round.point_stats.checked) {\n        return round.point_stats.checked >= round.point_stats.total;\n      }\n\n      // 如果没有point_stats或数据不完整，尝试使用round_records判断\n      if (task.round_records && Array.isArray(task.round_records)) {\n        // 查找对应轮次的记录\n        const roundRecord = task.round_records.find(r => r && r.round === round.round);\n\n        if (roundRecord) {\n          // 确保completed_points是数组\n          const completedPoints = Array.isArray(roundRecord.completed_points) ?\n                                 roundRecord.completed_points : [];\n\n          // 确保points是数组\n          const points = Array.isArray(round.points) ? round.points : [];\n\n          // 如果没有点位，返回false\n          if (points.length === 0) return false;\n\n          // 判断是否所有点位都已完成\n          return completedPoints.length >= points.length;\n        }\n      }\n\n      // 如果以上判断都无法执行，返回false\n      return false;\n    },\n\n    // 构建地图标记点\n    buildTaskMarkers() {\n      // 保存当前位置的范围圈\n      const locationCircle = this.circles.find(circle => \n        circle.strokeColor && (\n          circle.strokeColor === '#34C759' ||  // 精度极好 - 绿色\n          circle.strokeColor === '#00C58E' ||  // 精度良好 - 青色\n          circle.strokeColor === '#FFD60A' ||  // 精度一般 - 黄色\n          circle.strokeColor === '#FF9500' ||  // 精度较差 - 橙色\n          circle.strokeColor === '#FF6B2C' ||  // 精度很差 - 深橙色\n          circle.strokeColor === '#FF3B30'     // 精度极差 - 红色\n        )\n      );\n\n      // 清空当前标记点数组\n      this.markers = [];\n      this.polylines = [];\n      this.circles = []; // 清空范围圈数组\n\n      const task = this.currentTask;\n      if (!task) {\n        // 如果没有任务，但有位置范围圈，则保留位置范围圈\n        if (locationCircle) {\n          this.circles = [locationCircle];\n        }\n        console.warn('没有当前任务，无法构建标记点');\n        return;\n      }\n\n      // 获取当前任务的轮次信息\n      const taskId = task._id;\n      const roundInfo = this.taskRoundInfos[taskId];\n\n      if (!roundInfo) {\n        console.warn('没有轮次信息，无法构建标记点');\n        return;\n      }\n\n      // 获取当前轮次\n      let currentRound = null;\n\n      // 1. 如果有选中的轮次，优先使用\n      if (this.selectedRound && this.selectedRound.taskId === taskId) {\n        currentRound = task.rounds_detail.find(r => r.round === this.selectedRound.round.round);\n      }\n\n      // 2. 如果没有选中的轮次，查找进行中的轮次\n      if (!currentRound) {\n        currentRound = task.rounds_detail.find(r => r.status === 1);\n      }\n\n      // 3. 如果没有进行中的轮次，查找未开始的轮次\n      if (!currentRound) {\n        currentRound = task.rounds_detail.find(r => r.status === 0);\n      }\n\n      // 4. 如果还是没找到，使用最后一个轮次\n      if (!currentRound && task.rounds_detail && task.rounds_detail.length > 0) {\n        // 任务结束时，直接使用排序后的第一个轮次（最新完成的轮次）\n        currentRound = task.rounds_detail[0];\n      }\n\n      if (!currentRound) {\n        console.warn('没有找到可用的轮次');\n        return;\n      }\n\n      // 获取当前轮次的点位\n      let points = [];\n      if (currentRound.points && Array.isArray(currentRound.points)) {\n        points = currentRound.points;\n      } else if (task.route_detail && task.route_detail.points) {\n        points = task.route_detail.points;\n      }\n\n      if (points.length === 0) {\n        console.warn('没有可用的点位数据');\n        return;\n      }\n\n      // 确保点位按照顺序排序\n      const sortedPoints = [...points].sort((a, b) => {\n        if (a.order !== undefined && b.order !== undefined) {\n          return a.order - b.order;\n        }\n        return 0;\n      });\n\n      // 获取当前轮次记录\n      const roundRecord = (task.round_records || []).find(r => r.round === currentRound.round);\n      const completedPointIds = roundRecord ? roundRecord.completed_points || [] : [];\n\n      // 创建标记点和路线\n      const markers = [];\n      const coordinates = [];\n\n      // 创建所有点位的标记点\n      sortedPoints.forEach((point, index) => {\n        if (!point) {\n          console.error(`点位数据不完整，索引: ${index}`);\n          return;\n        }\n\n        // 提取点位ID\n        let pointId = point.point_id || point.id || point._id;\n        if (!pointId) {\n          console.error(`点位没有有效的ID，索引: ${index}`, point);\n          pointId = `point_${index}`;\n        }\n\n        // 提取经纬度\n        let latitude, longitude;\n\n        if (point.latitude && point.longitude) {\n          latitude = parseFloat(point.latitude);\n          longitude = parseFloat(point.longitude);\n        } else if (point.location && point.location.latitude && point.location.longitude) {\n          latitude = parseFloat(point.location.latitude);\n          longitude = parseFloat(point.location.longitude);\n        } else if (point.coordinates && Array.isArray(point.coordinates) && point.coordinates.length >= 2) {\n          longitude = parseFloat(point.coordinates[0]);\n          latitude = parseFloat(point.coordinates[1]);\n        } else if (point.position || point.lnglat) {\n          const posObj = point.position || point.lnglat;\n          if (posObj.lat || posObj.latitude) {\n            latitude = parseFloat(posObj.lat || posObj.latitude);\n          }\n          if (posObj.lng || posObj.lon || posObj.longitude) {\n            longitude = parseFloat(posObj.lng || posObj.lon || posObj.longitude);\n          }\n        }\n\n        if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) {\n          console.error(`点位没有有效的经纬度坐标，点位ID: ${pointId}`, point);\n          return;\n        }\n\n        // 检查坐标是否在有效范围内\n        if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {\n          console.error(`点位的经纬度坐标超出有效范围，点位ID: ${pointId}, 坐标: [${latitude}, ${longitude}]`);\n          return;\n        }\n\n        // 简化点位完成状态判断 - 直接使用点位自身属性\n        // 不再查询completedPointIds，除非必要\n        let isCompleted = false;\n\n        // 修改判断逻辑，确保状态为4的点位不会被标记为已完成\n        if (point.status === 4) {\n          // 状态为4的点位始终视为未完成，即使有record_id\n          isCompleted = false;\n        } else {\n          // 其他情况下使用常规判断\n          // 修复布尔表达式可能返回undefined的问题\n          isCompleted = !!(point.status === 1 ||\n                          point.status === 'completed' ||\n                          point.checkin_time ||\n                          point.record_id);\n        }\n\n        // 打印调试信息，帮助排查问题\n        // console.log(`点位状态检查：${point.name || `点位${index + 1}`}`, {\n        //   status: point.status,\n        //   hasCheckInTime: !!point.checkin_time,\n        //   hasRecordId: !!point.record_id,\n        //   isCompleted: isCompleted\n        // });\n\n        // 获取点位顺序号\n        const pointOrder = point.order || index + 1;\n\n        // 设置点位属性 - 使用不同图标区分完成状态\n        // 未打卡使用红色marker.png，已打卡使用map-pin.png\n        const iconPath = isCompleted ? '/static/map/map-pin.png' : '/static/map/marker.png';\n\n        // 创建标记点\n        const marker = {\n          id: index,\n          pointId: pointId,\n          latitude: latitude,\n          longitude: longitude,\n          iconPath: iconPath,\n          width: 32,\n          height: 32,\n          title: point.name || `点位${index + 1}`,\n          isCompleted: isCompleted,\n          callout: {\n            content: (point.name ? `${pointOrder}. ${point.name}` : `${pointOrder}. 点位${index + 1}`) + (isCompleted ? ' ✓' : ''),\n            color: '#FFFFFF',\n            fontSize: 12,\n            borderWidth: 0,\n            bgColor: isCompleted ? '#4CAF50' : '#3688FF',\n            padding: 5,\n            display: 'ALWAYS',\n            borderRadius: 4,\n            textAlign: 'center'\n          }\n        };\n\n        markers.push(marker);\n\n        // 添加坐标到路线数组\n        coordinates.push({\n          longitude: longitude,\n          latitude: latitude,\n          pointId: pointId, // 添加点位ID用于关联\n          order: index // 使用循环索引作为顺序\n        });\n\n        // 添加点位范围圈 - 使用数据库的范围值或默认50米半径\n        // 确保从点位数据读取正确的range值\n        let pointRange = 50; // 默认值\n\n        // 首先尝试从point对象获取range\n        if (point.range && !isNaN(parseFloat(point.range))) {\n          pointRange = parseFloat(point.range);\n        }\n        // 如果没有range属性，尝试从location对象内获取\n        else if (point.location && point.location.range && !isNaN(parseFloat(point.location.range))) {\n          pointRange = parseFloat(point.location.range);\n        }\n        // 如果没有range属性，尝试从其他可能的属性获取\n        else if (point.radius && !isNaN(parseFloat(point.radius))) {\n          pointRange = parseFloat(point.radius);\n        }\n\n        // 确保范围值是有效的正数\n        pointRange = pointRange > 0 ? pointRange : 50;\n\n        this.circles.push({\n          latitude: latitude,\n          longitude: longitude,\n          color: isCompleted ? '#52c41a88' : '#1677FF88', // 点位范围圈颜色（带透明度）\n          fillColor: isCompleted ? '#52c41a33' : '#1677FF33', // 点位范围填充色（更透明）\n          radius: pointRange,\n          strokeWidth: 2\n        });\n      });\n\n      // 创建路线 - 如果至少有两个点位\n      if (coordinates.length >= 2) {\n        // 确保路线坐标按顺序排列\n        const routeCoordinates = [...coordinates].sort((a, b) => a.order - b.order).map(coord => ({\n          latitude: coord.latitude,\n          longitude: coord.longitude\n        }));\n\n        // 创建路线 - 改为实线，不再添加距离计算\n        const polylinePoints = routeCoordinates;\n\n        const polyline = {\n          points: routeCoordinates,\n          color: '#1677FF',\n          width: 4, // 粗一点的实线\n          arrowLine: true,\n          dottedLine: false // 确保是实线\n        };\n\n        this.polylines = [polyline];\n      }\n\n      // 更新标记点 - 不包含距离标签\n      this.markers = markers;\n\n      // 设置地图中心点 - 使用第一个标记点或用户位置\n      if (markers.length > 0) {\n        this.mapCenter = {\n          latitude: markers[0].latitude,\n          longitude: markers[0].longitude\n        };\n      } else if (this.userLocation && this.userLocation.latitude && this.userLocation.longitude) {\n        this.mapCenter = {\n          latitude: this.userLocation.latitude,\n          longitude: this.userLocation.longitude\n        };\n      }\n\n      // 在最后添加位置范围圈\n      if (locationCircle) {\n        this.circles.push(locationCircle);\n      }\n      \n      // 确保更新当前位置标记\n      this.updateCurrentLocationMarker();\n    },\n\n    // 更新当前位置标记\n    updateCurrentLocationMarker() {\n        if (!this.currentLocation || !this.currentLocation.latitude || !this.currentLocation.longitude) {\n            return;\n        }\n\n        // 获取精度值\n        const accuracy = this.currentLocation.accuracy || 0;\n\n        // 根据精度决定圈的颜色\n        let circleColor, fillColor;\n        if (accuracy <= 5) {\n            circleColor = '#34C75988';    // 绿色 - 精度极好\n            fillColor = '#34C75933';\n        } else if (accuracy <= 10) {\n            circleColor = '#00C58E88';    // 青色 - 精度良好\n            fillColor = '#00C58E33';\n        } else if (accuracy <= 15) {\n            circleColor = '#FFD60A88';    // 黄色 - 精度一般\n            fillColor = '#FFD60A33';\n        } else if (accuracy <= 20) {\n            circleColor = '#FF950088';    // 橙色 - 精度较差\n            fillColor = '#FF950033';\n        } else if (accuracy <= 25) {\n            circleColor = '#FF6B2C88';    // 深橙色 - 精度很差\n            fillColor = '#FF6B2C33';\n        } else {\n            circleColor = '#FF3B3088';    // 红色 - 精度极差\n            fillColor = '#FF3B3033';\n        }\n\n        // 当前位置精度圈\n        const accuracyCircle = {\n            latitude: this.currentLocation.latitude,\n            longitude: this.currentLocation.longitude,\n            radius: 3, // 固定3米精度圈\n            color: circleColor,\n            fillColor: fillColor,\n            strokeWidth: 2,\n            strokeColor: circleColor.slice(0, 7) // 去掉透明度\n        };\n\n        // 更新圆形区域，保留任务点位范围圈\n        const circles = [...this.circles];\n\n        // 查找并更新或添加当前位置精度圈\n        const accuracyCircleIndex = circles.findIndex(circle =>\n            circle.strokeColor === '#34C759' ||  // 精度极好 - 绿色\n            circle.strokeColor === '#00C58E' ||  // 精度良好 - 青色\n            circle.strokeColor === '#FFD60A' ||  // 精度一般 - 黄色\n            circle.strokeColor === '#FF9500' ||  // 精度较差 - 橙色\n            circle.strokeColor === '#FF6B2C' ||  // 精度很差 - 深橙色\n            circle.strokeColor === '#FF3B30'     // 精度极差 - 红色\n        );\n\n        if (accuracyCircleIndex >= 0) {\n            circles[accuracyCircleIndex] = accuracyCircle;\n        } else {\n            circles.push(accuracyCircle);\n        }\n\n        this.circles = circles;\n    },\n\n    // 点击点位标记\n    onPointMarkerTap(markerId) {\n      console.log('onPointMarkerTap triggered with markerId:', markerId); // 添加这行日志用于调试点击事件\n      // 通过markerId找到对应索引\n      const markerIndex = parseInt(markerId);\n\n      // 找到对应的点位，通过索引匹配\n      if (isNaN(markerIndex) || markerIndex < 0 || !this.currentRoutePoints || markerIndex >= this.currentRoutePoints.length) {\n        return;\n      }\n\n      const point = this.currentRoutePoints[markerIndex];\n      if (!point) return;\n\n      // 检查是否有当前任务\n      if (!this.currentTask || !this.currentTask._id) {\n        uni.showToast({\n          title: '请先选择一个任务',\n          icon: 'none',\n          duration: 2000\n        });\n        return;\n      }\n\n      // 获取当前任务的轮次信息\n      const roundInfo = this.taskRoundInfos[this.currentTask._id];\n\n      // 打印调试信息，了解roundInfo的实际内容\n      console.log('任务轮次信息：', roundInfo);\n\n      // 检查任务是否有活跃或即将开始的轮次\n      if (!roundInfo) {\n        console.log('没有找到任务轮次信息');\n        // 尝试重新计算轮次信息\n        const shift = this.getTaskShift(this.currentTask);\n        if (shift) {\n          const calculatedRoundInfo = this.calculateCurrentRound(this.currentTask, shift, new Date());\n          console.log('重新计算的轮次信息：', calculatedRoundInfo);\n\n          // 如果仍然没有有效轮次，提示用户\n          if (!calculatedRoundInfo || (calculatedRoundInfo.status !== 'active' && calculatedRoundInfo.status !== 'waiting')) {\n            uni.showToast({\n              title: '当前没有可打卡的轮次',\n              icon: 'none',\n              duration: 2000\n            });\n            return;\n          }\n\n          // 如果有即将开始的轮次也允许打卡\n          this.taskRoundInfos[this.currentTask._id] = calculatedRoundInfo;\n        } else {\n          uni.showToast({\n            title: '任务未关联班次信息',\n            icon: 'none',\n            duration: 2000\n          });\n          return;\n        }\n      }\n\n      // 轮次检查：如果有选中轮次则使用选中轮次，否则使用当前活跃轮次或等待的轮次\n      let roundToUse = null;\n\n      // 验证当前选中的轮次状态\n      if (this.selectedRound && this.selectedRound.round) {\n        const roundData = this.currentTask.rounds_detail.find(r => r.round === this.selectedRound.round.round);\n        if (roundData && (roundData.status === 0 || roundData.status === 2 || roundData.status === 3)) {\n          // 轮次未开始、已完成或已超时，显示提示并阻止操作\n          let message = '';\n          if (roundData.status === 3) {\n            message = '所选轮次已超时';\n          } else if (roundData.status === 2) {\n            message = '所选轮次已完成';\n          } else if (roundData.status === 0) {\n            message = '所选轮次尚未开始';\n          }\n\n          uni.showToast({\n            title: message,\n            icon: 'none',\n            duration: 2000\n          });\n          return;\n        }\n        roundToUse = roundData;\n      } else {\n        // 没有选中轮次，使用当前活跃轮次或等待的轮次\n        const activeRound = this.getCurrentActiveRound(this.currentTask);\n        if (activeRound) {\n          roundToUse = activeRound;\n          console.log('使用当前活跃轮次：', activeRound);\n        } else {\n          console.log('没有找到可用的活跃轮次');\n          uni.showToast({\n            title: '当前没有可打卡的轮次',\n            icon: 'none',\n            duration: 2000\n          });\n          return;\n        }\n      }\n\n      // 直接跳转到打卡页面\n      this.navigateToCheckIn(this.currentTask, point);\n    },\n\n    // 获取当前活跃的轮次（状态为1-进行中的轮次）\n    getCurrentActiveRound(task) {\n      if (!task || !task.rounds_detail || !Array.isArray(task.rounds_detail)) {\n        return null;\n      }\n\n      // 查找状态为1的轮次（进行中）\n      let activeRound = task.rounds_detail.find(r => r.status === 1);\n\n      // 如果没有进行中的轮次，查找状态为0的轮次（未开始）\n      if (!activeRound) {\n        activeRound = task.rounds_detail.find(r => r.status === 0);\n      }\n\n      return activeRound;\n    },\n\n    // 天气数据加载完成\n    onWeatherLoaded(weatherInfo) {\n      this.weatherInfo = weatherInfo;\n    },\n\n    // 点击任务卡片\n    onTaskClick(task) {\n      // 如果切换到不同的任务，清除轮次选择状态\n      if (this.activeTaskId !== task._id) {\n        this.selectedRound = null;\n      }\n\n      // 设置当前激活的任务ID\n      this.activeTaskId = task._id;\n\n      // 加载任务详情\n      this.loadTaskDetail(task._id);\n    },\n\n    // 点击继续/开始巡检按钮\n    onTaskContinue(eventData) {\n      // 检查是否有传递完整的事件数据对象（包含task和selectedRound）\n      if (eventData && eventData.task) {\n        // 保存选中的轮次信息\n        if (eventData.selectedRound) {\n          this.selectedRound = {\n            round: eventData.selectedRound,\n            taskId: eventData.task._id\n          };\n        } else {\n          // 如果没有选中轮次，清除之前的选择\n          this.selectedRound = null;\n        }\n\n        // 加载任务详情并开始巡检\n        this.loadTaskDetail(eventData.task._id, true);\n      } else {\n        // 旧版本的处理方式 - 直接传递任务对象\n        const task = eventData;\n\n        // 加载任务详情并开始巡检\n        this.loadTaskDetail(task._id, true);\n      }\n    },\n\n    // 点击查看详情按钮\n    onTaskViewDetail(task) {\n      // 跳转到任务详情页面\n      uni.navigateTo({\n        url: `/pages/patrol_pkg/task/detail?id=${task._id}`\n      });\n    },\n\n    // 修改加载任务详情方法\n    async loadTaskDetail(taskId, startPatrol = false, forceRefresh = false) {\n      if (!taskId) {\n        console.error('任务ID为空');\n        this.showError('任务ID为空');\n        return;\n      }\n\n      try {\n        // 显示加载中\n        this.showLoading(true);\n\n        // 使用PatrolApi.call获取任务详情，与任务列表页保持一致\n        const res = await patrolApi.call({\n          name: 'patrol-task',\n          action: 'getTaskDetail',\n          data: {\n            task_id: taskId, // 🔥 使用优化后的参数格式\n            level: 'map', // 🔥 启用地图专用模式，保留位置信息，减少约30%数据传输量\n            forceRefresh: forceRefresh ? true : undefined // 添加强制刷新参数\n          }\n        });\n\n        if (res.code === 0 && res.data) {\n          // 处理任务详情\n          let newTaskData = res.data;\n\n          // 同步处理轮次信息\n          if (newTaskData.rounds_detail && newTaskData.rounds_detail.length > 0) {\n            const processedTasks = this.processTasks([newTaskData]);\n            if (processedTasks.length > 0) {\n              newTaskData = processedTasks[0]; // 使用处理后的数据\n            }\n          }\n\n          // 确保任务有round_records字段\n          if (!newTaskData.round_records) {\n            newTaskData.round_records = [];\n          }\n\n          // 更新轮次信息 (如果需要加载班次，确保 await 完成)\n          if (newTaskData.shift_id && !this.taskShifts[newTaskData.shift_id]) {\n            try {\n              const shiftRes = await patrolApi.call({ // 使用 await\n                name: 'patrol-shift',\n                action: 'getShiftDetail',\n                data: {\n                  params: { // 确认参数结构\n                    shift_id: newTaskData.shift_id\n                  }\n                }\n              });\n              if (shiftRes.code === 0 && shiftRes.data) {\n                this.taskShifts[newTaskData.shift_id] = shiftRes.data;\n                this.updateTaskRoundInfo(newTaskData); // 使用新数据更新\n              }\n            } catch (err) {\n              console.error('加载班次信息失败', err);\n            }\n          } else {\n            this.updateTaskRoundInfo(newTaskData); // 使用新数据更新\n          }\n\n          // 更新当前任务 - 使用新数据创建新引用\n          this.currentTask = { ...newTaskData };\n\n          // 更新taskList中相应的任务对象，确保引用变化\n          const taskIndex = this.taskList.findIndex(t => t._id === taskId);\n          if (taskIndex >= 0) {\n            // 创建全新的对象引用以触发Vue的检测\n            const updatedTaskList = [...this.taskList];\n            // 使用深拷贝或扩展运算符替换对象，确保引用变化\n            updatedTaskList[taskIndex] = { ...newTaskData }; // 使用扩展运算符创建新对象\n            // 替换整个数组以确保Vue检测到变化\n            this.taskList = updatedTaskList;\n\n            console.log('loadTaskDetail: 已更新taskList中的任务对象:', taskId);\n          }\n\n          // 构建标记点 - 确保在数据更新后执行\n          this.$nextTick(() => {\n            this.buildTaskMarkers();\n\n            // 检查是否要开始巡检\n            if (startPatrol === true) {\n              // 延迟执行startPatrol，确保地图已渲染\n              setTimeout(() => {\n                this.startPatrol(this.currentTask); // 传递更新后的currentTask\n              }, 300);\n            }\n          });\n\n          // 更新激活的任务ID\n          this.activeTaskId = taskId;\n\n          return this.currentTask; // 返回更新后的任务数据\n        } else {\n          this.showError(res.message || '获取任务详情失败');\n          return null; // 返回 null 表示失败\n        }\n      } catch (error) {\n        console.error('加载任务详情失败', error);\n        this.showError('加载任务详情失败');\n        return null; // 返回 null 表示失败\n      } finally {\n        // 添加延时和安全检查，避免多个hideLoading调用冲突\n        setTimeout(() => {\n          this.showLoading(false);\n        }, 200);\n      }\n    },\n\n    // 开始巡检\n    async startPatrol(taskData) {\n      // 检查任务数据是否有效\n      if (!taskData) {\n        console.error('任务数据为空');\n        this.showError('任务数据丢失');\n        return;\n      }\n\n      try {\n        // 显示加载中\n        this.showLoading(true);\n\n        // 先从服务器获取最新的任务数据\n        const res = await patrolApi.call({\n          name: 'patrol-task',\n          action: 'getTaskDetail',\n          data: {\n            task_id: taskData._id, // 🔥 使用优化后的参数格式\n            level: 'map', // 🔥 启用地图专用模式\n            forceRefresh: true // 强制刷新\n          }\n        });\n\n        // 创建处理任务的函数\n        const processPatrolTask = (task) => {\n          if (!task || !task.rounds_detail || !Array.isArray(task.rounds_detail)) {\n            return;\n          }\n\n          const now = new Date();\n\n          // 更新所有轮次的状态\n          task.rounds_detail.forEach(round => {\n            // 只有当轮次状态不是已完成(2)和超时(3)时才重新计算状态\n            if (round.status !== 2 && round.status !== 3) {\n              round.status = this.getRoundStatus(round, now);\n            }\n          });\n\n          // 获取当前选中的轮次或进行中的轮次\n          let currentRound = null;\n\n          // 检查是否所有轮次都已结束（完成或超时）\n          const allRoundsEnded = task.rounds_detail.every(round => round.status === 2 || round.status === 3);\n\n          if (allRoundsEnded) {\n            // 如果所有轮次都已结束，直接使用最后一轮\n            currentRound = task.rounds_detail[task.rounds_detail.length - 1];\n            console.log('所有轮次已结束，使用最后一轮:', currentRound.round);\n          } else {\n            // 1. 如果有选中的轮次，验证其状态\n            if (this.selectedRound && this.selectedRound.taskId === task._id) {\n              const selectedRound = task.rounds_detail.find(r => r.round === this.selectedRound.round.round);\n              // 只有当轮次状态为进行中(1)或未开始(0)时才使用\n              if (selectedRound && (selectedRound.status === 1 || selectedRound.status === 0)) {\n                currentRound = selectedRound;\n              }\n            }\n\n            // 2. 如果没有有效的选中轮次，查找进行中的轮次\n            if (!currentRound) {\n              const activeRounds = task.rounds_detail.filter(round => round.status === 1);\n              if (activeRounds.length > 0) {\n                // 如果有多个进行中的轮次，选择时间范围最长的\n                currentRound = activeRounds.reduce((prev, curr) => {\n                  const prevDuration = new Date(prev.end_time) - new Date(prev.start_time);\n                  const currDuration = new Date(curr.end_time) - new Date(curr.start_time);\n                  return currDuration > prevDuration ? curr : prev;\n                });\n              }\n            }\n\n            // 3. 如果没有进行中的轮次，查找未开始的轮次\n            if (!currentRound) {\n              const notStartedRounds = task.rounds_detail.filter(round => round.status === 0);\n              if (notStartedRounds.length > 0) {\n                // 如果有多个未开始的轮次，选择时间范围最长的\n                currentRound = notStartedRounds.reduce((prev, curr) => {\n                  const prevDuration = new Date(prev.end_time) - new Date(prev.start_time);\n                  const currDuration = new Date(curr.end_time) - new Date(curr.start_time);\n                  return currDuration > prevDuration ? curr : prev;\n                });\n              }\n            }\n          }\n\n          // 如果找到了轮次，继续处理\n          if (currentRound) {\n            console.log('处理巡视任务，当前轮次:', currentRound.round, '状态:', currentRound.status);\n\n            // 获取当前轮次的点位\n            let taskPoints = [];\n            if (currentRound.points && Array.isArray(currentRound.points)) {\n              console.log('从当前轮次获取点位信息:', currentRound.round);\n              taskPoints = currentRound.points;\n            } else if (task.route_detail && task.route_detail.points) {\n              console.log('从route_detail获取点位信息');\n              taskPoints = task.route_detail.points;\n            }\n\n            // 检查是否有可用的巡视点位\n            if (taskPoints.length === 0) {\n              console.error('未找到有效的点位信息');\n              this.showError('任务没有巡视点位');\n              return;\n            }\n\n            // 按照顺序对点位进行排序\n            const sortedPoints = [...taskPoints].sort((a, b) => {\n              const orderA = a.order !== undefined ? a.order : 0;\n              const orderB = b.order !== undefined ? b.order : 0;\n              return orderA - orderB;\n            });\n\n            console.log('排序后的点位:', sortedPoints);\n\n            // 如果所有轮次已结束，不再查找未完成的点位\n            if (!allRoundsEnded) {\n              // 🔥 修复：查找第一个未完成的点位（按顺序）\n              const incompletePoint = sortedPoints.find(point => {\n                // 修复状态判断逻辑，与buildTaskMarkers保持一致\n                if (point.status === 4) {\n                  return true; // 状态为4的点位视为未完成\n                }\n                // 优先使用status字段判断\n                return !(point.status === 1 || point.checkin_time || point.record_id);\n              });\n\n              if (incompletePoint) {\n                console.log(`🎯 继续巡视: ${incompletePoint.name || '未命名点位'} (order: ${incompletePoint.order})`);\n                // 跳转到打卡页面\n                this.navigateToCheckIn(task, incompletePoint);\n                return;\n              }\n              \n              console.log('✅ 所有点位已完成');\n            }\n\n            // 更新地图上的点位显示\n            this.buildTaskMarkers(task, currentRound, sortedPoints);\n          }\n        };\n\n        if (res.code === 0 && res.data) {\n          console.log('获取最新任务数据成功');\n          // 使用最新的任务数据继续处理\n          processPatrolTask(res.data);\n        } else {\n          console.warn('获取最新任务数据失败，使用缓存数据继续');\n          // 使用当前缓存的任务数据继续处理\n          processPatrolTask(taskData);\n        }\n      } catch (error) {\n        console.error('获取最新任务数据出错:', error);\n        // 出错时使用缓存数据\n        processPatrolTask(taskData);\n      } finally {\n        this.showLoading(false);\n      }\n    },\n\n    // 跳转到打卡页面\n    navigateToCheckIn(task, point) {\n      if (!task) {\n        console.error('任务数据为空');\n        this.showError('任务数据丢失');\n        return;\n      }\n\n      if (!point) {\n        console.error('点位数据为空');\n        this.showError('点位数据丢失');\n        return;\n      }\n\n      // 检查任务状态，允许已完成任务继续打卡\n      if (task.status === 4) { // 只有已取消的任务(status=4)不允许打卡\n        uni.showModal({\n          title: '提示',\n          content: '该任务已取消，无法打卡',\n          showCancel: false\n        });\n        return;\n      }\n\n      // 获取点位ID\n      const pointId = point.point_id || point._id;\n      if (!pointId) {\n        console.error('点位没有有效的ID');\n        this.showError('点位数据异常');\n        return;\n      }\n\n      // 获取所有可用的轮次（按顺序：进行中 -> 未开始 -> 已完成）\n      const availableRounds = task.rounds_detail\n        .filter(round => round.status !== 3 && round.status !== 4) // 排除超时和取消的轮次\n        .sort((a, b) => {\n          // 优先级：进行中 > 未开始 > 已完成\n          const getWeight = (status) => {\n            if (status === 1) return 0;  // 进行中\n            if (status === 0) return 1;  // 未开始\n            if (status === 2) return 2;  // 已完成\n            return 3;  // 其他状态\n          };\n          return getWeight(a.status) - getWeight(b.status);\n        });\n\n      // 如果有选中的轮次，验证其状态\n      let targetRound = null;\n      if (this.selectedRound && this.selectedRound.taskId === task._id) {\n        const selectedRound = task.rounds_detail.find(r => r.round === this.selectedRound.round.round);\n        if (selectedRound && (selectedRound.status === 1 || selectedRound.status === 0)) {\n          targetRound = selectedRound;\n        }\n      }\n\n      // 如果没有选中的轮次或选中的轮次无效，查找下一个可用的轮次\n      if (!targetRound) {\n        // 找到第一个未完成的轮次\n        targetRound = availableRounds.find(round => {\n          // 检查该轮次中的点位是否已完成\n          const roundRecord = task.round_records?.find(record => record.round === round.round);\n          const completedPoints = roundRecord?.completed_points || [];\n          return !completedPoints.includes(pointId);\n        });\n      }\n\n      // 如果没有找到可用轮次，说明所有轮次都已完成该点位\n      if (!targetRound) {\n        uni.showToast({\n          icon: 'none',\n          title: '当前没有可打卡的轮次',\n          duration: 2000\n        });\n        return;\n      }\n\n      // 构建跳转URL，传递当前位置信息作为初始值\n      let url = `/pages/patrol_pkg/checkin/index?task_id=${task._id}&point_id=${pointId}&round=${targetRound.round}`;\n      \n      // 如果有当前位置信息，传递给打卡页面作为初始定位\n      if (this.currentLocation && this.currentLocation.latitude && this.currentLocation.longitude) {\n        url += `&lat=${this.currentLocation.latitude}&lng=${this.currentLocation.longitude}&accuracy=${this.currentLocation.accuracy || 0}`;\n      }\n      \n      // 跳转到打卡页面\n      uni.navigateTo({\n        url: url\n      });\n    },\n\n    // 显示加载中\n    showLoading(show = true) {\n      this.isLoading = show;\n\n      if (show) {\n        uni.showLoading({\n          title: '加载中...',\n          mask: true\n        });\n      } else {\n        // 添加延时和检查，确保loading存在再隐藏\n        setTimeout(() => {\n          try {\n            // 检查当前是否有loading显示中\n            uni.hideLoading();\n          } catch (e) {\n          }\n        }, 150); // 延迟150ms执行，给loading显示和关闭之间留出缓冲时间\n      }\n    },\n\n    // 显示错误消息\n    showError(message) {\n      uni.showToast({\n        icon: 'none',\n        title: message || '操作失败',\n        duration: 2000\n      });\n    },\n\n    // 轮次状态更新定时器\n    startRoundUpdateTimer() {\n      // 先清除之前的定时器\n      this.clearRoundUpdateTimer();\n\n      // 创建新的定时器，定期更新轮次状态\n      this.roundUpdateTimer = setInterval(() => {\n        this.updateAllTaskRounds();\n      }, this.roundUpdateInterval);\n    },\n\n    // 清除轮次更新定时器\n    clearRoundUpdateTimer() {\n      if (this.roundUpdateTimer) {\n        clearInterval(this.roundUpdateTimer);\n        this.roundUpdateTimer = null;\n      }\n    },\n\n    // 更新所有任务的轮次状态\n    updateAllTaskRounds() {\n      if (!this.taskList || this.taskList.length === 0 || this.isInitialLoading) return;\n\n      const currentTime = new Date();\n      let needFullRefresh = false;\n\n      this.taskList.forEach(task => {\n        if (!task.shift_id || !this.taskShifts[task.shift_id]) return;\n\n        // 获取当前任务的轮次信息\n        const oldRoundInfo = this.taskRoundInfos[task._id];\n        const oldStatus = oldRoundInfo ? oldRoundInfo.status : null;\n\n        // 计算最新的轮次状态\n        const newRoundInfo = this.calculateCurrentRound(task, this.taskShifts[task.shift_id], currentTime);\n\n        // 检查轮次状态变化边界情况 - 特别是倒计时接近0的情况\n        if (oldRoundInfo && oldRoundInfo.status === 'waiting' &&\n            oldRoundInfo.timeUntilNext && oldRoundInfo.timeUntilNext < 60000) {\n          // 当等待时间少于1分钟时，更频繁地检查状态\n          console.log('轮次即将开始，更频繁检查');\n          // 立即强制重新计算状态\n          setTimeout(() => {\n            this.updateTaskRoundInfo(task);\n            this.$forceUpdate();\n          }, 2000); // 2秒后重新检查\n        }\n\n        this.taskRoundInfos[task._id] = newRoundInfo;\n\n        // 状态变化增强监测：特别关注从waiting到active的变化\n        if (oldStatus && newRoundInfo.status !== oldStatus) {\n          // 从等待到当前轮次，重要的状态变化，需要立即刷新UI\n          if (oldStatus === 'waiting' && newRoundInfo.status === 'active') {\n            console.log('重要状态变化: 从waiting到active');\n            // 立即强制刷新页面UI\n            this.$forceUpdate();\n            // 如果当前选中的是这个任务，立即更新地图等信息\n            if (this.currentTask && this.currentTask._id === task._id) {\n              this.buildTaskMarkers();\n            }\n            needFullRefresh = true;\n          }\n\n          // 其他重要状态变化\n          if (\n            (oldStatus === 'active' && newRoundInfo.status === 'completed') ||\n            (oldStatus === 'active' && newRoundInfo.status === 'between_rounds') ||\n            (oldStatus === 'between_rounds' && newRoundInfo.status === 'active')\n          ) {\n            needFullRefresh = true;\n\n            // 显示轮次变化提示\n            this.showRoundChangeNotification(task, oldRoundInfo, newRoundInfo);\n          }\n        }\n      });\n\n      // 如果有关键轮次状态变化，执行完整刷新\n      if (needFullRefresh) {\n        // 重要状态变化时先设置缓冲标记\n        this.isInitialLoading = true;\n\n        this.refreshTaskList(false);\n\n        // 延迟清除缓冲标记\n        setTimeout(() => {\n          this.isInitialLoading = false;\n        }, 800);\n      } else {\n        // 触发页面更新\n        this.$forceUpdate();\n      }\n    },\n\n    // 显示轮次变化通知\n    showRoundChangeNotification(task, oldRound, newRound) {\n      let title = '';\n      let content = '';\n\n      if (oldRound.status === 'waiting' && newRound.status === 'active') {\n        // 从等待到当前轮次激活\n        title = '新轮次开始';\n        content = `任务\"${task.route_name}\"的第${newRound.currentRound?.round || '?'}轮巡视已经开始`;\n      } else if (oldRound.status === 'active' && newRound.status === 'between_rounds') {\n        // 从当前轮次到轮次间隔\n        title = '当前轮次结束';\n        content = `任务\"${task.route_name}\"的第${oldRound.currentRound?.round || '?'}轮巡视已经结束，请等待下一轮开始`;\n      } else if (oldRound.status === 'between_rounds' && newRound.status === 'active') {\n        // 从轮次间隔到新轮次激活\n        title = '新轮次开始';\n        content = `任务\"${task.route_name}\"的第${newRound.currentRound?.round || '?'}轮巡视已经开始`;\n      } else if (oldRound.status === 'active' && newRound.status === 'completed') {\n        // 从当前轮次到全部完成\n        title = '所有轮次完成';\n        content = `任务\"${task.route_name}\"的所有巡视轮次已经结束`;\n      }\n\n      // 显示通知\n      if (title && content) {\n        uni.showModal({\n          title: title,\n          content: content,\n          showCancel: false,\n          success: () => {\n            // 用户确认后，如果是新轮次开始，自动选中该任务\n            if (\n              (oldRound.status === 'waiting' && newRound.status === 'active') ||\n              (oldRound.status === 'between_rounds' && newRound.status === 'active')\n            ) {\n              this.onTaskClick(task);\n            }\n          }\n        });\n      }\n    },\n\n    // 计算两点间距离（米）\n    calculateDistance(lat1, lon1, lat2, lon2) {\n      const R = 6371000; // 地球半径，单位米\n      const dLat = this.deg2rad(lat2 - lat1);\n      const dLon = this.deg2rad(lon2 - lon1);\n\n      const a =\n        Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n        Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *\n        Math.sin(dLon / 2) * Math.sin(dLon / 2);\n\n      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n      const distance = R * c;\n\n      return distance; // 返回米为单位的距离\n    },\n\n    // 角度转弧度\n    deg2rad(deg) {\n      return deg * (Math.PI / 180);\n    },\n\n    // 检查用户登录状态\n    checkLoginState() {\n      try {\n        // 如果已经显示了登录提示，不再重复显示\n        if (this.isShowingLoginTip) {\n          return false;\n        }\n\n        const userInfo = uni.getStorageSync('uni-id-pages-userInfo');\n        if (!userInfo || !userInfo._id) {\n          // 显示登录提示\n          this.showLoginTip();\n          return false;\n        }\n        return true;\n      } catch (e) {\n        console.error('检查登录状态出错', e);\n        if (!this.isShowingLoginTip) {\n          this.showLoginTip();\n        }\n        return false;\n      }\n    },\n\n    // 显示登录提示\n    showLoginTip() {\n      // 设置标志，防止重复显示\n      this.isShowingLoginTip = true;\n\n      // 设置页面为空状态\n      this.isEmpty = true;\n      this.taskList = [];\n\n      // 显示提示框\n      uni.showModal({\n        title: '需要登录',\n        content: '巡视打卡功能需要登录后才能使用',\n        confirmText: '去登录',\n        cancelText: '返回首页',\n        success: (res) => {\n          if (res.confirm) {\n            // 根据平台跳转到不同的登录页\n            // #ifdef MP-WEIXIN\n            uni.navigateTo({\n              url: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd'\n            });\n            // #endif\n            \n            // #ifndef MP-WEIXIN\n            uni.navigateTo({\n              url: '/uni_modules/uni-id-pages/pages/login/login-withpwd'\n            });\n            // #endif\n          } else {\n            // 返回首页\n            uni.switchTab({\n              url: '/pages/index/index'\n            });\n          }\n          // 清除标志\n          setTimeout(() => {\n            this.isShowingLoginTip = false;\n          }, 1000);\n        }\n      });\n    },\n\n    // 添加加载用户数据的方法\n    async loadUsers() {\n      try {\n        // 获取当前用户ID\n        const userInfo = uni.getStorageSync('uni-id-pages-userInfo');\n        const userId = userInfo ? (typeof userInfo === 'string' ? JSON.parse(userInfo)._id : userInfo._id) : '';\n\n        if (!userId) {\n          console.error('未获取到用户ID，可能未登录');\n          return;\n        }\n\n        const result = await patrolApi.call({\n          name: 'patrol-user',\n          action: 'getUsers',\n          data: {\n            userid: userId,\n            pageSize: 100\n          }\n        });\n\n        if (result.code === 0) {\n          const users = result.data.list || [];\n\n          // 清空之前的用户映射\n          this.userMap = {};\n\n          users.forEach(user => {\n            // 确保用户对象有name属性\n            const processedUser = {\n              ...user,\n              // 按优先级选择用户显示名称\n              name: user.real_name || user.nickname || user.username || '未命名用户'\n            };\n            this.userMap[user._id] = processedUser;\n          });\n\n          return users;\n        } else {\n          console.error('加载用户数据失败:', result.message);\n        }\n      } catch (e) {\n        console.error('加载用户错误:', e);\n      }\n      return [];\n    },\n\n    // 添加获取用户名称的方法\n    getUserName(userId) {\n      if (userId && this.userMap[userId]) {\n        return this.userMap[userId].name || '未知用户';\n      }\n      return '未分配';\n    },\n\n    // 添加格式化时间区间的方法\n    formatTimeRange(task, round) {\n      if (!round) return '--:-- - --:--';\n\n      try {\n        // 处理不同格式的时间字符串\n        let startTime = round.start_time;\n        let endTime = round.end_time;\n\n        // 如果没有明确的开始或结束时间，返回占位符\n        if (!startTime || !endTime) return '--:-- - --:--';\n\n        // 使用formatDate工具函数格式化时间\n        const formatTimeOnly = (dateStr) => {\n          if (!dateStr) return '--:--';\n\n          try {\n            // 使用replace确保跨平台兼容性\n            const d = new Date(dateStr.replace(/-/g, '/'));\n            if (isNaN(d.getTime())) return '--:--';\n\n            // 使用formatDate格式化时间\n            return formatDate(d, 'HH:mm');\n          } catch (e) {\n            console.error('时间格式化错误:', e);\n            return '--:--';\n          }\n        };\n\n        return `${formatTimeOnly(startTime)} - ${formatTimeOnly(endTime)}`;\n      } catch (e) {\n        console.error('格式化时间区间出错:', e);\n        return '--:-- - --:--';\n      }\n    },\n\n    // 格式化倒计时\n    formatCountdown(milliseconds) {\n      if (!milliseconds || milliseconds <= 0) return '00:00';\n\n      try {\n        // 转换毫秒为小时、分钟和秒\n        const totalSeconds = Math.floor(milliseconds / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor((totalSeconds % 3600) / 60);\n        const seconds = totalSeconds % 60;\n\n        // 格式化显示，使用padStart确保两位数显示\n        if (hours > 0) {\n          return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;\n        } else {\n          return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;\n        }\n      } catch (e) {\n        console.error('格式化倒计时出错:', e);\n        return '00:00';\n      }\n    },\n\n    // 添加格式化有效时长方法\n    formatValidTime(milliseconds, totalTime) {\n      if (!milliseconds || milliseconds <= 0) return '00:00';\n\n      try {\n        // 如果是倒计时（等待开始），使用原有的倒计时格式\n        if (milliseconds < 0) {\n          return this.formatCountdown(Math.abs(milliseconds));\n        }\n\n        // 如果是已开始的有效时长，使用新格式\n        const totalSeconds = Math.floor(milliseconds / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor((totalSeconds % 3600) / 60);\n\n        // 格式化总时长（仅显示小时和分钟）\n        let totalTimeStr = '';\n        if (totalTime) {\n          const totalSec = Math.floor(totalTime / 1000);\n          const totalHours = Math.floor(totalSec / 3600);\n          const totalMinutes = Math.floor((totalSec % 3600) / 60);\n\n          if (totalHours > 0) {\n            totalTimeStr = `${totalHours}小时${totalMinutes}分钟`;\n          } else {\n            totalTimeStr = `${totalMinutes}分钟`;\n          }\n        }\n\n        // 返回格式化的有效时长\n        if (hours > 0) {\n          return `有效时长: ${totalTimeStr || `${hours}小时${minutes}分钟`}`;\n        } else {\n          return `有效时长: ${totalTimeStr || `${minutes}分钟`}`;\n        }\n      } catch (e) {\n        console.error('格式化有效时长出错:', e);\n        return '有效时长: 0分钟';\n      }\n    },\n\n    // 判断时间是否紧急（小于10分钟）\n    isTimeUrgent(milliseconds) {\n      if (!milliseconds) return false;\n      return milliseconds < 10 * 60 * 1000; // 小于10分钟\n    },\n\n    // 添加判断任务是否为今天的方法\n    isTaskToday(task) {\n      if (!task || !task.patrol_date) return false;\n\n      try {\n        // 使用isToday工具函数判断任务日期是否为今天\n        return isToday(task.patrol_date);\n      } catch (e) {\n        console.error('判断任务日期出错:', e);\n        return false;\n      }\n    },\n\n    // 刷新任务列表\n    async refreshTaskList(showLoading = true) {\n      // 🔥 防抖机制：避免短时间内重复请求\n      const now = Date.now();\n      const timeSinceLastLoad = now - this.lastLoadTime;\n      \n      // 如果正在加载中，跳过重复请求\n      if (this.loadingInProgress) {\n        console.log('任务加载中，跳过重复请求');\n        return;\n      }\n      \n      // 防抖：3秒内的重复调用直接返回\n      if (timeSinceLastLoad < 3000) {\n        console.log('防抖阻止重复加载，距离上次加载', timeSinceLastLoad, 'ms');\n        return;\n      }\n      \n      // 🔥 设置加载状态和时间戳\n      this.loadingInProgress = true;\n      this.lastLoadTime = now;\n\n      if (showLoading) {\n        uni.showToast({\n          title: '正在刷新任务...',\n          icon: 'loading',\n          mask: true,\n          duration: 2000\n        });\n      }\n\n      // 保存当前激活的任务ID和地图中心点\n      const savedActiveTaskId = this.activeTaskId;\n      const savedMapCenter = { ...this.mapCenter };\n\n      // 设置初始加载标记\n      this.isInitialLoading = true;\n\n      try {\n        // 核心逻辑：先加载任务列表\n        await this.loadTaskList(false); // 使用静默加载\n\n        // 检查之前选中的任务是否仍然存在于新列表中\n        const taskExists = this.taskList.some(task => task._id === savedActiveTaskId);\n\n        if (savedActiveTaskId && taskExists) {\n          // 如果任务存在，重新加载该任务的详情以确保数据最新\n          // 并且确保 loadTaskDetail 内部会更新 currentTask 和 taskList\n          await this.loadTaskDetail(savedActiveTaskId, false, true); // 使用强制刷新\n          // 恢复地图中心点（如果需要）\n          // this.mapCenter = savedMapCenter; // 可以视情况决定是否恢复\n        } else if (this.taskList.length > 0 && !this.activeTaskId) {\n           // 如果之前没有选中任务，或者选中的任务消失了，自动选中第一个\n           this.activeTaskId = this.taskList[0]._id;\n           await this.loadTaskDetail(this.activeTaskId);\n        } else if (!taskExists) {\n          // 如果选中的任务消失了，清空状态\n          this.activeTaskId = '';\n          this.currentTask = null;\n          this.markers = [];\n          this.polylines = [];\n          this.circles = [];\n        }\n        // 如果 taskList 为空，则不需要做额外操作\n\n      } catch (error) {\n        console.error('刷新任务列表过程中出错:', error);\n        this.showError('刷新任务失败');\n      } finally {\n        // 🔥 确保清除加载状态\n        this.loadingInProgress = false;\n        \n        // 清除初始加载标记，并给予一定延迟\n        setTimeout(() => {\n          this.isInitialLoading = false;\n        }, 500); // 稍长延迟确保渲染稳定\n\n        if (showLoading) {\n          // 确保隐藏加载提示\n          setTimeout(() => {\n            uni.hideLoading();\n          }, 150);\n        }\n      }\n    },\n\n    // 处理任务更新事件 - 优化处理逻辑\n    handleTaskUpdated(data) {\n      console.log('收到任务更新事件:', data);\n      const { task_id, point_id, round } = data;\n\n      // 如果收到了轮次信息，先更新当前任务的轮次\n      if (round && this.currentTask && this.currentTask._id === task_id) {\n        console.log('检查轮次更新:', round);\n        // 确保rounds_detail存在\n        if (this.currentTask.rounds_detail) {\n          const existingRound = this.currentTask.rounds_detail.find(r => r.round === round);\n          if (!existingRound) {\n            console.log('发现新轮次，更新轮次信息:', round);\n            // 更新选中的轮次\n            this.selectedRound = {\n              taskId: task_id,\n              round: {\n                round: round,\n                status: 1\n              }\n            };\n          }\n        }\n      }\n\n      // 标记需要刷新，并延迟执行刷新，避免过于频繁的操作\n      if (!this.refreshTimeout) {\n        this.refreshTimeout = setTimeout(async () => {\n          console.log('执行延迟刷新...');\n          try {\n            // 简化刷新逻辑，只刷新任务详情，不进行额外的状态更新\n            // 避免状态计算冲突导致状态反复变化\n            if (this.activeTaskId === task_id) {\n              // 只有当前选中的任务才进行强制刷新\n              await this.loadTaskDetail(task_id, false, true);\n            } else {\n              // 否则只进行静默刷新\n              await this.refreshTaskList(false);\n            }\n          } catch (error) {\n            console.error('刷新任务数据失败:', error);\n          } finally {\n            this.refreshTimeout = null;\n          }\n        }, 1500); // 延长间隔时间，减少刷新频率\n      }\n    },\n\n    // 处理任务列表数据 - 从任务列表页复制的方法\n    processTasks(list) {\n      if (!list || !Array.isArray(list)) return [];\n\n      const now = new Date(); // 当前时间，用于判断轮次状态\n\n      // 处理任务数据\n      const processedTasks = list.map(task => {\n        // 基础任务数据处理\n        const processedTask = {\n          ...task,\n          points: task.points || [],\n          completed_points: task.completed_points || [],\n          status: parseInt(task.status || 0),\n          area: task.area || task.route_name || '未指定区域',\n          patrol_date: task.patrol_date || this.formatDateForCompare(new Date()),\n          shift_id: task.shift_id || '',\n          name: task.name || task.route_name || '未命名任务'\n        };\n\n        // 保存服务器返回的原始状态\n        const originalStatus = processedTask.status;\n\n        // 初始加载期间或对于已有明确状态的任务，保持服务器状态不变\n        if (this.isInitialLoading || originalStatus === 2 || originalStatus === 4) {\n          return processedTask;\n        }\n\n        // 如果任务已取消或已完成，直接返回\n        if (originalStatus === 4 || originalStatus === 2) {\n          return processedTask;\n        }\n\n        // 处理轮次详情，确保状态正确\n        if (processedTask.rounds_detail && processedTask.rounds_detail.length > 0) {\n          let allRoundsCompleted = true;\n          let hasActiveRound = false;\n          let hasUpcomingRound = false;\n          let allRoundsExpired = true;\n\n          // 遍历所有轮次，检查状态\n          processedTask.rounds_detail.forEach(round => {\n            if (!round) return;\n\n            // 确保轮次中有day_offset和duration字段\n            round.day_offset = round.day_offset !== undefined ? Number(round.day_offset) : 0;\n            round.duration = round.duration !== undefined ? Number(round.duration) : 60;\n\n            try {\n              // 解析轮次时间\n              const roundStartTime = new Date(round.start_time);\n              const roundEndTime = new Date(round.end_time);\n\n              // 检查点位完成情况\n              const isAllPointsCompleted = round.stats &&\n                                         round.stats.total_points > 0 &&\n                                         round.stats.completed_points >= round.stats.total_points;\n\n              // 增加状态稳定性，保留已有的完成和超时状态\n              if (round.status === 2 || round.status === 3) {\n                // 保持已完成或已超时状态\n                if (round.status === 2) allRoundsExpired = false;\n                if (round.status === 3) allRoundsCompleted = false;\n              } else {\n                // 确定轮次状态\n                if (isAllPointsCompleted) {\n                  round.status = 2; // 已完成\n                  allRoundsExpired = false;\n                } else if (now < roundStartTime) {\n                  round.status = 0; // 未开始\n                  hasUpcomingRound = true;\n                  allRoundsCompleted = false;\n                  allRoundsExpired = false;\n                } else if (now > roundEndTime) {\n                  round.status = 3; // 已超时\n                  allRoundsCompleted = false;\n                } else {\n                  round.status = 1; // 进行中\n                  hasActiveRound = true;\n                  allRoundsCompleted = false;\n                  allRoundsExpired = false;\n                }\n              }\n\n              // 记录状态变化日志，帮助诊断问题\n              if (originalStatus !== processedTask.status) {\n                console.log(`轮次[${round.round}]状态: ${round.status}`);\n              }\n            } catch (error) {\n              console.error(`解析轮次[${round.round}]时间出错:`, error);\n            }\n          });\n\n          // 记录原来的状态\n          const prevStatus = processedTask.status;\n\n          // 根据轮次状态确定任务状态\n          if (allRoundsCompleted ||\n              (processedTask.overall_stats &&\n               processedTask.overall_stats.completion_rate === 1)) {\n            // 如果所有轮次已完成或总体完成率为100%，任务状态为已完成\n            processedTask.status = 2;\n          } else if (hasActiveRound) {\n            // 如果有进行中的轮次，任务状态为进行中\n            processedTask.status = 1;\n          } else if (hasUpcomingRound) {\n            // 如果有未开始的轮次，任务状态为未开始\n            processedTask.status = 0;\n          } else if (allRoundsExpired) {\n            // 如果所有轮次都已超时，任务状态为已超时\n            processedTask.status = 3;\n          }\n\n          // 记录状态变化\n          if (prevStatus !== processedTask.status) {\n            console.log(`任务状态已更新: ${prevStatus} → ${processedTask.status}`);\n          }\n\n          // 根据轮次状态排序\n          const notStartedOrActive = processedTask.rounds_detail.filter(\n            round => round.status === 0 || round.status === 1\n          );\n          const completedOrExpired = processedTask.rounds_detail.filter(\n            round => round.status === 2 || round.status === 3\n          );\n\n          notStartedOrActive.sort((a, b) => a.round - b.round);\n          completedOrExpired.sort((a, b) => b.round - a.round);\n\n          processedTask.rounds_detail = [...notStartedOrActive, ...completedOrExpired];\n        }\n\n        return processedTask;\n      });\n\n      return processedTasks;\n    },\n\n    // 格式化日期用于比较\n    formatDateForCompare(date) {\n      if (!date) {\n        return '';\n      }\n\n      try {\n        // 确保date是Date对象\n        if (!(date instanceof Date)) {\n          // 尝试解析日期字符串\n          const parsedDate = new Date(date);\n          if (isNaN(parsedDate.getTime())) {\n            console.error('无效的日期字符串:', date);\n            return '';\n          }\n          date = parsedDate;\n        }\n\n        // 检查日期是否有效\n        if (isNaN(date.getTime())) {\n          console.error('无效的日期:', date);\n          return '';\n        }\n\n        // 获取本地时区的年月日\n        const year = date.getFullYear();\n        // 月份范围是0-11，需要+1并补零\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n\n        const formattedDate = `${year}-${month}-${day}`;\n        return formattedDate;\n      } catch (e) {\n        console.error('格式化日期错误:', e, '原始日期:', date);\n        return '';\n      }\n    },\n\n    // 处理轮次选择事件\n    onRoundSelected(data) {\n      // 性能优化：只在实际发生变化时更新和重建标记点\n      const isChanging = !this.selectedRound ||\n                         this.selectedRound.taskId !== data.taskId ||\n                         this.selectedRound.round.round !== data.round.round;\n\n      // 如果点击的是当前选中的轮次，取消选择（恢复自动模式）\n      if (this.selectedRound &&\n          this.selectedRound.taskId === data.taskId &&\n          this.selectedRound.round.round === data.round.round) {\n        this.selectedRound = null;\n      } else {\n        this.selectedRound = data;\n      }\n\n      // 如果选择的轮次属于当前激活的任务，且轮次发生了变化，重新构建标记点\n      if (isChanging && this.currentTask && this.currentTask._id === data.taskId) {\n        this.buildTaskMarkers();\n      }\n    },\n\n    // 切换抽屉菜单\n    toggleDrawer() {\n      this.isDrawerOpen = !this.isDrawerOpen;\n    },\n\n    // 关闭抽屉菜单\n    closeDrawer() {\n      this.drawerStyle = {\n        transform: 'translateY(100%)',\n        transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'\n      };\n      this.isDrawerOpen = false;\n    },\n\n    // 打开抽屉菜单\n    openDrawer() {\n      this.isDrawerOpen = true;\n    },\n\n    // 强制更新任务列表中的任务状态\n    forceUpdateTaskInList(taskId) {\n      if (!taskId || !this.taskList || this.taskList.length === 0) return;\n\n      console.log('强制更新任务列表中的任务:', taskId);\n\n      // 获取任务最新数据\n      patrolApi.call({\n        name: 'patrol-task',\n        action: 'getTaskDetail',\n        data: {\n          task_id: taskId, // 🔥 使用优化后的参数格式\n          level: 'map' // 🔥 启用地图专用模式\n        }\n      }).then(res => {\n        if (res.code === 0 && res.data) {\n          // 找到并替换任务\n          const taskIndex = this.taskList.findIndex(t => t._id === taskId);\n          if (taskIndex >= 0) {\n            // 使用深拷贝创建新引用\n            const newTaskList = [...this.taskList];\n            newTaskList[taskIndex] = JSON.parse(JSON.stringify(res.data));\n\n            // 确保处理轮次信息\n            if (newTaskList[taskIndex].rounds_detail && newTaskList[taskIndex].rounds_detail.length > 0) {\n              const processedTasks = this.processTasks([newTaskList[taskIndex]]);\n              if (processedTasks.length > 0) {\n                newTaskList[taskIndex] = processedTasks[0];\n              }\n            }\n\n            // 替换整个数组以确保视图更新\n            this.taskList = newTaskList;\n            console.log('已更新任务列表中的任务:', taskId);\n          }\n        }\n      }).catch(error => {\n        console.error('获取任务详情失败:', error);\n      });\n    },\n\n    handleTouchStart(event) {\n      if (!this.isDrawerOpen) return;\n\n      // iOS设备上禁用拖拽手势，避免事件穿透问题\n      if (uni.getSystemInfoSync().platform === 'ios') {\n        return;\n      }\n\n      this.touchStartY = event.touches[0].clientY;\n      this.isDragging = true;\n      this.currentTranslateY = 0;\n      this.lastTouchTime = Date.now();\n      this.lastDeltaY = 0;\n\n      // 获取抽屉高度\n      const query = uni.createSelectorQuery().in(this);\n      query.select('.task-drawer').boundingClientRect(data => {\n        if (data) {\n          this.drawerHeight = data.height;\n        }\n      }).exec();\n    },\n\n    handleTouchMove(event) {\n      if (!this.isDragging || !this.isDrawerOpen) return;\n\n      // iOS设备上禁用拖拽手势\n      if (uni.getSystemInfoSync().platform === 'ios') {\n        return;\n      }\n\n      const currentY = event.touches[0].clientY;\n      const deltaY = currentY - this.touchStartY;\n      const currentTime = Date.now();\n      const timeDiff = currentTime - this.lastTouchTime;\n\n      // 计算滑动速度 (像素/毫秒)\n      const velocity = Math.abs((deltaY - this.lastDeltaY) / timeDiff);\n\n      if (deltaY < 0) return;\n\n      // 根据滑动速度和距离动态调整阻尼效果\n      let damping = 0.9; // 基础阻尼系数\n      if (velocity > 0.5) { // 如果滑动速度较快\n        damping = 1; // 减小阻尼\n      } else if (deltaY < this.drawerHeight * 0.2) { // 在开始阶段\n        damping = 0.95; // 稍微减小阻尼，使初始滑动更容易\n      }\n\n      this.currentTranslateY = Math.min(deltaY * damping, this.drawerHeight);\n\n      // 应用变换\n      this.drawerStyle = {\n        transform: `translateY(${this.currentTranslateY}px)`,\n        transition: 'none'\n      };\n\n      // 更新遮罩层透明度\n      const maskOpacity = Math.max(0.4 - (deltaY / this.drawerHeight) * 0.4, 0);\n      this.maskStyle = {\n        backgroundColor: `rgba(0, 0, 0, ${maskOpacity})`\n      };\n\n      // 更新状态\n      this.lastTouchTime = currentTime;\n      this.lastDeltaY = deltaY;\n    },\n\n    handleTouchEnd() {\n      if (!this.isDragging || !this.isDrawerOpen) return;\n\n      // iOS设备上禁用拖拽手势\n      if (uni.getSystemInfoSync().platform === 'ios') {\n        return;\n      }\n\n      this.isDragging = false;\n\n      // 计算最终速度\n      const endTime = Date.now();\n      const timeDiff = endTime - this.lastTouchTime;\n      const velocity = Math.abs((this.currentTranslateY - this.lastDeltaY) / timeDiff);\n\n      // 动态关闭阈值：根据速度和距离综合判断\n      let closeThreshold = this.drawerHeight * 0.2; // 默认20%\n      if (velocity > 0.5) { // 如果速度较快\n        closeThreshold = this.drawerHeight * 0.1; // 降低到10%\n      }\n\n      // 恢复过渡动画\n      this.drawerStyle = {\n        transform: this.currentTranslateY > closeThreshold ? 'translateY(100%)' : 'translateY(0)',\n        transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'\n      };\n\n      // 如果拖动距离超过阈值，关闭抽屉\n      if (this.currentTranslateY > closeThreshold) {\n        this.closeDrawer();\n      } else {\n        // 否则恢复原位\n        this.maskStyle = {\n          backgroundColor: 'rgba(0, 0, 0, 0.4)'\n        };\n      }\n\n      this.currentTranslateY = 0;\n      this.lastDeltaY = 0;\n    },\n\n    // 添加getRoundStatus方法\n    getRoundStatus(round, now = new Date()) {\n      if (!round || !round.start_time || !round.end_time) {\n        return 0; // 默认未开始\n      }\n\n      try {\n        // 如果轮次已经有状态且为已完成，保持完成状态\n        if (round.status === 2) {\n          return 2;\n        }\n\n        const startTime = new Date(round.start_time.replace(/-/g, '/'));\n        const endTime = new Date(round.end_time.replace(/-/g, '/'));\n\n        // 检查点位完成情况\n        const isAllPointsCompleted = round.stats &&\n            round.stats.total_points > 0 &&\n            round.stats.completed_points >= round.stats.total_points;\n\n        // 如果所有点位已完成，则轮次状态为已完成\n        if (isAllPointsCompleted) {\n          return 2;\n        }\n\n        // 根据时间判断状态\n        if (now < startTime) {\n          return 0; // 未开始\n        } else if (now > endTime) {\n          return 3; // 已超时\n        } else {\n          return 1; // 进行中\n        }\n      } catch (error) {\n        console.error('计算轮次状态出错:', error);\n        return 0; // 出错时默认未开始\n      }\n    },\n  }\n};\n</script>\n\n<style lang=\"scss\">\n.patrol-index {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f8f8f8; /* 浅灰白色背景 */\n  position: relative;\n  overflow: hidden; /* 防止内容溢出 */\n\n  /* 强制硬件加速，解决滚动和动画卡顿问题 */\n  -webkit-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-backface-visibility: hidden;\n  backface-visibility: hidden;\n\n  /* GPS精度显示样式 */\n  .location-accuracy {\n    position: absolute;\n    left: 20rpx;\n    bottom: calc(20rpx + env(safe-area-inset-bottom));\n    background: rgba(255, 255, 255, 0.6);\n    backdrop-filter: blur(8px);\n    padding: 12rpx 20rpx;\n    border-radius: 12rpx;\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n    z-index: 120;\n    display: flex;\n    align-items: center;\n    gap: 12rpx;\n  }\n\n  .status-dot {\n    width: 12rpx;\n    height: 12rpx;\n    border-radius: 50%;\n    flex-shrink: 0;\n  }\n\n  .accuracy-text {\n    font-size: 26rpx;\n    color: #333;\n    font-weight: 500;\n  }\n\n  /* 所有scroll-view通用样式：隐藏滚动条 */\n  :deep(scroll-view) {\n    &::-webkit-scrollbar {\n      display: none;\n      width: 0;\n      height: 0;\n      color: transparent;\n    }\n    scrollbar-width: none;\n    -ms-overflow-style: none;\n  }\n\n  .map-controls {\n    position: absolute;\n    top: 20rpx;\n    right: 20rpx;\n    z-index: 100;\n    display: flex;\n    flex-direction: column;\n    align-items: flex-end;\n  }\n\n  /* 右下角浮动菜单按钮 */\n  .float-menu-btn {\n    position: fixed;\n    right: 30rpx;\n    bottom: calc(30rpx + constant(safe-area-inset-bottom));\n    bottom: calc(30rpx + env(safe-area-inset-bottom));\n    z-index: 200;\n    width: 120rpx;\n    height: 120rpx;\n\n    &__inner {\n      width: 100%;\n      height: 100%;\n      background: linear-gradient(135deg, #1677FF, #1890FF);\n      border-radius: 50%;\n      box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      transition: transform 0.3s ease;\n\n      &:active {\n        transform: scale(0.92);\n      }\n    }\n\n    &__text {\n      font-size: 24rpx;\n      color: #FFFFFF;\n      margin-top: 6rpx;\n      font-weight: 500;\n    }\n  }\n\n\n\n  .control-buttons {\n    display: flex;\n    flex-direction: column;\n\n    .control-btn {\n      width: 60rpx;\n      height: 60rpx;\n      border-radius: 50%;\n      background-color: rgba(255, 255, 255, 0.9);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 0;\n      margin: 0 0 20rpx 0;\n      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n\n      &::after {\n        border: none;\n      }\n\n      .iconfont {\n        font-size: 32rpx;\n        color: #1677FF;\n      }\n    }\n  }\n\n  .weather-compact {\n    position: absolute;\n    top: 20rpx;\n    left: 20rpx;\n    z-index: 120;\n    background: none;\n    border-radius: 0;\n    box-shadow: none;\n    padding: 0;\n    max-width: 60%;\n  }\n\n  /* 抽屉遮罩层 */\n  .drawer-mask {\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.4);\n    z-index: 900;\n    opacity: 0;\n    visibility: hidden;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    width: 100vw; /* 确保宽度100% */\n    height: 100vh; /* 确保高度100% */\n\n    &--active {\n      opacity: 1;\n      visibility: visible;\n    }\n  }\n\n  /* 任务抽屉菜单 */\n  .task-drawer {\n    position: fixed;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    background-color: #FFFFFF;\n    border-radius: 20rpx 20rpx 0 0;\n    transform: translateY(100%);\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    z-index: 901;\n    max-height: 85vh;\n    touch-action: pan-y;\n\n    &--active {\n      transform: translateY(0);\n    }\n\n    .drawer-handle {\n      width: 60rpx;\n      height: 6rpx;\n      background-color: #E0E0E0;\n      border-radius: 3rpx;\n      margin: 16rpx auto;\n      position: relative;\n    }\n\n    .drawer-content {\n      height: calc(85vh - 40rpx);\n      overflow-y: auto;\n      -webkit-overflow-scrolling: touch;\n    }\n\n    /* 抽屉头部 */\n    &__header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 30rpx 20rpx;\n      border-bottom: 1rpx solid #eee;\n      background: linear-gradient(to right, #f8f8f8, #ffffff);\n    }\n\n    &__title-container {\n      display: flex;\n      align-items: center;\n      flex: 1;\n    }\n\n    &__date {\n      font-size: 28rpx;\n      font-weight: bold;\n      color: #1677FF;\n      margin: 0 8rpx;\n    }\n\n    &__title-text {\n      font-size: 26rpx;\n      color: #666;\n      margin-left: 6rpx;\n    }\n\n    &__refresh-btn, &__close-btn {\n      width: 60rpx;\n      height: 60rpx;\n      border-radius: 50%;\n      background-color: #f0f7ff;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-left: 10rpx;\n\n      &:active {\n        opacity: 0.8;\n      }\n    }\n\n    &__close-btn {\n      background-color: #f5f5f5;\n    }\n\n    /* 任务列表区域 */\n    &__list {\n      flex: 1;\n      height: auto; /* 改为auto以适应内容 */\n      overflow-y: auto;\n      -webkit-overflow-scrolling: touch;\n      padding: 5rpx; /* 增加底部内边距 */\n      background-color: #fff;\n      width: 100%; /* 确保宽度100% */\n      box-sizing: border-box; /* 确保padding不会增加宽度 */\n\n      /* 隐藏滚动条 - 专为微信小程序优化 */\n      &::-webkit-scrollbar {\n        display: none;\n        width: 0 !important;\n        height: 0 !important;\n        background: transparent;\n        color: transparent;\n      }\n\n      /* 清除浮动和设置边界处理 */\n      &::after {\n        content: \"\";\n        display: block;\n        height: 0;\n        clear: both;\n      }\n    }\n\n    &__list-content {\n      padding: 10rpx;\n      padding-bottom: 50rpx; /* 增加底部内边距确保内容显示完整 */\n      background-color: #fff; /* 确保内容区也是白色 */\n      width: 100%; /* 确保宽度100% */\n      box-sizing: border-box; /* 确保padding不会增加宽度 */\n    }\n\n    /* 修改任务卡片在抽屉中的样式 */\n    :deep(.p-task-card) {\n      margin-bottom: 20rpx;\n      border-radius: 16rpx;\n      overflow: hidden;\n      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n\n      &[active=\"true\"] {\n        border: 3rpx solid #1677FF !important;\n        box-shadow: 0 4rpx 16rpx rgba(22, 119, 255, 0.15) !important;\n      }\n    }\n  }\n}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571660955\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
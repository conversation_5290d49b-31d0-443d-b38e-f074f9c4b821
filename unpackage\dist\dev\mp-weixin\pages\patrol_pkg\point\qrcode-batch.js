require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol_pkg/point/qrcode-batch"],{

/***/ 475:
/*!****************************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fpatrol_pkg%2Fpoint%2Fqrcode-batch"} ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _qrcodeBatch = _interopRequireDefault(__webpack_require__(/*! ./pages/patrol_pkg/point/qrcode-batch.vue */ 476));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_qrcodeBatch.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 476:
/*!*******************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue ***!
  \*******************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _qrcode_batch_vue_vue_type_template_id_89ff35b2___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./qrcode-batch.vue?vue&type=template&id=89ff35b2& */ 477);
/* harmony import */ var _qrcode_batch_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./qrcode-batch.vue?vue&type=script&lang=js& */ 479);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _qrcode_batch_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _qrcode_batch_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _qrcode_batch_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./qrcode-batch.vue?vue&type=style&index=0&lang=scss& */ 481);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _qrcode_batch_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _qrcode_batch_vue_vue_type_template_id_89ff35b2___WEBPACK_IMPORTED_MODULE_0__["render"],
  _qrcode_batch_vue_vue_type_template_id_89ff35b2___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _qrcode_batch_vue_vue_type_template_id_89ff35b2___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/patrol_pkg/point/qrcode-batch.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 477:
/*!**************************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?vue&type=template&id=89ff35b2& ***!
  \**************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_template_id_89ff35b2___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcode-batch.vue?vue&type=template&id=89ff35b2& */ 478);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_template_id_89ff35b2___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_template_id_89ff35b2___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_template_id_89ff35b2___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_template_id_89ff35b2___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 478:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?vue&type=template&id=89ff35b2& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uqrcode: function () {
      return Promise.all(/*! import() | uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode")]).then(__webpack_require__.bind(null, /*! @/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue */ 754))
    },
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 483))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.__map(_vm.points, function (point, __i0__) {
    var $orig = _vm.__get_orig(point)
    var g0 = point.location ? point.location.longitude.toFixed(6) : null
    var g1 = point.location ? point.location.latitude.toFixed(6) : null
    return {
      $orig: $orig,
      g0: g0,
      g1: g1,
    }
  })
  var g2 = _vm.loadMoreStatus === "noMore" && _vm.points.length > 0
  var g3 = !_vm.loading && _vm.points.length === 0
  if (!_vm._isMounted) {
    _vm.e0 = function (res, point) {
      var args = [],
        len = arguments.length - 2
      while (len-- > 0) args[len] = arguments[len + 2]

      var _temp = args[args.length - 1].currentTarget.dataset,
        _temp2 = _temp.eventParams || _temp["event-params"],
        point = _temp2.point
      var _temp, _temp2
      return _vm.onQRCodeComplete(res, point)
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
        g2: g2,
        g3: g3,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 479:
/*!********************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?vue&type=script&lang=js& ***!
  \********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcode-batch.vue?vue&type=script&lang=js& */ 480);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 480:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! @/utils/patrol-api.js */ 71));
var _qrcodeUtils = _interopRequireDefault(__webpack_require__(/*! @/utils/qrcode-utils.js */ 300));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var PEmptyState = function PEmptyState() {
  __webpack_require__.e(/*! require.ensure | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then((function () {
    return resolve(__webpack_require__(/*! @/components/p-empty-state/p-empty-state.vue */ 483));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    PEmptyState: PEmptyState
  },
  data: function data() {
    return {
      points: [],
      // 点位列表
      loading: false,
      hasGeneratedCodes: false,
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      loadMoreStatus: 'more',
      isLoadingMore: false
    };
  },
  onLoad: function onLoad() {
    this.loadPoints(true);
  },
  // 添加页面生命周期方法
  onReachBottom: function onReachBottom() {
    this.loadMore();
  },
  // 添加下拉刷新
  onPullDownRefresh: function onPullDownRefresh() {
    this.loadPoints(true).then(function () {
      uni.stopPullDownRefresh();
    });
  },
  computed: {
    // 判断是否所有点位都已启用并生成二维码
    allPointsEnabled: function allPointsEnabled() {
      return this.points.length > 0 && this.points.every(function (p) {
        return p.qrcode_enabled && p.generated;
      });
    }
  },
  methods: {
    goBack: function goBack() {
      uni.navigateBack();
    },
    // 加载更多
    loadMore: function loadMore() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (!(_this.loadMoreStatus === 'loading' || _this.loadMoreStatus === 'noMore' || _this.isLoadingMore)) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return");
              case 2:
                _this.isLoadingMore = true;
                _this.loadMoreStatus = 'loading';
                _this.pagination.page++;
                _context.next = 7;
                return _this.loadPoints();
              case 7:
              case "end":
                return _context.stop();
            }
          }
        }, _callee);
      }))();
    },
    // 加载点位列表
    loadPoints: function loadPoints() {
      var _arguments = arguments,
        _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var reset, res, newList, newPoints, total;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                reset = _arguments.length > 0 && _arguments[0] !== undefined ? _arguments[0] : false;
                if (reset) {
                  _this2.pagination.page = 1;
                  _this2.points = [];
                  _this2.loadMoreStatus = 'more';
                }
                _context2.prev = 2;
                if (reset) {
                  _this2.loading = true;
                  uni.showLoading({
                    title: '加载中...'
                  });
                }
                _context2.next = 6;
                return _patrolApi.default.callPointFunction('getPointList', {
                  params: {
                    keyword: '',
                    page: _this2.pagination.page,
                    pageSize: _this2.pagination.pageSize,
                    status: 1
                  }
                });
              case 6:
                res = _context2.sent;
                console.log('API response:', res);
                if (res.code === 0 && res.data) {
                  newList = res.data.list || []; // 处理新的点位数据
                  newPoints = newList.map(function (point) {
                    return _objectSpread(_objectSpread({}, point), {}, {
                      generated: !!point.qrcode_content,
                      // 如果已有二维码内容则标记为已生成
                      qrcodeContent: point.qrcode_content || null // 使用数据库中的二维码内容
                    });
                  }); // 更新列表

                  if (reset) {
                    _this2.points = newPoints;
                  } else {
                    _this2.points = [].concat((0, _toConsumableArray2.default)(_this2.points), (0, _toConsumableArray2.default)(newPoints));
                  }

                  // 确保total是数字
                  if (reset) {
                    if (typeof res.data.total === 'number' && res.data.total > 0) {
                      total = res.data.total;
                    } else if (typeof res.data.total === 'string' && parseInt(res.data.total) > 0) {
                      total = parseInt(res.data.total);
                    } else if (newList.length >= _this2.pagination.pageSize) {
                      total = newList.length + _this2.pagination.pageSize;
                    } else {
                      total = newList.length;
                    }
                    _this2.pagination.total = total;
                  }

                  // 更新加载状态
                  if (newList.length === 0) {
                    _this2.loadMoreStatus = 'noMore';
                  } else if (newList.length < _this2.pagination.pageSize) {
                    _this2.loadMoreStatus = 'noMore';
                  } else {
                    _this2.loadMoreStatus = 'more';
                  }
                  console.log('Data loaded:', {
                    newPointsCount: newPoints.length,
                    totalPoints: _this2.points.length,
                    totalExpected: _this2.pagination.total,
                    currentPage: _this2.pagination.page,
                    hasMore: _this2.loadMoreStatus === 'more',
                    loadMoreStatus: _this2.loadMoreStatus
                  });
                }
                _context2.next = 15;
                break;
              case 11:
                _context2.prev = 11;
                _context2.t0 = _context2["catch"](2);
                console.error('加载点位失败:', _context2.t0);
                uni.showToast({
                  title: '加载点位失败',
                  icon: 'none'
                });
              case 15:
                _context2.prev = 15;
                if (reset) {
                  _this2.loading = false;
                  uni.hideLoading();
                }
                _this2.isLoadingMore = false;
                return _context2.finish(15);
              case 19:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[2, 11, 15, 19]]);
      }))();
    },
    // 启用点位二维码
    enableQRCode: function enableQRCode(point) {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var res, index;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                uni.showLoading({
                  title: '启用中...'
                });
                _context3.next = 4;
                return _patrolApi.default.callPointFunction('updatePoint', {
                  data: {
                    id: point._id,
                    qrcode_enabled: true,
                    qrcode_version: 1
                  }
                });
              case 4:
                res = _context3.sent;
                if (!(res.code === 0)) {
                  _context3.next = 11;
                  break;
                }
                // 更新本地数据
                index = _this3.points.findIndex(function (p) {
                  return p._id === point._id;
                });
                if (index !== -1) {
                  _this3.$set(_this3.points[index], 'qrcode_enabled', true);
                  _this3.$set(_this3.points[index], 'qrcode_version', 1);
                  // 自动生成二维码
                  _this3.$nextTick(function () {
                    _this3.generateQRCode(_this3.points[index]);
                  });
                }
                uni.showToast({
                  title: '启用成功',
                  icon: 'success'
                });
                _context3.next = 12;
                break;
              case 11:
                throw new Error(res.message || '启用失败');
              case 12:
                _context3.next = 18;
                break;
              case 14:
                _context3.prev = 14;
                _context3.t0 = _context3["catch"](0);
                console.error('启用失败:', _context3.t0);
                uni.showToast({
                  title: _context3.t0.message || '启用失败',
                  icon: 'none'
                });
              case 18:
                _context3.prev = 18;
                uni.hideLoading();
                return _context3.finish(18);
              case 21:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 14, 18, 21]]);
      }))();
    },
    // 关闭点位二维码
    disableQRCode: function disableQRCode(point) {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                try {
                  uni.showModal({
                    title: '确认关闭',
                    content: '关闭二维码后，该点位的所有二维码数据将被清除，是否继续？',
                    success: function () {
                      var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4(res) {
                        var _res, index;
                        return _regenerator.default.wrap(function _callee4$(_context4) {
                          while (1) {
                            switch (_context4.prev = _context4.next) {
                              case 0:
                                if (!res.confirm) {
                                  _context4.next = 12;
                                  break;
                                }
                                uni.showLoading({
                                  title: '关闭中...'
                                });
                                _context4.next = 4;
                                return _patrolApi.default.callPointFunction('updatePoint', {
                                  data: {
                                    id: point._id,
                                    qrcode_enabled: false,
                                    qrcode_content: null,
                                    // 清除二维码内容
                                    qrcode_version: null,
                                    // 清除版本号
                                    qrcode_hash_key: null,
                                    // 清除哈希密钥
                                    qrcode_generated_time: null // 清除生成时间
                                  }
                                });
                              case 4:
                                _res = _context4.sent;
                                if (!(_res.code === 0)) {
                                  _context4.next = 11;
                                  break;
                                }
                                // 更新本地数据
                                index = _this4.points.findIndex(function (p) {
                                  return p._id === point._id;
                                });
                                if (index !== -1) {
                                  _this4.$set(_this4.points[index], 'qrcode_enabled', false);
                                  _this4.$set(_this4.points[index], 'qrcode_content', null);
                                  _this4.$set(_this4.points[index], 'qrcode_version', null);
                                  _this4.$set(_this4.points[index], 'qrcode_hash_key', null);
                                  _this4.$set(_this4.points[index], 'qrcode_generated_time', null);
                                  _this4.$set(_this4.points[index], 'generated', false);
                                  _this4.$set(_this4.points[index], 'qrcodeContent', null);
                                }
                                uni.showToast({
                                  title: '已关闭二维码',
                                  icon: 'success'
                                });
                                _context4.next = 12;
                                break;
                              case 11:
                                throw new Error(_res.message || '关闭失败');
                              case 12:
                              case "end":
                                return _context4.stop();
                            }
                          }
                        }, _callee4);
                      }));
                      function success(_x) {
                        return _success.apply(this, arguments);
                      }
                      return success;
                    }()
                  });
                } catch (e) {
                  console.error('关闭失败:', e);
                  uni.showToast({
                    title: e.message || '关闭失败',
                    icon: 'none'
                  });
                } finally {
                  uni.hideLoading();
                }
              case 1:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    // 生成单个点位的二维码
    generateQRCode: function generateQRCode(point) {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var enableRes, detailRes, _index, _detailRes, qrContent, updateResult, index;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _context6.prev = 0;
                if (point.qrcode_enabled) {
                  _context6.next = 17;
                  break;
                }
                _context6.next = 4;
                return _patrolApi.default.callPointFunction('updatePoint', {
                  data: {
                    id: point._id,
                    qrcode_enabled: true,
                    qrcode_version: 1
                  }
                });
              case 4:
                enableRes = _context6.sent;
                if (!(enableRes.code !== 0)) {
                  _context6.next = 7;
                  break;
                }
                throw new Error(enableRes.message || '启用二维码失败');
              case 7:
                _context6.next = 9;
                return _patrolApi.default.callPointFunction('getPointDetail', {
                  point_id: point._id
                });
              case 9:
                detailRes = _context6.sent;
                if (!(detailRes.code === 0 && detailRes.data)) {
                  _context6.next = 14;
                  break;
                }
                // 更新点位信息，特别是hash_key
                Object.assign(point, detailRes.data);
                _context6.next = 15;
                break;
              case 14:
                throw new Error('获取点位详情失败');
              case 15:
                // 更新本地数据
                _index = _this5.points.findIndex(function (p) {
                  return p._id === point._id;
                });
                if (_index !== -1) {
                  _this5.$set(_this5.points[_index], 'qrcode_enabled', true);
                  _this5.$set(_this5.points[_index], 'qrcode_version', 1);
                  _this5.$set(_this5.points[_index], 'qrcode_hash_key', point.qrcode_hash_key);
                }
              case 17:
                if (point.qrcode_hash_key) {
                  _context6.next = 26;
                  break;
                }
                _context6.next = 20;
                return _patrolApi.default.callPointFunction('getPointDetail', {
                  point_id: point._id
                });
              case 20:
                _detailRes = _context6.sent;
                if (!(_detailRes.code === 0 && _detailRes.data)) {
                  _context6.next = 25;
                  break;
                }
                point.qrcode_hash_key = _detailRes.data.qrcode_hash_key;
                _context6.next = 26;
                break;
              case 25:
                throw new Error('获取点位hash_key失败');
              case 26:
                // 生成新的二维码内容
                qrContent = _qrcodeUtils.default.getQRCodeData(_objectSpread(_objectSpread({}, point), {}, {
                  qrcode_content: null // 强制生成新内容
                }), {
                  includeTimestamp: false // 不包含时间戳
                }); // 保存到数据库
                _context6.next = 29;
                return _patrolApi.default.callPointFunction('updatePoint', {
                  data: {
                    id: point._id,
                    qrcode_content: qrContent,
                    qrcode_generated_time: new Date().toISOString(),
                    qrcode_enabled: true,
                    qrcode_version: point.qrcode_version || 1
                  }
                });
              case 29:
                updateResult = _context6.sent;
                if (!(updateResult.code !== 0)) {
                  _context6.next = 32;
                  break;
                }
                throw new Error(updateResult.message || '生成二维码失败');
              case 32:
                // 更新本地数据
                index = _this5.points.findIndex(function (p) {
                  return p._id === point._id;
                });
                if (index !== -1) {
                  _this5.$set(_this5.points[index], 'qrcodeContent', qrContent);
                  _this5.$set(_this5.points[index], 'qrcode_content', qrContent);
                  _this5.$set(_this5.points[index], 'generated', true);
                  _this5.$set(_this5.points[index], 'qrcode_enabled', true);
                  _this5.$set(_this5.points[index], 'qrcode_version', point.qrcode_version || 1);
                  _this5.$set(_this5.points[index], 'qrcode_generated_time', new Date().toISOString());
                }

                // 显示成功提示
                uni.showToast({
                  title: '生成成功',
                  icon: 'success'
                });
                _context6.next = 42;
                break;
              case 37:
                _context6.prev = 37;
                _context6.t0 = _context6["catch"](0);
                console.error("\u751F\u6210\u4E8C\u7EF4\u7801\u5931\u8D25 (".concat(point.name, "):"), _context6.t0);
                uni.showToast({
                  title: _context6.t0.message || '生成失败',
                  icon: 'none'
                });
                throw _context6.t0;
              case 42:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[0, 37]]);
      }))();
    },
    // 二维码生成完成回调
    onQRCodeComplete: function onQRCodeComplete(res, point) {
      if (res.success) {
        var index = this.points.findIndex(function (p) {
          return p._id === point._id;
        });
        if (index !== -1) {
          this.$set(this.points[index], 'generated', true);
          this.hasGeneratedCodes = true;
        }
      }
    },
    // 处理批量操作（生成或关闭）
    handleBatchOperation: function handleBatchOperation() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                if (!_this6.allPointsEnabled) {
                  _context7.next = 5;
                  break;
                }
                _context7.next = 3;
                return _this6.disableAllQRCodes();
              case 3:
                _context7.next = 7;
                break;
              case 5:
                _context7.next = 7;
                return _this6.generateAllQRCodes();
              case 7:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7);
      }))();
    },
    // 保存单个二维码
    saveQRCode: function saveQRCode(point) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var qrcodeRef, qrcode;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                _context8.prev = 0;
                uni.showLoading({
                  title: '保存中...'
                });
                qrcodeRef = _this7.$refs['qrcode-' + point._id];
                if (qrcodeRef) {
                  _context8.next = 5;
                  break;
                }
                throw new Error('二维码组件未找到');
              case 5:
                qrcode = Array.isArray(qrcodeRef) ? qrcodeRef[0] : qrcodeRef;
                _context8.next = 8;
                return qrcode.save({
                  success: function success() {
                    uni.showToast({
                      title: '保存成功',
                      icon: 'success'
                    });
                  },
                  fail: function fail(error) {
                    console.error('保存失败:', error);
                    uni.showToast({
                      title: '保存失败',
                      icon: 'none'
                    });
                  }
                });
              case 8:
                _context8.next = 14;
                break;
              case 10:
                _context8.prev = 10;
                _context8.t0 = _context8["catch"](0);
                console.error('保存失败:', _context8.t0);
                uni.showToast({
                  title: '保存失败',
                  icon: 'none'
                });
              case 14:
                _context8.prev = 14;
                uni.hideLoading();
                return _context8.finish(14);
              case 17:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[0, 10, 14, 17]]);
      }))();
    },
    // 批量生成二维码
    generateAllQRCodes: function generateAllQRCodes() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var pointsToGenerate, confirmRes, successCount, failCount, i, point;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                // 过滤出需要生成二维码的点位
                pointsToGenerate = _this8.points.filter(function (point) {
                  return !point.qrcode_content || !point.generated;
                });
                if (!(pointsToGenerate.length === 0)) {
                  _context9.next = 4;
                  break;
                }
                uni.showToast({
                  title: '没有需要生成的二维码',
                  icon: 'none'
                });
                return _context9.abrupt("return");
              case 4:
                _context9.prev = 4;
                _context9.next = 7;
                return new Promise(function (resolve, reject) {
                  uni.showModal({
                    title: '确认生成',
                    content: "\u786E\u5B9A\u8981\u4E3A".concat(pointsToGenerate.length, "\u4E2A\u70B9\u4F4D\u542F\u7528\u5E76\u751F\u6210\u4E8C\u7EF4\u7801\u5417\uFF1F"),
                    confirmText: '确定',
                    cancelText: '取消',
                    success: resolve,
                    fail: reject
                  });
                });
              case 7:
                confirmRes = _context9.sent;
                if (confirmRes.confirm) {
                  _context9.next = 10;
                  break;
                }
                return _context9.abrupt("return");
              case 10:
                successCount = 0;
                failCount = 0; // 显示初始进度
                uni.showLoading({
                  title: "\u5904\u7406\u4E2D(0/".concat(pointsToGenerate.length, ")"),
                  mask: true
                });

                // 逐个生成二维码
                i = 0;
              case 14:
                if (!(i < pointsToGenerate.length)) {
                  _context9.next = 30;
                  break;
                }
                point = pointsToGenerate[i];
                _context9.prev = 16;
                _context9.next = 19;
                return _this8.generateQRCode(point);
              case 19:
                successCount++;
                // 更新进度
                uni.showLoading({
                  title: "\u5904\u7406\u4E2D(".concat(successCount, "/").concat(pointsToGenerate.length, ")"),
                  mask: true
                });
                _context9.next = 27;
                break;
              case 23:
                _context9.prev = 23;
                _context9.t0 = _context9["catch"](16);
                console.error("\u751F\u6210 ".concat(point.name, " \u7684\u4E8C\u7EF4\u7801\u5931\u8D25:"), _context9.t0);
                failCount++;
              case 27:
                i++;
                _context9.next = 14;
                break;
              case 30:
                // 隐藏加载框
                uni.hideLoading();

                // 显示最终结果
                uni.showModal({
                  title: '处理完成',
                  content: "\u751F\u6210\u5B8C\u6210\n\u6210\u529F\uFF1A".concat(successCount, "\u4E2A\n\u5931\u8D25\uFF1A").concat(failCount, "\u4E2A"),
                  showCancel: false
                });
                _context9.next = 39;
                break;
              case 34:
                _context9.prev = 34;
                _context9.t1 = _context9["catch"](4);
                console.error('批量生成失败:', _context9.t1);
                uni.hideLoading();
                uni.showToast({
                  title: '操作失败',
                  icon: 'none'
                });
              case 39:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[4, 34], [16, 23]]);
      }))();
    },
    // 一键关闭所有二维码
    disableAllQRCodes: function disableAllQRCodes() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var enabledPoints, confirmRes, successCount, failCount, _loop, i;
        return _regenerator.default.wrap(function _callee10$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                // 获取所有已启用二维码的点位
                enabledPoints = _this9.points.filter(function (p) {
                  return p.qrcode_enabled;
                });
                if (!(enabledPoints.length === 0)) {
                  _context11.next = 4;
                  break;
                }
                uni.showToast({
                  title: '没有已启用的二维码',
                  icon: 'none'
                });
                return _context11.abrupt("return");
              case 4:
                _context11.next = 6;
                return new Promise(function (resolve) {
                  uni.showModal({
                    title: '确认关闭',
                    content: "\u5C06\u5173\u95ED".concat(enabledPoints.length, "\u4E2A\u70B9\u4F4D\u7684\u4E8C\u7EF4\u7801\u529F\u80FD\uFF0C\u5173\u95ED\u540E\u5C06\u6E05\u9664\u6240\u6709\u4E8C\u7EF4\u7801\u6570\u636E\uFF0C\u662F\u5426\u7EE7\u7EED\uFF1F"),
                    success: resolve
                  });
                });
              case 6:
                confirmRes = _context11.sent;
                if (confirmRes.confirm) {
                  _context11.next = 9;
                  break;
                }
                return _context11.abrupt("return");
              case 9:
                _context11.prev = 9;
                uni.showLoading({
                  title: '处理中(0/' + enabledPoints.length + ')',
                  mask: true
                });
                successCount = 0;
                failCount = 0;
                _loop = /*#__PURE__*/_regenerator.default.mark(function _loop(i) {
                  var point, res, index;
                  return _regenerator.default.wrap(function _loop$(_context10) {
                    while (1) {
                      switch (_context10.prev = _context10.next) {
                        case 0:
                          point = enabledPoints[i];
                          _context10.prev = 1;
                          _context10.next = 4;
                          return _patrolApi.default.callPointFunction('updatePoint', {
                            data: {
                              id: point._id,
                              qrcode_enabled: false,
                              qrcode_content: null,
                              // 清除二维码内容
                              qrcode_version: null,
                              // 清除版本号
                              qrcode_hash_key: null,
                              // 清除哈希密钥
                              qrcode_generated_time: null // 清除生成时间
                            }
                          });
                        case 4:
                          res = _context10.sent;
                          if (!(res.code === 0)) {
                            _context10.next = 11;
                            break;
                          }
                          // 更新本地数据
                          index = _this9.points.findIndex(function (p) {
                            return p._id === point._id;
                          });
                          if (index !== -1) {
                            _this9.$set(_this9.points[index], 'qrcode_enabled', false);
                            _this9.$set(_this9.points[index], 'qrcode_content', null);
                            _this9.$set(_this9.points[index], 'qrcode_version', null);
                            _this9.$set(_this9.points[index], 'qrcode_hash_key', null);
                            _this9.$set(_this9.points[index], 'qrcode_generated_time', null);
                            _this9.$set(_this9.points[index], 'generated', false);
                            _this9.$set(_this9.points[index], 'qrcodeContent', null);
                          }
                          successCount++;
                          _context10.next = 12;
                          break;
                        case 11:
                          throw new Error(res.message || '关闭失败');
                        case 12:
                          _context10.next = 18;
                          break;
                        case 14:
                          _context10.prev = 14;
                          _context10.t0 = _context10["catch"](1);
                          console.error("\u5173\u95ED ".concat(point.name, " \u7684\u4E8C\u7EF4\u7801\u5931\u8D25:"), _context10.t0);
                          failCount++;
                        case 18:
                          // 更新进度
                          uni.showLoading({
                            title: "\u5904\u7406\u4E2D(".concat(successCount, "/").concat(enabledPoints.length, ")"),
                            mask: true
                          });
                        case 19:
                        case "end":
                          return _context10.stop();
                      }
                    }
                  }, _loop, null, [[1, 14]]);
                });
                i = 0;
              case 15:
                if (!(i < enabledPoints.length)) {
                  _context11.next = 20;
                  break;
                }
                return _context11.delegateYield(_loop(i), "t0", 17);
              case 17:
                i++;
                _context11.next = 15;
                break;
              case 20:
                uni.hideLoading();
                uni.showModal({
                  title: '处理完成',
                  content: "\u6210\u529F\uFF1A".concat(successCount, "\u4E2A\n\u5931\u8D25\uFF1A").concat(failCount, "\u4E2A\n\u6240\u6709\u4E8C\u7EF4\u7801\u6570\u636E\u5DF2\u6E05\u9664"),
                  showCancel: false
                });
                _context11.next = 28;
                break;
              case 24:
                _context11.prev = 24;
                _context11.t1 = _context11["catch"](9);
                console.error('批量关闭失败:', _context11.t1);
                uni.showToast({
                  title: '批量关闭失败',
                  icon: 'none'
                });
              case 28:
                _context11.prev = 28;
                uni.hideLoading();
                return _context11.finish(28);
              case 31:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee10, null, [[9, 24, 28, 31]]);
      }))();
    },
    /**
     * @description 保存全部已生成的二维码图片
     */
    saveAllQRCodes: function saveAllQRCodes() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        var pointsToSave, confirmRes, successCount, failCount, totalCount, _loop2, i, _ret;
        return _regenerator.default.wrap(function _callee11$(_context13) {
          while (1) {
            switch (_context13.prev = _context13.next) {
              case 0:
                // 筛选出已生成二维码的点位
                pointsToSave = _this10.points.filter(function (p) {
                  return p.generated;
                }); // 如果没有可保存的二维码，则提示并返回
                if (!(pointsToSave.length === 0)) {
                  _context13.next = 4;
                  break;
                }
                uni.showToast({
                  title: '没有已生成的二维码可保存',
                  icon: 'none'
                });
                return _context13.abrupt("return");
              case 4:
                _context13.next = 6;
                return new Promise(function (resolve) {
                  uni.showModal({
                    title: '确认保存',
                    content: "\u5373\u5C06\u4FDD\u5B58 ".concat(pointsToSave.length, " \u4E2A\u4E8C\u7EF4\u7801\u56FE\u7247\u5230\u7CFB\u7EDF\u76F8\u518C\uFF0C\u662F\u5426\u7EE7\u7EED\uFF1F"),
                    success: resolve
                  });
                });
              case 6:
                confirmRes = _context13.sent;
                if (confirmRes.confirm) {
                  _context13.next = 9;
                  break;
                }
                return _context13.abrupt("return");
              case 9:
                successCount = 0;
                failCount = 0;
                totalCount = pointsToSave.length; // 显示初始进度
                uni.showLoading({
                  title: "\u4FDD\u5B58\u4E2D(0/".concat(totalCount, ")"),
                  mask: true
                });

                // 逐个保存二维码
                _loop2 = /*#__PURE__*/_regenerator.default.mark(function _loop2(i) {
                  var point, currentProgress, qrcodeRef, qrcode;
                  return _regenerator.default.wrap(function _loop2$(_context12) {
                    while (1) {
                      switch (_context12.prev = _context12.next) {
                        case 0:
                          point = pointsToSave[i];
                          currentProgress = i + 1; // 更新加载提示，显示当前点位名称和进度
                          uni.showLoading({
                            title: "\u4FDD\u5B58 ".concat(currentProgress, "/").concat(totalCount, ": ").concat(point.name || '未命名点位', "..."),
                            mask: true
                          });
                          _context12.prev = 3;
                          // 获取对应的 uqrcode 组件实例
                          qrcodeRef = _this10.$refs['qrcode-' + point._id];
                          if (qrcodeRef) {
                            _context12.next = 9;
                            break;
                          }
                          console.warn("\u4E8C\u7EF4\u7801\u7EC4\u4EF6 ".concat(point.name, " \u672A\u627E\u5230\uFF0C\u8DF3\u8FC7\u4FDD\u5B58"));
                          failCount++;
                          return _context12.abrupt("return", "continue");
                        case 9:
                          // 确保获取到的是单个组件实例
                          qrcode = Array.isArray(qrcodeRef) ? qrcodeRef[0] : qrcodeRef; // 调用组件的 save 方法保存图片
                          _context12.next = 12;
                          return new Promise(function (resolve, reject) {
                            qrcode.save({
                              success: function success() {
                                successCount++;
                                resolve();
                              },
                              fail: function fail(err) {
                                console.error("\u4FDD\u5B58 ".concat(point.name, " \u4E8C\u7EF4\u7801\u5931\u8D25:"), err);
                                failCount++;
                                // 即使失败也 resolve，以便继续处理下一个
                                resolve();
                              }
                            });
                          });
                        case 12:
                          _context12.next = 18;
                          break;
                        case 14:
                          _context12.prev = 14;
                          _context12.t0 = _context12["catch"](3);
                          // 捕获预料之外的错误
                          console.error("\u5904\u7406 ".concat(point.name, " \u65F6\u53D1\u751F\u610F\u5916\u9519\u8BEF:"), _context12.t0);
                          failCount++;
                        case 18:
                        case "end":
                          return _context12.stop();
                      }
                    }
                  }, _loop2, null, [[3, 14]]);
                });
                i = 0;
              case 15:
                if (!(i < totalCount)) {
                  _context13.next = 23;
                  break;
                }
                return _context13.delegateYield(_loop2(i), "t0", 17);
              case 17:
                _ret = _context13.t0;
                if (!(_ret === "continue")) {
                  _context13.next = 20;
                  break;
                }
                return _context13.abrupt("continue", 20);
              case 20:
                i++;
                _context13.next = 15;
                break;
              case 23:
                // 隐藏加载提示
                uni.hideLoading();

                // 显示最终结果
                uni.showModal({
                  title: '保存完成',
                  // 根据成功和失败数量显示不同的提示信息
                  content: "\u5171\u5904\u7406 ".concat(totalCount, " \u4E2A\u4E8C\u7EF4\u7801\u3002\n\u6210\u529F\u4FDD\u5B58 ").concat(successCount, " \u4E2A\uFF0C\u5931\u8D25 ").concat(failCount, " \u4E2A\u3002"),
                  showCancel: false
                });
              case 25:
              case "end":
                return _context13.stop();
            }
          }
        }, _callee11);
      }))();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 481:
/*!*****************************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?vue&type=style&index=0&lang=scss& ***!
  \*****************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcode-batch.vue?vue&type=style&index=0&lang=scss& */ 482);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qrcode_batch_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 482:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/point/qrcode-batch.vue?vue&type=style&index=0&lang=scss& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[475,"common/runtime","common/vendor","pages/patrol_pkg/common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/patrol_pkg/point/qrcode-batch.js.map
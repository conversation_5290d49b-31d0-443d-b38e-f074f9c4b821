<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>株水小智 - 污水厂智能化微信小程序</title>
    <script src="libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="libs/all.min.css">
    <style>
        body { min-height: 100vh; }
        .gradient-bg {
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #1e40af 100%);
        }
        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(30, 58, 138, 0.35); /* 深色玻璃态，与内容页一致 */
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        }
        .main-title {
            background: linear-gradient(135deg, #ffffff 0%, #e0f2fe 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255,255,255,0.3);
            animation: breath-glow 2.5s ease-in-out infinite;
        }
        @keyframes breath-glow {
            0% { text-shadow: 0 0 10px rgba(255,255,255,0.15), 0 0 30px rgba(255,255,255,0.3); }
            50% { text-shadow: 0 0 30px rgba(255,255,255,0.5), 0 0 60px rgba(255,255,255,0.6); }
            100% { text-shadow: 0 0 10px rgba(255,255,255,0.15), 0 0 30px rgba(255,255,255,0.3); }
        }
        .subtitle-bar {
            display: flex; align-items: center; justify-content: center; margin-top: 0.5rem; margin-bottom: 2rem;
        }
        .subtitle-bar:before, .subtitle-bar:after {
            content: "";
            flex: 1;
            height: 2px;
            background: linear-gradient(90deg, #38bdf8 0%, #a7f3d0 100%);
            margin: 0 1rem;
            border-radius: 1px;
        }
        .bubble {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.13);
            pointer-events: none;
            animation: floatBubble 12s linear infinite;
        }
        @keyframes floatBubble {
            0% { transform: translateY(0) scale(1); opacity: 0.7; }
            50% { opacity: 1; }
            100% { transform: translateY(-120vh) scale(1.2); opacity: 0; }
        }
        .industry-icons-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            row-gap: 3rem;
            column-gap: 5rem;
            justify-items: center;
            align-items: center;
        }
        .industry-icon {
            font-size: 3.5rem;
            background: white;
            border-radius: 1.5rem;
            padding: 1.1rem 1.2rem;
            box-shadow: 0 4px 16px rgba(30,64,175,0.10);
            border: 2px solid #e0e7ef;
            filter: drop-shadow(0 2px 8px rgba(30,64,175,0.12));
        }
        .industry-icon-blue { color: #1e40af; }
        .industry-icon-green { color: #059669; }
        .industry-icon-orange { color: #f59e0b; }
        .industry-label {
            font-size: 1.1rem;
            color: #e0f2fe;
            font-weight: bold;
            margin-top: 0.5rem;
            text-align: center;
        }
        .industry-label-small {
            font-size: 0.95rem;
            color: #bae6fd;
            font-weight: 500;
            margin-top: 0.1rem;
            text-align: center;
        }
        .watermark {
            color: #a7f3d0;
            font-size: 1.1rem;
            font-weight: bold;
            text-shadow: 0 2px 8px rgba(255,255,255,0.5), 0 0 2px #fff;
            margin-top: 1.5rem;
        }
        /* 左侧内容区小图标高对比色+阴影 */
        .icon-contrast {
            color: #38bdf8 !important;
            filter: drop-shadow(0 2px 4px rgba(30,64,175,0.12));
            background: #1e40af;
            border-radius: 0.6rem;
            padding: 0.18rem 0.32rem;
            margin-right: 0.7rem;
            font-size: 1.25rem;
            border: 1.5px solid #38bdf8;
        }
        .icon-contrast-green {
            color: #4ade80 !important;
            border-color: #bbf7d0;
            background: #166534;
        }
        .icon-contrast-orange {
            color: #fbbf24 !important;
            border-color: #fde68a;
            background: #78350f;
        }
        .highlight-blue { color: #38bdf8; font-weight: bold; }
        .highlight-green { color: #4ade80; font-weight: bold; }
        .highlight-yellow { color: #fbbf24; font-weight: bold; }
        .highlight-white { color: #fff; font-weight: bold; }
        .text-main { color: #fff; }
        .text-sub { color: #bae6fd; }
    </style>
</head>
<body class="gradient-bg min-h-screen flex flex-col relative overflow-x-hidden">
    <!-- 气泡粒子动画 -->
    <div class="absolute inset-0 z-0 overflow-hidden">
        <div class="bubble" style="width:60px;height:60px;left:10vw;bottom:10vh;animation-delay:0s;"></div>
        <div class="bubble" style="width:40px;height:40px;left:80vw;bottom:20vh;animation-delay:2s;"></div>
        <div class="bubble" style="width:80px;height:80px;left:30vw;bottom:5vh;animation-delay:4s;"></div>
        <div class="bubble" style="width:30px;height:30px;left:60vw;bottom:15vh;animation-delay:6s;"></div>
        <div class="bubble" style="width:50px;height:50px;left:50vw;bottom:8vh;animation-delay:1s;"></div>
    </div>
    <!-- 顶部主副标题 -->
    <header class="text-center pt-12 pb-2 z-10 relative">
        <h1 class="text-3xl md:text-4xl font-bold main-title">株水小智 - 污水厂智能化微信小程序</h1>
        <div class="subtitle-bar">
            <span class="text-lg text-blue-100 font-medium">一线员工自主研发，贴合实际运营</span>
        </div>
    </header>
    <!-- 内容区 -->
    <main class="flex-1 flex flex-col items-center justify-center px-4 z-10 relative">
        <div class="glass-effect rounded-3xl p-12 w-full max-w-5xl relative z-10 min-h-[600px] flex items-center" style="transform: translateY(-50px);">
            <div class="grid md:grid-cols-5 gap-6 items-center w-full">
                <!-- 左侧亮点描述 -->
                <div class="md:col-span-3 min-w-[320px] md:min-w-[400px] lg:min-w-[450px]">
                    <h2 class="text-2xl font-bold highlight-yellow mb-6 flex items-center"><i class="fas fa-lightbulb icon-contrast icon-contrast-orange"></i>系统亮点</h2>
                    <ul class="space-y-6 text-main text-base md:text-lg font-medium">
                        <li class="flex items-center gap-2 whitespace-nowrap"><i class="fas fa-tint icon-contrast icon-contrast-blue"></i><span class="highlight-blue">基于微信小程序</span>，一键登录，轻量化设计，无需下载安装</li>
                        <li class="flex items-center gap-2 whitespace-nowrap"><i class="fas fa-cubes icon-contrast icon-contrast-blue"></i><span class="highlight-blue">三大核心功能模块协同</span>形成完整闭环管理体系</li>
                        <li class="flex items-center gap-2 whitespace-nowrap"><i class="fas fa-check-circle icon-contrast icon-contrast-blue"></i><span class="highlight-blue">全流程数字化管理</span>，从发现问题到闭环处理，覆盖全厂运营场景</li>
                        <li class="flex items-center gap-2 whitespace-nowrap"><i class="fas fa-users icon-contrast icon-contrast-blue"></i><span class="highlight-blue">一线员工自主研发</span>，更贴合污水厂实际作业需求</li>
                    </ul>
                    <h2 class="text-2xl font-bold highlight-green mt-10 mb-6 flex items-center"><i class="fas fa-seedling icon-contrast icon-contrast-green"></i>创新背景</h2>
                    <p class="text-base md:text-lg text-sub leading-relaxed text-left">
                        针对污水厂传统管理中 <span class="highlight-green">“问题跟进难、巡视记录散、任务协同慢”</span> 等痛点，由一线员工自主设计开发，推动基层创新，助力智慧水务转型升级。
                    </p>
                </div>
                <!-- 右侧行业图标组 -->
                <div class="flex flex-col items-center justify-center md:col-span-2">
                    <div class="industry-icons-grid">
                        <div>
                            <i class="fas fa-industry industry-icon industry-icon-blue"></i>
                            <div class="industry-label">污水厂</div>
                            <div class="industry-label-small">数字化管理</div>
                        </div>
                        <div>
                            <i class="fas fa-tint industry-icon industry-icon-blue"></i>
                            <div class="industry-label">水务</div>
                            <div class="industry-label-small">智慧运营</div>
                        </div>
                        <div>
                            <i class="fas fa-leaf industry-icon industry-icon-blue"></i>
                            <div class="industry-label">绿色环保</div>
                            <div class="industry-label-small">节能减排</div>
                        </div>
                        <div>
                            <i class="fas fa-chart-bar industry-icon industry-icon-blue"></i>
                            <div class="industry-label">数据看板</div>
                            <div class="industry-label-small">移动决策</div>
                        </div>
                    </div>
                    <div class="watermark">智慧水务 · 绿色环保</div>
                </div>
            </div>
        </div>
    </main>
    <!-- 底部导航 -->
    <footer class="text-center py-6 text-blue-200 flex justify-center space-x-8 relative z-20">
        <button onclick="goPrev()" class="px-6 py-2 rounded-xl bg-white/20 hover:bg-white/30 transition text-lg font-semibold"><i class="fas fa-arrow-left mr-2"></i>上一页</button>
        <button onclick="goNext()" class="px-6 py-2 rounded-xl bg-white/20 hover:bg-white/30 transition text-lg font-semibold">下一页<i class="fas fa-arrow-right ml-2"></i></button>
    </footer>
    <script>
        // 导航
        function goPrev() { window.location.href = 'index.html'; }
        function goNext() { window.location.href = 'slide2.html'; }
        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                goNext();
            } else if (e.key === 'ArrowLeft') {
                goPrev();
            }
        });
    </script>
</body>
</html> 
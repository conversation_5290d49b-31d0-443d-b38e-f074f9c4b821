{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/notice/edit.vue?2d15", "webpack:///D:/Xwzc/pages/notice/edit.vue?06d2", "webpack:///D:/Xwzc/pages/notice/edit.vue?276e", "webpack:///D:/Xwzc/pages/notice/edit.vue?ef24", "uni-app:///pages/notice/edit.vue", "webpack:///D:/Xwzc/pages/notice/edit.vue?263f", "webpack:///D:/Xwzc/pages/notice/edit.vue?5ca5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "result", "data", "formData", "formOptions", "rules", "getValidator", "formDataId", "editorCtx", "editorInitialized", "onLoad", "onShow", "onReady", "methods", "onEditorInit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "html", "success", "fail", "console", "onEditorInput", "handleImageUpload", "uni", "title", "tempFiles", "file", "filePath", "fileID", "src", "alt", "icon", "uploadFile", "uniCloud", "cloudPath", "cloudPathAsRealPath", "resolve", "reject", "checkPermission", "hasPermission", "setTimeout", "getUserInfo", "currentUserInfo", "uniIdUserInfo", "userInfo", "userName", "publisher", "publisherName", "binddata", "ensurePublisherInfo", "submit", "mask", "submitForm", "updateData", "content", "showCancel", "getDetail", "db", "goBack", "chooseFile", "count", "type", "extension", "uploadFiles", "name", "url", "size", "Promise", "then", "catch", "deleteFile", "res", "fileList", "handleImageRemove", "getFileIcon", "formatFileSize"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,8RAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,4WAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChFA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC6DlnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAEA;EACA;EACA;IACA;MACAC;IACA;EACA;EACA;AACA;AAAA,eAEA;EACAC;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;MACAC;MACAC;QACA,uBACA;UACA;UACA;QACA,GACA;UACA;UACA;QACA,GACA;UACA;UACA;QACA,GACA;UACA;UACA;QACA;MAEA;MACAC,yBACAC,oCACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IAEA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;;IAEA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACAC;UACAC,6BACA;UACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACAC;QACAC;MACA;MAEA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,uCACAC;gBAAA;gBAAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAAC;gBAAA;gBAEA;gBACAC,2CAgCA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAGA;gBACAnB;kBACAoB;kBACAC;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAV;gBACAG;kBACAC;kBACAO;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGA;QACAR;MACA;IACA;IAEA;IACAS;MACA;QAwBA;QACA;QACA;;QAGA;QACAC;UACAN;UACAO;UACAC;UAAA;UACAT;UAAA;UACAR;YACAkB;UACA;UACAjB;YACAkB;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;;QAGA;QACA;;QAEA;QACA;UACAC;QACA;QACA;QAAA,KACA;UACAA;QACA;QACA;QAAA,KACA;UACA;UACA;;UAEA;UACA;YACAA;UACA;QACA;QACA;QAAA,KACA;UACA;UACA;;UAEA;UACA;YACAA;UACA;QACA;QACA;QAAA,KACA;UACA;UACA;;UAEA;UACA;YACAA;UACA;QACA;;QAEA;;QAEA;QACA;UACAhB;YACAC;YACAO;UACA;UAEAS;YACAjB;UACA;QACA;MACA;QACAH;QACAG;UACAC;UACAO;QACA;QACAS;UACAjB;QACA;MACA;IACA;IAEA;AACA;AACA;IACAkB;MACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA,mEACAC,kBACAC,gBACAC,SACA;;QAGA;QACA;UACA;UACA;YACA;UACA;YACA;UACA;YACAxB;UACA;;UAEA;UACA;UACA;YACAyB;UACA;YACAA;UACA;YACAA;UACA;YACAA;YACAzB;UACA;;UAEA;UACA;YACAyB;YACAzB;UACA;;UAEA;UACA;UAEAA;YACA0B;YACAC;UACA;QACA,QACA;MACA;QACA3B;QACA;UACA;QACA;MACA;IACA;IAEA4B;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;QACA7B;UACA0B;UACAC;QACA;QACA;MACA;;MAEA;;MAEA;MACA;QACA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;;QAEA;QACA,mEACAL,mBACAC,gBACAC,SACA;;QAGA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA,QACA;MAEAxB;QACA0B;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAG;MAAA;MACA;MACA;QACA;UACAhC;YACA;;YAEA;YACA;YAEAK;cACA4B;cACA3B;YACA;YAEA;cACA;YACA;cACAD;gBACAC;gBACAO;cACA;YACA;cACAR;YACA;UACA;QACA;MACA;QACAA;UACAC;UACAO;QACA;MACA;IACA;IAEA;AACA;AACA;IACAqB;MAAA;MACA;MACA;QACA;QACA;UAAAL;UAAAM;;QAEA;QACA;UACA9B;YACAQ;YACAP;UACA;UACA;UACAgB;YAAA;UAAA;QACA;UACAjB;YACA+B;YACAC;UACA;QACA;MACA;QACA;QACA;UACAhC;YACAQ;YACAP;UACA;UACA;UACAgB;YAAA;UAAA;QACA;UACAjB;YACA+B;YACAC;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACAjC;QACA4B;MACA;MACAM;QACA;QACA;UACA;UACA;YACAtD;UACA;UAEA;;UAEA;UACA;YACA;UACA;QACA;MACA;QACAoB;UACA+B;UACAC;QACA;MACA;QACAhC;MACA;IACA;IAEAmC;MACAnC;IACA;IAEA;IACAoC;MAAA;MACA;MACA;MAoCApC;QACAqC;QACAC;QACAC;QACA5C;UACA;UACA;YACA;UACA;QACA;QACAC;UACAC;UACAG;YACAC;YACAO;UACA;QACA;MACA;IAEA;IAEA;IACAgC;MAAA;MAAA;MACAxC;QAAAC;MAAA;MAEA;QACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA,WAyBA;YACA;YACAS;cACAN;cACAO;cACAC;cACAjB;gBACA;kBACA;gBACA;gBACA;kBACA8C;kBACAC;kBACAC;kBACAL;gBACA;gBACAzB;cACA;cACAjB;gBACAC;gBACAiB;cACA;YACA;UACA;QACA;MACA;MAEA8B,4BACAC;QACA7C;QACAA;UAAAC;UAAAO;QAAA;MACA,GACAsC;QACA9C;QACAA;UAAAC;UAAAO;QAAA;MACA;IACA;IAEA;IACAuC;MAAA;MACA/C;QACAC;QACA8B;QACApC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAqD;sBAAA;sBAAA;oBAAA;oBACA7C;oBAEAH;sBACAC;oBACA;oBAAA;oBAAA,KAIAE;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OACAO;sBACA+B;sBACA7D;wBACAqE;sBACA;oBACA;kBAAA;oBAGA;oBACA;oBAEAjD;sBACAC;sBACAO;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEAX;oBACAG;sBACAC;sBACAO;oBACA;kBAAA;oBAAA;oBAEAR;oBAAA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACA;IACAkD;MACA;;MAEA;MACA;QACA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACAxC;UACA+B;UACA7D;YACAqE;UACA;QACA,wBACA;UACApD;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAsD;MACA;MAEA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACj2BA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,i3BAAG,EAAC,C;;;;;;;;;;;ACAp4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/notice/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/notice/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=d1175310&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/notice/edit.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=template&id=d1175310&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker\" */ \"@/uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    spEditor: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/sp-editor/components/sp-editor/sp-editor\" */ \"@/uni_modules/sp-editor/components/sp-editor/sp-editor.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.formData.attachments && _vm.formData.attachments.length > 0\n  var l0 = g0\n    ? _vm.__map(_vm.formData.attachments, function (file, index) {\n        var $orig = _vm.__get_orig(file)\n        var m0 = _vm.getFileIcon(file.type)\n        var m1 = file.size ? _vm.formatFileSize(file.size) : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"uni-container\">\n    <uni-forms ref=\"form\" :model=\"formData\" validate-trigger=\"submit\" err-show-type=\"toast\">\n      <uni-forms-item name=\"title\" label=\"公告标题\" required>\n        <uni-easyinput placeholder=\"请输入公告标题\" v-model=\"formData.title\"></uni-easyinput>\n      </uni-forms-item>\n      \n      <uni-forms-item name=\"category\" label=\"公告分类\" required>\n        <uni-data-picker v-model=\"formData.category\" :localdata=\"formOptions.category_localdata\"></uni-data-picker>\n      </uni-forms-item>\n      \n      <uni-forms-item name=\"isTop\" label=\"是否置顶\">\n        <switch @change=\"binddata('isTop', $event.detail.value)\" :checked=\"formData.isTop\"></switch>\n      </uni-forms-item>\n      \n      <!-- 隐藏的发布者信息字段，仅在新增模式下显示 -->\n      <view v-if=\"!formDataId\" style=\"display: none;\">\n        <uni-forms-item name=\"publisher\" label=\"发布者ID\">\n          <uni-easyinput v-model=\"formData.publisher\"></uni-easyinput>\n        </uni-forms-item>\n        <uni-forms-item name=\"publisherName\" label=\"发布者名称\">\n          <uni-easyinput v-model=\"formData.publisherName\"></uni-easyinput>\n        </uni-forms-item>\n      </view>\n      \n      <uni-forms-item name=\"content\" label=\"公告内容\" required>\n        <view class=\"editor-container\">\n          <sp-editor ref=\"editor\" :placeholder=\"'请输入公告内容...'\" @init=\"onEditorInit\" @input=\"onEditorInput\" @upinImage=\"handleImageUpload\" @removeImage=\"handleImageRemove\"></sp-editor>\n        </view>\n      </uni-forms-item>\n      \n      <!-- 附件上传 -->\n      <uni-forms-item name=\"attachments\" label=\"上传附件\">\n        <view class=\"attachment-area\">\n          <view class=\"upload-btn\" @click=\"chooseFile\">\n            <uni-icons type=\"upload\" size=\"20\" color=\"#ffffff\"></uni-icons>\n            <text class=\"upload-text\">上传附件</text>\n          </view>\n        </view>\n        \n        <view class=\"attachment-list\" v-if=\"formData.attachments && formData.attachments.length > 0\">\n          <view v-for=\"(file, index) in formData.attachments\" :key=\"index\" class=\"attachment-item\">\n            <view class=\"file-info\">\n              <uni-icons :type=\"getFileIcon(file.type)\" size=\"20\" color=\"#3a86ff\"></uni-icons>\n              <text class=\"file-name\">{{file.name}}</text>\n              <text class=\"file-size\" v-if=\"file.size\">{{formatFileSize(file.size)}}</text>\n            </view>\n            <text class=\"delete-btn\" @click=\"deleteFile(index)\">×</text>\n          </view>\n        </view>\n      </uni-forms-item>\n      \n      <view class=\"uni-button-group\">\n        <button type=\"primary\" class=\"uni-button\" @click=\"submit\">提交</button>\n        <button type=\"default\" class=\"uni-button\" @click=\"goBack\">取消</button>\n      </view>\n    </uni-forms>\n  </view>\n</template>\n\n<script>\n  import { validator } from '../../js_sdk/validator/notice.js';\n\n  const db = uniCloud.database();\n  const dbCollectionName = 'notice';\n\n  function getValidator(fields) {\n    let result = {}\n    for (let key in validator) {\n      if (fields.indexOf(key) > -1) {\n        result[key] = validator[key]\n      }\n    }\n    return result\n  }\n\n  export default {\n    data() {\n      let formData = {\n        \"title\": \"\",\n        \"content\": \"\",\n        \"category\": \"\",\n        \"isTop\": false,\n        \"createTime\": null,\n        \"publisher\": \"\",\n        \"publisherName\": \"\",\n        \"readCount\": 0,\n        \"images\": [],\n        \"avatar\": \"\",\n        \"attachments\": []\n      }\n      return {\n        formData,\n        formOptions: {\n          \"category_localdata\": [\n            {\n              \"value\": \"公告通知\",\n              \"text\": \"公告通知\"\n            },\n            {\n              \"value\": \"重要通知\",\n              \"text\": \"重要通知\"\n            },\n            {\n              \"value\": \"活动通知\",\n              \"text\": \"活动通知\"\n            },\n            {\n              \"value\": \"其他通知\",\n              \"text\": \"其他通知\"\n            }\n          ]\n        },\n        rules: {\n          ...getValidator(Object.keys(formData))\n        },\n        formDataId: '',\n        editorCtx: null,\n        editorInitialized: false\n      }\n    },\n    onLoad(e) {\n      // 检查用户权限\n      this.checkPermission()\n      \n      if (e.id) {\n        const id = e.id\n        this.formDataId = id\n        this.getDetail(id)\n      }\n    },\n    onShow() {\n      // 每次页面显示时重新检查权限\n      this.checkPermission()\n    },\n    onReady() {\n      this.$refs.form.setRules(this.rules)\n      \n      // 如果是新增模式，获取用户信息\n      if (!this.formDataId) {\n        this.getUserInfo()\n      }\n    },\n    methods: {\n      // 编辑器初始化完成\n      onEditorInit(editorCtx) {\n        this.editorCtx = editorCtx\n        this.editorInitialized = true\n        \n        // 如果已经获取到了数据，设置编辑器内容\n        if (this.formData.content && this.editorCtx) {\n          this.setEditorContent()\n        }\n      },\n      \n      // 设置编辑器内容\n      setEditorContent() {\n        if (this.editorCtx && this.formData.content) {\n          this.editorCtx.setContents({\n            html: this.formData.content,\n            success: () => {\n            },\n            fail: (err) => {\n              console.error('设置编辑器内容失败:', err)\n            }\n          })\n        }\n      },\n      \n      // 编辑器内容变化\n      onEditorInput(e) {\n        this.formData.content = e.html\n      },\n      \n      // 处理图片上传\n      handleImageUpload(tempFiles, editorCtx) {\n        uni.showLoading({\n          title: '上传中'\n        });\n        \n        (async () => {\n          for (let file of tempFiles) {\n            try {\n              // 获取文件路径\n              const filePath = file.path || file.tempFilePath;\n              \n              // #ifdef H5\n              // H5环境下特殊处理\n              let fileExtension = '';\n              if (file.name) {\n                // 如果有原始文件名，从中获取扩展名\n                const match = file.name.match(/\\.([^.]+)$/);\n                fileExtension = match ? match[1].toLowerCase() : 'png';\n              } else if (filePath.includes('.')) {\n                // 从路径中获取扩展名\n                const match = filePath.match(/\\.([^.]+)$/);\n                fileExtension = match ? match[1].toLowerCase() : 'png';\n              } else {\n                fileExtension = 'png';\n              }\n              \n              // 生成文件名\n              const timestamp = Date.now();\n              const random = Math.floor(Math.random() * 1000);\n              const fileName = `${timestamp}_${random}.${fileExtension}`;\n              \n              // 直接使用uniCloud上传\n              const uploadResult = await uniCloud.uploadFile({\n                filePath: filePath,\n                cloudPath: `notice/images/${fileName}`,\n                cloudPathAsRealPath: true\n              });\n              const fileID = uploadResult.fileID;\n              // #endif\n              \n              // #ifndef H5\n              // 非H5环境使用原有方式\n              const fileID = await this.uploadFile(filePath, 'notice/images/', file);\n              // #endif\n              \n              // 插入图片到编辑器\n              editorCtx.insertImage({\n                src: fileID,\n                alt: '图片'\n              })\n              \n              // 将上传的图片ID保存到formData中，方便后续管理\n              if (!this.formData.images) {\n                this.formData.images = [];\n              }\n              this.formData.images.push(fileID);\n            } catch (err) {\n              console.error('上传失败:', err)\n              uni.showToast({\n                title: '上传失败',\n                icon: 'none'\n              })\n            }\n          }\n        })().finally(() => {\n          uni.hideLoading()\n        })\n      },\n      \n      // 上传文件通用方法\n      uploadFile(filePath, cloudPath, file) {\n        return new Promise((resolve, reject) => {\n          // #ifdef H5\n          // H5环境下处理文件扩展名\n          let fileExtension = '';\n          if (filePath.includes('.')) {\n            fileExtension = filePath.split('.').pop();\n          } else if (filePath.startsWith('blob:')) {\n            // 如果是blob URL，从file对象获取正确的扩展名\n            if (file && file.file && file.file.name) {\n              const match = file.file.name.match(/\\.([^.]+)$/);\n              fileExtension = match ? match[1] : 'png';\n            } else {\n              fileExtension = 'png'; // 默认为png\n            }\n          }\n          \n          // 确保路径不包含blob URL信息\n          let uploadPath = cloudPath;\n          if (uploadPath.includes('blob:')) {\n            uploadPath = 'notice/images/';\n          }\n          // #endif\n\n          // #ifndef H5\n          // 非H5环境下的扩展名获取\n          const fileExtension = filePath.match(/\\.(\\w+)$/)[1];\n          const uploadPath = cloudPath;\n          // #endif\n\n          // 使用uniCloud上传图片\n          uniCloud.uploadFile({\n            filePath: filePath,\n            cloudPath: uploadPath + new Date().getTime() + '_' + Math.floor(Math.random() * 1000) + '.' + fileExtension,\n            cloudPathAsRealPath: true, // 启用真实目录支持\n            file: file, // H5环境下传入原始文件对象\n            success: (uploadRes) => {\n              resolve(uploadRes.fileID)\n            },\n            fail: (err) => {\n              reject(err)\n            }\n          })\n        })\n      },\n      \n      // 检查用户权限\n      checkPermission() {\n        try {\n          // 检查用户是否有编辑权限\n          const pagesUserInfo = uni.getStorageSync('uni-id-pages-userInfo') || {}\n          const uniIdUserInfo = uni.getStorageSync('uni_id_user_info') || {}\n          // 获取当前用户信息\n          const currentUserInfo = uniCloud.getCurrentUserInfo()\n          \n          \n          // 默认设置为无权限\n          let hasPermission = false\n          \n          // 检查Pages用户信息中的用户名\n          if (pagesUserInfo && (pagesUserInfo.username === 'admin' || pagesUserInfo.username === 'reviser')) {\n            hasPermission = true\n          } \n          // 检查UniId用户信息中的用户名\n          else if (uniIdUserInfo && (uniIdUserInfo.username === 'admin' || uniIdUserInfo.username === 'reviser')) {\n            hasPermission = true\n          }\n          // 检查Pages用户信息中的角色\n          else if (pagesUserInfo && pagesUserInfo.role) {\n            // 确保role是数组\n            const roles = Array.isArray(pagesUserInfo.role) ? pagesUserInfo.role : [pagesUserInfo.role]\n            \n            // 如果用户是管理员或发布人，则有权限\n            if (roles.includes('admin') || roles.includes('reviser')) {\n              hasPermission = true\n            }\n          }\n          // 检查UniId用户信息中的角色\n          else if (uniIdUserInfo && uniIdUserInfo.role) {\n            // 确保role是数组\n            const roles = Array.isArray(uniIdUserInfo.role) ? uniIdUserInfo.role : [uniIdUserInfo.role]\n            \n            // 如果用户是管理员或发布人，则有权限\n            if (roles.includes('admin') || roles.includes('reviser')) {\n              hasPermission = true\n            }\n          }\n          // 检查CurrentUserInfo中的角色\n          else if (currentUserInfo && currentUserInfo.role) {\n            // 确保role是数组\n            const roles = Array.isArray(currentUserInfo.role) ? currentUserInfo.role : [currentUserInfo.role]\n            \n            // 如果用户是管理员或发布人，则有权限\n            if (roles.includes('admin') || roles.includes('reviser')) {\n              hasPermission = true\n            }\n          }\n          \n          // 调试输出\n          \n          // 如果没有权限，跳转回列表页\n          if (!hasPermission) {\n            uni.showToast({\n              title: '您没有编辑权限',\n              icon: 'none'\n            })\n            \n            setTimeout(() => {\n              uni.navigateBack()\n            }, 1500)\n          }\n        } catch (e) {\n          console.error('检查权限时出错:', e)\n          uni.showToast({\n            title: '权限检查失败',\n            icon: 'none'\n          })\n          setTimeout(() => {\n            uni.navigateBack()\n          }, 1500)\n        }\n      },\n      \n      /**\n       * 获取当前用户信息并设置发布者信息\n       */\n      getUserInfo() {\n        try {\n          // 首先尝试从uniCloud获取当前用户信息\n          const currentUserInfo = uniCloud.getCurrentUserInfo();\n          \n          // 然后从本地存储获取更详细的用户信息\n          const userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};\n          \n          // 尝试从另一个存储位置获取用户信息\n          const uniIdUserInfo = uni.getStorageSync('uni_id_user_info') || {};\n          \n          // 合并用户信息，优先使用本地存储的详细信息\n          const mergedUserInfo = {\n            ...currentUserInfo,\n            ...uniIdUserInfo,\n            ...userInfo\n          };\n          \n          \n          // 如果是编辑模式，不修改原有的发布者信息\n          if (!this.formDataId) {\n            // 设置发布者ID\n            if (mergedUserInfo._id) {\n              this.formData.publisher = mergedUserInfo._id;\n            } else if (mergedUserInfo.uid) {\n              this.formData.publisher = mergedUserInfo.uid;\n            } else {\n              console.warn('未找到有效的用户ID');\n            }\n            \n            // 设置发布者名称\n            let userName = '';\n            if (mergedUserInfo.nickname) {\n              userName = mergedUserInfo.nickname;\n            } else if (mergedUserInfo.username) {\n              userName = mergedUserInfo.username;\n            } else if (mergedUserInfo.realNameAuth && mergedUserInfo.realNameAuth.name) {\n              userName = mergedUserInfo.realNameAuth.name;\n            } else {\n              userName = '未知用户';\n              console.warn('未找到nickname或username，使用默认名称: 未知用户');\n            }\n            \n            // 确保publisherName不为空\n            if (!userName || userName.trim() === '') {\n              userName = '未知用户';\n              console.warn('发布者名称为空，设置为默认值');\n            }\n            \n            // 更新表单中的发布者名称\n            this.formData.publisherName = userName;\n            \n            console.log('最终设置的发布者信息:', {\n              publisher: this.formData.publisher,\n              publisherName: this.formData.publisherName\n            });\n          } else {\n          }\n        } catch (e) {\n          console.error('获取用户信息失败:', e);\n          if (!this.formDataId) {\n            this.formData.publisherName = '未知用户';\n          }\n        }\n      },\n      \n      binddata(name, value) {\n        this.formData[name] = value\n      },\n      \n      /**\n       * 确保发布者信息已正确设置\n       */\n      ensurePublisherInfo() {\n        // 如果是编辑模式，不修改原有的发布者信息\n        if (this.formDataId) {\n          console.log('编辑模式，保留原有发布者信息:', {\n            publisher: this.formData.publisher,\n            publisherName: this.formData.publisherName\n          });\n          return;\n        }\n        \n        // 以下代码只在新增模式下执行\n        \n        // 如果发布者ID为空，尝试重新获取\n        if (!this.formData.publisher) {\n          const currentUserInfo = uniCloud.getCurrentUserInfo();\n          if (currentUserInfo && currentUserInfo.uid) {\n            this.formData.publisher = currentUserInfo.uid;\n          }\n        }\n        \n        // 如果发布者名称为空，尝试从多个来源获取用户信息\n        if (!this.formData.publisherName || this.formData.publisherName.trim() === '') {\n          // 尝试从多个存储位置获取用户信息\n          const userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};\n          const uniIdUserInfo = uni.getStorageSync('uni_id_user_info') || {};\n          const currentUserInfo = uniCloud.getCurrentUserInfo();\n          \n          // 合并用户信息\n          const mergedUserInfo = {\n            ...currentUserInfo,\n            ...uniIdUserInfo,\n            ...userInfo\n          };\n          \n          \n          // 按优先级尝试获取用户名\n          if (mergedUserInfo.nickname) {\n            this.formData.publisherName = mergedUserInfo.nickname;\n          } else if (mergedUserInfo.username) {\n            this.formData.publisherName = mergedUserInfo.username;\n          } else if (mergedUserInfo.realNameAuth && mergedUserInfo.realNameAuth.name) {\n            this.formData.publisherName = mergedUserInfo.realNameAuth.name;\n          } else {\n            this.formData.publisherName = '未知用户';\n          }\n        } else {\n        }\n        \n        console.log('提交前最终的发布者信息:', {\n          publisher: this.formData.publisher,\n          publisherName: this.formData.publisherName\n        });\n      },\n\n      /**\n       * 验证表单并提交\n       */\n      submit() {\n        // 获取编辑器内容\n        if (this.editorCtx) {\n          this.editorCtx.getContents({\n            success: (res) => {\n              this.formData.content = res.html\n              \n              // 提交前再次确认发布者信息\n              this.ensurePublisherInfo();\n              \n              uni.showLoading({\n                mask: true,\n                title: '提交中...'\n              })\n              \n              this.$refs.form.validate().then((res) => {\n                return this.submitForm(res)\n              }).catch(() => {\n                uni.showToast({\n                  title: '表单验证失败',\n                  icon: 'none'\n                })\n              }).finally(() => {\n                uni.hideLoading()\n              })\n            }\n          })\n        } else {\n          uni.showToast({\n            title: '编辑器未初始化',\n            icon: 'none'\n          })\n        }\n      },\n\n      /**\n       * 提交表单\n       */\n      submitForm(value) {\n        // 在编辑模式下，从提交数据中移除发布者相关字段，保留原有信息\n        if (this.formDataId) {\n          // 创建一个新对象，不包含publisher和publisherName字段\n          const { publisher, publisherName, ...updateData } = value;\n          \n          // 使用 clientDB 提交数据\n          return db.collection(dbCollectionName).doc(this.formDataId).update(updateData).then((res) => {\n            uni.showToast({\n              icon: 'success',\n              title: '修改成功'\n            })\n            this.getOpenerEventChannel().emit('refreshData')\n            setTimeout(() => uni.navigateBack(), 500)\n          }).catch((err) => {\n            uni.showModal({\n              content: err.message || '请求服务失败',\n              showCancel: false\n            })\n          })\n        } else {\n          // 新增模式，提交所有数据\n          return db.collection(dbCollectionName).add(value).then((res) => {\n            uni.showToast({\n              icon: 'success',\n              title: '新增成功'\n            })\n            this.getOpenerEventChannel().emit('refreshData')\n            setTimeout(() => uni.navigateBack(), 500)\n          }).catch((err) => {\n            uni.showModal({\n              content: err.message || '请求服务失败',\n              showCancel: false\n            })\n          })\n        }\n      },\n\n      /**\n       * 获取表单数据\n       * @param {Object} id\n       */\n      getDetail(id) {\n        uni.showLoading({\n          mask: true\n        })\n        db.collection(dbCollectionName).doc(id).field(\"title,content,category,isTop,createTime,publisher,publisherName,readCount,images,avatar,attachments\").get().then((res) => {\n          const data = res.result.data[0]\n          if (data) {\n            // 确保attachments字段是数组\n            if (!data.attachments) {\n              data.attachments = [];\n            }\n            \n            this.formData = data\n            \n            // 如果编辑器已初始化，设置内容\n            if (this.editorInitialized && this.formData.content) {\n              this.setEditorContent()\n            }\n          }\n        }).catch((err) => {\n          uni.showModal({\n            content: err.message || '请求服务失败',\n            showCancel: false\n          })\n        }).finally(() => {\n          uni.hideLoading()\n        })\n      },\n      \n      goBack() {\n        uni.navigateBack()\n      },\n      \n      // 选择文件\n      chooseFile() {\n        // 微信小程序支持的文件类型\n        const allowedTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'txt'];\n        \n        // #ifdef H5\n        // 创建input元素\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.multiple = true; // 允许多选\n        input.accept = '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt';\n        \n        input.onchange = (event) => {\n          const files = Array.from(event.target.files);\n          if (files.length > 5) {\n            uni.showToast({\n              title: '最多只能选择5个文件',\n              icon: 'none'\n            });\n            return;\n          }\n          \n          // 转换文件格式以匹配uploadFiles方法的预期\n          const tempFiles = files.map(file => ({\n            name: file.name,\n            path: window.URL.createObjectURL(file),\n            size: file.size,\n            type: file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase(),\n            file: file // 保存原始文件对象用于上传\n          }));\n          \n          this.uploadFiles(tempFiles, true); // 传入true表示是H5环境\n        };\n        \n        // 触发文件选择\n        input.click();\n        // #endif\n        \n        // #ifdef MP-WEIXIN\n        uni.chooseMessageFile({\n          count: 5,\n          type: 'file',\n          extension: allowedTypes,\n          success: (res) => {\n            const tempFiles = res.tempFiles;\n            if (tempFiles.length > 0) {\n              this.uploadFiles(tempFiles, false);\n            }\n          },\n          fail: (err) => {\n            console.error('选择文件失败:', err);\n            uni.showToast({\n              title: '选择文件失败',\n              icon: 'none'\n            });\n          }\n        });\n        // #endif\n      },\n      \n      // 上传文件\n      uploadFiles(files, isH5 = false) {\n        uni.showLoading({ title: '上传中...' });\n        \n        const uploadPromises = files.map(file => {\n          return new Promise((resolve, reject) => {\n            const fileName = file.name;\n            const fileExt = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();\n            \n            // 确保文件名安全\n            const safeFileName = fileName.replace(/[^\\w\\u4e00-\\u9fa5\\.-]/g, '_');\n            \n            // 使用年月日创建目录结构\n            const now = new Date();\n            const year = now.getFullYear();\n            const month = String(now.getMonth() + 1).padStart(2, '0');\n            const day = String(now.getDate()).padStart(2, '0');\n            const dateFolder = `${year}${month}${day}`;\n            \n            // 创建唯一文件名\n            const uniqueFileName = `${Date.now()}_${Math.floor(Math.random() * 1000)}_${safeFileName}`;\n            \n            // H5环境特殊处理\n            if (isH5) {\n              // #ifdef H5\n              uniCloud.uploadFile({\n                filePath: file.path,\n                cloudPath: `notice/attachments/${dateFolder}/${uniqueFileName}`,\n                cloudPathAsRealPath: true,\n                file: file.file, // H5环境下传入原始文件对象\n                success: (res) => {\n                  if (!this.formData.attachments) {\n                    this.formData.attachments = [];\n                  }\n                  this.formData.attachments.push({\n                    name: fileName,\n                    url: res.fileID,\n                    size: file.size,\n                    type: fileExt.substring(1)\n                  });\n                  resolve();\n                },\n                fail: (err) => {\n                  console.error('上传失败:', err);\n                  reject(err);\n                }\n              });\n              // #endif\n            } else {\n              // 非H5环境使用原有的上传方式\n              uniCloud.uploadFile({\n                filePath: file.path,\n                cloudPath: `notice/attachments/${dateFolder}/${uniqueFileName}`,\n                cloudPathAsRealPath: true,\n                success: (res) => {\n                  if (!this.formData.attachments) {\n                    this.formData.attachments = [];\n                  }\n                  this.formData.attachments.push({\n                    name: fileName,\n                    url: res.fileID,\n                    size: file.size,\n                    type: fileExt.substring(1)\n                  });\n                  resolve();\n                },\n                fail: (err) => {\n                  console.error('上传失败:', err);\n                  reject(err);\n                }\n              });\n            }\n          });\n        });\n        \n        Promise.all(uploadPromises)\n          .then(() => {\n            uni.hideLoading();\n            uni.showToast({ title: '上传成功', icon: 'success' });\n          })\n          .catch(() => {\n            uni.hideLoading();\n            uni.showToast({ title: '部分文件上传失败', icon: 'none' });\n          });\n      },\n      \n      // 删除文件\n      deleteFile(index) {\n        uni.showModal({\n          title: '确认删除',\n          content: '是否确认删除该附件？',\n          success: async (res) => {\n            if (res.confirm) {\n              const file = this.formData.attachments[index];\n              \n              uni.showLoading({\n                title: '删除中...'\n              });\n              \n              try {\n                // 如果已上传，尝试从云存储删除\n                if (file.url) {\n                  await uniCloud.callFunction({\n                    name: 'delete-file',\n                    data: {\n                      fileList: [file.url]\n                    }\n                  });\n                }\n                \n                // 从列表中移除\n                this.formData.attachments.splice(index, 1);\n                \n                uni.showToast({\n                  title: '删除成功',\n                  icon: 'success'\n                });\n              } catch (err) {\n                console.error('删除失败:', err);\n                uni.showToast({\n                  title: '删除失败',\n                  icon: 'error'\n                });\n              } finally {\n                uni.hideLoading();\n              }\n            }\n          }\n        });\n      },\n      \n      // 从编辑器移除图片时删除云存储中的文件\n      // 此方法需要在sp-editor组件中添加移除图片的事件调用\n      handleImageRemove(src) {\n        if (!src) return;\n        \n        // 从formData.images中移除\n        if (this.formData.images && this.formData.images.length > 0) {\n          const index = this.formData.images.indexOf(src);\n          if (index !== -1) {\n            this.formData.images.splice(index, 1);\n          }\n        }\n        \n        // 调用云函数删除文件\n        try {\n          uniCloud.callFunction({\n            name: 'delete-file',\n            data: {\n              fileList: [src]\n            }\n          }).then(res => {\n          }).catch(err => {\n            console.error('云函数删除图片失败:', err);\n          });\n        } catch (e) {\n          console.error('调用云函数删除图片时发生异常:', e);\n        }\n      },\n      \n      // 获取文件类型图标\n      getFileIcon(fileType) {\n        if (!fileType) return 'file';\n        \n        const type = fileType.toLowerCase();\n        const iconMap = {\n          'pdf': 'pdf',\n          'doc': 'file',\n          'docx': 'file',\n          'xls': 'file',\n          'xlsx': 'file',\n          'ppt': 'file',\n          'pptx': 'file',\n          'txt': 'file'\n        };\n        return iconMap[type] || 'file';\n      },\n      \n      // 格式化文件大小\n      formatFileSize(size) {\n        if (!size) return '';\n        \n        if (size < 1024) {\n          return size + 'B';\n        } else if (size < 1024 * 1024) {\n          return (size / 1024).toFixed(2) + 'KB';\n        } else {\n          return (size / (1024 * 1024)).toFixed(2) + 'MB';\n        }\n      },\n    }\n  }\n</script>\n\n<style>\n  .uni-container {\n    padding: 30rpx;\n    background-color: #f8f9fc;\n    min-height: 100vh;\n  }\n  \n  /* 表单样式 */\n  .uni-forms {\n    background-color: #fff;\n    border-radius: 16rpx;\n    padding: 40rpx 30rpx;\n    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n    margin-bottom: 30rpx;\n  }\n  \n  .uni-forms-item {\n    margin-bottom: 30rpx;\n  }\n  \n  .uni-forms-item__label {\n    font-size: 28rpx;\n    font-weight: 600;\n    color: #333;\n    margin-bottom: 12rpx;\n    position: relative;\n  }\n  \n  .uni-forms-item__label.required::before {\n    content: '*';\n    color: #f56c6c;\n    margin-right: 4rpx;\n  }\n  \n  .uni-easyinput__content {\n    height: 80rpx !important;\n    background-color: #f8f9fc !important;\n    border: 2rpx solid #e5e7eb !important;\n    border-radius: 12rpx !important;\n    transition: all 0.3s ease;\n  }\n  \n  .uni-easyinput__content:focus-within {\n    border-color: #3a86ff !important;\n    box-shadow: 0 0 0 2px rgba(58, 134, 255, 0.1);\n    background-color: #fff !important;\n  }\n  \n  /* 编辑器样式 */\n  .editor-container {\n    border: 2rpx solid #e5e7eb;\n    border-radius: 12rpx;\n    overflow: hidden;\n    margin-bottom: 20rpx;\n    background-color: #fff;\n  }\n  \n  /* #ifdef H5 */\n  .editor-container {\n    height: 1000rpx;\n  }\n  /* #endif */\n  \n  /* #ifdef MP-WEIXIN */\n  .editor-container {\n    height: 600rpx;\n  }\n  /* #endif */\n  \n  .uni-button-group {\n    margin-top: 50rpx;\n    display: flex;\n    justify-content: center;\n    gap: 40rpx;\n  }\n\n  .uni-button {\n    width: 280rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 28rpx;\n    font-weight: 500;\n    border-radius: 40rpx;\n    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\n    transition: all 0.3s ease;\n  }\n  \n  button[type=\"primary\"].uni-button {\n    background: linear-gradient(135deg, #3a86ff 0%, #2a6edf 100%);\n  }\n  \n  button[type=\"default\"].uni-button {\n    background-color: #fff;\n    border: 2rpx solid #e5e7eb;\n    color: #666;\n  }\n  \n  .uni-button:active {\n    transform: translateY(2rpx);\n    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);\n  }\n\n  /* 附件上传区域 */\n  .attachment-area {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20rpx;\n  }\n\n  .upload-btn {\n    padding: 16rpx 30rpx;\n    background: linear-gradient(135deg, #3a86ff 0%, #2a6edf 100%);\n    border-radius: 40rpx;\n    display: flex;\n    align-items: center;\n    transition: all 0.3s ease;\n    box-shadow: 0 4rpx 8rpx rgba(58, 134, 255, 0.25);\n  }\n  \n  .upload-btn:active {\n    transform: translateY(2rpx);\n    box-shadow: 0 2rpx 4rpx rgba(58, 134, 255, 0.15);\n  }\n\n  .upload-text {\n    color: #fff;\n    font-size: 26rpx;\n    margin-left: 10rpx;\n    font-weight: 500;\n  }\n\n  .attachment-list {\n    display: flex;\n    flex-direction: column;\n    margin-top: 20rpx;\n  }\n\n  /* #ifdef H5 */\n  .attachment-list {\n    width: 100%;\n  }\n  /* #endif */\n\n  /* #ifdef MP-WEIXIN */\n  .attachment-list {\n    width: 90%;\n  }\n  /* #endif */\n\n  .attachment-item {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 20rpx 24rpx;\n    background-color: #f8f9fc;\n    border-radius: 12rpx;\n    margin-bottom: 16rpx;\n    transition: all 0.3s ease;\n    border-left: 4rpx solid #3a86ff;\n  }\n  \n  .attachment-item:active {\n    background-color: #eef1f8;\n  }\n\n  .file-info {\n    display: flex;\n    align-items: center;\n    overflow: hidden;\n  }\n\n  /* #ifdef H5 */\n  .file-info {\n    max-width: 100%;\n  }\n  /* #endif */\n\n  /* #ifdef MP-WEIXIN */\n  .file-info {\n    max-width: 85%;\n  }\n  /* #endif */\n\n  .file-name {\n    flex: 1;\n    padding: 0 10px;\n    color: #333;\n    \n    /* #ifdef H5 */\n    white-space: normal;\n    word-break: break-all;\n    text-overflow: initial;\n    display: block;\n    /* #endif */\n    \n    /* #ifndef H5 */\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    /* #endif */\n  }\n\n  /* #ifdef MP-WEIXIN */\n  .file-name {\n    max-width: 320rpx;\n  }\n  /* #endif */\n\n  .file-size {\n    margin-left: 16rpx;\n    font-size: 24rpx;\n    color: #999;\n    flex-shrink: 0;\n  }\n\n  .delete-btn {\n    width: 44rpx;\n    height: 44rpx;\n    line-height: 44rpx;\n    text-align: center;\n    border-radius: 50%;\n    background-color: #f0f0f0;\n    color: #f56c6c;\n    cursor: pointer;\n    font-size: 32rpx;\n    margin-left: 20rpx;\n    flex-shrink: 0;\n    transition: all 0.2s ease;\n  }\n  \n  .delete-btn:active {\n    background-color: #f56c6c;\n    color: #fff;\n    transform: scale(0.9);\n  }\n  \n  /* 开关样式 */\n  .switch-container {\n    display: flex;\n    align-items: center;\n    margin-top: 8rpx;\n  }\n  \n  .switch-label {\n    font-size: 26rpx;\n    color: #666;\n    margin-left: 20rpx;\n  }\n  \n  /* 添加动画效果 */\n  @keyframes fadeIn {\n    from { opacity: 0; transform: translateY(20rpx); }\n    to { opacity: 1; transform: translateY(0); }\n  }\n  \n  .uni-forms {\n    animation: fadeIn 0.4s ease;\n  }\n  \n  .attachment-item {\n    animation: fadeIn 0.3s ease;\n  }\n  \n  /* 提示信息样式 */\n  .form-tips {\n    font-size: 24rpx;\n    color: #999;\n    margin-top: 8rpx;\n    padding-left: 16rpx;\n    border-left: 4rpx solid #3a86ff;\n  }\n  \n  /* 编辑页面特有样式 */\n  .edit-header {\n    padding: 20rpx;\n    background-color: #fff;\n    border-radius: 16rpx;\n    margin-bottom: 20rpx;\n    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n  }\n  \n  .edit-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #333;\n  }\n  \n  .edit-info {\n    font-size: 24rpx;\n    color: #999;\n  }\n</style>\n\n\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571665820\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
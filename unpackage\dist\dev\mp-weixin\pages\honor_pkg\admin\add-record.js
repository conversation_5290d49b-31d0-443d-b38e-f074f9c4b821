require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/honor_pkg/admin/add-record"],{

/***/ 252:
/*!*************************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fhonor_pkg%2Fadmin%2Fadd-record"} ***!
  \*************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _addRecord = _interopRequireDefault(__webpack_require__(/*! ./pages/honor_pkg/admin/add-record.vue */ 253));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_addRecord.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 253:
/*!****************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/admin/add-record.vue ***!
  \****************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _add_record_vue_vue_type_template_id_0c706de4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./add-record.vue?vue&type=template&id=0c706de4&scoped=true& */ 254);
/* harmony import */ var _add_record_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./add-record.vue?vue&type=script&lang=js& */ 256);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _add_record_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _add_record_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _add_record_vue_vue_type_style_index_0_id_0c706de4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./add-record.vue?vue&type=style&index=0&id=0c706de4&lang=scss&scoped=true& */ 260);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _add_record_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _add_record_vue_vue_type_template_id_0c706de4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _add_record_vue_vue_type_template_id_0c706de4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "0c706de4",
  null,
  false,
  _add_record_vue_vue_type_template_id_0c706de4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/honor_pkg/admin/add-record.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 254:
/*!***********************************************************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/admin/add-record.vue?vue&type=template&id=0c706de4&scoped=true& ***!
  \***********************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_template_id_0c706de4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add-record.vue?vue&type=template&id=0c706de4&scoped=true& */ 255);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_template_id_0c706de4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_template_id_0c706de4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_template_id_0c706de4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_template_id_0c706de4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 255:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/honor_pkg/admin/add-record.vue?vue&type=template&id=0c706de4&scoped=true& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    uniEasyinput: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput */ "uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue */ 551))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.formData.reason.length
  var g1 = _vm.formData.images.length
  var g2 = _vm.formData.images.length
  var l0 = g2 > 0 ? _vm.formData.images.slice(0, 3) : null
  var g3 = g2 > 0 ? _vm.formData.images.length : null
  var g4 = g2 > 0 && g3 > 3 ? _vm.formData.images.length : null
  var g5 = _vm.showDepartmentSelector ? _vm.newDepartmentName.trim() : null
  var g6 = _vm.showDepartmentSelector ? _vm.departmentOptions.length : null
  var l1 = _vm.showDepartmentSelector
    ? _vm.__map(_vm.departmentOptions, function (dept, __i0__) {
        var $orig = _vm.__get_orig(dept)
        var g7 = _vm.departmentOptions.length
        return {
          $orig: $orig,
          g7: g7,
        }
      })
    : null
  var g8 = _vm.showBatchSelector ? _vm.recentBatches.length : null
  var l2 =
    _vm.showBatchSelector && g8 > 0
      ? _vm.__map(_vm.recentBatches, function (batch, __i2__) {
          var $orig = _vm.__get_orig(batch)
          var m0 = _vm.formatBatchDate(batch.data.meetingDate)
          var m1 = _vm.getBatchTypeText(batch.data.type)
          return {
            $orig: $orig,
            m0: m0,
            m1: m1,
          }
        })
      : null
  var g9 = _vm.showBatchSelector ? _vm.currentMonthBatches.length : null
  var l3 =
    _vm.showBatchSelector && g9 > 0
      ? _vm.__map(_vm.currentMonthBatches, function (batch, __i3__) {
          var $orig = _vm.__get_orig(batch)
          var m2 = _vm.formatBatchDate(batch.data.meetingDate)
          var m3 = _vm.getBatchTypeText(batch.data.type)
          return {
            $orig: $orig,
            m2: m2,
            m3: m3,
          }
        })
      : null
  var g10 = _vm.showBatchSelector ? _vm.filteredOlderBatches.length : null
  var g11 = _vm.showBatchSelector && g10 > 0 ? _vm.olderBatches.length : null
  var l4 =
    _vm.showBatchSelector && g10 > 0
      ? _vm.__map(_vm.filteredOlderBatches, function (batch, __i4__) {
          var $orig = _vm.__get_orig(batch)
          var m4 = _vm.formatBatchDate(batch.data.meetingDate)
          var m5 = _vm.getBatchTypeText(batch.data.type)
          return {
            $orig: $orig,
            m4: m4,
            m5: m5,
          }
        })
      : null
  var g12 = _vm.showBatchSelector && g10 > 0 ? _vm.olderBatches.length : null
  var g13 =
    _vm.showBatchSelector && g10 > 0 && g12 > _vm.showOlderBatchCount
      ? _vm.olderBatches.length
      : null
  var g14 = _vm.showBatchSelector ? _vm.filteredBatchOptions.length : null
  if (!_vm._isMounted) {
    _vm.e0 = function ($event, index) {
      var _temp = arguments[arguments.length - 1].currentTarget.dataset,
        _temp2 = _temp.eventParams || _temp["event-params"],
        index = _temp2.index
      var _temp, _temp2
      return _vm.previewImage(index)
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        g2: g2,
        l0: l0,
        g3: g3,
        g4: g4,
        g5: g5,
        g6: g6,
        l1: l1,
        g8: g8,
        l2: l2,
        g9: g9,
        l3: l3,
        g10: g10,
        g11: g11,
        l4: l4,
        g12: g12,
        g13: g13,
        g14: g14,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 256:
/*!*****************************************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/admin/add-record.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add-record.vue?vue&type=script&lang=js& */ 257);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 257:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/honor_pkg/admin/add-record.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, uniCloud) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ 13);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var AvatarPicker = function AvatarPicker() {
  __webpack_require__.e(/*! require.ensure | pages/honor_pkg/avatar-picker/avatar-picker */ "pages/honor_pkg/avatar-picker/avatar-picker").then((function () {
    return resolve(__webpack_require__(/*! ../avatar-picker/avatar-picker.vue */ 747));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    AvatarPicker: AvatarPicker
  },
  name: 'AddRecord',
  data: function data() {
    return {
      loading: false,
      saving: false,
      loadingText: '加载中...',
      isEditMode: false,
      recordId: null,
      // 表单数据
      formData: {
        userName: '',
        department: '',
        userAvatar: '',
        honorTypeId: '',
        batchId: '',
        reason: '',
        isFeatured: false,
        images: []
      },
      // 选项数据
      departmentOptions: [],
      honorTypeOptions: [],
      batchOptions: [],
      // 选中的数据
      selectedHonorType: null,
      selectedBatch: null,
      // 智能批次选择器
      showBatchSelector: false,
      batchSearchKeyword: '',
      showOlderBatchCount: 10,
      // 默认显示10个历史批次

      // 部门选择器
      showDepartmentSelector: false,
      newDepartmentName: '',
      // 荣誉类型选择器
      showHonorTypeSelector: false
    };
  },
  computed: {
    // 过滤后的批次选项
    filteredBatchOptions: function filteredBatchOptions() {
      if (!this.batchSearchKeyword.trim()) {
        return this.batchOptions;
      }
      var keyword = this.batchSearchKeyword.toLowerCase();
      return this.batchOptions.filter(function (batch) {
        return batch.text.toLowerCase().includes(keyword);
      });
    },
    // 最近批次（最近5个）
    recentBatches: function recentBatches() {
      var sorted = (0, _toConsumableArray2.default)(this.filteredBatchOptions).sort(function (a, b) {
        var dateA = new Date(a.data.meetingDate || '1970-01-01');
        var dateB = new Date(b.data.meetingDate || '1970-01-01');
        return dateB - dateA;
      });
      return sorted.slice(0, 5);
    },
    // 本月批次
    currentMonthBatches: function currentMonthBatches() {
      var now = new Date();
      var currentMonth = now.getMonth() + 1;
      var currentYear = now.getFullYear();
      return this.filteredBatchOptions.filter(function (batch) {
        var meetingDate = new Date(batch.data.meetingDate);
        return meetingDate.getMonth() + 1 === currentMonth && meetingDate.getFullYear() === currentYear;
      });
    },
    // 历史批次（3个月前的）
    olderBatches: function olderBatches() {
      var threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
      return this.filteredBatchOptions.filter(function (batch) {
        var meetingDate = new Date(batch.data.meetingDate);
        return meetingDate < threeMonthsAgo;
      }).sort(function (a, b) {
        var dateA = new Date(a.data.meetingDate || '1970-01-01');
        var dateB = new Date(b.data.meetingDate || '1970-01-01');
        return dateB - dateA;
      });
    },
    // 分页显示的历史批次
    filteredOlderBatches: function filteredOlderBatches() {
      return this.olderBatches.slice(0, this.showOlderBatchCount);
    }
  },
  onLoad: function onLoad(options) {
    var _this = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      var recordData;
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              // 检查是否为编辑模式
              if (options.mode === 'edit' && options.data) {
                _this.isEditMode = true;
                try {
                  recordData = JSON.parse(decodeURIComponent(options.data));
                  _this.recordId = recordData.id;

                  // 确保头像URL正确设置
                  if (recordData.userAvatar) {
                    _this.formData.userAvatar = recordData.userAvatar;
                  }

                  // 设置其他表单数据
                  _this.formData = _objectSpread(_objectSpread(_objectSpread({}, _this.formData), recordData), {}, {
                    userAvatar: recordData.userAvatar || '' // 确保头像URL不会被覆盖为undefined
                  });

                  delete _this.formData.id; // 移除id字段
                } catch (error) {
                  console.error('解析编辑数据失败:', error);
                }
              }
              _context.next = 3;
              return _this.initializeData();
            case 3:
            case "end":
              return _context.stop();
          }
        }
      }, _callee);
    }))();
  },
  methods: {
    initializeData: function initializeData() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return Promise.all([_this2.loadDepartmentList(), _this2.loadHonorTypeList(), _this2.loadBatchList()]);
              case 3:
                // 如果是编辑模式，设置选中的荣誉类型和批次
                if (_this2.isEditMode) {
                  _this2.setSelectedHonorType();
                  _this2.setSelectedBatch();
                }
                _context2.next = 9;
                break;
              case 6:
                _context2.prev = 6;
                _context2.t0 = _context2["catch"](0);
                uni.showToast({
                  title: '初始化失败，请重试',
                  icon: 'none'
                });
              case 9:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 6]]);
      }))();
    },
    loadDepartmentList: function loadDepartmentList() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var res, data;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _context3.next = 3;
                return uniCloud.callFunction({
                  name: 'honor-admin',
                  data: {
                    action: 'getDepartments'
                  }
                });
              case 3:
                res = _context3.sent;
                if (res.result.code === 0) {
                  data = res.result.data;
                  if (Array.isArray(data)) {
                    _this3.departmentOptions = data.map(function (dept) {
                      return {
                        value: dept.name || dept,
                        text: dept.name || dept
                      };
                    });
                  } else {
                    _this3.departmentOptions = [];
                  }
                }
                _context3.next = 11;
                break;
              case 7:
                _context3.prev = 7;
                _context3.t0 = _context3["catch"](0);
                console.error('加载部门列表失败:', _context3.t0);
                // 提供默认部门选项
                _this3.departmentOptions = [{
                  value: '管理部门',
                  text: '管理部门'
                }, {
                  value: '业务部门',
                  text: '业务部门'
                }, {
                  value: '技术部门',
                  text: '技术部门'
                }, {
                  value: '支持部门',
                  text: '支持部门'
                }];
              case 11:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 7]]);
      }))();
    },
    loadHonorTypeList: function loadHonorTypeList() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var res, data;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.prev = 0;
                _context4.next = 3;
                return uniCloud.callFunction({
                  name: 'honor-admin',
                  data: {
                    action: 'getHonorTypes'
                  }
                });
              case 3:
                res = _context4.sent;
                if (res.result.code === 0) {
                  data = res.result.data;
                  if (Array.isArray(data)) {
                    // 只显示启用的荣誉类型
                    _this4.honorTypeOptions = data.filter(function (type) {
                      return type.isActive !== false;
                    }).map(function (type) {
                      return {
                        value: type._id || type.id,
                        text: type.name,
                        color: type.color || '#3a86ff',
                        data: type
                      };
                    });
                  } else {
                    _this4.honorTypeOptions = [];
                  }
                }
                _context4.next = 11;
                break;
              case 7:
                _context4.prev = 7;
                _context4.t0 = _context4["catch"](0);
                console.error('加载荣誉类型失败:', _context4.t0);
                // 提供默认荣誉类型选项
                _this4.honorTypeOptions = [{
                  value: '1',
                  text: '优秀员工',
                  color: '#3a86ff',
                  data: {
                    id: '1',
                    name: '优秀员工',
                    color: '#3a86ff'
                  }
                }, {
                  value: '2',
                  text: '月度之星',
                  color: '#10b981',
                  data: {
                    id: '2',
                    name: '月度之星',
                    color: '#10b981'
                  }
                }, {
                  value: '3',
                  text: '团队协作奖',
                  color: '#f59e0b',
                  data: {
                    id: '3',
                    name: '团队协作奖',
                    color: '#f59e0b'
                  }
                }];
              case 11:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[0, 7]]);
      }))();
    },
    loadBatchList: function loadBatchList() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var res, data, currentDate, currentMonth, currentYear;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                _context5.next = 3;
                return uniCloud.callFunction({
                  name: 'honor-admin',
                  data: {
                    action: 'getBatches'
                  }
                });
              case 3:
                res = _context5.sent;
                if (res.result.code === 0) {
                  data = res.result.data;
                  if (Array.isArray(data)) {
                    _this5.batchOptions = data.map(function (batch) {
                      return {
                        value: batch._id,
                        text: batch.name,
                        meetingDate: batch.meetingDate,
                        data: batch
                      };
                    });
                  } else {
                    _this5.batchOptions = [];
                  }
                }
                _context5.next = 14;
                break;
              case 7:
                _context5.prev = 7;
                _context5.t0 = _context5["catch"](0);
                console.error('加载批次列表失败:', _context5.t0);
                // 提供默认批次选项
                currentDate = new Date();
                currentMonth = currentDate.getMonth() + 1;
                currentYear = currentDate.getFullYear();
                _this5.batchOptions = [{
                  value: 'default_monthly',
                  text: "".concat(currentYear, "\u5E74").concat(currentMonth, "\u6708\u8868\u5F70"),
                  meetingDate: "".concat(currentYear, "-").concat(currentMonth.toString().padStart(2, '0'), "-01"),
                  data: {
                    _id: 'default_monthly',
                    name: "".concat(currentYear, "\u5E74").concat(currentMonth, "\u6708\u8868\u5F70"),
                    type: 'monthly',
                    meetingDate: "".concat(currentYear, "-").concat(currentMonth.toString().padStart(2, '0'), "-01")
                  }
                }];
              case 14:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 7]]);
      }))();
    },
    setSelectedHonorType: function setSelectedHonorType() {
      var _this6 = this;
      if (this.formData.honorTypeId) {
        var found = this.honorTypeOptions.find(function (option) {
          return option.value === _this6.formData.honorTypeId;
        });
        if (found) {
          this.selectedHonorType = found.data;
        }
      }
    },
    setSelectedBatch: function setSelectedBatch() {
      var _this7 = this;
      if (this.formData.batchId) {
        var found = this.batchOptions.find(function (option) {
          return option.value === _this7.formData.batchId;
        });
        if (found) {
          this.selectedBatch = found.data;
        }
      }
    },
    // 荣誉类型变化
    onHonorTypeChange: function onHonorTypeChange(e) {
      var option = this.honorTypeOptions.find(function (opt) {
        return opt.value === e.detail.value;
      });
      this.selectedHonorType = option ? option.data : null;
    },
    // 批次变化
    onBatchChange: function onBatchChange(e) {
      var option = this.batchOptions.find(function (opt) {
        return opt.value === e.detail.value;
      });
      this.selectedBatch = option ? option.data : null;
    },
    // 精选状态变化
    onFeaturedChange: function onFeaturedChange(e) {
      this.formData.isFeatured = e.detail.value;
    },
    // 头像变化处理
    handleAvatarChange: function handleAvatarChange(data) {
      console.log('头像变化:', data);
      if (data && (data.cloudPath || data.url)) {
        // 优先使用url，如果没有则使用cloudPath
        this.formData.userAvatar = data.url || data.cloudPath;

        // 保存cloudPath用于后续删除
        if (data.cloudPath) {
          this.formData.userAvatarCloudPath = data.cloudPath;
        }

        // 显示成功提示
        uni.showToast({
          title: '头像更新成功',
          icon: 'success',
          duration: 2000
        });
      } else if (!data) {
        // 头像被删除
        this.formData.userAvatar = '';
        this.formData.userAvatarCloudPath = '';
      }
    },
    // 头像上传成功
    onAvatarUploadSuccess: function onAvatarUploadSuccess(data) {
      console.log('头像上传成功:', data);

      // 确保URL正确设置
      if (data.url) {
        this.formData.userAvatar = data.url;
        console.log('设置头像URL:', data.url);

        // 显示压缩信息
        if (data.compressed) {
          console.log('压缩信息:', {
            size: data.size,
            originalSize: data.originalSize,
            compressionRatio: data.compressionRatio
          });
        }
      }
      uni.showToast({
        title: "\u5934\u50CF\u4E0A\u4F20\u6210\u529F".concat(data.compressed ? '(已压缩)' : ''),
        icon: 'success'
      });
    },
    // 头像上传失败
    onAvatarUploadError: function onAvatarUploadError(error) {
      console.error('头像上传失败:', error);
      uni.showToast({
        title: '头像上传失败',
        icon: 'none'
      });
    },
    // 选择图片
    chooseImages: function chooseImages() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var remainCount, res, uploadUtils, result, successCount, failCount;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                remainCount = 5 - _this8.formData.images.length;
                _context6.prev = 1;
                _context6.next = 4;
                return _this8.chooseImageAsync({
                  count: remainCount,
                  sizeType: ['original'],
                  // 选择原图，后续压缩
                  sourceType: ['album', 'camera']
                });
              case 4:
                res = _context6.sent;
                if (!(res.errMsg === 'chooseImage:fail cancel')) {
                  _context6.next = 7;
                  break;
                }
                return _context6.abrupt("return");
              case 7:
                if (!(res.tempFilePaths && res.tempFilePaths.length > 0)) {
                  _context6.next = 24;
                  break;
                }
                // 显示上传进度
                uni.showLoading({
                  title: '处理中...'
                });

                // 使用上传工具批量上传
                _context6.next = 11;
                return Promise.resolve().then(function () {
                  return _interopRequireWildcard(__webpack_require__(/*! @/utils/upload-utils.js */ 258));
                });
              case 11:
                uploadUtils = _context6.sent.default;
                _context6.next = 14;
                return uploadUtils.uploadHonorImages(res.tempFilePaths, {
                  compress: true,
                  onProgress: function onProgress(progress) {
                    if (progress.phase === 'compress') {
                      uni.showLoading({
                        title: "\u538B\u7F29\u4E2D ".concat(progress.progress || 0, "%")
                      });
                    } else if (progress.phase === 'upload') {
                      uni.showLoading({
                        title: "\u4E0A\u4F20\u4E2D ".concat(progress.progress || 0, "%")
                      });
                    }
                  }
                });
              case 14:
                result = _context6.sent;
                uni.hideLoading();
                if (!result.success) {
                  _context6.next = 23;
                  break;
                }
                // 添加成功上传的图片
                result.results.forEach(function (item, index) {
                  if (item.success) {
                    _this8.formData.images.push({
                      id: Date.now() + Math.random() + index,
                      url: item.url,
                      cloudPath: item.cloudPath
                    });
                  }
                });
                successCount = result.totalUploaded;
                failCount = result.totalFailed;
                if (failCount > 0) {
                  uni.showToast({
                    title: "".concat(successCount, "\u5F20\u6210\u529F\uFF0C").concat(failCount, "\u5F20\u5931\u8D25"),
                    icon: 'none'
                  });
                } else {
                  uni.showToast({
                    title: "".concat(successCount, "\u5F20\u56FE\u7247\u4E0A\u4F20\u6210\u529F"),
                    icon: 'success'
                  });
                }
                _context6.next = 24;
                break;
              case 23:
                throw new Error('批量上传失败');
              case 24:
                _context6.next = 31;
                break;
              case 26:
                _context6.prev = 26;
                _context6.t0 = _context6["catch"](1);
                uni.hideLoading();
                console.error('选择图片失败:', _context6.t0);
                uni.showToast({
                  title: _context6.t0.message || '图片上传失败',
                  icon: 'none'
                });
              case 31:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[1, 26]]);
      }))();
    },
    // 异步选择图片
    chooseImageAsync: function chooseImageAsync(options) {
      return new Promise(function (resolve, reject) {
        uni.chooseImage(_objectSpread(_objectSpread({}, options), {}, {
          success: resolve,
          fail: reject
        }));
      });
    },
    // 移除图片
    removeImage: function removeImage(index) {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var image, _yield$uniCloud$callF, result;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _context7.prev = 0;
                image = _this9.formData.images[index];
                if (!(image && image.cloudPath)) {
                  _context7.next = 17;
                  break;
                }
                // 显示删除中提示
                uni.showLoading({
                  title: '删除中...'
                });

                // 调用云函数删除文件
                _context7.next = 6;
                return uniCloud.callFunction({
                  name: 'delete-file',
                  data: {
                    fileList: [image.cloudPath]
                  }
                });
              case 6:
                _yield$uniCloud$callF = _context7.sent;
                result = _yield$uniCloud$callF.result;
                uni.hideLoading();
                if (!(result.code === 0)) {
                  _context7.next = 14;
                  break;
                }
                // 从数组中移除
                _this9.formData.images.splice(index, 1);
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                _context7.next = 15;
                break;
              case 14:
                throw new Error(result.message || '删除失败');
              case 15:
                _context7.next = 18;
                break;
              case 17:
                // 如果没有cloudPath，直接从数组移除
                _this9.formData.images.splice(index, 1);
              case 18:
                _context7.next = 25;
                break;
              case 20:
                _context7.prev = 20;
                _context7.t0 = _context7["catch"](0);
                uni.hideLoading();
                console.error('删除图片失败:', _context7.t0);
                uni.showToast({
                  title: _context7.t0.message || '删除失败',
                  icon: 'none'
                });
              case 25:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[0, 20]]);
      }))();
    },
    // 保存记录
    saveRecord: function saveRecord() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var action, data, res;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                if (_this10.validateForm()) {
                  _context8.next = 2;
                  break;
                }
                return _context8.abrupt("return");
              case 2:
                _this10.saving = true;
                _context8.prev = 3;
                action = _this10.isEditMode ? 'updateHonor' : 'createHonor';
                data = {
                  userName: _this10.formData.userName,
                  department: _this10.formData.department,
                  userAvatar: _this10.formData.userAvatar,
                  honorTypeId: _this10.formData.honorTypeId,
                  batchId: _this10.formData.batchId,
                  reason: _this10.formData.reason,
                  isFeatured: _this10.formData.isFeatured,
                  images: _this10.formData.images
                }; // 编辑模式需要传递记录ID
                if (_this10.isEditMode) {
                  data.honorId = _this10.recordId;
                }
                _context8.next = 9;
                return uniCloud.callFunction({
                  name: 'honor-admin',
                  data: {
                    action: action,
                    data: data
                  }
                });
              case 9:
                res = _context8.sent;
                if (!(res.result.code === 0)) {
                  _context8.next = 15;
                  break;
                }
                uni.showToast({
                  title: _this10.isEditMode ? '表彰记录更新成功' : '表彰记录创建成功',
                  icon: 'success',
                  duration: 2000
                });
                setTimeout(function () {
                  uni.navigateBack();
                }, 2000);
                _context8.next = 16;
                break;
              case 15:
                throw new Error(res.result.message || (_this10.isEditMode ? '更新失败' : '创建失败'));
              case 16:
                _context8.next = 21;
                break;
              case 18:
                _context8.prev = 18;
                _context8.t0 = _context8["catch"](3);
                uni.showToast({
                  title: _context8.t0.message || '保存失败',
                  icon: 'none'
                });
              case 21:
                _context8.prev = 21;
                _this10.saving = false;
                return _context8.finish(21);
              case 24:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[3, 18, 21, 24]]);
      }))();
    },
    // 表单验证
    validateForm: function validateForm() {
      if (!this.formData.userName.trim()) {
        uni.showToast({
          title: '请输入被表彰人姓名',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.honorTypeId) {
        uni.showToast({
          title: '请选择荣誉类型',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.batchId) {
        uni.showToast({
          title: '请选择表彰批次',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.reason.trim()) {
        uni.showToast({
          title: '请填写表彰原因',
          icon: 'none'
        });
        return false;
      }
      return true;
    },
    // 返回
    goBack: function goBack() {
      if (this.hasChanges()) {
        uni.showModal({
          title: '确认离开',
          content: '您有未保存的内容，确定要离开吗？',
          success: function success(res) {
            if (res.confirm) {
              uni.navigateBack();
            }
          }
        });
      } else {
        uni.navigateBack();
      }
    },
    // 检查是否有更改
    hasChanges: function hasChanges() {
      return this.formData.userName.trim() || this.formData.reason.trim() || this.formData.honorTypeId || this.formData.batchId || this.formData.images.length > 0;
    },
    // ===== 选择器相关方法 =====
    // 部门选择器
    openDepartmentSelector: function openDepartmentSelector() {
      this.showDepartmentSelector = true;
    },
    closeDepartmentSelector: function closeDepartmentSelector() {
      this.showDepartmentSelector = false;
      this.newDepartmentName = ''; // 清空输入框
    },
    selectDepartment: function selectDepartment(dept) {
      this.formData.department = dept.value;
      this.closeDepartmentSelector();
    },
    // 添加新部门
    addNewDepartment: function addNewDepartment() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var departmentName, exists;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                departmentName = _this11.newDepartmentName.trim();
                if (departmentName) {
                  _context9.next = 4;
                  break;
                }
                uni.showToast({
                  title: '请输入部门名称',
                  icon: 'none'
                });
                return _context9.abrupt("return");
              case 4:
                // 检查是否已存在
                exists = _this11.departmentOptions.some(function (dept) {
                  return dept.value === departmentName;
                });
                if (!exists) {
                  _context9.next = 8;
                  break;
                }
                uni.showToast({
                  title: '部门已存在',
                  icon: 'none'
                });
                return _context9.abrupt("return");
              case 8:
                // 直接在本地添加，不调用云函数（因为是临时数据）
                _this11.departmentOptions.push({
                  value: departmentName,
                  text: departmentName
                });

                // 自动选择新添加的部门
                _this11.formData.department = departmentName;
                _this11.newDepartmentName = '';
                _this11.closeDepartmentSelector();
                uni.showToast({
                  title: '部门已添加（临时）',
                  icon: 'success',
                  duration: 2000
                });
              case 13:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9);
      }))();
    },
    // 删除部门
    deleteDepartment: function deleteDepartment(dept) {
      var _this12 = this;
      if (this.departmentOptions.length <= 1) {
        uni.showToast({
          title: '至少需要保留一个部门',
          icon: 'none'
        });
        return;
      }
      uni.showModal({
        title: '确认删除',
        content: "\u786E\u5B9A\u8981\u5220\u9664\"".concat(dept.text, "\"\u90E8\u95E8\u5417\uFF1F"),
        success: function () {
          var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10(res) {
            var result, index;
            return _regenerator.default.wrap(function _callee10$(_context10) {
              while (1) {
                switch (_context10.prev = _context10.next) {
                  case 0:
                    if (!res.confirm) {
                      _context10.next = 17;
                      break;
                    }
                    _context10.prev = 1;
                    _context10.next = 4;
                    return uniCloud.callFunction({
                      name: 'honor-admin',
                      data: {
                        action: 'checkDepartmentUsage',
                        data: {
                          name: dept.value
                        }
                      }
                    });
                  case 4:
                    result = _context10.sent;
                    if (!(result.result.code !== 0)) {
                      _context10.next = 8;
                      break;
                    }
                    uni.showToast({
                      title: result.result.message || '该部门正在使用中，无法删除',
                      icon: 'none',
                      duration: 3000
                    });
                    return _context10.abrupt("return");
                  case 8:
                    _context10.next = 13;
                    break;
                  case 10:
                    _context10.prev = 10;
                    _context10.t0 = _context10["catch"](1);
                    // 如果检查失败，仍然允许删除（因为可能是新添加的临时部门）
                    console.log('检查部门使用情况失败，继续删除:', _context10.t0);
                  case 13:
                    // 执行本地删除
                    index = _this12.departmentOptions.findIndex(function (d) {
                      return d.value === dept.value;
                    });
                    if (index > -1) {
                      _this12.departmentOptions.splice(index, 1);
                    }

                    // 如果当前选择的是被删除的部门，清空选择
                    if (_this12.formData.department === dept.value) {
                      _this12.formData.department = '';
                    }
                    uni.showToast({
                      title: '部门已移除',
                      icon: 'success'
                    });
                  case 17:
                  case "end":
                    return _context10.stop();
                }
              }
            }, _callee10, null, [[1, 10]]);
          }));
          function success(_x) {
            return _success.apply(this, arguments);
          }
          return success;
        }()
      });
    },
    // 荣誉类型选择器
    openHonorTypeSelector: function openHonorTypeSelector() {
      this.showHonorTypeSelector = true;
    },
    closeHonorTypeSelector: function closeHonorTypeSelector() {
      this.showHonorTypeSelector = false;
    },
    selectHonorType: function selectHonorType(type) {
      this.formData.honorTypeId = type.value;
      this.selectedHonorType = type.data;
      this.closeHonorTypeSelector();
    },
    // 批次选择器
    openBatchSelector: function openBatchSelector() {
      this.showBatchSelector = true;
    },
    closeBatchSelector: function closeBatchSelector() {
      this.showBatchSelector = false;
      this.batchSearchKeyword = '';
    },
    // 批次搜索
    onBatchSearch: function onBatchSearch() {
      // 重置历史批次显示数量
      this.showOlderBatchCount = 10;
    },
    // 选择批次
    selectBatch: function selectBatch(batch) {
      this.formData.batchId = batch.value;
      this.selectedBatch = batch.data;
      this.closeBatchSelector();
    },
    // 显示更多历史批次
    showMoreOlderBatches: function showMoreOlderBatches() {
      this.showOlderBatchCount += 10;
    },
    // 格式化批次日期
    formatBatchDate: function formatBatchDate(dateStr) {
      if (!dateStr) return '';
      var date = new Date(dateStr);
      var month = date.getMonth() + 1;
      var day = date.getDate();
      return "".concat(month, "\u6708").concat(day, "\u65E5");
    },
    // 获取批次类型文本
    getBatchTypeText: function getBatchTypeText(type) {
      var typeMap = {
        'weekly': '周表彰',
        'monthly': '月表彰',
        'quarterly': '季度表彰',
        'yearly': '年度表彰',
        'special': '特别表彰'
      };
      return typeMap[type] || '其他';
    },
    // 快速创建批次
    quickCreateBatch: function quickCreateBatch() {
      this.closeBatchSelector();
      uni.navigateTo({
        url: '/pages/honor_pkg/admin/batch-manager?quick=true'
      });
    },
    // 预览图片
    previewImage: function previewImage(index) {
      if (this.formData.images.length > 0) {
        uni.previewImage({
          urls: this.formData.images.map(function (img) {
            return img.url;
          }),
          current: this.formData.images[index].url,
          indicator: 'number'
        });
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"]))

/***/ }),

/***/ 260:
/*!**************************************************************************************************************!*\
  !*** D:/Xwzc/pages/honor_pkg/admin/add-record.vue?vue&type=style&index=0&id=0c706de4&lang=scss&scoped=true& ***!
  \**************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_style_index_0_id_0c706de4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add-record.vue?vue&type=style&index=0&id=0c706de4&lang=scss&scoped=true& */ 261);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_style_index_0_id_0c706de4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_style_index_0_id_0c706de4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_style_index_0_id_0c706de4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_style_index_0_id_0c706de4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_record_vue_vue_type_style_index_0_id_0c706de4_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 261:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/honor_pkg/admin/add-record.vue?vue&type=style&index=0&id=0c706de4&lang=scss&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[252,"common/runtime","common/vendor","pages/honor_pkg/common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/honor_pkg/admin/add-record.js.map
{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/ucenter_pkg/responsible-tasks.vue?b18b", "webpack:///D:/Xwzc/pages/ucenter_pkg/responsible-tasks.vue?a147", "webpack:///D:/Xwzc/pages/ucenter_pkg/responsible-tasks.vue?6881", "webpack:///D:/Xwzc/pages/ucenter_pkg/responsible-tasks.vue?b68f", "uni-app:///pages/ucenter_pkg/responsible-tasks.vue", "webpack:///D:/Xwzc/pages/ucenter_pkg/responsible-tasks.vue?9878", "webpack:///D:/Xwzc/pages/ucenter_pkg/responsible-tasks.vue?e68f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLoading", "hasInitialized", "taskList", "taskStats", "assigned", "completed", "confirmed", "statusOptions", "currentStatusIndex", "currentUserId", "lastRefreshTime", "refreshInterval", "lastSyncTime", "crossDeviceTimer", "isPageVisible", "lastTaskCount", "lastStatsSnapshot", "created", "uni", "console", "<PERSON><PERSON><PERSON><PERSON>", "onLoad", "onShow", "onHide", "onPullDownRefresh", "methods", "clearCrossDeviceCheck", "clearInterval", "silentRefreshTasks", "statusMap", "status", "uniCloud", "name", "action", "res", "newTaskList", "newStats", "has<PERSON><PERSON><PERSON>", "checkTaskChanges", "newTask", "handleVisibilityChange", "handleTaskAssigned", "setTimeout", "handleTaskCompleted", "handleFeedbackUpdated", "getCurrentUser", "shouldRefreshOnShow", "showLoginPrompt", "title", "content", "confirmText", "cancelText", "confirmColor", "success", "url", "loadTasks", "icon", "duration", "switchToStatus", "onStatusChange", "getStatusText", "getStatusIcon", "getTimingClass", "getTimingIcon", "getEmptyIcon", "getEmptyColor", "getEmptyTitle", "getEmptyDescription", "getEmptyActionText", "handleEmptyAction", "showCancel", "formatTime", "viewTaskDetail", "fail", "completeTask", "shouldRefreshOnCrossDeviceUpdate"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACqC;;;AAGrG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,4WAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAA2mB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgK/nB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IAAA;IACA;IACAC;MACA;QACA;QACA;QACA;UACAC;UACA;UACA;QACA;MACA;IACA;;IAEA;IACAD;IACAA;IACAA;;IAEA;IACA;EACA;EAEAE;IACA;IACA;;IAEA;IACAF;IACAA;IACAA;IACAA;;IAEA;EACA;EAEAG;IACA;IACA;EACA;EAEAC;IAAA;IACA;;IAEA;IACAJ;IACAA;MACA;QACA;QACA;QACA;UACAC;UACA;UACA;QACA;MACA;IACA;;IAEA;IACA;MACA;IACA;;IAEA;EACA;EAEAI;IACA;IACA;IACA;EACA;EAEAC;IACA;MACAN;IACA;EACA;EAEAO;IACA;IACAC;MACA;QACAC;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAKAC;kBACA;kBAAA;kBACA;kBAAA;kBACA;kBAAA;kBACA;gBACA;gBAEAC;gBAAA;gBAAA,OAEAC;kBACAC;kBACAjC;oBACAkC;oBACAH;kBACA;gBACA;cAAA;gBANAI;gBAQA;kBACAC;kBACAC,sDAEA;kBACAC;kBAEA;oBACA;oBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAmB;MACA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QAEA,gBACAC,qDACAA,6EACAA;UACA;QACA;MACA;MAEA;IACA;IAEAC;MACA;IAAA,CACA;IAEA;IACAC;MAAA;MACA;MACAC;QACA;MACA;IACA;IAEAC;MAAA;MACA;MACAD;QACA;MACA;IACA;IAEAE;MAAA;MACA;MACAF;QACA;MACA;IACA;IAEAG;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEAC;MACA7B;QACA8B;QACAC;QACAC;QACAC;QACAC;QACAC;UACA;YACAnC;cACAoC;YACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAGA1B;kBACA;kBAAA;kBACA;kBAAA;kBACA;kBAAA;kBACA;gBACA;gBAEAC;gBAAA;gBAAA,OAEAC;kBACAC;kBACAjC;oBACAkC;oBACAH;kBACA;gBACA;cAAA;gBANAI;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;;gBAEA;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAhB;kBACA8B;kBACAQ;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MAEA;MACA;MACA;MACA;IACA;IAEAC;MACA;MAEA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA,oBACA,4BACA,4BACA,6BACA,yBACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;QACA;QACAnD;UACA8B;UACAC;UACAqB;UACApB;QACA;MACA;QACA;QACA;MACA;IACA;IAEAqB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACAtD;QACAoC;QACAmB;UACAvD;YACA8B;YACAQ;UACA;QACA;MACA;IACA;IAEAkB;MACAxD;QACAoC;QACAmB;UACAvD;YACA8B;YACAQ;UACA;QACA;MACA;IACA;IAEAmB;MACA;MACA;QACAxD;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;QACA;UAAA;QAAA;QACA;UACAA;UACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;MAEAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7nBA;AAAA;AAAA;AAAA;AAAq5B,CAAgB,s5BAAG,EAAC,C;;;;;;;;;;;ACAz6B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ucenter_pkg/responsible-tasks.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/ucenter_pkg/responsible-tasks.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./responsible-tasks.vue?vue&type=template&id=01bd25d6&scoped=true&\"\nvar renderjs\nimport script from \"./responsible-tasks.vue?vue&type=script&lang=js&\"\nexport * from \"./responsible-tasks.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsible-tasks.vue?vue&type=style&index=0&id=01bd25d6&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"01bd25d6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ucenter_pkg/responsible-tasks.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./responsible-tasks.vue?vue&type=template&id=01bd25d6&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniSegmentedControl: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-segmented-control/components/uni-segmented-control/uni-segmented-control\" */ \"@/uni_modules/uni-segmented-control/components/uni-segmented-control/uni-segmented-control.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.taskList.length\n  var g1 = _vm.taskList.length\n  var l0 =\n    g1 > 0\n      ? _vm.__map(_vm.taskList, function (task, index) {\n          var $orig = _vm.__get_orig(task)\n          var m0 = _vm.getStatusIcon(task.workflowStatus)\n          var m1 = _vm.getStatusText(task.workflowStatus)\n          var m2 = _vm.formatTime(task.assignedTime)\n          var m3 = task.completedByResponsibleTime\n            ? _vm.formatTime(task.completedByResponsibleTime)\n            : null\n          var m4 =\n            (task.timing ||\n              task.workflowStatus === \"assigned_to_responsible\") &&\n            task.timing\n              ? _vm.getTimingIcon(task.timing)\n              : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n          }\n        })\n      : null\n  var m5 =\n    !(g1 > 0) && !_vm.isLoading && _vm.hasInitialized\n      ? _vm.getEmptyIcon()\n      : null\n  var m6 =\n    !(g1 > 0) && !_vm.isLoading && _vm.hasInitialized\n      ? _vm.getEmptyColor()\n      : null\n  var m7 =\n    !(g1 > 0) && !_vm.isLoading && _vm.hasInitialized\n      ? _vm.getEmptyTitle()\n      : null\n  var m8 =\n    !(g1 > 0) && !_vm.isLoading && _vm.hasInitialized\n      ? _vm.getEmptyDescription()\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./responsible-tasks.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./responsible-tasks.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 统计卡片 -->\n    <view class=\"stats-card\">\n      <view class=\"stats-title\">\n        <text>任务概览</text>\n      </view>\n      <view class=\"stats-grid\">\n        <view class=\"stat-item pending\" @click=\"switchToStatus(0)\">\n          <view class=\"stat-icon\">\n            <uni-icons type=\"loop\" size=\"18\" color=\"#ffffff\"></uni-icons>\n          </view>\n          <view class=\"stat-content\">\n            <text class=\"stat-number\">{{taskStats.assigned}}</text>\n            <text class=\"stat-label\">待完成</text>\n          </view>\n        </view>\n        <view class=\"stat-item completed\" @click=\"switchToStatus(1)\">\n          <view class=\"stat-icon\">\n            <uni-icons type=\"auth\" size=\"18\" color=\"#ffffff\"></uni-icons>\n          </view>\n          <view class=\"stat-content\">\n            <text class=\"stat-number\">{{taskStats.completed}}</text>\n            <text class=\"stat-label\">待确认</text>\n          </view>\n        </view>\n        <view class=\"stat-item confirmed\" @click=\"switchToStatus(2)\">\n          <view class=\"stat-icon\">\n            <uni-icons type=\"medal\" size=\"18\" color=\"#ffffff\"></uni-icons>\n          </view>\n          <view class=\"stat-content\">\n            <text class=\"stat-number\">{{taskStats.confirmed}}</text>\n            <text class=\"stat-label\">已确认</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 筛选器 -->\n    <view class=\"filter-card\">\n      <view class=\"filter-header\">\n        <view class=\"filter-title\">\n          <uni-icons type=\"funnel\" size=\"16\" color=\"#007aff\"></uni-icons>\n          <text>筛选条件</text>\n        </view>\n        <text class=\"filter-count\">共{{taskList.length}}项</text>\n      </view>\n      <view class=\"filter-segment\">\n        <uni-segmented-control \n          :values=\"statusOptions\" \n          :current=\"currentStatusIndex\"\n          @clickItem=\"onStatusChange\"\n          style-type=\"button\"\n          active-color=\"#007aff\">\n        </uni-segmented-control>\n      </view>\n    </view>\n\n    <!-- 任务列表 -->\n    <view class=\"task-list\" v-if=\"taskList.length > 0\">\n      <view \n        v-for=\"(task, index) in taskList\" \n        :key=\"task._id\" \n        class=\"task-card\"\n        @click=\"viewTaskDetail(task)\">\n        \n        <!-- 任务头部 -->\n        <view class=\"task-header\">\n          <view class=\"task-left\">\n            <view class=\"task-avatar\" :class=\"'status-' + (task.workflowStatus === 'assigned_to_responsible' ? 'pending' : task.workflowStatus === 'completed_by_responsible' ? 'pending-confirm' : task.workflowStatus === 'final_completed' ? 'confirmed' : 'default')\">\n              <uni-icons :type=\"getStatusIcon(task.workflowStatus)\" size=\"16\" color=\"#ffffff\"></uni-icons>\n            </view>\n            <view class=\"task-main\">\n              <view class=\"task-title\">{{task.name}}</view>\n              <view class=\"task-project\">{{task.project}}</view>\n            </view>\n          </view>\n          <view class=\"task-status-badge\" :class=\"'status-' + (task.workflowStatus === 'assigned_to_responsible' ? 'pending' : task.workflowStatus === 'completed_by_responsible' ? 'pending-confirm' : task.workflowStatus === 'final_completed' ? 'confirmed' : 'default')\">\n            {{getStatusText(task.workflowStatus)}}\n          </view>\n        </view>\n\n        <!-- 任务内容 -->\n        <view class=\"task-body\">\n          <view class=\"task-description\">\n            <view class=\"desc-row\">\n              <uni-icons type=\"compose\" size=\"12\" color=\"#666666\"></uni-icons>\n              <text class=\"desc-content\">{{task.description}}</text>\n            </view>\n\n          </view>\n          \n          <!-- 时间信息 -->\n          <view class=\"task-times\">\n            <view class=\"time-item\">\n              <uni-icons type=\"calendar\" size=\"12\" color=\"#999999\"></uni-icons>\n              <text class=\"time-text\">指派：{{formatTime(task.assignedTime)}}</text>\n            </view>\n            <view class=\"time-item\" v-if=\"task.completedByResponsibleTime\">\n              <uni-icons type=\"checkmarkempty\" size=\"12\" color=\"#4caf50\"></uni-icons>\n              <text class=\"time-text\">完成：{{formatTime(task.completedByResponsibleTime)}}</text>\n            </view>\n            <!-- 内联显示厂长指派说明 -->\n            <view class=\"time-item\" v-if=\"task.assignReason && task.assignReason !== '无'\">\n              <uni-icons type=\"chat\" size=\"12\"></uni-icons>\n              <text class=\"time-text\">厂长说明：<text class=\"assign-reason-inline\">{{task.assignReason}}</text></text>\n            </view>\n            <!-- 退回理由 -->\n            <view class=\"time-item\" v-if=\"task.rejectReason\">\n              <uni-icons type=\"loop\" size=\"12\" color=\"#999999\"></uni-icons>\n              <text class=\"time-text\">退回原因：<text class=\"reject-reason-text\">{{task.rejectReason}}</text></text>\n            </view>\n          </view>\n\n          <!-- 时效提醒和完成按钮 -->\n          <view class=\"task-timing\" v-if=\"task.timing || task.workflowStatus === 'assigned_to_responsible'\">\n            <view class=\"timing-content\" v-if=\"task.timing\">\n              <view class=\"timing-badge\" :class=\"'timing-' + (task.timing && task.timing.isOverdue ? 'overdue' : task.timing && task.timing.urgency === 'urgent' ? 'urgent' : task.timing && task.timing.urgency === 'warning' ? 'warning' : 'normal')\">\n                <uni-icons :type=\"getTimingIcon(task.timing)\" size=\"12\" color=\"#ffffff\"></uni-icons>\n                <text>{{task.timing.description}}</text>\n              </view>\n              <text v-if=\"task.timing.isOverdue\" class=\"overdue-text\">\n                <uni-icons type=\"help\" size=\"12\" color=\"#f44336\"></uni-icons>\n                已超期\n              </text>\n            </view>\n            \n            <!-- 完成任务按钮 -->\n            <button v-if=\"task.workflowStatus === 'assigned_to_responsible'\" \n                    class=\"complete-task-btn\" \n                    @click.stop=\"completeTask(task)\">\n              <uni-icons type=\"checkmarkempty\" size=\"12\" color=\"#ffffff\"></uni-icons>\n              <text>完成任务</text>\n            </button>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 优化的空状态 -->\n    <view v-else-if=\"!isLoading && hasInitialized\" class=\"empty-state\">\n      <view class=\"empty-content\">\n        <view class=\"empty-icon\">\n          <uni-icons :type=\"getEmptyIcon()\" size=\"48\" :color=\"getEmptyColor()\"></uni-icons>\n        </view>\n        <text class=\"empty-title\">{{getEmptyTitle()}}</text>\n        <text class=\"empty-desc\">{{getEmptyDescription()}}</text>\n      </view>\n    </view>\n\n    <!-- 加载状态 -->\n    <view v-if=\"isLoading\" class=\"loading-state\">\n      <view class=\"loading-content\">\n        <uni-load-more status=\"loading\" text=\"加载任务中...\"></uni-load-more>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      isLoading: false,\n      hasInitialized: false,\n      taskList: [],\n      taskStats: {\n        assigned: 0,\n        completed: 0,\n        confirmed: 0\n      },\n      statusOptions: ['待完成', '待确认', '已确认', '全部'],\n      currentStatusIndex: 0,\n      currentUserId: null,\n      lastRefreshTime: 0,\n      refreshInterval: 30000, // 30秒内不重复刷新\n      \n      // 跨设备同步相关\n      lastSyncTime: 0,\n      crossDeviceTimer: null,\n      isPageVisible: true,\n      lastTaskCount: 0,\n      lastStatsSnapshot: null\n    };\n  },\n  \n  created() {\n    // 监听角标管理器的跨设备更新事件\n    uni.$on('cross-device-update-detected', (data) => {\n      if (data.silent && this.currentUserId) {\n        // 智能判断是否需要刷新\n        const shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);\n        if (shouldRefresh) {\n          console.log('负责人任务页面收到跨设备更新通知，静默刷新数据');\n          // 静默刷新数据，不显示提示\n          this.silentRefreshTasks();\n        }\n      }\n    });\n    \n    // 监听任务指派事件\n    uni.$on('task-assigned', this.handleTaskAssigned);\n    uni.$on('task-completed', this.handleTaskCompleted);\n    uni.$on('feedback-updated', this.handleFeedbackUpdated);\n    \n    // 监听页面可见性变化\n    // 统一由角标管理器处理跨设备更新\n  },\n  \n  beforeDestroy() {\n    // 清除定时器\n    this.clearCrossDeviceCheck();\n    \n    // 移除事件监听\n    uni.$off('cross-device-update-detected');\n    uni.$off('task-assigned', this.handleTaskAssigned);\n    uni.$off('task-completed', this.handleTaskCompleted);\n    uni.$off('feedback-updated', this.handleFeedbackUpdated);\n    \n    // 统一由角标管理器处理跨设备更新\n  },\n  \n  onLoad() {\n    this.getCurrentUser();\n    this.loadTasks();\n  },\n  \n  onShow() {\n    this.isPageVisible = true;\n    \n    // 确保事件监听器正确绑定（修复从其他页面返回时监听器丢失的问题）\n    uni.$off('cross-device-update-detected');\n    uni.$on('cross-device-update-detected', (data) => {\n      if (data.silent && this.currentUserId) {\n        // 智能判断是否需要刷新\n        const shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);\n        if (shouldRefresh) {\n          console.log('负责人任务页面收到跨设备更新通知，静默刷新数据');\n          // 静默刷新数据，不显示提示\n          this.silentRefreshTasks();\n        }\n      }\n    });\n    \n    // 页面显示时刷新数据 - 仅在特定条件下刷新\n    if (this.hasInitialized && this.shouldRefreshOnShow()) {\n      this.loadTasks();\n    }\n    \n    // 不再启动独立的跨设备检查，统一由角标管理器处理\n  },\n  \n  onHide() {\n    this.isPageVisible = false;\n    // 清除定时器\n    this.clearCrossDeviceCheck();\n  },\n  \n  onPullDownRefresh() {\n    this.loadTasks().finally(() => {\n      uni.stopPullDownRefresh();\n    });\n  },\n  \n  methods: {\n    // 清除跨设备检查定时器（保留方法以保持兼容性）\n    clearCrossDeviceCheck() {\n      if (this.crossDeviceTimer) {\n        clearInterval(this.crossDeviceTimer);\n        this.crossDeviceTimer = null;\n      }\n    },\n    \n    async silentRefreshTasks() {\n      if (!this.currentUserId) {\n        return;\n      }\n      \n      try {\n        const statusMap = {\n          0: 'assigned_to_responsible',     // 待完成\n          1: 'completed_by_responsible',    // 待确认\n          2: 'final_completed',             // 已确认\n          3: 'all'                          // 全部\n        };\n        \n        const status = statusMap[this.currentStatusIndex];\n        \n        const res = await uniCloud.callFunction({\n          name: 'feedback-list',\n          data: {\n            action: 'getMyTasks',\n            status\n          }\n        });\n        \n        if (res.result && res.result.code === 0) {\n          const newTaskList = res.result.data.list || [];\n          const newStats = res.result.data.stats || this.taskStats;\n          \n          // 检查是否有实质性变化\n          const hasChanges = this.checkTaskChanges(newTaskList, newStats);\n          \n          if (hasChanges) {\n            this.taskList = newTaskList;\n            this.taskStats = newStats;\n            this.lastTaskCount = newTaskList.length;\n            this.lastStatsSnapshot = JSON.stringify(newStats);\n          }\n        }\n      } catch (error) {\n        console.log('静默刷新失败:', error);\n      }\n    },\n    \n    checkTaskChanges(newTaskList, newStats) {\n      // 检查任务数量变化\n      if (newTaskList.length !== this.lastTaskCount) {\n        return true;\n      }\n      \n      // 检查统计数据变化\n      const newStatsSnapshot = JSON.stringify(newStats);\n      if (newStatsSnapshot !== this.lastStatsSnapshot) {\n        return true;\n      }\n      \n      // 检查任务状态变化\n      for (let i = 0; i < newTaskList.length; i++) {\n        const newTask = newTaskList[i];\n        const oldTask = this.taskList[i];\n        \n        if (!oldTask || \n            newTask.workflowStatus !== oldTask.workflowStatus ||\n            newTask.completedByResponsibleTime !== oldTask.completedByResponsibleTime ||\n            newTask.rejectReason !== oldTask.rejectReason) {\n          return true;\n        }\n      }\n      \n      return false;\n    },\n    \n    handleVisibilityChange() {\n      // 页面可见性变化时不再启动独立检查，统一由角标管理器处理\n    },\n    \n    // 事件处理方法\n    handleTaskAssigned() {\n      // 任务被指派时刷新\n      setTimeout(() => {\n        this.silentRefreshTasks();\n      }, 1000);\n    },\n    \n    handleTaskCompleted() {\n      // 任务完成时刷新\n      setTimeout(() => {\n        this.silentRefreshTasks();\n      }, 1000);\n    },\n    \n    handleFeedbackUpdated() {\n      // 反馈更新时刷新\n      setTimeout(() => {\n        this.silentRefreshTasks();\n      }, 1500);\n    },\n    \n    getCurrentUser() {\n      try {\n        const userInfo = uniCloud.getCurrentUserInfo();\n        this.currentUserId = userInfo.uid;\n      } catch (error) {\n        this.showLoginPrompt();\n      }\n    },\n    \n    shouldRefreshOnShow() {\n      const now = Date.now();\n      const timeSinceLastRefresh = now - this.lastRefreshTime;\n      \n      // 如果距离上次刷新时间超过设定间隔，则允许刷新\n      if (timeSinceLastRefresh > this.refreshInterval) {\n        return true;\n      }\n      \n      // 在所有环境下，允许刷新\n      return true;\n    },\n    \n    showLoginPrompt() {\n      uni.showModal({\n        title: '登录提示',\n        content: '请先登录后查看任务',\n        confirmText: '去登录',\n        cancelText: '稍后再说',\n        confirmColor: '#007aff',\n        success: (res) => {\n          if (res.confirm) {\n            uni.navigateTo({\n              url: '/uni_modules/uni-id-pages/pages/login/login-withpwd'\n            });\n          }\n        }\n      });\n    },\n    \n    async loadTasks() {\n      if (!this.currentUserId) {\n        return;\n      }\n      \n      this.isLoading = true;\n      \n      try {\n        const statusMap = {\n          0: 'assigned_to_responsible',     // 待完成\n          1: 'completed_by_responsible',    // 待确认\n          2: 'final_completed',             // 已确认\n          3: 'all'                          // 全部\n        };\n        \n        const status = statusMap[this.currentStatusIndex];\n        \n        const res = await uniCloud.callFunction({\n          name: 'feedback-list',\n          data: {\n            action: 'getMyTasks',\n            status\n          }\n        });\n        \n        if (res.result && res.result.code === 0) {\n          this.taskList = res.result.data.list || [];\n          this.taskStats = res.result.data.stats || this.taskStats;\n          \n          // 初始化跨设备同步相关数据\n          this.lastTaskCount = this.taskList.length;\n          this.lastStatsSnapshot = JSON.stringify(this.taskStats);\n          this.lastSyncTime = Date.now();\n        } else {\n          throw new Error(res.result?.message || '获取任务列表失败');\n        }\n      } catch (error) {\n        uni.showToast({\n          title: error.message || '加载失败，请重试',\n          icon: 'none',\n          duration: 2000\n        });\n      } finally {\n        this.isLoading = false;\n        this.hasInitialized = true;\n        this.lastRefreshTime = Date.now();\n      }\n    },\n    \n    switchToStatus(statusIndex) {\n      if (this.currentStatusIndex !== statusIndex) {\n        this.currentStatusIndex = statusIndex;\n        this.loadTasks();\n      }\n    },\n    \n    onStatusChange(e) {\n      this.currentStatusIndex = e.currentIndex;\n      this.loadTasks();\n    },    \n    \n    getStatusText(status) {\n      const textMap = {\n        'assigned_to_responsible': '待完成',\n        'completed_by_responsible': '待确认',\n        'final_completed': '已确认'\n      };\n      return textMap[status] || status;\n    },\n    \n    getStatusIcon(status) {\n      const iconMap = {\n        'assigned_to_responsible': 'loop',\n        'completed_by_responsible': 'auth',\n        'final_completed': 'medal'\n      };\n      return iconMap[status] || 'help';\n    },\n    \n    getTimingClass(timing) {\n      if (!timing) return 'timing-normal';\n      \n      if (timing.isOverdue) return 'timing-overdue';\n      if (timing.urgency === 'urgent') return 'timing-urgent';\n      if (timing.urgency === 'warning') return 'timing-warning';\n      return 'timing-normal';\n    },\n    \n    getTimingIcon(timing) {\n      if (!timing) return 'loop';\n      \n      if (timing.isOverdue) return 'closeempty';\n      if (timing.urgency === 'urgent') return 'fire';\n      if (timing.urgency === 'warning') return 'help';\n      return 'loop';\n    },\n    \n    getEmptyIcon() {\n      const icons = ['list', 'auth', 'medal', 'search'];\n      return icons[this.currentStatusIndex] || 'list';\n    },\n    \n    getEmptyColor() {\n      const colors = ['#ff9800', '#ff9800', '#2196f3', '#666'];\n      return colors[this.currentStatusIndex] || '#666';\n    },\n    \n    getEmptyTitle() {\n      const titles = ['暂无待完成任务', '暂无待确认任务', '暂无已确认任务', '暂无任务'];\n      return titles[this.currentStatusIndex] || '暂无任务';\n    },\n    \n    getEmptyDescription() {\n      const descriptions = [\n        '当前没有需要您处理的任务\\n请耐心等待新任务分配',\n        '暂无等待厂长确认的任务\\n完成任务后会出现在这里',\n        '暂无最终确认的任务\\n厂长确认后的任务会出现在这里',\n        '系统中暂无任务记录\\n请联系管理员或稍后再试'\n      ];\n      return descriptions[this.currentStatusIndex] || '暂无数据';\n    },\n    \n    getEmptyActionText() {\n      return this.currentStatusIndex === 3 ? '联系管理员' : '刷新一下';\n    },\n    \n    handleEmptyAction() {\n      if (this.currentStatusIndex === 3) {\n        // 联系管理员\n        uni.showModal({\n          title: '联系管理员',\n          content: '如有问题请联系系统管理员或稍后重试',\n          showCancel: false,\n          confirmText: '我知道了'\n        });\n      } else {\n        // 刷新数据\n        this.loadTasks();\n      }\n    },\n    \n    formatTime(timestamp) {\n      if (!timestamp) return '-';\n      const date = new Date(timestamp);\n      const year = date.getFullYear();\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\n      const day = date.getDate().toString().padStart(2, '0');\n      const hour = date.getHours().toString().padStart(2, '0');\n      const minute = date.getMinutes().toString().padStart(2, '0');\n      const second = date.getSeconds().toString().padStart(2, '0');\n      return `${year}/${month}/${day} ${hour}:${minute}:${second}`;\n    },\n    \n    viewTaskDetail(task) {\n      uni.navigateTo({\n        url: `/pages/feedback_pkg/examine?id=${task._id}&readonly=true`,\n        fail: (error) => {\n          uni.showToast({\n            title: '页面跳转失败',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    \n    completeTask(task) {\n      uni.navigateTo({\n        url: `/pages/ucenter_pkg/complete-task?id=${task._id}`,\n        fail: (error) => {\n          uni.showToast({\n            title: '页面跳转失败',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    \n    shouldRefreshOnCrossDeviceUpdate(data) {\n      // 如果页面不可见，不需要刷新\n      if (!this.isPageVisible) {\n        console.log('负责人任务页面不可见，跳过跨设备更新');\n        return false;\n      }\n      \n      // 如果是删除操作，立即刷新\n      if (data.updateTypes && data.updateTypes.includes('feedback_deleted')) {\n        console.log('负责人任务页面检测到删除操作，需要立即刷新');\n        return true;\n      }\n      \n      // 如果距离上次刷新时间太短（小于3秒），避免频繁刷新\n      const timeSinceLastRefresh = Date.now() - (this.lastRefreshTime || 0);\n      if (timeSinceLastRefresh < 3000) {\n        console.log('负责人任务页面距离上次刷新时间太短，跳过跨设备更新');\n        return false;\n      }\n      \n      // 如果更新类型包含任务相关的操作，需要刷新\n      if (data.updateTypes) {\n        const relevantTypes = ['workflow_status_changed', 'feedback_submitted', 'feedback_deleted'];\n        const hasRelevantUpdate = data.updateTypes.some(type => relevantTypes.includes(type));\n        if (hasRelevantUpdate) {\n          console.log('负责人任务页面检测到相关更新类型，需要刷新:', data.updateTypes);\n          return true;\n        }\n      }\n      \n      // 如果有反馈ID信息，检查是否包含当前用户可能关注的任务\n      if (data.feedbackIds && data.feedbackIds.length > 0) {\n        console.log('负责人任务页面检测到反馈更新，需要刷新:', data.feedbackIds);\n        return true;\n      }\n      \n      // 如果有更新记录，采用保守策略：刷新\n      if (data.updateCount > 0) {\n        console.log('负责人任务页面检测到更新记录，采用保守策略刷新:', data.updateCount);\n        return true;\n      }\n      \n      // 如果没有明确的更新信息，采用保守策略：刷新\n      if (!data.updateTypes || data.updateTypes.length === 0) {\n        console.log('负责人任务页面没有明确的更新类型信息，采用保守策略刷新');\n        return true;\n      }\n      \n      console.log('负责人任务页面跨设备更新判断：不需要刷新');\n      return false;\n    }\n  }\n};\n</script>\n\n<style scoped>\npage {\n  background-color: #f8f9fa;\n}\n\n.container {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);\n}\n\n/* 自定义导航栏 */\n.custom-nav {\n  position: sticky;\n  top: 0;\n  z-index: 999;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  padding: 0 24rpx;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.nav-left, .nav-right {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.nav-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n/* 统计卡片 */\n.stats-card {\n  margin: 24rpx;\n  background: #fff;\n  border-radius: 20rpx;\n  padding: 32rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.08);\n}\n\n.stats-title {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 24rpx;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 24rpx;\n}\n\n.stat-item {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n  padding: 24rpx 20rpx;\n  border-radius: 16rpx;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.stat-item.pending {\n  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);\n}\n\n.stat-item.completed {\n  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);\n}\n\n.stat-item.confirmed {\n  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);\n}\n\n.stat-item:active {\n  transform: scale(0.98);\n}\n\n.stat-icon {\n  width: 52rpx;\n  height: 52rpx;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.stat-item.pending .stat-icon {\n  background: #ff9800;\n}\n\n.stat-item.completed .stat-icon {\n  background: #4caf50;\n}\n\n.stat-item.confirmed .stat-icon {\n  background: #2196f3;\n}\n\n.stat-content {\n  display: flex;\n  flex-direction: column;\n  gap: 4rpx;\n}\n\n.stat-number {\n  font-size: 32rpx;\n  font-weight: 700;\n  color: #333;\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 22rpx;\n  color: #666;\n  font-weight: 500;\n}\n\n/* 筛选卡片 */\n.filter-card {\n  margin: 0 24rpx 24rpx;\n  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);\n  border-radius: 20rpx;\n  padding: 28rpx;\n  box-shadow: 0 6rpx 24rpx rgba(0, 122, 255, 0.08);\n  border: 1rpx solid #e8f2ff;\n  position: relative;\n  overflow: hidden;\n}\n\n.filter-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4rpx;\n  background: linear-gradient(90deg, #007aff 0%, #4fc3f7 100%);\n}\n\n.filter-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.filter-title {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 600;\n}\n\n.filter-count {\n  font-size: 22rpx;\n  color: #007aff;\n  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);\n  padding: 6rpx 12rpx;\n  border-radius: 12rpx;\n  font-weight: 500;\n  border: 1rpx solid #e3f2fd;\n  min-width: 40rpx;\n  text-align: center;\n}\n\n.filter-segment {\n  margin-top: 0;\n}\n\n/* 自定义uni-segmented-control样式 */\n::v-deep .uni-segmented-control {\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  padding: 4rpx;\n  border: 1rpx solid #e8f2ff;\n}\n\n::v-deep .uni-segmented-control__item {\n  border-radius: 8rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n  color: #666;\n  padding: 12rpx 16rpx;\n  transition: all 0.3s ease;\n  border: none;\n  background: transparent;\n}\n\n::v-deep .uni-segmented-control__item--button--active {\n  background: linear-gradient(135deg, #007aff 0%, #4fc3f7 100%);\n  color: #fff;\n  font-weight: 600;\n  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);\n}\n\n::v-deep .uni-segmented-control__item--button {\n  margin: 0 2rpx;\n}\n\n/* 任务列表 */\n.task-list {\n  padding: 0 24rpx;\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n  margin-bottom: 120rpx;\n}\n\n.task-card {\n  position: relative;\n  background: #fff;\n  border-radius: 20rpx;\n  overflow: visible;\n  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);\n  transition: all 0.3s ease;\n}\n\n.task-card:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);\n}\n\n.task-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 24rpx 32rpx 20rpx;\n}\n\n.task-left {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n  flex: 1;\n  min-width: 0;\n}\n\n.task-avatar {\n  width: 56rpx;\n  height: 56rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.task-avatar.status-pending {\n  background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);\n}\n\n.task-avatar.status-pending-confirm {\n  background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);\n}\n\n.task-avatar.status-completed {\n  background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);\n}\n\n.task-avatar.status-confirmed {\n  background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%);\n}\n\n.task-main {\n  flex: 1;\n  min-width: 0;\n}\n\n.task-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 6rpx;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.task-project {\n  font-size: 24rpx;\n  color: #666;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.task-status-badge {\n  padding: 12rpx 20rpx;\n  border-radius: 20rpx;\n  font-size: 22rpx;\n  font-weight: 500;\n  color: #fff;\n  flex-shrink: 0;\n}\n\n.task-status-badge.status-pending {\n  background: #ff9800;\n}\n\n.task-status-badge.status-pending-confirm {\n  background: #ff9800;\n}\n\n.task-status-badge.status-completed {\n  background: #4caf50;\n}\n\n.task-status-badge.status-confirmed {\n  background: #2196f3;\n}\n\n.task-body {\n  padding: 0 32rpx 24rpx;\n}\n\n.task-description {\n  margin-bottom: 20rpx;\n}\n\n.desc-row {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n}\n\n\n.desc-content {\n  font-size: 26rpx;\n  color: #333;\n  line-height: 1.5;\n  word-break: break-word;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  flex: 1;\n}\n\n/* 内联指派说明样式 */\n.assign-reason-inline {\n  color: #1677FF;\n  font-weight: 500;\n}\n\n/* 状态标签样式 */\n.task-tags {\n  display: flex;\n  gap: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.status-tag {\n  display: flex;\n  align-items: center;\n  gap: 4rpx;\n  padding: 4rpx 8rpx;\n  border-radius: 12rpx;\n  font-size: 20rpx;\n  font-weight: 500;\n}\n\n.reject-tag {\n  background: #f39c12;\n  color: #ffffff;\n}\n\n/* 退回理由样式 */\n.reject-reason-text {\n  color: #f39c12;\n  font-weight: 500;\n}\n\n.task-times {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 24rpx;\n  margin-bottom: 16rpx;\n}\n\n.time-item {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.time-text {\n  font-size: 22rpx;\n  color: #666;\n}\n\n.timing-badge {\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  padding: 8rpx 16rpx;\n  border-radius: 16rpx;\n  font-size: 22rpx;\n  color: #fff;\n  height: auto;\n  line-height: 1.2;\n}\n\n.timing-badge.timing-normal {\n  background: #4caf50;\n}\n\n.timing-badge.timing-warning {\n  background: #ff9800;\n}\n\n.timing-badge.timing-urgent {\n  background: #f44336;\n}\n\n.timing-badge.timing-overdue {\n  background: #9c27b0;\n}\n\n.overdue-text {\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  font-size: 22rpx;\n  color: #f44336;\n  font-weight: 600;\n  height: auto;\n  line-height: 1.2;\n}\n\n/* 时效提醒和完成按钮容器 */\n.task-timing {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n.timing-content {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  flex: 1;\n}\n\n/* 完成任务按钮 */\n.complete-task-btn {\n  background: linear-gradient(45deg, #007aff, #4fc3f7);\n  color: #fff;\n  border: none;\n  border-radius: 16rpx;\n  padding: 8rpx 16rpx;\n  font-size: 22rpx;\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.15);\n  transition: all 0.3s ease;\n  flex-shrink: 0;\n  min-width: 120rpx;\n  justify-content: center;\n  height: auto;\n  line-height: 1.2;\n}\n\n.complete-task-btn:active {\n  transform: scale(0.96);\n  box-shadow: 0 1rpx 4rpx rgba(0, 122, 255, 0.25);\n}\n\n.complete-task-btn text {\n  color: #fff;\n  font-weight: 500;\n}\n\n/* 空状态 */\n.empty-state {\n  padding: 80rpx 40rpx;\n  display: flex;\n  justify-content: center;\n}\n\n.empty-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  max-width: 360rpx;\n}\n\n.empty-icon {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50rpx;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 24rpx;\n}\n\n.empty-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 12rpx;\n  text-align: center;\n}\n\n.empty-desc {\n  font-size: 24rpx;\n  color: #666;\n  text-align: center;\n  line-height: 1.5;\n  white-space: pre-line;\n}\n\n/* 加载状态 */\n.loading-state {\n  padding: 100rpx 0;\n  display: flex;\n  justify-content: center;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./responsible-tasks.vue?vue&type=style&index=0&id=01bd25d6&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./responsible-tasks.vue?vue&type=style&index=0&id=01bd25d6&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571664432\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
<view class="data-v-2980693f"><view class="uni-container data-v-2980693f"><view class="db-container data-v-2980693f"><uni-collapse vue-id="068d3fa7-1" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><uni-collapse-item vue-id="{{('068d3fa7-2')+','+('068d3fa7-1')}}" title="搜索筛选" title-border="none" border="{{false}}" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><view class="search-box data-v-2980693f"><view class="select-row data-v-2980693f"><view class="select-item data-v-2980693f"><view data-event-opts="{{[['tap',[['showStatusPicker',['$event']]]]]}}" class="picker-button data-v-2980693f" bindtap="__e"><text class="{{['picker-text','data-v-2980693f',(!searchParams.status)?'placeholder':'']}}">{{''+$root.m0+''}}</text><text class="picker-arrow data-v-2980693f">▼</text></view></view><view class="select-item data-v-2980693f"><view data-event-opts="{{[['tap',[['showUrgencyPicker',['$event']]]]]}}" class="picker-button data-v-2980693f" bindtap="__e"><text class="{{['picker-text','data-v-2980693f',(!searchParams.urgency)?'placeholder':'']}}">{{''+$root.m1+''}}</text><text class="picker-arrow data-v-2980693f">▼</text></view></view></view></view><view class="search-box data-v-2980693f"><view class="search-row data-v-2980693f"><view class="search-item full-width data-v-2980693f"><uni-easyinput vue-id="{{('068d3fa7-3')+','+('068d3fa7-2')}}" placeholder="搜索姓名、描述或项目" clearable="{{true}}" value="{{searchParams.keyword}}" data-event-opts="{{[['^input',[['__set_model',['$0','keyword','$event',[]],['searchParams']],['onKeywordSearch']]]]}}" bind:input="__e" class="data-v-2980693f" bind:__l="__l"></uni-easyinput></view></view><view class="select-row data-v-2980693f"><view class="select-item data-v-2980693f"><view data-event-opts="{{[['tap',[['showProjectPicker',['$event']]]]]}}" class="picker-button data-v-2980693f" bindtap="__e"><text class="{{['picker-text','data-v-2980693f',(!searchParams.project)?'placeholder':'']}}">{{''+$root.m2+''}}</text><text class="picker-arrow data-v-2980693f">▼</text></view></view><view class="select-item data-v-2980693f"><view data-event-opts="{{[['tap',[['showResponsiblePicker',['$event']]]]]}}" class="picker-button data-v-2980693f" bindtap="__e"><text class="{{['picker-text','data-v-2980693f',(!searchParams.responsible)?'placeholder':'']}}">{{''+$root.m3+''}}</text><text class="picker-arrow data-v-2980693f">▼</text></view></view></view></view><view class="search-box date-section data-v-2980693f"><view class="date-row data-v-2980693f"><view class="date-item full-width data-v-2980693f"><text class="search-label data-v-2980693f">创建日期：</text><uni-datetime-picker vue-id="{{('068d3fa7-4')+','+('068d3fa7-2')}}" type="daterange" clear-icon="{{true}}" value="{{createDateRange}}" data-event-opts="{{[['^change',[['onCreateDateChange']]],['^input',[['__set_model',['','createDateRange','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-2980693f" bind:__l="__l"></uni-datetime-picker></view></view></view></uni-collapse-item></uni-collapse><view class="search-table-divider data-v-2980693f"></view><view class="data-area data-v-2980693f"><block wx:if="{{$root.g0>0}}"><view class="data-v-2980693f"><uni-table vue-id="068d3fa7-5" loading="{{isLoading}}" emptyText="没有更多数据" border="{{true}}" stripe="{{true}}" data-ref="table" class="data-v-2980693f vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-tr vue-id="{{('068d3fa7-6')+','+('068d3fa7-5')}}" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><uni-th vue-id="{{('068d3fa7-7')+','+('068d3fa7-6')}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">序号</uni-th><uni-th vue-id="{{('068d3fa7-8')+','+('068d3fa7-6')}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">姓名</uni-th><uni-th vue-id="{{('068d3fa7-9')+','+('068d3fa7-6')}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">找茬项目</uni-th><uni-th vue-id="{{('068d3fa7-10')+','+('068d3fa7-6')}}" align="center" width="360" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">问题描述</uni-th><uni-th vue-id="{{('068d3fa7-11')+','+('068d3fa7-6')}}" align="center" width="240" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">图片</uni-th><uni-th vue-id="{{('068d3fa7-12')+','+('068d3fa7-6')}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">创建时间</uni-th><uni-th vue-id="{{('068d3fa7-13')+','+('068d3fa7-6')}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">负责人</uni-th><uni-th vue-id="{{('068d3fa7-14')+','+('068d3fa7-6')}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">状态</uni-th><uni-th vue-id="{{('068d3fa7-15')+','+('068d3fa7-6')}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">进度</uni-th><uni-th vue-id="{{('068d3fa7-16')+','+('068d3fa7-6')}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">时效</uni-th><uni-th vue-id="{{('068d3fa7-17')+','+('068d3fa7-6')}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">理由</uni-th><block wx:if="{{hasOperationPermission}}"><uni-th vue-id="{{('068d3fa7-18')+','+('068d3fa7-6')}}" align="center" width="120" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">操作</uni-th></block></uni-tr><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="_id"><uni-tr vue-id="{{('068d3fa7-19-'+index)+','+('068d3fa7-5')}}" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><uni-td vue-id="{{('068d3fa7-20-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">{{currentPageStart+index+1}}</uni-td><uni-td vue-id="{{('068d3fa7-21-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">{{item.$orig.name}}</uni-td><uni-td vue-id="{{('068d3fa7-22-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}">{{item.$orig.project}}</uni-td><uni-td vue-id="{{('068d3fa7-23-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><view class="description-cell data-v-2980693f"><text class="{{['description-text','data-v-2980693f',(!item.$orig.isExpanded)?'text-truncate':'']}}">{{''+item.$orig.description+''}}</text><block wx:if="{{item.g1}}"><view data-event-opts="{{[['tap',[['toggleExpand',[index]]]]]}}" class="expand-button data-v-2980693f" catchtap="__e">{{''+(item.$orig.isExpanded?'收起':'展开')+''}}</view></block></view></uni-td><uni-td vue-id="{{('068d3fa7-24-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{item.g2}}"><view class="image-container data-v-2980693f"><view class="image-wrapper data-v-2980693f"><block wx:if="{{item.g3>0}}"><image class="image-hover data-v-2980693f" src="{{item.$orig.images[0]}}" mode="aspectFit" lazy-load="{{true}}" data-event-opts="{{[['error',[['handleImageError',['$event']]]],['tap',[['previewImage',['$0',0],[[['feedbackList','_id',item.$orig._id,'images']]]]]]]}}" binderror="__e" catchtap="__e"></image></block></view><block wx:if="{{item.g4>1}}"><view class="image-wrapper data-v-2980693f"><image class="image-hover data-v-2980693f" src="{{item.$orig.images[1]}}" mode="aspectFit" lazy-load="{{true}}" data-event-opts="{{[['error',[['handleImageError',['$event']]]],['tap',[['previewImage',['$0',1],[[['feedbackList','_id',item.$orig._id,'images']]]]]]]}}" binderror="__e" catchtap="__e"></image><block wx:if="{{item.g5>2}}"><view class="image-overlay data-v-2980693f">{{'+'+(item.g6-2)+''}}</view></block></view></block></view></block><block wx:else><text class="no-image data-v-2980693f">无图片</text></block></uni-td><uni-td vue-id="{{('068d3fa7-25-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><text class="time-text data-v-2980693f">{{item.m4}}</text></uni-td><uni-td vue-id="{{('068d3fa7-26-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{item.$orig.responsibleInfo}}"><view class="responsible-info data-v-2980693f"><text class="responsible-name data-v-2980693f">{{item.$orig.responsibleInfo.name}}</text><block wx:if="{{item.$orig.responsibleInfo.assignedTime}}"><text class="assigned-time data-v-2980693f">{{''+item.m5+''}}</text></block><block wx:if="{{item.$orig.responsibleInfo.assignedTime&&item.$orig.createTime}}"><text class="debug-info data-v-2980693f" style="font-size:12px;color:red;display:block;">{{'调试: 创建'+item.m6+"\n\t\t\t\t\t\t\t\t\t\t"+(item.$orig.responsibleInfo.assignedTime===item.$orig.createTime?'⚠️时间相同':'✅时间不同')+''}}</text></block></view></block><block wx:else><text class="no-responsible data-v-2980693f">未指派</text></block></uni-td><uni-td vue-id="{{('068d3fa7-27-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><view class="status-cell data-v-2980693f"><view class="status-badge data-v-2980693f" style="{{'background-color:'+(item.$orig.statusInfo.color)+';'}}">{{''+item.$orig.statusInfo.name+''}}</view></view></uni-td><uni-td vue-id="{{('068d3fa7-28-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><view class="progress-cell data-v-2980693f"><view class="progress-bar data-v-2980693f"><view class="progress-fill data-v-2980693f" style="{{'width:'+(item.$orig.progress+'%')+';'}}"></view></view><text class="progress-text data-v-2980693f">{{item.$orig.progress+"%"}}</text></view></uni-td><uni-td vue-id="{{('068d3fa7-29-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><view class="timing-cell data-v-2980693f"><view class="{{['timing-badge','data-v-2980693f',item.$orig.timing.urgency]}}" title="{{item.$orig.timing.description}}">{{''+(item.$orig.timing.description||item.$orig.timing.daysPassed+'天')+''}}</view><block wx:if="{{item.$orig.timing.isOverdue}}"><text class="overdue-text data-v-2980693f">已超期</text></block></view></uni-td><uni-td vue-id="{{('068d3fa7-30-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><view class="remarks-cell data-v-2980693f"><block wx:if="{{item.m7!=='-'}}"><view class="reason-text data-v-2980693f"><rich-text nodes="{{item.m8}}"></rich-text></view></block><block wx:else><text class="no-reason data-v-2980693f">-</text></block></view></uni-td><block wx:if="{{hasOperationPermission}}"><uni-td vue-id="{{('068d3fa7-31-'+index)+','+('068d3fa7-19-'+index)}}" align="center" class="data-v-2980693f" bind:__l="__l" vue-slots="{{['default']}}"><view class="action-buttons data-v-2980693f"><button data-event-opts="{{[['tap',[['viewDetail',['$0'],[[['feedbackList','_id',item.$orig._id]]]]]]]}}" class="action-btn view-btn data-v-2980693f" bindtap="__e">查看</button><block wx:if="{{item.g7}}"><button data-event-opts="{{[['tap',[['editItem',['$0'],[[['feedbackList','_id',item.$orig._id]]]]]]]}}" class="action-btn edit-btn data-v-2980693f" bindtap="__e">编辑</button></block><block wx:if="{{hasDeletePermission}}"><button data-event-opts="{{[['tap',[['deleteItem',['$0'],[[['feedbackList','_id',item.$orig._id]]]]]]]}}" class="action-btn delete-btn data-v-2980693f" bindtap="__e">删除</button></block><block wx:if="{{item.g8}}"><button data-event-opts="{{[['tap',[['submitCompletion',['$0'],[[['feedbackList','_id',item.$orig._id]]]]]]]}}" class="action-btn complete-btn data-v-2980693f" bindtap="__e">完成任务</button></block></view></uni-td></block></uni-tr></block></uni-table><view class="pagination-container data-v-2980693f"><uni-pagination vue-id="068d3fa7-32" total="{{totalCount}}" pageSize="{{pageSize}}" current="{{currentPage}}" show-icon="true" data-event-opts="{{[['^change',[['onPageChange']]]]}}" bind:change="__e" class="data-v-2980693f" bind:__l="__l"></uni-pagination></view></view></block><block wx:else><view class="no-data-area data-v-2980693f"><block wx:if="{{!hasInitialized}}"><view class="data-loading data-v-2980693f"><uni-load-more vue-id="068d3fa7-33" status="loading" content-text="{{({contentdown:'正在加载数据...'})}}" class="data-v-2980693f" bind:__l="__l"></uni-load-more></view></block><block wx:else><block wx:if="{{isLoading}}"><view class="data-loading data-v-2980693f"><uni-load-more vue-id="068d3fa7-34" status="loading" content-text="{{({contentdown:'搜索中...'})}}" class="data-v-2980693f" bind:__l="__l"></uni-load-more></view></block><block wx:else><p-empty-state vue-id="068d3fa7-35" type="data" text="暂无问题反馈数据" size="medium" class="data-v-2980693f" bind:__l="__l"></p-empty-state></block></block></view></block></view></view></view><uni-popup vue-id="068d3fa7-36" type="bottom" safe-area="{{false}}" data-ref="statusPopup" class="data-v-2980693f vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-2980693f"><view class="popup-header data-v-2980693f"><text class="popup-title data-v-2980693f">选择工作流状态</text><text data-event-opts="{{[['tap',[['closeStatusPicker',['$event']]]]]}}" class="popup-close data-v-2980693f" bindtap="__e">取消</text></view><view class="popup-body data-v-2980693f"><block wx:for="{{statusOptions}}" wx:for-item="item" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['selectStatus',['$0'],[[['statusOptions','value',item.value,'value']]]]]]]}}" class="{{['popup-item','data-v-2980693f',(searchParams.status===item.value)?'active':'']}}" bindtap="__e"><text class="data-v-2980693f">{{item.text}}</text><block wx:if="{{searchParams.status===item.value}}"><text class="check-icon data-v-2980693f">✓</text></block></view></block></view></view></uni-popup><uni-popup vue-id="068d3fa7-37" type="bottom" safe-area="{{false}}" data-ref="urgencyPopup" class="data-v-2980693f vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-2980693f"><view class="popup-header data-v-2980693f"><text class="popup-title data-v-2980693f">选择紧急程度</text><text data-event-opts="{{[['tap',[['closeUrgencyPicker',['$event']]]]]}}" class="popup-close data-v-2980693f" bindtap="__e">取消</text></view><view class="popup-body data-v-2980693f"><block wx:for="{{urgencyOptions}}" wx:for-item="item" wx:for-index="__i1__" wx:key="value"><view data-event-opts="{{[['tap',[['selectUrgency',['$0'],[[['urgencyOptions','value',item.value,'value']]]]]]]}}" class="{{['popup-item','data-v-2980693f',(searchParams.urgency===item.value)?'active':'']}}" bindtap="__e"><text class="data-v-2980693f">{{item.text}}</text><block wx:if="{{searchParams.urgency===item.value}}"><text class="check-icon data-v-2980693f">✓</text></block></view></block></view></view></uni-popup><uni-popup vue-id="068d3fa7-38" type="bottom" safe-area="{{false}}" data-ref="projectPopup" class="data-v-2980693f vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-2980693f"><view class="popup-header data-v-2980693f"><text class="popup-title data-v-2980693f">选择找茬项目</text><text data-event-opts="{{[['tap',[['closeProjectPicker',['$event']]]]]}}" class="popup-close data-v-2980693f" bindtap="__e">取消</text></view><view class="popup-body data-v-2980693f"><block wx:for="{{projectOptions}}" wx:for-item="item" wx:for-index="__i2__" wx:key="value"><view data-event-opts="{{[['tap',[['selectProject',['$0'],[[['projectOptions','value',item.value,'value']]]]]]]}}" class="{{['popup-item','data-v-2980693f',(searchParams.project===item.value)?'active':'']}}" bindtap="__e"><text class="data-v-2980693f">{{item.text}}</text><block wx:if="{{searchParams.project===item.value}}"><text class="check-icon data-v-2980693f">✓</text></block></view></block></view></view></uni-popup><uni-popup vue-id="068d3fa7-39" type="bottom" safe-area="{{false}}" data-ref="responsiblePopup" class="data-v-2980693f vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-2980693f"><view class="popup-header data-v-2980693f"><text class="popup-title data-v-2980693f">选择责任人</text><text data-event-opts="{{[['tap',[['closeResponsiblePicker',['$event']]]]]}}" class="popup-close data-v-2980693f" bindtap="__e">取消</text></view><view class="popup-body data-v-2980693f"><block wx:for="{{responsibleOptions}}" wx:for-item="item" wx:for-index="__i3__" wx:key="value"><view data-event-opts="{{[['tap',[['selectResponsible',['$0'],[[['responsibleOptions','value',item.value,'value']]]]]]]}}" class="{{['popup-item','data-v-2980693f',(searchParams.responsible===item.value)?'active':'']}}" bindtap="__e"><text class="data-v-2980693f">{{item.text}}</text><block wx:if="{{searchParams.responsible===item.value}}"><text class="check-icon data-v-2980693f">✓</text></block></view></block></view></view></uni-popup></view>
{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/honor_pkg/admin/type-manager.vue?a7bf", "webpack:///D:/Xwzc/pages/honor_pkg/admin/type-manager.vue?3458", "webpack:///D:/Xwzc/pages/honor_pkg/admin/type-manager.vue?e668", "webpack:///D:/Xwzc/pages/honor_pkg/admin/type-manager.vue?1da4", "uni-app:///pages/honor_pkg/admin/type-manager.vue", "webpack:///D:/Xwzc/pages/honor_pkg/admin/type-manager.vue?9139", "webpack:///D:/Xwzc/pages/honor_pkg/admin/type-manager.vue?59f5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "loading", "refreshing", "saving", "stats", "totalTypes", "activeTypes", "thisMonthTypes", "typeList", "searchKeyword", "activeFilter", "showCreateModal", "editingType", "createForm", "code", "description", "isActive", "computed", "activeTypeList", "inactiveTypeList", "filteredTypeList", "list", "type", "onLoad", "methods", "initializeData", "uni", "title", "icon", "loadTypeList", "uniCloud", "action", "res", "loadStats", "createDate", "onRefresh", "duration", "loadMore", "changeFilter", "performSearch", "clearSearch", "openCreateModal", "closeCreateModal", "resetCreateForm", "editType", "deleteType", "content", "success", "typeId", "result", "onActiveChange", "saveType", "validateCreateForm", "formatTime", "goBack", "generateCode", "key", "value", "convertToPinyin", "text", "onNameChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClEA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2O1nB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;QACAd;QACAe;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACAC;MACA;QACAA;MACA;;MAEA;MACA;QACA;QACAA;UAAA,OACAC,6CACAA,6CACAA;QAAA,EACA;MACA;MAEA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBACA/B;kBACAC;oBACA+B;kBACA;gBACA;cAAA;gBALAC;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAKA;IAEA;IACAC;MACA;MACA;MACA;MAEA;MACA;MACA;QACA;QACA;QACA,mDACAC;MACA;MAEA;QACA7B;QACAC;QACAC;MACA;IACA;IAEA;IACA4B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAT;kBACAC;kBACAC;kBACAQ;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAV;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAS;MACA;IAAA,CACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA5C;QACAe;QACAC;QACAC;MACA;IACA;IAEA;IACA4B;MACA;MACA;QACA7C;QACAe;QACAC;QACAC;MACA;MACA;IACA;IAEA;IACA6B;MAAA;MACAnB;QACAC;QACAmB;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAf;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA;oBAAA,OAEAF;sBACA/B;sBACAC;wBACA+B;wBACA/B;0BACAgD;wBACA;sBACA;oBACA;kBAAA;oBARAC;oBAAA,MAUAA;sBAAA;sBAAA;oBAAA;oBACAvB;sBACAC;sBACAC;oBACA;oBAAA;oBAAA,OACA;kBAAA;oBACA;oBAAA;oBAAA;kBAAA;oBAAA,MAEA;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAGAF;sBACAC;sBACAC;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAsB;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAGApB;gBACA/B;kBACAD;kBACAe;kBACAC;kBACAC;gBACA;gBAEA;kBACAhB;gBACA;gBAAA;gBAAA,OAEA8B;kBACA/B;kBACAC;oBAAA+B;oBAAA/B;kBAAA;gBACA;cAAA;gBAHAgC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAC;gBACA;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwB;MACA;QACA1B;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAyB;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA5B;IACA;IAEA;IACA6B;MACA;QACA7B;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;QAAA;UAAA4B;UAAAC;QACA;UACA;UACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QAAA;QAAA;QAAA;QACA;QAAA;QAAA;QAAA;QACA;QAAA;QAAA;QAAA;MAAA,uDACA,4NACA,+NACA,6NACA,+NACA,+NACA,wRACA,iOACA,sKACA,kOACA,4LACA;MAEA;MAAA,2CACAC;QAAA;MAAA;QAAA;UAAA;UACA;YACAV;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MAEA;IACA;IAEA;IACAW;MACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5pBA;AAAA;AAAA;AAAA;AAAyqC,CAAgB,+oCAAG,EAAC,C;;;;;;;;;;;ACA7rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/honor_pkg/admin/type-manager.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/honor_pkg/admin/type-manager.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./type-manager.vue?vue&type=template&id=96ed2d2a&scoped=true&\"\nvar renderjs\nimport script from \"./type-manager.vue?vue&type=script&lang=js&\"\nexport * from \"./type-manager.vue?vue&type=script&lang=js&\"\nimport style0 from \"./type-manager.vue?vue&type=style&index=0&id=96ed2d2a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"96ed2d2a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/honor_pkg/admin/type-manager.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./type-manager.vue?vue&type=template&id=96ed2d2a&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.typeList.length\n  var g1 = _vm.activeTypeList.length\n  var g2 = _vm.inactiveTypeList.length\n  var l0 = _vm.__map(_vm.filteredTypeList, function (type, __i0__) {\n    var $orig = _vm.__get_orig(type)\n    var m0 = _vm.formatTime(type.createTime)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g3 = _vm.filteredTypeList.length\n  var g4 = _vm.showCreateModal ? _vm.createForm.name.length : null\n  var g5 = _vm.showCreateModal ? _vm.createForm.description.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./type-manager.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./type-manager.vue?vue&type=script&lang=js&\"", "<template>\n  <div class=\"type-manager-container\">\n    <!-- 顶部导航 -->\n    <div class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"back-btn\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"18\" color=\"#FFFFFF\"></uni-icons>\n        </div>\n        <div class=\"title-area\">\n          <text class=\"title\">荣誉类型管理</text>\n          <text class=\"subtitle\">类型配置 · 状态管理</text>\n        </div>\n        <div class=\"header-actions\">\n          <div class=\"add-btn\" @click=\"openCreateModal\">\n            <uni-icons type=\"plus\" size=\"18\" color=\"#FFFFFF\"></uni-icons>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 内容区域 -->\n    <scroll-view \n      class=\"content-scroll\" \n      scroll-y=\"true\"\n      @scrolltolower=\"loadMore\"\n      :refresher-enabled=\"true\"\n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\"\n    >\n      <!-- 统计卡片 -->\n      <div class=\"stats-section\">\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\" style=\"background: rgba(58, 134, 255, 0.1);\">\n            <uni-icons type=\"star\" size=\"24\" color=\"#3a86ff\"></uni-icons>\n          </div>\n          <div class=\"stat-info\">\n            <div class=\"stat-number\">{{ stats.totalTypes }}</div>\n            <div class=\"stat-label\">总类型</div>\n          </div>\n        </div>\n        \n        <div class=\"stat-card\">\n          <div class=\"stat-icon\" style=\"background: rgba(16, 185, 129, 0.1);\">\n            <uni-icons type=\"checkmarkempty\" size=\"24\" color=\"#10b981\"></uni-icons>\n          </div>\n          <div class=\"stat-info\">\n            <div class=\"stat-number\">{{ stats.activeTypes }}</div>\n            <div class=\"stat-label\">启用数</div>\n          </div>\n        </div>\n        \n        <div class=\"stat-card\">\n          <div class=\"stat-icon\" style=\"background: rgba(245, 158, 11, 0.1);\">\n            <uni-icons type=\"calendar\" size=\"24\" color=\"#f59e0b\"></uni-icons>\n          </div>\n          <div class=\"stat-info\">\n            <div class=\"stat-number\">{{ stats.thisMonthTypes }}</div>\n            <div class=\"stat-label\">月增数</div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 搜索和筛选 -->\n      <div class=\"filter-section\">\n        <div class=\"search-bar\">\n          <uni-easyinput \n            v-model=\"searchKeyword\" \n            placeholder=\"搜索荣誉类型名称...\"\n            prefixIcon=\"search\"\n            @input=\"performSearch\"\n            @clear=\"clearSearch\"\n            clearable\n          ></uni-easyinput>\n        </div>\n        \n        <div class=\"filter-tabs\">\n          <div \n            class=\"filter-tab\"\n            :class=\"{ active: activeFilter === 'all' }\"\n            @click=\"changeFilter('all')\"\n          >\n            全部 ({{ typeList.length }})\n          </div>\n          <div \n            class=\"filter-tab\"\n            :class=\"{ active: activeFilter === 'active' }\"\n            @click=\"changeFilter('active')\"\n          >\n            启用 ({{ activeTypeList.length }})\n          </div>\n          <div \n            class=\"filter-tab\"\n            :class=\"{ active: activeFilter === 'inactive' }\"\n            @click=\"changeFilter('inactive')\"\n          >\n            禁用 ({{ inactiveTypeList.length }})\n          </div>\n        </div>\n      </div>\n      \n      <!-- 类型列表 -->\n      <div class=\"type-list\">\n        <div \n          v-for=\"type in filteredTypeList\" \n          :key=\"type._id\"\n          class=\"type-item\"\n          @click=\"editType(type)\"\n        >\n          <div class=\"type-icon\">\n            <uni-icons type=\"star\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n          </div>\n          \n          <div class=\"type-info\">\n            <div class=\"type-name\">{{ type.name }}</div>\n            <div class=\"type-desc\" v-if=\"type.description\">{{ type.description }}</div>\n            <div class=\"type-meta\">\n              <span class=\"create-time\">{{ formatTime(type.createTime) }}</span>\n            </div>\n          </div>\n          \n          <div class=\"type-status\">\n            <div \n              class=\"status-badge\"\n              :class=\"{ active: type.isActive, inactive: !type.isActive }\"\n            >\n              {{ type.isActive ? '启用' : '禁用' }}\n            </div>\n          </div>\n          \n          <div class=\"type-actions\" @click.stop=\"\">\n            <div class=\"action-btn edit\" @click=\"editType(type)\">\n              <uni-icons type=\"compose\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n            </div>\n            <div class=\"action-btn delete\" @click=\"deleteType(type)\">\n              <uni-icons type=\"trash\" size=\"16\" color=\"#ef4444\"></uni-icons>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 空状态 -->\n        <div v-if=\"filteredTypeList.length === 0\" class=\"empty-state\">\n          <uni-icons type=\"star\" size=\"60\" color=\"#c4c4c4\"></uni-icons>\n          <div class=\"empty-title\">暂无荣誉类型</div>\n          <div class=\"empty-desc\">点击右上角 + 号创建第一个荣誉类型</div>\n        </div>\n      </div>\n    </scroll-view>\n    \n    <!-- 创建/编辑模态框 -->\n    <div v-if=\"showCreateModal\" class=\"modal-overlay\" @click=\"closeCreateModal\">\n      <div class=\"modal-content\" @click.stop=\"\">\n        <div class=\"modal-header\">\n          <div class=\"modal-title\">{{ editingType ? '编辑' : '创建' }}荣誉类型</div>\n          <div class=\"close-btn\" @click=\"closeCreateModal\">\n            <uni-icons type=\"close\" size=\"20\" color=\"#8a94a6\"></uni-icons>\n          </div>\n        </div>\n        \n        <div class=\"modal-body\">\n          <div class=\"form-item\">\n            <div class=\"form-label\">类型名称 <span class=\"required\">*</span></div>\n            <uni-easyinput \n              v-model=\"createForm.name\" \n              placeholder=\"请输入荣誉类型名称\"\n              maxlength=\"20\"\n              @input=\"onNameChange\"\n            ></uni-easyinput>\n            <div class=\"char-count\">{{ createForm.name.length }}/20</div>\n          </div>\n          \n          <div class=\"form-item\">\n            <div class=\"form-label\">类型代码 <span class=\"required\">*</span></div>\n            <div class=\"code-input-group\">\n              <uni-easyinput \n                v-model=\"createForm.code\" \n                placeholder=\"将根据类型名称自动生成\"\n                maxlength=\"50\"\n                :disabled=\"true\"\n              ></uni-easyinput>\n              <button class=\"generate-btn\" @click=\"generateCode\" type=\"button\">\n                <uni-icons type=\"refresh\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n                重新生成\n              </button>\n            </div>\n            <div class=\"form-desc\">类型代码将根据类型名称自动生成，用于系统识别</div>\n          </div>\n          \n          <div class=\"form-item\">\n            <div class=\"form-label\">类型描述</div>\n            <uni-easyinput \n              v-model=\"createForm.description\" \n              placeholder=\"请输入类型描述\"\n              type=\"textarea\"\n              :auto-height=\"true\"\n              maxlength=\"200\"\n            ></uni-easyinput>\n            <div class=\"char-count\">{{ createForm.description.length }}/200</div>\n          </div>\n          \n          <div class=\"form-item\">\n            <div class=\"switch-item\">\n              <div class=\"switch-label\">启用状态</div>\n              <switch \n                :checked=\"createForm.isActive\" \n                @change=\"onActiveChange\"\n                color=\"#3a86ff\"\n              />\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"modal-footer\">\n          <button class=\"btn-cancel\" @click=\"closeCreateModal\">取消</button>\n          <button \n            class=\"btn-confirm\" \n            @click=\"saveType\"\n            :disabled=\"!createForm.name || !createForm.code || saving\"\n          >\n            {{ saving ? '保存中...' : (editingType ? '更新' : '创建') }}\n          </button>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 加载中遮罩 -->\n    <div v-if=\"loading\" class=\"loading-overlay\">\n      <div class=\"loading-content\">\n        <uni-icons type=\"spinner-cycle\" size=\"40\" color=\"#3a86ff\" class=\"loading-spin\"></uni-icons>\n        <div class=\"loading-text\">加载中...</div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TypeManager',\n  data() {\n    return {\n      loading: false,\n      refreshing: false,\n      saving: false,\n      \n      // 统计数据\n      stats: {\n        totalTypes: 0,\n        activeTypes: 0,\n        thisMonthTypes: 0\n      },\n      \n      // 类型列表\n      typeList: [],\n      \n      // 搜索和筛选\n      searchKeyword: '',\n      activeFilter: 'all',\n      \n      // 创建/编辑\n      showCreateModal: false,\n      editingType: null,\n      createForm: {\n        name: '',\n        code: '',\n        description: '',\n        isActive: true\n      }\n    }\n  },\n  \n  computed: {\n    // 启用的类型列表\n    activeTypeList() {\n      return this.typeList.filter(type => type.isActive)\n    },\n    \n    // 禁用的类型列表\n    inactiveTypeList() {\n      return this.typeList.filter(type => !type.isActive)\n    },\n    \n    // 过滤后的类型列表\n    filteredTypeList() {\n      let list = this.typeList\n      \n      // 按状态筛选\n      if (this.activeFilter === 'active') {\n        list = this.activeTypeList\n      } else if (this.activeFilter === 'inactive') {\n        list = this.inactiveTypeList\n      }\n      \n      // 搜索筛选\n      if (this.searchKeyword.trim()) {\n        const keyword = this.searchKeyword.trim().toLowerCase()\n        list = list.filter(type => \n          type.name.toLowerCase().includes(keyword) ||\n          type.code.toLowerCase().includes(keyword) ||\n          (type.description && type.description.toLowerCase().includes(keyword))\n        )\n      }\n      \n      return list\n    }\n  },\n  \n  onLoad() {\n    this.initializeData()\n  },\n  \n  methods: {\n    // 初始化数据\n    async initializeData() {\n      this.loading = true\n      try {\n        // 先加载类型列表\n        await this.loadTypeList()\n        // 类型列表加载完成后再计算统计数据\n        this.loadStats()\n      } catch (error) {\n        uni.showToast({\n          title: '数据加载失败',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 加载荣誉类型列表\n    async loadTypeList() {\n      try {\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getHonorTypes'\n          }\n        })\n        \n        if (res.result.code === 0) {\n          this.typeList = res.result.data || []\n        } else {\n          throw new Error(res.result.message || '获取类型列表失败')\n        }\n      } catch (error) {\n        throw error\n      }\n    },\n    \n    // 加载统计数据\n    loadStats() {\n      const now = new Date()\n      const currentMonth = now.getMonth() + 1\n      const currentYear = now.getFullYear()\n      \n      const totalTypes = this.typeList.length\n      const activeTypes = this.activeTypeList.length\n      const thisMonthTypes = this.typeList.filter(type => {\n        if (!type.createTime) return false\n        const createDate = new Date(type.createTime)\n        return createDate.getFullYear() === currentYear && \n               createDate.getMonth() + 1 === currentMonth\n      }).length\n      \n      this.stats = {\n        totalTypes,\n        activeTypes,\n        thisMonthTypes\n      }\n    },\n    \n    // 下拉刷新\n    async onRefresh() {\n      this.refreshing = true\n      try {\n        await this.initializeData()\n        uni.showToast({\n          title: '刷新成功',\n          icon: 'success',\n          duration: 1500\n        })\n      } catch (error) {\n        uni.showToast({\n          title: '刷新失败',\n          icon: 'none'\n        })\n      } finally {\n        this.refreshing = false\n      }\n    },\n    \n    // 加载更多\n    loadMore() {\n      // 暂时不需要分页\n    },\n    \n    // 筛选变更\n    changeFilter(filter) {\n      this.activeFilter = filter\n    },\n    \n    // 搜索\n    performSearch() {\n      // 搜索在computed中实时进行\n    },\n    \n    // 清除搜索\n    clearSearch() {\n      this.searchKeyword = ''\n    },\n    \n    // 打开创建模态框\n    openCreateModal() {\n      this.showCreateModal = true\n      this.editingType = null\n      this.resetCreateForm()\n    },\n    \n    // 关闭创建模态框\n    closeCreateModal() {\n      this.showCreateModal = false\n      this.editingType = null\n    },\n    \n    // 重置创建表单\n    resetCreateForm() {\n      this.createForm = {\n        name: '',\n        code: '',\n        description: '',\n        isActive: true\n      }\n    },\n    \n    // 编辑类型\n    editType(type) {\n      this.editingType = type\n      this.createForm = {\n        name: type.name,\n        code: type.code,\n        description: type.description || '',\n        isActive: type.isActive\n      }\n      this.showCreateModal = true\n    },\n    \n    // 删除类型\n    deleteType(type) {\n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除荣誉类型\"${type.name}\"吗？删除后不可恢复。`,\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const result = await uniCloud.callFunction({\n                name: 'honor-admin',\n                data: {\n                  action: 'deleteHonorType',\n                  data: {\n                    typeId: type._id\n                  }\n                }\n              })\n              \n              if (result.result.code === 0) {\n                uni.showToast({\n                  title: '删除成功',\n                  icon: 'success'\n                })\n                await this.loadTypeList()\n                this.loadStats()\n              } else {\n                throw new Error(result.result.message || '删除失败')\n              }\n            } catch (error) {\n              uni.showToast({\n                title: error.message || '删除失败',\n                icon: 'none'\n              })\n            }\n          }\n        }\n      })\n    },\n    \n    // 启用状态变化\n    onActiveChange(e) {\n      this.createForm.isActive = e.detail.value\n    },\n    \n    // 保存类型\n    async saveType() {\n      if (!this.validateCreateForm()) {\n        return\n      }\n      \n      this.saving = true\n      \n      try {\n        const action = this.editingType ? 'updateHonorType' : 'createHonorType'\n        const data = {\n          name: this.createForm.name,\n          code: this.createForm.code,\n          description: this.createForm.description,\n          isActive: this.createForm.isActive\n        }\n        \n        if (this.editingType) {\n          data.typeId = this.editingType._id\n        }\n        \n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: { action, data }\n        })\n        \n        if (res.result.code === 0) {\n          uni.showToast({\n            title: this.editingType ? '更新成功' : '创建成功',\n            icon: 'success'\n          })\n          \n          this.closeCreateModal()\n          await this.loadTypeList()\n          this.loadStats()\n        } else {\n          throw new Error(res.result.message || '保存失败')\n        }\n              } catch (error) {\n          uni.showToast({\n          title: error.message || '保存失败',\n          icon: 'none'\n        })\n      } finally {\n        this.saving = false\n      }\n    },\n    \n    // 验证创建表单\n    validateCreateForm() {\n      if (!this.createForm.name.trim()) {\n        uni.showToast({\n          title: '请输入类型名称',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.createForm.code.trim()) {\n        uni.showToast({\n          title: '请输入类型代码',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      // 检查代码格式\n      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(this.createForm.code)) {\n        uni.showToast({\n          title: '类型代码格式不正确',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      return true\n    },\n    \n    // 格式化时间\n    formatTime(time) {\n      if (!time) return ''\n      const date = new Date(time)\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\n    },\n    \n    // 返回\n    goBack() {\n      uni.navigateBack()\n    },\n    \n    // 生成类型代码\n    generateCode() {\n      if (!this.createForm.name.trim()) {\n        uni.showToast({\n          title: '请先输入类型名称',\n          icon: 'none'\n        })\n        return\n      }\n      \n      // 将中文名称转换为英文代码\n      const nameMap = {\n        '优秀员工': 'excellent_employee',\n        '敬业奉献': 'dedication_award',\n        '技术革新': 'technology_innovator', \n        '工艺优化': 'process_optimization',\n        '节能降耗': 'energy_saving_award',\n        '团队协作': 'team_spirit',\n        '环保标兵': 'environmental_pioneer',\n        '安全生产': 'safety_production',\n        '质量标兵': 'quality_champion',\n        '创新发明': 'innovation_award',\n        '服务明星': 'service_star',\n        '进步奖': 'progress_award',\n        '突出贡献': 'outstanding_contribution',\n        '年度标兵': 'annual_model',\n        '季度之星': 'quarterly_star',\n        '月度优秀': 'monthly_excellent'\n      }\n      \n      const name = this.createForm.name.trim()\n      \n      // 首先尝试直接匹配\n      if (nameMap[name]) {\n        this.createForm.code = nameMap[name]\n        return\n      }\n      \n      // 如果没有直接匹配，尝试模糊匹配\n      for (const [key, value] of Object.entries(nameMap)) {\n        if (name.includes(key) || key.includes(name)) {\n          this.createForm.code = value\n          return\n        }\n      }\n      \n      // 如果都没有匹配，生成一个基于拼音的代码\n      const code = this.convertToPinyin(name)\n      this.createForm.code = code || 'custom_award'\n    },\n    \n    // 将中文转换为拼音代码（简化版）\n    convertToPinyin(text) {\n      const pinyinMap = {\n        '优': 'you', '秀': 'xiu', '员': 'yuan', '工': 'gong',\n        '敬': 'jing', '业': 'ye', '奉': 'feng', '献': 'xian',\n        '技': 'ji', '术': 'shu', '革': 'ge', '新': 'xin',\n        '工': 'gong', '艺': 'yi', '优': 'you', '化': 'hua',\n        '节': 'jie', '能': 'neng', '降': 'jiang', '耗': 'hao',\n        '团': 'tuan', '队': 'dui', '协': 'xie', '作': 'zuo',\n        '环': 'huan', '保': 'bao', '标': 'biao', '兵': 'bing',\n        '安': 'an', '全': 'quan', '生': 'sheng', '产': 'chan',\n        '质': 'zhi', '量': 'liang', '创': 'chuang', '发': 'fa', '明': 'ming',\n        '服': 'fu', '务': 'wu', '明': 'ming', '星': 'xing',\n        '进': 'jin', '步': 'bu', '奖': 'award',\n        '突': 'tu', '出': 'chu', '贡': 'gong', '献': 'xian',\n        '年': 'nian', '度': 'du', '季': 'ji', '月': 'yue'\n      }\n      \n      let result = ''\n      for (let char of text) {\n        if (pinyinMap[char]) {\n          result += pinyinMap[char] + '_'\n        }\n      }\n      \n      return result.slice(0, -1) // 移除最后一个下划线\n    },\n    \n    // 监听类型名称变化\n    onNameChange() {\n      // 当名称有值时自动生成代码\n      if (this.createForm.name.trim()) {\n        this.generateCode()\n      } else {\n        this.createForm.code = ''\n      }\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.type-manager-container {\n  min-height: 100vh;\n  background: linear-gradient(145deg, #f8faff 0%, #e9f0f8 100%);\n  position: relative;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  background: linear-gradient(180deg, #3a86ff 0%, #2563eb 100%);\n  overflow: hidden;\n  \n  // 装饰性背景图案\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    right: -20%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);\n    transform: rotate(-15deg);\n    pointer-events: none;\n  }\n  \n  .header-content {\n    display: flex;\n    align-items: center;\n    padding: 20rpx 40rpx;\n    padding-top: calc(var(--status-bar-height, 0px) + 20rpx);\n    position: relative;\n    z-index: 2;\n    \n    .back-btn {\n      width: 60rpx;\n      height: 60rpx;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.2);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 30rpx;\n    }\n    \n    .title-area {\n      flex: 1;\n      \n      .title {\n        display: block;\n        font-size: 36rpx;\n        font-weight: 600;\n        color: #FFFFFF;\n        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n        line-height: 1.2;\n      }\n      \n      .subtitle {\n        display: block;\n        font-size: 24rpx;\n        color: rgba(255, 255, 255, 0.8);\n        margin-top: 4rpx;\n      }\n    }\n    \n    .header-actions {\n      .add-btn {\n        width: 60rpx;\n        height: 60rpx;\n        border-radius: 50%;\n        background: rgba(255, 255, 255, 0.2);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n    }\n  }\n}\n\n.content-scroll {\n  position: fixed;\n  top: calc(var(--status-bar-height, 0px) + 140rpx);\n  left: 0;\n  right: 0;\n  bottom: 0;\n  padding-bottom: 40rpx;\n}\n\n.stats-section {\n  display: flex;\n  gap: 24rpx;\n  padding: 40rpx;\n  \n  .stat-card {\n    flex: 1;\n    background: #FFFFFF;\n    border-radius: 20rpx;\n    padding: 32rpx;\n    display: flex;\n    align-items: center;\n    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n    \n    .stat-icon {\n      width: 60rpx;\n      height: 60rpx;\n      border-radius: 16rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 24rpx;\n    }\n    \n    .stat-info {\n      .stat-number {\n        font-size: 40rpx;\n        font-weight: 700;\n        color: #1a1a1a;\n        line-height: 1;\n      }\n      \n      .stat-label {\n        font-size: 22rpx;\n        color: #8a94a6;\n        margin-top: 8rpx;\n      }\n    }\n  }\n}\n\n.filter-section {\n  margin: 0 40rpx 32rpx;\n  \n  .search-bar {\n    margin-bottom: 24rpx;\n  }\n  \n  .filter-tabs {\n    display: flex;\n    background: #FFFFFF;\n    border-radius: 16rpx;\n    padding: 8rpx;\n    \n    .filter-tab {\n      flex: 1;\n      text-align: center;\n      padding: 16rpx 0;\n      font-size: 26rpx;\n      color: #8a94a6;\n      border-radius: 12rpx;\n      transition: all 0.3s ease;\n      \n      &.active {\n        background: #3a86ff;\n        color: #FFFFFF;\n        font-weight: 500;\n      }\n    }\n  }\n}\n\n.type-list {\n  padding: 0 40rpx;\n  \n  .type-item {\n    background: #FFFFFF;\n    border-radius: 20rpx;\n    padding: 32rpx;\n    margin-bottom: 24rpx;\n    display: flex;\n    align-items: center;\n    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n    \n    .type-icon {\n      width: 80rpx;\n      height: 80rpx;\n      border-radius: 20rpx;\n      background: #3a86ff;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 24rpx;\n    }\n    \n    .type-info {\n      flex: 1;\n      \n      .type-name {\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #1a1a1a;\n        line-height: 1.2;\n      }\n      \n      .type-desc {\n        font-size: 24rpx;\n        color: #8a94a6;\n        margin: 8rpx 0;\n        line-height: 1.3;\n      }\n      \n      .type-meta {\n        .create-time {\n          font-size: 22rpx;\n          color: #c4c4c4;\n        }\n      }\n    }\n    \n    .type-status {\n      margin-right: 24rpx;\n      \n      .status-badge {\n        padding: 8rpx 16rpx;\n        border-radius: 20rpx;\n        font-size: 22rpx;\n        font-weight: 500;\n        \n        &.active {\n          background: rgba(16, 185, 129, 0.1);\n          color: #10b981;\n        }\n        \n        &.inactive {\n          background: rgba(138, 148, 166, 0.1);\n          color: #8a94a6;\n        }\n      }\n    }\n    \n    .type-actions {\n      display: flex;\n      gap: 16rpx;\n      \n      .action-btn {\n        width: 60rpx;\n        height: 60rpx;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        \n        &.edit {\n          background: rgba(58, 134, 255, 0.1);\n        }\n        \n        &.delete {\n          background: rgba(239, 68, 68, 0.1);\n        }\n      }\n    }\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 120rpx 60rpx;\n  \n  .empty-title {\n    font-size: 32rpx;\n    color: #8a94a6;\n    margin: 32rpx 0 16rpx;\n  }\n  \n  .empty-desc {\n    font-size: 24rpx;\n    color: #c4c4c4;\n    line-height: 1.4;\n  }\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n  \n  .modal-content {\n    width: 100%;\n    max-width: 600rpx;\n    max-height: 80vh;\n    background: #FFFFFF;\n    border-radius: 24rpx;\n    overflow: hidden;\n    \n    .modal-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 40rpx;\n      border-bottom: 1px solid #f0f0f0;\n      \n      .modal-title {\n        font-size: 36rpx;\n        font-weight: 600;\n        color: #1a1a1a;\n      }\n      \n      .close-btn {\n        width: 60rpx;\n        height: 60rpx;\n        border-radius: 50%;\n        background: #f5f5f5;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n    }\n    \n    .modal-body {\n      padding: 40rpx;\n      max-height: 50vh;\n      overflow-y: auto;\n      \n      .form-item {\n        margin-bottom: 32rpx;\n        \n        &:last-child {\n          margin-bottom: 0;\n        }\n        \n        .form-label {\n          display: block;\n          font-size: 28rpx;\n          font-weight: 500;\n          color: #1a1a1a;\n          margin-bottom: 16rpx;\n          \n          .required {\n            color: #ef4444;\n            margin-left: 4rpx;\n          }\n        }\n        \n        .form-desc {\n          font-size: 24rpx;\n          color: #8a94a6;\n          margin-top: 12rpx;\n          line-height: 1.4;\n        }\n        \n        .char-count {\n          text-align: right;\n          font-size: 22rpx;\n          color: #c4c4c4;\n          margin-top: 8rpx;\n        }\n        \n        .code-input-group {\n          display: flex;\n          gap: 16rpx;\n          align-items: flex-start;\n          \n          .generate-btn {\n            background: linear-gradient(145deg, rgba(58, 134, 255, 0.1) 0%, rgba(58, 134, 255, 0.05) 100%);\n            color: #3a86ff;\n            border: 1px solid rgba(58, 134, 255, 0.2);\n            border-radius: 8rpx;\n            padding: 12rpx 20rpx;\n            font-size: 24rpx;\n            font-weight: 500;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: 8rpx;\n            white-space: nowrap;\n            height: 75rpx;\n            box-shadow: 0 2rpx 8rpx rgba(58, 134, 255, 0.1),\n                        inset 0 1rpx 0 rgba(255, 255, 255, 0.3);\n            transition: all 0.2s ease;\n            \n            &:active {\n              background: linear-gradient(145deg, rgba(58, 134, 255, 0.2) 0%, rgba(58, 134, 255, 0.1) 100%);\n              transform: translateY(1rpx);\n              box-shadow: 0 1rpx 4rpx rgba(58, 134, 255, 0.2),\n                          inset 0 1rpx 0 rgba(255, 255, 255, 0.2);\n            }\n          }\n        }\n      }\n      \n      .switch-item {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        \n        .switch-label {\n          font-size: 28rpx;\n          color: #1a1a1a;\n        }\n      }\n    }\n    \n    .modal-footer {\n      display: flex;\n      gap: 24rpx;\n      padding: 40rpx;\n      border-top: 1px solid #f0f0f0;\n      \n      .btn-cancel {\n        flex: 1;\n        height: 80rpx;\n        border-radius: 16rpx;\n        background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);\n        color: #6c757d;\n        font-size: 28rpx;\n        font-weight: 600;\n        border: none;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1), \n                    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);\n        transition: all 0.2s ease;\n        \n        &:active {\n          transform: translateY(1rpx);\n          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15),\n                      inset 0 1rpx 0 rgba(255, 255, 255, 0.6);\n        }\n      }\n      \n      .btn-confirm {\n        flex: 1;\n        height: 80rpx;\n        border-radius: 16rpx;\n        background: linear-gradient(145deg, #4285f4 0%, #3a86ff 100%);\n        color: #FFFFFF;\n        font-size: 28rpx;\n        font-weight: 600;\n        border: none;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        box-shadow: 0 6rpx 16rpx rgba(58, 134, 255, 0.3),\n                    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);\n        transition: all 0.2s ease;\n        \n        &:active {\n          transform: translateY(1rpx);\n          box-shadow: 0 3rpx 10rpx rgba(58, 134, 255, 0.4),\n                      inset 0 1rpx 0 rgba(255, 255, 255, 0.1);\n        }\n        \n        &:disabled {\n          background: linear-gradient(145deg, #e9ecef 0%, #c4c4c4 100%);\n          color: #8a94a6;\n          box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n          transform: none;\n        }\n      }\n    }\n  }\n}\n\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  .loading-content {\n    background: #FFFFFF;\n    border-radius: 20rpx;\n    padding: 60rpx;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    \n    .loading-spin {\n      animation: spin 1s linear infinite;\n    }\n    \n    .loading-text {\n      font-size: 28rpx;\n      color: #8a94a6;\n      margin-top: 24rpx;\n    }\n  }\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./type-manager.vue?vue&type=style&index=0&id=96ed2d2a&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./type-manager.vue?vue&type=style&index=0&id=96ed2d2a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558438010\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
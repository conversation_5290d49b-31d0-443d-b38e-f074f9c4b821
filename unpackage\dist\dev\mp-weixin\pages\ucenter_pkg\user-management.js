(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/ucenter_pkg/user-management"],{

/***/ 135:
/*!************************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fucenter_pkg%2Fuser-management"} ***!
  \************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _userManagement = _interopRequireDefault(__webpack_require__(/*! ./pages/ucenter_pkg/user-management.vue */ 136));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_userManagement.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 136:
/*!*****************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/user-management.vue ***!
  \*****************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _user_management_vue_vue_type_template_id_0671abb8_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./user-management.vue?vue&type=template&id=0671abb8&scoped=true& */ 137);
/* harmony import */ var _user_management_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./user-management.vue?vue&type=script&lang=js& */ 139);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _user_management_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _user_management_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _user_management_vue_vue_type_style_index_0_id_0671abb8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./user-management.vue?vue&type=style&index=0&id=0671abb8&lang=scss&scoped=true& */ 142);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _user_management_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _user_management_vue_vue_type_template_id_0671abb8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _user_management_vue_vue_type_template_id_0671abb8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "0671abb8",
  null,
  false,
  _user_management_vue_vue_type_template_id_0671abb8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/ucenter_pkg/user-management.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 137:
/*!************************************************************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/user-management.vue?vue&type=template&id=0671abb8&scoped=true& ***!
  \************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_template_id_0671abb8_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-management.vue?vue&type=template&id=0671abb8&scoped=true& */ 138);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_template_id_0671abb8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_template_id_0671abb8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_template_id_0671abb8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_template_id_0671abb8_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 138:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter_pkg/user-management.vue?vue&type=template&id=0671abb8&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniSearchBar: function () {
      return Promise.all(/*! import() | uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue */ 655))
    },
    uniDataSelect: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-data-select/components/uni-data-select/uni-data-select */ "uni_modules/uni-data-select/components/uni-data-select/uni-data-select").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue */ 634))
    },
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    uniPagination: function () {
      return Promise.all(/*! import() | uni_modules/uni-pagination/components/uni-pagination/uni-pagination */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-pagination/components/uni-pagination/uni-pagination")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-pagination/components/uni-pagination/uni-pagination.vue */ 598))
    },
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 490))
    },
    uniDataCheckbox: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox */ "uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue */ 627))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.selectedUsers.length
  var g1 = _vm.selectedUsers.length
  var g2 = g1 > 0 ? _vm.selectedUsers.length : null
  var g3 = _vm.selectedUsers.length
  var g4 = g3 > 0 ? _vm.selectedUsers.length : null
  var g5 = !_vm.loading
    ? _vm.hasLoaded && !_vm.loading && _vm.userList.length === 0
    : null
  var l0 =
    !_vm.loading && !g5
      ? _vm.__map(_vm.userList, function (user, __i1__) {
          var $orig = _vm.__get_orig(user)
          var g6 = _vm.selectedUsers.includes(user._id)
          var m0 = _vm.formatDateMemoized(user.register_date)
          var m1 = _vm.formatDateMemoized(user.last_login_date)
          var g7 = user.role.includes("admin")
          return {
            $orig: $orig,
            g6: g6,
            m0: m0,
            m1: m1,
            g7: g7,
          }
        })
      : null
  var g8 = _vm.selectedUsers.length
  var g9 = _vm.selectedUsers.length
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        g2: g2,
        g3: g3,
        g4: g4,
        g5: g5,
        l0: l0,
        g8: g8,
        g9: g9,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 139:
/*!******************************************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/user-management.vue?vue&type=script&lang=js& ***!
  \******************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-management.vue?vue&type=script&lang=js& */ 140);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 140:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter_pkg/user-management.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, uniCloud) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _config = _interopRequireDefault(__webpack_require__(/*! @/uni_modules/uni-id-pages/config.js */ 43));
var _password = _interopRequireDefault(__webpack_require__(/*! @/uni_modules/uni-id-pages/common/password.js */ 141));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  name: 'UserManagement',
  data: function data() {
    return {
      // 列表数据
      userList: [],
      total: 0,
      currentPage: 1,
      pageSize: 20,
      loading: false,
      hasLoaded: false,
      // 标记是否已经加载过数据

      // 搜索筛选
      searchKeyword: '',
      filterRole: '',
      filterStatus: '',
      filterAnonymous: '',
      sortField: '',
      searchTimer: null,
      // 选择状态
      selectedUsers: [],
      // 表单数据
      userForm: {
        username: '',
        password: '',
        nickname: '',
        role: [],
        dcloud_appid: [],
        // 可登录应用
        status: 0
      },
      isEdit: false,
      saving: false,
      // 批量操作
      batchRoleForm: [],
      // 重置密码/设置账号密码
      resetPasswordUser: {},
      newUsername: '',
      newPassword: '',
      confirmPassword: '',
      // 快速角色变更
      quickRoleUser: {},
      quickRoleForm: [],
      // 滑动操作相关
      swipeStates: {},
      // 存储每个用户的滑动状态
      touchStartX: 0,
      touchStartY: 0,
      startTranslateX: 0,
      // 开始滑动时的初始translateX位置
      currentSwipeUserId: null,
      isDragging: false,
      // H5拖拽状态

      // 角色和状态选项
      roleList: [],
      roleOptions: [],
      appList: [],
      // 应用列表
      statusOptions: [{
        value: '',
        text: '状态'
      }, {
        value: 0,
        text: '正常'
      }, {
        value: 1,
        text: '禁用'
      }],
      anonymousOptions: [{
        value: '',
        text: '全部用户'
      }, {
        value: 'no',
        text: '实名用户'
      }, {
        value: 'yes',
        text: '匿名用户'
      }],
      sortOptions: [{
        value: '',
        text: '默认排序'
      }, {
        value: 'name_asc',
        text: '姓名排序'
      }, {
        value: 'register_desc',
        text: '注册时间'
      }, {
        value: 'login_desc',
        text: '登录时间'
      }],
      // 性能优化相关
      isInitializing: false,
      // 初始化状态
      cacheExpiry: {
        roles: 2 * 60 * 60 * 1000,
        // 角色缓存2小时
        apps: 1 * 60 * 60 * 1000 // 应用缓存1小时
      },

      formatDateCache: new Map(),
      // 日期格式化缓存
      lastRequestTime: 0,
      // 防止重复请求

      // 密码配置
      passwordConfig: _config.default.passwordStrength || 'weak'
    };
  },
  computed: {
    isAllSelected: function isAllSelected() {
      return this.userList.length > 0 && this.selectedUsers.length === this.userList.length;
    },
    // 判断当前用户是否有用户名和密码（即是否为手动创建的账号）
    hasUsername: function hasUsername() {
      var user = this.resetPasswordUser;
      if (!user) return false;

      // 优先使用云函数返回的判断字段
      if (user.hasPassword !== undefined && user.isThirdPartyUser !== undefined) {
        // 如果是第三方登录用户或者没有密码，则需要设置账号密码
        return user.hasPassword && !user.isThirdPartyUser;
      }

      // 后备判断逻辑：检查原始用户名
      var originalUsername = user.originalUsername || user.username;
      if (!originalUsername || originalUsername.trim() === '') {
        return false;
      }

      // 如果用户名看起来像是自动生成的（包含用户ID的前8位），则认为没有用户名
      if (user._id && originalUsername.includes(user._id.substring(0, 8))) {
        return false;
      }
      return true;
    },
    // 密码弹窗标题
    getPasswordPopupTitle: function getPasswordPopupTitle() {
      return this.hasUsername ? '重置密码' : '设置账号密码';
    },
    // 获取密码强度要求说明
    getPasswordRequirement: function getPasswordRequirement() {
      var requirements = {
        super: '密码必须包含大小写字母、数字和特殊符号，长度8-16位',
        strong: '密码必须包含字母、数字和特殊符号，长度8-16位',
        medium: '密码必须为字母、数字和特殊符号任意两种的组合，长度8-16位',
        weak: '密码必须包含字母和数字，长度6-16位'
      };
      return requirements[this.passwordConfig] || requirements.weak;
    },
    // 缓存的格式化日期方法
    formatDateMemoized: function formatDateMemoized() {
      var _this = this;
      return function (timestamp) {
        if (!timestamp) return '未知';

        // 检查缓存
        var cacheKey = "".concat(timestamp);
        if (_this.formatDateCache.has(cacheKey)) {
          return _this.formatDateCache.get(cacheKey);
        }

        // 格式化并缓存
        var formatted = _this.formatDate(timestamp);
        _this.formatDateCache.set(cacheKey, formatted);

        // 限制缓存大小
        if (_this.formatDateCache.size > 100) {
          var firstKey = _this.formatDateCache.keys().next().value;
          _this.formatDateCache.delete(firstKey);
        }
        return formatted;
      };
    }
  },
  onLoad: function onLoad() {
    this.initPage();
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.loadUserList();
    setTimeout(function () {
      uni.stopPullDownRefresh();
    }, 1000);
  },
  onUnload: function onUnload() {
    // 页面卸载时清理缓存，释放内存
    this.formatDateCache.clear();
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  },
  methods: {
    // 缓存管理方法
    setCache: function setCache(key, data) {
      var expiry = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
      try {
        var cacheData = {
          data: data,
          timestamp: Date.now(),
          expiry: expiry || this.cacheExpiry[key] || 30 * 60 * 1000 // 默认30分钟
        };

        uni.setStorageSync("user_mgmt_".concat(key), JSON.stringify(cacheData));
      } catch (error) {
        console.error('设置缓存失败:', error);
      }
    },
    getCache: function getCache(key) {
      try {
        var cached = uni.getStorageSync("user_mgmt_".concat(key));
        if (!cached) return null;
        var cacheData = JSON.parse(cached);
        var now = Date.now();

        // 检查是否过期
        if (now - cacheData.timestamp > cacheData.expiry) {
          this.clearCache(key);
          return null;
        }
        return cacheData.data;
      } catch (error) {
        console.error('获取缓存失败:', error);
        return null;
      }
    },
    clearCache: function clearCache(key) {
      try {
        uni.removeStorageSync("user_mgmt_".concat(key));
      } catch (error) {
        console.error('清除缓存失败:', error);
      }
    },
    clearAllCache: function clearAllCache() {
      var _this2 = this;
      ['roles', 'apps'].forEach(function (key) {
        return _this2.clearCache(key);
      });
    },
    // 防抖处理
    debounce: function debounce(func, delay) {
      var timer = null;
      return function () {
        var _this3 = this;
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        if (timer) clearTimeout(timer);
        timer = setTimeout(function () {
          func.apply(_this3, args);
        }, delay);
      };
    },
    // 节流处理
    throttle: function throttle(func, delay) {
      var canRun = true;
      return function () {
        var _this4 = this;
        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
          args[_key2] = arguments[_key2];
        }
        if (!canRun) return;
        canRun = false;
        setTimeout(function () {
          func.apply(_this4, args);
          canRun = true;
        }, delay);
      };
    },
    // 优化后的初始化页面 - 并行加载
    initPage: function initPage() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var loadPromises;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (!_this5.isInitializing) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return");
              case 2:
                // 防止重复初始化

                _this5.isInitializing = true;
                _this5.loading = true;
                _context.prev = 4;
                // 并行加载所有必要数据
                loadPromises = [_this5.loadRoleListWithCache(), _this5.loadAppListWithCache()]; // 等待角色和应用数据加载完成后再加载用户列表
                _context.next = 8;
                return Promise.allSettled(loadPromises);
              case 8:
                _context.next = 10;
                return _this5.loadUserList();
              case 10:
                _context.next = 16;
                break;
              case 12:
                _context.prev = 12;
                _context.t0 = _context["catch"](4);
                console.error('页面初始化失败:', _context.t0);
                uni.showToast({
                  title: '初始化失败，请重试',
                  icon: 'none'
                });
              case 16:
                _context.prev = 16;
                _this5.loading = false;
                _this5.hasLoaded = true;
                _this5.isInitializing = false;
                return _context.finish(16);
              case 21:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[4, 12, 16, 21]]);
      }))();
    },
    // 带缓存的角色列表加载
    loadRoleListWithCache: function loadRoleListWithCache() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var cachedRoles, result;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                // 先尝试从缓存获取
                cachedRoles = _this6.getCache('roles');
                if (!cachedRoles) {
                  _context2.next = 6;
                  break;
                }
                _this6.roleList = cachedRoles.roleList;
                _this6.roleOptions = cachedRoles.roleOptions;
                return _context2.abrupt("return");
              case 6:
                _context2.next = 8;
                return uniCloud.callFunction({
                  name: 'user-management',
                  data: {
                    action: 'getRoleList'
                  }
                });
              case 8:
                result = _context2.sent;
                if (!(result.result.code === 0)) {
                  _context2.next = 15;
                  break;
                }
                _this6.roleList = result.result.data.map(function (role) {
                  return {
                    value: role.value,
                    text: role.text
                  };
                });
                _this6.roleOptions = [{
                  value: '',
                  text: '全部角色'
                }].concat((0, _toConsumableArray2.default)(_this6.roleList));

                // 缓存结果
                _this6.setCache('roles', {
                  roleList: _this6.roleList,
                  roleOptions: _this6.roleOptions
                });
                _context2.next = 16;
                break;
              case 15:
                throw new Error(result.result.message || '加载角色失败');
              case 16:
                _context2.next = 23;
                break;
              case 18:
                _context2.prev = 18;
                _context2.t0 = _context2["catch"](0);
                console.error('加载角色列表失败:', _context2.t0);
                // 使用默认角色数据作为后备
                _this6.roleList = [{
                  value: 'user',
                  text: '普通用户'
                }];
                _this6.roleOptions = [{
                  value: '',
                  text: '全部角色'
                }].concat((0, _toConsumableArray2.default)(_this6.roleList));
              case 23:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 18]]);
      }))();
    },
    // 带缓存的应用列表加载
    loadAppListWithCache: function loadAppListWithCache() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var cachedApps, db, result, initResult, retryResult;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                // 先尝试从缓存获取
                cachedApps = _this7.getCache('apps');
                if (!cachedApps) {
                  _context3.next = 5;
                  break;
                }
                _this7.appList = cachedApps;
                return _context3.abrupt("return");
              case 5:
                // 缓存失效，从数据库获取
                db = uniCloud.database();
                _context3.next = 8;
                return db.collection('opendb-app-list').get();
              case 8:
                result = _context3.sent;
                if (!(result.result && result.result.data && result.result.data.length > 0)) {
                  _context3.next = 13;
                  break;
                }
                _this7.appList = result.result.data.map(function (app) {
                  return {
                    value: app.appid,
                    text: app.name
                  };
                });
                _context3.next = 28;
                break;
              case 13:
                _context3.next = 15;
                return uniCloud.callFunction({
                  name: 'user-management',
                  data: {
                    action: 'initApps'
                  }
                });
              case 15:
                initResult = _context3.sent;
                if (!(initResult.result.code === 0)) {
                  _context3.next = 27;
                  break;
                }
                _context3.next = 19;
                return db.collection('opendb-app-list').get();
              case 19:
                retryResult = _context3.sent;
                if (!(retryResult.result && retryResult.result.data && retryResult.result.data.length > 0)) {
                  _context3.next = 24;
                  break;
                }
                _this7.appList = retryResult.result.data.map(function (app) {
                  return {
                    value: app.appid,
                    text: app.name
                  };
                });
                _context3.next = 25;
                break;
              case 24:
                throw new Error('初始化应用数据失败');
              case 25:
                _context3.next = 28;
                break;
              case 27:
                throw new Error('初始化应用数据失败');
              case 28:
                // 缓存结果
                _this7.setCache('apps', _this7.appList);
                _context3.next = 35;
                break;
              case 31:
                _context3.prev = 31;
                _context3.t0 = _context3["catch"](0);
                console.error('加载应用列表失败:', _context3.t0);
                // 使用默认应用数据作为后备
                _this7.appList = [{
                  value: '__UNI__ZZPS',
                  text: '株水小智'
                }];
              case 35:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 31]]);
      }))();
    },
    // 保留原有方法，但添加请求去重
    loadRoleList: function loadRoleList() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                return _context4.abrupt("return", _this8.loadRoleListWithCache());
              case 1:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    // 保留原有方法，但添加请求去重
    loadAppList: function loadAppList() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                return _context5.abrupt("return", _this9.loadAppListWithCache());
              case 1:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    // 优化后的用户列表加载
    loadUserList: function loadUserList() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var now, result;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                // 防止重复请求
                now = Date.now();
                if (!(now - _this10.lastRequestTime < 500)) {
                  _context6.next = 3;
                  break;
                }
                return _context6.abrupt("return");
              case 3:
                _this10.lastRequestTime = now;

                // 如果不是初始化调用，则设置loading状态
                if (_this10.hasLoaded || _this10.loading === false) {
                  _this10.loading = true;
                }
                _context6.prev = 5;
                _context6.next = 8;
                return uniCloud.callFunction({
                  name: 'user-management',
                  data: {
                    action: 'getUserList',
                    params: {
                      page: _this10.currentPage,
                      pageSize: _this10.pageSize,
                      keyword: _this10.searchKeyword,
                      role: _this10.filterRole,
                      status: _this10.filterStatus,
                      anonymous: _this10.filterAnonymous,
                      sortField: _this10.sortField
                    }
                  }
                });
              case 8:
                result = _context6.sent;
                if (result.result.code === 0) {
                  _this10.userList = result.result.data.list;
                  _this10.total = result.result.data.total;
                } else {
                  uni.showToast({
                    title: result.result.message || '加载失败',
                    icon: 'none'
                  });
                }
                _context6.next = 16;
                break;
              case 12:
                _context6.prev = 12;
                _context6.t0 = _context6["catch"](5);
                console.error('加载用户列表失败:', _context6.t0);
                uni.showToast({
                  title: '网络错误',
                  icon: 'none'
                });
              case 16:
                _context6.prev = 16;
                _this10.loading = false;
                _this10.hasLoaded = true;
                _this10.selectedUsers = [];
                return _context6.finish(16);
              case 21:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[5, 12, 16, 21]]);
      }))();
    },
    // 优化搜索处理 - 使用防抖
    handleSearch: function () {
      var timer = null;
      return function () {
        var _this11 = this;
        if (timer) clearTimeout(timer);
        timer = setTimeout(function () {
          _this11.currentPage = 1;
          _this11.selectedUsers = [];
          _this11.loadUserList();
        }, 300);
      };
    }(),
    // 筛选处理
    handleFilter: function handleFilter() {
      // 重置滑动状态
      this.resetAllSwipeStates();
      this.currentPage = 1;
      this.selectedUsers = []; // 筛选时清空选择
      this.loadUserList();
    },
    // 排序变更处理
    handleSortChange: function handleSortChange(value) {
      // 确保sortField已经更新
      this.sortField = value;
      this.currentPage = 1;
      this.loadUserList();
    },
    // 分页处理
    handlePageChange: function handlePageChange(page) {
      this.currentPage = page.current;
      this.loadUserList();
    },
    // 切换全选状态
    toggleSelectAll: function toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedUsers = [];
      } else {
        this.selectedUsers = this.userList.map(function (user) {
          return user._id;
        });
      }
    },
    // 切换单个用户选择状态
    toggleUserSelect: function toggleUserSelect(userId) {
      var index = this.selectedUsers.indexOf(userId);
      if (index > -1) {
        this.selectedUsers.splice(index, 1);
      } else {
        this.selectedUsers.push(userId);
      }
    },
    // 用户点击处理
    handleUserClick: function handleUserClick(user) {
      // 如果刚刚进行了滑动操作，不触发点击事件
      if (this.currentSwipeUserId || this.isDragging) {
        return;
      }
      // 可以显示用户详情或进行其他操作
    },
    // 显示创建用户弹窗
    showCreateUser: function showCreateUser() {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _this12.isEdit = false;

                // 确保应用列表已加载
                if (!(!_this12.appList || _this12.appList.length === 0)) {
                  _context7.next = 12;
                  break;
                }
                _context7.prev = 2;
                _context7.next = 5;
                return _this12.loadAppList();
              case 5:
                _context7.next = 12;
                break;
              case 7:
                _context7.prev = 7;
                _context7.t0 = _context7["catch"](2);
                console.error('加载应用列表失败:', _context7.t0);
                uni.showToast({
                  title: '加载应用列表失败',
                  icon: 'none'
                });
                return _context7.abrupt("return");
              case 12:
                _this12.resetUserForm();
                _this12.$refs.userFormPopup.open();
              case 14:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[2, 7]]);
      }))();
    },
    // 编辑用户
    editUser: function editUser(user) {
      var _this13 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                _this13.isEdit = true;

                // 确保应用列表已加载
                if (!(!_this13.appList || _this13.appList.length === 0)) {
                  _context8.next = 12;
                  break;
                }
                _context8.prev = 2;
                _context8.next = 5;
                return _this13.loadAppList();
              case 5:
                _context8.next = 12;
                break;
              case 7:
                _context8.prev = 7;
                _context8.t0 = _context8["catch"](2);
                console.error('加载应用列表失败:', _context8.t0);
                uni.showToast({
                  title: '加载应用列表失败',
                  icon: 'none'
                });
                return _context8.abrupt("return");
              case 12:
                _this13.userForm = {
                  _id: user._id,
                  username: user.username,
                  password: '',
                  nickname: user.nickname || '',
                  role: user.role || [],
                  dcloud_appid: Array.isArray(user.dcloud_appid) ? user.dcloud_appid : user.dcloud_appid ? [user.dcloud_appid] : [],
                  status: user.status
                };
                _this13.$refs.userFormPopup.open();
              case 14:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[2, 7]]);
      }))();
    },
    // 重置表单
    resetUserForm: function resetUserForm() {
      this.userForm = {
        username: '',
        password: '',
        nickname: '',
        role: [],
        dcloud_appid: [],
        // 可登录应用
        status: 0
      };
    },
    // 关闭用户表单
    closeUserForm: function closeUserForm() {
      this.$refs.userFormPopup.close();
    },
    // 处理状态开关变化
    handleStatusChange: function handleStatusChange(e) {
      this.userForm.status = e.detail.value ? 0 : 1;
    },
    // 保存用户
    saveUser: function saveUser() {
      var _this14 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var passwordValidation, action, params, result;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                if (!(!_this14.isEdit && !_this14.userForm.username.trim())) {
                  _context9.next = 3;
                  break;
                }
                uni.showToast({
                  title: '请输入用户名',
                  icon: 'none'
                });
                return _context9.abrupt("return");
              case 3:
                if (!(!_this14.isEdit && !_this14.userForm.password.trim())) {
                  _context9.next = 6;
                  break;
                }
                uni.showToast({
                  title: '请输入密码',
                  icon: 'none'
                });
                return _context9.abrupt("return");
              case 6:
                if (!(!_this14.isEdit && _this14.userForm.password.trim())) {
                  _context9.next = 11;
                  break;
                }
                passwordValidation = _password.default.validPwd(_this14.userForm.password);
                if (!(passwordValidation !== true)) {
                  _context9.next = 11;
                  break;
                }
                uni.showToast({
                  title: passwordValidation,
                  icon: 'none',
                  duration: 3000
                });
                return _context9.abrupt("return");
              case 11:
                if (!(!_this14.userForm.role || _this14.userForm.role.length === 0)) {
                  _context9.next = 14;
                  break;
                }
                uni.showToast({
                  title: '请选择角色',
                  icon: 'none'
                });
                return _context9.abrupt("return");
              case 14:
                if (!(!_this14.userForm.dcloud_appid || _this14.userForm.dcloud_appid.length === 0)) {
                  _context9.next = 17;
                  break;
                }
                uni.showToast({
                  title: '请选择可登录应用',
                  icon: 'none'
                });
                return _context9.abrupt("return");
              case 17:
                _this14.saving = true;
                _context9.prev = 18;
                action = _this14.isEdit ? 'updateUser' : 'createUser';
                params = _objectSpread({}, _this14.userForm);
                if (_this14.isEdit) {
                  params.userId = _this14.userForm._id;
                  delete params._id;
                  delete params.password; // 编辑时不更新密码
                }
                _context9.next = 24;
                return uniCloud.callFunction({
                  name: 'user-management',
                  data: {
                    action: action,
                    params: params
                  }
                });
              case 24:
                result = _context9.sent;
                if (result.result.code === 0) {
                  uni.showToast({
                    title: _this14.isEdit ? '更新成功' : '创建成功',
                    icon: 'success'
                  });
                  _this14.closeUserForm();
                  // 只有创建成功后才重置表单
                  if (!_this14.isEdit) {
                    _this14.resetUserForm();
                  }

                  // 如果涉及角色变更，清除角色缓存
                  if (_this14.userForm.role && _this14.userForm.role.length > 0) {
                    _this14.clearCache('roles');
                  }
                  _this14.loadUserList();
                } else {
                  uni.showToast({
                    title: result.result.message || '操作失败',
                    icon: 'none'
                  });
                }
                _context9.next = 31;
                break;
              case 28:
                _context9.prev = 28;
                _context9.t0 = _context9["catch"](18);
                uni.showToast({
                  title: '网络错误',
                  icon: 'none'
                });
              case 31:
                _context9.prev = 31;
                _this14.saving = false;
                return _context9.finish(31);
              case 34:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[18, 28, 31, 34]]);
      }))();
    },
    // 重置密码/设置账号密码
    resetPassword: function resetPassword(user) {
      this.resetPasswordUser = user;
      this.newUsername = '';
      this.newPassword = '';
      this.confirmPassword = '';
      this.$refs.resetPasswordPopup.open();
    },
    // 关闭重置密码弹窗
    closeResetPassword: function closeResetPassword() {
      // 只清空输入的密码和用户名，不清空resetPasswordUser
      this.newUsername = '';
      this.newPassword = '';
      this.confirmPassword = '';
      this.$refs.resetPasswordPopup.close();
    },
    // 确认重置密码/设置账号密码
    confirmResetPassword: function confirmResetPassword() {
      var _this15 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var usernameRegex, passwordValidation, params, result;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                if (_this15.hasUsername) {
                  _context10.next = 8;
                  break;
                }
                if (_this15.newUsername.trim()) {
                  _context10.next = 4;
                  break;
                }
                uni.showToast({
                  title: '请输入用户账号',
                  icon: 'none'
                });
                return _context10.abrupt("return");
              case 4:
                // 用户名格式验证
                usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
                if (usernameRegex.test(_this15.newUsername.trim())) {
                  _context10.next = 8;
                  break;
                }
                uni.showToast({
                  title: '用户账号只能包含字母、数字、下划线，长度3-20位',
                  icon: 'none'
                });
                return _context10.abrupt("return");
              case 8:
                if (_this15.newPassword.trim()) {
                  _context10.next = 11;
                  break;
                }
                uni.showToast({
                  title: '请输入新密码',
                  icon: 'none'
                });
                return _context10.abrupt("return");
              case 11:
                if (!(_this15.newPassword !== _this15.confirmPassword)) {
                  _context10.next = 14;
                  break;
                }
                uni.showToast({
                  title: '两次密码输入不一致',
                  icon: 'none'
                });
                return _context10.abrupt("return");
              case 14:
                // 使用配置文件中的密码强度验证
                passwordValidation = _password.default.validPwd(_this15.newPassword);
                if (!(passwordValidation !== true)) {
                  _context10.next = 18;
                  break;
                }
                uni.showToast({
                  title: passwordValidation,
                  icon: 'none',
                  duration: 3000
                });
                return _context10.abrupt("return");
              case 18:
                _context10.prev = 18;
                // 构建请求参数
                params = {
                  userId: _this15.resetPasswordUser._id,
                  newPassword: _this15.newPassword
                }; // 如果用户没有用户名，添加用户名参数
                if (!_this15.hasUsername) {
                  params.username = _this15.newUsername.trim();
                }
                _context10.next = 23;
                return uniCloud.callFunction({
                  name: 'user-management',
                  data: {
                    action: 'setAccountPassword',
                    params: params
                  }
                });
              case 23:
                result = _context10.sent;
                if (result.result.code === 0) {
                  uni.showToast({
                    title: result.result.message,
                    icon: 'success'
                  });
                  // 操作成功后清空用户信息
                  _this15.resetPasswordUser = {};
                  _this15.closeResetPassword();
                  // 重新加载用户列表以显示更新后的信息
                  _this15.loadUserList();
                } else {
                  uni.showToast({
                    title: result.result.message || '操作失败',
                    icon: 'none'
                  });
                }
                _context10.next = 30;
                break;
              case 27:
                _context10.prev = 27;
                _context10.t0 = _context10["catch"](18);
                uni.showToast({
                  title: '网络错误',
                  icon: 'none'
                });
              case 30:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10, null, [[18, 27]]);
      }))();
    },
    // 删除用户
    deleteUser: function deleteUser(user) {
      var _this16 = this;
      if (user.role.includes('admin')) {
        uni.showToast({
          title: '不能删除管理员用户',
          icon: 'none'
        });
        return;
      }
      var displayName = user.nickname || user.username || '该用户';
      uni.showModal({
        title: '删除用户',
        content: '确定要删除用户"' + displayName + '"吗？此操作不可恢复',
        success: function () {
          var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11(res) {
            var result;
            return _regenerator.default.wrap(function _callee11$(_context11) {
              while (1) {
                switch (_context11.prev = _context11.next) {
                  case 0:
                    if (!res.confirm) {
                      _context11.next = 11;
                      break;
                    }
                    _context11.prev = 1;
                    _context11.next = 4;
                    return uniCloud.callFunction({
                      name: 'user-management',
                      data: {
                        action: 'deleteUser',
                        params: {
                          userId: user._id
                        }
                      }
                    });
                  case 4:
                    result = _context11.sent;
                    if (result.result.code === 0) {
                      uni.showToast({
                        title: '删除成功',
                        icon: 'success'
                      });
                      _this16.loadUserList();
                    } else {
                      uni.showToast({
                        title: result.result.message || '删除失败',
                        icon: 'none'
                      });
                    }
                    _context11.next = 11;
                    break;
                  case 8:
                    _context11.prev = 8;
                    _context11.t0 = _context11["catch"](1);
                    uni.showToast({
                      title: '网络错误',
                      icon: 'none'
                    });
                  case 11:
                  case "end":
                    return _context11.stop();
                }
              }
            }, _callee11, null, [[1, 8]]);
          }));
          function success(_x) {
            return _success.apply(this, arguments);
          }
          return success;
        }()
      });
    },
    // 显示批量操作菜单
    showBatchMenu: function showBatchMenu() {
      if (this.selectedUsers.length === 0) {
        uni.showToast({
          title: '请先选择用户',
          icon: 'none'
        });
        return;
      }
      this.$refs.batchMenuPopup.open();
    },
    // 关闭批量操作菜单
    closeBatchMenu: function closeBatchMenu() {
      this.$refs.batchMenuPopup.close();
    },
    // 批量更新状态
    batchUpdateStatus: function batchUpdateStatus(status) {
      var _this17 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee13() {
        var statusText;
        return _regenerator.default.wrap(function _callee13$(_context13) {
          while (1) {
            switch (_context13.prev = _context13.next) {
              case 0:
                _this17.closeBatchMenu();
                statusText = status === 0 ? '启用' : '禁用';
                uni.showModal({
                  title: "\u6279\u91CF".concat(statusText),
                  content: "\u786E\u5B9A\u8981".concat(statusText, "\u6240\u9009\u7684").concat(_this17.selectedUsers.length, "\u4E2A\u7528\u6237\u5417\uFF1F"),
                  success: function () {
                    var _success2 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12(res) {
                      return _regenerator.default.wrap(function _callee12$(_context12) {
                        while (1) {
                          switch (_context12.prev = _context12.next) {
                            case 0:
                              if (!res.confirm) {
                                _context12.next = 3;
                                break;
                              }
                              _context12.next = 3;
                              return _this17.performBatchOperation('updateStatus', {
                                status: status
                              });
                            case 3:
                            case "end":
                              return _context12.stop();
                          }
                        }
                      }, _callee12);
                    }));
                    function success(_x2) {
                      return _success2.apply(this, arguments);
                    }
                    return success;
                  }()
                });
              case 3:
              case "end":
                return _context13.stop();
            }
          }
        }, _callee13);
      }))();
    },
    // 显示批量角色更新
    showBatchRoleUpdate: function showBatchRoleUpdate() {
      this.closeBatchMenu();
      this.batchRoleForm = [];
      this.$refs.batchRolePopup.open();
    },
    // 关闭批量角色更新
    closeBatchRoleUpdate: function closeBatchRoleUpdate() {
      this.$refs.batchRolePopup.close();
    },
    // 确认批量角色更新
    confirmBatchRoleUpdate: function confirmBatchRoleUpdate() {
      var _this18 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee14() {
        return _regenerator.default.wrap(function _callee14$(_context14) {
          while (1) {
            switch (_context14.prev = _context14.next) {
              case 0:
                if (!(_this18.batchRoleForm.length === 0)) {
                  _context14.next = 3;
                  break;
                }
                uni.showToast({
                  title: '请选择角色',
                  icon: 'none'
                });
                return _context14.abrupt("return");
              case 3:
                _this18.closeBatchRoleUpdate();
                // 操作成功后会在performBatchOperation中重置数据
                _context14.next = 6;
                return _this18.performBatchOperation('updateRole', {
                  role: _this18.batchRoleForm
                });
              case 6:
              case "end":
                return _context14.stop();
            }
          }
        }, _callee14);
      }))();
    },
    // 批量删除
    batchDelete: function batchDelete() {
      var _this19 = this;
      this.closeBatchMenu();
      uni.showModal({
        title: '批量删除',
        content: "\u786E\u5B9A\u8981\u5220\u9664\u6240\u9009\u7684".concat(this.selectedUsers.length, "\u4E2A\u7528\u6237\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u6062\u590D"),
        success: function () {
          var _success3 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee15(res) {
            return _regenerator.default.wrap(function _callee15$(_context15) {
              while (1) {
                switch (_context15.prev = _context15.next) {
                  case 0:
                    if (!res.confirm) {
                      _context15.next = 3;
                      break;
                    }
                    _context15.next = 3;
                    return _this19.performBatchOperation('delete');
                  case 3:
                  case "end":
                    return _context15.stop();
                }
              }
            }, _callee15);
          }));
          function success(_x3) {
            return _success3.apply(this, arguments);
          }
          return success;
        }()
      });
    },
    // 执行批量操作
    performBatchOperation: function performBatchOperation(operation) {
      var _arguments = arguments,
        _this20 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee16() {
        var data, result;
        return _regenerator.default.wrap(function _callee16$(_context16) {
          while (1) {
            switch (_context16.prev = _context16.next) {
              case 0:
                data = _arguments.length > 1 && _arguments[1] !== undefined ? _arguments[1] : {};
                uni.showLoading({
                  title: '处理中...'
                });
                _context16.prev = 2;
                _context16.next = 5;
                return uniCloud.callFunction({
                  name: 'user-management',
                  data: {
                    action: 'batchOperation',
                    params: {
                      userIds: _this20.selectedUsers,
                      operation: operation,
                      data: data
                    }
                  }
                });
              case 5:
                result = _context16.sent;
                uni.hideLoading();
                if (result.result.code === 0) {
                  uni.showToast({
                    title: result.result.message,
                    icon: 'success'
                  });

                  // 操作成功后重置相关数据
                  if (operation === 'updateRole') {
                    _this20.batchRoleForm = [];
                    // 批量角色操作后清除角色缓存
                    _this20.clearCache('roles');
                  }
                  _this20.loadUserList();

                  // 如果有错误信息，显示详情
                  if (result.result.data.errors.length > 0) {
                    setTimeout(function () {
                      uni.showModal({
                        title: '操作详情',
                        content: result.result.data.errors.join('\n'),
                        showCancel: false
                      });
                    }, 1500);
                  }
                } else {
                  uni.showToast({
                    title: result.result.message || '操作失败',
                    icon: 'none'
                  });
                }
                _context16.next = 15;
                break;
              case 10:
                _context16.prev = 10;
                _context16.t0 = _context16["catch"](2);
                console.error('批量操作失败:', _context16.t0);
                uni.hideLoading();
                uni.showToast({
                  title: '网络错误',
                  icon: 'none'
                });
              case 15:
              case "end":
                return _context16.stop();
            }
          }
        }, _callee16, null, [[2, 10]]);
      }))();
    },
    // 显示角色变更
    showRoleChange: function showRoleChange(user) {
      this.quickRoleUser = user;
      this.quickRoleForm = (0, _toConsumableArray2.default)(user.role || []);
      this.$refs.quickRolePopup.open();
    },
    // 关闭快速角色变更
    closeQuickRole: function closeQuickRole() {
      this.$refs.quickRolePopup.close();
    },
    // 确认快速角色变更
    confirmQuickRole: function confirmQuickRole() {
      var _this21 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee17() {
        var result;
        return _regenerator.default.wrap(function _callee17$(_context17) {
          while (1) {
            switch (_context17.prev = _context17.next) {
              case 0:
                if (!(_this21.quickRoleForm.length === 0)) {
                  _context17.next = 3;
                  break;
                }
                uni.showToast({
                  title: '请选择角色',
                  icon: 'none'
                });
                return _context17.abrupt("return");
              case 3:
                _context17.prev = 3;
                _context17.next = 6;
                return uniCloud.callFunction({
                  name: 'user-management',
                  data: {
                    action: 'updateUser',
                    params: {
                      userId: _this21.quickRoleUser._id,
                      role: _this21.quickRoleForm
                    }
                  }
                });
              case 6:
                result = _context17.sent;
                if (result.result.code === 0) {
                  uni.showToast({
                    title: '角色更新成功',
                    icon: 'success'
                  });
                  _this21.closeQuickRole();
                  // 操作成功后重置数据
                  _this21.quickRoleUser = {};
                  _this21.quickRoleForm = [];
                  // 角色更新后清除角色缓存
                  _this21.clearCache('roles');
                  _this21.loadUserList();
                } else {
                  uni.showToast({
                    title: result.result.message || '更新失败',
                    icon: 'none'
                  });
                }
                _context17.next = 13;
                break;
              case 10:
                _context17.prev = 10;
                _context17.t0 = _context17["catch"](3);
                uni.showToast({
                  title: '网络错误',
                  icon: 'none'
                });
              case 13:
              case "end":
                return _context17.stop();
            }
          }
        }, _callee17, null, [[3, 10]]);
      }))();
    },
    // 重置所有筛选条件
    resetFilters: function resetFilters() {
      // 重置滑动状态
      this.resetAllSwipeStates();
      this.searchKeyword = '';
      this.filterRole = '';
      this.filterStatus = '';
      this.filterAnonymous = '';
      this.sortField = '';
      this.currentPage = 1;
      this.selectedUsers = [];

      // 清除所有缓存，强制重新获取最新数据
      this.clearAllCache();

      // 重新加载数据
      this.loadUserList();
      uni.showToast({
        title: '已重置筛选条件',
        icon: 'success',
        duration: 1500
      });
    },
    // 格式化日期 - 统一24小时制格式
    formatDate: function formatDate(timestamp) {
      if (!timestamp) return '未知';

      // 处理时间戳，确保是数字类型
      var time = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
      if (isNaN(time)) return '未知';
      var date = new Date(time);

      // 检查日期是否有效
      if (isNaN(date.getTime())) return '未知';

      // 统一格式：YYYY-MM-DD HH:mm:ss (24小时制)
      var year = date.getFullYear();
      var month = String(date.getMonth() + 1).padStart(2, '0');
      var day = String(date.getDate()).padStart(2, '0');
      var hours = String(date.getHours()).padStart(2, '0');
      var minutes = String(date.getMinutes()).padStart(2, '0');
      var seconds = String(date.getSeconds()).padStart(2, '0');
      return "".concat(year, "-").concat(month, "-").concat(day, " ").concat(hours, ":").concat(minutes, ":").concat(seconds);
    },
    // 滑动操作相关方法
    handleTouchStart: function handleTouchStart(e, userId) {
      if (!e.touches || !e.touches[0]) return;
      this.touchStartX = e.touches[0].pageX;
      this.touchStartY = e.touches[0].pageY;
      this.currentSwipeUserId = userId;

      // 为当前用户初始化滑动状态
      if (!this.swipeStates[userId]) {
        this.$set(this.swipeStates, userId, {
          translateX: 0,
          transitioning: false
        });
      }

      // 记录开始滑动时的初始位置
      this.startTranslateX = this.swipeStates[userId].translateX;
    },
    handleTouchMove: function handleTouchMove(e, userId) {
      if (this.currentSwipeUserId !== userId || !e.touches || !e.touches[0]) return;
      var touchX = e.touches[0].pageX;
      var touchY = e.touches[0].pageY;
      this.handleSwipeMove(touchX, touchY, userId, e);
    },
    handleTouchEnd: function handleTouchEnd(e, userId) {
      if (this.currentSwipeUserId !== userId) return;
      this.handleSwipeEnd(userId);
    },
    // H5鼠标事件处理
    handleMouseDown: function handleMouseDown(e, userId) {
      this.touchStartX = e.pageX;
      this.touchStartY = e.pageY;
      this.currentSwipeUserId = userId;
      this.isDragging = true;
      e.preventDefault(); // 防止文本选择

      // 为当前用户初始化滑动状态
      if (!this.swipeStates[userId]) {
        this.$set(this.swipeStates, userId, {
          translateX: 0,
          transitioning: false
        });
      }

      // 记录开始滑动时的初始位置
      this.startTranslateX = this.swipeStates[userId].translateX;
    },
    handleMouseMove: function handleMouseMove(e, userId) {
      if (!this.isDragging || this.currentSwipeUserId !== userId) return;
      var mouseX = e.pageX;
      var mouseY = e.pageY;
      this.handleSwipeMove(mouseX, mouseY, userId, e);
    },
    handleMouseUp: function handleMouseUp(e, userId) {
      if (!this.isDragging) return;
      // 注意：这里不要检查userId，因为鼠标可能离开了原始区域
      this.isDragging = false;
      if (this.currentSwipeUserId) {
        this.handleSwipeEnd(this.currentSwipeUserId);
      }
    },
    // 通用滑动移动处理
    handleSwipeMove: function handleSwipeMove(currentX, currentY, userId, e) {
      var deltaX = currentX - this.touchStartX;
      var deltaY = currentY - this.touchStartY;

      // 降低水平滑动检测阈值，提高灵敏度
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 5) {
        // 阻止页面滚动
        if (e.preventDefault) {
          e.preventDefault();
        }

        // 基于开始滑动时的位置计算新位置
        var newTranslateX = this.startTranslateX + deltaX;

        // 限制滑动范围 - 根据平台动态调整
        var maxLeftSwipe;
        maxLeftSwipe = -265; // 其他平台的滑动距离

        var maxRightSwipe = 0; // 最大向右滑动距离
        newTranslateX = Math.max(maxLeftSwipe, Math.min(maxRightSwipe, newTranslateX));
        this.$set(this.swipeStates, userId, {
          translateX: newTranslateX,
          transitioning: false
        });
      }
    },
    // 通用滑动结束处理
    handleSwipeEnd: function handleSwipeEnd(userId) {
      var _this22 = this;
      var state = this.swipeStates[userId];
      if (!state) {
        this.currentSwipeUserId = null;
        return;
      }

      // 降低滑动阈值，提高灵敏度
      var threshold = 50; // 滑动阈值（使用正数，更清晰）
      var finalTranslateX;

      // 计算相对于开始位置的移动距离
      var deltaFromStart = state.translateX - this.startTranslateX;
      if (deltaFromStart < -threshold) {
        // 向左滑动超过阈值，显示操作按钮

        finalTranslateX = -265; // 其他平台的最终位置
      } else if (deltaFromStart > threshold) {
        // 向右滑动超过阈值，隐藏操作按钮
        finalTranslateX = 0;
      } else {
        // 滑动距离不足，回到原状态
        finalTranslateX = this.startTranslateX;
      }

      // 关闭其他用户的滑动状态
      Object.keys(this.swipeStates).forEach(function (id) {
        if (id !== userId) {
          _this22.$set(_this22.swipeStates, id, {
            translateX: 0,
            transitioning: true
          });
        }
      });

      // 设置当前用户的最终状态
      this.$set(this.swipeStates, userId, {
        translateX: finalTranslateX,
        transitioning: true
      });
      this.currentSwipeUserId = null;
      this.isDragging = false;
    },
    // 重置所有滑动状态
    resetAllSwipeStates: function resetAllSwipeStates() {
      var _this23 = this;
      Object.keys(this.swipeStates).forEach(function (id) {
        _this23.$set(_this23.swipeStates, id, {
          translateX: 0,
          transitioning: true
        });
      });
    },
    // 实时验证密码强度（可选功能）
    validatePasswordStrength: function validatePasswordStrength(password) {
      if (!password) return '';
      var validation = _password.default.validPwd(password);
      return validation === true ? '' : validation;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"]))

/***/ }),

/***/ 142:
/*!***************************************************************************************************************!*\
  !*** D:/Xwzc/pages/ucenter_pkg/user-management.vue?vue&type=style&index=0&id=0671abb8&lang=scss&scoped=true& ***!
  \***************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_style_index_0_id_0671abb8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-management.vue?vue&type=style&index=0&id=0671abb8&lang=scss&scoped=true& */ 143);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_style_index_0_id_0671abb8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_style_index_0_id_0671abb8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_style_index_0_id_0671abb8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_style_index_0_id_0671abb8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_user_management_vue_vue_type_style_index_0_id_0671abb8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 143:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/ucenter_pkg/user-management.vue?vue&type=style&index=0&id=0671abb8&lang=scss&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[135,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ucenter_pkg/user-management.js.map
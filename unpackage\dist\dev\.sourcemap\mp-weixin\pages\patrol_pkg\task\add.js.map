{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/task/add.vue?2b6d", "webpack:///D:/Xwzc/pages/patrol_pkg/task/add.vue?0722", "webpack:///D:/Xwzc/pages/patrol_pkg/task/add.vue?da67", "webpack:///D:/Xwzc/pages/patrol_pkg/task/add.vue?8ea7", "uni-app:///pages/patrol_pkg/task/add.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/task/add.vue?8ced", "webpack:///D:/Xwzc/pages/patrol_pkg/task/add.vue?fe5b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "submitting", "formData", "name", "area", "date", "shift_id", "route_id", "user_id", "status", "shiftOptions", "_id", "rounds", "shiftIndex", "routeOptions", "routeIndex", "userOptions", "nickname", "userIndex", "<PERSON><PERSON><PERSON><PERSON>", "selectedShift", "selected<PERSON>ser", "roleNameMap", "STATUS", "NOT_STARTED", "IN_PROGRESS", "COMPLETED", "EXPIRED", "CANCELLED", "predictedStatus", "timeZoneIndicator", "processedRounds", "pointsRefreshed", "searchUserName", "filteredUsers", "showUserSelect", "onLoad", "uni", "title", "Promise", "console", "icon", "watch", "handler", "deep", "immediate", "methods", "loadShifts", "PatrolApi", "action", "with_rounds", "with_detail", "res", "defaultOption", "shifts", "shift", "across_day", "round", "time", "start_time", "end_time", "day_offset", "duration", "systemInfo", "platform", "isMobile", "loadRoutes", "getCurrentUserId", "loadUsers", "currentUserId", "userid", "params", "pageSize", "result", "avatar", "role", "userList", "filter", "map", "loadRoles", "roleMap", "onDateChange", "onShiftChange", "shiftData", "enabledRounds", "onRouteChange", "route", "with_points", "include_point_details", "onUserChange", "onStatusChange", "getRoleText", "getSingleRoleText", "formatDate", "validateForm", "processRoundsData", "taskDate", "points", "now", "roundHours", "actualDayOffset", "checkDateTime", "roundStatus", "point_id", "order", "location", "range", "check_time", "stats", "total_points", "completed_points", "missed_points", "completion_rate", "abnormal_count", "calculateOverallStats", "last_checkin_time", "totalPoints", "completedPoints", "abnormalCount", "submitForm", "overallStats", "shiftName", "routeName", "startTime", "dayOffset", "endDateTime", "endTime", "calculatedStatus", "cleanDate", "requestData", "patrol_date", "shift_name", "route_name", "enabled_rounds", "rounds_detail", "p", "qrcode_enabled", "qrcode_required", "qrcode_version", "overall_stats", "auto_closed", "shift_detail", "route_detail", "point_count", "total_distance", "response", "taskData", "hasRequiredFields", "setTimeout", "updatePredictedStatus", "calculateTaskStatus", "getStatusIcon", "getStatusText", "getStatusExplanation", "navigateBack", "refreshRoutePointsData", "force_refresh", "routeData", "pointsDetail", "filterUserOptions", "user", "selectUser", "hideUserSelect"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACc;;;AAGhE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnFA;AAAA;AAAA;AAAA;AAA6lB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACuQjnB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;;MACAC;QAAAC;QAAAR;QAAAS;MAAA;MACAC;MACAC;QAAAH;QAAAR;MAAA;MACAY;MACAC;QAAAL;QAAAM;MAAA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;;cAEA;cACA;;cAEA;cAAA;cAEAC;gBAAAC;cAAA;;cAEA;cAAA;cAAA,OACAC,aACA,oBACA,oBACA,mBACA,kBACA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;cACAH;gBACAC;gBACAG;cACA;YAAA;cAAA;cAEAJ;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAK;IACA;IACA;MACA;IACA;IACAtB;MACAuB;QACA;MACA;MACAC;IACA;IACA;IACA5B;MACA2B;QACA;MACA;MACAE;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBACA7C;kBACA8C;kBACAjD;oBACAS;oBACAyC;oBAAA;oBACAC;kBACA;gBACA;cAAA;gBARAC;gBAUA;kBACA;kBACAC;oBAAA1C;oBAAAR;oBAAAS;kBAAA;kBACA0C;oBAAA,uCACAC;sBACApD;sBACAqD;sBACA5C,sDACA2C;wBAAA;sBAAA;wBAAA;0BACAE;0BACAtD;0BACAuD;0BACAC;0BACAC;0BACAC;0BACAC;0BACArD;wBACA;sBAAA,KACA;oBAAA;kBAAA,CACA,GAEA;kBACAsD;kBACAC;kBACAC,kDAEA;kBACAX;oBACA;oBACA;;oBAEA;oBACA;oBACA;kBACA;;kBAEA;kBACA;gBACA;kBACA;oBAAA3C;oBAAAR;oBAAAS;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA4B;gBACA;kBAAA7B;kBAAAR;kBAAAS;gBAAA;gBACAyB;kBACAC;kBACAG;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAlB;kBACA7C;kBACA8C;kBACAjD;oBAAAS;kBAAA;gBACA;cAAA;gBAJA2C;gBAMA;kBACA;kBACA,uBACA;oBAAAzC;oBAAAR;kBAAA,2CACAiD,eACA;gBACA;kBACA;oBAAAzC;oBAAAR;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;kBAAAQ;kBAAAR;gBAAA;gBACAkC;kBACAC;kBACAG;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0B;MACA;QACA;QACA;QACA;UACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAGArB;kBACA7C;kBACA8C;kBACAjD;oBACAsE;oBACAC;sBACAC;oBACA;kBACA;gBACA;cAAA;gBATAC;gBAWA;kBACA;kBACA,sBACA;oBACA9D;oBACAM;oBACAyD;oBACAC;kBACA,EACA;;kBAEA;kBACAC,4BACAC;oBACA;oBACA;oBACA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;kBACA,GACAA;oBACA;oBACA;oBACA;kBACA,GACAC;oBAAA;sBACAnE;sBACAM;sBACAyD;sBACAC;oBACA;kBAAA,IAEA;kBACA;;kBAEA;kBACA;kBACA;kBACA;kBAEA;oBACAtC;sBACAC;sBACAG;oBACA;kBACA;gBACA;kBACA;oBACA9B;oBACAM;oBACAyD;oBACAC;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAtC;kBACAC;kBACAG;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAV;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAGArB;kBACA7C;kBACA8C;kBACAjD;oBACAsE;kBACA;gBACA;cAAA;gBANAG;gBAQA;kBACA;kBACAO;kBACAP;oBACAO;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA3C;kBACAC;kBACAG;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwC;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA3B;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGAP;kBACA7C;kBACA8C;kBACAjD;oBACAuE;sBACAjE;sBACA4C;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAVAC;gBAYA;kBACA;kBACA+B;kBACA;oBACAC;sBAAA;oBAAA;oBACA,uDACAD;sBACA;sBACA3B;sBACA5C;wBAAA;0BACA6C;0BACAtD;0BACAuD;0BACAC;0BACAC;0BACA;0BACAC;0BACAC;0BACArD;wBACA;sBAAA;oBAAA,EACA;;oBAEA;oBACA;sBACA4B;wBACAC;wBACAG;wBACAqB;sBACA;sBAEAtB;wBACArC;wBACAwD;wBACAC;wBACAhD;0BAAA;4BACA6C;4BACAC;4BACAG;0BACA;wBAAA;sBACA;oBACA;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEArB;gBACAH;kBACAC;kBACAG;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAEAtC;kBACA7C;kBACA8C;kBACAjD;oBACAuE;sBACAhE;sBACAgF;sBACApC;sBACAqC;oBACA;kBACA;gBACA;cAAA;gBAXApC;gBAaA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;gBACAH;kBACAC;kBACAG;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgD;MACA;MACA;MAEA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MAAA;MACA;;MAEA;MACA;QACA;QACA;QACA;UAAA;QAAA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACAzD;UAAAC;UAAAG;QAAA;QACA;MACA;MAEA;QACAJ;UAAAC;UAAAG;QAAA;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;MAEA;QACAJ;UAAAC;UAAAG;QAAA;QACA;MACA;MAEA;QACAJ;UAAAC;UAAAG;QAAA;QACA;MACA;MAEA;QACAJ;UAAAC;UAAAG;QAAA;QACA;MACA;MAEA;IACA;IAEA;IACAsD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;cAAA;gBAGA;gBACAC;gBACAzC,8BAEA;gBAAA,wBACAA,uKAEA;gBACA0C,4CAEA;gBACAC;gBAAA,kCAEA;kBACA;kBACA;kBACA;oBAAA;oBAAAC;;kBAEA;kBACA;;kBAEA;kBACA;oBACAC;kBACA;;kBAEA;kBACA;kBACAC;;kBAEA;kBACA;;kBAEA;kBACA;;kBAEA;oBACA;oBACAC;kBACA;oBACA;oBACAA;kBACA;;kBAEA;kBACA;oBAAA;sBACAC;sBACApG;sBACAqG;sBACA/F;sBACAgG;sBACAC;oBACA;kBAAA;kBAEA;oBACAjD;oBACAtD;oBACAuD;oBACAiD;oBACAhD;oBACAC;oBACAC;oBACAC;oBACArD;oBACA+C;oBACAyC;oBACAW;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACAL;UACAC;UACAC;UACAC;UACAC;UACAE;QACA;MACA;;MAEA;MACA;MACA;MACA;MAEAvG;QACA;UACAwG;UACAC;UACAC;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACAT;QACAC;QACAC;QACAC;QACAC;QACAE;MACA;IACA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA3G;gBAEA;gBACA;;gBAEA;gBACA4G,sDAEA;gBACAC;gBACAC,qEAEA;gBAEA;kBACA;kBACA;oBACAC;kBACA;kBAEA;oBACA;oBAAA,wBACA;oBAAA,yBACA;oBAEAC;oBACA;sBACAA;oBACA;oBAEAC;oBACAC;kBACA;gBACA;;gBAEA;gBACAC,2DAEA;gBACAC;gBAEA;gBACAC;kBACA9H;kBACAC;kBACA8H;kBAAA;kBACA5H;kBACA6H;kBACA5H;kBACA6H;kBACA5H;kBACAC;kBAAA;kBACA4H;oBAAA;kBAAA;kBACAC;oBAAA,uCACA7E;sBACAwC;wBAAA;wBAAA,uCACAsC;0BACAC;4BAAA;0BAAA;0BACAC;4BAAA;0BAAA;0BACAC;4BAAA;0BAAA;wBAAA;sBAAA,CACA;oBAAA;kBAAA,CACA;kBACAC;kBACAC;kBAEA;kBACAC;oBACA1I;oBACAwD;oBACAC;oBACAJ;kBACA;kBAEA;kBACAsF;oBACA3I;oBACA4I;oBACAC;oBACA/C;sBAAA;wBACAM;wBACApG;wBACAqG;wBACAC;wBACAC;wBACA8B;wBACAC;wBACAC;sBACA;oBAAA;kBACA;gBACA,GAEA;gBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACA1F;cAAA;gBAAAiG;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBAEA;kBACAC;kBACAC,qCACAD,iCACAA,0BACAA;kBAEA;oBACA1G;kBACA;kBAEAH;oBACAC;oBACAG;kBACA;;kBAEA;kBACAJ;;kBAEA;kBACA+G;oBACA/G;kBACA;gBACA;kBACAG;kBACAH;oBACAC;oBACAG;kBACA;kBACA;kBACAJ;kBACA+G;oBACA/G;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAG;gBACAH;kBACAC;kBACAG;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4G;MACA;MACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACA7G;;QAEA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;UACA;UACA;YACA;YACA;YACA;YACA;YACA;;YAEA;YACA;YACA;YACA;UACA;;UAEA;UACA;UACA;YACA;YACA;;YAEA;YACA;YAEAA;YACAA;;YAEA;YACA;cACAA;cACA;cACA;YACA;UACA;QACA;;QAEA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;MACA;QACAA;QACA;QACA;MACA;IACA;IAEA;IACA8G;MACA;MACA;QACA;QACA;UAAA;QAAA;QACA;UACA9G;UACA;QACA;;QAEA;QACA;UAAA;QAAA;QACA;UACAA;UACA;QACA;;QAEA;QACA;UAAA;QAAA;QACA;UACAA;UACA;QACA;;QAEA;QACA;QACA;UAAA;QAAA;QACA;UACAA;UACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;QACA;QACA;QACA;;QAEA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;UACA;UACA;YACA;YACA;YACA;YACA;YACA;;YAEA;YACA;YACA;YACA;UACA;;UAEA;UACA;UACA;UACA;;UAEA;UACA;UAEAA;UACAA;;UAEA;UACA;YACAA;YACA;UACA;QACA;;QAEA;QACA;UACAA;UACA;QACA;;QAEA;QACAA;QACA;MACA;QACAA;QACA;QACA;MACA;IACA;IAEA;IACA+G;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;UACA;YACA;UACA;UACA;QACA;UACA;YACA;UACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACArH;IACA;IAEA;IACAsH;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAtH;kBACAC;kBACAG;gBACA;gBAAA;cAAA;gBAAA;gBAKAJ;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACAU;kBACA7C;kBACA8C;kBACAjD;oBACAuE;sBACAhE;sBACAgF;sBACApC;sBACAqC;sBACAoE;oBACA;kBACA;gBACA;cAAA;gBAZAxG;gBAAA,MAcAA;kBAAA;kBAAA;gBAAA;gBACAyG,sBACA;gBACA,wDACA;kBACAC;gBAAA,EACA;;gBAEA;gBACA;;gBAEA;gBACAzH;gBACAA;kBACAC;kBACAG;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAD;gBACAH;gBACAA;kBACAC;kBACAG;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsH;MACA;MACA;QACA;QACA;MACA;QACA;UAAA,OACAC,sEACAA;QAAA;QAAA,CACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACp6CA;AAAA;AAAA;AAAA;AAAwoC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACA5pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/task/add.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/task/add.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./add.vue?vue&type=template&id=3e49dba4&\"\nvar renderjs\nimport script from \"./add.vue?vue&type=script&lang=js&\"\nexport * from \"./add.vue?vue&type=script&lang=js&\"\nimport style0 from \"./add.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/task/add.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=template&id=3e49dba4&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.showUserSelect\n    ? _vm.__map(_vm.filteredUsers, function (user, idx) {\n        var $orig = _vm.__get_orig(user)\n        var m0 = user.role && _vm.getRoleText(user.role)\n        var m1 = m0 ? _vm.getRoleText(user.role) : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var m2 = _vm.selectedUser ? _vm.getRoleText(_vm.selectedUser.role) : null\n  var m3 =\n    _vm.formData.date && _vm.selectedShift\n      ? _vm.getStatusIcon(_vm.predictedStatus)\n      : null\n  var m4 =\n    _vm.formData.date && _vm.selectedShift\n      ? _vm.getStatusText(_vm.predictedStatus)\n      : null\n  var m5 =\n    _vm.formData.date && _vm.selectedShift\n      ? _vm.getStatusExplanation(_vm.predictedStatus)\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.showUserSelect = true\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      _vm.showUserSelect = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showUserSelect = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"add-task-container\" @click=\"hideUserSelect\">\n\t\t\n\t\t<view class=\"form-container\">\n\t\t\t<form @submit=\"submitForm\">\n\t\t\t\t<!-- 基本信息 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">基本信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">任务名称</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\t\tv-model=\"formData.name\"\n\t\t\t\t\t\t\tplaceholder=\"请输入任务名称\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"false\">\n\t\t\t\t\t\t<text class=\"form-label required\">区域</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\t\tv-model=\"formData.area\"\n\t\t\t\t\t\t\tplaceholder=\"请输入巡视区域\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">执行日期</text>\n\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\tmode=\"date\" \n\t\t\t\t\t\t\t:value=\"formData.date\" \n\t\t\t\t\t\t\t@change=\"onDateChange\"\n\t\t\t\t\t\t\tclass=\"form-picker\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<text>{{ formData.date || '请选择执行日期' }}</text>\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"20\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 班次与轮次 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">班次与轮次</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">班次</text>\n\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t:range=\"shiftOptions\" \n\t\t\t\t\t\t\trange-key=\"name\" \n\t\t\t\t\t\t\t:value=\"shiftIndex\"\n\t\t\t\t\t\t\t@change=\"onShiftChange\"\n\t\t\t\t\t\t\tclass=\"form-picker\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<view class=\"shift-info\">\n\t\t\t\t\t\t\t\t\t<text>{{ shiftOptions[shiftIndex].name || '请选择班次' }}</text>\n\t\t\t\t\t\t\t\t\t<text v-if=\"selectedShift && selectedShift.across_day\" class=\"cross-day-tag\">跨天班次</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"selectedShift && selectedShift.rounds\">\n\t\t\t\t\t\t<text class=\"form-label\">轮次信息</text>\n\t\t\t\t\t\t<view class=\"shift-time-info\" v-if=\"selectedShift\">\n\t\t\t\t\t\t\t<view class=\"time-range\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>{{ selectedShift.start_time }} - {{ selectedShift.end_time }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"round-tags\">\n\t\t\t\t\t\t\t<view class=\"round-tag\" v-for=\"(round, index) in selectedShift.rounds\" :key=\"index\">\n\t\t\t\t\t\t\t\t轮次{{ round.round }}: {{ round.start_time || round.time }}\n\t\t\t\t\t\t\t\t<text v-if=\"round.day_offset > 0\" class=\"next-day-badge\">次日</text>\n\t\t\t\t\t\t\t\t<text v-if=\"round.duration\">, 有效时长{{ round.duration }}分钟</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 巡检路线 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">巡检路线</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">路线</text>\n\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t:range=\"routeOptions\" \n\t\t\t\t\t\t\trange-key=\"name\" \n\t\t\t\t\t\t\t:value=\"routeIndex\"\n\t\t\t\t\t\t\t@change=\"onRouteChange\"\n\t\t\t\t\t\t\tclass=\"form-picker\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<text>{{ routeOptions[routeIndex].name || '请选择巡检路线' }}</text>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"selectedRoute && selectedRoute.pointsDetail\">\n\t\t\t\t\t\t<view class=\"points-header\">\n\t\t\t\t\t\t\t<text class=\"form-label\">点位列表</text>\n\t\t\t\t\t\t\t<view class=\"refresh-points-btn\" @click=\"refreshRoutePointsData\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"refresh\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>刷新点位数据</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"points-info\" v-if=\"pointsRefreshed\">\n\t\t\t\t\t\t\t<uni-icons type=\"info\" size=\"14\" color=\"#52C41A\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"info-text success\">点位数据已更新至最新</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"points-list\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"point-item\" \n\t\t\t\t\t\t\t\tv-for=\"(point, index) in selectedRoute.pointsDetail\" \n\t\t\t\t\t\t\t\t:key=\"point._id\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<text class=\"point-index\">{{ index + 1 }}</text>\n\t\t\t\t\t\t\t\t<text class=\"point-name\">{{ point.name }}</text>\n\t\t\t\t\t\t\t\t<text class=\"point-range\">{{ point.range }}米</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 任务执行人 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">任务执行人</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">人员选择</text>\n\t\t\t\t\t\t<view class=\"custom-select\" @click.stop=\"showUserSelect = true\">\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<text>{{ selectedUser ? selectedUser.nickname : '请选择执行人员' }}</text>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 自定义下拉选择器 -->\n\t\t\t\t\t\t<view class=\"user-select-modal\" v-if=\"showUserSelect\" @click.stop=\"showUserSelect = false\">\n\t\t\t\t\t\t\t<view class=\"user-select-popup\" @click.stop>\n\t\t\t\t\t\t\t\t<view class=\"user-select-container\">\n\t\t\t\t\t\t\t\t\t<view class=\"user-select-header\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"user-select-title\">请选择执行人员</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"user-search-header\">\n\t\t\t\t\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t\t\t\t\tclass=\"user-search-input\" \n\t\t\t\t\t\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"输入姓名进行搜索\" \n\t\t\t\t\t\t\t\t\t\t\tv-model=\"searchUserName\"\n\t\t\t\t\t\t\t\t\t\t\t@input=\"filterUserOptions\"\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"search\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<scroll-view scroll-y class=\"user-select-list\">\n\t\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\t\tclass=\"user-select-item\" \n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(user, idx) in filteredUsers\" \n\t\t\t\t\t\t\t\t\t\t\t:key=\"user._id\"\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"selectUser(idx)\"\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{'first-item': idx === 0 && !user._id}\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"user-item-content\">\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"user.avatar || '/static/user/default-avatar.png'\" class=\"user-avatar-small\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"user-item-details\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"user-item-name\">{{ user.nickname }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"user-item-role\" v-if=\"user.role && getRoleText(user.role)\">{{ getRoleText(user.role) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"select-radio\" v-if=\"selectedUser && selectedUser._id === user._id\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"checkbox-filled\" size=\"18\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t\t\t\t\t<view class=\"user-select-footer\">\n\t\t\t\t\t\t\t\t\t\t<button class=\"user-select-close\" @tap=\"showUserSelect = false\">关闭</button>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"selectedUser\">\n\t\t\t\t\t\t<text class=\"form-label\">所选人员</text>\n\t\t\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t\t\t<image \n\t\t\t\t\t\t\t\t:src=\"selectedUser.avatar || '/static/user/default-avatar.png'\" \n\t\t\t\t\t\t\t\tmode=\"aspectFill\" \n\t\t\t\t\t\t\t\tclass=\"user-avatar\"\n\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t<view class=\"user-details\">\n\t\t\t\t\t\t\t\t<text class=\"user-name\">{{ selectedUser.nickname }}</text>\n\t\t\t\t\t\t\t\t<text class=\"user-role\">{{ getRoleText(selectedUser.role) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 任务状态 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">任务状态</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">状态判断方式</text>\n\t\t\t\t\t\t<view class=\"status-info\">\n\t\t\t\t\t\t\t<text class=\"status-desc\">系统将根据任务开始时间自动判断任务状态</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"timezone-indicator\" v-if=\"timeZoneIndicator\">\n\t\t\t\t\t\t\t<uni-icons type=\"info\" size=\"14\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t<text>当前时区: {{ timeZoneIndicator }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"formData.date && selectedShift\">\n\t\t\t\t\t\t<text class=\"form-label\">预计状态</text>\n\t\t\t\t\t\t<view class=\"status-preview\">\n\t\t\t\t\t\t\t<view class=\"status-tag\" :class=\"[`status-${predictedStatus}`]\">\n\t\t\t\t\t\t\t\t<uni-icons :type=\"getStatusIcon(predictedStatus)\" size=\"16\" color=\"currentColor\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>{{ getStatusText(predictedStatus) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"status-explanation\">{{ getStatusExplanation(predictedStatus) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 提交按钮 -->\n\t\t\t\t<view class=\"submit-section\">\n\t\t\t\t\t<button \n\t\t\t\t\t\tclass=\"cancel-btn\" \n\t\t\t\t\t\t@click=\"navigateBack\"\n\t\t\t\t\t>取消操作</button>\n\t\t\t\t\t<button \n\t\t\t\t\t\tclass=\"primary-btn\" \n\t\t\t\t\t\tform-type=\"submit\"\n\t\t\t\t\t\t:loading=\"submitting\"\n\t\t\t\t\t\t:disabled=\"submitting\"\n\t\t\t\t\t\tstyle=\"background-color: #1677FF !important; color: #FFFFFF !important;\"\n\t\t\t\t\t>创建任务</button>\n\t\t\t\t</view>\n\t\t\t</form>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport { calculateRoundTime, calculateEndTime, formatDate, detectTimeZone, preprocessDates } from '@/utils/date.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tsubmitting: false,\n\t\t\tformData: {\n\t\t\t\tname: '',\n\t\t\t\tarea: '',\n\t\t\t\tdate: '',\n\t\t\t\tshift_id: '',\n\t\t\t\troute_id: '',\n\t\t\t\tuser_id: '',\n\t\t\t\tstatus: 0 // 默认未开始，将在提交时根据时间计算正确状态\n\t\t\t},\n\t\t\tshiftOptions: [{ _id: '', name: '请选择班次', rounds: [] }],\n\t\t\tshiftIndex: 0,\n\t\t\trouteOptions: [{ _id: '', name: '请选择路线' }],\n\t\t\trouteIndex: 0,\n\t\t\tuserOptions: [{ _id: '', nickname: '请选择人员' }],\n\t\t\tuserIndex: 0,\n\t\t\tselectedRoute: null,\n\t\t\tselectedShift: null,\n\t\t\tselectedUser: null,\n\t\t\t// 角色映射表\n\t\t\troleNameMap: {},\n\t\t\t// 状态常量\n\t\t\tSTATUS: {\n\t\t\t\tNOT_STARTED: 0, // 未开始\n\t\t\t\tIN_PROGRESS: 1, // 进行中\n\t\t\t\tCOMPLETED: 2,   // 已完成\n\t\t\t\tEXPIRED: 3,     // 已超时\n\t\t\t\tCANCELLED: 4    // 已取消\n\t\t\t},\n\t\t\tpredictedStatus: 0, // 预测的任务状态\n\t\t\ttimeZoneIndicator: null, // 用于显示当前时区\n\t\t\tprocessedRounds: null, // 用于存储处理后的轮次数据\n\t\t\tpointsRefreshed: false, // 点位数据是否已刷新\n\t\t\tsearchUserName: '',\n\t\t\tfilteredUsers: [],\n\t\t\tshowUserSelect: false,\n\t\t};\n\t},\n\tasync onLoad() {\n\t\t// 设置默认日期为今天\n\t\tthis.formData.date = formatDate(new Date(), 'YYYY-MM-DD');\n\t\t\n\t\t// 检测并记录当前时区\n\t\tthis.timeZoneIndicator = detectTimeZone();\n\t\t\n\t\t// 🔥 优化：并行加载数据提升用户体验\n\t\ttry {\n\t\t\tuni.showLoading({ title: '加载中...' });\n\t\t\t\n\t\t\t// 并行加载减少用户等待时间，各项请求独立进行\n\t\t\tawait Promise.all([\n\t\t\t\tthis.loadShifts(),\n\t\t\t\tthis.loadRoutes(),\n\t\t\t\tthis.loadRoles(),\n\t\t\t\tthis.loadUsers()\n\t\t\t]);\n\t\t} catch (e) {\n\t\t\tconsole.error('初始化数据加载失败:', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '数据加载失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t} finally {\n\t\t\tuni.hideLoading();\n\t\t}\n\t},\n\twatch: {\n\t\t// 监听影响任务状态的关键数据变化\n\t\t'formData.date'() {\n\t\t\tthis.updatePredictedStatus();\n\t\t},\n\t\tselectedShift: {\n\t\t\thandler() {\n\t\t\t\tthis.updatePredictedStatus();\n\t\t\t},\n\t\t\tdeep: true\n\t\t},\n\t\t// 监听用户列表变化，初始化过滤后的列表\n\t\tuserOptions: {\n\t\t\thandler(val) {\n\t\t\t\tthis.filteredUsers = [...val];\n\t\t\t},\n\t\t\timmediate: true\n\t\t},\n\t},\n\tmethods: {\n\t\t// 加载班次数据\n\t\tasync loadShifts() {\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-shift',\n\t\t\t\t\taction: 'getShiftList',\n\t\t\t\t\tdata: { \n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t\twith_rounds: true,  // 请求包含轮次信息\n\t\t\t\t\t\twith_detail: true   // 请求包含详细信息（包括跨天标记）\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data && res.data.list) {\n\t\t\t\t\t// 只加载启用的班次\n\t\t\t\t\tconst defaultOption = { _id: '', name: '请选择班次', rounds: [] };\n\t\t\t\t\tconst shifts = res.data.list.map(shift => ({\n\t\t\t\t\t\t...shift,\n\t\t\t\t\t\tname: shift.name,\n\t\t\t\t\t\tacross_day: shift.across_day || false,\n\t\t\t\t\t\trounds: shift.rounds && Array.isArray(shift.rounds) \n\t\t\t\t\t\t\t? shift.rounds.filter(r => r.status !== 0).map(round => ({\n\t\t\t\t\t\t\t\tround: round.round,\n\t\t\t\t\t\t\t\tname: round.name || `轮次${round.round}`,\n\t\t\t\t\t\t\t\ttime: round.time,\n\t\t\t\t\t\t\t\tstart_time: round.start_time || round.time,\n\t\t\t\t\t\t\t\tend_time: round.end_time || round.time,\n\t\t\t\t\t\t\t\tday_offset: round.day_offset || 0,\n\t\t\t\t\t\t\t\tduration: round.duration || 60,\n\t\t\t\t\t\t\t\tstatus: round.status\n\t\t\t\t\t\t\t}))\n\t\t\t\t\t\t\t: []\n\t\t\t\t\t}));\n\t\t\t\t\t\n\t\t\t\t\t// 按照班次名称字母顺序排序\n\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\tconst platform = systemInfo.platform;\n\t\t\t\t\tconst isMobile = ['android', 'ios'].includes(platform);\n\t\t\t\t\t\n\t\t\t\t\t// 对班次进行排序\n\t\t\t\t\tshifts.sort((a, b) => {\n\t\t\t\t\t\tconst nameA = String(a.name || '').trim();\n\t\t\t\t\t\tconst nameB = String(b.name || '').trim();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 使用 localeCompare 进行中文排序，统一使用 A-Z 排序\n\t\t\t\t\t\tconst compareResult = nameA.localeCompare(nameB, 'zh-CN');\n\t\t\t\t\t\treturn isMobile ? -compareResult : compareResult;\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 合并默认选项和排序后的班次列表\n\t\t\t\t\tthis.shiftOptions = [defaultOption, ...shifts];\n\t\t\t\t} else {\n\t\t\t\t\tthis.shiftOptions = [{ _id: '', name: '请选择班次', rounds: [] }];\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载班次失败:', e);\n\t\t\t\tthis.shiftOptions = [{ _id: '', name: '请选择班次', rounds: [] }];\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载班次失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载路线数据\n\t\tasync loadRoutes() {\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-route',\n\t\t\t\t\taction: 'getRouteList',\n\t\t\t\t\tdata: { status: 1 }\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data && res.data.list) {\n\t\t\t\t\t// 只加载启用的路线\n\t\t\t\t\tthis.routeOptions = [\n\t\t\t\t\t\t{ _id: '', name: '请选择路线' },\n\t\t\t\t\t\t...res.data.list\n\t\t\t\t\t];\n\t\t\t\t} else {\n\t\t\t\t\tthis.routeOptions = [{ _id: '', name: '请选择路线' }];\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tthis.routeOptions = [{ _id: '', name: '请选择路线' }];\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载路线失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取当前用户ID\n\t\tgetCurrentUserId() {\n\t\t\ttry {\n\t\t\t\t// 尝试从本地存储获取用户信息\n\t\t\t\tconst userInfo = uni.getStorageSync('uni-id-pages-userInfo');\n\t\t\t\tif (userInfo) {\n\t\t\t\t\treturn userInfo._id || '';\n\t\t\t\t}\n\t\t\t\treturn '';\n\t\t\t} catch (e) {\n\t\t\t\treturn '';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载用户列表\n\t\tasync loadUsers() {\n\t\t\ttry {\n\t\t\t\tconst currentUserId = this.getCurrentUserId();\n\t\t\t\tif (!currentUserId) {\n\t\t\t\t\tthrow new Error('未能获取当前用户ID');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-user',\n\t\t\t\t\taction: 'getUsers',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tuserid: currentUserId,\n\t\t\t\t\t\tparams: {\n\t\t\t\t\t\t\tpageSize: 100\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (result.code === 0 && result.data) {\n\t\t\t\t\t// 添加一个空选项作为第一个选项\n\t\t\t\t\tthis.userOptions = [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t_id: '',\n\t\t\t\t\t\t\tnickname: '请选择执行人',\n\t\t\t\t\t\t\tavatar: '/static/user/default-avatar.png',\n\t\t\t\t\t\t\trole: []\n\t\t\t\t\t\t}\n\t\t\t\t\t];\n\t\t\t\t\t\n\t\t\t\t\t// 处理用户数据，过滤掉admin角色的用户\n\t\t\t\t\tconst userList = result.data.list\n\t\t\t\t\t\t.filter(user => {\n\t\t\t\t\t\t\t// 过滤掉admin角色用户\n\t\t\t\t\t\t\tif (!user.role) return true; // 没有角色的保留\n\t\t\t\t\t\t\tif (Array.isArray(user.role)) {\n\t\t\t\t\t\t\t\t// 如果是数组，检查是否包含admin\n\t\t\t\t\t\t\t\treturn !user.role.includes('admin');\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 如果是字符串，检查是否为admin\n\t\t\t\t\t\t\t\treturn user.role !== 'admin';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.filter(user => {\n\t\t\t\t\t\t\t// 过滤掉以\"匿名\"开头的用户\n\t\t\t\t\t\t\tconst nickname = user.real_name || user.nickname || user.username || '';\n\t\t\t\t\t\t\treturn !nickname.startsWith('匿名');\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.map(user => ({\n\t\t\t\t\t\t\t_id: user._id,\n\t\t\t\t\t\t\tnickname: user.real_name || user.nickname || user.username || '未命名用户',\n\t\t\t\t\t\t\tavatar: user.avatar || '/static/user/default-avatar.png',\n\t\t\t\t\t\t\trole: user.role || []\n\t\t\t\t\t\t}));\n\t\t\t\t\t\n\t\t\t\t\t// 合并空选项和用户列表\n\t\t\t\t\tthis.userOptions = [...this.userOptions, ...userList];\n\t\t\t\t\t\n\t\t\t\t\t// 初始选择第一项（请选择执行人）\n\t\t\t\t\tthis.userIndex = 0;\n\t\t\t\t\tthis.selectedUser = null;\n\t\t\t\t\tthis.formData.user_id = '';\n\t\t\t\t\t\n\t\t\t\t\tif (this.userOptions.length <= 1) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '暂无可用执行人员',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.userOptions = [{ \n\t\t\t\t\t\t_id: '',\n\t\t\t\t\t\tnickname: '请选择执行人',\n\t\t\t\t\t\tavatar: '/static/user/default-avatar.png',\n\t\t\t\t\t\trole: []\n\t\t\t\t\t}];\n\t\t\t\t\tthis.userIndex = 0;\n\t\t\t\t\tthis.selectedUser = null;\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载用户数据失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载角色数据\n\t\tasync loadRoles() {\n\t\t\ttry {\n\t\t\t\tconst currentUserId = this.getCurrentUserId();\n\t\t\t\tif (!currentUserId) {\n\t\t\t\t\tthrow new Error('未能获取当前用户ID');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-user',\n\t\t\t\t\taction: 'getRoleList',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tuserid: currentUserId\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\tif (result.code === 0 && result.data) {\n\t\t\t\t\t// 构建角色ID到名称的映射\n\t\t\t\t\tconst roleMap = {};\n\t\t\t\t\tresult.data.forEach(role => {\n\t\t\t\t\t\troleMap[role.role_id] = role.role_name;\n\t\t\t\t\t});\n\t\t\t\t\tthis.roleNameMap = roleMap;\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载角色数据失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 日期变更\n\t\tonDateChange(e) {\n\t\t\t// 确保日期格式一致 - 只保留YYYY-MM-DD部分\n\t\t\tconst dateValue = e.detail.value;\n\t\t\t// 检查是否需要处理日期格式\n\t\t\tif (dateValue.includes('T') || dateValue.includes('Z')) {\n\t\t\t\t// 如果有时区信息，提取日期部分\n\t\t\t\tthis.formData.date = dateValue.split('T')[0];\n\t\t\t} else {\n\t\t\t\t// 否则直接使用，这应该已经是YYYY-MM-DD格式\n\t\t\t\tthis.formData.date = dateValue;\n\t\t\t}\n\t\t\t\n\t\t\t// 在日期变更后，如果有选择班次，更新预计状态\n\t\t\tif (this.selectedShift) {\n\t\t\t\tthis.updatePredictedStatus();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 班次变更\n\t\tasync onShiftChange(e) {\n\t\t\tthis.shiftIndex = e.detail.value;\n\t\t\tconst shift = this.shiftOptions[this.shiftIndex];\n\t\t\t\n\t\t\tif (shift && shift._id) {\n\t\t\t\tthis.formData.shift_id = shift._id;\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\t\tname: 'patrol-shift',\n\t\t\t\t\t\taction: 'getShiftDetail',\n\t\t\t\t\t\tdata: { \n\t\t\t\t\t\t\tparams: { \n\t\t\t\t\t\t\t\tshift_id: shift._id,\n\t\t\t\t\t\t\t\twith_rounds: true,\n\t\t\t\t\t\t\t\twith_detail: true \n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t\t// 处理班次详情，确保包含轮次信息\n\t\t\t\t\t\tconst shiftData = res.data;\n\t\t\t\t\t\tif (shiftData.rounds && Array.isArray(shiftData.rounds)) {\n\t\t\t\t\t\t\tconst enabledRounds = shiftData.rounds.filter(r => r.status !== 0);\n\t\t\t\t\t\t\tthis.selectedShift = {\n\t\t\t\t\t\t\t\t...shiftData,\n\t\t\t\t\t\t\t\t// 确保跨天标记正确设置\n\t\t\t\t\t\t\t\tacross_day: shiftData.across_day || false,\n\t\t\t\t\t\t\t\trounds: enabledRounds.map(round => ({\n\t\t\t\t\t\t\t\t\tround: round.round,\n\t\t\t\t\t\t\t\t\tname: round.name || `轮次${round.round}`,\n\t\t\t\t\t\t\t\t\ttime: round.time,\n\t\t\t\t\t\t\t\t\tstart_time: round.start_time || round.time,\n\t\t\t\t\t\t\t\t\tend_time: round.end_time || round.time,\n\t\t\t\t\t\t\t\t\t// 确保天数偏移正确设置\n\t\t\t\t\t\t\t\t\tday_offset: round.day_offset || 0,\n\t\t\t\t\t\t\t\t\tduration: round.duration || 60,\n\t\t\t\t\t\t\t\t\tstatus: round.status\n\t\t\t\t\t\t\t\t}))\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 如果是跨天班次，给用户提示\n\t\t\t\t\t\t\tif (this.selectedShift.across_day) {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '您选择了跨天班次，系统将自动处理次日轮次',\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tconsole.log('选择了跨天班次:', {\n\t\t\t\t\t\t\t\t\tname: this.selectedShift.name,\n\t\t\t\t\t\t\t\t\tstart_time: this.selectedShift.start_time,\n\t\t\t\t\t\t\t\t\tend_time: this.selectedShift.end_time,\n\t\t\t\t\t\t\t\t\trounds: this.selectedShift.rounds.map(r => ({\n\t\t\t\t\t\t\t\t\t\tround: r.round,\n\t\t\t\t\t\t\t\t\t\ttime: r.time,\n\t\t\t\t\t\t\t\t\t\tday_offset: r.day_offset\n\t\t\t\t\t\t\t\t\t}))\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.selectedShift = shiftData;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取班次详情失败:', e);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取班次详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthis.formData.shift_id = '';\n\t\t\t\tthis.selectedShift = null;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 路线变更\n\t\tasync onRouteChange(e) {\n\t\t\tthis.routeIndex = e.detail.value;\n\t\t\tconst route = this.routeOptions[this.routeIndex];\n\t\t\t\n\t\t\tif (route && route._id) {\n\t\t\t\tthis.formData.route_id = route._id;\n\t\t\t\t\n\t\t\t\t// 自动设置区域为路线名称\n\t\t\t\tthis.formData.area = route.name || '';\n\t\t\t\t\n\t\t\t\t// 获取路线详情\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\t\tname: 'patrol-route',\n\t\t\t\t\t\taction: 'getRouteDetail',\n\t\t\t\t\t\tdata: { \n\t\t\t\t\t\t\tparams: { \n\t\t\t\t\t\t\t\troute_id: route._id,\n\t\t\t\t\t\t\t\twith_points: true,\n\t\t\t\t\t\t\t\twith_detail: true,\n\t\t\t\t\t\t\t\tinclude_point_details: true\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t\tthis.selectedRoute = res.data;\n\t\t\t\t\t\t// 再次确保区域被设置为路线名称\n\t\t\t\t\t\tthis.formData.area = res.data.name || route.name || '';\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取路线详情失败:', e);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取路线详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthis.formData.route_id = '';\n\t\t\t\tthis.formData.area = ''; // 清空区域\n\t\t\t\tthis.selectedRoute = null;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 用户变更\n\t\tonUserChange(e) {\n\t\t\tthis.userIndex = e.detail.value;\n\t\t\tconst user = this.filteredUsers[this.userIndex];\n\t\t\t\n\t\t\tif (user && user._id) {\n\t\t\t\tthis.formData.user_id = user._id;\n\t\t\t\tthis.selectedUser = user;\n\t\t\t} else {\n\t\t\t\tthis.formData.user_id = '';\n\t\t\t\tthis.selectedUser = null;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 状态变更 - 移除此方法或保留为空函数\n\t\tonStatusChange(e) {\n\t\t\t// 方法已不再需要，但保留避免引用错误\n\t\t},\n\t\t\n\t\t// 获取角色文本\n\t\tgetRoleText(role) {\n\t\t\tif (!role) return '普通员工';\n\t\t\t\n\t\t\t// 处理数组形式的角色\n\t\t\tif (Array.isArray(role)) {\n\t\t\t\tif (role.length === 0) return '普通员工';\n\t\t\t\t// 返回所有角色的文本，用逗号分隔\n\t\t\t\treturn role.map(r => this.getSingleRoleText(r)).join(', ');\n\t\t\t}\n\t\t\t\n\t\t\treturn this.getSingleRoleText(role);\n\t\t},\n\t\t\n\t\t// 处理单个角色值\n\t\tgetSingleRoleText(role) {\n\t\t\t// 首先从角色表的映射中查找\n\t\t\tif (this.roleNameMap[role]) {\n\t\t\t\treturn this.roleNameMap[role];\n\t\t\t}\n\t\t\t\n\t\t\t// 如果在角色表中找不到，使用预定义的映射作为备用\n\t\t\tconst roleMap = {\n\t\t\t\t'admin': '管理员',\n\t\t\t\t'responsible': '责任人',\n\t\t\t\t'reviser': '发布人',\n\t\t\t\t'supervisor': '主管',\n\t\t\t\t'PM': '副厂长',\n\t\t\t\t'GM': '厂长',\n\t\t\t\t'logistics': '后勤员',\n\t\t\t\t'dispatch': '调度员',\n\t\t\t\t'Integrated': '综合员',\n\t\t\t\t'operator': '设备员',\n\t\t\t\t'technician': '工艺员',\n\t\t\t\t'mechanic': '技术员',\n\t\t\t\t'user': '普通员工',\n\t\t\t\t'manager': '管理人员',\n\t\t\t\t'worker': '普通员工'\n\t\t\t};\n\t\t\t\n\t\t\treturn roleMap[role] || '用户 (' + role + ')';\n\t\t},\n\t\t\n\t\t// 格式化日期\n\t\tformatDate(date) {\n\t\t\treturn formatDate(date, 'YYYY-MM-DD');\n\t\t},\n\t\t\n\t\t// 表单验证\n\t\tvalidateForm() {\n\t\t\tif (!this.formData.name) {\n\t\t\t\tuni.showToast({ title: '请输入任务名称', icon: 'none' });\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.formData.route_id) {\n\t\t\t\tuni.showToast({ title: '请选择巡检路线', icon: 'none' });\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\t// 确保区域从路线名称填充\n\t\t\tif (!this.formData.area) {\n\t\t\t\tconst route = this.routeOptions[this.routeIndex];\n\t\t\t\tthis.formData.area = route && route.name ? route.name : '未知区域';\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.formData.date) {\n\t\t\t\tuni.showToast({ title: '请选择执行日期', icon: 'none' });\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.formData.shift_id) {\n\t\t\t\tuni.showToast({ title: '请选择班次', icon: 'none' });\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.formData.user_id) {\n\t\t\t\tuni.showToast({ title: '请选择执行人员', icon: 'none' });\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\treturn true;\n\t\t},\n\t\t\n\t\t// 处理轮次数据，生成新的数据结构\n\t\tasync processRoundsData() {\n\t\t\tif (!this.selectedShift || !this.selectedShift.rounds || !this.selectedRoute || !this.selectedRoute.pointsDetail) {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t\t\n\t\t\t// 获取任务执行日期作为基准日期\n\t\t\tconst taskDate = this.formData.date;\n\t\t\tconst shift = this.selectedShift;\n\t\t\t\n\t\t\t// 解析班次开始时间\n\t\t\tconst [shiftStartHours] = shift.start_time.split(':').map(Number);\n\t\t\t\n\t\t\t// 获取路线点位\n\t\t\tconst points = this.selectedRoute.pointsDetail;\n\t\t\t\n\t\t\t// 当前时间 - 用于计算轮次状态\n\t\t\tconst now = new Date();\n\t\t\t\n\t\t\treturn this.selectedShift.rounds.map(round => {\n\t\t\t\t// 获取轮次时间\n\t\t\t\tconst checkTime = round.time;\n\t\t\t\tconst [roundHours] = checkTime.split(':').map(Number);\n\t\t\t\t\n\t\t\t\t// 计算天数偏移\n\t\t\t\tlet actualDayOffset = 0; // 重置为0,不使用原始的day_offset值\n\t\t\t\t\n\t\t\t\t// 只有当班次是跨天且轮次时间小于班次开始时间时才设置为次日(day_offset=1)\n\t\t\t\tif (shift.across_day && roundHours < shiftStartHours) {\n\t\t\t\t\tactualDayOffset = 1; // 直接设置为1,表示次日\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 基于任务执行日计算轮次时间\n\t\t\t\tconst checkDateTime = new Date(`${taskDate}T${checkTime}:00+08:00`);\n\t\t\t\tcheckDateTime.setDate(checkDateTime.getDate() + actualDayOffset);\n\t\t\t\t\n\t\t\t\t// 使用 calculateEndTime 计算结束时间\n\t\t\t\tconst endDateTime = calculateEndTime(checkDateTime, round.duration || 60);\n\t\t\t\t\n\t\t\t\t// 根据当前时间计算轮次状态\n\t\t\t\tlet roundStatus = 0; // 默认未开始\n\t\t\t\t\n\t\t\t\tif (now > endDateTime) {\n\t\t\t\t\t// 当前时间已超过轮次结束时间，标记为\"已超时\"\n\t\t\t\t\troundStatus = 3; // 已超时状态\n\t\t\t\t} else if (now >= checkDateTime) {\n\t\t\t\t\t// 当前时间在轮次的开始和结束时间之间，标记为\"进行中\"\n\t\t\t\t\troundStatus = 1; // 进行中状态\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 轮次点位信息\n\t\t\t\tconst roundPoints = points.map(point => ({\n\t\t\t\t\tpoint_id: point._id,\n\t\t\t\t\tname: point.name,\n\t\t\t\t\torder: point.order || 0,\n\t\t\t\t\tstatus: 0,\n\t\t\t\t\tlocation: point.location,\n\t\t\t\t\trange: point.range\n\t\t\t\t}));\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\tround: round.round,\n\t\t\t\t\tname: round.name || `轮次${round.round}`,\n\t\t\t\t\ttime: checkTime,\n\t\t\t\t\tcheck_time: checkDateTime.toISOString(),\n\t\t\t\t\tstart_time: checkDateTime.toISOString(),\n\t\t\t\t\tend_time: endDateTime.toISOString(),\n\t\t\t\t\tday_offset: actualDayOffset,\n\t\t\t\t\tduration: round.duration || 60,\n\t\t\t\t\tstatus: roundStatus,\n\t\t\t\t\tacross_day: shift.across_day,\n\t\t\t\t\tpoints: roundPoints,\n\t\t\t\t\tstats: {\n\t\t\t\t\t\ttotal_points: roundPoints.length,\n\t\t\t\t\t\tcompleted_points: 0,\n\t\t\t\t\t\tmissed_points: roundPoints.length,\n\t\t\t\t\t\tcompletion_rate: 0,\n\t\t\t\t\t\tabnormal_count: 0\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 计算整体统计数据\n\t\tcalculateOverallStats(rounds) {\n\t\t\t// 如果没有轮次，返回默认统计数据\n\t\t\tif (!rounds || rounds.length === 0) {\n\t\t\t\treturn {\n\t\t\t\t\ttotal_points: 0,\n\t\t\t\t\tcompleted_points: 0,\n\t\t\t\t\tmissed_points: 0,\n\t\t\t\t\tcompletion_rate: 0,\n\t\t\t\t\tabnormal_count: 0,\n\t\t\t\t\tlast_checkin_time: null // 初始无打卡记录\n\t\t\t\t};\n\t\t\t}\n\t\t\t\n\t\t\t// 计算所有轮次的统计数据\n\t\t\tlet totalPoints = 0;\n\t\t\tlet completedPoints = 0;\n\t\t\tlet abnormalCount = 0;\n\t\t\t\n\t\t\trounds.forEach(round => {\n\t\t\t\tif (round.stats) {\n\t\t\t\t\ttotalPoints += round.stats.total_points || 0;\n\t\t\t\t\tcompletedPoints += round.stats.completed_points || 0;\n\t\t\t\t\tabnormalCount += round.stats.abnormal_count || 0;\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 计算未完成点位数和完成率\n\t\t\tconst missedPoints = totalPoints - completedPoints;\n\t\t\tconst completionRate = totalPoints > 0 ? completedPoints / totalPoints : 0;\n\t\t\t\n\t\t\treturn {\n\t\t\t\ttotal_points: totalPoints,\n\t\t\t\tcompleted_points: completedPoints,\n\t\t\t\tmissed_points: missedPoints,\n\t\t\t\tcompletion_rate: completionRate,\n\t\t\t\tabnormal_count: abnormalCount,\n\t\t\t\tlast_checkin_time: null // 初始无打卡记录\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 提交表单时整理数据（修改提交部分）\n\t\tasync submitForm() {\n\t\t\t// 表单验证\n\t\t\tif (!this.validateForm()) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 显示加载状态\n\t\t\tthis.submitting = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 处理轮次数据\n\t\t\t\tconst rounds = await this.processRoundsData();\n\t\t\t\t\n\t\t\t\t// 保存处理后的轮次数据，以便在计算任务状态时使用\n\t\t\t\tthis.processedRounds = rounds;\n\t\t\t\t\n\t\t\t\t// 计算整体统计数据\n\t\t\t\tconst overallStats = this.calculateOverallStats(rounds);\n\t\t\t\t\n\t\t\t\t// 确保获取当前选中的班次和路线的名称\n\t\t\t\tconst shiftName = this.selectedShift ? this.selectedShift.name : '';\n\t\t\t\tconst routeName = this.selectedRoute ? this.selectedRoute.name : '';\n\t\t\t\t\n\t\t\t\t// 计算任务整体开始和结束时间\n\t\t\t\tlet startTime, endTime;\n\t\t\t\tif (this.selectedShift) {\n\t\t\t\t\t// 如果班次有开始和结束时间，使用这些时间\n\t\t\t\t\tif (this.selectedShift.start_time) {\n\t\t\t\t\t\tstartTime = calculateRoundTime(this.formData.date, this.selectedShift.start_time).toISOString();\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (this.selectedShift.end_time) {\n\t\t\t\t\t\t// 计算结束时间时需要考虑跨天\n\t\t\t\t\t\tconst [endHours] = this.selectedShift.end_time.split(':').map(Number);\n\t\t\t\t\t\tconst [startHours] = this.selectedShift.start_time.split(':').map(Number);\n\t\t\t\t\t\t\n\t\t\t\t\t\tlet dayOffset = 0;\n\t\t\t\t\t\tif (this.selectedShift.across_day && endHours < startHours) {\n\t\t\t\t\t\t\tdayOffset = 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst endDateTime = calculateRoundTime(this.formData.date, this.selectedShift.end_time, dayOffset);\n\t\t\t\t\t\tendTime = endDateTime.toISOString();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 自动计算状态\n\t\t\t\tconst calculatedStatus = this.calculateTaskStatus(startTime);\n\t\t\t\t\n\t\t\t\t// 确保patrol_date只有日期部分，没有时间部分\n\t\t\t\tconst cleanDate = this.formData.date.split('T')[0]; // 提取YYYY-MM-DD部分，移除可能的时区信息\n\t\t\t\t\n\t\t\t\t// 构建请求数据\n\t\t\t\tlet requestData = {\n\t\t\t\t\tname: this.formData.name,\n\t\t\t\t\tarea: this.formData.area || '默认区域',\n\t\t\t\t\tpatrol_date: cleanDate, // 使用不带时区信息的纯日期格式\n\t\t\t\t\tshift_id: this.formData.shift_id,\n\t\t\t\t\tshift_name: shiftName,\n\t\t\t\t\troute_id: this.formData.route_id,\n\t\t\t\t\troute_name: routeName,\n\t\t\t\t\tuser_id: this.formData.user_id,\n\t\t\t\t\tstatus: calculatedStatus, // 直接使用计算后的状态\n\t\t\t\t\tenabled_rounds: this.selectedShift?.rounds?.map(r => r.round) || [],\n\t\t\t\t\trounds_detail: rounds.map(round => ({\n\t\t\t\t\t\t...round,\n\t\t\t\t\t\tpoints: round.points.map(p => ({\n\t\t\t\t\t\t\t...p,\n\t\t\t\t\t\t\tqrcode_enabled: this.selectedRoute?.pointsDetail?.find(pd => pd._id === p.point_id)?.qrcode_enabled || false,\n\t\t\t\t\t\t\tqrcode_required: this.selectedRoute?.pointsDetail?.find(pd => pd._id === p.point_id)?.qrcode_required || false,\n\t\t\t\t\t\t\tqrcode_version: this.selectedRoute?.pointsDetail?.find(pd => pd._id === p.point_id)?.qrcode_version || 0\n\t\t\t\t\t\t}))\n\t\t\t\t\t})),\n\t\t\t\t\toverall_stats: overallStats,\n\t\t\t\t\tauto_closed: false,\n\t\t\t\t\t\n\t\t\t\t\t// 添加shift_detail冗余字段\n\t\t\t\t\tshift_detail: {\n\t\t\t\t\t\tname: shiftName,\n\t\t\t\t\t\tstart_time: this.selectedShift?.start_time || \"\",\n\t\t\t\t\t\tend_time: this.selectedShift?.end_time || \"\",\n\t\t\t\t\t\tacross_day: this.selectedShift?.across_day || false\n\t\t\t\t\t},\n\t\t\t\t\t\n\t\t\t\t\t// 添加route_detail冗余字段\n\t\t\t\t\troute_detail: {\n\t\t\t\t\t\tname: routeName,\n\t\t\t\t\t\tpoint_count: this.selectedRoute?.pointsDetail?.length || 0,\n\t\t\t\t\t\ttotal_distance: this.selectedRoute?.total_distance || 0,\n\t\t\t\t\t\tpoints: this.selectedRoute?.pointsDetail?.map(p => ({\n\t\t\t\t\t\t\tpoint_id: p._id,\n\t\t\t\t\t\t\tname: p.name,\n\t\t\t\t\t\t\torder: p.order || 0,\n\t\t\t\t\t\t\tlocation: p.location,\n\t\t\t\t\t\t\trange: p.range,\n\t\t\t\t\t\t\tqrcode_enabled: p.qrcode_enabled || false,\n\t\t\t\t\t\t\tqrcode_required: p.qrcode_required || false,\n\t\t\t\t\t\t\tqrcode_version: p.qrcode_version || 0\n\t\t\t\t\t\t})) || []\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 设置任务开始和结束时间\n\t\t\t\tif (startTime) requestData.start_time = startTime;\n\t\t\t\tif (endTime) requestData.end_time = endTime;\n\t\t\t\t\n\t\t\t\t// 记录提交的数据（调试用）\n\t\t\t\t// console.log('提交任务数据:', JSON.stringify(requestData, null, 2));\n\t\t\t\t\n\t\t\t\t// 发起请求\n\t\t\t\tconst response = await PatrolApi.addTask(requestData);\n\t\t\t\t\n\t\t\t\t// 处理响应\n\t\t\t\tif (response && response.code === 0) {\n\t\t\t\t\t// 验证返回数据的完整性\n\t\t\t\t\tif (response.data && response.data._id) {\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查返回的数据是否符合预期\n\t\t\t\t\t\tconst taskData = response.data;\n\t\t\t\t\t\tconst hasRequiredFields = taskData.name && \n\t\t\t\t\t\t\t\t\t\t\t\t  taskData.status !== undefined && \n\t\t\t\t\t\t\t\t\t\t\t\t  taskData.rounds_detail && \n\t\t\t\t\t\t\t\t\t\t\t\t  taskData.overall_stats;\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (!hasRequiredFields) {\n\t\t\t\t\t\t\tconsole.warn('警告：创建的任务数据可能不完整');\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '创建成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 触发刷新任务列表事件\n\t\t\t\t\t\tuni.$emit('refresh-task-list');\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 跳转到详情页\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('警告：创建任务成功但返回数据不完整');\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '创建成功，但数据可能不完整',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\t// 即使数据不完整也触发刷新\n\t\t\t\t\t\tuni.$emit('refresh-task-list');\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(response.message || '创建失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('创建任务失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: error.message || '创建失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.submitting = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 更新预测状态\n\t\tupdatePredictedStatus() {\n\t\t\t// 检查是否有足够的数据进行预测\n\t\t\tif (!this.formData.date || !this.selectedShift || !this.selectedShift.start_time) {\n\t\t\t\tthis.predictedStatus = this.STATUS.NOT_STARTED;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 获取时区偏移（小时）- 确保与后端一致的时区处理\n\t\t\t\tconst timezoneOffset = -(new Date().getTimezoneOffset() / 60); // 例如：东八区为8\n\t\t\t\tconsole.log(`当前时区偏移: UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset}`);\n\t\t\t\t\n\t\t\t\t// 获取班次信息\n\t\t\t\tconst isAcrossDay = this.selectedShift.across_day;\n\t\t\t\tconst shiftStartTime = this.selectedShift.start_time;\n\t\t\t\t\n\t\t\t\t// 计算任务开始时间（使用calculateRoundTime，它会处理日期和时间的组合）\n\t\t\t\tlet startTime = calculateRoundTime(this.formData.date, shiftStartTime);\n\t\t\t\t\n\t\t\t\t// 当前时间（使用本地时区）\n\t\t\t\tconst now = new Date();\n\t\t\t\t\n\t\t\t\t// 调试输出时间比较\n\t\t\t\tconsole.log('预计任务状态计算:');\n\t\t\t\tconsole.log(`- 任务日期: ${this.formData.date}`);\n\t\t\t\tconsole.log(`- 班次开始时间: ${shiftStartTime}`);\n\t\t\t\tconsole.log(`- 完整开始时间: ${startTime.toISOString()}`);\n\t\t\t\tconsole.log(`- 当前时间: ${now.toISOString()}`);\n\t\t\t\tconsole.log(`- 是否跨天: ${isAcrossDay}`);\n\t\t\t\t\n\t\t\t\t// 首先检查是否有轮次信息\n\t\t\t\tif (this.selectedShift.rounds && this.selectedShift.rounds.length > 0) {\n\t\t\t\t\t// 按照轮次时间排序找出第一个轮次\n\t\t\t\t\tconst sortedRounds = [...this.selectedShift.rounds].sort((a, b) => {\n\t\t\t\t\t\t// 获取轮次时间并考虑day_offset\n\t\t\t\t\t\tconst timeA = a.time.split(':').map(Number);\n\t\t\t\t\t\tconst timeB = b.time.split(':').map(Number);\n\t\t\t\t\t\tconst offsetA = a.day_offset || 0;\n\t\t\t\t\t\tconst offsetB = b.day_offset || 0;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 先比较day_offset，再比较时间\n\t\t\t\t\t\tif (offsetA !== offsetB) return offsetA - offsetB;\n\t\t\t\t\t\tif (timeA[0] !== timeB[0]) return timeA[0] - timeB[0];\n\t\t\t\t\t\treturn timeA[1] - timeB[1];\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 获取第一个轮次的时间\n\t\t\t\t\tconst firstRound = sortedRounds[0];\n\t\t\t\t\tif (firstRound && firstRound.time) {\n\t\t\t\t\t\tconst roundTime = firstRound.time;\n\t\t\t\t\t\tconst dayOffset = firstRound.day_offset || 0;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 计算轮次实际开始时间\n\t\t\t\t\t\tconst roundStartTime = calculateRoundTime(this.formData.date, roundTime, dayOffset);\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log(`- 第一个轮次时间: ${roundTime}, 日期偏移: ${dayOffset}`);\n\t\t\t\t\t\tconsole.log(`- 轮次实际开始时间: ${roundStartTime.toISOString()}`);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果轮次开始时间在当前时间之后，任务状态为\"未开始\"\n\t\t\t\t\t\tif (roundStartTime > now) {\n\t\t\t\t\t\t\tconsole.log('> 判断结果: 未开始（轮次开始时间在未来）');\n\t\t\t\t\t\t\tthis.predictedStatus = this.STATUS.NOT_STARTED;\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果没有轮次信息或轮次已开始，则根据班次开始时间判断\n\t\t\t\tif (startTime > now) {\n\t\t\t\t\tconsole.log('> 判断结果: 未开始（开始时间在未来）');\n\t\t\t\t\tthis.predictedStatus = this.STATUS.NOT_STARTED;\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('> 判断结果: 进行中（开始时间已过）');\n\t\t\t\t\tthis.predictedStatus = this.STATUS.IN_PROGRESS;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('计算预计状态时出错:', error);\n\t\t\t\t// 出错时默认为未开始\n\t\t\t\tthis.predictedStatus = this.STATUS.NOT_STARTED;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 根据任务时间计算任务状态\n\t\tcalculateTaskStatus(startTimeISOString) {\n\t\t\t// 首先获取所有已处理过的轮次（有状态值的轮次）\n\t\t\tif (this.processedRounds && this.processedRounds.length > 0) {\n\t\t\t\t// 检查是否有进行中的轮次\n\t\t\t\tconst hasActiveRounds = this.processedRounds.some(round => round.status === 1);\n\t\t\t\tif (hasActiveRounds) {\n\t\t\t\t\tconsole.log('> 计算结果: 进行中（有进行中的轮次）');\n\t\t\t\t\treturn this.STATUS.IN_PROGRESS;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 检查是否所有轮次都已超时\n\t\t\t\tconst allRoundsExpired = this.processedRounds.every(round => round.status === 3);\n\t\t\t\tif (allRoundsExpired) {\n\t\t\t\t\tconsole.log('> 计算结果: 已超时（所有轮次都已超时）');\n\t\t\t\t\treturn this.STATUS.EXPIRED;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 检查是否所有轮次都未开始\n\t\t\t\tconst allRoundsNotStarted = this.processedRounds.every(round => round.status === 0);\n\t\t\t\tif (allRoundsNotStarted) {\n\t\t\t\t\tconsole.log('> 计算结果: 未开始（所有轮次都未开始）');\n\t\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果有些轮次已超时，有些未开始，但没有进行中的轮次\n\t\t\t\t// 我们应该根据最早的未开始轮次判断\n\t\t\t\tconst futureRounds = this.processedRounds.filter(round => round.status === 0);\n\t\t\t\tif (futureRounds.length > 0) {\n\t\t\t\t\tconsole.log('> 计算结果: 进行中（部分轮次已超时，部分未开始）');\n\t\t\t\t\treturn this.STATUS.IN_PROGRESS; // 或者在逻辑上选择其他状态\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 如果没有轮次信息，则回退到基于班次时间的判断\n\t\t\tif (!startTimeISOString) {\n\t\t\t\treturn this.STATUS.NOT_STARTED; // 没有开始时间默认为未开始\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 日期格式转换，确保一致性\n\t\t\t\tconst now = new Date();\n\t\t\t\tconst startTime = new Date(startTimeISOString);\n\t\t\t\t\n\t\t\t\t// 调试日志\n\t\t\t\tconsole.log('提交时状态计算:');\n\t\t\t\tconsole.log(`- 开始时间ISO: ${startTimeISOString}`);\n\t\t\t\tconsole.log(`- 解析后开始时间: ${startTime.toISOString()}`);\n\t\t\t\tconsole.log(`- 当前时间: ${now.toISOString()}`);\n\t\t\t\t\n\t\t\t\t// 获取任务的第一个轮次（通常是最早开始的轮次）\n\t\t\t\tif (this.selectedShift && this.selectedShift.rounds && this.selectedShift.rounds.length > 0) {\n\t\t\t\t\t// 按照轮次时间排序\n\t\t\t\t\tconst sortedRounds = [...this.selectedShift.rounds].sort((a, b) => {\n\t\t\t\t\t\t// 获取轮次时间并考虑day_offset\n\t\t\t\t\t\tconst timeA = a.time.split(':').map(Number);\n\t\t\t\t\t\tconst timeB = b.time.split(':').map(Number);\n\t\t\t\t\t\tconst offsetA = a.day_offset || 0;\n\t\t\t\t\t\tconst offsetB = b.day_offset || 0;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 先比较day_offset，再比较时间\n\t\t\t\t\t\tif (offsetA !== offsetB) return offsetA - offsetB;\n\t\t\t\t\t\tif (timeA[0] !== timeB[0]) return timeA[0] - timeB[0];\n\t\t\t\t\t\treturn timeA[1] - timeB[1];\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 获取第一个轮次的时间\n\t\t\t\t\tconst firstRound = sortedRounds[0];\n\t\t\t\t\tconst roundTime = firstRound.time;\n\t\t\t\t\tconst dayOffset = firstRound.day_offset || 0;\n\t\t\t\t\t\n\t\t\t\t\t// 计算轮次实际开始时间\n\t\t\t\t\tconst roundStartTime = calculateRoundTime(this.formData.date, roundTime, dayOffset);\n\t\t\t\t\t\n\t\t\t\t\tconsole.log(`- 第一个轮次时间: ${roundTime}, 日期偏移: ${dayOffset}`);\n\t\t\t\t\tconsole.log(`- 轮次实际开始时间: ${roundStartTime.toISOString()}`);\n\t\t\t\t\t\n\t\t\t\t\t// 如果轮次开始时间尚未到达，则任务未开始\n\t\t\t\t\tif (roundStartTime > now) {\n\t\t\t\t\t\tconsole.log('> 计算结果: 未开始 (基于轮次时间)');\n\t\t\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果没有轮次信息或轮次已开始，则根据班次开始时间判断\n\t\t\t\tif (startTime > now) {\n\t\t\t\t\tconsole.log('> 计算结果: 未开始 (基于班次时间)');\n\t\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 任务开始时间在当前时间之前或等于当前时间，则任务进行中\n\t\t\t\tconsole.log('> 计算结果: 进行中');\n\t\t\t\treturn this.STATUS.IN_PROGRESS;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('计算任务状态时出错:', error);\n\t\t\t\t// 出错时默认为未开始\n\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取状态图标\n\t\tgetStatusIcon(status) {\n\t\t\tswitch(status) {\n\t\t\t\tcase this.STATUS.NOT_STARTED: return 'info-filled';\n\t\t\t\tcase this.STATUS.IN_PROGRESS: return 'reload';\n\t\t\t\tcase this.STATUS.COMPLETED: return 'checkmarkempty';\n\t\t\t\tcase this.STATUS.EXPIRED: return 'closeempty';\n\t\t\t\tcase this.STATUS.CANCELLED: return 'minus';\n\t\t\t\tdefault: return 'info';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取状态文本\n\t\tgetStatusText(status) {\n\t\t\tswitch(status) {\n\t\t\t\tcase this.STATUS.NOT_STARTED: return '未开始';\n\t\t\t\tcase this.STATUS.IN_PROGRESS: return '进行中';\n\t\t\t\tcase this.STATUS.COMPLETED: return '已完成';\n\t\t\t\tcase this.STATUS.EXPIRED: return '已超时';\n\t\t\t\tcase this.STATUS.CANCELLED: return '已取消';\n\t\t\t\tdefault: return '未知状态';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取状态解释更新\n\t\tgetStatusExplanation(status) {\n\t\t\tconst now = new Date();\n\t\t\t// 手动格式化时间，避免时区信息显示\n\t\t\tconst hours = now.getHours().toString().padStart(2, '0');\n\t\t\tconst minutes = now.getMinutes().toString().padStart(2, '0');\n\t\t\tconst formattedNow = `${hours}:${minutes}`;\n\t\t\t\n\t\t\tswitch(status) {\n\t\t\t\tcase this.STATUS.NOT_STARTED:\n\t\t\t\t\tif (this.selectedShift && this.selectedShift.start_time) {\n\t\t\t\t\t\treturn `当前时间 ${formattedNow}，任务将在 ${this.selectedShift.start_time} 开始`;\n\t\t\t\t\t}\n\t\t\t\t\treturn '任务开始时间尚未到达';\n\t\t\t\tcase this.STATUS.IN_PROGRESS:\n\t\t\t\t\tif (this.selectedShift && this.selectedShift.start_time) {\n\t\t\t\t\t\treturn `当前时间 ${formattedNow}，已超过任务开始时间 ${this.selectedShift.start_time}`;\n\t\t\t\t\t}\n\t\t\t\t\treturn '当前时间已超过任务开始时间';\n\t\t\t\tdefault:\n\t\t\t\t\treturn '';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 返回上一页\n\t\tnavigateBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 刷新路线点位数据\n\t\tasync refreshRoutePointsData() {\n\t\t\tif (!this.formData.route_id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先选择路线',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '刷新中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 获取最新的路线点位数据\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-route',\n\t\t\t\t\taction: 'getRouteDetail',\n\t\t\t\t\tdata: { \n\t\t\t\t\t\tparams: { \n\t\t\t\t\t\t\troute_id: this.formData.route_id,\n\t\t\t\t\t\t\twith_points: true,\n\t\t\t\t\t\t\twith_detail: true,\n\t\t\t\t\t\t\tinclude_point_details: true,\n\t\t\t\t\t\t\tforce_refresh: true\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\tconst routeData = res.data;\n\t\t\t\t\t// 更新路线点位数据\n\t\t\t\t\tthis.selectedRoute = {\n\t\t\t\t\t\t...this.selectedRoute,\n\t\t\t\t\t\tpointsDetail: routeData.pointsDetail || []\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 标记点位数据已刷新\n\t\t\t\t\tthis.pointsRefreshed = true;\n\t\t\t\t\t\n\t\t\t\t\t// 显示成功提示\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '点位数据已更新',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(res.message || '获取最新点位数据失败');\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('刷新点位数据失败:', e);\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '刷新点位数据失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 过滤用户选项\n\t\tfilterUserOptions() {\n\t\t\tconst searchText = this.searchUserName.toLowerCase();\n\t\t\tif (!searchText) {\n\t\t\t\t// 如果搜索框为空，显示所有用户\n\t\t\t\tthis.filteredUsers = [...this.userOptions];\n\t\t\t} else {\n\t\t\t\tthis.filteredUsers = this.userOptions.filter(user => \n\t\t\t\t\tuser.nickname && user.nickname.toLowerCase().includes(searchText) && \n\t\t\t\t\t(user._id === '' || !user.nickname.startsWith('匿名')) // 确保搜索结果也遵循匿名用户过滤规则，但保留默认选项\n\t\t\t\t);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 选择用户\n\t\tselectUser(index) {\n\t\t\tconst user = this.filteredUsers[index];\n\t\t\tif (user && user._id) {\n\t\t\t\tthis.formData.user_id = user._id;\n\t\t\t\tthis.selectedUser = user;\n\t\t\t} else {\n\t\t\t\tthis.formData.user_id = '';\n\t\t\t\tthis.selectedUser = null;\n\t\t\t}\n\t\t\tthis.showUserSelect = false;\n\t\t},\n\t\t\n\t\t// 隐藏用户选择器\n\t\thideUserSelect() {\n\t\t\tthis.showUserSelect = false;\n\t\t},\n\t}\n};\n</script>\n\n<style lang=\"scss\">\n.add-task-container {\n\tmin-height: 100vh;\n\tbackground-color: #F2F3F5;\n}\n\n.header {\n\tpadding: 30rpx;\n\tbackground-color: #1677FF;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #FFFFFF;\n}\n\n.form-container {\n\tpadding: 20rpx;\n}\n\n.form-section {\n\tmargin: 10rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.card-header {\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid #EAEAEA;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #2C3E50;\n\tposition: relative;\n\tpadding-left: 20rpx;\n}\n\n.section-title:before {\n\tcontent: '';\n\tposition: absolute;\n\tleft: 0;\n\ttop: 50%;\n\ttransform: translateY(-50%);\n\twidth: 6rpx;\n\theight: 28rpx;\n\tbackground-color: #1677FF;\n\tborder-radius: 6rpx;\n}\n\n.form-item {\n\tpadding: 20rpx;\n\tborder-bottom: 1px solid #F5F5F5;\n\t\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n}\n\n.form-label {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tcolor: #666666;\n\tmargin-bottom: 16rpx;\n\t\n\t&.required::before {\n\t\tcontent: '*';\n\t\tcolor: #FF4D4F;\n\t\tmargin-right: 8rpx;\n\t}\n}\n\n.form-input {\n\theight: 80rpx;\n\tbackground-color: #F9F9F9;\n\tborder: 1px solid #EEEEEE;\n\tborder-radius: 8rpx;\n\tpadding: 0 20rpx;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\ttransition: all 0.2s ease;\n\t\n\t&:focus {\n\t\tborder-color: #1677FF;\n\t\tbackground-color: #FFFFFF;\n\t}\n}\n\n.form-picker {\n\twidth: 100%;\n}\n\n.picker-value {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\twidth: 100%;\n\theight: 80rpx;\n\tborder: 1px solid #EEEEEE;\n\tborder-radius: 8rpx;\n\tpadding: 0 20rpx;\n\tfont-size: 28rpx;\n\tbox-sizing: border-box;\n\tbackground-color: #F9F9F9;\n\ttransition: all 0.2s ease;\n}\n\n.shift-info {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.round-tags {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 12rpx;\n\tmargin-top: 8rpx;\n}\n\n.round-tag {\n\tbackground-color: #E6F7FF;\n\tcolor: #1677FF;\n\tfont-size: 24rpx;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tborder: 1px solid rgba(22, 119, 255, 0.2);\n}\n\n.points-list {\n\tbackground-color: #F9F9F9;\n\tborder-radius: 8rpx;\n\tpadding: 12rpx;\n\tmargin-top: 8rpx;\n\tmax-height: 400rpx;\n\toverflow-y: auto;\n}\n\n.point-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx;\n\tborder-bottom: 1px solid rgba(238, 238, 238, 0.6);\n}\n\n.point-item:last-child {\n\tborder-bottom: none;\n}\n\n.point-index {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tborder-radius: 50%;\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tfont-size: 24rpx;\n\tmargin-right: 20rpx;\n\tflex-shrink: 0;\n}\n\n.point-name {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tflex: 1;\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n.point-range {\n\tfont-size: 24rpx;\n\tcolor: #1677FF;\n\tbackground-color: #E6F7FF;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 999rpx;\n\tmargin-left: 10rpx;\n\tflex-shrink: 0;\n}\n\n.points-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 10rpx;\n}\n\n.refresh-points-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tfont-size: 24rpx;\n\tcolor: #1677FF;\n}\n\n.points-info {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tmargin-top: 10rpx;\n}\n\n.info-text {\n\tfont-size: 24rpx;\n\tcolor: #666666;\n}\n\n.success {\n\tcolor: #52C41A;\n}\n\n.user-info {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: #F9F9F9;\n\tborder-radius: 8rpx;\n\tpadding: 16rpx;\n\tmargin-top: 8rpx;\n\tborder: 1px solid rgba(238, 238, 238, 0.8);\n}\n\n.user-avatar {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tmargin-right: 16rpx;\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n.user-details {\n\tflex: 1;\n}\n\n.user-name {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tfont-weight: bold;\n\tdisplay: block;\n\tmargin-bottom: 8rpx;\n}\n\n.user-role {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tdisplay: block;\n}\n\n.status-info {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.status-desc {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tpadding: 8rpx 0;\n}\n\n.status-preview {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: flex-start;\n}\n\n.status-tag {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tfont-size: 24rpx;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tmargin-bottom: 8rpx;\n\t\n\t&.status-0 {\n\t\tbackground-color: #E6F7FF;\n\t\tcolor: #1677FF;\n\t\tborder: 1px solid rgba(22, 119, 255, 0.2);\n\t}\n\t\n\t&.status-1 {\n\t\tbackground-color: #F0FFF0;\n\t\tcolor: #52C41A;\n\t\tborder: 1px solid rgba(82, 196, 26, 0.2);\n\t}\n\t\n\t&.status-2 {\n\t\tbackground-color: #F6FFED;\n\t\tcolor: #52C41A;\n\t\tborder: 1px solid rgba(82, 196, 26, 0.2);\n\t}\n\t\n\t&.status-3 {\n\t\tbackground-color: #FFF1F0;\n\t\tcolor: #F5222D;\n\t\tborder: 1px solid rgba(245, 34, 45, 0.2);\n\t}\n\t\n\t&.status-4 {\n\t\tbackground-color: #F5F5F5;\n\t\tcolor: #999999;\n\t\tborder: 1px solid rgba(0, 0, 0, 0.1);\n\t}\n}\n\n.status-explanation {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tpadding: 4rpx 0;\n}\n\n.submit-section {\n\tmargin: 40rpx 20rpx;\n\tpadding: 20rpx 30rpx;\n\tdisplay: flex;\n\tflex-direction: row;\n\tjustify-content: space-between;\n\tgap: 20rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\n}\n\n.primary-btn, .cancel-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tcolor: #FFFFFF;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\t\n\t&:after {\n\t\tborder: none;\n\t}\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n}\n\n.primary-btn {\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n}\n\n.cancel-btn {\n\tbackground-color: #F5F5F5;\n\tcolor: #666666;\n\tborder: 1rpx solid #EEEEEE;\n}\n\n/* 图标字体 */\n.iconfont {\n\tfont-family: 'iconfont';\n}\n\n.icon-calendar:before {\n\tcontent: '\\e6dc';\n\tcolor: #1677FF;\n}\n\n.icon-down:before {\n\tcontent: '\\e61a';\n\tcolor: #999999;\n}\n\n.timezone-indicator {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 22rpx;\n\tcolor: #999999;\n\tmargin-top: 8rpx;\n\t\n\tuni-icons {\n\t\tmargin-right: 8rpx;\n\t}\n}\n\n/* 跨天班次标记样式 */\n.cross-day-tag {\n\tfont-size: 22rpx;\n\tcolor: #FA8C16;\n\tbackground-color: rgba(250, 140, 22, 0.1);\n\tpadding: 2rpx 8rpx;\n\tborder-radius: 4rpx;\n\tmargin-left: 8rpx;\n}\n\n/* 次日轮次标记样式 */\n.next-day-badge {\n\tdisplay: inline-block;\n\tfont-size: 22rpx;\n\tpadding: 2rpx 8rpx;\n\tbackground-color: #FA8C16;\n\tcolor: #FFFFFF;\n\tborder-radius: 20rpx;\n\tmargin-left: 6rpx;\n}\n\n.shift-time-info {\n\tmargin-bottom: 12rpx;\n}\n\n.time-range {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tfont-size: 26rpx;\n\tcolor: #666666;\n\tpadding: 8rpx 0;\n}\n\n/* 确保加载状态按钮的文字和图标为白色 */\nbutton[loading]::before {\n\tcolor: #FFFFFF !important;\n}\n\nbutton[loading]::after {\n\tborder-color: #FFFFFF transparent #FFFFFF transparent !important;\n}\n\n/* 自定义人员选择器 */\n.custom-select {\n\tposition: relative;\n\twidth: 100%;\n}\n\n.user-select-modal {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tz-index: 9999;\n}\n\n.user-select-popup {\n\twidth: 90%;\n\tmax-width: 650rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);\n}\n\n/* #ifdef H5 */\n/* H5 端的响应式样式 */\n@media screen and (min-width: 1200px) {\n    .user-select-popup {\n        max-width: 600px;\n    }\n}\n\n@media screen and (min-width: 992px) and (max-width: 1199px) {\n    .user-select-popup {\n        max-width: 560px;\n    }\n}\n\n@media screen and (min-width: 768px) and (max-width: 991px) {\n    .user-select-popup {\n        max-width: 500px;\n    }\n}\n\n@media screen and (min-width: 576px) and (max-width: 767px) {\n    .user-select-popup {\n        max-width: 450px;\n    }\n}\n\n@media screen and (max-width: 575px) {\n    .user-select-popup {\n        width: 92%;\n        max-width: none;\n    }\n}\n/* #endif */\n\n.user-select-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\theight: 100%;\n\tmax-height: 80vh;\n}\n\n.user-select-header {\n\tpadding: 20rpx;\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #2C3E50;\n\tborder-bottom: 1px solid #EEEEEE;\n\ttext-align: center;\n}\n\n.user-search-header {\n\tpadding: 20rpx;\n\tborder-bottom: 1px solid #EEEEEE;\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: #F9F9F9;\n}\n\n.user-search-input {\n\tflex: 1;\n\theight: 70rpx;\n\tfont-size: 28rpx;\n\tpadding: 0 10rpx;\n\tbackground-color: transparent;\n}\n\n.user-select-list {\n\tflex: 1;\n\toverflow-y: auto;\n}\n\n/* #ifdef MP-WEIXIN */\n/* 在微信小程序环境下隐藏滚动条 */\n.user-select-list::-webkit-scrollbar {\n\tdisplay: none;\n\twidth: 0;\n\theight: 0;\n\tbackground: transparent;\n}\n\n.user-select-list {\n\tscrollbar-width: none; /* Firefox */\n\t-ms-overflow-style: none; /* IE and Edge */\n}\n/* #endif */\n\n/* #ifndef MP-WEIXIN */\n/* 非微信小程序环境下显示滚动条 */\n.user-select-list::-webkit-scrollbar {\n\twidth: 6px;\n\theight: 6px;\n}\n\n.user-select-list::-webkit-scrollbar-thumb {\n\tbackground: rgba(0, 0, 0, 0.2);\n\tborder-radius: 3px;\n}\n\n.user-select-list::-webkit-scrollbar-thumb:hover {\n\tbackground: rgba(0, 0, 0, 0.3);\n}\n\n.user-select-list {\n\tscrollbar-width: thin;\n\tscrollbar-color: rgba(0, 0, 0, 0.2) transparent;\n}\n/* #endif */\n\n.user-select-item {\n\tpadding: 16rpx 20rpx;\n\tborder-bottom: 1px solid #F5F5F5;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\t\n\t&.first-item {\n\t\tbackground-color: #F9F9F9;\n\t}\n}\n\n.user-select-item:active {\n\tbackground-color: #F9F9F9;\n}\n\n.select-radio {\n\tmargin-left: 10rpx;\n}\n\n.user-select-footer {\n\tpadding: 20rpx;\n\tborder-top: 1px solid #EEEEEE;\n}\n\n.user-select-close {\n\theight: 80rpx;\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n\tborder-radius: 40rpx;\n\tfont-size: 28rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\t\n\t&:after {\n\t\tborder: none;\n\t}\n}\n\n.user-avatar-small {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tmargin-right: 16rpx;\n\tborder: 1px solid #EEEEEE;\n}\n\n.user-item-content {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.user-item-details {\n\tdisplay: flex;\n\tflex-direction: column;\n\tflex: 1;\n}\n\n.user-item-name {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tfont-weight: 500;\n}\n\n.user-item-role {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tmargin-top: 4rpx;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571661686\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/honor_pkg/admin/add-record.vue?60a5", "webpack:///D:/Xwzc/pages/honor_pkg/admin/add-record.vue?4850", "webpack:///D:/Xwzc/pages/honor_pkg/admin/add-record.vue?a017", "webpack:///D:/Xwzc/pages/honor_pkg/admin/add-record.vue?8cbb", "uni-app:///pages/honor_pkg/admin/add-record.vue", "webpack:///D:/Xwzc/pages/honor_pkg/admin/add-record.vue?de7c", "webpack:///D:/Xwzc/pages/honor_pkg/admin/add-record.vue?a17e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "AvatarPicker", "name", "data", "loading", "saving", "loadingText", "isEditMode", "recordId", "formData", "userName", "department", "userAvatar", "honorTypeId", "batchId", "reason", "isFeatured", "images", "departmentOptions", "honorTypeOptions", "batchOptions", "selectedHonorType", "selected<PERSON><PERSON>", "showBatchSelector", "batchSearchKeyword", "showOlderBatchCount", "showDepartmentSelector", "newDepartmentName", "showHonorTypeSelector", "computed", "filteredBatchOptions", "batch", "recentBatches", "currentMonthBatches", "meetingDate", "<PERSON><PERSON><PERSON><PERSON>", "threeMonthsAgo", "filteredOlderBatches", "onLoad", "recordData", "console", "methods", "initializeData", "Promise", "uni", "title", "icon", "loadDepartmentList", "uniCloud", "action", "res", "value", "text", "loadHonorTypeList", "filter", "map", "color", "id", "loadBatchList", "currentDate", "currentMonth", "currentYear", "_id", "type", "setSelectedHonorType", "setSelectedBatch", "onHonorTypeChange", "onBatchChange", "onFeaturedChange", "handleAvatarChange", "duration", "onAvatarUploadSuccess", "size", "originalSize", "compressionRatio", "onAvatarUploadError", "chooseImages", "remainCount", "count", "sizeType", "sourceType", "uploadUtils", "compress", "onProgress", "result", "url", "cloudPath", "successCount", "failCount", "chooseImageAsync", "options", "success", "fail", "removeImage", "image", "fileList", "saveRecord", "setTimeout", "validateForm", "goBack", "content", "has<PERSON><PERSON><PERSON>", "openDepartmentSelector", "closeDepartmentSelector", "selectDepartment", "addNewDepartment", "departmentName", "exists", "deleteDepartment", "index", "openHonorTypeSelector", "closeHonorTypeSelector", "selectHonorType", "openBatchSelector", "closeBatchSelector", "onBatchSearch", "selectBatch", "showMoreOlderBatches", "formatBatchDate", "getBatchTypeText", "quickCreateBatch", "previewImage", "urls", "current", "indicator"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5IA;AAAA;AAAA;AAAA;AAAomB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmdxnB;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MACAC;MAEA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;MACA;MAEA;MACA;QAAA,OACAC;MAAA,EACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA;QACA;QACA,sDACAC;MACA;IACA;IAEA;IACAC;MACA;MACAC;MAEA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;EAEAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;gBACA;gBACA;kBACAC;kBACA;;kBAEA;kBACA;oBACA;kBACA;;kBAEA;kBACA,+DACA,iBACAA;oBACA3B;kBAAA,EACA;;kBACA;gBACA;kBACA4B;gBACA;cACA;cAAA;cAAA,OAEA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC,aACA,6BACA,4BACA,uBACA;cAAA;gBAEA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBACA9C;kBACAC;oBACA8C;kBACA;gBACA;cAAA;gBALAC;gBAOA;kBACA/C;kBACA;oBACA;sBAAA;wBACAgD;wBACAC;sBACA;oBAAA;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;gBACA;gBACA,4BACA;kBAAAW;kBAAAC;gBAAA,GACA;kBAAAD;kBAAAC;gBAAA,GACA;kBAAAD;kBAAAC;gBAAA,GACA;kBAAAD;kBAAAC;gBAAA,EACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAL;kBACA9C;kBACAC;oBACA8C;kBACA;gBACA;cAAA;gBALAC;gBAOA;kBACA/C;kBACA;oBACA;oBACA,+BACAmD;sBAAA;oBAAA,GACAC;sBAAA;wBACAJ;wBACAC;wBACAI;wBACArD;sBACA;oBAAA;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAqC;gBACA;gBACA,2BACA;kBAAAW;kBAAAC;kBAAAI;kBAAArD;oBAAAsD;oBAAAvD;oBAAAsD;kBAAA;gBAAA,GACA;kBAAAL;kBAAAC;kBAAAI;kBAAArD;oBAAAsD;oBAAAvD;oBAAAsD;kBAAA;gBAAA,GACA;kBAAAL;kBAAAC;kBAAAI;kBAAArD;oBAAAsD;oBAAAvD;oBAAAsD;kBAAA;gBAAA,EACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAV;kBACA9C;kBACAC;oBACA8C;kBACA;gBACA;cAAA;gBALAC;gBAOA;kBACA/C;kBACA;oBACA;sBAAA;wBACAgD;wBACAC;wBACAlB;wBACA/B;sBACA;oBAAA;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAqC;gBACA;gBACAmB;gBACAC;gBACAC;gBAEA,uBACA;kBACAV;kBACAC;kBACAlB;kBACA/B;oBACA2D;oBACA5D;oBACA6D;oBACA7B;kBACA;gBACA,EACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA8B;MAAA;MACA;QACA;UAAA;QAAA;QACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;UAAA;QAAA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA7B;MACA;QACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACAI;UACAC;UACAC;UACAwB;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA/B;;MAEA;MACA;QACA;QACAA;;QAEA;QACA;UACAA;YACAgC;YACAC;YACAC;UACA;QACA;MACA;MAEA9B;QACAC;QACAC;MACA;IACA;IAEA;IACA6B;MACAnC;MACAI;QACAC;QACAC;MACA;IACA;IAEA;IACA8B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA;gBAAA,OAGA;kBACAC;kBACAC;kBAAA;kBACAC;gBACA;cAAA;gBAJA9B;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAIAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAN;kBAAAC;gBAAA;;gBAEA;gBAAA;gBAAA;kBAAA,mDACA;gBAAA;cAAA;gBAAAoC;gBAAA;gBAAA,OAEAA;kBACAC;kBACAC;oBACA;sBACAvC;wBAAAC;sBAAA;oBACA;sBACAD;wBAAAC;sBAAA;oBACA;kBACA;gBACA;cAAA;gBATAuC;gBAWAxC;gBAAA,KAEAwC;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;kBACA;oBACA;sBACA3B;sBACA4B;sBACAC;oBACA;kBACA;gBACA;gBAEAC;gBACAC;gBAEA;kBACA5C;oBACAC;oBACAC;kBACA;gBACA;kBACAF;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAF;gBACAJ;gBACAI;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA2C;MACA;QACA7C,gDACA8C;UACAC;UACAC;QAAA,GACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAlD;kBAAAC;gBAAA;;gBAEA;gBAAA;gBAAA,OACAG;kBACA9C;kBACAC;oBACA4F;kBACA;gBACA;cAAA;gBAAA;gBALAX;gBAOAxC;gBAAA,MAEAwC;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACAxC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;gBACAJ;gBACAI;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAGA/C;gBACA9C;kBACAO;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACA;kBACAd;gBACA;gBAAA;gBAAA,OAEA6C;kBACA9C;kBACAC;oBAAA8C;oBAAA9C;kBAAA;gBACA;cAAA;gBAHA+C;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAN;kBACAC;kBACAC;kBACAwB;gBACA;gBAEA2B;kBACArD;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoD;MACA;QACAtD;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAqD;MACA;QACAvD;UACAC;UACAuD;UACAT;YACA;cACA/C;YACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAyD;MACA,wCACA,+BACA,6BACA,yBACA;IACA;IAEA;IAEA;IACAC;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA9D;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACA6D;kBAAA;gBAAA;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA/D;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACA;kBACAK;kBACAC;gBACA;;gBAEA;gBACA;gBACA;gBACA;gBAEAR;kBACAC;kBACAC;kBACAwB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAsC;MAAA;MACA;QACAhE;UACAC;UACAC;QACA;QACA;MACA;MAEAF;QACAC;QACAuD;QACAT;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAzC;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA;oBAAA,OAGAF;sBACA9C;sBACAC;wBACA8C;wBACA9C;0BACAD;wBACA;sBACA;oBACA;kBAAA;oBARAkF;oBAAA,MAUAA;sBAAA;sBAAA;oBAAA;oBACAxC;sBACAC;sBACAC;sBACAwB;oBACA;oBAAA;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAKA;oBACA9B;kBAAA;oBAGA;oBACAqE;sBAAA;oBAAA;oBACA;sBACA;oBACA;;oBAEA;oBACA;sBACA;oBACA;oBAEAjE;sBACAC;sBACAC;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAgE;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAIA;IACAC;MACA;MACA5E;QACAyC;MACA;IACA;IAEA;IACAoC;MACA;QACA7E;UACA8E;YAAA;UAAA;UACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9vCA;AAAA;AAAA;AAAA;AAAuqC,CAAgB,6oCAAG,EAAC,C;;;;;;;;;;;ACA3rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/honor_pkg/admin/add-record.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/honor_pkg/admin/add-record.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./add-record.vue?vue&type=template&id=0c706de4&scoped=true&\"\nvar renderjs\nimport script from \"./add-record.vue?vue&type=script&lang=js&\"\nexport * from \"./add-record.vue?vue&type=script&lang=js&\"\nimport style0 from \"./add-record.vue?vue&type=style&index=0&id=0c706de4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c706de4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/honor_pkg/admin/add-record.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add-record.vue?vue&type=template&id=0c706de4&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.formData.reason.length\n  var g1 = _vm.formData.images.length\n  var g2 = _vm.formData.images.length\n  var l0 = g2 > 0 ? _vm.formData.images.slice(0, 3) : null\n  var g3 = g2 > 0 ? _vm.formData.images.length : null\n  var g4 = g2 > 0 && g3 > 3 ? _vm.formData.images.length : null\n  var g5 = _vm.showDepartmentSelector ? _vm.newDepartmentName.trim() : null\n  var g6 = _vm.showDepartmentSelector ? _vm.departmentOptions.length : null\n  var l1 = _vm.showDepartmentSelector\n    ? _vm.__map(_vm.departmentOptions, function (dept, __i0__) {\n        var $orig = _vm.__get_orig(dept)\n        var g7 = _vm.departmentOptions.length\n        return {\n          $orig: $orig,\n          g7: g7,\n        }\n      })\n    : null\n  var g8 = _vm.showBatchSelector ? _vm.recentBatches.length : null\n  var l2 =\n    _vm.showBatchSelector && g8 > 0\n      ? _vm.__map(_vm.recentBatches, function (batch, __i2__) {\n          var $orig = _vm.__get_orig(batch)\n          var m0 = _vm.formatBatchDate(batch.data.meetingDate)\n          var m1 = _vm.getBatchTypeText(batch.data.type)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n      : null\n  var g9 = _vm.showBatchSelector ? _vm.currentMonthBatches.length : null\n  var l3 =\n    _vm.showBatchSelector && g9 > 0\n      ? _vm.__map(_vm.currentMonthBatches, function (batch, __i3__) {\n          var $orig = _vm.__get_orig(batch)\n          var m2 = _vm.formatBatchDate(batch.data.meetingDate)\n          var m3 = _vm.getBatchTypeText(batch.data.type)\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n          }\n        })\n      : null\n  var g10 = _vm.showBatchSelector ? _vm.filteredOlderBatches.length : null\n  var g11 = _vm.showBatchSelector && g10 > 0 ? _vm.olderBatches.length : null\n  var l4 =\n    _vm.showBatchSelector && g10 > 0\n      ? _vm.__map(_vm.filteredOlderBatches, function (batch, __i4__) {\n          var $orig = _vm.__get_orig(batch)\n          var m4 = _vm.formatBatchDate(batch.data.meetingDate)\n          var m5 = _vm.getBatchTypeText(batch.data.type)\n          return {\n            $orig: $orig,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  var g12 = _vm.showBatchSelector && g10 > 0 ? _vm.olderBatches.length : null\n  var g13 =\n    _vm.showBatchSelector && g10 > 0 && g12 > _vm.showOlderBatchCount\n      ? _vm.olderBatches.length\n      : null\n  var g14 = _vm.showBatchSelector ? _vm.filteredBatchOptions.length : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, index) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        index = _temp2.index\n      var _temp, _temp2\n      return _vm.previewImage(index)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        l1: l1,\n        g8: g8,\n        l2: l2,\n        g9: g9,\n        l3: l3,\n        g10: g10,\n        g11: g11,\n        l4: l4,\n        g12: g12,\n        g13: g13,\n        g14: g14,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add-record.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add-record.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"add-record-container\">\n    <!-- 头部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"18\" color=\"#FFFFFF\"></uni-icons>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"title\">{{ isEditMode ? '编辑表彰记录' : '添加表彰记录' }}</text>\n          <text class=\"subtitle\">记录管理 · 数据录入</text>\n        </view>\n        <view class=\"header-actions\">\n          <button class=\"save-btn\" @click=\"saveRecord\" :disabled=\"saving\">\n            {{ saving ? '保存中...' : (isEditMode ? '更新' : '保存') }}\n          </button>\n        </view>\n      </view>\n    </view>\n\n    <scroll-view class=\"content-scroll\" scroll-y>\n      <view class=\"form-container\">\n        <!-- 被表彰人信息 -->\n        <view class=\"form-section\">\n          <view class=\"section-header\">\n            <uni-icons type=\"person-filled\" size=\"20\" color=\"#3a86ff\"></uni-icons>\n            <text class=\"section-title\">被表彰人信息</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"form-label\">姓名 <text class=\"required\">*</text></view>\n            <uni-easyinput \n              v-model=\"formData.userName\"\n              placeholder=\"请输入被表彰人姓名\"\n              :clearable=\"true\"\n              class=\"custom-input\"\n            ></uni-easyinput>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">部门</text>\n            <view class=\"custom-department-picker\" @click=\"openDepartmentSelector\">\n              <view class=\"picker-display\">\n                <text v-if=\"formData.department\" class=\"selected-text\">{{ formData.department }}</text>\n                <text v-else class=\"placeholder-text\">请选择部门</text>\n                <uni-icons type=\"bottom\" size=\"16\" color=\"#999\"></uni-icons>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">头像</text>\n            <avatar-picker \n              ref=\"avatarPicker\" \n              v-model=\"formData.userAvatar\"\n              @change=\"handleAvatarChange\"\n            ></avatar-picker>\n          </view>\n        </view>\n\n        <!-- 表彰信息 -->\n        <view class=\"form-section\">\n          <view class=\"section-header\">\n            <uni-icons type=\"medal-filled\" size=\"20\" color=\"#3a86ff\"></uni-icons>\n            <text class=\"section-title\">表彰信息</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"form-label\">荣誉类型 <text class=\"required\">*</text></view>\n            <view class=\"custom-honor-type-picker\" @click=\"openHonorTypeSelector\">\n              <view class=\"picker-display\">\n                <text v-if=\"selectedHonorType\" class=\"selected-text\">{{ selectedHonorType.name }}</text>\n                <text v-else class=\"placeholder-text\">请选择荣誉类型</text>\n                <uni-icons type=\"bottom\" size=\"16\" color=\"#999\"></uni-icons>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"form-label\">表彰批次 <text class=\"required\">*</text></view>\n            <!-- 智能批次选择器 -->\n            <view class=\"custom-batch-picker\" @click=\"openBatchSelector\">\n              <view class=\"picker-display\">\n                <text v-if=\"selectedBatch\" class=\"selected-text\">{{ selectedBatch.name }}</text>\n                <text v-else class=\"placeholder-text\">请选择表彰批次</text>\n                <uni-icons type=\"bottom\" size=\"16\" color=\"#999\"></uni-icons>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"form-label\">表彰原因 <text class=\"required\">*</text></view>\n            <uni-easyinput \n              v-model=\"formData.reason\"\n              type=\"textarea\"\n              placeholder=\"请描述表彰原因和具体事迹\"\n              :auto-height=\"true\"\n              maxlength=\"500\"\n              class=\"custom-textarea\"\n            ></uni-easyinput>\n            <view class=\"char-count\">{{ formData.reason.length }}/500</view>\n          </view>\n        </view>\n\n        <!-- 附加设置 -->\n        <view class=\"form-section\">\n          <view class=\"section-header\">\n            <uni-icons type=\"gear-filled\" size=\"20\" color=\"#3a86ff\"></uni-icons>\n            <text class=\"section-title\">附加设置</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"switch-item\">\n              <text class=\"switch-label\">设为精选表彰</text>\n              <switch \n                :checked=\"formData.isFeatured\"\n                @change=\"onFeaturedChange\"\n                color=\"#3a86ff\"\n              ></switch>\n            </view>\n            <view class=\"form-desc\">精选表彰将在荣誉展厅中优先展示</view>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">相关图片</text>\n            <view class=\"image-picker\">\n              <view class=\"image-list\">\n                <view \n                  class=\"image-item\" \n                  v-for=\"(image, index) in formData.images\" \n                  :key=\"index\"\n                >\n                  <image \n                    :src=\"image.url\" \n                    class=\"image-preview\"\n                    mode=\"aspectFill\"\n                    @click=\"previewImage(index)\"\n                  ></image>\n                  <view class=\"image-remove\" @click.stop=\"removeImage(index)\">\n                    <uni-icons type=\"close\" size=\"14\" color=\"#FFFFFF\"></uni-icons>\n                  </view>\n                </view>\n                <view class=\"image-add\" @click=\"chooseImages\" v-if=\"formData.images.length < 5\">\n                  <uni-icons type=\"plus\" size=\"28\" color=\"#8a94a6\"></uni-icons>\n                  <text class=\"add-text\">添加图片</text>\n                </view>\n              </view>\n              <view class=\"image-tip\">最多可上传5张图片，支持JPG/PNG格式</view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 预览区域 -->\n        <view class=\"form-section\">\n          <view class=\"section-header\">\n            <uni-icons type=\"eye\" size=\"20\" color=\"#3a86ff\"></uni-icons>\n            <text class=\"section-title\">预览效果</text>\n          </view>\n          \n          <view class=\"preview-card\">\n            <view class=\"preview-header\">\n              <image \n                :src=\"formData.userAvatar || '/static/user/default-avatar.png'\" \n                class=\"preview-avatar\"\n                mode=\"aspectFill\"\n              ></image>\n              <view class=\"preview-info\">\n                <text class=\"preview-name\">{{ formData.userName || '姓名' }}</text>\n                <text class=\"preview-dept\">{{ formData.department || '部门' }}</text>\n              </view>\n              <view class=\"preview-badge\" v-if=\"formData.isFeatured\">\n                <uni-icons type=\"star-filled\" size=\"16\" color=\"#f59e0b\"></uni-icons>\n                <text class=\"badge-text\">精选</text>\n              </view>\n            </view>\n            \n            <view class=\"preview-type\" v-if=\"selectedHonorType\">\n              <view \n                class=\"type-tag\" \n                :style=\"{ backgroundColor: selectedHonorType.color || '#3a86ff' }\"\n              >\n                {{ selectedHonorType.name }}\n              </view>\n            </view>\n            \n            <view class=\"preview-reason\">\n              {{ formData.reason || '表彰原因将在这里显示...' }}\n            </view>\n            \n            <view class=\"preview-images\" v-if=\"formData.images.length > 0\">\n              <image \n                v-for=\"(image, index) in formData.images.slice(0, 3)\" \n                :key=\"index\"\n                :src=\"image.url\" \n                class=\"preview-image\"\n                mode=\"aspectFill\"\n                @click=\"previewImage(index)\"\n              ></image>\n              <view class=\"more-images\" v-if=\"formData.images.length > 3\">\n                +{{ formData.images.length - 3 }}\n              </view>\n            </view>\n            \n            <view class=\"preview-batch\" v-if=\"selectedBatch\">\n              <text class=\"batch-text\">{{ selectedBatch.name }}</text>\n              <text class=\"batch-date\">{{ selectedBatch.meetingDate }}</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 底部安全区域 -->\n        <view class=\"bottom-safe-area\"></view>\n      </view>\n    </scroll-view>\n\n    <!-- 部门选择弹窗 -->\n    <view v-if=\"showDepartmentSelector\" class=\"selector-modal-overlay\" @click=\"closeDepartmentSelector\">\n      <view class=\"selector-modal-content\" @click.stop>\n        <view class=\"selector-modal-header\">\n          <view class=\"header-left\">\n            <text class=\"selector-modal-title\">选择部门</text>\n            <text class=\"selector-modal-subtitle\">部门数据来自已有荣誉记录</text>\n          </view>\n          <view class=\"selector-modal-close\" @click=\"closeDepartmentSelector\">\n            <uni-icons type=\"close\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n          </view>\n        </view>\n        \n        <!-- 使用说明 -->\n        <view class=\"department-info-section\">\n          <view class=\"info-item\">\n            <uni-icons type=\"info-filled\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n            <text class=\"info-text\">新添加的部门仅在本次会话中有效</text>\n          </view>\n          <view class=\"info-item\">\n            <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#10b981\"></uni-icons>\n            <text class=\"info-text\">创建荣誉记录后，部门将永久保存</text>\n          </view>\n        </view>\n        \n        <!-- 添加新部门输入框 -->\n        <view class=\"add-department-section\">\n          <view class=\"add-department-input\">\n            <uni-easyinput \n              v-model=\"newDepartmentName\"\n              placeholder=\"输入新部门名称\"\n              :clearable=\"true\"\n              @confirm=\"addNewDepartment\"\n            ></uni-easyinput>\n          </view>\n          <button class=\"add-department-btn\" @click=\"addNewDepartment\" :disabled=\"!newDepartmentName.trim()\">\n            <uni-icons type=\"plus\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n            添加部门\n          </button>\n        </view>\n        \n        <scroll-view scroll-y class=\"selector-list-container\">\n          <!-- 如果没有部门选项，显示提示 -->\n          <view v-if=\"departmentOptions.length === 0\" class=\"empty-department-hint\">\n            <uni-icons type=\"info\" size=\"32\" color=\"#c4c4c4\"></uni-icons>\n            <text class=\"empty-text\">暂无部门数据</text>\n            <text class=\"empty-desc\">请在上方输入框中添加新部门</text>\n          </view>\n          \n          <!-- 部门列表 -->\n          <view \n            class=\"selector-item\" \n            v-for=\"dept in departmentOptions\" \n            :key=\"dept.value\"\n            :class=\"{ 'selected': formData.department === dept.value }\"\n            @click=\"selectDepartment(dept)\"\n          >\n            <view class=\"selector-item-left\">\n              <view v-if=\"formData.department === dept.value\" class=\"selected-icon\">\n                <uni-icons type=\"checkmarkempty\" size=\"18\" color=\"#3a86ff\"></uni-icons>\n              </view>\n            </view>\n            <view class=\"selector-item-content\">\n              <text class=\"selector-name\">{{ dept.text }}</text>\n            </view>\n            <view class=\"selector-item-right\">\n              <view class=\"delete-department-btn\" @click.stop=\"deleteDepartment(dept)\" v-if=\"departmentOptions.length > 1\">\n                <uni-icons type=\"trash\" size=\"14\" color=\"#ef4444\"></uni-icons>\n              </view>\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n    </view>\n\n    <!-- 荣誉类型选择弹窗 -->\n    <view v-if=\"showHonorTypeSelector\" class=\"selector-modal-overlay\" @click=\"closeHonorTypeSelector\">\n      <view class=\"selector-modal-content\" @click.stop>\n        <view class=\"selector-modal-header\">\n          <text class=\"selector-modal-title\">选择荣誉类型</text>\n          <view class=\"selector-modal-close\" @click=\"closeHonorTypeSelector\">\n            <uni-icons type=\"close\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n          </view>\n        </view>\n        \n        <scroll-view scroll-y class=\"honor-type-list-container\">\n          <view \n            class=\"selector-item\" \n            v-for=\"type in honorTypeOptions\" \n            :key=\"type.value\"\n            :class=\"{ 'selected': selectedHonorType && selectedHonorType._id === type.value }\"\n            @click=\"selectHonorType(type)\"\n          >\n            <view class=\"selector-item-left\">\n              <view v-if=\"selectedHonorType && selectedHonorType._id === type.value\" class=\"selected-icon\">\n                <uni-icons type=\"checkmarkempty\" size=\"18\" color=\"#3a86ff\"></uni-icons>\n              </view>\n            </view>\n            <view class=\"selector-item-content\">\n              <text class=\"selector-name\">{{ type.text }}</text>\n              <view v-if=\"type.color\" class=\"color-indicator\" :style=\"{ backgroundColor: type.color }\"></view>\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n    </view>\n\n    <!-- 智能批次选择弹窗 -->\n    <view v-if=\"showBatchSelector\" class=\"batch-modal-overlay\" @click=\"closeBatchSelector\">\n      <view class=\"batch-modal-content\" @click.stop>\n        <view class=\"batch-modal-header\">\n          <text class=\"batch-modal-title\">选择表彰批次</text>\n          <view class=\"batch-modal-close\" @click=\"closeBatchSelector\">\n            <uni-icons type=\"close\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n          </view>\n        </view>\n        \n        <!-- 搜索栏 -->\n        <view class=\"batch-search-section\">\n          <uni-easyinput \n            v-model=\"batchSearchKeyword\"\n            placeholder=\"搜索批次名称...\"\n            prefixIcon=\"search\"\n            @input=\"onBatchSearch\"\n            clearable\n          ></uni-easyinput>\n        </view>\n        \n        <!-- 批次分组列表 -->\n        <scroll-view scroll-y class=\"batch-list-container\">\n          <!-- 最近批次 -->\n          <view v-if=\"recentBatches.length > 0\" class=\"batch-group\">\n            <view class=\"batch-group-header\">\n              <uni-icons type=\"calendar\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n              <text class=\"batch-group-title\">最近批次</text>\n            </view>\n            <view \n              class=\"batch-item\" \n              v-for=\"batch in recentBatches\" \n              :key=\"batch.value\"\n              :class=\"{ 'selected': selectedBatch && selectedBatch._id === batch.value }\"\n              @click=\"selectBatch(batch)\"\n            >\n              <view class=\"batch-item-left\">\n                <view v-if=\"selectedBatch && selectedBatch._id === batch.value\" class=\"selected-icon\">\n                  <uni-icons type=\"checkmarkempty\" size=\"18\" color=\"#3a86ff\"></uni-icons>\n                </view>\n              </view>\n              <view class=\"batch-item-content\">\n                <text class=\"batch-name\">{{ batch.text }}</text>\n                <text class=\"batch-date\">{{ formatBatchDate(batch.data.meetingDate) }}</text>\n              </view>\n              <view class=\"batch-type-tag\" :class=\"batch.data.type ? 'type-' + batch.data.type : ''\">\n                {{ getBatchTypeText(batch.data.type) }}\n              </view>\n            </view>\n          </view>\n          \n          <!-- 本月批次 -->\n          <view v-if=\"currentMonthBatches.length > 0\" class=\"batch-group\">\n            <view class=\"batch-group-header\">\n              <uni-icons type=\"calendar-filled\" size=\"16\" color=\"#10b981\"></uni-icons>\n              <text class=\"batch-group-title\">本月批次</text>\n            </view>\n            <view \n              class=\"batch-item\" \n              v-for=\"batch in currentMonthBatches\" \n              :key=\"batch.value\"\n              :class=\"{ 'selected': selectedBatch && selectedBatch._id === batch.value }\"\n              @click=\"selectBatch(batch)\"\n            >\n              <view class=\"batch-item-left\">\n                <view v-if=\"selectedBatch && selectedBatch._id === batch.value\" class=\"selected-icon\">\n                  <uni-icons type=\"checkmarkempty\" size=\"18\" color=\"#3a86ff\"></uni-icons>\n                </view>\n              </view>\n              <view class=\"batch-item-content\">\n                <text class=\"batch-name\">{{ batch.text }}</text>\n                <text class=\"batch-date\">{{ formatBatchDate(batch.data.meetingDate) }}</text>\n              </view>\n              <view class=\"batch-type-tag\" :class=\"batch.data.type ? 'type-' + batch.data.type : ''\">\n                {{ getBatchTypeText(batch.data.type) }}\n              </view>\n            </view>\n          </view>\n          \n          <!-- 历史批次（分页加载） -->\n          <view v-if=\"filteredOlderBatches.length > 0\" class=\"batch-group\">\n            <view class=\"batch-group-header\">\n              <uni-icons type=\"folder\" size=\"16\" color=\"#8b5cf6\"></uni-icons>\n              <text class=\"batch-group-title\">历史批次</text>\n              <text class=\"batch-count\">{{ olderBatches.length }}个</text>\n            </view>\n            <view \n              class=\"batch-item\" \n              v-for=\"batch in filteredOlderBatches\" \n              :key=\"batch.value\"\n              :class=\"{ 'selected': selectedBatch && selectedBatch._id === batch.value }\"\n              @click=\"selectBatch(batch)\"\n            >\n              <view class=\"batch-item-left\">\n                <view v-if=\"selectedBatch && selectedBatch._id === batch.value\" class=\"selected-icon\">\n                  <uni-icons type=\"checkmarkempty\" size=\"18\" color=\"#3a86ff\"></uni-icons>\n                </view>\n              </view>\n              <view class=\"batch-item-content\">\n                <text class=\"batch-name\">{{ batch.text }}</text>\n                <text class=\"batch-date\">{{ formatBatchDate(batch.data.meetingDate) }}</text>\n              </view>\n              <view class=\"batch-type-tag\" :class=\"batch.data.type ? 'type-' + batch.data.type : ''\">\n                {{ getBatchTypeText(batch.data.type) }}\n              </view>\n            </view>\n            \n            <!-- 显示更多按钮 -->\n            <view v-if=\"olderBatches.length > showOlderBatchCount\" class=\"show-more-btn\" @click=\"showMoreOlderBatches\">\n              <text class=\"show-more-text\">显示更多 ({{ olderBatches.length - showOlderBatchCount }}个)</text>\n              <uni-icons type=\"bottom\" size=\"14\" color=\"#3a86ff\"></uni-icons>\n            </view>\n          </view>\n          \n          <!-- 空状态 -->\n          <view v-if=\"filteredBatchOptions.length === 0\" class=\"batch-empty-state\">\n            <uni-icons type=\"search\" size=\"40\" color=\"#c4c4c4\"></uni-icons>\n            <text class=\"empty-text\">未找到匹配的批次</text>\n          </view>\n        </scroll-view>\n        \n        <!-- 底部快捷操作 -->\n        <view class=\"batch-modal-footer\">\n          <button class=\"batch-footer-btn secondary\" @click=\"quickCreateBatch\">\n            <uni-icons type=\"plus\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n            快速创建\n          </button>\n          <button class=\"batch-footer-btn primary\" @click=\"closeBatchSelector\">确定</button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-overlay\">\n      <view class=\"loading-content\">\n        <uni-icons type=\"spinner-cycle\" size=\"32\" color=\"#3a86ff\" class=\"loading-spin\"></uni-icons>\n        <text class=\"loading-text\">{{ loadingText }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport AvatarPicker from '../avatar-picker/avatar-picker.vue'\n\nexport default {\n  components: {\n    AvatarPicker\n  },\n  name: 'AddRecord',\n  data() {\n    return {\n      loading: false,\n      saving: false,\n      loadingText: '加载中...',\n      isEditMode: false,\n      recordId: null,\n      \n      // 表单数据\n      formData: {\n        userName: '',\n        department: '',\n        userAvatar: '',\n        honorTypeId: '',\n        batchId: '',\n        reason: '',\n        isFeatured: false,\n        images: []\n      },\n      \n      // 选项数据\n      departmentOptions: [],\n      honorTypeOptions: [],\n      batchOptions: [],\n      \n      // 选中的数据\n      selectedHonorType: null,\n      selectedBatch: null,\n      \n      // 智能批次选择器\n      showBatchSelector: false,\n      batchSearchKeyword: '',\n      showOlderBatchCount: 10, // 默认显示10个历史批次\n      \n      // 部门选择器\n      showDepartmentSelector: false,\n      newDepartmentName: '',\n      \n      // 荣誉类型选择器\n      showHonorTypeSelector: false\n    }\n  },\n  \n  computed: {\n    // 过滤后的批次选项\n    filteredBatchOptions() {\n      if (!this.batchSearchKeyword.trim()) {\n        return this.batchOptions\n      }\n      \n      const keyword = this.batchSearchKeyword.toLowerCase()\n      return this.batchOptions.filter(batch => \n        batch.text.toLowerCase().includes(keyword)\n      )\n    },\n    \n    // 最近批次（最近5个）\n    recentBatches() {\n      const sorted = [...this.filteredBatchOptions].sort((a, b) => {\n        const dateA = new Date(a.data.meetingDate || '1970-01-01')\n        const dateB = new Date(b.data.meetingDate || '1970-01-01')\n        return dateB - dateA\n      })\n      return sorted.slice(0, 5)\n    },\n    \n    // 本月批次\n    currentMonthBatches() {\n      const now = new Date()\n      const currentMonth = now.getMonth() + 1\n      const currentYear = now.getFullYear()\n      \n      return this.filteredBatchOptions.filter(batch => {\n        const meetingDate = new Date(batch.data.meetingDate)\n        return meetingDate.getMonth() + 1 === currentMonth && \n               meetingDate.getFullYear() === currentYear\n      })\n    },\n    \n    // 历史批次（3个月前的）\n    olderBatches() {\n      const threeMonthsAgo = new Date()\n      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)\n      \n      return this.filteredBatchOptions.filter(batch => {\n        const meetingDate = new Date(batch.data.meetingDate)\n        return meetingDate < threeMonthsAgo\n      }).sort((a, b) => {\n        const dateA = new Date(a.data.meetingDate || '1970-01-01')\n        const dateB = new Date(b.data.meetingDate || '1970-01-01')\n        return dateB - dateA\n      })\n    },\n    \n    // 分页显示的历史批次\n    filteredOlderBatches() {\n      return this.olderBatches.slice(0, this.showOlderBatchCount)\n    }\n  },\n  \n  async onLoad(options) {\n    // 检查是否为编辑模式\n    if (options.mode === 'edit' && options.data) {\n      this.isEditMode = true\n      try {\n        const recordData = JSON.parse(decodeURIComponent(options.data))\n        this.recordId = recordData.id\n        \n        // 确保头像URL正确设置\n        if (recordData.userAvatar) {\n          this.formData.userAvatar = recordData.userAvatar\n        }\n        \n        // 设置其他表单数据\n        this.formData = { \n          ...this.formData, \n          ...recordData,\n          userAvatar: recordData.userAvatar || '' // 确保头像URL不会被覆盖为undefined\n        }\n        delete this.formData.id // 移除id字段\n      } catch (error) {\n        console.error('解析编辑数据失败:', error)\n      }\n    }\n    \n    await this.initializeData()\n  },\n  \n  methods: {\n    async initializeData() {\n      try {\n        await Promise.all([\n          this.loadDepartmentList(),\n          this.loadHonorTypeList(),\n          this.loadBatchList()\n        ])\n        \n        // 如果是编辑模式，设置选中的荣誉类型和批次\n        if (this.isEditMode) {\n          this.setSelectedHonorType()\n          this.setSelectedBatch()\n        }\n      } catch (error) {\n        uni.showToast({\n          title: '初始化失败，请重试',\n          icon: 'none'\n        })\n      }\n    },\n\n    async loadDepartmentList() {\n      try {\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getDepartments'\n          }\n        })\n\n        if (res.result.code === 0) {\n          const data = res.result.data\n          if (Array.isArray(data)) {\n            this.departmentOptions = data.map(dept => ({\n              value: dept.name || dept,\n              text: dept.name || dept\n            }))\n          } else {\n            this.departmentOptions = []\n          }\n        }\n      } catch (error) {\n        console.error('加载部门列表失败:', error)\n        // 提供默认部门选项\n        this.departmentOptions = [\n          { value: '管理部门', text: '管理部门' },\n          { value: '业务部门', text: '业务部门' },\n          { value: '技术部门', text: '技术部门' },\n          { value: '支持部门', text: '支持部门' }\n        ]\n      }\n    },\n\n    async loadHonorTypeList() {\n      try {\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getHonorTypes'\n          }\n        })\n\n        if (res.result.code === 0) {\n          const data = res.result.data\n          if (Array.isArray(data)) {\n            // 只显示启用的荣誉类型\n            this.honorTypeOptions = data\n              .filter(type => type.isActive !== false)\n              .map(type => ({\n                value: type._id || type.id,\n                text: type.name,\n                color: type.color || '#3a86ff',\n                data: type\n              }))\n          } else {\n            this.honorTypeOptions = []\n          }\n        }\n      } catch (error) {\n        console.error('加载荣誉类型失败:', error)\n        // 提供默认荣誉类型选项\n        this.honorTypeOptions = [\n          { value: '1', text: '优秀员工', color: '#3a86ff', data: { id: '1', name: '优秀员工', color: '#3a86ff' } },\n          { value: '2', text: '月度之星', color: '#10b981', data: { id: '2', name: '月度之星', color: '#10b981' } },\n          { value: '3', text: '团队协作奖', color: '#f59e0b', data: { id: '3', name: '团队协作奖', color: '#f59e0b' } }\n        ]\n      }\n    },\n\n    async loadBatchList() {\n      try {\n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: {\n            action: 'getBatches'\n          }\n        })\n\n        if (res.result.code === 0) {\n          const data = res.result.data\n          if (Array.isArray(data)) {\n            this.batchOptions = data.map(batch => ({\n              value: batch._id,\n              text: batch.name,\n              meetingDate: batch.meetingDate,\n              data: batch\n            }))\n          } else {\n            this.batchOptions = []\n          }\n        }\n      } catch (error) {\n        console.error('加载批次列表失败:', error)\n        // 提供默认批次选项\n        const currentDate = new Date()\n        const currentMonth = currentDate.getMonth() + 1\n        const currentYear = currentDate.getFullYear()\n        \n        this.batchOptions = [\n          { \n            value: 'default_monthly', \n            text: `${currentYear}年${currentMonth}月表彰`, \n            meetingDate: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`,\n            data: { \n              _id: 'default_monthly', \n              name: `${currentYear}年${currentMonth}月表彰`, \n              type: 'monthly',\n              meetingDate: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`\n            }\n          }\n        ]\n      }\n    },\n\n    setSelectedHonorType() {\n      if (this.formData.honorTypeId) {\n        const found = this.honorTypeOptions.find(option => option.value === this.formData.honorTypeId)\n        if (found) {\n          this.selectedHonorType = found.data\n        }\n      }\n    },\n\n    setSelectedBatch() {\n      if (this.formData.batchId) {\n        const found = this.batchOptions.find(option => option.value === this.formData.batchId)\n        if (found) {\n          this.selectedBatch = found.data\n        }\n      }\n    },\n\n    // 荣誉类型变化\n    onHonorTypeChange(e) {\n      const option = this.honorTypeOptions.find(opt => opt.value === e.detail.value)\n      this.selectedHonorType = option ? option.data : null\n    },\n    \n    // 批次变化\n    onBatchChange(e) {\n      const option = this.batchOptions.find(opt => opt.value === e.detail.value)\n      this.selectedBatch = option ? option.data : null\n    },\n    \n    // 精选状态变化\n    onFeaturedChange(e) {\n      this.formData.isFeatured = e.detail.value\n    },\n    \n    // 头像变化处理\n    handleAvatarChange(data) {\n      console.log('头像变化:', data)\n      if (data && (data.cloudPath || data.url)) {\n        // 优先使用url，如果没有则使用cloudPath\n        this.formData.userAvatar = data.url || data.cloudPath\n        \n        // 保存cloudPath用于后续删除\n        if (data.cloudPath) {\n          this.formData.userAvatarCloudPath = data.cloudPath\n        }\n        \n        // 显示成功提示\n        uni.showToast({\n          title: '头像更新成功',\n          icon: 'success',\n          duration: 2000\n        })\n      } else if (!data) {\n        // 头像被删除\n        this.formData.userAvatar = ''\n        this.formData.userAvatarCloudPath = ''\n      }\n    },\n    \n    // 头像上传成功\n    onAvatarUploadSuccess(data) {\n      console.log('头像上传成功:', data)\n      \n      // 确保URL正确设置\n      if (data.url) {\n        this.formData.userAvatar = data.url\n        console.log('设置头像URL:', data.url)\n        \n        // 显示压缩信息\n        if (data.compressed) {\n          console.log('压缩信息:', {\n            size: data.size,\n            originalSize: data.originalSize,\n            compressionRatio: data.compressionRatio\n          })\n        }\n      }\n      \n      uni.showToast({\n        title: `头像上传成功${data.compressed ? '(已压缩)' : ''}`,\n        icon: 'success'\n      })\n    },\n    \n    // 头像上传失败\n    onAvatarUploadError(error) {\n      console.error('头像上传失败:', error)\n      uni.showToast({\n        title: '头像上传失败',\n        icon: 'none'\n      })\n    },\n    \n    // 选择图片\n    async chooseImages() {\n      const remainCount = 5 - this.formData.images.length\n      \n      try {\n        const res = await this.chooseImageAsync({\n          count: remainCount,\n          sizeType: ['original'], // 选择原图，后续压缩\n          sourceType: ['album', 'camera']\n        })\n        \n        // 用户取消选择时直接返回，不显示错误\n        if (res.errMsg === 'chooseImage:fail cancel') {\n          return\n        }\n        \n        if (res.tempFilePaths && res.tempFilePaths.length > 0) {\n          // 显示上传进度\n          uni.showLoading({ title: '处理中...' })\n          \n          // 使用上传工具批量上传\n          const uploadUtils = (await import('@/utils/upload-utils.js')).default\n          \n          const result = await uploadUtils.uploadHonorImages(res.tempFilePaths, {\n            compress: true,\n            onProgress: (progress) => {\n              if (progress.phase === 'compress') {\n                uni.showLoading({ title: `压缩中 ${progress.progress || 0}%` })\n              } else if (progress.phase === 'upload') {\n                uni.showLoading({ title: `上传中 ${progress.progress || 0}%` })\n              }\n            }\n          })\n          \n          uni.hideLoading()\n          \n          if (result.success) {\n            // 添加成功上传的图片\n            result.results.forEach((item, index) => {\n              if (item.success) {\n                this.formData.images.push({\n                  id: Date.now() + Math.random() + index,\n                  url: item.url,\n                  cloudPath: item.cloudPath\n                })\n              }\n            })\n            \n            const successCount = result.totalUploaded\n            const failCount = result.totalFailed\n            \n            if (failCount > 0) {\n              uni.showToast({\n                title: `${successCount}张成功，${failCount}张失败`,\n                icon: 'none'\n              })\n            } else {\n              uni.showToast({\n                title: `${successCount}张图片上传成功`,\n                icon: 'success'\n              })\n            }\n          } else {\n            throw new Error('批量上传失败')\n          }\n        }\n      } catch (error) {\n        uni.hideLoading()\n        console.error('选择图片失败:', error)\n        uni.showToast({\n          title: error.message || '图片上传失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 异步选择图片\n    chooseImageAsync(options) {\n      return new Promise((resolve, reject) => {\n        uni.chooseImage({\n          ...options,\n          success: resolve,\n          fail: reject\n        })\n      })\n    },\n    \n    // 移除图片\n    async removeImage(index) {\n      try {\n        const image = this.formData.images[index]\n        if (image && image.cloudPath) {\n          // 显示删除中提示\n          uni.showLoading({ title: '删除中...' })\n          \n          // 调用云函数删除文件\n          const { result } = await uniCloud.callFunction({\n            name: 'delete-file',\n            data: {\n              fileList: [image.cloudPath]\n            }\n          })\n          \n          uni.hideLoading()\n          \n          if (result.code === 0) {\n            // 从数组中移除\n            this.formData.images.splice(index, 1)\n            uni.showToast({\n              title: '删除成功',\n              icon: 'success'\n            })\n          } else {\n            throw new Error(result.message || '删除失败')\n          }\n        } else {\n          // 如果没有cloudPath，直接从数组移除\n          this.formData.images.splice(index, 1)\n        }\n      } catch (error) {\n        uni.hideLoading()\n        console.error('删除图片失败:', error)\n        uni.showToast({\n          title: error.message || '删除失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 保存记录\n    async saveRecord() {\n      if (!this.validateForm()) {\n        return\n      }\n      \n      this.saving = true\n      \n      try {\n        const action = this.isEditMode ? 'updateHonor' : 'createHonor'\n        const data = {\n          userName: this.formData.userName,\n          department: this.formData.department,\n          userAvatar: this.formData.userAvatar,\n          honorTypeId: this.formData.honorTypeId,\n          batchId: this.formData.batchId,\n          reason: this.formData.reason,\n          isFeatured: this.formData.isFeatured,\n          images: this.formData.images\n        }\n        \n        // 编辑模式需要传递记录ID\n        if (this.isEditMode) {\n          data.honorId = this.recordId\n        }\n        \n        const res = await uniCloud.callFunction({\n          name: 'honor-admin',\n          data: { action, data }\n        })\n        \n        if (res.result.code === 0) {\n          uni.showToast({\n            title: this.isEditMode ? '表彰记录更新成功' : '表彰记录创建成功',\n            icon: 'success',\n            duration: 2000\n          })\n          \n          setTimeout(() => {\n            uni.navigateBack()\n          }, 2000)\n        } else {\n          throw new Error(res.result.message || (this.isEditMode ? '更新失败' : '创建失败'))\n        }\n              } catch (error) {\n          uni.showToast({\n          title: error.message || '保存失败',\n          icon: 'none'\n        })\n      } finally {\n        this.saving = false\n      }\n    },\n    \n    // 表单验证\n    validateForm() {\n      if (!this.formData.userName.trim()) {\n        uni.showToast({\n          title: '请输入被表彰人姓名',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.formData.honorTypeId) {\n        uni.showToast({\n          title: '请选择荣誉类型',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.formData.batchId) {\n        uni.showToast({\n          title: '请选择表彰批次',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.formData.reason.trim()) {\n        uni.showToast({\n          title: '请填写表彰原因',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      return true\n    },\n    \n    // 返回\n    goBack() {\n      if (this.hasChanges()) {\n        uni.showModal({\n          title: '确认离开',\n          content: '您有未保存的内容，确定要离开吗？',\n          success: (res) => {\n            if (res.confirm) {\n              uni.navigateBack()\n            }\n          }\n        })\n      } else {\n        uni.navigateBack()\n      }\n    },\n    \n    // 检查是否有更改\n    hasChanges() {\n      return this.formData.userName.trim() ||\n             this.formData.reason.trim() ||\n             this.formData.honorTypeId ||\n             this.formData.batchId ||\n             this.formData.images.length > 0\n    },\n    \n    // ===== 选择器相关方法 =====\n    \n    // 部门选择器\n    openDepartmentSelector() {\n      this.showDepartmentSelector = true\n    },\n    \n    closeDepartmentSelector() {\n      this.showDepartmentSelector = false\n      this.newDepartmentName = '' // 清空输入框\n    },\n    \n    selectDepartment(dept) {\n      this.formData.department = dept.value\n      this.closeDepartmentSelector()\n    },\n    \n    // 添加新部门\n    async addNewDepartment() {\n      const departmentName = this.newDepartmentName.trim()\n      if (!departmentName) {\n        uni.showToast({\n          title: '请输入部门名称',\n          icon: 'none'\n        })\n        return\n      }\n      \n      // 检查是否已存在\n      const exists = this.departmentOptions.some(dept => dept.value === departmentName)\n      if (exists) {\n        uni.showToast({\n          title: '部门已存在',\n          icon: 'none'\n        })\n        return\n      }\n      \n      // 直接在本地添加，不调用云函数（因为是临时数据）\n      this.departmentOptions.push({\n        value: departmentName,\n        text: departmentName\n      })\n      \n      // 自动选择新添加的部门\n      this.formData.department = departmentName\n      this.newDepartmentName = ''\n      this.closeDepartmentSelector()\n      \n      uni.showToast({\n        title: '部门已添加（临时）',\n        icon: 'success',\n        duration: 2000\n      })\n    },\n    \n    // 删除部门\n    deleteDepartment(dept) {\n      if (this.departmentOptions.length <= 1) {\n        uni.showToast({\n          title: '至少需要保留一个部门',\n          icon: 'none'\n        })\n        return\n      }\n      \n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除\"${dept.text}\"部门吗？`,\n        success: async (res) => {\n          if (res.confirm) {\n            // 检查该部门是否在荣誉记录中被使用（本地检查）\n            try {\n              const result = await uniCloud.callFunction({\n                name: 'honor-admin',\n                data: {\n                  action: 'checkDepartmentUsage',\n                  data: {\n                    name: dept.value\n                  }\n                }\n              })\n              \n              if (result.result.code !== 0) {\n                uni.showToast({\n                  title: result.result.message || '该部门正在使用中，无法删除',\n                  icon: 'none',\n                  duration: 3000\n                })\n                return\n              }\n              \n            } catch (error) {\n              // 如果检查失败，仍然允许删除（因为可能是新添加的临时部门）\n              console.log('检查部门使用情况失败，继续删除:', error)\n            }\n            \n            // 执行本地删除\n            const index = this.departmentOptions.findIndex(d => d.value === dept.value)\n            if (index > -1) {\n              this.departmentOptions.splice(index, 1)\n            }\n            \n            // 如果当前选择的是被删除的部门，清空选择\n            if (this.formData.department === dept.value) {\n              this.formData.department = ''\n            }\n            \n            uni.showToast({\n              title: '部门已移除',\n              icon: 'success'\n            })\n          }\n        }\n      })\n    },\n    \n    // 荣誉类型选择器\n    openHonorTypeSelector() {\n      this.showHonorTypeSelector = true\n    },\n    \n    closeHonorTypeSelector() {\n      this.showHonorTypeSelector = false\n    },\n    \n    selectHonorType(type) {\n      this.formData.honorTypeId = type.value\n      this.selectedHonorType = type.data\n      this.closeHonorTypeSelector()\n    },\n    \n    // 批次选择器\n    openBatchSelector() {\n      this.showBatchSelector = true\n    },\n    \n    closeBatchSelector() {\n      this.showBatchSelector = false\n      this.batchSearchKeyword = ''\n    },\n    \n    // 批次搜索\n    onBatchSearch() {\n      // 重置历史批次显示数量\n      this.showOlderBatchCount = 10\n    },\n    \n    // 选择批次\n    selectBatch(batch) {\n      this.formData.batchId = batch.value\n      this.selectedBatch = batch.data\n      this.closeBatchSelector()\n    },\n    \n    // 显示更多历史批次\n    showMoreOlderBatches() {\n      this.showOlderBatchCount += 10\n    },\n    \n    // 格式化批次日期\n    formatBatchDate(dateStr) {\n      if (!dateStr) return ''\n      \n      const date = new Date(dateStr)\n      const month = date.getMonth() + 1\n      const day = date.getDate()\n      return `${month}月${day}日`\n    },\n    \n    // 获取批次类型文本\n    getBatchTypeText(type) {\n      const typeMap = {\n        'weekly': '周表彰',\n        'monthly': '月表彰', \n        'quarterly': '季度表彰',\n        'yearly': '年度表彰',\n        'special': '特别表彰'\n      }\n      return typeMap[type] || '其他'\n    },\n    \n\n    \n    // 快速创建批次\n    quickCreateBatch() {\n      this.closeBatchSelector()\n      uni.navigateTo({\n        url: '/pages/honor_pkg/admin/batch-manager?quick=true'\n      })\n    },\n\n    // 预览图片\n    previewImage(index) {\n      if (this.formData.images.length > 0) {\n        uni.previewImage({\n          urls: this.formData.images.map(img => img.url),\n          current: this.formData.images[index].url,\n          indicator: 'number'\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.add-record-container {\n  min-height: 100vh;\n  background: linear-gradient(145deg, #f8faff 0%, #e9f0f8 100%);\n  position: relative;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  background: linear-gradient(180deg, #3a86ff 0%, #2563eb 100%);\n  overflow: hidden;\n  \n  // 装饰性背景图案\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    right: -20%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);\n    transform: rotate(-15deg);\n    pointer-events: none;\n  }\n  \n  .header-content {\n    display: flex;\n    align-items: center;\n    padding: 20rpx 40rpx;\n    padding-top: calc(var(--status-bar-height, 0px) + 20rpx);\n    position: relative;\n    z-index: 2;\n    \n    .back-btn {\n      width: 60rpx;\n      height: 60rpx;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.2);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 30rpx;\n    }\n    \n    .title-area {\n      flex: 1;\n      \n      .title {\n        display: block;\n        font-size: 36rpx;\n        font-weight: 600;\n        color: #FFFFFF;\n        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n        line-height: 1.2;\n      }\n      \n      .subtitle {\n        display: block;\n        font-size: 24rpx;\n        color: rgba(255, 255, 255, 0.8);\n        margin-top: 4rpx;\n      }\n    }\n    \n    .header-actions {\n      .save-btn {\n        background: rgba(255, 255, 255, 0.2);\n        color: #FFFFFF;\n        border-radius: 8rpx;\n        padding: 12rpx 22rpx;\n        font-size: 24rpx;\n        font-weight: 500;\n        border: none;\n        min-width: 88rpx;\n        height: 75rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        \n        &:disabled {\n          background: rgba(255, 255, 255, 0.1);\n          color: rgba(255, 255, 255, 0.5);\n        }\n      }\n    }\n  }\n}\n\n.content-scroll {\n  position: fixed;\n  top: calc(var(--status-bar-height, 0px) + 140rpx);\n  left: 0;\n  right: 0;\n  bottom: 0;\n}\n\n.form-container {\n  padding: 40rpx;\n}\n\n.form-section {\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 32rpx;\n  \n  .section-header {\n    display: flex;\n    align-items: center;\n    margin-bottom: 32rpx;\n    \n    .section-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #1a1a1a;\n      margin-left: 16rpx;\n    }\n  }\n}\n\n.form-item {\n  margin-bottom: 32rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n  \n  .form-label {\n    display: block;\n    font-size: 28rpx;\n    font-weight: 500;\n    color: #1a1a1a;\n    margin-bottom: 16rpx;\n    \n    .required {\n      color: #ef4444;\n      margin-left: 4rpx;\n    }\n  }\n  \n  .form-desc {\n    font-size: 24rpx;\n    color: #8a94a6;\n    margin-top: 12rpx;\n    line-height: 1.4;\n  }\n  \n  .char-count {\n    text-align: right;\n    font-size: 22rpx;\n    color: #c4c4c4;\n    margin-top: 8rpx;\n  }\n}\n\n// 旧的头像选择器样式已移除，使用新的avatar-picker组件\n\n.switch-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  .switch-label {\n    font-size: 28rpx;\n    color: #1a1a1a;\n  }\n}\n\n.image-picker {\n  .image-list {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 12rpx;\n    margin-bottom: 16rpx;\n    \n    .image-item {\n      position: relative;\n      width: 160rpx;\n      height: 160rpx;\n      flex-shrink: 0;\n      \n      .image-preview {\n        width: 100%;\n        height: 100%;\n        border-radius: 12rpx;\n        object-fit: cover;\n      }\n      \n      .image-remove {\n        position: absolute;\n        top: -8rpx;\n        right: -8rpx;\n        width: 32rpx;\n        height: 32rpx;\n        border-radius: 50%;\n        background: #ef4444;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 0;\n        line-height: 1;\n        \n        :deep(.uni-icons) {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 100%;\n          height: 100%;\n        }\n      }\n    }\n    \n    .image-add {\n      width: 160rpx;\n      height: 160rpx;\n      border: 2px dashed #c4c4c4;\n      border-radius: 12rpx;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      flex-shrink: 0;\n      transition: all 0.2s ease;\n      \n      &:active {\n        background: rgba(58, 134, 255, 0.05);\n        border-color: #3a86ff;\n        transform: scale(0.98);\n      }\n      \n      .add-text {\n        font-size: 24rpx;\n        color: #8a94a6;\n        margin-top: 8rpx;\n        font-weight: 500;\n      }\n    }\n  }\n  \n  .image-tip {\n    font-size: 22rpx;\n    color: #8a94a6;\n    line-height: 1.4;\n  }\n}\n\n.preview-card {\n  background: #f8f9fa;\n  border-radius: 16rpx;\n  padding: 32rpx;\n  border: 1px solid #e9ecef;\n  \n  .preview-header {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20rpx;\n    \n    .preview-avatar {\n      flex-shrink: 0; // 防止头像被压缩\n      width: 80rpx; // 增大尺寸使其更清晰\n      height: 80rpx;\n      border-radius: 50%;\n      margin-right: 20rpx;\n      object-fit: cover;\n      background-color: #f8f9fa; // 添加背景色\n      border: 2rpx solid #e5e7eb; // 添加边框\n      \n      /* #ifdef MP-WEIXIN */\n      // 微信小程序特殊处理\n      &[mode=\"aspectFill\"] {\n        object-fit: cover;\n      }\n      /* #endif */\n    }\n    \n    .preview-info {\n      flex: 1;\n      \n      .preview-name {\n        display: block;\n        font-size: 28rpx;\n        font-weight: 600;\n        color: #1a1a1a;\n        line-height: 1.2;\n      }\n      \n      .preview-dept {\n        display: block;\n        font-size: 22rpx;\n        color: #8a94a6;\n        margin-top: 4rpx;\n      }\n    }\n    \n    .preview-badge {\n      display: flex;\n      align-items: center;\n      background: rgba(245, 158, 11, 0.1);\n      border-radius: 12rpx;\n      padding: 8rpx 16rpx;\n      \n      .badge-text {\n        font-size: 20rpx;\n        color: #f59e0b;\n        margin-left: 4rpx;\n      }\n    }\n  }\n  \n  .preview-type {\n    margin-bottom: 20rpx;\n    \n    .type-tag {\n      display: inline-block;\n      background: #3a86ff;\n      color: #FFFFFF;\n      font-size: 22rpx;\n      padding: 8rpx 16rpx;\n      border-radius: 12rpx;\n    }\n  }\n  \n  .preview-reason {\n    font-size: 26rpx;\n    color: #1a1a1a;\n    line-height: 1.5;\n    margin-bottom: 20rpx;\n  }\n  \n  .preview-images {\n    display: flex;\n    gap: 8rpx;\n    margin-bottom: 20rpx;\n    \n    .preview-image {\n      width: 80rpx;\n      height: 80rpx;\n      border-radius: 8rpx;\n      object-fit: cover;\n    }\n    \n    .more-images {\n      width: 80rpx;\n      height: 80rpx;\n      border-radius: 8rpx;\n      background: rgba(0, 0, 0, 0.1);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 22rpx;\n      color: #8a94a6;\n    }\n  }\n  \n  .preview-batch {\n    .batch-text {\n      font-size: 24rpx;\n      color: #3a86ff;\n      font-weight: 500;\n    }\n    \n    .batch-date {\n      font-size: 22rpx;\n      color: #8a94a6;\n      margin-left: 16rpx;\n    }\n  }\n}\n\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  .loading-content {\n    background: #FFFFFF;\n    border-radius: 20rpx;\n    padding: 60rpx;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    \n    .loading-spin {\n      animation: spin 1s linear infinite;\n    }\n    \n    .loading-text {\n      font-size: 28rpx;\n      color: #8a94a6;\n      margin-top: 24rpx;\n    }\n  }\n}\n\n.bottom-safe-area {\n  height: env(safe-area-inset-bottom);\n}\n\n// 统一选择器样式 - 匹配uni-easyinput默认样式\n.custom-department-picker,\n.custom-honor-type-picker,\n.custom-batch-picker {\n  min-height: 88rpx; // 增加高度，让下拉菜单看起来更合理\n  \n  .picker-display {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 20rpx 20rpx; // 增加上下内边距\n    border: 1px solid #dcdfe6; // 调整为1px边框，匹配uni-easyinput默认样式\n    border-radius: 8rpx; // 调整圆角匹配输入框\n    background: #FFFFFF;\n    transition: all 0.2s ease;\n    \n    &:active {\n      border-color: #3a86ff;\n      background: rgba(58, 134, 255, 0.02);\n    }\n    \n    .selected-text {\n      font-size: 28rpx;\n      color: #606266; // 匹配uni-easyinput的文字颜色\n    }\n    \n    .placeholder-text {\n      font-size: 28rpx;\n      color: #c0c4cc; // 匹配uni-easyinput的占位符颜色\n    }\n  }\n}\n\n// 自定义输入框placeholder样式\n.custom-input {\n  :deep(.uni-easyinput__placeholder-class) {\n    font-size: 28rpx !important;\n    color: #606266 !important;\n  }\n  \n  // 针对不同平台的placeholder样式\n  :deep(.uni-easyinput__content-input) {\n    font-size: 28rpx !important;\n    \n    &::placeholder {\n      font-size: 28rpx !important;\n      color: #606266 !important;\n    }\n  }\n  \n  // 微信小程序特殊处理\n  :deep(.uni-input-input) {\n    font-size: 28rpx !important;\n    \n    &::placeholder {\n      font-size: 28rpx !important;\n      color: #606266 !important;\n    }\n  }\n}\n\n.custom-textarea {\n  :deep(.uni-easyinput__placeholder-class) {\n    font-size: 28rpx !important;\n    color: #606266 !important;\n  }\n  \n  // 针对textarea的placeholder样式\n  :deep(.uni-easyinput__content-textarea) {\n    font-size: 28rpx !important;\n    \n    &::placeholder {\n      font-size: 28rpx !important;\n      color: #606266 !important;\n    }\n  }\n  \n  // 微信小程序textarea特殊处理\n  :deep(.uni-textarea-textarea) {\n    font-size: 28rpx !important;\n    \n    &::placeholder {\n      font-size: 28rpx !important;\n      color: #606266 !important;\n    }\n  }\n}\n\n// 通用选择器弹窗样式\n.selector-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 999;\n  display: flex;\n  align-items: flex-end;\n  \n  .selector-modal-content {\n    width: 100%;\n    max-height: 80vh; // 进一步增加高度，给更多显示空间\n    background: #FFFFFF;\n    border-radius: 32rpx 32rpx 0 0;\n    display: flex;\n    flex-direction: column;\n    \n    .selector-modal-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 40rpx 48rpx 32rpx;\n      border-bottom: 1px solid #f0f0f0;\n      \n      .header-left {\n        flex: 1;\n        \n        .selector-modal-title {\n          font-size: 32rpx;\n          font-weight: 600;\n          color: #1a1a1a;\n          display: block;\n        }\n        \n        .selector-modal-subtitle {\n          font-size: 24rpx;\n          color: #8a94a6;\n          margin-top: 4rpx;\n          display: block;\n        }\n      }\n      \n      .selector-modal-close {\n        width: 60rpx;\n        height: 60rpx;\n        border-radius: 50%;\n        background: #f8f9fa;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        \n        &:active {\n          background: #e9ecef;\n        }\n      }\n    }\n    \n    // 使用说明区域样式\n    .department-info-section {\n      padding: 20rpx 48rpx;\n      background: #f8faff;\n      border-bottom: 1px solid #e5e7eb;\n      \n      .info-item {\n        display: flex;\n        align-items: center;\n        margin-bottom: 12rpx;\n        \n        &:last-child {\n          margin-bottom: 0;\n        }\n        \n        .info-text {\n          font-size: 24rpx;\n          color: #6b7280;\n          margin-left: 8rpx;\n        }\n      }\n    }\n    \n    // 添加部门区域样式\n    .add-department-section {\n      padding: 24rpx 48rpx;\n      border-bottom: 1px solid #f0f0f0;\n      background: #f8faff;\n      \n      .add-department-input {\n        margin-bottom: 20rpx;\n      }\n      \n      .add-department-btn {\n        width: 100%;\n        height: 80rpx;\n        background: #3a86ff;\n        color: #FFFFFF;\n        border-radius: 12rpx;\n        border: none;\n        font-size: 28rpx;\n        font-weight: 500;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        gap: 8rpx;\n        \n        &:disabled {\n          background: #d1d5db;\n          color: #9ca3af;\n        }\n        \n        &:active {\n          background: #2563eb;\n        }\n      }\n    }\n    \n    // 共同的选择器容器样式\n    .selector-list-container,\n    .honor-type-list-container {\n      padding: 24rpx 0;\n      \n      // 空状态提示\n      .empty-department-hint {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 80rpx 40rpx;\n        \n        .empty-text {\n          font-size: 28rpx;\n          color: #8a94a6;\n          margin-top: 16rpx;\n        }\n        \n        .empty-desc {\n          font-size: 24rpx;\n          color: #c4c4c4;\n          margin-top: 8rpx;\n        }\n      }\n      \n      .selector-item {\n        display: flex;\n        align-items: center;\n        padding: 24rpx 48rpx;\n        border-bottom: 1px solid #f8f9fa;\n        transition: all 0.2s ease;\n        \n        &:active {\n          background: rgba(58, 134, 255, 0.05);\n        }\n        \n        &.selected {\n          background: rgba(58, 134, 255, 0.08);\n          border-left: 4rpx solid #3a86ff;\n        }\n        \n        .selector-item-left {\n          width: 48rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n        \n        .selector-item-content {\n          flex: 1;\n          margin-left: 16rpx;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          \n          .selector-name {\n            font-size: 28rpx;\n            font-weight: 500;\n            color: #1a1a1a;\n          }\n          \n          .color-indicator {\n            width: 24rpx;\n            height: 24rpx;\n            border-radius: 50%;\n            border: 2rpx solid #FFFFFF;\n            box-shadow: 0 0 0 1rpx rgba(0, 0, 0, 0.1);\n          }\n        }\n        \n        .selected-icon {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 32rpx;\n          height: 32rpx;\n          border-radius: 50%;\n          background: rgba(58, 134, 255, 0.1);\n        }\n        \n        .selector-item-right {\n          .delete-department-btn {\n            width: 48rpx;\n            height: 48rpx;\n            border-radius: 50%;\n            background: rgba(239, 68, 68, 0.1);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            margin-left: 16rpx;\n            \n            &:active {\n              background: rgba(239, 68, 68, 0.2);\n            }\n          }\n        }\n      }\n    }\n    \n    // 部门选择器特定高度\n    .selector-list-container {\n      height: 550rpx; // 部门选择器高度\n    }\n    \n    // 荣誉类型选择器特定高度\n    .honor-type-list-container {\n      height: 650rpx; // 荣誉类型选择器单独增加高度\n    }\n  }\n}\n\n.batch-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 999;\n  display: flex;\n  align-items: flex-end;\n  \n  .batch-modal-content {\n    width: 100%;\n    max-height: 80vh;\n    background: #FFFFFF;\n    border-radius: 32rpx 32rpx 0 0;\n    display: flex;\n    flex-direction: column;\n    \n    .batch-modal-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 40rpx 48rpx 32rpx;\n      border-bottom: 1px solid #f0f0f0;\n      \n      .batch-modal-title {\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #1a1a1a;\n      }\n      \n      .batch-modal-close {\n        width: 60rpx;\n        height: 60rpx;\n        border-radius: 50%;\n        background: #f8f9fa;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        \n        &:active {\n          background: #e9ecef;\n        }\n      }\n    }\n    \n    .batch-search-section {\n      padding: 32rpx 48rpx;\n      border-bottom: 1px solid #f0f0f0;\n    }\n    \n    .batch-list-container {\n      height: 600rpx; // 增加批次选择器高度，显示更多批次\n      padding: 24rpx 0;\n      \n      .batch-group {\n        margin-bottom: 40rpx;\n        \n        .batch-group-header {\n          display: flex;\n          align-items: center;\n          padding: 16rpx 48rpx;\n          margin-bottom: 16rpx;\n          \n          .batch-group-title {\n            font-size: 26rpx;\n            font-weight: 600;\n            color: #1a1a1a;\n            margin-left: 12rpx;\n          }\n          \n          .batch-count {\n            font-size: 22rpx;\n            color: #8a94a6;\n            margin-left: auto;\n          }\n        }\n        \n        .batch-item {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          padding: 24rpx 48rpx;\n          border-bottom: 1px solid #f8f9fa;\n          transition: all 0.2s ease;\n          \n          &:active {\n            background: rgba(58, 134, 255, 0.05);\n          }\n          \n          &.selected {\n            background: rgba(58, 134, 255, 0.08);\n            border-left: 4rpx solid #3a86ff;\n          }\n          \n          .batch-item-left {\n            width: 48rpx;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n          }\n          \n          .batch-item-content {\n            flex: 1;\n            margin-left: 16rpx;\n            \n            .batch-name {\n              display: block;\n              font-size: 28rpx;\n              font-weight: 500;\n              color: #1a1a1a;\n              line-height: 1.2;\n            }\n            \n            .batch-date {\n              display: block;\n              font-size: 24rpx;\n              color: #8a94a6;\n              margin-top: 8rpx;\n            }\n          }\n          \n          .batch-type-tag {\n            padding: 8rpx 16rpx;\n            border-radius: 12rpx;\n            font-size: 20rpx;\n            font-weight: 500;\n            \n            &.type-weekly {\n              background: rgba(58, 134, 255, 0.1);\n              color: #3a86ff;\n            }\n            \n            &.type-monthly {\n              background: rgba(16, 185, 129, 0.1);\n              color: #10b981;\n            }\n            \n            &.type-quarterly {\n              background: rgba(139, 92, 246, 0.1);\n              color: #8b5cf6;\n            }\n            \n            &.type-yearly {\n              background: rgba(245, 158, 11, 0.1);\n              color: #f59e0b;\n            }\n            \n            &.type-special {\n              background: rgba(239, 68, 68, 0.1);\n              color: #ef4444;\n            }\n          }\n          \n          .selected-icon {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            width: 32rpx;\n            height: 32rpx;\n            border-radius: 50%;\n            background: rgba(58, 134, 255, 0.1);\n          }\n        }\n        \n        .show-more-btn {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 24rpx 48rpx;\n          color: #3a86ff;\n          transition: all 0.2s ease;\n          \n          &:active {\n            background: rgba(58, 134, 255, 0.05);\n          }\n          \n          .show-more-text {\n            font-size: 26rpx;\n            margin-right: 8rpx;\n          }\n        }\n      }\n      \n      .batch-empty-state {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 80rpx 48rpx;\n        \n        .empty-text {\n          font-size: 26rpx;\n          color: #8a94a6;\n          margin-top: 24rpx;\n        }\n      }\n    }\n    \n    .batch-modal-footer {\n      display: flex;\n      gap: 24rpx;\n      padding: 32rpx 48rpx;\n      padding-bottom: calc(32rpx + env(safe-area-inset-bottom));\n      border-top: 1px solid #f0f0f0;\n      \n      .batch-footer-btn {\n        flex: 1;\n        height: 80rpx;\n        border-radius: 16rpx;\n        font-size: 28rpx;\n        font-weight: 500;\n        border: none;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        gap: 12rpx;\n        transition: all 0.2s ease;\n        \n        &.secondary {\n          background: rgba(58, 134, 255, 0.1);\n          color: #3a86ff;\n          \n          &:active {\n            background: rgba(58, 134, 255, 0.2);\n            transform: scale(0.98);\n          }\n        }\n        \n        &.primary {\n          background: linear-gradient(135deg, #3a86ff 0%, #2563eb 100%);\n          color: #FFFFFF;\n          \n          &:active {\n            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);\n            transform: scale(0.98);\n          }\n        }\n      }\n    }\n  }\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add-record.vue?vue&type=style&index=0&id=0c706de4&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add-record.vue?vue&type=style&index=0&id=0c706de4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558443256\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
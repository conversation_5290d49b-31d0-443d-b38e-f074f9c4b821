<template>
	<view class="container">
		<!-- 用户信息卡片 -->
		<view class="user-card" @click="goToLogin" v-if="!hasLogin">
			<view class="avatar-section">
				<view class="default-avatar">
					<uni-icons type="person-filled" size="40" color="#FFFFFF"></uni-icons>
				</view>
			</view>
			<view class="user-info">
				<text class="username login-prompt">点击登录</text>
				<text class="login-tip">登录后可查看更多功能</text>
			</view>
			<view class="card-arrow">
				<uni-icons type="right" size="16" color="#BBBBBB"></uni-icons>
			</view>
		</view>
		
		<!-- 已登录用户信息卡片 -->
		<view class="user-card" v-else>
			<view class="avatar-section">
				<!-- 头像容器，始终显示蓝色背景作为加载状态 -->
				<view class="avatar-container">
					<!-- 默认背景（蓝色+图标），始终存在 -->
					<view class="default-avatar-bg">
						<uni-icons type="person-filled" size="40" color="#FFFFFF"></uni-icons>
					</view>
					<!-- 真实头像，加载成功后覆盖默认背景 -->
					<image
						class="avatar-image"
						v-if="userInfo.avatar_file && userInfo.avatar_file.url"
						:src="userInfo.avatar_file.url"
						mode="aspectFill"
						@load="onAvatarLoad"
						@error="onAvatarError"
						:style="{ opacity: avatarLoaded ? 1 : 0 }">
					</image>
					<image
						class="avatar-image"
						v-else-if="userInfo.avatar"
						:src="userInfo.avatar"
						mode="aspectFill"
						@load="onAvatarLoad"
						@error="onAvatarError"
						:style="{ opacity: avatarLoaded ? 1 : 0 }">
					</image>
				</view>
			</view>
			<view class="user-info">
				<text class="username">{{userInfo.nickname || userInfo.username || '未设置昵称'}}</text>
				<view class="role-tags" v-if="roleNames && roleNames.length > 0">
					<text class="role-tag" v-for="(role, index) in roleNames" :key="index">{{role}}</text>
				</view>
			</view>
		</view>
		
		<!-- 登录后才显示功能区 -->
		<block v-if="hasLogin">
			<!-- 综合中心 - 集中展示各种业务功能入口 -->
			<view class="patrol-section">
				<view class="patrol-title">综合中心</view>
				<scroll-view scroll-x="true" class="patrol-scroll" :show-scrollbar="false">
					<view class="patrol-grid">
						<!-- 点位管理入口  -->
						<view class="patrol-item" @click="navTo('/pages/patrol_pkg/point/index')" v-if="uniIDHasRole('supervisor') || uniIDHasRole('PM') || uniIDHasRole('GM') || uniIDHasRole('admin')">
							<view class="patrol-icon point-icon">
								<uni-icons type="location-filled" size="24" color="#FFFFFF"></uni-icons>
							</view>
							<text class="patrol-text">点位管理</text>
						</view>

						<!-- 班次时间入口 -->
						<view class="patrol-item" @click="navTo('/pages/patrol_pkg/shift/index')" v-if="uniIDHasRole('supervisor') || uniIDHasRole('PM') || uniIDHasRole('GM') || uniIDHasRole('admin')">
							<view class="patrol-icon shift-icon">
								<uni-icons type="calendar" size="24" color="#FFFFFF"></uni-icons>
							</view>
							<text class="patrol-text">班次管理</text>
						</view>
						
						<!-- 线路管理入口 -->
						<view class="patrol-item" @click="navTo('/pages/patrol_pkg/route/index')" v-if="uniIDHasRole('supervisor') || uniIDHasRole('PM') || uniIDHasRole('GM') || uniIDHasRole('admin')">
							<view class="patrol-icon route-icon">
								<uni-icons type="map-filled" size="24" color="#FFFFFF"></uni-icons>
							</view>
							<text class="patrol-text">线路管理</text>
						</view>
						
						<!-- 任务管理入口 -->
						<view class="patrol-item" @click="navTo('/pages/patrol_pkg/task/index')">
							<view class="patrol-icon task-icon">
								<uni-icons type="flag-filled" size="24" color="#FFFFFF"></uni-icons>
							</view>
							<text class="patrol-text">任务管理</text>
						</view>
						
						<!-- 巡视记录入口 -->
						<view class="patrol-item" @click="navTo('/pages/patrol_pkg/record/index')">
							<view class="patrol-icon record-icon">
								<uni-icons type="bars" size="24" color="#FFFFFF"></uni-icons>
							</view>
							<text class="patrol-text">巡视记录</text>
						</view>
						
						<!-- 二维码批量管理入口 -->
						<view class="patrol-item" @click="navTo('/pages/patrol_pkg/point/qrcode-batch')" v-if="uniIDHasRole('reviser') || uniIDHasRole('supervisor') || uniIDHasRole('PM') || uniIDHasRole('GM') || uniIDHasRole('admin')">
							<view class="patrol-icon qrcode-icon">
								<uni-icons type="scan" size="24" color="#FFFFFF"></uni-icons>
							</view>
							<text class="patrol-text">二维码管理</text>
						</view>
						
						<!-- 荣誉展厅入口 -->
						<view class="patrol-item" @click="navTo('/pages/honor_pkg/gallery/index')">
							<view class="patrol-icon gallery-icon">
								<uni-icons type="star-filled" size="24" color="#FFFFFF"></uni-icons>
							</view>
							<text class="patrol-text">荣誉展厅</text>
						</view>
					</view>
				</scroll-view>
			</view>
			
			<!-- 功能按钮区域 -->
			<view class="action-list">
				<view class="action-item" @click="goToReadNewsLog">
					<view class="action-icon news-icon">
						<uni-icons type="notification-filled" size="22" color="#FFFFFF"></uni-icons>
					</view>
					<view class="action-content">
						<text class="action-text">公告通知</text>
					</view>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>
				<view class="action-item" @click="checkExportPermission">
					<view class="action-icon export-icon">
						<uni-icons type="download" size="22" color="#FFFFFF"></uni-icons>
					</view>
					<view class="action-content">
						<text class="action-text">文档导出</text>
					</view>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>
				<!-- 负责人任务入口，仅负责人可见 -->
				<view class="action-item" @click="goToResponsibleTasks" v-if="hasResponsiblePermission">
					<view class="action-icon responsible-tasks-icon">
						<uni-icons type="list" size="22" color="#FFFFFF"></uni-icons>
					</view>
					<view class="action-content">
						<text class="action-text">我的任务</text>
						<text class="action-badge" v-if="responsibleTaskCount > 0">{{responsibleTaskCount}}</text>
					</view>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>
				<!-- 用户管理入口，仅管理员可见 -->
				<view class="action-item" @click="goToUserManagement" v-if="hasAdminPermission">
					<view class="action-icon user-management-icon">
						<uni-icons type="person-filled" size="22" color="#FFFFFF"></uni-icons>
					</view>
					<view class="action-content">
						<text class="action-text">用户管理</text>
					</view>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>

				<!-- 任务监督入口，仅厂长可见 -->
				<view class="action-item" @click="goToGMSupervision" v-if="hasGMPermission">
					<view class="action-icon gm-supervision-icon">
						<uni-icons type="eye-filled" size="22" color="#FFFFFF"></uni-icons>
					</view>
					<view class="action-content">
						<text class="action-text">任务监督</text>
						<text class="action-badge" v-if="supervisionTaskCount > 0">{{supervisionTaskCount}}</text>
					</view>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>

				<view class="action-item" @click="modifyNickname">
					<view class="action-icon settings-icon">
						<uni-icons type="gear" size="22" color="#FFFFFF"></uni-icons>
					</view>
					<view class="action-content">
						<text class="action-text">用户设置</text>
					</view>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>
				<view class="action-item" @click="logout">
					<view class="action-icon logout-icon">
						<uni-icons type="closeempty" size="22" color="#FFFFFF"></uni-icons>
					</view>
					<view class="action-content">
						<text class="action-text logout-text">退出登录</text>
					</view>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>
			</view>
			
			<!-- 待办事项区域 -->
			<view class="todo-section" v-if="hasLogin && hasRole">
				<view class="section-header">
					<text class="section-title">我的待办</text>
					<view class="todo-count-wrapper" v-if="hasRole" @click="goToTodoList">
						<text class="todo-count" v-if="todoCount > 0">共{{todoCount}}项待处理</text>
						<text class="todo-count" v-else>暂无待办</text>
					</view>
				</view>
				
				<view class="todo-list" v-if="todoList.length > 0">
					<view class="todo-item" v-for="(item, index) in todoList" :key="index" @click="handleTodoClick(item)">
						<view class="todo-content">
							<view class="todo-title">
								<text class="name">{{item.name}}</text>
								<text class="project">{{getProjectName(item.project)}}</text>
							</view>
							<text class="description">{{item.description}}</text>
							<view class="todo-footer">
								<text class="time">{{getTodoTimeText(item)}}</text>
								<text class="todo-type">{{getTodoTypeText(item.workflowStatus)}}</text>
							</view>
						</view>
						<uni-icons type="right" size="16" color="#BBBBBB"></uni-icons>
					</view>
				</view>
				
				<p-empty-state v-else 
					:text="hasRole ? '暂无待办事项' : '权限不够无法查看待办事项'" 
					image="/static/empty/empty_todo.png">
				</p-empty-state>
			</view>
		</block>
		
		<!-- 未登录时显示提示信息 -->
		<p-empty-state v-else
			text="登录后查看更多功能"
			image="/static/empty/empty_todo.png">
		</p-empty-state>
	</view>
</template>

<script>
	import { store, mutations } from '@/uni_modules/uni-id-pages/common/store.js'
	import { checkUpdate } from '@/utils/wx-utils.js';
	import cacheManager, { getCacheKey } from '@/utils/cache.js';
	import PEmptyState from '@/components/p-empty-state/p-empty-state.vue';
	import { formatDate as formatDateUtil } from '@/utils/date.js';
	import todoBadgeManager from '@/utils/todo-badge.js'; // 重新添加导入
	
	export default {
		components: {
			PEmptyState
		},
		data() {
			// 初始化 - 先显示缓存，再异步更新
			const token = uni.getStorageSync('uni_id_token') || '';
			const hasLogin = !!token;
			
			// 获取用户信息 - 使用自己的缓存系统
			const cachedUserInfo = cacheManager.get(cacheManager.cacheKeys.USER_INFO) || {};
			const userInfo = {
				nickname: cachedUserInfo.nickname || '加载中...',
				username: cachedUserInfo.username || '',
				avatar_file: cachedUserInfo.avatar_file || null,
				_id: cachedUserInfo._id || ''
			};
			
			return {
				roleNames: [],
				todoList: [],
				todoCount: 0,
				userRole: [],
				hasRole: false,
				isWechatPlatform: false,
				isRefreshing: false,
				lastRefreshTime: 0,
				localUserInfo: userInfo,
				localHasLogin: hasLogin,
				todoUpdateTimer: null,
				isTokenValid: true,
				responsibleTaskCount: 0,
				supervisionTaskCount: 0, // 厂长监督任务数量
				avatarLoaded: false, // 头像加载状态
			}
		},
		computed: {
			userInfo() {
				// 直接使用本地用户信息（已经在data()中优化了初始化逻辑）
				return this.localUserInfo || {};
			},
			hasLogin() {
				return (store.hasLogin || this.localHasLogin) && this.isTokenValid;
			},
			// 检查是否有管理员权限
			hasAdminPermission() {
				return this.userRole.some(role => 
					['admin'].includes(role)
				);
			},
			// 检查是否有负责人权限
			hasResponsiblePermission() {
				return this.userRole.some(role =>
					['responsible'].includes(role)
				);
			},
			// 检查是否有厂长权限
			hasGMPermission() {
				return this.userRole.some(role =>
					['GM', 'admin'].includes(role)
				);
			},

		},
		created() {
			// 监听登录成功事件
			uni.$on('uni-id-pages-login-success', () => {
				// 清除角色缓存
				cacheManager.remove(cacheManager.cacheKeys.USER_ROLE);
				// 立即刷新页面数据
				this.refreshData(true);
				// 使用新的登录成功处理方法更新角标
				todoBadgeManager.onLoginSuccess();
			});
			
			// 监听待办数量更新事件
			uni.$on('todo-count-updated', (count) => {
				// 只有在登录状态且有角色权限时才更新
				if (this.hasLogin && this.hasRole) {
					this.todoCount = count;
					// 同时更新待办列表
					this.getTodoList();
				}
			});
			
			// 监听刷新待办列表事件
			uni.$on('refresh-todo-list', () => {
				if (this.hasLogin && this.hasRole) {
					// 直接刷新数据，不显示loading
					this.refreshData(false);
				}
			});
			
			// 监听全局强制登出UI更新事件（当在用户中心页面时）
			uni.$on('force-logout-ui-update', () => {
				this.performLogout(false); // 不显示toast，因为App.vue已经显示了
			});
			
			// 监听反馈更新事件 - 添加这个监听来处理审核页面的操作
			uni.$on('feedback-updated', () => {
				if (this.hasLogin && this.hasRole) {
					// 延迟100ms执行，确保云端数据已更新
					setTimeout(() => {
						this.refreshData(false);
					}, 100);
				}
			});
			
			// 监听用户中心页面刷新事件 - 专门针对审核页面操作后的刷新
			uni.$on('ucenter-need-refresh', (data) => {
				if (this.hasLogin && this.hasRole) {
					console.log('收到用户中心刷新事件:', data);
					// 立即刷新数据，不等待延迟
					this.refreshData(false);
				}
			});
			
			// 监听角标管理器的跨设备更新事件
			uni.$on('cross-device-update-detected', (data) => {
				if (this.hasLogin && this.hasRole) {
					// 智能判断是否需要刷新
					const shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);
					if (shouldRefresh) {
						console.log('用户中心收到跨设备更新通知，静默刷新数据');
						// 静默刷新数据，不显示提示
						this.refreshData(false);
					}
				}
			});
			
			// 检测是否为微信环境
			// #ifdef MP-WEIXIN
			this.isWechatPlatform = true;
			// #endif
			
			// 启动待办事项数量定时更新
			this.startTodoUpdateTimer();

			// 添加页面可见性变化监听（针对H5端）
			// #ifdef H5
			document.addEventListener('visibilitychange', this.handleVisibilityChange);
			// #endif
		},
		beforeDestroy() {
			// 移除事件监听
			uni.$off('uni-id-pages-login-success');
			uni.$off('todo-count-updated');
			uni.$off('refresh-todo-list');
			uni.$off('force-logout-ui-update');
			uni.$off('feedback-updated'); // 移除新添加的监听
			uni.$off('ucenter-need-refresh'); // 移除用户中心刷新事件监听
			uni.$off('cross-device-update-detected'); // 移除跨设备更新事件监听
			
			// 清除定时器
			this.clearTodoUpdateTimer();

			// 移除页面可见性变化监听（针对H5端）
			// #ifdef H5
			document.removeEventListener('visibilitychange', this.handleVisibilityChange);
			// #endif
		},
		onLoad() {
			if (this.hasLogin) {
				// 直接刷新所有数据
				this.refreshData(true);
			}
		},
		onShow() {
			if (this.hasLogin) {
				// 调整缓存时间为30秒，配合跨设备同步机制，避免过度刷新
				const now = Date.now();
				if (now - this.lastRefreshTime > 30000) {
					this.refreshData(false);
				}
			}
			
			// 检查更新
			// #ifdef MP-WEIXIN
			checkUpdate();
			// #endif
			
			// 每次显示页面时检查token状态
			this.checkTokenStatus();
			
			// 强制同步角标状态，确保角标与页面数据一致
			if (this.hasLogin && this.hasRole) {
				setTimeout(() => {
					todoBadgeManager.forceSyncBadge();
				}, 300);
			}
		},
		onPullDownRefresh() {
			if (this.hasLogin) {
				// 刷新数据
				this.refreshData(true).then(() => {
					// 完成下拉刷新
					uni.stopPullDownRefresh();
					// 添加刷新成功提示
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					});
				}).catch(e => {
					console.error('刷新数据失败:', e);
					uni.stopPullDownRefresh();
					// 添加刷新失败提示
					uni.showToast({
						title: '刷新失败',
						icon: 'error',
						duration: 1500
					});
				});
			} else {
				// 未登录状态下直接停止下拉刷新
				uni.stopPullDownRefresh();
			}
		},
		watch: {
			hasLogin(newVal) {
				if (newVal) {
					// 用户登录状态变为已登录，立即获取用户信息和待办数量
					this.$nextTick(() => {
						this.refreshData(true);
					});
				}
			},
			// 监听用户头像变化，重置加载状态
			'localUserInfo.avatar_file.url': {
				handler(newVal, oldVal) {
					if (newVal !== oldVal) {
						this.avatarLoaded = false;
					}
				},
				deep: true
			},
			'localUserInfo.avatar': {
				handler(newVal, oldVal) {
					if (newVal !== oldVal) {
						this.avatarLoaded = false;
					}
				}
			}
		},
		methods: {
			// 头像加载成功
			onAvatarLoad() {
				this.avatarLoaded = true;
			},

			// 头像加载失败
			onAvatarError() {
				this.avatarLoaded = false;
				console.log('头像加载失败，显示默认头像');
			},

			// 显示功能开发中提示
			showFeatureInDevelopment() {
				uni.showToast({
					title: '功能开发中，敬请期待',
					icon: 'none',
					duration: 1000
				});
			},
			
			// 添加页面可见性变化处理函数（针对H5端）
			handleVisibilityChange() {
				// #ifdef H5
				if (document.visibilityState === 'visible') {
					// 页面变为可见时，立即刷新数据
					if (this.hasLogin && this.hasRole) {
						this.refreshData(false);
					}
				}
				// #endif
			},
			
			// 启动待办事项数量定时更新
			startTodoUpdateTimer() {
				// 先清除可能存在的定时器
				this.clearTodoUpdateTimer();
				
				// 简化定时器：只做基础的数据同步，跨设备检查交给角标管理器处理
				this.todoUpdateTimer = setInterval(() => {
					// 只有在登录状态下才更新
					if (this.hasLogin && this.hasRole) {
						this.updateTodoCountFromBadge();
					}
				}, 30000); // 调整为30秒，避免与角标管理器冲突
				
				// 立即执行一次
				if (this.hasLogin && this.hasRole) {
					this.updateTodoCountFromBadge();
				}
			},
			
			// 清除待办事项更新定时器
			clearTodoUpdateTimer() {
				if (this.todoUpdateTimer) {
					clearInterval(this.todoUpdateTimer);
					this.todoUpdateTimer = null;
				}
			},
			
			// 从角标管理器更新待办数量
			async updateTodoCountFromBadge() {
				try {
					// 直接从todoBadgeManager获取最新的待办数量
					const count = await todoBadgeManager.getTodoCount();
					// 更新本地待办数量
					this.todoCount = count;
				} catch (e) {
					console.error('更新待办数量失败:', e);
				}
			},
			
			// 统一的数据刷新方法，避免重复请求
			async refreshData(showLoading = false) {
				if (!this.hasLogin || this.isRefreshing) return Promise.resolve();
				
				this.isRefreshing = true;
				
				if (showLoading) {
					uni.showLoading({
						title: '加载中...'
					});
				}
				
				try {
					// 获取用户信息
					await this.getUserInfo();
					
					// 智能检查：如果已有完整的角色信息，跳过角色获取
					if (!this.userRole || this.userRole.length === 0 || !this.roleNames || this.roleNames.length === 0) {
						await this.getUserRole();
					}
					
					// 如果有特定角色，再获取待办列表
					if (this.hasRole) {
						await this.getTodoList();
					} else {
						this.todoList = [];
						this.todoCount = 0;
					}
					
					// 如果有负责人权限，获取任务数量
					if (this.hasResponsiblePermission) {
						await this.getResponsibleTaskCount();
					} else {
						this.responsibleTaskCount = 0;
					}

					// 如果有厂长权限，获取监督任务数量
					if (this.hasGMPermission) {
						await this.getSupervisionTaskCount();
					} else {
						this.supervisionTaskCount = 0;
					}

					// 更新最后刷新时间
					this.lastRefreshTime = Date.now();
					
					return Promise.resolve();
				} catch (e) {
					// 错误处理
					// #ifdef DEBUG
					console.error('刷新数据失败:', e);
					// #endif
					return Promise.reject(e);
				} finally {
					if (showLoading) {
						uni.hideLoading();
					}
					this.isRefreshing = false;
				}
			},
			
			// 获取用户角色 - 改用cacheManager统一管理缓存
			async getUserRole() {
				try {
					if (!this.hasLogin) {
						this.roleNames = ['未登录'];
						this.userRole = [];
						this.hasRole = false;
						return;
					}
					
					// 尝试从缓存获取角色信息
					const cachedRole = cacheManager.get(cacheManager.cacheKeys.USER_ROLE);
					if (cachedRole) {
						this.userRole = cachedRole.userRole;
						this.roleNames = cachedRole.roleNames;
						this.hasRole = cachedRole.hasRole;
						
						return;
					}
					
					const db = uniCloud.database();
					const { result } = await db.collection('uni-id-users')
						.where("'_id' == $cloudEnv_uid")
						.field('role')
						.get();
					
					if (result.data && result.data.length > 0) {
						this.userRole = result.data[0].role || [];
						
						// 手动设置角色名称映射
						const roleNameMap = {
							'admin': '管理员',
							'responsible': '责任人',
							'reviser': '发布人',
							'supervisor': '主管',
							'PM': '副厂长',
							'GM': '厂长',
							'logistics': '后勤员',
							'dispatch': '调度员',
							'Integrated': '综合员',
							'operator': '设备员',
							'technician': '工艺员',
							'mechanic': '技术员',
							'user': '普通员工'
						};
						
						// 检查用户是否有特定角色
						this.hasRole = this.userRole.some(role => 
							['supervisor', 'PM', 'GM', 'admin', 'responsible'].includes(role)
						);
						
						// 将角色ID转换为中文名称
						if (this.userRole.length > 0) {
							this.roleNames = this.userRole.map(roleId => 
								roleNameMap[roleId] || roleId
							);
						} else {
							// 如果没有角色，显示为普通用户
							this.roleNames = ['普通用户'];
						}
						
						// 缓存角色信息，有效期60分钟
						cacheManager.set(cacheManager.cacheKeys.USER_ROLE, {
							userRole: this.userRole,
							roleNames: this.roleNames,
							hasRole: this.hasRole
						}, 60);
					} else {
						this.roleNames = ['普通用户'];
						this.userRole = [];
						this.hasRole = false;
					}
				} catch (e) {
					// #ifdef DEBUG
					console.error('获取用户角色失败:', e);
					// #endif
					
					// 检查是否是token相关的错误
					const errorMessage = e.message || e.toString();
					if (errorMessage.includes('token') || errorMessage.includes('unauthorized') || errorMessage.includes('expired')) {
						console.log('检测到token相关错误，清除待办数据');
						// 清除待办相关数据
						this.todoList = [];
						this.todoCount = 0;
						this.hasRole = false;
						// 显示为普通用户，但不强制退出登录
						this.roleNames = ['普通用户'];
					} else {
						// 其他错误，默认处理
						this.roleNames = ['普通用户'];
					}
					
					this.userRole = [];
					this.hasRole = false;
				}
			},
			
			// 获取项目名称 - 简化为直接返回项目名称
			getProjectName(projectId) {
				// 由于feedback表的project字段直接存储文本值，直接返回即可
				return projectId || '未分类';
			},
			
			// 获取待办列表 - 优化查询逻辑
			async getTodoList() {
				if (!this.hasLogin || !this.hasRole) {
					this.todoList = [];
					this.todoCount = 0;
					return;
				}
				
				try {
					const db = uniCloud.database();
					const dbCmd = db.command;
					
					// 简化查询条件构建
					const whereConditions = this.buildTodoQueryConditions(dbCmd);
					
					// 先获取总数
					const countRes = await db.collection('feedback')
						.where(whereConditions)
						.count();
					
					if (countRes.result && countRes.result.total !== undefined) {
						this.todoCount = countRes.result.total;
					}
					
					// 增加待办项数量限制为3条，优化加载性能
					if (this.todoCount > 0) {
						const res = await db.collection('feedback')
							.where(whereConditions)
							.orderBy('createTime', 'desc')
							.limit(3) // 只显示前3条，提高性能
							.get();
						
						if (res.result && res.result.data) {
							this.todoList = res.result.data;
						} else {
							this.todoList = [];
						}
					} else {
						this.todoList = [];
					}
				} catch (e) {
					// #ifdef DEBUG
					console.error('获取待办列表失败', e);
					// #endif
					this.todoList = [];
					this.todoCount = 0;
				}
			},
			
			// 获取负责人任务数量
			async getResponsibleTaskCount() {
				if (!this.hasLogin || !this.hasResponsiblePermission) {
					this.responsibleTaskCount = 0;
					return;
				}
				
				try {
					const res = await uniCloud.callFunction({
						name: 'feedback-list',
						data: {
							action: 'getMyTasks',
							status: 'assigned_to_responsible'
						}
					});
					
					if (res.result && res.result.code === 0) {
						this.responsibleTaskCount = res.result.data.stats?.assigned || 0;
					}
				} catch (error) {
					console.error('获取负责人任务数量失败:', error);
					this.responsibleTaskCount = 0;
				}
			},

			// 获取厂长监督任务数量
			async getSupervisionTaskCount() {
				if (!this.hasLogin || !this.hasGMPermission) {
					this.supervisionTaskCount = 0;
					return;
				}

				try {
					const res = await uniCloud.callFunction({
						name: 'feedback-list',
						data: {
							action: 'getGMSupervisionTasks'
						}
					});

					if (res.result && res.result.code === 0) {
						const stats = res.result.data.stats || {};
						// 计算需要关注的任务数量：执行中 + 待确认
						this.supervisionTaskCount = (stats.assigned || 0) + (stats.pending || 0);
					}
				} catch (error) {
					console.error('获取厂长监督任务数量失败:', error);
					this.supervisionTaskCount = 0;
				}
			},

			// 构建待办查询条件（统一待办版本）
			buildTodoQueryConditions(dbCmd) {
				const conditions = [];
				
				// 1. 审核待办
				if (this.userRole.includes('supervisor')) {
					// supervisor看到的是待主管审核的项目
					conditions.push({ workflowStatus: 'pending_supervisor' });
				}
				
				if (this.userRole.includes('PM')) {
					// PM看到的是待副厂长审核的项目
					conditions.push({ workflowStatus: 'pending_pm' });
				}
				
				if (this.userRole.includes('GM')) {
					// GM看到的是待厂长审核的项目 + 待指派负责人的项目 + 最终确认的项目
					conditions.push({ workflowStatus: 'pending_gm' });
					conditions.push({ workflowStatus: 'gm_approved_pending_assign' });
					conditions.push({ workflowStatus: 'completed_by_responsible' });
				}
				
				// 2. 指派任务待办（如果用户有responsible角色）
				if (this.userRole.includes('responsible')) {
					try {
						const currentUserInfo = uniCloud.getCurrentUserInfo();
						const userId = currentUserInfo?.uid;
						if (userId) {
							// 待完成的指派任务
							conditions.push({
								workflowStatus: 'assigned_to_responsible',
								responsibleUserId: userId
							});
						}
					} catch (e) {
						console.warn('获取用户ID失败:', e);
					}
				}
				
				// 3. 管理员可以看到所有待办，但不包括指派给负责人的任务
				if (this.userRole.includes('admin')) {
					return {
						workflowStatus: dbCmd.in([
							'pending_supervisor', 
							'pending_pm', 
							'pending_gm', 
							'gm_approved_pending_assign',
							// 移除 'assigned_to_responsible',
							'completed_by_responsible'
						])
					};
				}
				
				// 如果有条件，使用or查询
				if (conditions.length > 0) {
					return dbCmd.or(conditions);
				}
				
				// 默认返回空条件
				return {};
			},
			
			// 格式化日期
			formatDate(timestamp) {
				if (!timestamp) return '';
				return formatDateUtil(timestamp);
			},
			
			// 获取待办时间文本
			getTodoTimeText(item) {
				// 自定义时间格式：MM-DD HH:mm（去掉年份和秒数）
				const timeFormat = 'MM-DD HH:mm';
				if (item.workflowStatus === 'assigned_to_responsible' && item.assignedTime) {
					return `指派时间：${formatDateUtil(item.assignedTime, timeFormat)}`;
				} else if (item.workflowStatus === 'completed_by_responsible' && item.completedByResponsibleTime) {
					return `完成时间：${formatDateUtil(item.completedByResponsibleTime, timeFormat)}`;
				} else {
					return `提交时间：${formatDateUtil(item.createTime, timeFormat)}`;
				}
			},
			
			// 获取待办类型文本
			getTodoTypeText(status) {
				const typeMap = {
					'pending_supervisor': '待主管审核',
					'pending_pm': '待副厂长审核',
					'pending_gm': '待厂长审核',
					'gm_approved_pending_assign': '待指派负责人',
					'assigned_to_responsible': '待我完成',
					'completed_by_responsible': '待最终确认'
				};
				return typeMap[status] || '待处理';
			},
			
			// 处理待办点击事件
			handleTodoClick(item) {
				if (item.workflowStatus === 'assigned_to_responsible') {
					// 指派任务，跳转到任务管理页面
					uni.navigateTo({
						url: '/pages/ucenter_pkg/responsible-tasks'
					});
				} else {
					// 审核待办，跳转到审核页面
					this.goToExamine(item._id);
				}
			},
			
			// 跳转到审核页面
			goToExamine(id) {
				uni.navigateTo({
					url: `/pages/feedback_pkg/examine?id=${id}`,
					events: {
						refreshData: () => {
							this.refreshData(false);
						}
					}
				});
			},
			
			// 公告通知
			goToReadNewsLog() {
				uni.navigateTo({
					url: '/pages/notice/list'
				});
			},
			
			// Excel导出
			exportexcel() {
				uni.navigateTo({
					url: '/pages/ucenter_pkg/export-excel'
				});
			},
			
			// 检查导出权限
			checkExportPermission() {
				// 检查用户是否有特定角色
				const hasExportPermission = this.userRole.some(role => 
					['reviser', 'supervisor', 'PM', 'GM', 'admin', 'dispatch'].includes(role)
				);
				
				if (hasExportPermission) {
					// 有权限，跳转到导出页面
					this.exportexcel();
				} else {
					// 无权限，显示提示
					uni.showToast({
						title: '权限不够,无法查看',
						icon: 'none',
						duration: 2000
					});
				}
			},
			
			// 跳转到负责人任务页面
			goToResponsibleTasks() {
				if (!this.hasResponsiblePermission) {
					uni.showToast({
						title: '权限不足，无法访问任务管理',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				
				uni.navigateTo({
					url: '/pages/ucenter_pkg/responsible-tasks',
					fail: (err) => {
						console.error('跳转任务管理页面失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 跳转到用户管理页面
			goToUserManagement() {
				if (!this.hasAdminPermission) {
					uni.showToast({
						title: '权限不足，无法访问用户管理',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				
				uni.navigateTo({
					url: '/pages/ucenter_pkg/user-management',
					fail: (err) => {
						console.error('跳转用户管理页面失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},

			// 跳转到厂长监督页面
			goToGMSupervision() {
				if (!this.hasGMPermission) {
					uni.showToast({
						title: '权限不足，只有厂长才能访问',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				uni.navigateTo({
					url: '/pages/ucenter_pkg/gm-supervision',
					fail: (err) => {
						console.error('跳转厂长监督页面失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},

			// 修改昵称/用户设置
			modifyNickname() {
				// 直接跳转到uni-id-pages提供的修改页面
				uni.navigateTo({
					url: '/uni_modules/uni-id-pages/pages/userinfo/userinfo?showLoginManage=false&showUserInfo=false&showSet=false&showEdit=true'
				});
			},

			
			// 退出登录
			async logout() {
				try {
					const res = await uni.showModal({
						title: '提示',
						content: '确定要退出登录吗？',
						confirmText: '退出',
						cancelText: '取消'
					});
					
					if (res.confirm) {
						uni.showLoading({
							title: '退出中...',
							mask: true
						});
						
						// 先清除角标
						todoBadgeManager.forceCleanBadge();
						
						try {
							const uniIdCo = uniCloud.importObject('uni-id-co');
							await uniIdCo.logout();
						} catch (e) {
							// 即使云函数调用失败，也执行本地登出
							console.error('云函数退出登录失败:', e);
						}
						
						// 执行本地退出清理
						this.performLogout(false);
						
						// 再次确保角标被清除
						setTimeout(() => {
							todoBadgeManager.forceCleanBadge();
						}, 100);
						
						uni.hideLoading();
						uni.showToast({
							title: '已退出登录',
							icon: 'success'
						});
					}
				} catch (e) {
					uni.showToast({
						title: '退出登录失败',
						icon: 'none'
					});
				}
			},
			
			// 跳转到待办列表页面
			goToTodoList() {
				uni.navigateTo({
					url: '/pages/ucenter_pkg/todo'
				});
			},
			
			// 根据平台自动跳转到相应的登录页面
			goToLogin() {
				// 微信小程序环境
				// #ifdef MP-WEIXIN
				uni.navigateTo({
					url: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd'
				});
				// #endif
				
				// 非微信小程序环境
				// #ifndef MP-WEIXIN
				uni.navigateTo({
					url: '/uni_modules/uni-id-pages/pages/login/login-withpwd'
				});
				// #endif
			},
			
			// 获取用户信息
			async getUserInfo() {
				try {
					const token = uni.getStorageSync('uni_id_token') || '';
					this.localHasLogin = !!token;
					
					if (!this.localHasLogin) {
						this.localUserInfo = {};
						return;
					}
					
					// 先尝试从自己的缓存获取
					const cachedInfo = cacheManager.get(cacheManager.cacheKeys.USER_INFO) || {};
					if (cachedInfo.nickname || cachedInfo.username) {
						this.localUserInfo = cachedInfo;
						return;
					}
					
					// 缓存中没有完整信息，从数据库获取
					const db = uniCloud.database();
					const { result } = await db.collection('uni-id-users')
						.where("'_id' == $cloudEnv_uid")
						.field('nickname, username, avatar_file')
						.get();
					
					if (result.data && result.data.length > 0) {
						const userData = result.data[0];
						this.localUserInfo = {
							_id: userData._id,
							nickname: userData.nickname || userData.username || '未设置昵称',
							username: userData.username || userData.nickname || '未设置昵称',
							avatar_file: userData.avatar_file || null
						};

						// 重置头像加载状态，确保新头像重新加载
						this.avatarLoaded = false;

						// 更新自己的缓存系统
						cacheManager.set(cacheManager.cacheKeys.USER_INFO, this.localUserInfo);
					} else {
						this.localUserInfo = { nickname: '用户', username: 'user' };
						this.avatarLoaded = false;
					}
				} catch (e) {
					console.error('获取用户信息失败:', e);
					this.localUserInfo = { nickname: '用户', username: 'user' };
					this.avatarLoaded = false;
				}
			},
			
			// 导航到登录页
			navToLogin() {
				uni.navigateTo({
					url: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd'
				});
			},
			
			// 导航到指定页面
			navTo(url) {
				// 荣誉展厅特殊处理：预加载优化
				if (url === '/pages/honor_pkg/gallery/index') {
					this.navToHonorGallery(url);
					return;
				}
				
				// 使用缓存优化，记录最近访问的页面
				const recentPages = cacheManager.get('recent_pages', []);
				if (!recentPages.includes(url)) {
					recentPages.unshift(url);
					// 只保留最近10个页面
					if (recentPages.length > 10) {
						recentPages.pop();
					}
					cacheManager.set('recent_pages', recentPages);
				}
				
				uni.navigateTo({
					url,
					fail: (err) => {
						console.error('导航失败:', err);
						uni.showToast({
							title: '页面不存在',
							icon: 'none'
						});
					}
				});
			},
			
			// 荣誉展厅专用导航 - 优化加载体验
			navToHonorGallery(url) {
				// 记录访问历史
				const recentPages = cacheManager.get('recent_pages', []);
				if (!recentPages.includes(url)) {
					recentPages.unshift(url);
					if (recentPages.length > 10) {
						recentPages.pop();
					}
					cacheManager.set('recent_pages', recentPages);
				}
				
				// 保持页面切换动画，但优化加载体验
				uni.navigateTo({
					url,
					animationType: 'slide-in-right', // 使用滑入动画替代默认
					animationDuration: 200,          // 缩短动画时长
					success: () => {
						// 跳转成功后的处理
					},
					fail: (err) => {
						console.error('导航失败:', err);
						// 降级到普通跳转
						uni.navigateTo({
							url,
							fail: (err2) => {
								uni.showToast({
									title: '页面不存在',
									icon: 'none'
								});
							}
						});
					}
				});
			},
			
			// 检查token状态
			async checkTokenStatus() {
				try {
					const token = uni.getStorageSync('uni_id_token');
					if (!token) {
						this.handleTokenInvalid();
						return;
					}
					
					// 验证token
					const tokenValid = await this.validateToken();
					if (!tokenValid) {
						this.handleTokenInvalid();
					}
				} catch (error) {
					console.error('验证登录状态失败:', error);
					this.handleTokenInvalid();
				}
			},
			
			// 验证token的方法
			async validateToken() {
				try {
					const token = uni.getStorageSync('uni_id_token');
					if (!token) {
						return false;
					}
					const tokenExpired = uni.getStorageSync('uni_id_token_expired');
					if (tokenExpired < Date.now()) {
						return false;
					}
					return true;
				} catch (error) {
					console.error('Token validation error:', error);
					return false;
				}
			},
			
			// 统一的登出处理方法
			performLogout(showToast = true) {
				// 第一步：立即设置登录状态为false，确保待办区域立即消失
				this.localHasLogin = false;
				
				// 第二步：清除所有登录相关存储
				uni.removeStorageSync('uni_id_token');
				uni.removeStorageSync('uni_id_token_expired');
				uni.removeStorageSync('uni_id_user');
				uni.removeStorageSync('uni-id-pages-userInfo');
				
				// 第三步：清除缓存
				cacheManager.remove(cacheManager.cacheKeys.USER_INFO);
				cacheManager.remove(cacheManager.cacheKeys.USER_ROLE);
				cacheManager.remove(cacheManager.cacheKeys.PROJECT_OPTIONS);
				
				// 第四步：更新store状态
				mutations.setUserInfo({}, {cover: true});
				
				// 第五步：清除角标
				todoBadgeManager.clearBadge();
				
				// 第六步：清除敏感缓存
				this.clearSensitiveCache();
				
				// 第七步：重置其他页面状态
				Object.assign(this, {
					roleNames: [],
					todoList: [],
					todoCount: 0,
					userRole: [],
					hasRole: false,
					localUserInfo: {},
					isTokenValid: false
				});
				
				// 显示提示
				if (showToast) {
					uni.showToast({
						title: '登录已过期，请重新登录',
						icon: 'none',
						duration: 2000
					});
				}
				
				// 更新页面状态
				this.$nextTick(() => this.getUserInfo());
			},
			
			// 清除敏感缓存数据（与拦截器保持一致的逻辑）
			clearSensitiveCache() {
				try {
					const storageInfo = uni.getStorageInfoSync();
					const keys = storageInfo.keys;
					
					// 定义需要清除的真正敏感数据（个人设备使用场景，重点保护隐私）
					const sensitivePatterns = [
						'user_info_',     // 用户详细信息（可能包含个人隐私）
						'user_mgmt_',     // 用户管理数据（管理员功能）
					];
					
					// 定义需要明确保留的数据
					const preserveKeys = [
						'_DC_STAT_UUID',      // 设备统计标识
						getCacheKey('recent_pages'), // 最近访问页面（动态生成键名）
						'last_app_start_time', // 应用启动时间
						'uni-id-pages-userInfo', // 框架用户信息（可能为空对象）
					];
					
					let clearedCount = 0;
					
					keys.forEach(key => {
						// 检查是否在保留列表中
						if (preserveKeys.includes(key)) {
							return; // 跳过保留的键
						}
						
						// 检查是否匹配敏感数据模式
						const shouldClear = sensitivePatterns.some(pattern => 
							key === pattern || key.startsWith(pattern)
						);
						
						if (shouldClear) {
							uni.removeStorageSync(key);
							clearedCount++;
						}
					});
				} catch (error) {
					console.error('清除敏感缓存失败:', error);
				}
			},
			
			// 处理token失效（兼容原有逻辑）
			handleTokenInvalid() {
				this.performLogout(false);
			},
			
			// 智能判断是否需要刷新
			shouldRefreshOnCrossDeviceUpdate(data) {
				// 如果距离上次刷新时间太短（小于15秒），避免频繁刷新
				const timeSinceLastRefresh = Date.now() - this.lastRefreshTime;
				if (timeSinceLastRefresh < 15000) {
					return false;
				}
				
				// 如果更新类型包含待办相关的操作，需要刷新
				if (data.updateTypes) {
					const relevantTypes = ['workflow_status_changed', 'feedback_submitted'];
					const hasRelevantUpdate = data.updateTypes.some(type => relevantTypes.includes(type));
					if (hasRelevantUpdate) {
						return true;
					}
				}
				
				// 如果有多个更新记录，可能需要刷新
				if (data.updateCount > 1) {
					return true;
				}
				
				// 默认不刷新，避免过度刷新
				return false;
			},
		}
	}
</script>

<style lang="scss" scoped>
	/* #ifndef APP-NVUE */
	view {
		display: flex;
		box-sizing: border-box;
		flex-direction: column;
	}

	page {
		background-color: #f8f9fc;
	}
	/* #endif*/
	
	/* 定义动画 */
	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	.container {
		min-height: 100vh;
		background-color: #f8f9fc;
		padding: 30rpx;
		background: linear-gradient(145deg, #f8faff 0%, #e9f0f8 100%);
		letter-spacing: 1rpx;
	}
	
	/* 用户信息卡片 */
	.user-card {
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 40rpx 30rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);
		margin-bottom: 30rpx;
		position: relative;
		animation: fadeInUp 0.5s ease;
		transition: all 0.3s ease;
		
		&:active {
			transform: scale(0.98);
		}		
	}
	
	.avatar-section {
		margin-right: 30rpx;
	}
	
	.avatar-container {
		width: 150rpx;
		height: 150rpx;
		border-radius: 75rpx;
		position: relative;
		overflow: hidden;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);
	}

	.default-avatar-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 75rpx;
		background: linear-gradient(135deg, #3a86ff 0%, #2563eb 100%);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1;
	}

	.avatar-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 75rpx;
		z-index: 2;
		transition: opacity 0.3s ease;
	}

	/* 未登录状态的默认头像样式 */
	.default-avatar {
		width: 150rpx;
		height: 150rpx;
		border-radius: 75rpx;
		background: linear-gradient(135deg, #3a86ff 0%, #2563eb 100%);
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 8rpx 16rpx rgba(0, 120, 255, 0.2);
	}

	.user-info {
		flex: 1;
	}
	
	.username {
		font-size: 36rpx;
		font-weight: bold;
		color: #2b2e4a;
		margin-bottom: 16rpx;
		display: block;
		text-align: left !important;
		align-self: flex-start;
		width: 100%;
		
		&.login-prompt {
			color: #4a78c9;
		}
	}
	
	.login-tip {
		font-size: 28rpx;
		color: #8a94a6;
		margin-bottom: 16rpx;
	}
	
	.card-arrow {
		position: absolute;
		right: 30rpx;
		top: 50%;
		transform: translateY(-50%);
	}
	
	.role-tags {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}
	
	.role-tag {
		font-size: 24rpx;
		color: #4a78c9;
		background-color: rgba(74, 120, 201, 0.1);
		padding: 6rpx 20rpx;
		border-radius: 30rpx;
		margin-right: 16rpx;
		margin-bottom: 8rpx;
		transition: all 0.2s ease;
		letter-spacing: 1.5rpx;
		
		&:active {
			background-color: rgba(74, 120, 201, 0.2);
		}
	}
	
	/* 登录提示区域 */
	.login-required-tip {
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 60rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);
		margin-top: 30rpx;
		animation: fadeInUp 0.6s ease;
	}
	
	.login-required-image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
		opacity: 0.9;
	}
	
	.login-required-text {
		font-size: 32rpx;
		color: #8a94a6;
		text-align: center;
		letter-spacing: 2rpx;
	}
	
	/* 功能中心区域 */
	.patrol-section {
		background-color: #FFFFFF;
		border-radius: 24rpx;
		margin-bottom: 30rpx;
		padding: 40rpx 30rpx;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);
		animation: fadeInUp 0.5s ease;
		animation-delay: 0.1s;
		animation-fill-mode: both;
	}
	
	.patrol-title {
		font-size: 34rpx;
		font-weight: 600;
		color: #2b2e4a;
		margin-bottom: 36rpx;
		padding-left: 20rpx;
		position: relative;
		letter-spacing: 2rpx;
		
		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 6rpx;
			height: 32rpx;
			background: linear-gradient(180deg, #3a86ff 0%, #2563eb 100%);
			border-radius: 6rpx;
		}
	}
	
	.patrol-scroll {
		white-space: nowrap;
		width: 100%;
	}
	
	/* #ifdef MP-WEIXIN */
	/* 在微信小程序环境下隐藏滚动条 */
	.patrol-scroll ::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		background: transparent;
	}

	.patrol-scroll {
		scrollbar-width: none; /* Firefox */
		-ms-overflow-style: none; /* IE and Edge */
	}
	/* #endif */

	/* #ifndef MP-WEIXIN */
	/* 非微信小程序环境下显示滚动条 */
	.patrol-scroll ::-webkit-scrollbar {
		width: 6px;
		height: 6px;
	}

	.patrol-scroll ::-webkit-scrollbar-thumb {
		background: rgba(0, 0, 0, 0.2);
		border-radius: 3px;
	}

	.patrol-scroll ::-webkit-scrollbar-thumb:hover {
		background: rgba(0, 0, 0, 0.3);
	}

	.patrol-scroll {
		scrollbar-width: thin;
		scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
	}
	/* #endif */
	
	.patrol-grid {
		display: inline-flex;
		flex-direction: row;
		padding: 10rpx 0;
	}
	
	.patrol-item {
		display: inline-flex;
		flex-direction: column;
		align-items: center;
		width: 160rpx;
		margin-right: 50rpx;
		transition: all 0.3s ease;
		
		&:active {
			transform: scale(0.95);
		}
	}
	
	/* 图标样式统一管理 */
	.action-icon, .patrol-icon {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 12rpx;
		transition: all 0.3s ease;
	}
	
	.patrol-icon {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		margin-bottom: 16rpx;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);
		position: relative;
		overflow: hidden;
		
		&:before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
			z-index: 1;
		}
		
		&:active {
			transform: scale(0.9);
		}
	}
	
	/* 图标颜色-使用渐变色 */
	.news-icon { background: linear-gradient(145deg, #5586e8, #2563eb); }
	.export-icon { background: linear-gradient(145deg, #49a2e3, #3794dc); }
	.responsible-tasks-icon { background: linear-gradient(145deg, #4CAF50, #45a049); }
	.user-management-icon { background: linear-gradient(145deg, #667eea, #764ba2); }
	.gm-supervision-icon { background: linear-gradient(145deg, #0ea5e9, #3b82f6); } /* 任务监督-蓝色水务主题 */
	.cleanup-icon { background: linear-gradient(145deg, #ff6b6b, #ee5a24); }
	.settings-icon { background: linear-gradient(145deg, #7b8de0, #5e6fd8); }
	.logout-icon { background: linear-gradient(145deg, #e06666, #d44c4c); }
	.record-icon { background: linear-gradient(145deg, #47b8e0, #32a7d6); } /* 巡视记录 */
	.calendar-icon { background: linear-gradient(145deg, #4a95e5, #3887df); }
	.area-icon { background: linear-gradient(145deg, #4aabe5, #3a9ddf); }
	.route-icon { background: linear-gradient(145deg, #7469d4, #5c4fc2); } /* 线路管理-紫色系 */
	.manage-icon { background: linear-gradient(145deg, #e06666, #d44c4c); }
	.shift-icon { background: linear-gradient(145deg, #e0984a, #d6893c); } /* 班次时间 */
	.system-settings-icon { background: linear-gradient(145deg, #4a95e5, #3887df); }
	.point-icon { background: linear-gradient(145deg, #66aee0, #4ca0d9); } /* 点位管理 */
	.guide-icon { background: linear-gradient(145deg, #7b8de0, #5e6fd8); }
	.database-icon { background: linear-gradient(145deg, #e06666, #d44c4c); }
	.task-icon { background: linear-gradient(145deg, #3975d9, #2862c6); } /* 任务管理-深蓝色系 */
	.collection-icon { background: linear-gradient(145deg, #8e44ad, #6c3483); } /* 数据采集-紫色系 */
	.notice-icon { background: linear-gradient(145deg, #4a95e5, #3887df); }
	.config-icon { background: linear-gradient(145deg, #4cb050, #389e3c); }
	.qrcode-icon { background: linear-gradient(145deg, #4cb050, #389e3c); }
	.gallery-icon { background: linear-gradient(145deg, #ff6b6b, #ee5a24); } /* 荣誉展厅-温暖橙红色 */
	
	.patrol-text {
		font-size: 28rpx;
		color: #3d4b66;
		text-align: center;
		white-space: normal;
		width: 100%;
		letter-spacing: 2rpx;
		margin-top: 12rpx;
		font-weight: 500;
	}
	
	/* 功能按钮区域 */
	.action-list {
		background-color: #ffffff;
		border-radius: 24rpx;
		overflow: hidden;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);
		animation: fadeInUp 0.5s ease;
		animation-delay: 0.2s;
		animation-fill-mode: both;
	}
	
	.action-item {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #eef0f6;
		transition: all 0.2s ease;
		
		&:last-child {
			border-bottom: none;
		}
		
		&:active {
			background-color: #f8f9fc;
		}
	}
	
	.action-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 16rpx;
		box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.12);
		position: relative;
		overflow: hidden;
		
		&:before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 50%;
			background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
			z-index: 1;
		}
	}
	
	.action-content {
		flex: 1;
		margin-left: 24rpx;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}
	
	.action-text {
		font-size: 32rpx;
		color: #3d4b66;
		letter-spacing: 2rpx;
		position: relative;
		text-align: left;
		align-self: flex-start;
	}
	
	.action-badge {
		background: #ff5a5f;
		color: white;
		font-size: 20rpx;
		padding: 2rpx 8rpx;
		border-radius: 10rpx;
		min-width: 24rpx;
		text-align: center;
		line-height: 1.2;
		position: absolute;
		top: -16rpx;
		right: -5rpx; /* 调整到更贴近"务"字的右上角 */
		transform: scale(0.9);
		z-index: 10;
	}
	
	.logout-text {
		color: #ff5a5f;
	}
	
	/* 待办事项区域 */
	.todo-section {
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 40rpx 30rpx;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);
		animation: fadeInUp 0.5s ease;
		animation-delay: 0.3s;
		animation-fill-mode: both;
	}
	
	.section-header {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.section-title {
		font-size: 34rpx;
		font-weight: 600;
		color: #2b2e4a;
		position: relative;
		padding-left: 20rpx;
		letter-spacing: 2rpx;
		
		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 6rpx;
			height: 32rpx;
			background: linear-gradient(180deg, #ff5a5f 0%, #ff3a3f 100%);
			border-radius: 6rpx;
		}
	}
	
	.todo-count-wrapper {
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	
	.todo-count {
		font-size: 26rpx;
		color: #ff5a5f;
		background-color: rgba(255, 90, 95, 0.1);
		padding: 8rpx 20rpx;
		border-radius: 30rpx;
		transition: all 0.2s ease;
		
		&:active {
			background-color: rgba(255, 90, 95, 0.2);
		}
	}
	
	.todo-list {
		margin-top: 20rpx;
	}
	
	.todo-item {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 24rpx;
		border-bottom: 1rpx solid #eef0f6;
		border-radius: 12rpx;
		margin-bottom: 16rpx;
		transition: all 0.3s ease;
		background-color: #f8f9fc;
		
		&:last-child {
			border-bottom: none;
			margin-bottom: 0;
		}
		
		&:active {
			background-color: #eef1f8;
			transform: translateY(2rpx);
		}
		
		.todo-content {
			flex: 1;
			margin-right: 20rpx;
		}
		
		.todo-title {
			display: flex;
			flex-direction: row;
			align-items: center;
			margin-bottom: 16rpx;
			flex-wrap: wrap;
			
			.name {
				font-size: 30rpx;
				font-weight: bold;
				color: #2b2e4a;
				margin-right: 16rpx;
				margin-bottom: 8rpx;
				letter-spacing: 2rpx;
			}
			
			.project {
				font-size: 24rpx;
				color: #4a78c9;
				background-color: rgba(74, 120, 201, 0.1);
				padding: 4rpx 16rpx;
				border-radius: 20rpx;
				margin-bottom: 8rpx;
				letter-spacing: 2rpx;
			}
		}
		
		.description {
			font-size: 28rpx;
			color: #718096;
			margin-bottom: 16rpx;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			line-clamp: 2;
			overflow: hidden;
			line-height: 1.6;
			letter-spacing: 1.5rpx;
		}
		
		.todo-footer {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			
			.time {
				font-size: 24rpx;
				color: #a0aec0;
				letter-spacing: 1rpx;
			}
			
			.todo-type {
				font-size: 22rpx;
				color: #4a78c9;
				background-color: rgba(74, 120, 201, 0.1);
				padding: 4rpx 12rpx;
				border-radius: 16rpx;
				border: 1rpx solid rgba(74, 120, 201, 0.3);
				margin-left: 16rpx;
				letter-spacing: 1rpx;
			}
		}
	}
	
	.empty-todo {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx 0;
		
		.empty-image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 30rpx;
			opacity: 0.8;
		}
		
		.empty-text {
			font-size: 28rpx;
			color: #8a94a6;
			text-align: center;
			letter-spacing: 2rpx;
		}
	}
</style> 
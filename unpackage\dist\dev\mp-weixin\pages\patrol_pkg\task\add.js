require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol_pkg/task/add"],{

/***/ 411:
/*!******************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fpatrol_pkg%2Ftask%2Fadd"} ***!
  \******************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _add = _interopRequireDefault(__webpack_require__(/*! ./pages/patrol_pkg/task/add.vue */ 412));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_add.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 412:
/*!*********************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/add.vue ***!
  \*********************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _add_vue_vue_type_template_id_3e49dba4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./add.vue?vue&type=template&id=3e49dba4& */ 413);
/* harmony import */ var _add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./add.vue?vue&type=script&lang=js& */ 415);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./add.vue?vue&type=style&index=0&lang=scss& */ 417);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _add_vue_vue_type_template_id_3e49dba4___WEBPACK_IMPORTED_MODULE_0__["render"],
  _add_vue_vue_type_template_id_3e49dba4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _add_vue_vue_type_template_id_3e49dba4___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/patrol_pkg/task/add.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 413:
/*!****************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/add.vue?vue&type=template&id=3e49dba4& ***!
  \****************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_template_id_3e49dba4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=template&id=3e49dba4& */ 414);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_template_id_3e49dba4___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_template_id_3e49dba4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_template_id_3e49dba4___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_template_id_3e49dba4___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 414:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/add.vue?vue&type=template&id=3e49dba4& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.showUserSelect
    ? _vm.__map(_vm.filteredUsers, function (user, idx) {
        var $orig = _vm.__get_orig(user)
        var m0 = user.role && _vm.getRoleText(user.role)
        var m1 = m0 ? _vm.getRoleText(user.role) : null
        return {
          $orig: $orig,
          m0: m0,
          m1: m1,
        }
      })
    : null
  var m2 = _vm.selectedUser ? _vm.getRoleText(_vm.selectedUser.role) : null
  var m3 =
    _vm.formData.date && _vm.selectedShift
      ? _vm.getStatusIcon(_vm.predictedStatus)
      : null
  var m4 =
    _vm.formData.date && _vm.selectedShift
      ? _vm.getStatusText(_vm.predictedStatus)
      : null
  var m5 =
    _vm.formData.date && _vm.selectedShift
      ? _vm.getStatusExplanation(_vm.predictedStatus)
      : null
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      $event.stopPropagation()
      _vm.showUserSelect = true
    }
    _vm.e1 = function ($event) {
      $event.stopPropagation()
      _vm.showUserSelect = false
    }
    _vm.e2 = function ($event) {
      _vm.showUserSelect = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
        m2: m2,
        m3: m3,
        m4: m4,
        m5: m5,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 415:
/*!**********************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/add.vue?vue&type=script&lang=js& ***!
  \**********************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=script&lang=js& */ 416);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 416:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/add.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! @/utils/patrol-api.js */ 71));
var _date = __webpack_require__(/*! @/utils/date.js */ 72);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      submitting: false,
      formData: {
        name: '',
        area: '',
        date: '',
        shift_id: '',
        route_id: '',
        user_id: '',
        status: 0 // 默认未开始，将在提交时根据时间计算正确状态
      },

      shiftOptions: [{
        _id: '',
        name: '请选择班次',
        rounds: []
      }],
      shiftIndex: 0,
      routeOptions: [{
        _id: '',
        name: '请选择路线'
      }],
      routeIndex: 0,
      userOptions: [{
        _id: '',
        nickname: '请选择人员'
      }],
      userIndex: 0,
      selectedRoute: null,
      selectedShift: null,
      selectedUser: null,
      // 角色映射表
      roleNameMap: {},
      // 状态常量
      STATUS: {
        NOT_STARTED: 0,
        // 未开始
        IN_PROGRESS: 1,
        // 进行中
        COMPLETED: 2,
        // 已完成
        EXPIRED: 3,
        // 已超时
        CANCELLED: 4 // 已取消
      },

      predictedStatus: 0,
      // 预测的任务状态
      timeZoneIndicator: null,
      // 用于显示当前时区
      processedRounds: null,
      // 用于存储处理后的轮次数据
      pointsRefreshed: false,
      // 点位数据是否已刷新
      searchUserName: '',
      filteredUsers: [],
      showUserSelect: false
    };
  },
  onLoad: function onLoad() {
    var _this = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              // 设置默认日期为今天
              _this.formData.date = (0, _date.formatDate)(new Date(), 'YYYY-MM-DD');

              // 检测并记录当前时区
              _this.timeZoneIndicator = (0, _date.detectTimeZone)();

              // 🔥 优化：并行加载数据提升用户体验
              _context.prev = 2;
              uni.showLoading({
                title: '加载中...'
              });

              // 并行加载减少用户等待时间，各项请求独立进行
              _context.next = 6;
              return Promise.all([_this.loadShifts(), _this.loadRoutes(), _this.loadRoles(), _this.loadUsers()]);
            case 6:
              _context.next = 12;
              break;
            case 8:
              _context.prev = 8;
              _context.t0 = _context["catch"](2);
              console.error('初始化数据加载失败:', _context.t0);
              uni.showToast({
                title: '数据加载失败',
                icon: 'none'
              });
            case 12:
              _context.prev = 12;
              uni.hideLoading();
              return _context.finish(12);
            case 15:
            case "end":
              return _context.stop();
          }
        }
      }, _callee, null, [[2, 8, 12, 15]]);
    }))();
  },
  watch: {
    // 监听影响任务状态的关键数据变化
    'formData.date': function formDataDate() {
      this.updatePredictedStatus();
    },
    selectedShift: {
      handler: function handler() {
        this.updatePredictedStatus();
      },
      deep: true
    },
    // 监听用户列表变化，初始化过滤后的列表
    userOptions: {
      handler: function handler(val) {
        this.filteredUsers = (0, _toConsumableArray2.default)(val);
      },
      immediate: true
    }
  },
  methods: {
    // 加载班次数据
    loadShifts: function loadShifts() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var res, defaultOption, shifts, systemInfo, platform, isMobile;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return _patrolApi.default.call({
                  name: 'patrol-shift',
                  action: 'getShiftList',
                  data: {
                    status: 1,
                    with_rounds: true,
                    // 请求包含轮次信息
                    with_detail: true // 请求包含详细信息（包括跨天标记）
                  }
                });
              case 3:
                res = _context2.sent;
                if (res.code === 0 && res.data && res.data.list) {
                  // 只加载启用的班次
                  defaultOption = {
                    _id: '',
                    name: '请选择班次',
                    rounds: []
                  };
                  shifts = res.data.list.map(function (shift) {
                    return _objectSpread(_objectSpread({}, shift), {}, {
                      name: shift.name,
                      across_day: shift.across_day || false,
                      rounds: shift.rounds && Array.isArray(shift.rounds) ? shift.rounds.filter(function (r) {
                        return r.status !== 0;
                      }).map(function (round) {
                        return {
                          round: round.round,
                          name: round.name || "\u8F6E\u6B21".concat(round.round),
                          time: round.time,
                          start_time: round.start_time || round.time,
                          end_time: round.end_time || round.time,
                          day_offset: round.day_offset || 0,
                          duration: round.duration || 60,
                          status: round.status
                        };
                      }) : []
                    });
                  }); // 按照班次名称字母顺序排序
                  systemInfo = uni.getSystemInfoSync();
                  platform = systemInfo.platform;
                  isMobile = ['android', 'ios'].includes(platform); // 对班次进行排序
                  shifts.sort(function (a, b) {
                    var nameA = String(a.name || '').trim();
                    var nameB = String(b.name || '').trim();

                    // 使用 localeCompare 进行中文排序，统一使用 A-Z 排序
                    var compareResult = nameA.localeCompare(nameB, 'zh-CN');
                    return isMobile ? -compareResult : compareResult;
                  });

                  // 合并默认选项和排序后的班次列表
                  _this2.shiftOptions = [defaultOption].concat((0, _toConsumableArray2.default)(shifts));
                } else {
                  _this2.shiftOptions = [{
                    _id: '',
                    name: '请选择班次',
                    rounds: []
                  }];
                }
                _context2.next = 12;
                break;
              case 7:
                _context2.prev = 7;
                _context2.t0 = _context2["catch"](0);
                console.error('加载班次失败:', _context2.t0);
                _this2.shiftOptions = [{
                  _id: '',
                  name: '请选择班次',
                  rounds: []
                }];
                uni.showToast({
                  title: '加载班次失败',
                  icon: 'none'
                });
              case 12:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 7]]);
      }))();
    },
    // 加载路线数据
    loadRoutes: function loadRoutes() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var res;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _context3.next = 3;
                return _patrolApi.default.call({
                  name: 'patrol-route',
                  action: 'getRouteList',
                  data: {
                    status: 1
                  }
                });
              case 3:
                res = _context3.sent;
                if (res.code === 0 && res.data && res.data.list) {
                  // 只加载启用的路线
                  _this3.routeOptions = [{
                    _id: '',
                    name: '请选择路线'
                  }].concat((0, _toConsumableArray2.default)(res.data.list));
                } else {
                  _this3.routeOptions = [{
                    _id: '',
                    name: '请选择路线'
                  }];
                }
                _context3.next = 11;
                break;
              case 7:
                _context3.prev = 7;
                _context3.t0 = _context3["catch"](0);
                _this3.routeOptions = [{
                  _id: '',
                  name: '请选择路线'
                }];
                uni.showToast({
                  title: '加载路线失败',
                  icon: 'none'
                });
              case 11:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 7]]);
      }))();
    },
    // 获取当前用户ID
    getCurrentUserId: function getCurrentUserId() {
      try {
        // 尝试从本地存储获取用户信息
        var userInfo = uni.getStorageSync('uni-id-pages-userInfo');
        if (userInfo) {
          return userInfo._id || '';
        }
        return '';
      } catch (e) {
        return '';
      }
    },
    // 加载用户列表
    loadUsers: function loadUsers() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var currentUserId, result, userList;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.prev = 0;
                currentUserId = _this4.getCurrentUserId();
                if (currentUserId) {
                  _context4.next = 4;
                  break;
                }
                throw new Error('未能获取当前用户ID');
              case 4:
                _context4.next = 6;
                return _patrolApi.default.call({
                  name: 'patrol-user',
                  action: 'getUsers',
                  data: {
                    userid: currentUserId,
                    params: {
                      pageSize: 100
                    }
                  }
                });
              case 6:
                result = _context4.sent;
                if (result.code === 0 && result.data) {
                  // 添加一个空选项作为第一个选项
                  _this4.userOptions = [{
                    _id: '',
                    nickname: '请选择执行人',
                    avatar: '/static/user/default-avatar.png',
                    role: []
                  }];

                  // 处理用户数据，过滤掉admin角色的用户
                  userList = result.data.list.filter(function (user) {
                    // 过滤掉admin角色用户
                    if (!user.role) return true; // 没有角色的保留
                    if (Array.isArray(user.role)) {
                      // 如果是数组，检查是否包含admin
                      return !user.role.includes('admin');
                    } else {
                      // 如果是字符串，检查是否为admin
                      return user.role !== 'admin';
                    }
                  }).filter(function (user) {
                    // 过滤掉以"匿名"开头的用户
                    var nickname = user.real_name || user.nickname || user.username || '';
                    return !nickname.startsWith('匿名');
                  }).map(function (user) {
                    return {
                      _id: user._id,
                      nickname: user.real_name || user.nickname || user.username || '未命名用户',
                      avatar: user.avatar || '/static/user/default-avatar.png',
                      role: user.role || []
                    };
                  }); // 合并空选项和用户列表
                  _this4.userOptions = [].concat((0, _toConsumableArray2.default)(_this4.userOptions), (0, _toConsumableArray2.default)(userList));

                  // 初始选择第一项（请选择执行人）
                  _this4.userIndex = 0;
                  _this4.selectedUser = null;
                  _this4.formData.user_id = '';
                  if (_this4.userOptions.length <= 1) {
                    uni.showToast({
                      title: '暂无可用执行人员',
                      icon: 'none'
                    });
                  }
                } else {
                  _this4.userOptions = [{
                    _id: '',
                    nickname: '请选择执行人',
                    avatar: '/static/user/default-avatar.png',
                    role: []
                  }];
                  _this4.userIndex = 0;
                  _this4.selectedUser = null;
                }
                _context4.next = 13;
                break;
              case 10:
                _context4.prev = 10;
                _context4.t0 = _context4["catch"](0);
                uni.showToast({
                  title: '加载用户数据失败',
                  icon: 'none'
                });
              case 13:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[0, 10]]);
      }))();
    },
    // 加载角色数据
    loadRoles: function loadRoles() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var currentUserId, result, roleMap;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                currentUserId = _this5.getCurrentUserId();
                if (currentUserId) {
                  _context5.next = 4;
                  break;
                }
                throw new Error('未能获取当前用户ID');
              case 4:
                _context5.next = 6;
                return _patrolApi.default.call({
                  name: 'patrol-user',
                  action: 'getRoleList',
                  data: {
                    userid: currentUserId
                  }
                });
              case 6:
                result = _context5.sent;
                if (result.code === 0 && result.data) {
                  // 构建角色ID到名称的映射
                  roleMap = {};
                  result.data.forEach(function (role) {
                    roleMap[role.role_id] = role.role_name;
                  });
                  _this5.roleNameMap = roleMap;
                }
                _context5.next = 13;
                break;
              case 10:
                _context5.prev = 10;
                _context5.t0 = _context5["catch"](0);
                uni.showToast({
                  title: '加载角色数据失败',
                  icon: 'none'
                });
              case 13:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 10]]);
      }))();
    },
    // 日期变更
    onDateChange: function onDateChange(e) {
      // 确保日期格式一致 - 只保留YYYY-MM-DD部分
      var dateValue = e.detail.value;
      // 检查是否需要处理日期格式
      if (dateValue.includes('T') || dateValue.includes('Z')) {
        // 如果有时区信息，提取日期部分
        this.formData.date = dateValue.split('T')[0];
      } else {
        // 否则直接使用，这应该已经是YYYY-MM-DD格式
        this.formData.date = dateValue;
      }

      // 在日期变更后，如果有选择班次，更新预计状态
      if (this.selectedShift) {
        this.updatePredictedStatus();
      }
    },
    // 班次变更
    onShiftChange: function onShiftChange(e) {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var shift, res, shiftData, enabledRounds;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _this6.shiftIndex = e.detail.value;
                shift = _this6.shiftOptions[_this6.shiftIndex];
                if (!(shift && shift._id)) {
                  _context6.next = 17;
                  break;
                }
                _this6.formData.shift_id = shift._id;
                _context6.prev = 4;
                _context6.next = 7;
                return _patrolApi.default.call({
                  name: 'patrol-shift',
                  action: 'getShiftDetail',
                  data: {
                    params: {
                      shift_id: shift._id,
                      with_rounds: true,
                      with_detail: true
                    }
                  }
                });
              case 7:
                res = _context6.sent;
                if (res.code === 0 && res.data) {
                  // 处理班次详情，确保包含轮次信息
                  shiftData = res.data;
                  if (shiftData.rounds && Array.isArray(shiftData.rounds)) {
                    enabledRounds = shiftData.rounds.filter(function (r) {
                      return r.status !== 0;
                    });
                    _this6.selectedShift = _objectSpread(_objectSpread({}, shiftData), {}, {
                      // 确保跨天标记正确设置
                      across_day: shiftData.across_day || false,
                      rounds: enabledRounds.map(function (round) {
                        return {
                          round: round.round,
                          name: round.name || "\u8F6E\u6B21".concat(round.round),
                          time: round.time,
                          start_time: round.start_time || round.time,
                          end_time: round.end_time || round.time,
                          // 确保天数偏移正确设置
                          day_offset: round.day_offset || 0,
                          duration: round.duration || 60,
                          status: round.status
                        };
                      })
                    });

                    // 如果是跨天班次，给用户提示
                    if (_this6.selectedShift.across_day) {
                      uni.showToast({
                        title: '您选择了跨天班次，系统将自动处理次日轮次',
                        icon: 'none',
                        duration: 3000
                      });
                      console.log('选择了跨天班次:', {
                        name: _this6.selectedShift.name,
                        start_time: _this6.selectedShift.start_time,
                        end_time: _this6.selectedShift.end_time,
                        rounds: _this6.selectedShift.rounds.map(function (r) {
                          return {
                            round: r.round,
                            time: r.time,
                            day_offset: r.day_offset
                          };
                        })
                      });
                    }
                  } else {
                    _this6.selectedShift = shiftData;
                  }
                }
                _context6.next = 15;
                break;
              case 11:
                _context6.prev = 11;
                _context6.t0 = _context6["catch"](4);
                console.error('获取班次详情失败:', _context6.t0);
                uni.showToast({
                  title: '获取班次详情失败',
                  icon: 'none'
                });
              case 15:
                _context6.next = 19;
                break;
              case 17:
                _this6.formData.shift_id = '';
                _this6.selectedShift = null;
              case 19:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[4, 11]]);
      }))();
    },
    // 路线变更
    onRouteChange: function onRouteChange(e) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var route, res;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _this7.routeIndex = e.detail.value;
                route = _this7.routeOptions[_this7.routeIndex];
                if (!(route && route._id)) {
                  _context7.next = 18;
                  break;
                }
                _this7.formData.route_id = route._id;

                // 自动设置区域为路线名称
                _this7.formData.area = route.name || '';

                // 获取路线详情
                _context7.prev = 5;
                _context7.next = 8;
                return _patrolApi.default.call({
                  name: 'patrol-route',
                  action: 'getRouteDetail',
                  data: {
                    params: {
                      route_id: route._id,
                      with_points: true,
                      with_detail: true,
                      include_point_details: true
                    }
                  }
                });
              case 8:
                res = _context7.sent;
                if (res.code === 0 && res.data) {
                  _this7.selectedRoute = res.data;
                  // 再次确保区域被设置为路线名称
                  _this7.formData.area = res.data.name || route.name || '';
                }
                _context7.next = 16;
                break;
              case 12:
                _context7.prev = 12;
                _context7.t0 = _context7["catch"](5);
                console.error('获取路线详情失败:', _context7.t0);
                uni.showToast({
                  title: '获取路线详情失败',
                  icon: 'none'
                });
              case 16:
                _context7.next = 21;
                break;
              case 18:
                _this7.formData.route_id = '';
                _this7.formData.area = ''; // 清空区域
                _this7.selectedRoute = null;
              case 21:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[5, 12]]);
      }))();
    },
    // 用户变更
    onUserChange: function onUserChange(e) {
      this.userIndex = e.detail.value;
      var user = this.filteredUsers[this.userIndex];
      if (user && user._id) {
        this.formData.user_id = user._id;
        this.selectedUser = user;
      } else {
        this.formData.user_id = '';
        this.selectedUser = null;
      }
    },
    // 状态变更 - 移除此方法或保留为空函数
    onStatusChange: function onStatusChange(e) {
      // 方法已不再需要，但保留避免引用错误
    },
    // 获取角色文本
    getRoleText: function getRoleText(role) {
      var _this8 = this;
      if (!role) return '普通员工';

      // 处理数组形式的角色
      if (Array.isArray(role)) {
        if (role.length === 0) return '普通员工';
        // 返回所有角色的文本，用逗号分隔
        return role.map(function (r) {
          return _this8.getSingleRoleText(r);
        }).join(', ');
      }
      return this.getSingleRoleText(role);
    },
    // 处理单个角色值
    getSingleRoleText: function getSingleRoleText(role) {
      // 首先从角色表的映射中查找
      if (this.roleNameMap[role]) {
        return this.roleNameMap[role];
      }

      // 如果在角色表中找不到，使用预定义的映射作为备用
      var roleMap = {
        'admin': '管理员',
        'responsible': '责任人',
        'reviser': '发布人',
        'supervisor': '主管',
        'PM': '副厂长',
        'GM': '厂长',
        'logistics': '后勤员',
        'dispatch': '调度员',
        'Integrated': '综合员',
        'operator': '设备员',
        'technician': '工艺员',
        'mechanic': '技术员',
        'user': '普通员工',
        'manager': '管理人员',
        'worker': '普通员工'
      };
      return roleMap[role] || '用户 (' + role + ')';
    },
    // 格式化日期
    formatDate: function formatDate(date) {
      return (0, _date.formatDate)(date, 'YYYY-MM-DD');
    },
    // 表单验证
    validateForm: function validateForm() {
      if (!this.formData.name) {
        uni.showToast({
          title: '请输入任务名称',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.route_id) {
        uni.showToast({
          title: '请选择巡检路线',
          icon: 'none'
        });
        return false;
      }

      // 确保区域从路线名称填充
      if (!this.formData.area) {
        var route = this.routeOptions[this.routeIndex];
        this.formData.area = route && route.name ? route.name : '未知区域';
      }
      if (!this.formData.date) {
        uni.showToast({
          title: '请选择执行日期',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.shift_id) {
        uni.showToast({
          title: '请选择班次',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.user_id) {
        uni.showToast({
          title: '请选择执行人员',
          icon: 'none'
        });
        return false;
      }
      return true;
    },
    // 处理轮次数据，生成新的数据结构
    processRoundsData: function processRoundsData() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var taskDate, shift, _shift$start_time$spl, _shift$start_time$spl2, shiftStartHours, points, now;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                if (!(!_this9.selectedShift || !_this9.selectedShift.rounds || !_this9.selectedRoute || !_this9.selectedRoute.pointsDetail)) {
                  _context8.next = 2;
                  break;
                }
                return _context8.abrupt("return", []);
              case 2:
                // 获取任务执行日期作为基准日期
                taskDate = _this9.formData.date;
                shift = _this9.selectedShift; // 解析班次开始时间
                _shift$start_time$spl = shift.start_time.split(':').map(Number), _shift$start_time$spl2 = (0, _slicedToArray2.default)(_shift$start_time$spl, 1), shiftStartHours = _shift$start_time$spl2[0]; // 获取路线点位
                points = _this9.selectedRoute.pointsDetail; // 当前时间 - 用于计算轮次状态
                now = new Date();
                return _context8.abrupt("return", _this9.selectedShift.rounds.map(function (round) {
                  // 获取轮次时间
                  var checkTime = round.time;
                  var _checkTime$split$map = checkTime.split(':').map(Number),
                    _checkTime$split$map2 = (0, _slicedToArray2.default)(_checkTime$split$map, 1),
                    roundHours = _checkTime$split$map2[0];

                  // 计算天数偏移
                  var actualDayOffset = 0; // 重置为0,不使用原始的day_offset值

                  // 只有当班次是跨天且轮次时间小于班次开始时间时才设置为次日(day_offset=1)
                  if (shift.across_day && roundHours < shiftStartHours) {
                    actualDayOffset = 1; // 直接设置为1,表示次日
                  }

                  // 基于任务执行日计算轮次时间
                  var checkDateTime = new Date("".concat(taskDate, "T").concat(checkTime, ":00+08:00"));
                  checkDateTime.setDate(checkDateTime.getDate() + actualDayOffset);

                  // 使用 calculateEndTime 计算结束时间
                  var endDateTime = (0, _date.calculateEndTime)(checkDateTime, round.duration || 60);

                  // 根据当前时间计算轮次状态
                  var roundStatus = 0; // 默认未开始

                  if (now > endDateTime) {
                    // 当前时间已超过轮次结束时间，标记为"已超时"
                    roundStatus = 3; // 已超时状态
                  } else if (now >= checkDateTime) {
                    // 当前时间在轮次的开始和结束时间之间，标记为"进行中"
                    roundStatus = 1; // 进行中状态
                  }

                  // 轮次点位信息
                  var roundPoints = points.map(function (point) {
                    return {
                      point_id: point._id,
                      name: point.name,
                      order: point.order || 0,
                      status: 0,
                      location: point.location,
                      range: point.range
                    };
                  });
                  return {
                    round: round.round,
                    name: round.name || "\u8F6E\u6B21".concat(round.round),
                    time: checkTime,
                    check_time: checkDateTime.toISOString(),
                    start_time: checkDateTime.toISOString(),
                    end_time: endDateTime.toISOString(),
                    day_offset: actualDayOffset,
                    duration: round.duration || 60,
                    status: roundStatus,
                    across_day: shift.across_day,
                    points: roundPoints,
                    stats: {
                      total_points: roundPoints.length,
                      completed_points: 0,
                      missed_points: roundPoints.length,
                      completion_rate: 0,
                      abnormal_count: 0
                    }
                  };
                }));
              case 8:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8);
      }))();
    },
    // 计算整体统计数据
    calculateOverallStats: function calculateOverallStats(rounds) {
      // 如果没有轮次，返回默认统计数据
      if (!rounds || rounds.length === 0) {
        return {
          total_points: 0,
          completed_points: 0,
          missed_points: 0,
          completion_rate: 0,
          abnormal_count: 0,
          last_checkin_time: null // 初始无打卡记录
        };
      }

      // 计算所有轮次的统计数据
      var totalPoints = 0;
      var completedPoints = 0;
      var abnormalCount = 0;
      rounds.forEach(function (round) {
        if (round.stats) {
          totalPoints += round.stats.total_points || 0;
          completedPoints += round.stats.completed_points || 0;
          abnormalCount += round.stats.abnormal_count || 0;
        }
      });

      // 计算未完成点位数和完成率
      var missedPoints = totalPoints - completedPoints;
      var completionRate = totalPoints > 0 ? completedPoints / totalPoints : 0;
      return {
        total_points: totalPoints,
        completed_points: completedPoints,
        missed_points: missedPoints,
        completion_rate: completionRate,
        abnormal_count: abnormalCount,
        last_checkin_time: null // 初始无打卡记录
      };
    },
    // 提交表单时整理数据（修改提交部分）
    submitForm: function submitForm() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var _this10$selectedShift5, _this10$selectedShift6, _this10$selectedShift7, _this10$selectedShift8, _this10$selectedShift9, _this10$selectedRoute10, _this10$selectedRoute11, _this10$selectedRoute12, _this10$selectedRoute13, _this10$selectedRoute14, rounds, overallStats, shiftName, routeName, startTime, endTime, _this10$selectedShift, _this10$selectedShift2, endHours, _this10$selectedShift3, _this10$selectedShift4, startHours, dayOffset, endDateTime, calculatedStatus, cleanDate, requestData, response, taskData, hasRequiredFields;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                if (_this10.validateForm()) {
                  _context9.next = 2;
                  break;
                }
                return _context9.abrupt("return");
              case 2:
                // 显示加载状态
                _this10.submitting = true;
                _context9.prev = 3;
                _context9.next = 6;
                return _this10.processRoundsData();
              case 6:
                rounds = _context9.sent;
                // 保存处理后的轮次数据，以便在计算任务状态时使用
                _this10.processedRounds = rounds;

                // 计算整体统计数据
                overallStats = _this10.calculateOverallStats(rounds); // 确保获取当前选中的班次和路线的名称
                shiftName = _this10.selectedShift ? _this10.selectedShift.name : '';
                routeName = _this10.selectedRoute ? _this10.selectedRoute.name : ''; // 计算任务整体开始和结束时间
                if (_this10.selectedShift) {
                  // 如果班次有开始和结束时间，使用这些时间
                  if (_this10.selectedShift.start_time) {
                    startTime = (0, _date.calculateRoundTime)(_this10.formData.date, _this10.selectedShift.start_time).toISOString();
                  }
                  if (_this10.selectedShift.end_time) {
                    // 计算结束时间时需要考虑跨天
                    _this10$selectedShift = _this10.selectedShift.end_time.split(':').map(Number), _this10$selectedShift2 = (0, _slicedToArray2.default)(_this10$selectedShift, 1), endHours = _this10$selectedShift2[0];
                    _this10$selectedShift3 = _this10.selectedShift.start_time.split(':').map(Number), _this10$selectedShift4 = (0, _slicedToArray2.default)(_this10$selectedShift3, 1), startHours = _this10$selectedShift4[0];
                    dayOffset = 0;
                    if (_this10.selectedShift.across_day && endHours < startHours) {
                      dayOffset = 1;
                    }
                    endDateTime = (0, _date.calculateRoundTime)(_this10.formData.date, _this10.selectedShift.end_time, dayOffset);
                    endTime = endDateTime.toISOString();
                  }
                }

                // 自动计算状态
                calculatedStatus = _this10.calculateTaskStatus(startTime); // 确保patrol_date只有日期部分，没有时间部分
                cleanDate = _this10.formData.date.split('T')[0]; // 提取YYYY-MM-DD部分，移除可能的时区信息
                // 构建请求数据
                requestData = {
                  name: _this10.formData.name,
                  area: _this10.formData.area || '默认区域',
                  patrol_date: cleanDate,
                  // 使用不带时区信息的纯日期格式
                  shift_id: _this10.formData.shift_id,
                  shift_name: shiftName,
                  route_id: _this10.formData.route_id,
                  route_name: routeName,
                  user_id: _this10.formData.user_id,
                  status: calculatedStatus,
                  // 直接使用计算后的状态
                  enabled_rounds: ((_this10$selectedShift5 = _this10.selectedShift) === null || _this10$selectedShift5 === void 0 ? void 0 : (_this10$selectedShift6 = _this10$selectedShift5.rounds) === null || _this10$selectedShift6 === void 0 ? void 0 : _this10$selectedShift6.map(function (r) {
                    return r.round;
                  })) || [],
                  rounds_detail: rounds.map(function (round) {
                    return _objectSpread(_objectSpread({}, round), {}, {
                      points: round.points.map(function (p) {
                        var _this10$selectedRoute, _this10$selectedRoute2, _this10$selectedRoute3, _this10$selectedRoute4, _this10$selectedRoute5, _this10$selectedRoute6, _this10$selectedRoute7, _this10$selectedRoute8, _this10$selectedRoute9;
                        return _objectSpread(_objectSpread({}, p), {}, {
                          qrcode_enabled: ((_this10$selectedRoute = _this10.selectedRoute) === null || _this10$selectedRoute === void 0 ? void 0 : (_this10$selectedRoute2 = _this10$selectedRoute.pointsDetail) === null || _this10$selectedRoute2 === void 0 ? void 0 : (_this10$selectedRoute3 = _this10$selectedRoute2.find(function (pd) {
                            return pd._id === p.point_id;
                          })) === null || _this10$selectedRoute3 === void 0 ? void 0 : _this10$selectedRoute3.qrcode_enabled) || false,
                          qrcode_required: ((_this10$selectedRoute4 = _this10.selectedRoute) === null || _this10$selectedRoute4 === void 0 ? void 0 : (_this10$selectedRoute5 = _this10$selectedRoute4.pointsDetail) === null || _this10$selectedRoute5 === void 0 ? void 0 : (_this10$selectedRoute6 = _this10$selectedRoute5.find(function (pd) {
                            return pd._id === p.point_id;
                          })) === null || _this10$selectedRoute6 === void 0 ? void 0 : _this10$selectedRoute6.qrcode_required) || false,
                          qrcode_version: ((_this10$selectedRoute7 = _this10.selectedRoute) === null || _this10$selectedRoute7 === void 0 ? void 0 : (_this10$selectedRoute8 = _this10$selectedRoute7.pointsDetail) === null || _this10$selectedRoute8 === void 0 ? void 0 : (_this10$selectedRoute9 = _this10$selectedRoute8.find(function (pd) {
                            return pd._id === p.point_id;
                          })) === null || _this10$selectedRoute9 === void 0 ? void 0 : _this10$selectedRoute9.qrcode_version) || 0
                        });
                      })
                    });
                  }),
                  overall_stats: overallStats,
                  auto_closed: false,
                  // 添加shift_detail冗余字段
                  shift_detail: {
                    name: shiftName,
                    start_time: ((_this10$selectedShift7 = _this10.selectedShift) === null || _this10$selectedShift7 === void 0 ? void 0 : _this10$selectedShift7.start_time) || "",
                    end_time: ((_this10$selectedShift8 = _this10.selectedShift) === null || _this10$selectedShift8 === void 0 ? void 0 : _this10$selectedShift8.end_time) || "",
                    across_day: ((_this10$selectedShift9 = _this10.selectedShift) === null || _this10$selectedShift9 === void 0 ? void 0 : _this10$selectedShift9.across_day) || false
                  },
                  // 添加route_detail冗余字段
                  route_detail: {
                    name: routeName,
                    point_count: ((_this10$selectedRoute10 = _this10.selectedRoute) === null || _this10$selectedRoute10 === void 0 ? void 0 : (_this10$selectedRoute11 = _this10$selectedRoute10.pointsDetail) === null || _this10$selectedRoute11 === void 0 ? void 0 : _this10$selectedRoute11.length) || 0,
                    total_distance: ((_this10$selectedRoute12 = _this10.selectedRoute) === null || _this10$selectedRoute12 === void 0 ? void 0 : _this10$selectedRoute12.total_distance) || 0,
                    points: ((_this10$selectedRoute13 = _this10.selectedRoute) === null || _this10$selectedRoute13 === void 0 ? void 0 : (_this10$selectedRoute14 = _this10$selectedRoute13.pointsDetail) === null || _this10$selectedRoute14 === void 0 ? void 0 : _this10$selectedRoute14.map(function (p) {
                      return {
                        point_id: p._id,
                        name: p.name,
                        order: p.order || 0,
                        location: p.location,
                        range: p.range,
                        qrcode_enabled: p.qrcode_enabled || false,
                        qrcode_required: p.qrcode_required || false,
                        qrcode_version: p.qrcode_version || 0
                      };
                    })) || []
                  }
                }; // 设置任务开始和结束时间
                if (startTime) requestData.start_time = startTime;
                if (endTime) requestData.end_time = endTime;

                // 记录提交的数据（调试用）
                // console.log('提交任务数据:', JSON.stringify(requestData, null, 2));

                // 发起请求
                _context9.next = 19;
                return _patrolApi.default.addTask(requestData);
              case 19:
                response = _context9.sent;
                if (!(response && response.code === 0)) {
                  _context9.next = 24;
                  break;
                }
                // 验证返回数据的完整性
                if (response.data && response.data._id) {
                  // 检查返回的数据是否符合预期
                  taskData = response.data;
                  hasRequiredFields = taskData.name && taskData.status !== undefined && taskData.rounds_detail && taskData.overall_stats;
                  if (!hasRequiredFields) {
                    console.warn('警告：创建的任务数据可能不完整');
                  }
                  uni.showToast({
                    title: '创建成功',
                    icon: 'success'
                  });

                  // 触发刷新任务列表事件
                  uni.$emit('refresh-task-list');

                  // 跳转到详情页
                  setTimeout(function () {
                    uni.navigateBack();
                  }, 1500);
                } else {
                  console.warn('警告：创建任务成功但返回数据不完整');
                  uni.showToast({
                    title: '创建成功，但数据可能不完整',
                    icon: 'none'
                  });
                  // 即使数据不完整也触发刷新
                  uni.$emit('refresh-task-list');
                  setTimeout(function () {
                    uni.navigateBack();
                  }, 1500);
                }
                _context9.next = 25;
                break;
              case 24:
                throw new Error(response.message || '创建失败');
              case 25:
                _context9.next = 31;
                break;
              case 27:
                _context9.prev = 27;
                _context9.t0 = _context9["catch"](3);
                console.error('创建任务失败:', _context9.t0);
                uni.showToast({
                  title: _context9.t0.message || '创建失败',
                  icon: 'none'
                });
              case 31:
                _context9.prev = 31;
                _this10.submitting = false;
                return _context9.finish(31);
              case 34:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[3, 27, 31, 34]]);
      }))();
    },
    // 更新预测状态
    updatePredictedStatus: function updatePredictedStatus() {
      // 检查是否有足够的数据进行预测
      if (!this.formData.date || !this.selectedShift || !this.selectedShift.start_time) {
        this.predictedStatus = this.STATUS.NOT_STARTED;
        return;
      }
      try {
        // 获取时区偏移（小时）- 确保与后端一致的时区处理
        var timezoneOffset = -(new Date().getTimezoneOffset() / 60); // 例如：东八区为8
        console.log("\u5F53\u524D\u65F6\u533A\u504F\u79FB: UTC".concat(timezoneOffset >= 0 ? '+' : '').concat(timezoneOffset));

        // 获取班次信息
        var isAcrossDay = this.selectedShift.across_day;
        var shiftStartTime = this.selectedShift.start_time;

        // 计算任务开始时间（使用calculateRoundTime，它会处理日期和时间的组合）
        var startTime = (0, _date.calculateRoundTime)(this.formData.date, shiftStartTime);

        // 当前时间（使用本地时区）
        var now = new Date();

        // 调试输出时间比较
        console.log('预计任务状态计算:');
        console.log("- \u4EFB\u52A1\u65E5\u671F: ".concat(this.formData.date));
        console.log("- \u73ED\u6B21\u5F00\u59CB\u65F6\u95F4: ".concat(shiftStartTime));
        console.log("- \u5B8C\u6574\u5F00\u59CB\u65F6\u95F4: ".concat(startTime.toISOString()));
        console.log("- \u5F53\u524D\u65F6\u95F4: ".concat(now.toISOString()));
        console.log("- \u662F\u5426\u8DE8\u5929: ".concat(isAcrossDay));

        // 首先检查是否有轮次信息
        if (this.selectedShift.rounds && this.selectedShift.rounds.length > 0) {
          // 按照轮次时间排序找出第一个轮次
          var sortedRounds = (0, _toConsumableArray2.default)(this.selectedShift.rounds).sort(function (a, b) {
            // 获取轮次时间并考虑day_offset
            var timeA = a.time.split(':').map(Number);
            var timeB = b.time.split(':').map(Number);
            var offsetA = a.day_offset || 0;
            var offsetB = b.day_offset || 0;

            // 先比较day_offset，再比较时间
            if (offsetA !== offsetB) return offsetA - offsetB;
            if (timeA[0] !== timeB[0]) return timeA[0] - timeB[0];
            return timeA[1] - timeB[1];
          });

          // 获取第一个轮次的时间
          var firstRound = sortedRounds[0];
          if (firstRound && firstRound.time) {
            var roundTime = firstRound.time;
            var dayOffset = firstRound.day_offset || 0;

            // 计算轮次实际开始时间
            var roundStartTime = (0, _date.calculateRoundTime)(this.formData.date, roundTime, dayOffset);
            console.log("- \u7B2C\u4E00\u4E2A\u8F6E\u6B21\u65F6\u95F4: ".concat(roundTime, ", \u65E5\u671F\u504F\u79FB: ").concat(dayOffset));
            console.log("- \u8F6E\u6B21\u5B9E\u9645\u5F00\u59CB\u65F6\u95F4: ".concat(roundStartTime.toISOString()));

            // 如果轮次开始时间在当前时间之后，任务状态为"未开始"
            if (roundStartTime > now) {
              console.log('> 判断结果: 未开始（轮次开始时间在未来）');
              this.predictedStatus = this.STATUS.NOT_STARTED;
              return;
            }
          }
        }

        // 如果没有轮次信息或轮次已开始，则根据班次开始时间判断
        if (startTime > now) {
          console.log('> 判断结果: 未开始（开始时间在未来）');
          this.predictedStatus = this.STATUS.NOT_STARTED;
        } else {
          console.log('> 判断结果: 进行中（开始时间已过）');
          this.predictedStatus = this.STATUS.IN_PROGRESS;
        }
      } catch (error) {
        console.error('计算预计状态时出错:', error);
        // 出错时默认为未开始
        this.predictedStatus = this.STATUS.NOT_STARTED;
      }
    },
    // 根据任务时间计算任务状态
    calculateTaskStatus: function calculateTaskStatus(startTimeISOString) {
      // 首先获取所有已处理过的轮次（有状态值的轮次）
      if (this.processedRounds && this.processedRounds.length > 0) {
        // 检查是否有进行中的轮次
        var hasActiveRounds = this.processedRounds.some(function (round) {
          return round.status === 1;
        });
        if (hasActiveRounds) {
          console.log('> 计算结果: 进行中（有进行中的轮次）');
          return this.STATUS.IN_PROGRESS;
        }

        // 检查是否所有轮次都已超时
        var allRoundsExpired = this.processedRounds.every(function (round) {
          return round.status === 3;
        });
        if (allRoundsExpired) {
          console.log('> 计算结果: 已超时（所有轮次都已超时）');
          return this.STATUS.EXPIRED;
        }

        // 检查是否所有轮次都未开始
        var allRoundsNotStarted = this.processedRounds.every(function (round) {
          return round.status === 0;
        });
        if (allRoundsNotStarted) {
          console.log('> 计算结果: 未开始（所有轮次都未开始）');
          return this.STATUS.NOT_STARTED;
        }

        // 如果有些轮次已超时，有些未开始，但没有进行中的轮次
        // 我们应该根据最早的未开始轮次判断
        var futureRounds = this.processedRounds.filter(function (round) {
          return round.status === 0;
        });
        if (futureRounds.length > 0) {
          console.log('> 计算结果: 进行中（部分轮次已超时，部分未开始）');
          return this.STATUS.IN_PROGRESS; // 或者在逻辑上选择其他状态
        }
      }

      // 如果没有轮次信息，则回退到基于班次时间的判断
      if (!startTimeISOString) {
        return this.STATUS.NOT_STARTED; // 没有开始时间默认为未开始
      }

      try {
        // 日期格式转换，确保一致性
        var now = new Date();
        var startTime = new Date(startTimeISOString);

        // 调试日志
        console.log('提交时状态计算:');
        console.log("- \u5F00\u59CB\u65F6\u95F4ISO: ".concat(startTimeISOString));
        console.log("- \u89E3\u6790\u540E\u5F00\u59CB\u65F6\u95F4: ".concat(startTime.toISOString()));
        console.log("- \u5F53\u524D\u65F6\u95F4: ".concat(now.toISOString()));

        // 获取任务的第一个轮次（通常是最早开始的轮次）
        if (this.selectedShift && this.selectedShift.rounds && this.selectedShift.rounds.length > 0) {
          // 按照轮次时间排序
          var sortedRounds = (0, _toConsumableArray2.default)(this.selectedShift.rounds).sort(function (a, b) {
            // 获取轮次时间并考虑day_offset
            var timeA = a.time.split(':').map(Number);
            var timeB = b.time.split(':').map(Number);
            var offsetA = a.day_offset || 0;
            var offsetB = b.day_offset || 0;

            // 先比较day_offset，再比较时间
            if (offsetA !== offsetB) return offsetA - offsetB;
            if (timeA[0] !== timeB[0]) return timeA[0] - timeB[0];
            return timeA[1] - timeB[1];
          });

          // 获取第一个轮次的时间
          var firstRound = sortedRounds[0];
          var roundTime = firstRound.time;
          var dayOffset = firstRound.day_offset || 0;

          // 计算轮次实际开始时间
          var roundStartTime = (0, _date.calculateRoundTime)(this.formData.date, roundTime, dayOffset);
          console.log("- \u7B2C\u4E00\u4E2A\u8F6E\u6B21\u65F6\u95F4: ".concat(roundTime, ", \u65E5\u671F\u504F\u79FB: ").concat(dayOffset));
          console.log("- \u8F6E\u6B21\u5B9E\u9645\u5F00\u59CB\u65F6\u95F4: ".concat(roundStartTime.toISOString()));

          // 如果轮次开始时间尚未到达，则任务未开始
          if (roundStartTime > now) {
            console.log('> 计算结果: 未开始 (基于轮次时间)');
            return this.STATUS.NOT_STARTED;
          }
        }

        // 如果没有轮次信息或轮次已开始，则根据班次开始时间判断
        if (startTime > now) {
          console.log('> 计算结果: 未开始 (基于班次时间)');
          return this.STATUS.NOT_STARTED;
        }

        // 任务开始时间在当前时间之前或等于当前时间，则任务进行中
        console.log('> 计算结果: 进行中');
        return this.STATUS.IN_PROGRESS;
      } catch (error) {
        console.error('计算任务状态时出错:', error);
        // 出错时默认为未开始
        return this.STATUS.NOT_STARTED;
      }
    },
    // 获取状态图标
    getStatusIcon: function getStatusIcon(status) {
      switch (status) {
        case this.STATUS.NOT_STARTED:
          return 'info-filled';
        case this.STATUS.IN_PROGRESS:
          return 'reload';
        case this.STATUS.COMPLETED:
          return 'checkmarkempty';
        case this.STATUS.EXPIRED:
          return 'closeempty';
        case this.STATUS.CANCELLED:
          return 'minus';
        default:
          return 'info';
      }
    },
    // 获取状态文本
    getStatusText: function getStatusText(status) {
      switch (status) {
        case this.STATUS.NOT_STARTED:
          return '未开始';
        case this.STATUS.IN_PROGRESS:
          return '进行中';
        case this.STATUS.COMPLETED:
          return '已完成';
        case this.STATUS.EXPIRED:
          return '已超时';
        case this.STATUS.CANCELLED:
          return '已取消';
        default:
          return '未知状态';
      }
    },
    // 获取状态解释更新
    getStatusExplanation: function getStatusExplanation(status) {
      var now = new Date();
      // 手动格式化时间，避免时区信息显示
      var hours = now.getHours().toString().padStart(2, '0');
      var minutes = now.getMinutes().toString().padStart(2, '0');
      var formattedNow = "".concat(hours, ":").concat(minutes);
      switch (status) {
        case this.STATUS.NOT_STARTED:
          if (this.selectedShift && this.selectedShift.start_time) {
            return "\u5F53\u524D\u65F6\u95F4 ".concat(formattedNow, "\uFF0C\u4EFB\u52A1\u5C06\u5728 ").concat(this.selectedShift.start_time, " \u5F00\u59CB");
          }
          return '任务开始时间尚未到达';
        case this.STATUS.IN_PROGRESS:
          if (this.selectedShift && this.selectedShift.start_time) {
            return "\u5F53\u524D\u65F6\u95F4 ".concat(formattedNow, "\uFF0C\u5DF2\u8D85\u8FC7\u4EFB\u52A1\u5F00\u59CB\u65F6\u95F4 ").concat(this.selectedShift.start_time);
          }
          return '当前时间已超过任务开始时间';
        default:
          return '';
      }
    },
    // 返回上一页
    navigateBack: function navigateBack() {
      uni.navigateBack();
    },
    // 刷新路线点位数据
    refreshRoutePointsData: function refreshRoutePointsData() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var res, routeData;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                if (_this11.formData.route_id) {
                  _context10.next = 3;
                  break;
                }
                uni.showToast({
                  title: '请先选择路线',
                  icon: 'none'
                });
                return _context10.abrupt("return");
              case 3:
                _context10.prev = 3;
                uni.showLoading({
                  title: '刷新中...'
                });

                // 获取最新的路线点位数据
                _context10.next = 7;
                return _patrolApi.default.call({
                  name: 'patrol-route',
                  action: 'getRouteDetail',
                  data: {
                    params: {
                      route_id: _this11.formData.route_id,
                      with_points: true,
                      with_detail: true,
                      include_point_details: true,
                      force_refresh: true
                    }
                  }
                });
              case 7:
                res = _context10.sent;
                if (!(res.code === 0 && res.data)) {
                  _context10.next = 16;
                  break;
                }
                routeData = res.data; // 更新路线点位数据
                _this11.selectedRoute = _objectSpread(_objectSpread({}, _this11.selectedRoute), {}, {
                  pointsDetail: routeData.pointsDetail || []
                });

                // 标记点位数据已刷新
                _this11.pointsRefreshed = true;

                // 显示成功提示
                uni.hideLoading();
                uni.showToast({
                  title: '点位数据已更新',
                  icon: 'success'
                });
                _context10.next = 17;
                break;
              case 16:
                throw new Error(res.message || '获取最新点位数据失败');
              case 17:
                _context10.next = 24;
                break;
              case 19:
                _context10.prev = 19;
                _context10.t0 = _context10["catch"](3);
                console.error('刷新点位数据失败:', _context10.t0);
                uni.hideLoading();
                uni.showToast({
                  title: '刷新点位数据失败',
                  icon: 'none'
                });
              case 24:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10, null, [[3, 19]]);
      }))();
    },
    // 过滤用户选项
    filterUserOptions: function filterUserOptions() {
      var searchText = this.searchUserName.toLowerCase();
      if (!searchText) {
        // 如果搜索框为空，显示所有用户
        this.filteredUsers = (0, _toConsumableArray2.default)(this.userOptions);
      } else {
        this.filteredUsers = this.userOptions.filter(function (user) {
          return user.nickname && user.nickname.toLowerCase().includes(searchText) && (user._id === '' || !user.nickname.startsWith('匿名'));
        } // 确保搜索结果也遵循匿名用户过滤规则，但保留默认选项
        );
      }
    },
    // 选择用户
    selectUser: function selectUser(index) {
      var user = this.filteredUsers[index];
      if (user && user._id) {
        this.formData.user_id = user._id;
        this.selectedUser = user;
      } else {
        this.formData.user_id = '';
        this.selectedUser = null;
      }
      this.showUserSelect = false;
    },
    // 隐藏用户选择器
    hideUserSelect: function hideUserSelect() {
      this.showUserSelect = false;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 417:
/*!*******************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/add.vue?vue&type=style&index=0&lang=scss& ***!
  \*******************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=style&index=0&lang=scss& */ 418);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 418:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/add.vue?vue&type=style&index=0&lang=scss& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[411,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/patrol_pkg/task/add.js.map
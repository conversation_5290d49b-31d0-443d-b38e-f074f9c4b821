





































































































































































































































































































































































	/* 搜索区域样式 */
.search-box.data-v-2980693f {
		background: #f9fafb;
		border: 1rpx solid #e2e8f0;
		border-radius: 8rpx;
		margin-bottom: 16rpx;
		padding: 16rpx;
}
.full-width.data-v-2980693f {
	flex: 1;
	width: 100%;
}
	/* 状态徽章样式 */
.status-cell.data-v-2980693f {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4rpx;
}
.status-badge.data-v-2980693f {
	padding: 6rpx 12rpx;
	color: white;
	border-radius: 16rpx;
	font-size: 24rpx;
	text-align: center;
	min-width: 120rpx;
}
.workflow-type.data-v-2980693f {
	font-size: 20rpx;
	color: #999;
	background: #f0f0f0;
	padding: 2rpx 8rpx;
	border-radius: 8rpx;
}
	/* 进度条样式 */
.progress-cell.data-v-2980693f {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}
.progress-bar.data-v-2980693f {
	width: 80rpx;
	height: 12rpx;
	background: #e0e0e0;
	border-radius: 6rpx;
	overflow: hidden;
}
.progress-fill.data-v-2980693f {
	height: 100%;
	background: linear-gradient(90deg, #4caf50, #8bc34a);
	transition: width 0.3s ease;
}
.progress-text.data-v-2980693f {
	font-size: 22rpx;
	color: #666;
}
	/* 时效显示样式 */
.timing-cell.data-v-2980693f {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4rpx;
}
.timing-badge.data-v-2980693f {
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	font-size: 24rpx;
	color: white;
	min-width: 80rpx;
	text-align: center;
}
.timing-badge.normal.data-v-2980693f {
	background: #4caf50;
}
.timing-badge.warning.data-v-2980693f {
	background: #ff9800;
}
.timing-badge.urgent.data-v-2980693f {
	background: #f44336;
}
.timing-badge.completed.data-v-2980693f {
	background: #4caf50;
	color: #fff;
}
.timing-badge.terminated.data-v-2980693f {
	background: #9e9e9e;
	color: #fff;
}
.overdue-text.data-v-2980693f {
	font-size: 20rpx;
	color: #f44336;
	font-weight: bold;
}
	/* 负责人信息样式 */
.responsible-info.data-v-2980693f {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4rpx;
}
.responsible-name.data-v-2980693f {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
}
.assigned-time.data-v-2980693f {
	font-size: 20rpx;
	color: #999;
}
.no-responsible.data-v-2980693f {
	color: #ccc;
	font-style: italic;
}
	/* 操作按钮样式 */
.action-buttons.data-v-2980693f {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
	justify-content: center;
}
.action-btn.data-v-2980693f {
	padding: 8rpx 16rpx;
	border: none;
	border-radius: 16rpx;
	font-size: 22rpx;
	color: white;
	min-width: 80rpx;
}
	/* 微信小程序按钮垂直排列 */
.action-buttons.data-v-2980693f {
	flex-direction: column;
	gap: 6rpx;
	width: 100%;
}
.action-btn.data-v-2980693f {
	width: 100%;
	padding: 8rpx 16rpx;
	font-size: 24rpx;
	min-width: auto;
	border-radius: 12rpx;
}
.view-btn.data-v-2980693f {
	background: #2196f3;
}
.edit-btn.data-v-2980693f {
	background: #4caf50;
}
.assign-btn.data-v-2980693f {
	background: #ff9800;
}
.complete-btn.data-v-2980693f {
	background: #00bcd4;
}
.delete-btn.data-v-2980693f {
	background: #f44336;
}
.approve-btn.data-v-2980693f {
	background: #9c27b0;
}
.convert-btn.data-v-2980693f {
	background: #607d8b;
}
.archive-btn.data-v-2980693f {
	background: #9e9e9e;
}
	/* 图片样式优化 */
.image-container.data-v-2980693f {
	display: flex;
	gap: 8rpx;
	justify-content: center;
	align-items: center;
	min-height: 120rpx;
	margin: 0;
	padding: 0;
}
.image-wrapper.data-v-2980693f {
	position: relative;
	width: 160rpx;
	height: 160rpx;
	cursor: pointer;
	overflow: hidden;
	border: 1px solid #e2e8f0;
	transition: all 0.3s ease;
}
.image-wrapper.data-v-2980693f:hover {
	-webkit-transform: translateY(-2px);
	        transform: translateY(-2px);
	border-color: #cbd5e1;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.image-wrapper image.data-v-2980693f {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
	object-fit: cover;
}
	/* 微信小程序图片优化 */
.image-wrapper.data-v-2980693f {
	width: 140rpx !important;
	height: 140rpx !important;
}
.image-container.data-v-2980693f {
	gap: 16rpx;
	min-height: 120rpx;
}
.image-overlay.data-v-2980693f {
	position: absolute;
	top: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.7);
	color: white;
	font-size: 18rpx;
	padding: 2rpx 6rpx;
	border-radius: 0 8rpx 0 8rpx;
}
.no-image.data-v-2980693f {
	color: #ccc;
	font-style: italic;
	font-size: 24rpx;
}
	/* 时间文本样式 */
.time-text.data-v-2980693f {
	font-size: 24rpx;
	color: #666;
}
	/* 备注区域样式 */
.remarks-cell.data-v-2980693f {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
	white-space: pre-wrap;
	word-break: break-word;
}
	/* 分页容器样式 */
.pagination-container.data-v-2980693f {
	margin: 32rpx 0;
	display: flex;
	justify-content: center;
}
	/* 数据区域样式 */
.data-area.data-v-2980693f {
	min-height: 400rpx;
	background: #fff;
	border-radius: 8rpx;
}
.no-data-area.data-v-2980693f {
	min-height: 400rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #fff;
	border-radius: 8rpx;
}
	/* 数据加载区域样式 */
.data-loading.data-v-2980693f {
	padding: 60rpx 0;
	text-align: center;
	background: #fff;
	border-radius: 8rpx;
}
	/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.workflow-summary.data-v-2980693f {
		flex-direction: column;
		gap: 8rpx;
}
.action-buttons.data-v-2980693f {
		flex-direction: column;
}
.action-btn.data-v-2980693f {
		width: 100%;
}
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 基础布局样式 */
.uni-container {
  padding: 24px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafb 0%, #e8f4f8 100%);
}
.db-container {
  width: 100%;
  max-width: 92%;
  margin: 0 auto;
  padding: 24px;
  background-color: #ffffff;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  overflow: visible;
  /* 允许弹窗溢出容器 */
  box-sizing: border-box;
}
/* 按钮样式增强 */
.uni-group {
  display: flex;
  gap: 12px;
}
.uni-button {
  padding: 8px 18px;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.3px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
  position: relative;
  overflow: hidden;
  text-transform: none;
  border: none;
}
.uni-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  z-index: 1;
}
.uni-button[type=default] {
  background: #f9fafc;
  color: #475569;
  border: 1px solid #e2e8f0;
}
.uni-button[type=primary] {
  background: linear-gradient(145deg, #3975d9, #2862c6);
  color: #fff;
}
.uni-button[type=warn] {
  background: linear-gradient(135deg, #f43f5e, #ef4444);
  color: #fff;
}
.uni-button:active {
  -webkit-transform: translateY(1px);
          transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.uni-button:hover {
  -webkit-transform: translateY(-3px);
          transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}
.uni-button[type=primary]:hover {
  background: linear-gradient(145deg, #4986ea, #3974d7);
}
.uni-button[type=warn]:hover {
  background: linear-gradient(135deg, #fb7185, #f87171);
}
/* 表格样式优化 */
.uni-table {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #f1f5f9;
  width: 100% !important;
  margin: 0 auto;
}
.uni-th {
  background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
  font-weight: 600;
  padding: 16px 12px;
  text-align: center;
  color: #334155;
  border-bottom: 2px solid #e2e8f0;
  position: relative;
}
.uni-th::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #3b82f6, transparent);
  -webkit-transform: scaleX(0);
          transform: scaleX(0);
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.uni-th:hover::after {
  -webkit-transform: scaleX(0.8);
          transform: scaleX(0.8);
}
.uni-td {
  padding: 14px 12px;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
  transition: background-color 0.2s ease;
}
/* 重复的image-container定义已合并到上面 */
/* 重复的image-wrapper定义已合并到上面 */
.image-count {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px;
}
.image-count:hover {
  background: rgba(0, 0, 0, 0.65);
}
.image-hover {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  object-fit: cover;
  transition: all 0.3s ease;
}
/* 分页控件样式 - 使用默认样式 */
.uni-pagination-box {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  margin-top: 24px;
}
/* 日期选择器弹窗层级和位置修复 */
 .uni-datetime-picker__mask {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9998 !important;
}
 .uni-datetime-picker__popup {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  -webkit-transform: translate(-50%, -50%) !important;
          transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
  max-height: 80vh !important;
  overflow: auto !important;
}
/* 修改微信小程序分页样式 */
/* 保留微信小程序的基础样式，但不自定义太多 */
.uni-dateformat {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}
/* 表格行和单元格样式 */
.uni-table-th-row {
  font-size: 16px;
  color: #334155;
  font-weight: 600;
}
.uni-table-td-row {
  color: #475569;
  font-size: 15px;
}
.uni-table-td {
  height: 100% !important;
  vertical-align: middle !important;
}
.uni-table-tr:hover .uni-table-td {
  background-color: rgba(59, 130, 246, 0.04);
}
/* 描述单元格优化 */
.description-cell {
  max-width: 300px;
  margin: 0 auto;
  text-align: left;
  position: relative;
  -webkit-user-select: text;
          user-select: text;
}
.description-text {
  text-align: center;
  display: block;
  font-size: 14px;
  line-height: 1.6;
  color: #334155;
  word-break: break-all;
}
.description-text.text-truncate {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.expand-button {
  font-size: 13px;
  cursor: pointer;
  padding: 4px 8px;
  text-align: center;
  color: #4A9FD1;
  font-weight: 500;
  margin-top: 4px;
  border-radius: 4px;
  background-color: rgba(74, 159, 209, 0.08);
  transition: all 0.2s ease;
  display: inline-block;
  float: right;
}
.expand-button:hover {
  background-color: rgba(74, 159, 209, 0.15);
  color: #3d8bc2;
}
/* 搜索区域美化 - 修复间距问题 */
.search-box {
  background: linear-gradient(135deg, #ffffff, #f9fafb);
  padding: 24px;
  margin-bottom: 24px;
  /* 统一上下间距 */
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(226, 232, 240, 0.8);
}
/* 日期搜索区域特殊样式 */
.date-section {
  margin-top: 24px;
}
.date-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.date-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 260px;
}
.search-box .search-row {
  display: flex;
  margin-bottom: 18px;
}
.search-box .search-item {
  flex: 1;
  margin-right: 16px;
}
.search-box .search-item:last-child {
  margin-right: 0;
}
.search-box .search-label {
  min-width: 64px;
  font-size: 14px;
  color: #475569;
  margin-right: 10px;
  font-weight: 600;
  line-height: 36px;
}
.search-box .select-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}
/* 小程序端特殊处理 */
.search-box .search-row {
  flex-direction: column;
}
.search-box .search-item {
  margin-right: 0;
  margin-bottom: 16px;
}
.search-box .search-item:last-child {
  margin-bottom: 0;
}
.search-box .select-row {
  grid-template-columns: 1fr;
}
.date-row {
  flex-direction: column;
}
.date-item {
  margin-bottom: 16px;
}
.date-item:last-child {
  margin-bottom: 0;
}
/* 微信小程序样式增强 */
.uni-table-tr {
  background-color: #ffffff;
}
.uni-table-tr:nth-child(even) {
  background-color: #f8fafc;
}
.uni-table-tr:hover {
  background-color: rgba(59, 130, 246, 0.05);
}
.uni-table-th {
  background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
  font-weight: 600 !important;
  color: #334155;
  padding: 16px 12px !important;
  border-bottom: 2px solid #e2e8f0 !important;
}
.uni-table-td {
  padding: 14px 12px !important;
  height: auto !important;
  vertical-align: middle !important;
}
/* 图片列专门优化 - 减少内边距 */
.uni-table-td:nth-child(3) {
  padding: 8px 6px !important;
}
/* 重复的image-container定义已合并到上面 */
.uni-easyinput__content {
  background-color: #ffffff !important;
  border: 1px solid #cbd5e1 !important;
  border-radius: 8px !important;
  transition: all 0.3s !important;
  padding: 0 14px !important;
  height: 40px !important;
  box-sizing: border-box !important;
}
.uni-easyinput__content-input {
  font-size: 15px !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
  text-indent: 0 !important;
}
/* 调整输入框占位符文字 */
.uni-easyinput__placeholder-class {
  font-size: 15px !important;
  padding-left: 0 !important;
}
.uni-datetime-picker--button {
  border-radius: 8px !important;
  border: 1px solid #cbd5e1 !important;
  transition: all 0.3s !important;
  background-color: #ffffff !important;
  height: 40px !important;
  box-sizing: border-box !important;
  padding: 0 14px !important;
  font-size: 15px !important;
}
.uni-datetime-picker--button text {
  font-size: 15px !important;
}
/* 统一下拉选择器字体大小 */
.uni-data-select {
  font-size: 15px !important;
}
.uni-data-select .uni-select__input-text {
  font-size: 15px !important;
  padding-left: 0 !important;
  text-indent: 0 !important;
}
/* 调整选择器内部元素 */
.uni-data-select .uni-select__selector {
  padding: 0 14px !important;
  height: 40px !important;
  box-sizing: border-box !important;
}
.uni-load-more {
  margin: 20px auto !important;
}
/* 理由显示区域样式 */
.remarks-cell {
  text-align: center;
  padding: 12px 8px;
  line-height: 1.5;
}
.reason-text {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 100%;
}
.reason-item {
  font-size: 13px;
  line-height: 1.4;
  padding: 4px 0;
  word-wrap: break-word;
  text-align: center;
  color: #666;
}
.no-reason {
  color: #9ca3af;
  font-size: 13px;
  font-style: italic;
}
/* 工作流状态文本增强 - 新工作流系统样式 */
.approval-status-text {
  display: inline-block;
  padding: 8px 14px;
  border-radius: 50px;
  font-weight: 500;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
  position: relative;
  overflow: hidden;
  z-index: 1;
}
.approval-status-text::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  z-index: -1;
}
.approval-status-text:hover {
  -webkit-transform: translateY(-3px);
          transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}
.approval-status-text:hover::before {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
/* 加载遮罩优化（已弃用，避免全屏蒙层） */
/* .loading-mask {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(6px);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 999;
} */
/* 视觉分隔线 - 增强搜索区域与表格的视觉分隔 */
.search-table-divider {
  height: 10px;
}
/* 表格居中样式 */
.db-container {
  width: 100%;
  max-width: 92%;
  margin: 0 auto;
  padding: 24px;
  box-sizing: border-box;
}
/* 微信小程序特定样式 */
.db-container {
  width: 100%;
  max-width: 100%;
  padding: 16px;
  box-sizing: border-box;
}
.uni-table {
  width: 100% !important;
  margin: 0 auto;
}
/* 针对小屏幕设备优化表格居中 */
@media screen and (max-width: 768px) {
.db-container {
    padding-left: 12px;
    padding-right: 12px;
}
.uni-container {
    padding: 16px;
}
}
/* 微信小程序弹窗选择器样式 */
.picker-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 14px;
  background-color: #ffffff;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  height: 40px;
  box-sizing: border-box;
  transition: all 0.3s ease;
}
.picker-button:active {
  background-color: #f5f5f5;
  border-color: #4A9FD1;
}
.picker-text {
  flex: 1;
  font-size: 15px;
  color: #333333;
  text-align: left;
  line-height: 1.2;
  font-weight: 400;
}
.picker-text.placeholder {
  color: #999999;
}
.picker-arrow {
  font-size: 12px;
  color: #999999;
  margin-left: 8px;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.picker-button:active .picker-arrow {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
/* 弹窗内容样式 */
.popup-content {
  background-color: #ffffff;
  border-radius: 16px 16px 0 0;
  max-height: 60vh;
  overflow: hidden;
}
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}
.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}
.popup-close {
  font-size: 14px;
  color: #4A9FD1;
  padding: 4px 8px;
}
.popup-body {
  max-height: 50vh;
  overflow-y: auto;
}
.popup-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s ease;
}
.popup-item:last-child {
  border-bottom: none;
}
.popup-item:active {
  background-color: #f8f9fa;
}
.popup-item.active {
  background-color: #e6f3ff;
  color: #4A9FD1;
}
.popup-item text {
  font-size: 15px;
  color: #333333;
}
.popup-item.active text {
  color: #4A9FD1;
  font-weight: 500;
}
.check-icon {
  font-size: 16px;
  color: #4A9FD1;
  font-weight: bold;
}

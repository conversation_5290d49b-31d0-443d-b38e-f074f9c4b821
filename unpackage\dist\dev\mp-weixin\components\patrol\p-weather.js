(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["components/patrol/p-weather"],{

/***/ 523:
/*!***********************************************!*\
  !*** D:/Xwzc/components/patrol/p-weather.vue ***!
  \***********************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _p_weather_vue_vue_type_template_id_5192dacf___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./p-weather.vue?vue&type=template&id=5192dacf& */ 524);
/* harmony import */ var _p_weather_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./p-weather.vue?vue&type=script&lang=js& */ 526);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _p_weather_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _p_weather_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _p_weather_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./p-weather.vue?vue&type=style&index=0&lang=scss& */ 528);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _p_weather_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _p_weather_vue_vue_type_template_id_5192dacf___WEBPACK_IMPORTED_MODULE_0__["render"],
  _p_weather_vue_vue_type_template_id_5192dacf___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _p_weather_vue_vue_type_template_id_5192dacf___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "components/patrol/p-weather.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 524:
/*!******************************************************************************!*\
  !*** D:/Xwzc/components/patrol/p-weather.vue?vue&type=template&id=5192dacf& ***!
  \******************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_template_id_5192dacf___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./p-weather.vue?vue&type=template&id=5192dacf& */ 525);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_template_id_5192dacf___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_template_id_5192dacf___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_template_id_5192dacf___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_template_id_5192dacf___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 525:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/components/patrol/p-weather.vue?vue&type=template&id=5192dacf& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.weather.weather.length
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 526:
/*!************************************************************************!*\
  !*** D:/Xwzc/components/patrol/p-weather.vue?vue&type=script&lang=js& ***!
  \************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./p-weather.vue?vue&type=script&lang=js& */ 527);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 527:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/components/patrol/p-weather.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/**
 * 天气组件
 * 基于腾讯地图天气API，显示实时天气信息
 */
var _default = {
  name: 'p-weather',
  props: {
    // 是否为迷你模式（只显示温度和天气状态）
    mini: {
      type: Boolean,
      default: false
    }
  },
  data: function data() {
    return {
      weather: {
        weather: '获取中...',
        temperature: '--',
        weatherCode: 'default',
        humidity: '--',
        windPower: '--',
        windDirection: '--',
        updateTime: '--'
      },
      air: null,
      address: '',
      loading: false,
      error: null,
      apiKey: '5MPBZ-FCW63-3C43B-R7G72-UOSAO-ZWBTJ' // 腾讯地图API Key
    };
  },

  computed: {
    // 根据天气代码返回对应的表情符号
    weatherIconText: function weatherIconText() {
      // 如果weatherCode不存在，返回默认图标
      if (!this.weather || !this.weather.weatherCode) return '🌈';

      // 腾讯地图天气代码映射表
      var iconMap = {
        '00': '☀️',
        // 晴
        '01': '🌥️',
        // 多云
        '02': '☁️',
        // 阴
        '03': '🌦️',
        // 阵雨
        '04': '⛈️',
        // 雷阵雨
        '05': '⛈️',
        // 雷阵雨伴有冰雹
        '06': '🌨️',
        // 雨夹雪
        '07': '🌧️',
        // 小雨
        '08': '🌧️',
        // 中雨
        '09': '🌧️',
        // 大雨
        '10': '🌧️',
        // 暴雨
        '11': '🌧️',
        // 大暴雨
        '12': '🌧️',
        // 特大暴雨
        '13': '🌨️',
        // 阵雪
        '14': '❄️',
        // 小雪
        '15': '❄️',
        // 中雪
        '16': '❄️',
        // 大雪
        '17': '❄️',
        // 暴雪
        '18': '🌫️',
        // 雾
        '19': '🌧️',
        // 冻雨
        '20': '🌪️',
        // 沙尘暴
        '21': '🌧️',
        // 小到中雨
        '22': '🌧️',
        // 中到大雨
        '23': '🌧️',
        // 大到暴雨
        '24': '🌧️',
        // 暴雨到大暴雨
        '25': '🌧️',
        // 大暴雨到特大暴雨
        '26': '❄️',
        // 小到中雪
        '27': '❄️',
        // 中到大雪
        '28': '❄️',
        // 大到暴雪
        '29': '🌫️',
        // 浮尘
        '30': '🌫️',
        // 扬沙
        '31': '🌪️',
        // 强沙尘暴
        '32': '🌫️',
        // 轻雾
        '33': '🌫️',
        // 大雾
        '34': '🌫️',
        // 特强浓雾
        '35': '🌡️',
        // 热
        '36': '🌡️',
        // 冷
        '37': '🌪️',
        // 龙卷风
        '38': '🌧️',
        // 雨
        '39': '🌨️',
        // 雪
        '40': '☔️',
        // 阵雨转晴
        '41': '⛈️',
        // 雷阵雨转晴
        '42': '🌤️',
        // 晴转多云
        '43': '🌥️',
        // 多云转晴
        '44': '☁️',
        // 阴转晴
        '45': '🌦️',
        // 晴转雨
        '46': '🌨️',
        // 晴转雪
        '47': '🌫️',
        // 霾转晴
        '48': '🌫️',
        // 晴转霾
        '49': '🌪️',
        // 扬沙转晴
        '50': '🌪️',
        // 晴转扬沙
        '51': '🌫️',
        // 雾转晴
        '52': '🌤️',
        // 晴转雾
        '53': '🌫️',
        // 霾
        '54': '💨',
        // 大风
        '55': '🌪️',
        // 飑
        '56': '🌡️',
        // 寒潮
        '57': '🌡️',
        // 热浪
        '58': '🌫️',
        // 轻度霾
        '59': '🌫️',
        // 中度霾
        '60': '🌫️',
        // 重度霾
        // 默认图标
        'default': '🌈'
      };
      return iconMap[this.weather.weatherCode] || iconMap['default'];
    },
    airQualityLevel: function airQualityLevel() {
      if (!this.air) return '';

      // 根据AQI数值确定空气质量等级
      var aqi = parseInt(this.air.aqi);
      if (aqi <= 50) return 'excellent';
      if (aqi <= 100) return 'good';
      if (aqi <= 150) return 'moderate';
      if (aqi <= 200) return 'poor';
      if (aqi <= 300) return 'bad';
      return 'severe';
    }
  },
  created: function created() {
    // 组件创建时立即加载天气数据
    this.fetchData();
  },
  methods: {
    fetchData: function fetchData() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var cacheKey, weatherData, locationData;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (!_this.loading) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return");
              case 2:
                _this.loading = true;
                _this.error = null;
                _context.prev = 4;
                // 在加载前先设置显示加载状态
                _this.weather = {
                  weather: '获取中...',
                  temperature: '--',
                  weatherCode: 'default',
                  humidity: '--',
                  windPower: '--',
                  windDirection: '--',
                  updateTime: '--'
                };

                // 尝试从缓存获取天气数据
                cacheKey = 'weather_data';
                weatherData = _this.getCachedData(cacheKey);
                if (!(!weatherData || _this.isCacheExpired(weatherData.timestamp))) {
                  _context.next = 19;
                  break;
                }
                _context.next = 11;
                return _this.getLocationFromIP();
              case 11:
                locationData = _context.sent;
                _context.next = 14;
                return _this.fetchWeatherByAdcode(locationData.adcode);
              case 14:
                weatherData = _context.sent;
                // 设置地址
                _this.address = locationData.address;

                // 缓存天气数据
                _this.cacheData(cacheKey, weatherData);
                _context.next = 21;
                break;
              case 19:
                weatherData = weatherData.data;
                // 从缓存恢复地址
                _this.address = weatherData.address || '';
              case 21:
                // 更新天气数据到界面，只在成功获取数据后才更新
                if (weatherData && weatherData.weather) {
                  Object.assign(_this.weather, weatherData.weather);
                  _this.air = weatherData.air;
                  _this.$emit('weather-loaded', {
                    weather: weatherData.weather,
                    air: weatherData.air,
                    address: _this.address
                  });
                }
                _context.next = 28;
                break;
              case 24:
                _context.prev = 24;
                _context.t0 = _context["catch"](4);
                _this.error = _context.t0.message || '获取天气数据失败';
                _this.$emit('weather-error', _this.error);
              case 28:
                _context.prev = 28;
                _this.loading = false;
                return _context.finish(28);
              case 31:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[4, 24, 28, 31]]);
      }))();
    },
    // 从IP获取位置信息（包括adcode和地址）
    getLocationFromIP: function getLocationFromIP() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                return _context2.abrupt("return", new Promise(function (resolve, reject) {
                  uni.request({
                    url: 'https://apis.map.qq.com/ws/location/v1/ip',
                    data: {
                      key: _this2.apiKey,
                      output: 'json'
                    },
                    success: function success(res) {
                      if (res.statusCode === 200 && res.data && res.data.status === 0) {
                        var result = res.data.result;
                        var adInfo = result.ad_info;

                        // 构建地址文本
                        var address = "".concat(adInfo.province).concat(adInfo.city).concat(adInfo.district);
                        resolve({
                          adcode: adInfo.adcode,
                          address: address,
                          location: result.location
                        });
                      } else {
                        var _res$data;
                        reject(new Error(((_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.message) || 'IP定位请求失败'));
                      }
                    },
                    fail: function fail(err) {
                      reject(new Error(err.errMsg || 'IP定位请求失败'));
                    }
                  });
                }));
              case 1:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    // 使用adcode获取天气数据
    fetchWeatherByAdcode: function fetchWeatherByAdcode(adcode) {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                return _context3.abrupt("return", new Promise(function (resolve, reject) {
                  uni.request({
                    url: 'https://apis.map.qq.com/ws/weather/v1/',
                    data: {
                      key: _this3.apiKey,
                      adcode: adcode,
                      output: 'json'
                    },
                    success: function success(res) {
                      if (res.statusCode === 200 && res.data && res.data.status === 0) {
                        // 处理并标准化天气数据
                        var weatherData = _this3.parseWeatherData(res.data);
                        resolve(weatherData);
                      } else {
                        var _res$data2;
                        reject(new Error(((_res$data2 = res.data) === null || _res$data2 === void 0 ? void 0 : _res$data2.message) || '获取天气数据失败'));
                      }
                    },
                    fail: function fail(err) {
                      reject(new Error(err.errMsg || '请求天气数据失败'));
                    }
                  });
                }));
              case 1:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    // 解析腾讯地图天气API返回的数据
    parseWeatherData: function parseWeatherData(data) {
      var result = {
        weather: {},
        air: null
      };
      try {
        // 处理实时天气数据 - 腾讯天气API返回格式为 result.realtime[0].infos
        if (data.result && data.result.realtime && data.result.realtime[0] && data.result.realtime[0].infos) {
          var realtime = data.result.realtime[0];
          var infos = realtime.infos;

          // 保存地址信息供缓存使用
          result.address = "".concat(realtime.province).concat(realtime.city).concat(realtime.district);

          // 根据文档，获取天气码 (https://lbs.qq.com/service/webService/webServiceGuide/weatherinfo)
          // 将天气文本转换为天气码
          var weatherText = infos.weather || '';
          var weatherCode = this.getWeatherCodeFromText(weatherText);
          result.weather = {
            weather: infos.weather || '未知',
            // 天气文本
            temperature: String(infos.temperature || '--'),
            // 确保温度是字符串
            weatherCode: weatherCode,
            // 天气代码
            humidity: String(infos.humidity || '--') + '%',
            // 确保湿度是字符串 
            windPower: infos.wind_power || '--',
            // 风力
            windDirection: infos.wind_direction || '--',
            // 风向
            updateTime: realtime.update_time || '--' // 更新时间
          };
        } else {
          // 设置默认值防止界面显示错误
          result.weather = {
            weather: '获取中...',
            // 使用一致的加载中文本
            temperature: '--',
            weatherCode: 'default',
            // 使用default确保显示默认天气图标
            humidity: '--',
            windPower: '--',
            windDirection: '--',
            updateTime: '--'
          };
        }
      } catch (error) {
        // 出错时设置默认值
        result.weather = {
          weather: '获取中...',
          // 使用一致的加载中文本
          temperature: '--',
          weatherCode: 'default',
          // 使用default确保显示默认天气图标
          humidity: '--',
          windPower: '--',
          windDirection: '--',
          updateTime: '--'
        };
      }
      return result;
    },
    // 根据天气文本获取相应的天气代码
    getWeatherCodeFromText: function getWeatherCodeFromText(text) {
      // 天气文本到代码的映射表
      var weatherMapping = {
        '晴': '00',
        '晴天': '00',
        '多云': '01',
        '阴': '02',
        '阵雨': '03',
        '雷阵雨': '04',
        '雷阵雨伴有冰雹': '05',
        '雨夹雪': '06',
        '小雨': '07',
        '中雨': '08',
        '大雨': '09',
        '暴雨': '10',
        '大暴雨': '11',
        '特大暴雨': '12',
        '阵雪': '13',
        '小雪': '14',
        '中雪': '15',
        '大雪': '16',
        '暴雪': '17',
        '雾': '18',
        '冻雨': '19',
        '沙尘暴': '20',
        '小到中雨': '21',
        '中到大雨': '22',
        '大到暴雨': '23',
        '暴雨到大暴雨': '24',
        '大暴雨到特大暴雨': '25',
        '小到中雪': '26',
        '中到大雪': '27',
        '大到暴雪': '28',
        '浮尘': '29',
        '扬沙': '30',
        '强沙尘暴': '31',
        '轻雾': '32',
        '大雾': '33',
        '特强浓雾': '34',
        '热': '35',
        '冷': '36',
        '龙卷风': '37',
        '雨': '38',
        '雪': '39',
        '阵雨转晴': '40',
        '雷阵雨转晴': '41',
        '晴转多云': '42',
        '多云转晴': '43',
        '阴转晴': '44',
        '晴转雨': '45',
        '晴转雪': '46',
        '霾转晴': '47',
        '晴转霾': '48',
        '扬沙转晴': '49',
        '晴转扬沙': '50',
        '雾转晴': '51',
        '晴转雾': '52',
        '霾': '53',
        '大风': '54',
        '飑': '55',
        '寒潮': '56',
        '热浪': '57',
        '轻度霾': '58',
        '中度霾': '59',
        '重度霾': '60'
      };
      return weatherMapping[text] || 'default';
    },
    // 获取缓存数据
    getCachedData: function getCachedData(key) {
      try {
        var cached = uni.getStorageSync(key);
        if (cached) {
          return JSON.parse(cached);
        }
      } catch (e) {
        console.error('获取缓存数据失败', e);
      }
      return null;
    },
    // 缓存数据
    cacheData: function cacheData(key, data) {
      try {
        uni.setStorageSync(key, JSON.stringify({
          data: data,
          timestamp: Date.now()
        }));
      } catch (e) {
        console.error('缓存数据失败', e);
      }
    },
    // 判断缓存是否过期（1小时）
    isCacheExpired: function isCacheExpired(timestamp) {
      var ONE_HOUR = 60 * 60 * 1000;
      return Date.now() - timestamp > ONE_HOUR;
    },
    // 手动刷新天气
    refresh: function refresh() {
      return this.fetchData();
    },
    // 清除缓存
    clearCache: function clearCache(key) {
      try {
        uni.removeStorageSync(key);
        console.log('已清除缓存:', key);
      } catch (e) {
        console.error('清除缓存失败', e);
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 528:
/*!*********************************************************************************!*\
  !*** D:/Xwzc/components/patrol/p-weather.vue?vue&type=style&index=0&lang=scss& ***!
  \*********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./p-weather.vue?vue&type=style&index=0&lang=scss& */ 529);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_p_weather_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 529:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/components/patrol/p-weather.vue?vue&type=style&index=0&lang=scss& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/patrol/p-weather.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/patrol/p-weather-create-component',
    {
        'components/patrol/p-weather-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(523))
        })
    },
    [['components/patrol/p-weather-create-component']]
]);

{"version": 3, "sources": ["webpack:///D:/Xwzc/uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue?7c7f", "webpack:///D:/Xwzc/uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue?d180", "webpack:///D:/Xwzc/uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue?4f9d", "webpack:///D:/Xwzc/uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue?5617", "uni-app:///uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue", "webpack:///D:/Xwzc/uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue?59f5", "webpack:///D:/Xwzc/uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue?40c0"], "names": ["name", "mixins", "emits", "props", "mode", "type", "default", "multiple", "value", "modelValue", "localdata", "min", "max", "wrap", "icon", "selectedColor", "selectedTextColor", "emptyText", "disabled", "map", "text", "watch", "handler", "deep", "mixinDatacomResData", "data", "dataList", "range", "contentText", "contentdown", "contentrefresh", "contentnomore", "isLocal", "styles", "isTop", "computed", "dataValue", "created", "methods", "loadData", "getForm", "parent", "parentName", "change", "detail", "getDataList", "item", "list", "setRang<PERSON>", "setStyles", "getSelectedValue", "<PERSON><PERSON><PERSON>", "setStyleBackgroud", "classles", "setStyleIcon", "setStyleIconText", "setStyleRightIcon"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACqE;AACL;AACc;;;AAG9E;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA2mB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgD/nB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvBA,gBAyBA;EACAA;EACAC;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IAEAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;QACA;MACA;IACA;IACAI;MACAL;MACAC;QACA;MACA;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;QACA;UACAc;UACAZ;QACA;MACA;IACA;EACA;EACAa;IACAX;MACAY;QACA;QACA;MACA;MACAC;IACA;IACAC;MACA;MACA;IACA;IACAhB;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAgB;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;QACAlB;QACAC;MACA;MACAkB;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;MACA;MACA;MACA;IACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;UACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;QACA;QACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MAEA;QACAnC;QACAiB;MACA;MAEA;QACA;UAEA;YACAmB;YACAA;UACA;QACA;MACA;QACA;UAAA;QAAA;QACA;UACAA;YACApC;YACAiB;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAmB;MACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACA;QACA;UACArC;QACA;MACA;QACA;UACAA;QACA;MACA;MACAkB;QACAoB;QACA;UACA;YACA;cAAA;YAAA;YACAA;UACA;YACAA;UACA;QACA;UACAA;QACA;QAEAC;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;MACA;MACAD;QACA;UACA;YACA;cAAA;YAAA;YACA;cACAD;YACA;UACA;UAEA;YACA;cAAA;YAAA;YACA;cACAA;YACA;UACA;QACA;QACA;QACAC;MACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAE;MACA;MACAH;MACAA;MACAA;MACAA;IACA;IAEA;AACA;AACA;AACA;IACAI;MAAA;MACA;MACA;MACAvB;QACA;UACAwB;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;UACAnB;QACA;QACA;UACAA;QACA;MACA;MACA;MACA;QACAoB;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACArB;QACAA;QAEA;UACAA;UACAA;QACA;MACA;MACA;QACAoB;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;QACA;QACA;UACAtB;QACA;UACAA;QACA;QACA;UACAA;QACA;MACA;MACA;QACAoB;MACA;MACA;IACA;IACAG;MACA;MACA;MACA;QACAvB;MACA;MACA;QACAoB;MACA;MAEA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC5cA;AAAA;AAAA;AAAA;AAAspC,CAAgB,4nCAAG,EAAC,C;;;;;;;;;;;ACA1qC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-data-checkbox.vue?vue&type=template&id=84d5d996&\"\nvar renderjs\nimport script from \"./uni-data-checkbox.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-data-checkbox.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-data-checkbox.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-data-checkbox.vue?vue&type=template&id=84d5d996&\"", "var components\ntry {\n  components = {\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-data-checkbox.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-data-checkbox.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"uni-data-checklist\" :style=\"{'margin-top':isTop+'px'}\">\n\t\t<template v-if=\"!isLocal\">\n\t\t\t<view class=\"uni-data-loading\">\n\t\t\t\t<uni-load-more v-if=\"!mixinDatacomErrorMessage\" status=\"loading\" iconType=\"snow\" :iconSize=\"18\"\n\t\t\t\t\t:content-text=\"contentText\"></uni-load-more>\n\t\t\t\t<text v-else>{{mixinDatacomErrorMessage}}</text>\n\t\t\t</view>\n\t\t</template>\n\t\t<template v-else>\n\t\t\t<checkbox-group v-if=\"multiple\" class=\"checklist-group\" :class=\"{'is-list':mode==='list' || wrap}\"\n\t\t\t\t@change=\"change\">\n\t\t\t\t<label class=\"checklist-box\"\n\t\t\t\t\t:class=\"['is--'+mode,item.selected?'is-checked':'',(disabled || !!item.disabled)?'is-disable':'',index!==0&&mode==='list'?'is-list-border':'']\"\n\t\t\t\t\t:style=\"item.styleBackgroud\" v-for=\"(item,index) in dataList\" :key=\"index\">\n\t\t\t\t\t<checkbox class=\"hidden\" hidden :disabled=\"disabled || !!item.disabled\" :value=\"item[map.value]+''\"\n\t\t\t\t\t\t:checked=\"item.selected\" />\n\t\t\t\t\t<view v-if=\"(mode !=='tag' && mode !== 'list') || ( mode === 'list' && icon === 'left')\"\n\t\t\t\t\t\tclass=\"checkbox__inner\" :style=\"item.styleIcon\">\n\t\t\t\t\t\t<view class=\"checkbox__inner-icon\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"checklist-content\" :class=\"{'list-content':mode === 'list' && icon ==='left'}\">\n\t\t\t\t\t\t<text class=\"checklist-text\" :style=\"item.styleIconText\">{{item[map.text]}}</text>\n\t\t\t\t\t\t<view v-if=\"mode === 'list' && icon === 'right'\" class=\"checkobx__list\" :style=\"item.styleBackgroud\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</label>\n\t\t\t</checkbox-group>\n\t\t\t<radio-group v-else class=\"checklist-group\" :class=\"{'is-list':mode==='list','is-wrap':wrap}\" @change=\"change\">\n\t\t\t\t<label class=\"checklist-box\"\n\t\t\t\t\t:class=\"['is--'+mode,item.selected?'is-checked':'',(disabled || !!item.disabled)?'is-disable':'',index!==0&&mode==='list'?'is-list-border':'']\"\n\t\t\t\t\t:style=\"item.styleBackgroud\" v-for=\"(item,index) in dataList\" :key=\"index\">\n\t\t\t\t\t<radio class=\"hidden\" hidden :disabled=\"disabled || item.disabled\" :value=\"item[map.value]+''\"\n\t\t\t\t\t\t:checked=\"item.selected\" />\n\t\t\t\t\t<view v-if=\"(mode !=='tag' && mode !== 'list') || ( mode === 'list' && icon === 'left')\" class=\"radio__inner\"\n\t\t\t\t\t\t:style=\"item.styleBackgroud\">\n\t\t\t\t\t\t<view class=\"radio__inner-icon\" :style=\"item.styleIcon\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"checklist-content\" :class=\"{'list-content':mode === 'list' && icon ==='left'}\">\n\t\t\t\t\t\t<text class=\"checklist-text\" :style=\"item.styleIconText\">{{item[map.text]}}</text>\n\t\t\t\t\t\t<view v-if=\"mode === 'list' && icon === 'right'\" :style=\"item.styleRightIcon\" class=\"checkobx__list\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</label>\n\t\t\t</radio-group>\n\t\t</template>\n\t</view>\n</template>\n\n<script>\n\t/**\n\t * DataChecklist 数据选择器\n\t * @description 通过数据渲染 checkbox 和 radio\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=xxx\n\t * @property {String} mode = [default| list | button | tag] 显示模式\n\t * @value default  \t默认横排模式\n\t * @value list\t\t列表模式\n\t * @value button\t按钮模式\n\t * @value tag \t\t标签模式\n\t * @property {Boolean} multiple = [true|false] 是否多选\n\t * @property {Array|String|Number} value 默认值\n\t * @property {Array} localdata 本地数据 ，格式 [{text:'',value:''}]\n\t * @property {Number|String} min 最小选择个数 ，multiple为true时生效\n\t * @property {Number|String} max 最大选择个数 ，multiple为true时生效\n\t * @property {Boolean} wrap 是否换行显示\n\t * @property {String} icon = [left|right]  list 列表模式下icon显示位置\n\t * @property {Boolean} selectedColor 选中颜色\n\t * @property {Boolean} emptyText 没有数据时显示的文字 ，本地数据无效\n\t * @property {Boolean} selectedTextColor 选中文本颜色，如不填写则自动显示\n\t * @property {Object} map 字段映射， 默认 map={text:'text',value:'value'}\n\t * @value left 左侧显示\n\t * @value right 右侧显示\n\t * @event {Function} change  选中发生变化触发\n\t */\n\n\texport default {\n\t\tname: 'uniDataChecklist',\n\t\tmixins: [uniCloud.mixinDatacom || {}],\n\t\temits: ['input', 'update:modelValue', 'change'],\n\t\tprops: {\n\t\t\tmode: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'default'\n\t\t\t},\n\n\t\t\tmultiple: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tvalue: {\n\t\t\t\ttype: [Array, String, Number],\n\t\t\t\tdefault () {\n\t\t\t\t\treturn ''\n\t\t\t\t}\n\t\t\t},\n\t\t\t// TODO vue3\n\t\t\tmodelValue: {\n\t\t\t\ttype: [Array, String, Number],\n\t\t\t\tdefault () {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t},\n\t\t\tlocaldata: {\n\t\t\t\ttype: Array,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn []\n\t\t\t\t}\n\t\t\t},\n\t\t\tmin: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tmax: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\twrap: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\ticon: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'left'\n\t\t\t},\n\t\t\tselectedColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tselectedTextColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\temptyText: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '暂无数据'\n\t\t\t},\n\t\t\tdisabled: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tmap: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttext: 'text',\n\t\t\t\t\t\tvalue: 'value'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tlocaldata: {\n\t\t\t\thandler(newVal) {\n\t\t\t\t\tthis.range = newVal\n\t\t\t\t\tthis.dataList = this.getDataList(this.getSelectedValue(newVal))\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\tmixinDatacomResData(newVal) {\n\t\t\t\tthis.range = newVal\n\t\t\t\tthis.dataList = this.getDataList(this.getSelectedValue(newVal))\n\t\t\t},\n\t\t\tvalue(newVal) {\n\t\t\t\tthis.dataList = this.getDataList(newVal)\n\t\t\t\t// fix by mehaotian is_reset 在 uni-forms 中定义\n\t\t\t\t// if(!this.is_reset){\n\t\t\t\t// \tthis.is_reset = false\n\t\t\t\t// \tthis.formItem && this.formItem.setValue(newVal)\n\t\t\t\t// }\n\t\t\t},\n\t\t\tmodelValue(newVal) {\n\t\t\t\tthis.dataList = this.getDataList(newVal);\n\t\t\t\t// if(!this.is_reset){\n\t\t\t\t// \tthis.is_reset = false\n\t\t\t\t// \tthis.formItem && this.formItem.setValue(newVal)\n\t\t\t\t// }\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tdataList: [],\n\t\t\t\trange: [],\n\t\t\t\tcontentText: {\n\t\t\t\t\tcontentdown: '查看更多',\n\t\t\t\t\tcontentrefresh: '加载中',\n\t\t\t\t\tcontentnomore: '没有更多'\n\t\t\t\t},\n\t\t\t\tisLocal: true,\n\t\t\t\tstyles: {\n\t\t\t\t\tselectedColor: '#2979ff',\n\t\t\t\t\tselectedTextColor: '#666',\n\t\t\t\t},\n\t\t\t\tisTop: 0\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\tdataValue() {\n\t\t\t\tif (this.value === '') return this.modelValue\n\t\t\t\tif (this.modelValue === '') return this.value\n\t\t\t\treturn this.value\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// this.form = this.getForm('uniForms')\n\t\t\t// this.formItem = this.getForm('uniFormsItem')\n\t\t\t// this.formItem && this.formItem.setValue(this.value)\n\n\t\t\t// if (this.formItem) {\n\t\t\t// \tthis.isTop = 6\n\t\t\t// \tif (this.formItem.name) {\n\t\t\t// \t\t// 如果存在name添加默认值,否则formData 中不存在这个字段不校验\n\t\t\t// \t\tif(!this.is_reset){\n\t\t\t// \t\t\tthis.is_reset = false\n\t\t\t// \t\t\tthis.formItem.setValue(this.dataValue)\n\t\t\t// \t\t}\n\t\t\t// \t\tthis.rename = this.formItem.name\n\t\t\t// \t\tthis.form.inputChildrens.push(this)\n\t\t\t// \t}\n\t\t\t// }\n\n\t\t\tif (this.localdata && this.localdata.length !== 0) {\n\t\t\t\tthis.isLocal = true\n\t\t\t\tthis.range = this.localdata\n\t\t\t\tthis.dataList = this.getDataList(this.getSelectedValue(this.range))\n\t\t\t} else {\n\t\t\t\tif (this.collection) {\n\t\t\t\t\tthis.isLocal = false\n\t\t\t\t\tthis.loadData()\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tloadData() {\n\t\t\t\tthis.mixinDatacomGet().then(res => {\n\t\t\t\t\tthis.mixinDatacomResData = res.result.data\n\t\t\t\t\tif (this.mixinDatacomResData.length === 0) {\n\t\t\t\t\t\tthis.isLocal = false\n\t\t\t\t\t\tthis.mixinDatacomErrorMessage = this.emptyText\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.isLocal = true\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tthis.mixinDatacomErrorMessage = err.message\n\t\t\t\t})\n\t\t\t},\n\t\t\t/**\n\t\t\t * 获取父元素实例\n\t\t\t */\n\t\t\tgetForm(name = 'uniForms') {\n\t\t\t\tlet parent = this.$parent;\n\t\t\t\tlet parentName = parent.$options.name;\n\t\t\t\twhile (parentName !== name) {\n\t\t\t\t\tparent = parent.$parent;\n\t\t\t\t\tif (!parent) return false\n\t\t\t\t\tparentName = parent.$options.name;\n\t\t\t\t}\n\t\t\t\treturn parent;\n\t\t\t},\n\t\t\tchange(e) {\n\t\t\t\tconst values = e.detail.value\n\n\t\t\t\tlet detail = {\n\t\t\t\t\tvalue: [],\n\t\t\t\t\tdata: []\n\t\t\t\t}\n\n\t\t\t\tif (this.multiple) {\n\t\t\t\t\tthis.range.forEach(item => {\n\n\t\t\t\t\t\tif (values.includes(item[this.map.value] + '')) {\n\t\t\t\t\t\t\tdetail.value.push(item[this.map.value])\n\t\t\t\t\t\t\tdetail.data.push(item)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tconst range = this.range.find(item => (item[this.map.value] + '') === values)\n\t\t\t\t\tif (range) {\n\t\t\t\t\t\tdetail = {\n\t\t\t\t\t\t\tvalue: range[this.map.value],\n\t\t\t\t\t\t\tdata: range\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// this.formItem && this.formItem.setValue(detail.value)\n\t\t\t\t// TODO 兼容 vue2\n\t\t\t\tthis.$emit('input', detail.value);\n\t\t\t\t// // TOTO 兼容 vue3\n\t\t\t\tthis.$emit('update:modelValue', detail.value);\n\t\t\t\tthis.$emit('change', {\n\t\t\t\t\tdetail\n\t\t\t\t})\n\t\t\t\tif (this.multiple) {\n\t\t\t\t\t// 如果 v-model 没有绑定 ，则走内部逻辑\n\t\t\t\t\t// if (this.value.length === 0) {\n\t\t\t\t\tthis.dataList = this.getDataList(detail.value, true)\n\t\t\t\t\t// }\n\t\t\t\t} else {\n\t\t\t\t\tthis.dataList = this.getDataList(detail.value)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取渲染的新数组\n\t\t\t * @param {Object} value 选中内容\n\t\t\t */\n\t\t\tgetDataList(value) {\n\t\t\t\t// 解除引用关系，破坏原引用关系，避免污染源数据\n\t\t\t\tlet dataList = JSON.parse(JSON.stringify(this.range))\n\t\t\t\tlet list = []\n\t\t\t\tif (this.multiple) {\n\t\t\t\t\tif (!Array.isArray(value)) {\n\t\t\t\t\t\tvalue = []\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (Array.isArray(value) && value.length) {\n\t\t\t\t\t\tvalue = value[0]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tdataList.forEach((item, index) => {\n\t\t\t\t\titem.disabled = item.disable || item.disabled || false\n\t\t\t\t\tif (this.multiple) {\n\t\t\t\t\t\tif (value.length > 0) {\n\t\t\t\t\t\t\tlet have = value.find(val => val === item[this.map.value])\n\t\t\t\t\t\t\titem.selected = have !== undefined\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\titem.selected = false\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\titem.selected = value === item[this.map.value]\n\t\t\t\t\t}\n\n\t\t\t\t\tlist.push(item)\n\t\t\t\t})\n\t\t\t\treturn this.setRange(list)\n\t\t\t},\n\t\t\t/**\n\t\t\t * 处理最大最小值\n\t\t\t * @param {Object} list\n\t\t\t */\n\t\t\tsetRange(list) {\n\t\t\t\tlet selectList = list.filter(item => item.selected)\n\t\t\t\tlet min = Number(this.min) || 0\n\t\t\t\tlet max = Number(this.max) || ''\n\t\t\t\tlist.forEach((item, index) => {\n\t\t\t\t\tif (this.multiple) {\n\t\t\t\t\t\tif (selectList.length <= min) {\n\t\t\t\t\t\t\tlet have = selectList.find(val => val[this.map.value] === item[this.map.value])\n\t\t\t\t\t\t\tif (have !== undefined) {\n\t\t\t\t\t\t\t\titem.disabled = true\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (selectList.length >= max && max !== '') {\n\t\t\t\t\t\t\tlet have = selectList.find(val => val[this.map.value] === item[this.map.value])\n\t\t\t\t\t\t\tif (have === undefined) {\n\t\t\t\t\t\t\t\titem.disabled = true\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis.setStyles(item, index)\n\t\t\t\t\tlist[index] = item\n\t\t\t\t})\n\t\t\t\treturn list\n\t\t\t},\n\t\t\t/**\n\t\t\t * 设置 class\n\t\t\t * @param {Object} item\n\t\t\t * @param {Object} index\n\t\t\t */\n\t\t\tsetStyles(item, index) {\n\t\t\t\t//  设置自定义样式\n\t\t\t\titem.styleBackgroud = this.setStyleBackgroud(item)\n\t\t\t\titem.styleIcon = this.setStyleIcon(item)\n\t\t\t\titem.styleIconText = this.setStyleIconText(item)\n\t\t\t\titem.styleRightIcon = this.setStyleRightIcon(item)\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取选中值\n\t\t\t * @param {Object} range\n\t\t\t */\n\t\t\tgetSelectedValue(range) {\n\t\t\t\tif (!this.multiple) return this.dataValue\n\t\t\t\tlet selectedArr = []\n\t\t\t\trange.forEach((item) => {\n\t\t\t\t\tif (item.selected) {\n\t\t\t\t\t\tselectedArr.push(item[this.map.value])\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\treturn this.dataValue.length > 0 ? this.dataValue : selectedArr\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 设置背景样式\n\t\t\t */\n\t\t\tsetStyleBackgroud(item) {\n\t\t\t\tlet styles = {}\n\t\t\t\tlet selectedColor = this.selectedColor ? this.selectedColor : '#2979ff'\n\t\t\t\tif (this.selectedColor) {\n\t\t\t\t\tif (this.mode !== 'list') {\n\t\t\t\t\t\tstyles['border-color'] = item.selected ? selectedColor : '#DCDFE6'\n\t\t\t\t\t}\n\t\t\t\t\tif (this.mode === 'tag') {\n\t\t\t\t\t\tstyles['background-color'] = item.selected ? selectedColor : '#f5f5f5'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlet classles = ''\n\t\t\t\tfor (let i in styles) {\n\t\t\t\t\tclassles += `${i}:${styles[i]};`\n\t\t\t\t}\n\t\t\t\treturn classles\n\t\t\t},\n\t\t\tsetStyleIcon(item) {\n\t\t\t\tlet styles = {}\n\t\t\t\tlet classles = ''\n\t\t\t\tif (this.selectedColor) {\n\t\t\t\t\tlet selectedColor = this.selectedColor ? this.selectedColor : '#2979ff'\n\t\t\t\t\tstyles['background-color'] = item.selected ? selectedColor : '#fff'\n\t\t\t\t\tstyles['border-color'] = item.selected ? selectedColor : '#DCDFE6'\n\n\t\t\t\t\tif (!item.selected && item.disabled) {\n\t\t\t\t\t\tstyles['background-color'] = '#F2F6FC'\n\t\t\t\t\t\tstyles['border-color'] = item.selected ? selectedColor : '#DCDFE6'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor (let i in styles) {\n\t\t\t\t\tclassles += `${i}:${styles[i]};`\n\t\t\t\t}\n\t\t\t\treturn classles\n\t\t\t},\n\t\t\tsetStyleIconText(item) {\n\t\t\t\tlet styles = {}\n\t\t\t\tlet classles = ''\n\t\t\t\tif (this.selectedColor) {\n\t\t\t\t\tlet selectedColor = this.selectedColor ? this.selectedColor : '#2979ff'\n\t\t\t\t\tif (this.mode === 'tag') {\n\t\t\t\t\t\tstyles.color = item.selected ? (this.selectedTextColor ? this.selectedTextColor : '#fff') : '#666'\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstyles.color = item.selected ? (this.selectedTextColor ? this.selectedTextColor : selectedColor) : '#666'\n\t\t\t\t\t}\n\t\t\t\t\tif (!item.selected && item.disabled) {\n\t\t\t\t\t\tstyles.color = '#999'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor (let i in styles) {\n\t\t\t\t\tclassles += `${i}:${styles[i]};`\n\t\t\t\t}\n\t\t\t\treturn classles\n\t\t\t},\n\t\t\tsetStyleRightIcon(item) {\n\t\t\t\tlet styles = {}\n\t\t\t\tlet classles = ''\n\t\t\t\tif (this.mode === 'list') {\n\t\t\t\t\tstyles['border-color'] = item.selected ? this.styles.selectedColor : '#DCDFE6'\n\t\t\t\t}\n\t\t\t\tfor (let i in styles) {\n\t\t\t\t\tclassles += `${i}:${styles[i]};`\n\t\t\t\t}\n\n\t\t\t\treturn classles\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t$uni-primary: #2979ff !default;\n\t$border-color: #DCDFE6;\n\t$disable: 0.4;\n\n\t@mixin flex {\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t}\n\n\t.uni-data-loading {\n\t\t@include flex;\n\t\tflex-direction: row;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 36px;\n\t\tpadding-left: 10px;\n\t\tcolor: #999;\n\t}\n\n\t.uni-data-checklist {\n\t\tposition: relative;\n\t\tz-index: 0;\n\t\tflex: 1;\n\n\t\t// 多选样式\n\t\t.checklist-group {\n\t\t\t@include flex;\n\t\t\tflex-direction: row;\n\t\t\tflex-wrap: wrap;\n\n\t\t\t&.is-list {\n\t\t\t\tflex-direction: column;\n\t\t\t}\n\n\t\t\t.checklist-box {\n\t\t\t\t@include flex;\n\t\t\t\tflex-direction: row;\n\t\t\t\talign-items: center;\n\t\t\t\tposition: relative;\n\t\t\t\tmargin: 5px 0;\n\t\t\t\tmargin-right: 25px;\n\n\t\t\t\t.hidden {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\topacity: 0;\n\t\t\t\t}\n\n\t\t\t\t// 文字样式\n\t\t\t\t.checklist-content {\n\t\t\t\t\t@include flex;\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tflex-direction: row;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: space-between;\n\n\t\t\t\t\t.checklist-text {\n\t\t\t\t\t\tfont-size: 14px;\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\tmargin-left: 5px;\n\t\t\t\t\t\tline-height: 14px;\n\t\t\t\t\t}\n\n\t\t\t\t\t.checkobx__list {\n\t\t\t\t\t\tborder-right-width: 1px;\n\t\t\t\t\t\tborder-right-color: #007aff;\n\t\t\t\t\t\tborder-right-style: solid;\n\t\t\t\t\t\tborder-bottom-width: 1px;\n\t\t\t\t\t\tborder-bottom-color: #007aff;\n\t\t\t\t\t\tborder-bottom-style: solid;\n\t\t\t\t\t\theight: 12px;\n\t\t\t\t\t\twidth: 6px;\n\t\t\t\t\t\tleft: -5px;\n\t\t\t\t\t\ttransform-origin: center;\n\t\t\t\t\t\ttransform: rotate(45deg);\n\t\t\t\t\t\topacity: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 多选样式\n\t\t\t\t.checkbox__inner {\n\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\t/* #endif */\n\t\t\t\t\tposition: relative;\n\t\t\t\t\twidth: 16px;\n\t\t\t\t\theight: 16px;\n\t\t\t\t\tborder: 1px solid $border-color;\n\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t\tz-index: 1;\n\n\t\t\t\t\t.checkbox__inner-icon {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t/* #ifdef APP-NVUE */\n\t\t\t\t\t\ttop: 2px;\n\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\t\ttop: 1px;\n\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t\tleft: 5px;\n\t\t\t\t\t\theight: 8px;\n\t\t\t\t\t\twidth: 4px;\n\t\t\t\t\t\tborder-right-width: 1px;\n\t\t\t\t\t\tborder-right-color: #fff;\n\t\t\t\t\t\tborder-right-style: solid;\n\t\t\t\t\t\tborder-bottom-width: 1px;\n\t\t\t\t\t\tborder-bottom-color: #fff;\n\t\t\t\t\t\tborder-bottom-style: solid;\n\t\t\t\t\t\topacity: 0;\n\t\t\t\t\t\ttransform-origin: center;\n\t\t\t\t\t\ttransform: rotate(40deg);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 单选样式\n\t\t\t\t.radio__inner {\n\t\t\t\t\t@include flex;\n\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\t/* #endif */\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\twidth: 16px;\n\t\t\t\t\theight: 16px;\n\t\t\t\t\tborder: 1px solid $border-color;\n\t\t\t\t\tborder-radius: 16px;\n\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t\tz-index: 1;\n\n\t\t\t\t\t.radio__inner-icon {\n\t\t\t\t\t\twidth: 8px;\n\t\t\t\t\t\theight: 8px;\n\t\t\t\t\t\tborder-radius: 10px;\n\t\t\t\t\t\topacity: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 默认样式\n\t\t\t\t&.is--default {\n\n\t\t\t\t\t// 禁用\n\t\t\t\t\t&.is-disable {\n\t\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\t\tcursor: not-allowed;\n\n\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t\t.checkbox__inner {\n\t\t\t\t\t\t\tbackground-color: #F2F6FC;\n\t\t\t\t\t\t\tborder-color: $border-color;\n\t\t\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.radio__inner {\n\t\t\t\t\t\t\tbackground-color: #F2F6FC;\n\t\t\t\t\t\t\tborder-color: $border-color;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.checklist-text {\n\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// 选中\n\t\t\t\t\t&.is-checked {\n\t\t\t\t\t\t.checkbox__inner {\n\t\t\t\t\t\t\tborder-color: $uni-primary;\n\t\t\t\t\t\t\tbackground-color: $uni-primary;\n\n\t\t\t\t\t\t\t.checkbox__inner-icon {\n\t\t\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t\t\ttransform: rotate(45deg);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.radio__inner {\n\t\t\t\t\t\t\tborder-color: $uni-primary;\n\n\t\t\t\t\t\t\t.radio__inner-icon {\n\t\t\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t\t\tbackground-color: $uni-primary;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.checklist-text {\n\t\t\t\t\t\t\tcolor: $uni-primary;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 选中禁用\n\t\t\t\t\t\t&.is-disable {\n\t\t\t\t\t\t\t.checkbox__inner {\n\t\t\t\t\t\t\t\topacity: $disable;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.checklist-text {\n\t\t\t\t\t\t\t\topacity: $disable;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.radio__inner {\n\t\t\t\t\t\t\t\topacity: $disable;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 按钮样式\n\t\t\t\t&.is--button {\n\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\tpadding: 5px 10px;\n\t\t\t\t\tborder: 1px $border-color solid;\n\t\t\t\t\tborder-radius: 3px;\n\t\t\t\t\ttransition: border-color 0.2s;\n\n\t\t\t\t\t// 禁用\n\t\t\t\t\t&.is-disable {\n\t\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t\tborder: 1px #eee solid;\n\t\t\t\t\t\topacity: $disable;\n\n\t\t\t\t\t\t.checkbox__inner {\n\t\t\t\t\t\t\tbackground-color: #F2F6FC;\n\t\t\t\t\t\t\tborder-color: $border-color;\n\t\t\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.radio__inner {\n\t\t\t\t\t\t\tbackground-color: #F2F6FC;\n\t\t\t\t\t\t\tborder-color: $border-color;\n\t\t\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.checklist-text {\n\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&.is-checked {\n\t\t\t\t\t\tborder-color: $uni-primary;\n\n\t\t\t\t\t\t.checkbox__inner {\n\t\t\t\t\t\t\tborder-color: $uni-primary;\n\t\t\t\t\t\t\tbackground-color: $uni-primary;\n\n\t\t\t\t\t\t\t.checkbox__inner-icon {\n\t\t\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t\t\ttransform: rotate(45deg);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.radio__inner {\n\t\t\t\t\t\t\tborder-color: $uni-primary;\n\n\t\t\t\t\t\t\t.radio__inner-icon {\n\t\t\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t\t\tbackground-color: $uni-primary;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.checklist-text {\n\t\t\t\t\t\t\tcolor: $uni-primary;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 选中禁用\n\t\t\t\t\t\t&.is-disable {\n\t\t\t\t\t\t\topacity: $disable;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 标签样式\n\t\t\t\t&.is--tag {\n\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\tpadding: 5px 10px;\n\t\t\t\t\tborder: 1px $border-color solid;\n\t\t\t\t\tborder-radius: 3px;\n\t\t\t\t\tbackground-color: #f5f5f5;\n\n\t\t\t\t\t.checklist-text {\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 禁用\n\t\t\t\t\t&.is-disable {\n\t\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t\topacity: $disable;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.is-checked {\n\t\t\t\t\t\tbackground-color: $uni-primary;\n\t\t\t\t\t\tborder-color: $uni-primary;\n\n\t\t\t\t\t\t.checklist-text {\n\t\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 列表样式\n\t\t\t\t&.is--list {\n\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t/* #endif */\n\t\t\t\t\tpadding: 10px 15px;\n\t\t\t\t\tpadding-left: 0;\n\t\t\t\t\tmargin: 0;\n\n\t\t\t\t\t&.is-list-border {\n\t\t\t\t\t\tborder-top: 1px #eee solid;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 禁用\n\t\t\t\t\t&.is-disable {\n\t\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\t\tcursor: not-allowed;\n\n\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t\t.checkbox__inner {\n\t\t\t\t\t\t\tbackground-color: #F2F6FC;\n\t\t\t\t\t\t\tborder-color: $border-color;\n\t\t\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.checklist-text {\n\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&.is-checked {\n\t\t\t\t\t\t.checkbox__inner {\n\t\t\t\t\t\t\tborder-color: $uni-primary;\n\t\t\t\t\t\t\tbackground-color: $uni-primary;\n\n\t\t\t\t\t\t\t.checkbox__inner-icon {\n\t\t\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t\t\ttransform: rotate(45deg);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.radio__inner {\n\t\t\t\t\t\t\tborder-color: $uni-primary;\n\t\t\t\t\t\t\t.radio__inner-icon {\n\t\t\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t\t\tbackground-color: $uni-primary;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.checklist-text {\n\t\t\t\t\t\t\tcolor: $uni-primary;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.checklist-content {\n\t\t\t\t\t\t\t.checkobx__list {\n\t\t\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t\t\tborder-color: $uni-primary;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 选中禁用\n\t\t\t\t\t\t&.is-disable {\n\t\t\t\t\t\t\t.checkbox__inner {\n\t\t\t\t\t\t\t\topacity: $disable;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.checklist-text {\n\t\t\t\t\t\t\t\topacity: $disable;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-data-checkbox.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-data-checkbox.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558448905\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol_pkg/shift/edit"],{

/***/ 387:
/*!********************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fpatrol_pkg%2Fshift%2Fedit"} ***!
  \********************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _edit = _interopRequireDefault(__webpack_require__(/*! ./pages/patrol_pkg/shift/edit.vue */ 388));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_edit.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 388:
/*!***********************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/shift/edit.vue ***!
  \***********************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _edit_vue_vue_type_template_id_1f5c9678___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edit.vue?vue&type=template&id=1f5c9678& */ 389);
/* harmony import */ var _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./edit.vue?vue&type=script&lang=js& */ 391);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./edit.vue?vue&type=style&index=0&lang=scss& */ 393);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _edit_vue_vue_type_template_id_1f5c9678___WEBPACK_IMPORTED_MODULE_0__["render"],
  _edit_vue_vue_type_template_id_1f5c9678___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _edit_vue_vue_type_template_id_1f5c9678___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/patrol_pkg/shift/edit.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 389:
/*!******************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/shift/edit.vue?vue&type=template&id=1f5c9678& ***!
  \******************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_1f5c9678___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=1f5c9678& */ 390);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_1f5c9678___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_1f5c9678___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_1f5c9678___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_1f5c9678___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 390:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/shift/edit.vue?vue&type=template&id=1f5c9678& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniForms: function () {
      return Promise.all(/*! import() | uni_modules/uni-forms/components/uni-forms/uni-forms */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-forms/components/uni-forms/uni-forms")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-forms/components/uni-forms/uni-forms.vue */ 611))
    },
    uniFormsItem: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-forms/components/uni-forms-item/uni-forms-item */ "uni_modules/uni-forms/components/uni-forms-item/uni-forms-item").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue */ 620))
    },
    uniEasyinput: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput */ "uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue */ 551))
    },
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 490))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.formData.rounds.length || 0
  var g1 = _vm.formData.rounds.length
  if (!_vm._isMounted) {
    _vm.e0 = function (e) {
      return (_vm.formData.status = e.detail.value ? 1 : 0)
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 391:
/*!************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/shift/edit.vue?vue&type=script&lang=js& ***!
  \************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js& */ 392);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 392:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/shift/edit.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! @/utils/patrol-api.js */ 71));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      shift_id: '',
      // 班次ID，用于编辑
      formData: {
        name: '',
        start_time: '08:30',
        end_time: '17:00',
        is_cross_day: false,
        status: 1,
        rounds: []
      },
      rules: {
        name: {
          rules: [{
            required: true,
            errorMessage: '请输入班次名称'
          }]
        },
        start_time: {
          rules: [{
            required: true,
            errorMessage: '请选择开始时间'
          }]
        },
        end_time: {
          rules: [{
            required: true,
            errorMessage: '请选择结束时间'
          }]
        }
      },
      // 默认的轮次对象
      currentRound: {
        round: 1,
        name: '',
        time: '08:30',
        day_offset: 0,
        duration: 60,
        // 默认60分钟有效时长
        status: 1
      },
      // 天数偏移选项
      dayOptions: ['当天', '次日'],
      isEditingRound: false,
      editingRoundIndex: null
    };
  },
  onLoad: function onLoad(options) {
    // 获取班次ID
    if (options.id) {
      this.shift_id = options.id;
      // 加载班次详情
      this.getShiftDetail();
    } else {
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(function () {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 开始时间变化
    onStartTimeChange: function onStartTimeChange(e) {
      this.formData.start_time = e.detail.value;

      // 如果是跨天班次，重新计算所有轮次的日期偏移
      if (this.formData.is_cross_day) {
        this.recalculateAllRoundOffsets();
      }
    },
    // 结束时间变化
    onEndTimeChange: function onEndTimeChange(e) {
      this.formData.end_time = e.detail.value;
    },
    // 获取班次详情
    getShiftDetail: function getShiftDetail() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var res, data;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                uni.showLoading({
                  title: '加载中...'
                });
                _context.prev = 1;
                _context.next = 4;
                return _patrolApi.default.getShiftDetail(_this.shift_id);
              case 4:
                res = _context.sent;
                if (res.code === 0 && res.data) {
                  // 支持处理两种字段格式，兼容驼峰和下划线格式
                  data = res.data; // 更新表单数据
                  _this.formData = {
                    name: data.name || '',
                    // 优先使用下划线格式，如不存在则尝试使用驼峰格式
                    start_time: data.start_time || data.startTime || '08:30',
                    end_time: data.end_time || data.endTime || '17:00',
                    is_cross_day: data.is_cross_day !== undefined ? data.is_cross_day : data.isCrossDay || false,
                    status: data.status !== undefined ? data.status : 1,
                    rounds: []
                  };

                  // 处理轮次数据，确保有所有必要字段
                  if (data.rounds && Array.isArray(data.rounds)) {
                    _this.formData.rounds = data.rounds.map(function (round) {
                      return {
                        round: round.round || 1,
                        name: round.name || "\u8F6E\u6B21".concat(round.round || 1),
                        time: round.time || '08:30',
                        day_offset: round.day_offset !== undefined ? round.day_offset : 0,
                        duration: round.duration || 60,
                        status: round.status !== undefined ? round.status : 1
                      };
                    });

                    // 确保轮次按照序号排序
                    _this.formData.rounds.sort(function (a, b) {
                      return a.round - b.round;
                    });
                  }

                  // 如果没有轮次，添加默认轮次
                  if (_this.formData.rounds.length === 0) {
                    _this.addRound();
                  }
                } else {
                  uni.showToast({
                    title: res.message || '获取班次详情失败',
                    icon: 'none'
                  });
                  setTimeout(function () {
                    uni.navigateBack();
                  }, 1500);
                }
                _context.next = 13;
                break;
              case 8:
                _context.prev = 8;
                _context.t0 = _context["catch"](1);
                console.error('获取班次详情错误', _context.t0);
                uni.showToast({
                  title: '获取班次详情出错',
                  icon: 'none'
                });
                setTimeout(function () {
                  uni.navigateBack();
                }, 1500);
              case 13:
                _context.prev = 13;
                uni.hideLoading();
                return _context.finish(13);
              case 16:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[1, 8, 13, 16]]);
      }))();
    },
    // 添加轮次
    addRound: function addRound() {
      this.isEditingRound = false;

      // 获取合理的默认时间
      var suggestedTime = this.formData.start_time;
      if (this.formData.rounds.length > 0) {
        // 基于最后一个轮次的时间，加上合理间隔
        var lastRound = this.formData.rounds[this.formData.rounds.length - 1];
        var _lastRound$time$split = lastRound.time.split(':').map(Number),
          _lastRound$time$split2 = (0, _slicedToArray2.default)(_lastRound$time$split, 2),
          lastHour = _lastRound$time$split2[0],
          lastMinute = _lastRound$time$split2[1];

        // 假设间隔为2小时
        var newHour = lastHour + 2;
        var newMinute = lastMinute;

        // 处理24小时制
        if (newHour >= 24) {
          newHour = newHour - 24;
        }
        suggestedTime = "".concat(newHour.toString().padStart(2, '0'), ":").concat(newMinute.toString().padStart(2, '0'));
      }
      this.currentRound = {
        round: this.formData.rounds.length + 1,
        name: "\u8F6E\u6B21".concat(this.formData.rounds.length + 1),
        time: suggestedTime,
        day_offset: 0,
        // 初始化为当天，将在下一步自动计算
        duration: 60,
        // 默认60分钟
        status: 1
      };

      // 如果是跨天班次，自动计算day_offset
      if (this.formData.is_cross_day) {
        this.calculateRoundDayOffset(this.currentRound);
      }
      this.$refs.roundPopup.open();
    },
    // 删除轮次
    removeRound: function removeRound(index) {
      this.formData.rounds.splice(index, 1);

      // 重新排序轮次编号
      this.formData.rounds.forEach(function (round, idx) {
        round.round = idx + 1;
      });
    },
    // 验证轮次时限的方法
    validateRoundDuration: function validateRoundDuration(round) {
      if (!round.time || !round.duration) return true;

      // 获取轮次开始时间
      var _round$time$split$map = round.time.split(':').map(Number),
        _round$time$split$map2 = (0, _slicedToArray2.default)(_round$time$split$map, 2),
        roundHour = _round$time$split$map2[0],
        roundMinute = _round$time$split$map2[1];
      var roundStartMinutes = roundHour * 60 + roundMinute;

      // 获取班次时间
      var _this$formData$start_ = this.formData.start_time.split(':').map(Number),
        _this$formData$start_2 = (0, _slicedToArray2.default)(_this$formData$start_, 2),
        startHour = _this$formData$start_2[0],
        startMinute = _this$formData$start_2[1];
      var _this$formData$end_ti = this.formData.end_time.split(':').map(Number),
        _this$formData$end_ti2 = (0, _slicedToArray2.default)(_this$formData$end_ti, 2),
        endHour = _this$formData$end_ti2[0],
        endMinute = _this$formData$end_ti2[1];
      var shiftStartMinutes = startHour * 60 + startMinute;
      var shiftEndMinutes = endHour * 60 + endMinute;

      // 计算轮次结束时间
      var roundEndMinutes = roundStartMinutes + parseInt(round.duration);

      // 检查轮次是否跨天
      var isRoundCrossDay = roundEndMinutes >= 24 * 60;
      var adjustedRoundEndMinutes = isRoundCrossDay ? roundEndMinutes - 24 * 60 : roundEndMinutes;

      // 是否班次本身跨天
      var isShiftCrossDay = this.formData.is_cross_day;

      // 检查轮次时间是否有效
      var isTimeInvalid = false;
      var timeErrorMessage = '';
      if (isShiftCrossDay) {
        // 班次跨天的情况
        if (round.day_offset === 0) {
          // 当天轮次
          if (roundStartMinutes < shiftStartMinutes) {
            var diffMinutes = shiftStartMinutes - roundStartMinutes;
            var diffHours = Math.floor(diffMinutes / 60);
            var diffMins = diffMinutes % 60;
            timeErrorMessage = "\u7B2C".concat(round.round, "\u8F6E\u5F00\u59CB\u65F6\u95F4").concat(round.time, "\u65E9\u4E8E\u73ED\u6B21\u5F00\u59CB\u65F6\u95F4").concat(this.formData.start_time, "\uFF0C");
            timeErrorMessage += "\u63D0\u524D\u4E86".concat(diffHours, "\u5C0F\u65F6").concat(diffMins, "\u5206\u949F");
            isTimeInvalid = true;
          }
        } else {
          // 次日轮次
          if (roundStartMinutes > shiftEndMinutes && roundStartMinutes < shiftStartMinutes) {
            var diffMinutes1 = roundStartMinutes - shiftEndMinutes;
            var diffHours1 = Math.floor(diffMinutes1 / 60);
            var diffMins1 = diffMinutes1 % 60;
            var diffMinutes2 = shiftStartMinutes - roundStartMinutes;
            var diffHours2 = Math.floor(diffMinutes2 / 60);
            var diffMins2 = diffMinutes2 % 60;
            timeErrorMessage = "\u7B2C".concat(round.round, "\u8F6E(\u6B21\u65E5)\u5F00\u59CB\u65F6\u95F4").concat(round.time, "\u5728\u73ED\u6B21\u4E0B\u73ED\u65F6\u95F4").concat(this.formData.end_time, "\u4E4B\u540E");
            timeErrorMessage += "".concat(diffHours1, "\u5C0F\u65F6").concat(diffMins1, "\u5206\u949F\uFF0C\u4E14\u5728\u73ED\u6B21\u4E0A\u73ED\u65F6\u95F4").concat(this.formData.start_time, "\u4E4B\u524D");
            timeErrorMessage += "".concat(diffHours2, "\u5C0F\u65F6").concat(diffMins2, "\u5206\u949F\uFF0C\u4E0D\u5728\u73ED\u6B21\u65F6\u95F4\u8303\u56F4\u5185");
            isTimeInvalid = true;
          }
        }
      } else {
        // 班次不跨天的情况
        if (roundStartMinutes < shiftStartMinutes) {
          var _diffMinutes = shiftStartMinutes - roundStartMinutes;
          var _diffHours = Math.floor(_diffMinutes / 60);
          var _diffMins = _diffMinutes % 60;
          timeErrorMessage = "\u7B2C".concat(round.round, "\u8F6E\u5F00\u59CB\u65F6\u95F4\u65E9\u4E8E\u73ED\u6B21\u5F00\u59CB\u65F6\u95F4\uFF0C");
          timeErrorMessage += "\u63D0\u524D\u4E86".concat(_diffHours, "\u5C0F\u65F6").concat(_diffMins, "\u5206\u949F");
          isTimeInvalid = true;
        }
      }
      if (isTimeInvalid) {
        uni.showModal({
          title: '轮次时间错误提示',
          content: timeErrorMessage,
          showCancel: true,
          confirmText: '我知道了',
          success: function success(res) {
            if (res.confirm) {
              // 用户确认了解
            }
          }
        });
        return false;
      }

      // 检查轮次结束时间是否超出范围
      var isEndTimeInvalid = false;
      var endTimeErrorMessage = '';
      if (isShiftCrossDay) {
        // 班次跨天的情况
        if (round.day_offset === 0) {
          // 当天轮次
          if (roundEndMinutes > shiftEndMinutes + 24 * 60) {
            var overMinutes = roundEndMinutes - (shiftEndMinutes + 24 * 60);
            var overHours = Math.floor(overMinutes / 60);
            var overMins = overMinutes % 60;
            isEndTimeInvalid = true;
            endTimeErrorMessage = "\u8D85\u51FA\u6B21\u65E5\u4E0B\u73ED\u65F6\u95F4".concat(overHours, "\u5C0F\u65F6").concat(overMins, "\u5206\u949F");
          }
        } else {
          // 次日轮次
          if (adjustedRoundEndMinutes > shiftEndMinutes) {
            var _overMinutes = adjustedRoundEndMinutes - shiftEndMinutes;
            var _overHours = Math.floor(_overMinutes / 60);
            var _overMins = _overMinutes % 60;
            isEndTimeInvalid = true;
            endTimeErrorMessage = "\u8D85\u51FA\u6B21\u65E5\u4E0B\u73ED\u65F6\u95F4".concat(_overHours, "\u5C0F\u65F6").concat(_overMins, "\u5206\u949F");
          }
        }
      } else {
        // 班次不跨天的情况
        if (adjustedRoundEndMinutes > shiftEndMinutes) {
          var _overMinutes2 = adjustedRoundEndMinutes - shiftEndMinutes;
          var _overHours2 = Math.floor(_overMinutes2 / 60);
          var _overMins2 = _overMinutes2 % 60;
          isEndTimeInvalid = true;
          endTimeErrorMessage = "\u8D85\u51FA\u4E0B\u73ED\u65F6\u95F4".concat(_overHours2, "\u5C0F\u65F6").concat(_overMins2, "\u5206\u949F");
        }
      }
      if (isEndTimeInvalid) {
        // 计算轮次结束时间的小时和分钟
        var endTimeHour = Math.floor(adjustedRoundEndMinutes / 60);
        var endTimeMin = adjustedRoundEndMinutes % 60;
        var formattedEndTime = "".concat(endTimeHour.toString().padStart(2, '0'), ":").concat(endTimeMin.toString().padStart(2, '0'));
        var message = "\u7B2C".concat(round.round, "\u8F6E\u5C06\u5728").concat(round.time).concat(round.day_offset === 1 ? '(次日)' : '', "\u5F00\u59CB\uFF0C\u6301\u7EED").concat(round.duration, "\u5206\u949F\uFF0C");
        message += "\u5C06\u5728".concat(formattedEndTime).concat(isRoundCrossDay ? '(次日)' : '', "\u7ED3\u675F\uFF0C");
        message += endTimeErrorMessage;
        uni.showModal({
          title: '轮次时限超出提示',
          content: message,
          showCancel: true,
          confirmText: '我知道了',
          success: function success(res) {
            if (res.confirm) {
              // 用户确认了解
            }
          }
        });
        return false;
      }

      // 显示轮次跨天提示（仅首次显示）
      if (isRoundCrossDay && !round.crossDayNotified) {
        round.crossDayNotified = true;
        var endTimeStr = "".concat(Math.floor(adjustedRoundEndMinutes / 60).toString().padStart(2, '0'), ":").concat((adjustedRoundEndMinutes % 60).toString().padStart(2, '0'));
        uni.showModal({
          title: '轮次跨天提示',
          content: "\u7B2C".concat(round.round, "\u8F6E\u5C06\u4ECE").concat(round.time, "\u5F00\u59CB\uFF0C\u6301\u7EED").concat(round.duration, "\u5206\u949F\uFF0C\u5C06\u5728\u6B21\u65E5").concat(endTimeStr, "\u7ED3\u675F"),
          showCancel: false,
          confirmText: '我知道了'
        });
      }
      return true;
    },
    // 修改轮次时间变化的处理方法
    onRoundTimeChange: function onRoundTimeChange(e) {
      this.currentRound.time = e.detail.value;

      // 如果班次设为跨天，则自动计算该轮次是否应为次日
      if (this.formData.is_cross_day) {
        this.calculateRoundDayOffset(this.currentRound);
      } else {
        this.currentRound.day_offset = 0;
      }

      // 验证时限
      if (this.currentRound.duration) {
        this.validateRoundDuration(this.currentRound);
      }
    },
    // 添加duration变化的监听
    onDurationChange: function onDurationChange(e) {
      var newDuration = parseInt(e.detail.value);
      this.currentRound.duration = newDuration;
      if (this.currentRound.time) {
        this.validateRoundDuration(this.currentRound);
      }
    },
    // 轮次日期变化（保留但不再主动调用）
    onRoundDayChange: function onRoundDayChange(e) {
      this.currentRound.day_offset = parseInt(e.detail.value);
    },
    // 计算一个轮次的day_offset
    calculateRoundDayOffset: function calculateRoundDayOffset(round) {
      if (!this.formData.is_cross_day) {
        round.day_offset = 0;
        return;
      }

      // 获取班次开始时间和结束时间
      var _this$formData$start_3 = this.formData.start_time.split(':').map(Number),
        _this$formData$start_4 = (0, _slicedToArray2.default)(_this$formData$start_3, 2),
        startHour = _this$formData$start_4[0],
        startMinute = _this$formData$start_4[1];
      var _this$formData$end_ti3 = this.formData.end_time.split(':').map(Number),
        _this$formData$end_ti4 = (0, _slicedToArray2.default)(_this$formData$end_ti3, 2),
        endHour = _this$formData$end_ti4[0],
        endMinute = _this$formData$end_ti4[1];
      var startTimeMinutes = startHour * 60 + startMinute;
      var endTimeMinutes = endHour * 60 + endMinute;

      // 获取轮次时间
      var _round$time$split$map3 = round.time.split(':').map(Number),
        _round$time$split$map4 = (0, _slicedToArray2.default)(_round$time$split$map3, 2),
        roundHour = _round$time$split$map4[0],
        roundMinute = _round$time$split$map4[1];
      var roundTimeMinutes = roundHour * 60 + roundMinute;

      // 计算轮次结束时间（考虑持续时间）
      var roundEndMinutes = roundTimeMinutes + (parseInt(round.duration) || 60);

      // 判断班次是否跨天（结束时间小于开始时间）
      var isShiftCrossDay = endTimeMinutes < startTimeMinutes;
      if (isShiftCrossDay) {
        // 班次跨天的情况
        if (roundTimeMinutes <= endTimeMinutes || roundTimeMinutes >= startTimeMinutes) {
          // 轮次开始时间在有效范围内
          round.day_offset = roundTimeMinutes <= endTimeMinutes ? 1 : 0;
        } else {
          round.day_offset = 0;
        }
      } else {
        // 班次不跨天，但需要检查轮次是否因持续时间而跨天
        if (roundEndMinutes > 24 * 60) {
          // 轮次结束时间超过当天24:00，标记为跨天
          round.day_offset = 1;
        } else {
          round.day_offset = 0;
        }
      }
    },
    // 重新计算所有轮次的日期偏移
    recalculateAllRoundOffsets: function recalculateAllRoundOffsets() {
      var _this2 = this;
      if (!this.formData.rounds || !this.formData.rounds.length) return;

      // 如果班次不跨天，所有轮次都设为当天
      if (!this.formData.is_cross_day) {
        this.formData.rounds.forEach(function (round) {
          round.day_offset = 0;
        });
        return;
      }

      // 处理每个轮次
      this.formData.rounds.forEach(function (round) {
        _this2.calculateRoundDayOffset(round);
      });
    },
    // 修改编辑轮次方法
    editRound: function editRound(index) {
      this.isEditingRound = true;
      this.editingRoundIndex = index;

      // 确保轮次有day_offset字段
      var round = this.formData.rounds[index];
      this.currentRound = _objectSpread(_objectSpread({}, round), {}, {
        round: round.round,
        name: round.name || "\u8F6E\u6B21".concat(round.round),
        day_offset: round.day_offset !== undefined ? round.day_offset : 0,
        duration: round.duration || 60
      });
      this.$refs.roundPopup.open();
    },
    // 确认添加或编辑轮次
    confirmRound: function confirmRound() {
      // 验证轮次名称和时间
      if (!this.currentRound.name || !this.currentRound.time) {
        uni.showToast({
          title: '轮次名称和时间不能为空',
          icon: 'none'
        });
        return;
      }

      // 验证时限
      if (!this.validateRoundDuration(this.currentRound)) {
        return; // 如果验证不通过，不关闭弹窗
      }

      // 确保day_offset有值
      if (this.currentRound.day_offset === undefined) {
        // 自动计算day_offset
        this.calculateRoundDayOffset(this.currentRound);
      }
      if (this.isEditingRound && this.editingRoundIndex !== null) {
        // 更新现有轮次
        this.formData.rounds.splice(this.editingRoundIndex, 1, _objectSpread({}, this.currentRound));
      } else {
        // 添加新轮次
        this.formData.rounds.push(_objectSpread({}, this.currentRound));
      }

      // 关闭弹窗
      this.$refs.roundPopup.close();

      // 重新排序轮次
      this.formData.rounds.sort(function (a, b) {
        return a.round - b.round;
      });
    },
    // 处理状态变化
    onStatusChange: function onStatusChange(e) {
      this.currentRound.status = e.detail.value ? 1 : 0;
    },
    // 关闭轮次编辑弹窗
    closeRoundPopup: function closeRoundPopup() {
      this.$refs.roundPopup.close();
    },
    // 修改验证表单方法
    validateForm: function validateForm() {
      var _this3 = this;
      return new Promise(function (resolve, reject) {
        _this3.$refs.form.validate().then(function (res) {
          if (res) {
            // 验证轮次信息
            if (_this3.formData.rounds.length === 0) {
              uni.showToast({
                title: '请至少添加一个轮次',
                icon: 'none'
              });
              reject('请至少添加一个轮次');
              return;
            }

            // 验证所有轮次的时间和时限
            var hasInvalidRound = false;
            var invalidMessage = '';
            for (var i = 0; i < _this3.formData.rounds.length; i++) {
              var round = _this3.formData.rounds[i];

              // 验证时间是否设置
              if (!round.time) {
                invalidMessage = "\u7B2C".concat(round.round, "\u8F6E\u65F6\u95F4\u4E0D\u5B8C\u6574");
                hasInvalidRound = true;
                break;
              }

              // 验证有效时长范围
              var duration = parseInt(round.duration);
              if (isNaN(duration) || duration < 1 || duration > 480) {
                invalidMessage = "\u7B2C".concat(round.round, "\u8F6E\u6709\u6548\u65F6\u957F\u5E94\u57281-480\u5206\u949F\u4E4B\u95F4");
                hasInvalidRound = true;
                break;
              }

              // 验证轮次时限
              if (!_this3.validateRoundDuration(round)) {
                hasInvalidRound = true;
                break;
              }
            }
            if (hasInvalidRound) {
              if (invalidMessage) {
                uni.showToast({
                  title: invalidMessage,
                  icon: 'none'
                });
              }
              reject('轮次验证未通过');
              return;
            }
            resolve(true);
          } else {
            reject('表单验证不通过');
          }
        }).catch(function (err) {
          reject(err);
        });
      });
    },
    // 取消
    handleCancel: function handleCancel() {
      uni.navigateBack();
    },
    // 提交表单
    handleSubmit: function handleSubmit() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var hasInvalidRound, invalidMessage, i, round, duration;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                if (!_this4.$refs.form) {
                  _context2.next = 4;
                  break;
                }
                _context2.next = 4;
                return _this4.$refs.form.validate();
              case 4:
                // 验证所有轮次
                hasInvalidRound = false;
                invalidMessage = '';
                i = 0;
              case 7:
                if (!(i < _this4.formData.rounds.length)) {
                  _context2.next = 24;
                  break;
                }
                round = _this4.formData.rounds[i]; // 验证时间是否设置
                if (round.time) {
                  _context2.next = 13;
                  break;
                }
                invalidMessage = "\u7B2C".concat(round.round, "\u8F6E\u65F6\u95F4\u4E0D\u5B8C\u6574");
                hasInvalidRound = true;
                return _context2.abrupt("break", 24);
              case 13:
                // 验证有效时长范围
                duration = parseInt(round.duration);
                if (!(isNaN(duration) || duration < 1 || duration > 480)) {
                  _context2.next = 18;
                  break;
                }
                invalidMessage = "\u7B2C".concat(round.round, "\u8F6E\u6709\u6548\u65F6\u957F\u5E94\u57281-480\u5206\u949F\u4E4B\u95F4");
                hasInvalidRound = true;
                return _context2.abrupt("break", 24);
              case 18:
                if (_this4.validateRoundDuration(round)) {
                  _context2.next = 21;
                  break;
                }
                hasInvalidRound = true;
                return _context2.abrupt("break", 24);
              case 21:
                i++;
                _context2.next = 7;
                break;
              case 24:
                if (!hasInvalidRound) {
                  _context2.next = 27;
                  break;
                }
                if (invalidMessage) {
                  uni.showToast({
                    title: invalidMessage,
                    icon: 'none'
                  });
                }
                return _context2.abrupt("return");
              case 27:
                // 确保所有轮次都有day_offset
                _this4.formData.rounds.forEach(function (round) {
                  if (round.day_offset === undefined) {
                    if (_this4.formData.is_cross_day) {
                      _this4.calculateRoundDayOffset(round);
                    } else {
                      round.day_offset = 0;
                    }
                  }
                });

                // 提交表单
                _context2.next = 30;
                return _this4.submitFormData();
              case 30:
                _context2.next = 35;
                break;
              case 32:
                _context2.prev = 32;
                _context2.t0 = _context2["catch"](0);
                uni.showToast({
                  title: typeof _context2.t0 === 'string' ? _context2.t0 : '表单验证失败',
                  icon: 'none'
                });
              case 35:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 32]]);
      }))();
    },
    // 显示轮次的实际时间
    getActualDateDisplay: function getActualDateDisplay(round) {
      if (!round || !round.time) return '未设置';

      // 简化显示，不再添加(次日)，只依靠标签显示
      return round.time;
    },
    // 班次跨天设置变化
    onCrossDayChange: function onCrossDayChange(e) {
      this.formData.is_cross_day = e.detail.value;

      // 自动重新计算所有轮次的day_offset
      this.recalculateAllRoundOffsets();

      // 显示提示
      if (this.formData.is_cross_day) {
        uni.showToast({
          title: '已自动调整轮次日期',
          icon: 'none',
          duration: 2000
        });
      }
    },
    // 提交表单数据
    submitFormData: function submitFormData() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var submitData, res, pages, prevPage;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                // 准备提交数据
                submitData = {
                  shift_id: _this5.shift_id,
                  name: _this5.formData.name,
                  start_time: _this5.formData.start_time,
                  end_time: _this5.formData.end_time,
                  is_cross_day: _this5.formData.is_cross_day,
                  status: _this5.formData.status,
                  rounds: _this5.formData.rounds.map(function (round) {
                    return {
                      round: round.round,
                      name: round.name || "\u8F6E\u6B21".concat(round.round),
                      time: round.time,
                      day_offset: parseInt(round.day_offset || 0),
                      duration: parseInt(round.duration || 60),
                      status: round.status
                    };
                  })
                };
                uni.showLoading({
                  title: '保存中...'
                });
                _context3.next = 5;
                return _patrolApi.default.updateShift(submitData);
              case 5:
                res = _context3.sent;
                if (res.code === 0) {
                  // 先隐藏加载中提示
                  uni.hideLoading();

                  // 标记列表页需要刷新
                  pages = getCurrentPages();
                  prevPage = pages[pages.length - 2];
                  if (prevPage && prevPage.$vm) {
                    prevPage.$vm.needRefresh = true;
                  }

                  // 显示成功提示并返回
                  uni.showToast({
                    title: '保存成功',
                    icon: 'success',
                    duration: 2000,
                    complete: function complete() {
                      setTimeout(function () {
                        uni.navigateBack();
                      }, 500);
                    }
                  });
                } else {
                  uni.hideLoading();
                  uni.showToast({
                    title: res.message || '保存失败',
                    icon: 'none',
                    duration: 2000
                  });
                }
                _context3.next = 14;
                break;
              case 9:
                _context3.prev = 9;
                _context3.t0 = _context3["catch"](0);
                uni.hideLoading();
                console.error('保存班次出错', _context3.t0);
                if (typeof _context3.t0 === 'string') {
                  uni.showToast({
                    title: _context3.t0,
                    icon: 'none',
                    duration: 2000
                  });
                } else {
                  uni.showToast({
                    title: '保存出错',
                    icon: 'none',
                    duration: 2000
                  });
                }
              case 14:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 9]]);
      }))();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 393:
/*!*********************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/shift/edit.vue?vue&type=style&index=0&lang=scss& ***!
  \*********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=scss& */ 394);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 394:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/shift/edit.vue?vue&type=style&index=0&lang=scss& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[387,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/patrol_pkg/shift/edit.js.map
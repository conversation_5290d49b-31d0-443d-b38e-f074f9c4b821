(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol_pkg/common/vendor"],{

/***/ 300:
/*!*************************************!*\
  !*** D:/Xwzc/utils/qrcode-utils.js ***!
  \*************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.getQRCodeData = getQRCodeData;
exports.incrementQRCodeVersion = incrementQRCodeVersion;
exports.parseQRCodeContent = parseQRCodeContent;
exports.verifyQRCode = verifyQRCode;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _md = _interopRequireDefault(__webpack_require__(/*! md5 */ 301));
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! ./patrol-api.js */ 71));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
// 默认密钥 - 实际开发中应该设置为复杂且安全的值
var DEFAULT_SECRET_KEY = 'CU5t0mPatr0lQrc0d3S3cr3tKey';

/**
 * 生成巡检点二维码数据
 * @param {Object} pointInfo 巡检点信息
 * @param {Object} options 额外选项
 * @param {Boolean} options.includeTimestamp 是否包含时间戳
 * @returns {String} 二维码内容
 */
function getQRCodeData(pointInfo) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  if (!pointInfo || !pointInfo._id) {
    console.error('点位信息不完整');
    return '';
  }

  // 修复时间戳逻辑
  var includeTimestamp = options.includeTimestamp !== false; // 默认为true，除非明确设置为false

  // 如果已有二维码内容，直接使用其中的时间戳
  var timestamp;
  if (pointInfo.qrcode_content) {
    try {
      var existingContent = JSON.parse(pointInfo.qrcode_content);
      timestamp = existingContent.t;
    } catch (e) {
      console.error('解析现有二维码内容失败:', e);
    }
  }

  // 如果没有现有时间戳且需要包含时间戳，则生成新的
  if (!timestamp && includeTimestamp) {
    timestamp = Date.now();
  }
  var version = pointInfo.qrcode_version || 1;
  var secretKey = pointInfo.qrcode_hash_key || DEFAULT_SECRET_KEY;
  console.log('生成二维码使用的密钥:', secretKey);

  // 构建原始内容（按固定顺序）
  var rawContent = {
    type: 'PATROL_CHECK_IN',
    // 1. 类型
    pid: pointInfo._id,
    // 2. 点位ID
    name: pointInfo.name || '',
    // 3. 名称
    v: version,
    // 4. 版本号
    t: timestamp || 0 // 5. 时间戳
  };

  // 按固定顺序生成字符串
  var orderedContent = ['type=' + rawContent.type, 'pid=' + rawContent.pid, 'name=' + rawContent.name, 'v=' + rawContent.v, 't=' + rawContent.t].join('&');

  // 添加分隔符后计算哈希值
  var hashInput = orderedContent + '|' + secretKey; // 使用|作为分隔符
  console.log('生成二维码的内容字符串:', orderedContent);
  console.log('生成二维码的完整字符串:', hashInput);

  // 计算哈希值
  var hash = (0, _md.default)(hashInput).substring(0, 8);
  console.log('生成的哈希值:', hash);

  // 完整的二维码内容
  var qrcodeContent = _objectSpread(_objectSpread({}, rawContent), {}, {
    h: hash
  });
  return JSON.stringify(qrcodeContent);
}

/**
 * 解析二维码内容
 * @param {String} qrcodeContent 二维码内容
 * @returns {Object|null} 解析后的数据，若解析失败则返回null
 */
function parseQRCodeContent(qrcodeContent) {
  try {
    var data = JSON.parse(qrcodeContent);
    if (!data.pid || !data.h || !data.type || data.type !== 'PATROL_CHECK_IN') {
      console.error('二维码内容缺失必要参数或类型不正确');
      return null;
    }
    return data;
  } catch (e) {
    console.error('解析二维码内容失败', e);
    return null;
  }
}

/**
 * 验证二维码内容是否有效
 * @param {String} qrcodeContent 二维码内容
 * @param {Object} options 验证选项
 * @param {String} options.pointId 预期的点位ID
 * @param {Boolean} options.checkExpired 是否检查过期
 * @param {Number} options.expireTime 过期时间(毫秒)
 * @returns {Promise<Object>} 验证结果
 */
function verifyQRCode(_x) {
  return _verifyQRCode.apply(this, arguments);
}
/**
 * 增加二维码版本号
 * @param {String} pointId 巡检点ID
 * @returns {Promise<Object>} 更新结果
 */
function _verifyQRCode() {
  _verifyQRCode = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee(qrcodeContent) {
    var options,
      defaultOptions,
      config,
      qrcodeData,
      now,
      pointRes,
      pointInfo,
      secretKey,
      orderedContent,
      hashInput,
      expectedHash,
      _args = arguments;
    return _regenerator.default.wrap(function _callee$(_context) {
      while (1) {
        switch (_context.prev = _context.next) {
          case 0:
            options = _args.length > 1 && _args[1] !== undefined ? _args[1] : {};
            // 默认配置
            defaultOptions = {
              pointId: null,
              // 预期点位ID，如果提供则验证点位ID是否匹配
              checkExpired: true,
              // 是否检查是否过期
              expireTime: 24 * 60 * 60 * 1000 // 默认24小时过期
            };
            config = _objectSpread(_objectSpread({}, defaultOptions), options); // 解析二维码内容
            qrcodeData = parseQRCodeContent(qrcodeContent);
            if (qrcodeData) {
              _context.next = 6;
              break;
            }
            return _context.abrupt("return", {
              valid: false,
              code: 'PARSE_ERROR',
              message: '二维码格式错误'
            });
          case 6:
            if (!(config.pointId && qrcodeData.pid !== config.pointId)) {
              _context.next = 8;
              break;
            }
            return _context.abrupt("return", {
              valid: false,
              code: 'POINT_MISMATCH',
              message: '二维码不匹配当前点位'
            });
          case 8:
            if (!(config.checkExpired && qrcodeData.t)) {
              _context.next = 12;
              break;
            }
            now = Date.now();
            if (!(now - qrcodeData.t > config.expireTime)) {
              _context.next = 12;
              break;
            }
            return _context.abrupt("return", {
              valid: false,
              code: 'EXPIRED',
              message: '二维码已过期'
            });
          case 12:
            _context.prev = 12;
            _context.next = 15;
            return _patrolApi.default.callPointFunction('getPointDetail', {
              point_id: qrcodeData.pid
            });
          case 15:
            pointRes = _context.sent;
            if (!(!pointRes || !pointRes.data)) {
              _context.next = 18;
              break;
            }
            return _context.abrupt("return", {
              valid: false,
              code: 'POINT_NOT_FOUND',
              message: '点位信息不存在'
            });
          case 18:
            pointInfo = pointRes.data;
            secretKey = pointInfo.qrcode_hash_key || DEFAULT_SECRET_KEY;
            console.log('验证二维码使用的密钥:', secretKey);
            console.log('验证的二维码数据:', qrcodeData);

            // 按固定顺序生成字符串
            orderedContent = ['type=' + qrcodeData.type, 'pid=' + qrcodeData.pid, 'name=' + qrcodeData.name, 'v=' + qrcodeData.v, 't=' + qrcodeData.t].join('&'); // 添加分隔符后计算哈希值
            hashInput = orderedContent + '|' + secretKey; // 使用|作为分隔符
            console.log('验证时的内容字符串:', orderedContent);
            console.log('验证时的完整字符串:', hashInput);

            // 计算哈希值
            expectedHash = (0, _md.default)(hashInput).substring(0, 8);
            console.log('哈希值对比:', {
              expected: expectedHash,
              actual: qrcodeData.h,
              content: orderedContent,
              secretKey: secretKey,
              pointInfo: {
                id: pointInfo._id,
                name: pointInfo.name,
                hash_key: pointInfo.qrcode_hash_key,
                version: pointInfo.qrcode_version
              }
            });
            if (!(qrcodeData.h !== expectedHash)) {
              _context.next = 30;
              break;
            }
            return _context.abrupt("return", {
              valid: false,
              code: 'INVALID_HASH',
              message: '二维码验证失败'
            });
          case 30:
            return _context.abrupt("return", {
              valid: true,
              code: 'SUCCESS',
              message: '验证通过',
              data: pointInfo
            });
          case 33:
            _context.prev = 33;
            _context.t0 = _context["catch"](12);
            console.error('验证二维码时发生错误', _context.t0);
            return _context.abrupt("return", {
              valid: false,
              code: 'VERIFY_ERROR',
              message: '验证过程发生错误'
            });
          case 37:
          case "end":
            return _context.stop();
        }
      }
    }, _callee, null, [[12, 33]]);
  }));
  return _verifyQRCode.apply(this, arguments);
}
function incrementQRCodeVersion(_x2) {
  return _incrementQRCodeVersion.apply(this, arguments);
} // 为了向后兼容，导出所有函数作为默认导出
function _incrementQRCodeVersion() {
  _incrementQRCodeVersion = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2(pointId) {
    var result, returnData;
    return _regenerator.default.wrap(function _callee2$(_context2) {
      while (1) {
        switch (_context2.prev = _context2.next) {
          case 0:
            console.log('incrementQRCodeVersion被调用，参数:', {
              pointId: pointId,
              type: (0, _typeof2.default)(pointId)
            });
            if (!(!pointId || typeof pointId !== 'string' || pointId.trim() === '')) {
              _context2.next = 4;
              break;
            }
            console.error('incrementQRCodeVersion: 无效的点位ID', {
              pointId: pointId,
              type: (0, _typeof2.default)(pointId)
            });
            throw new Error('缺少点位ID');
          case 4:
            _context2.prev = 4;
            console.log('调用云函数updatePointQRCode，参数:', {
              params: {
                point_id: pointId.trim(),
                increment_version: true
              }
            });

            // 使用云函数更新二维码版本
            _context2.next = 8;
            return _patrolApi.default.callPointFunction('updatePointQRCode', {
              params: {
                point_id: pointId.trim(),
                increment_version: true
              }
            });
          case 8:
            result = _context2.sent;
            console.log('云函数返回结果:', result);
            if (!(result.code === 0 && result.data)) {
              _context2.next = 16;
              break;
            }
            returnData = {
              updated: true,
              oldVersion: result.data.qrcode_version - 1,
              newVersion: result.data.qrcode_version,
              result: result.data
            };
            console.log('函数返回数据:', returnData);
            return _context2.abrupt("return", returnData);
          case 16:
            console.error('云函数返回错误:', result);
            throw new Error(result.message || '更新失败');
          case 18:
            _context2.next = 24;
            break;
          case 20:
            _context2.prev = 20;
            _context2.t0 = _context2["catch"](4);
            console.error('更新二维码版本失败', {
              error: _context2.t0,
              pointId: pointId,
              errorMessage: _context2.t0.message
            });
            throw _context2.t0;
          case 24:
          case "end":
            return _context2.stop();
        }
      }
    }, _callee2, null, [[4, 20]]);
  }));
  return _incrementQRCodeVersion.apply(this, arguments);
}
var _default = {
  getQRCodeData: getQRCodeData,
  parseQRCodeContent: parseQRCodeContent,
  verifyQRCode: verifyQRCode,
  incrementQRCodeVersion: incrementQRCodeVersion
};
exports.default = _default;

/***/ }),

/***/ 301:
/*!***************************************!*\
  !*** D:/Xwzc/node_modules/md5/md5.js ***!
  \***************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

(function () {
  var crypt = __webpack_require__(/*! crypt */ 302),
    utf8 = __webpack_require__(/*! charenc */ 303).utf8,
    isBuffer = __webpack_require__(/*! is-buffer */ 304),
    bin = __webpack_require__(/*! charenc */ 303).bin,
    // The core
    md5 = function md5(message, options) {
      // Convert to byte array
      if (message.constructor == String) {
        if (options && options.encoding === 'binary') message = bin.stringToBytes(message);else message = utf8.stringToBytes(message);
      } else if (isBuffer(message)) message = Array.prototype.slice.call(message, 0);else if (!Array.isArray(message) && message.constructor !== Uint8Array) message = message.toString();
      // else, assume byte array already

      var m = crypt.bytesToWords(message),
        l = message.length * 8,
        a = 1732584193,
        b = -271733879,
        c = -1732584194,
        d = 271733878;

      // Swap endian
      for (var i = 0; i < m.length; i++) {
        m[i] = (m[i] << 8 | m[i] >>> 24) & 0x00FF00FF | (m[i] << 24 | m[i] >>> 8) & 0xFF00FF00;
      }

      // Padding
      m[l >>> 5] |= 0x80 << l % 32;
      m[(l + 64 >>> 9 << 4) + 14] = l;

      // Method shortcuts
      var FF = md5._ff,
        GG = md5._gg,
        HH = md5._hh,
        II = md5._ii;
      for (var i = 0; i < m.length; i += 16) {
        var aa = a,
          bb = b,
          cc = c,
          dd = d;
        a = FF(a, b, c, d, m[i + 0], 7, -680876936);
        d = FF(d, a, b, c, m[i + 1], 12, -389564586);
        c = FF(c, d, a, b, m[i + 2], 17, 606105819);
        b = FF(b, c, d, a, m[i + 3], 22, -1044525330);
        a = FF(a, b, c, d, m[i + 4], 7, -176418897);
        d = FF(d, a, b, c, m[i + 5], 12, 1200080426);
        c = FF(c, d, a, b, m[i + 6], 17, -1473231341);
        b = FF(b, c, d, a, m[i + 7], 22, -45705983);
        a = FF(a, b, c, d, m[i + 8], 7, 1770035416);
        d = FF(d, a, b, c, m[i + 9], 12, -1958414417);
        c = FF(c, d, a, b, m[i + 10], 17, -42063);
        b = FF(b, c, d, a, m[i + 11], 22, -1990404162);
        a = FF(a, b, c, d, m[i + 12], 7, 1804603682);
        d = FF(d, a, b, c, m[i + 13], 12, -40341101);
        c = FF(c, d, a, b, m[i + 14], 17, -1502002290);
        b = FF(b, c, d, a, m[i + 15], 22, 1236535329);
        a = GG(a, b, c, d, m[i + 1], 5, -165796510);
        d = GG(d, a, b, c, m[i + 6], 9, -1069501632);
        c = GG(c, d, a, b, m[i + 11], 14, 643717713);
        b = GG(b, c, d, a, m[i + 0], 20, -373897302);
        a = GG(a, b, c, d, m[i + 5], 5, -701558691);
        d = GG(d, a, b, c, m[i + 10], 9, 38016083);
        c = GG(c, d, a, b, m[i + 15], 14, -660478335);
        b = GG(b, c, d, a, m[i + 4], 20, -405537848);
        a = GG(a, b, c, d, m[i + 9], 5, 568446438);
        d = GG(d, a, b, c, m[i + 14], 9, -1019803690);
        c = GG(c, d, a, b, m[i + 3], 14, -187363961);
        b = GG(b, c, d, a, m[i + 8], 20, 1163531501);
        a = GG(a, b, c, d, m[i + 13], 5, -1444681467);
        d = GG(d, a, b, c, m[i + 2], 9, -51403784);
        c = GG(c, d, a, b, m[i + 7], 14, 1735328473);
        b = GG(b, c, d, a, m[i + 12], 20, -1926607734);
        a = HH(a, b, c, d, m[i + 5], 4, -378558);
        d = HH(d, a, b, c, m[i + 8], 11, -2022574463);
        c = HH(c, d, a, b, m[i + 11], 16, 1839030562);
        b = HH(b, c, d, a, m[i + 14], 23, -35309556);
        a = HH(a, b, c, d, m[i + 1], 4, -1530992060);
        d = HH(d, a, b, c, m[i + 4], 11, 1272893353);
        c = HH(c, d, a, b, m[i + 7], 16, -155497632);
        b = HH(b, c, d, a, m[i + 10], 23, -1094730640);
        a = HH(a, b, c, d, m[i + 13], 4, 681279174);
        d = HH(d, a, b, c, m[i + 0], 11, -358537222);
        c = HH(c, d, a, b, m[i + 3], 16, -722521979);
        b = HH(b, c, d, a, m[i + 6], 23, 76029189);
        a = HH(a, b, c, d, m[i + 9], 4, -640364487);
        d = HH(d, a, b, c, m[i + 12], 11, -421815835);
        c = HH(c, d, a, b, m[i + 15], 16, 530742520);
        b = HH(b, c, d, a, m[i + 2], 23, -995338651);
        a = II(a, b, c, d, m[i + 0], 6, -198630844);
        d = II(d, a, b, c, m[i + 7], 10, 1126891415);
        c = II(c, d, a, b, m[i + 14], 15, -1416354905);
        b = II(b, c, d, a, m[i + 5], 21, -57434055);
        a = II(a, b, c, d, m[i + 12], 6, 1700485571);
        d = II(d, a, b, c, m[i + 3], 10, -1894986606);
        c = II(c, d, a, b, m[i + 10], 15, -1051523);
        b = II(b, c, d, a, m[i + 1], 21, -2054922799);
        a = II(a, b, c, d, m[i + 8], 6, 1873313359);
        d = II(d, a, b, c, m[i + 15], 10, -30611744);
        c = II(c, d, a, b, m[i + 6], 15, -1560198380);
        b = II(b, c, d, a, m[i + 13], 21, 1309151649);
        a = II(a, b, c, d, m[i + 4], 6, -145523070);
        d = II(d, a, b, c, m[i + 11], 10, -1120210379);
        c = II(c, d, a, b, m[i + 2], 15, 718787259);
        b = II(b, c, d, a, m[i + 9], 21, -343485551);
        a = a + aa >>> 0;
        b = b + bb >>> 0;
        c = c + cc >>> 0;
        d = d + dd >>> 0;
      }
      return crypt.endian([a, b, c, d]);
    };

  // Auxiliary functions
  md5._ff = function (a, b, c, d, x, s, t) {
    var n = a + (b & c | ~b & d) + (x >>> 0) + t;
    return (n << s | n >>> 32 - s) + b;
  };
  md5._gg = function (a, b, c, d, x, s, t) {
    var n = a + (b & d | c & ~d) + (x >>> 0) + t;
    return (n << s | n >>> 32 - s) + b;
  };
  md5._hh = function (a, b, c, d, x, s, t) {
    var n = a + (b ^ c ^ d) + (x >>> 0) + t;
    return (n << s | n >>> 32 - s) + b;
  };
  md5._ii = function (a, b, c, d, x, s, t) {
    var n = a + (c ^ (b | ~d)) + (x >>> 0) + t;
    return (n << s | n >>> 32 - s) + b;
  };

  // Package private blocksize
  md5._blocksize = 16;
  md5._digestsize = 16;
  module.exports = function (message, options) {
    if (message === undefined || message === null) throw new Error('Illegal argument ' + message);
    var digestbytes = crypt.wordsToBytes(md5(message, options));
    return options && options.asBytes ? digestbytes : options && options.asString ? bin.bytesToString(digestbytes) : crypt.bytesToHex(digestbytes);
  };
})();

/***/ }),

/***/ 302:
/*!*******************************************!*\
  !*** D:/Xwzc/node_modules/crypt/crypt.js ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports) {

(function () {
  var base64map = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',
    crypt = {
      // Bit-wise rotation left
      rotl: function rotl(n, b) {
        return n << b | n >>> 32 - b;
      },
      // Bit-wise rotation right
      rotr: function rotr(n, b) {
        return n << 32 - b | n >>> b;
      },
      // Swap big-endian to little-endian and vice versa
      endian: function endian(n) {
        // If number given, swap endian
        if (n.constructor == Number) {
          return crypt.rotl(n, 8) & 0x00FF00FF | crypt.rotl(n, 24) & 0xFF00FF00;
        }

        // Else, assume array and swap all items
        for (var i = 0; i < n.length; i++) {
          n[i] = crypt.endian(n[i]);
        }
        return n;
      },
      // Generate an array of any length of random bytes
      randomBytes: function randomBytes(n) {
        for (var bytes = []; n > 0; n--) {
          bytes.push(Math.floor(Math.random() * 256));
        }
        return bytes;
      },
      // Convert a byte array to big-endian 32-bit words
      bytesToWords: function bytesToWords(bytes) {
        for (var words = [], i = 0, b = 0; i < bytes.length; i++, b += 8) {
          words[b >>> 5] |= bytes[i] << 24 - b % 32;
        }
        return words;
      },
      // Convert big-endian 32-bit words to a byte array
      wordsToBytes: function wordsToBytes(words) {
        for (var bytes = [], b = 0; b < words.length * 32; b += 8) {
          bytes.push(words[b >>> 5] >>> 24 - b % 32 & 0xFF);
        }
        return bytes;
      },
      // Convert a byte array to a hex string
      bytesToHex: function bytesToHex(bytes) {
        for (var hex = [], i = 0; i < bytes.length; i++) {
          hex.push((bytes[i] >>> 4).toString(16));
          hex.push((bytes[i] & 0xF).toString(16));
        }
        return hex.join('');
      },
      // Convert a hex string to a byte array
      hexToBytes: function hexToBytes(hex) {
        for (var bytes = [], c = 0; c < hex.length; c += 2) {
          bytes.push(parseInt(hex.substr(c, 2), 16));
        }
        return bytes;
      },
      // Convert a byte array to a base-64 string
      bytesToBase64: function bytesToBase64(bytes) {
        for (var base64 = [], i = 0; i < bytes.length; i += 3) {
          var triplet = bytes[i] << 16 | bytes[i + 1] << 8 | bytes[i + 2];
          for (var j = 0; j < 4; j++) {
            if (i * 8 + j * 6 <= bytes.length * 8) base64.push(base64map.charAt(triplet >>> 6 * (3 - j) & 0x3F));else base64.push('=');
          }
        }
        return base64.join('');
      },
      // Convert a base-64 string to a byte array
      base64ToBytes: function base64ToBytes(base64) {
        // Remove non-base-64 characters
        base64 = base64.replace(/[^A-Z0-9+\/]/ig, '');
        for (var bytes = [], i = 0, imod4 = 0; i < base64.length; imod4 = ++i % 4) {
          if (imod4 == 0) continue;
          bytes.push((base64map.indexOf(base64.charAt(i - 1)) & Math.pow(2, -2 * imod4 + 8) - 1) << imod4 * 2 | base64map.indexOf(base64.charAt(i)) >>> 6 - imod4 * 2);
        }
        return bytes;
      }
    };
  module.exports = crypt;
})();

/***/ }),

/***/ 303:
/*!***********************************************!*\
  !*** D:/Xwzc/node_modules/charenc/charenc.js ***!
  \***********************************************/
/*! no static exports found */
/***/ (function(module, exports) {

var charenc = {
  // UTF-8 encoding
  utf8: {
    // Convert a string to a byte array
    stringToBytes: function stringToBytes(str) {
      return charenc.bin.stringToBytes(unescape(encodeURIComponent(str)));
    },
    // Convert a byte array to a string
    bytesToString: function bytesToString(bytes) {
      return decodeURIComponent(escape(charenc.bin.bytesToString(bytes)));
    }
  },
  // Binary encoding
  bin: {
    // Convert a string to a byte array
    stringToBytes: function stringToBytes(str) {
      for (var bytes = [], i = 0; i < str.length; i++) {
        bytes.push(str.charCodeAt(i) & 0xFF);
      }
      return bytes;
    },
    // Convert a byte array to a string
    bytesToString: function bytesToString(bytes) {
      for (var str = [], i = 0; i < bytes.length; i++) {
        str.push(String.fromCharCode(bytes[i]));
      }
      return str.join('');
    }
  }
};
module.exports = charenc;

/***/ }),

/***/ 304:
/*!*****************************************!*\
  !*** ./node_modules/is-buffer/index.js ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports) {

/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */

// The _isBuffer check is for Safari 5-7 support, because it's missing
// Object.prototype.constructor. Remove this eventually
module.exports = function (obj) {
  return obj != null && (isBuffer(obj) || isSlowBuffer(obj) || !!obj._isBuffer)
}

function isBuffer (obj) {
  return !!obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)
}

// For Node v0.10 support. Remove this eventually.
function isSlowBuffer (obj) {
  return typeof obj.readFloatLE === 'function' && typeof obj.slice === 'function' && isBuffer(obj.slice(0, 0))
}


/***/ })

}]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/patrol_pkg/common/vendor.js.map
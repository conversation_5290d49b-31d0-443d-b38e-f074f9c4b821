<view class="checkin-container"><view class="nav-header"><view class="nav-left"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="nav-back" bindtap="__e"><uni-icons vue-id="2045429a-1" type="back" size="20" color="#000000" bind:__l="__l"></uni-icons></view></view><text class="nav-title">打卡签到</text></view><block wx:if="{{isLoading}}"><view class="loading-container"><view class="loading-spinner"><view class="spinner-circle"></view></view><text class="loading-text">加载中...</text></view></block><block wx:if="{{showCamera}}"><view class="camera-container"><camera style="width:100%;height:calc(100vh - 200rpx);" id="nativeCamera" device-position="back" flash="{{flashMode}}" data-event-opts="{{[['error',[['handleCameraError',['$event']]]]]}}" binderror="__e"></camera><view class="camera-controls"><view data-event-opts="{{[['tap',[['toggleFlash',['$event']]]]]}}" class="control-item" bindtap="__e"><uni-icons vue-id="2045429a-2" type="{{$root.m0}}" size="26" color="#FFFFFF" bind:__l="__l"></uni-icons><text>{{$root.m1}}</text></view><view data-event-opts="{{[['tap',[['takePhoto',['$event']]]]]}}" class="control-item photo-btn" bindtap="__e"><view class="photo-circle"></view></view><view data-event-opts="{{[['tap',[['closeCamera',['$event']]]]]}}" class="control-item" bindtap="__e"><uni-icons vue-id="2045429a-3" type="closeempty" size="26" color="#FFFFFF" bind:__l="__l"></uni-icons><text>取消</text></view></view></view></block><block wx:if="{{showScanner}}"><view class="scanner-container"><camera class="scanner-camera" mode="scanCode" device-position="back" flash="{{flashMode}}" scan-area="{{[[0.1,0.1,0.8,0.8]]}}" auto-focus="{{true}}" data-event-opts="{{[['scancode',[['onScanCode',['$event']]]],['error',[['handleCameraError',['$event']]]]]}}" bindscancode="__e" binderror="__e"><cover-view class="scan-frame"><cover-view class="frame-line top"></cover-view><cover-view class="frame-line right"></cover-view><cover-view class="frame-line bottom"></cover-view><cover-view class="frame-line left"></cover-view><cover-view class="corner-box left-top"></cover-view><cover-view class="corner-box right-top"></cover-view><cover-view class="corner-box left-bottom"></cover-view><cover-view class="corner-box right-bottom"></cover-view><cover-view class="scan-line"></cover-view></cover-view><cover-view class="scan-tips"><cover-view class="tips-text">将二维码放入框内，即可自动扫描</cover-view></cover-view><cover-view class="scanner-controls"><cover-view data-event-opts="{{[['tap',[['toggleScannerFlash',['$event']]]]]}}" class="control-item" bindtap="__e"><cover-view class="control-icon"><block wx:if="{{flashMode==='off'}}"><cover-image src="/static/icons/flashlight.png"></cover-image></block><block wx:else><cover-image src="/static/icons/flashlight-off.png"></cover-image></block></cover-view><cover-view class="control-text">{{flashMode==='off'?'打开照明':'关闭照明'}}</cover-view></cover-view><cover-view data-event-opts="{{[['tap',[['closeScanner',['$event']]]]]}}" class="control-item" bindtap="__e"><cover-view class="control-icon"><cover-image src="/static/icons/close.png"></cover-image></cover-view><cover-view class="control-text">取消</cover-view></cover-view></cover-view></camera></view></block><block wx:else><block><view class="{{['info-section',(isInRange)?'in-range':'']}}"><view class="info-content"><view class="{{['status-indicator',(isInRange)?'status-indicator--active':'']}}"><view class="indicator-dot"></view><text class="status-text">{{isInRange?'已进入打卡范围':'请靠近打卡点'}}</text></view><view class="distance-info"><view class="distance-value"><text class="value">{{distanceText}}</text></view><block wx:if="{{currentRound}}"><view class="round-badge"><uni-icons vue-id="2045429a-4" type="calendar" color="#8F959E" size="12" bind:__l="__l"></uni-icons><text>{{"轮次 "+currentRound.round}}</text></view></block></view></view></view><view class="map-section"><map class="checkin-map" id="checkInMap" latitude="{{currentLocation.latitude}}" longitude="{{currentLocation.longitude}}" markers="{{markers}}" circles="{{circles}}" scale="{{19}}" show-location="{{true}}" enable-zoom="{{true}}" enable-scroll="{{true}}" enable-rotate="{{true}}" data-event-opts="{{[['regionchange',[['onMapRegionChange',['$event']]]],['markertap',[['onMarkerTap',['$event']]]]]}}" bindregionchange="__e" bindmarkertap="__e"><view class="map-controls"><view data-event-opts="{{[['tap',[['relocate',['$event']]]]]}}" class="{{['control-btn',(isFollowMode)?'control-btn--active':'']}}" bindtap="__e"><uni-icons vue-id="2045429a-5" type="location" size="20" color="{{isFollowMode?'#34C759':'#8F959E'}}" bind:__l="__l"></uni-icons></view></view><view class="location-accuracy"><view class="status-dot" style="{{'background:'+($root.m2)+';'}}"></view><text class="accuracy-text">{{"GPS精度: "+(currentLocation.accuracy?$root.g0:'0')+"米"}}</text></view></map></view><scroll-view class="content-wrapper" scroll-y="{{true}}"><view class="info-card current-point"><view class="card-header"><view class="point-icon current"><uni-icons vue-id="2045429a-6" type="location-filled" size="16" color="#FFFFFF" bind:__l="__l"></uni-icons></view><view class="point-info"><text class="point-title">当前点位</text><text class="point-name">{{formattedCurrentPointName}}</text></view><block wx:if="{{isCurrentPointChecked}}"><view class="point-status checked"><text class="status-text">已打卡</text></view></block><block wx:else><view class="point-status pending"><text class="status-text">未打卡</text></view></block></view></view><block wx:if="{{nextUnCheckedPoint}}"><view data-event-opts="{{[['tap',[['goToNextPoint',['$event']]]]]}}" class="info-card next-point" bindtap="__e"><view class="card-header"><view class="point-icon next"><uni-icons vue-id="2045429a-7" type="arrowright" size="16" color="#FFFFFF" bind:__l="__l"></uni-icons></view><view class="point-info"><text class="point-title">下个点位</text><text class="point-name">{{formattedNextPointName}}</text></view><view class="distance-badge"><text class="distance-text">{{$root.m3}}</text></view></view><view class="card-details"><text class="detail-text">{{"🚶‍♂️ 点击导航到下个点位"+(nextUnCheckedPoint.qrcode_enabled?' · 🔍 需要扫码打卡':'')}}</text></view></view></block><block wx:if="{{isRoundCompleted&&pointInfo}}"><view class="info-card completed"><view class="card-header"><view class="point-icon completed"><uni-icons vue-id="2045429a-8" type="checkbox-filled" size="16" color="#FFFFFF" bind:__l="__l"></uni-icons></view><view class="point-info"><text class="point-title">🎉 恭喜</text><text class="point-name">本轮次已完成</text></view><view class="completed-badge"><text class="status-text">完成打卡</text></view></view></view></block><block wx:if="{{isLastUnCheckedPoint&&!isRoundCompleted&&pointInfo}}"><view class="info-card last-point"><view class="card-header"><view class="point-icon last"><uni-icons vue-id="2045429a-9" type="flag" size="16" color="#FFFFFF" bind:__l="__l"></uni-icons></view><view class="point-info"><text class="point-title">最后点位</text><text class="point-name">完成后即可结束巡检</text></view><view class="last-badge"><text class="status-text">最终点位</text></view></view><view class="card-details"><text class="detail-text">🎯 这是本轮次的最后一个点位，完成打卡后即可结束巡检</text></view></view></block><block wx:if="{{!isRoundValid||!isInRange&&!pointInfo.qrcode_enabled}}"><view class="warning-area"><view class="warning-icon"><uni-icons vue-id="2045429a-10" type="info" size="24" color="#FF9500" bind:__l="__l"></uni-icons></view><block wx:if="{{!isRoundValid}}"><text class="warning-text">{{roundErrorMessage}}</text></block><block wx:else><text class="warning-text">您当前不在打卡范围内，请靠近点位再进行打卡</text></block></view></block></scroll-view><view class="action-section"><view class="checkin-btn-container"><block wx:if="{{pointInfo&&pointInfo.qrcode_enabled===true}}"><button data-event-opts="{{[['tap',[['handleQRCodeClick',['$event']]]]]}}" class="{{['btn-checkin','qrcode-button',(qrcodeVerified||isInRange&&isRoundValid)?'btn-checkin--active':'',(loading||!isRoundValid||isCurrentPointChecked||isAutoJumping)?'btn-disabled':'']}}" bindtap="__e"><uni-icons vue-id="2045429a-11" type="scan" size="20" color="#FFFFFF" bind:__l="__l"></uni-icons><text>{{isAutoJumping?'跳转中...':'扫码打卡'}}</text></button></block><block wx:if="{{!pointInfo||pointInfo.qrcode_enabled===false}}"><button data-event-opts="{{[['tap',[['handleGPSClick',['$event']]]]]}}" class="{{['btn-checkin','gps-button',(isInRange&&isRoundValid)?'btn-checkin--active':'',(loading||!isRoundValid||isCurrentPointChecked||!isInRange||isAutoJumping)?'btn-disabled':'']}}" bindtap="__e"><uni-icons vue-id="2045429a-12" type="paperplane" size="20" color="#FFFFFF" bind:__l="__l"></uni-icons><text>{{isAutoJumping?'跳转中...':'GPS打卡'}}</text></button></block></view></view></block></block><block wx:if="{{showDistanceMessage}}"><view class="custom-distance-message"><text class="message-text">{{distancePopupMessage}}</text></view></block></view>
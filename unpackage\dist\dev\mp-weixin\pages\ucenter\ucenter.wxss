@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-4883731c {
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
}
page.data-v-4883731c {
  background-color: #f8f9fc;
}
/* 定义动画 */
@-webkit-keyframes fadeInUp-data-v-4883731c {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx);
            transform: translateY(20rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes fadeInUp-data-v-4883731c {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx);
            transform: translateY(20rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.container.data-v-4883731c {
  min-height: 100vh;
  background-color: #f8f9fc;
  padding: 30rpx;
  background: linear-gradient(145deg, #f8faff 0%, #e9f0f8 100%);
  letter-spacing: 1rpx;
}
/* 用户信息卡片 */
.user-card.data-v-4883731c {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 30rpx;
  position: relative;
  -webkit-animation: fadeInUp-data-v-4883731c 0.5s ease;
          animation: fadeInUp-data-v-4883731c 0.5s ease;
  transition: all 0.3s ease;
}
.user-card.data-v-4883731c:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.avatar-section.data-v-4883731c {
  margin-right: 30rpx;
}
.avatar-container.data-v-4883731c {
  width: 150rpx;
  height: 150rpx;
  border-radius: 75rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);
}
.default-avatar-bg.data-v-4883731c {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 75rpx;
  background: linear-gradient(135deg, #3a86ff 0%, #2563eb 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}
.avatar-image.data-v-4883731c {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 75rpx;
  z-index: 2;
  transition: opacity 0.3s ease;
}
/* 未登录状态的默认头像样式 */
.default-avatar.data-v-4883731c {
  width: 150rpx;
  height: 150rpx;
  border-radius: 75rpx;
  background: linear-gradient(135deg, #3a86ff 0%, #2563eb 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8rpx 16rpx rgba(0, 120, 255, 0.2);
}
.user-info.data-v-4883731c {
  flex: 1;
}
.username.data-v-4883731c {
  font-size: 36rpx;
  font-weight: bold;
  color: #2b2e4a;
  margin-bottom: 16rpx;
  display: block;
  text-align: left !important;
  align-self: flex-start;
  width: 100%;
}
.username.login-prompt.data-v-4883731c {
  color: #4a78c9;
}
.login-tip.data-v-4883731c {
  font-size: 28rpx;
  color: #8a94a6;
  margin-bottom: 16rpx;
}
.card-arrow.data-v-4883731c {
  position: absolute;
  right: 30rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.role-tags.data-v-4883731c {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.role-tag.data-v-4883731c {
  font-size: 24rpx;
  color: #4a78c9;
  background-color: rgba(74, 120, 201, 0.1);
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
  transition: all 0.2s ease;
  letter-spacing: 1.5rpx;
}
.role-tag.data-v-4883731c:active {
  background-color: rgba(74, 120, 201, 0.2);
}
/* 登录提示区域 */
.login-required-tip.data-v-4883731c {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);
  margin-top: 30rpx;
  -webkit-animation: fadeInUp-data-v-4883731c 0.6s ease;
          animation: fadeInUp-data-v-4883731c 0.6s ease;
}
.login-required-image.data-v-4883731c {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.9;
}
.login-required-text.data-v-4883731c {
  font-size: 32rpx;
  color: #8a94a6;
  text-align: center;
  letter-spacing: 2rpx;
}
/* 功能中心区域 */
.patrol-section.data-v-4883731c {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);
  -webkit-animation: fadeInUp-data-v-4883731c 0.5s ease;
          animation: fadeInUp-data-v-4883731c 0.5s ease;
  -webkit-animation-delay: 0.1s;
          animation-delay: 0.1s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}
.patrol-title.data-v-4883731c {
  font-size: 34rpx;
  font-weight: 600;
  color: #2b2e4a;
  margin-bottom: 36rpx;
  padding-left: 20rpx;
  position: relative;
  letter-spacing: 2rpx;
}
.patrol-title.data-v-4883731c::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(180deg, #3a86ff 0%, #2563eb 100%);
  border-radius: 6rpx;
}
.patrol-scroll.data-v-4883731c {
  white-space: nowrap;
  width: 100%;
}
/* 在微信小程序环境下隐藏滚动条 */
.patrol-scroll.data-v-4883731c ::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}
.patrol-scroll.data-v-4883731c {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}
.patrol-grid.data-v-4883731c {
  display: inline-flex;
  flex-direction: row;
  padding: 10rpx 0;
}
.patrol-item.data-v-4883731c {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  width: 160rpx;
  margin-right: 50rpx;
  transition: all 0.3s ease;
}
.patrol-item.data-v-4883731c:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
/* 图标样式统一管理 */
.action-icon.data-v-4883731c, .patrol-icon.data-v-4883731c {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.patrol-icon.data-v-4883731c {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}
.patrol-icon.data-v-4883731c:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  z-index: 1;
}
.patrol-icon.data-v-4883731c:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
/* 图标颜色-使用渐变色 */
.news-icon.data-v-4883731c {
  background: linear-gradient(145deg, #5586e8, #2563eb);
}
.export-icon.data-v-4883731c {
  background: linear-gradient(145deg, #49a2e3, #3794dc);
}
.responsible-tasks-icon.data-v-4883731c {
  background: linear-gradient(145deg, #4CAF50, #45a049);
}
.user-management-icon.data-v-4883731c {
  background: linear-gradient(145deg, #667eea, #764ba2);
}
.gm-supervision-icon.data-v-4883731c {
  background: linear-gradient(145deg, #2E8B57, #20B2AA);
}
/* 厂长监督-环保绿色系 */
.cleanup-icon.data-v-4883731c {
  background: linear-gradient(145deg, #ff6b6b, #ee5a24);
}
.settings-icon.data-v-4883731c {
  background: linear-gradient(145deg, #7b8de0, #5e6fd8);
}
.logout-icon.data-v-4883731c {
  background: linear-gradient(145deg, #e06666, #d44c4c);
}
.record-icon.data-v-4883731c {
  background: linear-gradient(145deg, #47b8e0, #32a7d6);
}
/* 巡视记录 */
.calendar-icon.data-v-4883731c {
  background: linear-gradient(145deg, #4a95e5, #3887df);
}
.area-icon.data-v-4883731c {
  background: linear-gradient(145deg, #4aabe5, #3a9ddf);
}
.route-icon.data-v-4883731c {
  background: linear-gradient(145deg, #7469d4, #5c4fc2);
}
/* 线路管理-紫色系 */
.manage-icon.data-v-4883731c {
  background: linear-gradient(145deg, #e06666, #d44c4c);
}
.shift-icon.data-v-4883731c {
  background: linear-gradient(145deg, #e0984a, #d6893c);
}
/* 班次时间 */
.system-settings-icon.data-v-4883731c {
  background: linear-gradient(145deg, #4a95e5, #3887df);
}
.point-icon.data-v-4883731c {
  background: linear-gradient(145deg, #66aee0, #4ca0d9);
}
/* 点位管理 */
.guide-icon.data-v-4883731c {
  background: linear-gradient(145deg, #7b8de0, #5e6fd8);
}
.database-icon.data-v-4883731c {
  background: linear-gradient(145deg, #e06666, #d44c4c);
}
.task-icon.data-v-4883731c {
  background: linear-gradient(145deg, #3975d9, #2862c6);
}
/* 任务管理-深蓝色系 */
.collection-icon.data-v-4883731c {
  background: linear-gradient(145deg, #8e44ad, #6c3483);
}
/* 数据采集-紫色系 */
.notice-icon.data-v-4883731c {
  background: linear-gradient(145deg, #4a95e5, #3887df);
}
.config-icon.data-v-4883731c {
  background: linear-gradient(145deg, #4cb050, #389e3c);
}
.qrcode-icon.data-v-4883731c {
  background: linear-gradient(145deg, #4cb050, #389e3c);
}
.gallery-icon.data-v-4883731c {
  background: linear-gradient(145deg, #ff6b6b, #ee5a24);
}
/* 荣誉展厅-温暖橙红色 */
.patrol-text.data-v-4883731c {
  font-size: 28rpx;
  color: #3d4b66;
  text-align: center;
  white-space: normal;
  width: 100%;
  letter-spacing: 2rpx;
  margin-top: 12rpx;
  font-weight: 500;
}
/* 功能按钮区域 */
.action-list.data-v-4883731c {
  background-color: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);
  -webkit-animation: fadeInUp-data-v-4883731c 0.5s ease;
          animation: fadeInUp-data-v-4883731c 0.5s ease;
  -webkit-animation-delay: 0.2s;
          animation-delay: 0.2s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}
.action-item.data-v-4883731c {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eef0f6;
  transition: all 0.2s ease;
}
.action-item.data-v-4883731c:last-child {
  border-bottom: none;
}
.action-item.data-v-4883731c:active {
  background-color: #f8f9fc;
}
.action-icon.data-v-4883731c {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: hidden;
}
.action-icon.data-v-4883731c:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  z-index: 1;
}
.action-content.data-v-4883731c {
  flex: 1;
  margin-left: 24rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.action-text.data-v-4883731c {
  font-size: 32rpx;
  color: #3d4b66;
  letter-spacing: 2rpx;
  position: relative;
  text-align: left;
  align-self: flex-start;
}
.action-badge.data-v-4883731c {
  background: #ff5a5f;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  min-width: 24rpx;
  text-align: center;
  line-height: 1.2;
  position: absolute;
  top: -16rpx;
  right: -5rpx;
  /* 调整到更贴近"务"字的右上角 */
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
  z-index: 10;
}
.logout-text.data-v-4883731c {
  color: #ff5a5f;
}
/* 待办事项区域 */
.todo-section.data-v-4883731c {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.04);
  -webkit-animation: fadeInUp-data-v-4883731c 0.5s ease;
          animation: fadeInUp-data-v-4883731c 0.5s ease;
  -webkit-animation-delay: 0.3s;
          animation-delay: 0.3s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}
.section-header.data-v-4883731c {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.section-title.data-v-4883731c {
  font-size: 34rpx;
  font-weight: 600;
  color: #2b2e4a;
  position: relative;
  padding-left: 20rpx;
  letter-spacing: 2rpx;
}
.section-title.data-v-4883731c::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(180deg, #ff5a5f 0%, #ff3a3f 100%);
  border-radius: 6rpx;
}
.todo-count-wrapper.data-v-4883731c {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.todo-count.data-v-4883731c {
  font-size: 26rpx;
  color: #ff5a5f;
  background-color: rgba(255, 90, 95, 0.1);
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  transition: all 0.2s ease;
}
.todo-count.data-v-4883731c:active {
  background-color: rgba(255, 90, 95, 0.2);
}
.todo-list.data-v-4883731c {
  margin-top: 20rpx;
}
.todo-item.data-v-4883731c {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eef0f6;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  background-color: #f8f9fc;
}
.todo-item.data-v-4883731c:last-child {
  border-bottom: none;
  margin-bottom: 0;
}
.todo-item.data-v-4883731c:active {
  background-color: #eef1f8;
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
}
.todo-item .todo-content.data-v-4883731c {
  flex: 1;
  margin-right: 20rpx;
}
.todo-item .todo-title.data-v-4883731c {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}
.todo-item .todo-title .name.data-v-4883731c {
  font-size: 30rpx;
  font-weight: bold;
  color: #2b2e4a;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
  letter-spacing: 2rpx;
}
.todo-item .todo-title .project.data-v-4883731c {
  font-size: 24rpx;
  color: #4a78c9;
  background-color: rgba(74, 120, 201, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-bottom: 8rpx;
  letter-spacing: 2rpx;
}
.todo-item .description.data-v-4883731c {
  font-size: 28rpx;
  color: #718096;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  line-height: 1.6;
  letter-spacing: 1.5rpx;
}
.todo-item .todo-footer.data-v-4883731c {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.todo-item .todo-footer .time.data-v-4883731c {
  font-size: 24rpx;
  color: #a0aec0;
  letter-spacing: 1rpx;
}
.todo-item .todo-footer .todo-type.data-v-4883731c {
  font-size: 22rpx;
  color: #4a78c9;
  background-color: rgba(74, 120, 201, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  border: 1rpx solid rgba(74, 120, 201, 0.3);
  margin-left: 16rpx;
  letter-spacing: 1rpx;
}
.empty-todo.data-v-4883731c {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}
.empty-todo .empty-image.data-v-4883731c {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
}
.empty-todo .empty-text.data-v-4883731c {
  font-size: 28rpx;
  color: #8a94a6;
  text-align: center;
  letter-spacing: 2rpx;
}

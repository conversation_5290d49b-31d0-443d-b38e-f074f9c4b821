'use strict';
const db = uniCloud.database();
const dbCmd = db.command;

/**
 * 时间转换工具函数 - 将UTC时间戳转换为北京时间
 * @param {number|string|Date} timestamp - 时间戳或时间对象
 * @returns {string|null} 北京时间字符串（格式：YYYY-MM-DD HH:mm:ss）
 */
const convertToBeijingTime = (timestamp) => {
  if (!timestamp) return null;
  
  // 如果是数字时间戳，转换为北京时间
  if (typeof timestamp === 'number') {
    const beijingTime = new Date(timestamp + 8 * 60 * 60 * 1000); // UTC+8
    return beijingTime.toISOString().replace('T', ' ').substring(0, 19);
  }
  
  // 如果已经是Date对象或字符串，直接返回
  return timestamp;
};

/**
 * 创建北京时间戳
 * @returns {number} 当前时间戳
 */
const createBeijingTimestamp = () => {
  return Date.now(); // 保持原有逻辑，在显示时转换
};

/**
 * 工作流状态映射配置
 * 定义了各个状态的显示名称、颜色和优先级
 * 
 * 状态流程：
 * pending_supervisor → approved_supervisor → pending_pm → approved_pm → 
 * pending_gm → gm_approved_pending_assign → assigned_to_responsible → 
 * completed_by_responsible → final_completed
 * 
 * 特殊状态：
 * - meeting_required: 需要例会讨论（从approved_supervisor分支出来）
 * - terminated: 流程终止（可以在任何阶段终止）
 */
const WORKFLOW_STATUS_MAP = {
  'pending_supervisor': { name: '待主管审核', color: '#ff9800', priority: 1 },
  'approved_supervisor': { name: '主管已通过', color: '#4caf50', priority: 2 },
  'meeting_required': { name: '需要例会讨论', color: '#9c27b0', priority: 3 },
  'pending_pm': { name: '待副厂长审核', color: '#ff9800', priority: 4 },
  'approved_pm': { name: '副厂长已通过', color: '#4caf50', priority: 5 },
  'pending_gm': { name: '待厂长审核', color: '#ff9800', priority: 6 },
  'gm_approved_pending_assign': { name: '待指派负责人', color: '#03a9f4', priority: 6.5 },
  'assigned_to_responsible': { name: '已指派负责人', color: '#2196f3', priority: 7 },
  'completed_by_responsible': { name: '待厂长确认', color: '#00bcd4', priority: 8 },
  'final_completed': { name: '已完成', color: '#4caf50', priority: 10 },
  'terminated': { name: '已终止', color: '#f44336', priority: 11 }
};

/**
 * 获取状态信息
 * @param {string} status - 工作流状态
 * @returns {Object} 状态信息对象，包含name、color、priority
 */
const getStatusInfo = (status) => {
  return WORKFLOW_STATUS_MAP[status] || { name: status, color: '#999', priority: 99 };
};

/**
 * 权限验证函数
 * @param {string} userId - 用户ID
 * @param {string} action - 操作类型（'view'|'admin'等）
 * @returns {Promise<Object>} 权限验证结果
 * @returns {boolean} hasPermission - 是否有权限
 * @returns {string} role - 用户主要角色
 * @returns {Array} roles - 用户所有角色
 * @returns {boolean} isAdmin - 是否为管理员
 */
const checkPermission = async (userId, action = 'view') => {
  if (!userId) {
    return { hasPermission: false, role: null };
  }
  
  try {
    // 从uni-id-users表获取用户信息
    const userRes = await db.collection('uni-id-users')
      .doc(userId)
      .field({ role: true, nickname: true, username: true })
      .get();
    
    if (!userRes.data?.[0]) {
      return { hasPermission: false, role: null };
    }
    
    const userData = userRes.data[0];
    const userRoles = userData.role;
    
    // 处理角色数据（支持单个角色或角色数组）
    let roles = [];
    if (Array.isArray(userRoles)) {
      roles = userRoles;
    } else if (userRoles) {
      roles = [userRoles];
    }
    
    // 基础查看权限：所有登录用户都有（暂时放宽权限）
    if (action === 'view') {
      return { 
        hasPermission: true, 
        role: roles[0] || 'user', 
        roles: roles.length > 0 ? roles : ['user']
      };
    }
    
    // 管理权限：特定角色才有
    const adminRoles = ['admin', 'supervisor', 'PM', 'GM', 'manager', 'reviser'];
    const hasAdminPermission = roles.some(role => adminRoles.includes(role));
    
    return { 
      hasPermission: hasAdminPermission, 
      role: roles[0] || 'user', 
      roles: roles.length > 0 ? roles : ['user'],
      isAdmin: hasAdminPermission
    };
  } catch (error) {
    console.error('权限检查失败:', error);
    // 暂时允许通过，避免阻塞
    return { 
      hasPermission: true, 
      role: 'user', 
      roles: ['user']
    };
  }
};

/**
 * 云函数主入口
 * @param {Object} event - 云函数事件对象
 * @param {string} event.action - 操作类型
 * @param {string} event.uniIdToken - 用户token
 * @param {Object} context - 云函数上下文
 * @returns {Promise<Object>} 响应结果
 */
exports.main = async (event, context) => {
  const { action, uniIdToken } = event;
  
  // 获取用户ID，优先使用context，fallback到token解析
  let userId = context.CLOUDENV_UID;
  
  // 如果context中没有用户ID，尝试从token中解析
  if (!userId && uniIdToken) {
    try {
      // 简单的JWT token解析，提取用户ID
      const tokenParts = uniIdToken.split('.');
      if (tokenParts.length === 3) {
        const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
        if (payload.uid) {
          userId = payload.uid;
        }
      }
    } catch (error) {
      console.error('❌ token解析失败:', error);
    }
  }  

  try {
    // 根据action分发到不同的处理函数
    switch (action) {
      case 'getList':
        return await getList(event, context, userId);
      case 'getStats':
        return await getStats(event, context, userId);
      case 'getMyTasks':
        return await getMyTasks(event, context, userId);
      case 'searchFeedback':
        return await searchFeedback(event, context, userId);
      case 'getResponsibleUsers':
        return await getResponsibleUsers(event, context, userId);
      case 'getGMSupervisionTasks':
        return await getGMSupervisionTasks(event, context, userId);
      default:
        throw new Error(`未知的操作类型: ${action}`);
    }
  } catch (error) {
    console.error('❌ 操作失败:', error);
    return {
      code: -1,
      message: error.message || '操作失败'
    };
  }
};

/**
 * 获取问题列表 - 核心数据获取函数
 * 
 * 功能：
 * - 支持多维度筛选（项目、状态、关键词、时间范围、负责人、紧急程度）
 * - 分页查询
 * - 数据增强处理（状态信息、负责人信息、进度计算、时效计算）
 * - 权限控制
 * 
 * @param {Object} event - 事件参数
 * @param {number} event.pageSize - 每页数量，默认20
 * @param {number} event.pageNum - 页码，默认1
 * @param {string} event.project - 项目筛选
 * @param {string} event.status - 状态筛选
 * @param {string} event.keyword - 关键词搜索
 * @param {Object} event.dateRange - 时间范围筛选
 * @param {string} event.urgency - 紧急程度筛选
 * @param {string} event.responsible - 负责人筛选
 * @param {string} event.sortBy - 排序字段，默认'createTime'
 * @param {string} event.sortOrder - 排序方向，默认'desc'
 * @param {Object} context - 上下文
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 列表数据和分页信息
 */
async function getList(event, context, userId) {
  const { 
    pageSize = 20, 
    pageNum = 1, 
    project, 
    status, 
    workflowType,
    keyword,
    dateRange,
    urgency,
    responsible,
    sortBy = 'createTime',
    sortOrder = 'desc'
  } = event;
  
  try {
    // 权限检查 - 允许未登录用户查看数据，但无操作权限
    let permission = { hasPermission: true, role: 'guest', roles: ['guest'] };
    
    if (userId) {
      permission = await checkPermission(userId, 'view');
      // 如果权限检查失败，但用户已登录，给予基础查看权限
      if (!permission.hasPermission) {
        permission.hasPermission = true;
        permission.role = 'user';
        permission.roles = ['user'];
      }
    }
    
    // 构建基础查询条件
    let whereCondition = {};
    
    // 默认不显示已归档的数据
    whereCondition.isArchived = dbCmd.neq(true);
    
    // 项目筛选
    if (project && project !== 'all') {
      whereCondition.project = project;
    }
    
    // 关键词搜索 - 支持姓名和描述的模糊搜索
    if (keyword && keyword.trim()) {
      const searchKeyword = keyword.trim();
      // 使用原生MongoDB正则表达式语法
      const regexPattern = {
        $regex: searchKeyword,
        $options: 'i' // 不区分大小写
      };

      // 在姓名和描述字段中搜索
      whereCondition.$or = [
        { name: regexPattern },
        { description: regexPattern }
      ];
    }
    
    // 时间范围筛选
    if (dateRange && dateRange.start && dateRange.end) {
      // 直接使用前端传递的时间范围，不再额外添加时间
      whereCondition.createTime = dbCmd.gte(dateRange.start).and(dbCmd.lte(dateRange.end));
    }
    
    // 状态筛选（数据迁移完成后简化版）
    if (status && status !== 'all') {
      whereCondition.workflowStatus = status;
    }
    
    // 负责人筛选
    if (responsible && responsible !== 'all' && responsible !== '') {
      whereCondition.responsibleUserId = responsible;
    }
    
    // 执行数据库查询
    const collection = db.collection('feedback');
    const query = collection.where(whereCondition);
    
    // 排序
    const orderBy = sortOrder === 'desc' ? 'desc' : 'asc';
    const sortedQuery = query.orderBy(sortBy, orderBy);
    
    // 分页查询
    const res = await sortedQuery
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .get();
    
    // 获取总数
    const countRes = await query.count();
    
    // 数据增强处理 - 为每个问题添加额外信息
    const enhancedList = await Promise.all(res.data.map(async (item) => {
      const dataType = 'new_workflow'; // 数据迁移完成后，所有数据都是新工作流
      const currentStatus = item.workflowStatus;
      
      // 获取状态显示信息
      const statusInfo = getStatusInfo(currentStatus);
      
      // 获取负责人信息
      let responsibleInfo = null;
      if (item.responsibleUserId) {
        try {
          const userRes = await db.collection('uni-id-users')
            .doc(item.responsibleUserId)
            .field({ nickname: true, username: true })
            .get();
          
          if (userRes.data?.[0]) {
            responsibleInfo = {
              userId: item.responsibleUserId,
              name: userRes.data[0].nickname || userRes.data[0].username || '未知用户',
              assignedTime: item.assignedTime
            };
          }
        } catch (error) {
          console.error('获取负责人信息失败:', error);
        }
      }
      
      // 计算进度百分比和时效信息
      const progress = calculateProgress(currentStatus, item);
      const timing = calculateTiming(item);
      
      // 使用新工作流的备注字段
      const smartRemark = item.remark || '';

      return {
        _id: item._id,
        name: item.name,
        project: item.project,
        description: item.description,
        createTime: item.createTime,
        createTimeFormatted: convertToBeijingTime(item.createTime), // 北京时间格式
        dataType,
        currentStatus,
        workflowStatus: item.workflowStatus, // 添加workflowStatus字段
        statusInfo,
        responsibleInfo,
        progress,
        timing,
        isAdopted: item.isAdopted,
        isCompleted: item.isCompleted,
        remark: smartRemark,
        // 新工作流字段 - 审核历史
        actionHistory: item.actionHistory || [],
        terminatedBy: item.terminatedBy || '', // 终止操作人
        images: item.images || [], // 图片列表
        // 为前端提供可用操作列表
        availableActions: getAvailableActions(currentStatus, dataType, permission.roles)
      };
    }));
    
    // 紧急程度筛选（在数据增强后进行，因为紧急程度是计算出来的）
    let filteredList = enhancedList;
    if (urgency && urgency !== 'all' && urgency !== '') {
      filteredList = enhancedList.filter(item => {
        return item.timing && item.timing.urgency === urgency;
      });
    }
    
    // 按时间排序（最新的在前面）
    filteredList.sort((a, b) => {
      return new Date(b.createTime) - new Date(a.createTime);
    });
    
    return {
      code: 0,
      message: '获取列表成功',
      data: {
        list: filteredList,
        userInfo: {
          roles: permission.roles,
          hasPermission: permission.hasPermission
        },
        pagination: {
          total: filteredList.length, // 使用过滤后的数量
          pageNum,
          pageSize,
          totalPages: Math.ceil(filteredList.length / pageSize),
          hasMore: (pageNum * pageSize) < filteredList.length
        },
        userPermission: permission
      }
    };
  } catch (error) {
    console.error('❌ 获取列表失败:', error);
    return {
      code: -1,
      message: error.message || '获取列表失败'
    };
  }
}

/**
 * 获取统计信息
 * 
 * 功能：
 * - 各状态数量统计
 * - 总体统计
 * - 项目分布统计
 * - 近期趋势统计
 * 
 * @param {Object} event - 事件参数
 * @param {Object} context - 上下文
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 统计信息
 */
async function getStats(event, context, userId) {
  try {
    const permission = await checkPermission(userId, 'view');
    if (!permission.hasPermission) {
      throw new Error('无权限查看统计信息');
    }

    // 统计各状态数量
    const statusStats = {};
    for (const status of Object.keys(WORKFLOW_STATUS_MAP)) {
      const count = await db.collection('feedback')
        .where({ workflowStatus: status })
        .count();
      statusStats[status] = count.total;
    }
    
    // 总体统计
    const totalCount = await db.collection('feedback').count();
    
    // 项目分布统计
    const projectStats = await db.collection('feedback')
      .aggregate()
      .group({
        _id: '$project',
        count: { $sum: 1 }
      })
      .end();
    
    // 近期趋势（最近7天）
    const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    const recentCount = await db.collection('feedback')
      .where({ createTime: dbCmd.gte(sevenDaysAgo) })
      .count();
    
    return {
      code: 0,
      message: '获取统计信息成功',
      data: {
        overview: {
          total: totalCount.total,
          recent7Days: recentCount.total
        },
        statusStats: statusStats,
        projectStats: projectStats.data,
        statusMap: WORKFLOW_STATUS_MAP
      }
    };
  } catch (error) {
    console.error('❌ 获取统计信息失败:', error);
    return {
      code: -1,
      message: error.message || '获取统计信息失败'
    };
  }
}

/**
 * 获取我的任务（负责人视角）
 * 
 * 功能：
 * - 查询指派给当前用户的任务
 * - 支持按状态筛选
 * - 计算任务统计信息
 * 
 * @param {Object} event - 事件参数
 * @param {string} event.status - 状态筛选，默认'assigned_to_responsible'
 * @param {Object} context - 上下文
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 任务列表和统计信息
 */
async function getMyTasks(event, context, userId) {
  const { status = 'assigned_to_responsible' } = event;
  
  try {
    if (!userId) {
      throw new Error('用户未登录');
    }

    // 查询所有任务统计（用于统计信息）
    const allTasksRes = await db.collection('feedback')
      .where({
        workflowStatus: dbCmd.in([
          'assigned_to_responsible', 
          'completed_by_responsible', 
          'final_completed'
        ]),
        responsibleUserId: userId
      })
      .get();
    
    let whereCondition = { responsibleUserId: userId };
    
    // 根据状态参数构建查询条件
    if (status === 'assigned_to_responsible') {
      whereCondition.workflowStatus = 'assigned_to_responsible';
    } else if (status === 'completed_by_responsible') {
      whereCondition.workflowStatus = 'completed_by_responsible';
    } else if (status === 'final_completed') {
      whereCondition.workflowStatus = 'final_completed';
    } else if (status === 'all') {
      whereCondition.workflowStatus = dbCmd.in([
        'assigned_to_responsible', 
        'completed_by_responsible', 
        'final_completed'
      ]);
    }
    
    // 执行查询
    const res = await db.collection('feedback')
      .where(whereCondition)
      .orderBy('assignedTime', 'desc')
      .get();
    
    // 数据增强处理
    const enhancedTasks = res.data.map(item => {
      const statusInfo = getStatusInfo(item.workflowStatus);
      const timing = calculateTiming(item);
      
      return {
        _id: item._id,
        name: item.name,
        project: item.project,
        description: item.description,
        createTime: item.createTime,
        assignedTime: item.assignedTime,
        completedByResponsibleTime: item.completedByResponsibleTime,
        workflowStatus: item.workflowStatus,
        currentStatus: item.workflowStatus,
        statusInfo,
        timing,
        // 任务相关字段（使用正确的字段名）
        responsibleCompletionDescription: item.responsibleCompletionDescription,
        responsibleCompletionEvidence: item.responsibleCompletionEvidence,
        finalCompletedTime: item.finalCompletedTime,
        // 指派和退回理由相关字段
        assignReason: item.assignReason,
        rejectReason: item.rejectReason,
        rejectedTime: item.rejectedTime,
        // 新工作流字段
        actionHistory: item.actionHistory || [],
        remark: item.remark || ''
      };
    });
    
    // 计算任务统计信息
    const stats = {
      assigned: allTasksRes.data.filter(item => item.workflowStatus === 'assigned_to_responsible').length,
      completed: allTasksRes.data.filter(item => item.workflowStatus === 'completed_by_responsible').length,
      confirmed: allTasksRes.data.filter(item => item.workflowStatus === 'final_completed').length
    };
    
    return {
      code: 0,
      message: '获取我的任务成功',
      data: {
        list: enhancedTasks,
        stats: stats,
        count: enhancedTasks.length
      }
    };
  } catch (error) {
    console.error('❌ 获取我的任务失败:', error);
    return {
      code: -1,
      message: error.message || '获取我的任务失败'
    };
  }
}

/**
 * 搜索问题
 * 
 * 功能：
 * - 关键词搜索
 * - 多字段匹配（姓名、描述、项目、备注）
 * - 限制返回数量
 * 
 * @param {Object} event - 事件参数
 * @param {string} event.keyword - 搜索关键词
 * @param {number} event.limit - 返回数量限制，默认10
 * @param {Object} context - 上下文
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 搜索结果
 */
async function searchFeedback(event, context, userId) {
  const { keyword, limit = 10 } = event;
  
  try {
    const permission = await checkPermission(userId, 'view');
    if (!permission.hasPermission) {
      throw new Error('无权限搜索问题');
    }

    if (!keyword || keyword.trim().length === 0) {
      return {
        code: 0,
        message: '搜索关键词为空',
        data: { results: [] }
      };
    }

    // 构建搜索条件
    const searchCondition = {
      $or: [
        { name: new RegExp(keyword, 'i') },
        { description: new RegExp(keyword, 'i') },
        { project: new RegExp(keyword, 'i') },
        { remark: new RegExp(keyword, 'i') }
      ]
    };
    
    // 执行搜索
    const res = await db.collection('feedback')
      .where(searchCondition)
      .field({
        _id: true,
        name: true,
        project: true,
        description: true,
        createTime: true,
        workflowStatus: true
      })
      .orderBy('createTime', 'desc')
      .limit(limit)
      .get();
    
    // 处理搜索结果
    const results = res.data.map(item => {
      const currentStatus = item.workflowStatus;
      
      return {
        _id: item._id,
        name: item.name,
        project: item.project,
        description: item.description.substring(0, 100) + '...',
        createTime: item.createTime,
        statusInfo: getStatusInfo(currentStatus)
      };
    });
    
    return {
      code: 0,
      message: '搜索完成',
      data: {
        results,
        count: results.length,
        keyword
      }
    };
  } catch (error) {
    console.error('❌ 搜索失败:', error);
    return {
      code: -1,
      message: error.message || '搜索失败'
    };
  }
}

/**
 * 计算工作流进度百分比
 * 
 * 根据当前状态计算整个工作流的完成进度
 * 
 * @param {string} status - 当前工作流状态
 * @param {Object} item - 问题数据项
 * @returns {number} 进度百分比（0-100）
 */
function calculateProgress(status, item) {
  const progressMap = {
    'pending_supervisor': 0,           // 待主管审核 - 0%
    'approved_supervisor': 15,         // 主管审核通过 - 15%
    'meeting_required': 12,            // 需要会议讨论 - 12%（特殊分支）
    'pending_pm': 20,                 // 待副厂长审核 - 20%
    'approved_pm': 35,                // 副厂长审核通过 - 35%
    'pending_gm': 40,                 // 待厂长审核 - 40%
    'gm_approved_pending_assign': 55, // 厂长审核通过待指派 - 55%
    'assigned_to_responsible': 70,    // 已指派负责人 - 70%
    'completed_by_responsible': 85,   // 负责人已完成 - 85%
    'final_completed': 100,           // 最终完成 - 100%
    'terminated': 0                   // 已终止 - 0%
  };
  
  return progressMap[status] || 0;
}

/**
 * 计算时效信息
 * 
 * 根据当前状态和相关时间节点计算时效信息
 * 包括：已用时间、紧急程度、是否超期等
 * 
 * 时效计算逻辑：
 * - 每个状态都有自己的时效起点（状态开始时间）
 * - 根据业务规则设置警告和紧急阈值
 * - 已完成/终止的状态显示总用时
 * 
 * @param {Object} item - 问题数据项
 * @returns {Object} 时效信息对象
 * @returns {number} daysPassed - 已过天数
 * @returns {string} description - 时效描述
 * @returns {string} urgency - 紧急程度（'normal'|'warning'|'urgent'|'completed'|'terminated'）
 * @returns {boolean} isOverdue - 是否超期
 * @returns {string} status - 当前状态
 */
function calculateTiming(item) {
  const now = Date.now();
  
  // 使用新工作流状态
  const currentStatus = item.workflowStatus;
  
  let description = '';
  let urgency = 'normal';
  let isOverdue = false;
  let relevantTime = item.createTime; // 默认使用创建时间
  
  // 时效阈值配置（天数）
  const timeThresholds = {
    pending_supervisor: { warning: 3, urgent: 7 },      // 主管审核：3天警告，7天紧急
    pending_pm: { warning: 5, urgent: 10 },             // 副厂长审核：5天警告，10天紧急
    pending_gm: { warning: 7, urgent: 14 },             // 厂长审核：7天警告，14天紧急
    assigned_to_responsible: { warning: 7, urgent: 14 }, // 负责人处理：7天警告，14天紧急
    completed_by_responsible: { warning: 2, urgent: 5 }  // 厂长确认：2天警告，5天紧急
  };

  // 从actionHistory中获取相关时间的辅助函数
  const getTimeFromActionHistory = (actionTypes) => {
    if (!item.actionHistory || !Array.isArray(item.actionHistory)) {
      return null;
    }
    
    // 查找指定类型的最新操作
    const relevantActions = item.actionHistory.filter(action => 
      actionTypes.includes(action.action)
    );
    
    if (relevantActions.length === 0) {
      return null;
    }
    
    // 返回最新的时间戳
    return Math.max(...relevantActions.map(action => action.timestamp));
  };

  // 根据状态确定相关时间起点
  switch (currentStatus) {
    case 'pending_supervisor':
      relevantTime = item.createTime; // 从创建时间开始
      break;
      
    case 'pending_pm':
      // 从主管审核时间开始（从actionHistory获取）
      const supervisorTime = getTimeFromActionHistory(['supervisor_approve', 'supervisor_meeting']);
      relevantTime = supervisorTime || item.createTime;
      break;
      
    case 'pending_gm':
      // 从副厂长审核时间开始（从actionHistory获取）
      const pmTime = getTimeFromActionHistory(['pm_approve']);
      const supervisorTimeForGM = getTimeFromActionHistory(['supervisor_approve', 'supervisor_meeting']);
      relevantTime = pmTime || supervisorTimeForGM || item.createTime;
      break;
      
    case 'assigned_to_responsible':
      // 从指派时间开始
      relevantTime = item.assignedTime || item.createTime;
      break;
      
    case 'completed_by_responsible':
      // 从负责人完成时间开始
      relevantTime = item.completedByResponsibleTime || item.assignedTime || item.createTime;
      break;
      
    case 'final_completed':
    case 'rejected_supervisor':
    case 'rejected_pm':  
    case 'rejected_gm':
    case 'terminated':
      // 已结束的状态，计算总用时
      const endTime = item.finalCompletedTime || item.terminatedTime || 
                      item.lastUpdateTime || now;
      
      // 计算总的毫秒差
      const totalMilliseconds = endTime - item.createTime;
      const totalHours = Math.floor(totalMilliseconds / (60 * 60 * 1000));
      const totalDays = Math.floor(totalMilliseconds / (24 * 60 * 60 * 1000));
      
      if (currentStatus === 'final_completed') {
        // 已完成状态：少于1天显示小时，否则显示天数
        let completedTimeDisplay;
        if (totalDays >= 1) {
          completedTimeDisplay = `${totalDays} 天`;
        } else {
          completedTimeDisplay = `${Math.max(1, totalHours)} 小时`;
        }
        
        return {
          daysPassed: totalDays,
          description: `已完成（用时 ${completedTimeDisplay}）`,
          urgency: 'completed',
          isOverdue: false,
          status: currentStatus
        };
      } else {
        // 已终止状态：少于1天显示小时，否则显示天数
        let terminatedTimeDisplay;
        if (totalDays >= 1) {
          terminatedTimeDisplay = `${totalDays} 天`;
        } else {
          terminatedTimeDisplay = `${Math.max(1, totalHours)} 小时`;
        }
        
        return {
          daysPassed: totalDays,
          description: `已终止（用时 ${terminatedTimeDisplay}）`,
          urgency: 'terminated',
          isOverdue: false,
          status: currentStatus
        };
      }
  }
  
  // 计算时间差（毫秒）
  const timeDiff = now - relevantTime;
  const hoursPassed = Math.floor(timeDiff / (60 * 60 * 1000));
  const daysPassed = Math.floor(timeDiff / (24 * 60 * 60 * 1000));
  
  // 生成人性化的时间描述
  let timeDisplay;
  if (daysPassed >= 1) {
    timeDisplay = `${daysPassed} 天`;
  } else {
    timeDisplay = `${Math.max(1, hoursPassed)} 小时`; // 至少显示1小时
  }
  
  // 根据状态定义时效含义和紧急程度
  switch (currentStatus) {
    case 'pending_supervisor':
      description = `等待主管审核 ${timeDisplay}`;
      const supervisorThreshold = timeThresholds.pending_supervisor;
      if (daysPassed > supervisorThreshold.warning) urgency = 'warning';
      if (daysPassed > supervisorThreshold.urgent) urgency = 'urgent';
      isOverdue = daysPassed > supervisorThreshold.urgent;
      break;
      
    case 'pending_pm':
      description = `等待副厂长审核 ${timeDisplay}`;
      const pmThreshold = timeThresholds.pending_pm;
      if (daysPassed > pmThreshold.warning) urgency = 'warning';
      if (daysPassed > pmThreshold.urgent) urgency = 'urgent';
      isOverdue = daysPassed > pmThreshold.urgent;
      break;
      
    case 'pending_gm':
      description = `等待厂长审核 ${timeDisplay}`;
      const gmThreshold = timeThresholds.pending_gm;
      if (daysPassed > gmThreshold.warning) urgency = 'warning';
      if (daysPassed > gmThreshold.urgent) urgency = 'urgent';
      isOverdue = daysPassed > gmThreshold.urgent;
      break;
      
    case 'assigned_to_responsible':
      description = `指派后 ${timeDisplay}`;
      const assignedThreshold = timeThresholds.assigned_to_responsible;
      if (daysPassed > assignedThreshold.warning) urgency = 'warning';
      if (daysPassed > assignedThreshold.urgent) urgency = 'urgent';
      isOverdue = daysPassed > assignedThreshold.urgent;
      break;
      
    case 'completed_by_responsible':
      description = `等待厂长确认 ${timeDisplay}`;
      const confirmThreshold = timeThresholds.completed_by_responsible;
      if (daysPassed > confirmThreshold.warning) urgency = 'warning';
      if (daysPassed > confirmThreshold.urgent) urgency = 'urgent';
      isOverdue = daysPassed > confirmThreshold.urgent;
      break;
      
    default:
      description = `提交后 ${timeDisplay}`;
      if (daysPassed > 7) urgency = 'warning';
      if (daysPassed > 14) urgency = 'urgent';
      isOverdue = daysPassed > 14;
  }
  
  return {
    daysPassed,
    description,
    urgency,
    isOverdue,
    status: currentStatus,
    relevantTime
  };
}

/**
 * 获取可用操作列表
 * 
 * 根据当前状态和用户角色，返回用户可以执行的操作列表
 * 
 * @param {string} status - 当前工作流状态
 * @param {string} dataType - 数据类型（统一为'new_workflow'）
 * @param {Array} userRoles - 用户角色列表
 * @returns {Array} 可用操作列表
 */
function getAvailableActions(status, dataType, userRoles = []) {
  const actions = [];
  
  // 基础操作：所有用户都能查看详情
  actions.push('view');
  
  // 未登录用户（guest）只能查看，无其他操作权限
  if (userRoles.includes('guest')) {
    return actions;
  }
  
  // 管理员操作
  const adminRoles = ['admin', 'supervisor', 'PM', 'GM', 'manager'];
  const isAdmin = userRoles.some(role => adminRoles.includes(role));
  
  if (isAdmin) {
    actions.push('edit');
    
    // 指派负责人：厂长审核通过后可以指派
    if (status === 'gm_approved_pending_assign' && userRoles.includes('GM')) {
      actions.push('assign_responsible');
    }
    
    // 最终确认：厂长在completed_by_responsible状态时可以确认
    if (status === 'completed_by_responsible' && userRoles.includes('GM')) {
      actions.push('final_approve');
    }
  }
  
  // 负责人操作：assigned_to_responsible状态时负责人可以提交完成
  if (status === 'assigned_to_responsible' && userRoles.includes('responsible')) {
    actions.push('submit_completion');
  }
  
  return actions;
}

/**
 * 获取负责人用户列表
 * 
 * 查询所有具有'responsible'角色的用户
 * 用于前端下拉选择器和筛选功能
 * 
 * @param {Object} event - 事件参数
 * @param {Object} context - 上下文
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 负责人用户列表
 */
async function getResponsibleUsers(event, context, userId) {
  try {
    // 权限检查 - 允许所有登录用户查看负责人列表
    if (!userId) {
      return {
        code: -1,
        message: '请先登录'
      };
    }
    
    // 查询负责人用户（根据实际的角色字段结构）
    const userRes = await db.collection('uni-id-users')
      .where(dbCmd.or([
        { role: 'responsible' },
        { role: dbCmd.in(['responsible']) },
        { 'role.0': 'responsible' }
      ]))
      .field({
        _id: true,
        nickname: true,
        username: true,
        role: true
      })
      .orderBy('nickname', 'asc')
      .get();
    
    const responsibleUsers = userRes.data || [];
    
    return {
      code: 0,
      message: '获取成功',
      data: responsibleUsers
    };
  } catch (error) {
    console.error('❌ 获取负责人用户失败:', error);
    return {
      code: -1,
      message: error.message || '获取负责人用户失败'
    };
  }
}

/**
 * 获取厂长监督任务数据
 * 包括已指派、待确认、已完成的任务列表和统计信息
 */
async function getGMSupervisionTasks(event, context, userId) {
  try {
    console.log('🔍 开始获取厂长监督任务，用户ID:', userId);

    // 暂时简化权限验证，用于调试
    console.log('📋 用户ID:', userId);

    if (!userId) {
      console.log('❌ 用户未登录');
      return {
        code: -1,
        message: '请先登录'
      };
    }

    console.log('✅ 权限检查通过（调试模式）');

    // 查询所有指派相关的任务
    console.log('📊 开始查询任务数据...');
    const taskRes = await db.collection('feedback')
      .where({
        workflowStatus: dbCmd.in([
          'assigned_to_responsible',    // 执行中
          'completed_by_responsible',   // 待确认
          'final_completed'            // 已完成
        ])
      })
      .field({
        _id: true,
        name: true,
        project: true,
        description: true,
        workflowStatus: true,
        responsibleUserId: true,
        assignedTime: true,
        completedByResponsibleTime: true,
        finalCompletedTime: true,
        createTime: true,
        assignReason: true,
        responsibleCompletionDescription: true
      })
      .orderBy('assignedTime', 'desc')
      .limit(100)
      .get();

    const taskList = taskRes.data || [];
    console.log('📋 查询到任务数量:', taskList.length);

    // 获取所有负责人信息
    const responsibleUserIds = [...new Set(taskList.map(task => task.responsibleUserId).filter(Boolean))];
    let responsibleUsers = {};

    if (responsibleUserIds.length > 0) {
      const userRes = await db.collection('uni-id-users')
        .where({
          _id: dbCmd.in(responsibleUserIds)
        })
        .field({
          _id: true,
          nickname: true,
          username: true
        })
        .get();

      // 转换为对象格式便于查找
      responsibleUsers = userRes.data.reduce((acc, user) => {
        acc[user._id] = user;
        return acc;
      }, {});
    }

    // 统计各状态任务数量
    const stats = {
      assigned: 0,    // 执行中
      pending: 0,     // 待确认
      completed: 0,   // 已完成
      overdue: 0      // 超时
    };

    const threeDays = 3 * 24 * 60 * 60 * 1000; // 3天毫秒数
    const now = Date.now();

    taskList.forEach(task => {
      switch (task.workflowStatus) {
        case 'assigned_to_responsible':
          stats.assigned++;
          // 检查是否超时（超过3天未完成）
          if (task.assignedTime && (now - task.assignedTime) > threeDays) {
            stats.overdue++;
          }
          break;
        case 'completed_by_responsible':
          stats.pending++;
          break;
        case 'final_completed':
          stats.completed++;
          break;
      }
    });

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: taskList,
        stats: stats,
        responsibleUsers: responsibleUsers
      }
    };

  } catch (error) {
    console.error('❌ 获取厂长监督任务失败:', error);
    return {
      code: -1,
      message: error.message || '获取厂长监督任务失败'
    };
  }
}
{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/ucenter_pkg/export-excel.vue?aa3e", "webpack:///D:/Xwzc/pages/ucenter_pkg/export-excel.vue?1a7d", "webpack:///D:/Xwzc/pages/ucenter_pkg/export-excel.vue?fe49", "webpack:///D:/Xwzc/pages/ucenter_pkg/export-excel.vue?1a02", "uni-app:///pages/ucenter_pkg/export-excel.vue", "webpack:///D:/Xwzc/pages/ucenter_pkg/export-excel.vue?2410", "webpack:///D:/Xwzc/pages/ucenter_pkg/export-excel.vue?fbfe"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "dataType", "filterType", "dayValue", "monthValue", "yearValue", "previewData", "dataCount", "isLoading", "exportLoading", "fetchTimer", "exportProgress", "showProgressBar", "exportTimeoutId", "MAX_EXPORT_COUNT", "hasWarnedAboutLargeData", "hasTasksWithoutRecords", "startTimeValue", "endTimeValue", "startDateValue", "endDateValue", "computed", "isLargeDataSet", "exportBtnDisabled", "hasTimeRangeSelected", "hasDateRangeSelected", "tableTitle", "onLoad", "setTimeout", "onUnload", "methods", "setDataType", "setFilterType", "onDayChange", "onMonthChange", "onYearChange", "debounceFetchData", "clearTimeout", "formatDate", "formatDateTime", "formatDateForPicker", "fetchData", "uni", "title", "mask", "db", "dbCmd", "collectionName", "whereCondition", "createTime", "where", "count", "countRes", "content", "confirmText", "cancelText", "success", "console", "showCancel", "formattedStartDate", "formattedEndDate", "patrol_date", "get", "taskResult", "normalPointCount", "missedPointCount", "notCheckedPointCount", "totalPointCount", "taskIds", "tasks", "patrolRecords", "checkin_time", "field", "recordsResult", "task", "round", "point", "fields", "orderBy", "limit", "res", "records", "_id", "tasksRes", "tasksMap", "record", "locationName", "userName", "pointsToShow", "task_id", "point_id", "status", "isPreview", "icon", "getTimeRange", "startTime", "endTime", "cleanupResources", "exportExcel", "resolve", "confirmRes", "duration", "fileName", "requestId", "exportExcelCloud", "customUI", "maxCount", "allowLargeData", "result", "isDateRange", "urlWithCache", "errorMessage", "startRealProgressUpdate", "statusText", "stageProgress", "targetProgress", "progressStage", "delay", "addCacheBuster", "openExcelPreview", "addQueryParam", "openExcelPreviewWeixin", "fs", "files", "url", "filePath", "fileType", "showMenu", "fail", "openExcelPreviewApp", "tempFile<PERSON>ath", "onStartTimeChange", "onEndTimeChange", "onStartDateChange", "onEndDateChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7EA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuK1nB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA,qCACA,6BACA,uBACA;IACA;IACA;IACAC;MACA,0CACA,uBACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAC;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAGAC;gBACAC,oBAEA;gBAAA,sBACA,yGAEA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACAC;gBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAEAJ,8BACAK,sBACAC;cAAA;gBAFAC;gBAIA;kBACA;;kBAEA;kBACA;oBACA;oBACAV;sBACAC;sBACAU;sBACAC;sBACAC;sBACAC;wBACA;0BACA;0BACA;4BACA;4BACA;4BACA;0BACA;4BACA;4BACA;4BACA;0BACA;wBACA;sBACA;oBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAf;kBACAC;kBACAU;kBACAK;gBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACA;gBACAC;gBACAC;gBAAA;gBAAA;gBAAA,OAIAf,6BACAK;kBACAW,2CACAF,qBACAb;gBACA,GACAgB;cAAA;gBANAC;gBAQA;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;gBAAA,MAEAL;kBAAA;kBAAA;gBAAA;gBACAM,gCAEA;gBACAD;kBAAA;gBAAA;;gBAEA;gBACAE;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAGAzB,+BACAK;kBACAqB;gBACA,GACAC,0BACAV;cAAA;gBALAW;gBAOA;kBACA;kBACAA;oBACA;sBACA;sBACAH;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAb;cAAA;gBAIA;gBACAY;kBACA;oBACAK;sBACA;wBACAC;0BACAR;;0BAEA;0BACA;4BACA;4BACA;8BACA;8BACAH;8BACA;8BACAY;4BACA;8BACA;8BACAX;4BACA;8BACA;8BACAC;8BACAU;4BACA;0BACA;4BACA;4BACA;8BACAZ;4BACA;8BACAC;4BACA;8BACAC;4BACA;0BACA;wBACA;sBACA;oBACA;kBACA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAT;gBACA;cAAA;gBAGA;gBACAV;gBACAC;kBACAuB;gBACA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBAAA;gBAGA;;gBAEA;kBACAM;kBACAC;gBACA;kBACAD;kBACAC;gBACA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAjC,8BACAK,sBACA4B,yBACAC,SACAP,cACAV;cAAA;gBALAkB;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAC,2BAEA;gBAAA;gBAEA;gBACAb;kBAAA;gBAAA;kBAAA;gBAAA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAvB,6BACAK;kBACAgC;gBACA,GACAV,2CACAV;cAAA;gBALAqB;gBAOAC;gBACA;kBACAD;oBACAC;kBACA;gBACA;;gBAEA;gBACA;kBACA;kBACA,uCACAC;oBACAC;oBACAC;kBAAA;gBAEA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;kBAAA,uCACAF;oBACAC;oBACAC;kBAAA;gBAAA,CACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA9B;gBACA;gBACA;kBAAA,uCACA4B;oBACAC;oBACAC;kBAAA;gBAAA,CACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAKA;gBAAA,uBACA;gBACA5B;gBACAC;gBAAA;gBAAA,OAEAf,6BACAK;kBACAW,2CACAF,sBACAb;gBACA,GACAiC;gBAAA,CACAjB;cAAA;gBAPAqB;gBASA;kBACA;kBACA;;kBAEA;kBAAA,uCACAA;kBAAA;oBAAA;sBAAA;sBACA;wBACA;wBACA;wBACA;0BACA;0BACA;;0BAEA;0BACAK;4BACA;8BACAC;8BACAC;8BACAf;8BACAW;8BACAC;8BACAhB;8BAAA;8BACAoB;8BAAA;8BACAC;4BACA;0BACA;wBACA;sBACA;oBAAA;oBAtBA;sBAAA;oBAuBA;kBAAA;oBAAA;kBAAA;oBAAA;kBAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAKAZ,8BACAK,sBACA4B,yBACAC,SACAP,cACAV;cAAA;gBALAkB;gBAOA;kBACA;gBACA;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAvB;gBACAf;kBACAC;kBACAkD;gBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAMAnD;kBACAC;kBACAkD;gBACA;gBACA;gBACA;cAAA;gBAAA;gBAEAnD;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoD;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;UACA;UACA;UACA;UAEA;UACA;UAEA;UACA;;UAEA;UACAC;UACAC;;UAEA;UACA;YACA;YACA;YACAA;UACA;UAEAvC;QACA;UACA;UACAsC;UACAC;QACA;QAEAvC;MACA;QACA;QACA;QACA;QACA;;QAEAsC;QACAC;;QAEA;QACAvC;QACAA;MACA;QACA;QACA;QACA;UACA;UACA;YAAAsC;YAAAC;UAAA;QACA;;QAEA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;;QAEA;QACAD;QACA;QACAC;;QAEA;QACA;UACA;UACA;UACAD;UACAC;;UAEA;UACA;UACA;UACA;QACA;;QAEA;QACA;QACA;QACA;UACAvC;QACA;QAEAA;MACA;QACA;QACA;QAEAsC;QACAC;QAEAvC;MACA;MAEA;QAAAsC;QAAAC;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACA5D;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA6D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAxD;kBACAC;kBACAkD;gBACA;gBAAA;cAAA;gBAAA,KAKA;kBAAA;kBAAA;gBAAA;gBACAnD;kBACAC;kBACAkD;gBACA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBAAA;gBAEAnD;kBACAC;kBACAC;gBACA;gBAEAC;gBACAC;gBAAA,sBACA,yGAEA;gBACA;gBACAa;gBACAC;gBAEA;gBAAA;gBAAA,OACAf,6BACAK;kBACAW,2CACAF,qBACAb;gBACA,GACAgB;cAAA;gBANAC;gBAQA;gBACAC;gBACAC;gBACAC;gBACAC;gBAEA;kBACAE,gCAEA;kBACAA;oBACA;sBACA;sBACAK;wBACA;0BACA;0BACAC;4BACAR;;4BAEA;4BACA;8BACA;8BACAH;4BACA;8BACA;8BACAC;4BACA;8BACA;8BACAC;4BACA;0BACA;wBACA;sBACA;oBACA;kBACA;gBACA;;gBAEA;gBACAC;;gBAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA,OACA;kBACAzB;oBACAC;oBACAU;oBACAC;oBACAC;oBACAC;sBACA2C;oBACA;kBACA;gBACA;cAAA;gBAVAC;gBAAA,IAYAA;kBAAA;kBAAA;gBAAA;gBACA1D;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAKA;gBACAA;gBACAA;kBACAC;kBACAkD;kBACAQ;gBACA;gBAAA;cAAA;gBAIA3D;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAe;gBACAf;gBACAA;kBACAC;kBACAkD;gBACA;gBAAA;cAAA;gBAAA,MAMA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA,OACA;kBACAnD;oBACAC;oBACAU;oBACAC;oBACAC;oBACAC;sBACA2C;oBACA;kBACA;gBACA;cAAA;gBAVAC;gBAAA,IAYAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAKA;gBACA;gBACA;gBACA;;gBAEA;gBACA1D;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;kBACA;oBACA;oBACA;oBACAF;oBACAA;sBACAC;sBACAU;sBACAK;oBACA;kBACA;gBACA;gBAAA;gBAGA;gBAAA,uBACA,+GAEA;gBACA4C;gBACA;kBACAA;;kBAEA;kBACA;oBACAA;kBACA;gBACA;kBACAA;gBACA;kBACA;kBACAA;gBACA;kBACAA;gBACA;gBACAA;;gBAEA;gBACA;;gBAEA;gBACAC;gBACA9C;;gBAEA;gBACA+C;kBACAC;gBACA,IAEA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAD;kBACAT;kBACAC;kBACAM;kBACAI;kBAAA;kBACAC;kBAAA;kBACAJ;gBACA;cAAA;gBAPAK;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OASAJ;kBACAT;kBACAC;kBACAM;kBACAI;kBACAC;kBACAzG;kBAAA;kBACA2G;kBAAA;kBACAN;gBACA;cAAA;gBATAK;cAAA;gBAYAnD;;gBAEA;gBACA;kBACApB;kBACA;gBACA;;gBAEA;gBACA;gBACAK;kBACAC;kBACAC;gBACA;gBAEAhB;kBACA;gBACA;;gBAEA;gBACA;kBACA;kBACAkF,sDAEA;kBACApE;kBACAA;oBACAC;oBACAkD;oBACAQ;kBACA;kBAEAzE;oBACA;kBACA;kBAEA6B;gBACA;kBACAf;;kBAEA;kBACA;oBACAA;sBACAC;sBACAU;sBACAK;oBACA;kBACA;oBACAhB;sBACAC;sBACAkD;oBACA;kBACA;kBACApC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;kBACApB;kBACA;gBACA;gBAEAoB;gBACAf;;gBAEA;gBACAqE;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEArE;kBACAC;kBACAkD;kBACAQ;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBACA5C;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAuD;MAAA;MACA;MACA;MAEA;QACA;QACA;QAEA;QACA;;QAEA;QACA;UACA;YAAA;YACAC;YACAC;YACAC;YACA;cACAC;cACAF;YACA;YACA;UAEA;YAAA;YACAD;YACAC;YACAC;YACA;cACAC;cACAF;YACA;YACA;UAEA;YAAA;YACAD;YACAC;YACAC;YACA;cACAC;cACAF;YACA;YACA;UAEA;YAAA;YACAD;YACAC;YACAC;YACA;cACAC;cACAF;YACA;YACA;UAEA;YAAA;YACAD;YACAC;YACAC;YACA;QAAA;;QAGA;QACA;UACAA;QACA;QAEA;QAEAzE;UACAC;UACAC;QACA;;QAEA;QACA;UACA;UACA;UACA;YACAyE;UACA;YACAA;UACA;YACAA;UACA;;UAEAzF;QACA;MACA;;MAEA;MACA;MACAA;IACA;IAEA;IACA0F;MACA;MACA;IACA;IAEA;IACAC;MAEA;IA0EA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA/E;QACAC;QACAC;MACA;;MAEA;MACA;QACA;QACA;;QAEA;QACA;UACA8E;UACA;UACAC;YACA;cACAD;YACA,aACA;UACA;QACA;UACA;UACA;YACAA;UACA,eACA;QACA;MACA,aACA;;MAEA;MACA;MACA;;MAEA;MACA;QACAhF;QACAA;UACAC;UACAkD;QACA;MACA;MAEAnD;QACAkF;QACAC;QAAA;QACArE;UACAnB;UACA;YACA;YACAK;cACAmF;cACAC;cACAC;cAAA;cACAvE;gBAGAd;cACA;cACAsF;gBAIAtF;gBACAA;kBACAC;kBACAkD;gBACA;cACA;YACA;UACA;YACAnD;YACAA;cACAC;cACAkD;YACA;UACA;QACA;QACAmC;UACA3F;UAIAK;UACAA;YACAC;YACAkD;UACA;QACA;MACA;IACA;IAEA;IACAoC;MACAvF;QACAC;QACAC;MACA;;MAEA;MACA;;MAEA;MACA;QACAF;QACAA;UACAC;UACAkD;QACA;MACA;MAEAnD;QACAkF;QACApE;UACAnB;UACA;YACA;;YAEA;YACAK;cACAwF;cACA1E;gBACAd;gBACAA;kBACAC;kBACAkD;gBACA;;gBAEA;gBACAnD;kBACAmF;kBACArE,6BAGA;gBACA;cACA;cACAwE;gBACAtF;gBAIAA;kBACAC;kBACAkD;gBACA;cACA;YACA;UACA;YACAnD;YACAA;cACAC;cACAkD;YACA;UACA;QACA;QACAmC;UACA3F;UAIAK;UACAA;YACAC;YACAkD;UACA;QACA;MACA;IACA;IAEA;IACAsC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxiDA;AAAA;AAAA;AAAA;AAAyqC,CAAgB,+oCAAG,EAAC,C;;;;;;;;;;;ACA7rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ucenter_pkg/export-excel.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/ucenter_pkg/export-excel.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./export-excel.vue?vue&type=template&id=9133d462&scoped=true&\"\nvar renderjs\nimport script from \"./export-excel.vue?vue&type=script&lang=js&\"\nexport * from \"./export-excel.vue?vue&type=script&lang=js&\"\nimport style0 from \"./export-excel.vue?vue&type=style&index=0&id=9133d462&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9133d462\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ucenter_pkg/export-excel.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./export-excel.vue?vue&type=template&id=9133d462&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.dataType === \"feedback\" && _vm.previewData.length > 0\n  var l0 = g0\n    ? _vm.__map(_vm.previewData, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.formatDate(item.createTime)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var g1 = _vm.dataType === \"patrol\" && _vm.previewData.length > 0\n  var l1 = g1\n    ? _vm.__map(_vm.previewData, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m1 = item.checkin_time\n          ? _vm.formatDateTime(item.checkin_time)\n          : null\n        return {\n          $orig: $orig,\n          m1: m1,\n        }\n      })\n    : null\n  var g2 = _vm.previewData.length\n  var g3 = _vm.showProgressBar ? Math.floor(_vm.exportProgress) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        l1: l1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./export-excel.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./export-excel.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<view class=\"page-header\">\n\t\t\t<text class=\"page-title\">数据导出</text>\n\t\t</view>\n\t\t\n\t\t<!-- 数据类型选择器 -->\n\t\t<view class=\"data-type-card\">\n\t\t\t<view class=\"data-type-title\">\n\t\t\t\t<uni-icons type=\"database\" size=\"20\" color=\"#007AFF\"></uni-icons>\n\t\t\t\t<text>数据类型</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"data-type-options\">\n\t\t\t\t<view class=\"data-type-option\" :class=\"{ active: dataType === 'feedback' }\" @click=\"setDataType('feedback')\">\n\t\t\t\t\t<text>找茬数据</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"data-type-option\" :class=\"{ active: dataType === 'patrol' }\" @click=\"setDataType('patrol')\">\n\t\t\t\t\t<text>巡视记录</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"filter-card\">\n\t\t\t<view class=\"filter-title\">\n\t\t\t\t<uni-icons type=\"calendar\" size=\"20\" color=\"#007AFF\"></uni-icons>\n\t\t\t\t<text>时间筛选</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"filter-options\">\n\t\t\t\t<view class=\"filter-option\" :class=\"{ active: filterType === 'day' }\" @click=\"setFilterType('day')\">\n\t\t\t\t\t<text>按日</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"filter-option\" :class=\"{ active: filterType === 'month' }\" @click=\"setFilterType('month')\">\n\t\t\t\t\t<text>按月</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"filter-option\" :class=\"{ active: filterType === 'dateRange' }\" @click=\"setFilterType('dateRange')\">\n\t\t\t\t\t<text>日期范围</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"date-picker-section\">\n\t\t\t\t<!-- 按日筛选 -->\n\t\t\t\t<view v-if=\"filterType === 'day'\" class=\"date-picker\">\n\t\t\t\t\t<picker mode=\"date\" :value=\"dayValue\" @change=\"onDayChange\">\n\t\t\t\t\t\t<view class=\"date-picker-value date-range-picker-btn\">\n\t\t\t\t\t\t\t<text>{{dayValue || '请选择日期'}}</text>\n\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#007AFF\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker>\n\t\t\t\t\t<!-- 移除可能阻止点击的悬浮层 -->\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 按月筛选 -->\n\t\t\t\t<view v-if=\"filterType === 'month'\" class=\"date-picker\">\n\t\t\t\t\t<picker mode=\"date\" fields=\"month\" :value=\"monthValue\" @change=\"onMonthChange\">\n\t\t\t\t\t\t<view class=\"date-picker-value date-range-picker-btn\">\n\t\t\t\t\t\t\t<text>{{monthValue || '请选择月份'}}</text>\n\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#007AFF\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker>\n\t\t\t\t\t<!-- 移除可能阻止点击的悬浮层 -->\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 日期范围筛选 -->\n\t\t\t\t<view v-if=\"filterType === 'dateRange'\" class=\"date-range-picker\">\n\t\t\t\t\t<view class=\"date-range-inputs\">\n\t\t\t\t\t\t<view class=\"date-input-container\">\n\t\t\t\t\t\t\t<picker mode=\"date\" :value=\"startDateValue\" @change=\"onStartDateChange\">\n\t\t\t\t\t\t\t\t<view class=\"date-picker-value date-range-picker-btn\" :class=\"{'date-range-active': startDateValue}\">\n\t\t\t\t\t\t\t\t\t<text>{{startDateValue || '开始日期'}}</text>\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#007AFF\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t<!-- 移除可能阻止点击的悬浮层 -->\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"date-separator\">至</view>\n\t\t\t\t\t\t<view class=\"date-input-container\">\n\t\t\t\t\t\t\t<picker mode=\"date\" :value=\"endDateValue\" @change=\"onEndDateChange\">\n\t\t\t\t\t\t\t\t<view class=\"date-picker-value date-range-picker-btn\" :class=\"{'date-range-active': endDateValue}\">\n\t\t\t\t\t\t\t\t\t<text>{{endDateValue || '结束日期'}}</text>\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#007AFF\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t<!-- 移除可能阻止点击的悬浮层 -->\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"preview-card\">\n\t\t\t<view class=\"preview-header\">\n\t\t\t\t<text class=\"preview-title\">数据预览</text>\n\t\t\t\t<text class=\"preview-count\" v-if=\"dataCount > 0\">共 {{dataCount}} 条数据</text>\n\t\t\t\t<text class=\"preview-count\" v-else>暂无数据</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 找茬数据预览 -->\n\t\t\t<view class=\"preview-list\" v-if=\"dataType === 'feedback' && previewData.length > 0\">\n\t\t\t\t<view class=\"preview-item\" v-for=\"(item, index) in previewData\" :key=\"index\">\n\t\t\t\t\t<view class=\"preview-item-header\">\n\t\t\t\t\t\t<text class=\"preview-item-name\">{{item.name}}</text>\n\t\t\t\t\t\t<text class=\"preview-item-time\">{{formatDate(item.createTime)}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"preview-item-desc\">{{item.description}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 巡视记录预览 -->\n\t\t\t<view class=\"preview-list\" v-if=\"dataType === 'patrol' && previewData.length > 0\">\n\t\t\t\t<view class=\"preview-item\" v-for=\"(item, index) in previewData\" :key=\"index\">\n\t\t\t\t\t<view class=\"preview-item-header\">\n\t\t\t\t\t\t<text class=\"preview-item-name\">{{item.locationName || '未知地点'}}</text>\n\t\t\t\t\t\t<text class=\"preview-item-time\" v-if=\"item.checkin_time\">{{formatDateTime(item.checkin_time)}}</text>\n\t\t\t\t\t\t<text class=\"preview-item-time missed-status\" v-else-if=\"item.status === 3 || item.status === 4\">缺卡</text>\n\t\t\t\t\t\t<text class=\"preview-item-time not-checked-status\" v-else>未打卡</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"preview-detail\">\n\t\t\t\t\t\t<text class=\"preview-detail-item\">巡检人: {{item.userName || '未知'}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<p-empty-state v-if=\"previewData.length === 0\" \n\t\t\t\ttext=\"暂无数据\"\n\t\t\t\timage=\"/static/empty/empty_data.png\">\n\t\t\t</p-empty-state>\n\t\t\t\n\t\t\t<view class=\"preview-footer\" v-if=\"dataCount > 5\">\n\t\t\t\t<text class=\"more-text\">仅显示前5条数据，导出Excel可查看全部</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 导出进度条 -->\n\t\t<view class=\"progress-card\" v-if=\"showProgressBar\">\n\t\t\t<view class=\"progress-header\">\n\t\t\t\t<text class=\"progress-title\">导出进度</text>\n\t\t\t\t<text class=\"progress-percentage\">{{Math.floor(exportProgress)}}%</text>\n\t\t\t</view>\n\t\t\t<view class=\"progress-bar-container\">\n\t\t\t\t<view class=\"progress-bar\" :style=\"{width: exportProgress + '%'}\"></view>\n\t\t\t</view>\n\t\t\t<view class=\"progress-footer\">\n\t\t\t\t<text class=\"progress-status\">{{ isLargeDataSet ? '数据量较大，请耐心等待...' : '导出中，请稍候...' }}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 网络状态提示 -->\n\t\t<!-- 移除网络检测功能 - 不必要且可能误报 -->\n\t\t<!-- <view class=\"network-warning\" v-if=\"showNetworkWarning\">\n\t\t\t<uni-icons type=\"error\" size=\"20\" color=\"#ff5a5f\"></uni-icons>\n\t\t\t<text class=\"warning-text\">检测到网络不稳定，可能会影响导出操作</text>\n\t\t</view> -->\n\t\t\n\t\t<view class=\"export-section\">\n\t\t\t<button class=\"export-btn\" :disabled=\"exportBtnDisabled\" @click=\"exportExcel\">\n\t\t\t\t<uni-icons type=\"download\" size=\"18\" :color=\"exportBtnDisabled ? '#CCCCCC' : '#FFFFFF'\"></uni-icons>\n\t\t\t\t<text>导出Excel</text>\n\t\t\t</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n\t\n\texport default {\n\t\tcomponents: {\n\t\t\tPEmptyState\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tdataType: 'feedback', // 默认导出找茬数据\n\t\t\t\tfilterType: 'day', // 默认按日筛选\n\t\t\t\tdayValue: this.formatDateForPicker(new Date()), // 默认今天\n\t\t\t\tmonthValue: this.formatDateForPicker(new Date(), 'month'), // 默认本月\n\t\t\t\tyearValue: this.formatDateForPicker(new Date(), 'year'), // 默认今年\n\t\t\t\tpreviewData: [], // 预览数据\n\t\t\t\tdataCount: 0, // 数据总数\n\t\t\t\tisLoading: false,\n\t\t\t\texportLoading: false, // 导出状态标志\n\t\t\t\tfetchTimer: null, // 用于防抖处理\n\t\t\t\texportProgress: 0, // 导出进度\n\t\t\t\tshowProgressBar: false, // 是否显示进度条\n\t\t\t\texportTimeoutId: null, // 导出超时计时器ID\n\t\t\t\tMAX_EXPORT_COUNT: 5000, // 最大导出记录数\n\t\t\t\thasWarnedAboutLargeData: false, // 是否已警告大数据量\n\t\t\t\thasTasksWithoutRecords: false, // 标记是否有任务但没有记录\n\t\t\t\tstartTimeValue: '', // 添加时间段选择\n\t\t\t\tendTimeValue: '', // 添加时间段选择\n\t\t\t\tstartDateValue: '', // 添加开始日期\n\t\t\t\tendDateValue: '' // 添加结束日期\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 判断数据量是否过大\n\t\t\tisLargeDataSet() {\n\t\t\t\treturn this.dataCount > this.MAX_EXPORT_COUNT;\n\t\t\t},\n\t\t\t// 导出按钮禁用状态\n\t\t\texportBtnDisabled() {\n\t\t\t\t// 当选择日期范围但未完成选择时禁用按钮\n\t\t\t\tif (this.filterType === 'dateRange' && (!this.startDateValue || !this.endDateValue)) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 特殊处理巡检记录导出按钮\n\t\t\t\tif (this.dataType === 'patrol') {\n\t\t\t\t\t// 只要显示有预览数据或已知数据数量大于0，就允许导出\n\t\t\t\t\treturn this.previewData.length === 0 && this.dataCount === 0 && !this.hasTasksWithoutRecords;\n\t\t\t\t}\n\t\t\t\t// 其他数据类型使用原来的禁用逻辑\n\t\t\t\treturn this.dataCount === 0 || this.exportLoading;\n\t\t\t},\n\t\t\t// 时间范围选择器的状态\n\t\t\thasTimeRangeSelected() {\n\t\t\t\treturn this.dataType === 'patrol' && \n\t\t\t\t\tthis.filterType === 'day' && \n\t\t\t\t\tthis.startTimeValue && \n\t\t\t\t\tthis.endTimeValue;\n\t\t\t},\n\t\t\t// 日期范围选择器的状态\n\t\t\thasDateRangeSelected() {\n\t\t\t\treturn this.filterType === 'dateRange' && \n\t\t\t\t\tthis.startDateValue && \n\t\t\t\t\tthis.endDateValue;\n\t\t\t},\n\t\t\t// 当前数据类型的表格标题\n\t\t\ttableTitle() {\n\t\t\t\treturn this.dataType === 'feedback' ? '找茬数据' : '巡视记录';\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t// 页面加载时获取数据，但使用延迟加载\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.fetchData();\n\t\t\t\t// 移除网络检测启动 - 功能不必要\n\t\t\t\t// this.startNetworkCheck(); // 启动网络检测\n\t\t\t}, 200);\n\t\t},\n\t\tonUnload() {\n\t\t\t// 页面卸载时清理资源\n\t\t\tthis.cleanupResources();\n\t\t},\n\t\tmethods: {\n\t\t\t// 设置数据类型\n\t\t\tsetDataType(type) {\n\t\t\t\tif (this.dataType === type) return; // 如果类型相同，不重复请求\n\t\t\t\tthis.dataType = type;\n\t\t\t\t// 重置大数据警告标志\n\t\t\t\tthis.hasWarnedAboutLargeData = false;\n\t\t\t\tthis.debounceFetchData(); // 使用防抖处理\n\t\t\t},\n\t\t\t\n\t\t\t// 设置筛选类型\n\t\t\tsetFilterType(type) {\n\t\t\t\tif (this.filterType === type) return; // 如果类型相同，不重复请求\n\t\t\t\tthis.filterType = type;\n\t\t\t\t// 重置大数据警告标志\n\t\t\t\tthis.hasWarnedAboutLargeData = false;\n\t\t\t\tthis.debounceFetchData(); // 使用防抖处理\n\t\t\t},\n\t\t\t\n\t\t\t// 日期选择器变化 - 按日\n\t\t\tonDayChange(e) {\n\t\t\t\tthis.dayValue = e.detail.value;\n\t\t\t\t// 重置大数据警告标志\n\t\t\t\tthis.hasWarnedAboutLargeData = false;\n\t\t\t\tthis.debounceFetchData(); // 使用防抖处理\n\t\t\t},\n\t\t\t\n\t\t\t// 日期选择器变化 - 按月\n\t\t\tonMonthChange(e) {\n\t\t\t\tthis.monthValue = e.detail.value;\n\t\t\t\t// 重置大数据警告标志\n\t\t\t\tthis.hasWarnedAboutLargeData = false;\n\t\t\t\tthis.debounceFetchData(); // 使用防抖处理\n\t\t\t},\n\t\t\t\n\t\t\t// 日期选择器变化 - 按年\n\t\t\tonYearChange(e) {\n\t\t\t\tthis.yearValue = e.detail.value;\n\t\t\t\t// 重置大数据警告标志\n\t\t\t\tthis.hasWarnedAboutLargeData = false;\n\t\t\t\tthis.debounceFetchData(); // 使用防抖处理\n\t\t\t},\n\t\t\t\n\t\t\t// 防抖处理函数\n\t\t\tdebounceFetchData() {\n\t\t\t\t// 清除之前的定时器\n\t\t\t\tif (this.fetchTimer) {\n\t\t\t\t\tclearTimeout(this.fetchTimer);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 设置新的定时器，300ms后执行\n\t\t\t\tthis.fetchTimer = setTimeout(() => {\n\t\t\t\t\tthis.fetchData();\n\t\t\t\t}, 300);\n\t\t\t},\n\t\t\t\n\t\t\t// 格式化日期用于显示\n\t\t\tformatDate(timestamp) {\n\t\t\t\tif (!timestamp) return '';\n\t\t\t\tconst date = new Date(timestamp);\n\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n\t\t\t},\n\t\t\t\n\t\t\t// 格式化日期和时间用于显示\n\t\t\tformatDateTime(timestamp) {\n\t\t\t\tif (!timestamp) return '';\n\t\t\t\tconst date = new Date(timestamp);\n\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n\t\t\t},\n\t\t\t\n\t\t\t// 格式化日期用于日期选择器\n\t\t\tformatDateForPicker(date, type = 'day') {\n\t\t\t\tif (!date) return '';\n\t\t\t\t\n\t\t\t\tif (type === 'day') {\n\t\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n\t\t\t\t} else if (type === 'month') {\n\t\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;\n\t\t\t\t} else if (type === 'year') {\n\t\t\t\t\treturn `${date.getFullYear()}`;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn '';\n\t\t\t},\n\t\t\t\n\t\t\t// 获取数据\n\t\t\tasync fetchData() {\n\t\t\t\tif (this.isLoading) return;\n\t\t\t\t\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中...',\n\t\t\t\t\tmask: true // 添加遮罩，防止用户重复操作\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst db = uniCloud.database();\n\t\t\t\t\tconst dbCmd = db.command;\n\t\t\t\t\t\n\t\t\t\t\t// 根据筛选类型构建查询条件\n\t\t\t\t\tconst { startTime, endTime } = this.getTimeRange();\n\t\t\t\t\t\n\t\t\t\t\t// 根据数据类型选择集合和查询条件\n\t\t\t\t\tlet collectionName, whereCondition;\n\t\t\t\t\t\n\t\t\t\t\tif (this.dataType === 'feedback') {\n\t\t\t\t\t\tcollectionName = 'feedback';\n\t\t\t\t\t\twhereCondition = {\n\t\t\t\t\t\t\tcreateTime: dbCmd.gte(startTime).and(dbCmd.lt(endTime))\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 先获取总数 - 使用try-catch单独包装，避免影响整体功能\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst countRes = await db.collection(collectionName)\n\t\t\t\t\t\t\t\t.where(whereCondition)\n\t\t\t\t\t\t\t\t.count();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (countRes.result && countRes.result.total !== undefined) {\n\t\t\t\t\t\t\t\tthis.dataCount = countRes.result.total;\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 数据量大时提示用户\n\t\t\t\t\t\t\t\tif (this.isLargeDataSet && !this.hasWarnedAboutLargeData) {\n\t\t\t\t\t\t\t\t\tthis.hasWarnedAboutLargeData = true;\n\t\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\t\ttitle: '数据量提示',\n\t\t\t\t\t\t\t\t\t\tcontent: `当前筛选条件下共有 ${this.dataCount} 条记录，超过推荐导出数量(${this.MAX_EXPORT_COUNT})，导出可能需要较长时间，建议缩小筛选范围。`,\n\t\t\t\t\t\t\t\t\t\tconfirmText: '继续',\n\t\t\t\t\t\t\t\t\t\tcancelText: '知道了',\n\t\t\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\t\t\tif (!res.confirm) {\n\t\t\t\t\t\t\t\t\t\t\t\t// 用户取消，可以在这里自动切换到更小的时间范围\n\t\t\t\t\t\t\t\t\t\t\t\tif (this.filterType === 'year') {\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 年太大，切换到月\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.filterType = 'month';\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.debounceFetchData();\n\t\t\t\t\t\t\t\t\t\t\t\t} else if (this.filterType === 'month') {\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 月太大，切换到日\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.filterType = 'day';\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.debounceFetchData();\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.dataCount = 0;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.error('获取数据数量失败:', e);\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '数据获取错误',\n\t\t\t\t\t\t\t\tcontent: `获取${this.tableTitle}数量失败: ${e.message || e}`,\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthis.dataCount = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 巡检记录 - 使用不同的处理逻辑\n\t\t\t\t\t\t// 从patrol-task表中获取任务\n\t\t\t\t\t\tconst formattedStartDate = this.formatDate(startTime);\n\t\t\t\t\t\tconst formattedEndDate = this.formatDate(endTime - 86400000); // 减去一天，因为endTime是下一天的0点\n\t\t\t\t\t\t\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t// 获取任务列表\n\t\t\t\t\t\t\tconst taskResult = await db.collection('patrol-task')\n\t\t\t\t\t\t\t\t.where({\n\t\t\t\t\t\t\t\t\tpatrol_date: this.filterType === 'day' ? \n\t\t\t\t\t\t\t\t\t\tformattedStartDate : \n\t\t\t\t\t\t\t\t\t\tdbCmd.gte(formattedStartDate).and(dbCmd.lte(formattedEndDate))\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.get();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 初始化计数\n\t\t\t\t\t\t\tlet normalPointCount = 0;  // 正常打卡点位数\n\t\t\t\t\t\t\tlet missedPointCount = 0;  // 缺卡点位数\n\t\t\t\t\t\t\tlet notCheckedPointCount = 0;  // 未打卡点位数\n\t\t\t\t\t\t\tlet totalPointCount = 0;  // 任务总点位数\n\t\t\t\t\t\t\tlet taskIds = [];  // 任务ID列表，用于后续查询打卡记录\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (taskResult.result && taskResult.result.data && taskResult.result.data.length > 0) {\n\t\t\t\t\t\t\t\tconst tasks = taskResult.result.data;\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 收集任务ID\n\t\t\t\t\t\t\t\ttaskIds = tasks.map(task => task._id);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 如果是跨月查询，先获取巡视记录数据用于辅助判断状态\n\t\t\t\t\t\t\t\tlet patrolRecords = {};\n\t\t\t\t\t\t\t\tif (this.filterType !== 'day' && startTime && endTime) {\n\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t// 查询该时间范围内的所有巡视记录\n\t\t\t\t\t\t\t\t\t\tconst recordsResult = await db.collection('patrol-record')\n\t\t\t\t\t\t\t\t\t\t\t.where({\n\t\t\t\t\t\t\t\t\t\t\t\tcheckin_time: dbCmd.gte(new Date(startTime)).and(dbCmd.lt(new Date(endTime)))\n\t\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\t\t.field('point_id,task_id')\n\t\t\t\t\t\t\t\t\t\t\t.get();\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\tif (recordsResult.result && recordsResult.result.data) {\n\t\t\t\t\t\t\t\t\t\t\t// 创建点位ID到记录的映射\n\t\t\t\t\t\t\t\t\t\t\trecordsResult.result.data.forEach(record => {\n\t\t\t\t\t\t\t\t\t\t\t\tif (record.point_id && record.task_id) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tconst key = `${record.task_id}_${record.point_id}`;\n\t\t\t\t\t\t\t\t\t\t\t\t\tpatrolRecords[key] = true;\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\t\t\tconsole.error('获取巡视记录数据失败:', e);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 统计所有任务的点位情况\n\t\t\t\t\t\t\t\ttasks.forEach(task => {\n\t\t\t\t\t\t\t\t\tif (task.rounds_detail && Array.isArray(task.rounds_detail)) {\n\t\t\t\t\t\t\t\t\t\ttask.rounds_detail.forEach(round => {\n\t\t\t\t\t\t\t\t\t\t\tif (round.points && round.points.length > 0) {\n\t\t\t\t\t\t\t\t\t\t\t\tround.points.forEach(point => {\n\t\t\t\t\t\t\t\t\t\t\t\t\ttotalPointCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 对于跨月查询，使用巡视记录来辅助判断状态\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (this.filterType !== 'day' && Object.keys(patrolRecords).length > 0) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst recordKey = `${task._id}_${point.point_id}`;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (patrolRecords[recordKey]) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 有记录，说明已打卡\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnormalPointCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 更新点的状态为已打卡\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpoint.status = 1;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t} else if (point.status === 3 || point.status === 4) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 本身就是缺卡状态\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmissedPointCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 其他情况视为未打卡\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnotCheckedPointCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpoint.status = 0;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 单日查询使用原有逻辑\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (point.status === 1) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnormalPointCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t} else if (point.status === 3 || point.status === 4) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmissedPointCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t} else if (point.status === 0) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnotCheckedPointCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 更新总数\n\t\t\t\t\t\t\t\tthis.dataCount = totalPointCount;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.dataCount = 0;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.error('获取巡视任务数据失败:', e);\n\t\t\t\t\t\t\tthis.dataCount = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果有任务，继续获取预览数据\n\t\t\t\t\t\tcollectionName = 'patrol-record';\n\t\t\t\t\t\twhereCondition = {\n\t\t\t\t\t\t\tcheckin_time: dbCmd.gte(new Date(startTime)).and(dbCmd.lt(new Date(endTime)))\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 只有当有数据时才获取预览\n\t\t\t\t\tif (this.dataCount > 0 || this.dataType === 'patrol') {\n\t\t\t\t\t\t// 获取预览数据（仅前5条）- 使用try-catch单独包装\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t// 根据数据类型选择字段和排序字段\n\t\t\t\t\t\t\tlet fields, orderBy;\n\t\t\t\t\t\t\tif (this.dataType === 'feedback') {\n\t\t\t\t\t\t\t\tfields = 'name,project,description,createTime';\n\t\t\t\t\t\t\t\torderBy = 'createTime';\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tfields = 'point_id,user_id,task_id,round,remark,checkin_time,photos,point_name,user_name';\n\t\t\t\t\t\t\t\torderBy = 'checkin_time';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 对于巡视记录，尝试获取实际记录和未打卡记录的混合预览\n\t\t\t\t\t\t\tif (this.dataType === 'patrol') {\n\t\t\t\t\t\t\t\t// 先查询patrol-record获取实际打卡记录\n\t\t\t\t\t\t\t\tconst res = await db.collection(collectionName)\n\t\t\t\t\t\t\t\t\t.where(whereCondition)\n\t\t\t\t\t\t\t\t\t.orderBy(orderBy, 'desc')\n\t\t\t\t\t\t\t\t\t.limit(5)\n\t\t\t\t\t\t\t\t\t.field(fields)\n\t\t\t\t\t\t\t\t\t.get();\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tif (res.result && res.result.data && res.result.data.length > 0) {\n\t\t\t\t\t\t\t\t\t// 有打卡记录\n\t\t\t\t\t\t\t\t\tconst records = res.result.data;\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 对于patrol数据，我们需要尝试获取更多任务信息\n\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t// 获取所有相关任务的ID\n\t\t\t\t\t\t\t\t\t\tconst taskIds = [...new Set(records.map(record => record.task_id).filter(id => id))];\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\tif (taskIds.length > 0) {\n\t\t\t\t\t\t\t\t\t\t\t// 从任务表获取更多信息\n\t\t\t\t\t\t\t\t\t\t\tconst tasksRes = await db.collection('patrol-task')\n\t\t\t\t\t\t\t\t\t\t\t\t.where({\n\t\t\t\t\t\t\t\t\t\t\t\t\t_id: dbCmd.in(taskIds)\n\t\t\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\t\t\t.field('_id,user_id,user_name,patrol_date')\n\t\t\t\t\t\t\t\t\t\t\t\t.get();\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\tconst tasksMap = {};\n\t\t\t\t\t\t\t\t\t\t\tif (tasksRes.result && tasksRes.result.data) {\n\t\t\t\t\t\t\t\t\t\t\t\ttasksRes.result.data.forEach(task => {\n\t\t\t\t\t\t\t\t\t\t\t\t\ttasksMap[task._id] = task;\n\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t// 使用任务信息补充记录数据\n\t\t\t\t\t\t\t\t\t\t\tthis.previewData = records.map(record => {\n\t\t\t\t\t\t\t\t\t\t\t\tconst task = record.task_id ? tasksMap[record.task_id] : null;\n\t\t\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\t\t\t...record,\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocationName: record.point_name || '未知地点',\n\t\t\t\t\t\t\t\t\t\t\t\t\tuserName: task?.user_name || record.user_name || '巡检人员'\n\t\t\t\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t// 如果没有任务ID，使用原有的记录信息\n\t\t\t\t\t\t\t\t\t\t\tthis.previewData = records.map(record => ({\n\t\t\t\t\t\t\t\t\t\t\t\t...record,\n\t\t\t\t\t\t\t\t\t\t\t\tlocationName: record.point_name || '未知地点',\n\t\t\t\t\t\t\t\t\t\t\t\tuserName: record.user_name || '巡检人员'\n\t\t\t\t\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} catch(e) {\n\t\t\t\t\t\t\t\t\t\tconsole.error('获取巡视任务信息失败:', e);\n\t\t\t\t\t\t\t\t\t\t// 失败时使用原有的记录信息\n\t\t\t\t\t\t\t\t\t\tthis.previewData = records.map(record => ({\n\t\t\t\t\t\t\t\t\t\t\t...record,\n\t\t\t\t\t\t\t\t\t\t\tlocationName: record.point_name || '未知地点',\n\t\t\t\t\t\t\t\t\t\t\tuserName: record.user_name || '巡检人员'\n\t\t\t\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t// 没有打卡记录，尝试从任务表获取\"未打卡\"的预览数据\n\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t// 获取日期范围内的任务\n\t\t\t\t\t\t\t\t\t\tconst { startTime, endTime } = this.getTimeRange();\n\t\t\t\t\t\t\t\t\t\tconst formattedStartDate = this.formatDate(startTime);\n\t\t\t\t\t\t\t\t\t\tconst formattedEndDate = this.formatDate(endTime - 86400000);\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\tconst tasksRes = await db.collection('patrol-task')\n\t\t\t\t\t\t\t\t\t\t\t.where({\n\t\t\t\t\t\t\t\t\t\t\t\tpatrol_date: this.filterType === 'day' ? \n\t\t\t\t\t\t\t\t\t\t\t\t\tformattedStartDate : \n\t\t\t\t\t\t\t\t\t\t\t\t\tdbCmd.gte(formattedStartDate).and(dbCmd.lte(formattedEndDate))\n\t\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\t\t.limit(2) // 只获取前2个任务\n\t\t\t\t\t\t\t\t\t\t\t.get();\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\tif (tasksRes.result && tasksRes.result.data && tasksRes.result.data.length > 0) {\n\t\t\t\t\t\t\t\t\t\t\t// 从任务中创建\"未打卡\"预览数据\n\t\t\t\t\t\t\t\t\t\t\tthis.previewData = [];\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t// 处理每个任务\n\t\t\t\t\t\t\t\t\t\t\tfor (const task of tasksRes.result.data) {\n\t\t\t\t\t\t\t\t\t\t\t\tif (task.rounds_detail && task.rounds_detail.length > 0) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 获取第一个轮次的前几个点\n\t\t\t\t\t\t\t\t\t\t\t\t\tconst round = task.rounds_detail[0];\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (round.points && round.points.length > 0) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 获取该轮次的前3个点位（或更少）\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst pointsToShow = round.points.slice(0, 3);\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 为每个点位创建一个预览记录\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpointsToShow.forEach(point => {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.previewData.push({\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttask_id: task._id,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpoint_id: point.point_id,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tround: round.round || 1,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlocationName: point.name || '未知地点',\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tuserName: task.user_name || '巡检人员',\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcheckin_time: null, // 没有打卡时间\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstatus: point.status || 0, // 使用点位状态或默认为0（未打卡）\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisPreview: true // 标记为预览数据，非实际打卡记录\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\tthis.previewData = [];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\t\t\tconsole.error('获取任务预览数据失败:', e);\n\t\t\t\t\t\t\t\t\t\tthis.previewData = [];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 非巡视记录，使用原有的处理逻辑\n\t\t\t\t\t\t\t\tconst res = await db.collection(collectionName)\n\t\t\t\t\t\t\t\t\t.where(whereCondition)\n\t\t\t\t\t\t\t\t\t.orderBy(orderBy, 'desc')\n\t\t\t\t\t\t\t\t\t.limit(5)\n\t\t\t\t\t\t\t\t\t.field(fields)\n\t\t\t\t\t\t\t\t\t.get();\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tif (res.result && res.result.data) {\n\t\t\t\t\t\t\t\t\tthis.previewData = res.result.data;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.previewData = [];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.error('获取预览数据失败:', e);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: `获取${this.tableTitle}预览失败`,\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthis.previewData = [];\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.previewData = [];\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\t// #ifdef DEBUG\n\t\t\t\t\tconsole.error('获取数据失败:', e);\n\t\t\t\t\t// #endif\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tthis.previewData = [];\n\t\t\t\t\tthis.dataCount = 0;\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取时间范围\n\t\t\tgetTimeRange() {\n\t\t\t\tlet startTime, endTime;\n\t\t\t\t\n\t\t\t\tif (this.filterType === 'day') {\n\t\t\t\t\t// 按日筛选 - 当天0点到次日0点\n\t\t\t\t\t// 从日期字符串中提取年月日\n\t\t\t\t\tconst dateParts = this.dayValue.split('-');\n\t\t\t\t\tconst year = parseInt(dateParts[0]);\n\t\t\t\t\tconst month = parseInt(dateParts[1]) - 1; // JS月份从0开始\n\t\t\t\t\tconst day = parseInt(dateParts[2]);\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否有时间段选择（针对巡视记录）\n\t\t\t\t\tif (this.dataType === 'patrol' && this.startTimeValue && this.endTimeValue) {\n\t\t\t\t\t\t// 如果有时间段选择，则使用指定的时间段\n\t\t\t\t\t\tconst startTimeParts = this.startTimeValue.split(':');\n\t\t\t\t\t\tconst endTimeParts = this.endTimeValue.split(':');\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst startHour = parseInt(startTimeParts[0]);\n\t\t\t\t\t\tconst startMinute = parseInt(startTimeParts[1]);\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst endHour = parseInt(endTimeParts[0]);\n\t\t\t\t\t\tconst endMinute = parseInt(endTimeParts[1]);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 创建指定时间段的日期对象\n\t\t\t\t\t\tstartTime = new Date(year, month, day, startHour, startMinute, 0).getTime();\n\t\t\t\t\t\tendTime = new Date(year, month, day, endHour, endMinute, 59).getTime();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 确保开始时间小于结束时间\n\t\t\t\t\t\tif (startTime > endTime) {\n\t\t\t\t\t\t\t// 如果用户选择了结束时间早于开始时间，我们假设是跨天的时间段\n\t\t\t\t\t\t\t// 将结束时间调整为下一天\n\t\t\t\t\t\t\tendTime = new Date(year, month, day + 1, endHour, endMinute, 59).getTime();\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log(`使用指定时间段: ${this.startTimeValue} - ${this.endTimeValue}`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 默认使用整天时间范围\n\t\t\t\t\t\tstartTime = new Date(year, month, day, 0, 0, 0).getTime();\n\t\t\t\t\t\tendTime = new Date(year, month, day + 1, 0, 0, 0).getTime();\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log(`按日筛选: ${this.dayValue}, 时间范围: ${new Date(startTime).toISOString()} - ${new Date(endTime).toISOString()}`);\n\t\t\t\t} else if (this.filterType === 'month') {\n\t\t\t\t\t// 按月筛选 - 当月1日0点到次月1日0点\n\t\t\t\t\tconst parts = this.monthValue.split('-');\n\t\t\t\t\tconst year = parseInt(parts[0]);\n\t\t\t\t\tconst month = parseInt(parts[1]) - 1; // 月份从0开始\n\t\t\t\t\t\n\t\t\t\t\tstartTime = new Date(year, month, 1, 0, 0, 0).getTime();\n\t\t\t\t\tendTime = new Date(year, month + 1, 1, 0, 0, 0).getTime();\n\t\t\t\t\t\n\t\t\t\t\t// 添加更多调试信息\n\t\t\t\t\tconsole.log(`按月筛选: ${this.monthValue}, 起始日期: ${this.formatDate(startTime)}, 结束日期: ${this.formatDate(endTime - 1)}`);\n\t\t\t\t\tconsole.log(`时间范围: ${new Date(startTime).toISOString()} - ${new Date(endTime).toISOString()}`);\n\t\t\t\t} else if (this.filterType === 'dateRange') {\n\t\t\t\t\t// 日期范围筛选 - 从起始日期0点到结束日期次日0点\n\t\t\t\t\t// 确保两个日期都已选择\n\t\t\t\t\tif (!this.startDateValue || !this.endDateValue) {\n\t\t\t\t\t\t// 如果没有完整选择日期范围，返回空时间范围\n\t\t\t\t\t\treturn { startTime: 0, endTime: 0 };\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 解析开始日期\n\t\t\t\t\tconst startDateParts = this.startDateValue.split('-');\n\t\t\t\t\tconst startYear = parseInt(startDateParts[0]);\n\t\t\t\t\tconst startMonth = parseInt(startDateParts[1]) - 1; // JS月份从0开始\n\t\t\t\t\tconst startDay = parseInt(startDateParts[2]);\n\t\t\t\t\t\n\t\t\t\t\t// 解析结束日期\n\t\t\t\t\tconst endDateParts = this.endDateValue.split('-');\n\t\t\t\t\tconst endYear = parseInt(endDateParts[0]);\n\t\t\t\t\tconst endMonth = parseInt(endDateParts[1]) - 1; // JS月份从0开始\n\t\t\t\t\tconst endDay = parseInt(endDateParts[2]);\n\t\t\t\t\t\n\t\t\t\t\t// 创建开始和结束时间戳\n\t\t\t\t\tstartTime = new Date(startYear, startMonth, startDay, 0, 0, 0).getTime();\n\t\t\t\t\t// 结束时间戳是结束日期的次日0点（为了包含整个结束日期）\n\t\t\t\t\tendTime = new Date(endYear, endMonth, endDay + 1, 0, 0, 0).getTime();\n\t\t\t\t\t\n\t\t\t\t\t// 确保开始时间小于结束时间\n\t\t\t\t\tif (startTime > endTime) {\n\t\t\t\t\t\t// 如果用户选择的开始日期晚于结束日期，交换它们\n\t\t\t\t\t\tconst tempTime = startTime;\n\t\t\t\t\t\tstartTime = endTime - 24 * 60 * 60 * 1000; // 减去一天\n\t\t\t\t\t\tendTime = tempTime + 24 * 60 * 60 * 1000; // 加上一天\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 为了避免UI显示混淆，也更新日期选择器的值\n\t\t\t\t\t\tconst tempDate = this.startDateValue;\n\t\t\t\t\t\tthis.startDateValue = this.endDateValue;\n\t\t\t\t\t\tthis.endDateValue = tempDate;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否是跨月查询，为调试输出更多信息\n\t\t\t\t\tconst startMonthYear = `${startYear}-${startMonth+1}`;\n\t\t\t\t\tconst endMonthYear = `${endYear}-${endMonth+1}`;\n\t\t\t\t\tif (startMonthYear !== endMonthYear) {\n\t\t\t\t\t\tconsole.log(`跨月查询: ${startMonthYear} 到 ${endMonthYear}`);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log(`日期范围筛选: ${this.startDateValue} 至 ${this.endDateValue}, 时间范围: ${new Date(startTime).toISOString()} - ${new Date(endTime).toISOString()}`);\n\t\t\t\t} else if (this.filterType === 'year') {\n\t\t\t\t\t// 按年筛选 - 当年1月1日0点到次年1月1日0点\n\t\t\t\t\tconst year = parseInt(this.yearValue);\n\t\t\t\t\t\n\t\t\t\t\tstartTime = new Date(year, 0, 1, 0, 0, 0).getTime();\n\t\t\t\t\tendTime = new Date(year + 1, 0, 1, 0, 0, 0).getTime();\n\t\t\t\t\t\n\t\t\t\t\tconsole.log(`按年筛选: ${this.yearValue}, 时间范围: ${new Date(startTime).toISOString()} - ${new Date(endTime).toISOString()}`);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn { startTime, endTime };\n\t\t\t},\n\t\t\t\n\t\t\t// 清理资源方法\n\t\t\tcleanupResources() {\n\t\t\t\t// 清除所有计时器\n\t\t\t\tif (this.fetchTimer) {\n\t\t\t\t\tclearTimeout(this.fetchTimer);\n\t\t\t\t\tthis.fetchTimer = null;\n\t\t\t\t}\n\t\t\t\tif (this.exportTimeoutId) {\n\t\t\t\t\tclearTimeout(this.exportTimeoutId);\n\t\t\t\t\tthis.exportTimeoutId = null;\n\t\t\t\t}\n\t\t\t\t// 移除网络检测定时器清理 - 功能已移除\n\t\t\t\t// if (this.networkCheckTimer) {\n\t\t\t\t// \tclearInterval(this.networkCheckTimer);\n\t\t\t\t// \tthis.networkCheckTimer = null;\n\t\t\t\t// }\n\t\t\t},\n\t\t\t\n\t\t\t// 网络状态检测\n\t\t\t// startNetworkCheck() {\n\t\t\t// \t// 先检查一次\n\t\t\t// \tthis.checkNetworkStatus();\n\t\t\t// \t\n\t\t\t// \t// 设置定期检查（每30秒）\n\t\t\t// \tthis.networkCheckTimer = setInterval(() => {\n\t\t\t// \t\tthis.checkNetworkStatus();\n\t\t\t// \t}, 30000);\n\t\t\t// },\n\t\t\t// \n\t\t\t// // 检查网络状态\n\t\t\t// async checkNetworkStatus() {\n\t\t\t// \ttry {\n\t\t\t// \t\tuni.getNetworkType({\n\t\t\t// \t\t\tsuccess: (res) => {\n\t\t\t// \t\t\t\t// 如果网络类型是none或unknown，显示警告\n\t\t\t// \t\t\t\tif (res.networkType === 'none' || res.networkType === 'unknown') {\n\t\t\t// \t\t\t\t\tthis.showNetworkWarning = true;\n\t\t\t// \t\t\t\t} else {\n\t\t\t// \t\t\t\t\t// 测试网络连接性\n\t\t\t// \t\t\t\t\tthis.testNetworkConnection();\n\t\t\t// \t\t\t\t}\n\t\t\t// \t\t\t},\n\t\t\t// \t\t\tfail: () => {\n\t\t\t// \t\t\t\t// 获取网络状态失败，也显示警告\n\t\t\t// \t\t\t\tthis.showNetworkWarning = true;\n\t\t\t// \t\t\t}\n\t\t\t// \t\t});\n\t\t\t// \t} catch (e) {\n\t\t\t// \t\tconsole.error('检查网络状态失败:', e);\n\t\t\t// \t}\n\t\t\t// },\n\t\t\t// \n\t\t\t// // 测试网络连接\n\t\t\t// async testNetworkConnection() {\n\t\t\t// \ttry {\n\t\t\t// \t\t// 使用ping云函数或其他轻量级方法测试连接\n\t\t\t// \t\tconst db = uniCloud.database();\n\t\t\t// \t\t\n\t\t\t// \t\t// 设置超时\n\t\t\t// \t\tconst timeout = new Promise((_, reject) => {\n\t\t\t// \t\t\tsetTimeout(() => reject(new Error('网络请求超时')), 5000);\n\t\t\t// \t\t});\n\t\t\t// \t\t\n\t\t\t// \t\t// 轻量级ping请求（仅返回服务器时间）- 使用patrol-record而非feedback\n\t\t\t// \t\tconst pingRequest = db.collection('patrol-record').limit(1).field('_id').get();\n\t\t\t// \t\t\n\t\t\t// \t\t// 用Promise.race竞争，哪个先完成就用哪个结果\n\t\t\t// \t\tawait Promise.race([pingRequest, timeout]);\n\t\t\t// \t\t\n\t\t\t// \t\t// 如果能到这里，说明网络正常\n\t\t\t// \t\tthis.showNetworkWarning = false;\n\t\t\t// \t} catch (e) {\n\t\t\t// \t\tconsole.error('网络连接测试失败:', e);\n\t\t\t// \t\tthis.showNetworkWarning = true;\n\t\t\t// \t}\n\t\t\t// },\n\t\t\t\n\t\t\t// 导出Excel\n\t\t\tasync exportExcel() {\n\t\t\t\tif (this.dataCount === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '没有数据可导出',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 🔥 强化防重复点击机制\n\t\t\t\tif (this.exportLoading) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '正在导出中，请勿重复操作',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果是巡视记录，获取真实的导出数量\n\t\t\t\tif (this.dataType === 'patrol') {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: '计算导出数据...',\n\t\t\t\t\t\t\tmask: true\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst db = uniCloud.database();\n\t\t\t\t\t\tconst dbCmd = db.command;\n\t\t\t\t\t\tconst { startTime, endTime } = this.getTimeRange();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 使用patrol-task表来获取巡视任务数据量\n\t\t\t\t\t\t// 转换为日期格式，该表使用日期字符串而非时间戳\n\t\t\t\t\t\tconst formattedStartDate = this.formatDate(startTime);\n\t\t\t\t\t\tconst formattedEndDate = this.formatDate(endTime - 86400000); // 减去一天，因为endTime是下一天的0点\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 使用与index.vue相同的方式获取任务列表\n\t\t\t\t\t\tconst taskResult = await db.collection('patrol-task')\n\t\t\t\t\t\t\t.where({\n\t\t\t\t\t\t\t\tpatrol_date: this.filterType === 'day' ? \n\t\t\t\t\t\t\t\t\tformattedStartDate : \n\t\t\t\t\t\t\t\t\tdbCmd.gte(formattedStartDate).and(dbCmd.lte(formattedEndDate))\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.get();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 初始化计数\n\t\t\t\t\t\tlet normalPointCount = 0;  // 正常打卡点位数\n\t\t\t\t\t\tlet missedPointCount = 0;  // 缺卡点位数\n\t\t\t\t\t\tlet notCheckedPointCount = 0;  // 未打卡点位数\n\t\t\t\t\t\tlet totalPointCount = 0;  // 任务总点位数\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (taskResult.result && taskResult.result.data) {\n\t\t\t\t\t\t\tconst tasks = taskResult.result.data;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 统计所有任务的点位情况\n\t\t\t\t\t\t\ttasks.forEach(task => {\n\t\t\t\t\t\t\t\tif (task.rounds_detail && Array.isArray(task.rounds_detail)) {\n\t\t\t\t\t\t\t\t\t// 每个任务有多个轮次的点位\n\t\t\t\t\t\t\t\t\ttask.rounds_detail.forEach(round => {\n\t\t\t\t\t\t\t\t\t\tif (round.points && Array.isArray(round.points)) {\n\t\t\t\t\t\t\t\t\t\t\t// 统计每个点位的状态\n\t\t\t\t\t\t\t\t\t\t\tround.points.forEach(point => {\n\t\t\t\t\t\t\t\t\t\t\t\ttotalPointCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t// 根据状态分类统计\n\t\t\t\t\t\t\t\t\t\t\t\tif (point.status === 1) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 正常打卡\n\t\t\t\t\t\t\t\t\t\t\t\t\tnormalPointCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t} else if (point.status === 3 || point.status === 4) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 缺卡（状态为3或4）\n\t\t\t\t\t\t\t\t\t\t\t\t\tmissedPointCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t} else if (point.status === 0) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 未打卡（状态为0）\n\t\t\t\t\t\t\t\t\t\t\t\t\tnotCheckedPointCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 确保总数至少包含所有类型的点位数量\n\t\t\t\t\t\ttotalPointCount = Math.max(totalPointCount, normalPointCount + missedPointCount + notCheckedPointCount);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更新数据量信息 - 使用总点位数，包括未打卡和缺卡的点位\n\t\t\t\t\t\tif (totalPointCount > 0) {\n\t\t\t\t\t\t\tthis.dataCount = totalPointCount;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 大数据量时的警告处理\n\t\t\t\t\t\t\tif (this.isLargeDataSet && !this.hasWarnedAboutLargeData) {\n\t\t\t\t\t\t\t\tthis.hasWarnedAboutLargeData = true;\n\t\t\t\t\t\t\t\tconst confirmRes = await new Promise((resolve) => {\n\t\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\t\ttitle: '数据量较大',\n\t\t\t\t\t\t\t\t\t\tcontent: `当前筛选条件下共有 ${this.dataCount} 个点位记录(包括 ${normalPointCount} 个正常打卡, ${missedPointCount} 个缺卡, ${notCheckedPointCount} 个未打卡)，导出可能需要较长时间，确定继续吗？`,\n\t\t\t\t\t\t\t\t\t\tconfirmText: '继续导出',\n\t\t\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\t\t\tresolve(res.confirm);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tif (!confirmRes) {\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\treturn; // 用户取消导出\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果没有点位数据，显示一个更友好的提示\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '当前时间范围内没有巡视点位数据',\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn; // 终止导出\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('获取巡视任务点位数量失败:', e);\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '获取巡视点位数据失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn; // 出现错误时终止导出\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 大数据量时确认是否继续\n\t\t\t\tif (this.isLargeDataSet && !this.hasWarnedAboutLargeData) {\n\t\t\t\t\tthis.hasWarnedAboutLargeData = true;\n\t\t\t\t\tconst confirmRes = await new Promise((resolve) => {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '数据量较大',\n\t\t\t\t\t\t\tcontent: `当前筛选条件下共有 ${this.dataCount} 条记录，超过推荐导出数量(${this.MAX_EXPORT_COUNT})，导出可能需要较长时间，确定继续吗？`,\n\t\t\t\t\t\t\tconfirmText: '继续导出',\n\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tresolve(res.confirm);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (!confirmRes) {\n\t\t\t\t\t\treturn; // 用户取消导出\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 🔥 设置导出状态，防止重复提交\n\t\t\t\tthis.exportLoading = true;\n\t\t\t\tthis.showProgressBar = true;\n\t\t\t\tthis.exportProgress = 0;\n\t\t\t\t\n\t\t\t\t// 显示初始加载提示\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '准备导出...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 🔥 延长超时时间到5分钟，避免误判\n\t\t\t\tthis.exportTimeoutId = setTimeout(() => {\n\t\t\t\t\tif (this.exportLoading) {\n\t\t\t\t\t\tthis.exportLoading = false;\n\t\t\t\t\t\tthis.showProgressBar = false;\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '导出超时',\n\t\t\t\t\t\t\tcontent: '导出操作耗时过长，可能是数据量太大导致。请尝试缩小时间范围后重试。',\n\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}, 300000); // 🔥 改为5分钟超时\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 获取时间范围\n\t\t\t\t\tconst { startTime, endTime } = this.getTimeRange();\n\t\t\t\t\t\n\t\t\t\t\t// 生成文件名\n\t\t\t\t\tlet fileName = this.tableTitle + '_';\n\t\t\t\t\tif (this.filterType === 'day') {\n\t\t\t\t\t\tfileName += this.dayValue;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果是巡视记录且选择了时间段，将时间段添加到文件名\n\t\t\t\t\t\tif (this.hasTimeRangeSelected) {\n\t\t\t\t\t\t\tfileName += `_${this.startTimeValue.replace(':', '')}至${this.endTimeValue.replace(':', '')}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (this.filterType === 'month') {\n\t\t\t\t\t\tfileName += this.monthValue;\n\t\t\t\t\t} else if (this.filterType === 'dateRange') {\n\t\t\t\t\t\t// 对于日期范围，在文件名中包含起止日期\n\t\t\t\t\t\tfileName += `${this.startDateValue}至${this.endDateValue}`;\n\t\t\t\t\t} else if (this.filterType === 'year') {\n\t\t\t\t\t\tfileName += this.yearValue;\n\t\t\t\t\t}\n\t\t\t\t\tfileName += '.xlsx';\n\t\t\t\t\t\n\t\t\t\t\t// 🔥 使用真实的分阶段导出进度\n\t\t\t\t\tthis.startRealProgressUpdate();\n\t\t\t\t\t\n\t\t\t\t\t// 🔥 增加请求唯一标识，防止重复请求\n\t\t\t\t\tconst requestId = `export_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;\n\t\t\t\t\tconsole.log(`🚀 开始导出请求: ${requestId}`);\n\t\t\t\t\t\n\t\t\t\t\t// 调用云函数导出Excel，传递最大数量参数\n\t\t\t\t\tconst exportExcelCloud = uniCloud.importObject('export-excel', {\n\t\t\t\t\t\tcustomUI: true  // 使用自定义UI\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 根据不同的数据类型调用不同的方法\n\t\t\t\t\tlet result;\n\t\t\t\t\tif (this.dataType === 'feedback') {\n\t\t\t\t\t\tresult = await exportExcelCloud.exportFeedbackData({\n\t\t\t\t\t\t\tstartTime,\n\t\t\t\t\t\t\tendTime,\n\t\t\t\t\t\t\tfileName,\n\t\t\t\t\t\t\tmaxCount: this.MAX_EXPORT_COUNT * 2, // 给一定冗余，防止极端情况\n\t\t\t\t\t\t\tallowLargeData: this.isLargeDataSet, // 告知后端这是大数据量导出\n\t\t\t\t\t\t\trequestId // 🔥 添加请求ID\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult = await exportExcelCloud.exportPatrolData({\n\t\t\t\t\t\t\tstartTime,\n\t\t\t\t\t\t\tendTime,\n\t\t\t\t\t\t\tfileName,\n\t\t\t\t\t\t\tmaxCount: this.MAX_EXPORT_COUNT * 2,\n\t\t\t\t\t\t\tallowLargeData: this.isLargeDataSet,\n\t\t\t\t\t\t\tfilterType: this.filterType, // 传递筛选类型\n\t\t\t\t\t\t\tisDateRange: this.filterType !== 'day', // 告知后端是否是日期范围查询\n\t\t\t\t\t\t\trequestId // 🔥 添加请求ID\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log(`✅ 导出请求完成: ${requestId}`, result);\n\t\t\t\t\t\n\t\t\t\t\t// 清除超时计时器\n\t\t\t\t\tif (this.exportTimeoutId) {\n\t\t\t\t\t\tclearTimeout(this.exportTimeoutId);\n\t\t\t\t\t\tthis.exportTimeoutId = null;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 完成进度到100%\n\t\t\t\t\tthis.exportProgress = 100;\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '导出完成 100%',\n\t\t\t\t\t\tmask: true\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.showProgressBar = false;\n\t\t\t\t\t}, 1000);\n\t\t\t\t\t\n\t\t\t\t\t// 🔥 优化成功判断逻辑，只看code和fileUrl\n\t\t\t\t\tif (result && result.code === 0 && result.fileUrl) {\n\t\t\t\t\t\t// 添加清除缓存参数，防止浏览器缓存\n\t\t\t\t\t\tconst urlWithCache = this.addCacheBuster(result.fileUrl);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 下载并预览文件\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '导出成功',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.openExcelPreview(urlWithCache);\n\t\t\t\t\t\t}, 500);\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log(`🎉 导出成功: ${requestId}`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 特殊处理未来日期导出的错误\n\t\t\t\t\t\tif (result && result.message && result.message.includes('未来日期')) {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '日期提示',\n\t\t\t\t\t\t\t\tcontent: '系统仅支持导出当前日期及以前的巡视数据，未来日期的数据尚未产生，无法导出。',\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: (result && result.message) || '导出失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.error(`❌ 导出失败: ${requestId}`, result);\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\t// 清除超时计时器\n\t\t\t\t\tif (this.exportTimeoutId) {\n\t\t\t\t\t\tclearTimeout(this.exportTimeoutId);\n\t\t\t\t\t\tthis.exportTimeoutId = null;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.error('导出Excel异常:', e);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\t// 🔥 优化错误提示，区分网络错误和其他错误\n\t\t\t\t\tlet errorMessage = '导出Excel失败';\n\t\t\t\t\tif (e.message && e.message.includes('timeout')) {\n\t\t\t\t\t\terrorMessage = '网络超时，请检查网络连接后重试';\n\t\t\t\t\t} else if (e.message && e.message.includes('network')) {\n\t\t\t\t\t\terrorMessage = '网络连接失败，请检查网络后重试';\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: errorMessage,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t\tthis.showProgressBar = false;\n\t\t\t\t} finally {\n\t\t\t\t\t// 🔥 确保在所有情况下都重置导出状态\n\t\t\t\t\tthis.exportLoading = false;\n\t\t\t\t\tconsole.log('🔄 导出状态已重置');\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 真实进度更新（配合云函数的处理阶段）\n\t\t\tstartRealProgressUpdate() {\n\t\t\t\tlet progressStage = 1;\n\t\t\t\tlet stageProgress = 0;\n\t\t\t\t\n\t\t\t\tconst updateProgress = () => {\n\t\t\t\t\t// 如果已经不在导出状态，停止更新\n\t\t\t\t\tif (!this.exportLoading) return;\n\t\t\t\t\t\n\t\t\t\t\tlet statusText = '导出中';\n\t\t\t\t\tlet targetProgress = 0;\n\t\t\t\t\t\n\t\t\t\t\t// 根据阶段设置进度和状态\n\t\t\t\t\tswitch(progressStage) {\n\t\t\t\t\t\tcase 1: // 查询数据阶段 (0-25%)\n\t\t\t\t\t\t\tstatusText = '查询数据中';\n\t\t\t\t\t\t\tstageProgress += 5 + Math.random() * 5; // 每次增加5-10%\n\t\t\t\t\t\t\ttargetProgress = Math.min(25, stageProgress);\n\t\t\t\t\t\t\tif (targetProgress >= 25) {\n\t\t\t\t\t\t\t\tprogressStage = 2;\n\t\t\t\t\t\t\t\tstageProgress = 25;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\tcase 2: // 处理数据阶段 (25-50%)\n\t\t\t\t\t\t\tstatusText = '处理数据中';\n\t\t\t\t\t\t\tstageProgress += 4 + Math.random() * 4; // 每次增加4-8%\n\t\t\t\t\t\t\ttargetProgress = Math.min(50, stageProgress);\n\t\t\t\t\t\t\tif (targetProgress >= 50) {\n\t\t\t\t\t\t\t\tprogressStage = 3;\n\t\t\t\t\t\t\t\tstageProgress = 50;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\tcase 3: // 生成Excel阶段 (50-80%)\n\t\t\t\t\t\t\tstatusText = '生成Excel中';\n\t\t\t\t\t\t\tstageProgress += 4 + Math.random() * 4; // 每次增加4-8%\n\t\t\t\t\t\t\ttargetProgress = Math.min(80, stageProgress);\n\t\t\t\t\t\t\tif (targetProgress >= 80) {\n\t\t\t\t\t\t\t\tprogressStage = 4;\n\t\t\t\t\t\t\t\tstageProgress = 80;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\tcase 4: // 准备下载阶段 (80-92%)\n\t\t\t\t\t\t\tstatusText = '准备下载';\n\t\t\t\t\t\t\tstageProgress += 3 + Math.random() * 3; // 每次增加3-6%\n\t\t\t\t\t\t\ttargetProgress = Math.min(92, stageProgress);\n\t\t\t\t\t\t\tif (targetProgress >= 92) {\n\t\t\t\t\t\t\t\tprogressStage = 5;\n\t\t\t\t\t\t\t\tstageProgress = 92;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\tcase 5: // 即将完成阶段 (92-95%)\n\t\t\t\t\t\t\tstatusText = '即将完成';\n\t\t\t\t\t\t\tstageProgress += 0.5 + Math.random() * 0.5; // 每次增加0.5-1%\n\t\t\t\t\t\t\ttargetProgress = Math.min(95, stageProgress);\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果是大数据量，进度稍慢一些\n\t\t\t\t\tif (this.isLargeDataSet) {\n\t\t\t\t\t\ttargetProgress *= 0.95;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.exportProgress = targetProgress;\n\t\t\t\t\t\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: `${statusText} ${Math.floor(this.exportProgress)}%`,\n\t\t\t\t\t\tmask: true\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 计划下一次更新\n\t\t\t\t\tif (this.exportProgress < 95) {\n\t\t\t\t\t\t// 根据阶段调整更新频率 - 进一步缩短间隔\n\t\t\t\t\t\tlet delay;\n\t\t\t\t\t\tif (progressStage <= 2) {\n\t\t\t\t\t\t\tdelay = 200 + Math.random() * 150; // 前期200-350ms\n\t\t\t\t\t\t} else if (progressStage === 3) {\n\t\t\t\t\t\t\tdelay = 250 + Math.random() * 200; // 中期250-450ms\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tdelay = 300 + Math.random() * 250; // 后期300-550ms\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tsetTimeout(updateProgress, delay);\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 启动进度更新\n\t\t\t\tthis.exportProgress = 0;\n\t\t\t\tsetTimeout(updateProgress, 150); // 延迟150ms开始\n\t\t\t},\n\t\t\t\n\t\t\t// 添加防缓存参数到URL\n\t\t\taddCacheBuster(url) {\n\t\t\t\tconst separator = url.includes('?') ? '&' : '?';\n\t\t\t\treturn `${url}${separator}_t=${Date.now()}`;\n\t\t\t},\n\t\t\t\n\t\t\t// 打开Excel预览\n\t\t\topenExcelPreview(url) {\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tthis.openExcelPreviewWeixin(url);\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// #ifdef H5\n\t\t\t\t// H5端优化下载处理 - 使用最可靠的单一下载方法\n\t\t\t\ttry {\n\t\t\t\t\t// 确保URL包含正确的文件扩展名和MIME类型提示\n\t\t\t\t\tif (!url.toLowerCase().includes('.xlsx')) {\n\t\t\t\t\t\turl = this.addQueryParam(url, 'filename', 'export.xlsx');\n\t\t\t\t\t\turl = this.addQueryParam(url, 'content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 生成文件名 - 根据实际选择的时间范围生成\n\t\t\t\t\tlet fileName = this.tableTitle + '_';\n\t\t\t\t\tif (this.filterType === 'day') {\n\t\t\t\t\t\tfileName += this.dayValue;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果是巡视记录且选择了时间段，将时间段添加到文件名\n\t\t\t\t\t\tif (this.hasTimeRangeSelected) {\n\t\t\t\t\t\t\tfileName += `_${this.startTimeValue.replace(':', '')}至${this.endTimeValue.replace(':', '')}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (this.filterType === 'month') {\n\t\t\t\t\t\tfileName += this.monthValue;\n\t\t\t\t\t} else if (this.filterType === 'dateRange') {\n\t\t\t\t\t\t// 对于日期范围，在文件名中包含起止日期\n\t\t\t\t\t\tfileName += `${this.startDateValue}至${this.endDateValue}`;\n\t\t\t\t\t} else if (this.filterType === 'year') {\n\t\t\t\t\t\tfileName += this.yearValue;\n\t\t\t\t\t}\n\t\t\t\t\tfileName += '.xlsx';\n\t\t\t\t\t\n\t\t\t\t\t// 使用Blob API方法下载 - 最可靠的现代浏览器方法\n\t\t\t\t\tfetch(url)\n\t\t\t\t\t\t.then(response => {\n\t\t\t\t\t\t\tif (!response.ok) {\n\t\t\t\t\t\t\t\tthrow new Error('Network response was not ok');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn response.blob();\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.then(blob => {\n\t\t\t\t\t\t\t// 创建Blob URL\n\t\t\t\t\t\t\tconst blobUrl = window.URL.createObjectURL(blob);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 创建下载链接\n\t\t\t\t\t\t\tconst link = document.createElement('a');\n\t\t\t\t\t\t\tlink.style.display = 'none';\n\t\t\t\t\t\t\tlink.href = blobUrl;\n\t\t\t\t\t\t\tlink.setAttribute('download', fileName);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 触发下载\n\t\t\t\t\t\t\tdocument.body.appendChild(link);\n\t\t\t\t\t\t\tlink.click();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 清理资源\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tdocument.body.removeChild(link);\n\t\t\t\t\t\t\t\twindow.URL.revokeObjectURL(blobUrl); // 重要：释放Blob URL\n\t\t\t\t\t\t\t}, 100);\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\t\tconsole.error('下载文件失败:', error);\n\t\t\t\t\t\t\t// 如果fetch方法失败，回退到window.open\n\t\t\t\t\t\t\twindow.open(url, '_blank');\n\t\t\t\t\t\t});\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('H5端下载文件失败:', e);\n\t\t\t\t\t// 如果所有方法都失败，回退到最基本的方法\n\t\t\t\t\twindow.open(url, '_blank');\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tthis.openExcelPreviewApp(url);\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t\n\t\t\t// 添加URL参数辅助函数\n\t\t\taddQueryParam(url, name, value) {\n\t\t\t\tconst separator = url.includes('?') ? '&' : '?';\n\t\t\t\treturn `${url}${separator}${name}=${encodeURIComponent(value)}`;\n\t\t\t},\n\t\t\t\n\t\t\t// 微信小程序环境下打开Excel预览\n\t\t\topenExcelPreviewWeixin(url) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在打开...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 清除之前的临时文件\n\t\t\t\ttry {\n\t\t\t\t\tconst fs = uni.getFileSystemManager();\n\t\t\t\t\tconst tmpDir = `${wx.env.USER_DATA_PATH}/excel_tmp/`;\n\t\t\t\t\t\n\t\t\t\t\t// 检查目录是否存在，如果存在则清空\n\t\t\t\t\ttry {\n\t\t\t\t\t\tfs.accessSync(tmpDir);\n\t\t\t\t\t\tconst files = fs.readdirSync(tmpDir);\n\t\t\t\t\t\tfiles.forEach(file => {\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tfs.unlinkSync(`${tmpDir}${file}`);\n\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t// 目录不存在，创建它\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tfs.mkdirSync(tmpDir, true);\n\t\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 使用新的临时文件名\n\t\t\t\tconst tempFileName = `excel_${Date.now()}.xlsx`;\n\t\t\t\tconst tempFilePath = `${wx.env.USER_DATA_PATH}/excel_tmp/${tempFileName}`;\n\t\t\t\t\n\t\t\t\t// 设置下载超时 (30秒)\n\t\t\t\tconst downloadTimeoutId = setTimeout(() => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '下载超时，请重试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}, 30000);\n\t\t\t\t\n\t\t\t\tuni.downloadFile({\n\t\t\t\t\turl: url,\n\t\t\t\t\tfilePath: tempFilePath, // 指定保存路径\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tclearTimeout(downloadTimeoutId);\n\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\t// 打开文件预览，确保显示右上角菜单\n\t\t\t\t\t\t\tuni.openDocument({\n\t\t\t\t\t\t\t\tfilePath: res.filePath || tempFilePath,\n\t\t\t\t\t\t\t\tfileType: 'xlsx',\n\t\t\t\t\t\t\t\tshowMenu: true, // 关键参数：显示右上角菜单按钮\n\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t// #ifdef DEBUG\n\t\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\t// #ifdef DEBUG\n\t\t\t\t\t\t\t\t\tconsole.error('打开文档失败:', err);\n\t\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '打开文档失败',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '下载文件失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tclearTimeout(downloadTimeoutId);\n\t\t\t\t\t\t// #ifdef DEBUG\n\t\t\t\t\t\tconsole.error('下载文件失败:', err);\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '下载文件失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// App环境下打开Excel预览\n\t\t\topenExcelPreviewApp(url) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在下载...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 生成唯一的临时文件名\n\t\t\t\tconst tempFileName = `excel_${Date.now()}.xlsx`;\n\t\t\t\t\n\t\t\t\t// 设置下载超时 (30秒)\n\t\t\t\tconst downloadTimeoutId = setTimeout(() => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '下载超时，请重试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}, 30000);\n\t\t\t\t\n\t\t\t\tuni.downloadFile({\n\t\t\t\t\turl: url,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tclearTimeout(downloadTimeoutId);\n\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\tconst tempFilePath = res.tempFilePath;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 保存文件时使用唯一文件名\n\t\t\t\t\t\t\tuni.saveFile({\n\t\t\t\t\t\t\t\ttempFilePath: tempFilePath,\n\t\t\t\t\t\t\t\tsuccess: (saveRes) => {\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '文件已保存',\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 打开文件\n\t\t\t\t\t\t\t\t\tuni.openDocument({\n\t\t\t\t\t\t\t\t\t\tfilePath: saveRes.savedFilePath,\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t\t\t// #ifdef DEBUG\n\t\t\t\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\t// #ifdef DEBUG\n\t\t\t\t\t\t\t\t\tconsole.error('保存文件失败:', err);\n\t\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '保存文件失败',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '下载文件失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tclearTimeout(downloadTimeoutId);\n\t\t\t\t\t\t// #ifdef DEBUG\n\t\t\t\t\t\tconsole.error('下载文件失败:', err);\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '下载文件失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 处理时间段选择\n\t\t\tonStartTimeChange(e) {\n\t\t\t\tthis.startTimeValue = e.detail.value;\n\t\t\t\tthis.debounceFetchData();\n\t\t\t},\n\t\t\t\n\t\t\tonEndTimeChange(e) {\n\t\t\t\tthis.endTimeValue = e.detail.value;\n\t\t\t\tthis.debounceFetchData();\n\t\t\t},\n\t\t\t\n\t\t\t// 处理日期范围选择\n\t\t\tonStartDateChange(e) {\n\t\t\t\tthis.startDateValue = e.detail.value;\n\t\t\t\tthis.debounceFetchData();\n\t\t\t},\n\t\t\t\n\t\t\tonEndDateChange(e) {\n\t\t\t\tthis.endDateValue = e.detail.value;\n\t\t\t\tthis.debounceFetchData();\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t/* #ifndef APP-NVUE */\n\tview {\n\t\tdisplay: flex;\n\t\tbox-sizing: border-box;\n\t\tflex-direction: column;\n\t}\n\n\tpage {\n\t\tbackground-color: #f5f5f5;\n\t}\n\t/* #endif*/\n\t\n\t.container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t\tpadding: 20px;\n\t\tpadding-bottom: 50px; /* 增加底部安全距离 */\n\t\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\t}\n\t\n\t.page-header {\n\t\tpadding: 40rpx;\n\t\tbackground: linear-gradient(145deg, #3688FF, #5A9FFF);\n\t\tmargin-bottom: 40rpx;\n\t\tborder-radius: 24rpx;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(54, 136, 255, 0.15);\n\t}\n\t\n\t.page-title {\n\t\tfont-size: 40rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #FFFFFF;\n\t\tletter-spacing: 1rpx;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t/* 数据类型选择器样式 */\n\t.data-type-card {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px;\n\t\tmargin-bottom: 20px;\n\t\tbox-shadow: 0 1px 5px rgba(0, 0, 0, 0.03); /* 降低阴影深度 */\n\t\tborder: 1px solid #eaeaea; /* 更细的淡色边框 */\n\t}\n\t\n\t.data-type-title {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmargin-bottom: 15px;\n\t\t\n\t\ttext {\n\t\t\tfont-size: 16px;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: #333;\n\t\t\tmargin-left: 8px;\n\t\t}\n\t}\n\t\n\t.data-type-options {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t}\n\t\n\t.data-type-option {\n\t\tflex: 1;\n\t\theight: 40px; /* 稍微降低高度 */\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbackground-color: #f8f9fa; /* 更淡的背景色 */\n\t\tmargin-right: 10px;\n\t\tborder-radius: 8px;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\ttransition: all 0.3s ease; /* 添加过渡动画 */\n\t\tborder: 1px solid #eaeaea; /* 边框 */\n\t\t\n\t\t&:last-child {\n\t\t\tmargin-right: 0;\n\t\t}\n\t\t\n\t\t&.active {\n\t\t\tbackground-color: #007AFF;\n\t\t\tborder-color: #0057cc; /* 活动状态边框颜色 */\n\t\t\tbox-shadow: 0 2px 5px rgba(0, 122, 255, 0.2); /* 轻微阴影效果 */\n\t\t\t\n\t\t\ttext {\n\t\t\t\tcolor: #ffffff;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\t\t\t\n\t\t\t&::after {\n\t\t\t\tcontent: \"\";\n\t\t\t\tposition: absolute;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 2px; /* 细一点的指示条 */\n\t\t\t\tbackground-color: #0057cc;\n\t\t\t}\n\t\t}\n\t\t\n\t\ttext {\n\t\t\tfont-size: 14px; /* 还原字体大小 */\n\t\t\tcolor: #666;\n\t\t}\n\t\t\n\t\t&:hover {\n\t\t\tbackground-color: #f0f4ff; /* 鼠标悬停效果 */\n\t\t\ttransform: translateY(-1px); /* 轻微上浮效果 */\n\t\t}\n\t}\n\t\n\t.filter-card {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px;\n\t\tmargin-bottom: 20px;\n\t\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.filter-title {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmargin-bottom: 15px;\n\t\t\n\t\ttext {\n\t\t\tfont-size: 16px;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: #333;\n\t\t\tmargin-left: 8px;\n\t\t}\n\t}\n\t\n\t.filter-options {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tmargin-bottom: 15px;\n\t}\n\t\n\t.filter-option {\n\t\tflex: 1;\n\t\theight: 40px;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbackground-color: #f5f5f5;\n\t\tmargin-right: 10px;\n\t\tborder-radius: 8px;\n\t\t\n\t\t&:last-child {\n\t\t\tmargin-right: 0;\n\t\t}\n\t\t\n\t\t&.active {\n\t\t\tbackground-color: #007AFF;\n\t\t\t\n\t\t\ttext {\n\t\t\t\tcolor: #ffffff;\n\t\t\t}\n\t\t}\n\t\t\n\t\ttext {\n\t\t\tfont-size: 14px;\n\t\t\tcolor: #666;\n\t\t}\n\t}\n\t\n\t.date-picker-section {\n\t\tmargin-top: 10px;\n\t}\n\t\n\t.date-picker {\n\t\twidth: 100%;\n\t\tposition: relative; /* 添加相对定位，支持悬浮层 */\n\t}\n\t\n\t/* 使用date-picker-value代替这个样式 */\n\t\n\t.preview-card {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px;\n\t\tmargin-bottom: 20px;\n\t\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.preview-header {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 15px;\n\t}\n\t\n\t.preview-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.preview-count {\n\t\tfont-size: 14px;\n\t\tcolor: #007AFF;\n\t}\n\t\n\t.preview-list {\n\t\tmargin-bottom: 10px;\n\t}\n\t\n\t.preview-item {\n\t\tpadding: 15px;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t\t\n\t\t&:last-child {\n\t\t\tborder-bottom: none;\n\t\t}\n\t}\n\t\n\t.preview-item-header {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 8px;\n\t}\n\t\n\t.preview-item-name {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.preview-item-time {\n\t\tfont-size: 12px;\n\t\tcolor: #999;\n\t}\n\t\n\t.preview-item-desc {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t\tdisplay: -webkit-box;\n\t\t-webkit-box-orient: vertical;\n\t\t-webkit-line-clamp: 2;\n\t\toverflow: hidden;\n\t}\n\t\n\t.preview-detail {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmargin-top: 5px;\n\t}\n\t\n\t.preview-detail-item {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t\tmargin-top: 3px;\n\t}\n\t\n\t.empty-preview {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 30px 0;\n\t}\n\t\n\t.empty-image {\n\t\twidth: 100px;\n\t\theight: 100px;\n\t\tmargin-bottom: 15px;\n\t}\n\t\n\t.empty-text {\n\t\tfont-size: 14px;\n\t\tcolor: #999;\n\t}\n\t\n\t.preview-footer {\n\t\tmargin-top: 10px;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\t\n\t.more-text {\n\t\tfont-size: 12px;\n\t\tcolor: #999;\n\t}\n\t\n\t.export-section {\n\t\tmargin-top: 10px;\n\t\tmargin-bottom: 30px; /* 增加底部间距 */\n\t}\n\t\n\t.export-btn {\n\t\theight: 50px;\n\t\tbackground-color: #007AFF;\n\t\tcolor: #ffffff;\n\t\tborder-radius: 25px;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 16px;\n\t\tbox-shadow: 0 4px 10px rgba(0, 122, 255, 0.3);\n\t\t\n\t\t&:disabled {\n\t\t\tbackground-color: #cccccc;\n\t\t\tbox-shadow: none;\n\t\t}\n\t\t\n\t\ttext {\n\t\t\tmargin-left: 8px;\n\t\t}\n\t}\n\t\n\t.progress-card {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px;\n\t\tmargin-bottom: 20px;\n\t\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.progress-header {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 15px;\n\t}\n\t\n\t.progress-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.progress-percentage {\n\t\tfont-size: 16px;\n\t\tcolor: #007AFF;\n\t\tfont-weight: bold;\n\t}\n\t\n\t.progress-bar-container {\n\t\theight: 8px;\n\t\tbackground-color: #f0f0f0;\n\t\tborder-radius: 4px;\n\t\toverflow: hidden;\n\t\tmargin-bottom: 10px;\n\t}\n\t\n\t.progress-bar {\n\t\theight: 100%;\n\t\tbackground: linear-gradient(90deg, #007AFF, #3688FF);\n\t\tborder-radius: 4px;\n\t\ttransition: width 0.3s ease;\n\t}\n\t\n\t.progress-footer {\n\t\tmargin-top: 10px;\n\t}\n\t\n\t.progress-status {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\t\n\t/* 添加过渡效果 */\n\t.data-type-option, .filter-option {\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.preview-list {\n\t\ttransition: opacity 0.3s ease;\n\t}\n\t\n\t/* 修复按钮背景样式 */\n\t.data-type-option.active, .filter-option.active {\n\t\tposition: relative;\n\t\tbackground-color: #007AFF !important; /* 强制应用背景色 */\n\t\tborder-color: #0057cc !important;\n\t\tz-index: 1; /* 确保活动项在最上层 */\n\t}\n\t\n\t.missed-status {\n\t\tcolor: #F5222D;\n\t\tbackground-color: #FFF1F0;\n\t\tpadding: 2rpx 10rpx;\n\t\tborder-radius: 4rpx;\n\t}\n\t\n\t.not-checked-status {\n\t\tcolor: #FAAD14;\n\t\tbackground-color: #FFFBE6;\n\t\tpadding: 2rpx 10rpx;\n\t\tborder-radius: 4rpx;\n\t}\n\t\n\t.time-range-selector {\n\t\tmargin-top: 15px;\n\t\tpadding-top: 15px;\n\t\tborder-top: 1px dashed #eaeaea;\n\t}\n\t\n\t.date-range-picker {\n\t\twidth: 100%;\n\t}\n\t\n\t.date-range-inputs {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\t\n\t.date-input-container {\n\t\tflex: 1;\n\t\tposition: relative; /* 添加相对定位 */\n\t}\n\t\n\t.date-input-container picker {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tposition: relative;\n\t\tz-index: 2; /* 确保picker在最上层 */\n\t}\n\t\n\t/* 移除悬浮层样式，防止阻止点击事件 */\n\t\n\t/* 日期选择器按钮样式 */\n\t.date-picker-value {\n\t\theight: 45px;\n\t\tbackground-color: #f5f5f5;\n\t\tborder-radius: 8px;\n\t\tpadding: 0 15px;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tcursor: pointer; /* 添加手型光标 */\n\t\tposition: relative; /* 为伪元素定位 */\n\t\ttransition: all 0.2s ease; /* 平滑过渡效果 */\n\t\tborder: 1px solid transparent; /* 为悬停状态准备 */\n\t\tuser-select: none; /* 防止文本被选中 */\n\t}\n\t\n\t/* 增强可点击的视觉反馈 */\n\t.date-picker-value:hover {\n\t\tbackground-color: #e9e9e9;\n\t\tborder-color: #d1d1d1;\n\t\tbox-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t/* 日期选择器激活状态 */\n\t.date-range-active {\n\t\tbackground-color: #E1F2FF !important;\n\t\tborder-color: #b8daff !important;\n\t}\n\t\n\t.date-range-active:hover {\n\t\tbackground-color: #D0E8FF !important;\n\t\tborder-color: #99caff !important;\n\t}\n\t\n\t/* 改进日期分隔符样式 */\n\t.date-separator {\n\t\tpadding: 0 10px;\n\t\tcolor: #666;\n\t\tfont-weight: bold;\n\t\tuser-select: none; /* 防止文本被选中 */\n\t}\n\t\n\t/* 日期选择器内容样式 */\n\t.date-picker-value text {\n\t\tfont-size: 14px;\n\t\tcolor: #333;\n\t}\n\t\n\t/* 默认日期值的颜色 */\n\t.date-picker-value:not(.date-range-active) text:first-child {\n\t\tcolor: #999;\n\t}\n\t\n\t/* 日期范围选择器特殊样式 */\n\t.date-range-picker-btn {\n\t\tbackground-color: #f8f8f8;\n\t\tborder: 1px solid #e0e0e0;\n\t\tbox-shadow: 0 1px 2px rgba(0,0,0,0.05);\n\t\ttransition: all 0.2s ease;\n\t}\n\t\n\t.date-range-picker-btn:hover {\n\t\tbackground-color: #f0f0f0;\n\t\tborder-color: #ccc;\n\t\tbox-shadow: 0 2px 4px rgba(0,0,0,0.1);\n\t\ttransform: translateY(-1px);\n\t}\n\t\n\t/* 未选择日期时的提示样式 */\n\t.date-range-picker-btn text:first-child {\n\t\tcolor: #999;\n\t}\n\t\n\t/* 已选择日期时的样式 */\n\t.date-range-active text:first-child {\n\t\tcolor: #333 !important;\n\t\tfont-weight: 500;\n\t}\n\t\n\t/* 增强日期范围选择器在PC端的可点击区域 */\n\t@media screen and (min-width: 768px) {\n\t\t.date-range-picker-btn {\n\t\t\theight: 45px; /* 增加高度 */\n\t\t\tpadding: 0 20px; /* 增加内边距 */\n\t\t}\n\t\t\n\t\t/* 确保按钮在PC端有明显的鼠标指针样式 */\n\t\t.date-picker-value, .date-range-picker-btn {\n\t\t\tcursor: pointer !important;\n\t\t}\n\t\t\n\t\t/* 添加拟态效果增强视觉 */\n\t\t.date-range-picker-btn:active {\n\t\t\ttransform: translateY(1px);\n\t\t\tbox-shadow: 0 1px 1px rgba(0,0,0,0.05);\n\t\t}\n\t}\n\t\n\t/* 日期选择限制提示样式 */\n\t.date-limit-hint {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tpadding: 5px 10px;\n\t\tmargin: 5px 0;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 4px;\n\t\tborder-left: 3px solid #909399;\n\t}\n\n\t.date-limit-hint text {\n\t\tfont-size: 12px;\n\t\tcolor: #606266;\n\t\tmargin-left: 5px;\n\t}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./export-excel.vue?vue&type=style&index=0&id=9133d462&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./export-excel.vue?vue&type=style&index=0&id=9133d462&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571664663\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
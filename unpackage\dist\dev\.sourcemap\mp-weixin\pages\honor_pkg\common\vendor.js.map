{"version": 3, "sources": ["uni-app:///utils/upload-utils.js", "uni-app:///utils/image-utils.js"], "names": ["UploadUtils", "maxRetries", "retry<PERSON><PERSON><PERSON>", "chunkSize", "errorTypes", "NETWORK", "TIMEOUT", "SIZE", "FORMAT", "COMPRESS", "UPLOAD", "retryStrategies", "operation", "options", "retries", "errorType", "context", "shouldRetry", "delay", "Math", "pow", "console", "log", "withRetry", "filePath", "compress", "onProgress", "customPath", "originalSize", "uploadPath", "compressInfo", "path", "compressed", "size", "compressionRatio", "cloudPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadToCloud", "fileType", "uploadResult", "getFileInfo", "fileID", "fileInfo", "success", "url", "tempFileURL", "actualSize", "error", "Error", "message", "filePaths", "onItemProgress", "maxConcurrent", "paths", "Array", "isArray", "pathCount", "length", "results", "compressResults", "imageUtils", "batchCompressImages", "progress", "phase", "round", "current", "total", "filter", "r", "map", "result", "width", "height", "validPaths", "valid", "batchUpload", "pathGenerator", "generateHonorImagePath", "uploadResults", "uploaded", "summary", "successful", "failed", "failedCompress", "allResults", "f", "totalUploaded", "totalFailed", "useCache", "cacheTime", "cache<PERSON>ey", "getCache", "cached", "uploadPromise", "Promise", "resolve", "reject", "uploadTask", "uniCloud", "uploadFile", "cloudPathAsRealPath", "onUploadProgress", "progressEvent", "loaded", "speed", "calculateSpeed", "then", "finalResult", "fileSize", "setCache", "errMsg", "catch", "timeoutPromise", "_", "setTimeout", "timeout", "race", "now", "Date", "lastProgress", "time", "timeDiff", "loadedDiff", "speedBps", "toFixed", "key", "uni", "getStorageSync", "JSON", "parse", "data", "expire", "warn", "duration", "cache", "setStorageSync", "stringify", "autoRetry", "chunks", "i", "push", "slice", "completed", "activeUploads", "memoryUsage", "checkMemory", "index", "generateImagePath", "fileName", "chunk", "uploadPromises", "batch", "batchPromises", "idx", "all", "batchResults", "global", "gc", "failedUploads", "retryResults", "for<PERSON>ach", "retry", "findIndex", "totalSize", "reduce", "sum", "timestamp", "random", "toString", "substring", "ms", "fileIDs", "ids", "deleteFile", "fileList", "deletedCount", "status", "failedCount", "details", "getTempFileURL", "uploadUtils", "ImageUtils", "defaultConfig", "avatar", "max<PERSON><PERSON><PERSON>", "maxHeight", "quality", "maxSize", "format", "honor", "fallback", "errorMessage", "errorCode", "code", "toISOString", "type", "customConfig", "config", "compressImageMP", "handleError", "getFileInfoAsync", "getImageInfoAsync", "imageInfo", "cropParams", "min", "targetSize", "calculateCropSize", "calculateSize", "processedPath", "sourceWidth", "offsetX", "sourceHeight", "offsetY", "cropImage", "src", "cropOffset", "cropScale", "res", "tempFile<PERSON>ath", "fail", "err", "cropResult", "wxCompressImage", "firstCompress", "firstInfo", "secondCompress", "secondInfo", "compressImage", "floor", "getImageInfo", "targetWidth", "targetHeight", "scale", "cropWidth", "cropHeight", "max", "info", "bytes", "k", "sizes", "allowedFormats", "extension", "split", "pop", "toLowerCase", "includes", "progressCallback"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAKA;AAAyC;AAAA;AAAA,IAEnCA,WAAW;EACf,uBAAc;IAAA;IAAA;IACZ,IAAI,CAACC,UAAU,GAAG,CAAC,EAAC;IACpB,IAAI,CAACC,UAAU,GAAG,IAAI,EAAC;IACvB,IAAI,CAACC,SAAS,GAAG,IAAI,GAAG,IAAI,EAAC;;IAE7B;IACA,IAAI,CAACC,UAAU,GAAG;MAChBC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE,eAAe;MACxBC,IAAI,EAAE,YAAY;MAClBC,MAAM,EAAE,cAAc;MACtBC,QAAQ,EAAE,gBAAgB;MAC1BC,MAAM,EAAE;IACV,CAAC;;IAED;IACA,IAAI,CAACC,eAAe,qFACjB,IAAI,CAACP,UAAU,CAACC,OAAO,EAAG,IAAI,wDAC9B,IAAI,CAACD,UAAU,CAACE,OAAO,EAAG,IAAI,wDAC9B,IAAI,CAACF,UAAU,CAACM,MAAM,EAAG,IAAI,wDAC7B,IAAI,CAACN,UAAU,CAACK,QAAQ,EAAG,KAAK,wDAChC,IAAI,CAACL,UAAU,CAACI,MAAM,EAAG,KAAK,wDAC9B,IAAI,CAACJ,UAAU,CAACG,IAAI,EAAG,KAAK,yBAC9B;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAA;IAAA;MAAA,yFAMA,iBAAgBK,SAAS;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAEC,OAAO,2DAAG,CAAC,CAAC;gBAAA,mBAKjCA,OAAO,CAHTC,OAAO,EAAPA,OAAO,iCAAG,CAAC,0CAGTD,OAAO,CAFTE,SAAS,EAATA,SAAS,mCAAG,IAAI,CAACX,UAAU,CAACM,MAAM,0CAEhCG,OAAO,CADTG,OAAO,EAAPA,OAAO,iCAAG,CAAC,CAAC;gBAAA;gBAAA;gBAAA,OAICJ,SAAS,EAAE;cAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAElBK,WAAW,GAAG,IAAI,CAACN,eAAe,CAACI,SAAS,CAAC,IAChCD,OAAO,GAAG,IAAI,CAACb,UAAU;gBAAA,KAExCgB,WAAW;kBAAA;kBAAA;gBAAA;gBACPC,KAAK,GAAG,IAAI,CAAChB,UAAU,GAAGiB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,OAAO,CAAC,EAAC;gBAAA;gBAAA,OAC/C,IAAI,CAACI,KAAK,CAACA,KAAK,CAAC;cAAA;gBAEvBG,OAAO,CAACC,GAAG,6BAAOR,OAAO,GAAG,CAAC,cAAM;kBACjCC,SAAS,EAATA,SAAS;kBACTG,KAAK,EAALA,KAAK;kBACLF,OAAO,EAAPA;gBACF,CAAC,CAAC;gBAAA,iCAEK,IAAI,CAACO,SAAS,CAACX,SAAS,kCAC1BC,OAAO;kBACVC,OAAO,EAAEA,OAAO,GAAG;gBAAC,GACpB;cAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAKP;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;EALE;IAAA;IAAA;MAAA,4FAMA,kBAAmBU,QAAQ;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAEX,OAAO,8DAAG,CAAC,CAAC;gBAAA,oBAMnCA,OAAO,CAJTY,QAAQ,EAARA,QAAQ,kCAAG,IAAI,sBACfC,UAAU,GAGRb,OAAO,CAHTa,UAAU,EACVC,UAAU,GAERd,OAAO,CAFTc,UAAU,EACVC,YAAY,GACVf,OAAO,CADTe,YAAY;gBAAA;gBAIRC,UAAU,GAAGL,QAAQ;gBACrBM,YAAY,GAAG,IAAI,EAEvB;gBAEA;gBACAA,YAAY,GAAG;kBACbC,IAAI,EAAEP,QAAQ;kBACdQ,UAAU,EAAE,KAAK;kBACjBC,IAAI,EAAEL,YAAY;kBAClBA,YAAY,EAAEA,YAAY;kBAC1BM,gBAAgB,EAAE;gBACpB,CAAC;;gBAWD;gBACMC,SAAS,GAAGR,UAAU,IAAI,IAAI,CAACS,kBAAkB,EAAE,EAEzD;gBAAA;gBAAA,OAC2B,IAAI,CAACC,aAAa,CAACR,UAAU,EAAEM,SAAS,EAAE;kBACnET,UAAU,EAAVA,UAAU;kBACVY,QAAQ,EAAE;gBACZ,CAAC,CAAC;cAAA;gBAHIC,YAAY;gBAAA;gBAAA,OAMK,IAAI,CAACC,WAAW,CAACD,YAAY,CAACE,MAAM,CAAC;cAAA;gBAAtDC,QAAQ;gBAAA,kCAEP;kBACLC,OAAO,EAAE,IAAI;kBACbR,SAAS,EAAEI,YAAY,CAACE,MAAM;kBAC9BG,GAAG,EAAEF,QAAQ,CAACG,WAAW,IAAIN,YAAY,CAACE,MAAM;kBAAE;kBAClDR,IAAI,EAAEM,YAAY,CAACO,UAAU,sBAAIhB,YAAY,kDAAZ,cAAcG,IAAI,KAAI,CAAC;kBAAE;kBAC1DD,UAAU,EAAE,mBAAAF,YAAY,mDAAZ,eAAcE,UAAU,KAAI,KAAK;kBAC7CE,gBAAgB,EAAE,mBAAAJ,YAAY,mDAAZ,eAAcI,gBAAgB,KAAI,CAAC;kBACrDN,YAAY,EAAEA,YAAY,uBAAIE,YAAY,mDAAZ,eAAcF,YAAY,KAAI;gBAC9D,CAAC;cAAA;gBAAA;gBAAA;gBAEDP,OAAO,CAAC0B,KAAK,CAAC,SAAS,eAAQ;gBAAA,MACzB,IAAIC,KAAK,iDAAY,aAAMC,OAAO,EAAG;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAE9C;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;EALE;IAAA;IAAA;MAAA,iGAMA,kBAAwBC,SAAS;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAErC,OAAO,8DAAG,CAAC,CAAC;gBAAA,qBAMzCA,OAAO,CAJTY,QAAQ,EAARA,QAAQ,mCAAG,IAAI,uBACfC,WAAU,GAGRb,OAAO,CAHTa,UAAU,EACVyB,cAAc,GAEZtC,OAAO,CAFTsC,cAAc,0BAEZtC,OAAO,CADTuC,aAAa,EAAbA,aAAa,sCAAG,CAAC;gBAGbC,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACL,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;gBAEhE7B,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;kBAC1BkC,SAAS,EAAEH,KAAK,CAACI,MAAM;kBACvBhC,QAAQ,EAARA,QAAQ;kBACR2B,aAAa,EAAbA;gBACF,CAAC,CAAC;gBAAA,MAEEC,KAAK,CAACI,MAAM,KAAK,CAAC;kBAAA;kBAAA;gBAAA;gBAAA,kCACb;kBAAEd,OAAO,EAAE,IAAI;kBAAEe,OAAO,EAAE;gBAAG,CAAC;cAAA;gBAAA;gBAIrC;gBACIC,eAAe,GAAG,EAAE;gBAAA,KACpBlC,QAAQ;kBAAA;kBAAA;gBAAA;gBACVJ,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;gBAAA;gBAAA,OACHsC,mBAAU,CAACC,mBAAmB,CACpDR,KAAK,EACL,OAAO,EACP,UAACS,QAAQ,EAAK;kBACZ,IAAIpC,WAAU,EAAE;oBACdA,WAAU;sBACRqC,KAAK,EAAE,UAAU;sBACjBD,QAAQ,EAAE3C,IAAI,CAAC6C,KAAK,CAAEF,QAAQ,CAACG,OAAO,GAAGH,QAAQ,CAACI,KAAK,GAAI,GAAG;oBAAC,GAC5DJ,QAAQ,EACX;kBACJ;gBACF,CAAC,CACF;cAAA;gBAZDH,eAAe;gBAaftC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;kBACrB4C,KAAK,EAAEP,eAAe,CAACF,MAAM;kBAC7Bd,OAAO,EAAEgB,eAAe,CAACQ,MAAM,CAAC,UAAAC,CAAC;oBAAA,OAAIA,CAAC,CAACzB,OAAO;kBAAA,EAAC,CAACc;gBAClD,CAAC,CAAC;gBAAA;gBAAA;cAAA;gBAEFE,eAAe,GAAGN,KAAK,CAACgB,GAAG,CAAC,UAAAtC,IAAI;kBAAA,OAAK;oBACnCY,OAAO,EAAE,IAAI;oBACb2B,MAAM,EAAE;sBACNvC,IAAI,EAAJA,IAAI;sBACJC,UAAU,EAAE,KAAK;sBACjBC,IAAI,EAAE,CAAC;sBACPsC,KAAK,EAAE,CAAC;sBACRC,MAAM,EAAE,CAAC;sBACT5C,YAAY,EAAE,CAAC;sBACfM,gBAAgB,EAAE;oBACpB;kBACF,CAAC;gBAAA,CAAC,CAAC;gBACHb,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;cAAA;gBAG5B;gBACMmD,UAAU,GAAGd,eAAe,CAC/BQ,MAAM,CAAC,UAAAC,CAAC;kBAAA,OAAIA,CAAC,CAACzB,OAAO,IAAIyB,CAAC,CAACE,MAAM,IAAIF,CAAC,CAACE,MAAM,CAACvC,IAAI;gBAAA,EAAC,CACnDsC,GAAG,CAAC,UAAAD,CAAC;kBAAA,OAAIA,CAAC,CAACE,MAAM,CAACvC,IAAI;gBAAA,EAAC;gBAE1BV,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;kBACxB4C,KAAK,EAAEP,eAAe,CAACF,MAAM;kBAC7BiB,KAAK,EAAED,UAAU,CAAChB;gBACpB,CAAC,CAAC;gBAAA,MAEEgB,UAAU,CAAChB,MAAM,KAAK,CAAC;kBAAA;kBAAA;gBAAA;gBAAA,MACnB,IAAIT,KAAK,CAAC,UAAU,CAAC;cAAA;gBAG7B3B,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;gBAAA;gBAAA,OACC,IAAI,CAACqD,WAAW,CAC1CF,UAAU,EACV;kBACE/C,UAAU,EAAE,oBAACoC,QAAQ,EAAK;oBACxB,IAAIpC,WAAU,EAAE;sBACdA,WAAU;wBACRqC,KAAK,EAAE,QAAQ;wBACfD,QAAQ,EAAE3C,IAAI,CAAC6C,KAAK,CAAEF,QAAQ,CAACG,OAAO,GAAGH,QAAQ,CAACI,KAAK,GAAI,GAAG;sBAAC,GAC5DJ,QAAQ,EACX;oBACJ;kBACF,CAAC;kBACDX,cAAc,EAAdA,cAAc;kBACdC,aAAa,EAAbA,aAAa;kBACbwB,aAAa,EAAE;oBAAA,OAAM,KAAI,CAACC,sBAAsB,EAAE;kBAAA;gBACpD,CAAC,CACF;cAAA;gBAhBKC,aAAa;gBAiBnBzD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;kBACrByD,QAAQ,EAAE,0BAAAD,aAAa,CAACE,OAAO,0DAArB,sBAAuBC,UAAU,KAAI,CAAC;kBAChDC,MAAM,EAAE,2BAAAJ,aAAa,CAACE,OAAO,2DAArB,uBAAuBE,MAAM,KAAI;gBAC3C,CAAC,CAAC;;gBAEF;gBACMC,cAAc,GAAGxB,eAAe,CAACQ,MAAM,CAAC,UAAAC,CAAC;kBAAA,OAAI,CAACA,CAAC,CAACzB,OAAO;gBAAA,EAAC;gBACxDyC,UAAU,8CACXN,aAAa,CAACpB,OAAO,oCACrByB,cAAc,CAACd,GAAG,CAAC,UAAAgB,CAAC;kBAAA,OAAK;oBAC1B1C,OAAO,EAAE,KAAK;oBACdI,KAAK,EAAEsC,CAAC,CAACtC,KAAK,IAAI,QAAQ;oBAC1BvB,QAAQ,EAAE6D,CAAC,CAACtD;kBACd,CAAC;gBAAA,CAAC,CAAC;gBAAA,kCAGE;kBACLY,OAAO,EAAE,IAAI;kBACbe,OAAO,EAAE0B,UAAU;kBACnBE,aAAa,EAAER,aAAa,CAACpB,OAAO,CAACS,MAAM,CAAC,UAAAC,CAAC;oBAAA,OAAIA,CAAC,CAACzB,OAAO;kBAAA,EAAC,CAACc,MAAM;kBAClE8B,WAAW,EAAEH,UAAU,CAACjB,MAAM,CAAC,UAAAC,CAAC;oBAAA,OAAI,CAACA,CAAC,CAACzB,OAAO;kBAAA,EAAC,CAACc;gBAClD,CAAC;cAAA;gBAAA;gBAAA;gBAEDpC,OAAO,CAAC0B,KAAK,CAAC,WAAW,eAAQ;gBAAA,MAC3B,IAAIC,KAAK,6DAAc,aAAMC,OAAO,EAAG;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAEhD;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;EANE;IAAA;IAAA;MAAA,6FAOA,kBAAoBzB,QAAQ,EAAEW,SAAS;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAEtB,OAAO,8DAAG,CAAC,CAAC;gBAEjDa,UAAU,GAIRb,OAAO,CAJTa,UAAU,sBAIRb,OAAO,CAHTyB,QAAQ,EAARA,QAAQ,kCAAG,OAAO,0CAGhBzB,OAAO,CAFT2E,QAAQ,EAARA,QAAQ,kCAAG,IAAI,2CAEb3E,OAAO,CADT4E,SAAS,EAATA,SAAS,mCAAG,IAAI,GAAG,IAAI,uBAGzB;gBACMC,QAAQ,oBAAalE,QAAQ,cAAIW,SAAS,GAEhD;gBAAA,KACIqD,QAAQ;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACW,IAAI,CAACG,QAAQ,CAACD,QAAQ,CAAC;cAAA;gBAAtCE,MAAM;gBAAA,KACRA,MAAM;kBAAA;kBAAA;gBAAA;gBACRvE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEsE,MAAM,CAAC;gBAAA,kCACvBA,MAAM;cAAA;gBAAA;gBAMTC,aAAa,GAAG,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;kBAUrDC,UAAU,GAAGC,QAAQ,CAACC,UAAU,CAAC;oBAC/B3E,QAAQ,EAAEA,QAAQ;oBAClBW,SAAS,EAAEA,SAAS;oBACpBiE,mBAAmB,EAAE,IAAI;oBACzBC,gBAAgB,EAAE,0BAACC,aAAa,EAAK;sBACnC,IAAI5E,UAAU,EAAE;wBACd,IAAMoC,QAAQ,GAAG3C,IAAI,CAAC6C,KAAK,CACxBsC,aAAa,CAACC,MAAM,GAAGD,aAAa,CAACpC,KAAK,GAAI,GAAG,CACnD;wBACDxC,UAAU,CAAC;0BACT6E,MAAM,EAAED,aAAa,CAACC,MAAM;0BAC5BrC,KAAK,EAAEoC,aAAa,CAACpC,KAAK;0BAC1BJ,QAAQ,EAAEA,QAAQ;0BAClB0C,KAAK,EAAE,MAAI,CAACC,cAAc,CAACH,aAAa;wBAC1C,CAAC,CAAC;sBACJ;oBACF;kBACF,CAAC,CAAC;kBAEFL,UAAU,CACPS,IAAI,CAAC,UAAApC,MAAM,EAAI;oBACd,IAAIA,MAAM,CAAC3B,OAAO,EAAE;sBAClB,IAAMgE,WAAW,mCACZrC,MAAM;wBACTxB,UAAU,EAAEwB,MAAM,CAACsC,QAAQ,IAAItC,MAAM,CAACrC,IAAI,IAAI;sBAAC,EAChD;;sBAED;sBACA,IAAIuD,QAAQ,EAAE;wBACZ,MAAI,CAACqB,QAAQ,CAACnB,QAAQ,EAAEiB,WAAW,EAAElB,SAAS,CAAC;sBACjD;sBAEAM,OAAO,CAACY,WAAW,CAAC;oBACtB,CAAC,MAAM;sBACLX,MAAM,CAAC,IAAIhD,KAAK,CAACsB,MAAM,CAACwC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5C;kBACF,CAAC,CAAC,CACDC,KAAK,CAACf,MAAM,CAAC;gBAClB,CAAC,CAAC,EAEF;gBACMgB,cAAc,GAAG,IAAIlB,OAAO,CAAC,UAACmB,CAAC,EAAEjB,MAAM,EAAK;kBAChDkB,UAAU,CAAC,YAAM;oBACflB,MAAM,CAAC,IAAIhD,KAAK,CAAC,MAAM,CAAC,CAAC;kBAC3B,CAAC,EAAEnC,OAAO,CAACsG,OAAO,IAAI,KAAK,CAAC;gBAC9B,CAAC,CAAC;gBAAA;gBAAA,OAEW,IAAI,CAAC5F,SAAS,CACzB;kBAAA,OAAMuE,OAAO,CAACsB,IAAI,CAAC,CAACvB,aAAa,EAAEmB,cAAc,CAAC,CAAC;gBAAA,GACnD;kBACEjG,SAAS,EAAE,IAAI,CAACX,UAAU,CAACM,MAAM;kBACjCM,OAAO,EAAE;oBAAEQ,QAAQ,EAARA,QAAQ;oBAAEW,SAAS,EAATA;kBAAU;gBACjC,CAAC,CACF;cAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEDd,OAAO,CAAC0B,KAAK,CAAC,OAAO,eAAQ;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGhC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA,OAKA,wBAAeuD,aAAa,EAAE;MAC5B,IAAMe,GAAG,GAAGC,IAAI,CAACD,GAAG,EAAE;MACtB,IAAI,CAAC,IAAI,CAACE,YAAY,EAAE;QACtB,IAAI,CAACA,YAAY,GAAG;UAAEC,IAAI,EAAEH,GAAG;UAAEd,MAAM,EAAE;QAAE,CAAC;QAC5C,OAAO,QAAQ;MACjB;MAEA,IAAMkB,QAAQ,GAAGJ,GAAG,GAAG,IAAI,CAACE,YAAY,CAACC,IAAI;MAC7C,IAAME,UAAU,GAAGpB,aAAa,CAACC,MAAM,GAAG,IAAI,CAACgB,YAAY,CAAChB,MAAM;MAElE,IAAIkB,QAAQ,GAAG,CAAC,EAAE;QAChB,IAAME,QAAQ,GAAID,UAAU,GAAG,IAAI,GAAID,QAAQ;QAC/C,IAAI,CAACF,YAAY,GAAG;UAAEC,IAAI,EAAEH,GAAG;UAAEd,MAAM,EAAED,aAAa,CAACC;QAAO,CAAC;QAE/D,IAAIoB,QAAQ,GAAG,IAAI,EAAE;UACnB,iBAAUA,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC;QAC/B,CAAC,MAAM,IAAID,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAE;UACjC,iBAAU,CAACA,QAAQ,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC;QACxC,CAAC,MAAM;UACL,iBAAU,CAACD,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;QACjD;MACF;MAEA,OAAO,QAAQ;IACjB;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,wFAKA,kBAAeC,GAAG;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAERjC,MAAM,GAAGkC,GAAG,CAACC,cAAc,wBAAiBF,GAAG,EAAG;gBAAA,KACpDjC,MAAM;kBAAA;kBAAA;gBAAA;gBAAA,cACiBoC,IAAI,CAACC,KAAK,CAACrC,MAAM,CAAC,EAAnCsC,IAAI,eAAJA,IAAI,EAAEC,MAAM,eAANA,MAAM;gBAAA,MAChBA,MAAM,GAAGb,IAAI,CAACD,GAAG,EAAE;kBAAA;kBAAA;gBAAA;gBAAA,kCACda,IAAI;cAAA;gBAAA,kCAGR,IAAI;cAAA;gBAAA;gBAAA;gBAEX7G,OAAO,CAAC+G,IAAI,CAAC,WAAW,eAAQ;gBAAA,kCACzB,IAAI;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAEd;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;EALE;IAAA;IAAA,OAMA,kBAASP,GAAG,EAAEK,IAAI,EAAEG,QAAQ,EAAE;MAC5B,IAAI;QACF,IAAMC,KAAK,GAAG;UACZJ,IAAI,EAAJA,IAAI;UACJC,MAAM,EAAEb,IAAI,CAACD,GAAG,EAAE,GAAGgB;QACvB,CAAC;QACDP,GAAG,CAACS,cAAc,wBAAiBV,GAAG,GAAIG,IAAI,CAACQ,SAAS,CAACF,KAAK,CAAC,CAAC;MAClE,CAAC,CAAC,OAAOvF,KAAK,EAAE;QACd1B,OAAO,CAAC+G,IAAI,CAAC,WAAW,EAAErF,KAAK,CAAC;MAClC;IACF;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;;IA8DE;AACF;AACA;AACA;AACA;AACA;EALE;IAAA;IAAA;MAAA,2FAMA,kBAAkBG,SAAS;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAErC,OAAO,8DAAG,CAAC,CAAC;gBAErCa,UAAU,GAMRb,OAAO,CANTa,UAAU,EACVyB,cAAc,GAKZtC,OAAO,CALTsC,cAAc,2BAKZtC,OAAO,CAJTuC,aAAa,EAAbA,aAAa,uCAAG,CAAC,2BACjBwB,aAAa,GAGX/D,OAAO,CAHT+D,aAAa,uBAGX/D,OAAO,CAFTV,SAAS,EAATA,SAAS,mCAAG,IAAI,CAACA,SAAS,4CAExBU,OAAO,CADT4H,SAAS,EAATA,SAAS,mCAAG,IAAI,uBAGlB;gBACMC,MAAM,GAAG,EAAE;gBACjB,KAASC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzF,SAAS,CAACO,MAAM,EAAEkF,CAAC,IAAIxI,SAAS,EAAE;kBACpDuI,MAAM,CAACE,IAAI,CAAC1F,SAAS,CAAC2F,KAAK,CAACF,CAAC,EAAEA,CAAC,GAAGxI,SAAS,CAAC,CAAC;gBAChD;gBAEMuD,OAAO,GAAG,EAAE;gBACZQ,KAAK,GAAGhB,SAAS,CAACO,MAAM;gBAC1BqF,SAAS,GAAG,CAAC;gBACbC,aAAa,GAAG,CAAC;gBACjBC,WAAW,GAAG,CAAC,EAEnB;gBACMC,WAAW,GAAG,SAAdA,WAAW,GAAS;kBAWxB,OAAO,KAAK;gBACd,CAAC,EAED;gBACM9C,UAAU;kBAAA,mFAAG,kBAAO3E,QAAQ,EAAE0H,KAAK;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;4BAErCH,aAAa,EAAE;4BACT5G,SAAS,GAAGyC,aAAa,GAAGA,aAAa,EAAE,GAAG,MAAI,CAACuE,iBAAiB,EAAE;4BAAA;4BAAA,OAEvD,MAAI,CAAC5H,SAAS,CACjC;8BAAA,OAAM,MAAI,CAACc,aAAa,CAACb,QAAQ,EAAEW,SAAS,EAAE;gCAC5CT,UAAU,EAAE,oBAACoC,QAAQ,EAAK;kCACxB,IAAIX,cAAc,EAAE;oCAClBA,cAAc;sCACZ+F,KAAK,EAALA,KAAK;sCACLE,QAAQ,EAAE5H;oCAAQ,GACfsC,QAAQ,EACX;kCACJ;gCACF;8BACF,CAAC,CAAC;4BAAA,GACF;8BACE/C,SAAS,EAAE,MAAI,CAACX,UAAU,CAACM,MAAM;8BACjCM,OAAO,EAAE;gCAAEQ,QAAQ,EAARA,QAAQ;gCAAE0H,KAAK,EAALA;8BAAM;4BAC7B,CAAC,CACF;0BAAA;4BAhBK5E,MAAM;4BAAA;4BAAA,OAmBW,MAAI,CAAC9B,WAAW,CAAC8B,MAAM,CAAC7B,MAAM,CAAC;0BAAA;4BAAhDC,QAAQ;4BAEdoG,SAAS,EAAE;4BACX,IAAIpH,UAAU,EAAE;8BACdA,UAAU,CAAC;gCACTuC,OAAO,EAAE6E,SAAS;gCAClB5E,KAAK,EAALA,KAAK;gCACLJ,QAAQ,EAAE3C,IAAI,CAAC6C,KAAK,CAAE8E,SAAS,GAAG5E,KAAK,GAAI,GAAG,CAAC;gCAC/C6E,aAAa,EAAbA,aAAa;gCACbC,WAAW,EAAXA;8BACF,CAAC,CAAC;4BACJ;4BAAC,kCAEM;8BACLrG,OAAO,EAAE,IAAI;8BACbuG,KAAK,EAALA,KAAK;8BACL1H,QAAQ,EAARA,QAAQ;8BACRW,SAAS,EAAEmC,MAAM,CAAC7B,MAAM;8BACxBG,GAAG,EAAEF,QAAQ,CAACG,WAAW,IAAIyB,MAAM,CAAC7B,MAAM;8BAC1CR,IAAI,EAAEqC,MAAM,CAACxB;4BACf,CAAC;0BAAA;4BAAA;4BAAA;4BAEDgG,SAAS,EAAE;4BACXzH,OAAO,CAAC0B,KAAK,iDAAYmG,KAAK,sBAAY;4BAE1C,IAAIxH,UAAU,EAAE;8BACdA,UAAU,CAAC;gCACTuC,OAAO,EAAE6E,SAAS;gCAClB5E,KAAK,EAALA,KAAK;gCACLJ,QAAQ,EAAE3C,IAAI,CAAC6C,KAAK,CAAE8E,SAAS,GAAG5E,KAAK,GAAI,GAAG,CAAC;gCAC/C6E,aAAa,EAAbA,aAAa;gCACbC,WAAW,EAAXA;8BACF,CAAC,CAAC;4BACJ;4BAAC,kCAEM;8BACLrG,OAAO,EAAE,KAAK;8BACduG,KAAK,EAALA,KAAK;8BACL1H,QAAQ,EAARA,QAAQ;8BACRuB,KAAK,EAAE,aAAME;4BACf,CAAC;0BAAA;4BAAA;4BAED8F,aAAa,EAAE;4BAAA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CAElB;kBAAA,gBApEK5C,UAAU;oBAAA;kBAAA;gBAAA,KAsEhB;gBAAA,kBACoBuC,MAAM;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAfW,KAAK;cAAA;gBAAA,KAEPJ,WAAW,EAAE;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACZ,IAAI,CAAC/H,KAAK,CAAC,IAAI,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAGxB;gBACMoI,cAAc,GAAG,EAAE;gBAChBX,GAAC,GAAG,CAAC;cAAA;gBAAA,MAAEA,GAAC,GAAGU,KAAK,CAAC5F,MAAM;kBAAA;kBAAA;gBAAA;gBACxB8F,KAAK,GAAGF,KAAK,CAACR,KAAK,CAACF,GAAC,EAAEA,GAAC,GAAGvF,aAAa,CAAC;gBACzCoG,aAAa,GAAGD,KAAK,CAAClF,GAAG,CAAC,UAAC7C,QAAQ,EAAEiI,GAAG;kBAAA,OAC5CtD,UAAU,CAAC3E,QAAQ,EAAEkC,OAAO,CAACD,MAAM,GAAGgG,GAAG,CAAC;gBAAA,EAC3C;gBAAA;gBAAA,OAE0B3D,OAAO,CAAC4D,GAAG,CAACF,aAAa,CAAC;cAAA;gBAA/CG,YAAY;gBAClBjG,OAAO,CAACkF,IAAI,OAAZlF,OAAO,mCAASiG,YAAY,EAAC;;gBAE7B;gBACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,EAAE,EAAE;kBAC9CD,MAAM,CAACC,EAAE,EAAE;gBACb;cAAC;gBAZ+BlB,GAAC,IAAIvF,aAAa;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA,KA0BlDqF,SAAS;kBAAA;kBAAA;gBAAA;gBACLqB,aAAa,GAAGpG,OAAO,CAACS,MAAM,CAAC,UAAAC,CAAC;kBAAA,OAAI,CAACA,CAAC,CAACzB,OAAO;gBAAA,EAAC;gBAAA,MACjDmH,aAAa,CAACrG,MAAM,GAAG,CAAC;kBAAA;kBAAA;gBAAA;gBAC1BpC,OAAO,CAACC,GAAG,uBAAMwI,aAAa,CAACrG,MAAM,0CAAS;gBAAA;gBAAA,OAEnB,IAAI,CAACkB,WAAW,CACzCmF,aAAa,CAACzF,GAAG,CAAC,UAAAgB,CAAC;kBAAA,OAAIA,CAAC,CAAC7D,QAAQ;gBAAA,EAAC,kCAE7BX,OAAO;kBACV4H,SAAS,EAAE,KAAK,CAAC;gBAAA,GAEpB;cAAA;gBANKsB,YAAY;gBAQlB;gBACAA,YAAY,CAACrG,OAAO,CAACsG,OAAO,CAAC,UAAAC,KAAK,EAAI;kBACpC,IAAMf,KAAK,GAAGxF,OAAO,CAACwG,SAAS,CAAC,UAAA9F,CAAC;oBAAA,OAAIA,CAAC,CAAC5C,QAAQ,KAAKyI,KAAK,CAACzI,QAAQ;kBAAA,EAAC;kBACnE,IAAI0H,KAAK,KAAK,CAAC,CAAC,EAAE;oBAChBxF,OAAO,CAACwF,KAAK,CAAC,GAAGe,KAAK;kBACxB;gBACF,CAAC,CAAC;cAAA;gBAAA,kCAIC;kBACLtH,OAAO,EAAE,IAAI;kBACbe,OAAO,EAAPA,OAAO;kBACPsB,OAAO,EAAE;oBACPd,KAAK,EAALA,KAAK;oBACLe,UAAU,EAAEvB,OAAO,CAACS,MAAM,CAAC,UAAAC,CAAC;sBAAA,OAAIA,CAAC,CAACzB,OAAO;oBAAA,EAAC,CAACc,MAAM;oBACjDyB,MAAM,EAAExB,OAAO,CAACS,MAAM,CAAC,UAAAC,CAAC;sBAAA,OAAI,CAACA,CAAC,CAACzB,OAAO;oBAAA,EAAC,CAACc,MAAM;oBAC9C0G,SAAS,EAAEzG,OAAO,CAAC0G,MAAM,CAAC,UAACC,GAAG,EAAEjG,CAAC;sBAAA,OAAKiG,GAAG,IAAIjG,CAAC,CAACnC,IAAI,IAAI,CAAC,CAAC;oBAAA,GAAE,CAAC;kBAC9D;gBACF,CAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACF;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;EAHE;IAAA;IAAA,OAIA,8BAAqB;MACnB,IAAMqI,SAAS,GAAGhD,IAAI,CAACD,GAAG,EAAE;MAC5B,IAAMkD,MAAM,GAAGpJ,IAAI,CAACoJ,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;MACzD,yBAAkBH,SAAS,cAAIC,MAAM;IACvC;;IAEA;AACF;AACA;AACA;EAHE;IAAA;IAAA,OAIA,kCAAyB;MACvB,IAAMD,SAAS,GAAGhD,IAAI,CAACD,GAAG,EAAE;MAC5B,IAAMkD,MAAM,GAAGpJ,IAAI,CAACoJ,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;MACzD,8BAAuBH,SAAS,cAAIC,MAAM;IAC5C;;IAEA;AACF;AACA;AACA;EAHE;IAAA;IAAA,OAIA,6BAAoB;MAClB,IAAMD,SAAS,GAAGhD,IAAI,CAACD,GAAG,EAAE;MAC5B,IAAMkD,MAAM,GAAGpJ,IAAI,CAACoJ,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;MACzD,wBAAiBH,SAAS,cAAIC,MAAM;IACtC;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA,OAKA,eAAMG,EAAE,EAAE;MACR,OAAO,IAAI5E,OAAO,CAAC,UAAAC,OAAO;QAAA,OAAImB,UAAU,CAACnB,OAAO,EAAE2E,EAAE,CAAC;MAAA,EAAC;IACxD;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,gGAKA,kBAAuBC,OAAO;QAAA;QAAA;UAAA;YAAA;cAAA;gBACtBC,GAAG,GAAGtH,KAAK,CAACC,OAAO,CAACoH,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;gBAAA;gBAAA;gBAAA,OAGjCzE,QAAQ,CAAC2E,UAAU,CAAC;kBACvCC,QAAQ,EAAEF;gBACZ,CAAC,CAAC;cAAA;gBAFItG,MAAM;gBAAA,kCAIL;kBACL3B,OAAO,EAAE,IAAI;kBACboI,YAAY,EAAEzG,MAAM,CAACwG,QAAQ,CAAC3G,MAAM,CAAC,UAAAkB,CAAC;oBAAA,OAAIA,CAAC,CAAC2F,MAAM,KAAK,CAAC;kBAAA,EAAC,CAACvH,MAAM;kBAChEwH,WAAW,EAAE3G,MAAM,CAACwG,QAAQ,CAAC3G,MAAM,CAAC,UAAAkB,CAAC;oBAAA,OAAIA,CAAC,CAAC2F,MAAM,KAAK,CAAC;kBAAA,EAAC,CAACvH,MAAM;kBAC/DyH,OAAO,EAAE5G,MAAM,CAACwG;gBAClB,CAAC;cAAA;gBAAA;gBAAA;gBAEDzJ,OAAO,CAAC0B,KAAK,CAAC,YAAY,eAAQ;gBAAA,MAC5B,IAAIC,KAAK,iDAAY,aAAMC,OAAO,EAAG;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAE9C;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,2FAKA,kBAAkBR,MAAM;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAECyD,QAAQ,CAACiF,cAAc,CAAC;kBAC3CL,QAAQ,EAAE,CAACrI,MAAM;gBACnB,CAAC,CAAC;cAAA;gBAFI6B,MAAM;gBAAA,MAIRA,MAAM,CAACwG,QAAQ,IAAIxG,MAAM,CAACwG,QAAQ,CAACrH,MAAM,GAAG,CAAC;kBAAA;kBAAA;gBAAA;gBAAA,kCACxCa,MAAM,CAACwG,QAAQ,CAAC,CAAC,CAAC;cAAA;gBAAA,MAEnB,IAAI9H,KAAK,CAAC,OAAO,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG1B3B,OAAO,CAAC0B,KAAK,CAAC,WAAW,eAAQ;gBACjC;gBAAA,kCACO;kBACLN,MAAM,EAAEA,MAAM;kBACdI,WAAW,EAAEJ,MAAM;kBACnBuI,MAAM,EAAE;gBACV,CAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAEJ;MAAA;QAAA;MAAA;MAAA;IAAA;EAAA;EAAA;AAAA,KAGH;AACA,IAAMI,WAAW,GAAG,IAAIpL,WAAW,EAAE;AAAA,eAEtBoL,WAAW;AAAA,2B;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1wB1B;AACA;AACA;AACA;AACA;AAJA,IAMMC,UAAU;EACZ,sBAAc;IAAA;IACZ;IACA,IAAI,CAACC,aAAa,GAAG;MACnB;MACAC,MAAM,EAAE;QACNC,QAAQ,EAAE,GAAG;QACbC,SAAS,EAAE,GAAG;QACdC,OAAO,EAAE,GAAG;QACZC,OAAO,EAAE,GAAG,GAAG,IAAI;QACnBC,MAAM,EAAE;MACV,CAAC;MACD;MACAC,KAAK,EAAE;QACLL,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,GAAG;QACZC,OAAO,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;QACxBC,MAAM,EAAE;MACV;IACF,CAAC;EAYH;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAA;IAAA,OAOA,qBAAY7I,KAAK,EAAEnC,SAAS,EAAiB;MAAA,IAAfkL,QAAQ,uEAAG,CAAC,CAAC;MACzC,IAAMC,YAAY,GAAG,CAAAhJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,OAAO,eAAOrC,SAAS,iBAAI;MACvD,IAAMoL,SAAS,GAAG,CAAAjJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkJ,IAAI,KAAI,eAAe;MAEhD,IAAM3H,MAAM;QACV3B,OAAO,EAAE,KAAK;QACdI,KAAK,EAAE;UACLE,OAAO,EAAE8I,YAAY;UACrBE,IAAI,EAAED,SAAS;UACfd,OAAO,EAAEnI,KAAK;UACduH,SAAS,EAAE,IAAIhD,IAAI,EAAE,CAAC4E,WAAW;QACnC;MAAC,GACEJ,QAAQ,CACZ;;MAED;MACAzK,OAAO,CAAC0B,KAAK,wBAAiBnC,SAAS,oBAAO;QAC5CqC,OAAO,EAAE8I,YAAY;QACrBE,IAAI,EAAED,SAAS;QACf1B,SAAS,EAAEhG,MAAM,CAACvB,KAAK,CAACuH;MAC1B,CAAC,CAAC;MAEF,OAAOhG,MAAM;IACf;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAA;IAAA;MAAA,6FAOA,iBAAoB9C,QAAQ;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAE2K,IAAI,2DAAG,QAAQ;gBAAEC,YAAY,2DAAG,CAAC,CAAC;gBACxDC,MAAM,iDACP,IAAI,CAACf,aAAa,CAACa,IAAI,CAAC,GACxBC,YAAY;kBACfD,IAAI,EAAJA,IAAI,CAAC;gBAAA;gBAECvK,YAAY,GAAKwK,YAAY,CAA7BxK,YAAY;gBAAA;gBAAA;gBAAA,OASH,IAAI,CAAC0K,eAAe,CAAC9K,QAAQ,EAAE6K,MAAM,EAAEzK,YAAY,CAAC;cAAA;gBAAnE0C,MAAM;gBAAA;kBAIJ3B,OAAO,EAAE;gBAAI,GACV2B,MAAM;cAAA;gBAAA;gBAAA;gBAAA,iCAGJ,IAAI,CAACiI,WAAW,cAAQ,MAAM,EAAE;kBACrCxK,IAAI,EAAEP,QAAQ;kBACdS,IAAI,EAAEL,YAAY,IAAI,CAAC;kBACvB2C,KAAK,EAAE,CAAC;kBACRC,MAAM,EAAE,CAAC;kBACTxC,UAAU,EAAE,KAAK;kBACjBJ,YAAY,EAAEA,YAAY,IAAI,CAAC;kBAC/BM,gBAAgB,EAAE;gBACpB,CAAC,CAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAEL;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IA+GI;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAA;IAAA;MAAA,+FAQA,kBAAsBV,QAAQ,EAAE6K,MAAM;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAEzK,YAAY,8DAAG,CAAC;gBAAA;gBAAA,IAG/CA,YAAY;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEU,IAAI,CAAC4K,gBAAgB,CAAChL,QAAQ,CAAC;cAAA;gBAAhDkB,QAAQ;gBACdd,YAAY,GAAGc,QAAQ,CAACT,IAAI;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,MAEtB,IAAIe,KAAK,CAAC,YAAY,GAAG,aAAMC,OAAO,CAAC;cAAA;gBAAA;gBAAA,OAKzB,IAAI,CAACwJ,iBAAiB,CAACjL,QAAQ,CAAC;cAAA;gBAAlDkL,SAAS;gBAGXC,UAAU,GAAG,IAAI;gBAErB,IAAIN,MAAM,CAACF,IAAI,KAAK,QAAQ,EAAE;kBAC5B;kBACMlK,IAAI,GAAGd,IAAI,CAACyL,GAAG,CAACP,MAAM,CAACb,QAAQ,EAAEa,MAAM,CAACZ,SAAS,CAAC;kBACxDoB,UAAU,GAAG;oBAAEtI,KAAK,EAAEtC,IAAI;oBAAEuC,MAAM,EAAEvC;kBAAK,CAAC;kBAC1C0K,UAAU,GAAG,IAAI,CAACG,iBAAiB,CAACJ,SAAS,CAACnI,KAAK,EAAEmI,SAAS,CAAClI,MAAM,EAAEvC,IAAI,EAAEA,IAAI,CAAC;gBACpF,CAAC,MAAM;kBACL;kBACA4K,UAAU,GAAG,IAAI,CAACE,aAAa,CAC7BL,SAAS,CAACnI,KAAK,EACfmI,SAAS,CAAClI,MAAM,EAChB6H,MAAM,CAACb,QAAQ,IAAI,GAAG,EACtBa,MAAM,CAACZ,SAAS,IAAI,GAAG,CACxB;gBACH;;gBAEA;gBACIuB,aAAa,GAAGxL,QAAQ;gBAAA,KACxBmL,UAAU;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACa,IAAI7G,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;kBACxD,IAAMzB,KAAK,GAAGoI,UAAU,CAACM,WAAW,GAAGN,UAAU,CAACO,OAAO,GAAG,CAAC;kBAC7D,IAAM1I,MAAM,GAAGmI,UAAU,CAACQ,YAAY,GAAGR,UAAU,CAACS,OAAO,GAAG,CAAC;kBAC/DtF,GAAG,CAACuF,SAAS,CAAC;oBACZC,GAAG,EAAE9L,QAAQ;oBACb+L,UAAU,EAAE,CAACZ,UAAU,CAACO,OAAO,EAAEP,UAAU,CAACS,OAAO,CAAC;oBACpDI,SAAS,YAAKjJ,KAAK,cAAIC,MAAM,CAAE;oBAC/B7B,OAAO,EAAE,iBAAC8K,GAAG;sBAAA,OAAK1H,OAAO,CAAC0H,GAAG,CAACC,YAAY,CAAC;oBAAA;oBAC3CC,IAAI,EAAE,cAACC,GAAG;sBAAA,OAAK5H,MAAM,CAAC,IAAIhD,KAAK,CAAC,QAAQ,GAAG4K,GAAG,CAAC9G,MAAM,CAAC,CAAC;oBAAA;kBACzD,CAAC,CAAC;gBACJ,CAAC,CAAC;cAAA;gBAVI+G,UAAU;gBAWhBb,aAAa,GAAGa,UAAU;cAAA;gBAAA;gBAAA,OAIA,IAAI,CAACC,eAAe,CAACd,aAAa,EAAEX,MAAM,CAACX,OAAO,IAAI,GAAG,CAAC;cAAA;gBAAhFqC,aAAa;gBAAA;gBAAA,OACK,IAAI,CAACvB,gBAAgB,CAACuB,aAAa,CAACL,YAAY,CAAC;cAAA;gBAAnEM,SAAS;gBAAA,MAGXA,SAAS,CAAC/L,IAAI,GAAGoK,MAAM,CAACV,OAAO;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACJ,IAAI,CAACmC,eAAe,CAC/CC,aAAa,CAACL,YAAY,EAC1B,CAACrB,MAAM,CAACX,OAAO,IAAI,GAAG,IAAI,GAAG,CAAC;gBAAA,CAC/B;cAAA;gBAHKuC,cAAc;gBAAA;gBAAA,OAIK,IAAI,CAACzB,gBAAgB,CAACyB,cAAc,CAACP,YAAY,CAAC;cAAA;gBAArEQ,UAAU;gBAAA,kCAET;kBACLnM,IAAI,EAAEkM,cAAc,CAACP,YAAY;kBACjCzL,IAAI,EAAEiM,UAAU,CAACjM,IAAI;kBACrBsC,KAAK,EAAEsI,UAAU,CAACtI,KAAK;kBACvBC,MAAM,EAAEqI,UAAU,CAACrI,MAAM;kBACzBxC,UAAU,EAAE,IAAI;kBAChBJ,YAAY,EAAEA,YAAY;kBAC1BM,gBAAgB,EAAE,CAAC,CAAC,GAAGgM,UAAU,CAACjM,IAAI,GAAGL,YAAY,IAAI;gBAC3D,CAAC;cAAA;gBAAA,kCAGI;kBACLG,IAAI,EAAEgM,aAAa,CAACL,YAAY;kBAChCzL,IAAI,EAAE+L,SAAS,CAAC/L,IAAI;kBACpBsC,KAAK,EAAEsI,UAAU,CAACtI,KAAK;kBACvBC,MAAM,EAAEqI,UAAU,CAACrI,MAAM;kBACzBxC,UAAU,EAAE,IAAI;kBAChBJ,YAAY,EAAEA,YAAY;kBAC1BM,gBAAgB,EAAE,CAAC,CAAC,GAAG8L,SAAS,CAAC/L,IAAI,GAAGL,YAAY,IAAI;gBAC1D,CAAC;cAAA;gBAAA;gBAAA;gBAAA,MAEK,IAAIoB,KAAK,CAAC,WAAW,GAAG,aAAMC,OAAO,CAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAE/C;MAAA;QAAA;MAAA;MAAA;IAAA;IAGD;AACJ;AACA;AACA;AACA;AACA;EALI;IAAA;IAAA,OAOA,yBAAgBzB,QAAQ,EAAEkK,OAAO,EAAE;MACjC,OAAO,IAAI5F,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC8B,GAAG,CAACqG,aAAa,CAAC;UAChBb,GAAG,EAAE9L,QAAQ;UACbkK,OAAO,EAAEvK,IAAI,CAACiN,KAAK,CAAC1C,OAAO,GAAG,GAAG,CAAC;UAClC/I,OAAO,EAAEoD,OAAO;UAChB4H,IAAI,EAAE,cAAC5K,KAAK;YAAA,OAAKiD,MAAM,CAAC,IAAIhD,KAAK,CAAC,QAAQ,GAAGD,KAAK,CAAC+D,MAAM,CAAC,CAAC;UAAA;QAC7D,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;;IAGA;AACJ;AACA;AACA;AACA;EAJI;IAAA;IAAA,OAMA,2BAAkBtF,QAAQ,EAAE;MAC1B,OAAO,IAAIsE,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC8B,GAAG,CAACuG,YAAY,CAAC;UACff,GAAG,EAAE9L,QAAQ;UACbmB,OAAO,EAAEoD,OAAO;UAChB4H,IAAI,EAAE,cAAC5K,KAAK;YAAA,OAAKiD,MAAM,CAAC,IAAIhD,KAAK,CAAC,YAAY,GAAGD,KAAK,CAAC+D,MAAM,CAAC,CAAC;UAAA;QACjE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;;IAGA;AACJ;AACA;AACA;AACA;EAJI;IAAA;IAAA,OAMA,0BAAiBtF,QAAQ,EAAE;MACzB,OAAO,IAAIsE,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC8B,GAAG,CAACtF,WAAW,CAAC;UACdhB,QAAQ,EAAEA,QAAQ;UAClBmB,OAAO,EAAEoD,OAAO;UAChB4H,IAAI,EAAE,cAAC5K,KAAK;YAAA,OAAKiD,MAAM,CAAC,IAAIhD,KAAK,CAAC,YAAY,GAAGD,KAAK,CAAC+D,MAAM,CAAC,CAAC;UAAA;QACjE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;;IAGA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAA;IAAA,OAQA,uBAAcvC,KAAK,EAAEC,MAAM,EAAEgH,QAAQ,EAAEC,SAAS,EAAE;MAChD,IAAI6C,WAAW,GAAG/J,KAAK;MACvB,IAAIgK,YAAY,GAAG/J,MAAM;;MAEzB;MACA,IAAID,KAAK,GAAGiH,QAAQ,EAAE;QACpB8C,WAAW,GAAG9C,QAAQ;QACtB+C,YAAY,GAAGpN,IAAI,CAAC6C,KAAK,CAACQ,MAAM,IAAIgH,QAAQ,GAAGjH,KAAK,CAAC,CAAC;MACxD;;MAEA;MACA,IAAIgK,YAAY,GAAG9C,SAAS,EAAE;QAC5B8C,YAAY,GAAG9C,SAAS;QACxB6C,WAAW,GAAGnN,IAAI,CAAC6C,KAAK,CAACO,KAAK,IAAIkH,SAAS,GAAGjH,MAAM,CAAC,CAAC;MACxD;MAEA,OAAO;QACLD,KAAK,EAAE+J,WAAW;QAClB9J,MAAM,EAAE+J;MACV,CAAC;IACH;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAA;IAAA,OAQA,2BAAkBhK,KAAK,EAAEC,MAAM,EAAE8J,WAAW,EAAEC,YAAY,EAAE;MAC1D;MACA,IAAMC,KAAK,GAAGrN,IAAI,CAACyL,GAAG,CAACrI,KAAK,GAAG+J,WAAW,EAAE9J,MAAM,GAAG+J,YAAY,CAAC;;MAElE;MACA,IAAME,SAAS,GAAGH,WAAW,GAAGE,KAAK;MACrC,IAAME,UAAU,GAAGH,YAAY,GAAGC,KAAK;;MAEvC;MACA,IAAMtB,OAAO,GAAG/L,IAAI,CAACwN,GAAG,CAAC,CAAC,EAAE,CAACpK,KAAK,GAAGkK,SAAS,IAAI,CAAC,CAAC;MACpD,IAAMrB,OAAO,GAAGjM,IAAI,CAACwN,GAAG,CAAC,CAAC,EAAE,CAACnK,MAAM,GAAGkK,UAAU,IAAI,CAAC,CAAC;MAEtD,OAAO;QACLzB,WAAW,EAAE1I,KAAK;QAClB4I,YAAY,EAAE3I,MAAM;QACpB8J,WAAW,EAAEA,WAAW;QACxBC,YAAY,EAAEA,YAAY;QAC1BrB,OAAO,EAAE/L,IAAI,CAAC6C,KAAK,CAACkJ,OAAO,CAAC;QAC5BE,OAAO,EAAEjM,IAAI,CAAC6C,KAAK,CAACoJ,OAAO,CAAC;QAC5BoB,KAAK,EAAEA;MACT,CAAC;IACH;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAA;IAAA;MAAA,2FAKA,kBAAkBhN,QAAQ;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEH,IAAI,CAACgL,gBAAgB,CAAChL,QAAQ,CAAC;cAAA;gBAA5CoN,IAAI;gBAAA,kCACHA,IAAI,CAAC3M,IAAI;cAAA;gBAAA;gBAAA;gBAAA,MAEV,IAAIe,KAAK,CAAC,YAAY,GAAG,aAAMC,OAAO,CAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAEhD;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACJ;AACA;AACA;AACA;EAJI;IAAA;IAAA,OAKA,wBAAe4L,KAAK,EAAE;MACpB,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7B,IAAMC,CAAC,GAAG,IAAI;MACd,IAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACrC,IAAMpG,CAAC,GAAGxH,IAAI,CAACiN,KAAK,CAACjN,IAAI,CAACG,GAAG,CAACuN,KAAK,CAAC,GAAG1N,IAAI,CAACG,GAAG,CAACwN,CAAC,CAAC,CAAC;MACnD,OAAO,CAACD,KAAK,GAAG1N,IAAI,CAACC,GAAG,CAAC0N,CAAC,EAAEnG,CAAC,CAAC,EAAEf,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGmH,KAAK,CAACpG,CAAC,CAAC;IAC7D;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAA;IAAA,OAKA,6BAAoBS,QAAQ,EAAE;MAAA;MAC5B,IAAM4F,cAAc,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;MACrD,IAAMC,SAAS,0BAAG7F,QAAQ,CAAC8F,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,wDAAzB,oBAA2BC,WAAW,EAAE;MAE1D,IAAI,CAACH,SAAS,IAAI,CAACD,cAAc,CAACK,QAAQ,CAACJ,SAAS,CAAC,EAAE;QACrD,OAAO;UACLvK,KAAK,EAAE,KAAK;UACZzB,OAAO,EAAE;QACX,CAAC;MACH;MAEA,OAAO;QACLyB,KAAK,EAAE,IAAI;QACXkH,MAAM,EAAEqD;MACV,CAAC;IACH;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAA;IAAA;MAAA,iGAMA,kBAAwBzN,QAAQ;QAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAES,IAAI,8DAAG,GAAG;gBAAA;gBAAA;gBAAA,OAEnB,IAAI,CAACkM,aAAa,CAAC3M,QAAQ,EAAE,OAAO,EAAE;kBACzDgK,QAAQ,EAAEvJ,IAAI;kBACdwJ,SAAS,EAAExJ,IAAI;kBACfyJ,OAAO,EAAE,GAAG;kBACZC,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC;gBACrB,CAAC,CAAC;cAAA;gBALIrH,MAAM;gBAAA,kCAOLA,MAAM;cAAA;gBAAA;gBAAA;gBAAA,MAEP,IAAItB,KAAK,CAAC,WAAW,GAAG,aAAMC,OAAO,CAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAE/C;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAA;IAAA;MAAA,mGAOA,kBAA0BC,SAAS;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAEiJ,IAAI,8DAAG,OAAO;gBAAEmD,gBAAgB;gBAC7DpL,KAAK,GAAGhB,SAAS,CAACO,MAAM;gBACxBC,OAAO,GAAG,EAAE;gBAETiF,CAAC,GAAG,CAAC;cAAA;gBAAA,MAAEA,CAAC,GAAGzE,KAAK;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEA,IAAI,CAACiK,aAAa,CAACjL,SAAS,CAACyF,CAAC,CAAC,EAAEwD,IAAI,CAAC;cAAA;gBAArD7H,MAAM;gBACZZ,OAAO,CAACkF,IAAI,CAAC;kBACXjG,OAAO,EAAE2B,MAAM,CAAC3B,OAAO;kBACvB2B,MAAM,EAAEA,MAAM,CAAC3B,OAAO,GAAG2B,MAAM,GAAG,IAAI;kBACtCvB,KAAK,EAAEuB,MAAM,CAAC3B,OAAO,GAAG,IAAI,GAAG2B,MAAM,CAACvB;gBACxC,CAAC,CAAC;gBAEF,IAAIuM,gBAAgB,EAAE;kBACpBA,gBAAgB,CAAC;oBACfrL,OAAO,EAAE0E,CAAC,GAAG,CAAC;oBACdzE,KAAK,EAALA,KAAK;oBACLnC,IAAI,EAAEmB,SAAS,CAACyF,CAAC,CAAC;oBAClBhG,OAAO,EAAE2B,MAAM,CAAC3B;kBAClB,CAAC,CAAC;gBACJ;gBAAC;gBAAA;cAAA;gBAAA;gBAAA;gBAEDe,OAAO,CAACkF,IAAI,CAAC;kBACXjG,OAAO,EAAE,KAAK;kBACd2B,MAAM,EAAE,IAAI;kBACZvB,KAAK,EAAE,aAAME,OAAO;kBACpBlB,IAAI,EAAEmB,SAAS,CAACyF,CAAC;gBACnB,CAAC,CAAC;gBAEF,IAAI2G,gBAAgB,EAAE;kBACpBA,gBAAgB,CAAC;oBACfrL,OAAO,EAAE0E,CAAC,GAAG,CAAC;oBACdzE,KAAK,EAALA,KAAK;oBACLnC,IAAI,EAAEmB,SAAS,CAACyF,CAAC,CAAC;oBAClBhG,OAAO,EAAE,KAAK;oBACdI,KAAK,EAAE,aAAME;kBACf,CAAC,CAAC;gBACJ;cAAC;gBAjCsB0F,CAAC,EAAE;gBAAA;gBAAA;cAAA;gBAAA,kCAqCvBjF,OAAO;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACf;MAAA;QAAA;MAAA;MAAA;IAAA;EAAA;EAAA;AAAA;AAAA,eAGY,IAAI2H,UAAU,EAAE;AAAA,2B", "file": "pages/honor_pkg/common/vendor.js", "sourcesContent": ["/**\n * 云存储上传工具类\n * 支持进度显示、重试机制、批量上传等功能\n */\n\nimport imageUtils from './image-utils.js'\n\nclass UploadUtils {\n  constructor() {\n    this.maxRetries = 3 // 最大重试次数\n    this.retryDelay = 1000 // 重试延迟（毫秒）\n    this.chunkSize = 1024 * 1024 // 分片大小（1MB）\n    \n    // 新增错误类型定义\n    this.errorTypes = {\n      NETWORK: 'NETWORK_ERROR',\n      TIMEOUT: 'TIMEOUT_ERROR',\n      SIZE: 'SIZE_ERROR',\n      FORMAT: 'FORMAT_ERROR',\n      COMPRESS: 'COMPRESS_ERROR',\n      UPLOAD: 'UPLOAD_ERROR'\n    }\n    \n    // 新增重试策略\n    this.retryStrategies = {\n      [this.errorTypes.NETWORK]: true, // 网络错误可以重试\n      [this.errorTypes.TIMEOUT]: true, // 超时可以重试\n      [this.errorTypes.UPLOAD]: true,  // 上传错误可以重试\n      [this.errorTypes.COMPRESS]: false, // 压缩错误不重试\n      [this.errorTypes.FORMAT]: false,  // 格式错误不重试\n      [this.errorTypes.SIZE]: false     // 大小错误不重试\n    }\n  }\n\n  /**\n   * 智能重试处理\n   * @param {Function} operation - 要执行的操作\n   * @param {object} options - 重试选项\n   * @returns {Promise} 执行结果\n   */\n  async withRetry(operation, options = {}) {\n    const {\n      retries = 0,\n      errorType = this.errorTypes.UPLOAD,\n      context = {}\n    } = options\n\n    try {\n      return await operation()\n    } catch (error) {\n      const shouldRetry = this.retryStrategies[errorType] && \n                         retries < this.maxRetries\n\n      if (shouldRetry) {\n        const delay = this.retryDelay * Math.pow(2, retries) // 指数退避\n        await this.delay(delay)\n        \n        console.log(`重试第${retries + 1}次:`, {\n          errorType,\n          delay,\n          context\n        })\n        \n        return this.withRetry(operation, {\n          ...options,\n          retries: retries + 1\n        })\n      }\n      \n      throw error\n    }\n  }\n\n  /**\n   * 上传头像\n   * @param {string} filePath - 文件路径\n   * @param {object} options - 上传选项\n   * @returns {Promise<object>} 上传结果\n   */\n  async uploadAvatar(filePath, options = {}) {\n    const {\n      compress = true,\n      onProgress,\n      customPath,\n      originalSize\n    } = options\n\n    try {\n      let uploadPath = filePath\n      let compressInfo = null\n\n      // 压缩图片\n\n      // 微信小程序环境下跳过压缩，因为chooseImage已经压缩过了\n      compressInfo = {\n        path: filePath,\n        compressed: false,\n        size: originalSize,\n        originalSize: originalSize,\n        compressionRatio: 1\n      }\n\n      \n\n\n\n\n\n\n\n\n      // 生成云存储路径\n      const cloudPath = customPath || this.generateAvatarPath()\n\n      // 上传到云存储\n      const uploadResult = await this.uploadToCloud(uploadPath, cloudPath, {\n        onProgress,\n        fileType: 'avatar'\n      })\n\n      // 获取访问URL\n      const fileInfo = await this.getFileInfo(uploadResult.fileID)\n\n      return {\n        success: true,\n        cloudPath: uploadResult.fileID,\n        url: fileInfo.tempFileURL || uploadResult.fileID, // 优先使用临时URL\n        size: uploadResult.actualSize || compressInfo?.size || 0, // 优先使用实际上传大小\n        compressed: compressInfo?.compressed || false,\n        compressionRatio: compressInfo?.compressionRatio || 0,\n        originalSize: originalSize || compressInfo?.originalSize || 0\n      }\n    } catch (error) {\n      console.error('头像上传失败:', error)\n      throw new Error(`头像上传失败: ${error.message}`)\n    }\n  }\n\n  /**\n   * 上传表彰图片\n   * @param {Array|string} filePaths - 文件路径（数组或单个路径）\n   * @param {object} options - 上传选项\n   * @returns {Promise<object>} 上传结果\n   */\n  async uploadHonorImages(filePaths, options = {}) {\n    const {\n      compress = true,\n      onProgress,\n      onItemProgress,\n      maxConcurrent = 3\n    } = options\n\n    const paths = Array.isArray(filePaths) ? filePaths : [filePaths]\n    \n    console.log('🚀 开始上传表彰图片:', { \n      pathCount: paths.length, \n      compress, \n      maxConcurrent \n    })\n    \n    if (paths.length === 0) {\n      return { success: true, results: [] }\n    }\n\n    try {\n      // 批量压缩\n      let compressResults = []\n      if (compress) {\n        console.log('🔧 开始压缩图片...')\n        compressResults = await imageUtils.batchCompressImages(\n          paths, \n          'honor', \n          (progress) => {\n            if (onProgress) {\n              onProgress({\n                phase: 'compress',\n                progress: Math.round((progress.current / progress.total) * 100),\n                ...progress\n              })\n            }\n          }\n        )\n        console.log('✅ 压缩完成:', { \n          total: compressResults.length, \n          success: compressResults.filter(r => r.success).length \n        })\n      } else {\n        compressResults = paths.map(path => ({\n          success: true,\n          result: { \n            path, \n            compressed: false,\n            size: 0,\n            width: 0,\n            height: 0,\n            originalSize: 0,\n            compressionRatio: 0\n          }\n        }))\n        console.log('⏩ 跳过压缩，直接上传')\n      }\n\n      // 批量上传\n      const validPaths = compressResults\n        .filter(r => r.success && r.result && r.result.path)\n        .map(r => r.result.path)\n      \n      console.log('🔍 有效路径检查:', { \n        total: compressResults.length, \n        valid: validPaths.length\n      })\n      \n      if (validPaths.length === 0) {\n        throw new Error('没有可上传的图片')\n      }\n      \n      console.log('📤 开始批量上传...')\n      const uploadResults = await this.batchUpload(\n        validPaths,\n        {\n          onProgress: (progress) => {\n            if (onProgress) {\n              onProgress({\n                phase: 'upload',\n                progress: Math.round((progress.current / progress.total) * 100),\n                ...progress\n              })\n            }\n          },\n          onItemProgress,\n          maxConcurrent,\n          pathGenerator: () => this.generateHonorImagePath()\n        }\n      )\n      console.log('✅ 上传完成:', { \n        uploaded: uploadResults.summary?.successful || 0,\n        failed: uploadResults.summary?.failed || 0\n      })\n\n      // 处理压缩失败的文件\n      const failedCompress = compressResults.filter(r => !r.success)\n      const allResults = [\n        ...uploadResults.results,  // 正确访问 results 数组\n        ...failedCompress.map(f => ({\n          success: false,\n          error: f.error || '图片压缩失败',\n          filePath: f.path\n        }))\n      ]\n\n      return {\n        success: true,\n        results: allResults,\n        totalUploaded: uploadResults.results.filter(r => r.success).length,\n        totalFailed: allResults.filter(r => !r.success).length\n      }\n    } catch (error) {\n      console.error('表彰图片上传失败:', error)\n      throw new Error(`表彰图片上传失败: ${error.message}`)\n    }\n  }\n\n  /**\n   * 上传到云存储\n   * @param {string} filePath - 本地文件路径\n   * @param {string} cloudPath - 云存储路径\n   * @param {object} options - 上传选项\n   * @returns {Promise<object>} 上传结果\n   */\n  async uploadToCloud(filePath, cloudPath, options = {}) {\n    const { \n      onProgress, \n      fileType = 'image',\n      useCache = true,  // 是否使用缓存\n      cacheTime = 3600 * 1000 // 缓存时间，默认1小时\n    } = options\n\n    // 生成缓存key\n    const cacheKey = `upload_${filePath}_${cloudPath}`\n    \n    // 检查缓存\n    if (useCache) {\n      const cached = await this.getCache(cacheKey)\n      if (cached) {\n        console.log('使用上传缓存:', cached)\n        return cached\n      }\n    }\n\n    try {\n      let uploadTask\n      const uploadPromise = new Promise((resolve, reject) => {\n\n\n\n\n\n\n\n\n\n        uploadTask = uniCloud.uploadFile({\n          filePath: filePath,\n          cloudPath: cloudPath,\n          cloudPathAsRealPath: true,\n          onUploadProgress: (progressEvent) => {\n            if (onProgress) {\n              const progress = Math.round(\n                (progressEvent.loaded / progressEvent.total) * 100\n              )\n              onProgress({\n                loaded: progressEvent.loaded,\n                total: progressEvent.total,\n                progress: progress,\n                speed: this.calculateSpeed(progressEvent)\n              })\n            }\n          }\n        })\n\n        uploadTask\n          .then(result => {\n            if (result.success) {\n              const finalResult = {\n                ...result,\n                actualSize: result.fileSize || result.size || 0\n              }\n              \n              // 设置缓存\n              if (useCache) {\n                this.setCache(cacheKey, finalResult, cacheTime)\n              }\n              \n              resolve(finalResult)\n            } else {\n              reject(new Error(result.errMsg || '上传失败'))\n            }\n          })\n          .catch(reject)\n      })\n\n      // 添加超时控制\n      const timeoutPromise = new Promise((_, reject) => {\n        setTimeout(() => {\n          reject(new Error('上传超时'))\n        }, options.timeout || 30000)\n      })\n\n      return await this.withRetry(\n        () => Promise.race([uploadPromise, timeoutPromise]),\n        {\n          errorType: this.errorTypes.UPLOAD,\n          context: { filePath, cloudPath }\n        }\n      )\n    } catch (error) {\n      console.error('上传失败:', error)\n      throw error\n    }\n  }\n\n  /**\n   * 计算上传速度\n   * @param {object} progressEvent - 进度事件\n   * @returns {string} 格式化的速度\n   */\n  calculateSpeed(progressEvent) {\n    const now = Date.now()\n    if (!this.lastProgress) {\n      this.lastProgress = { time: now, loaded: 0 }\n      return '0 KB/s'\n    }\n\n    const timeDiff = now - this.lastProgress.time\n    const loadedDiff = progressEvent.loaded - this.lastProgress.loaded\n\n    if (timeDiff > 0) {\n      const speedBps = (loadedDiff * 1000) / timeDiff\n      this.lastProgress = { time: now, loaded: progressEvent.loaded }\n\n      if (speedBps < 1024) {\n        return `${speedBps.toFixed(1)} B/s`\n      } else if (speedBps < 1024 * 1024) {\n        return `${(speedBps / 1024).toFixed(1)} KB/s`\n      } else {\n        return `${(speedBps / (1024 * 1024)).toFixed(1)} MB/s`\n      }\n    }\n\n    return '计算中...'\n  }\n\n  /**\n   * 获取缓存\n   * @param {string} key - 缓存键\n   * @returns {Promise<object>} 缓存数据\n   */\n  async getCache(key) {\n    try {\n      const cached = uni.getStorageSync(`upload_cache_${key}`)\n      if (cached) {\n        const { data, expire } = JSON.parse(cached)\n        if (expire > Date.now()) {\n          return data\n        }\n      }\n      return null\n    } catch (error) {\n      console.warn('读取上传缓存失败:', error)\n      return null\n    }\n  }\n\n  /**\n   * 设置缓存\n   * @param {string} key - 缓存键\n   * @param {object} data - 缓存数据\n   * @param {number} duration - 缓存时长（毫秒）\n   */\n  setCache(key, data, duration) {\n    try {\n      const cache = {\n        data,\n        expire: Date.now() + duration\n      }\n      uni.setStorageSync(`upload_cache_${key}`, JSON.stringify(cache))\n    } catch (error) {\n      console.warn('设置上传缓存失败:', error)\n    }\n  }\n\n  /**\n   * H5端上传Base64数据URL到云存储\n   * @param {string} dataURL - Base64数据URL\n   * @param {string} cloudPath - 云存储路径\n   * @param {object} options - 上传选项\n   * @returns {Promise<object>} 上传结果\n   */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  /**\n   * 批量上传\n   * @param {Array} filePaths - 文件路径数组\n   * @param {object} options - 上传选项\n   * @returns {Promise<Array>} 上传结果数组\n   */\n  async batchUpload(filePaths, options = {}) {\n    const {\n      onProgress,\n      onItemProgress,\n      maxConcurrent = 3,\n      pathGenerator,\n      chunkSize = this.chunkSize,\n      autoRetry = true\n    } = options\n\n    // 对大文件列表进行分块处理\n    const chunks = []\n    for (let i = 0; i < filePaths.length; i += chunkSize) {\n      chunks.push(filePaths.slice(i, i + chunkSize))\n    }\n\n    const results = []\n    const total = filePaths.length\n    let completed = 0\n    let activeUploads = 0\n    let memoryUsage = 0\n\n    // 监控内存使用\n    const checkMemory = () => {\n\n\n\n\n\n\n\n\n\n\n      return false\n    }\n\n    // 处理单个文件上传\n    const uploadFile = async (filePath, index) => {\n      try {\n        activeUploads++\n        const cloudPath = pathGenerator ? pathGenerator() : this.generateImagePath()\n        \n        const result = await this.withRetry(\n          () => this.uploadToCloud(filePath, cloudPath, {\n            onProgress: (progress) => {\n              if (onItemProgress) {\n                onItemProgress({\n                  index,\n                  fileName: filePath,\n                  ...progress\n                })\n              }\n            }\n          }),\n          {\n            errorType: this.errorTypes.UPLOAD,\n            context: { filePath, index }\n          }\n        )\n\n        // 获取访问URL\n        const fileInfo = await this.getFileInfo(result.fileID)\n\n        completed++\n        if (onProgress) {\n          onProgress({\n            current: completed,\n            total,\n            progress: Math.round((completed / total) * 100),\n            activeUploads,\n            memoryUsage\n          })\n        }\n\n        return {\n          success: true,\n          index,\n          filePath,\n          cloudPath: result.fileID,\n          url: fileInfo.tempFileURL || result.fileID,\n          size: result.actualSize\n        }\n      } catch (error) {\n        completed++\n        console.error(`文件上传失败 [${index}]:`, error)\n        \n        if (onProgress) {\n          onProgress({\n            current: completed,\n            total,\n            progress: Math.round((completed / total) * 100),\n            activeUploads,\n            memoryUsage\n          })\n        }\n\n        return {\n          success: false,\n          index,\n          filePath,\n          error: error.message\n        }\n      } finally {\n        activeUploads--\n      }\n    }\n\n    // 处理每个分块\n    for (const chunk of chunks) {\n      // 检查内存使用情况\n      while (checkMemory()) {\n        await this.delay(1000) // 等待内存释放\n      }\n\n      // 并发上传当前分块中的文件\n      const uploadPromises = []\n      for (let i = 0; i < chunk.length; i += maxConcurrent) {\n        const batch = chunk.slice(i, i + maxConcurrent)\n        const batchPromises = batch.map((filePath, idx) => \n          uploadFile(filePath, results.length + idx)\n        )\n        \n        const batchResults = await Promise.all(batchPromises)\n        results.push(...batchResults)\n        \n        // 强制垃圾回收和内存清理\n        if (typeof global !== 'undefined' && global.gc) {\n          global.gc()\n        }\n        \n\n\n\n\n\n\n\n\n      }\n    }\n\n    // 处理失败的上传（如果启用自动重试）\n    if (autoRetry) {\n      const failedUploads = results.filter(r => !r.success)\n      if (failedUploads.length > 0) {\n        console.log(`重试${failedUploads.length}个失败的上传`)\n        \n        const retryResults = await this.batchUpload(\n          failedUploads.map(f => f.filePath),\n          {\n            ...options,\n            autoRetry: false // 防止无限重试\n          }\n        )\n        \n        // 更新结果\n        retryResults.results.forEach(retry => {\n          const index = results.findIndex(r => r.filePath === retry.filePath)\n          if (index !== -1) {\n            results[index] = retry\n          }\n        })\n      }\n    }\n\n    return {\n      success: true,\n      results,\n      summary: {\n        total,\n        successful: results.filter(r => r.success).length,\n        failed: results.filter(r => !r.success).length,\n        totalSize: results.reduce((sum, r) => sum + (r.size || 0), 0)\n      }\n    }\n  }\n\n  /**\n   * 生成头像存储路径\n   * @returns {string} 云存储路径\n   */\n  generateAvatarPath() {\n    const timestamp = Date.now()\n    const random = Math.random().toString(36).substring(2, 8)\n    return `avatars/${timestamp}_${random}.jpg`\n  }\n\n  /**\n   * 生成表彰图片存储路径\n   * @returns {string} 云存储路径\n   */\n  generateHonorImagePath() {\n    const timestamp = Date.now()\n    const random = Math.random().toString(36).substring(2, 8)\n    return `honor-images/${timestamp}_${random}.jpg`\n  }\n\n  /**\n   * 生成通用图片存储路径\n   * @returns {string} 云存储路径\n   */\n  generateImagePath() {\n    const timestamp = Date.now()\n    const random = Math.random().toString(36).substring(2, 8)\n    return `images/${timestamp}_${random}.jpg`\n  }\n\n  /**\n   * 延迟函数\n   * @param {number} ms - 延迟毫秒数\n   * @returns {Promise} Promise对象\n   */\n  delay(ms) {\n    return new Promise(resolve => setTimeout(resolve, ms))\n  }\n\n  /**\n   * 删除云存储文件\n   * @param {string|Array} fileIDs - 文件ID或ID数组\n   * @returns {Promise<object>} 删除结果\n   */\n  async deleteCloudFiles(fileIDs) {\n    const ids = Array.isArray(fileIDs) ? fileIDs : [fileIDs]\n    \n    try {\n      const result = await uniCloud.deleteFile({\n        fileList: ids\n      })\n      \n      return {\n        success: true,\n        deletedCount: result.fileList.filter(f => f.status === 0).length,\n        failedCount: result.fileList.filter(f => f.status !== 0).length,\n        details: result.fileList\n      }\n    } catch (error) {\n      console.error('删除云存储文件失败:', error)\n      throw new Error(`删除文件失败: ${error.message}`)\n    }\n  }\n\n  /**\n   * 获取文件信息\n   * @param {string} fileID - 文件ID\n   * @returns {Promise<object>} 文件信息\n   */\n  async getFileInfo(fileID) {\n    try {\n      const result = await uniCloud.getTempFileURL({\n        fileList: [fileID]\n      })\n      \n      if (result.fileList && result.fileList.length > 0) {\n        return result.fileList[0]\n      } else {\n        throw new Error('文件不存在')\n      }\n    } catch (error) {\n      console.error('获取文件信息失败:', error)\n      // 如果获取临时URL失败，返回文件ID作为备用\n      return {\n        fileID: fileID,\n        tempFileURL: fileID,\n        status: 0\n      }\n    }\n  }\n}\n\n// 创建单例实例\nconst uploadUtils = new UploadUtils()\n\nexport default uploadUtils ", "/**\n * 图片处理工具类\n * 支持H5端和小程序端图片压缩、格式转换、尺寸调整等功能\n * 解决图片文件过大的问题\n */\n\nclass ImageUtils {\n    constructor() {\n      // 默认配置\n      this.defaultConfig = {\n        // 头像配置\n        avatar: {\n          maxWidth: 800,\n          maxHeight: 800,\n          quality: 0.6,\n          maxSize: 500 * 1024,\n          format: 'jpg'\n        },\n        // 表彰图片配置\n        honor: {\n          maxWidth: 1600,\n          maxHeight: 1600,\n          quality: 0.8,\n          maxSize: 1 * 1024 * 1024,\n          format: 'jpg'\n        }\n      }\n      \n\n\n\n\n\n\n\n\n\n\n    }\n  \n    /**\n     * 统一的错误处理方法\n     * @param {Error} error - 错误对象\n     * @param {string} operation - 操作名称\n     * @param {object} fallback - 失败时的返回值\n     * @returns {object} 标准化的错误结果\n     */\n    handleError(error, operation, fallback = {}) {\n      const errorMessage = error?.message || `${operation}失败`\n      const errorCode = error?.code || 'UNKNOWN_ERROR'\n      \n      const result = {\n        success: false,\n        error: {\n          message: errorMessage,\n          code: errorCode,\n          details: error,\n          timestamp: new Date().toISOString()\n        },\n        ...fallback\n      }\n      \n      // 只记录关键错误信息，添加时间戳便于调试\n      console.error(`[ImageUtils] ${operation}失败:`, {\n        message: errorMessage,\n        code: errorCode,\n        timestamp: result.error.timestamp\n      })\n      \n      return result\n    }\n  \n    /**\n     * 压缩图片\n     * @param {string} filePath - 图片路径\n     * @param {string} type - 图片类型：'avatar' | 'honor'\n     * @param {object} customConfig - 自定义配置\n     * @returns {Promise<object>} 压缩后的图片信息\n     */\n    async compressImage(filePath, type = 'avatar', customConfig = {}) {\n      const config = { \n        ...this.defaultConfig[type], \n        ...customConfig,\n        type // 添加type标记用于区分处理方式\n      }\n      const { originalSize } = customConfig\n      \n      try {\n        let result\n\n\n\n        \n\n        result = await this.compressImageMP(filePath, config, originalSize)\n\n\n        return {\n          success: true,\n          ...result\n        }\n      } catch (error) {\n        return this.handleError(error, '图片压缩', {\n          path: filePath,\n          size: originalSize || 0,\n          width: 0,\n          height: 0,\n          compressed: false,\n          originalSize: originalSize || 0,\n          compressionRatio: 0\n        })\n      }\n    }\n  \n    /**\n     * H5端压缩图片\n     * @param {string} filePath - 图片路径\n     * @param {object} config - 压缩配置\n     * @param {number} originalSize - 原始文件大小\n     * @returns {Promise<object>} 压缩后的图片信息\n     */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    /**\n     * 小程序端压缩图片\n     * @param {string} filePath - 图片路径\n     * @param {object} config - 压缩配置\n     * @param {number} originalSize - 原始文件大小\n     * @returns {Promise<object>} 压缩结果\n     */\n\n    async compressImageMP(filePath, config, originalSize = 0) {\n      try {\n        // 获取原始文件大小（如果没有传入）\n        if (!originalSize) {\n          try {\n            const fileInfo = await this.getFileInfoAsync(filePath)\n            originalSize = fileInfo.size\n          } catch (error) {\n            throw new Error('获取文件信息失败: ' + error.message)\n          }\n        }\n\n        // 获取图片信息\n        const imageInfo = await this.getImageInfoAsync(filePath)\n        \n        let targetSize\n        let cropParams = null\n        \n        if (config.type === 'avatar') {\n          // 头像使用1:1裁剪\n          const size = Math.min(config.maxWidth, config.maxHeight)\n          targetSize = { width: size, height: size }\n          cropParams = this.calculateCropSize(imageInfo.width, imageInfo.height, size, size)\n        } else {\n          // 其他图片保持比例缩放\n          targetSize = this.calculateSize(\n            imageInfo.width,\n            imageInfo.height,\n            config.maxWidth || 800,\n            config.maxHeight || 800\n          )\n        }\n\n        // 如果需要裁剪，先裁剪再压缩\n        let processedPath = filePath\n        if (cropParams) {\n          const cropResult = await new Promise((resolve, reject) => {\n            const width = cropParams.sourceWidth - cropParams.offsetX * 2\n            const height = cropParams.sourceHeight - cropParams.offsetY * 2\n            uni.cropImage({\n              src: filePath,\n              cropOffset: [cropParams.offsetX, cropParams.offsetY],\n              cropScale: `${width}:${height}`,\n              success: (res) => resolve(res.tempFilePath),\n              fail: (err) => reject(new Error('裁剪失败: ' + err.errMsg))\n            })\n          })\n          processedPath = cropResult\n        }\n\n        // 第一轮压缩：使用较高质量\n        const firstCompress = await this.wxCompressImage(processedPath, config.quality || 0.6)\n        const firstInfo = await this.getFileInfoAsync(firstCompress.tempFilePath)\n\n        // 如果第一轮压缩后还是太大，进行第二轮压缩\n        if (firstInfo.size > config.maxSize) {\n          const secondCompress = await this.wxCompressImage(\n            firstCompress.tempFilePath, \n            (config.quality || 0.6) * 0.7 // 降低30%质量\n          )\n          const secondInfo = await this.getFileInfoAsync(secondCompress.tempFilePath)\n\n          return {\n            path: secondCompress.tempFilePath,\n            size: secondInfo.size,\n            width: targetSize.width,\n            height: targetSize.height,\n            compressed: true,\n            originalSize: originalSize,\n            compressionRatio: (1 - secondInfo.size / originalSize) * 100\n          }\n        }\n\n        return {\n          path: firstCompress.tempFilePath,\n          size: firstInfo.size,\n          width: targetSize.width,\n          height: targetSize.height,\n          compressed: true,\n          originalSize: originalSize,\n          compressionRatio: (1 - firstInfo.size / originalSize) * 100\n        }\n      } catch (error) {\n        throw new Error('小程序压缩失败: ' + error.message)\n      }\n    }\n\n  \n    /**\n     * 调用微信压缩图片API\n     * @param {string} filePath - 图片路径\n     * @param {number} quality - 压缩质量\n     * @returns {Promise} 压缩结果\n     */\n\n    wxCompressImage(filePath, quality) {\n      return new Promise((resolve, reject) => {\n        uni.compressImage({\n          src: filePath,\n          quality: Math.floor(quality * 100),\n          success: resolve,\n          fail: (error) => reject(new Error('压缩失败: ' + error.errMsg))\n        })\n      })\n    }\n\n  \n    /**\n     * 获取图片信息\n     * @param {string} filePath - 图片路径\n     * @returns {Promise} 图片信息\n     */\n\n    getImageInfoAsync(filePath) {\n      return new Promise((resolve, reject) => {\n        uni.getImageInfo({\n          src: filePath,\n          success: resolve,\n          fail: (error) => reject(new Error('获取图片信息失败: ' + error.errMsg))\n        })\n      })\n    }\n\n  \n    /**\n     * 获取文件信息\n     * @param {string} filePath - 文件路径\n     * @returns {Promise} 文件信息\n     */\n\n    getFileInfoAsync(filePath) {\n      return new Promise((resolve, reject) => {\n        uni.getFileInfo({\n          filePath: filePath,\n          success: resolve,\n          fail: (error) => reject(new Error('获取文件信息失败: ' + error.errMsg))\n        })\n      })\n    }\n\n  \n    /**\n     * 计算目标尺寸\n     * @param {number} width - 原始宽度\n     * @param {number} height - 原始高度\n     * @param {number} maxWidth - 最大宽度\n     * @param {number} maxHeight - 最大高度\n     * @returns {object} 目标尺寸\n     */\n    calculateSize(width, height, maxWidth, maxHeight) {\n      let targetWidth = width\n      let targetHeight = height\n      \n      // 如果宽度超过限制\n      if (width > maxWidth) {\n        targetWidth = maxWidth\n        targetHeight = Math.round(height * (maxWidth / width))\n      }\n      \n      // 如果高度超过限制\n      if (targetHeight > maxHeight) {\n        targetHeight = maxHeight\n        targetWidth = Math.round(width * (maxHeight / height))\n      }\n      \n      return {\n        width: targetWidth,\n        height: targetHeight\n      }\n    }\n\n    /**\n     * 计算裁剪尺寸（保持宽高比）\n     * @param {number} width - 原始宽度\n     * @param {number} height - 原始高度\n     * @param {number} targetWidth - 目标宽度\n     * @param {number} targetHeight - 目标高度\n     * @returns {object} 裁剪参数\n     */\n    calculateCropSize(width, height, targetWidth, targetHeight) {\n      // 计算最小的缩放比例，确保裁剪区域不会超出原图\n      const scale = Math.min(width / targetWidth, height / targetHeight)\n      \n      // 计算裁剪区域的实际尺寸\n      const cropWidth = targetWidth * scale\n      const cropHeight = targetHeight * scale\n      \n      // 计算偏移量，使裁剪区域居中\n      const offsetX = Math.max(0, (width - cropWidth) / 2)\n      const offsetY = Math.max(0, (height - cropHeight) / 2)\n      \n      return {\n        sourceWidth: width,\n        sourceHeight: height,\n        targetWidth: targetWidth,\n        targetHeight: targetHeight,\n        offsetX: Math.round(offsetX),\n        offsetY: Math.round(offsetY),\n        scale: scale\n      }\n    }\n  \n    /**\n     * 获取文件大小\n     * @param {string} filePath - 文件路径\n     * @returns {Promise<number>} 文件大小\n     */\n    async getFileSize(filePath) {\n      try {\n        const info = await this.getFileInfoAsync(filePath)\n        return info.size\n      } catch (error) {\n        throw new Error('获取文件大小失败: ' + error.message)\n      }\n    }\n  \n    /**\n     * 格式化文件大小\n     * @param {number} bytes - 字节数\n     * @returns {string} 格式化后的大小\n     */\n    formatFileSize(bytes) {\n      if (bytes === 0) return '0 B'\n      const k = 1024\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i]\n    }\n  \n    /**\n     * 验证图片格式\n     * @param {string} fileName - 文件名\n     * @returns {object} 验证结果\n     */\n    validateImageFormat(fileName) {\n      const allowedFormats = ['jpg', 'jpeg', 'png', 'webp']\n      const extension = fileName.split('.').pop()?.toLowerCase()\n      \n      if (!extension || !allowedFormats.includes(extension)) {\n        return {\n          valid: false,\n          message: '不支持的图片格式，请选择JPG、PNG或WebP格式'\n        }\n      }\n      \n      return {\n        valid: true,\n        format: extension\n      }\n    }\n  \n    /**\n     * 生成缩略图\n     * @param {string} filePath - 图片路径\n     * @param {number} size - 缩略图尺寸\n     * @returns {Promise<object>} 缩略图信息\n     */\n    async generateThumbnail(filePath, size = 100) {\n      try {\n        const result = await this.compressImage(filePath, 'honor', {\n          maxWidth: size,\n          maxHeight: size,\n          quality: 0.6,\n          maxSize: 50 * 1024 // 50KB\n        })\n        \n        return result\n      } catch (error) {\n        throw new Error('生成缩略图失败: ' + error.message)\n      }\n    }\n  \n    /**\n     * 批量压缩图片\n     * @param {Array<string>} filePaths - 图片路径数组\n     * @param {string} type - 图片类型\n     * @param {Function} progressCallback - 进度回调\n     * @returns {Promise<Array>} 压缩结果数组\n     */\n    async batchCompressImages(filePaths, type = 'honor', progressCallback) {\n      const total = filePaths.length\n      const results = []\n      \n      for (let i = 0; i < total; i++) {\n        try {\n          const result = await this.compressImage(filePaths[i], type)\n          results.push({\n            success: result.success,\n            result: result.success ? result : null,\n            error: result.success ? null : result.error\n          })\n          \n          if (progressCallback) {\n            progressCallback({\n              current: i + 1,\n              total,\n              path: filePaths[i],\n              success: result.success\n            })\n          }\n        } catch (error) {\n          results.push({\n            success: false,\n            result: null,\n            error: error.message,\n            path: filePaths[i]\n          })\n          \n          if (progressCallback) {\n            progressCallback({\n              current: i + 1,\n              total,\n              path: filePaths[i],\n              success: false,\n              error: error.message\n            })\n          }\n        }\n      }\n      \n      return results\n    }\n  }\n  \n  export default new ImageUtils()"], "sourceRoot": ""}
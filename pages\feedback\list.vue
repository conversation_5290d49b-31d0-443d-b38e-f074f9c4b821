<template>
	<view>
		<view class="uni-container">			
			<view class="db-container">
				<!-- 搜索区域统一处理 -->
				<!-- #ifdef MP-WEIXIN -->
				<uni-collapse>
					<uni-collapse-item title="搜索筛选" title-border="none" :border="false">
				<!-- #endif -->
				
				<!-- 状态和紧急程度选择 -->
				<view class="search-box">
					<view class="select-row">
						<view class="select-item">
							<!-- #ifdef MP-WEIXIN -->
							<view class="picker-button" @click="showStatusPicker">
								<text class="picker-text" :class="{ 'placeholder': !searchParams.status }">
									{{ getStatusText(searchParams.status) || '工作流状态' }}
								</text>
								<text class="picker-arrow">▼</text>
							</view>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<uni-data-select 
								v-model="searchParams.status"
								:localdata="statusOptions" 
								placeholder="工作流状态"
								:clear="!!searchParams.status" 
								@change="onStatusChange" />
							<!-- #endif -->
						</view>
						<view class="select-item">
							<!-- #ifdef MP-WEIXIN -->
							<view class="picker-button" @click="showUrgencyPicker">
								<text class="picker-text" :class="{ 'placeholder': !searchParams.urgency }">
									{{ getUrgencyText(searchParams.urgency) || '紧急程度' }}
								</text>
								<text class="picker-arrow">▼</text>
							</view>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<uni-data-select 
								v-model="searchParams.urgency"
								:localdata="urgencyOptions" 
								placeholder="紧急程度"
								:clear="!!searchParams.urgency" 
								@change="onUrgencyChange" />
							<!-- #endif -->
						</view>
					</view>
				</view>

				<!-- 搜索区域 -->
				<view class="search-box">
					<!-- 文本搜索 -->
					<view class="search-row">
						<view class="search-item full-width">
							<uni-easyinput v-model="searchParams.keyword" placeholder="搜索姓名、描述或项目" 
								:clearable="true" @input="onKeywordSearch" />
						</view>
					</view>

					<!-- 下拉选择 -->
					<view class="select-row">
						<view class="select-item">
							<!-- #ifdef MP-WEIXIN -->
							<view class="picker-button" @click="showProjectPicker">
								<text class="picker-text" :class="{ 'placeholder': !searchParams.project }">
									{{ getProjectText(searchParams.project) || '找茬项目' }}
								</text>
								<text class="picker-arrow">▼</text>
							</view>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<uni-data-select 
								v-model="searchParams.project"
								:localdata="projectOptions" 
								placeholder="找茬项目"
								:clear="!!searchParams.project" 
								@change="onProjectChange" />
							<!-- #endif -->
						</view>
						<view class="select-item">
							<!-- #ifdef MP-WEIXIN -->
							<view class="picker-button" @click="showResponsiblePicker">
								<text class="picker-text" :class="{ 'placeholder': !searchParams.responsible }">
									{{ getResponsibleText(searchParams.responsible) || '责任人' }}
								</text>
								<text class="picker-arrow">▼</text>
							</view>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<uni-data-select 
								v-model="searchParams.responsible" 
								:localdata="responsibleOptions"
								placeholder="责任人" 
								:clear="!!searchParams.responsible" 
								@change="onResponsibleChange" />
							<!-- #endif -->
						</view>
					</view>
				</view>
				
				<!-- 日期搜索区域 -->
				<view class="search-box date-section">
					<!-- 日期搜索 -->
					<view class="date-row">
						<view class="date-item full-width">
							<text class="search-label">创建日期：</text>
							<uni-datetime-picker type="daterange" v-model="createDateRange"
								@change="onCreateDateChange" :clear-icon="true" />
						</view>
					</view>
				</view>
				
				<!-- #ifdef MP-WEIXIN -->
					</uni-collapse-item>
				</uni-collapse>
				<!-- #endif -->				
				
				<!-- 添加搜索和表格区域之间的分隔线 -->
				<view class="search-table-divider"></view>
				
				<!-- 数据显示区域 -->
				<view class="data-area">
					<!-- 有数据时显示表格 -->
					<view v-if="feedbackList.length > 0">
						<uni-table ref="table" :loading="isLoading" :emptyText="'没有更多数据'" border stripe>
							<uni-tr>
								<uni-th align="center">序号</uni-th>
								<uni-th align="center">姓名</uni-th>
								<uni-th align="center">找茬项目</uni-th>
								<uni-th align="center" width="360">问题描述</uni-th>
								<uni-th align="center" width="240">图片</uni-th>
								<uni-th align="center">创建时间</uni-th>
								<uni-th align="center">负责人</uni-th>
								<uni-th align="center">状态</uni-th>
								<uni-th align="center">进度</uni-th>
								<uni-th align="center">时效</uni-th>
								<uni-th align="center">理由</uni-th>
								<uni-th align="center" v-if="hasOperationPermission" width="120">操作</uni-th>
							</uni-tr>
							<uni-tr v-for="(item,index) in feedbackList" :key="item._id">
								<uni-td align="center">{{ currentPageStart + index + 1 }}</uni-td>
								<uni-td align="center">{{item.name}}</uni-td>
								<uni-td align="center">{{item.project}}</uni-td>
								<uni-td align="center">
									<view class="description-cell">
										<text class="description-text" :class="{ 'text-truncate': !item.isExpanded }">
											{{item.description}}
										</text>
										<view v-if="item.description && item.description.length > 45"
											class="expand-button" @click.stop="toggleExpand(index)">
											{{ item.isExpanded ? '收起' : '展开' }}
										</view>
									</view>
								</uni-td>
								<uni-td align="center">
									<view v-if="item.images && item.images.length > 0" class="image-container">
										<!-- 显示第一张图片 -->
										<view class="image-wrapper">
											<image v-if="item.images.length > 0" :src="item.images[0]" mode="aspectFit"
												class="image-hover" @error="handleImageError" lazy-load
												@click.stop="previewImage(item.images, 0)" />
										</view>

										<!-- 显示第二张图片，如果有更多则显示 +n -->
										<view v-if="item.images.length > 1" class="image-wrapper">
											<image :src="item.images[1]" mode="aspectFit" class="image-hover" lazy-load
												@error="handleImageError" @click.stop="previewImage(item.images, 1)" />
											<!-- 显示剩余图片数量 -->
											<view v-if="item.images.length > 2" class="image-overlay">
												+{{item.images.length - 2}}
											</view>
										</view>
									</view>
									<text v-else class="no-image">无图片</text>
								</uni-td>
								<uni-td align="center">
									<text class="time-text">{{formatTime(item.createTime, item)}}</text>
								</uni-td>
								<uni-td align="center">
									<view v-if="item.responsibleInfo" class="responsible-info">
										<text class="responsible-name">{{item.responsibleInfo.name}}</text>
										<text class="assigned-time" v-if="item.responsibleInfo.assignedTime">
											{{formatTime(item.responsibleInfo.assignedTime, item)}}
										</text>
									</view>
									<text v-else class="no-responsible">未指派</text>
								</uni-td>
								<uni-td align="center">
									<view class="status-cell">
									<view class="status-badge" :style="{ backgroundColor: item.statusInfo.color }">
										{{item.statusInfo.name}}
									</view>
									</view>
								</uni-td>
								<uni-td align="center">
									<view class="progress-cell">
										<view class="progress-bar">
											<view class="progress-fill" :style="{ width: item.progress + '%' }"></view>
										</view>
										<text class="progress-text">{{item.progress}}%</text>
									</view>
								</uni-td>
								<uni-td align="center">
								<view class="timing-cell">
								<view class="timing-badge" :class="item.timing.urgency" :title="item.timing.description">
									{{item.timing.description || (item.timing.daysPassed + '天')}}
								</view>
								<text v-if="item.timing.isOverdue" class="overdue-text">已超期</text>
							</view>
								</uni-td>
								<uni-td align="center">
									<view class="remarks-cell">
										<view v-if="getReasonDisplay(item) !== '-'" class="reason-text" v-html="formatReasonText(getReasonDisplay(item))"></view>
										<text v-else class="no-reason">-</text>
									</view>
								</uni-td>
								<uni-td align="center" v-if="hasOperationPermission">
									<view class="action-buttons">
										<button class="action-btn view-btn" @click="viewDetail(item)">查看</button>
										<button v-if="hasEditPermission && item.availableActions.includes('edit')" 
											class="action-btn edit-btn" @click="editItem(item)">编辑</button>
										<button v-if="hasDeletePermission" 
											class="action-btn delete-btn" @click="deleteItem(item)">删除</button>
										<button v-if="item.availableActions.includes('submit_completion')" 
											class="action-btn complete-btn" @click="submitCompletion(item)">完成任务</button>
									</view>
								</uni-td>
							</uni-tr>
						</uni-table>
						
						<!-- 分页组件 -->
						<view class="pagination-container">
							<uni-pagination 
								:total="totalCount" 
								:pageSize="pageSize" 
								:current="currentPage"
								@change="onPageChange"
								show-icon="true">
							</uni-pagination>
						</view>
					</view>
					
					<!-- 无数据时的不同状态 -->
					<view v-else class="no-data-area">
						<!-- 初次加载中 -->
						<view v-if="!hasInitialized" class="data-loading">
							<uni-load-more status="loading" :content-text="{ contentdown: '正在加载数据...' }"></uni-load-more>
						</view>
						<!-- 搜索加载中 -->
						<view v-else-if="isLoading" class="data-loading">
							<uni-load-more status="loading" :content-text="{ contentdown: '搜索中...' }"></uni-load-more>
						</view>
						<!-- 数据为空 -->
						<p-empty-state v-else 
							type="data" 
							text="暂无问题反馈数据"
							size="medium"
						></p-empty-state>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 微信小程序弹窗选择器 -->
		<!-- #ifdef MP-WEIXIN -->
		<!-- 状态选择弹窗 -->
		<uni-popup ref="statusPopup" type="bottom" :safe-area="false">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">选择工作流状态</text>
					<text class="popup-close" @click="closeStatusPicker">取消</text>
				</view>
				<view class="popup-body">
					<view class="popup-item" 
						v-for="item in statusOptions" 
						:key="item.value"
						:class="{ 'active': searchParams.status === item.value }"
						@click="selectStatus(item.value)">
						<text>{{ item.text }}</text>
						<text v-if="searchParams.status === item.value" class="check-icon">✓</text>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 紧急程度选择弹窗 -->
		<uni-popup ref="urgencyPopup" type="bottom" :safe-area="false">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">选择紧急程度</text>
					<text class="popup-close" @click="closeUrgencyPicker">取消</text>
				</view>
				<view class="popup-body">
					<view class="popup-item" 
						v-for="item in urgencyOptions" 
						:key="item.value"
						:class="{ 'active': searchParams.urgency === item.value }"
						@click="selectUrgency(item.value)">
						<text>{{ item.text }}</text>
						<text v-if="searchParams.urgency === item.value" class="check-icon">✓</text>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 项目选择弹窗 -->
		<uni-popup ref="projectPopup" type="bottom" :safe-area="false">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">选择找茬项目</text>
					<text class="popup-close" @click="closeProjectPicker">取消</text>
				</view>
				<view class="popup-body">
					<view class="popup-item" 
						v-for="item in projectOptions" 
						:key="item.value"
						:class="{ 'active': searchParams.project === item.value }"
						@click="selectProject(item.value)">
						<text>{{ item.text }}</text>
						<text v-if="searchParams.project === item.value" class="check-icon">✓</text>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 责任人选择弹窗 -->
		<uni-popup ref="responsiblePopup" type="bottom" :safe-area="false">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">选择责任人</text>
					<text class="popup-close" @click="closeResponsiblePicker">取消</text>
				</view>
				<view class="popup-body">
					<view class="popup-item" 
						v-for="item in responsibleOptions" 
						:key="item.value"
						:class="{ 'active': searchParams.responsible === item.value }"
						@click="selectResponsible(item.value)">
						<text>{{ item.text }}</text>
						<text v-if="searchParams.responsible === item.value" class="check-icon">✓</text>
					</view>
				</view>
			</view>
		</uni-popup>
		<!-- #endif -->
	</view>
</template>

<style scoped>
	/* 搜索区域样式 */
	.search-box {
		background: #f9fafb;
		border: 1rpx solid #e2e8f0;
		border-radius: 8rpx;
		margin-bottom: 16rpx;
		padding: 16rpx;
	}

.full-width {
	flex: 1;
	width: 100%;
}

/* 状态徽章样式 */
.status-cell {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4rpx;
}

.status-badge {
	padding: 6rpx 12rpx;
	color: white;
	border-radius: 16rpx;
	font-size: 24rpx;
	text-align: center;
	min-width: 120rpx;
}

.workflow-type {
	font-size: 20rpx;
	color: #999;
	background: #f0f0f0;
	padding: 2rpx 8rpx;
	border-radius: 8rpx;
}

/* 进度条样式 */
.progress-cell {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.progress-bar {
	width: 80rpx;
	height: 12rpx;
	background: #e0e0e0;
	border-radius: 6rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #4caf50, #8bc34a);
	transition: width 0.3s ease;
}

.progress-text {
	font-size: 22rpx;
	color: #666;
}

/* 时效显示样式 */
.timing-cell {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4rpx;
}

.timing-badge {
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	font-size: 24rpx;
	color: white;
	min-width: 80rpx;
	text-align: center;
}

.timing-badge.normal {
	background: #4caf50;
}

.timing-badge.warning {
	background: #ff9800;
}

.timing-badge.urgent {
	background: #f44336;
}

.timing-badge.completed {
	background: #4caf50;
	color: #fff;
}

.timing-badge.terminated {
	background: #9e9e9e;
	color: #fff;
}

.overdue-text {
	font-size: 20rpx;
	color: #f44336;
	font-weight: bold;
}

/* 负责人信息样式 */
.responsible-info {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4rpx;
}

.responsible-name {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
}

.assigned-time {
	font-size: 20rpx;
	color: #999;
}

.no-responsible {
	color: #ccc;
	font-style: italic;
}

/* 操作按钮样式 */
.action-buttons {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
	justify-content: center;
}

.action-btn {
	padding: 8rpx 16rpx;
	border: none;
	border-radius: 16rpx;
	font-size: 22rpx;
	color: white;
	min-width: 80rpx;
}

/* 微信小程序按钮垂直排列 */
/* #ifdef MP-WEIXIN */
.action-buttons {
	flex-direction: column;
	gap: 6rpx;
	width: 100%;
}

.action-btn {
	width: 100%;
	padding: 8rpx 16rpx;
	font-size: 24rpx;
	min-width: auto;
	border-radius: 12rpx;
}
/* #endif */

.view-btn {
	background: #2196f3;
}

.edit-btn {
	background: #4caf50;
}

.assign-btn {
	background: #ff9800;
}

.complete-btn {
	background: #00bcd4;
}

.delete-btn {
	background: #f44336;
}

.approve-btn {
	background: #9c27b0;
}

.convert-btn {
	background: #607d8b;
}

.archive-btn {
	background: #9e9e9e;
}

/* 图片样式优化 */
.image-container {
	display: flex;
	gap: 8rpx;
	justify-content: center;
	align-items: center;
	min-height: 120rpx;
	margin: 0;
	padding: 0;
}

.image-wrapper {
	position: relative;
	width: 160rpx;
	height: 160rpx;
	cursor: pointer;
	overflow: hidden;
	border: 1px solid #e2e8f0;
	transition: all 0.3s ease;
}

.image-wrapper:hover {
	transform: translateY(-2px);
	border-color: #cbd5e1;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.image-wrapper image {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
	object-fit: cover;
}

/* 微信小程序图片优化 */
/* #ifdef MP-WEIXIN */
.image-wrapper {
	width: 140rpx !important;
	height: 140rpx !important;
}

.image-container {
	gap: 16rpx;
	min-height: 120rpx;
}
/* #endif */

.image-overlay {
	position: absolute;
	top: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.7);
	color: white;
	font-size: 18rpx;
	padding: 2rpx 6rpx;
	border-radius: 0 8rpx 0 8rpx;
}

.no-image {
	color: #ccc;
	font-style: italic;
	font-size: 24rpx;
}

/* 时间文本样式 */
.time-text {
	font-size: 24rpx;
	color: #666;
}

/* 备注区域样式 */
.remarks-cell {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
	white-space: pre-wrap;
	word-break: break-word;
}

/* 分页容器样式 */
.pagination-container {
	margin: 32rpx 0;
	display: flex;
	justify-content: center;
}

/* 数据区域样式 */
.data-area {
	min-height: 400rpx;
	background: #fff;
	border-radius: 8rpx;
}

.no-data-area {
	min-height: 400rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #fff;
	border-radius: 8rpx;
}

/* 数据加载区域样式 */
.data-loading {
	padding: 60rpx 0;
	text-align: center;
	background: #fff;
	border-radius: 8rpx;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.workflow-summary {
		flex-direction: column;
		gap: 8rpx;
	}
	
	.action-buttons {
		flex-direction: column;
	}
	
	.action-btn {
		width: 100%;
	}
}
</style>

<script>
	import { getCacheKey, CACHE_KEYS, cacheManager } from '@/utils/cache.js';
	import PEmptyState from '@/components/p-empty-state/p-empty-state.vue';

	// 添加防抖函数作为外部单例
	const debounce = function(fn, delay = 300) {
		let timer = null;
		return function(...args) {
			if (timer) clearTimeout(timer);
			timer = setTimeout(() => {
				fn.apply(this, args);
			}, delay);
		};
	};

	const db = uniCloud.database()
	const dbCmd = db.command
	// 表查询配置
	const dbOrderBy = 'createTime desc' // 按创建时间降序排列
	const dbSearchFields = [] // 模糊搜索字段，支持模糊搜索的字段列表
	// 分页配置
	const pageSize = 10
	const pageCurrent = 1

	const orderByMapping = {
		"ascending": "asc",
		"descending": "desc"
	}

	export default {
		components: {
			PEmptyState
		},
		data() {
			// 初始化本地分页数据，与udb分开管理
			const localPagination = {
				size: pageSize,
				current: pageCurrent,
				count: 0
			};
			
			return {
				responsibleOptions: [],
				responsibleMap: {},
				createDateRange: [],
				searchParams: {
					keyword: '',
					project: '',
					responsible: '',
					status: '',
					urgency: ''
				},
				projectOptions: [
					{ text: '全部', value: '' },
					{ text: '安全找茬', value: '安全找茬' },
					{ text: '设备找茬', value: '设备找茬' },
					{ text: '其他找茬', value: '其他找茬' }
				],
				isLoading: false,
				isTokenValid: true,
				userRoles: [], // 用户角色列表
	
				statusOptions: [],
				urgencyOptions: [],
				feedbackList: [],
				totalCount: 0,
				currentPage: 1,
				pageSize: 20,
				currentPageStart: 0,
				searchTimer: null,
				hasInitialized: false, // 添加初始化标记
				lastRefreshTime: 0, // 上次刷新时间
				isPageVisible: true, // 页面是否可见
				needsRefreshOnShow: false, // 是否需要在页面显示时刷新
			}
		},
		computed: {
			// 判断是否有权限查看操作按钮 - 只有特定角色才能看到
			hasOperationPermission() {
				if (!this.isTokenValid) return false;
				
				// 有权限的角色：负责人、主管、厂长、副厂长、管理员
				const authorizedRoles = ['responsible', 'supervisor', 'GM', 'PM', 'admin', 'manager'];
				return this.userRoles.some(role => authorizedRoles.includes(role));
			},
			
			// 编辑权限：只有管理员可以编辑
			hasEditPermission() {
				if (!this.isTokenValid) return false;
				return this.userRoles.some(role => ['admin', 'manager'].includes(role));
			},
			
			// 删除权限：管理员和厂长可以删除
			hasDeletePermission() {
				if (!this.isTokenValid) return false;
				return this.userRoles.some(role => ['admin', 'manager', 'GM'].includes(role));
			}
		},
		created() {
			// 页面创建时立即设置加载状态，确保页面显示正确
			this.isLoading = true;
			
			// 监听角标管理器的跨设备更新事件
			uni.$on('cross-device-update-detected', (data) => {
				if (data.silent) {
					// 智能判断是否需要刷新
					const shouldRefresh = this.shouldRefreshOnCrossDeviceUpdate(data);
					if (shouldRefresh) {
						console.log('反馈列表页面收到跨设备更新通知，静默刷新数据');
						// 静默刷新数据
						this.silentRefresh();
					}
				}
			});			

		},
		async onLoad() {
			// 确保加载状态正确
			this.isLoading = true;
			this.hasInitialized = false;
			
			try {
				// 检查登录状态，设置token有效性
				this.checkAndSetTokenStatus();
				
				await this.initializeWorkflowOptions();
				await this.loadResponsibleMap();
				await this.loadFeedbackList();
				// 添加请求拦截器
				this.setupRequestInterceptor();
				// 监听token过期事件
				this.setupTokenEventListeners();
				// 监听新反馈提交事件
				this.setupFeedbackEventListeners();
			} catch (error) {
				uni.showToast({
					title: '页面加载失败',
					icon: 'none'
				});
				// 即使加载失败也要标记为已初始化，避免一直显示加载状态
				this.hasInitialized = true;
				this.isLoading = false;
			}
		},
		onReady() {
			// 移除原有的udb.loadData，使用新的API
		},
		onPullDownRefresh() {
			this.loadFeedbackList().finally(() => {
				uni.stopPullDownRefresh();
				this.hasInitialized = true; // 确保下拉刷新后也标记为已初始化
			});
		},
		onShow() {
			// 标记页面为可见状态
			this.isPageVisible = true;
			
			// 每次显示页面时检查token状态
			const oldTokenStatus = this.isTokenValid;
			this.checkAndSetTokenStatus();
			
			// 如果token状态发生变化，重新加载数据
			if (oldTokenStatus !== this.isTokenValid) {
				// 重新加载负责人数据（根据登录状态决定是否加载）
				this.loadResponsibleMap();
				this.loadFeedbackList();
				this.needsRefreshOnShow = false;
				return;
			}
			
			// 如果有待处理的刷新请求，立即执行
			if (this.needsRefreshOnShow) {
				this.loadFeedbackList();
				this.lastRefreshTime = Date.now();
				this.needsRefreshOnShow = false;
				return;
			}
			
			// 智能刷新策略：只在必要时自动刷新
			const now = Date.now();
			const lastRefresh = this.lastRefreshTime || 0;
			const timeSinceLastRefresh = now - lastRefresh;
			
			// 自动刷新条件（优化后）：
			// 1. 初次显示页面
			// 2. 距离上次刷新超过5分钟（避免频繁刷新）
			if (!this.hasInitialized) {
				// 初次显示，需要加载数据
				this.loadFeedbackList();
				this.lastRefreshTime = now;
			} else if (timeSinceLastRefresh > 5 * 60 * 1000) {
				// 超过5分钟，静默刷新（不显示加载状态）
				this.silentRefresh();
			}		
		},
		
		onHide() {
			// 标记页面为不可见状态
			this.isPageVisible = false;
		},
		onUnload() {
			// 移除事件监听
			this.removeTokenEventListeners();
			this.removeFeedbackEventListeners();
			// 移除跨设备更新事件监听
			uni.$off('cross-device-update-detected');
		},
		methods: {
			// ===== 原有方法 =====
			
			// 统一的错误处理函数
			handleError(message, error) {
				// 向用户显示友好提示
				uni.showToast({
					title: message,
					icon: 'none'
				});
			},

			// 静默刷新 - 在后台更新数据，不影响用户体验
			async silentRefresh() {
				try {
					// 使用静默模式刷新数据
					await this.loadFeedbackList(true);
				} catch (error) {
					console.error('❌ 静默刷新失败:', error);
					// 静默刷新失败不显示错误提示，避免打扰用户
				}
			},
			
			// 加载负责人映射 - 使用缓存系统优化性能
			async loadResponsibleMap() {
				// 如果未登录，跳过负责人数据加载，避免权限错误
				if (!this.isTokenValid) {
					// 设置默认的负责人选项
					this.responsibleOptions = [
						{ text: '全部', value: '' }
					];
					this.responsibleMap = {};
					return;
				}
				
				try {
					// 先尝试从缓存获取负责人映射
					this.responsibleMap = cacheManager.getResponsibleMap();
					
					// 使用缓存管理器获取负责人列表，如果缓存未命中则从服务器获取
					const responsibleList = await cacheManager.getResponsibleList(async () => {
					// 通过云函数获取负责人数据，避免权限问题
						const res = await uniCloud.callFunction({
							name: 'feedback-list',
							data: {
								action: 'getResponsibleUsers'
							}
						});

						if (!res || !res.result || res.result.code !== 0) {
							throw new Error(res.result?.message || '获取责任人数据失败');
						}

						return res.result.data || [];
					});

					// 构建负责人选项
					this.responsibleOptions = [
						{ text: '全部', value: '' },
						...responsibleList.map(user => ({
							value: user._id,
							text: user.nickname || user.username || '-'
						}))
					];

					// 更新负责人映射
					this.responsibleMap = responsibleList.reduce((map, user) => {
						map[user._id] = user.nickname || user.username || '-';
						return map;
					}, {});
					
	
				} catch (e) {
					// 只在登录状态下才显示错误提示
					if (this.isTokenValid) {
						this.handleError('获取责任人数据失败', e);
						console.error('❌ 负责人数据加载失败:', e);
					}
					
					// 设置默认选项，避免界面异常
					this.responsibleOptions = [
						{ text: '全部', value: '' }
					];
					this.responsibleMap = {};
				}
			},			

			getResponsibleName(responsibleId) {
				if (!responsibleId) return '-';
				return this.responsibleMap[responsibleId] || '-';
			},
			previewImage(images, index) {
				if (!images || !Array.isArray(images) || images.length === 0) {
					this.handleError('无图片可预览');
					return;
				}
				
				// 微信小程序特殊处理
				// #ifdef MP-WEIXIN
				try {
					uni.previewImage({
						urls: images,
						current: index || 0,
						fail: (err) => {
							// 预览失败不显示错误提示，因为这可能是微信小程序的限制导致
						}
					});
				} catch (error) {
					// 预览图片出错，静默处理
				}
				// #endif
				
				// 非微信小程序处理
				// #ifndef MP-WEIXIN
				uni.previewImage({
					urls: images,
					current: index || 0,
					fail: (err) => {
						this.handleError('预览图片失败', err);
					}
				});
				// #endif
			},
			handleImageError(event) {
				event.target.src = '/static/empty/default-image.png';
			},


			// 分页变化
			onPageChange(e) {
				this.currentPage = e.current;
				this.loadFeedbackList();
			},
			
			// 原搜索方法更新为调用统一方法
			// 项目选择变化
			onProjectChange(value) {
				this.searchParams.project = value;
				this.currentPage = 1;
				this.loadFeedbackList();
			},
			
			// 负责人选择变化
			onResponsibleChange(value) {
				this.searchParams.responsible = value;
				this.currentPage = 1;
				this.loadFeedbackList();
			},
			
			// 创建日期变化
			onCreateDateChange(e) {
				this.createDateRange = e;
				this.currentPage = 1;
				this.loadFeedbackList();
			},

			// 验证响应数据
			validateResponse(res) {
				if (!res) return false;
				if (!res.result) return false;
				if (!res.result.data) return false;
				return true;
			},

			// 检查并设置token状态
			checkAndSetTokenStatus() {
				const token = uni.getStorageSync('uni_id_token');
				if (!token) {
					this.isTokenValid = false;
					return;
				}
				
				const tokenExpired = uni.getStorageSync('uni_id_token_expired');
				if (tokenExpired && tokenExpired < Date.now()) {
					this.isTokenValid = false;
					// 清除过期的token
					this.handleTokenInvalid();
				} else {
					this.isTokenValid = true;
				}
			},
			
			// 检查token状态
			async checkTokenStatus() {
				try {
					const token = uni.getStorageSync('uni_id_token');
					if (!token) {
						this.handleTokenInvalid();
						return;
					}
					const tokenExpired = uni.getStorageSync('uni_id_token_expired');
					if (tokenExpired < Date.now()) {
						this.handleTokenInvalid();
					}
				} catch (error) {
					this.handleError('验证登录状态失败', error);
				}
			},
			
			// 处理token失效 - 清除用户相关缓存
			handleTokenInvalid() {
				this.isTokenValid = false;
				// 清除登录信息
				uni.removeStorageSync('uni_id_token');
				uni.removeStorageSync('uni_id_token_expired');
				uni.removeStorageSync('uni-id-pages-userInfo');
				
				// 使用缓存管理器清除用户相关缓存
				cacheManager.clearUserRelatedCache();
				
				// 重新加载数据
				this.loadFeedbackList();
			},
			// 设置请求拦截器
			setupRequestInterceptor() {
				// 添加请求拦截器
				const db = uniCloud.database();
				db.interceptorAdd('callFunction', {
					invoke: (options) => {
						// 请求前的处理
					},
					success: (result) => {
						// 处理token失效的情况
						if (result.result && result.result.code === 'TOKEN_INVALID') {
							this.handleTokenInvalid();
						}
						return result;
					},
					fail: (err) => {
						return err;
					},
					complete: (res) => {
						return res;
					}
				});
			},
			
			// 设置token事件监听
			setupTokenEventListeners() {
				// 监听全局token过期事件
				uni.$on('token-expired', this.handleGlobalTokenExpired);
				uni.$on('token-invalid', this.handleGlobalTokenExpired);
			},
			
			// 移除token事件监听
			removeTokenEventListeners() {
				uni.$off('token-expired', this.handleGlobalTokenExpired);
				uni.$off('token-invalid', this.handleGlobalTokenExpired);
			},
			
			// 处理全局token过期事件 - 清除用户相关缓存
			handleGlobalTokenExpired() {
				this.isTokenValid = false;
				// 清除登录信息
				uni.removeStorageSync('uni_id_token');
				uni.removeStorageSync('uni_id_token_expired');
				uni.removeStorageSync('uni-id-pages-userInfo');
				
				// 使用缓存管理器清除用户相关缓存
				cacheManager.clearUserRelatedCache();
				
				// 重新加载负责人数据（会因为未登录而跳过）
				this.loadResponsibleMap();
				
				// 重新加载数据
				this.loadFeedbackList();
				
				// 强制更新页面，让权限相关的按钮重新渲染
				this.$forceUpdate();
			},
			
			// 设置反馈事件监听
			setupFeedbackEventListeners() {
				// 监听新反馈提交事件
				uni.$on('feedback-submitted', this.handleFeedbackSubmitted);
				// 监听反馈数据更新事件
				uni.$on('feedback-updated', this.handleFeedbackUpdated);
				// 监听任务完成事件
				uni.$on('task-completed', this.handleTaskCompleted);
				// 监听任务状态变化事件
				uni.$on('task-status-changed', this.handleTaskStatusChanged);
			},
			
			// 移除反馈事件监听
			removeFeedbackEventListeners() {
				uni.$off('feedback-submitted', this.handleFeedbackSubmitted);
				uni.$off('feedback-updated', this.handleFeedbackUpdated);
				uni.$off('task-completed', this.handleTaskCompleted);
				uni.$off('task-status-changed', this.handleTaskStatusChanged);
			},
			
						// 处理新反馈提交事件
			handleFeedbackSubmitted(data) {
				// 重置到第一页并刷新数据 - 无论页面是否可见都要刷新
				this.currentPage = 1;
				this.loadFeedbackList();
				this.lastRefreshTime = Date.now(); // 更新刷新时间

				// 清除待刷新标记，因为已经刷新了
				this.needsRefreshOnShow = false;

				// 显示提示（只在页面可见时显示，避免干扰用户）
				if (this.isPageVisible) {
				uni.showToast({
					title: '列表已更新',
					icon: 'success',
					duration: 1500
				});
				}
			},

			// 处理反馈数据更新事件
			handleFeedbackUpdated(data) {
				// 智能刷新：如果是当前页面可见，立即刷新；否则标记需要刷新
				if (this.isPageVisible) {
					this.loadFeedbackList();
					this.lastRefreshTime = Date.now();
				} else {
					// 页面不可见时，标记需要刷新，等页面显示时再刷新
					this.needsRefreshOnShow = true;
				}
			},

			// 处理任务完成事件
			handleTaskCompleted(data) {
				// 刷新当前页数据
				this.loadFeedbackList();
				this.lastRefreshTime = Date.now();

				// 显示提示
				uni.showToast({
					title: '任务状态已更新',
					icon: 'success',
					duration: 1500
				});
			},

			// 处理任务状态变化事件
			handleTaskStatusChanged(data) {
				// 智能刷新策略
				if (this.isPageVisible) {
					this.silentRefresh();
				} else {
					this.needsRefreshOnShow = true;
				}
			},
			// ===== 新工作流相关方法 =====
			
			// 初始化工作流选项 - 使用缓存系统优化
			async initializeWorkflowOptions() {
				// 从缓存获取状态选项，提升页面加载速度
				this.statusOptions = cacheManager.getStatusOptions();
				
				// 从缓存获取紧急程度选项
				this.urgencyOptions = cacheManager.getUrgencyOptions();
				
				// 从缓存获取项目选项
				this.projectOptions = await cacheManager.getProjectOptions();
			},

			/**
			 * 加载问题列表 - 新工作流系统的核心数据获取方法
			 * 
			 * 功能特点：
			 * - 支持多维度搜索筛选（项目、状态、关键词、时间范围、负责人）
			 * - 自动获取用户角色信息，用于权限控制
			 * - 集成分页功能，提升大数据量场景的性能
			 * - 统一的错误处理和用户友好提示
			 * 
			 * 数据流：前端搜索参数 → feedback-list云函数 → 数据库查询 → 增强处理 → 前端显示
			 */
			async loadFeedbackList(silent = false) {
				if (!silent) {
					this.isLoading = true;
				}
				
				try {
					const params = {
						action: 'getList',
						pageSize: this.pageSize,
						pageNum: this.currentPage,
						project: this.searchParams.project || '',
						status: this.searchParams.status || '',
						keyword: this.searchParams.keyword || '',
						urgency: this.searchParams.urgency || '',
						responsible: this.searchParams.responsible || ''
					};
										
					// 添加日期范围
					if (this.createDateRange && this.createDateRange.length === 2) {
						const startDateStr = this.createDateRange[0];
						const endDateStr = this.createDateRange[1];
						
						const startParts = startDateStr.split('-');
						const endParts = endDateStr.split('-');
						
						// 构建本地时间的开始和结束时间戳
						const startDate = new Date(parseInt(startParts[0]), parseInt(startParts[1]) - 1, parseInt(startParts[2]), 0, 0, 0, 0);
						const endDate = new Date(parseInt(endParts[0]), parseInt(endParts[1]) - 1, parseInt(endParts[2]), 23, 59, 59, 999);
						
						params.dateRange = {
							start: startDate.getTime(),
							end: endDate.getTime()
						};
					}

					const res = await uniCloud.callFunction({
						name: 'feedback-list',
						data: params
					});

					if (res.result && res.result.code === 0) {
						const { list, pagination, userInfo } = res.result.data;
						this.feedbackList = list || [];
						this.totalCount = pagination ? pagination.total : 0;
						this.currentPageStart = pagination ? (pagination.pageNum - 1) * pagination.pageSize : 0;
						
						// 更新用户角色信息
						if (userInfo && userInfo.roles) {
							this.userRoles = userInfo.roles;
						} else {
							this.userRoles = [];
						}
					} else {
						throw new Error(res.result ? res.result.message : '未知错误');
					}
				} catch (error) {
					this.feedbackList = [];
					this.totalCount = 0;
					uni.showToast({
						title: '加载数据失败: ' + (error.message || error),
						icon: 'none',
						duration: 3000
					});
				} finally {
					if (!silent) {
						this.isLoading = false;
					}
					this.hasInitialized = true; // 标记为已初始化
					this.lastRefreshTime = Date.now(); // 更新刷新时间
				}
			},

			// 工作流状态变化 - 新工作流系统的状态筛选
			onStatusChange(value) {
				this.searchParams.status = value;
				this.currentPage = 1;
				this.loadFeedbackList();
			},

			// 紧急程度变化
			onUrgencyChange(value) {
				this.searchParams.urgency = value;
				this.currentPage = 1;
				this.loadFeedbackList();
			},

			// 关键词搜索
			onKeywordSearch() {
				// 防抖处理，避免频繁请求
				clearTimeout(this.searchTimer);
				this.searchTimer = setTimeout(() => {
					this.currentPage = 1;
					this.loadFeedbackList();
				}, 300);
			},

			// 查看详情
			viewDetail(item) {
				uni.navigateTo({
					url: `/pages/feedback_pkg/examine?id=${item._id}&readonly=true`
				});
			},

			// 编辑项目
			editItem(item) {
				uni.navigateTo({
					url: `/pages/feedback_pkg/edit?id=${item._id}`
				});
			},

			// 负责人提交工作完成
			async submitCompletion(item) {
				// 直接跳转到我的任务页面进行详细的完成操作
				uni.navigateTo({
					url: `/pages/ucenter_pkg/complete-task?id=${item._id}`,
					fail: (err) => {
						uni.showToast({
							title: '跳转失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 删除项目
			async deleteItem(item) {
				uni.showModal({
					title: '确认删除',
					content: `确定要删除"${item.name}"的问题反馈吗？此操作不可恢复！`,
					confirmText: '删除',
					confirmColor: '#ff4444',
					success: async (res) => {
						if (res.confirm) {
							await this.performDelete(item);
						}
					}
				});
			},
			
			// 执行删除操作
			async performDelete(item) {
				uni.showLoading({
					title: '删除中...',
					mask: true
				});
				
				try {
					// 调用云函数删除数据
					const res = await uniCloud.callFunction({
						name: 'feedback-workflow',
						data: {
							action: 'delete',
							id: item._id
						}
					});
					
					if (res.result.code === 0) {
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
						
						// 发送全局事件通知其他页面数据已更新
						uni.$emit('feedback-updated', {
							action: 'delete',
							id: item._id,
							timestamp: Date.now()
						});
						
						// 重新加载数据
						await this.loadFeedbackList();
					} else {
						throw new Error(res.result.message || '删除失败');
					}
				} catch (error) {
					uni.showToast({
						title: error.message || '删除失败，请重试',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},

			// 分页变化
			onPageChange(e) {
				this.currentPage = e.current;
				this.loadFeedbackList();
			},

			// 格式化时间显示 - 使用后端提供的北京时间
			formatTime(timestamp, item = null) {
				if (!timestamp) return '-';
				
				// 使用后端提供的格式化时间（北京时间）
				if (item && item.createTimeFormatted) {
					return item.createTimeFormatted;
				}
				
				// 后备方案：简单格式化
				const date = new Date(timestamp);
				return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
			},

			// 切换展开/收起
			toggleExpand(index) {
				this.$set(this.feedbackList[index], 'isExpanded', !this.feedbackList[index].isExpanded);
			},
			
			// ===== 微信小程序弹窗选择器相关方法 =====
			// #ifdef MP-WEIXIN
			// 显示状态选择器
			showStatusPicker() {
				this.$refs.statusPopup.open();
			},
			
			// 关闭状态选择器
			closeStatusPicker() {
				this.$refs.statusPopup.close();
			},
			
			// 选择状态
			selectStatus(value) {
				this.searchParams.status = value;
				this.currentPage = 1;
				this.loadFeedbackList();
				this.closeStatusPicker();
			},
			
			// 显示紧急程度选择器
			showUrgencyPicker() {
				this.$refs.urgencyPopup.open();
			},
			
			// 关闭紧急程度选择器
			closeUrgencyPicker() {
				this.$refs.urgencyPopup.close();
			},
			
			// 选择紧急程度
			selectUrgency(value) {
				this.searchParams.urgency = value;
				this.currentPage = 1;
				this.loadFeedbackList();
				this.closeUrgencyPicker();
			},
			
			// 显示项目选择器
			showProjectPicker() {
				this.$refs.projectPopup.open();
			},
			
			// 关闭项目选择器
			closeProjectPicker() {
				this.$refs.projectPopup.close();
			},
			
			// 选择项目
			selectProject(value) {
				this.searchParams.project = value;
				this.currentPage = 1;
				this.loadFeedbackList();
				this.closeProjectPicker();
			},
			
			// 显示责任人选择器
			showResponsiblePicker() {
				this.$refs.responsiblePopup.open();
			},
			
			// 关闭责任人选择器
			closeResponsiblePicker() {
				this.$refs.responsiblePopup.close();
			},
			
			// 选择责任人
			selectResponsible(value) {
				this.searchParams.responsible = value;
				this.currentPage = 1;
				this.loadFeedbackList();
				this.closeResponsiblePicker();
			},
			
			// 获取状态显示文本
			getStatusText(value) {
				if (!value) return '';
				const option = this.statusOptions.find(item => item.value === value);
				return option ? option.text : '';
			},
			
			// 获取紧急程度显示文本
			getUrgencyText(value) {
				if (!value) return '';
				const option = this.urgencyOptions.find(item => item.value === value);
				return option ? option.text : '';
			},
			
			// 获取项目显示文本
			getProjectText(value) {
				if (!value) return '';
				const option = this.projectOptions.find(item => item.value === value);
				return option ? option.text : '';
			},
			
			// 获取责任人显示文本
			getResponsibleText(value) {
				if (!value) return '';
				const option = this.responsibleOptions.find(item => item.value === value);
				return option ? option.text : '';
			},
			// #endif
			
			/**
			 * 智能理由显示 - 根据工作流状态显示合适的理由
			 * 
			 * 逻辑：
			 * 1. 如果流程被终止(terminated)，只显示导致终止的理由
			 * 2. 如果流程正常进行，显示已完成步骤的理由
			 * 3. 不显示未到达步骤的理由
			 */
			getReasonDisplay(item) {
				const status = item.workflowStatus;
				const reasons = [];
				
				// 优先从actionHistory中提取理由（新数据）
				if (item.actionHistory && item.actionHistory.length > 0) {
					// 过滤出审核类型的操作
					const auditActions = item.actionHistory.filter(action => 
						['supervisor_approve', 'supervisor_reject', 'supervisor_meeting',
						 'pm_approve', 'pm_reject', 'gm_approve', 'gm_reject'].includes(action.action)
					);
					
					// 按时间排序并提取理由
					auditActions.sort((a, b) => a.timestamp - b.timestamp).forEach(action => {
						const roleMap = {
							'supervisor_approve': '主管',
							'supervisor_reject': '主管',
							'supervisor_meeting': '主管',
							'pm_approve': '副厂长',
							'pm_reject': '副厂长',
							'gm_approve': '厂长',
							'gm_reject': '厂长'
						};
						
						const roleName = roleMap[action.action];
						if (roleName && action.reason) {
							reasons.push(`${roleName}：${action.reason}`);
						}
					});
				}
				
				return reasons.length > 0 ? reasons.join('\n') : '-';
			},
			
			/**
			 * 格式化理由文本 - 为每个角色的理由添加简洁的样式
			 */
			formatReasonText(reasonText) {
				if (!reasonText || reasonText === '-') {
					return '';
				}
				
				const lines = reasonText.split('\n');
				const formattedLines = lines.map(line => {
					return `<div class="reason-item">${line}</div>`;
				});
				
				return formattedLines.join('');
			},
			
			// 智能判断是否需要刷新数据
			shouldRefreshOnCrossDeviceUpdate(data) {
				// 如果页面不可见，标记需要刷新但不立即刷新
				if (!this.isPageVisible) {
					this.needsRefreshOnShow = true;
					return false;
				}
				
				// 如果距离上次刷新时间太短（小于3秒），仅对非重要更新进行节流
				const timeSinceLastRefresh = Date.now() - (this.lastRefreshTime || 0);
				if (timeSinceLastRefresh < 3000) {
					// 检查是否为重要更新类型
					if (data.updateTypes) {
						const importantTypes = ['feedback_submitted', 'feedback_deleted', 'workflow_status_changed'];
						const hasImportantUpdate = data.updateTypes.some(type => importantTypes.includes(type));
						if (!hasImportantUpdate) {
							return false;
						}
					}
				}
				
				// 如果更新类型包含反馈相关的操作，需要刷新
				if (data.updateTypes && data.updateTypes.length > 0) {
					const relevantTypes = ['workflow_status_changed', 'feedback_submitted', 'feedback_deleted'];
					const hasRelevantUpdate = data.updateTypes.some(type => relevantTypes.includes(type));
					if (hasRelevantUpdate) {
						return true;
					}
				}
				
				// 如果有多个更新记录，可能需要刷新
				if (data.updateCount > 2) {
					return true;
				}
				
				// 如果没有明确的更新类型信息，采用保守策略：刷新
				if (!data.updateTypes || data.updateTypes.length === 0) {
					return true;
				}
				
				return false;
			},
		},
		beforeDestroy() {
			// 移除事件监听
			uni.$off('cross-device-update-detected');
		},
	}
</script>

<style lang="scss">
	/* 基础布局样式 */
	.uni-container {
		padding: 24px;
		min-height: 100vh;
		background: linear-gradient(135deg, #f8fafb 0%, #e8f4f8 100%);
	}

	.db-container {
		width: 100%;
		max-width: 92%;
		margin: 0 auto;
		padding: 24px;
		background-color: #ffffff;
		border-radius: 16px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
		overflow: visible; /* 允许弹窗溢出容器 */
		box-sizing: border-box;
	}

	/* 按钮样式增强 */
	.uni-group {
		display: flex;
		gap: 12px;
	}

	.uni-button {
		padding: 8px 18px;
		font-size: 14px;
		font-weight: 600;
		letter-spacing: 0.3px;
		border-radius: 8px;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
		position: relative;
		overflow: hidden;
		text-transform: none;
		border: none;
	}

	.uni-button::after {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: transparent;
		transform: translateY(100%);
		transition: transform 0.3s ease;
		z-index: 1;
	}

	.uni-button[type="default"] {
		background: #f9fafc;
		color: #475569;
		border: 1px solid #e2e8f0;
	}

	.uni-button[type="primary"] {
		background: linear-gradient(145deg, #3975d9, #2862c6);
		color: #fff;
	}

	.uni-button[type="warn"] {
		background: linear-gradient(135deg, #f43f5e, #ef4444);
		color: #fff;
	}

	.uni-button:active {
		transform: translateY(1px);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.uni-button:hover {
		transform: translateY(-3px);
		box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
	}

	.uni-button[type="primary"]:hover {
		background: linear-gradient(145deg, #4986ea, #3974d7);
	}

	.uni-button[type="warn"]:hover {
		background: linear-gradient(135deg, #fb7185, #f87171);
	}

	/* 表格样式优化 */
	.uni-table {
		background-color: #ffffff;
		border-radius: 12px;
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
		overflow: hidden;
		border: 1px solid #f1f5f9;
		width: 100% !important;
		margin: 0 auto;
	}

	.uni-th {
		background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
		font-weight: 600;
		padding: 16px 12px;
		text-align: center;
		color: #334155;
		border-bottom: 2px solid #e2e8f0;
		position: relative;
	}

	.uni-th::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 2px;
		background: linear-gradient(to right, transparent, #3b82f6, transparent);
		transform: scaleX(0);
		transition: transform 0.3s ease;
	}

	.uni-th:hover::after {
		transform: scaleX(0.8);
	}

	.uni-td {
		padding: 14px 12px;
		text-align: center;
		border-bottom: 1px solid #e2e8f0;
		transition: background-color 0.2s ease;
	}

	/* 重复的image-container定义已合并到上面 */

	/* 重复的image-wrapper定义已合并到上面 */

	.image-count {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		backdrop-filter: blur(2px);
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 16px;
		font-weight: bold;
		border-radius: 8px;
	}

	.image-count:hover {
		background: rgba(0, 0, 0, 0.65);
	}

	.image-hover {
		width: 100%;
		height: 100%;
		border-radius: 8px;
		object-fit: cover;
		transition: all 0.3s ease;
	}

	/* 分页控件样式 - 使用默认样式 */
	.uni-pagination-box {
		display: flex;
		justify-content: center;
		padding: 16px 0;
		margin-top: 24px;
	}
	
	/* 日期选择器弹窗层级和位置修复 */
	::v-deep .uni-datetime-picker__mask {
		position: fixed !important;
		top: 0 !important;
		left: 0 !important;
		right: 0 !important;
		bottom: 0 !important;
		z-index: 9998 !important;
	}
	
	::v-deep .uni-datetime-picker__popup {
		position: fixed !important;
		top: 50% !important;
		left: 50% !important;
		transform: translate(-50%, -50%) !important;
		z-index: 9999 !important;
		max-height: 80vh !important;
		overflow: auto !important;
	}
	
	/* 修改微信小程序分页样式 */
	/* #ifdef MP-WEIXIN */
	/* 保留微信小程序的基础样式，但不自定义太多 */
	/* #endif */

	.uni-dateformat {
		color: #64748b;
		font-size: 14px;
		font-weight: 500;
	}

	/* 表格行和单元格样式 */
	.uni-table-th-row {
		font-size: 16px;
		color: #334155;
		font-weight: 600;
	}

	.uni-table-td-row {
		color: #475569;
		font-size: 15px;
	}

	.uni-table-td {
		height: 100% !important;
		vertical-align: middle !important;
	}
	
	.uni-table-tr:hover .uni-table-td {
		background-color: rgba(59, 130, 246, 0.04);
	}

	/* 描述单元格优化 */
	.description-cell {
		max-width: 300px;
		margin: 0 auto;
		text-align: left;
		position: relative;
		user-select: text;
	}

	.description-text {
		text-align: center;
		display: block;
		font-size: 14px;
		line-height: 1.6;
		color: #334155;
		word-break: break-all;
	}

	.description-text.text-truncate {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.expand-button {
		font-size: 13px;
		cursor: pointer;
		padding: 4px 8px;
		text-align: center;
		color: #4A9FD1;
		font-weight: 500;
		margin-top: 4px;
		border-radius: 4px;
		background-color: rgba(74, 159, 209, 0.08);
		transition: all 0.2s ease;
		display: inline-block;
		float: right;
	}

	.expand-button:hover {
		background-color: rgba(74, 159, 209, 0.15);
		color: #3d8bc2;
	}

	/* 搜索区域美化 - 修复间距问题 */
	.search-box {
		background: linear-gradient(135deg, #ffffff, #f9fafb);
		padding: 24px;
		margin-bottom: 24px; /* 统一上下间距 */
		border-radius: 16px;
		box-shadow: 0 8px 20px rgba(0, 0, 0, 0.04);
		border: 1px solid rgba(226, 232, 240, 0.8);
	}

	/* 日期搜索区域特殊样式 */
	.date-section {
		margin-top: 24px;
	}

	.date-row {
		display: flex;
		flex-wrap: wrap;
		gap: 20px;
	}

	.date-item {
		display: flex;
		align-items: center;
		flex: 1;
		min-width: 260px;
	}

	.search-box .search-row {
		display: flex;
		margin-bottom: 18px;
	}

	.search-box .search-item {
		flex: 1;
		margin-right: 16px;
	}

	.search-box .search-item:last-child {
		margin-right: 0;
	}

	.search-box .search-label {
		min-width: 64px;
		font-size: 14px;
		color: #475569;
		margin-right: 10px;
		font-weight: 600;
		line-height: 36px;
	}

	.search-box .select-row {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 16px;
	}

	/* 小程序端特殊处理 */
	/* #ifdef MP-WEIXIN */
	.search-box .search-row {
		flex-direction: column;
	}

	.search-box .search-item {
		margin-right: 0;
		margin-bottom: 16px;
	}

	.search-box .search-item:last-child {
		margin-bottom: 0;
	}

	.search-box .select-row {
		grid-template-columns: 1fr;
	}
	
	.date-row {
		flex-direction: column;
	}
	
	.date-item {
		margin-bottom: 16px;
	}
	
	.date-item:last-child {
		margin-bottom: 0;
	}
	
	/* 微信小程序样式增强 */
	.uni-table-tr {
		background-color: #ffffff;
	}
	
	.uni-table-tr:nth-child(even) {
		background-color: #f8fafc;
	}
		
	.uni-table-tr:hover {
		background-color: rgba(59, 130, 246, 0.05);
	}
	
	.uni-table-th {
		background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
		font-weight: 600 !important;
		color: #334155;
		padding: 16px 12px !important;
		border-bottom: 2px solid #e2e8f0 !important;
	}
	
	.uni-table-td {
		padding: 14px 12px !important;
		height: auto !important;
		vertical-align: middle !important;
	}
	
	/* 图片列专门优化 - 减少内边距 */
	.uni-table-td:nth-child(3) {
		padding: 8px 6px !important;
	}
	
	/* 重复的image-container定义已合并到上面 */
	
	.uni-easyinput__content {
		background-color: #ffffff !important;
		border: 1px solid #cbd5e1 !important;
		border-radius: 8px !important;
		transition: all 0.3s !important;
		padding: 0 14px !important;
		height: 40px !important;
		box-sizing: border-box !important;
	}
	
	.uni-easyinput__content-input {
		font-size: 15px !important;
		padding-left: 0 !important;
		margin-left: 0 !important;
		text-indent: 0 !important;
	}
	
	/* 调整输入框占位符文字 */
	.uni-easyinput__placeholder-class {
		font-size: 15px !important;
		padding-left: 0 !important;
	}
	
	.uni-datetime-picker--button {
		border-radius: 8px !important;
		border: 1px solid #cbd5e1 !important;
		transition: all 0.3s !important;
		background-color: #ffffff !important;
		height: 40px !important;
		box-sizing: border-box !important;
		padding: 0 14px !important;
		font-size: 15px !important;
	}
	
	.uni-datetime-picker--button text {
		font-size: 15px !important;
	}
	
	/* 统一下拉选择器字体大小 */
	.uni-data-select {
		font-size: 15px !important;
	}
	
	.uni-data-select .uni-select__input-text {
		font-size: 15px !important;
		padding-left: 0 !important;
		text-indent: 0 !important;
	}
	
	/* 调整选择器内部元素 */
	.uni-data-select .uni-select__selector {
		padding: 0 14px !important;
		height: 40px !important;
		box-sizing: border-box !important;
	}
	
	.uni-load-more {
		margin: 20px auto !important;
	}
	/* #endif */

	/* 理由显示区域样式 */
	.remarks-cell {
		text-align: center;
		padding: 12px 8px;
		line-height: 1.5;
	}
	
	.reason-text {
		display: flex;
		flex-direction: column;
		gap: 8px;
		max-width: 100%;
	}
	
	.reason-item {
		font-size: 13px;
		line-height: 1.4;
		padding: 4px 0;
		word-wrap: break-word;
		text-align: center;
		color: #666;
	}
	
	.no-reason {
		color: #9ca3af;
		font-size: 13px;
		font-style: italic;
	}

	/* 工作流状态文本增强 - 新工作流系统样式 */
	.approval-status-text {
		display: inline-block;
		padding: 8px 14px;
		border-radius: 50px;
		font-weight: 500;
		font-size: 14px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		cursor: pointer;
		transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
		position: relative;
		overflow: hidden;
		z-index: 1;
	}
	
	.approval-status-text::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(255, 255, 255, 0.2);
		transform: translateY(100%);
		transition: transform 0.3s ease;
		z-index: -1;
	}
	
	.approval-status-text:hover {
		transform: translateY(-3px);
		box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
	}
	
	.approval-status-text:hover::before {
		transform: translateY(0);
	}

	/* 加载遮罩优化（已弃用，避免全屏蒙层） */
	/* .loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 0.8);
		backdrop-filter: blur(6px);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
	} */

	/* 视觉分隔线 - 增强搜索区域与表格的视觉分隔 */
	.search-table-divider {
		height: 10px;
	}

	/* 表格居中样式 */
	.db-container {
		width: 100%;
		max-width: 92%;
		margin: 0 auto;
		padding: 24px;
		box-sizing: border-box;
	}
	
	/* 微信小程序特定样式 */
	/* #ifdef MP-WEIXIN */
	.db-container {
		width: 100%;
		max-width: 100%;
		padding: 16px;
		box-sizing: border-box;
	}
	
	.uni-table {
		width: 100% !important;
		margin: 0 auto;
	}
	
	/* 针对小屏幕设备优化表格居中 */
	@media screen and (max-width: 768px) {
		.db-container {
			padding-left: 12px;
			padding-right: 12px;
		}
		
		.uni-container {
			padding: 16px;
		}
	}
	
	/* 微信小程序弹窗选择器样式 */
	.picker-button {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 10px 14px;
		background-color: #ffffff;
		border: 1px solid #cbd5e1;
		border-radius: 8px;
		height: 40px;
		box-sizing: border-box;
		transition: all 0.3s ease;
	}
	
	.picker-button:active {
		background-color: #f5f5f5;
		border-color: #4A9FD1;
	}
	
	.picker-text {
		flex: 1;
		font-size: 15px;
		color: #333333;
		text-align: left;
		line-height: 1.2;
		font-weight: 400;
	}
	
	.picker-text.placeholder {
		color: #999999;
	}
	
	.picker-arrow {
		font-size: 12px;
		color: #999999;
		margin-left: 8px;
		transition: transform 0.3s ease;
	}
	
	.picker-button:active .picker-arrow {
		transform: rotate(180deg);
	}
	
	/* 弹窗内容样式 */
	.popup-content {
		background-color: #ffffff;
		border-radius: 16px 16px 0 0;
		max-height: 60vh;
		overflow: hidden;
	}
	
	.popup-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16px 20px;
		border-bottom: 1px solid #f0f0f0;
		background-color: #fafafa;
	}
	
	.popup-title {
		font-size: 16px;
		font-weight: 600;
		color: #333333;
	}
	
	.popup-close {
		font-size: 14px;
		color: #4A9FD1;
		padding: 4px 8px;
	}
	
	.popup-body {
		max-height: 50vh;
		overflow-y: auto;
	}
	
	.popup-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16px 20px;
		border-bottom: 1px solid #f5f5f5;
		transition: background-color 0.2s ease;
	}
	
	.popup-item:last-child {
		border-bottom: none;
	}
	
	.popup-item:active {
		background-color: #f8f9fa;
	}
	
	.popup-item.active {
		background-color: #e6f3ff;
		color: #4A9FD1;
	}
	
	.popup-item text {
		font-size: 15px;
		color: #333333;
	}
	
	.popup-item.active text {
		color: #4A9FD1;
		font-weight: 500;
	}
	
	.check-icon {
		font-size: 16px;
		color: #4A9FD1;
		font-weight: bold;
	}
	/* #endif */
</style>
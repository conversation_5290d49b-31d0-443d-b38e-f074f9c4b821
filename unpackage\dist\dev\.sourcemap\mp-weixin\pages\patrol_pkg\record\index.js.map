{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/record/index.vue?4a33", "webpack:///D:/Xwzc/pages/patrol_pkg/record/index.vue?68d0", "webpack:///D:/Xwzc/pages/patrol_pkg/record/index.vue?5563", "webpack:///D:/Xwzc/pages/patrol_pkg/record/index.vue?b783", "uni-app:///pages/patrol_pkg/record/index.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/record/index.vue?ad7c", "webpack:///D:/Xwzc/pages/patrol_pkg/record/index.vue?cc54"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "loading", "refreshing", "page", "pageSize", "hasMore", "routeList", "filterParams", "startDate", "endDate", "date<PERSON><PERSON><PERSON>", "totalTaskCount", "loadedTaskCount", "sortType", "showSortOptions", "h5DatePickerVisible", "h5DatePickerDateRange", "isFirstLoad", "lastScrollTop", "pageStateKey", "needRestoreState", "stateRestored", "computed", "userInfo", "onLoad", "onShow", "onHide", "onUnload", "methods", "loadRecords", "apiParams", "filters", "viewScope", "fields", "PatrolApi", "taskListRes", "tasks", "totalCount", "beforeFilterCount", "routeMap", "_id", "name", "user_name", "point_count", "completion_rate", "normal_count", "missed_count", "not_checked_count", "record_count", "rounds_count", "last_update_time", "date", "Math", "routeRecords", "backendHasMore", "currentTotal", "uni", "title", "icon", "console", "refresh", "loadMore", "resetFilters", "getRouteRecordSummary", "formatTimeDisplay", "viewRouteDetails", "url", "checkAndRestorePageState", "savePageState", "scrollTop", "timestamp", "restorePageState", "savedState", "restoreScrollPosition", "duration", "clearPageState", "onScroll", "handlePullDownRefresh", "getDateRangeText", "handleDateRangeChange", "formatDateString", "setDateFilter", "onSearch", "formatDateForQuery", "standardizeDate", "dateStr", "getSortText", "setSortType", "openH5DatePicker", "handleH5DateRangeChange", "resetH5DateRange", "closeH5DatePicker", "calculateRoundStats", "task", "roundUncompletedPoints", "isRoundFinished"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gZAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1EA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC+KnnB;AAEA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QAAA;QACAC;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC,4BACA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IACA;IACA;;IAEA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;;MAEA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EAAA,CACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBAAA;gBAGA;gBACAC;kBACA3B;kBACAC;kBACA2B;kBAAA;kBACA;kBACAC;kBACA;kBACAC,SACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBAEA,GAEA;gBAAA,cACA;gBAAA,gCACA,gCAIA,gCAIA;gBAAA;cAAA;gBAPAH;gBACAA;gBAAA;cAAA;gBAGAA;gBACAA;gBAAA;cAAA;gBAGAA;gBACAA;gBAAA;cAAA;gBAIA;gBACA;kBACA;kBACA;oBACA;oBACAA;kBACA;oBACA;oBACAA;oBACAA;kBACA;gBACA;kBACA;kBACAA;gBACA;kBACA;kBACAA;gBACA;;gBAEA;gBAAA;gBAAA,OACAI;cAAA;gBAAAC;gBAEA;kBACA;kBACAC,qCAEA;kBACAC,0CAEA;kBACA;oBACAC;oBAEAF;sBACA;sBACA;;sBAEA;sBACA;sBACA;sBACA;;sBAEA;sBACA;wBACA;sBACA;;sBAEA;sBACA;wBACA;sBACA;;sBAEA;sBACA;wBACA;sBACA;;sBAEA;sBACA;wBACA;sBACA;sBAEA;oBACA;kBAGA;;kBAEA;kBACAG,eAEA;kBACAH;oBACA;;oBAEA;oBACA;oBAEA;;oBAEA;oBACA;;oBAEA;oBACA;sBAAA;sBACAG;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;sBACA;oBACA;;oBAEA;oBACA;sBAAA;sBACA;;sBAEA;sBACA;sBACA;sBACA;;sBAEA;sBACAZ;sBACAA;;sBAEA;sBACA;sBACAA;sBACAA;;sBAEA;sBACAA,2DACAa,0CACA;oBACA;;oBAEA;oBACA;sBACAb;oBACA;kBACA;;kBAEA;kBACAc,wCAEA;kBACA;oBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;kBACAC,oDAEA;kBACA;oBACA;oBACA;kBACA;oBACA;oBACAC;oBACA;kBACA;oBACA;oBACA;kBACA;kBAEA;oBACA;kBACA;gBACA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;kBACAC;kBACAC;gBACA;gBACAC;cAAA;gBAAA;gBAEA;gBACA;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;MACA;MAEA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAtD;QACAC;MACA;MACA;MACA;IACA;IAEA;IACAsD;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;UACA;UACA;QACA;;QAEA;QACA;MACA;QACAL;QACA;QACA;UACA;UACA;YACA;UACA;UACA;QACA;;QACA;MACA;IACA;IAEA;IACAM;MACA;MACA;MAEAT;QACAU;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;UACA;UACA;UACA;;UAEA;YACA;UACA;YACA;YACAX;UACA;QACA;MACA;QACAG;MACA;IACA;IAEA;IACAS;MACA;QACA;UACA;UACAjE;UACAE;UACAC;UAEA;UACAC;UACAG;UACAG;UAEA;UACAwD;UAEA;UACAC;QACA;QAEAd;MACA;QACAG;MACA;IACA;IAEA;IACAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACA;gBACA;;gBAEA;gBACA;kBAAAhE;kBAAAC;gBAAA;gBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAkD;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAc;MACA;QACA;QACAjB;UACAa;UACAK;QACA;MACA;QACAf;MACA;IACA;IAEA;IACAgB;MACA;QACAnB;MACA;QACAG;MACA;IACA;IAEA;IACAiB;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACA;;QAEA;QACA;UACAvE;UACAC;QACA;;QAEA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAuE;MACA;MAEA;QACA;QACA;UACA;QACA;QAEA;QACA;QACA;UACA;QACA;QAEA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;;MAEA;MACA;QACA;QACA;UACA;QACA;;QAEA;QACA;QACA;UACA;QACA;;QAEA;QACA;QACA;QACA;QACA;MACA;QACAxB;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAyB;MACA;;MAEA;MACA;QACA;QACAC;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;QACA;UACA;UACA;UACA;UACA;QACA;QAEA;MACA;QACA1B;QACA;MACA;IACA;IAEA;IACA2B;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;QACA;MACA;;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACA;;QAEA;QACA;UACAjF;UACAC;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAiF;MACA;QACAlF;QACAC;MACA;MACA;MACA;IACA;IAEA;IACAkF;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;UAAA9C;UAAAC;QAAA;MACA;MAEA;MACA;MACA;MAEA8C;QACA;QACA;;QAEA;QACA;QAEA;UACA;UACA;UACA;UACAC;QACA;UACA;UACAnC;UACA;QACA;QAEA;UACA;UACAb;QACA;UACA;UACAC;QACA;MACA;MAEA;QAAAD;QAAAC;MAAA;IACA;IAEA;IACAgD;MAAA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;UACA;UACA;YACA;UACA;QACA;UACApC;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACn9BA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/record/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/record/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=f8ba29be&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/record/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=f8ba29be&\"", "var components\ntry {\n  components = {\n    uniDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker\" */ \"@/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getDateRangeText()\n  var m1 = _vm.getSortText()\n  var g0 = _vm.routeList.length === 0 && !_vm.loading\n  var l0 = _vm.__map(_vm.routeList, function (route, index) {\n    var $orig = _vm.__get_orig(route)\n    var m2 = _vm.formatTimeDisplay(route.date)\n    return {\n      $orig: $orig,\n      m2: m2,\n    }\n  })\n  var g1 = _vm.loading && _vm.routeList.length > 0\n  var g2 = !_vm.hasMore && _vm.routeList.length > 0\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showSortOptions = !_vm.showSortOptions\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"record-container\">\n\t\t<!-- 固定区域：筛选选项卡 -->\n\t\t<view class=\"fixed-header\">\n\t\t\t<!-- 筛选选项卡 -->\n\t\t\t<view class=\"tab-filter\">\n\t\t\t\t<!-- 日期选择 -->\n\t\t\t\t<view class=\"tab-group\">\n\t\t\t\t\t<view class=\"tab-options\">\n\t\t\t\t\t\t<view :class=\"['tab-item', !filterParams.startDate && !filterParams.endDate ? 'active' : '']\" @click=\"setDateFilter('', '')\">全部</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t\t\t\t\t<!-- 微信小程序保持原有实现 -->\n\t\t\t\t\t\t<view :class=\"['tab-item date-range', filterParams.startDate || filterParams.endDate ? 'active' : '']\">\n\t\t\t\t\t\t\t<uni-datetime-picker\n\t\t\t\t\t\t\t\ttype=\"daterange\"\n\t\t\t\t\t\t\t\tv-model=\"dateRange\"\n\t\t\t\t\t\t\t\t@change=\"handleDateRangeChange\"\n\t\t\t\t\t\t\t\t:clear-icon=\"true\"\n\t\t\t\t\t\t\t\t:border=\"false\"\n\t\t\t\t\t\t\t\tstart-placeholder=\"开始日期\"\n\t\t\t\t\t\t\t\tend-placeholder=\"结束日期\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<text class=\"date-range-text\">{{ getDateRangeText() }}</text>\n\t\t\t\t\t\t\t</uni-datetime-picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- #ifdef H5 -->\n\t\t\t\t\t\t<!-- H5平台的日期选择器 -->\n\t\t\t\t\t\t<view :class=\"['tab-item', filterParams.startDate || filterParams.endDate ? 'active' : '']\" @click=\"openH5DatePicker\">\n\t\t\t\t\t\t\t<text class=\"date-range-text\">{{ getDateRangeText() }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 添加排序功能 -->\n\t\t\t\t\t<view class=\"sort-container\">\n\t\t\t\t\t\t<view class=\"sort-button\" @click=\"showSortOptions = !showSortOptions\">\n\t\t\t\t\t\t\t<text class=\"sort-text\">{{ getSortText() }}</text>\n\t\t\t\t\t\t\t<uni-icons type=\"arrow-down\" size=\"14\" color=\"#666666\" :class=\"{'rotate-icon': showSortOptions}\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 排序选项下拉菜单 -->\n\t\t\t\t\t\t<view class=\"sort-options\" v-if=\"showSortOptions\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"sort-option\" \n\t\t\t\t\t\t\t\t:class=\"{'active': sortType === 'execute'}\"\n\t\t\t\t\t\t\t\t@click=\"setSortType('execute')\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<text>执行日期</text>\n\t\t\t\t\t\t\t\t<uni-icons v-if=\"sortType === 'execute'\" type=\"checkmarkempty\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"sort-option\" \n\t\t\t\t\t\t\t\t:class=\"{'active': sortType === 'create'}\"\n\t\t\t\t\t\t\t\t@click=\"setSortType('create')\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<text>添加日期</text>\n\t\t\t\t\t\t\t\t<uni-icons v-if=\"sortType === 'create'\" type=\"checkmarkempty\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"sort-option\" \n\t\t\t\t\t\t\t\t:class=\"{'active': sortType === 'checkin'}\"\n\t\t\t\t\t\t\t\t@click=\"setSortType('checkin')\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<text>打卡日期</text>\n\t\t\t\t\t\t\t\t<uni-icons v-if=\"sortType === 'checkin'\" type=\"checkmarkempty\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- #ifdef H5 -->\n\t\t<!-- H5平台的日期选择器 -->\n\t\t<view class=\"h5-date-picker-container\" v-if=\"h5DatePickerVisible\">\n\t\t\t<view class=\"h5-date-picker-mask\" @click=\"closeH5DatePicker\"></view>\n\t\t\t<view class=\"h5-date-picker-content\">\n\t\t\t\t<view class=\"h5-date-picker-header\">\n\t\t\t\t\t<text class=\"h5-date-picker-title\">选择日期范围</text>\n\t\t\t\t\t<view class=\"h5-date-picker-close\" @click=\"closeH5DatePicker\">×</view>\n\t\t\t\t</view>\n\t\t\t\t<uni-datetime-picker\n\t\t\t\t\tref=\"h5DatePicker\"\n\t\t\t\t\ttype=\"daterange\"\n\t\t\t\t\tv-model=\"dateRange\"\n\t\t\t\t\t@change=\"handleH5DateRangeChange\"\n\t\t\t\t\t:clear-icon=\"true\"\n\t\t\t\t\t:border=\"false\"\n\t\t\t\t\tstart-placeholder=\"开始日期\"\n\t\t\t\t\tend-placeholder=\"结束日期\"\n\t\t\t\t\t:styles=\"{\n\t\t\t\t\t\tcontent: 'date-picker-custom-content',\n\t\t\t\t\t\tinput: 'date-picker-custom-input',\n\t\t\t\t\t\trange: 'date-picker-custom-range',\n\t\t\t\t\t\tbutton: 'date-picker-custom-button'\n\t\t\t\t\t}\"\n\t\t\t\t></uni-datetime-picker>\n\t\t\t\t<!-- 移除多余的底部按钮，日期选择器本身已有内置功能 -->\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- #endif -->\n\t\t\n\t\t<!-- 可滚动区域：记录列表 -->\n\t\t<scroll-view class=\"record-list\" scroll-y @scrolltolower=\"loadMore\" refresher-enabled @refresherrefresh=\"handlePullDownRefresh\" :refresher-triggered=\"refreshing\" @scroll=\"onScroll\">\n\t\t\t<p-empty-state v-if=\"routeList.length === 0 && !loading\" type=\"record\" text=\"暂无巡视记录\"></p-empty-state>\n\t\t\t\n\t\t\t<view class=\"route-area\" v-for=\"(route, index) in routeList\" :key=\"index\">\n\t\t\t\t<view class=\"route-header\">\n\t\t\t\t\t<view class=\"route-title-container\">\n\t\t\t\t\t\t<view class=\"title-left\">\n\t\t\t\t\t\t\t<view class=\"title-decorator\"></view>\n\t\t\t\t\t\t\t<text class=\"route-title-text\">{{ route.name }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text v-if=\"route.user_name\" class=\"user-name\">{{ route.user_name }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"route-card\" @click=\"viewRouteDetails(route)\">\n\t\t\t\t\t<view class=\"route-info\">\n\t\t\t\t\t\t<view class=\"route-points\">\n\t\t\t\t\t\t\t<text class=\"route-label\">点位数量：</text>\n\t\t\t\t\t\t\t<text class=\"route-value\">{{ route.point_count }}个</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"route-completion\">\n\t\t\t\t\t\t\t<text class=\"route-label\">完成率：</text>\n\t\t\t\t\t\t\t<text class=\"route-value\">{{ route.completion_rate }}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"route-stats\">\n\t\t\t\t\t\t<view class=\"stat-item normal\">\n\t\t\t\t\t\t\t<text class=\"stat-value\">{{route.normal_count || 0}}</text>\n\t\t\t\t\t\t\t<text class=\"stat-label\">已打卡</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stat-item missed\">\n\t\t\t\t\t\t\t<text class=\"stat-value\">{{route.missed_count || 0}}</text>\n\t\t\t\t\t\t\t<text class=\"stat-label\">缺卡</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stat-item not-checked\">\n\t\t\t\t\t\t\t<text class=\"stat-value\">{{route.not_checked_count || 0}}</text>\n\t\t\t\t\t\t\t<text class=\"stat-label\">未打卡</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stat-item total-time\">\n\t\t\t\t\t\t\t<text class=\"stat-value\">{{ route.rounds_count || 0 }}轮</text>\n\t\t\t\t\t\t\t<text class=\"stat-label\">打卡轮次</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"route-last-updated\">\n\t\t\t\t\t\t<view class=\"last-updated-text\">\n\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"14\" color=\"#8F959E\"></uni-icons>\n\t\t\t\t\t\t\t<text>执行时间：{{ formatTimeDisplay(route.date) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<uni-icons type=\"forward\" size=\"16\" color=\"#C0C4CC\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 加载更多提示 -->\n\t\t\t<view class=\"loading-more\" v-if=\"loading && routeList.length > 0\">\n\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 全部加载完毕提示 -->\n\t\t\t<view class=\"no-more\" v-if=\"!hasMore && routeList.length > 0\">\n\t\t\t\t<text class=\"no-more-text\">没有更多记录了</text>\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\nimport { mapState } from 'vuex';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\nimport { formatDate, getRelativeTime, safeDateFormat } from '@/utils/date.js';\nimport PatrolApi from '@/utils/patrol-api.js';\n\nexport default {\n\tcomponents: {\n\t\tPEmptyState\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tloading: false,\n\t\t\trefreshing: false,\n\t\t\tpage: 1,\n\t\t\tpageSize: 10,\n\t\t\thasMore: true,\n\t\t\trouteList: [],\n\t\t\tfilterParams: {\n\t\t\t\tstartDate: '', // 开始日期\n\t\t\t\tendDate: ''   // 结束日期\n\t\t\t},\n\t\t\tdateRange: [], // 用于datetime-picker的日期范围\n\t\t\ttotalTaskCount: 0, // 添加总任务数计数器\n\t\t\tloadedTaskCount: 0, // 添加已加载任务计数器\n\t\t\tsortType: 'execute', // 排序类型：execute-执行日期(默认), create-添加日期, checkin-打卡时间\n\t\t\tshowSortOptions: false, // 是否显示排序选项下拉菜单\n\t\t\th5DatePickerVisible: false, // 控制H5平台的日期选择器显示\n\t\t\th5DatePickerDateRange: [], // 存储H5平台的日期选择器选中的日期范围\n\t\t\t// 新增：页面状态保持相关\n\t\t\tisFirstLoad: true, // 是否首次加载\n\t\t\tlastScrollTop: 0, // 记录滚动位置\n\t\t\tpageStateKey: 'patrol_record_page_state', // 页面状态缓存key\n\t\t\tneedRestoreState: false, // 是否需要恢复状态\n\t\t\tstateRestored: false // 状态是否已恢复\n\t\t};\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\tuserInfo: state => state.user.userInfo\n\t\t})\n\t},\n\tonLoad() {\n\t\t// 检查是否需要恢复页面状态\n\t\tthis.checkAndRestorePageState();\n\t\t\n\t\t// 如果不需要恢复状态，则正常加载数据\n\t\tif (!this.needRestoreState) {\n\t\t\tthis.loadRecords();\n\t\t}\n\t},\n\tonShow() {\n\t\t// 只在首次加载或用户主动刷新时才重新加载数据\n\t\tif (this.isFirstLoad) {\n\t\t\tthis.isFirstLoad = false;\n\t\t\t\n\t\t\t// 如果需要恢复状态，则恢复；否则正常加载\n\t\t\tif (this.needRestoreState && !this.stateRestored) {\n\t\t\t\tthis.restorePageState();\n\t\t\t} else if (!this.needRestoreState) {\n\t\t\t\t// 首次进入页面，正常加载\n\t\t\t\tthis.refresh();\n\t\t\t}\n\t\t}\n\t\t// 非首次显示时不做任何操作，保持当前状态\n\t},\n\tonHide() {\n\t\t// 页面隐藏时保存状态\n\t\tthis.savePageState();\n\t},\n\tonUnload() {\n\t\t// 页面卸载时清理状态（可选，根据需求决定是否保留状态到下次进入）\n\t\t// this.clearPageState();\n\t},\n\tmethods: {\n\t\t// 加载记录列表\n\t\tasync loadRecords() {\n\t\t\tif (this.loading || !this.hasMore) return;\n\t\t\tthis.loading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 准备API参数 - 🔥 新增：角色权限控制\n\t\t\t\tconst apiParams = {\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tpageSize: this.pageSize,\n\t\t\t\t\tfilters: {},  // 添加filters对象，适配后端接口期望的参数格式\n\t\t\t\t\t// 🔥 新增：角色决定模式，管理层看全部，普通用户看自己\n\t\t\t\t\tviewScope: 'role-based',\n\t\t\t\t\t// 添加精确字段过滤，只获取页面显示需要的字段\n\t\t\t\t\tfields: [\n\t\t\t\t\t\t'_id', 'name', 'route_name', 'user_name', 'patrol_date', 'create_date', 'update_date',\n\t\t\t\t\t\t// 统计信息：使用后端预计算的overall_stats\n\t\t\t\t\t\t'overall_stats.total_points', 'overall_stats.completed_points', 'overall_stats.missed_points', 'overall_stats.completion_rate',\n\t\t\t\t\t\t// 轮次信息：只需要轮次数量，不需要详细点位数据\n\t\t\t\t\t\t'rounds_detail.round', 'rounds_detail.name',\n\t\t\t\t\t\t// 执行者信息\n\t\t\t\t\t\t'executor.name'\n\t\t\t\t\t]\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 添加排序参数\n\t\t\t\tswitch(this.sortType) {\n\t\t\t\t\tcase 'execute':\n\t\t\t\t\t\tapiParams.filters.sort = 'patrol_date'; \n\t\t\t\t\t\tapiParams.filters.sortDirection = 'desc'; // 默认降序(最新的在前)\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'create':\n\t\t\t\t\t\tapiParams.filters.sort = 'create_date';\n\t\t\t\t\t\tapiParams.filters.sortDirection = 'desc';\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'checkin':\n\t\t\t\t\t\tapiParams.filters.sort = 'last_checkin_time';\n\t\t\t\t\t\tapiParams.filters.sortDirection = 'desc';\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 处理日期筛选条件 - 确保格式一致性\n\t\t\t\tif (this.filterParams.startDate && this.filterParams.endDate) {\n\t\t\t\t\t// 如果开始和结束日期相同，直接使用单个日期参数进行精确匹配\n\t\t\t\t\tif (this.filterParams.startDate === this.filterParams.endDate) {\n\t\t\t\t\t\t// 确保日期格式为 YYYY-MM-DD，不包含时间部分\n\t\t\t\t\t\tapiParams.filters.patrolDate = this.formatDateForQuery(this.filterParams.startDate);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 对于日期范围，使用范围查询参数\n\t\t\t\t\t\tapiParams.filters.startDate = this.formatDateForQuery(this.filterParams.startDate);\n\t\t\t\t\t\tapiParams.filters.endDate = this.formatDateForQuery(this.filterParams.endDate);\n\t\t\t\t\t}\n\t\t\t\t} else if (this.filterParams.startDate) {\n\t\t\t\t\t// 只有开始日期\n\t\t\t\t\tapiParams.filters.startDate = this.formatDateForQuery(this.filterParams.startDate);\n\t\t\t\t} else if (this.filterParams.endDate) {\n\t\t\t\t\t// 只有结束日期\n\t\t\t\t\tapiParams.filters.endDate = this.formatDateForQuery(this.filterParams.endDate);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 这里使用 getTaskList，它会将参数正确封装\n\t\t\t\tconst taskListRes = await PatrolApi.getTaskList(apiParams);\n\t\t\t\t\n\t\t\t\tif (taskListRes.code === 0 && taskListRes.data) {\n\t\t\t\t\t// 获取任务列表\n\t\t\t\t\tlet tasks = taskListRes.data.list || [];\n\t\t\t\t\t\n\t\t\t\t\t// 保存后端返回的总任务数\n\t\t\t\t\tconst totalCount = taskListRes.data.total || 0;\n\t\t\t\t\t\n\t\t\t\t\t// 如果API没有正确应用日期筛选，在前端进行筛选\n\t\t\t\t\tif (this.filterParams.startDate || this.filterParams.endDate) {\n\t\t\t\t\t\tconst beforeFilterCount = tasks.length;\n\t\t\t\t\t\t\n\t\t\t\t\t\ttasks = tasks.filter(task => {\n\t\t\t\t\t\t\tconst taskDate = task.patrol_date || task.date || '';\n\t\t\t\t\t\t\tif (!taskDate) return false;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 标准化日期格式，确保比较的是相同格式的日期字符串\n\t\t\t\t\t\t\tconst normalizedTaskDate = this.standardizeDate(taskDate);\n\t\t\t\t\t\t\tconst startDate = this.filterParams.startDate ? this.standardizeDate(this.filterParams.startDate) : '';\n\t\t\t\t\t\t\tconst endDate = this.filterParams.endDate ? this.standardizeDate(this.filterParams.endDate) : '';\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 对于单一日期筛选\n\t\t\t\t\t\t\tif (startDate && endDate && startDate === endDate) {\n\t\t\t\t\t\t\t\treturn normalizedTaskDate === startDate;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 对于日期范围筛选\n\t\t\t\t\t\t\tif (startDate && endDate) {\n\t\t\t\t\t\t\t\treturn normalizedTaskDate >= startDate && normalizedTaskDate <= endDate;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 只有开始日期\n\t\t\t\t\t\t\tif (startDate) {\n\t\t\t\t\t\t\t\treturn normalizedTaskDate >= startDate;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 只有结束日期\n\t\t\t\t\t\t\tif (endDate) {\n\t\t\t\t\t\t\t\treturn normalizedTaskDate <= endDate;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 准备路线数据\n\t\t\t\t\tconst routeMap = {};\n\t\t\t\t\t\n\t\t\t\t\t// 处理任务数据\n\t\t\t\t\ttasks.forEach(task => {\n\t\t\t\t\t\tif (!task._id) return;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 确保任务有执行日期\n\t\t\t\t\t\tconst taskDate = task.patrol_date || task.date || '';\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (!taskDate) return; // 跳过没有日期的任务\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 任务ID\n\t\t\t\t\t\tconst taskId = task._id;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 初始化路线数据\n\t\t\t\t\t\tif (!routeMap[taskId]) {\n\t\t\t\t\t\t\trouteMap[taskId] = {\n\t\t\t\t\t\t\t\t_id: taskId,\n\t\t\t\t\t\t\t\tname: task.name || task.route_name || '未命名线路',\n\t\t\t\t\t\t\t\tuser_name: task.user_name || (task.executor ? task.executor.name : '') || '',\n\t\t\t\t\t\t\t\tpoint_count: 0,\n\t\t\t\t\t\t\t\tcompletion_rate: 0,\n\t\t\t\t\t\t\t\tnormal_count: 0,\n\t\t\t\t\t\t\t\tmissed_count: 0,\n\t\t\t\t\t\t\t\tnot_checked_count: 0,\n\t\t\t\t\t\t\t\trecord_count: 0,\n\t\t\t\t\t\t\t\trounds_count: task.rounds_detail?.length || 0,\n\t\t\t\t\t\t\t\tlast_update_time: task.update_date || task.create_date || null,\n\t\t\t\t\t\t\t\tdate: taskDate\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 从overall_stats获取统计信息\n\t\t\t\t\t\tif (task.overall_stats) {\n\t\t\t\t\t\t\tconst stats = task.overall_stats;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 计算线路真实的点位数量（不是所有轮次的点位总数）\n\t\t\t\t\t\t\t// 使用简单计算：总点位数 ÷ 轮次数量 = 线路点位数\n\t\t\t\t\t\t\tconst totalRounds = task.rounds_detail?.length || 1;\n\t\t\t\t\t\t\tconst realPointCount = Math.round((stats.total_points || 0) / totalRounds);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 更新基础统计数据\n\t\t\t\t\t\t\trouteMap[taskId].point_count = realPointCount;\n\t\t\t\t\t\t\trouteMap[taskId].normal_count = stats.completed_points || 0;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 按轮次计算缺卡数和未打卡数\n\t\t\t\t\t\t\tconst roundStats = this.calculateRoundStats(task);\n\t\t\t\t\t\t\trouteMap[taskId].missed_count = roundStats.missed_count;\n\t\t\t\t\t\t\trouteMap[taskId].not_checked_count = roundStats.not_checked_count;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 更新完成率\n\t\t\t\t\t\t\trouteMap[taskId].completion_rate = stats.completion_rate\n\t\t\t\t\t\t\t\t? Math.round(stats.completion_rate * 100)\n\t\t\t\t\t\t\t\t: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 简化轮次处理：只获取轮次数量\n\t\t\t\t\t\tif (task.rounds_detail && task.rounds_detail.length > 0) {\n\t\t\t\t\t\t\trouteMap[taskId].rounds_count = task.rounds_detail.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 将路线映射转换为数组\n\t\t\t\t\tconst routeRecords = Object.values(routeMap);\n\t\t\t\t\t\n\t\t\t\t\t// 更新路线列表\n\t\t\t\t\tif (this.page === 1) {\n\t\t\t\t\t\tthis.routeList = routeRecords;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.routeList = [...this.routeList, ...routeRecords];\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 更新分页信息\n\t\t\t\t\t// 检查后端返回的hasMore标志\n\t\t\t\t\tconst backendHasMore = taskListRes.data.hasMore || false;\n\t\t\t\t\t\n\t\t\t\t\t// 使用更复杂的判断逻辑来确定是否还有更多数据\n\t\t\t\t\tif (taskListRes.data.hasMore !== undefined) {\n\t\t\t\t\t\t// 优先使用后端明确返回的hasMore标志\n\t\t\t\t\t\tthis.hasMore = backendHasMore;\n\t\t\t\t\t} else if (totalCount > 0) {\n\t\t\t\t\t\t// 其次使用总数比较\n\t\t\t\t\t\tconst currentTotal = (this.page - 1) * this.pageSize + routeRecords.length;\n\t\t\t\t\t\tthis.hasMore = currentTotal < totalCount;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 最后回退到老方法\n\t\t\t\t\t\tthis.hasMore = tasks.length > 0 && routeRecords.length >= this.pageSize;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (this.hasMore) {\n\t\t\t\t\t\tthis.page++;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: taskListRes.message || '获取记录失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载失败，请稍后再试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tconsole.error('加载记录出错:', e);\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t\tif (this.refreshing) {\n\t\t\t\t\tthis.refreshing = false;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 下拉刷新\n\t\trefresh(forceRefresh = false) {\n\t\t\t// 如果是强制刷新（用户主动下拉刷新），清除保存的状态\n\t\t\tif (forceRefresh) {\n\t\t\t\tthis.clearPageState();\n\t\t\t\tthis.stateRestored = false;\n\t\t\t\tthis.needRestoreState = false;\n\t\t\t}\n\t\t\t\n\t\t\tthis.refreshing = true;\n\t\t\tthis.page = 1;\n\t\t\tthis.hasMore = true;\n\t\t\tthis.routeList = []; // 清空当前列表\n\t\t\tthis.loadRecords();\n\t\t},\n\t\t\n\t\t// 加载更多\n\t\tloadMore() {\n\t\t\tif (this.hasMore && !this.loading) {\n\t\t\t\tthis.loadRecords();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 重置筛选条件\n\t\tresetFilters() {\n\t\t\tthis.filterParams = {\n\t\t\t\tstartDate: '',\n\t\t\t\tendDate: ''\n\t\t\t};\n\t\t\tthis.dateRange = []; // 清空日期范围选择器的值\n\t\t\tthis.refresh(true); // 重置筛选条件时强制刷新\n\t\t},\n\t\t\n\t\t// 获取路线记录汇总\n\t\tgetRouteRecordSummary(route) {\n\t\t\t// 如果有点位数量，显示完成信息\n\t\t\tif (route.point_count > 0) {\n\t\t\t\tconst completedCount = route.normal_count + route.overtime_count;\n\t\t\t\treturn `完成 ${completedCount}/${route.point_count} 个点位 (缺卡: ${route.missed_count}, 未打卡: ${route.not_checked_count || 0})`;\n\t\t\t}\n\t\t\t\n\t\t\t// 退化情况，只显示记录数\n\t\t\tif (route.record_count > 0) {\n\t\t\t\treturn `共${route.record_count}条记录`;\n\t\t\t}\n\t\t\t\n\t\t\treturn '暂无记录';\n\t\t},\n\t\t\n\t\t// 格式化时间显示\n\t\tformatTimeDisplay(timeStr) {\n\t\t\tif (!timeStr) return '未知时间';\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 检查是否只是日期部分\n\t\t\t\tif (typeof timeStr === 'string' && timeStr.length === 10 && timeStr.includes('-')) {\n\t\t\t\t\t// 只包含日期部分 YYYY-MM-DD，直接返回\n\t\t\t\t\treturn timeStr;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 使用safeDateFormat安全地格式化日期\n\t\t\t\treturn safeDateFormat(timeStr, 'YYYY-MM-DD');\n\t\t\t} catch (e) {\n\t\t\t\tconsole.warn('时间格式化错误', e);\n\t\t\t\t// 尝试原始处理\n\t\t\t\tif (typeof timeStr === 'string') {\n\t\t\t\t\t// 如果是标准日期格式，只取日期部分\n\t\t\t\t\tif (timeStr.includes('T')) {\n\t\t\t\t\t\treturn timeStr.split('T')[0];\n\t\t\t\t\t}\n\t\t\t\t\treturn timeStr.split(' ')[0]; // 只返回日期部分\n\t\t\t\t}\n\t\t\t\treturn String(timeStr);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 查看路线详情\n\t\tviewRouteDetails(route) {\n\t\t\t// 在跳转前保存当前页面状态\n\t\t\tthis.savePageState();\n\t\t\t\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/patrol_pkg/record/route-detail?id=${route._id}&name=${encodeURIComponent(route.name)}&executorName=${encodeURIComponent(route.user_name || '')}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 检查并恢复页面状态\n\t\tcheckAndRestorePageState() {\n\t\t\ttry {\n\t\t\t\tconst savedState = uni.getStorageSync(this.pageStateKey);\n\t\t\t\tif (savedState) {\n\t\t\t\t\t// 检查状态是否在有效期内（比如30分钟）\n\t\t\t\t\tconst now = Date.now();\n\t\t\t\t\tconst stateAge = now - (savedState.timestamp || 0);\n\t\t\t\t\tconst maxAge = 30 * 60 * 1000; // 30分钟\n\t\t\t\t\t\n\t\t\t\t\tif (stateAge < maxAge) {\n\t\t\t\t\t\tthis.needRestoreState = true;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 状态过期，清除\n\t\t\t\t\t\tuni.removeStorageSync(this.pageStateKey);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('检查页面状态失败:', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 保存页面状态\n\t\tsavePageState() {\n\t\t\ttry {\n\t\t\t\tconst state = {\n\t\t\t\t\t// 分页信息\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\thasMore: this.hasMore,\n\t\t\t\t\trouteList: this.routeList,\n\t\t\t\t\t\n\t\t\t\t\t// 筛选条件\n\t\t\t\t\tfilterParams: { ...this.filterParams },\n\t\t\t\t\tdateRange: [...this.dateRange],\n\t\t\t\t\tsortType: this.sortType,\n\t\t\t\t\t\n\t\t\t\t\t// 滚动位置\n\t\t\t\t\tscrollTop: this.lastScrollTop,\n\t\t\t\t\t\n\t\t\t\t\t// 时间戳\n\t\t\t\t\ttimestamp: Date.now()\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tuni.setStorageSync(this.pageStateKey, state);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('保存页面状态失败:', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 恢复页面状态\n\t\tasync restorePageState() {\n\t\t\ttry {\n\t\t\t\tconst savedState = uni.getStorageSync(this.pageStateKey);\n\t\t\t\tif (!savedState) {\n\t\t\t\t\tthis.loadRecords();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 恢复数据状态\n\t\t\t\tthis.page = savedState.page || 1;\n\t\t\t\tthis.hasMore = savedState.hasMore !== undefined ? savedState.hasMore : true;\n\t\t\t\tthis.routeList = savedState.routeList || [];\n\t\t\t\t\n\t\t\t\t// 恢复筛选条件\n\t\t\t\tthis.filterParams = savedState.filterParams || { startDate: '', endDate: '' };\n\t\t\t\tthis.dateRange = savedState.dateRange || [];\n\t\t\t\tthis.sortType = savedState.sortType || 'execute';\n\t\t\t\t\n\t\t\t\t// 标记状态已恢复\n\t\t\t\tthis.stateRestored = true;\n\t\t\t\t\n\t\t\t\t// 恢复滚动位置\n\t\t\t\tif (savedState.scrollTop > 0) {\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.restoreScrollPosition(savedState.scrollTop);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('恢复页面状态失败:', e);\n\t\t\t\t// 恢复失败时正常加载数据\n\t\t\t\tthis.loadRecords();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 恢复滚动位置\n\t\trestoreScrollPosition(scrollTop) {\n\t\t\ttry {\n\t\t\t\t// 使用页面滚动API恢复位置\n\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\tscrollTop: scrollTop,\n\t\t\t\t\tduration: 0 // 立即滚动，不要动画\n\t\t\t\t});\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('恢复滚动位置失败:', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 清除页面状态\n\t\tclearPageState() {\n\t\t\ttry {\n\t\t\t\tuni.removeStorageSync(this.pageStateKey);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('清除页面状态失败:', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 监听滚动事件\n\t\tonScroll(e) {\n\t\t\tif (e && e.detail) {\n\t\t\t\tthis.lastScrollTop = e.detail.scrollTop;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 处理下拉刷新\n\t\thandlePullDownRefresh() {\n\t\t\tthis.refresh(true); // 传入true表示强制刷新\n\t\t},\n\t\t\n\t\t// 获取日期范围显示文本\n\t\tgetDateRangeText() {\n\t\t\tif (this.filterParams.startDate && this.filterParams.endDate) {\n\t\t\t\tif (this.filterParams.startDate === this.filterParams.endDate) {\n\t\t\t\t\treturn this.filterParams.startDate; // 如果开始和结束日期相同，只显示一个日期\n\t\t\t\t}\n\t\t\t\treturn `${this.filterParams.startDate} 至 ${this.filterParams.endDate}`;\n\t\t\t} else if (this.filterParams.startDate) {\n\t\t\t\treturn `${this.filterParams.startDate} 起`;\n\t\t\t} else if (this.filterParams.endDate) {\n\t\t\t\treturn `至 ${this.filterParams.endDate}`;\n\t\t\t} else {\n\t\t\t\treturn '选择日期范围';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 处理日期范围变化\n\t\thandleDateRangeChange(e) {\n\t\t\tif (!e || e.length === 0) {\n\t\t\t\t// 清空筛选条件\n\t\t\t\tthis.resetFilters();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (e.length === 2) {\n\t\t\t\t// 确保日期格式为YYYY-MM-DD\n\t\t\t\tconst startDate = this.formatDateString(e[0]);\n\t\t\t\tconst endDate = this.formatDateString(e[1]);\n\t\t\t\t\n\t\t\t\t// 设置筛选参数\n\t\t\t\tthis.filterParams = {\n\t\t\t\t\tstartDate: startDate,\n\t\t\t\t\tendDate: endDate\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 刷新数据\n\t\t\t\tthis.page = 1; // 重置页码\n\t\t\t\tthis.hasMore = true;\n\t\t\t\tthis.refresh(true); // 日期筛选变化时强制刷新\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 格式化日期字符串为YYYY-MM-DD格式\n\t\tformatDateString(dateStr) {\n\t\t\tif (!dateStr) return '';\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 如果已经是格式化的日期字符串，直接返回\n\t\t\t\tif (typeof dateStr === 'string' && dateStr.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n\t\t\t\t\treturn dateStr;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst date = new Date(dateStr);\n\t\t\t\t// 验证日期是否有效\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t} catch (e) {\n\t\t\t\treturn '';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 设置日期筛选\n\t\tsetDateFilter(startDate, endDate) {\n\t\t\tthis.filterParams.startDate = startDate;\n\t\t\tthis.filterParams.endDate = endDate;\n\t\t\t\n\t\t\t// 同步更新dateRange\n\t\t\tif (startDate && endDate) {\n\t\t\t\tthis.dateRange = [startDate, endDate];\n\t\t\t} else {\n\t\t\t\tthis.dateRange = [];\n\t\t\t}\n\t\t\t\n\t\t\tthis.refresh(true); // 筛选条件变化时强制刷新\n\t\t},\n\t\t\n\t\t// 使用日期筛选查询数据\n\t\tonSearch() {\n\t\t\tthis.page = 1;\n\t\t\tthis.refresh(true); // 搜索时强制刷新\n\t\t},\n\t\t\n\t\t/**\n\t\t * 格式化日期字符串，确保用于查询的日期格式统一\n\t\t * @param {String} dateStr 日期字符串\n\t\t * @returns {String} 格式化后的日期字符串 YYYY-MM-DD\n\t\t */\n\t\tformatDateForQuery(dateStr) {\n\t\t\tif (!dateStr) return '';\n\t\t\t\n\t\t\t// 提取年月日\n\t\t\ttry {\n\t\t\t\t// 如果已经是YYYY-MM-DD格式，直接返回\n\t\t\t\tif (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateStr)) {\n\t\t\t\t\treturn dateStr;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 尝试创建日期对象\n\t\t\t\tconst date = new Date(dateStr);\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\treturn dateStr; // 如果是无效日期，返回原字符串\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 格式化为YYYY-MM-DD\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('日期格式化错误:', e);\n\t\t\t\treturn dateStr;\n\t\t\t}\n\t\t},\n\t\t\n\t\t/**\n\t\t * 标准化日期字符串，用于日期比较\n\t\t * @param {String} dateStr 日期字符串\n\t\t * @returns {String} 标准化后的日期字符串 YYYY-MM-DD\n\t\t */\n\t\tstandardizeDate(dateStr) {\n\t\t\tif (!dateStr) return '';\n\t\t\t\n\t\t\t// 尝试各种格式转换\n\t\t\ttry {\n\t\t\t\t// 处理YYYY/MM/DD格式\n\t\t\t\tdateStr = dateStr.replace(/\\//g, '-');\n\t\t\t\t\n\t\t\t\t// 如果包含时间部分，去除\n\t\t\t\tif (dateStr.includes(' ')) {\n\t\t\t\t\tdateStr = dateStr.split(' ')[0];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果是ISO格式，提取日期部分\n\t\t\t\tif (dateStr.includes('T')) {\n\t\t\t\t\tdateStr = dateStr.split('T')[0];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 确保格式为YYYY-MM-DD\n\t\t\t\tconst parts = dateStr.split('-');\n\t\t\t\tif (parts.length === 3) {\n\t\t\t\t\tconst year = parts[0];\n\t\t\t\t\tconst month = parts[1].padStart(2, '0');\n\t\t\t\t\tconst day = parts[2].padStart(2, '0');\n\t\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn dateStr;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('日期标准化错误:', e);\n\t\t\t\treturn dateStr;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取排序方式的显示文本\n\t\tgetSortText() {\n\t\t\tswitch(this.sortType) {\n\t\t\t\tcase 'execute':\n\t\t\t\t\treturn '按执行日期';\n\t\t\t\tcase 'create':\n\t\t\t\t\treturn '按添加日期';\n\t\t\t\tcase 'checkin':\n\t\t\t\t\treturn '按打卡时间';\n\t\t\t\tdefault:\n\t\t\t\t\treturn '按执行日期';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 设置排序方式\n\t\tsetSortType(type) {\n\t\t\tif (this.sortType !== type) {\n\t\t\t\tthis.sortType = type;\n\t\t\t\tthis.refresh(true); // 更新排序后强制刷新数据\n\t\t\t}\n\t\t\tthis.showSortOptions = false; // 选择后关闭下拉菜单\n\t\t},\n\t\t\n\t\t// 打开H5平台的日期选择器\n\t\topenH5DatePicker() {\n\t\t\tthis.h5DatePickerVisible = true;\n\t\t},\n\t\t\n\t\t// 处理H5平台的日期选择器变化\n\t\thandleH5DateRangeChange(e) {\n\t\t\tif (!e || e.length === 0) {\n\t\t\t\t// 清空筛选条件\n\t\t\t\tthis.resetH5DateRange();\n\t\t\t\tthis.h5DatePickerVisible = false; // 自动关闭弹窗\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (e.length === 2) {\n\t\t\t\t// 确保日期格式为YYYY-MM-DD\n\t\t\t\tconst startDate = this.formatDateString(e[0]);\n\t\t\t\tconst endDate = this.formatDateString(e[1]);\n\t\t\t\t\n\t\t\t\t// 设置筛选参数\n\t\t\t\tthis.filterParams = {\n\t\t\t\t\tstartDate: startDate,\n\t\t\t\t\tendDate: endDate\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t\t\t\t// 刷新数据\n\t\t\tthis.page = 1; // 重置页码\n\t\t\tthis.hasMore = true;\n\t\t\tthis.refresh(true); // H5日期筛选变化时强制刷新\n\t\t\t\t\n\t\t\t\t// 选择完成后自动关闭弹窗\n\t\t\t\tthis.h5DatePickerVisible = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 重置H5平台的日期选择器\n\t\tresetH5DateRange() {\n\t\t\tthis.filterParams = {\n\t\t\t\tstartDate: '',\n\t\t\t\tendDate: ''\n\t\t\t};\n\t\t\tthis.dateRange = [];\n\t\t\tthis.refresh(true); // 重置筛选时强制刷新\n\t\t},\n\t\t\n\t\t// 关闭H5平台的日期选择器\n\t\tcloseH5DatePicker() {\n\t\t\tthis.h5DatePickerVisible = false;\n\t\t},\n\t\t\n\t\t// 计算轮次统计信息\n\t\tcalculateRoundStats(task) {\n\t\t\tif (!task || !task.rounds_detail || !Array.isArray(task.rounds_detail)) {\n\t\t\t\treturn { missed_count: 0, not_checked_count: 0 };\n\t\t\t}\n\t\t\t\n\t\t\tlet missed_count = 0;      // 缺卡数（已结束轮次的未完成点位）\n\t\t\tlet not_checked_count = 0; // 未打卡数（进行中或未开始轮次的未完成点位）\n\t\t\tconst currentTime = new Date();\n\t\t\t\n\t\t\ttask.rounds_detail.forEach(round => {\n\t\t\t\t// 判断轮次是否已结束\n\t\t\t\tconst isRoundFinished = this.isRoundFinished(round, currentTime);\n\t\t\t\t\n\t\t\t\t// 使用轮次的stats数据计算未完成点位数\n\t\t\t\tlet roundUncompletedPoints = 0;\n\t\t\t\t\n\t\t\t\tif (round.stats) {\n\t\t\t\t\t// 从stats数据计算未完成点位数\n\t\t\t\t\tconst totalPoints = round.stats.total_points || 0;\n\t\t\t\t\tconst completedPoints = round.stats.completed_points || 0;\n\t\t\t\t\troundUncompletedPoints = Math.max(0, totalPoints - completedPoints);\n\t\t\t\t} else {\n\t\t\t\t\t// 如果没有stats数据，输出警告并跳过\n\t\t\t\t\tconsole.warn('轮次缺少stats统计数据:', round);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (isRoundFinished) {\n\t\t\t\t\t// 轮次已结束：未完成的点位算缺卡\n\t\t\t\t\tmissed_count += roundUncompletedPoints;\n\t\t\t\t} else {\n\t\t\t\t\t// 轮次进行中或未开始：未完成的点位算未打卡\n\t\t\t\t\tnot_checked_count += roundUncompletedPoints;\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\treturn { missed_count, not_checked_count };\n\t\t},\n\t\t\n\t\t// 判断轮次是否已结束\n\t\tisRoundFinished(round, currentTime = new Date()) {\n\t\t\tif (!round) return true;\n\t\t\t\n\t\t\t// 1. 检查轮次状态\n\t\t\t// 状态2=已完成, 状态3=已超时\n\t\t\tif (round.status === 2 || round.status === 3) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\t\n\t\t\t// 2. 检查轮次结束时间\n\t\t\tif (round.end_time) {\n\t\t\t\ttry {\n\t\t\t\t\tconst roundEndTime = new Date(round.end_time);\n\t\t\t\t\tif (currentTime > roundEndTime) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.warn('解析轮次结束时间失败:', round.end_time);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 3. 如果轮次状态是未开始(0)，则未结束\n\t\t\tif (round.status === 0) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\t// 4. 如果轮次状态是进行中(1)，检查是否超时\n\t\t\treturn false;\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\">\n// H5平台日期选择器样式覆盖\n// #ifdef H5\n// H5平台的日期选择器样式\n.h5-date-picker-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 99999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.h5-date-picker-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n}\n\n.h5-date-picker-content {\n  position: relative;\n  background-color: #fff;\n  border-radius: 16px;\n  width: 90%;\n  max-width: 380px;\n  padding: 20px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n  z-index: 100000;\n}\n\n.h5-date-picker-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.h5-date-picker-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  padding-left: 8px;\n}\n\n.h5-date-picker-close {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 22px;\n  color: #666;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all 0.3s;\n  background-color: #f5f5f5;\n}\n\n.h5-date-picker-close:active {\n  background-color: #e0e0e0;\n}\n\n.h5-date-picker-footer {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 24px;\n  padding: 0 8px;\n}\n\n.h5-date-picker-reset,\n.h5-date-picker-confirm {\n  flex: 1;\n  height: 44px;\n  font-size: 16px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s;\n  font-weight: 500;\n  outline: none;\n  border: none;\n}\n\n.h5-date-picker-reset {\n  background-color: #f0f0f0;\n  color: #666;\n  margin-right: 12px;\n}\n\n.h5-date-picker-confirm {\n  background-color: #1677FF;\n  color: #fff;\n  box-shadow: 0 4px 8px rgba(22, 119, 255, 0.2);\n}\n\n.h5-date-picker-reset:active {\n  opacity: 0.8;\n}\n\n.h5-date-picker-confirm:active {\n  background-color: #0e5ecc;\n  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);\n}\n// #endif\n\n.record-container {\n\theight: 100vh;\n\tbackground-color: #F5F7FA;\n\tdisplay: flex;\n\tflex-direction: column;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.fixed-header {\n\twidth: 100%;\n\tz-index: 10;\n\tbackground-color: #F5F7FA;\n\tposition: relative;\n}\n\n.tab-filter {\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid #F0F0F0;\n\twidth: 100%;\n\tbox-sizing: border-box;\n\tmargin-bottom: 10rpx;\n}\n\n.tab-group {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between; /* 修改为space-between以便排序选项靠右 */\n\tmargin: 10rpx 0;\n\twidth: 100%;\n}\n\n.tab-options {\n\tdisplay: flex;\n\tflex-wrap: nowrap;\n\talign-items: center;\n\tflex: 1; /* 让选项占据剩余空间 */\n\toverflow-x: auto;\n\twhite-space: nowrap;\n\t-webkit-overflow-scrolling: touch;\n\t/* 隐藏滚动条 */\n\tscrollbar-width: none; /* Firefox */\n\t-ms-overflow-style: none; /* IE/Edge */\n\t&::-webkit-scrollbar {\n\t\tdisplay: none; /* Chrome/Safari */\n\t}\n}\n\n.tab-item {\n\tfont-size: 26rpx;\n\tcolor: #666666;\n\tpadding: 10rpx 20rpx; /* 进一步减小内边距 */\n\tbackground-color: #F5F7FA;\n\tborder-radius: 30rpx;\n\tmargin-right: 20rpx;\n\tflex-shrink: 0;\n\tdisplay: inline-block;\n\ttransition: all 0.3s;\n\t\n\t&:last-child {\n\t\tmargin-right: 10rpx;\n\t}\n\t\n\t&.active {\n\t\tbackground-color: #E6F7FF;\n\t\tcolor: #1677FF;\n\t\tborder: 1rpx solid #91D5FF;\n\t}\n\t\n\t&.date-range {\n\t\tposition: relative;\n\t\tpadding: 10rpx 20rpx; /* 进一步减小内边距 */\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\theight: 56rpx; /* 进一步减小高度 */\n\t\t/* #ifndef H5 */\n\t\toverflow: hidden;\n\t\t/* #endif */\n\t\tbox-sizing: border-box;\n\t\t\n\t\t.uni-date {\n\t\t\twidth: 100%;\n\t\t}\n\t\t\n\t\t.uni-date-editor {\n\t\t\twidth: 100%;\n\t\t\tbackground: transparent !important;\n\t\t}\n\t\t\n\t\t.uni-date-range {\n\t\t\twidth: 100% !important;\n\t\t}\n\t\t\n\t\t.uni-date-x {\n\t\t\tbackground: transparent !important;\n\t\t\tborder: none !important;\n\t\t}\n\t}\n}\n\n.date-range-text {\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\tmax-width: 370rpx; /* 增加最大宽度 */\n\tfont-size: 26rpx;\n\tline-height: 36rpx;\n\tcolor: #666666;\n}\n\n.record-list {\n\tflex: 1;\n\theight: calc(100vh - 150rpx); /* 减去固定头部的高度 */\n\toverflow-y: auto;\n}\n\n.route-area {\n\tmargin-bottom: 24rpx;\n\tpadding-top: 10rpx;\n}\n\n.route-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin: 10rpx 20rpx 16rpx 20rpx;\n\tpadding: 0;\n}\n\n.route-title-container {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 10rpx;\n\tpadding: 20rpx 24rpx;\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\n\twidth: 100%;\n}\n\n.title-left {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.title-decorator {\n\twidth: 6rpx;\n\theight: 36rpx;\n\tbackground-color: #1677FF;\n\tborder-radius: 3rpx;\n\tmargin-right: 16rpx;\n}\n\n.route-title-text {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #222222;\n}\n\n.user-name {\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #1677FF;\n\tbackground-color: #E6F7FF;\n\tborder-radius: 20rpx;\n\tpadding: 4rpx 12rpx;\n\tdisplay: inline-block;\n\tborder: 1rpx solid #91D5FF;\n}\n\n.route-card {\n\tbackground-color: #FFFFFF;\n\tborder-radius: 10rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 8rpx rgba(22, 119, 255, 0.15);\n\tpadding: 30rpx 24rpx;\n\tmargin: 0 20rpx 30rpx 20rpx;\n\ttransition: all 0.2s;\n\t\n\t&:active {\n\t\ttransform: scale(0.98);\n\t}\n}\n\n.route-info {\n\tdisplay: flex;\n\tmargin-bottom: 24rpx;\n}\n\n.route-points, .route-completion {\n\tmargin-right: 40rpx;\n}\n\n.route-label {\n\tfont-size: 28rpx;\n\tcolor: #8F959E;\n}\n\n.route-value {\n\tfont-size: 30rpx;\n\tcolor: #222222;\n\tfont-weight: 500;\n}\n\n.route-rounds {\n\tmargin-right: 40rpx;\n}\n\n.route-stats {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tmargin: 24rpx 0;\n\tpadding: 24rpx 0;\n\tborder-top: 1rpx solid #F0F0F0;\n\tborder-bottom: 1rpx solid #F0F0F0;\n}\n\n.stat-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tflex: 1;\n\tpadding: 0 4rpx;\n\tmin-width: 120rpx; /* 添加最小宽度 */\n}\n\n.stat-value {\n\tfont-size: 36rpx;\n\tfont-weight: 600;\n\tmargin-bottom: 10rpx;\n\tline-height: 1.2;\n\ttext-align: center; /* 确保文字居中 */\n}\n\n.stat-label {\n\tfont-size: 24rpx;\n\tcolor: #8F959E;\n\ttext-align: center;\n}\n\n.normal .stat-value {\n\tcolor: #52C41A;\n}\n\n.overtime .stat-value {\n\tcolor: #FAAD14;\n}\n\n.missed .stat-value {\n\tcolor: #F5222D;\n}\n\n.not-checked .stat-value {\n\tcolor: #8C8C8C;\n}\n\n.total-time {\n\tflex: 1.5; /* 给总用时1.5倍的空间 */\n}\n\n.total-time .stat-value {\n\tcolor: #1677FF;\n\tfont-size: 36rpx; /* 稍微减小字号 */\n\tfont-weight: 600;\n\tline-height: 1.2;\n\twhite-space: nowrap; /* 防止换行 */\n}\n\n.route-last-updated {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-top: 8rpx;\n}\n\n.last-updated-text {\n\tdisplay: flex;\n\talign-items: center;\n\t\n\ttext {\n\t\tfont-size: 26rpx;\n\t\tcolor: #8F959E;\n\t\tmargin-left: 8rpx;\n\t}\n}\n\n.route-card uni-icons[type=\"forward\"] {\n\tmargin-right: 8rpx;\n}\n\n.loading-more, .no-more {\n\ttext-align: center;\n\tpadding: 30rpx 0;\n}\n\n.loading-text, .no-more-text {\n\tfont-size: 24rpx;\n\tcolor: #8F959E;\n\tletter-spacing: 1rpx;\n}\n\n.sort-container {\n\tposition: relative;\n\tmargin-left: 20rpx;\n\tflex-shrink: 0;\n}\n\n.sort-button {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: #F5F7FA;\n\tborder-radius: 30rpx;\n\tpadding: 10rpx 20rpx; /* 进一步减小内边距 */\n\ttransition: all 0.3s;\n\t\n\t&:active {\n\t\tbackground-color: #E6F7FF;\n\t}\n}\n\n.sort-text {\n\tfont-size: 26rpx;\n\tcolor: #666666;\n\tmargin-right: 8rpx;\n}\n\n.sort-options {\n\tposition: absolute;\n\ttop: calc(100% + 10rpx);\n\tright: 0;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 10rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n\tpadding: 10rpx;\n\tz-index: 100;\n\tmin-width: 180rpx;\n}\n\n.sort-option {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 16rpx 20rpx;\n\tfont-size: 26rpx;\n\tcolor: #666666;\n\ttransition: all 0.2s;\n\tborder-radius: 6rpx;\n\t\n\t&:not(:last-child) {\n\t\tmargin-bottom: 6rpx;\n\t}\n\t\n\t&:active {\n\t\tbackground-color: #F5F7FA;\n\t}\n\t\n\t&.active {\n\t\tcolor: #1677FF;\n\t\tbackground-color: #E6F7FF;\n\t}\n}\n\n.rotate-icon {\n\ttransform: rotate(180deg);\n\ttransition: transform 0.3s;\n}\n\n// H5平台的日期选择器内部样式\n// #ifdef H5\n:deep(.date-picker-custom-content) {\n  padding: 8px 0;\n}\n\n:deep(.date-picker-custom-input) {\n  border-radius: 8px;\n  border: 1px solid #e0e0e0;\n  padding: 8px 10px;\n  transition: all 0.3s;\n}\n\n:deep(.date-picker-custom-input:focus) {\n  border-color: #1677FF;\n  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);\n}\n\n:deep(.date-picker-custom-range) {\n  padding: 10px;\n}\n\n:deep(.uni-date-range--text) {\n  font-size: 15px;\n  color: #333;\n}\n\n:deep(.uni-date-editor .uni-date__icon-clear) {\n  color: #999;\n}\n\n:deep(.uni-date-editor--x .uni-date__icon-clear) {\n  border-radius: 50%;\n  background-color: #f5f5f5;\n  width: 18px;\n  height: 18px;\n  line-height: 18px;\n  right: 8px;\n}\n\n:deep(.uni-datetime-picker-container) {\n  margin-top: 10px;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n\n:deep(.uni-datetime-picker-container .uni-datetime-picker-btn) {\n  border-radius: 20px;\n  padding: 6px 20px;\n}\n\n:deep(.uni-datetime-picker-container .uni-datetime-picker-btn-text) {\n  font-size: 15px;\n  font-weight: 500;\n}\n\n:deep(.uni-datetime-picker-container .uni-datetime-picker-btn-group) {\n  padding: 10px;\n}\n// #endif\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571661144\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
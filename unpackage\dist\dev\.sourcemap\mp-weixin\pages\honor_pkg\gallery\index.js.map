{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/honor_pkg/gallery/index.vue?1d72", "webpack:///D:/Xwzc/pages/honor_pkg/gallery/index.vue?6dfa", "webpack:///D:/Xwzc/pages/honor_pkg/gallery/index.vue?0b86", "webpack:///D:/Xwzc/pages/honor_pkg/gallery/index.vue?163f", "uni-app:///pages/honor_pkg/gallery/index.vue", "webpack:///D:/Xwzc/pages/honor_pkg/gallery/index.vue?8431", "webpack:///D:/Xwzc/pages/honor_pkg/gallery/index.vue?2b1b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "PEmptyState", "data", "loading", "current<PERSON>iew", "selected<PERSON><PERSON>r", "currentMonth", "currentYear", "scrollTop", "refreshing", "currentPage", "pageSize", "autoPlayTimer", "autoPlayInterval", "honors", "allHonorsData", "lastUserId", "pendingHonorId", "devicePerformance", "touchStartX", "touchStartY", "touchStartTime", "touchMoved", "minSwipeDistance", "maxSwipeTime", "swipe<PERSON><PERSON><PERSON><PERSON>", "debounceTimers", "isProcessing", "currentRankingPeriod", "currentRankingMetric", "currentWeek", "currentQuarter", "currentWeekOfMonth", "listViewMode", "expandedUsers", "rankingPeriods", "value", "label", "icon", "rankingMetrics", "honorsCache", "rankingCache", "lastCacheTime", "cacheExpireTime", "maxCacheSize", "maxCacheItemSize", "userLikeStats", "likeProcessing", "customToast", "show", "message", "type", "timer", "computed", "userInfo", "isAdmin", "displayHonors", "now", "timestamp", "getCurrentMonthHonors", "honors<PERSON>y<PERSON>atch", "grouped", "batch", "gridPagedData", "pages", "pageIndex", "hasMore", "featuredCount", "totalViews", "totalLikes", "totalPages", "currentPageData", "sortedHonorsWithRank", "filtered", "userAggregatedData", "userName", "userAvatar", "department", "latestHonor", "totalAwards", "totalFeatured", "honorTypes", "userData", "Object", "id", "createTime", "honorType", "reason", "images", "isFeatured", "viewCount", "likeCount", "rankClass", "displayScore", "scoreLabel", "_rankKey", "imageGridClass", "displayListHonors", "groupedListHonors", "totalCount", "expanded", "<PERSON><PERSON><PERSON>d", "group", "currentTimeDisplay", "watch", "immediate", "handler", "onLoad", "initPromises", "Promise", "honor", "setTimeout", "onShow", "onHide", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "methods", "initUserInfo", "token", "cachedUserInfo", "get<PERSON><PERSON><PERSON><PERSON>", "CACHE_KEYS", "userRoleStr", "userRole", "permissions", "uni", "fallbackUserInfo", "goBack", "delta", "fail", "url", "goToAdmin", "debounce", "fn", "debounceImmediate", "initGallery", "loadAllHonorData", "currentUserId", "requestData", "action", "page", "size", "status", "uniIdToken", "uniCloud", "result", "rawData", "console", "loadHonorData", "year", "month", "calculateWeekLabel", "convertToPageFormat", "userId", "period", "meetingDate", "week<PERSON><PERSON><PERSON>", "userLikeCount", "todayLikeCount", "isLiked", "_userId", "selectHonor", "closeDetail", "disableScroll", "enableScroll", "switchView", "startAutoPlay", "stopAutoPlay", "clearInterval", "prevPage", "nextPage", "goToPage", "resetAutoPlay", "onBtnTouchStart", "e", "onBtnTouchEnd", "sleep", "previewImage", "urls", "current", "indicator", "loop", "onImageError", "loadMonthData", "addLikeToHonor", "<PERSON><PERSON><PERSON>", "title", "content", "showCancel", "confirmText", "confirmColor", "originalLikeCount", "originalUserLikeCount", "honorId", "removeLikeFromHonor", "updateData", "updateHonorInAllArrays", "clearAllCaches", "shareHonor", "shareKey", "provider", "scene", "href", "summary", "imageUrl", "success", "fallbackShare", "copyToClipboard", "loadUserLikeStats", "stats", "incrementViewCount", "targetHonor", "oldCount", "newCount", "honorInAllData", "honor<PERSON>n<PERSON><PERSON><PERSON>", "serverCount", "rollbackCount", "clearHonorsCache", "clearRankingCache", "manageCacheSize", "map", "key", "sort", "keysToDelete", "cacheItem", "formatDate", "formatBatchDate", "setListViewMode", "toggleUserGroup", "setRankingPeriod", "syncTimeDataForPeriod", "setRankingMetric", "getFilteredHonorsByPeriod", "cache<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON>", "honorWeekOfMonth", "calculateRankingScore", "getScoreLabel", "getAwardCount", "prevMonth", "nextMonth", "smartLoadMonthData", "honorDate", "recordCount", "onPullRefresh", "onRefreshRestore", "refreshData", "onTouchStart", "onTouchMove", "onTouchEnd", "onTouchCancel", "detectDevicePerformance", "performanceLevel", "clearStorageSpace", "cacheKeys", "prevTimePeriod", "nextTimePeriod", "prevWeek", "nextWeek", "prevQuarter", "nextQuarter", "prevYear", "nextYear", "date"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9HA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC40BnnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC,iBACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MAEA;MACAC,iBACA;QAAAH;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MAEA;MACAE;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAEA;MACAC;MAAA;;MAEA;MACAC;QACAC;QACAC;QACAC;QAAA;QACAC;MACA;IACA;EACA;EAEAC,0CACA;IACAC;EACA;IAEA;IACAC;MACA,qCACA,mCACA,2BACA,2BACA;IACA;IAEA;IACAC;MAAA;QAAA;MACA;MACA;MACA;;MAEA;MACA,kCACAC;QACA;MACA;;MAEA;MACA;QACA;QACA;UACA;QACA;QAEA;QACA;QACA;QAEA;MACA;;MAEA;MACA;QACAvD;QACAwD;MACA;MACA;;MAEA;MACA;MAEA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;QACA;QACA;UACAC;YACAC;YACAhD;UACA;QACA;QACA+C;MACA;MAEA;QACA;QACA;QAEA;UACA;QACA;QAEA;MACA;IACA;IAEA;IACAE;MACA;MACA;MACA;MAEA;QACA;QACA;UACAC;YACAC;YACAH;YACAhD;YACAoD;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;IACA;IAEAC;MACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;QACA;QACA;MACA;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;QACA;UACA;QACA;MACA;MAEA;;MAEA;MACA;MAEAC;QACA;UACAC;YACAC;YACAC;YACAC;YACA;YACAC;YACA;YACAC;YACAC;YACAX;YACAD;YACA;YACAa;UACA;QACA;QAEA;;QAEA;QACA;UACAC;QACA;;QAEA;QACAA;QACA;UACAA;QACA;QACAA;QACAA;;QAEA;QACA;QACAA;MACA;;MAEA;MACA;QACA;QACA;UAAA,OACAA;QAAA,GACAC,4CACA;QAEA;UACA;UACAC;UACAT;UACAC;UACAC;UACAQ;UACA;UACAC;YACAF;YACArF;UACA;UACAwF;UACAzB;UACA0B;UACAC;UAAA;UACA;UACAV;UACAC;UACAX;UACAD;UACA;UACAsB;UACAC;QACA;MACA;;MAEA;MACA;QACA;QACA;QAEA;UACA;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;cACA;YACA;cACA;UAAA;QAEA;QAEA;MACA;MAEA;QACA;QACA;QAEA,uCACAT;UACAU;UACAC;UACAC;UACAC;QAAA;MAEA;;MAEA;MACA;QACA;MACA;MACA;QACA7F;QACAwD;MACA;;MAEA;;MAEA;IACA;IAEA;IACAsC;MACA;QACA;MACA;MAEA;MAEA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;;MAEA;MACA;QACA;UACArC;YACAc;YACAC;YACA9D;YACAqF;YACAhC;YACAC;YACAC;YACA+B;YACAC;UACA;QACA;;QAEAxC;QACAA;QACA;UACAA;QACA;QACAA;QACAA;;QAEA;QACA;UACAA;QACA;MACA;;MAEA;MACA;QACA;UACA;QACA;;QACA;UACA;QACA;;QACA;MACA;QACA;QACAyC;UAAA;QAAA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;YACA;QAAA;MAEA;MACA;MACA;IACA;EAAA,EAGA;EAEAC;IACAlD;MACAmD;MACAC;QACA;QACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;EAEAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA;;cAEA;cACAC,gBACA,uBACA;cAAA,CACA,EAEA;;cACA;cACA;;cAEA;cAAA;cAAA,OACAC;YAAA;cAEA;cACA;gBACAC;kBAAA;gBAAA;kBAAA;gBAAA;gBACA;kBACA;kBACA;gBACA;cACA;;cAEA;cAAA;cAAA,OACA;YAAA;cAEA;cACA;;cAEA;cACAC;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EAEAC;IACA;IAEA;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IAAA;IACA;IAEA/B;MACA;QACAgC;MACA;IACA;IACA;;IAEA;IACA;MACAA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBACAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAEAjE,6FAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA;kBAAA,mDAGA;gBAAA;cAAA;gBAAA;gBAAAkE;gBAAAC;gBACAC;gBACA;kBACAC;kBACA;oBACArE;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAKA;gBACAA;gBACAA;gBAEA;kBACAA;kBACAgE;kBACAM;gBACA;gBAAA;cAAA;gBAIA;gBACAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACAA;cAAA;gBAIA;gBACA;gBACA;gBACAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAxE;gBACAA;gBACAA;gBAAA,KAEAA;kBAAA;kBAAA;gBAAA;gBACA;kBACAA;kBACAgE;kBACAM;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAOA;IAEA;IACAG;MACAF;QACAG;QACAC;UACAJ;YACAK;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MAEAN;QACAK;MACA;IACA;IAEA;IACAE;MAAA;MAAA;MACA;QACAjB;MACA;MAEA;QACAkB;QACA;MACA;IACA;IAEAC;MAAA;MAAA;MACA;MACA;MACA;MAEA;QACAnB;MACA;;MAEA;MACA;QACAkB;QACA;MACA;MAEA;QACAA;QACA;MACA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAGA;gBACA;;gBAEA;gBACA;kBACAxB;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAEAC;kBACAC;kBACAzI;oBACA0I;oBACAC;oBACAC;kBACA;kBACAC;gBACA;gBAAA;gBAAA,OAEAC;kBACAjJ;kBACAG;gBACA;cAAA;gBAHA+I;gBAKA;kBACA;kBACA;oBACAC;oBACA;;oBAEA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAV;kBACAC;kBACAzI;oBACAmJ;oBACAC;oBACAV;oBACAC;oBACAC;kBACA;kBACAC;gBACA;gBAAA;gBAAA,OAEAC;kBACAjJ;kBACAG;gBACA;cAAA;gBAHA+I;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACA;gBAEA;kBACApC;oBAAA;kBAAA;kBACA;oBACA;oBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyC;MACA;MACA;QACA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;UACA;UACA;YACA;YACA;YACA;;YAEA;YACA;YACA;UACA;QACA;UACA;QAAA;MAEA;MAEA;IACA;IAEA;IACAC;MAAA;QAAA;MACA;QACA;MACA;MAEA;MAEA;QAAA;QACA;QAEA;UACApE;UACAqE;UACA9E;UACAC;UACAC;UACAS;YACAF;YACArF;UACA;UACAwF;UACAC;UACAH;UACAvB;YACAsB;YACArF;YACAoD;YACAuG;YACAC;YACAC;UACA;UACAlE;UACAC;UACAkE;UACAC;UACArE;UACAsE;UACA;UACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAd;QACA;MACA;;MAEA;MACA;QACA;;QAEA;QACA;;QAEA;QACApC;UACA;QACA;MACA;IACA;IAEA;IACAmD;MACA;MACA;MACA;IACA;IAEA;IACAC,yCAKA;IAEA;IACAC,uCAKA;IAEA;IACAC;MAAA;MACA;MAEA;QACA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;QAEAtD;UACA;UAEA;YACA;cACA;gBACA;cACA;cACA;gBACA;cACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAuD;MAAA;MACA;MACA;MAEA;QACA;MACA;IACA;IAEAC;MACA;QACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QAEA;UACA;QACA;UACA;QACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;QAEA;UACA;QACA;UACA;QACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IAEAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACAC;IACA;IAEAC;MACAD;IACA;IAEA;IACAE;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MAEA;MAEAlE;QACAc;UACAqD;UACAC;UACAC;UACAC;UACApD;YACA;UAAA;QAEA;MACA;IACA;IAEA;IACAqD;MACA;IAAA,CACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAEA;gBACA;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAIA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAEA;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BAAA;0BAEA;0BACAhC;0BAAA,MACA;4BAAA;4BAAA;0BAAA;0BACA5B;4BACA6D;4BACAC;4BACAC;4BACAC;4BACAC;0BACA;0BAAA;wBAAA;0BAAA,MAKA;4BAAA;4BAAA;0BAAA;0BACAjE;4BACA6D;4BACAC;4BACAC;4BACAC;4BACAC;0BACA;0BAAA;wBAAA;0BAIA;0BACAC;0BACAC,mDAEA;0BACA;;0BAEA;0BACAlF;0BACAA;0BACAA;0BAAA;0BAAA,OAEAkC;4BACAjJ;4BACAG;8BACAyI;8BACAzI;gCACA+L;8BACA;8BACAlD;4BACA;0BACA;wBAAA;0BATAE;0BAAA,MAWAA;4BAAA;4BAAA;0BAAA;0BAAA,QACAA,iNAEA;0BACAnC;0BACAA;0BACAA;;0BAEA;0BACA;4BACAnB;4BACAkE;4BACAC;4BACAC;0BACA;;0BAEA;0BACA;4BACA;4BACA;4BACA;0BACA;;0BAEA;0BACA;;0BAEA;0BACA;4BACA;0BACA;;0BAEA;0BACA;0BAAA;0BAAA;wBAAA;0BAAA,MAGA;wBAAA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAGA;;0BAEA;0BACAjD;0BACAA;0BACAA;;0BAEA;0BACA;;0BAEA;0BACA;4BACA;0BACA;4BACA;0BACA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA,CAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAoF;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAT;gBAEA;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BAAA;0BAEA;0BACAhC;0BAAA,MACA;4BAAA;4BAAA;0BAAA;0BACA5B;4BACA6D;4BACAC;4BACAC;4BACAC;4BACAC;0BACA;0BAAA;wBAAA;0BAAA,MAKA;4BAAA;4BAAA;0BAAA;0BACA5I,oCACA,cACA,WAEA;0BACA;0BAAA;wBAAA;0BAIA;0BACA6I;0BACAC,oDAEA;0BACA;0BAAA;0BAAA,OAEAhD;4BACAjJ;4BACAG;8BACAyI;8BACAzI;gCACA+L;8BACA;8BACAlD;4BACA;0BACA;wBAAA;0BATAE;0BAAA,MAWAA;4BAAA;4BAAA;0BAAA;0BAAA,QACAA,wKAEA;0BACAnC;0BACAA;0BACAA;0BACAA;;0BAEA;0BACAqF;4BACAxG;4BACAkE;4BACAC;4BACAC;0BACA;0BAIA;;0BAEA;0BACA;4BACA;4BACA;4BACA;0BACA;;0BAEA;0BACA;;0BAEA;0BACA;4BACA;0BACA;;0BAEA;0BACA7G;0BACA;4BACAA;0BACA;0BAEA;0BAAA;0BAAA;wBAAA;0BAAA,MAGA;wBAAA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAGA;;0BAEA;0BACA4D;0BACAA;0BACAA;;0BAEA;0BACA;;0BAEA;0BACA;4BACA;0BACA;4BACA;0BACA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA,CAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAsF;MACA;QACA;UACAjH;QACA;MACA;MAEA;MACA;;MAEA;MACA;QACAA;MACA;IACA;IAEA;IACAkH;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAEA;kBAAA;oBAAA;sBAAA;wBAAA;0BACA;4BACA;;4BAEA1E;8BACA2E;8BACAC;8BACAtJ;8BACAuJ;8BACAhB;8BACAiB;8BACAC;8BACAC;gCACA;8BACA;8BACA5E;gCACA;8BACA;4BACA;0BAkBA;4BACA;4BACA;0BACA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA,CACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA6E;MACA;MAmBA;IAEA;IAEA;IACAC;MAEA;MACArN;QACAQ;QACA2M;UACA;QAAA,CACA;QACA5E;UACAJ;YACA6D;YACAC;YACAC;YACAC;UACA;QACA;MACA;IAqBA;IAEA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAvD;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEAT;kBACAjJ;kBACAG;oBACAyI;oBACAI;kBACA;gBACA;cAAA;gBANAE;gBAQA;kBACAgE,4BAGA;kBACA;;kBAEA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;kBAAA;gBAAA;gBACA;kBACAA;oBAAA;kBAAA;gBACA;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAMA;gBACAC;gBACAC,0BAEA;gBACAC;kBAAA;gBAAA;gBACA;kBACAA;gBACA;;gBAEA;gBACAC;kBAAA;gBAAA;gBACA;kBACAA;gBACA;;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACAvE;kBACAjJ;kBACAG;oBACAyI;oBACAzI;sBAAA+L;oBAAA;kBACA;gBACA;cAAA;gBANAhD;gBAQA;kBACAuE;kBACA;oBACA;oBACA;sBACAF;oBACA;oBACA;sBACAC;oBACA;oBACA;sBACA;oBACA;;oBAEA;oBACA;kBACA;gBACA;kBACA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBAEA;gBACAE;gBACA;kBACAH;gBACA;gBACA;kBACAC;gBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;;MAEA;MACA;QACA,2BACAC;UAAA;YACAC;YACApK;UACA;QAAA,GACAqK;UAAA;QAAA;QAEA;QACAC;UACA;QACA;MACA;;MAEA;MACA7I;QACA;QACA;UACA8I;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;QACA;;QAEA;QACA;UACA;UACA;;UAEA;UACA;YACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;QACA;QACA;;QAEA;QACA;QAEA;;QAEA;QACA;QAEAvH;UACA;UAEA;YACA;UACA;QAEA;MACA;IACA;IAEA;IACAwH;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;QAEA;UACA;UACA;UACA;QACA;MACA;QACA;QACA;QACA;QAEA;UACA;UACA;UACA;UACA;QACA;MACA;QACA;QACA;QAEA;UACA;QACA;MACA;MACA;IACA;IAEAC;MAAA;MACA;MAEA;QACA;QACA;QACA;QAEAzH;UACA;UAEA;YACA;UACA;QAEA;MACA;IACA;IAEA;IACA0H;MAAA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;;MAEA;MACA;QACA;UACA;UACAC;UACA;QACA;UACAA;UACA;QACA;UACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;MAAA;;MAGA;MACA;QACA;MACA;;MAEA;MACA;MAEA;QACA;QACA;QACA;QAEA;UACA;YACA;YACA;YACA;YAEA,qCACAC,gCACAC;UAEA;YACA;YACA;UAEA;YACA;YACA;YACA;YAEA;UAEA;YACA;YACA;UAEA;YACA;QAAA;MAEA;;MAEA;MACA;QACA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;UACA;UACA;UACA;UACA;UAEA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;UACA;QACA;UACA;UACA;QACA;QACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;UACA;QACA;UACA;UACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAR;gBACAjL;gBAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACA;kBACA;kBACA0B;oBACA;sBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAAA,MAKA,iCACA1B;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACAgB;kBACA;kBACA,0DACA0K;gBACA;gBAAA,MAEA1K;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACAvE;kBACAwD;kBACA0L;gBACA;gBACA;gBAAA;cAAA;gBAKA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAIA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;kBAAA;oBAAA;sBAAA;wBAAA;0BACA;0BACA;0BAAA;0BAAA;0BAAA,OAEA;wBAAA;0BAAA;0BAEA;0BACA;0BACAxH;0BAAA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA,CAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAyH;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;kBACA1H;oBACA6D;kBACA;gBACA;;gBAEA;gBACA;gBACA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA7E,aACA,yBACA,2BACA;cAAA;gBAEA;gBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;cAAA;gBAAA;gBAEA;kBACAgB;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA2H;MACA;MAEA;MACA;MAEA;QACA;QACA;MACA;IACA;IAEAC;MACA;MAEA;MAEA;QACA;QACA;QAEA;UACA3E;QACA;MACA;IACA;IAEA4E;MAAA;MACA;MAEA;MACA;MAEA;QACA;MACA;MAEA;QACA;QACA;QAEA;QACA;QAEA;QACA;QAEA;UACA;UAEA;YACA;UACA;YACA;UACA;UAEA3I;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA4I;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;;QAEA;QACA;QAEA;UACAC;QACA;UACAA;QACA;QAEA;;QAEA;QACA;UACA;YACA;YACA;YACA;UACA;YACA;YACA;YACA;UACA;YACA;YACA;YACA;QAAA;MAEA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QAEA;UACA;UACA;YACA;cAAA,OACAhC,kCACAA,2BACAA;YAAA,EACA;YAEAiC;cACA;gBACAlI;cACA;gBACA;cAAA;YAEA;UACA;QACA;MACA;QACA;MAAA;IAEA;IAEA;IACAmI;MAAA;MACA;QACA;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;UAAA;QAEA;UACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;UAAA;QAEA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;EAAA,6EAGA;IACA;MACA;IACA;MACA;MACA;IACA;IACA;EACA,8EAEA;IACA;MACA;IACA;MACA;MACA;IACA;IACA;EACA,0GAGA;IAAA;IACA;IACA;IACA;MACA;IACA;;IAEA;IACA;;IAEA;IACA;MACA;MACA;MACA;IACA;EACA,0FAGA;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,MAGA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;cAGA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAIA,oFAGA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;EAGA,sFAGAC;IACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;;IAEA;IACA;EACA,wFAKAtN;IAAA;IAAA;IAAA;IACA;IACA;MACAiE;IACA;;IAEA;IACA;IACA;IACA;;IAEA;IACA;MACA;MACA;IACA;EACA,8FAGA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cACAuH;cACA;gBACA;cACA;;cAEA;cAAA;cAAA,OACA;YAAA;cAEA;cACA;;cAEA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA;YAAA;cAAA;cAEA;cACA7G;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AAEA;AAAA,2B;;;;;;;;;;;;;AC7rGA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/honor_pkg/gallery/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/honor_pkg/gallery/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4213b958&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/honor_pkg/gallery/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=4213b958&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.currentView === \"grid\" && _vm.currentPageData && !_vm.loading\n      ? _vm.currentPageData.honors.length\n      : null\n  var g1 =\n    _vm.currentView === \"grid\" && !_vm.loading\n      ? _vm.currentPageData && _vm.currentPageData.honors.length > 0\n      : null\n  var g2 =\n    _vm.currentView === \"grid\"\n      ? _vm.totalPages > 1 && _vm.gridPagedData.length > 0\n      : null\n  var g3 =\n    _vm.currentView === \"grid\"\n      ? _vm.totalPages > 1 && _vm.gridPagedData.length > 0\n      : null\n  var g4 = _vm.currentView === \"list\" ? _vm.displayListHonors.length : null\n  var g5 =\n    _vm.currentView === \"list\" && _vm.listViewMode === \"grouped\"\n      ? _vm.groupedListHonors.length\n      : null\n  var g6 =\n    _vm.currentView === \"list\" && !_vm.loading\n      ? _vm.displayListHonors.length\n      : null\n  var g7 =\n    _vm.currentView === \"ranking\" && !_vm.loading\n      ? _vm.sortedHonorsWithRank.length\n      : null\n  var g8 = _vm.currentView === \"grid\" ? _vm.displayHonors.length : null\n  var g9 =\n    _vm.currentView === \"grid\" && !_vm.loading ? _vm.displayHonors.length : null\n  var g10 = _vm.selectedHonor\n    ? _vm.selectedHonor.images && _vm.selectedHonor.images.length > 0\n    : null\n  var g11 = _vm.selectedHonor && g10 ? _vm.selectedHonor.images.length : null\n  var g12 = _vm.selectedHonor && g10 ? _vm.selectedHonor.images.length : null\n  var l0 =\n    _vm.selectedHonor && g10 && !(g12 === 1)\n      ? _vm.__map(\n          _vm.selectedHonor.images.slice(0, 5),\n          function (image, index) {\n            var $orig = _vm.__get_orig(image)\n            var g13 = index === 4 && _vm.selectedHonor.images.length > 5\n            var g14 = g13 ? _vm.selectedHonor.images.length : null\n            return {\n              $orig: $orig,\n              g13: g13,\n              g14: g14,\n            }\n          }\n        )\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, image) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        image = _temp2.image\n      var _temp, _temp2\n      return _vm.previewImage(image.url, _vm.selectedHonor.images)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n        g9: g9,\n        g10: g10,\n        g11: g11,\n        g12: g12,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <!-- 页面元数据，用于控制页面滚动 -->\n  <!-- #ifdef MP-WEIXIN -->\n  <page-meta \n    :page-style=\"selectedHonor ? 'overflow: hidden;' : ''\"\n    :scroll-top=\"selectedHonor ? scrollTop : 0\"\n  ></page-meta>\n  <!-- #endif -->\n  \n  <scroll-view \n    class=\"honor-gallery\" \n    :class=\"{ 'modal-open': selectedHonor }\"\n    :scroll-y=\"!selectedHonor\"\n    :scroll-top=\"scrollTop\"\n    :enable-back-to-top=\"!selectedHonor\"\n    :scroll-with-animation=\"true\"\n    :enable-passive=\"false\"\n  >\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"18\" color=\"#FFFFFF\"></uni-icons>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"title\">荣誉展厅</text>\n          <text class=\"subtitle\">表彰风采 · 荣耀时刻</text>\n        </view>\n        <view class=\"header-stats\">\n          <text class=\"stat-text\">{{ currentMonth }}月</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 月份选择器 -->\n    <view class=\"month-selector\">\n      <view class=\"selector-content\">\n        <view class=\"month-btn\" @click=\"prevTimePeriod\">\n          <uni-icons type=\"left\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n        </view>\n        <view class=\"current-month-wrapper\">\n          <uni-icons type=\"calendar-filled\" size=\"18\" color=\"#3a86ff\"></uni-icons>\n          <text class=\"current-month\">{{ currentTimeDisplay }}</text>\n        </view>\n        <view class=\"month-btn\" @click=\"nextTimePeriod\">\n          <uni-icons type=\"right\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n        </view>\n      </view>\n    </view>\n\n    <!-- 快速操作按钮 -->\n    <!-- #ifdef H5 -->\n    <view class=\"quick-actions\">\n      <view class=\"action-btn\" :class=\"{ active: currentView === 'grid' }\" @click=\"switchView('grid')\">\n        <view class=\"action-icon awards-icon\">\n          <uni-icons type=\"star-filled\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n        </view>\n        <text class=\"action-text\">宫格</text>\n      </view>\n      <view class=\"action-btn\" :class=\"{ active: currentView === 'list' }\" @click=\"switchView('list')\">\n        <view class=\"action-icon list-icon\">\n          <uni-icons type=\"list\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n        </view>\n        <text class=\"action-text\">列表</text>\n      </view>\n      <view class=\"action-btn\" :class=\"{ active: currentView === 'ranking' }\" @click=\"switchView('ranking')\">\n        <view class=\"action-icon ranking-icon\">\n          <uni-icons type=\"medal-filled\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n        </view>\n        <text class=\"action-text\">排行</text>\n      </view>\n      <!-- 管理员按钮 -->\n      <view v-if=\"isAdmin\" class=\"action-btn\" @click=\"goToAdmin\">\n        <view class=\"action-icon admin-icon\">\n          <uni-icons type=\"gear\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n        </view>\n        <text class=\"action-text\">管理</text>\n      </view>\n    </view>\n    <!-- #endif -->\n    \n    <!-- #ifdef MP-WEIXIN -->\n    <view class=\"quick-actions-mobile\">\n      <scroll-view class=\"actions-scroll\" scroll-x=\"true\" :show-scrollbar=\"false\">\n        <view class=\"actions-container\">\n          <view class=\"action-btn\" :class=\"{ active: currentView === 'grid' }\" @click=\"switchView('grid')\">\n            <view class=\"action-icon awards-icon\">\n              <uni-icons type=\"star-filled\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n            <text class=\"action-text\">宫格</text>\n          </view>\n          <view class=\"action-btn\" :class=\"{ active: currentView === 'list' }\" @click=\"switchView('list')\">\n            <view class=\"action-icon list-icon\">\n              <uni-icons type=\"list\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n            <text class=\"action-text\">列表</text>\n          </view>\n          <view class=\"action-btn\" :class=\"{ active: currentView === 'ranking' }\" @click=\"switchView('ranking')\">\n            <view class=\"action-icon ranking-icon\">\n              <uni-icons type=\"medal-filled\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n            <text class=\"action-text\">排行</text>\n          </view>\n          <!-- 管理员按钮 -->\n          <view v-if=\"isAdmin\" class=\"action-btn admin-btn\" @click=\"goToAdmin\">\n            <view class=\"action-icon admin-icon\">\n              <uni-icons type=\"gear\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n            <text class=\"action-text\">管理</text>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n    <!-- #endif -->\n\n    <!-- 宫格展示 -->\n    <view v-if=\"currentView === 'grid'\" class=\"honor-carousel\">\n      <!-- 背景装饰 -->\n      <view class=\"grid-background\">\n        <view class=\"bg-decoration decoration-1\"></view>\n        <view class=\"bg-decoration decoration-2\"></view>\n        <view class=\"bg-decoration decoration-3\"></view>\n      </view>\n      \n      <!-- 当前页面的宫格 -->\n      <view \n        class=\"grid-container\"\n        @touchstart=\"onTouchStart\"\n        @touchmove=\"onTouchMove\"\n        @touchend=\"onTouchEnd\"\n        @touchcancel=\"onTouchCancel\"\n      >\n        <!-- 有数据时显示批次标题 -->\n        <view class=\"batch-header\" v-if=\"currentPageData && !loading\">\n          <view class=\"batch-icon\">\n            <uni-icons type=\"calendar\" size=\"18\" color=\"#3a86ff\"></uni-icons>\n          </view>\n          <view class=\"batch-info\">\n            <text class=\"batch-title\">{{ currentPageData.batch.name }}</text>\n          </view>\n          <view class=\"batch-count\">\n            <text class=\"count-text\">{{ currentPageData.honors.length }}人获奖</text>\n          </view>\n        </view>\n        \n        <!-- 无数据时显示月份信息和空状态 -->\n        <template v-if=\"!currentPageData && !loading\">\n          <!-- 空数据时的月份标题 -->\n          <view class=\"empty-month-header\">\n            <view class=\"empty-month-icon\">\n              <uni-icons type=\"calendar\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n            </view>\n            <view class=\"empty-month-info\">\n              <text class=\"empty-month-title\">{{ currentMonth }}月表彰记录</text>\n              <text class=\"empty-month-subtitle\">暂无表彰数据</text>\n            </view>\n          </view>\n        </template>\n\n        <view class=\"honors-grid\">\n          <!-- 宫格加载状态 - 在网格内居中显示 -->\n          <template v-if=\"loading\">\n            <view class=\"grid-loading-container\">\n              <uni-load-more status=\"loading\" :content-text=\"{ contentdown: '下拉刷新', contentrefresh: '加载中...', contentnomore: '没有更多' }\"></uni-load-more>\n            </view>\n          </template>\n          \n          <!-- 有数据时显示宫格 -->\n          <template v-else-if=\"currentPageData && currentPageData.honors.length > 0\">\n            <view \n              v-for=\"(honor, index) in currentPageData.honors\" \n              :key=\"honor.id\"\n              class=\"grid-item\"\n              @click=\"selectHonor(honor)\"\n            >\n              <view class=\"honor-card\" :class=\"{ 'featured-card': honor.isFeatured }\">\n                <!-- 用户点赞状态指示器 -->\n                <view v-if=\"honor.isLiked\" class=\"liked-indicator\">\n                  <uni-icons type=\"heart-filled\" size=\"12\" color=\"#ff6b6b\"></uni-icons>\n                </view>\n                \n                <!-- 用户头像区域 -->\n                <view class=\"avatar-section\">\n                  <view class=\"avatar-container\">\n                    <image \n                      v-if=\"honor.userAvatar\"\n                      :src=\"honor.userAvatar\" \n                      class=\"avatar\"\n                      mode=\"aspectFill\"\n                    />\n                    <view v-else class=\"default-avatar\">\n                      <uni-icons type=\"person-filled\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n                    </view>\n                    <!-- 荣誉徽章 - 分离背景和图标 -->\n                    <view class=\"award-badge-bg\" :class=\"{ 'featured-award': honor.isFeatured }\"></view>\n                    <view class=\"award-badge-icon\">\n                      <uni-icons :type=\"honor.isFeatured ? 'medal-filled' : 'medal'\" size=\"12\" color=\"#FFFFFF\"></uni-icons>\n                    </view>\n                  </view>\n                </view>\n                \n                <!-- 卡片内容 -->\n                <view class=\"card-content\">\n                  <text class=\"user-name\">{{ honor.userName }}</text>\n                  <text class=\"award-title\">{{ honor.honorType.name }}</text>\n                </view>\n              </view>\n            </view>\n          </template>\n          \n          <!-- 宫格区域加载状态已在上方统一处理 -->\n          \n          <!-- 无数据时显示空状态 -->\n          <template v-else-if=\"!currentPageData && !loading\">\n            <view class=\"grid-empty-container\">\n              <p-empty-state \n                type=\"data\"\n                text=\"该月份暂无表彰数据\"\n                size=\"small\"\n                text-color=\"#8a94a6\"\n                :container-style=\"{ padding: '60rpx 40rpx' }\"\n              />\n            </view>\n          </template>\n        </view>\n        \n        <!-- 批次更多信息提示 -->\n        <view v-if=\"currentPageData && currentPageData.hasMore && !loading\" class=\"batch-more-info\">\n          <uni-icons type=\"info\" size=\"14\" color=\"#8a94a6\"></uni-icons>\n          <text class=\"more-text\">本批次还有更多表彰，请在列表视图查看完整内容</text>\n        </view>\n      </view>\n      \n      <!-- 分页控制 -->\n      <view class=\"pagination-controls\" v-if=\"totalPages > 1 && gridPagedData.length > 0\">\n        <view class=\"page-info\">\n          <text class=\"page-text\">{{ currentPage }}/{{ totalPages }}</text>\n        </view>\n        <view class=\"page-buttons\">\n          <view \n            class=\"page-btn prev-btn\" \n            @click=\"prevPage\"\n            @touchstart=\"onBtnTouchStart\"\n            @touchend=\"onBtnTouchEnd\"\n          >\n            <uni-icons type=\"left\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n          </view>\n          <view \n            class=\"page-btn next-btn\" \n            @click=\"nextPage\"\n            @touchstart=\"onBtnTouchStart\"\n            @touchend=\"onBtnTouchEnd\"\n          >\n            <uni-icons type=\"right\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 页面指示器 -->\n      <view class=\"page-dots\" v-if=\"totalPages > 1 && gridPagedData.length > 0\">\n        <view \n          v-for=\"(item, index) in totalPages\" \n          :key=\"index\"\n          class=\"dot\"\n          :class=\"{ active: currentPage === (index + 1) }\"\n          @click=\"goToPage(index + 1)\"\n        ></view>\n      </view>\n    </view>\n\n    <!-- 列表视图 -->\n    <view v-if=\"currentView === 'list'\" class=\"honor-list\">\n      <!-- 列表筛选器 -->\n      <view class=\"list-filters\">\n        <view class=\"filter-tabs\">\n          <view \n            class=\"filter-tab\"\n            :class=\"{ active: listViewMode === 'all' }\"\n            @click=\"setListViewMode('all')\"\n          >\n            <uni-icons type=\"list\" size=\"14\" :color=\"listViewMode === 'all' ? '#FFFFFF' : '#8a94a6'\"></uni-icons>\n            <text class=\"tab-text\">全部记录</text>\n          </view>\n          <view \n            class=\"filter-tab\"\n            :class=\"{ active: listViewMode === 'grouped' }\"\n            @click=\"setListViewMode('grouped')\"\n          >\n            <uni-icons type=\"person-filled\" size=\"14\" :color=\"listViewMode === 'grouped' ? '#FFFFFF' : '#8a94a6'\"></uni-icons>\n            <text class=\"tab-text\">按人分组</text>\n          </view>\n        </view>\n        <!-- 获奖人数统计 -->\n        <view class=\"list-stats-summary\">\n          <view class=\"stats-item\">\n            <uni-icons type=\"medal-filled\" size=\"14\" color=\"#3a86ff\"></uni-icons>\n            <text class=\"stats-text\">{{ displayListHonors.length }}次获奖</text>\n          </view>\n          <view class=\"stats-item\" v-if=\"listViewMode === 'grouped'\">\n            <uni-icons type=\"person-filled\" size=\"14\" color=\"#3a86ff\"></uni-icons>\n            <text class=\"stats-text\">{{ groupedListHonors.length }}人</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 列表加载状态 - 使用简单loading -->\n      <template v-if=\"loading\">\n        <view class=\"loading-container\">\n          <uni-load-more status=\"loading\" :content-text=\"{ contentdown: '下拉刷新', contentrefresh: '加载中...', contentnomore: '没有更多' }\"></uni-load-more>\n        </view>\n      </template>\n      \n      <!-- 有数据时显示列表 -->\n      <template v-else-if=\"displayListHonors.length > 0\">\n        <!-- 全部记录模式 -->\n        <template v-if=\"listViewMode === 'all'\">\n        <view \n            v-for=\"honor in displayListHonors\" \n          :key=\"honor.id\"\n          class=\"list-item\"\n          @click=\"selectHonor(honor)\"\n        >\n          <view class=\"list-avatar\">\n            <image \n              v-if=\"honor.userAvatar\"\n              :src=\"honor.userAvatar\" \n              class=\"avatar-img\"\n              mode=\"aspectFill\"\n            />\n            <view v-else class=\"default-avatar\">\n              <uni-icons type=\"person-filled\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n            <!-- 列表徽章 - 分离背景和图标 -->\n            <view class=\"list-badge-bg\" :class=\"{ 'featured-award': honor.isFeatured }\"></view>\n            <view class=\"list-badge-icon\">\n              <uni-icons :type=\"honor.isFeatured ? 'medal-filled' : 'medal'\" size=\"12\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n          </view>\n          <view class=\"list-content\">\n            <view class=\"list-header\">\n              <text class=\"list-name\">{{ honor.userName }}</text>\n              <text class=\"list-batch\">{{ honor.batch.weekLabel }}</text>\n            </view>\n            <text class=\"list-title\">{{ honor.honorType.name }}</text>\n            <text class=\"list-reason\">{{ honor.reason }}</text>\n          </view>\n          <view class=\"list-stats\">\n            <view class=\"stat-item\">\n              <uni-icons type=\"eye\" size=\"14\" color=\"#8a94a6\"></uni-icons>\n              <text class=\"stat-num\">{{ honor.viewCount || 0 }}</text>\n            </view>\n            <view class=\"stat-item\" :class=\"{ 'stat-liked': honor.isLiked }\">\n              <uni-icons \n                :type=\"honor.isLiked ? 'heart-filled' : 'heart'\" \n                size=\"14\" \n                :color=\"honor.isLiked ? '#ff6b6b' : '#8a94a6'\">\n              </uni-icons>\n              <text class=\"stat-num\" :class=\"{ 'stat-liked-text': honor.isLiked }\">{{ honor.likeCount || 0 }}</text>\n            </view>\n          </view>\n        </view>\n        </template>\n        \n        <!-- 按人分组模式 -->\n        <template v-else>\n          <view \n            v-for=\"userGroup in groupedListHonors\" \n            :key=\"userGroup.userName\"\n            class=\"user-group\"\n          >\n            <!-- 用户组头部 -->\n            <view class=\"user-group-header\" @click=\"toggleUserGroup(userGroup.userName)\">\n              <view class=\"user-info\">\n                <view class=\"user-avatar\">\n                  <image \n                    v-if=\"userGroup.userAvatar\"\n                    :src=\"userGroup.userAvatar\" \n                    class=\"avatar-img\"\n                    mode=\"aspectFill\"\n                  />\n                  <view v-else class=\"default-avatar\">\n                    <uni-icons type=\"person-filled\" size=\"18\" color=\"#FFFFFF\"></uni-icons>\n                  </view>\n                  <!-- 用户总获奖徽章 -->\n                  <view class=\"user-total-badge\" :class=\"{ 'has-featured': userGroup.featuredCount > 0 }\">\n                    <text class=\"badge-count\">{{ userGroup.totalCount }}</text>\n                  </view>\n                </view>\n                <view class=\"user-details\">\n                  <text class=\"user-name\">{{ userGroup.userName }}</text>\n                  <view class=\"user-summary\">\n                    <text class=\"summary-text\">本月获奖{{ userGroup.totalCount }}次</text>\n                    <text v-if=\"userGroup.featuredCount > 0\" class=\"featured-text\">{{ userGroup.featuredCount }}次精选</text>\n                  </view>\n                </view>\n              </view>\n              <view class=\"expand-controls\">\n                <view class=\"stats-summary\">\n                  <view class=\"summary-item\">\n                    <uni-icons type=\"eye\" size=\"12\" color=\"#8a94a6\"></uni-icons>\n                    <text class=\"summary-num\">{{ userGroup.totalViews }}</text>\n                  </view>\n                  <view class=\"summary-item\">\n                    <uni-icons \n                      :type=\"userGroup.hasLiked ? 'heart-filled' : 'heart'\" \n                      size=\"12\" \n                      :color=\"userGroup.hasLiked ? '#ff6b6b' : '#8a94a6'\">\n                    </uni-icons>\n                    <text class=\"summary-num\" :class=\"{ 'stat-liked-text': userGroup.hasLiked }\">{{ userGroup.totalLikes }}</text>\n                  </view>\n                </view>\n                <view class=\"expand-icon\" :class=\"{ expanded: userGroup.expanded }\">\n                  <uni-icons type=\"down\" size=\"16\" color=\"#8a94a6\"></uni-icons>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 用户奖项列表（可展开收起） -->\n            <view v-if=\"userGroup.expanded\" class=\"user-honors\">\n              <view \n                v-for=\"honor in userGroup.honors\" \n                :key=\"honor.id\"\n                class=\"group-honor-item\"\n                @click=\"selectHonor(honor)\"\n              >\n                <view class=\"honor-indicator\">\n                  <view class=\"indicator-dot\" :class=\"{ 'featured-dot': honor.isFeatured }\"></view>\n                </view>\n                <view class=\"honor-content\">\n                  <view class=\"honor-header\">\n                    <text class=\"honor-title\">{{ honor.honorType.name }}</text>\n                    <text class=\"honor-batch\">{{ honor.batch.weekLabel }}</text>\n                  </view>\n                  <text class=\"honor-reason\">{{ honor.reason }}</text>\n                  <view class=\"honor-stats\">\n                    <view class=\"stat-item\">\n                      <uni-icons type=\"eye\" size=\"12\" color=\"#8a94a6\"></uni-icons>\n                      <text class=\"stat-num\">{{ honor.viewCount || 0 }}</text>\n                    </view>\n                    <view class=\"stat-item\" :class=\"{ 'stat-liked': honor.isLiked }\">\n                      <uni-icons \n                        :type=\"honor.isLiked ? 'heart-filled' : 'heart'\" \n                        size=\"12\" \n                        :color=\"honor.isLiked ? '#ff6b6b' : '#8a94a6'\">\n                      </uni-icons>\n                      <text class=\"stat-num\" :class=\"{ 'stat-liked-text': honor.isLiked }\">{{ honor.likeCount || 0 }}</text>\n                    </view>\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </template>\n      </template>\n      \n      <!-- 无数据时显示空状态 -->\n      <template v-else>\n        <view class=\"list-empty-container\">\n          <p-empty-state \n            type=\"data\"\n            text=\"暂无表彰数据\"\n            size=\"small\"\n            text-color=\"#8a94a6\"\n            :container-style=\"{ padding: '60rpx 40rpx' }\"\n          />\n        </view>\n      </template>\n    </view>\n\n    <!-- 排行榜视图 -->\n    <view v-if=\"currentView === 'ranking'\" class=\"ranking-view\">\n      <!-- 排行榜筛选器 - 固定在顶部 -->\n      <view class=\"ranking-filters-fixed\">\n        <view class=\"filter-section\">\n          <text class=\"filter-title\">时间范围</text>\n          <view class=\"filter-tabs\">\n            <view \n              v-for=\"period in rankingPeriods\" \n              :key=\"period.value\"\n              class=\"filter-tab\"\n              :class=\"{ active: currentRankingPeriod === period.value }\"\n              @click=\"setRankingPeriod(period.value)\"\n            >\n              <uni-icons :type=\"period.icon\" size=\"14\" :color=\"currentRankingPeriod === period.value ? '#FFFFFF' : '#8a94a6'\"></uni-icons>\n              <text class=\"tab-text\">{{ period.label }}</text>\n            </view>\n          </view>\n        </view>\n        <view class=\"filter-section\">\n          <text class=\"filter-title\">排行指标</text>\n          <view class=\"filter-tabs\">\n            <view \n              v-for=\"metric in rankingMetrics\" \n              :key=\"metric.value\"\n              class=\"filter-tab\"\n              :class=\"{ active: currentRankingMetric === metric.value }\"\n              @click=\"setRankingMetric(metric.value)\"\n            >\n              <uni-icons :type=\"metric.icon\" size=\"14\" :color=\"currentRankingMetric === metric.value ? '#FFFFFF' : '#8a94a6'\"></uni-icons>\n              <text class=\"tab-text\">{{ metric.label }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 排行榜内容区域 - 独立滚动 -->\n      <view class=\"ranking-content-area\">\n        <!-- 排行榜加载状态 - 使用简单loading -->\n        <template v-if=\"loading\">\n          <view class=\"loading-container\">\n            <uni-load-more status=\"loading\" :content-text=\"{ contentdown: '下拉刷新', contentrefresh: '加载中...', contentnomore: '没有更多' }\"></uni-load-more>\n          </view>\n        </template>\n        \n        <!-- 有数据时显示排行榜 -->\n        <template v-else-if=\"sortedHonorsWithRank.length > 0\">\n          <view \n            v-for=\"(honor, index) in sortedHonorsWithRank\" \n            :key=\"honor._rankKey\"\n            class=\"ranking-item\"\n            :class=\"honor.rankClass\"\n            @click=\"selectHonor(honor)\"\n          >\n            <view class=\"rank-number\">\n              <text class=\"rank-text\">{{ index + 1 }}</text>\n            </view>\n            <view class=\"rank-avatar\">\n              <image \n                v-if=\"honor.userAvatar\"\n                :src=\"honor.userAvatar\" \n                class=\"rank-avatar-img\"\n                mode=\"aspectFill\"\n                :lazy-load=\"true\"\n              />\n              <view v-else class=\"default-avatar\">\n                <uni-icons type=\"person-filled\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n              </view>\n            </view>\n            <view class=\"rank-info\">\n              <text class=\"rank-name\">{{ honor.userName }}</text>\n              <view class=\"rank-details\">\n                <view class=\"rank-stats\">\n                  <text class=\"stat-text\">{{ honor.totalAwards }}次获奖</text>\n                  <text v-if=\"honor.totalFeatured > 0\" class=\"featured-text\">{{ honor.totalFeatured }}次精选</text>\n                </view>\n              </view>\n            </view>\n            <view class=\"rank-score\">\n              <text class=\"score-num\">{{ honor.displayScore }}</text>\n              <text class=\"score-label\">{{ honor.scoreLabel }}</text>\n            </view>\n          </view>\n        </template>\n        \n        <!-- 无数据时显示空状态 -->\n        <template v-else>\n          <view class=\"ranking-empty-container\">\n            <p-empty-state \n              type=\"data\"\n              text=\"暂无排行数据\"\n              size=\"small\"\n              text-color=\"#8a94a6\"\n              :container-style=\"{ padding: '60rpx 40rpx' }\"\n            />\n          </view>\n        </template>\n      </view>\n    </view>\n\n    <!-- 统计区域 - 只在宫格页面显示 -->\n    <view v-if=\"currentView === 'grid'\" class=\"statistics-section\">\n      <view class=\"section-header\">\n        <view class=\"section-icon\">\n          <uni-icons type=\"bars\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n        </view>\n        <text class=\"section-title\">本月表彰统计</text>\n        <text class=\"period-info\">{{ currentMonth }}月 · 共{{ displayHonors.length }}次获奖</text>\n      </view>\n      \n      <!-- 加载时显示loading -->\n      <view v-if=\"loading\" class=\"stats-loading\">\n        <uni-load-more status=\"loading\" :content-text=\"{ contentdown: '下拉刷新', contentrefresh: '加载中...', contentnomore: '没有更多' }\"></uni-load-more>\n      </view>\n      \n      <!-- 有数据时显示统计 -->\n      <view v-else-if=\"displayHonors.length > 0\" class=\"stats-grid\">\n        <view class=\"stat-item stat-awards\">\n          <view class=\"stat-icon-wrapper\">\n            <view class=\"stat-icon awards-icon\">\n              <uni-icons type=\"medal-filled\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n          </view>\n          <text class=\"stat-number\">{{ featuredCount }}</text>\n          <text class=\"stat-label\">精选表彰</text>\n        </view>\n        <view class=\"stat-item stat-views\">\n          <view class=\"stat-icon-wrapper\">\n            <view class=\"stat-icon views-icon\">\n              <uni-icons type=\"eye-filled\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n          </view>\n          <text class=\"stat-number\">{{ totalViews }}</text>\n          <text class=\"stat-label\">总浏览量</text>\n        </view>\n        <view class=\"stat-item stat-likes\">\n          <view class=\"stat-icon-wrapper\">\n            <view class=\"stat-icon likes-icon\">\n              <uni-icons type=\"heart-filled\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n          </view>\n          <text class=\"stat-number\">{{ totalLikes }}</text>\n          <text class=\"stat-label\">总点赞数</text>\n        </view>\n      </view>\n      \n      <!-- 无数据时显示占位 -->\n      <view v-else class=\"stats-empty\">\n        <text class=\"empty-text\">暂无统计数据</text>\n      </view>\n    </view>\n\n    <!-- 底部留白区域 - 只在宫格视图显示，确保统计区域不会贴着屏幕底部 -->\n    <view v-if=\"currentView === 'grid'\" class=\"bottom-spacer\"></view>\n  </scroll-view>\n\n  <!-- 自定义Toast - 最高层级 -->\n  <view v-if=\"customToast.show\" class=\"custom-toast\" :class=\"'toast-' + customToast.type\">\n    <view class=\"toast-content\">\n      <view class=\"toast-icon\">\n        <uni-icons \n          :type=\"customToast.type === 'success' ? 'checkmarkempty' : customToast.type === 'error' ? 'closeempty' : 'info'\"\n          size=\"16\" \n          color=\"#FFFFFF\">\n        </uni-icons>\n      </view>\n      <text class=\"toast-message\">{{ customToast.message }}</text>\n    </view>\n  </view>\n\n  <!-- 详情弹窗 -->\n  <view v-if=\"selectedHonor\" class=\"detail-modal\" @click=\"closeDetail\">\n    <view class=\"modal-backdrop\" :class=\"{ 'featured-backdrop': selectedHonor.isFeatured }\"></view>\n    <view class=\"modal-content\" :class=\"{ \n      'featured-modal': selectedHonor.isFeatured, \n      'low-performance': selectedHonor.isFeatured && devicePerformance === 'low' \n    }\" @click.stop=\"\">\n      <!-- 精选表彰特殊装饰 - 根据设备性能显示不同效果 -->\n      <view v-if=\"selectedHonor.isFeatured\" class=\"featured-decoration\" :class=\"{ 'low-performance': devicePerformance === 'low' }\">\n        <view class=\"featured-crown\">👑</view>\n        <!-- 低性能设备减少闪光元素 -->\n        <view v-if=\"devicePerformance !== 'low'\" class=\"featured-sparkles\">\n          <text class=\"sparkle sparkle-1\">✨</text>\n          <text class=\"sparkle sparkle-2\">⭐</text>\n          <text class=\"sparkle sparkle-3\">✨</text>\n          <text class=\"sparkle sparkle-4\">⭐</text>\n        </view>\n        <!-- 低性能设备使用简化闪光效果 -->\n        <view v-else-if=\"devicePerformance === 'low'\" class=\"featured-sparkles-simple\">\n          <text class=\"sparkle sparkle-1\">✨</text>\n          <text class=\"sparkle sparkle-2\">⭐</text>\n        </view>\n        <view class=\"featured-ribbon\">\n          <text class=\"ribbon-text\">精选表彰</text>\n        </view>\n      </view>\n      \n      <!-- 弹窗装饰背景 -->\n      <view class=\"modal-decoration\">\n        <view class=\"decoration-shape shape-1\"></view>\n        <view class=\"decoration-shape shape-2\"></view>\n        <view class=\"decoration-shape shape-3\"></view>\n      </view>\n      \n      <!-- 顶部关闭按钮 -->\n      <view class=\"modal-close\" @click=\"closeDetail\">\n        <view class=\"close-btn\">\n          <uni-icons type=\"closeempty\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n        </view>\n      </view>\n      \n      <!-- 用户头像区域 -->\n      <view class=\"modal-avatar-section\" :class=\"{ 'featured-avatar-section': selectedHonor.isFeatured }\">\n        <view class=\"avatar-wrapper\" :class=\"{ 'featured-avatar-wrapper': selectedHonor.isFeatured }\">\n          <view class=\"avatar-ring\" :class=\"{ 'featured-avatar-ring': selectedHonor.isFeatured }\"></view>\n          <image \n            :src=\"selectedHonor.userAvatar || '/static/user/default-avatar.png'\" \n            class=\"modal-avatar\"\n            mode=\"aspectFill\"\n          />\n          <view class=\"avatar-badge\" :class=\"{ 'featured-avatar-badge': selectedHonor.isFeatured }\">\n            <uni-icons :type=\"selectedHonor.isFeatured ? 'medal-filled' : 'medal'\" size=\"14\" color=\"#FFFFFF\"></uni-icons>\n          </view>\n        </view>\n        <text class=\"modal-username\" :class=\"{ 'featured-username': selectedHonor.isFeatured }\">{{ selectedHonor.userName }}</text>\n        <view class=\"honor-type-chip\" :class=\"{ 'featured-honor-chip': selectedHonor.isFeatured }\">\n          <view class=\"star-icon-wrapper\">\n            <uni-icons type=\"star-filled\" size=\"12\" color=\"#FFFFFF\"></uni-icons>\n          </view>\n          <text class=\"chip-text\">{{ selectedHonor.honorType.name }}</text>\n        </view>\n      </view>\n      \n      <!-- 表彰内容 -->\n      <view class=\"modal-content-section\">\n        <view class=\"content-header\">\n          <view class=\"header-icon\">\n            <uni-icons type=\"chat-filled\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n          </view>\n          <text class=\"header-title\">表彰详情</text>\n        </view>\n        <view class=\"content-body\">\n          <text class=\"honor-description\">{{ selectedHonor.reason }}</text>\n        </view>\n      </view>\n      \n      <!-- 表彰图片展示 -->\n      <view v-if=\"selectedHonor.images && selectedHonor.images.length > 0\" class=\"modal-images-section\">\n        <view class=\"images-header\">\n          <view class=\"header-icon\">\n            <uni-icons type=\"image-filled\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n          </view>\n          <text class=\"header-title\">表彰风采</text>\n          <view class=\"images-count\">\n            <text class=\"count-text\">{{ selectedHonor.images.length }}张</text>\n          </view>\n        </view>\n        <view class=\"images-container\">\n          <!-- 单张图片 -->\n          <view \n            v-if=\"selectedHonor.images.length === 1\" \n            class=\"single-image-wrapper\"\n            @click=\"previewImage(selectedHonor.images[0].url, selectedHonor.images)\"\n          >\n            <image \n              :src=\"selectedHonor.images[0].url\" \n              class=\"single-image\"\n              mode=\"aspectFill\"\n              @error=\"onImageError\"\n            />\n            <view class=\"image-overlay\">\n              <view class=\"overlay-icon\">\n                <uni-icons type=\"eye\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 多张图片网格 -->\n          <view v-else class=\"images-grid\" :class=\"imageGridClass\">\n            <view \n              v-for=\"(image, index) in selectedHonor.images.slice(0, 5)\" \n              :key=\"index\"\n              class=\"grid-image-item\"\n              @click=\"previewImage(image.url, selectedHonor.images)\"\n            >\n              <image \n                :src=\"image.url\" \n                class=\"grid-image\"\n                mode=\"aspectFill\"\n                @error=\"onImageError\"\n              />\n              <view class=\"image-overlay\">\n                <view class=\"overlay-icon\">\n                  <uni-icons type=\"eye\" size=\"14\" color=\"#FFFFFF\"></uni-icons>\n                </view>\n              </view>\n              <!-- 超过5张时显示更多提示 -->\n              <view v-if=\"index === 4 && selectedHonor.images.length > 5\" class=\"more-images-overlay\">\n                <text class=\"more-text\">+{{ selectedHonor.images.length - 5 }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 批次信息 -->\n      <view class=\"modal-batch-section\">\n        <view class=\"batch-item\">\n          <uni-icons type=\"calendar-filled\" size=\"14\" color=\"#8a94a6\"></uni-icons>\n          <text class=\"batch-text\">{{ selectedHonor.batch.name }}</text>\n        </view>\n      </view>\n      \n      <!-- 互动数据 -->\n      <view class=\"modal-stats-section\">\n        <view class=\"stats-container\">\n          <view class=\"stat-box\">\n            <view class=\"stat-icon-wrapper views\">\n              <uni-icons type=\"eye-filled\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n            <text class=\"stat-value\">{{ selectedHonor.viewCount || 0 }}</text>\n            <text class=\"stat-name\">浏览</text>\n          </view>\n          <view class=\"stat-divider\"></view>\n          <view class=\"stat-box\">\n            <view class=\"stat-icon-wrapper likes\">\n              <uni-icons type=\"heart-filled\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n            </view>\n            <text class=\"stat-value\">{{ selectedHonor.likeCount || 0 }}</text>\n            <text class=\"stat-name\">点赞</text>\n          </view>\n        </view>\n      </view>\n      \n              <!-- 操作按钮 -->\n        <view class=\"modal-actions\">\n          <view class=\"actions-row\">\n            <!-- 点赞统计显示 -->\n            <view class=\"like-stats\">\n              <uni-icons type=\"heart-filled\" size=\"16\" color=\"#ff6b6b\"></uni-icons>\n              <view class=\"stats-text\">\n                <text class=\"like-label\">我的点赞</text>\n                <text class=\"like-count\">{{ selectedHonor.userLikeCount || 0 }}</text>\n              </view>\n            </view>\n            \n            <!-- 点赞操作按钮 -->\n            <view class=\"like-actions\">\n              <view \n                class=\"action-button like-button\" \n                :class=\"{ 'disabled': !userLikeStats || userLikeStats.remainingLikes <= 0 || likeProcessing[selectedHonor.id] }\"\n                @click=\"addLikeToHonor(selectedHonor)\">\n                <view class=\"button-icon\">\n                  <uni-icons type=\"plus\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n                </view>\n                <text class=\"button-text\">点赞</text>\n              </view>\n              \n              <view \n                class=\"action-button cancel-button\" \n                :class=\"{ 'disabled': !selectedHonor.userLikeCount || selectedHonor.userLikeCount <= 0 || likeProcessing[selectedHonor.id] }\"\n                @click=\"removeLikeFromHonor(selectedHonor)\">\n                <view class=\"button-icon\">\n                  <uni-icons type=\"minus\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n                </view>\n                <text class=\"button-text\">取消</text>\n              </view>\n            </view>\n          </view>\n        </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue'\n\nexport default {\n  name: 'HonorGallery',\n  components: {\n    PEmptyState\n  },\n  data() {\n    return {\n      loading: true,\n      currentView: 'grid',\n      selectedHonor: null,\n      currentMonth: new Date().getMonth() + 1,\n      currentYear: new Date().getFullYear(),\n      scrollTop: 0,\n      refreshing: false,\n      currentPage: 1,\n      pageSize: 9,\n      autoPlayTimer: null,\n      autoPlayInterval: 8000,\n      honors: [],\n      allHonorsData: [],\n      lastUserId: '', // 添加用户ID追踪\n      pendingHonorId: null,\n      \n      // 设备性能等级：'low', 'medium', 'high'\n      devicePerformance: 'medium',\n      \n      // 手势相关\n      touchStartX: 0,\n      touchStartY: 0,\n      touchStartTime: 0,\n      touchMoved: false,\n      minSwipeDistance: 50,\n      maxSwipeTime: 500,\n      swipeThreshold: 0.3,\n      \n      // 防抖相关\n      debounceTimers: {},\n      isProcessing: false,\n      \n      // 排行榜筛选\n      currentRankingPeriod: 'month',\n      currentRankingMetric: 'likes',\n      \n      // 智能时间选择器\n      currentWeek: 1, // 当前选择的周数\n      currentQuarter: 1, // 当前选择的季度\n      currentWeekOfMonth: 1, // 当前月份的第几周（1-4周）\n      \n      // 列表视图模式\n      listViewMode: 'all', // 'all' 或 'grouped'\n      expandedUsers: {}, // 记录展开状态的用户\n      \n      // 排行榜时间范围选项\n      rankingPeriods: [\n        { value: 'week', label: '本周', icon: 'checkbox-filled' },\n        { value: 'month', label: '本月', icon: 'calendar-filled' },\n        { value: 'quarter', label: '本季度', icon: 'bars' },\n        { value: 'year', label: '本年度', icon: 'medal-filled' }\n      ],\n      \n      // 排行榜指标选项\n      rankingMetrics: [\n        { value: 'likes', label: '点赞', icon: 'heart-filled' },\n        { value: 'views', label: '浏览', icon: 'eye-filled' },\n        { value: 'awards', label: '获奖', icon: 'medal-filled' },\n        { value: 'featured', label: '精选', icon: 'fire-filled' },\n        { value: 'comprehensive', label: '综合', icon: 'star-filled' }\n      ],\n\n      // 缓存相关\n      honorsCache: {},\n      rankingCache: {}, // 排行榜数据缓存，按时间范围分别缓存\n      lastCacheTime: 0,\n      cacheExpireTime: 5 * 60 * 1000, // 缓存过期时间：5分钟\n      maxCacheSize: 10, // 最大缓存条目数（控制内存占用）\n      maxCacheItemSize: 100, // 单个缓存项最大记录数\n      \n      // 用户点赞统计\n      userLikeStats: null,\n      \n      // 点赞操作状态\n      likeProcessing: {}, // 记录正在处理的点赞操作 { honorId: true }\n      \n      // 自定义toast\n      customToast: {\n        show: false,\n        message: '',\n        type: 'success', // success, error, warning\n        timer: null\n      },\n    }\n  },\n  \n  computed: {\n    ...mapGetters({\n      userInfo: 'user/userInfo'\n    }),\n    \n    // 判断是否为管理员 - 使用uniIDHasRole方法\n    isAdmin() {\n      return this.uniIDHasRole('admin') ||\n             this.uniIDHasRole('supervisor') ||\n             this.uniIDHasRole('PM') ||\n             this.uniIDHasRole('GM') ||\n             this.uniIDHasRole('reviser')\n    },\n    \n    // 显示的荣誉数据\n    displayHonors() {\n      const currentUserId = this.userInfo?._id || '';\n      const cacheKey = `${currentUserId}_${this.currentYear}-${this.currentMonth}`;\n      const now = Date.now();\n      \n      // 检查缓存是否有效\n      if (this.honorsCache[cacheKey] && \n          (now - this.lastCacheTime) < this.cacheExpireTime) {\n        return this.honorsCache[cacheKey].data;\n      }\n      \n      // 过滤当前月份的数据\n      const filteredHonors = this.allHonorsData.filter(honor => {\n        // 检查用户ID是否匹配\n        if (honor._userId !== currentUserId) {\n          return false;\n        }\n        \n        const honorDate = new Date(honor.createTime);\n        const honorYear = honorDate.getFullYear();\n        const honorMonth = honorDate.getMonth() + 1;\n        \n        return honorYear === this.currentYear && honorMonth === this.currentMonth;\n      });\n      \n      // 更新缓存\n      this.honorsCache[cacheKey] = {\n        data: filteredHonors,\n        timestamp: now\n      };\n      this.lastCacheTime = now;\n      \n      // 执行缓存管理\n      this.manageCacheSize();\n      \n      return filteredHonors;\n    },\n    \n    // 当前月份的荣誉数据\n    getCurrentMonthHonors() {\n      return this.displayHonors || [];\n    },\n    \n    // 按批次分组的荣誉数据\n    honorsByBatch() {\n      if (!this.displayHonors) return [];\n      \n      const grouped = {};\n      this.displayHonors.forEach(honor => {\n        const batchKey = honor.batch.name || honor.batch.id;\n        if (!grouped[batchKey]) {\n          grouped[batchKey] = {\n            batch: honor.batch,\n            honors: []\n          };\n        }\n        grouped[batchKey].honors.push(honor);\n      });\n      \n      return Object.values(grouped).sort((a, b) => {\n        const dateA = new Date(a.batch.meetingDate || '1970-01-01');\n        const dateB = new Date(b.batch.meetingDate || '1970-01-01');\n        \n        if (dateB.getTime() !== dateA.getTime()) {\n          return dateB - dateA;\n        }\n        \n        return (a.batch.name || '').localeCompare(b.batch.name || '');\n      });\n    },\n    \n    // 宫格视图分页数据\n    gridPagedData() {\n      const batches = this.honorsByBatch\n      const totalPages = Math.ceil(batches.length / 1)\n      const pages = []\n      \n      for (let i = 0; i < totalPages; i++) {\n        const batch = batches[i]\n        if (batch) {\n          pages.push({\n            pageIndex: i,\n            batch: batch.batch,\n            honors: batch.honors.slice(0, 9),\n            hasMore: batch.honors.length > 9\n          })\n        }\n      }\n      \n      return pages\n    },\n    \n    // 统计数据\n    featuredCount() {\n      return this.getCurrentMonthHonors.filter(honor => honor.isFeatured).length\n    },\n    \n    totalViews() {\n      const currentMonthData = this.getCurrentMonthHonors\n      const total = currentMonthData.reduce((sum, honor) => {\n        const viewCount = typeof honor.viewCount === 'number' && honor.viewCount >= 0 ? honor.viewCount : 0\n        return sum + viewCount\n      }, 0)\n      return total\n    },\n    \n    totalLikes() {\n      return this.getCurrentMonthHonors.reduce((sum, honor) => sum + (honor.likeCount || 0), 0)\n    },\n    \n    // 总页数\n    totalPages() {\n      return this.gridPagedData.length || 1\n    },\n    \n    // 当前页的批次数据\n    currentPageData() {\n      const pageIndex = this.currentPage - 1\n      const pages = this.gridPagedData\n      \n      if (pageIndex >= pages.length) {\n        const fallback = pages.length > 0 ? pages[0] : null\n        return fallback\n      }\n      \n      const result = pages[pageIndex] || null\n      return result\n    },\n    \n    // 排行榜数据\n    sortedHonorsWithRank() {\n      const metric = this.currentRankingMetric\n      const period = this.currentRankingPeriod\n      \n      // 生成排行榜缓存键\n      const cacheKey = `ranking-${period}-${metric}-${this.currentYear}-${this.currentMonth}-${this.currentWeekOfMonth}-${this.currentQuarter}`\n      \n      // 检查缓存是否有效\n      if (this.rankingCache && this.rankingCache[cacheKey]) {\n        const cachedData = this.rankingCache[cacheKey]\n        // 检查数据时效性（5分钟缓存）\n        if (Date.now() - cachedData.timestamp < 5 * 60 * 1000) {\n          return cachedData.data\n        }\n      }\n      \n      const filtered = this.getFilteredHonorsByPeriod()\n      \n      // 按用户聚合所有数据\n      const userAggregatedData = {}\n      \n      filtered.forEach(honor => {\n        if (!userAggregatedData[honor.userName]) {\n          userAggregatedData[honor.userName] = {\n            userName: honor.userName,\n            userAvatar: honor.userAvatar,\n            department: honor.department,\n            // 使用最新的一条记录作为显示基础\n            latestHonor: honor,\n            // 累计统计数据\n            totalAwards: 0,\n            totalFeatured: 0,\n            totalLikes: 0,\n            totalViews: 0,\n            // 荣誉类型统计（显示最常获得的奖项）\n            honorTypes: {}\n          }\n        }\n        \n        const userData = userAggregatedData[honor.userName]\n        \n        // 更新最新记录（用于显示）\n        if (new Date(honor.createTime) > new Date(userData.latestHonor.createTime)) {\n          userData.latestHonor = honor\n        }\n        \n        // 累计统计\n        userData.totalAwards++\n        if (honor.isFeatured) {\n          userData.totalFeatured++\n        }\n        userData.totalLikes += (honor.likeCount || 0)\n        userData.totalViews += (honor.viewCount || 0)\n        \n        // 统计荣誉类型\n        const typeName = honor.honorType.name\n        userData.honorTypes[typeName] = (userData.honorTypes[typeName] || 0) + 1\n      })\n      \n      // 转换为数组并计算分数\n      const userRankingData = Object.values(userAggregatedData).map(userData => {\n        // 找出最常获得的荣誉类型\n        const mostFrequentType = Object.keys(userData.honorTypes).reduce((a, b) => \n          userData.honorTypes[a] > userData.honorTypes[b] ? a : b, \n          Object.keys(userData.honorTypes)[0] || '未知'\n        )\n        \n        return {\n          // 基础显示信息（来自最新记录）\n          id: userData.latestHonor.id,\n          userName: userData.userName,\n          userAvatar: userData.userAvatar,\n          department: userData.department,\n          createTime: userData.latestHonor.createTime,\n          // 显示最常获得的荣誉类型\n          honorType: {\n            id: userData.latestHonor.honorType.id,\n            name: mostFrequentType\n          },\n          reason: userData.latestHonor.reason,\n          batch: userData.latestHonor.batch,\n          images: userData.latestHonor.images,\n          isFeatured: userData.totalFeatured > 0, // 只要有精选就显示为精选用户\n          // 累计统计数据\n          totalAwards: userData.totalAwards,\n          totalFeatured: userData.totalFeatured,\n          totalLikes: userData.totalLikes,\n          totalViews: userData.totalViews,\n          // 为了兼容现有显示逻辑，设置这些字段\n          viewCount: userData.totalViews,\n          likeCount: userData.totalLikes\n        }\n      })\n      \n      // 排序\n      const sorted = userRankingData.sort((a, b) => {\n        const aScore = this.calculateRankingScore(a, metric)\n        const bScore = this.calculateRankingScore(b, metric)\n        \n        if (bScore === aScore) {\n          // 相同分数时的排序规则\n          switch (metric) {\n            case 'likes':\n              if (b.totalViews !== a.totalViews) return b.totalViews - a.totalViews\n              return b.totalAwards - a.totalAwards\n            case 'views':\n              if (b.totalLikes !== a.totalLikes) return b.totalLikes - a.totalLikes\n              return b.totalAwards - a.totalAwards\n            case 'awards':\n              if (b.totalFeatured !== a.totalFeatured) return b.totalFeatured - a.totalFeatured\n              return b.totalLikes - a.totalLikes\n            case 'featured':\n              if (b.totalAwards !== a.totalAwards) return b.totalAwards - a.totalAwards\n              return b.totalLikes - a.totalLikes\n            case 'comprehensive':\n              if (b.totalFeatured !== a.totalFeatured) return b.totalFeatured - a.totalFeatured\n              if (b.totalAwards !== a.totalAwards) return b.totalAwards - a.totalAwards\n              return b.totalLikes - a.totalLikes\n            default:\n              return b.totalLikes - a.totalLikes\n          }\n        }\n        \n        return bScore - aScore\n      })\n      \n      const finalRanking = sorted.map((userData, index) => {\n        const displayScore = this.calculateRankingScore(userData, metric)\n        const scoreLabel = this.getScoreLabel(metric)\n        \n        return {\n          ...userData,\n          rankClass: index === 0 ? 'rank-1' : index === 1 ? 'rank-2' : index === 2 ? 'rank-3' : '',\n          displayScore,\n          scoreLabel,\n          _rankKey: `${metric}-${period}-${index}-${userData.userName}`\n        }\n      })\n      \n      // 缓存结果\n      if (!this.rankingCache) {\n        this.rankingCache = {}\n      }\n      this.rankingCache[cacheKey] = {\n        data: finalRanking,\n        timestamp: Date.now()\n      }\n      \n      // 排行榜数据准备完成\n      \n      return finalRanking\n    },\n\n    // 图片网格样式类\n    imageGridClass() {\n      if (!this.selectedHonor || !this.selectedHonor.images) {\n        return 'grid-single'\n      }\n      \n      const imageCount = this.selectedHonor.images.length\n      \n      if (imageCount === 1) return 'grid-single'\n      if (imageCount === 2) return 'grid-double'\n      if (imageCount === 3) return 'grid-triple'\n      if (imageCount === 4) return 'grid-quad'\n      if (imageCount >= 5) return 'grid-five'\n      return 'grid-single'\n    },\n\n    // 列表视图显示的数据\n    displayListHonors() {\n      return this.getCurrentMonthHonors\n    },\n\n    // 按用户分组的列表数据\n    groupedListHonors() {\n      const grouped = {}\n      \n      // 按用户名分组\n      this.getCurrentMonthHonors.forEach(honor => {\n        if (!grouped[honor.userName]) {\n          grouped[honor.userName] = {\n            userName: honor.userName,\n            userAvatar: honor.userAvatar,\n            honors: [],\n            totalCount: 0,\n            featuredCount: 0,\n            totalViews: 0,\n            totalLikes: 0,\n            expanded: this.expandedUsers[honor.userName] || false,\n            hasLiked: false // 初始化为false\n          }\n        }\n        \n        grouped[honor.userName].honors.push(honor)\n        grouped[honor.userName].totalCount++\n        if (honor.isFeatured) {\n          grouped[honor.userName].featuredCount++\n        }\n        grouped[honor.userName].totalViews += (honor.viewCount || 0)\n        grouped[honor.userName].totalLikes += (honor.likeCount || 0)\n        \n        // 如果有任何一条荣誉被点赞，设置hasLiked为true\n        if (honor.isLiked) {\n          grouped[honor.userName].hasLiked = true\n        }\n      })\n      \n      // 转换为数组并排序（按总获奖次数降序，然后按精选次数降序）\n      return Object.values(grouped).sort((a, b) => {\n        if (b.featuredCount !== a.featuredCount) {\n          return b.featuredCount - a.featuredCount // 精选次数优先\n        }\n        if (b.totalCount !== a.totalCount) {\n          return b.totalCount - a.totalCount // 总获奖次数其次\n        }\n        return b.totalLikes - a.totalLikes // 最后按点赞数\n      }).map(group => {\n        // 对每个用户的奖项按时间倒序排列\n        group.honors.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))\n        return group\n      })\n    },\n\n    // 智能时间显示 - 根据排行榜筛选维度动态显示\n    currentTimeDisplay() {\n      if (this.currentView === 'ranking') {\n        switch (this.currentRankingPeriod) {\n          case 'week':\n            return `${this.currentYear}年${this.currentMonth}月第${this.currentWeekOfMonth}周`\n          case 'month':\n            return `${this.currentYear}年${this.currentMonth}月`\n          case 'quarter':\n            return `${this.currentYear}年第${this.currentQuarter}季度`\n          case 'year':\n            return `${this.currentYear}年`\n          default:\n            return `${this.currentYear}年${this.currentMonth}月`\n        }\n      }\n      // 非排行榜视图显示传统的年月格式\n      return `${this.currentYear}年${this.currentMonth}月`\n    },\n\n\n  },\n\n  watch: {\n    userInfo: {\n      immediate: true,\n      handler(newUserInfo) {\n        const newUserId = newUserInfo?._id || '';\n        if (this.lastUserId !== newUserId) {\n          this.lastUserId = newUserId;\n          this.honors = [];\n          this.allHonorsData = [];\n          this.honorsCache = {};\n          this.loadAllHonorData();\n        }\n      }\n    }\n  },\n\n  async onLoad(options) {\n    try {\n      this.loading = true\n      \n      // 并行初始化基础数据，减少等待时间\n      const initPromises = [\n        this.initUserInfo(),\n        this.loadAllHonorData() // 与用户信息并行加载\n      ]\n      \n      // 同步执行不耗时的操作\n      this.initTimeData()\n      this.detectDevicePerformance()\n      \n      // 等待并行任务完成\n      await Promise.all(initPromises)\n      \n      // 如果有传入的honorId，在allHonorsData中查找\n      if (options.honorId) {\n        const honor = this.allHonorsData.find(h => h.id === options.honorId) || this.honors.find(h => h.id === options.honorId)\n        if (honor) {\n          this.selectedHonor = honor\n          this.incrementViewCount(honor.id)\n        }\n      }\n      \n      // 优化后的展厅初始化（已移除内部延迟）\n      await this.initGallery()\n      \n      // 强制触发displayHonors重新计算\n      this.$forceUpdate()      \n     \n      // 异步清理存储空间（不阻塞页面显示）\n      setTimeout(() => {\n        this.clearStorageSpace()\n      }, 2000)\n    } catch (error) {\n      this.showCustomToast('页面加载失败', 'error')\n      this.loading = false\n    }\n  },\n\n  onShow() {\n    this.initUserInfo()\n    \n    if (this.currentView === 'grid' && this.totalPages > 1) {\n      this.startAutoPlay()\n    }\n  },\n\n  onHide() {\n    this.stopAutoPlay()\n  },\n\n  beforeDestroy() {\n    this.stopAutoPlay()\n    \n    Object.keys(this.debounceTimers).forEach(key => {\n      if (typeof this.debounceTimers[key] === 'number') {\n        clearTimeout(this.debounceTimers[key])\n      }\n    })\n    this.debounceTimers = {}\n    \n    // 清理自定义toast定时器\n    if (this.customToast.timer) {\n      clearTimeout(this.customToast.timer)\n    }\n  },\n\n  methods: {\n    // 初始化用户信息\n    async initUserInfo() {\n      try {\n        const token = uni.getStorageSync('uni_id_token')\n        const cachedUserInfo = uni.getStorageSync('uni-id-pages-userInfo')\n        \n        if (cachedUserInfo && token) {\n          try {\n            const userInfo = typeof cachedUserInfo === 'string' ? JSON.parse(cachedUserInfo) : cachedUserInfo\n            \n            // 确保用户信息包含必要的ID字段\n            if (userInfo._id || userInfo.uid) {\n              // 获取角色信息\n          try {\n            const { getCacheKey, CACHE_KEYS } = await import('@/utils/cache.js')\n            const userRoleStr = uni.getStorageSync(getCacheKey(CACHE_KEYS.USER_ROLE))\n            if (userRoleStr) {\n              const userRole = typeof userRoleStr === 'string' ? JSON.parse(userRoleStr) : userRoleStr\n              if (userRole && userRole.value && userRole.value.userRole) {\n                userInfo.role = userRole.value.userRole\n              }\n            }\n          } catch (error) {\n            // 获取用户角色信息失败，使用默认权限\n          }\n              \n              // 设置用户ID和token\n              userInfo.uid = userInfo._id || userInfo.uid\n              userInfo.token = token\n          \n          this.$store.dispatch('user/loginSuccess', {\n            userInfo,\n            token,\n            permissions: userInfo.permissions || []\n          })\n              \n              return\n            } else {\n                          // 用户信息缺少ID字段，删除无效缓存\n            uni.removeStorageSync('uni-id-pages-userInfo')\n          }\n        } catch (e) {\n          // 解析本地存储的用户信息失败，忽略错误\n            uni.removeStorageSync('uni-id-pages-userInfo')\n          }\n        }\n        \n        // 如果本地用户信息无效，尝试获取当前用户信息\n        // 这里可以调用 uniCloud 获取用户信息，但对于荣誉展厅，用户应该已经在其他地方登录过了\n        // 所以如果走到这里，说明用户状态有问题\n        const fallbackUserInfo = uni.getStorageSync('uni_id_user_info')\n        if (fallbackUserInfo && token) {\n          const userInfo = typeof fallbackUserInfo === 'string' ? JSON.parse(fallbackUserInfo) : fallbackUserInfo\n          userInfo.uid = userInfo._id || userInfo.uid\n          userInfo.token = token\n          \n          if (userInfo.uid) {\n            this.$store.dispatch('user/loginSuccess', {\n              userInfo,\n              token,\n              permissions: userInfo.permissions || []\n            })\n            return\n          }\n        }\n      } catch (error) {\n        // 初始化用户信息失败，忽略错误\n      }\n    },\n\n    // 返回上一页\n    goBack() {\n      uni.navigateBack({\n        delta: 1,\n        fail: () => {\n          uni.switchTab({\n            url: '/pages/ucenter/ucenter'\n          })\n        }\n      })\n    },\n\n    // 跳转到管理员页面\n    goToAdmin() {\n      if (!this.isAdmin) {\n        this.showCustomToast('无管理权限', 'error')\n        return\n      }\n      \n      uni.navigateTo({\n        url: '/pages/honor_pkg/admin/index'\n      })\n    },\n\n    // 防抖工具方法\n    debounce(key, fn, delay = 300) {\n      if (this.debounceTimers[key]) {\n        clearTimeout(this.debounceTimers[key])\n      }\n      \n      this.debounceTimers[key] = setTimeout(() => {\n        fn.call(this)\n        delete this.debounceTimers[key]\n      }, delay)\n    },\n\n    debounceImmediate(key, fn, delay = 100) {\n      // 为滚动相关的操作使用更小的延迟\n      const scrollKeys = ['rankingPeriod', 'rankingMetric', 'listViewMode']\n      const optimizedDelay = scrollKeys.includes(key) ? 30 : delay\n      \n      if (this.debounceTimers[key]) {\n        clearTimeout(this.debounceTimers[key])\n      }\n      \n      // 立即执行一次，然后设置延迟\n      if (scrollKeys.includes(key)) {\n        fn()\n        return\n      }\n      \n      this.debounceTimers[key] = setTimeout(() => {\n        fn()\n        delete this.debounceTimers[key]\n      }, optimizedDelay)\n    },\n\n    // 初始化展厅\n    async initGallery() {\n      try {\n        \n        // 如果allHonorsData已经加载，则无需重复加载当前月份数据\n        // 如果allHonorsData为空，则加载当前月份数据作为备用\n        if (this.allHonorsData.length === 0) {\n          await this.loadHonorData()\n        }\n        \n        // 立即隐藏loading，避免骨架屏显示过久\n        this.loading = false\n        \n        // 异步加载用户点赞统计（不阻塞页面显示）\n        if (this.userInfo?.uid || this.userInfo?._id) {\n          setTimeout(() => {\n            this.loadUserLikeStats()\n          }, 1000)\n        }\n      } catch (error) {\n        // 展厅初始化失败\n        this.showCustomToast('加载失败', 'error')\n        this.loading = false\n      } finally {\n        // 使用immediate模式启动自动轮播\n        if (this.currentView === 'grid' && this.totalPages > 1) {\n          this.startAutoPlay()\n        }\n      }\n    },\n\n    // 加载完整荣誉数据（用于排行榜）\n    async loadAllHonorData() {\n      try {\n        const currentUserId = this.userInfo?._id || '';\n        \n        const requestData = {\n          action: 'getHonorList',\n          data: {\n            page: 1,\n            size: 500,\n            status: 'published'\n          },\n          uniIdToken: this.userInfo?.token || uni.getStorageSync('uni_id_token')\n        };\n        \n        const result = await uniCloud.callFunction({\n          name: 'honor-gallery',\n          data: requestData\n        });\n        \n        if (result.result.code === 0) {\n          // 确保用户ID没有改变\n          if (currentUserId === (this.userInfo?._id || '')) {\n            const rawData = result.result.data.list || [];\n            this.allHonorsData = this.convertToPageFormat(rawData);\n            \n            // 清除缓存\n            this.honorsCache = {};\n            this.clearRankingCache();\n          }\n        }\n      } catch (error) {\n        console.error('加载荣誉数据失败:', error);\n        this.allHonorsData = [];\n      }\n    },\n\n    // 加载荣誉数据\n    async loadHonorData() {\n      try {\n        const requestData = {\n            action: 'getHonorList',\n            data: {\n              year: this.currentYear,\n              month: this.currentMonth,\n              page: 1,\n              size: 100,\n              status: 'published'\n            },\n            uniIdToken: this.userInfo?.token || uni.getStorageSync('uni_id_token') // 传递用户token\n          }\n        \n        const result = await uniCloud.callFunction({\n          name: 'honor-gallery',\n          data: requestData\n        })\n        \n        if (result.result.code === 0) {\n          const rawData = result.result.data.list || []\n          this.honors = this.convertToPageFormat(rawData)\n          \n          if (this.pendingHonorId) {\n            const honor = this.honors.find(h => h.id === this.pendingHonorId)\n            if (honor) {\n              this.selectedHonor = honor\n              this.incrementViewCount(honor.id)\n            }\n            this.pendingHonorId = null\n          }\n        } else {\n          // 云函数返回错误\n          throw new Error(result.result.message || '数据加载失败')\n        }\n      } catch (error) {\n        // 荣誉数据加载失败\n        this.honors = []\n      }\n    },\n\n    // 计算周次标签（优化版本）\n    calculateWeekLabel(batchName, meetingDate) {\n      // 首先尝试从批次名称中提取\n      if (batchName) {\n        const weekMatch = batchName.match(/第(\\d+)周/);\n        if (weekMatch) {\n          return `第${weekMatch[1]}周`;\n        }\n      }\n      \n      // 如果没有找到，尝试从日期计算（使用优化后的简单算法）\n      if (meetingDate) {\n        try {\n          const date = new Date(meetingDate.replace(/\\//g, '-'));\n          if (!isNaN(date.getTime())) {\n            const year = date.getFullYear();\n            const month = date.getMonth() + 1;\n            const day = date.getDate();\n            \n            // 使用简单的周次计算：每7天为一周\n            const week = Math.ceil(day / 7);\n            return `第${Math.min(week, 4)}周`; // 最多4周\n          }\n        } catch (e) {\n          // 计算周次失败，使用默认值\n        }\n      }\n      \n      return '第0周'; // 默认值\n    },\n\n    // 将云数据库数据转换为页面所需格式\n    convertToPageFormat(cloudData) {\n      if (!Array.isArray(cloudData)) {\n        return []\n      }\n      \n      const currentUserId = this.userInfo?._id || '';\n      \n      return cloudData.map(item => {\n        const viewCount = typeof item.viewCount === 'number' ? item.viewCount : 0;\n        \n        return {\n          id: item._id,\n          userId: item.userId,\n          userName: item.userName,\n          userAvatar: item.userAvatar || '/static/user/default-avatar.png',\n          department: item.department,\n          honorType: {\n            id: item.honorTypeId,\n            name: item.honorType?.name || '未知类型'\n          },\n          reason: item.reason,\n          images: item.images || [],\n          createTime: item.createTime,\n          batch: {\n            id: item.batchId,\n            name: item.batch?.name || '未知批次',\n            type: item.batch?.type || 'weekly',\n            period: item.batch?.period || '',\n            meetingDate: item.batch?.meetingDate || '',\n            weekLabel: item.batch?.weekLabel || this.calculateWeekLabel(item.batch?.name, item.batch?.meetingDate)\n          },\n          viewCount: viewCount,\n          likeCount: item.likeCount || 0,\n          userLikeCount: item.userLikeCount || 0,\n          todayLikeCount: item.todayLikeCount || 0,\n          isFeatured: item.isFeatured || false,\n          isLiked: item.isLiked || false,\n          // 添加用户标识\n          _userId: currentUserId\n        }\n      })\n    },\n\n    // 选择荣誉查看详情\n    selectHonor(honor) {\n      // 检查honor参数是否有效\n      if (!honor || !honor.id) {\n        console.warn('selectHonor: honor参数无效', honor)\n        return\n      }\n      \n      // 使用nextTick确保DOM更新流畅\n      this.$nextTick(() => {\n        this.selectedHonor = honor\n        \n        // 禁用页面滚动\n        this.disableScroll()\n        \n        // 异步增加浏览量，不阻塞弹窗显示\n        setTimeout(() => {\n          this.incrementViewCount(honor.id)\n        }, 100)\n      })\n    },\n\n    // 关闭详情\n    closeDetail() {\n      this.selectedHonor = null\n      // 恢复页面滚动\n      this.enableScroll()\n    },\n\n    // 禁止页面滚动\n    disableScroll() {\n      // #ifdef H5\n      document.body.style.overflow = 'hidden'\n      document.documentElement.style.overflow = 'hidden'\n      // #endif\n    },\n\n    // 恢复页面滚动\n    enableScroll() {\n      // #ifdef H5\n      document.body.style.overflow = ''\n      document.documentElement.style.overflow = ''\n      // #endif\n    },\n\n    // 切换视图模式\n    switchView(mode) {\n      if (this.currentView === mode) return\n      \n      this.debounceImmediate('switchView', () => {\n        // 显示短暂加载状态提升体验\n        this.loading = true\n        this.currentView = mode\n        this.selectedHonor = null\n        \n        // 切换到排行榜时，同步时间数据\n        if (mode === 'ranking') {\n          this.syncTimeDataForPeriod(this.currentRankingPeriod)\n        }\n        \n        setTimeout(() => {\n          this.loading = false\n        \n        if (mode === 'grid') {\n          this.$nextTick(() => {\n            if (this.currentPage > this.totalPages) {\n              this.currentPage = 1\n            }\n            if (this.totalPages > 1) {\n              this.startAutoPlay()\n            }\n          })\n        } else {\n          this.stopAutoPlay()\n        }\n        }, 200)\n      }, 250)\n    },\n\n    // 自动轮播相关方法\n    startAutoPlay() {\n      this.stopAutoPlay()\n      if (this.totalPages <= 1) return\n      \n      this.autoPlayTimer = setInterval(() => {\n        this.nextPage()\n      }, this.autoPlayInterval)\n    },\n\n    stopAutoPlay() {\n      if (this.autoPlayTimer) {\n        clearInterval(this.autoPlayTimer)\n        this.autoPlayTimer = null\n      }\n    },\n\n    // 分页相关方法\n    prevPage() {\n      this.debounceImmediate('prevPage', () => {\n        if (this.totalPages <= 1) return\n        \n        if (this.currentPage > 1) {\n          this.currentPage--\n        } else {\n          this.currentPage = this.totalPages\n        }\n        this.resetAutoPlay()\n      }, 200)\n    },\n\n    nextPage() {\n      this.debounceImmediate('nextPage', () => {\n        if (this.totalPages <= 1) return\n        \n        if (this.currentPage < this.totalPages) {\n          this.currentPage++\n        } else {\n          this.currentPage = 1\n        }\n        this.resetAutoPlay()\n      }, 200)\n    },\n\n    goToPage(page) {\n      this.debounceImmediate('goToPage', () => {\n        if (page >= 1 && page <= this.totalPages) {\n          this.currentPage = page\n          this.resetAutoPlay()\n        }\n      }, 300)\n    },\n\n    resetAutoPlay() {\n      if (this.currentView === 'grid' && this.totalPages > 1) {\n        this.startAutoPlay()\n      }\n    },\n\n    // 按钮触摸事件\n    onBtnTouchStart(e) {\n      e.stopPropagation()\n    },\n\n    onBtnTouchEnd(e) {\n      e.stopPropagation()\n    },\n\n    // 工具方法\n    sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms))\n    },\n\n    // 图片预览\n    previewImage(currentUrl, imageList) {\n      const urls = imageList.map(img => img.url)\n      \n      this.selectedHonor = null\n      \n      setTimeout(() => {\n        uni.previewImage({\n          urls: urls,\n          current: currentUrl,\n          indicator: 'number',\n          loop: true,\n          fail: (err) => {\n            // 图片预览失败\n          }\n        })\n      }, 100)\n    },\n\n    // 图片加载错误处理\n    onImageError(e) {\n      // 图片加载失败\n    },\n\n    // 月份相关方法\n    async loadMonthData() {\n      try {\n        // 显示骨架屏\n        this.loading = true\n        this.honors = []\n        this.currentPage = 1\n        \n        await this.loadHonorData()\n        \n        this.stopAutoPlay()\n        this.$nextTick(() => {\n          if (this.currentView === 'grid' && this.totalPages > 1) {\n            this.startAutoPlay()\n          }\n        })\n      } catch (error) {\n        // 加载月份数据失败\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 给荣誉增加1个点赞\n    async addLikeToHonor(honor) {\n      const likeKey = `addLike_${honor.id}`\n      \n      this.debounceImmediate(likeKey, async () => {\n        try {\n          // 检查用户登录状态\n          const userId = this.userInfo?.uid || this.userInfo?._id\n          if (!this.userInfo || !userId) {\n            uni.showModal({\n              title: '用户信息异常',\n              content: '用户登录状态异常\\n请退出页面重新进入',\n              showCancel: false,\n              confirmText: '知道了',\n              confirmColor: '#3a86ff'\n            })\n            return\n          }\n          \n          // 检查剩余次数\n          if (!this.userLikeStats || this.userLikeStats.remainingLikes <= 0) {\n            uni.showModal({\n              title: '点赞次数已用完',\n              content: '今日点赞次数已用完\\n每天最多可点赞3次\\n明天可继续点赞',\n              showCancel: false,\n              confirmText: '知道了',\n              confirmColor: '#3a86ff'\n            })\n            return\n          }\n          \n          // 保存原始值用于回滚\n          const originalLikeCount = honor.likeCount || 0\n          const originalUserLikeCount = honor.userLikeCount || 0\n          \n          // 设置处理状态，禁用按钮避免重复操作\n          this.$set(this.likeProcessing, honor.id, true)\n          \n          // 乐观更新UI\n          honor.likeCount = originalLikeCount + 1\n          honor.userLikeCount = originalUserLikeCount + 1\n          honor.isLiked = true\n          \n          const result = await uniCloud.callFunction({\n            name: 'honor-gallery',\n            data: {\n              action: 'likeHonor',\n              data: {\n                honorId: honor.id\n              },\n              uniIdToken: this.userInfo?.token || uni.getStorageSync('uni_id_token')\n            }\n          })\n          \n          if (result.result.code === 0) {\n            const { likeCount, userLikeCount, todayLikeCount, todayUsedLikes, remainingLikes } = result.result.data || {}\n            \n            // 使用服务器返回的准确数据\n            honor.likeCount = likeCount || honor.likeCount\n            honor.userLikeCount = userLikeCount || honor.userLikeCount\n            honor.todayLikeCount = todayLikeCount || 0\n            \n            // 同步更新所有位置的数据\n            this.updateHonorInAllArrays(honor.id, {\n              likeCount: honor.likeCount,\n              userLikeCount: honor.userLikeCount,\n              todayLikeCount: honor.todayLikeCount,\n              isLiked: honor.userLikeCount > 0\n            })\n            \n            // 更新用户统计\n            if (this.userLikeStats) {\n              this.userLikeStats.todayUsedLikes = todayUsedLikes\n              this.userLikeStats.remainingLikes = remainingLikes\n              this.userLikeStats.canLike = remainingLikes > 0\n            }\n            \n            // 先清除处理状态，确保按钮立即可用\n            this.$delete(this.likeProcessing, honor.id)\n            \n            // 使用nextTick确保DOM更新完成后再清除缓存\n            this.$nextTick(() => {\n              this.clearAllCaches()\n            })\n            \n            // 显示成功提示，保持弹窗打开\n            this.showCustomToast(`点赞成功！剩余${remainingLikes}次`, 'success')\n            \n          } else {\n            throw new Error(result.result.message || '点赞失败')\n          }\n        } catch (error) {\n          // 点赞失败\n          \n          // 回滚UI状态\n          honor.likeCount = originalLikeCount\n          honor.userLikeCount = originalUserLikeCount\n          honor.isLiked = originalUserLikeCount > 0\n          \n          // 清除处理状态\n          this.$delete(this.likeProcessing, honor.id)\n          \n          // 显示错误提示，保持弹窗打开\n          if (error.message && error.message.includes('今日点赞次数已用完')) {\n            this.showCustomToast('今日点赞已用完', 'error')\n          } else {\n            this.showCustomToast(error.message || '点赞失败', 'error')\n          }\n        }\n      }, 300)\n    },\n\n    // 取消对荣誉的所有点赞\n    async removeLikeFromHonor(honor) {\n      const likeKey = `removeLike_${honor.id}`\n      \n      this.debounceImmediate(likeKey, async () => {\n        try {\n          // 检查用户登录状态\n          const userId = this.userInfo?.uid || this.userInfo?._id\n          if (!this.userInfo || !userId) {\n            uni.showModal({\n              title: '用户信息异常',\n              content: '用户登录状态异常\\n请退出页面重新进入',\n              showCancel: false,\n              confirmText: '知道了',\n              confirmColor: '#3a86ff'\n            })\n            return\n          }\n          \n          // 检查是否有今日点赞可以取消\n          if (!honor.todayLikeCount || honor.todayLikeCount <= 0) {\n            const message = honor.userLikeCount > 0 \n              ? '只能取消今日的点赞'\n              : '还没有点赞记录'\n            \n            // 显示提示，保持弹窗打开\n            this.showCustomToast(message, 'warning')\n            return\n          }\n          \n          // 保存原始值用于回滚\n          const originalLikeCount = honor.likeCount || 0\n          const originalUserLikeCount = honor.userLikeCount || 0\n          \n          // 设置处理状态，禁用按钮避免重复操作\n          this.$set(this.likeProcessing, honor.id, true)\n          \n          const result = await uniCloud.callFunction({\n            name: 'honor-gallery',\n            data: {\n              action: 'unlikeHonor',\n              data: {\n                honorId: honor.id\n              },\n              uniIdToken: this.userInfo?.token || uni.getStorageSync('uni_id_token')\n            }\n          })\n          \n          if (result.result.code === 0) {\n            const { likeCount, userLikeCount, todayLikeCount, canceledCount } = result.result.data || {}\n            \n            // 使用服务器返回的准确数据\n            honor.likeCount = likeCount || honor.likeCount\n            honor.userLikeCount = userLikeCount || 0\n            honor.todayLikeCount = todayLikeCount || 0\n            honor.isLiked = (userLikeCount || 0) > 0\n            \n            // 同步更新所有位置的数据\n            const updateData = {\n              likeCount: honor.likeCount,\n              userLikeCount: honor.userLikeCount,\n              todayLikeCount: honor.todayLikeCount,\n              isLiked: honor.isLiked\n            }\n            \n\n            \n            this.updateHonorInAllArrays(honor.id, updateData)\n            \n            // 更新用户统计（取消点赞会增加剩余次数）\n            if (this.userLikeStats && canceledCount) {\n              this.userLikeStats.todayUsedLikes = Math.max((this.userLikeStats.todayUsedLikes || 0) - 1, 0)\n              this.userLikeStats.remainingLikes = Math.min((this.userLikeStats.remainingLikes || 0) + 1, 3)\n              this.userLikeStats.canLike = this.userLikeStats.remainingLikes > 0\n            }\n            \n            // 先清除处理状态，确保按钮立即可用\n            this.$delete(this.likeProcessing, honor.id)\n            \n            // 使用nextTick确保DOM更新完成后再清除缓存\n            this.$nextTick(() => {\n              this.clearAllCaches()\n            })\n            \n            // 显示成功提示，保持弹窗打开\n            let message = '取消点赞成功'\n            if (todayLikeCount > 0) {\n              message += `，今日还可取消${todayLikeCount}次`\n            }\n            \n            this.showCustomToast(message, 'success')\n            \n          } else {\n            throw new Error(result.result.message || '取消点赞失败')\n          }\n        } catch (error) {\n          // 取消点赞失败\n          \n          // 回滚UI状态\n          honor.likeCount = originalLikeCount\n          honor.userLikeCount = originalUserLikeCount\n          honor.isLiked = originalUserLikeCount > 0\n          \n          // 清除处理状态\n          this.$delete(this.likeProcessing, honor.id)\n          \n          // 显示错误提示，保持弹窗打开\n          if (error.message && error.message.includes('只能取消今日的点赞')) {\n            this.showCustomToast('只能取消今日点赞', 'error')\n          } else {\n            this.showCustomToast(error.message || '取消失败', 'error')\n          }\n        }\n      }, 300)\n    },\n\n    // 统一更新荣誉数据的辅助方法\n    updateHonorInAllArrays(honorId, updateData) {\n      const updateRecord = (record) => {\n        if (record.id === honorId) {\n          Object.assign(record, updateData)\n        }\n      }\n      \n      this.allHonorsData.forEach(updateRecord)\n      this.honors.forEach(updateRecord)\n      \n      // 如果当前选中的荣誉就是这个，也要更新\n      if (this.selectedHonor && this.selectedHonor.id === honorId) {\n        Object.assign(this.selectedHonor, updateData)\n      }\n    },\n\n    // 清除所有缓存的辅助方法\n    clearAllCaches() {\n      this.clearHonorsCache()\n      this.clearRankingCache()\n    },\n\n    // 分享荣誉\n    async shareHonor(honor) {\n      const shareKey = `share_${honor.id}`\n      \n      this.debounceImmediate(shareKey, async () => {\n        try {\n          // 尝试使用系统分享\n          // #ifdef MP-WEIXIN\n          uni.share({\n            provider: 'weixin',\n            scene: 'WXSceneSession',\n            type: 0,\n            href: '',\n            title: `${honor.userName}获得${honor.honorType.name}`,\n            summary: honor.reason || '查看表彰详情',\n            imageUrl: honor.images && honor.images.length > 0 ? honor.images[0].url : '',\n            success: () => {\n              this.showCustomToast('分享成功', 'success')\n            },\n            fail: () => {\n              this.fallbackShare(honor)\n            }\n          })\n          // #endif\n          \n          // #ifdef H5\n          this.fallbackShare(honor)\n          // #endif\n          \n          // #ifdef APP-PLUS\n          plus.share.sendWithSystem({\n            type: 'text',\n            content: `${honor.userName}获得${honor.honorType.name}: ${honor.reason || '查看表彰详情'}`\n          }, () => {\n            this.showCustomToast('分享成功', 'success')\n          }, () => {\n            this.fallbackShare(honor)\n          })\n          // #endif\n          \n        } catch (error) {\n          // 分享失败\n          this.fallbackShare(honor)\n        }\n      }, 300)\n    },\n    \n    // 备用分享方案\n    fallbackShare(honor) {\n      const shareText = `${honor.userName}获得${honor.honorType.name}: ${honor.reason || '查看表彰详情'}`\n      \n      // #ifdef H5\n      if (navigator.share) {\n        navigator.share({\n          title: `${honor.userName}获得${honor.honorType.name}`,\n          text: honor.reason || '查看表彰详情',\n          url: window.location.href\n        }).then(() => {\n          this.showCustomToast('分享成功', 'success')\n        }).catch(() => {\n          this.copyToClipboard(shareText)\n        })\n      } else {\n        this.copyToClipboard(shareText)\n      }\n      // #endif\n      \n      // #ifndef H5\n      this.copyToClipboard(shareText)\n      // #endif\n    },\n    \n    // 复制到剪贴板\n    copyToClipboard(text) {\n      // #ifdef MP-WEIXIN\n      // 微信小程序：使用系统默认提示\n      wx.setClipboardData({\n        data: text,\n        success: () => {\n          // 系统会自动显示\"内容已复制\"提示，无需额外处理\n        },\n        fail: () => {\n          uni.showModal({\n            title: '分享内容',\n            content: text,\n            showCancel: false,\n            confirmText: '知道了'\n          })\n        }\n      })\n      // #endif\n      \n      // #ifdef H5\n      // H5环境：先复制再显示高层级Toast\n      uni.setClipboardData({\n        data: text,\n        showToast: false, // 禁用系统默认的\"内容已复制\"提示\n        success: () => {\n          this.showCustomToast('内容已复制到剪贴板', 'success')\n        },\n        fail: () => {\n          uni.showModal({\n            title: '分享内容',\n            content: text,\n            showCancel: false,\n            confirmText: '知道了'\n          })\n        }\n      })\n      // #endif\n    },\n\n    // 加载用户点赞统计\n    async loadUserLikeStats() {\n      try {\n        const userId = this.userInfo?.uid || this.userInfo?._id\n        if (!userId) return\n                \n        const result = await uniCloud.callFunction({\n          name: 'honor-gallery',\n          data: {\n            action: 'getUserLikeStats',\n            uniIdToken: this.userInfo?.token || uni.getStorageSync('uni_id_token')\n          }\n        })\n        \n        if (result.result.code === 0) {\n          const stats = result.result.data\n\n          \n          // 存储到data中，可以在界面上显示\n          this.userLikeStats = stats\n          \n          // 移除页面进入时的提醒，只在点赞后提醒\n          \n        }\n      } catch (error) {\n        // 加载用户点赞统计失败\n      }\n    },\n\n    // 增加浏览量\n    async incrementViewCount(honorId) {\n      // 先在allHonorsData中查找，再在honors中查找\n      let targetHonor = this.allHonorsData.find(h => h.id === honorId)\n      if (!targetHonor) {\n        targetHonor = this.honors.find(h => h.id === honorId)\n      }\n      \n      if (!targetHonor) {\n        // 未找到目标荣誉\n        return\n      }\n      \n      try {\n        // 乐观更新 - 同时更新allHonorsData和honors中的数据\n        const oldCount = targetHonor.viewCount || 0\n        const newCount = oldCount + 1\n        \n        // 更新allHonorsData中的数据\n        const honorInAllData = this.allHonorsData.find(h => h.id === honorId)\n        if (honorInAllData) {\n          honorInAllData.viewCount = newCount\n        }\n        \n        // 更新honors数组中的数据\n        const honorInArray = this.honors.find(h => h.id === honorId)\n        if (honorInArray) {\n          honorInArray.viewCount = newCount\n        }\n        \n        // 更新selectedHonor中的数据（如果当前正在查看这个荣誉）\n        if (this.selectedHonor && this.selectedHonor.id === honorId) {\n          this.selectedHonor.viewCount = newCount\n        }\n        \n        // 清除相关缓存\n        this.clearHonorsCache()\n        this.clearRankingCache() // 同时清除排行榜缓存\n        \n        // 调用云函数更新数据库\n        const result = await uniCloud.callFunction({\n          name: 'honor-gallery',\n          data: {\n            action: 'incrementViewCount',\n            data: { honorId }\n          }\n        })\n        \n        if (result.result.code === 0) {\n          const serverCount = result.result.data?.viewCount\n          if (typeof serverCount === 'number' && serverCount >= 0) {\n            // 使用服务器返回的准确数据同步更新所有位置\n            if (honorInAllData) {\n              honorInAllData.viewCount = serverCount\n            }\n            if (honorInArray) {\n              honorInArray.viewCount = serverCount\n            }\n            if (this.selectedHonor && this.selectedHonor.id === honorId) {\n              this.selectedHonor.viewCount = serverCount\n            }\n            \n            // 强制重新计算排行榜数据\n            this.$forceUpdate()\n          }\n        } else {\n          // 云函数返回错误\n        }\n      } catch (error) {\n        // 增加浏览量失败\n        \n        // 回滚浏览量 - 需要回滚所有位置的数据\n        const rollbackCount = Math.max(oldCount, 0)\n        if (honorInAllData) {\n          honorInAllData.viewCount = rollbackCount\n        }\n        if (honorInArray) {\n          honorInArray.viewCount = rollbackCount\n        }\n        if (this.selectedHonor && this.selectedHonor.id === honorId) {\n          this.selectedHonor.viewCount = rollbackCount\n        }\n      }\n    },\n\n    // 清除荣誉缓存\n    clearHonorsCache() {\n      this.honorsCache = {}\n      this.lastCacheTime = 0\n    },\n\n    // 清除排行榜缓存\n    clearRankingCache() {\n      this.rankingCache = {}\n    },\n\n    // 智能缓存管理\n    manageCacheSize() {\n      const cacheKeys = Object.keys(this.honorsCache);\n      \n      // 超出最大缓存条目数时，删除最旧的\n      if (cacheKeys.length > this.maxCacheSize) {\n        const sortedKeys = cacheKeys\n          .map(key => ({\n            key,\n            timestamp: this.honorsCache[key].timestamp\n          }))\n          .sort((a, b) => a.timestamp - b.timestamp);\n        \n        const keysToDelete = sortedKeys.slice(0, cacheKeys.length - this.maxCacheSize);\n        keysToDelete.forEach(item => {\n          delete this.honorsCache[item.key];\n        });\n      }\n      \n      // 检查单个缓存项大小，防止内存溢出\n      Object.keys(this.honorsCache).forEach(key => {\n        const cacheItem = this.honorsCache[key];\n        if (cacheItem.data && cacheItem.data.length > this.maxCacheItemSize) {\n          cacheItem.data = cacheItem.data.slice(0, this.maxCacheItemSize);\n        }\n      });\n    },\n\n    // 格式化相关方法\n    formatDate(dateStr) {\n      if (!dateStr) return ''\n      try {\n        const date = new Date(dateStr)\n        return `${date.getMonth() + 1}月${date.getDate()}日`\n      } catch (e) {\n        return dateStr\n      }\n    },\n\n    formatBatchDate(dateStr) {\n      if (!dateStr) return ''\n      try {\n        const date = new Date(dateStr)\n        const month = date.getMonth() + 1\n        const day = date.getDate()\n        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']\n        const weekday = weekdays[date.getDay()]\n        return `${month}月${day}日 ${weekday} 周会`\n      } catch (e) {\n        return dateStr\n      }\n    },\n    \n    // 列表视图相关方法\n    setListViewMode(mode) {\n      if (this.listViewMode === mode) return\n      \n      this.debounceImmediate('listViewMode', () => {\n        this.listViewMode = mode\n        \n        // 如果切换到分组模式，重新初始化所有用户的展开状态\n        if (mode === 'grouped') {\n          // 清空现有状态，确保重新初始化\n          this.expandedUsers = {}\n          \n          // 使用nextTick确保数据更新后再设置展开状态\n          this.$nextTick(() => {\n            this.groupedListHonors.forEach(group => {\n              // 所有用户都默认为收起状态，用户可以手动点击展开\n              // 这样可以确保第一个用户也能正常点击\n              this.$set(this.expandedUsers, group.userName, false)\n            })\n          })\n        }\n      }, 100)\n    },\n\n    toggleUserGroup(userName) {\n      this.debounceImmediate(`toggleUser_${userName}`, () => {\n        // 确保expandedUsers对象存在该用户的属性\n        if (!this.expandedUsers.hasOwnProperty(userName)) {\n          this.$set(this.expandedUsers, userName, false)\n        }\n        // 切换展开状态\n        this.$set(this.expandedUsers, userName, !this.expandedUsers[userName])\n      }, 150) // 减少防抖延迟，提升响应速度\n    },\n    \n    // 排行榜相关方法\n    setRankingPeriod(period) {\n      if (this.currentRankingPeriod === period) return\n      \n      this.debounceImmediate('rankingPeriod', () => {\n        // 显示短暂加载状态\n        this.loading = true\n        \n        // 清空排行榜缓存，确保数据更新\n        this.rankingCache = {}\n        \n        this.currentRankingPeriod = period\n        \n        // 根据新的时间维度同步时间数据\n        this.syncTimeDataForPeriod(period)\n        \n        setTimeout(() => {\n          this.loading = false\n        // #ifdef MP-WEIXIN\n        this.$nextTick(() => {\n          this.$forceUpdate()\n        })\n        // #endif\n        }, 200) // 减少加载时间，提升用户体验\n      }, 50) // 减少防抖延迟\n    },\n\n    // 同步时间数据（基于排行榜期间）\n    syncTimeDataForPeriod(period) {\n      const currentDate = new Date()\n      \n      // 只在首次切换到对应模式时初始化为当前时间，不覆盖用户的选择\n      if (period === 'week') {\n        // 如果当前不在当前周，则初始化为当前周\n        const nowYear = currentDate.getFullYear()\n        const nowMonth = currentDate.getMonth() + 1\n        const nowWeekOfMonth = this.getWeekOfMonth(currentDate)\n        \n        if (this.currentYear !== nowYear || this.currentMonth !== nowMonth) {\n          this.currentYear = nowYear\n          this.currentMonth = nowMonth\n          this.currentWeekOfMonth = nowWeekOfMonth\n        }\n      } else if (period === 'quarter') {\n        // 如果当前不在当前季度，则初始化为当前季度\n        const nowYear = currentDate.getFullYear()\n        const nowQuarter = Math.ceil((currentDate.getMonth() + 1) / 3)\n        \n        if (this.currentYear !== nowYear || this.currentQuarter !== nowQuarter) {\n          this.currentYear = nowYear\n          this.currentQuarter = nowQuarter\n          // 同步更新月份到季度的第一个月\n          this.currentMonth = (nowQuarter - 1) * 3 + 1\n        }\n      } else if (period === 'year') {\n        // 如果当前不在当前年，则初始化为当前年\n        const nowYear = currentDate.getFullYear()\n        \n        if (this.currentYear !== nowYear) {\n          this.currentYear = nowYear\n        }\n      }\n      // month 模式保持原有月份选择器的值不变\n    },\n    \n    setRankingMetric(metric) {\n      if (this.currentRankingMetric === metric) return\n      \n      this.debounceImmediate('rankingMetric', () => {\n        // 显示短暂加载状态\n        this.loading = true\n        this.currentRankingMetric = metric\n        \n        setTimeout(() => {\n          this.loading = false\n        // #ifdef MP-WEIXIN\n        this.$nextTick(() => {\n          this.$forceUpdate()\n        })\n        // #endif\n        }, 150) // 减少加载时间\n      }, 50) // 减少防抖延迟\n    },\n\n    // 根据排行榜时间维度过滤荣誉数据\n    getFilteredHonorsByPeriod() {\n      // 生成缓存键，包含时间范围和具体时间参数\n      const period = this.currentRankingPeriod\n      let cacheKey = `ranking-${period}`\n      \n      const currentYear = new Date().getFullYear()\n      const currentMonth = new Date().getMonth() + 1\n      const selectedYear = this.currentYear\n      const selectedMonth = this.currentMonth\n      \n      // 根据不同时间范围生成不同的缓存键\n      switch (period) {\n        case 'week':\n          const selectedWeekOfMonth = this.currentWeekOfMonth\n          cacheKey += `-${selectedYear}-${selectedMonth}-W${selectedWeekOfMonth}`\n          break\n        case 'month':\n          cacheKey += `-${selectedYear}-${selectedMonth}`\n          break\n        case 'quarter':\n          const selectedQuarter = this.currentQuarter\n          cacheKey += `-${selectedYear}-Q${selectedQuarter}`\n          break\n        case 'year':\n          cacheKey += `-${selectedYear}`\n          break\n        default:\n          cacheKey += `-${selectedYear}-${selectedMonth}`\n      }\n      \n      // 检查缓存\n      if (this.rankingCache && this.rankingCache[cacheKey]) {\n        return this.rankingCache[cacheKey]\n      }\n      \n      // 使用allHonorsData作为数据源，支持跨时间段查询\n      const dataSource = this.allHonorsData.length > 0 ? this.allHonorsData : this.getCurrentMonthHonors\n      \n      const filtered = dataSource.filter(honor => {\n        const honorDate = new Date(honor.createTime)\n        const honorYear = honorDate.getFullYear()\n        const honorMonth = honorDate.getMonth() + 1\n        \n        switch (period) {\n          case 'week':\n            // 本周：使用用户选择的周（通过左右箭头切换）\n            const selectedWeekOfMonth = this.currentWeekOfMonth\n            const honorWeekOfMonth = this.getWeekOfMonth(honorDate)\n            \n            return honorYear === selectedYear && \n                   honorMonth === selectedMonth && \n                   honorWeekOfMonth === selectedWeekOfMonth\n            \n          case 'month':\n            // 本月：跟随月份选择器，可以选择任意月份\n            return honorYear === selectedYear && honorMonth === selectedMonth\n            \n          case 'quarter':\n            // 本季度：使用用户选择的季度（通过左右箭头切换）\n            const selectedQuarter = this.currentQuarter\n            const honorQuarter = Math.ceil(honorMonth / 3)\n            \n            return honorYear === selectedYear && honorQuarter === selectedQuarter\n            \n          case 'year':\n            // 本年度：使用用户选择的年份（通过左右箭头切换）\n            return honorYear === selectedYear\n            \n          default:\n            return honorYear === selectedYear && honorMonth === selectedMonth\n        }\n      })\n      \n      // 缓存结果\n      if (!this.rankingCache) {\n        this.rankingCache = {}\n      }\n      this.rankingCache[cacheKey] = filtered\n      \n      return filtered\n    },\n\n    // 计算排行榜分数（基于聚合数据）\n    calculateRankingScore(userData, metric) {\n      switch (metric) {\n        case 'likes':\n          return userData.totalLikes || 0\n        case 'views':\n          return userData.totalViews || 0\n        case 'awards':\n          return userData.totalAwards || 0\n        case 'featured':\n          return userData.totalFeatured || 0\n        case 'comprehensive':\n          // 综合分数：精选*50 + 获奖*10 + 点赞*1 + 浏览*0.1\n          const featuredScore = (userData.totalFeatured || 0) * 50\n          const awardScore = (userData.totalAwards || 0) * 10\n          const likeScore = (userData.totalLikes || 0) * 1\n          const viewScore = (userData.totalViews || 0) * 0.1\n          \n          return Math.round(featuredScore + awardScore + likeScore + viewScore)\n        default:\n          return userData.totalLikes || 0\n      }\n    },\n\n    // 获取分数标签\n    getScoreLabel(metric = null) {\n      const targetMetric = metric || this.currentRankingMetric\n      switch (targetMetric) {\n        case 'likes':\n          return '点赞数'\n        case 'views':\n          return '浏览数'\n        case 'awards':\n          return '获奖数'\n        case 'featured':\n          return '精选数'\n        case 'comprehensive':\n          return '综合分'\n        default:\n          return '点赞数'\n      }\n    },\n\n    // 获取用户获奖次数\n    getAwardCount(userName) {\n      try {\n        const filtered = this.getFilteredHonorsByPeriod().filter(honor => honor.userName === userName)\n        return filtered.length\n      } catch (error) {\n        // 获取获奖次数失败\n        return 0\n      }\n    },\n\n    // 月份切换（优化版 - 使用缓存避免重新加载）\n    prevMonth() {\n      this.debounce('prevMonth', () => {\n        if (this.currentMonth > 1) {\n          this.currentMonth--\n        } else {\n          this.currentMonth = 12\n          this.currentYear--\n        }\n        // 优化：只有在排行榜视图或者缓存中没有数据时才重新加载\n        this.smartLoadMonthData()\n      }, 400)\n    },\n\n    nextMonth() {\n      this.debounce('nextMonth', () => {\n        if (this.currentMonth < 12) {\n          this.currentMonth++\n        } else {\n          this.currentMonth = 1\n          this.currentYear++\n        }\n        // 优化：只有在排行榜视图或者缓存中没有数据时才重新加载\n        this.smartLoadMonthData()\n      }, 400)\n    },\n\n    // 智能切换月份 - 优先使用缓存和allHonorsData\n    async smartLoadMonthData() {\n      const cacheKey = `${this.currentYear}-${this.currentMonth}`\n      const now = Date.now()\n      \n      try {\n        // 如果是排行榜视图且处于月度模式，需要清空相关缓存\n        if (this.currentView === 'ranking') {\n          if (this.currentRankingPeriod === 'month') {\n            // 清空月度相关的排行榜缓存\n            Object.keys(this.rankingCache).forEach(key => {\n              if (key.includes('-month-')) {\n                delete this.rankingCache[key]\n              }\n            })\n          }\n          return\n        }\n        \n        // 检查缓存\n        if (this.honorsCache[cacheKey] && \n            (now - this.honorsCache[cacheKey].timestamp) < this.cacheExpireTime) {\n          // 缓存有效，瞬间切换\n          this.loading = false\n          return\n        }\n        \n        // 尝试从allHonorsData筛选\n        if (this.allHonorsData.length > 0) {\n          const filtered = this.allHonorsData.filter(honor => {\n            const honorDate = new Date(honor.createTime)\n            return honorDate.getFullYear() === this.currentYear && \n                   honorDate.getMonth() + 1 === this.currentMonth\n          })\n          \n          if (filtered.length > 0) {\n            // 更新缓存\n            this.honorsCache[cacheKey] = {\n              data: filtered,\n              timestamp: now,\n              recordCount: filtered.length\n            }\n            this.loading = false\n            return\n          }\n        }\n        \n        // 需要从服务器加载\n        this.loading = true\n        await this.loadHonorData()\n      } catch (error) {\n        // 智能加载月份数据失败\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 下拉刷新\n    async onPullRefresh() {\n      if (this.isProcessing) return\n      \n      this.debounce('pullRefresh', async () => {\n        this.refreshing = true\n        this.isProcessing = true\n        try {\n          await this.refreshData()\n        } finally {\n          this.refreshing = false\n          this.isProcessing = false\n          uni.stopPullDownRefresh()\n        }\n      }, 500)\n    },\n\n    onRefreshRestore() {\n      this.refreshing = false\n    },\n\n    // 刷新数据\n    async refreshData() {\n      try {\n        // 下拉刷新时不需要额外的loading，scroll-view有自己的刷新指示器\n        if (!this.refreshing) {\n          uni.showLoading({\n            title: '刷新中...'\n          })\n        }\n        \n        // 下拉刷新时需要显示loading状态（宫格中心的转圈）\n        this.loading = true\n        this.honors = []\n        this.allHonorsData = []\n        this.clearHonorsCache()\n        \n        // 并行加载月份数据和完整数据\n        await Promise.all([\n          this.loadHonorData(),\n          this.loadAllHonorData()\n        ])\n        \n        this.loading = false\n        \n        if (!this.refreshing) {\n          this.showCustomToast('刷新成功', 'success')\n        }\n      } catch (error) {\n        // 刷新数据失败\n        this.showCustomToast('刷新失败', 'error')\n      } finally {\n        if (!this.refreshing) {\n          uni.hideLoading()\n        }\n      }\n    },\n\n    // 手势检测方法\n    onTouchStart(e) {\n      if (this.currentView !== 'grid' || this.totalPages <= 1) return\n      \n      this.touchMoved = false\n      this.touchStartTime = Date.now()\n      \n      if (e.touches && e.touches.length > 0) {\n        this.touchStartX = e.touches[0].clientX\n        this.touchStartY = e.touches[0].clientY\n      }\n    },\n\n    onTouchMove(e) {\n      if (this.currentView !== 'grid' || this.totalPages <= 1) return\n      \n      this.touchMoved = true\n      \n      if (e.touches && e.touches.length > 0) {\n        const deltaX = Math.abs(e.touches[0].clientX - this.touchStartX)\n        const deltaY = Math.abs(e.touches[0].clientY - this.touchStartY)\n        \n        if (deltaX > deltaY && deltaX > 10) {\n          e.preventDefault()\n        }\n      }\n    },\n\n    onTouchEnd(e) {\n      if (this.currentView !== 'grid' || this.totalPages <= 1) return\n      \n      const touchEndTime = Date.now()\n      const touchDuration = touchEndTime - this.touchStartTime\n      \n      if (!this.touchMoved || touchDuration > this.maxSwipeTime) {\n        return\n      }\n      \n      if (e.changedTouches && e.changedTouches.length > 0) {\n        const touchEndX = e.changedTouches[0].clientX\n        const touchEndY = e.changedTouches[0].clientY\n        \n        const deltaX = touchEndX - this.touchStartX\n        const deltaY = touchEndY - this.touchStartY\n        \n        const distanceX = Math.abs(deltaX)\n        const distanceY = Math.abs(deltaY)\n        \n        if (distanceX > distanceY && distanceX > this.minSwipeDistance) {\n          this.stopAutoPlay()\n          \n          if (deltaX > 0) {\n            this.prevPage()\n          } else {\n            this.nextPage()\n          }\n          \n          setTimeout(() => {\n            if (this.currentView === 'grid' && this.totalPages > 1) {\n              this.startAutoPlay()\n            }\n          }, 1500)\n        }\n      }\n    },\n\n    onTouchCancel(e) {\n      this.touchMoved = false\n      this.touchStartX = 0\n      this.touchStartY = 0\n      this.touchStartTime = 0\n    },\n\n    // 检测设备性能\n    detectDevicePerformance() {\n      try {\n        const systemInfo = uni.getSystemInfoSync()\n        const totalMemory = systemInfo.memSize || 0\n        const platform = systemInfo.platform || 'unknown'\n        \n        // 根据内存和平台判断设备性能\n        let performanceLevel = 'medium'\n        \n        if (totalMemory > 6000 || platform === 'ios') {\n          performanceLevel = 'high'\n        } else if (totalMemory < 3000) {\n          performanceLevel = 'low'\n        }\n        \n        this.devicePerformance = performanceLevel\n        \n        // 根据性能调整缓存策略\n        switch (performanceLevel) {\n          case 'high':\n            this.maxCacheSize = 15\n            this.maxCacheItemSize = 150\n            break\n          case 'medium':\n            this.maxCacheSize = 10\n            this.maxCacheItemSize = 100\n            break\n          case 'low':\n            this.maxCacheSize = 5\n            this.maxCacheItemSize = 50\n            break\n        }\n      } catch (error) {\n        // 设备性能检测失败\n        // 默认中等性能设置\n        this.devicePerformance = 'medium'\n        this.maxCacheSize = 10\n        this.maxCacheItemSize = 100\n      }\n    },\n\n    // 清理存储空间\n    clearStorageSpace() {\n      try {\n        const storageInfo = uni.getStorageInfoSync()\n        \n        if (storageInfo.currentSize && storageInfo.limitSize) {\n          const usagePercent = (storageInfo.currentSize / storageInfo.limitSize) * 100\n          if (usagePercent > 80) {\n            const cacheKeys = storageInfo.keys.filter(key => \n              key.startsWith('honor_cache_') || \n              key.startsWith('temp_') ||\n              key.includes('qrcode_temp')\n            )\n            \n            cacheKeys.forEach(key => {\n              try {\n                uni.removeStorageSync(key)\n              } catch (e) {\n                // 静默处理清理失败\n              }\n            })\n          }\n        }\n      } catch (error) {\n        // 静默处理错误，不影响页面加载\n      }\n    },\n\n    // 智能时间切换 - 根据当前视图和排行榜筛选维度决定切换方式\n    prevTimePeriod() {\n      this.debounce('prevTimePeriod', () => {\n        if (this.currentView === 'ranking') {\n          switch (this.currentRankingPeriod) {\n            case 'week':\n              this.prevWeek()\n              break\n            case 'month':\n              this.prevMonth()\n              break\n            case 'quarter':\n              this.prevQuarter()\n              break\n            case 'year':\n              this.prevYear()\n              break\n            default:\n              this.prevMonth()\n          }\n        } else {\n          // 非排行榜视图使用传统月份切换\n          this.prevMonth()\n        }\n      }, 400)\n    },\n\n    nextTimePeriod() {\n      this.debounce('nextTimePeriod', () => {\n        if (this.currentView === 'ranking') {\n          switch (this.currentRankingPeriod) {\n            case 'week':\n              this.nextWeek()\n              break\n            case 'month':\n              this.nextMonth()\n              break\n            case 'quarter':\n              this.nextQuarter()\n              break\n            case 'year':\n              this.nextYear()\n              break\n            default:\n              this.nextMonth()\n          }\n        } else {\n          // 非排行榜视图使用传统月份切换\n          this.nextMonth()\n        }\n      }, 400)\n    },\n\n    // 周切换方法\n    prevWeek() {\n      if (this.currentWeekOfMonth > 1) {\n        this.currentWeekOfMonth--\n      } else {\n        // 切换到上个月的第4周\n        this.prevMonth()\n        this.currentWeekOfMonth = 4\n      }\n      this.loadRankingData()\n    },\n\n    nextWeek() {\n      if (this.currentWeekOfMonth < 4) {\n        this.currentWeekOfMonth++\n      } else {\n        // 切换到下个月的第1周\n        this.nextMonth()\n        this.currentWeekOfMonth = 1\n      }\n      this.loadRankingData()\n    },\n\n    // 季度切换方法\n    prevQuarter() {\n      if (this.currentQuarter > 1) {\n        this.currentQuarter--\n      } else {\n        this.currentQuarter = 4\n        this.currentYear--\n      }\n      // 同步更新月份到季度的第一个月\n      this.currentMonth = (this.currentQuarter - 1) * 3 + 1\n      this.loadRankingData()\n    },\n\n    nextQuarter() {\n      if (this.currentQuarter < 4) {\n        this.currentQuarter++\n      } else {\n        this.currentQuarter = 1\n        this.currentYear++\n      }\n      // 同步更新月份到季度的第一个月\n      this.currentMonth = (this.currentQuarter - 1) * 3 + 1\n      this.loadRankingData()\n    },\n\n    // 年份切换方法\n    prevYear() {\n      this.currentYear--\n      this.loadRankingData()\n    },\n\n    nextYear() {\n      this.currentYear++\n      this.loadRankingData()\n    },\n\n    // 月份切换（保留原有逻辑）\n    prevMonth() {\n      if (this.currentMonth > 1) {\n        this.currentMonth--\n      } else {\n        this.currentMonth = 12\n        this.currentYear--\n      }\n      this.refreshCurrentMonthData()\n    },\n\n    nextMonth() {\n      if (this.currentMonth < 12) {\n        this.currentMonth++\n      } else {\n        this.currentMonth = 1\n        this.currentYear++\n      }\n      this.refreshCurrentMonthData()\n    },\n\n    // 刷新当前月份数据\n    refreshCurrentMonthData() {\n      // 清除当前月份的缓存，强制displayHonors重新计算\n      const cacheKey = `${this.currentYear}-${this.currentMonth}`\n      if (this.honorsCache[cacheKey]) {\n        delete this.honorsCache[cacheKey]\n      }\n      \n      // 强制重新计算所有计算属性\n      this.$forceUpdate()\n      \n      // 确保统计数据立即更新\n      this.$nextTick(() => {\n        // 触发统计计算属性的重新计算\n        const temp = this.featuredCount + this.totalViews + this.totalLikes\n        // 用temp变量确保计算被执行，但不做其他操作\n      })\n    },\n\n    // 排行榜数据加载（排行榜专用）\n    async loadRankingData() {\n      try {\n        // 确保有完整数据源\n        if (this.allHonorsData.length === 0) {\n          await this.loadAllHonorData()\n        }\n        \n        // 强制重新计算排行榜\n        this.$forceUpdate()\n      } catch (error) {\n        // 加载排行榜数据失败\n      }\n    },\n\n    // 初始化时间相关数据\n    initTimeData() {\n      const now = new Date()\n      this.currentYear = now.getFullYear()\n      this.currentMonth = now.getMonth() + 1\n      \n      // 计算当前周数（简化版本：一年中的第几周）\n      const startOfYear = new Date(now.getFullYear(), 0, 1)\n      const dayOfYear = Math.floor((now - startOfYear) / (24 * 60 * 60 * 1000)) + 1\n      this.currentWeek = Math.ceil(dayOfYear / 7)\n      \n      // 计算当前月份的第几周（1-4周）\n      this.currentWeekOfMonth = this.getWeekOfMonth(now)\n      \n      // 计算当前季度\n      this.currentQuarter = Math.ceil(this.currentMonth / 3)\n      \n\n    },\n\n    // 计算指定日期在当月的第几周\n    getWeekOfMonth(date) {\n      const firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1)\n      const dayOfMonth = date.getDate()\n      \n      // 计算月初是周几（0=周日，1=周一...）\n      const firstDayWeekday = firstDayOfMonth.getDay()\n      \n      // 计算当前日期是当月第几周\n      // 如果月初不是周一，第一周可能不完整\n      const weekOfMonth = Math.ceil((dayOfMonth + firstDayWeekday) / 7)\n      \n      // 限制在1-4周范围内\n      return Math.min(weekOfMonth, 4)\n    },\n\n\n\n    // 显示自定义toast（高层级，不被弹窗遮挡）\n    showCustomToast(message, type = 'success', duration = 2000) {\n      // 清除之前的定时器\n      if (this.customToast.timer) {\n        clearTimeout(this.customToast.timer)\n      }\n      \n      // 设置toast内容\n      this.customToast.message = message\n      this.customToast.type = type\n      this.customToast.show = true\n      \n      // 自动隐藏\n      this.customToast.timer = setTimeout(() => {\n        this.customToast.show = false\n        this.customToast.timer = null\n      }, duration)\n    },\n\n    // 处理下拉刷新\n    async onPullDownRefresh() {\n      try {\n        // 清除缓存\n        const cacheKey = `${this.currentYear}-${this.currentMonth}`\n        if (this.honorsCache[cacheKey]) {\n          delete this.honorsCache[cacheKey]\n        }\n        \n        // 重新加载数据\n        await this.loadAllHonorData()\n        \n        // 刷新当前视图\n        this.refreshCurrentMonthData()\n        \n        // 显示刷新成功提示\n        this.showCustomToast('刷新成功')\n      } catch (error) {\n        this.showCustomToast('刷新失败', 'error')\n      } finally {\n        // 停止下拉刷新动画\n        uni.stopPullDownRefresh()\n      }\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\">\n@import './gallery.scss';\n/* 整体操作区域 */\n.actions-row {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 255, 0.6));\n  border-radius: 20rpx;\n  padding: 20rpx 24rpx;\n  border: 2rpx solid rgba(58, 134, 255, 0.08);\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n/* 点赞统计显示 */\n.like-stats {\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  flex-shrink: 0;\n}\n\n.stats-text {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0rpx;\n}\n\n.like-label {\n  font-size: 24rpx;\n  color: #6b7280;\n  font-weight: 400;\n  line-height: 1.2;\n}\n\n.like-count {\n  font-size: 32rpx;\n  font-weight: 700;\n  color: #1a1d2e;\n  letter-spacing: 0.5rpx;\n  line-height: 1;\n}\n\n/* 点赞操作按钮 */\n.like-actions {\n  display: flex;\n  gap: 16rpx;\n  flex-shrink: 0;\n}\n\n.like-actions .action-button {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10rpx;\n  padding: 20rpx 24rpx;\n  border-radius: 28rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  backdrop-filter: blur(20rpx);\n}\n\n.like-actions .action-button.like-button {\n  background: #ff6b6b !important;\n  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.25) !important;\n  border: none !important;\n}\n\n.like-actions .action-button.like-button:active {\n  transform: translateY(3rpx) scale(0.96) !important;\n}\n\n.like-actions .action-button.cancel-button {\n  background: #64748b !important;\n  box-shadow: 0 8rpx 24rpx rgba(100, 116, 139, 0.25) !important;\n  border: none !important;\n}\n\n.like-actions .action-button.cancel-button:active {\n  transform: translateY(3rpx) scale(0.96) !important;\n}\n\n.like-actions .action-button.disabled {\n  background: #94a3b8 !important;\n  box-shadow: none !important;\n  opacity: 0.8 !important;\n  cursor: not-allowed !important;\n  transform: none !important;\n}\n\n.like-actions .button-icon {\n  width: 32rpx;\n  height: 32rpx;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.like-actions .button-text {\n  color: #FFFFFF;\n  font-weight: 600;\n  letter-spacing: 0.5rpx;\n}\n\n/* 旧的点赞按钮样式 */\n.like-button.liked {\n  background: linear-gradient(135deg, #ff6b6b, #ff8e8e) !important;\n  box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.3);\n  transform: scale(1.05);\n}\n\n.like-button.liked .button-text {\n  color: #FFFFFF;\n  font-weight: 600;\n}\n\n/* 荣誉卡片点赞状态指示器 */\n.liked-indicator {\n  position: absolute;\n  top: 8rpx;\n  right: 8rpx;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  width: 28rpx;\n  height: 28rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2;\n  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);\n}\n\n/* 自定义Toast样式 - 确保最高层级 */\n.custom-toast {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 99999; /* 比弹窗更高的层级 */\n  animation: toastFadeIn 0.3s ease-out;\n  max-width: 90vw; /* 确保在小屏幕上不会超出屏幕 */\n  padding: 0 20rpx; /* 左右留白 */\n  box-sizing: border-box;\n}\n\n.toast-content {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  padding: 20rpx 28rpx;\n  border-radius: 24rpx;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);\n  backdrop-filter: blur(10rpx);\n  min-width: auto; /* 移除最小宽度限制，完全自适应 */\n  max-width: 100%;\n  white-space: nowrap; /* 防止文字换行 */\n  width: max-content; /* 根据内容自动调整宽度 */\n}\n\n.toast-icon {\n  flex-shrink: 0;\n  width: 32rpx;\n  height: 32rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.toast-message {\n  color: #FFFFFF;\n  font-size: 28rpx;\n  font-weight: 500;\n  line-height: 1.2;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  flex: 1;\n}\n\n/* 不同类型的toast样式 */\n.toast-success .toast-content {\n  background: linear-gradient(135deg, #10b981, #059669);\n  border: 1rpx solid rgba(255, 255, 255, 0.2);\n}\n\n.toast-error .toast-content {\n  background: linear-gradient(135deg, #ef4444, #dc2626);\n  border: 1rpx solid rgba(255, 255, 255, 0.2);\n}\n\n.toast-warning .toast-content {\n  background: linear-gradient(135deg, #f59e0b, #d97706);\n  border: 1rpx solid rgba(255, 255, 255, 0.2);\n}\n\n/* Toast动画 */\n@keyframes toastFadeIn {\n  0% {\n    opacity: 0;\n    transform: translate(-50%, -50%) scale(0.8);\n  }\n  100% {\n    opacity: 1;\n    transform: translate(-50%, -50%) scale(1);\n  }\n}\n\n/* 底部留白区域 - 确保统计区域不贴屏幕底部 */\n.bottom-spacer {\n  height: 40rpx; // H5环境的底部留白\n  \n  /* #ifdef MP-WEIXIN */\n  height: 20rpx; // 微信小程序的底部留白\n  /* #endif */\n  \n  /* 如果设备有安全区域，额外添加安全区域高度 */\n  padding-bottom: env(safe-area-inset-bottom);\n}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571671465\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
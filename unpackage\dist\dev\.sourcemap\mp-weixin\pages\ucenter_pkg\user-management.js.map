{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/ucenter_pkg/user-management.vue?6b32", "webpack:///D:/Xwzc/pages/ucenter_pkg/user-management.vue?a9ae", "webpack:///D:/Xwzc/pages/ucenter_pkg/user-management.vue?4465", "webpack:///D:/Xwzc/pages/ucenter_pkg/user-management.vue?ad90", "uni-app:///pages/ucenter_pkg/user-management.vue", "webpack:///D:/Xwzc/pages/ucenter_pkg/user-management.vue?f0ac", "webpack:///D:/Xwzc/pages/ucenter_pkg/user-management.vue?bdd5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "userList", "total", "currentPage", "pageSize", "loading", "hasLoaded", "searchKeyword", "filterRole", "filterStatus", "filterAnonymous", "sortField", "searchTimer", "selectedUsers", "userForm", "username", "password", "nickname", "role", "dcloud_appid", "status", "isEdit", "saving", "batchRoleForm", "resetPasswordUser", "newUsername", "newPassword", "confirmPassword", "quickRoleUser", "quickRoleForm", "swipeStates", "touchStartX", "touchStartY", "startTranslateX", "currentSwipeUserId", "isDragging", "roleList", "roleOptions", "appList", "statusOptions", "value", "text", "anonymousOptions", "sortOptions", "isInitializing", "cacheExpiry", "roles", "apps", "formatDateCache", "lastRequestTime", "passwordConfig", "computed", "isAllSelected", "hasUsername", "getPasswordPopupTitle", "getPasswordRequirement", "super", "strong", "medium", "weak", "formatDateMemoized", "onLoad", "onPullDownRefresh", "setTimeout", "uni", "onUnload", "clearTimeout", "methods", "setCache", "timestamp", "expiry", "console", "getCache", "clearCache", "clearAllCache", "debounce", "args", "timer", "func", "throttle", "canRun", "initPage", "loadPromises", "Promise", "title", "icon", "loadRoleListWithCache", "cachedRoles", "uniCloud", "action", "result", "loadAppListWithCache", "cachedApps", "db", "initResult", "retryResult", "loadRoleList", "loadAppList", "loadUserList", "now", "params", "page", "keyword", "anonymous", "handleSearch", "handleFilter", "handleSortChange", "handlePageChange", "toggleSelectAll", "toggleUserSelect", "handleUserClick", "showCreateUser", "editUser", "_id", "resetUserForm", "closeUserForm", "handleStatusChange", "saveUser", "passwordValidation", "duration", "resetPassword", "closeResetPassword", "confirmResetPassword", "usernameRegex", "userId", "deleteUser", "content", "success", "res", "showBatchMenu", "closeBatchMenu", "batchUpdateStatus", "statusText", "showBatchRoleUpdate", "closeBatchRoleUpdate", "confirmBatchRoleUpdate", "batchDelete", "performBatchOperation", "userIds", "operation", "showCancel", "showRoleChange", "closeQuickRole", "confirmQuickRole", "resetFilters", "formatDate", "handleTouchStart", "translateX", "transitioning", "handleTouchMove", "handleTouchEnd", "handleMouseDown", "e", "handleMouseMove", "handleMouseUp", "handleSwipeMove", "maxLeftSwipe", "newTranslateX", "handleSwipeEnd", "finalTranslateX", "Object", "resetAllSwipeStates", "validatePasswordStrength"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,wUAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAAymB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACob7nB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;MACA;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MAAA;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC,mBACA;QAAAF;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAE,cACA;QAAAH;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MAEA;MACAG;MAAA;MACAC;QACAC;QAAA;QACAC;MACA;;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;;QAEA;QACA;QACA;UACA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;UACA;UACA;QACA;QAEA;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MACA;QACA;UACApE;UACAqE;UACAC;QACA;;QACAN;MACA;QACAO;MACA;IACA;IAEAC;MACA;QACA;QACA;QAEA;QACA;;QAEA;QACA;UACA;UACA;QACA;QAEA;MACA;QACAD;QACA;MACA;IACA;IAEAE;MACA;QACAT;MACA;QACAO;MACA;IACA;IAEAG;MAAA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QAAA;QAAA;UAAAC;QAAA;QACA;QACAC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QAAA;QAAA;UAAAH;QAAA;QACA;QACAI;QACAjB;UACAe;UACAE;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;;gBAEA;gBACA;gBAAA;gBAGA;gBACAC,gBACA,gCACA,8BACA,EAEA;gBAAA;gBAAA,OACAC;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;gBACAP;kBACAoB;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKAC;kBACAzF;kBACAC;oBACAyF;kBACA;gBACA;cAAA;gBALAC;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACA;kBAAA;oBACAlD;oBACAC;kBACA;gBAAA;gBAEA,sBACA;kBAAAD;kBAAAC;gBAAA,2CACA,iBACA;;gBAEA;gBACA;kBACAL;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAkC;gBACA;gBACA;kBAAA/B;kBAAAC;gBAAA;gBACA,sBACA;kBAAAD;kBAAAC;gBAAA,2CACA,iBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACAC;gBAAA;gBAAA,OACAA;cAAA;gBAAAH;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;kBAAA;oBACAlD;oBACAC;kBACA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA+C;kBACAzF;kBACAC;oBAAAyF;kBAAA;gBACA;cAAA;gBAHAK;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAD;cAAA;gBAAAE;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;kBAAA;oBACAvD;oBACAC;kBACA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA8B;gBACA;gBACA;kBAAA/B;kBAAAC;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAuD;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAGA;;gBAEA;gBACA;kBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAX;kBACAzF;kBACAC;oBACAyF;oBACAW;sBACAC;sBACAjG;sBACAkG;sBACApF;sBACAE;sBACAmF;sBACA5F;oBACA;kBACA;gBACA;cAAA;gBAdA+E;gBAgBA;kBACA;kBACA;gBACA;kBACA1B;oBACAoB;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAd;gBACAP;kBACAoB;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmB;MACA;MACA;QAAA;QACA;QACA3B;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACA4B;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAxC;gBACAP;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAKA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA2B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAzC;gBACAP;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAKA;kBACA4B;kBACAlG;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA8F;MACA;QACAnG;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;MACA;IACA;IAEA;IACA+F;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACArD;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACArB;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACAiC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAtD;kBACAoB;kBACAC;kBACAkC;gBACA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACAvD;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACArB;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAEAI;gBACAW;gBAEA;kBACAA;kBACA;kBACA;gBACA;gBAAA;gBAAA,OAEAZ;kBACAzF;kBACAC;oBACAyF;oBACAW;kBACA;gBACA;cAAA;gBANAV;gBAQA;kBACA1B;oBACAoB;oBACAC;kBACA;kBACA;kBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA;kBACA;kBAEA;gBACA;kBACArB;oBACAoB;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEArB;kBACAoB;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmC;MACA;MACA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA1D;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACAsC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA3D;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAKA;kBAAA;kBAAA;gBAAA;gBACArB;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACArB;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACAiC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAtD;kBACAoB;kBACAC;kBACAkC;gBACA;gBAAA;cAAA;gBAAA;gBAKA;gBACAnB;kBACAwB;kBACAlG;gBACA,GAEA;gBACA;kBACA0E;gBACA;gBAAA;gBAAA,OAEAZ;kBACAzF;kBACAC;oBACAyF;oBACAW;kBACA;gBACA;cAAA;gBANAV;gBAQA;kBACA1B;oBACAoB;oBACAC;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACArB;oBACAoB;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEArB;kBACAoB;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwC;MAAA;MACA;QACA7D;UACAoB;UACAC;QACA;QACA;MACA;MAEA;MACArB;QACAoB;QACA0C;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAC;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA;oBAAA,OAEAxC;sBACAzF;sBACAC;wBACAyF;wBACAW;0BACAwB;wBACA;sBACA;oBACA;kBAAA;oBARAlC;oBAUA;sBACA1B;wBACAoB;wBACAC;sBACA;sBACA;oBACA;sBACArB;wBACAoB;wBACAC;sBACA;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEArB;sBACAoB;sBACAC;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACA4C;MACA;QACAjE;UACAoB;UACAC;QACA;QACA;MACA;MACA;IACA;IAEA;IACA6C;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAEAC;gBACApE;kBACAoB;kBACA0C;kBACAC;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAC;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,OACA;gCAAA5G;8BAAA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAiH;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAvE;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA,OACA;kBAAAnE;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAsH;MAAA;MACA;MAEAxE;QACAoB;QACA0C;QACAC;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAC;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAS;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAzI;gBACAgE;kBACAoB;gBACA;gBAAA;gBAAA;gBAAA,OAGAI;kBACAzF;kBACAC;oBACAyF;oBACAW;sBACAsC;sBACAC;sBACA3I;oBACA;kBACA;gBACA;cAAA;gBAVA0F;gBAYA1B;gBAEA;kBACAA;oBACAoB;oBACAC;kBACA;;kBAEA;kBACA;oBACA;oBACA;oBACA;kBACA;kBAEA;;kBAEA;kBACA;oBACAtB;sBACAC;wBACAoB;wBACA0C;wBACAc;sBACA;oBACA;kBACA;gBACA;kBACA5E;oBACAoB;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAd;gBACAP;gBACAA;kBACAoB;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwD;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA/E;kBACAoB;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAG;kBACAzF;kBACAC;oBACAyF;oBACAW;sBACAwB;sBACA1G;oBACA;kBACA;gBACA;cAAA;gBATAwE;gBAWA;kBACA1B;oBACAoB;oBACAC;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACArB;oBACAoB;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEArB;kBACAoB;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA2D;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MAEAhF;QACAoB;QACAC;QACAkC;MACA;IACA;IAEA;IACA0B;MACA;;MAEA;MACA;MACA;MAEA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;;MAEA;MACA;QACA;UACAC;UACAC;QACA;MACA;;MAEA;MACA;IACA;IAEAC;MACA;MAEA;MACA;MACA;IACA;IAEAC;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACAC;;MAEA;MACA;QACA;UACAL;UACAC;QACA;MACA;;MAEA;MACA;IACA;IAEAK;MACA;MAEA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA;QACA;UACAH;QACA;;QAEA;QACA;;QAEA;QACA;QAKAI;;QAEA;QACAC;QAEA;UACAV;UACAC;QACA;MACA;IACA;IAEA;IACAU;MAAA;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MAEA;QACA;;QAKAC;MAEA;QACA;QACAA;MACA;QACA;QACAA;MACA;;MAEA;MACAC;QACA;UACA;YACAb;YACAC;UACA;QACA;MACA;;MAEA;MACA;QACAD;QACAC;MACA;MAEA;MACA;IACA;IAEA;IACAa;MAAA;MACAD;QACA;UACAb;UACAC;QACA;MACA;IACA;IAEA;IACAc;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1qDA;AAAA;AAAA;AAAA;AAA4qC,CAAgB,kpCAAG,EAAC,C;;;;;;;;;;;ACAhsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ucenter_pkg/user-management.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/ucenter_pkg/user-management.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./user-management.vue?vue&type=template&id=0671abb8&scoped=true&\"\nvar renderjs\nimport script from \"./user-management.vue?vue&type=script&lang=js&\"\nexport * from \"./user-management.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user-management.vue?vue&type=style&index=0&id=0671abb8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0671abb8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ucenter_pkg/user-management.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-management.vue?vue&type=template&id=0671abb8&scoped=true&\"", "var components\ntry {\n  components = {\n    uniSearchBar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar\" */ \"@/uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue\"\n      )\n    },\n    uniDataSelect: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-data-select/components/uni-data-select/uni-data-select\" */ \"@/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniPagination: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-pagination/components/uni-pagination/uni-pagination\" */ \"@/uni_modules/uni-pagination/components/uni-pagination/uni-pagination.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniDataCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox\" */ \"@/uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.selectedUsers.length\n  var g1 = _vm.selectedUsers.length\n  var g2 = g1 > 0 ? _vm.selectedUsers.length : null\n  var g3 = _vm.selectedUsers.length\n  var g4 = g3 > 0 ? _vm.selectedUsers.length : null\n  var g5 = !_vm.loading\n    ? _vm.hasLoaded && !_vm.loading && _vm.userList.length === 0\n    : null\n  var l0 =\n    !_vm.loading && !g5\n      ? _vm.__map(_vm.userList, function (user, __i1__) {\n          var $orig = _vm.__get_orig(user)\n          var g6 = _vm.selectedUsers.includes(user._id)\n          var m0 = _vm.formatDateMemoized(user.register_date)\n          var m1 = _vm.formatDateMemoized(user.last_login_date)\n          var g7 = user.role.includes(\"admin\")\n          return {\n            $orig: $orig,\n            g6: g6,\n            m0: m0,\n            m1: m1,\n            g7: g7,\n          }\n        })\n      : null\n  var g8 = _vm.selectedUsers.length\n  var g9 = _vm.selectedUsers.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        l0: l0,\n        g8: g8,\n        g9: g9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-management.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-management.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"user-management\">\n\t\t<!-- 顶部搜索和操作栏 -->\n\t\t<view class=\"header-section\">\n\t\t\t<view class=\"search-bar\">\n\t\t\t\t<uni-search-bar \n\t\t\t\t\tv-model=\"searchKeyword\" \n\t\t\t\t\tplaceholder=\"搜索用户名或昵称\"\n\t\t\t\t\t@input=\"handleSearch\"\n\t\t\t\t\t@clear=\"handleSearch\"\n\t\t\t\t\t@confirm=\"handleSearch\"\n\t\t\t\t/>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"filter-action-row\">\n\t\t\t\t<view class=\"filter-group\">\n\t\t\t\t\t<uni-data-select\n\t\t\t\t\t\tv-model=\"filterRole\"\n\t\t\t\t\t\t:localdata=\"roleOptions\"\n\t\t\t\t\t\tplaceholder=\"角色筛选\"\n\t\t\t\t\t\t@change=\"handleFilter\"\n\t\t\t\t\t\t:clear=\"false\"\n\t\t\t\t\t/>\n\t\t\t\t\t<uni-data-select\n\t\t\t\t\t\tv-model=\"filterAnonymous\"\n\t\t\t\t\t\t:localdata=\"anonymousOptions\"\n\t\t\t\t\t\tplaceholder=\"用户类型\"\n\t\t\t\t\t\t@change=\"handleFilter\"\n\t\t\t\t\t\t:clear=\"false\"\n\t\t\t\t\t/>\n\t\t\t\t\t<uni-data-select\n\t\t\t\t\t\tv-model=\"sortField\"\n\t\t\t\t\t\t:localdata=\"sortOptions\"\n\t\t\t\t\t\tplaceholder=\"排序方式\"\n\t\t\t\t\t\t@change=\"handleSortChange\"\n\t\t\t\t\t\t:clear=\"false\"\n\t\t\t\t\t/>\n\t\t\t\t\t<uni-data-select\n\t\t\t\t\t\tv-model=\"filterStatus\"\n\t\t\t\t\t\t:localdata=\"statusOptions\"\n\t\t\t\t\t\tplaceholder=\"状态\"\n\t\t\t\t\t\t@change=\"handleFilter\"\n\t\t\t\t\t\t:clear=\"false\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"action-group\">\n\t\t\t\t\t<button class=\"add-btn\" @click=\"showCreateUser\">新增用户</button>\n\t\t\t\t\t<button class=\"batch-btn\" :class=\"{disabled: selectedUsers.length === 0}\" @click=\"showBatchMenu\">\n\t\t\t\t\t\t批量操作{{selectedUsers.length > 0 ? `(${selectedUsers.length})` : ''}}\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"reset-btn\" @click=\"resetFilters\">重置选项</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 用户列表 -->\n\t\t<view class=\"user-list\">\n\t\t\t<view class=\"list-header\">\n\t\t\t\t<view class=\"select-all-wrapper\" @click=\"toggleSelectAll\">\n\t\t\t\t\t<checkbox :checked=\"isAllSelected\" />\n\t\t\t\t\t<text>全选</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"header-text\">用户列表 (共{{total}}人)</text>\n\t\t\t\t<text class=\"select-tip\" v-if=\"selectedUsers.length > 0\">已选择{{selectedUsers.length}}个用户</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view v-if=\"loading\" class=\"loading\">\n\t\t\t\t<!-- 骨架屏 -->\n\t\t\t\t<view class=\"skeleton-wrapper\">\n\t\t\t\t\t<view v-for=\"n in 5\" :key=\"n\" class=\"skeleton-item\">\n\t\t\t\t\t\t<view class=\"skeleton-avatar skeleton-animate\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-content\">\n\t\t\t\t\t\t\t<view class=\"skeleton-line skeleton-name skeleton-animate\"></view>\n\t\t\t\t\t\t\t<view class=\"skeleton-line skeleton-role skeleton-animate\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"skeleton-meta\">\n\t\t\t\t\t\t\t<view class=\"skeleton-line skeleton-status skeleton-animate\"></view>\n\t\t\t\t\t\t\t<view class=\"skeleton-line skeleton-time skeleton-animate\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view v-else-if=\"hasLoaded && !loading && userList.length === 0\" class=\"empty\">\n\t\t\t\t<uni-icons type=\"person\" size=\"60\" color=\"#ccc\" />\n\t\t\t\t<text>暂无用户数据</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view v-else class=\"user-items\">\n\t\t\t\t<view \n\t\t\t\t\tv-for=\"user in userList\" \n\t\t\t\t\t:key=\"user._id\" \n\t\t\t\t\tclass=\"user-item-wrapper\"\n\t\t\t\t>\n\t\t\t\t\t<!-- 用户信息卡片 -->\n\t\t\t\t\t<view class=\"user-item\" @click=\"handleUserClick(user)\">\n\t\t\t\t\t\t<view class=\"user-checkbox-wrapper\" @click.stop=\"toggleUserSelect(user._id)\">\n\t\t\t\t\t\t\t<checkbox :checked=\"selectedUsers.includes(user._id)\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 用户信息部分 - 固定不动 -->\n\t\t\t\t\t\t<view class=\"user-info-fixed\">\n\t\t\t\t\t\t\t<!-- 用户基本信息 -->\n\t\t\t\t\t\t\t<view class=\"user-basic-info\">\n\t\t\t\t\t\t\t\t<text class=\"username\">{{user.nickname || user.username || '微信用户'}}</text>\n\t\t\t\t\t\t\t\t<!-- 时间信息 -->\n\t\t\t\t\t\t\t\t<view class=\"user-meta\">\n\t\t\t\t\t\t\t\t\t<text class=\"register-date\">注册时间: {{formatDateMemoized(user.register_date)}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"last-login\">最后登录: {{formatDateMemoized(user.last_login_date)}}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 右侧可滑动区域容器 - 整个区域都可以滑动 -->\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"user-right-container\"\n\t\t\t\t\t\t\t@touchstart=\"handleTouchStart($event, user._id)\"\n\t\t\t\t\t\t\t@touchmove=\"handleTouchMove($event, user._id)\"\n\t\t\t\t\t\t\t@touchend=\"handleTouchEnd($event, user._id)\"\n\t\t\t\t\t\t\t@mousedown=\"handleMouseDown($event, user._id)\"\n\t\t\t\t\t\t\t@mousemove=\"handleMouseMove($event, user._id)\"\n\t\t\t\t\t\t\t@mouseup=\"handleMouseUp($event, user._id)\"\n\t\t\t\t\t\t\t@mouseleave=\"handleMouseUp($event, user._id)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<!-- 可滑动的右侧内容 -->\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"user-right-swipable\"\n\t\t\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\t\t\ttransform: swipeStates[user._id] ? `translateX(${swipeStates[user._id].translateX}px)` : 'translateX(0px)',\n\t\t\t\t\t\t\t\t\ttransition: swipeStates[user._id] && swipeStates[user._id].transitioning ? 'transform 0.3s ease' : 'none'\n\t\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<!-- 左侧内容区域 - 状态和角色 -->\n\t\t\t\t\t\t\t\t<view class=\"left-content\">\n\t\t\t\t\t\t\t\t\t<view class=\"status-badge\" :class=\"user.status === 0 ? 'active' : 'inactive'\">\n\t\t\t\t\t\t\t\t\t\t{{user.status === 0 ? '正常' : '禁用'}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"user-roles\">\n\t\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\t\tv-for=\"roleName in user.roleNames\" \n\t\t\t\t\t\t\t\t\t\t\t:key=\"roleName\" \n\t\t\t\t\t\t\t\t\t\t\tclass=\"role-tag\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t{{roleName}}\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 齿轮按钮固定在最右侧 -->\n\t\t\t\t\t\t\t\t<button class=\"change-role-btn\" @click.stop=\"showRoleChange(user)\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"gear\" size=\"16\" color=\"#007aff\" />\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 操作按钮区域 - 默认隐藏在右侧外部 -->\n\t\t\t\t\t\t\t<view class=\"user-actions-buttons\">\n\t\t\t\t\t\t\t\t<button class=\"edit-btn\" @click.stop=\"editUser(user)\">\n\t\t\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"compose\" size=\"18\" color=\"white\" />\n\t\t\t\t\t\t\t\t\t\t<text>编辑</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t<button class=\"password-btn\" @click.stop=\"resetPassword(user)\">\n\t\t\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"locked\" size=\"18\" color=\"white\" />\n\t\t\t\t\t\t\t\t\t\t<text>密码</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t<button class=\"delete-btn\" @click.stop=\"deleteUser(user)\" :disabled=\"user.role.includes('admin')\">\n\t\t\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"18\" color=\"white\" />\n\t\t\t\t\t\t\t\t\t\t<text>删除</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 分页 -->\n\t\t<view class=\"pagination\" v-if=\"total > pageSize\">\n\t\t\t<uni-pagination \n\t\t\t\t:current=\"currentPage\" \n\t\t\t\t:total=\"total\" \n\t\t\t\t:pageSize=\"pageSize\"\n\t\t\t\t@change=\"handlePageChange\"\n\t\t\t/>\n\t\t</view>\n\n\t\t<!-- 创建/编辑用户弹窗 -->\n\t\t<uni-popup ref=\"userFormPopup\" type=\"center\" :mask-click=\"false\">\n\t\t\t<view class=\"user-form-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">{{isEdit ? '编辑用户' : '新增用户'}}</text>\n\t\t\t\t\t<button class=\"close-btn\" @click=\"closeUserForm\">×</button>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"popup-content\">\n\t\t\t\t\t<view class=\"form-item\" v-if=\"!isEdit\">\n\t\t\t\t\t\t<view class=\"label\">登录账号 <text class=\"required-star\">*</text></view>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tv-model=\"userForm.username\" \n\t\t\t\t\t\t\tplaceholder=\"请输入账号\"\n\t\t\t\t\t\t\tclass=\"input\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"!isEdit\">\n\t\t\t\t\t\t<view class=\"label\">登录密码 <text class=\"required-star\">*</text></view>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tv-model=\"userForm.password\" \n\t\t\t\t\t\t\tplaceholder=\"请输入密码\"\n\t\t\t\t\t\t\ttype=\"password\"\n\t\t\t\t\t\t\tclass=\"input\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"label\">用户名</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tv-model=\"userForm.nickname\" \n\t\t\t\t\t\t\tplaceholder=\"请输入用户昵称\"\n\t\t\t\t\t\t\tclass=\"input\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"label\">角色名 <text class=\"required-star\">*</text></view>\n\t\t\t\t\t\t<uni-data-checkbox \n\t\t\t\t\t\t\tv-model=\"userForm.role\" \n\t\t\t\t\t\t\t:localdata=\"roleList\"\n\t\t\t\t\t\t\tmultiple\n\t\t\t\t\t\t\twrap\n\t\t\t\t\t\t\tmode=\"tag\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"label\">可登录应用 <text class=\"required-star\">*</text></view>\n\t\t\t\t\t\t<uni-data-checkbox \n\t\t\t\t\t\t\tv-model=\"userForm.dcloud_appid\" \n\t\t\t\t\t\t\t:localdata=\"appList\"\n\t\t\t\t\t\t\tmultiple\n\t\t\t\t\t\t\twrap\n\t\t\t\t\t\t\tmode=\"tag\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item status-switch\">\n\t\t\t\t\t\t<text class=\"label\">用户状态：</text>\n\t\t\t\t\t\t<view class=\"status-display\">\n\t\t\t\t\t\t\t<switch \n\t\t\t\t\t\t\t\t:checked=\"userForm.status === 0\" \n\t\t\t\t\t\t\t\t@change=\"handleStatusChange\"\n\t\t\t\t\t\t\t\tcolor=\"#007aff\"\n\t\t\t\t\t\t\t\tstyle=\"transform: scale(0.8);\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t<view class=\"status-badge\" :class=\"userForm.status === 0 ? 'active' : 'inactive'\">\n\t\t\t\t\t\t\t\t{{userForm.status === 0 ? '正常' : '禁用'}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"popup-footer\">\n\t\t\t\t\t<button class=\"cancel-btn\" @click=\"closeUserForm\">取消</button>\n\t\t\t\t\t<button class=\"confirm-btn\" @click=\"saveUser\" :loading=\"saving\">\n\t\t\t\t\t\t{{isEdit ? '更新' : '创建'}}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t<!-- 批量操作菜单 -->\n\t\t<uni-popup ref=\"batchMenuPopup\" type=\"bottom\">\n\t\t\t<view class=\"batch-menu\">\n\t\t\t\t<view class=\"menu-header\">\n\t\t\t\t\t<text>批量操作 (已选择{{selectedUsers.length}}个用户)</text>\n\t\t\t\t\t<button @click=\"closeBatchMenu\">取消</button>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"menu-items\">\n\t\t\t\t\t<button class=\"menu-item\" @click=\"batchUpdateStatus(0)\">\n\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" />\n\t\t\t\t\t\t<text>批量启用</text>\n\t\t\t\t\t</button>\n\t\t\t\t\t\n\t\t\t\t\t<button class=\"menu-item\" @click=\"batchUpdateStatus(1)\">\n\t\t\t\t\t\t<uni-icons type=\"close\" />\n\t\t\t\t\t\t<text>批量禁用</text>\n\t\t\t\t\t</button>\n\t\t\t\t\t\n\t\t\t\t\t<button class=\"menu-item\" @click=\"showBatchRoleUpdate\">\n\t\t\t\t\t\t<uni-icons type=\"gear\" />\n\t\t\t\t\t\t<text>批量设置角色</text>\n\t\t\t\t\t</button>\n\t\t\t\t\t\n\t\t\t\t\t<button class=\"menu-item danger\" @click=\"batchDelete\">\n\t\t\t\t\t\t<uni-icons type=\"trash\" />\n\t\t\t\t\t\t<text>批量删除</text>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t<!-- 批量角色设置弹窗 -->\n\t\t<uni-popup ref=\"batchRolePopup\" type=\"center\">\n\t\t\t<view class=\"batch-role-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">批量设置角色</text>\n\t\t\t\t\t<button class=\"close-btn\" @click=\"closeBatchRoleUpdate\">×</button>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"popup-content\">\n\t\t\t\t\t<!-- 批量操作提示 - 统一风格 -->\n\t\t\t\t\t<view class=\"batch-operation-tip\">\n\t\t\t\t\t\t<uni-icons type=\"gear-filled\" size=\"18\" color=\"#ff9500\" />\n\t\t\t\t\t\t<text class=\"operation-text\">将为所选{{selectedUsers.length}}个用户设置以下角色：</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<uni-data-checkbox \n\t\t\t\t\t\tv-model=\"batchRoleForm\" \n\t\t\t\t\t\t:localdata=\"roleList\"\n\t\t\t\t\t\tmultiple\n\t\t\t\t\t\twrap\n\t\t\t\t\t\tmode=\"tag\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"popup-footer\">\n\t\t\t\t\t<button class=\"cancel-btn\" @click=\"closeBatchRoleUpdate\">取消</button>\n\t\t\t\t\t<button class=\"confirm-btn\" @click=\"confirmBatchRoleUpdate\">确定</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t<!-- 重置密码/设置账号密码弹窗 -->\n\t\t<uni-popup ref=\"resetPasswordPopup\" type=\"center\">\n\t\t\t<view class=\"reset-password-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">{{getPasswordPopupTitle}}</text>\n\t\t\t\t\t<button class=\"close-btn\" @click=\"closeResetPassword\">×</button>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"popup-content\">\n\t\t\t\t\t<!-- 用户信息展示 - 简洁版本 -->\n\t\t\t\t\t<view class=\"user-info-section\">\n\t\t\t\t\t\t<view class=\"user-basic-info\">\n\t\t\t\t\t\t\t<uni-icons type=\"person-filled\" size=\"18\" color=\"#007aff\" />\n\t\t\t\t\t\t\t<text class=\"user-name\">{{resetPasswordUser.nickname || resetPasswordUser.username || '该用户'}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 账号信息提示 - 当用户已有账号时显示 -->\n\t\t\t\t\t\t<view class=\"account-tip\" v-if=\"hasUsername\">\n\t\t\t\t\t\t\t<text class=\"tip-text\">当前登录账号：</text>\n\t\t\t\t\t\t\t<text class=\"account-name\">{{resetPasswordUser.username || '未知账号'}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 用户名输入框 - 只在用户没有用户名时显示 -->\n\t\t\t\t\t<view class=\"form-item\" v-if=\"!hasUsername\">\n\t\t\t\t\t\t<view class=\"label\">用户账号 <text class=\"required-star\">*</text></view>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tv-model=\"newUsername\" \n\t\t\t\t\t\t\tplaceholder=\"请输入用户账号\"\n\t\t\t\t\t\t\tclass=\"input\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"label\">新密码 <text class=\"required-star\">*</text></view>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tv-model=\"newPassword\" \n\t\t\t\t\t\t\tplaceholder=\"请输入新密码\"\n\t\t\t\t\t\t\ttype=\"password\"\n\t\t\t\t\t\t\tclass=\"input\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"label\">确认密码 <text class=\"required-star\">*</text></view>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tv-model=\"confirmPassword\" \n\t\t\t\t\t\t\tplaceholder=\"请再次输入新密码\"\n\t\t\t\t\t\t\ttype=\"password\"\n\t\t\t\t\t\t\tclass=\"input\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"popup-footer\">\n\t\t\t\t\t<button class=\"cancel-btn\" @click=\"closeResetPassword\">取消</button>\n\t\t\t\t\t<button class=\"confirm-btn\" @click=\"confirmResetPassword\">确定</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t<!-- 快速角色变更弹窗 -->\n\t\t<uni-popup ref=\"quickRolePopup\" type=\"center\">\n\t\t\t<view class=\"quick-role-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">变更角色</text>\n\t\t\t\t\t<button class=\"close-btn\" @click=\"closeQuickRole\">×</button>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"popup-content\">\n\t\t\t\t\t<!-- 用户信息展示 - 与密码弹窗保持统一风格 -->\n\t\t\t\t\t<view class=\"user-info-section\">\n\t\t\t\t\t\t<view class=\"user-basic-info\">\n\t\t\t\t\t\t\t<uni-icons type=\"person-filled\" size=\"18\" color=\"#007aff\" />\n\t\t\t\t\t\t\t<text class=\"user-name\">{{quickRoleUser.nickname || quickRoleUser.username || '该用户'}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<uni-data-checkbox \n\t\t\t\t\t\tv-model=\"quickRoleForm\" \n\t\t\t\t\t\t:localdata=\"roleList\"\n\t\t\t\t\t\tmultiple\n\t\t\t\t\t\twrap\n\t\t\t\t\t\tmode=\"tag\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"popup-footer\">\n\t\t\t\t\t<button class=\"cancel-btn\" @click=\"closeQuickRole\">取消</button>\n\t\t\t\t\t<button class=\"confirm-btn\" @click=\"confirmQuickRole\">确定</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</view>\n</template>\n\n<script>\n// 导入配置\nimport config from '@/uni_modules/uni-id-pages/config.js'\nimport passwordMod from '@/uni_modules/uni-id-pages/common/password.js'\n\nexport default {\n\tname: 'UserManagement',\n\tdata() {\n\t\treturn {\n\t\t\t// 列表数据\n\t\t\tuserList: [],\n\t\t\ttotal: 0,\n\t\t\tcurrentPage: 1,\n\t\t\tpageSize: 20,\n\t\t\tloading: false,\n\t\t\thasLoaded: false, // 标记是否已经加载过数据\n\t\t\t\n\t\t\t// 搜索筛选\n\t\t\tsearchKeyword: '',\n\t\t\tfilterRole: '',\n\t\t\tfilterStatus: '',\n\t\t\tfilterAnonymous: '',\n\t\t\tsortField: '',\n\t\t\tsearchTimer: null,\n\t\t\t\n\t\t\t// 选择状态\n\t\t\tselectedUsers: [],\n\t\t\t\n\t\t\t// 表单数据\n\t\t\tuserForm: {\n\t\t\t\tusername: '',\n\t\t\t\tpassword: '',\n\t\t\t\tnickname: '',\n\t\t\t\trole: [],\n\t\t\t\tdcloud_appid: [], // 可登录应用\n\t\t\t\tstatus: 0\n\t\t\t},\n\t\t\tisEdit: false,\n\t\t\tsaving: false,\n\t\t\t\n\t\t\t// 批量操作\n\t\t\tbatchRoleForm: [],\n\t\t\t\n\t\t\t// 重置密码/设置账号密码\n\t\t\tresetPasswordUser: {},\n\t\t\tnewUsername: '',\n\t\t\tnewPassword: '',\n\t\t\tconfirmPassword: '',\n\t\t\t\n\t\t\t// 快速角色变更\n\t\t\tquickRoleUser: {},\n\t\t\tquickRoleForm: [],\n\t\t\t\n\t\t\t// 滑动操作相关\n\t\t\tswipeStates: {}, // 存储每个用户的滑动状态\n\t\t\ttouchStartX: 0,\n\t\t\ttouchStartY: 0,\n\t\t\tstartTranslateX: 0, // 开始滑动时的初始translateX位置\n\t\t\tcurrentSwipeUserId: null,\n\t\t\tisDragging: false, // H5拖拽状态\n\t\t\t\n\t\t\t// 角色和状态选项\n\t\t\troleList: [],\n\t\t\troleOptions: [],\n\t\t\tappList: [], // 应用列表\n\t\t\tstatusOptions: [\n\t\t\t\t{ value: '', text: '状态' },\n\t\t\t\t{ value: 0, text: '正常' },\n\t\t\t\t{ value: 1, text: '禁用' }\n\t\t\t],\n\t\t\tanonymousOptions: [\n\t\t\t\t{ value: '', text: '全部用户' },\n\t\t\t\t{ value: 'no', text: '实名用户' },\n\t\t\t\t{ value: 'yes', text: '匿名用户' }\n\t\t\t],\n\t\t\tsortOptions: [\n\t\t\t\t{ value: '', text: '默认排序' },\n\t\t\t\t{ value: 'name_asc', text: '姓名排序' },\n\t\t\t\t{ value: 'register_desc', text: '注册时间' },\n\t\t\t\t{ value: 'login_desc', text: '登录时间' }\n\t\t\t],\n\t\t\t\n\t\t\t// 性能优化相关\n\t\t\tisInitializing: false, // 初始化状态\n\t\t\tcacheExpiry: {\n\t\t\t\troles: 2 * 60 * 60 * 1000,  // 角色缓存2小时\n\t\t\t\tapps: 1 * 60 * 60 * 1000    // 应用缓存1小时\n\t\t\t},\n\t\t\tformatDateCache: new Map(), // 日期格式化缓存\n\t\t\tlastRequestTime: 0, // 防止重复请求\n\t\t\t\n\t\t\t// 密码配置\n\t\t\tpasswordConfig: config.passwordStrength || 'weak',\n\t\t};\n\t},\n\t\n\tcomputed: {\n\t\tisAllSelected() {\n\t\t\treturn this.userList.length > 0 && this.selectedUsers.length === this.userList.length;\n\t\t},\n\t\t\n\t\t// 判断当前用户是否有用户名和密码（即是否为手动创建的账号）\n\t\thasUsername() {\n\t\t\tconst user = this.resetPasswordUser;\n\t\t\tif (!user) return false;\n\t\t\t\n\t\t\t// 优先使用云函数返回的判断字段\n\t\t\tif (user.hasPassword !== undefined && user.isThirdPartyUser !== undefined) {\n\t\t\t\t// 如果是第三方登录用户或者没有密码，则需要设置账号密码\n\t\t\t\treturn user.hasPassword && !user.isThirdPartyUser;\n\t\t\t}\n\t\t\t\n\t\t\t// 后备判断逻辑：检查原始用户名\n\t\t\tconst originalUsername = user.originalUsername || user.username;\n\t\t\tif (!originalUsername || originalUsername.trim() === '') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果用户名看起来像是自动生成的（包含用户ID的前8位），则认为没有用户名\n\t\t\tif (user._id && originalUsername.includes(user._id.substring(0, 8))) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\treturn true;\n\t\t},\n\t\t\n\t\t// 密码弹窗标题\n\t\tgetPasswordPopupTitle() {\n\t\t\treturn this.hasUsername ? '重置密码' : '设置账号密码';\n\t\t},\n\t\t\n\t\t// 获取密码强度要求说明\n\t\tgetPasswordRequirement() {\n\t\t\tconst requirements = {\n\t\t\t\tsuper: '密码必须包含大小写字母、数字和特殊符号，长度8-16位',\n\t\t\t\tstrong: '密码必须包含字母、数字和特殊符号，长度8-16位',\n\t\t\t\tmedium: '密码必须为字母、数字和特殊符号任意两种的组合，长度8-16位',\n\t\t\t\tweak: '密码必须包含字母和数字，长度6-16位'\n\t\t\t};\n\t\t\treturn requirements[this.passwordConfig] || requirements.weak;\n\t\t},\n\t\t\n\t\t// 缓存的格式化日期方法\n\t\tformatDateMemoized() {\n\t\t\treturn (timestamp) => {\n\t\t\t\tif (!timestamp) return '未知';\n\t\t\t\t\n\t\t\t\t// 检查缓存\n\t\t\t\tconst cacheKey = `${timestamp}`;\n\t\t\t\tif (this.formatDateCache.has(cacheKey)) {\n\t\t\t\t\treturn this.formatDateCache.get(cacheKey);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 格式化并缓存\n\t\t\t\tconst formatted = this.formatDate(timestamp);\n\t\t\t\tthis.formatDateCache.set(cacheKey, formatted);\n\t\t\t\t\n\t\t\t\t// 限制缓存大小\n\t\t\t\tif (this.formatDateCache.size > 100) {\n\t\t\t\t\tconst firstKey = this.formatDateCache.keys().next().value;\n\t\t\t\t\tthis.formatDateCache.delete(firstKey);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn formatted;\n\t\t\t};\n\t\t}\n\t},\n\t\n\tonLoad() {\n\t\tthis.initPage();\n\t},\n\t\n\tonPullDownRefresh() {\n\t\tthis.loadUserList();\n\t\tsetTimeout(() => {\n\t\t\tuni.stopPullDownRefresh();\n\t\t}, 1000);\n\t},\n\t\n\tonUnload() {\n\t\t// 页面卸载时清理缓存，释放内存\n\t\tthis.formatDateCache.clear();\n\t\tif (this.searchTimer) {\n\t\t\tclearTimeout(this.searchTimer);\n\t\t}\n\t},\n\t\n\tmethods: {\n\t\t// 缓存管理方法\n\t\tsetCache(key, data, expiry = null) {\n\t\t\ttry {\n\t\t\t\tconst cacheData = {\n\t\t\t\t\tdata,\n\t\t\t\t\ttimestamp: Date.now(),\n\t\t\t\t\texpiry: expiry || this.cacheExpiry[key] || (30 * 60 * 1000) // 默认30分钟\n\t\t\t\t};\n\t\t\t\tuni.setStorageSync(`user_mgmt_${key}`, JSON.stringify(cacheData));\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('设置缓存失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\tgetCache(key) {\n\t\t\ttry {\n\t\t\t\tconst cached = uni.getStorageSync(`user_mgmt_${key}`);\n\t\t\t\tif (!cached) return null;\n\t\t\t\t\n\t\t\t\tconst cacheData = JSON.parse(cached);\n\t\t\t\tconst now = Date.now();\n\t\t\t\t\n\t\t\t\t// 检查是否过期\n\t\t\t\tif (now - cacheData.timestamp > cacheData.expiry) {\n\t\t\t\t\tthis.clearCache(key);\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn cacheData.data;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取缓存失败:', error);\n\t\t\t\treturn null;\n\t\t\t}\n\t\t},\n\t\t\n\t\tclearCache(key) {\n\t\t\ttry {\n\t\t\t\tuni.removeStorageSync(`user_mgmt_${key}`);\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('清除缓存失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\tclearAllCache() {\n\t\t\t['roles', 'apps'].forEach(key => this.clearCache(key));\n\t\t},\n\t\t\n\t\t// 防抖处理\n\t\tdebounce(func, delay) {\n\t\t\tlet timer = null;\n\t\t\treturn function(...args) {\n\t\t\t\tif (timer) clearTimeout(timer);\n\t\t\t\ttimer = setTimeout(() => {\n\t\t\t\t\tfunc.apply(this, args);\n\t\t\t\t}, delay);\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 节流处理\n\t\tthrottle(func, delay) {\n\t\t\tlet canRun = true;\n\t\t\treturn function(...args) {\n\t\t\t\tif (!canRun) return;\n\t\t\t\tcanRun = false;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tfunc.apply(this, args);\n\t\t\t\t\tcanRun = true;\n\t\t\t\t}, delay);\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 优化后的初始化页面 - 并行加载\n\t\tasync initPage() {\n\t\t\tif (this.isInitializing) return; // 防止重复初始化\n\t\t\t\n\t\t\tthis.isInitializing = true;\n\t\t\tthis.loading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 并行加载所有必要数据\n\t\t\t\tconst loadPromises = [\n\t\t\t\t\tthis.loadRoleListWithCache(),\n\t\t\t\t\tthis.loadAppListWithCache()\n\t\t\t\t];\n\t\t\t\t\n\t\t\t\t// 等待角色和应用数据加载完成后再加载用户列表\n\t\t\t\tawait Promise.allSettled(loadPromises);\n\t\t\t\t\n\t\t\t\t// 最后加载用户列表\n\t\t\t\tawait this.loadUserList();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('页面初始化失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '初始化失败，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t\tthis.hasLoaded = true;\n\t\t\t\tthis.isInitializing = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 带缓存的角色列表加载\n\t\tasync loadRoleListWithCache() {\n\t\t\ttry {\n\t\t\t\t// 先尝试从缓存获取\n\t\t\t\tconst cachedRoles = this.getCache('roles');\n\t\t\t\tif (cachedRoles) {\n\t\t\t\t\tthis.roleList = cachedRoles.roleList;\n\t\t\t\t\tthis.roleOptions = cachedRoles.roleOptions;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 缓存失效，从服务器获取\n\t\t\t\tconst result = await uniCloud.callFunction({\n\t\t\t\t\tname: 'user-management',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'getRoleList'\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (result.result.code === 0) {\n\t\t\t\t\tthis.roleList = result.result.data.map(role => ({\n\t\t\t\t\t\tvalue: role.value,\n\t\t\t\t\t\ttext: role.text\n\t\t\t\t\t}));\n\t\t\t\t\t\n\t\t\t\t\tthis.roleOptions = [\n\t\t\t\t\t\t{ value: '', text: '全部角色' },\n\t\t\t\t\t\t...this.roleList\n\t\t\t\t\t];\n\t\t\t\t\t\n\t\t\t\t\t// 缓存结果\n\t\t\t\t\tthis.setCache('roles', {\n\t\t\t\t\t\troleList: this.roleList,\n\t\t\t\t\t\troleOptions: this.roleOptions\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(result.result.message || '加载角色失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载角色列表失败:', error);\n\t\t\t\t// 使用默认角色数据作为后备\n\t\t\t\tthis.roleList = [{ value: 'user', text: '普通用户' }];\n\t\t\t\tthis.roleOptions = [\n\t\t\t\t\t{ value: '', text: '全部角色' },\n\t\t\t\t\t...this.roleList\n\t\t\t\t];\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 带缓存的应用列表加载\n\t\tasync loadAppListWithCache() {\n\t\t\ttry {\n\t\t\t\t// 先尝试从缓存获取\n\t\t\t\tconst cachedApps = this.getCache('apps');\n\t\t\t\tif (cachedApps) {\n\t\t\t\t\tthis.appList = cachedApps;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 缓存失效，从数据库获取\n\t\t\t\tconst db = uniCloud.database();\n\t\t\t\tconst result = await db.collection('opendb-app-list').get();\n\t\t\t\t\n\t\t\t\tif (result.result && result.result.data && result.result.data.length > 0) {\n\t\t\t\t\tthis.appList = result.result.data.map(app => ({\n\t\t\t\t\t\tvalue: app.appid,\n\t\t\t\t\t\ttext: app.name\n\t\t\t\t\t}));\n\t\t\t\t} else {\n\t\t\t\t\t// 如果数据库中没有应用数据，尝试初始化\n\t\t\t\t\tconst initResult = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'user-management',\n\t\t\t\t\t\tdata: { action: 'initApps' }\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (initResult.result.code === 0) {\n\t\t\t\t\t\tconst retryResult = await db.collection('opendb-app-list').get();\n\t\t\t\t\t\tif (retryResult.result && retryResult.result.data && retryResult.result.data.length > 0) {\n\t\t\t\t\t\t\tthis.appList = retryResult.result.data.map(app => ({\n\t\t\t\t\t\t\t\tvalue: app.appid,\n\t\t\t\t\t\t\t\ttext: app.name\n\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrow new Error('初始化应用数据失败');\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error('初始化应用数据失败');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 缓存结果\n\t\t\t\tthis.setCache('apps', this.appList);\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载应用列表失败:', error);\n\t\t\t\t// 使用默认应用数据作为后备\n\t\t\t\tthis.appList = [{ value: '__UNI__ZZPS', text: '株水小智' }];\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 保留原有方法，但添加请求去重\n\t\tasync loadRoleList() {\n\t\t\treturn this.loadRoleListWithCache();\n\t\t},\n\t\t\n\t\t// 保留原有方法，但添加请求去重\n\t\tasync loadAppList() {\n\t\t\treturn this.loadAppListWithCache();\n\t\t},\n\t\t\n\t\t// 优化后的用户列表加载\n\t\tasync loadUserList() {\n\t\t\t// 防止重复请求\n\t\t\tconst now = Date.now();\n\t\t\tif (now - this.lastRequestTime < 500) { // 500ms内不重复请求\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.lastRequestTime = now;\n\t\t\t\n\t\t\t// 如果不是初始化调用，则设置loading状态\n\t\t\tif (this.hasLoaded || this.loading === false) {\n\t\t\t\tthis.loading = true;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst result = await uniCloud.callFunction({\n\t\t\t\t\tname: 'user-management',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'getUserList',\n\t\t\t\t\t\tparams: {\n\t\t\t\t\t\t\tpage: this.currentPage,\n\t\t\t\t\t\t\tpageSize: this.pageSize,\n\t\t\t\t\t\t\tkeyword: this.searchKeyword,\n\t\t\t\t\t\t\trole: this.filterRole,\n\t\t\t\t\t\t\tstatus: this.filterStatus,\n\t\t\t\t\t\t\tanonymous: this.filterAnonymous,\n\t\t\t\t\t\t\tsortField: this.sortField\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (result.result.code === 0) {\n\t\t\t\t\tthis.userList = result.result.data.list;\n\t\t\t\t\tthis.total = result.result.data.total;\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: result.result.message || '加载失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载用户列表失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t\tthis.hasLoaded = true;\n\t\t\t\tthis.selectedUsers = [];\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 优化搜索处理 - 使用防抖\n\t\thandleSearch: (() => {\n\t\t\tlet timer = null;\n\t\t\treturn function() {\n\t\t\t\tif (timer) clearTimeout(timer);\n\t\t\t\ttimer = setTimeout(() => {\n\t\t\t\t\tthis.currentPage = 1;\n\t\t\t\t\tthis.selectedUsers = [];\n\t\t\t\t\tthis.loadUserList();\n\t\t\t\t}, 300);\n\t\t\t};\n\t\t})(),\n\t\t\n\t\t// 筛选处理\n\t\thandleFilter() {\n\t\t\t// 重置滑动状态\n\t\t\tthis.resetAllSwipeStates();\n\t\t\tthis.currentPage = 1;\n\t\t\tthis.selectedUsers = []; // 筛选时清空选择\n\t\t\tthis.loadUserList();\n\t\t},\n\t\t\n\t\t// 排序变更处理\n\t\thandleSortChange(value) {\n\t\t\t// 确保sortField已经更新\n\t\t\tthis.sortField = value;\n\t\t\tthis.currentPage = 1;\n\t\t\tthis.loadUserList();\n\t\t},\n\t\t\n\t\t// 分页处理\n\t\thandlePageChange(page) {\n\t\t\tthis.currentPage = page.current;\n\t\t\tthis.loadUserList();\n\t\t},\n\t\t\n\t\t// 切换全选状态\n\t\ttoggleSelectAll() {\n\t\t\tif (this.isAllSelected) {\n\t\t\t\tthis.selectedUsers = [];\n\t\t\t} else {\n\t\t\t\tthis.selectedUsers = this.userList.map(user => user._id);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 切换单个用户选择状态\n\t\ttoggleUserSelect(userId) {\n\t\t\tconst index = this.selectedUsers.indexOf(userId);\n\t\t\tif (index > -1) {\n\t\t\t\tthis.selectedUsers.splice(index, 1);\n\t\t\t} else {\n\t\t\t\tthis.selectedUsers.push(userId);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 用户点击处理\n\t\thandleUserClick(user) {\n\t\t\t// 如果刚刚进行了滑动操作，不触发点击事件\n\t\t\tif (this.currentSwipeUserId || this.isDragging) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t// 可以显示用户详情或进行其他操作\n\t\t},\n\t\t\n\t\t// 显示创建用户弹窗\n\t\tasync showCreateUser() {\n\t\t\tthis.isEdit = false;\n\t\t\t\n\t\t\t// 确保应用列表已加载\n\t\t\tif (!this.appList || this.appList.length === 0) {\n\t\t\t\ttry {\n\t\t\t\t\tawait this.loadAppList();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载应用列表失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载应用列表失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tthis.resetUserForm();\n\t\t\tthis.$refs.userFormPopup.open();\n\t\t},\n\t\t\n\t\t// 编辑用户\n\t\tasync editUser(user) {\n\t\t\tthis.isEdit = true;\n\t\t\t\n\t\t\t// 确保应用列表已加载\n\t\t\tif (!this.appList || this.appList.length === 0) {\n\t\t\t\ttry {\n\t\t\t\t\tawait this.loadAppList();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载应用列表失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载应用列表失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tthis.userForm = {\n\t\t\t\t_id: user._id,\n\t\t\t\tusername: user.username,\n\t\t\t\tpassword: '',\n\t\t\t\tnickname: user.nickname || '',\n\t\t\t\trole: user.role || [],\n\t\t\t\tdcloud_appid: Array.isArray(user.dcloud_appid) ? user.dcloud_appid : (user.dcloud_appid ? [user.dcloud_appid] : []),\n\t\t\t\tstatus: user.status\n\t\t\t};\n\t\t\tthis.$refs.userFormPopup.open();\n\t\t},\n\t\t\n\t\t// 重置表单\n\t\tresetUserForm() {\n\t\t\tthis.userForm = {\n\t\t\t\tusername: '',\n\t\t\t\tpassword: '',\n\t\t\t\tnickname: '',\n\t\t\t\trole: [],\n\t\t\t\tdcloud_appid: [], // 可登录应用\n\t\t\t\tstatus: 0\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 关闭用户表单\n\t\tcloseUserForm() {\n\t\t\tthis.$refs.userFormPopup.close();\n\t\t},\n\t\t\n\t\t// 处理状态开关变化\n\t\thandleStatusChange(e) {\n\t\t\tthis.userForm.status = e.detail.value ? 0 : 1;\n\t\t},\n\t\t\n\t\t// 保存用户\n\t\tasync saveUser() {\n\t\t\t// 表单验证\n\t\t\tif (!this.isEdit && !this.userForm.username.trim()) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入用户名',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.isEdit && !this.userForm.password.trim()) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入密码',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 新建用户时验证密码强度\n\t\t\tif (!this.isEdit && this.userForm.password.trim()) {\n\t\t\t\tconst passwordValidation = passwordMod.validPwd(this.userForm.password);\n\t\t\t\tif (passwordValidation !== true) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: passwordValidation,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.userForm.role || this.userForm.role.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择角色',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.userForm.dcloud_appid || this.userForm.dcloud_appid.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择可登录应用',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.saving = true;\n\t\t\ttry {\n\t\t\t\tconst action = this.isEdit ? 'updateUser' : 'createUser';\n\t\t\t\tconst params = { ...this.userForm };\n\t\t\t\t\n\t\t\t\tif (this.isEdit) {\n\t\t\t\t\tparams.userId = this.userForm._id;\n\t\t\t\t\tdelete params._id;\n\t\t\t\t\tdelete params.password; // 编辑时不更新密码\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst result = await uniCloud.callFunction({\n\t\t\t\t\tname: 'user-management',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction,\n\t\t\t\t\t\tparams\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (result.result.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: this.isEdit ? '更新成功' : '创建成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.closeUserForm();\n\t\t\t\t\t// 只有创建成功后才重置表单\n\t\t\t\t\tif (!this.isEdit) {\n\t\t\t\t\t\tthis.resetUserForm();\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果涉及角色变更，清除角色缓存\n\t\t\t\t\tif (this.userForm.role && this.userForm.role.length > 0) {\n\t\t\t\t\t\tthis.clearCache('roles');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.loadUserList();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: result.result.message || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.saving = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 重置密码/设置账号密码\n\t\tresetPassword(user) {\n\t\t\tthis.resetPasswordUser = user;\n\t\t\tthis.newUsername = '';\n\t\t\tthis.newPassword = '';\n\t\t\tthis.confirmPassword = '';\n\t\t\t\n\t\t\tthis.$refs.resetPasswordPopup.open();\n\t\t},\n\t\t\n\t\t// 关闭重置密码弹窗\n\t\tcloseResetPassword() {\n\t\t\t// 只清空输入的密码和用户名，不清空resetPasswordUser\n\t\t\tthis.newUsername = '';\n\t\t\tthis.newPassword = '';\n\t\t\tthis.confirmPassword = '';\n\t\t\tthis.$refs.resetPasswordPopup.close();\n\t\t},\n\t\t\n\t\t// 确认重置密码/设置账号密码\n\t\tasync confirmResetPassword() {\n\t\t\t// 如果用户没有用户名，需要验证用户名输入\n\t\t\tif (!this.hasUsername) {\n\t\t\t\tif (!this.newUsername.trim()) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入用户账号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 用户名格式验证\n\t\t\t\tconst usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;\n\t\t\t\tif (!usernameRegex.test(this.newUsername.trim())) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '用户账号只能包含字母、数字、下划线，长度3-20位',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.newPassword.trim()) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入新密码',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (this.newPassword !== this.confirmPassword) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '两次密码输入不一致',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 使用配置文件中的密码强度验证\n\t\t\tconst passwordValidation = passwordMod.validPwd(this.newPassword);\n\t\t\tif (passwordValidation !== true) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: passwordValidation,\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 3000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 构建请求参数\n\t\t\t\tconst params = {\n\t\t\t\t\tuserId: this.resetPasswordUser._id,\n\t\t\t\t\tnewPassword: this.newPassword\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 如果用户没有用户名，添加用户名参数\n\t\t\t\tif (!this.hasUsername) {\n\t\t\t\t\tparams.username = this.newUsername.trim();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst result = await uniCloud.callFunction({\n\t\t\t\t\tname: 'user-management',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'setAccountPassword',\n\t\t\t\t\t\tparams: params\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (result.result.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: result.result.message,\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t// 操作成功后清空用户信息\n\t\t\t\t\tthis.resetPasswordUser = {};\n\t\t\t\t\tthis.closeResetPassword();\n\t\t\t\t\t// 重新加载用户列表以显示更新后的信息\n\t\t\t\t\tthis.loadUserList();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: result.result.message || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 删除用户\n\t\tdeleteUser(user) {\n\t\t\tif (user.role.includes('admin')) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '不能删除管理员用户',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tconst displayName = user.nickname || user.username || '该用户';\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '删除用户',\n\t\t\t\tcontent: '确定要删除用户\"' + displayName + '\"吗？此操作不可恢复',\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst result = await uniCloud.callFunction({\n\t\t\t\t\t\t\t\tname: 'user-management',\n\t\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\t\taction: 'deleteUser',\n\t\t\t\t\t\t\t\t\tparams: {\n\t\t\t\t\t\t\t\t\t\tuserId: user._id\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (result.result.code === 0) {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tthis.loadUserList();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: result.result.message || '删除失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 显示批量操作菜单\n\t\tshowBatchMenu() {\n\t\t\tif (this.selectedUsers.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先选择用户',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.$refs.batchMenuPopup.open();\n\t\t},\n\t\t\n\t\t// 关闭批量操作菜单\n\t\tcloseBatchMenu() {\n\t\t\tthis.$refs.batchMenuPopup.close();\n\t\t},\n\t\t\n\t\t// 批量更新状态\n\t\tasync batchUpdateStatus(status) {\n\t\t\tthis.closeBatchMenu();\n\t\t\t\n\t\t\tconst statusText = status === 0 ? '启用' : '禁用';\n\t\t\tuni.showModal({\n\t\t\t\ttitle: `批量${statusText}`,\n\t\t\t\tcontent: `确定要${statusText}所选的${this.selectedUsers.length}个用户吗？`,\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tawait this.performBatchOperation('updateStatus', { status });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 显示批量角色更新\n\t\tshowBatchRoleUpdate() {\n\t\t\tthis.closeBatchMenu();\n\t\t\tthis.batchRoleForm = [];\n\t\t\tthis.$refs.batchRolePopup.open();\n\t\t},\n\t\t\n\t\t// 关闭批量角色更新\n\t\tcloseBatchRoleUpdate() {\n\t\t\tthis.$refs.batchRolePopup.close();\n\t\t},\n\t\t\n\t\t// 确认批量角色更新\n\t\tasync confirmBatchRoleUpdate() {\n\t\t\tif (this.batchRoleForm.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择角色',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.closeBatchRoleUpdate();\n\t\t\t// 操作成功后会在performBatchOperation中重置数据\n\t\t\tawait this.performBatchOperation('updateRole', { role: this.batchRoleForm });\n\t\t},\n\t\t\n\t\t// 批量删除\n\t\tbatchDelete() {\n\t\t\tthis.closeBatchMenu();\n\t\t\t\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '批量删除',\n\t\t\t\tcontent: `确定要删除所选的${this.selectedUsers.length}个用户吗？此操作不可恢复`,\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tawait this.performBatchOperation('delete');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 执行批量操作\n\t\tasync performBatchOperation(operation, data = {}) {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '处理中...'\n\t\t\t});\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst result = await uniCloud.callFunction({\n\t\t\t\t\tname: 'user-management',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'batchOperation',\n\t\t\t\t\t\tparams: {\n\t\t\t\t\t\t\tuserIds: this.selectedUsers,\n\t\t\t\t\t\t\toperation,\n\t\t\t\t\t\t\tdata\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (result.result.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: result.result.message,\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 操作成功后重置相关数据\n\t\t\t\t\tif (operation === 'updateRole') {\n\t\t\t\t\t\tthis.batchRoleForm = [];\n\t\t\t\t\t\t// 批量角色操作后清除角色缓存\n\t\t\t\t\t\tthis.clearCache('roles');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.loadUserList();\n\t\t\t\t\t\n\t\t\t\t\t// 如果有错误信息，显示详情\n\t\t\t\t\tif (result.result.data.errors.length > 0) {\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '操作详情',\n\t\t\t\t\t\t\t\tcontent: result.result.data.errors.join('\\n'),\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: result.result.message || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('批量操作失败:', error);\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 显示角色变更\n\t\tshowRoleChange(user) {\n\t\t\tthis.quickRoleUser = user;\n\t\t\tthis.quickRoleForm = [...(user.role || [])];\n\t\t\tthis.$refs.quickRolePopup.open();\n\t\t},\n\t\t\n\t\t// 关闭快速角色变更\n\t\tcloseQuickRole() {\n\t\t\tthis.$refs.quickRolePopup.close();\n\t\t},\n\t\t\n\t\t// 确认快速角色变更\n\t\tasync confirmQuickRole() {\n\t\t\tif (this.quickRoleForm.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择角色',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst result = await uniCloud.callFunction({\n\t\t\t\t\tname: 'user-management',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'updateUser',\n\t\t\t\t\t\tparams: {\n\t\t\t\t\t\t\tuserId: this.quickRoleUser._id,\n\t\t\t\t\t\t\trole: this.quickRoleForm\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (result.result.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '角色更新成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.closeQuickRole();\n\t\t\t\t\t// 操作成功后重置数据\n\t\t\t\t\tthis.quickRoleUser = {};\n\t\t\t\t\tthis.quickRoleForm = [];\n\t\t\t\t\t// 角色更新后清除角色缓存\n\t\t\t\t\tthis.clearCache('roles');\n\t\t\t\t\tthis.loadUserList();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: result.result.message || '更新失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 重置所有筛选条件\n\t\tresetFilters() {\n\t\t\t// 重置滑动状态\n\t\t\tthis.resetAllSwipeStates();\n\t\t\t\n\t\t\tthis.searchKeyword = '';\n\t\t\tthis.filterRole = '';\n\t\t\tthis.filterStatus = '';\n\t\t\tthis.filterAnonymous = '';\n\t\t\tthis.sortField = '';\n\t\t\tthis.currentPage = 1;\n\t\t\tthis.selectedUsers = [];\n\t\t\t\n\t\t\t// 清除所有缓存，强制重新获取最新数据\n\t\t\tthis.clearAllCache();\n\t\t\t\n\t\t\t// 重新加载数据\n\t\t\tthis.loadUserList();\n\t\t\t\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '已重置筛选条件',\n\t\t\t\ticon: 'success',\n\t\t\t\tduration: 1500\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 格式化日期 - 统一24小时制格式\n\t\tformatDate(timestamp) {\n\t\t\tif (!timestamp) return '未知';\n\t\t\t\n\t\t\t// 处理时间戳，确保是数字类型\n\t\t\tconst time = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;\n\t\t\tif (isNaN(time)) return '未知';\n\t\t\t\n\t\t\tconst date = new Date(time);\n\t\t\t\n\t\t\t// 检查日期是否有效\n\t\t\tif (isNaN(date.getTime())) return '未知';\n\t\t\t\n\t\t\t// 统一格式：YYYY-MM-DD HH:mm:ss (24小时制)\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\n\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\n\t\t\tconst seconds = String(date.getSeconds()).padStart(2, '0');\n\t\t\t\n\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n\t\t},\n\t\t\n\t\t// 滑动操作相关方法\n\t\thandleTouchStart(e, userId) {\n\t\t\tif (!e.touches || !e.touches[0]) return;\n\t\t\tthis.touchStartX = e.touches[0].pageX;\n\t\t\tthis.touchStartY = e.touches[0].pageY;\n\t\t\tthis.currentSwipeUserId = userId;\n\t\t\t\n\t\t\t// 为当前用户初始化滑动状态\n\t\t\tif (!this.swipeStates[userId]) {\n\t\t\t\tthis.$set(this.swipeStates, userId, {\n\t\t\t\t\ttranslateX: 0,\n\t\t\t\t\ttransitioning: false\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 记录开始滑动时的初始位置\n\t\t\tthis.startTranslateX = this.swipeStates[userId].translateX;\n\t\t},\n\t\t\n\t\thandleTouchMove(e, userId) {\n\t\t\tif (this.currentSwipeUserId !== userId || !e.touches || !e.touches[0]) return;\n\t\t\t\n\t\t\tconst touchX = e.touches[0].pageX;\n\t\t\tconst touchY = e.touches[0].pageY;\n\t\t\tthis.handleSwipeMove(touchX, touchY, userId, e);\n\t\t},\n\t\t\n\t\thandleTouchEnd(e, userId) {\n\t\t\tif (this.currentSwipeUserId !== userId) return;\n\t\t\t\n\t\t\tthis.handleSwipeEnd(userId);\n\t\t},\n\t\t\n\t\t// H5鼠标事件处理\n\t\thandleMouseDown(e, userId) {\n\t\t\tthis.touchStartX = e.pageX;\n\t\t\tthis.touchStartY = e.pageY;\n\t\t\tthis.currentSwipeUserId = userId;\n\t\t\tthis.isDragging = true;\n\t\t\te.preventDefault(); // 防止文本选择\n\t\t\t\n\t\t\t// 为当前用户初始化滑动状态\n\t\t\tif (!this.swipeStates[userId]) {\n\t\t\t\tthis.$set(this.swipeStates, userId, {\n\t\t\t\t\ttranslateX: 0,\n\t\t\t\t\ttransitioning: false\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 记录开始滑动时的初始位置\n\t\t\tthis.startTranslateX = this.swipeStates[userId].translateX;\n\t\t},\n\t\t\n\t\thandleMouseMove(e, userId) {\n\t\t\tif (!this.isDragging || this.currentSwipeUserId !== userId) return;\n\t\t\t\n\t\t\tconst mouseX = e.pageX;\n\t\t\tconst mouseY = e.pageY;\n\t\t\tthis.handleSwipeMove(mouseX, mouseY, userId, e);\n\t\t},\n\t\t\n\t\thandleMouseUp(e, userId) {\n\t\t\tif (!this.isDragging) return;\n\t\t\t// 注意：这里不要检查userId，因为鼠标可能离开了原始区域\n\t\t\tthis.isDragging = false;\n\t\t\tif (this.currentSwipeUserId) {\n\t\t\t\tthis.handleSwipeEnd(this.currentSwipeUserId);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 通用滑动移动处理\n\t\thandleSwipeMove(currentX, currentY, userId, e) {\n\t\t\tconst deltaX = currentX - this.touchStartX;\n\t\t\tconst deltaY = currentY - this.touchStartY;\n\t\t\t\n\t\t\t// 降低水平滑动检测阈值，提高灵敏度\n\t\t\tif (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 5) {\n\t\t\t\t// 阻止页面滚动\n\t\t\t\tif (e.preventDefault) {\n\t\t\t\t\te.preventDefault();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 基于开始滑动时的位置计算新位置\n\t\t\t\tlet newTranslateX = this.startTranslateX + deltaX;\n\t\t\t\t\n\t\t\t\t\t\t\t// 限制滑动范围 - 根据平台动态调整\n\t\t\tlet maxLeftSwipe;\n\t\t\t// #ifdef H5\n\t\t\tmaxLeftSwipe = -320; // H5端更宽松的滑动距离\n\t\t\t// #endif\n\t\t\t// #ifndef H5\n\t\t\tmaxLeftSwipe = -265; // 其他平台的滑动距离\n\t\t\t// #endif\n\t\t\tconst maxRightSwipe = 0; // 最大向右滑动距离\n\t\t\t\tnewTranslateX = Math.max(maxLeftSwipe, Math.min(maxRightSwipe, newTranslateX));\n\t\t\t\t\n\t\t\t\tthis.$set(this.swipeStates, userId, {\n\t\t\t\t\ttranslateX: newTranslateX,\n\t\t\t\t\ttransitioning: false\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 通用滑动结束处理\n\t\thandleSwipeEnd(userId) {\n\t\t\tconst state = this.swipeStates[userId];\n\t\t\tif (!state) {\n\t\t\t\tthis.currentSwipeUserId = null;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 降低滑动阈值，提高灵敏度\n\t\t\tconst threshold = 50; // 滑动阈值（使用正数，更清晰）\n\t\t\tlet finalTranslateX;\n\t\t\t\n\t\t\t// 计算相对于开始位置的移动距离\n\t\t\tconst deltaFromStart = state.translateX - this.startTranslateX;\n\t\t\t\n\t\t\tif (deltaFromStart < -threshold) {\n\t\t\t\t// 向左滑动超过阈值，显示操作按钮\n\t\t\t\t// #ifdef H5\n\t\t\t\tfinalTranslateX = -320; // H5端更宽松的最终位置\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef H5\n\t\t\t\tfinalTranslateX = -265; // 其他平台的最终位置\n\t\t\t\t// #endif\n\t\t\t} else if (deltaFromStart > threshold) {\n\t\t\t\t// 向右滑动超过阈值，隐藏操作按钮\n\t\t\t\tfinalTranslateX = 0;\n\t\t\t} else {\n\t\t\t\t// 滑动距离不足，回到原状态\n\t\t\t\tfinalTranslateX = this.startTranslateX;\n\t\t\t}\n\t\t\t\n\t\t\t// 关闭其他用户的滑动状态\n\t\t\tObject.keys(this.swipeStates).forEach(id => {\n\t\t\t\tif (id !== userId) {\n\t\t\t\t\tthis.$set(this.swipeStates, id, {\n\t\t\t\t\t\ttranslateX: 0,\n\t\t\t\t\t\ttransitioning: true\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 设置当前用户的最终状态\n\t\t\tthis.$set(this.swipeStates, userId, {\n\t\t\t\ttranslateX: finalTranslateX,\n\t\t\t\ttransitioning: true\n\t\t\t});\n\t\t\t\n\t\t\tthis.currentSwipeUserId = null;\n\t\t\tthis.isDragging = false;\n\t\t},\n\t\t\n\t\t// 重置所有滑动状态\n\t\tresetAllSwipeStates() {\n\t\t\tObject.keys(this.swipeStates).forEach(id => {\n\t\t\t\tthis.$set(this.swipeStates, id, {\n\t\t\t\t\ttranslateX: 0,\n\t\t\t\t\ttransitioning: true\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 实时验证密码强度（可选功能）\n\t\tvalidatePasswordStrength(password) {\n\t\t\tif (!password) return '';\n\t\t\tconst validation = passwordMod.validPwd(password);\n\t\t\treturn validation === true ? '' : validation;\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.user-management {\n\tpadding: 16rpx;\n\tbackground: linear-gradient(180deg, #f2f2f7 0%, #ffffff 100%);\n\tmin-height: 100vh;\n}\n\n/* 全局隐藏滚动条 */\n:deep(.uni-scroll-view) {\n\t&::-webkit-scrollbar {\n\t\tdisplay: none;\n\t}\n\tscrollbar-width: none;\n}\n\n/* 隐藏弹窗内所有滚动条 */\n:deep(.uni-popup__wrapper) {\n\t* {\n\t\t&::-webkit-scrollbar {\n\t\t\tdisplay: none;\n\t\t}\n\t\tscrollbar-width: none;\n\t}\n}\n\n/* 顶部区域 */\n.header-section {\n\tbackground: white;\n\tborder-radius: 16rpx;\n\tpadding: 32rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n\tposition: relative;\n\tz-index: 5;\n\toverflow: visible;\n}\n\n.search-bar {\n\tmargin-bottom: 24rpx;\n\t\n\t:deep(.uni-searchbar) {\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 4rpx;\n\t\t\n\t\t.uni-searchbar__box {\n\t\t\tborder-radius: 12rpx;\n\t\t\theight: 76rpx;\n\t\t}\n\t\t\n\t\t.uni-searchbar__text-input {\n\t\t\tfont-size: 28rpx;\n\t\t\theight: 76rpx;\n\t\t\tline-height: 76rpx;\n\t\t}\n\t}\n}\n\n.filter-action-row {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\talign-items: flex-start;\n\tgap: 20rpx;\n\t\n\t// 响应式布局优化\n\t/* #ifdef MP-WEIXIN */\n\tflex-direction: column;\n\tgap: 24rpx;\n\t/* #endif */\n\t\n\t// H5环境自适应布局\n\t/* #ifdef H5 */\n\talign-items: flex-end;\n\t/* #endif */\n\t\n\t// 更精细的响应式控制\n\t@media (max-width: 750px) {\n\t\tflex-direction: column;\n\t\tgap: 24rpx;\n\t}\n\t\n\t.filter-group {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 12rpx;\n\t\tflex: 1;\n\t\tmin-width: 0;\n\t\t\n\t\t/* #ifdef MP-WEIXIN */\n\t\twidth: 100%;\n\t\t/* #endif */\n\t\t\n\t\t/* #ifdef H5 */\n\t\tmin-width: 360rpx;\n\t\t/* #endif */\n\t\t\n\t\t// 在较小屏幕上调整布局\n\t\t@media (max-width: 750px) {\n\t\t\twidth: 100%;\n\t\t\tmin-width: auto;\n\t\t}\n\t\t\n\t\t:deep(.uni-data-select) {\n\t\t\tflex: 0 0 auto;\n\t\t\tposition: relative;\n\t\t\tz-index: 10;\n\t\t\t\n\t\t\t/* H5端保持固定宽度 */\n\t\t\t/* #ifdef H5 */\n\t\t\tmin-width: 120rpx;\n\t\t\tmax-width: 160rpx;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t/* 小程序和小屏幕设备：每行显示2个 */\n\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\tflex: 0 0 calc(50% - 6rpx);\n\t\t\tmin-width: auto;\n\t\t\tmax-width: none;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t@media (max-width: 750px) {\n\t\t\t\t/* #ifndef MP-WEIXIN */\n\t\t\t\tflex: 0 0 calc(50% - 6rpx);\n\t\t\t\tmin-width: auto;\n\t\t\t\tmax-width: none;\n\t\t\t\t/* #endif */\n\t\t\t}\n\t\t\t\n\t\t\t.uni-select {\n\t\t\t\tbackground: #f8f9fa;\n\t\t\t\tborder: 2rpx solid #e9ecef;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tpadding: 16rpx;\n\t\t\t\theight: 76rpx;\n\t\t\t\tline-height: 44rpx;\n\t\t\t\ttransition: all 0.2s ease;\n\t\t\t\tposition: relative;\n\t\t\t\tz-index: 10;\n\t\t\t\t\n\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\theight: 72rpx;\n\t\t\t\tline-height: 40rpx;\n\t\t\t\tpadding: 16rpx 14rpx;\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\t/* #endif */\n\t\t\t\t\n\t\t\t\t&:focus, &:active {\n\t\t\t\t\tborder-color: #007aff;\n\t\t\t\t\tbackground: #ffffff;\n\t\t\t\t\tz-index: 100;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 下拉选项列表层级\n\t\t\t.uni-select__selector {\n\t\t\t\tz-index: 999 !important;\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 100%;\n\t\t\t\tleft: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbackground: white;\n\t\t\t\tborder: 2rpx solid #e9ecef;\n\t\t\t\tborder-top: none;\n\t\t\t\tborder-radius: 0 0 12rpx 12rpx;\n\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n\t\t\t\tmax-height: 300rpx;\n\t\t\t\toverflow-y: auto;\n\t\t\t}\n\t\t\t\n\t\t\t.uni-select__selector-item {\n\t\t\t\tpadding: 16rpx 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t\t\t\tbackground: white;\n\t\t\t\t\n\t\t\t\t&:hover {\n\t\t\t\t\tbackground: #f8f9fa;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&:last-child {\n\t\t\t\t\tborder-bottom: none;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.uni-select__input-text {\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\toverflow: hidden;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\t\n\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\t/* #endif */\n\t\t\t}\n\t\t\t\n\t\t\t.uni-select__input-placeholder {\n\t\t\t\tcolor: #999;\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\t\n\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\tfont-size: 18rpx;\n\t\t\t\t/* #endif */\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.action-group {\n\t\tdisplay: flex;\n\t\tgap: 12rpx;\n\t\tflex-shrink: 0;\n\t\talign-items: center;\n\t\t\n\t\t/* #ifdef MP-WEIXIN */\n\t\twidth: 100%;\n\t\tjustify-content: space-between;\n\t\tflex-wrap: nowrap;\n\t\t/* #endif */\n\t\t\n\t\t/* #ifdef H5 */\n\t\tflex-wrap: wrap;\n\t\tmin-width: 320rpx;\n\t\t/* #endif */\n\t\t\n\t\t.add-btn, .batch-btn, .reset-btn {\n\t\t\tborder: none;\n\t\t\tborder-radius: 12rpx;\n\t\t\tfont-size: 26rpx;\n\t\t\tfont-weight: 600;\n\t\t\twhite-space: nowrap;\n\t\t\ttransition: all 0.3s ease;\n\t\t\theight: 76rpx;\n\t\t\tline-height: 76rpx;\n\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t\t\tpadding: 0 32rpx;\n\t\t\tmin-width: 120rpx;\n\t\t\t\n\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\tflex: 1;\n\t\t\tpadding: 0 16rpx;\n\t\t\theight: 72rpx;\n\t\t\tline-height: 72rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tmin-width: 80rpx;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t&:active {\n\t\t\t\ttransform: translateY(2rpx);\n\t\t\t\tbox-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);\n\t\t\t}\n\t\t}\n\t\t\n\t\t.add-btn {\n\t\t\tbackground: linear-gradient(135deg, #007AFF, #5AC8FA);\n\t\t\tcolor: white;\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.4);\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\tbackground: linear-gradient(135deg, #0056CC, #4A9FE7);\n\t\t\t}\n\t\t}\n\t\t\n\t\t.batch-btn {\n\t\t\tbackground: linear-gradient(135deg, #34C759, #30D158);\n\t\t\tcolor: white;\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.4);\n\t\t\t\n\t\t\t&.disabled {\n\t\t\t\tbackground: #E5E5EA;\n\t\t\t\tcolor: #8E8E93;\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n\t\t\t}\n\t\t\t\n\t\t\t&:active:not(.disabled) {\n\t\t\t\ttransform: translateY(2rpx);\n\t\t\t\tbox-shadow: 0 1rpx 4rpx rgba(52, 199, 89, 0.6);\n\t\t\t}\n\t\t\t\n\t\t\t&:hover:not(.disabled) {\n\t\t\t\tbackground: linear-gradient(135deg, #28A745, #2DD653);\n\t\t\t}\n\t\t}\n\t\t\n\t\t.reset-btn {\n\t\t\tbackground: linear-gradient(135deg, #FF9500, #FFAD33);\n\t\t\tcolor: white;\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.4);\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\tbackground: linear-gradient(135deg, #E6851A, #FF9F1A);\n\t\t\t}\n\t\t\t\n\t\t\t&:active {\n\t\t\t\ttransform: translateY(2rpx);\n\t\t\t\tbox-shadow: 0 1rpx 4rpx rgba(255, 149, 0, 0.6);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* 用户列表 */\n.user-list {\n\tbackground: white;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n}\n\n.list-header {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 16rpx;\n\tpadding: 20rpx 24rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n\tbackground: linear-gradient(135deg, #f8f9fa, #ffffff);\n\t\n\t.select-all-wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8rpx;\n\t\tcursor: pointer;\n\t\t\n\t\tcheckbox {\n\t\t\ttransform: scale(1.1);\n\t\t}\n\t\t\n\t\ttext {\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #333;\n\t\t}\n\t}\n\t\n\t.header-text {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #1d1d1f;\n\t}\n\t\n\t.select-tip {\n\t\tfont-size: 24rpx;\n\t\tcolor: #007aff;\n\t\tmargin-left: auto;\n\t\tbackground: #e6f2ff;\n\t\tpadding: 6rpx 12rpx;\n\t\tborder-radius: 12rpx;\n\t}\n}\n\n.loading {\n\tpadding: 20rpx 0;\n\t\n\t.skeleton-wrapper {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 16rpx;\n\t}\n\t\n\t.skeleton-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 24rpx;\n\t\tpadding: 32rpx;\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n\t}\n\t\n\t.skeleton-avatar {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tborder-radius: 50%;\n\t\tbackground: #f0f0f0;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t.skeleton-content {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 12rpx;\n\t}\n\t\n\t.skeleton-meta {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 8rpx;\n\t\talign-items: flex-end;\n\t}\n\t\n\t.skeleton-line {\n\t\tbackground: #f0f0f0;\n\t\tborder-radius: 8rpx;\n\t\theight: 32rpx;\n\t}\n\t\n\t.skeleton-name {\n\t\twidth: 160rpx;\n\t}\n\t\n\t.skeleton-role {\n\t\twidth: 120rpx;\n\t\theight: 24rpx;\n\t}\n\t\n\t.skeleton-status {\n\t\twidth: 60rpx;\n\t\theight: 24rpx;\n\t}\n\t\n\t.skeleton-time {\n\t\twidth: 200rpx;\n\t\theight: 20rpx;\n\t}\n\t\n\t/* 动画效果 */\n\t.skeleton-animate {\n\t\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n\t\tbackground-size: 200% 100%;\n\t\tanimation: skeleton-loading 1.5s ease-in-out infinite;\n\t}\n\t\n\t@keyframes skeleton-loading {\n\t\t0% {\n\t\t\tbackground-position: -200% 0;\n\t\t}\n\t\t100% {\n\t\t\tbackground-position: 200% 0;\n\t\t}\n\t}\n}\n\n.empty {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 60rpx;\n\tcolor: #8e8e93;\n\t\n\ttext {\n\t\tmargin-top: 16rpx;\n\t\tfont-size: 26rpx;\n\t}\n}\n\n.user-items {\n\t.user-item-wrapper {\n\t\tposition: relative;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t\toverflow: hidden;\n\t\t\n\t\t&:last-child {\n\t\t\tborder-bottom: none;\n\t\t}\n\t}\n\t\n\t.user-item {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tgap: 16rpx;\n\t\tpadding: 20rpx 24rpx;\n\t\tbackground: white;\n\t\ttransition: background-color 0.2s ease;\n\t\t\n\t\t&:active {\n\t\t\tbackground: #f8f9fa;\n\t\t}\n\t\t\n\t\t.user-checkbox-wrapper {\n\t\t\tmargin-right: 8rpx;\n\t\t\tcursor: pointer;\n\t\t\tpadding-top: 8rpx; /* 对齐用户名 */\n\t\t\t\n\t\t\tcheckbox {\n\t\t\t\ttransform: scale(1.1);\n\t\t\t}\n\t\t}\n\t\t\n\t\t.user-info-fixed {\n\t\t\tflex: 1;\n\t\t\tmin-width: 0;\n\t\t\t\n\t\t\t.user-basic-info {\n\t\t\t\t.username {\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #1d1d1f;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.user-meta {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tgap: 6rpx;\n\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\tcolor: #c7c7cc;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.register-date {\n\t\t\t\t\tcolor: #999;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.last-login {\n\t\t\t\t\tcolor: #bbb;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.user-right-container {\n\t\t\tposition: relative;\n\t\t\tflex-shrink: 0;\n\t\t\toverflow: hidden;\n\t\t\twidth: 265rpx; /* 增加5rpx宽度，从260rpx调整为265rpx */\n\t\t\tmin-height: 120rpx; /* 设置最小高度，让容器能显示完整内容 */\n\t\t\talign-self: stretch; /* 让容器填满父容器的高度 */\n\t\t\t\n\t\t\t/* H5端增加宽度，电脑屏幕更宽松 */\n\t\t\t/* #ifdef H5 */\n\t\t\twidth: 320rpx;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t/* 确保整个区域都能响应触摸事件 */\n\t\t\ttouch-action: pan-x; /* 只允许水平滑动 */\n\t\t\tuser-select: none;\n\t\t\t-webkit-user-select: none;\n\t\t\t-moz-user-select: none;\n\t\t\t-ms-user-select: none;\n\t\t}\n\t}\n\t\n\t/* 可滑动的右侧内容 - 状态、角色、齿轮 */\n\t.user-right-swipable {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-end; /* 确保内容靠右对齐 */\n\t\tpadding: 8rpx 52rpx 8rpx 16rpx; /* 右侧留出齿轮按钮的空间(36rpx + 16rpx) */\n\t\tbackground: white;\n\t\tz-index: 2;\n\t\tuser-select: none;\n\t\t-webkit-user-select: none;\n\t\t-moz-user-select: none;\n\t\t-ms-user-select: none;\n\t\toverflow: hidden; /* 隐藏超出容器的内容 */\n\t\t\n\t\t/* 小程序兼容性 */\n\t\t/* #ifdef MP-WEIXIN */\n\t\ttouch-action: pan-x; /* 只允许水平滑动 */\n\t\t/* #endif */\n\t\t\n\t\t/* 左侧内容区域 - 状态和角色 */\n\t\t.left-content {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tgap: 6rpx;\n\t\t\tflex: 1;\n\t\t\tmin-width: 0;\n\t\t\tjustify-content: flex-end; /* 内容靠右对齐 */\n\t\t\toverflow: hidden; /* 隐藏超出的内容 */\n\t\t\twhite-space: nowrap; /* 强制不换行 */\n\t\t}\n\t\t\n\t\t.status-badge {\n\t\t\tpadding: 4rpx 8rpx;\n\t\t\tborder-radius: 8rpx;\n\t\t\tfont-size: 18rpx;\n\t\t\tfont-weight: 500;\n\t\t\tflex-shrink: 0;\n\t\t\t\n\t\t\t&.active {\n\t\t\t\tbackground: #30d158;\n\t\t\t\tcolor: white;\n\t\t\t}\n\t\t\t\n\t\t\t&.inactive {\n\t\t\t\tbackground: #ff3b30;\n\t\t\t\tcolor: white;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.user-roles {\n\t\t\tdisplay: flex;\n\t\t\tgap: 4rpx;\n\t\t\tflex-wrap: nowrap; /* 禁止换行 */\n\t\t\tflex-shrink: 0;\n\t\t\t/* 移除max-width限制，让内容自然延伸 */\n\t\t\tjustify-content: flex-end; /* 角色标签也靠右对齐 */\n\t\t\toverflow: hidden; /* 隐藏超出的内容 */\n\t\t\t\n\t\t\t.role-tag {\n\t\t\t\tpadding: 4rpx 8rpx;\n\t\t\t\tbackground: #007aff;\n\t\t\t\tcolor: white;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tfont-size: 18rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\tflex-shrink: 0; /* 防止角色标签被压缩 */\n\t\t\t}\n\t\t}\n\t\t\n\t\t/* 齿轮按钮固定在最右侧 */\n\t\t.change-role-btn {\n\t\t\tpadding: 0;\n\t\t\tbackground: #f5f5f5;\n\t\t\tborder: none;\n\t\t\tborder-radius: 50%;\n\t\t\twidth: 36rpx;\n\t\t\theight: 36rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tflex-shrink: 0;\n\t\t\ttransition: all 0.2s ease;\n\t\t\tbox-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);\n\t\t\tposition: absolute; /* 绝对定位固定在右侧 */\n\t\t\tright: 8rpx; /* 距离右边8rpx */\n\t\t\ttop: 50%;\n\t\t\ttransform: translateY(-50%); /* 垂直居中 */\n\t\t\t\n\t\t\t&:active {\n\t\t\t\tbackground: #e8e8e8;\n\t\t\t\ttransform: translateY(-50%) scale(0.9);\n\t\t\t\tbox-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.15);\n\t\t\t}\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\tbackground: #eaeaea;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t/* 操作按钮区域 - 默认隐藏在右侧外部 */\n\t.user-actions-buttons {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0; /* 改为从左边开始，通过滑动来显示 */\n\t\twidth: 265rpx; /* 增加5rpx宽度，与容器宽度保持一致 */\n\t\tbottom: 0;\n\t\tdisplay: flex;\n\t\tz-index: 1;\n\t\t\n\t\t/* H5端增加宽度 */\n\t\t/* #ifdef H5 */\n\t\twidth: 320rpx;\n\t\t/* #endif */\n\t\t\n\t\tbutton {\n\t\t\tflex: 1;\n\t\t\tborder: none;\n\t\t\tborder-radius: 0;\n\t\t\tcolor: white;\n\t\t\ttransition: all 0.2s ease;\n\t\t\tmin-width: 80rpx;\n\t\t\tpadding: 0;\n\t\t\tposition: relative;\n\t\t\toverflow: hidden;\n\t\t\t\n\t\t\t/* 按钮内容容器 */\n\t\t\t.btn-content {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\theight: 100%;\n\t\t\t\twidth: 100%;\n\t\t\t\tgap: 6rpx;\n\t\t\t\tpadding: 12rpx 4rpx;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\n\t\t\t\t/* H5平台 - 恢复到原始居中效果 */\n\t\t\t\t/* #ifdef H5 */\n\t\t\t\tgap: 8rpx;\n\t\t\t\tpadding: 16rpx 8rpx;\n\t\t\t\t/* #endif */\n\t\t\t\t\n\t\t\t\t/* APP平台 - 真机适配 */\n\t\t\t\t/* #ifdef APP-PLUS */\n\t\t\t\tgap: 6rpx;\n\t\t\t\tpadding: 14rpx 6rpx;\n\t\t\t\t/* #endif */\n\t\t\t\t\n\t\t\t\t/* 小程序平台 - 开发者工具适配 */\n\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\tgap: 2rpx;\n\t\t\t\tpadding: 12rpx 4rpx;\n\t\t\t\t/* #endif */\n\t\t\t\t\n\t\t\t\t/* 图标样式优化 */\n\t\t\t\t:deep(.uni-icons) {\n\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\tline-height: 1 !important;\n\t\t\t\t\theight: auto;\n\t\t\t\t\t\n\t\t\t\t\t/* H5平台图标 */\n\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\t/* #endif */\n\t\t\t\t\t\n\t\t\t\t\t/* APP平台图标 */\n\t\t\t\t\t/* #ifdef APP-PLUS */\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\t/* #endif */\n\t\t\t\t\t\n\t\t\t\t\t/* 小程序平台图标 - 开发者工具修正 */\n\t\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\t\ttransform: translateY(1rpx);\n\t\t\t\t\t/* #endif */\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tline-height: 1;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\n\t\t\t\t\t/* H5平台文字 - 恢复原始效果 */\n\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\tline-height: 1.1;\n\t\t\t\t\ttransform: none;\n\t\t\t\t\t/* #endif */\n\t\t\t\t\t\n\t\t\t\t\t/* APP平台文字 - 真机适配 */\n\t\t\t\t\t/* #ifdef APP-PLUS */\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tline-height: 1;\n\t\t\t\t\ttransform: none;\n\t\t\t\t\t/* #endif */\n\t\t\t\t\t\n\t\t\t\t\t/* 小程序平台文字 - 开发者工具修正 */\n\t\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\t\tfont-size: 18rpx;\n\t\t\t\t\tline-height: 1;\n\t\t\t\t\ttransform: translateY(2rpx);\n\t\t\t\t\t/* #endif */\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.edit-btn {\n\t\t\t\tbackground: linear-gradient(135deg, #007aff, #4A9FE7);\n\t\t\t\t\n\t\t\t\t&:active {\n\t\t\t\t\tbackground: linear-gradient(135deg, #0056cc, #3A8FD7);\n\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.password-btn {\n\t\t\t\tbackground: linear-gradient(135deg, #8E44AD, #9B59B6);\n\t\t\t\t\n\t\t\t\t&:active {\n\t\t\t\t\tbackground: linear-gradient(135deg, #7D3C98, #8B4BA6);\n\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.delete-btn {\n\t\t\t\tbackground: linear-gradient(135deg, #E74C3C, #EC7063);\n\t\t\t\t\n\t\t\t\t&:disabled {\n\t\t\t\t\tbackground: #E5E5EA;\n\t\t\t\t\tcolor: #8E8E93;\n\t\t\t\t\topacity: 0.6;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&:active:not(:disabled) {\n\t\t\t\t\tbackground: linear-gradient(135deg, #C0392B, #DC5F53);\n\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* 分页 */\n.pagination {\n\tmargin-top: 20rpx;\n\tpadding: 0 16rpx;\n}\n\n/* 弹窗样式 */\n.user-form-popup, .batch-role-popup, .reset-password-popup, .quick-role-popup {\n\twidth: 90vw;\n\tmax-width: 700rpx;\n\tbackground: white;\n\tborder-radius: 24rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);\n}\n\n/* #ifdef H5 */\n/* H5 端的响应式弹窗样式 */\n@media screen and (min-width: 1200px) {\n    .user-form-popup, .batch-role-popup, .reset-password-popup, .quick-role-popup {\n        max-width: 600px;\n        width: 80vw;\n    }\n}\n\n@media screen and (min-width: 992px) and (max-width: 1199px) {\n    .user-form-popup, .batch-role-popup, .reset-password-popup, .quick-role-popup {\n        max-width: 560px;\n        width: 85vw;\n    }\n}\n\n@media screen and (min-width: 768px) and (max-width: 991px) {\n    .user-form-popup, .batch-role-popup, .reset-password-popup, .quick-role-popup {\n        max-width: 500px;\n        width: 88vw;\n    }\n}\n\n@media screen and (min-width: 576px) and (max-width: 767px) {\n    .user-form-popup, .batch-role-popup, .reset-password-popup, .quick-role-popup {\n        max-width: 450px;\n        width: 90vw;\n    }\n}\n\n@media screen and (max-width: 575px) {\n    .user-form-popup, .batch-role-popup, .reset-password-popup, .quick-role-popup {\n        width: 92vw;\n        max-width: none;\n    }\n}\n/* #endif */\n\n/* #ifdef MP-WEIXIN */\n/* 微信小程序端弹窗样式优化 */\n.user-form-popup, .batch-role-popup, .reset-password-popup, .quick-role-popup {\n\twidth: 85vw;\n\tmax-width: 680rpx;\n}\n/* #endif */\n\n.popup-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 40rpx 40rpx 30rpx;\n\tbackground: linear-gradient(135deg, #f8f9fa, #ffffff);\n\tborder-bottom: 1rpx solid #e9ecef;\n\t\n\t.popup-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tflex: 1;\n\t\tmargin-right: 20rpx;\n\t\t\n\t\t/* #ifdef H5 */\n\t\tfont-size: 18px;\n\t\t/* #endif */\n\t\t\n\t\t/* #ifdef MP-WEIXIN */\n\t\tfont-size: 30rpx;\n\t\t/* #endif */\n\t}\n\t\n\t.close-btn {\n\t\twidth: 50rpx;\n\t\theight: 50rpx;\n\t\tmin-width: 50rpx;\n\t\tborder: none;\n\t\tbackground: #f0f0f0;\n\t\tborder-radius: 50%;\n\t\tfont-size: 32rpx;\n\t\tcolor: #666;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.2s ease;\n\t\tflex-shrink: 0;\n\t\t\n\t\t&:active {\n\t\t\tbackground: #e0e0e0;\n\t\t\ttransform: scale(0.9);\n\t\t}\n\t}\n}\n\n.popup-content {\n\tpadding: 40rpx;\n\tmax-height: 60vh;\n\toverflow-y: auto;\n\t\n\t/* #ifdef MP-WEIXIN */\n\t/* 微信小程序环境下隐藏滚动条 */\n\t&::-webkit-scrollbar {\n\t\tdisplay: none;\n\t\twidth: 0;\n\t\theight: 0;\n\t\tbackground: transparent;\n\t}\n\t\n\tscrollbar-width: none; /* Firefox */\n\t-ms-overflow-style: none; /* IE and Edge */\n\t/* #endif */\n\t\n\t/* #ifndef MP-WEIXIN */\n\t/* 非微信小程序环境下显示滚动条 */\n\t&::-webkit-scrollbar {\n\t\twidth: 6px;\n\t\theight: 6px;\n\t}\n\t\n\t&::-webkit-scrollbar-thumb {\n\t\tbackground: rgba(0, 0, 0, 0.2);\n\t\tborder-radius: 3px;\n\t}\n\t\n\t&::-webkit-scrollbar-thumb:hover {\n\t\tbackground: rgba(0, 0, 0, 0.3);\n\t}\n\t\n\tscrollbar-width: thin;\n\tscrollbar-color: rgba(0, 0, 0, 0.2) transparent;\n\t/* #endif */\n\t\n\t.form-item {\n\t\tmargin-bottom: 36rpx;\n\t\t\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\t\t\n\t\t.label {\n\t\t\tdisplay: block;\n\t\t\tmargin-bottom: 16rpx;\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #333;\n\t\t\tfont-weight: 500;\n\t\t\t\n\t\t\t/* #ifdef H5 */\n\t\t\tfont-size: 14px;\n\t\t\tmargin-bottom: 8px;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\tfont-size: 24rpx;\n\t\tmargin-bottom: 12rpx;\n\t\t/* #endif */\n\t}\n\t\n\t.required-star {\n\t\tcolor: #FF4757;\n\t\tfont-weight: bold;\n\t\tfont-size: 1.2em;\n\t\tmargin-left: 4rpx;\n\t\t\n\t\t/* #ifdef H5 */\n\t\tcolor: #FF3B3B;\n\t\tfont-size: 16px;\n\t\tmargin-left: 2px;\n\t\t/* #endif */\n\t\t\n\t\t/* #ifdef MP-WEIXIN */\n\t\tcolor: #FF4757;\n\t\tfont-size: 28rpx;\n\t\tmargin-left: 6rpx;\n\t\t/* #endif */\n\t}\n\t\t\n\t\t.input {\n\t\t\twidth: 100%;\n\t\t\tpadding: 0 20rpx;\n\t\t\tborder: 2rpx solid #e1e5e9;\n\t\t\tborder-radius: 12rpx;\n\t\t\tfont-size: 26rpx;\n\t\t\tbackground: #fafbfc;\n\t\t\ttransition: all 0.2s ease;\n\t\t\tbox-sizing: border-box;\n\t\t\theight: 80rpx;\n\t\t\t\n\t\t\t/* 关键：使用text-align和line-height的组合 */\n\t\t\tline-height: 80rpx;\n\t\t\tvertical-align: middle;\n\t\t\t\n\t\t\t/* 重置可能冲突的默认样式 */\n\t\t\tmargin: 0;\n\t\t\toutline: none;\n\t\t\ttext-indent: 0;\n\t\t\t\n\t\t\t/* #ifdef H5 */\n\t\t\theight: 40px;\n\t\t\tline-height: 40px;\n\t\t\tfont-size: 14px;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\theight: 70rpx;\n\t\t\tline-height: 70rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t/* 确保placeholder和输入文字使用相同的基线 */\n\t\t\t&::placeholder {\n\t\t\t\tline-height: 80rpx;\n\t\t\t\tvertical-align: middle;\n\t\t\t\tcolor: #999;\n\t\t\t\t\n\t\t\t\t/* #ifdef H5 */\n\t\t\t\tline-height: 40px;\n\t\t\t\tfont-size: 14px;\n\t\t\t\t/* #endif */\n\t\t\t\t\n\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\tline-height: 70rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\t/* #endif */\n\t\t\t}\n\t\t\t\n\t\t\t&:focus {\n\t\t\t\tborder-color: #007aff;\n\t\t\t\tbackground: #ffffff;\n\t\t\t\tbox-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);\n\t\t\t}\n\t\t\t\n\t\t\t&:disabled {\n\t\t\t\tbackground: #f5f5f5;\n\t\t\t\tcolor: #999;\n\t\t\t\tborder-color: #e1e5e9;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.password-hint {\n\t\t\tdisplay: block;\n\t\t\tmargin-top: 8rpx;\n\t\t\tfont-size: 20rpx;\n\t\t\tcolor: #8e8e93;\n\t\t\tline-height: 1.4;\n\t\t\t\n\t\t\t/* #ifdef H5 */\n\t\t\tfont-size: 11px;\n\t\t\tmargin-top: 4px;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\tfont-size: 18rpx;\n\t\t\tmargin-top: 6rpx;\n\t\t\t/* #endif */\n\t\t}\n\t\t\n\t\t/* 状态开关样式 */\n\t\t&.status-switch {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tgap: 20rpx;\n\t\t\tpadding: 20rpx 0;\n\t\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t\t\t\n\t\t\t.label {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\tflex-shrink: 0;\n\t\t\t}\n\t\t\t\n\t\t\t.status-display {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tgap: 20rpx;\n\t\t\t\tflex: 1;\n\t\t\t\t\n\t\t\t\t.status-badge {\n\t\t\t\t\tpadding: 8rpx 16rpx;\n\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\n\t\t\t\t\t&.active {\n\t\t\t\t\t\tbackground: #e8f5e8;\n\t\t\t\t\t\tcolor: #52c41a;\n\t\t\t\t\t\tborder: 1rpx solid #b7eb8f;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.inactive {\n\t\t\t\t\t\tbackground: #fff2f0;\n\t\t\t\t\t\tcolor: #ff4d4f;\n\t\t\t\t\t\tborder: 1rpx solid #ffccc7;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\t}\n\t\t\n\t\t.user-info-section {\n\t\t\tmargin-bottom: 32rpx;\n\t\t\t\n\t\t\t/* #ifdef H5 */\n\t\t\tmargin-bottom: 16px;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\tmargin-bottom: 28rpx;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t.user-basic-info {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tgap: 12rpx;\n\t\t\t\tpadding: 20rpx 24rpx;\n\t\t\t\tbackground: #f8fafc;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tborder-left: 4rpx solid #007aff;\n\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\t\n\t\t\t\t/* #ifdef H5 */\n\t\t\t\tpadding: 10px 12px;\n\t\t\t\tgap: 6px;\n\t\t\t\tborder-radius: 6px;\n\t\t\t\tmargin-bottom: 8px;\n\t\t\t\tborder-left-width: 2px;\n\t\t\t\t/* #endif */\n\t\t\t\t\n\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\tpadding: 16rpx 20rpx;\n\t\t\t\tgap: 10rpx;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t/* #endif */\n\t\t\t\t\n\t\t\t\t.user-name {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #1e293b;\n\t\t\t\t\t\n\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\tfont-size: 15px;\n\t\t\t\t\t/* #endif */\n\t\t\t\t\t\n\t\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t/* #endif */\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.account-tip {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tgap: 8rpx;\n\t\t\t\tpadding: 16rpx 24rpx;\n\t\t\t\tbackground: #e0f2fe;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\n\t\t\t\t/* #ifdef H5 */\n\t\t\t\tpadding: 8px 12px;\n\t\t\t\tgap: 4px;\n\t\t\t\tborder-radius: 4px;\n\t\t\t\t/* #endif */\n\t\t\t\t\n\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\tpadding: 12rpx 20rpx;\n\t\t\t\tgap: 6rpx;\n\t\t\t\tborder-radius: 6rpx;\n\t\t\t\t/* #endif */\n\t\t\t\t\n\t\t\t\t.tip-text {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #0369a1;\n\t\t\t\t\t\n\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\t/* #endif */\n\t\t\t\t\t\n\t\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t/* #endif */\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.account-name {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #0c4a6e;\n\t\t\t\t\tbackground: white;\n\t\t\t\t\tpadding: 4rpx 8rpx;\n\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\t\n\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\tpadding: 2px 4px;\n\t\t\t\t\tborder-radius: 2px;\n\t\t\t\t\t/* #endif */\n\t\t\t\t\t\n\t\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\tpadding: 2rpx 6rpx;\n\t\t\t\t\tborder-radius: 2rpx;\n\t\t\t\t\t/* #endif */\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.batch-operation-tip {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tgap: 12rpx;\n\t\t\tpadding: 20rpx 24rpx;\n\t\t\tbackground: #fff7ed;\n\t\t\tborder-radius: 12rpx;\n\t\t\tborder-left: 4rpx solid #ff9500;\n\t\t\tmargin-bottom: 32rpx;\n\t\t\t\n\t\t\t/* #ifdef H5 */\n\t\t\tpadding: 10px 12px;\n\t\t\tgap: 6px;\n\t\t\tborder-radius: 6px;\n\t\t\tmargin-bottom: 16px;\n\t\t\tborder-left-width: 2px;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\tpadding: 16rpx 20rpx;\n\t\t\tgap: 10rpx;\n\t\t\tborder-radius: 10rpx;\n\t\t\tmargin-bottom: 28rpx;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t.operation-text {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #ea580c;\n\t\t\t\t\n\t\t\t\t/* #ifdef H5 */\n\t\t\t\tfont-size: 15px;\n\t\t\t\t/* #endif */\n\t\t\t\t\n\t\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\t/* #endif */\n\t\t\t}\n\t\t}\n\t\t\n\t\t.tip {\n\t\t\tdisplay: block;\n\t\t\tmargin-bottom: 24rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #666;\n\t\t\tbackground: #f8f9fa;\n\t\t\tpadding: 16rpx 20rpx;\n\t\t\tborder-radius: 8rpx;\n\t\t\tborder-left: 4rpx solid #007aff;\n\t\t\n\t\t/* #ifdef H5 */\n\t\tfont-size: 13px;\n\t\tmargin-bottom: 12px;\n\t\tpadding: 8px 10px;\n\t\tborder-radius: 4px;\n\t\tborder-left-width: 2px;\n\t\t/* #endif */\n\t\t\n\t\t/* #ifdef MP-WEIXIN */\n\t\tfont-size: 22rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tpadding: 12rpx 16rpx;\n\t\t/* #endif */\n\t}\n\t\n\t/* 角色和应用选择横向布局样式 */\n\t:deep(.uni-data-checkbox) {\n\t\t.checklist-group {\n\t\t\tdisplay: flex !important;\n\t\t\tflex-wrap: wrap !important;\n\t\t\tgap: 16rpx !important;\n\t\t\t\n\t\t\t.checklist-box {\n\t\t\t\tflex: none !important;\n\t\t\t\tmargin-right: 0 !important;\n\t\t\t\tmargin-bottom: 0 !important;\n\t\t\t}\n\t\t\t\n\t\t\t.checklist-content {\n\t\t\t\tdisplay: flex !important;\n\t\t\t\talign-items: center !important;\n\t\t\t\tpadding: 12rpx 20rpx !important;\n\t\t\t\tborder: 2rpx solid #e1e5e9 !important;\n\t\t\t\tborder-radius: 24rpx !important;\n\t\t\t\tbackground: #fafbfc !important;\n\t\t\t\tfont-size: 24rpx !important;\n\t\t\t\tcolor: #666 !important;\n\t\t\t\ttransition: all 0.2s ease !important;\n\t\t\t\twhite-space: nowrap !important;\n\t\t\t\t\n\t\t\t\t&.is--checked {\n\t\t\t\t\tbackground: #e6f7ff !important;\n\t\t\t\t\tborder-color: #1890ff !important;\n\t\t\t\t\tcolor: #1890ff !important;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.checkbox__inner {\n\t\t\t\tdisplay: none !important; /* 隐藏原始复选框 */\n\t\t\t}\n\t\t\t\n\t\t\t.checklist-text {\n\t\t\t\tmargin-left: 0 !important;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.popup-footer {\n\tdisplay: flex;\n\tgap: 16rpx;\n\tpadding: 20rpx 40rpx 30rpx;\n\tborder-top: 1rpx solid #f0f0f0;\n\tbackground: #fafbfc;\n\t\n\tbutton {\n\t\tflex: 1;\n\t\tpadding: 16rpx;\n\t\tborder: none;\n\t\tborder-radius: 10rpx;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 500;\n\t\ttransition: all 0.2s ease;\n\t\t\n\t\t/* #ifdef H5 */\n\t\tpadding: 8px;\n\t\tborder-radius: 5px;\n\t\tfont-size: 14px;\n\t\t/* #endif */\n\t\t\n\t\t/* #ifdef MP-WEIXIN */\n\t\tpadding: 12rpx;\n\t\tfont-size: 22rpx;\n\t\t/* #endif */\n\t\t\n\t\t&:active {\n\t\t\ttransform: translateY(1rpx);\n\t\t}\n\t}\n\t\n\t.cancel-btn {\n\t\tbackground: #f5f5f5;\n\t\tcolor: #666;\n\t\tborder: 1rpx solid #e1e5e9;\n\t\t\n\t\t&:active {\n\t\t\tbackground: #e8e8e8;\n\t\t}\n\t}\n\t\n\t.confirm-btn {\n\t\tbackground: linear-gradient(135deg, #007aff, #4096ff);\n\t\tcolor: white;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);\n\t\t\n\t\t&:active {\n\t\t\tbackground: linear-gradient(135deg, #0056cc, #3a8bff);\n\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.4);\n\t\t}\n\t}\n}\n\n/* 批量操作菜单 */\n.batch-menu {\n\tbackground: white;\n\tborder-radius: 20rpx 20rpx 0 0;\n\t\n\t.menu-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t\t\n\t\ttext {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #333;\n\t\t\tflex: 1; /* 让文本占据剩余空间 */\n\t\t\t\n\t\t\t/* #ifdef H5 */\n\t\t\tfont-size: 18px;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\tfont-size: 28rpx;\n\t\t\t/* #endif */\n\t\t}\n\t\t\n\t\tbutton {\n\t\t\tbackground: none;\n\t\t\tborder: none;\n\t\t\tcolor: #666;\n\t\t\tfont-size: 28rpx;\n\t\t\tpadding: 16rpx 24rpx; /* 增加内边距，让按钮更大更舒适 */\n\t\t\tborder-radius: 8rpx;\n\t\t\tflex-shrink: 0; /* 防止按钮被压缩 */\n\t\t\tmargin-left: auto; /* 确保按钮始终在最右侧 */\n\t\t\ttransition: background-color 0.2s ease;\n\t\t\tmin-width: 80rpx; /* 设置最小宽度 */\n\t\t\theight: 60rpx; /* 设置固定高度 */\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\t\n\t\t\t&:active {\n\t\t\t\tbackground: #f0f0f0;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.menu-items {\n\t\tpadding: 20rpx 0;\n\t\t\n\t\t.menu-item {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tgap: 20rpx;\n\t\t\twidth: 100%;\n\t\t\tpadding: 30rpx;\n\t\t\tborder: none;\n\t\t\tbackground: none;\n\t\t\tfont-size: 30rpx;\n\t\t\tcolor: #333;\n\t\t\ttext-align: left;\n\t\t\t\n\t\t\t/* #ifdef H5 */\n\t\t\tfont-size: 16px;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t/* #ifdef MP-WEIXIN */\n\t\t\tfont-size: 26rpx;\n\t\t\t/* #endif */\n\t\t\t\n\t\t\t&:active {\n\t\t\t\tbackground: #f5f5f5;\n\t\t\t}\n\t\t\t\n\t\t\t&.danger {\n\t\t\t\tcolor: #ff4d4f;\n\t\t\t}\n\t\t}\n\t}\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-management.vue?vue&type=style&index=0&id=0671abb8&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-management.vue?vue&type=style&index=0&id=0671abb8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571664614\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/point/index.vue?c36a", "webpack:///D:/Xwzc/pages/patrol_pkg/point/index.vue?7e8d", "webpack:///D:/Xwzc/pages/patrol_pkg/point/index.vue?cd5e", "webpack:///D:/Xwzc/pages/patrol_pkg/point/index.vue?c3f5", "uni-app:///pages/patrol_pkg/point/index.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/point/index.vue?c1dd", "webpack:///D:/Xwzc/pages/patrol_pkg/point/index.vue?578d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "needRefresh", "keyword", "filter", "status", "pointList", "pagination", "page", "pageSize", "total", "loadMoreStatus", "isRefreshing", "isLoadingMore", "scrollThrottle", "loadMoreText", "contentdown", "contentrefresh", "contentnomore", "showFab", "isLoading", "onLoad", "uni", "onPullDownRefresh", "onReachBottom", "onShow", "onUnload", "computed", "displayTotal", "methods", "onScroll", "windowHeight", "formatDate", "onRefresh", "loadPointList", "reset", "params", "PatrolApi", "res", "newList", "title", "icon", "Promise", "loadMore", "finally", "handleSearch", "handleSearchClear", "changeFilter", "confirmDelete", "content", "confirmColor", "success", "deletePoint", "id", "goToAdd", "url", "goToEdit", "goToDetail", "viewQrcode"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACwKnnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;MACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;;IAEA;IACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACAJ;EACA;EACAK;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;QACA;MACA;MAEA;QACA;QACA;QACA;;QAEA;QACA;QAEA;UAAAC;QAAA;QACAA;;QAOA;QACA;QAEA;UACA;QACA;MACA;QACA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;QACA;QACA;QACA;;QAEA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACAX;MACA;IACA;IAEA;IACAY;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBACA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAEA;gBAAA;gBAGAC;kBACAjC;kBACAK;kBACAC;gBACA,GAEA;gBACA;kBACA2B;gBACA;gBAAA;gBAAA,OAEAC;kBAAAD;gBAAA;cAAA;gBAAAE;gBAEA;kBACAC;kBAEA;oBACA;kBACA;oBACA;kBACA;;kBAEA;;kBAEA;oBACA7B;kBACA;oBACAA;kBACA;oBACA;oBACA;sBACA;sBACAA;oBACA;sBACA;sBACAA;oBACA;kBACA;oBACAA;kBACA;kBAEA;;kBAEA;kBACA;oBACA;kBACA;oBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;gBACA;kBACAY;oBACAkB;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnB;kBACAkB;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACAnB;gBAAA,iCACAoB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;MAEA;MACA;MACA;MAEA,qBACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA1B;QACAkB;QACAS;QACAC;QACAC;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAb;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAc;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAf;kBACAgB;gBACA;cAAA;gBAFAf;gBAIA;kBACA;kBACA;oBAAA;kBAAA;;kBAEA;kBACA;kBAEAhB;oBACAkB;oBACAC;kBACA;gBACA;kBACAnB;oBACAkB;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnB;kBACAkB;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAa;MACAhC;QACAiC;MACA;IACA;IAEA;IACAC;MACAlC;QACAiC;MACA;IACA;IAEA;IACAE;MACAnC;QACAiC;MACA;IACA;IAEA;IACAG;MACApC;QACAiC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpfA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/point/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/point/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=8eafe5d4&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/point/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=8eafe5d4&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isLoading && _vm.pointList.length === 0\n  var l0 = !g0\n    ? _vm.__map(_vm.pointList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g1 = item.location ? item.location.longitude.toFixed(6) : null\n        var g2 = item.location ? item.location.latitude.toFixed(6) : null\n        var m0 = _vm.formatDate(item.create_date)\n        return {\n          $orig: $orig,\n          g1: g1,\n          g2: g2,\n          m0: m0,\n        }\n      })\n    : null\n  var g3 = !_vm.isLoading && _vm.pointList.length === 0\n  var g4 = _vm.pointList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"point-list-container\">\n\t\t<!-- 头部区域 - 更加轻量化 -->\n\t\t<view class=\"header-section\">\n\t\t\t<view class=\"header-content\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<text class=\"header-title\">点位管理</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"header-right\">\n\t\t\t\t\t<view class=\"point-count\">\n\t\t\t\t\t\t<text>{{ displayTotal }}</text>\n\t\t\t\t\t\t<text>个点位</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 搜索区域 -->\n\t\t<view class=\"search-area\">\n\t\t\t<view class=\"search-box\">\n\t\t\t\t<uni-icons type=\"search\" size=\"18\" color=\"#8F959E\"></uni-icons>\n\t\t\t\t<input\n\t\t\t\t\tv-model=\"keyword\"\n\t\t\t\t\tplaceholder=\"搜索点位名称或地址\"\n\t\t\t\t\tplaceholder-class=\"placeholder\"\n\t\t\t\t\t@confirm=\"handleSearch\"\n\t\t\t\t\t@input=\"handleSearch\"\n\t\t\t\t/>\n\t\t\t\t<view class=\"clear-button\" v-if=\"keyword\" @click=\"handleSearchClear\">\n\t\t\t\t\t<uni-icons type=\"clear\" size=\"14\" color=\"#8F959E\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 筛选区域 -->\n\t\t<scroll-view scroll-x=\"true\" class=\"filter-scroll scroll-view-hidden-scrollbar\" :show-scrollbar=\"false\">\n\t\t\t<view class=\"filter-area\">\n\t\t\t\t<view class=\"filter-item\" :class=\"{ active: filter.status === '' }\" @click=\"changeFilter('status', '')\">\n\t\t\t\t\t<text>全部</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"filter-item\" :class=\"{ active: filter.status === 1 }\" @click=\"changeFilter('status', 1)\">\n\t\t\t\t\t<text>启用中</text>\n\t\t\t\t\t<view class=\"status-dot active-dot\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"filter-item\" :class=\"{ active: filter.status === 0 }\" @click=\"changeFilter('status', 0)\">\n\t\t\t\t\t<text>已停用</text>\n\t\t\t\t\t<view class=\"status-dot inactive-dot\"></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\t\t\n\t\t<!-- 列表区域 -->\n\t\t<scroll-view \n\t\t\tscroll-y=\"true\" \n\t\t\tclass=\"list-area scroll-view-hidden-scrollbar\"\n\t\t\t@scroll=\"onScroll\"\n\t\t\t@scrolltolower=\"loadMore\"\n\t\t\trefresher-enabled\n\t\t\t:refresher-triggered=\"isRefreshing\"\n\t\t\t@refresherrefresh=\"onRefresh\"\n\t\t\t:show-scrollbar=\"false\"\n\t\t>\n\t\t\t<!-- 加载中占位 -->\n\t\t\t<view class=\"loading-skeleton\" v-if=\"isLoading && pointList.length === 0\">\n\t\t\t\t<view class=\"skeleton-item\" v-for=\"i in 3\" :key=\"i\">\n\t\t\t\t\t<view class=\"skeleton-header\">\n\t\t\t\t\t\t<view class=\"skeleton-title\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-status\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"skeleton-details\">\n\t\t\t\t\t\t<view class=\"skeleton-line\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-line\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-coords\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"skeleton-actions\">\n\t\t\t\t\t\t<view class=\"skeleton-action\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-action\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 点位列表 -->\n\t\t\t<view class=\"point-list\" v-else>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"point-item\" \n\t\t\t\t\tv-for=\"(item, index) in pointList\" \n\t\t\t\t\t:key=\"item._id\" \n\t\t\t\t\t@click=\"goToDetail(item._id)\"\n\t\t\t\t\t:style=\"{ animationDelay: index * 0.05 + 's' }\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"point-content\">\n\t\t\t\t\t\t<view class=\"point-header\">\n\t\t\t\t\t\t\t<view class=\"point-title\">\n\t\t\t\t\t\t\t\t<view class=\"title-wrapper\">\n\t\t\t\t\t\t\t\t\t<view class=\"status-indicator\" :class=\"{'inactive': item.status === 0}\"></view>\n\t\t\t\t\t\t\t\t\t<text class=\"point-name\">{{ item.name }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"point-status\" :class=\"{ 'status-active': item.status === 1, 'status-inactive': item.status === 0 }\">\n\t\t\t\t\t\t\t\t\t{{ item.status === 1 ? '启用中' : '已停用' }}\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"point-details\">\n\t\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"location-filled\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"detail-text\">范围: {{ item.range }}米</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"detail-item address-item\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"home-filled\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"detail-text address-text\">{{ item.location && item.location.address ? item.location.address : '暂无地址信息' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"coordinate-row\">\n\t\t\t\t\t\t\t\t<view class=\"coordinate-wrapper\">\n\t\t\t\t\t\t\t\t\t<view class=\"coordinate\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"coordinate-label\">经度:</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"coordinate-value\">{{ item.location ? item.location.longitude.toFixed(6) : '0.000000' }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"coordinate\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"coordinate-label\">纬度:</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"coordinate-value\">{{ item.location ? item.location.latitude.toFixed(6) : '0.000000' }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"point-time\">{{ formatDate(item.create_date) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"point-actions\">\n\t\t\t\t\t\t\t<view class=\"action-btn qrcode-btn\" @click.stop=\"viewQrcode(item)\" v-if=\"item.qrcode_enabled\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"scan\" size=\"16\" color=\"#07C160\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>二维码</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"action-btn edit-btn\" @click.stop=\"goToEdit(item._id)\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"compose\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>编辑</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"action-btn delete-btn\" @click.stop=\"confirmDelete(item)\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"16\" color=\"#FF3B30\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>删除</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 空列表提示 -->\n\t\t\t<view class=\"empty-container\" v-if=\"!isLoading && pointList.length === 0\">\n\t\t\t\t<p-empty-state \n\t\t\t\t\ttype=\"data\" \n\t\t\t\t\ttext=\"暂无点位数据\" \n\t\t\t\t/>\n\t\t\t\t<view class=\"empty-tip\">点击右下角按钮添加点位</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 加载更多 -->\n\t\t\t<uni-load-more v-if=\"pointList.length > 0\" :status=\"loadMoreStatus\" :content-text=\"loadMoreText\" />\n\t\t</scroll-view>\n\t\t\n\t\t<!-- 悬浮添加按钮 -->\n\t\t<view class=\"fab-button\" v-if=\"showFab\" @click=\"goToAdd\">\n\t\t\t<uni-icons type=\"plusempty\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n\nexport default {\n\tcomponents: {\n\t\tPEmptyState\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tneedRefresh: false,\n\t\t\tkeyword: '',\n\t\t\tfilter: {\n\t\t\t\tstatus: ''\n\t\t\t},\n\t\t\tpointList: [],\n\t\t\tpagination: {\n\t\t\t\tpage: 1,\n\t\t\t\tpageSize: 20,\n\t\t\t\ttotal: 0\n\t\t\t},\n\t\t\tloadMoreStatus: 'more',\n\t\t\tisRefreshing: false,\n\t\t\tisLoadingMore: false,\n\t\t\tscrollThrottle: null,\n\t\t\tloadMoreText: {\n\t\t\t\tcontentdown: '上拉加载更多',\n\t\t\t\tcontentrefresh: '加载中...',\n\t\t\t\tcontentnomore: '没有更多数据了'\n\t\t\t},\n\t\t\tshowFab: false,\n\t\t\tisLoading: false\n\t\t}\n\t},\n\tonLoad() {\n\t\t// 初始加载数据\n\t\tthis.loadPointList();\n\t\t\n\t\t// 监听更新事件\n\t\tuni.$on('pointUpdated', () => {\n\t\t\tthis.loadPointList();\n\t\t});\n\t},\n\tonPullDownRefresh() {\n\t\tthis.onRefresh();\n\t},\n\tonReachBottom() {\n\t\tthis.loadMore();\n\t},\n\tonShow() {\n\t\t// 只在需要时刷新\n\t\tif (this.needRefresh) {\n\t\t\tthis.loadPointList(true);\n\t\t\tthis.needRefresh = false;\n\t\t}\n\t},\n\tonUnload() {\n\t\t// 页面卸载时重置标记\n\t\tthis.needRefresh = false;\n\t\t// 移除事件监听，防止内存泄漏\n\t\tuni.$off('pointUpdated');\n\t},\n\tcomputed: {\n\t\t// 计算显示的点位总数\n\t\tdisplayTotal() {\n\t\t\treturn this.pagination.total || this.pointList.length || 0;\n\t\t}\n\t},\n\tmethods: {\n\t\t// 监听滚动事件，自动加载更多数据\n\t\tonScroll(e) {\n\t\t\t// 如果已经在加载或没有更多数据，则不处理\n\t\t\tif (this.loadMoreStatus === 'loading' || this.loadMoreStatus === 'noMore' || this.isLoadingMore) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 节流控制，300ms内只执行一次\n\t\t\tif (this.scrollThrottle) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.scrollThrottle = setTimeout(() => {\n\t\t\t\tthis.scrollThrottle = null;\n\t\t\t}, 300);\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 获取滚动信息\n\t\t\t\tconst scrollTop = e.detail.scrollTop;\n\t\t\t\tconst scrollHeight = e.detail.scrollHeight;\n\t\t\t\t\n\t\t\t\t// 使用新API替代已废弃的getSystemInfoSync\n\t\t\t\tlet windowHeight;\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tconst info = uni.getWindowInfo ? uni.getWindowInfo() : { windowHeight: e.detail.height || 500 };\n\t\t\t\twindowHeight = info.windowHeight;\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\twindowHeight = e.detail.height || 500;\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// 计算滚动比例，当滚动到80%位置时加载更多\n\t\t\t\tconst scrollPercent = scrollTop / (scrollHeight - windowHeight);\n\t\t\t\t\n\t\t\t\tif (scrollPercent > 0.8 && this.pointList.length > 0) {\n\t\t\t\t\tthis.loadMore();\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t// 错误处理\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 格式化日期\n\t\tformatDate(dateStr) {\n\t\t\tif (!dateStr) return '未知';\n\t\t\ttry {\n\t\t\t\t// 将日期字符串转换为Date对象\n\t\t\t\tconst date = new Date(dateStr);\n\t\t\t\tif (isNaN(date.getTime())) return '无效日期';\n\t\t\t\t\n\t\t\t\t// 格式化为 YYYY-MM-DD HH:MM\n\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n\t\t\t} catch (e) {\n\t\t\t\treturn '格式错误';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 下拉刷新处理\n\t\tonRefresh() {\n\t\t\tthis.isRefreshing = true;\n\t\t\tthis.showFab = false;\n\t\t\tthis.loadPointList(true).then(() => {\n\t\t\t\tthis.isRefreshing = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 加载点位列表\n\t\tasync loadPointList(reset = false) {\n\t\t\tif (reset) {\n\t\t\t\tthis.pagination.page = 1;\n\t\t\t\tthis.pointList = [];\n\t\t\t\tthis.loadMoreStatus = 'loading';\n\t\t\t\tthis.showFab = false;\n\t\t\t}\n\t\t\t\n\t\t\tthis.isLoading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst params = {\n\t\t\t\t\tkeyword: this.keyword,\n\t\t\t\t\tpage: this.pagination.page,\n\t\t\t\t\tpageSize: this.pagination.pageSize\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 添加状态筛选\n\t\t\t\tif (this.filter.status !== '') {\n\t\t\t\t\tparams.status = this.filter.status;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst res = await PatrolApi.callPointFunction('getPointList', { params });\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\tconst newList = res.data.list || [];\n\t\t\t\t\t\n\t\t\t\t\tif (reset) {\n\t\t\t\t\t\tthis.pointList = newList;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.pointList = [...this.pointList, ...newList];\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 确保total是数字\n\t\t\t\t\tlet total;\n\t\t\t\t\tif (typeof res.data.total === 'number' && res.data.total > 0) {\n\t\t\t\t\t\ttotal = res.data.total;\n\t\t\t\t\t} else if (typeof res.data.total === 'string' && parseInt(res.data.total) > 0) {\n\t\t\t\t\t\ttotal = parseInt(res.data.total);\n\t\t\t\t\t} else if (res.data.list && Array.isArray(res.data.list)) {\n\t\t\t\t\t\t// 如果返回的数量等于pageSize，假设可能还有更多数据\n\t\t\t\t\t\tif (res.data.list.length >= this.pagination.pageSize) {\n\t\t\t\t\t\t\t// 设置一个较大的值，确保可以尝试加载更多\n\t\t\t\t\t\t\ttotal = (this.pointList.length || 0) + res.data.list.length + 20;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果返回的数量小于pageSize，可能已经是最后一页\n\t\t\t\t\t\t\ttotal = reset ? res.data.list.length : this.pointList.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttotal = 0;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.pagination.total = isNaN(total) ? 0 : total;\n\t\t\t\t\t\n\t\t\t\t\t// 只有当返回的数据为空时才确定没有更多数据\n\t\t\t\t\tif (newList.length === 0) {\n\t\t\t\t\t\tthis.loadMoreStatus = 'noMore';\n\t\t\t\t\t} else if (newList.length < this.pagination.pageSize) {\n\t\t\t\t\t\tthis.loadMoreStatus = 'noMore';\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.loadMoreStatus = 'more';\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 显示悬浮按钮\n\t\t\t\t\tthis.showFab = true;\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '加载点位列表失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载点位列表出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\treturn Promise.resolve(); // 确保返回Promise以支持链式调用\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载更多\n\t\tloadMore() {\n\t\t\t// 避免重复加载和已经没有更多数据的情况\n\t\t\tif (this.loadMoreStatus === 'loading' || this.loadMoreStatus === 'noMore' || this.isLoadingMore) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.isLoadingMore = true;\n\t\t\tthis.pagination.page++;\n\t\t\tthis.loadMoreStatus = 'loading';\n\t\t\t\n\t\t\tthis.loadPointList()\n\t\t\t\t.finally(() => {\n\t\t\t\t\tthis.isLoadingMore = false;\n\t\t\t\t});\n\t\t},\n\t\t\n\t\t// 处理搜索\n\t\thandleSearch() {\n\t\t\tthis.loadPointList(true);\n\t\t},\n\t\t\n\t\t// 清除搜索\n\t\thandleSearchClear() {\n\t\t\tthis.keyword = '';\n\t\t\tthis.loadPointList(true);\n\t\t},\n\t\t\n\t\t// 切换筛选条件\n\t\tchangeFilter(type, value) {\n\t\t\tif (this.filter[type] === value) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.filter[type] = value;\n\t\t\tthis.loadPointList(true);\n\t\t},\n\t\t\n\t\t// 确认删除\n\t\tconfirmDelete(point) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认删除',\n\t\t\t\tcontent: `确定要删除点位\"${point.name}\"吗？`,\n\t\t\t\tconfirmColor: '#FF3B30',\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tawait this.deletePoint(point._id);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 删除点位\n\t\tasync deletePoint(pointId) {\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.callPointFunction('deletePoint', {\n\t\t\t\t\tid: pointId\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t// 更新本地列表数据\n\t\t\t\t\tthis.pointList = this.pointList.filter(item => item._id !== pointId);\n\t\t\t\t\t\n\t\t\t\t\t// 更新总数\n\t\t\t\t\tthis.pagination.total--;\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '删除失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '删除出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 跳转到添加页面\n\t\tgoToAdd() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/patrol_pkg/point/add'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到编辑页面\n\t\tgoToEdit(pointId) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/patrol_pkg/point/edit?id=${pointId}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到详情页面\n\t\tgoToDetail(pointId) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/patrol_pkg/point/detail?id=${pointId}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 查看点位二维码\n\t\tviewQrcode(item) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/patrol_pkg/point/qrcode?id=${item._id}`\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n/* 主色调变量 */\n$primary-color: #1677FF; // 支付宝蓝\n$primary-light: #E7F1FF;\n$primary-dark: #0E5FD8;\n\n$success-color: #07C160;\n$warning-color: #FFA300;\n$danger-color: #FF3B30;\n$info-color: #8F959E;\n\n$text-primary: #2C3E50;\n$text-secondary: #666666;\n$text-tertiary: #999999;\n$border-color: #EAEAEA;\n$background: #F7F8FA;\n\n$radius-sm: 6rpx;\n$radius-md: 12rpx;\n$radius-lg: 16rpx;\n$radius-xl: 24rpx;\n$radius-full: 999rpx;\n\n$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\n/* 淡入动画 */\n@keyframes fadeIn {\n\tfrom {\n\t\topacity: 0;\n\t\ttransform: translateY(20rpx);\n\t}\n\tto {\n\t\topacity: 1;\n\t\ttransform: translateY(0);\n\t}\n}\n\n/* 脉冲动画 */\n@keyframes pulse {\n\t0% {\n\t\ttransform: scale(1);\n\t}\n\t50% {\n\t\ttransform: scale(1.05);\n\t}\n\t100% {\n\t\ttransform: scale(1);\n\t}\n}\n\n/* 闪烁动画用于骨架屏 */\n@keyframes shimmer {\n\t0% {\n\t\tbackground-position: -200% 0;\n\t}\n\t100% {\n\t\tbackground-position: 200% 0;\n\t}\n}\n\n.point-list-container {\n\tbackground-color: #F5F7FA;\n\tdisplay: flex;\n\tflex-direction: column;\n\theight: 100vh;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.header-section {\n\tpadding: 20rpx 30rpx;\n\tbackground-color: #FFFFFF;\n\tborder-bottom: 1rpx solid #EAEAEA;\n\twidth: 100%;\n\tbox-sizing: border-box;\n\tposition: relative;\n\tz-index: 10;\n}\n\n.header-content {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.header-title {\n\tfont-size: 36rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\tposition: relative;\n\tpadding-left: 24rpx;\n\t\n\t&:before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 8rpx;\n\t\theight: 32rpx;\n\t\tbackground-color: $primary-color;\n\t\tborder-radius: $radius-sm;\n\t}\n}\n\n.point-count {\n\tbackground-color: $primary-light;\n\tborder-radius: $radius-full;\n\tpadding: 4rpx 16rpx;\n\tdisplay: flex;\n\talign-items: center;\n\ttransition: all 0.3s ease;\n\t\n\ttext {\n\t\tfont-size: 24rpx;\n\t\tcolor: $primary-color;\n\t\t\n\t\t&:first-child {\n\t\t\tfont-weight: 600;\n\t\t\tmargin-right: 4rpx;\n\t\t}\n\t}\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t}\n}\n\n.search-area {\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\twidth: 100%;\n\tbox-sizing: border-box;\n\tposition: relative;\n\tz-index: 9;\n}\n\n.search-box {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: $background;\n\tborder-radius: $radius-full;\n\tpadding: 16rpx 20rpx;\n\ttransition: all 0.3s ease;\n\t\n\t&:focus-within {\n\t\tbox-shadow: 0 0 0 4rpx rgba(22, 119, 255, 0.1);\n\t}\n\t\n\tinput {\n\t\tflex: 1;\n\t\theight: 60rpx;\n\t\tmargin-left: 12rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: $text-primary;\n\t}\n\t\n\t.placeholder {\n\t\tcolor: $text-tertiary;\n\t}\n\t\n\t.clear-button {\n\t\tpadding: 10rpx;\n\t\topacity: 0.7;\n\t\t\n\t\t&:active {\n\t\t\topacity: 1;\n\t\t}\n\t}\n}\n\n.filter-scroll {\n\tbackground-color: #FFFFFF;\n\twhite-space: nowrap;\n\tborder-bottom: 1rpx solid #EAEAEA;\n\tposition: relative;\n\tz-index: 8;\n\t/* 统一隐藏滚动条 */\n\tscrollbar-width: none; /* Firefox */\n\t-ms-overflow-style: none; /* IE/Edge */\n\t&::-webkit-scrollbar {\n\t\tdisplay: none; /* Chrome/Safari */\n\t\twidth: 0 !important;\n\t\theight: 0 !important;\n\t\tbackground: transparent;\n\t}\n}\n\n.filter-area {\n\tdisplay: inline-flex;\n\tpadding: 16rpx 30rpx;\n}\n\n.filter-item {\n\tdisplay: inline-flex;\n\talign-items: center;\n\tpadding: 12rpx 30rpx;\n\tmargin-right: 20rpx;\n\tfont-size: 28rpx;\n\tcolor: $text-secondary;\n\tbackground-color: $background;\n\tborder-radius: $radius-full;\n\ttransition: all 0.3s ease;\n\t\n\t.status-dot {\n\t\twidth: 10rpx;\n\t\theight: 10rpx;\n\t\tborder-radius: 50%;\n\t\tmargin-left: 8rpx;\n\t}\n\t\n\t.active-dot {\n\t\tbackground-color: $success-color;\n\t}\n\t\n\t.inactive-dot {\n\t\tbackground-color: $danger-color;\n\t}\n\t\n\t&.active {\n\t\tbackground-color: $primary-light;\n\t\tcolor: $primary-color;\n\t\tfont-weight: 500;\n\t\ttransform: scale(1.05);\n\t}\n\t\n\t&:active {\n\t\topacity: 0.8;\n\t\ttransform: scale(0.95);\n\t}\n}\n\n.list-area {\n\tflex: 1;\n\toverflow-y: auto;\n\theight: calc(100vh - 300rpx); /* 减去顶部区域高度 */\n\tposition: relative;\n\t/* 统一隐藏滚动条 */\n\tscrollbar-width: none; /* Firefox */\n\t-ms-overflow-style: none; /* IE/Edge */\n\t&::-webkit-scrollbar {\n\t\tdisplay: none; /* Chrome/Safari */\n\t\twidth: 0 !important;\n\t\theight: 0 !important;\n\t\tbackground: transparent;\n\t}\n}\n\n.loading-skeleton {\n\tpadding: 10rpx 20rpx;\n}\n\n.skeleton-item {\n\tmargin-bottom: 20rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: $radius-lg;\n\tbox-shadow: $shadow-sm;\n\tpadding: 20rpx;\n\toverflow: hidden;\n}\n\n.skeleton-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.skeleton-title {\n\twidth: 60%;\n\theight: 32rpx;\n\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n\tbackground-size: 200% 100%;\n\tborder-radius: $radius-sm;\n\tanimation: shimmer 1.5s infinite;\n}\n\n.skeleton-status {\n\twidth: 20%;\n\theight: 32rpx;\n\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n\tbackground-size: 200% 100%;\n\tborder-radius: $radius-full;\n\tanimation: shimmer 1.5s infinite;\n}\n\n.skeleton-details {\n\tbackground-color: $background;\n\tborder-radius: $radius-md;\n\tpadding: 16rpx 20rpx;\n\tmargin-bottom: 16rpx;\n}\n\n.skeleton-line {\n\theight: 26rpx;\n\tmargin-bottom: 12rpx;\n\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n\tbackground-size: 200% 100%;\n\tborder-radius: $radius-sm;\n\tanimation: shimmer 1.5s infinite;\n\t\n\t&:first-child {\n\t\twidth: 40%;\n\t}\n\t\n\t&:nth-child(2) {\n\t\twidth: 90%;\n\t}\n}\n\n.skeleton-coords {\n\theight: 24rpx;\n\twidth: 70%;\n\tmargin-top: 16rpx;\n\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n\tbackground-size: 200% 100%;\n\tborder-radius: $radius-sm;\n\tanimation: shimmer 1.5s infinite;\n}\n\n.skeleton-actions {\n\tdisplay: flex;\n\tpadding-top: 20rpx;\n\tborder-top: 1rpx solid $border-color;\n}\n\n.skeleton-action {\n\theight: 56rpx;\n\tborder-radius: $radius-full;\n\tflex: 1;\n\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n\tbackground-size: 200% 100%;\n\tmargin: 0 10rpx;\n\tanimation: shimmer 1.5s infinite;\n\t\n\t&:first-child {\n\t\tmargin-left: 0;\n\t}\n\t\n\t&:last-child {\n\t\tmargin-right: 0;\n\t}\n}\n\n.point-list {\n\tpadding: 10rpx 20rpx;\n}\n\n.point-item {\n\tmargin-bottom: 20rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: $radius-lg;\n\tbox-shadow: $shadow-sm;\n\toverflow: hidden;\n\ttransition: all 0.3s ease;\n\tanimation: fadeIn 0.5s ease-out forwards;\n\t\n\t&:active {\n\t\ttransform: scale(0.98);\n\t\tbox-shadow: $shadow-md;\n\t}\n}\n\n.point-content {\n\tpadding: 20rpx;\n}\n\n.point-header {\n\tmargin-bottom: 20rpx;\n}\n\n.point-title {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 10rpx;\n}\n\n.title-wrapper {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.status-indicator {\n\twidth: 10rpx;\n\theight: 10rpx;\n\tborder-radius: 50%;\n\tbackground-color: $success-color;\n\tmargin-right: 10rpx;\n\tposition: relative;\n\t\n\t&:after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: -4rpx;\n\t\tleft: -4rpx;\n\t\tright: -4rpx;\n\t\tbottom: -4rpx;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid rgba(7, 193, 96, 0.3);\n\t\topacity: 0;\n\t\tanimation: pulse 2s infinite;\n\t}\n\t\n\t&.inactive {\n\t\tbackground-color: $danger-color;\n\t\t\n\t\t&:after {\n\t\t\tborder-color: rgba(255, 59, 48, 0.3);\n\t\t}\n\t}\n}\n\n.point-name {\n\tfont-size: 32rpx;\n\tcolor: $text-primary;\n\tfont-weight: 600;\n}\n\n.point-status {\n\tfont-size: 22rpx;\n\tpadding: 4rpx 16rpx;\n\tborder-radius: $radius-full;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\t\n\t&.status-active {\n\t\tbackground-color: rgba(7, 193, 96, 0.1);\n\t\tcolor: $success-color;\n\t}\n\t\n\t&.status-inactive {\n\t\tbackground-color: rgba(255, 59, 48, 0.1);\n\t\tcolor: $danger-color;\n\t}\n}\n\n.point-details {\n\tbackground-color: $background;\n\tborder-radius: $radius-md;\n\tpadding: 16rpx 20rpx;\n\tmargin-bottom: 16rpx;\n\ttransition: all 0.3s ease;\n}\n\n.detail-item {\n\tdisplay: flex;\n\talign-items: flex-start;\n\tmargin-bottom: 12rpx;\n\tfont-size: 26rpx;\n\tcolor: $text-secondary;\n\t\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n}\n\n.detail-text {\n\tmargin-left: 10rpx;\n\tline-height: 1.5;\n}\n\n.address-item {\n\tword-break: break-all;\n}\n\n.address-text {\n\tcolor: $text-secondary;\n}\n\n.coordinate-row {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: flex-start;\n\tmargin-top: 16rpx;\n\tpadding-top: 16rpx;\n\tborder-top: 1rpx dashed $border-color;\n}\n\n.coordinate-wrapper {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n}\n\n.coordinate {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-right: 30rpx;\n\tfont-size: 24rpx;\n}\n\n.coordinate-label {\n\tcolor: $text-tertiary;\n\tmargin-right: 8rpx;\n}\n\n.coordinate-value {\n\tcolor: $text-secondary;\n\tfont-family: monospace;\n}\n\n.point-time {\n\tfont-size: 22rpx;\n\tcolor: $text-tertiary;\n\tmargin-top: 4rpx;\n}\n\n.point-actions {\n\tdisplay: flex;\n\tborder-top: 1rpx solid $border-color;\n\tpadding-top: 20rpx;\n}\n\n.action-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 12rpx 0;\n\tborder-radius: $radius-full;\n\tflex: 1;\n\tfont-size: 26rpx;\n\ttransition: all 0.3s ease;\n\tmargin: 0 10rpx;\n\t\n\t&:first-child {\n\t\tmargin-left: 0;\n\t}\n\t\n\t&:last-child {\n\t\tmargin-right: 0;\n\t}\n\t\n\ttext {\n\t\tmargin-left: 6rpx;\n\t}\n\t\n\t&:active {\n\t\topacity: 0.8;\n\t\ttransform: scale(0.95);\n\t}\n}\n\n.edit-btn {\n\tbackground-color: $primary-light;\n\tcolor: $primary-color;\n}\n\n.qrcode-btn {\n\tbackground-color: #E6F7EF;\n\tcolor: #07C160;\n}\n\n.delete-btn {\n\tbackground-color: rgba(255, 59, 48, 0.1);\n\tcolor: $danger-color;\n}\n\n/* 空状态容器样式 */\n.empty-container {\n\tpadding: 80rpx 0;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: center;\n\talign-items: center;\n\tanimation: fadeIn 0.5s ease-out;\n}\n\n.empty-tip {\n\tfont-size: 26rpx;\n\tcolor: $text-tertiary;\n\tmargin-top: 20rpx;\n\tpadding: 10rpx 30rpx;\n\tbackground-color: $primary-light;\n\tborder-radius: $radius-full;\n\topacity: 0.8;\n}\n\n/* 悬浮添加按钮 */\n.fab-button {\n\tposition: fixed;\n\tright: 40rpx;\n\tbottom: calc(40rpx + env(safe-area-inset-bottom, 34px)); /* 考虑底部安全区域 */\n\twidth: 100rpx;\n\theight: 100rpx;\n\tborder-radius: 50%;\n\tbackground: $primary-color;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: $shadow-lg;\n\ttransition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n\tz-index: 999;\n\tanimation: fadeIn 0.5s;\n\t\n\t&:after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tborder-radius: 50%;\n\t\tbackground-color: $primary-color;\n\t\tz-index: -1;\n\t\topacity: 0.5;\n\t\ttransform: scale(0.9);\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t&:active {\n\t\ttransform: scale(0.92) rotate(90deg);\n\t\tbackground-color: $primary-dark;\n\t\t\n\t\t&:after {\n\t\t\ttransform: scale(1.1);\n\t\t\topacity: 0;\n\t\t}\n\t}\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558438092\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
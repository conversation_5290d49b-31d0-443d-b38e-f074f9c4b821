(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-ucenter-ucenter"],{"0777":function(e,t,a){"use strict";var n=a("19d0"),i=a.n(n);i.a},"14db":function(e,t,a){"use strict";(function(e){a("6a54");var n=a("3639").default,i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(a("2634")),r=i(a("2fdc"));a("bf0f"),a("4626"),a("fd3c"),a("c9b5"),a("ab80"),a("5ac7"),a("aa9c"),a("3efd"),a("de6c"),a("2797"),a("9db6");var s=a("423e"),c=(a("fb20"),n(a("eddf"))),d=i(a("9b76")),l=a("7fc2"),u=i(a("7e11")),f={components:{PEmptyState:d.default},data:function(){var e=uni.getStorageSync("uni_id_token")||"",t=!!e,a=c.default.get(c.default.cacheKeys.USER_INFO)||{},n={nickname:a.nickname||"加载中...",username:a.username||"",avatar_file:a.avatar_file||null,_id:a._id||""};return{roleNames:[],todoList:[],todoCount:0,userRole:[],hasRole:!1,isWechatPlatform:!1,isRefreshing:!1,lastRefreshTime:0,localUserInfo:n,localHasLogin:t,todoUpdateTimer:null,isTokenValid:!0,responsibleTaskCount:0,supervisionTaskCount:0,avatarLoaded:!1}},computed:{userInfo:function(){return this.localUserInfo||{}},hasLogin:function(){return(s.store.hasLogin||this.localHasLogin)&&this.isTokenValid},hasAdminPermission:function(){return this.userRole.some((function(e){return["admin"].includes(e)}))},hasResponsiblePermission:function(){return this.userRole.some((function(e){return["responsible"].includes(e)}))},hasGMPermission:function(){return this.userRole.some((function(e){return["GM","admin"].includes(e)}))}},created:function(){var e=this;uni.$on("uni-id-pages-login-success",(function(){c.default.remove(c.default.cacheKeys.USER_ROLE),e.refreshData(!0),u.default.onLoginSuccess()})),uni.$on("todo-count-updated",(function(t){e.hasLogin&&e.hasRole&&(e.todoCount=t,e.getTodoList())})),uni.$on("refresh-todo-list",(function(){e.hasLogin&&e.hasRole&&e.refreshData(!1)})),uni.$on("force-logout-ui-update",(function(){e.performLogout(!1)})),uni.$on("feedback-updated",(function(){e.hasLogin&&e.hasRole&&setTimeout((function(){e.refreshData(!1)}),100)})),uni.$on("ucenter-need-refresh",(function(t){e.hasLogin&&e.hasRole&&(console.log("收到用户中心刷新事件:",t),e.refreshData(!1))})),uni.$on("cross-device-update-detected",(function(t){if(e.hasLogin&&e.hasRole){var a=e.shouldRefreshOnCrossDeviceUpdate(t);a&&(console.log("用户中心收到跨设备更新通知，静默刷新数据"),e.refreshData(!1))}})),this.startTodoUpdateTimer(),document.addEventListener("visibilitychange",this.handleVisibilityChange)},beforeDestroy:function(){uni.$off("uni-id-pages-login-success"),uni.$off("todo-count-updated"),uni.$off("refresh-todo-list"),uni.$off("force-logout-ui-update"),uni.$off("feedback-updated"),uni.$off("ucenter-need-refresh"),uni.$off("cross-device-update-detected"),this.clearTodoUpdateTimer(),document.removeEventListener("visibilitychange",this.handleVisibilityChange)},onLoad:function(){this.hasLogin&&this.refreshData(!0)},onShow:function(){if(this.hasLogin){var e=Date.now();e-this.lastRefreshTime>3e4&&this.refreshData(!1)}this.checkTokenStatus(),this.hasLogin&&this.hasRole&&setTimeout((function(){u.default.forceSyncBadge()}),300)},onPullDownRefresh:function(){this.hasLogin?this.refreshData(!0).then((function(){uni.stopPullDownRefresh(),uni.showToast({title:"刷新成功",icon:"success",duration:1500})})).catch((function(e){console.error("刷新数据失败:",e),uni.stopPullDownRefresh(),uni.showToast({title:"刷新失败",icon:"error",duration:1500})})):uni.stopPullDownRefresh()},watch:{hasLogin:function(e){var t=this;e&&this.$nextTick((function(){t.refreshData(!0)}))},"localUserInfo.avatar_file.url":{handler:function(e,t){e!==t&&(this.avatarLoaded=!1)},deep:!0},"localUserInfo.avatar":{handler:function(e,t){e!==t&&(this.avatarLoaded=!1)}}},methods:{onAvatarLoad:function(){this.avatarLoaded=!0},onAvatarError:function(){this.avatarLoaded=!1,console.log("头像加载失败，显示默认头像")},showFeatureInDevelopment:function(){uni.showToast({title:"功能开发中，敬请期待",icon:"none",duration:1e3})},handleVisibilityChange:function(){"visible"===document.visibilityState&&this.hasLogin&&this.hasRole&&this.refreshData(!1)},startTodoUpdateTimer:function(){var e=this;this.clearTodoUpdateTimer(),this.todoUpdateTimer=setInterval((function(){e.hasLogin&&e.hasRole&&e.updateTodoCountFromBadge()}),3e4),this.hasLogin&&this.hasRole&&this.updateTodoCountFromBadge()},clearTodoUpdateTimer:function(){this.todoUpdateTimer&&(clearInterval(this.todoUpdateTimer),this.todoUpdateTimer=null)},updateTodoCountFromBadge:function(){var e=this;return(0,r.default)((0,o.default)().mark((function t(){var a;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,u.default.getTodoCount();case 3:a=t.sent,e.todoCount=a,t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("更新待办数量失败:",t.t0);case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},refreshData:function(){var e=arguments,t=this;return(0,r.default)((0,o.default)().mark((function a(){var n;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(n=e.length>0&&void 0!==e[0]&&e[0],t.hasLogin&&!t.isRefreshing){a.next=3;break}return a.abrupt("return",Promise.resolve());case 3:return t.isRefreshing=!0,n&&uni.showLoading({title:"加载中..."}),a.prev=5,a.next=8,t.getUserInfo();case 8:if(t.userRole&&0!==t.userRole.length&&t.roleNames&&0!==t.roleNames.length){a.next=11;break}return a.next=11,t.getUserRole();case 11:if(!t.hasRole){a.next=16;break}return a.next=14,t.getTodoList();case 14:a.next=18;break;case 16:t.todoList=[],t.todoCount=0;case 18:if(!t.hasResponsiblePermission){a.next=23;break}return a.next=21,t.getResponsibleTaskCount();case 21:a.next=24;break;case 23:t.responsibleTaskCount=0;case 24:if(!t.hasGMPermission){a.next=29;break}return a.next=27,t.getSupervisionTaskCount();case 27:a.next=30;break;case 29:t.supervisionTaskCount=0;case 30:return t.lastRefreshTime=Date.now(),a.abrupt("return",Promise.resolve());case 34:return a.prev=34,a.t0=a["catch"](5),a.abrupt("return",Promise.reject(a.t0));case 37:return a.prev=37,n&&uni.hideLoading(),t.isRefreshing=!1,a.finish(37);case 41:case"end":return a.stop()}}),a,null,[[5,34,37,41]])})))()},getUserRole:function(){var t=this;return(0,r.default)((0,o.default)().mark((function a(){var n,i,r,s,d,l;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,t.hasLogin){a.next=6;break}return t.roleNames=["未登录"],t.userRole=[],t.hasRole=!1,a.abrupt("return");case 6:if(n=c.default.get(c.default.cacheKeys.USER_ROLE),!n){a.next=12;break}return t.userRole=n.userRole,t.roleNames=n.roleNames,t.hasRole=n.hasRole,a.abrupt("return");case 12:return i=e.database(),a.next=15,i.collection("uni-id-users").where("'_id' == $cloudEnv_uid").field("role").get();case 15:r=a.sent,s=r.result,s.data&&s.data.length>0?(t.userRole=s.data[0].role||[],d={admin:"管理员",responsible:"责任人",reviser:"发布人",supervisor:"主管",PM:"副厂长",GM:"厂长",logistics:"后勤员",dispatch:"调度员",Integrated:"综合员",operator:"设备员",technician:"工艺员",mechanic:"技术员",user:"普通员工"},t.hasRole=t.userRole.some((function(e){return["supervisor","PM","GM","admin","responsible"].includes(e)})),t.userRole.length>0?t.roleNames=t.userRole.map((function(e){return d[e]||e})):t.roleNames=["普通用户"],c.default.set(c.default.cacheKeys.USER_ROLE,{userRole:t.userRole,roleNames:t.roleNames,hasRole:t.hasRole},60)):(t.roleNames=["普通用户"],t.userRole=[],t.hasRole=!1),a.next=26;break;case 20:a.prev=20,a.t0=a["catch"](0),l=a.t0.message||a.t0.toString(),l.includes("token")||l.includes("unauthorized")||l.includes("expired")?(console.log("检测到token相关错误，清除待办数据"),t.todoList=[],t.todoCount=0,t.hasRole=!1,t.roleNames=["普通用户"]):t.roleNames=["普通用户"],t.userRole=[],t.hasRole=!1;case 26:case"end":return a.stop()}}),a,null,[[0,20]])})))()},getProjectName:function(e){return e||"未分类"},getTodoList:function(){var t=this;return(0,r.default)((0,o.default)().mark((function a(){var n,i,r,s,c;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.hasLogin&&t.hasRole){a.next=4;break}return t.todoList=[],t.todoCount=0,a.abrupt("return");case 4:return a.prev=4,n=e.database(),i=n.command,r=t.buildTodoQueryConditions(i),a.next=10,n.collection("feedback").where(r).count();case 10:if(s=a.sent,s.result&&void 0!==s.result.total&&(t.todoCount=s.result.total),!(t.todoCount>0)){a.next=19;break}return a.next=15,n.collection("feedback").where(r).orderBy("createTime","desc").limit(3).get();case 15:c=a.sent,c.result&&c.result.data?t.todoList=c.result.data:t.todoList=[],a.next=20;break;case 19:t.todoList=[];case 20:a.next=26;break;case 22:a.prev=22,a.t0=a["catch"](4),t.todoList=[],t.todoCount=0;case 26:case"end":return a.stop()}}),a,null,[[4,22]])})))()},getResponsibleTaskCount:function(){var t=this;return(0,r.default)((0,o.default)().mark((function a(){var n,i;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.hasLogin&&t.hasResponsiblePermission){a.next=3;break}return t.responsibleTaskCount=0,a.abrupt("return");case 3:return a.prev=3,a.next=6,e.callFunction({name:"feedback-list",data:{action:"getMyTasks",status:"assigned_to_responsible"}});case 6:n=a.sent,n.result&&0===n.result.code&&(t.responsibleTaskCount=(null===(i=n.result.data.stats)||void 0===i?void 0:i.assigned)||0),a.next=14;break;case 10:a.prev=10,a.t0=a["catch"](3),console.error("获取负责人任务数量失败:",a.t0),t.responsibleTaskCount=0;case 14:case"end":return a.stop()}}),a,null,[[3,10]])})))()},getSupervisionTaskCount:function(){var t=this;return(0,r.default)((0,o.default)().mark((function a(){var n,i;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.hasLogin&&t.hasGMPermission){a.next=3;break}return t.supervisionTaskCount=0,a.abrupt("return");case 3:return a.prev=3,a.next=6,e.callFunction({name:"feedback-list",data:{action:"getGMSupervisionTasks"}});case 6:n=a.sent,n.result&&0===n.result.code&&(i=n.result.data.stats||{},t.supervisionTaskCount=(i.assigned||0)+(i.pending||0)),a.next=14;break;case 10:a.prev=10,a.t0=a["catch"](3),console.error("获取厂长监督任务数量失败:",a.t0),t.supervisionTaskCount=0;case 14:case"end":return a.stop()}}),a,null,[[3,10]])})))()},buildTodoQueryConditions:function(t){var a=[];if(this.userRole.includes("supervisor")&&a.push({workflowStatus:"pending_supervisor"}),this.userRole.includes("PM")&&a.push({workflowStatus:"pending_pm"}),this.userRole.includes("GM")&&(a.push({workflowStatus:"pending_gm"}),a.push({workflowStatus:"gm_approved_pending_assign"}),a.push({workflowStatus:"completed_by_responsible"})),this.userRole.includes("responsible"))try{var n=e.getCurrentUserInfo(),i=null===n||void 0===n?void 0:n.uid;i&&a.push({workflowStatus:"assigned_to_responsible",responsibleUserId:i})}catch(o){console.warn("获取用户ID失败:",o)}return this.userRole.includes("admin")?{workflowStatus:t.in(["pending_supervisor","pending_pm","pending_gm","gm_approved_pending_assign","completed_by_responsible"])}:a.length>0?t.or(a):{}},formatDate:function(e){return e?(0,l.formatDate)(e):""},getTodoTimeText:function(e){return"assigned_to_responsible"===e.workflowStatus&&e.assignedTime?"指派时间：".concat((0,l.formatDate)(e.assignedTime,"MM-DD HH:mm")):"completed_by_responsible"===e.workflowStatus&&e.completedByResponsibleTime?"完成时间：".concat((0,l.formatDate)(e.completedByResponsibleTime,"MM-DD HH:mm")):"提交时间：".concat((0,l.formatDate)(e.createTime,"MM-DD HH:mm"))},getTodoTypeText:function(e){return{pending_supervisor:"待主管审核",pending_pm:"待副厂长审核",pending_gm:"待厂长审核",gm_approved_pending_assign:"待指派负责人",assigned_to_responsible:"待我完成",completed_by_responsible:"待最终确认"}[e]||"待处理"},handleTodoClick:function(e){"assigned_to_responsible"===e.workflowStatus?uni.navigateTo({url:"/pages/ucenter_pkg/responsible-tasks"}):this.goToExamine(e._id)},goToExamine:function(e){var t=this;uni.navigateTo({url:"/pages/feedback_pkg/examine?id=".concat(e),events:{refreshData:function(){t.refreshData(!1)}}})},goToReadNewsLog:function(){uni.navigateTo({url:"/pages/notice/list"})},exportexcel:function(){uni.navigateTo({url:"/pages/ucenter_pkg/export-excel"})},checkExportPermission:function(){var e=this.userRole.some((function(e){return["reviser","supervisor","PM","GM","admin","dispatch"].includes(e)}));e?this.exportexcel():uni.showToast({title:"权限不够,无法查看",icon:"none",duration:2e3})},goToResponsibleTasks:function(){this.hasResponsiblePermission?uni.navigateTo({url:"/pages/ucenter_pkg/responsible-tasks",fail:function(e){console.error("跳转任务管理页面失败:",e),uni.showToast({title:"页面跳转失败",icon:"none"})}}):uni.showToast({title:"权限不足，无法访问任务管理",icon:"none",duration:2e3})},goToUserManagement:function(){this.hasAdminPermission?uni.navigateTo({url:"/pages/ucenter_pkg/user-management",fail:function(e){console.error("跳转用户管理页面失败:",e),uni.showToast({title:"页面跳转失败",icon:"none"})}}):uni.showToast({title:"权限不足，无法访问用户管理",icon:"none",duration:2e3})},goToGMSupervision:function(){this.hasGMPermission?uni.navigateTo({url:"/pages/ucenter_pkg/gm-supervision",fail:function(e){console.error("跳转厂长监督页面失败:",e),uni.showToast({title:"页面跳转失败",icon:"none"})}}):uni.showToast({title:"权限不足，只有厂长才能访问",icon:"none",duration:2e3})},modifyNickname:function(){uni.navigateTo({url:"/uni_modules/uni-id-pages/pages/userinfo/userinfo?showLoginManage=false&showUserInfo=false&showSet=false&showEdit=true"})},logout:function(){var t=this;return(0,r.default)((0,o.default)().mark((function a(){var n,i;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,uni.showModal({title:"提示",content:"确定要退出登录吗？",confirmText:"退出",cancelText:"取消"});case 3:if(n=a.sent,!n.confirm){a.next=20;break}return uni.showLoading({title:"退出中...",mask:!0}),u.default.forceCleanBadge(),a.prev=7,i=e.importObject("uni-id-co"),a.next=11,i.logout();case 11:a.next=16;break;case 13:a.prev=13,a.t0=a["catch"](7),console.error("云函数退出登录失败:",a.t0);case 16:t.performLogout(!1),setTimeout((function(){u.default.forceCleanBadge()}),100),uni.hideLoading(),uni.showToast({title:"已退出登录",icon:"success"});case 20:a.next=25;break;case 22:a.prev=22,a.t1=a["catch"](0),uni.showToast({title:"退出登录失败",icon:"none"});case 25:case"end":return a.stop()}}),a,null,[[0,22],[7,13]])})))()},goToTodoList:function(){uni.navigateTo({url:"/pages/ucenter_pkg/todo"})},goToLogin:function(){uni.navigateTo({url:"/uni_modules/uni-id-pages/pages/login/login-withpwd"})},getUserInfo:function(){var t=this;return(0,r.default)((0,o.default)().mark((function a(){var n,i,r,s,d,l;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,n=uni.getStorageSync("uni_id_token")||"",t.localHasLogin=!!n,t.localHasLogin){a.next=6;break}return t.localUserInfo={},a.abrupt("return");case 6:if(i=c.default.get(c.default.cacheKeys.USER_INFO)||{},!i.nickname&&!i.username){a.next=10;break}return t.localUserInfo=i,a.abrupt("return");case 10:return r=e.database(),a.next=13,r.collection("uni-id-users").where("'_id' == $cloudEnv_uid").field("nickname, username, avatar_file").get();case 13:s=a.sent,d=s.result,d.data&&d.data.length>0?(l=d.data[0],t.localUserInfo={_id:l._id,nickname:l.nickname||l.username||"未设置昵称",username:l.username||l.nickname||"未设置昵称",avatar_file:l.avatar_file||null},t.avatarLoaded=!1,c.default.set(c.default.cacheKeys.USER_INFO,t.localUserInfo)):(t.localUserInfo={nickname:"用户",username:"user"},t.avatarLoaded=!1),a.next=23;break;case 18:a.prev=18,a.t0=a["catch"](0),console.error("获取用户信息失败:",a.t0),t.localUserInfo={nickname:"用户",username:"user"},t.avatarLoaded=!1;case 23:case"end":return a.stop()}}),a,null,[[0,18]])})))()},navToLogin:function(){uni.navigateTo({url:"/uni_modules/uni-id-pages/pages/login/login-withoutpwd"})},navTo:function(e){if("/pages/honor_pkg/gallery/index"!==e){var t=c.default.get("recent_pages",[]);t.includes(e)||(t.unshift(e),t.length>10&&t.pop(),c.default.set("recent_pages",t)),uni.navigateTo({url:e,fail:function(e){console.error("导航失败:",e),uni.showToast({title:"页面不存在",icon:"none"})}})}else this.navToHonorGallery(e)},navToHonorGallery:function(e){var t=c.default.get("recent_pages",[]);t.includes(e)||(t.unshift(e),t.length>10&&t.pop(),c.default.set("recent_pages",t)),uni.navigateTo({url:e,animationType:"slide-in-right",animationDuration:200,success:function(){},fail:function(t){console.error("导航失败:",t),uni.navigateTo({url:e,fail:function(e){uni.showToast({title:"页面不存在",icon:"none"})}})}})},checkTokenStatus:function(){var e=this;return(0,r.default)((0,o.default)().mark((function t(){var a,n;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,a=uni.getStorageSync("uni_id_token"),a){t.next=5;break}return e.handleTokenInvalid(),t.abrupt("return");case 5:return t.next=7,e.validateToken();case 7:n=t.sent,n||e.handleTokenInvalid(),t.next=15;break;case 11:t.prev=11,t.t0=t["catch"](0),console.error("验证登录状态失败:",t.t0),e.handleTokenInvalid();case 15:case"end":return t.stop()}}),t,null,[[0,11]])})))()},validateToken:function(){return(0,r.default)((0,o.default)().mark((function e(){var t,a;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,t=uni.getStorageSync("uni_id_token"),t){e.next=4;break}return e.abrupt("return",!1);case 4:if(a=uni.getStorageSync("uni_id_token_expired"),!(a<Date.now())){e.next=7;break}return e.abrupt("return",!1);case 7:return e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e["catch"](0),console.error("Token validation error:",e.t0),e.abrupt("return",!1);case 14:case"end":return e.stop()}}),e,null,[[0,10]])})))()},performLogout:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.localHasLogin=!1,uni.removeStorageSync("uni_id_token"),uni.removeStorageSync("uni_id_token_expired"),uni.removeStorageSync("uni_id_user"),uni.removeStorageSync("uni-id-pages-userInfo"),c.default.remove(c.default.cacheKeys.USER_INFO),c.default.remove(c.default.cacheKeys.USER_ROLE),c.default.remove(c.default.cacheKeys.PROJECT_OPTIONS),s.mutations.setUserInfo({},{cover:!0}),u.default.clearBadge(),this.clearSensitiveCache(),Object.assign(this,{roleNames:[],todoList:[],todoCount:0,userRole:[],hasRole:!1,localUserInfo:{},isTokenValid:!1}),t&&uni.showToast({title:"登录已过期，请重新登录",icon:"none",duration:2e3}),this.$nextTick((function(){return e.getUserInfo()}))},clearSensitiveCache:function(){try{var e=uni.getStorageInfoSync(),t=e.keys,a=["user_info_","user_mgmt_"],n=["_DC_STAT_UUID",(0,c.getCacheKey)("recent_pages"),"last_app_start_time","uni-id-pages-userInfo"];t.forEach((function(e){if(!n.includes(e)){var t=a.some((function(t){return e===t||e.startsWith(t)}));t&&(uni.removeStorageSync(e))}}))}catch(i){console.error("清除敏感缓存失败:",i)}},handleTokenInvalid:function(){this.performLogout(!1)},shouldRefreshOnCrossDeviceUpdate:function(e){var t=Date.now()-this.lastRefreshTime;if(t<15e3)return!1;if(e.updateTypes){var a=["workflow_status_changed","feedback_submitted"],n=e.updateTypes.some((function(e){return a.includes(e)}));if(n)return!0}return e.updateCount>1}}};t.default=f}).call(this,a("861b")["uniCloud"])},"19d0":function(e,t,a){var n=a("b72a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("4b722938",n,!0,{sourceMap:!1,shadowMode:!1})},"249e":function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */uni-view[data-v-6dd487e0]{display:flex;box-sizing:border-box;flex-direction:column}uni-page-body[data-v-6dd487e0]{background-color:#f8f9fc}body.?%PAGE?%[data-v-6dd487e0]{background-color:#f8f9fc}\n/* 定义动画 */@-webkit-keyframes fadeInUp-data-v-6dd487e0{from{opacity:0;-webkit-transform:translateY(%?20?%);transform:translateY(%?20?%)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fadeInUp-data-v-6dd487e0{from{opacity:0;-webkit-transform:translateY(%?20?%);transform:translateY(%?20?%)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}.container[data-v-6dd487e0]{min-height:100vh;background-color:#f8f9fc;padding:%?30?%;background:linear-gradient(145deg,#f8faff,#e9f0f8);letter-spacing:%?1?%}\n/* 用户信息卡片 */.user-card[data-v-6dd487e0]{background-color:#fff;border-radius:%?24?%;padding:%?40?% %?30?%;display:flex;flex-direction:row;align-items:center;box-shadow:0 %?8?% %?30?% rgba(0,0,0,.04);margin-bottom:%?30?%;position:relative;-webkit-animation:fadeInUp-data-v-6dd487e0 .5s ease;animation:fadeInUp-data-v-6dd487e0 .5s ease;transition:all .3s ease}.user-card[data-v-6dd487e0]:active{-webkit-transform:scale(.98);transform:scale(.98)}.avatar-section[data-v-6dd487e0]{margin-right:%?30?%}.avatar-container[data-v-6dd487e0]{width:%?150?%;height:%?150?%;border-radius:%?75?%;position:relative;overflow:hidden;box-shadow:0 %?8?% %?16?% rgba(0,0,0,.15)}.default-avatar-bg[data-v-6dd487e0]{position:absolute;top:0;left:0;width:100%;height:100%;border-radius:%?75?%;background:linear-gradient(135deg,#3a86ff,#2563eb);display:flex;justify-content:center;align-items:center;z-index:1}.avatar-image[data-v-6dd487e0]{position:absolute;top:0;left:0;width:100%;height:100%;border-radius:%?75?%;z-index:2;transition:opacity .3s ease}\n/* 未登录状态的默认头像样式 */.default-avatar[data-v-6dd487e0]{width:%?150?%;height:%?150?%;border-radius:%?75?%;background:linear-gradient(135deg,#3a86ff,#2563eb);display:flex;justify-content:center;align-items:center;box-shadow:0 %?8?% %?16?% rgba(0,120,255,.2)}.user-info[data-v-6dd487e0]{flex:1}.username[data-v-6dd487e0]{font-size:%?36?%;font-weight:700;color:#2b2e4a;margin-bottom:%?16?%;display:block;text-align:left!important;align-self:flex-start;width:100%}.username.login-prompt[data-v-6dd487e0]{color:#4a78c9}.login-tip[data-v-6dd487e0]{font-size:%?28?%;color:#8a94a6;margin-bottom:%?16?%}.card-arrow[data-v-6dd487e0]{position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.role-tags[data-v-6dd487e0]{display:flex;flex-direction:row;flex-wrap:wrap}.role-tag[data-v-6dd487e0]{font-size:%?24?%;color:#4a78c9;background-color:rgba(74,120,201,.1);padding:%?6?% %?20?%;border-radius:%?30?%;margin-right:%?16?%;margin-bottom:%?8?%;transition:all .2s ease;letter-spacing:%?1.5?%}.role-tag[data-v-6dd487e0]:active{background-color:rgba(74,120,201,.2)}\n/* 登录提示区域 */.login-required-tip[data-v-6dd487e0]{background-color:#fff;border-radius:%?24?%;padding:%?60?% %?30?%;display:flex;flex-direction:column;align-items:center;justify-content:center;box-shadow:0 %?8?% %?30?% rgba(0,0,0,.04);margin-top:%?30?%;-webkit-animation:fadeInUp-data-v-6dd487e0 .6s ease;animation:fadeInUp-data-v-6dd487e0 .6s ease}.login-required-image[data-v-6dd487e0]{width:%?200?%;height:%?200?%;margin-bottom:%?30?%;opacity:.9}.login-required-text[data-v-6dd487e0]{font-size:%?32?%;color:#8a94a6;text-align:center;letter-spacing:%?2?%}\n/* 功能中心区域 */.patrol-section[data-v-6dd487e0]{background-color:#fff;border-radius:%?24?%;margin-bottom:%?30?%;padding:%?40?% %?30?%;box-shadow:0 %?8?% %?30?% rgba(0,0,0,.04);-webkit-animation:fadeInUp-data-v-6dd487e0 .5s ease;animation:fadeInUp-data-v-6dd487e0 .5s ease;-webkit-animation-delay:.1s;animation-delay:.1s;-webkit-animation-fill-mode:both;animation-fill-mode:both}.patrol-title[data-v-6dd487e0]{font-size:%?34?%;font-weight:600;color:#2b2e4a;margin-bottom:%?36?%;padding-left:%?20?%;position:relative;letter-spacing:%?2?%}.patrol-title[data-v-6dd487e0]::before{content:"";position:absolute;left:0;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:%?6?%;height:%?32?%;background:linear-gradient(180deg,#3a86ff,#2563eb);border-radius:%?6?%}.patrol-scroll[data-v-6dd487e0]{white-space:nowrap;width:100%}\n/* 非微信小程序环境下显示滚动条 */.patrol-scroll[data-v-6dd487e0] ::-webkit-scrollbar{width:6px;height:6px}.patrol-scroll[data-v-6dd487e0] ::-webkit-scrollbar-thumb{background:rgba(0,0,0,.2);border-radius:3px}.patrol-scroll[data-v-6dd487e0] ::-webkit-scrollbar-thumb:hover{background:rgba(0,0,0,.3)}.patrol-scroll[data-v-6dd487e0]{scrollbar-width:thin;scrollbar-color:rgba(0,0,0,.2) transparent}.patrol-grid[data-v-6dd487e0]{display:inline-flex;flex-direction:row;padding:%?10?% 0}.patrol-item[data-v-6dd487e0]{display:inline-flex;flex-direction:column;align-items:center;width:%?160?%;margin-right:%?50?%;transition:all .3s ease}.patrol-item[data-v-6dd487e0]:active{-webkit-transform:scale(.95);transform:scale(.95)}\n/* 图标样式统一管理 */.action-icon[data-v-6dd487e0], .patrol-icon[data-v-6dd487e0]{display:flex;align-items:center;justify-content:center;border-radius:%?12?%;transition:all .3s ease}.patrol-icon[data-v-6dd487e0]{width:%?120?%;height:%?120?%;border-radius:50%;margin-bottom:%?16?%;box-shadow:0 %?8?% %?16?% rgba(0,0,0,.15);position:relative;overflow:hidden}.patrol-icon[data-v-6dd487e0]:before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(hsla(0,0%,100%,.2),hsla(0,0%,100%,0));z-index:1}.patrol-icon[data-v-6dd487e0]:active{-webkit-transform:scale(.9);transform:scale(.9)}\n/* 图标颜色-使用渐变色 */.news-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#5586e8,#2563eb)}.export-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#49a2e3,#3794dc)}.responsible-tasks-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#4caf50,#45a049)}.user-management-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#667eea,#764ba2)}.gm-supervision-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#0ea5e9,#3b82f6)}\n/* 厂长监督-蓝色水务主题 */.cleanup-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#ff6b6b,#ee5a24)}.settings-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#7b8de0,#5e6fd8)}.logout-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#e06666,#d44c4c)}.record-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#47b8e0,#32a7d6)}\n/* 巡视记录 */.calendar-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#4a95e5,#3887df)}.area-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#4aabe5,#3a9ddf)}.route-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#7469d4,#5c4fc2)}\n/* 线路管理-紫色系 */.manage-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#e06666,#d44c4c)}.shift-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#e0984a,#d6893c)}\n/* 班次时间 */.system-settings-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#4a95e5,#3887df)}.point-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#66aee0,#4ca0d9)}\n/* 点位管理 */.guide-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#7b8de0,#5e6fd8)}.database-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#e06666,#d44c4c)}.task-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#3975d9,#2862c6)}\n/* 任务管理-深蓝色系 */.collection-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#8e44ad,#6c3483)}\n/* 数据采集-紫色系 */.notice-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#4a95e5,#3887df)}.config-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#4cb050,#389e3c)}.qrcode-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#4cb050,#389e3c)}.gallery-icon[data-v-6dd487e0]{background:linear-gradient(145deg,#ff6b6b,#ee5a24)}\n/* 荣誉展厅-温暖橙红色 */.patrol-text[data-v-6dd487e0]{font-size:%?28?%;color:#3d4b66;text-align:center;white-space:normal;width:100%;letter-spacing:%?2?%;margin-top:%?12?%;font-weight:500}\n/* 功能按钮区域 */.action-list[data-v-6dd487e0]{background-color:#fff;border-radius:%?24?%;overflow:hidden;margin-bottom:%?30?%;box-shadow:0 %?8?% %?30?% rgba(0,0,0,.04);-webkit-animation:fadeInUp-data-v-6dd487e0 .5s ease;animation:fadeInUp-data-v-6dd487e0 .5s ease;-webkit-animation-delay:.2s;animation-delay:.2s;-webkit-animation-fill-mode:both;animation-fill-mode:both}.action-item[data-v-6dd487e0]{display:flex;flex-direction:row;align-items:center;padding:%?30?%;border-bottom:%?1?% solid #eef0f6;transition:all .2s ease}.action-item[data-v-6dd487e0]:last-child{border-bottom:none}.action-item[data-v-6dd487e0]:active{background-color:#f8f9fc}.action-icon[data-v-6dd487e0]{width:%?80?%;height:%?80?%;border-radius:%?16?%;box-shadow:0 %?6?% %?12?% rgba(0,0,0,.12);position:relative;overflow:hidden}.action-icon[data-v-6dd487e0]:before{content:"";position:absolute;top:0;left:0;width:100%;height:50%;background:linear-gradient(hsla(0,0%,100%,.2),hsla(0,0%,100%,0));z-index:1}.action-content[data-v-6dd487e0]{flex:1;margin-left:%?24?%;position:relative;display:flex;align-items:center;justify-content:flex-start}.action-text[data-v-6dd487e0]{font-size:%?32?%;color:#3d4b66;letter-spacing:%?2?%;position:relative;text-align:left;align-self:flex-start}.action-badge[data-v-6dd487e0]{background:#ff5a5f;color:#fff;font-size:%?20?%;padding:%?2?% %?8?%;border-radius:%?10?%;min-width:%?24?%;text-align:center;line-height:1.2;position:absolute;top:%?-16?%;right:%?-5?%;\n  /* 调整到更贴近"务"字的右上角 */-webkit-transform:scale(.9);transform:scale(.9);z-index:10}.logout-text[data-v-6dd487e0]{color:#ff5a5f}\n/* 待办事项区域 */.todo-section[data-v-6dd487e0]{background-color:#fff;border-radius:%?24?%;padding:%?40?% %?30?%;box-shadow:0 %?8?% %?30?% rgba(0,0,0,.04);-webkit-animation:fadeInUp-data-v-6dd487e0 .5s ease;animation:fadeInUp-data-v-6dd487e0 .5s ease;-webkit-animation-delay:.3s;animation-delay:.3s;-webkit-animation-fill-mode:both;animation-fill-mode:both}.section-header[data-v-6dd487e0]{display:flex;flex-direction:row;justify-content:space-between;align-items:center;margin-bottom:%?30?%}.section-title[data-v-6dd487e0]{font-size:%?34?%;font-weight:600;color:#2b2e4a;position:relative;padding-left:%?20?%;letter-spacing:%?2?%}.section-title[data-v-6dd487e0]::before{content:"";position:absolute;left:0;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:%?6?%;height:%?32?%;background:linear-gradient(180deg,#ff5a5f,#ff3a3f);border-radius:%?6?%}.todo-count-wrapper[data-v-6dd487e0]{display:flex;flex-direction:row;align-items:center}.todo-count[data-v-6dd487e0]{font-size:%?26?%;color:#ff5a5f;background-color:rgba(255,90,95,.1);padding:%?8?% %?20?%;border-radius:%?30?%;transition:all .2s ease}.todo-count[data-v-6dd487e0]:active{background-color:rgba(255,90,95,.2)}.todo-list[data-v-6dd487e0]{margin-top:%?20?%}.todo-item[data-v-6dd487e0]{display:flex;flex-direction:row;align-items:center;padding:%?24?%;border-bottom:%?1?% solid #eef0f6;border-radius:%?12?%;margin-bottom:%?16?%;transition:all .3s ease;background-color:#f8f9fc}.todo-item[data-v-6dd487e0]:last-child{border-bottom:none;margin-bottom:0}.todo-item[data-v-6dd487e0]:active{background-color:#eef1f8;-webkit-transform:translateY(%?2?%);transform:translateY(%?2?%)}.todo-item .todo-content[data-v-6dd487e0]{flex:1;margin-right:%?20?%}.todo-item .todo-title[data-v-6dd487e0]{display:flex;flex-direction:row;align-items:center;margin-bottom:%?16?%;flex-wrap:wrap}.todo-item .todo-title .name[data-v-6dd487e0]{font-size:%?30?%;font-weight:700;color:#2b2e4a;margin-right:%?16?%;margin-bottom:%?8?%;letter-spacing:%?2?%}.todo-item .todo-title .project[data-v-6dd487e0]{font-size:%?24?%;color:#4a78c9;background-color:rgba(74,120,201,.1);padding:%?4?% %?16?%;border-radius:%?20?%;margin-bottom:%?8?%;letter-spacing:%?2?%}.todo-item .description[data-v-6dd487e0]{font-size:%?28?%;color:#718096;margin-bottom:%?16?%;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;line-clamp:2;overflow:hidden;line-height:1.6;letter-spacing:%?1.5?%}.todo-item .todo-footer[data-v-6dd487e0]{display:flex;flex-direction:row;align-items:center;justify-content:space-between}.todo-item .todo-footer .time[data-v-6dd487e0]{font-size:%?24?%;color:#a0aec0;letter-spacing:%?1?%}.todo-item .todo-footer .todo-type[data-v-6dd487e0]{font-size:%?22?%;color:#4a78c9;background-color:rgba(74,120,201,.1);padding:%?4?% %?12?%;border-radius:%?16?%;border:%?1?% solid rgba(74,120,201,.3);margin-left:%?16?%;letter-spacing:%?1?%}.empty-todo[data-v-6dd487e0]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?60?% 0}.empty-todo .empty-image[data-v-6dd487e0]{width:%?200?%;height:%?200?%;margin-bottom:%?30?%;opacity:.8}.empty-todo .empty-text[data-v-6dd487e0]{font-size:%?28?%;color:#8a94a6;text-align:center;letter-spacing:%?2?%}',""]),e.exports=t},"2d76":function(e,t,a){"use strict";a.r(t);var n=a("14db"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},"5edc":function(e,t,a){var n=a("249e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("af6284e0",n,!0,{sourceMap:!1,shadowMode:!1})},"72b8":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"p-empty-state",style:e.containerStyle},[a("v-uni-image",{staticClass:"p-empty-state__icon",style:e.iconStyle,attrs:{src:e.icon||e.defaultIcon,mode:"aspectFit"}}),a("v-uni-text",{staticClass:"p-empty-state__text",style:{color:e.textColor}},[e._v(e._s(e.text||"暂无数据"))]),e.showAction?a("v-uni-button",{staticClass:"p-empty-state__action",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$emit("action")}}},[e._v(e._s(e.actionText))]):e._e()],1)},i=[]},"73e1":function(e,t,a){"use strict";var n=a("29d8");e.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(n)},"795c":function(e,t,a){"use strict";var n=a("8bdb"),i=a("db04").start,o=a("73e1");n({target:"String",proto:!0,forced:o},{padStart:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},"7fc2":function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.calculateEndTime=g,t.calculateRoundTime=p,t.default=void 0,t.detectTimeZone=h,t.ensureCorrectTimeZone=b,t.formatDate=s,t.formatTime=v,t.getDaysDiff=u,t.getMonthFirstDay=d,t.getMonthLastDay=l,t.getRelativeTime=c,t.isTimeInRange=m,t.isToday=f,t.preprocessDates=w,t.safeDateFormat=y,t.smartFormatDate=k;var i=n(a("9b1b")),o=n(a("fcf3")),r=n(a("5de6"));function s(e){var t,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";if(!e)return"";if("string"===typeof e)if(!e.includes("T")||!e.includes("Z")&&e.includes("+")){if(e.includes(",")&&(e.includes("AM")||e.includes("PM"))){var n=e.split(",");if(n.length>=1){var i=n[0].trim(),o=i.split("/");if(3===o.length){var r=o[0].padStart(2,"0"),s=o[1].padStart(2,"0"),c=o[2];e="".concat(c,"/").concat(r,"/").concat(s)}}}t=new Date(e.replace(/-/g,"/"))}else{console.log("检测到需要调整时区的ISO日期:",e);try{var d=e.split("T")[0],l=e.includes("T")?e.split("T")[1].split(".")[0]:"00:00:00";t=new Date("".concat(d,"T").concat(l,"+08:00")),isNaN(t.getTime())&&(t=new Date(e.replace(/-/g,"/")))}catch(h){console.warn("处理日期时区出错",h),t=new Date(e.replace(/-/g,"/"))}}else if("number"===typeof e)t=new Date(e);else{if(!(e instanceof Date))return"";t=e}isNaN(t.getTime())&&(console.warn("无效的日期格式:",e),t=new Date);var u=t.getFullYear(),f=t.getMonth()+1,p=t.getDate(),g=t.getHours(),v=t.getMinutes(),m=t.getSeconds();return a.replace(/YYYY/g,u).replace(/YY/g,String(u).slice(2)).replace(/MM/g,(f<10?"0":"")+f).replace(/M/g,f).replace(/DD/g,(p<10?"0":"")+p).replace(/D/g,p).replace(/HH/g,(g<10?"0":"")+g).replace(/H/g,g).replace(/mm/g,(v<10?"0":"")+v).replace(/m/g,v).replace(/ss/g,(m<10?"0":"")+m).replace(/s/g,m)}function c(e){if(!e)return"";var t;if("string"===typeof e){var a=(new Date).getFullYear(),n=a+3,i=e.match(/^(\d{4})/);if(i&&parseInt(i[1])>=n)return console.log("检测到远期未来日期，自动转换为今天:",e),"今天";if(e.includes(",")&&(e.includes("AM")||e.includes("PM"))){var o=e.split(",");if(o.length>=1){var r=o[0].trim(),s=r.split("/");if(3===s.length){var c=s[0].padStart(2,"0"),d=s[1].padStart(2,"0"),l=s[2];e="".concat(l,"/").concat(c,"/").concat(d)}}}t=new Date(e.replace(/-/g,"/"))}else if("number"===typeof e)t=new Date(e);else{if(!(e instanceof Date))return"";t=e}if(isNaN(t.getTime()))return console.warn("无效的日期格式:",e),"未知时间";var u=new Date,f=new Date(u);if(f.setFullYear(u.getFullYear()+1),t>f)return"今天";var p=u.getTime()-t.getTime();if(p<0)return"即将";var g=864e5;return p<6e4?"刚刚":p<36e5?Math.floor(p/6e4)+"分钟前":p<g?Math.floor(p/36e5)+"小时前":p<6048e5?Math.floor(p/g)+"天前":p<2592e6?Math.floor(p/6048e5)+"周前":p<31536e6?Math.floor(p/2592e6)+"个月前":Math.floor(p/31536e6)+"年前"}function d(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,t=e.getFullYear(),a=e.getMonth();return new Date(t,a,1)}function l(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,t=e.getFullYear(),a=e.getMonth();return new Date(t,a+1,0)}function u(e,t){var a=new Date(e),n=new Date(t);a.setHours(0,0,0,0),n.setHours(0,0,0,0);var i=n.getTime()-a.getTime();return Math.round(i/864e5)}function f(e){if(!e)return!1;var t;if("string"===typeof e){if(e.includes(",")&&(e.includes("AM")||e.includes("PM"))){var a=e.split(",");if(a.length>=1){var n=a[0].trim(),i=n.split("/");if(3===i.length){var o=i[0].padStart(2,"0"),r=i[1].padStart(2,"0"),s=i[2];e="".concat(s,"/").concat(o,"/").concat(r)}}}t=new Date(e.replace(/-/g,"/"))}else if("number"===typeof e)t=new Date(e);else{if(!(e instanceof Date))return!1;t=e}if(isNaN(t.getTime()))return console.warn("无效的日期格式:",e),!1;var c=new Date;return t.getDate()===c.getDate()&&t.getMonth()===c.getMonth()&&t.getFullYear()===c.getFullYear()}function p(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(!e||!t)return console.warn("计算轮次时间：参数不完整",e,t,a),new Date;try{var n,i=t.split(":").map(Number),o=(0,r.default)(i,2),s=o[0],c=o[1];if("string"===typeof e){if(e.includes(",")&&(e.includes("AM")||e.includes("PM"))){var d=e.split(",");if(d.length>=1){var l=d[0].trim(),u=l.split("/");if(3===u.length){var f=u[0].padStart(2,"0"),p=u[1].padStart(2,"0"),g=u[2];e="".concat(g,"/").concat(f,"/").concat(p)}}}n=new Date(e.replace(/-/g,"/"))}else n=e instanceof Date?new Date(e):new Date;return isNaN(n.getTime())?(console.warn("无效的日期格式:",e),new Date):(n.setHours(s,c,0,0),a&&"number"===typeof a&&n.setDate(n.getDate()+a),n)}catch(v){return console.error("计算轮次时间出错:",v),new Date}}function g(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60;if(!e)return console.warn("计算结束时间：开始时间无效",e),new Date;try{var a=new Date(e.getTime());return a.setMinutes(a.getMinutes()+(t||60)),a}catch(n){return console.error("计算结束时间出错:",n),new Date}}function v(e){if(!e)return"";try{var t=e.getHours().toString().padStart(2,"0"),a=e.getMinutes().toString().padStart(2,"0");return"".concat(t,":").concat(a)}catch(n){return console.error("格式化时间出错:",n),""}}function m(e,t,a){var n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return!!(e&&t&&a)&&(n?e>=t&&e<=a:e>=t&&e<a)}function h(){try{var e=new Date,t=e.getTimezoneOffset(),a=-t/60,n={8:"北京时间(UTC+8)",9:"东京时间(UTC+9)",7:"雅加达时间(UTC+7)",0:"格林威治时间(UTC+0)","-5":"纽约时间(UTC-5)","-8":"洛杉矶时间(UTC-8)"}[a]||"UTC".concat(a>=0?"+":"").concat(a);return console.log("当前时区检测: ".concat(n)),console.log("- 本地时间: ".concat(e.toString())),console.log("- ISO格式: ".concat(e.toISOString())),console.log("- 时区偏移(分钟): ".concat(t)),n}catch(i){return console.error("检测时区出错:",i),"北京时间(UTC+8)"}}function b(e){if(!e)return null;try{if("string"===typeof e&&e.endsWith("Z")&&e.includes("T"))return e;var t=new Date(e);return isNaN(t.getTime())?(console.warn("无效的日期格式:",e),e):t.toISOString()}catch(a){return console.error("时区转换错误:",a,e),e}}function w(e){if(!e)return e;if("object"===(0,o.default)(e)){if(Array.isArray(e))return e.map((function(e){return w(e)}));var t=(0,i.default)({},e);for(var a in t)t.hasOwnProperty(a)&&("string"===typeof t[a]&&(a.includes("time")||a.includes("Time")||a.includes("date")||a.includes("Date"))?t[a]=b(t[a]):"object"===(0,o.default)(t[a])&&(t[a]=w(t[a])));return t}return e}function k(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"display";if(!e)return"";try{var a;if("string"===typeof e)a=new Date(e.replace(/-/g,"/"));else if("number"===typeof e)a=new Date(e);else{if(!(e instanceof Date))return"";a=e}return isNaN(a.getTime())?(console.warn("无效的日期格式:",e),""):"storage"===t?a.toISOString():"time"===t?v(a):s(a,"YYYY-MM-DD HH:mm")}catch(n){return console.error("智能格式化日期出错:",n),""}}function y(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";try{if(!e)return"未知时间";if("string"===typeof e){var a=(new Date).getFullYear(),n=a+3,i=e.match(/^(\d{4})/);if(i&&parseInt(i[1])>=n)return console.log("安全格式化检测到远期未来日期，使用当前时间:",e),s(new Date,t)}if("string"===typeof e&&e.includes("Z"))try{var o=new Date(e),r=new Date(o.getTime()+288e5);return s(r,t)}catch(c){console.warn("UTC转本地时间失败:",c)}return s(e,t)}catch(c){return console.warn("日期安全格式化失败:",c,e),"未知时间"}}a("4626"),a("5ac7"),a("c223"),a("5c47"),a("a1c1"),a("0c26"),a("795c"),a("f7a5"),a("2c10"),a("e966"),a("fd3c"),a("64aa"),a("c9b5"),a("bf0f"),a("ab80"),a("9327");var x={formatDate:s,getRelativeTime:c,getMonthFirstDay:d,getMonthLastDay:l,getDaysDiff:u,isToday:f,calculateRoundTime:p,calculateEndTime:g,formatTime:v,isTimeInRange:m,detectTimeZone:h,ensureCorrectTimeZone:b,preprocessDates:w,smartFormatDate:k,safeDateFormat:y};t.default=x},9327:function(e,t,a){"use strict";var n=a("8bdb"),i=a("9f69"),o=a("1ded").f,r=a("c435"),s=a("9e70"),c=a("b6a1"),d=a("862c"),l=a("0931"),u=a("a734"),f=i("".slice),p=Math.min,g=l("endsWith"),v=!u&&!g&&!!function(){var e=o(String.prototype,"endsWith");return e&&!e.writable}();n({target:"String",proto:!0,forced:!v&&!g},{endsWith:function(e){var t=s(d(this));c(e);var a=arguments.length>1?arguments[1]:void 0,n=t.length,i=void 0===a?n:p(r(a),n),o=s(e);return f(t,i-o.length,i)===o}})},"9b76":function(e,t,a){"use strict";a.r(t);var n=a("72b8"),i=a("b6c2");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("0777");var r=a("828b"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"bf86960a",null,!1,n["a"],void 0);t["default"]=s.exports},a915:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return n}));var n={uniIcons:a("6ddf").default,pEmptyState:a("9b76").default},i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"container"},[e.hasLogin?a("v-uni-view",{staticClass:"user-card"},[a("v-uni-view",{staticClass:"avatar-section"},[a("v-uni-view",{staticClass:"avatar-container"},[a("v-uni-view",{staticClass:"default-avatar-bg"},[a("uni-icons",{attrs:{type:"person-filled",size:"40",color:"#FFFFFF"}})],1),e.userInfo.avatar_file&&e.userInfo.avatar_file.url?a("v-uni-image",{staticClass:"avatar-image",style:{opacity:e.avatarLoaded?1:0},attrs:{src:e.userInfo.avatar_file.url,mode:"aspectFill"},on:{load:function(t){arguments[0]=t=e.$handleEvent(t),e.onAvatarLoad.apply(void 0,arguments)},error:function(t){arguments[0]=t=e.$handleEvent(t),e.onAvatarError.apply(void 0,arguments)}}}):e.userInfo.avatar?a("v-uni-image",{staticClass:"avatar-image",style:{opacity:e.avatarLoaded?1:0},attrs:{src:e.userInfo.avatar,mode:"aspectFill"},on:{load:function(t){arguments[0]=t=e.$handleEvent(t),e.onAvatarLoad.apply(void 0,arguments)},error:function(t){arguments[0]=t=e.$handleEvent(t),e.onAvatarError.apply(void 0,arguments)}}}):e._e()],1)],1),a("v-uni-view",{staticClass:"user-info"},[a("v-uni-text",{staticClass:"username"},[e._v(e._s(e.userInfo.nickname||e.userInfo.username||"未设置昵称"))]),e.roleNames&&e.roleNames.length>0?a("v-uni-view",{staticClass:"role-tags"},e._l(e.roleNames,(function(t,n){return a("v-uni-text",{key:n,staticClass:"role-tag"},[e._v(e._s(t))])})),1):e._e()],1)],1):a("v-uni-view",{staticClass:"user-card",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goToLogin.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"avatar-section"},[a("v-uni-view",{staticClass:"default-avatar"},[a("uni-icons",{attrs:{type:"person-filled",size:"40",color:"#FFFFFF"}})],1)],1),a("v-uni-view",{staticClass:"user-info"},[a("v-uni-text",{staticClass:"username login-prompt"},[e._v("点击登录")]),a("v-uni-text",{staticClass:"login-tip"},[e._v("登录后可查看更多功能")])],1),a("v-uni-view",{staticClass:"card-arrow"},[a("uni-icons",{attrs:{type:"right",size:"16",color:"#BBBBBB"}})],1)],1),e.hasLogin?[a("v-uni-view",{staticClass:"patrol-section"},[a("v-uni-view",{staticClass:"patrol-title"},[e._v("综合中心")]),a("v-uni-scroll-view",{staticClass:"patrol-scroll",attrs:{"scroll-x":"true","show-scrollbar":!1}},[a("v-uni-view",{staticClass:"patrol-grid"},[e.uniIDHasRole("supervisor")||e.uniIDHasRole("PM")||e.uniIDHasRole("GM")||e.uniIDHasRole("admin")?a("v-uni-view",{staticClass:"patrol-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.navTo("/pages/patrol_pkg/point/index")}}},[a("v-uni-view",{staticClass:"patrol-icon point-icon"},[a("uni-icons",{attrs:{type:"location-filled",size:"24",color:"#FFFFFF"}})],1),a("v-uni-text",{staticClass:"patrol-text"},[e._v("点位管理")])],1):e._e(),e.uniIDHasRole("supervisor")||e.uniIDHasRole("PM")||e.uniIDHasRole("GM")||e.uniIDHasRole("admin")?a("v-uni-view",{staticClass:"patrol-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.navTo("/pages/patrol_pkg/shift/index")}}},[a("v-uni-view",{staticClass:"patrol-icon shift-icon"},[a("uni-icons",{attrs:{type:"calendar",size:"24",color:"#FFFFFF"}})],1),a("v-uni-text",{staticClass:"patrol-text"},[e._v("班次管理")])],1):e._e(),e.uniIDHasRole("supervisor")||e.uniIDHasRole("PM")||e.uniIDHasRole("GM")||e.uniIDHasRole("admin")?a("v-uni-view",{staticClass:"patrol-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.navTo("/pages/patrol_pkg/route/index")}}},[a("v-uni-view",{staticClass:"patrol-icon route-icon"},[a("uni-icons",{attrs:{type:"map-filled",size:"24",color:"#FFFFFF"}})],1),a("v-uni-text",{staticClass:"patrol-text"},[e._v("线路管理")])],1):e._e(),a("v-uni-view",{staticClass:"patrol-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.navTo("/pages/patrol_pkg/task/index")}}},[a("v-uni-view",{staticClass:"patrol-icon task-icon"},[a("uni-icons",{attrs:{type:"flag-filled",size:"24",color:"#FFFFFF"}})],1),a("v-uni-text",{staticClass:"patrol-text"},[e._v("任务管理")])],1),a("v-uni-view",{staticClass:"patrol-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.navTo("/pages/patrol_pkg/record/index")}}},[a("v-uni-view",{staticClass:"patrol-icon record-icon"},[a("uni-icons",{attrs:{type:"bars",size:"24",color:"#FFFFFF"}})],1),a("v-uni-text",{staticClass:"patrol-text"},[e._v("巡视记录")])],1),e.uniIDHasRole("reviser")||e.uniIDHasRole("supervisor")||e.uniIDHasRole("PM")||e.uniIDHasRole("GM")||e.uniIDHasRole("admin")?a("v-uni-view",{staticClass:"patrol-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.navTo("/pages/patrol_pkg/point/qrcode-batch")}}},[a("v-uni-view",{staticClass:"patrol-icon qrcode-icon"},[a("uni-icons",{attrs:{type:"scan",size:"24",color:"#FFFFFF"}})],1),a("v-uni-text",{staticClass:"patrol-text"},[e._v("二维码管理")])],1):e._e(),a("v-uni-view",{staticClass:"patrol-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.navTo("/pages/honor_pkg/gallery/index")}}},[a("v-uni-view",{staticClass:"patrol-icon gallery-icon"},[a("uni-icons",{attrs:{type:"star-filled",size:"24",color:"#FFFFFF"}})],1),a("v-uni-text",{staticClass:"patrol-text"},[e._v("荣誉展厅")])],1)],1)],1)],1),a("v-uni-view",{staticClass:"action-list"},[a("v-uni-view",{staticClass:"action-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goToReadNewsLog.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"action-icon news-icon"},[a("uni-icons",{attrs:{type:"notification-filled",size:"22",color:"#FFFFFF"}})],1),a("v-uni-view",{staticClass:"action-content"},[a("v-uni-text",{staticClass:"action-text"},[e._v("公告通知")])],1),a("uni-icons",{attrs:{type:"right",size:"16",color:"#CCCCCC"}})],1),a("v-uni-view",{staticClass:"action-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.checkExportPermission.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"action-icon export-icon"},[a("uni-icons",{attrs:{type:"download",size:"22",color:"#FFFFFF"}})],1),a("v-uni-view",{staticClass:"action-content"},[a("v-uni-text",{staticClass:"action-text"},[e._v("文档导出")])],1),a("uni-icons",{attrs:{type:"right",size:"16",color:"#CCCCCC"}})],1),e.hasResponsiblePermission?a("v-uni-view",{staticClass:"action-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goToResponsibleTasks.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"action-icon responsible-tasks-icon"},[a("uni-icons",{attrs:{type:"list",size:"22",color:"#FFFFFF"}})],1),a("v-uni-view",{staticClass:"action-content"},[a("v-uni-text",{staticClass:"action-text"},[e._v("我的任务")]),e.responsibleTaskCount>0?a("v-uni-text",{staticClass:"action-badge"},[e._v(e._s(e.responsibleTaskCount))]):e._e()],1),a("uni-icons",{attrs:{type:"right",size:"16",color:"#CCCCCC"}})],1):e._e(),e.hasAdminPermission?a("v-uni-view",{staticClass:"action-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goToUserManagement.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"action-icon user-management-icon"},[a("uni-icons",{attrs:{type:"person-filled",size:"22",color:"#FFFFFF"}})],1),a("v-uni-view",{staticClass:"action-content"},[a("v-uni-text",{staticClass:"action-text"},[e._v("用户管理")])],1),a("uni-icons",{attrs:{type:"right",size:"16",color:"#CCCCCC"}})],1):e._e(),e.hasGMPermission?a("v-uni-view",{staticClass:"action-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goToGMSupervision.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"action-icon gm-supervision-icon"},[a("uni-icons",{attrs:{type:"eye-filled",size:"22",color:"#FFFFFF"}})],1),a("v-uni-view",{staticClass:"action-content"},[a("v-uni-text",{staticClass:"action-text"},[e._v("指派监督")]),e.supervisionTaskCount>0?a("v-uni-text",{staticClass:"action-badge"},[e._v(e._s(e.supervisionTaskCount))]):e._e()],1),a("uni-icons",{attrs:{type:"right",size:"16",color:"#CCCCCC"}})],1):e._e(),a("v-uni-view",{staticClass:"action-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.modifyNickname.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"action-icon settings-icon"},[a("uni-icons",{attrs:{type:"gear",size:"22",color:"#FFFFFF"}})],1),a("v-uni-view",{staticClass:"action-content"},[a("v-uni-text",{staticClass:"action-text"},[e._v("用户设置")])],1),a("uni-icons",{attrs:{type:"right",size:"16",color:"#CCCCCC"}})],1),a("v-uni-view",{staticClass:"action-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.logout.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"action-icon logout-icon"},[a("uni-icons",{attrs:{type:"closeempty",size:"22",color:"#FFFFFF"}})],1),a("v-uni-view",{staticClass:"action-content"},[a("v-uni-text",{staticClass:"action-text logout-text"},[e._v("退出登录")])],1),a("uni-icons",{attrs:{type:"right",size:"16",color:"#CCCCCC"}})],1)],1),e.hasLogin&&e.hasRole?a("v-uni-view",{staticClass:"todo-section"},[a("v-uni-view",{staticClass:"section-header"},[a("v-uni-text",{staticClass:"section-title"},[e._v("我的待办")]),e.hasRole?a("v-uni-view",{staticClass:"todo-count-wrapper",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goToTodoList.apply(void 0,arguments)}}},[e.todoCount>0?a("v-uni-text",{staticClass:"todo-count"},[e._v("共"+e._s(e.todoCount)+"项待处理")]):a("v-uni-text",{staticClass:"todo-count"},[e._v("暂无待办")])],1):e._e()],1),e.todoList.length>0?a("v-uni-view",{staticClass:"todo-list"},e._l(e.todoList,(function(t,n){return a("v-uni-view",{key:n,staticClass:"todo-item",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.handleTodoClick(t)}}},[a("v-uni-view",{staticClass:"todo-content"},[a("v-uni-view",{staticClass:"todo-title"},[a("v-uni-text",{staticClass:"name"},[e._v(e._s(t.name))]),a("v-uni-text",{staticClass:"project"},[e._v(e._s(e.getProjectName(t.project)))])],1),a("v-uni-text",{staticClass:"description"},[e._v(e._s(t.description))]),a("v-uni-view",{staticClass:"todo-footer"},[a("v-uni-text",{staticClass:"time"},[e._v(e._s(e.getTodoTimeText(t)))]),a("v-uni-text",{staticClass:"todo-type"},[e._v(e._s(e.getTodoTypeText(t.workflowStatus)))])],1)],1),a("uni-icons",{attrs:{type:"right",size:"16",color:"#BBBBBB"}})],1)})),1):a("p-empty-state",{attrs:{text:e.hasRole?"暂无待办事项":"权限不够无法查看待办事项",image:"/static/empty/empty_todo.png"}})],1):e._e()]:a("p-empty-state",{attrs:{text:"登录后查看更多功能",image:"/static/empty/empty_todo.png"}})],2)},o=[]},b050:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={name:"p-empty-state",props:{icon:{type:String,default:""},text:{type:String,default:"暂无数据"},type:{type:String,default:"default"},size:{type:String,default:"medium"},textColor:{type:String,default:"#999"},containerStyle:{type:Object,default:function(){return{}}},showAction:{type:Boolean,default:!1},actionText:{type:String,default:"点击操作"}},computed:{defaultIcon:function(){var e={default:"/static/empty/empty.png",task:"/static/empty/empty_task.png",record:"/static/empty/empty_record.png",search:"/static/empty/empty-search.png",data:"/static/empty/empty_data.png",todo:"/static/empty/empty_todo.png"};return e[this.type]||e.default},iconStyle:function(){var e={small:"80rpx",medium:"120rpx",large:"180rpx"},t=e[this.size]||e.medium;return{width:t,height:t}}}};t.default=n},b6c2:function(e,t,a){"use strict";a.r(t);var n=a("b050"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},b72a:function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.p-empty-state[data-v-bf86960a]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?40?%;box-sizing:border-box}.p-empty-state__icon[data-v-bf86960a]{width:%?120?%;height:%?120?%;margin-bottom:%?20?%}.p-empty-state__text[data-v-bf86960a]{font-size:%?28?%;color:#999;text-align:center;margin-bottom:%?20?%}.p-empty-state__action[data-v-bf86960a]{margin-top:%?20?%;background-color:#1677ff;color:#fff;font-size:%?28?%;border-radius:%?40?%;padding:%?10?% %?30?%;border:none}.p-empty-state__action[data-v-bf86960a]:active{opacity:.8}',""]),e.exports=t},bb54:function(e,t,a){"use strict";var n=a("5edc"),i=a.n(n);i.a},db04:function(e,t,a){"use strict";var n=a("bb80"),i=a("c435"),o=a("9e70"),r=a("f298"),s=a("862c"),c=n(r),d=n("".slice),l=Math.ceil,u=function(e){return function(t,a,n){var r,u,f=o(s(t)),p=i(a),g=f.length,v=void 0===n?" ":o(n);return p<=g||""===v?f:(r=p-g,u=c(v,l(r/v.length)),u.length>r&&(u=d(u,0,r)),e?f+u:u+f)}};e.exports={start:u(!1),end:u(!0)}},f298:function(e,t,a){"use strict";var n=a("497b"),i=a("9e70"),o=a("862c"),r=RangeError;e.exports=function(e){var t=i(o(this)),a="",s=n(e);if(s<0||s===1/0)throw new r("Wrong number of repetitions");for(;s>0;(s>>>=1)&&(t+=t))1&s&&(a+=t);return a}},fb20:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={}},ff36:function(e,t,a){"use strict";a.r(t);var n=a("a915"),i=a("2d76");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("bb54");var r=a("828b"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"6dd487e0",null,!1,n["a"],void 0);t["default"]=s.exports}}]);
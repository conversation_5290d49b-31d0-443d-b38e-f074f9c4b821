{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode.vue?8cdf", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode.vue?9245", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode.vue?b6cb", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode.vue?54bb", "uni-app:///pages/patrol_pkg/point/qrcode.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode.vue?644f", "webpack:///D:/Xwzc/pages/patrol_pkg/point/qrcode.vue?ea21"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pointInfo", "pointId", "errorMsg", "loading", "loadingText", "more", "noMore", "qrcodeGenerated", "qrcodeEnabled", "currentTime", "generating", "regenerating", "qrGeneratedTime", "saving", "hasEditPermission", "qrcodeContent", "isInitialLoad", "needsNewQRCode", "onLoad", "methods", "loadPointDetail", "PatrolApi", "res", "console", "generateQrCode", "qr<PERSON><PERSON>nt", "qrcode_content", "includeTimestamp", "id", "qrcode_generated_time", "updateResult", "uni", "title", "icon", "onQRCodeComplete", "qrcode_enabled", "regenerateQrCode", "QRCodeUtil", "result", "duration", "saveQrCode", "success", "fail", "goBack", "goToEdit", "url", "formatDate", "date", "showError"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClDA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACiHpnB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAD;QACAE;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;MACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACA;;gBAEA;gBACAC,+EACA;kBACAC;gBAAA,IACA;kBACAC;gBACA;;gBAEAJ;gBAAA;gBAAA;gBAAA,OAIAF;kBACAtB;oBACA6B;oBACAF;oBACAG;kBACA;gBACA;cAAA;gBANAC;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBAEAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAV;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAX;gBAAA,KACAD;kBAAA;kBAAA;gBAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAGAD;kBACAtB;oBACA6B;oBACAF;oBACAG;oBACAM;kBACA;gBACA;cAAA;gBAPAL;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACAC;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAR;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAIA;cAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAb;gBACAQ;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBACAV;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAGAQ;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACAK;cAAA;gBAAAC;gBACAf;gBAAA,MAEAe;kBAAA;kBAAA;gBAAA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEAP;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAV;gBACAQ;kBACAC;kBACAC;kBACAM;gBACA;cAAA;gBAAA;gBAEAR;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAR;gBACAQ;kBACAC;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAT;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACAF;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACAS;oBACAV;sBACAC;sBACAC;oBACA;kBACA;kBACAS;oBACAnB;oBACA;;oBAEA;oBACA;sBACArB;oBACA;oBAEA6B;sBACAC;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAAA;gBAEAF;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MACAZ;IACA;IAEA;IACAa;MACAb;QACAc;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;MACA;QACAxB;QACA;MACA;IACA;IAEA;IACAyB;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChbA;AAAA;AAAA;AAAA;AAA2oC,CAAgB,inCAAG,EAAC,C;;;;;;;;;;;ACA/pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/point/qrcode.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/point/qrcode.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./qrcode.vue?vue&type=template&id=4ccd2c0c&\"\nvar renderjs\nimport script from \"./qrcode.vue?vue&type=script&lang=js&\"\nexport * from \"./qrcode.vue?vue&type=script&lang=js&\"\nimport style0 from \"./qrcode.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/point/qrcode.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qrcode.vue?vue&type=template&id=4ccd2c0c&\"", "var components\ntry {\n  components = {\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    uqrcode: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode\" */ \"@/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !_vm.loading && !_vm.errorMsg && !!_vm.qrcodeEnabled && _vm.qrcodeGenerated\n      ? _vm.formatDate(_vm.qrGeneratedTime)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qrcode.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qrcode.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"qrcode-container\">\n\t\t<!-- 删除自定义导航栏，使用系统导航栏 -->\n\t\t<view class=\"content\">\n\t\t\t<!-- 加载状态 -->\n\t\t\t<view class=\"loading-box\" v-if=\"loading\">\n\t\t\t\t<uni-load-more status=\"loading\" :content-text=\"loadingText\"></uni-load-more>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 错误提示 -->\n\t\t\t<view class=\"error-box\" v-else-if=\"errorMsg\">\n\t\t\t\t<view class=\"error-icon\">\n\t\t\t\t\t<text class=\"iconfont icon-warning\"></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"error-text\">{{errorMsg}}</view>\n\t\t\t\t<view class=\"error-action\">\n\t\t\t\t\t<button class=\"btn-retry\" @click=\"loadPointDetail\">重试</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 二维码信息 -->\n\t\t\t<view class=\"qrcode-box\" v-else>\n\t\t\t\t<view class=\"qrcode-info\">\n\t\t\t\t\t<view class=\"qrcode-title\">{{pointInfo.name || '未命名巡检点'}}</view>\n\t\t\t\t\t<view class=\"qrcode-subtitle\">{{(pointInfo.location && pointInfo.location.address) || '无地址信息'}}</view>\n\t\t\t\t\t<view class=\"qrcode-id\">ID: {{pointInfo._id}}</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"qrcode-content\">\n\t\t\t\t\t<!-- 二维码未启用提示 -->\n\t\t\t\t\t<view class=\"qrcode-disabled\" v-if=\"!qrcodeEnabled\">\n\t\t\t\t\t\t<view class=\"disabled-icon\">\n\t\t\t\t\t\t\t<text class=\"iconfont icon-qrcode-disabled\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"disabled-text\">此巡检点未启用二维码</view>\n\t\t\t\t\t\t<view class=\"disabled-action\">\n\t\t\t\t\t\t\t<button class=\"btn-enable\" @click=\"goToEdit\" v-if=\"hasEditPermission\">去启用</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 二维码展示区域 -->\n\t\t\t\t\t<view class=\"qrcode-display\" v-else>\n\t\t\t\t\t\t<view class=\"qrcode-canvas-container\" :class=\"{ 'is-generating': generating }\">\n\t\t\t\t\t\t\t<!-- 加载中显示 -->\n\t\t\t\t\t\t\t<view class=\"qrcode-loading\" v-if=\"generating\">\n\t\t\t\t\t\t\t\t<uni-load-more status=\"loading\" :contentText=\"{ contentdown: '生成中...', contentrefresh: '生成中...' }\"></uni-load-more>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 二维码组件 -->\n\t\t\t\t\t\t\t<uqrcode\n\t\t\t\t\t\t\t\tv-if=\"qrcodeEnabled && qrcodeContent\"\n\t\t\t\t\t\t\t\tref=\"uqrcode\"\n\t\t\t\t\t\t\t\t:canvas-id=\"'qrcode-canvas'\"\n\t\t\t\t\t\t\t\tclass=\"qrcode-canvas\"\n\t\t\t\t\t\t\t\t:value=\"qrcodeContent\"\n\t\t\t\t\t\t\t\t:options=\"{\n\t\t\t\t\t\t\t\t\tsize: 200,\n\t\t\t\t\t\t\t\t\tmargin: 10,\n\t\t\t\t\t\t\t\t\tbackgroundColor: '#ffffff',\n\t\t\t\t\t\t\t\t\tforegroundColor: '#000000',\n\t\t\t\t\t\t\t\t\terrorCorrectLevel: 'H',\n\t\t\t\t\t\t\t\t\ttype: 'image'\n\t\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\t\t@complete=\"onQRCodeComplete\"\n\t\t\t\t\t\t\t></uqrcode>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"qrcode-meta\" v-if=\"qrcodeGenerated\">\n\t\t\t\t\t\t\t<view class=\"meta-item\">\n\t\t\t\t\t\t\t\t<text class=\"meta-label\">版本</text>\n\t\t\t\t\t\t\t\t<text class=\"meta-value\">{{pointInfo.qrcode_version || 1}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"meta-item\">\n\t\t\t\t\t\t\t\t<text class=\"meta-label\">生成时间</text>\n\t\t\t\t\t\t\t\t<text class=\"meta-value\">{{formatDate(qrGeneratedTime)}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"qrcode-actions\">\n\t\t\t\t\t\t\t<button class=\"btn-generate\" @click=\"generateQrCode\" v-if=\"!qrcodeGenerated\" :disabled=\"generating\">\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-qrcode\"></text>\n\t\t\t\t\t\t\t\t<text>{{generating ? '生成中...' : '生成二维码'}}</text>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t<button class=\"btn-regenerate\" @click=\"regenerateQrCode\" v-if=\"qrcodeGenerated\" :disabled=\"generating\">\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-refresh\"></text>\n\t\t\t\t\t\t\t\t<text>{{generating ? '生成中...' : '重新生成'}}</text>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t<button class=\"btn-save\" @click=\"saveQrCode\" v-if=\"qrcodeGenerated\" :disabled=\"generating || saving\">\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-download\"></text>\n\t\t\t\t\t\t\t\t<text>{{saving ? '保存中...' : '保存到相册'}}</text>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"qrcode-help\">\n\t\t\t\t\t\t\t<view class=\"help-title\">\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-info\"></text>\n\t\t\t\t\t\t\t\t<text>使用说明</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"help-content\">\n\t\t\t\t\t\t\t\t<text class=\"help-item\">1. 生成二维码后，请保存备用</text>\n\t\t\t\t\t\t\t\t<text class=\"help-item\">2. 打印后张贴在巡检点位置</text>\n\t\t\t\t\t\t\t\t<text class=\"help-item\">3. 巡检员可通过扫描二维码进行精准打卡</text>\n\t\t\t\t\t\t\t\t<text class=\"help-item\">4. 重新生成二维码将使旧二维码失效</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport QRCodeUtil from '@/utils/qrcode-utils.js';\nimport PatrolApi from '@/utils/patrol-api.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tpointInfo: {},\n\t\t\tpointId: '',\n\t\t\terrorMsg: '',\n\t\t\tloading: true,\n\t\t\tloadingText: {\n\t\t\t\tloading: '加载中...',\n\t\t\t\tmore: '加载更多...',\n\t\t\t\tnoMore: '没有更多数据了'\n\t\t\t},\n\t\t\tqrcodeGenerated: false,\n\t\t\tqrcodeEnabled: false,\n\t\t\tcurrentTime: '',\n\t\t\tgenerating: false,\n\t\t\tregenerating: false,\n\t\t\tqrGeneratedTime: null,\n\t\t\tsaving: false,\n\t\t\thasEditPermission: true,\n\t\t\tqrcodeContent: '',\n\t\t\tisInitialLoad: true,\n\t\t\tneedsNewQRCode: false,\n\t\t};\n\t},\n\tonLoad(options) {\n\t\tthis.pointId = options.id;\n\t\tif (!this.pointId) {\n\t\t\tthis.errorMsg = '缺少点位ID';\n\t\t\tthis.loading = false;\n\t\t\treturn;\n\t\t}\n\t\tthis.loadPointDetail();\n\t},\n\tmethods: {\n\t\t// 加载点位详情\n\t\tasync loadPointDetail() {\n\t\t\tif (!this.pointId) {\n\t\t\t\tthis.errorMsg = '缺少点位ID';\n\t\t\t\tthis.loading = false;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.loading = true;\n\t\t\tthis.errorMsg = '';\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.getPointDetail(this.pointId);\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\tthis.pointInfo = res.data;\n\t\t\t\t\tthis.qrcodeEnabled = !!this.pointInfo.qrcode_enabled;\n\t\t\t\t\t\n\t\t\t\t\t// 如果已经有二维码内容，直接显示\n\t\t\t\t\tif (this.qrcodeEnabled && this.pointInfo.qrcode_content) {\n\t\t\t\t\t\tthis.qrcodeContent = this.pointInfo.qrcode_content;\n\t\t\t\t\t\tthis.qrcodeGenerated = true;\n\t\t\t\t\t\tthis.qrGeneratedTime = this.pointInfo.qrcode_generated_time;\n\t\t\t\t\t} else if (this.qrcodeEnabled) {\n\t\t\t\t\t\t// 如果启用了二维码但还没有内容，自动生成\n\t\t\t\t\t\tthis.needsNewQRCode = true;\n\t\t\t\t\t\tawait this.generateQrCode();\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.errorMsg = res.message || '找不到点位信息';\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载点位详情失败', e);\n\t\t\t\tthis.errorMsg = '加载点位详情失败: ' + (e.message || '未知错误');\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 生成二维码\n\t\tasync generateQrCode() {\n\t\t\ttry {\n\t\t\t\tif (!this.pointInfo || !this.pointInfo._id) {\n\t\t\t\t\tthis.showError('无法生成二维码：点位信息不完整');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.generating = true;\n\t\t\t\tthis.errorMsg = '';\n\t\t\t\t\n\t\t\t\t// 始终重新生成二维码内容，使用最新的hash_key\n\t\t\t\tconst qrContent = QRCodeUtil.getQRCodeData({\n\t\t\t\t\t...this.pointInfo,\n\t\t\t\t\tqrcode_content: null  // 强制重新生成\n\t\t\t\t}, {\n\t\t\t\t\tincludeTimestamp: false  // 不包含时间戳\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconsole.log('生成的二维码数据:', qrContent);\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 保存到数据库\n\t\t\t\t\tconst updateResult = await PatrolApi.callPointFunction('updatePoint', { \n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tid: this.pointInfo._id,\n\t\t\t\t\t\t\tqrcode_content: qrContent,\n\t\t\t\t\t\t\tqrcode_generated_time: new Date().toISOString()\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (updateResult.code === 0) {\n\t\t\t\t\t\t// 更新本地数据\n\t\t\t\t\t\tthis.qrcodeContent = qrContent;\n\t\t\t\t\t\tthis.qrcodeGenerated = true;\n\t\t\t\t\t\tthis.qrGeneratedTime = new Date();\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '二维码已生成',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(updateResult.message || '保存失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tconsole.error('保存二维码到数据库失败:', err);\n\t\t\t\t\tthis.showError('保存二维码失败：' + (err.message || '未知错误'));\n\t\t\t\t}\n\t\t\t\t\n\t\t\t} catch (err) {\n\t\t\t\tconsole.error('二维码生成处理错误:', err);\n\t\t\t\tthis.showError('生成二维码时发生错误：' + (err.message || '未知错误'));\n\t\t\t} finally {\n\t\t\t\tthis.generating = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 二维码生成完成回调\n\t\tasync onQRCodeComplete(res) {\n\t\t\tconsole.log('二维码生成完成:', res);\n\t\t\tif (res.success) {\n\t\t\t\t// 只有在以下情况才更新数据库：\n\t\t\t\t// 1. 手动点击生成/重新生成按钮 (!isInitialLoad)\n\t\t\t\t// 2. 自动生成新二维码 (needsNewQRCode)\n\t\t\t\tif (this.generating && (!this.isInitialLoad || this.needsNewQRCode)) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// 保存二维码内容到数据库\n\t\t\t\t\t\tconst updateResult = await PatrolApi.callPointFunction('updatePoint', { \n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\tid: this.pointInfo._id,\n\t\t\t\t\t\t\t\tqrcode_content: this.qrcodeContent,\n\t\t\t\t\t\t\t\tqrcode_generated_time: new Date().toISOString(),\n\t\t\t\t\t\t\t\tqrcode_enabled: true\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (updateResult.code === 0) {\n\t\t\t\t\t\t\tthis.qrcodeGenerated = true;\n\t\t\t\t\t\t\tthis.qrGeneratedTime = new Date();\n\t\t\t\t\t\t\tthis.needsNewQRCode = false;\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrow new Error(updateResult.message || '保存二维码失败');\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\tconsole.error('保存二维码到数据库失败:', err);\n\t\t\t\t\t\tthis.showError('保存二维码失败：' + (err.message || '未知错误'));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthis.showError('生成二维码失败：' + (res.message || '未知错误'));\n\t\t\t}\n\t\t\tthis.generating = false;\n\t\t},\n\t\t\n\t\t// 重新生成二维码\n\t\tasync regenerateQrCode() {\n\t\t\ttry {\n\t\t\t\tif (!this.pointInfo || !this.pointInfo._id) {\n\t\t\t\t\tconsole.error('regenerateQrCode: pointInfo is missing', this.pointInfo);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '点位信息不完整',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (this.regenerating) {\n\t\t\t\t\tconsole.log('正在生成中，请勿重复操作');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.regenerating = true;\n\t\t\t\tthis.isInitialLoad = false;\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '更新中...'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 先更新版本号\n\t\t\t\t\tconst result = await QRCodeUtil.incrementQRCodeVersion(this.pointInfo._id);\n\t\t\t\t\tconsole.log('版本号更新结果:', result);\n\t\t\t\t\t\n\t\t\t\t\tif (result && result.updated) {\n\t\t\t\t\t\t// 更新本地点位信息\n\t\t\t\t\t\tthis.pointInfo = result.result;  // 使用返回的最新点位信息\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 重新生成二维码\n\t\t\t\t\t\tawait this.generateQrCode();\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '二维码已更新',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error('更新失败，请重试');\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('更新二维码失败', e);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: e.message || '更新失败，请重试',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.regenerating = false;\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('重新生成二维码失败:', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '重新生成失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tthis.regenerating = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 保存到相册\n\t\tasync saveQrCode() {\n\t\t\tif (!this.qrcodeGenerated) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先生成二维码',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.saving = true;\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '保存中...'\n\t\t\t});\n\t\t\t\n\t\t\ttry {\n\t\t\t\tawait this.$refs.uqrcode.save({\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '已保存到相册',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('保存到相册失败', err);\n\t\t\t\t\t\tlet errorMsg = '保存失败';\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查是否是权限问题\n\t\t\t\t\t\tif (err.errMsg && err.errMsg.includes('auth deny')) {\n\t\t\t\t\t\t\terrorMsg = '没有保存到相册的权限';\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: errorMsg,\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tthis.saving = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 返回上一页\n\t\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 前往编辑页\n\t\tgoToEdit() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/patrol_pkg/point/edit?id=${this.pointId}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 格式化日期\n\t\tformatDate(date) {\n\t\t\tif (!date) return '';\n\t\t\ttry {\n\t\t\t\tif (typeof date === 'string') {\n\t\t\t\t\tdate = new Date(date);\n\t\t\t\t}\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\n\t\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\n\t\t\t\tconst seconds = String(date.getSeconds()).padStart(2, '0');\n\t\t\t\t\n\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('日期格式化错误:', e);\n\t\t\t\treturn '日期格式错误';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 显示错误信息\n\t\tshowError(message) {\n\t\t\tthis.errorMsg = message;\n\t\t\tthis.loading = false;\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n.qrcode-container {\n\tmin-height: 100vh;\n\tbackground-color: #f5f5f5;\n\tpadding-bottom: env(safe-area-inset-bottom);\n\t\n\t.content {\n\t\tpadding: 20rpx;\n\t\tpadding-top: 20rpx;\n\t\t\n\t\t.loading-box {\n\t\t\tmargin-top: 100rpx;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t}\n\t\t\n\t\t.error-box {\n\t\t\tmargin-top: 100rpx;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\t\n\t\t\t.error-icon {\n\t\t\t\tfont-size: 100rpx;\n\t\t\t\tcolor: #ff5a5f;\n\t\t\t\tmargin-bottom: 30rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.error-text {\n\t\t\t\tcolor: #666;\n\t\t\t\tmargin-bottom: 40rpx;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t\t\n\t\t\t.btn-retry {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #ffffff;\n\t\t\t\tbackground-color: #007aff;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tpadding: 12rpx 32rpx;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.qrcode-box {\n\t\t\t.qrcode-info {\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tpadding: 40rpx;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);\n\t\t\t\t\n\t\t\t\t.qrcode-title {\n\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.qrcode-subtitle {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.qrcode-id {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t\tfont-family: monospace;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.qrcode-content {\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tpadding: 40rpx;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);\n\t\t\t\t\n\t\t\t\t.qrcode-disabled {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tpadding: 80rpx 0;\n\t\t\t\t\t\n\t\t\t\t\t.disabled-icon {\n\t\t\t\t\t\tfont-size: 120rpx;\n\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\tmargin-bottom: 30rpx;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.disabled-text {\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\tmargin-bottom: 40rpx;\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.btn-enable {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tcolor: #ffffff;\n\t\t\t\t\t\tbackground-color: #007aff;\n\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\tpadding: 16rpx 48rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.qrcode-display {\n\t\t\t\t\t.qrcode-canvas-container {\n\t\t\t\t\t\twidth: 280px;\n\t\t\t\t\t\theight: 280px;\n\t\t\t\t\t\tmargin: 20px auto 40rpx;\n\t\t\t\t\t\tborder-radius: 16rpx;\n\t\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);\n\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tpadding: 20px;\n\t\t\t\t\t\t\n\t\t\t\t\t\t&.is-generating {\n\t\t\t\t\t\t\tbackground-color: #f8f8f8;\n\t\t\t\t\t\t}\t\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\t.qrcode-loading {\n\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\t\ttop: 0;\n\t\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\t\tbottom: 0;\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tbackground-color: rgba(248,248,248,0.9);\n\t\t\t\t\t\t\tborder-radius: 16rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.qrcode-meta {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\tgap: 16rpx;\n\t\t\t\t\t\tpadding: 24rpx;\n\t\t\t\t\t\tbackground-color: #f8f8f8;\n\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\tmargin-bottom: 40rpx;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.meta-item {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.meta-label {\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.meta-value {\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\t\tfont-family: monospace;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.qrcode-actions {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\tgap: 20rpx;\n\t\t\t\t\t\tmargin-bottom: 40rpx;\n\t\t\t\t\t\t\n\t\t\t\t\t\tbutton {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\tgap: 12rpx;\n\t\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\t\theight: 88rpx;\n\t\t\t\t\t\t\tline-height: 1;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.iconfont {\n\t\t\t\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t\t\t\t\ttransition: transform 0.2s;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.btn-generate {\n\t\t\t\t\t\t\tbackground-color: #007aff;\n\t\t\t\t\t\t\tcolor: #ffffff;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&:disabled {\n\t\t\t\t\t\t\t\topacity: 0.6;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.btn-regenerate {\n\t\t\t\t\t\t\tbackground-color: #ff9500;\n\t\t\t\t\t\t\tcolor: #ffffff;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&:disabled {\n\t\t\t\t\t\t\t\topacity: 0.6;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.btn-save {\n\t\t\t\t\t\t\tbackground-color: #34c759;\n\t\t\t\t\t\t\tcolor: #ffffff;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&:disabled {\n\t\t\t\t\t\t\t\topacity: 0.6;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.qrcode-help {\n\t\t\t\t\t\tbackground-color: #f8f8f8;\n\t\t\t\t\t\tpadding: 24rpx;\n\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.help-title {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tgap: 12rpx;\n\t\t\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.iconfont {\n\t\t\t\t\t\t\t\tcolor: #007aff;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.help-content {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\t\tgap: 16rpx;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.help-item {\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\t\t\tline-height: 1.5;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qrcode.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qrcode.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571660733\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
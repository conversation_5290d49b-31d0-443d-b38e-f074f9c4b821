<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>核心指标提升 - 株水小智</title>
    <script src="libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="libs/all.min.css">
    <script src="libs/echarts.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #1e40af 100%);
        }
        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .glass-effect:hover {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .bubble {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.13);
            pointer-events: none;
            animation: floatBubble 12s linear infinite;
        }
        @keyframes floatBubble {
            0% { transform: translateY(0) scale(1); opacity: 0.7; }
            50% { opacity: 1; }
            100% { transform: translateY(-120vh) scale(1.2); opacity: 0; }
        }
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .animate-pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .subtitle-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 0.5rem;
            margin-bottom: 2rem;
        }
        .subtitle-bar:before, .subtitle-bar:after {
            content: "";
            flex: 1;
            height: 2px;
            background: linear-gradient(90deg, #38bdf8 0%, #a7f3d0 100%);
            margin: 0 1rem;
            border-radius: 1px;
            box-shadow: 0 0 8px rgba(56, 189, 248, 0.6);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex flex-col overflow-hidden">
    <!-- 进度条 -->
    <div class="fixed top-0 left-0 w-full h-1 bg-white/20 z-50">
        <div class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-1000" style="width: 0%"></div>
    </div>

    <!-- 气泡粒子动画 -->
    <div class="absolute inset-0 z-0 overflow-hidden">
        <div class="bubble" style="width:60px;height:60px;left:10vw;bottom:10vh;animation-delay:0s;"></div>
        <div class="bubble" style="width:40px;height:40px;left:80vw;bottom:20vh;animation-delay:2s;"></div>
        <div class="bubble" style="width:80px;height:80px;left:30vw;bottom:5vh;animation-delay:4s;"></div>
        <div class="bubble" style="width:30px;height:30px;left:60vw;bottom:15vh;animation-delay:6s;"></div>
        <div class="bubble" style="width:50px;height:50px;left:50vw;bottom:8vh;animation-delay:1s;"></div>
    </div>



    <div class="relative z-10 flex-1 flex flex-col">
        <!-- 头部 -->
        <header class="text-center py-8">
            <div class="animate-pulse-slow">
                <h1 class="text-5xl font-bold text-white mb-4 flex items-center justify-center">
                    <i class="fas fa-chart-line mr-4 text-blue-200"></i>
                    核心指标提升
                </h1>
                <div class="subtitle-bar">
                    <span class="text-xl text-blue-100 font-light">数据驱动管理</span>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="flex-1 px-8 pb-8">
            <div class="max-w-7xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-8 h-full">
                    <!-- 左侧指标卡片 -->
                    <div class="space-y-6">
                        <!-- 问题处理效率 -->
                        <div class="glass-effect rounded-2xl p-8 transform hover:scale-105 transition-all duration-300">
                            <div class="flex items-center mb-6">
                                <i class="fas fa-bolt text-blue-300 mr-4 text-2xl"></i>
                                <h3 class="text-2xl font-bold text-white">问题处理效率</h3>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold text-green-400 mb-4">提升 400%</div>
                                <div class="text-white/80">从被动等待到主动推送</div>
                            </div>
                        </div>

                        <!-- 数据准确率 -->
                        <div class="glass-effect rounded-2xl p-8 transform hover:scale-105 transition-all duration-300">
                            <div class="flex items-center mb-6">
                                <i class="fas fa-bullseye text-green-300 mr-4 text-2xl"></i>
                                <h3 class="text-2xl font-bold text-white">数据准确率</h3>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold text-blue-200 mb-4">70% → 99%</div>
                                <div class="text-white/80">自动化数据采集</div>
                            </div>
                        </div>

                        <!-- 隐患处理时效 -->
                        <div class="glass-effect rounded-2xl p-8 transform hover:scale-105 transition-all duration-300">
                            <div class="flex items-center mb-6">
                                <i class="fas fa-clock text-orange-300 mr-4 text-2xl"></i>
                                <h3 class="text-2xl font-bold text-white">隐患处理时效</h3>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold text-purple-400 mb-4">72h → 8h</div>
                                <div class="text-white/80">效率提升 89%</div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧图表 -->
                    <div class="space-y-6">
                        <!-- 核心指标提升图表 -->
                        <div class="glass-effect rounded-2xl p-8 h-80">
                            <h3 class="text-2xl font-bold text-white mb-6 flex items-center">
                                <i class="fas fa-chart-bar mr-3 text-purple-300"></i>
                                核心指标提升对比
                            </h3>
                            <div id="metricsChart" class="w-full h-64"></div>
                        </div>

                        <!-- 效率提升总结 -->
                        <div class="glass-effect rounded-2xl p-8">
                            <h3 class="text-2xl font-bold text-white mb-6 flex items-center">
                                <i class="fas fa-trophy mr-3 text-yellow-300"></i>
                                效率提升总结
                            </h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="text-center p-4 bg-white/10 rounded-xl">
                                    <div class="text-2xl font-bold text-green-400 mb-2 flex items-center justify-center">
                                        <i class="fas fa-arrow-up text-green-400 mr-2"></i>400%
                                    </div>
                                    <div class="text-white/80 text-sm">处理效率提升</div>
                                </div>
                                <div class="text-center p-4 bg-white/10 rounded-xl">
                                    <div class="text-2xl font-bold text-blue-400 mb-2 flex items-center justify-center">
                                        <i class="fas fa-arrow-up text-blue-400 mr-2"></i>29%
                                    </div>
                                    <div class="text-white/80 text-sm">准确率提升</div>
                                </div>
                                <div class="text-center p-4 bg-white/10 rounded-xl">
                                    <div class="text-2xl font-bold text-purple-400 mb-2 flex items-center justify-center">
                                        <i class="fas fa-arrow-up text-purple-400 mr-2"></i>89%
                                    </div>
                                    <div class="text-white/80 text-sm">时效提升</div>
                                </div>
                                <div class="text-center p-4 bg-white/10 rounded-xl">
                                    <div class="text-2xl font-bold text-orange-400 mb-2 flex items-center justify-center">
                                        <i class="fas fa-arrow-up text-orange-400 mr-2"></i>33%
                                    </div>
                                    <div class="text-white/80 text-sm">闭环率提升</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    <footer class="text-center py-6 text-blue-200 flex justify-center space-x-8 relative z-20">
        <button onclick="prevSlide()" class="px-6 py-2 rounded-xl bg-white/20 hover:bg-white/30 transition text-lg font-semibold"><i class="fas fa-arrow-left mr-2"></i>上一页</button>
        <button onclick="nextSlide()" class="px-6 py-2 rounded-xl bg-white/20 hover:bg-white/30 transition text-lg font-semibold">下一页<i class="fas fa-arrow-right ml-2"></i></button>
    </footer>

    <script>
        // 初始化图表
        function initCharts() {
            // 核心指标提升对比图
            const metricsChart = echarts.init(document.getElementById('metricsChart'));
            const metricsOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['升级前', '升级后'],
                    textStyle: { color: '#fff' }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['处理效率', '数据准确率', '处理时效', '闭环率'],
                    axisLabel: { color: '#fff' }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: { color: '#fff' }
                },
                series: [
                    {
                        name: '升级前',
                        type: 'bar',
                        data: [100, 70, 72, 65],
                        itemStyle: { color: '#EF4444' }
                    },
                    {
                        name: '升级后',
                        type: 'bar',
                        data: [500, 99, 8, 98],
                        itemStyle: { color: '#10B981' }
                    }
                ]
            };
            metricsChart.setOption(metricsOption);

            // 响应式处理
            window.addEventListener('resize', () => {
                metricsChart.resize();
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            
            // 进度条动画
            setTimeout(() => {
                document.querySelector('.bg-gradient-to-r').style.width = '100%';
            }, 500);
        });

        // 导航函数
        function prevSlide() {
            window.location.href = 'slide7.html';
        }

        function nextSlide() {
            window.location.href = 'slide9.html';
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                prevSlide();
            }
        });

        // 鼠标移动视差效果
        document.addEventListener('mousemove', function(e) {
            const moveX = (e.clientX - window.innerWidth / 2) * 0.01;
            const moveY = (e.clientY - window.innerHeight / 2) * 0.01;
            
            document.querySelectorAll('.animate-float').forEach(element => {
                element.style.transform = `translate(${moveX}px, ${moveY}px)`;
            });
        });
    </script>
</body>
</html> 
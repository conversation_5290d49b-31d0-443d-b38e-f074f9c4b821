(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-patrol_pkg-checkin-index"],{"0e01":function(t,n,e){"use strict";(function(t){e("6a54");var i=e("f5bd").default,a=e("3639").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("aa77"),e("bf0f"),e("bd06"),e("8f71"),e("c223"),e("e838"),e("e966"),e("d4b5"),e("fd3c"),e("64aa"),e("4100"),e("aa9c"),e("f7a5"),e("4626"),e("5ac7"),e("18f7"),e("de6c"),e("dd2b"),e("795c"),e("5c47"),e("2c10"),e("dc8a");var o,r=i(e("39d8")),s=i(e("b7c7")),c=i(e("fcf3")),d=i(e("2634")),u=i(e("2fdc")),l=i(e("9b1b")),f=e("8f59"),h=a(e("696a")),p=i(e("b96b")),v=i(e("95a7")),g={data:function(){return{pointInfo:{},taskInfo:{},currentLocation:{latitude:30,longitude:120,accuracy:0},markers:[],circles:[],isInRange:!1,distance:0,distanceToBoundary:0,distanceText:"0m",loading:!1,isLoading:!0,imageList:[],maxImageCount:3,formData:{remark:"",round:1},locationUpdateTimer:null,currentRound:null,shiftInfo:null,isRoundValid:!1,roundErrorMessage:"",roundStatusTimer:null,mapContext:null,locationErrorShown:!1,locationWarningShown:!1,isLocationAccuracyLow:!1,uploadRetryCount:0,uploadMaxRetries:3,lastInRange:!1,isFirstLocation:!0,lastUpdateTime:0,minimumUpdateInterval:2e3,locationChangeThreshold:5,lastLocation:null,isFollowMode:!0,showCamera:!1,flashMode:"off",cameraContext:null,flashPopupMessage:"",distancePopupMessage:"",qrcodeScanned:!1,qrcodeVerified:!1,qrcodeData:null,qrcodeVerifyResult:{valid:!1,title:"",message:"",code:"",data:null},qrcodeAllowedDistance:30,showScanner:!1,nextUnCheckedPoint:null,showDistanceMessage:!1,lastValidLocation:null,locationWatchId:null,isAutoJumping:!1}},computed:(0,l.default)((0,l.default)({},(0,f.mapState)({userInfo:function(t){return t.user.userInfo}})),{},{isCurrentPointChecked:function(){var t=this;if(!this.currentRound||!this.currentRound.points||!this.pointInfo)return!1;var n=this.currentRound.points.find((function(n){return n.point_id===t.pointInfo._id}));return n&&n.status&&n.status>0},isCurrentPointFirstUnchecked:function(){if(!this.currentRound||!this.currentRound.points||!this.pointInfo)return!1;var t=this.currentRound.points.find((function(t){return!t.status||0===t.status}));return t&&t.point_id===this.pointInfo._id},isRoundCompleted:function(){if(!this.currentRound||!this.currentRound.points||!Array.isArray(this.currentRound.points))return!1;var t=this.currentRound.points.every((function(t){return t.status&&t.status>0}));return t&&this.isCurrentPointChecked},isLastPoint:function(){var t=this;if(!this.currentRound||!this.currentRound.points||!Array.isArray(this.currentRound.points)||!this.pointInfo)return!1;var n=this.currentRound.points.findIndex((function(n){return n.point_id===t.pointInfo._id}));return-1!==n&&n===this.currentRound.points.length-1},isLastUnCheckedPoint:function(){if(!this.currentRound||!this.currentRound.points||!this.pointInfo)return!1;var t=this.currentRound.points.filter((function(t){return!t.status||0===t.status}));return 1===t.length&&t[0].point_id===this.pointInfo._id},currentPointIndex:function(){var t=this;if(!this.currentRound||!this.currentRound.points||!this.pointInfo)return null;var n=this.currentRound.points.findIndex((function(n){return n.point_id===t.pointInfo._id}));return-1!==n?n+1:null},nextPointIndex:function(){var t=this;if(!this.currentRound||!this.currentRound.points||!this.nextUnCheckedPoint)return null;var n=this.currentRound.points.findIndex((function(n){return n.point_id===t.nextUnCheckedPoint.point_id}));return-1!==n?n+1:null},formattedCurrentPointName:function(){if(!this.pointInfo)return"获取中...";var t=this.currentPointIndex;return t?"".concat(t,". ").concat(this.pointInfo.name):this.pointInfo.name},formattedNextPointName:function(){if(!this.nextUnCheckedPoint)return"未知点位";var t=this.nextPointIndex;return t?"".concat(t,". ").concat(this.nextUnCheckedPoint.name):this.nextUnCheckedPoint.name}}),onLoad:function(t){this.isLoading=!0,this.initLocationFromParams(t),this.parseAndValidateParams(t),this.checkLocationPermission()},onUnload:function(){this.stopLocationWatch(),this.locationUpdateTimer&&(clearInterval(this.locationUpdateTimer),this.locationUpdateTimer=null),this.stopRoundStatusTimer(),this.isAutoJumping=!1},onShow:function(){this.startRoundStatusTimer()},onHide:function(){this.stopRoundStatusTimer()},onReady:function(){var t=this;this.mapContext=uni.createMapContext("checkInMap",this),setTimeout((function(){t.mapContext&&t.currentLocation.latitude&&30!==t.currentLocation.latitude&&t.mapContext.moveToLocation({latitude:t.currentLocation.latitude,longitude:t.currentLocation.longitude})}),500)},methods:(o={initLocationFromParams:function(t){if(t&&t.lat&&t.lng){var n=parseFloat(t.lat),e=parseFloat(t.lng),i=parseFloat(t.accuracy)||0;!isNaN(n)&&!isNaN(e)&&n>=-90&&n<=90&&e>=-180&&e<=180&&(this.lastValidLocation={latitude:n,longitude:e,accuracy:i,lastUpdated:Date.now()},this.currentLocation=(0,l.default)((0,l.default)({},this.lastValidLocation),{},{altitude:0,speed:0,address:""}),console.log("保存巡视首页位置作为备用:",n,e,"精度:",i),this.updateMapMarkers(),this.updateCircles())}},parseAndValidateParams:function(t){var n=this;if(t){var e=t.point_id,i=t.task_id,a=t.round;e&&i?(a&&(this.formData.round=parseInt(a)||1),this.getPointInfo(e).then((function(){return n.getTaskInfo(i)})).finally((function(){n.isLoading=!1}))):this.showErrorAndGoBack("参数错误：缺少必要参数")}else this.showErrorAndGoBack("参数错误")},showErrorAndGoBack:function(t){var n=this;uni.showToast({title:t||"出错了",icon:"none"}),setTimeout((function(){n.goBack()}),1500)},checkLocationPermission:function(){var t=this;return(0,u.default)((0,d.default)().mark((function n(){var e,i;return(0,d.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,h.checkLocationPermission();case 3:if(e=n.sent,e){n.next=11;break}return n.next=7,h.requestLocationPermission();case 7:if(i=n.sent,i){n.next=11;break}return uni.showModal({title:"温馨提示",content:"请授权位置权限，否则无法使用打卡功能",confirmText:"去设置",cancelText:"返回",success:function(n){n.confirm?uni.openSetting({success:function(){var n=(0,u.default)((0,d.default)().mark((function n(e){return(0,d.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!e.authSetting["scope.userLocation"]){n.next=5;break}return n.next=3,t.initLocation();case 3:n.next=7;break;case 5:uni.showToast({title:"未获得位置权限",icon:"none"}),setTimeout((function(){t.goBack()}),1500);case 7:case"end":return n.stop()}}),n)})));return function(t){return n.apply(this,arguments)}}()}):setTimeout((function(){t.goBack()}),1e3)}}),n.abrupt("return");case 11:return n.next=13,t.initLocation();case 13:n.next=19;break;case 15:n.prev=15,n.t0=n["catch"](0),console.error("位置权限检查失败:",n.t0),uni.showToast({title:"位置权限检查失败",icon:"none"});case 19:case"end":return n.stop()}}),n,null,[[0,15]])})))()},initLocation:function(){var t=this;return(0,u.default)((0,d.default)().mark((function n(){var e;return(0,d.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,h.getCurrentLocation({type:"gcj02",isHighAccuracy:!0,maxRetries:2,retryDelay:1e3,fallbackToLowAccuracy:!1});case 3:return e=n.sent,t.currentLocation=e,t.updateMapMarkers(),t.updateCircles(),t.calculateDistance(),n.next=10,t.startLocationWatch();case 10:n.next=26;break;case 12:return n.prev=12,n.t0=n["catch"](0),console.error("初始化位置失败:",n.t0),n.prev=15,n.next=18,t.relocate(!0);case 18:return n.next=20,t.startLocationWatch();case 20:n.next=26;break;case 22:n.prev=22,n.t1=n["catch"](15),console.error("回退定位也失败:",n.t1),uni.showToast({title:"初始化位置失败",icon:"none"});case 26:case"end":return n.stop()}}),n,null,[[0,12],[15,22]])})))()},startLocationWatch:function(){var t=this;try{uni.startLocationUpdate({success:function(){console.log("位置监听已开启"),uni.onLocationChange((function(n){var e={latitude:n.latitude,longitude:n.longitude,accuracy:n.accuracy||0,altitude:n.altitude||0,speed:n.speed||0,lastUpdated:Date.now()};if(n.accuracy<=100?(t.currentLocation=e,t.lastValidLocation=e):n.accuracy>100&&t.lastValidLocation?(t.currentLocation=(0,l.default)((0,l.default)({},t.lastValidLocation),{},{accuracy:n.accuracy}),console.log("使用最后有效位置作为备选")):t.currentLocation=e,t.updateMapMarkers(),t.updateCircles(),t.calculateDistance(),t.isFollowMode&&t.mapContext)t.mapContext.moveToLocation({latitude:t.currentLocation.latitude,longitude:t.currentLocation.longitude});else if(t.isFirstLocation&&t.mapContext){var i=Math.abs(n.latitude-30)>.01||Math.abs(n.longitude-120)>.01;i&&t.mapContext.moveToLocation({latitude:t.currentLocation.latitude,longitude:t.currentLocation.longitude}),t.isFirstLocation=!1}}))},fail:function(n){console.error("启动位置更新失败:",n),t.showLocationError("无法启动位置监听: "+(n.errMsg||JSON.stringify(n)))}})}catch(n){console.error("初始化位置监听出错:",n),uni.showToast({title:"位置监听失败，请检查定位权限",icon:"none"})}},relocate:function(){var t=arguments,n=this;return(0,u.default)((0,d.default)().mark((function e(){var i,a;return(0,d.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i=!(t.length>0&&void 0!==t[0])||t[0],e.prev=1,i&&uni.showLoading({title:"定位中..."}),e.next=5,h.getLocationWithChecks({type:"gcj02",isHighAccuracy:!0,maxRetries:2,retryDelay:1e3,fallbackToLowAccuracy:!1});case 5:a=e.sent,h.getSignalQuality(a.accuracy),n.currentLocation=a,n.updateMapMarkers(),n.updateCircles(),n.mapContext&&(n.mapContext.moveToLocation({latitude:n.currentLocation.latitude,longitude:n.currentLocation.longitude}),n.isFollowMode=!0),n.calculateDistance(),i&&(uni.hideLoading(),uni.showToast({title:"定位成功",icon:"success",duration:1500})),e.next=19;break;case 15:e.prev=15,e.t0=e["catch"](1),console.error("重新定位失败:",e.t0),i&&(uni.hideLoading(),uni.showToast({title:"重新定位失败: "+(e.t0.message||"请检查GPS是否开启"),icon:"none",duration:2e3}));case 19:case"end":return e.stop()}}),e,null,[[1,15]])})))()},getPointInfo:function(t){var n=this;return(0,u.default)((0,d.default)().mark((function e(){var i,a,o,r;return(0,d.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,p.default.call({name:"patrol-point",action:"getPointDetail",data:{point_id:t,fields:["_id","name","location","latitude","longitude","range","qrcode_enabled","qrcode_required","status","address","description","remark"]}});case 5:if(i=e.sent,!i||0!==i.code||!i.data){e.next=16;break}return n.pointInfo=i.data,n.pointInfo.location&&"object"===(0,c.default)(n.pointInfo.location)&&(n.pointInfo.latitude=n.pointInfo.latitude||n.pointInfo.location.latitude||n.pointInfo.location.lat,n.pointInfo.longitude=n.pointInfo.longitude||n.pointInfo.location.longitude||n.pointInfo.location.lng),n.currentRound&&n.currentRound.points&&(a=n.currentRound.points.find((function(t){return t.point_id===n.pointInfo._id})),a&&a.status>0&&(n.updateMapMarkers(),n.isRoundValid=!1,n.roundErrorMessage="该点位在当前轮次已完成打卡")),n.updateMapMarkers(),n.updateCircles(),n.currentLocation.latitude&&n.currentLocation.longitude&&n.calculateDistance(),e.abrupt("return",i.data);case 16:return o=(null===i||void 0===i?void 0:i.message)||"获取点位信息失败",console.error("获取点位信息失败:",o),uni.showToast({title:o,icon:"none"}),e.abrupt("return",null);case 20:e.next=28;break;case 22:return e.prev=22,e.t0=e["catch"](2),r="获取点位信息出错: "+(e.t0.message||e.t0),console.error(r),uni.showToast({title:r,icon:"none"}),e.abrupt("return",null);case 28:case"end":return e.stop()}}),e,null,[[2,22]])})))()},getTaskInfo:function(t){var n=this;return(0,u.default)((0,d.default)().mark((function e(){var i;return(0,d.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,p.default.call({name:"patrol-task",action:"getTaskDetail",data:{task_id:t,level:"checkin"}});case 5:i=e.sent,0===i.code&&i.data?(n.taskInfo=i.data,n.taskInfo.rounds_detail&&n.taskInfo.rounds_detail.length>0?(n.processRoundsData(),n.$nextTick((function(){n.getNextUnCheckedPoint()}))):(n.isRoundValid=!1,n.roundErrorMessage="任务无轮次数据")):uni.showToast({title:"获取任务信息失败",icon:"none"}),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](2),uni.showToast({title:"获取任务信息出错",icon:"none"});case 12:case"end":return e.stop()}}),e,null,[[2,9]])})))()},processRoundsData:function(){var t=this;if(this.taskInfo&&this.taskInfo.rounds_detail){var n=this.taskInfo.patrol_date?new Date(this.taskInfo.patrol_date):new Date(this.taskInfo.create_date),e=new Date;this.taskInfo.rounds_detail=this.taskInfo.rounds_detail.map((function(t){if(t.day_offset=void 0!==t.day_offset?Number(t.day_offset):0,t.duration=void 0!==t.duration?Number(t.duration):60,t.isHidden=!1,t.day_offset>0){var i=new Date(n);i.setDate(n.getDate()+t.day_offset);var a=new Date;a.setHours(0,0,0,0);var o=new Date(i);o.setHours(0,0,0,0),a<o&&(t.isHidden=!0)}if(t.start_time)try{var r=new Date(t.start_time),s=new Date(t.end_time);t.actualStartTime=r,t.actualEndTime=s,e<r?t.status=0:e>s?t.point_stats&&t.point_stats.total>0&&t.point_stats.checked>=t.point_stats.total?t.status=2:t.status=3:t.point_stats&&t.point_stats.total>0&&t.point_stats.checked>=t.point_stats.total?t.status=2:t.status=1}catch(c){console.error("解析轮次[".concat(t.round,"]时间出错:"),c),t.point_stats&&t.point_stats.total>0&&t.point_stats.checked>=t.point_stats.total?t.status=2:t.point_stats&&t.point_stats.checked>0?t.status=1:t.status=0}else t.point_stats&&t.point_stats.total>0&&t.point_stats.checked>=t.point_stats.total?t.status=2:t.point_stats&&t.point_stats.checked>0?t.status=1:t.status=0;return t.status=parseInt(t.status||0),t}));var i=this.taskInfo.rounds_detail.filter((function(t){return!t.isHidden})),a=i.filter((function(t){return 0===t.status||1===t.status})),o=i.filter((function(t){return 2===t.status||3===t.status}));if(a.sort((function(t,n){return t.round-n.round})),o.sort((function(t,n){return n.round-t.round})),this.taskInfo.visibleRounds=[].concat((0,s.default)(a),(0,s.default)(o)),this.taskInfo.visibleRounds&&this.taskInfo.visibleRounds.length>0){if(this.currentRound=this.taskInfo.visibleRounds[0],this.formData.round=this.currentRound.round,this.pointInfo&&this.pointInfo._id){var r=this.currentRound.points.find((function(n){return n.point_id===t.pointInfo._id}));r&&r.status>0&&(this.updateMapMarkers(),this.isRoundValid=!1,this.roundErrorMessage="该点位在当前轮次已完成打卡")}this.validateCurrentRound(),this.startRoundStatusTimer(),this.$nextTick((function(){t.getNextUnCheckedPoint()}))}}},validateCurrentRound:function(){var t=this;if(!this.currentRound)return this.isRoundValid=!1,void(this.roundErrorMessage="无法获取轮次信息");var n=new Date;if(this.currentRound.isHidden)return this.isRoundValid=!1,void(this.roundErrorMessage="轮次".concat(this.currentRound.round,"尚未开始，请在指定日期执行"));if(0!==this.currentRound.status){if(1===this.currentRound.status){if(this.pointInfo&&this.pointInfo._id){var e=this.currentRound.points.find((function(n){return n.point_id===t.pointInfo._id}));if(e&&e.status>0)return this.isRoundValid=!1,void(this.roundErrorMessage="该点位在当前轮次已完成打卡")}return this.isRoundValid=!0,void(this.roundErrorMessage="")}if(2===this.currentRound.status)return this.isRoundValid=!1,void(this.roundErrorMessage="轮次".concat(this.currentRound.round,"已完成"));if(3===this.currentRound.status)return this.isRoundValid=!1,void(this.roundErrorMessage="轮次".concat(this.currentRound.round,"已超时"));if(this.pointInfo&&this.pointInfo._id){var i=this.currentRound.points.find((function(n){return n.point_id===t.pointInfo._id}));if(i&&i.status>0)return this.isRoundValid=!1,void(this.roundErrorMessage="该点位在当前轮次已完成打卡")}this.isRoundValid=!0,this.roundErrorMessage=""}else if(this.currentRound.actualStartTime){var a=this.currentRound.actualStartTime-n;if(a<=0)this.isRoundValid=!1,this.roundErrorMessage="轮次".concat(this.currentRound.round,"正在开始...");else if(a<6e4){var o=Math.floor(a/1e3);this.isRoundValid=!1,this.roundErrorMessage="距离轮次".concat(this.currentRound.round,"开始还有").concat(o,"秒")}else if(a<36e5){var r=Math.floor(a/6e4),s=Math.floor(a%6e4/1e3);this.isRoundValid=!1,this.roundErrorMessage="距离轮次".concat(this.currentRound.round,"开始还有").concat(r,"分").concat(s,"秒")}else{var c=Math.floor(a/36e5),d=Math.floor(a%36e5/6e4);this.isRoundValid=!1,this.roundErrorMessage="距离轮次".concat(this.currentRound.round,"开始还有").concat(c,"小时").concat(d,"分钟")}}else this.isRoundValid=!1,this.roundErrorMessage="轮次".concat(this.currentRound.round,"尚未开始")},updateCircles:function(){if(this.pointInfo&&this.pointInfo.latitude&&this.pointInfo.longitude){var t,n,e=this.pointInfo.range||10,i=this.currentLocation.accuracy;if(this.circles=[{latitude:this.pointInfo.latitude,longitude:this.pointInfo.longitude,color:this.isInRange?"#52C41A33":"#FF4D4F33",fillColor:this.isInRange?"#52C41A22":"#FF4D4F22",radius:e,strokeWidth:2}],this.currentLocation.accuracy>0)i<=5?(t="#34C75988",n="#34C75933"):i<=10?(t="#00C58E88",n="#00C58E33"):i<=15?(t="#FFD60A88",n="#FFD60A33"):i<=20?(t="#FF950088",n="#FF950033"):i<=25?(t="#FF6B2C88",n="#FF6B2C33"):(t="#FF3B3088",n="#FF3B3033"),this.circles.push({latitude:this.currentLocation.latitude,longitude:this.currentLocation.longitude,color:t,fillColor:n,radius:3,strokeWidth:2,strokeColor:t.slice(0,7)})}},calculateDistance:function(){if(this.pointInfo&&this.pointInfo.latitude&&this.pointInfo.longitude)try{var t=h.calculateDistance(this.currentLocation,{latitude:this.pointInfo.latitude,longitude:this.pointInfo.longitude}),n=this.pointInfo.range||10;t<=n?(this.isInRange=!0,this.distanceText=t<5?"很近":"还需".concat(Math.round(t),"米靠近中心点")):(this.isInRange=!1,this.distanceText="还需".concat(Math.round(t-n),"米靠近范围圈")),this.isInRange!==this.lastInRange&&(this.updateCircles(),this.lastInRange=this.isInRange)}catch(e){console.error("计算距离出错",e)}},getAccuracyColor:function(){var t=this.currentLocation.accuracy;return t?t<=5?"#34C759":t<=10?"#00C58E":t<=15?"#FFD60A":t<=20?"#FF9500":t<=25?"#FF6B2C":"#FF3B30":"#999999"},showLocationError:function(t){var n=this;this.locationErrorShown||(this.locationErrorShown=!0,uni.showToast({title:t,icon:"none",duration:3e3}),setTimeout((function(){n.locationErrorShown=!1}),5e3))},showLocationWarning:function(t){var n=this;this.locationWarningShown||(this.locationWarningShown=!0,uni.showToast({title:t,icon:"none",duration:3e3}),setTimeout((function(){n.locationWarningShown=!1}),1e4))},updateMapMarkers:function(){var t,n=this;if(this.pointInfo&&this.pointInfo.latitude&&this.pointInfo.longitude){var e=this.currentRound&&this.currentRound.points&&(null===(t=this.currentRound.points.find((function(t){return t.point_id===n.pointInfo._id})))||void 0===t?void 0:t.status)>0;this.markers=[{id:1,latitude:this.pointInfo.latitude,longitude:this.pointInfo.longitude,title:this.pointInfo.name,iconPath:e?"/static/map/map-pin.png":"/static/map/marker.png",width:32,height:32,callout:{content:"".concat(this.pointInfo.name||"未命名点位").concat(e?" ✓":""),color:"#FFFFFF",fontSize:12,borderWidth:0,bgColor:e?"#34C759":"#3688FF",padding:5,display:"ALWAYS",borderRadius:4,textAlign:"center"},anchorX:.5,anchorY:1}]}},onMapRegionChange:function(t){"end"===t.type&&"drag"===t.causedBy&&(this.isFirstLocation=!1,this.isFollowMode=!1)},onMarkerTap:function(t){},chooseImage:function(){var t=this;uni.chooseImage({count:this.maxImageCount-this.imageList.length,sizeType:["compressed"],sourceType:["camera"],success:function(n){t.compressImages(n.tempFilePaths)},fail:function(t){t.errMsg&&t.errMsg.includes("auth deny")?uni.showModal({title:"提示",content:"需要相机权限才能拍照上传，请在设置中允许访问相机",confirmText:"去设置",success:function(t){t.confirm&&uni.openSetting()}}):uni.showToast({title:"拍照失败",icon:"none"})}})},toggleFlash:function(){this.flashMode;"off"===this.flashMode?this.flashMode="on":"on"===this.flashMode?this.flashMode="torch":this.flashMode="off";var t="";"off"===this.flashMode?t="已关闭闪光灯":"on"===this.flashMode?t="闪光灯已开启":"torch"===this.flashMode&&(t="常亮模式已开启"),uni.showToast({title:t,icon:"none",duration:1e3})},toggleScannerFlash:function(){this.flashMode="off"===this.flashMode?"torch":"off",uni.showToast({title:"off"===this.flashMode?"已关闭照明":"已开启照明",icon:"none",duration:1e3})},getFlashIcon:function(){switch(this.flashMode){case"on":return"eye-filled";case"torch":return"fire-filled";default:return"eye"}},getFlashText:function(){switch(this.flashMode){case"on":return"闪光灯开";case"torch":return"常亮模式";default:return"闪光灯关"}},closeCamera:function(){this.showCamera=!1,this.cameraContext=null},takePhoto:function(){var t=this;if(this.cameraContext){var n=this.flashMode;"off"===n?this.doTakePhoto(n):"on"===n?(this.flashMode="torch",setTimeout((function(){t.flashMode="off",setTimeout((function(){t.flashMode="on",t.doTakePhoto("on"),setTimeout((function(){t.flashMode="off",setTimeout((function(){t.flashMode=n}),500)}),800)}),200)}),1e3)):this.doTakePhoto(n)}else uni.showToast({title:"相机未初始化",icon:"none"})},doTakePhoto:function(t){var n=this;this.cameraContext.takePhoto({quality:"high",flash:t,success:function(t){var e=[t.tempImagePath];n.compressImages(e),n.showCamera=!1},fail:function(t){console.error("拍照失败:",t),uni.showToast({title:"拍照失败: "+(t.errMsg||JSON.stringify(t)),icon:"none"})}})},handleCameraError:function(t){t.detail&&t.detail.errMsg&&t.detail.errMsg.includes("auth deny")?uni.showModal({title:"提示",content:"需要相机权限才能拍照上传，请在设置中允许访问相机",confirmText:"去设置",success:function(t){t.confirm&&uni.openSetting()}}):uni.showToast({title:"相机出错",icon:"none"})},compressImages:function(t){var n=this,e=t.map((function(t){return new Promise((function(n,e){uni.compressImage({src:t,quality:80,success:function(t){n(t.tempFilePath)},fail:function(e){n(t)}})}))}));Promise.all(e).then((function(t){n.imageList=[].concat((0,s.default)(n.imageList),(0,s.default)(t))}))},previewImage:function(t){uni.previewImage({urls:this.imageList,current:t})},deleteImage:function(t){this.imageList.splice(t,1)},uploadImage:function(n){var e=this;return(0,u.default)((0,d.default)().mark((function i(){return(0,d.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.abrupt("return",new Promise((function(i,a){uni.showLoading({title:"上传图片中...",mask:!0});var o=new Date,r=o.getFullYear(),s=String(o.getMonth()+1).padStart(2,"0"),c=String(o.getDate()).padStart(2,"0"),d="".concat(r).concat(s).concat(c),u=".jpg";n.match(/\.(\w+)$/)&&(u=n.match(/\.(\w+)$/)[0]);var l="".concat(Date.now(),"_").concat(Math.floor(1e3*Math.random())).concat(u);t.uploadFile({filePath:n,cloudPath:"patrol/photos/".concat(d,"/").concat(l),cloudPathAsRealPath:!0,success:function(t){uni.hideLoading(),i(t.fileID)},fail:function(t){uni.hideLoading(),e.uploadRetryCount<e.uploadMaxRetries?(e.uploadRetryCount++,uni.showToast({title:"上传失败，正在重试(".concat(e.uploadRetryCount,"/").concat(e.uploadMaxRetries,")"),icon:"none"}),setTimeout((function(){e.uploadImage(n).then(i).catch(a)}),1e3)):(e.uploadRetryCount=0,uni.showToast({title:"图片上传失败，请重试",icon:"none"}),a(t))}})})));case 1:case"end":return i.stop()}}),i)})))()},submitCheckin:function(){var t=this;return(0,u.default)((0,d.default)().mark((function n(){var e;return(0,d.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!t.loading){n.next=2;break}return n.abrupt("return");case 2:if(!t.pointInfo||!t.pointInfo.qrcode_required||t.qrcodeVerified){n.next=6;break}return uni.showToast({title:"该点位必须使用二维码打卡",icon:"none"}),setTimeout((function(){t.scanQRCode()}),1500),n.abrupt("return");case 6:if(t.pointInfo.qrcode_required){n.next=22;break}return n.prev=7,n.next=10,h.getCurrentLocation();case 10:if(e=n.sent,t.currentLocation=e,t.calculateDistance(),t.isInRange){n.next=16;break}return uni.showToast({title:"您已离开打卡范围，请靠近点位",icon:"none"}),n.abrupt("return");case 16:n.next=22;break;case 18:return n.prev=18,n.t0=n["catch"](7),uni.showToast({title:"获取当前位置失败，请重试",icon:"none"}),n.abrupt("return");case 22:t.doCheckIn(t.qrcodeVerified,t.qrcodeVerifyResult);case 23:case"end":return n.stop()}}),n,null,[[7,18]])})))()},handleQRCodeSuccess:function(){if(this.pointInfo.qrcode_required)this.doCheckIn(!0,this.qrcodeVerifyResult);else{var t=h.calculateDistance(this.currentLocation,{latitude:this.pointInfo.latitude,longitude:this.pointInfo.longitude});if(t<=this.qrcodeAllowedDistance)this.doCheckIn(!0,this.qrcodeVerifyResult);else{var n="您距离点位中心点".concat(Math.round(t),"米，请靠近后再扫码");this.showDistancePopup(n)}}},verifyQRCode:function(t){var n=this;return(0,u.default)((0,d.default)().mark((function e(){return(0,d.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,v.default.verifyQRCode(t,{pointId:n.pointInfo._id,checkExpired:!1});case 3:return e.abrupt("return",e.sent);case 6:return e.prev=6,e.t0=e["catch"](0),console.error("验证二维码出错",e.t0),e.abrupt("return",{valid:!1,code:"VERIFY_ERROR",message:"验证过程中出错"});case 10:case"end":return e.stop()}}),e,null,[[0,6]])})))()},doCheckIn:function(){var t=arguments,n=this;return(0,u.default)((0,d.default)().mark((function e(){var i,a,o,r,s,c,u,l,f,v,g,b,m,x;return(0,d.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=t.length>0&&void 0!==t[0]&&t[0],a=t.length>1&&void 0!==t[1]?t[1]:null,!n.loading){e.next=4;break}return e.abrupt("return");case 4:if(n.loading=!0,e.prev=5,!n.currentRound||!n.currentRound.points){e.next=14;break}if(o=n.currentRound.points.find((function(t){return t.point_id===n.pointInfo._id})),!(o&&o.status>0)){e.next=14;break}return n.distancePopupMessage="该点位在当前轮次已完成打卡",n.$refs.distancePopup.open(),setTimeout((function(){n.$refs.distancePopup.close()}),2e3),n.loading=!1,e.abrupt("return");case 14:if(i||!n.pointInfo||!n.pointInfo.qrcode_required){e.next=21;break}return n.distancePopupMessage="该点位必须使用二维码打卡",n.$refs.distancePopup.open(),setTimeout((function(){n.$refs.distancePopup.close()}),2e3),n.loading=!1,setTimeout((function(){n.scanQRCode()}),1500),e.abrupt("return");case 21:if(r=h.calculateDistance(n.currentLocation,{latitude:n.pointInfo.latitude,longitude:n.pointInfo.longitude}),s=!1,!i){e.next=36;break}if(!n.pointInfo.qrcode_required){e.next=28;break}s=!0,e.next=34;break;case 28:if(s=r<=n.qrcodeAllowedDistance,s){e.next=34;break}return c="您距离点位中心点".concat(Math.round(r),"米，请靠近后再扫码"),n.showDistancePopup(c),n.loading=!1,e.abrupt("return");case 34:e.next=43;break;case 36:if(n.isInRange){e.next=42;break}return n.distancePopupMessage="您不在打卡范围内",n.$refs.distancePopup.open(),setTimeout((function(){n.$refs.distancePopup.close()}),2e3),n.loading=!1,e.abrupt("return");case 42:s=!0;case 43:if(n.isRoundValid){e.next=47;break}return uni.showToast({title:n.roundErrorMessage||"当前轮次不可用",icon:"none"}),n.loading=!1,e.abrupt("return");case 47:if(u=[],!(n.imageList.length>0)){e.next=79;break}uni.showLoading({title:"正在上传图片...",mask:!0}),l=!1,f=0;case 52:if(!(f<n.imageList.length)){e.next=69;break}return e.prev=53,v=n.imageList[f],uni.showLoading({title:"上传第".concat(f+1,"/").concat(n.imageList.length,"张图片"),mask:!0}),e.next=58,n.uploadImage(v);case 58:g=e.sent,g&&u.push(g),e.next=66;break;case 62:e.prev=62,e.t0=e["catch"](53),console.error("图片上传失败",e.t0),l=!0;case 66:f++,e.next=52;break;case 69:if(uni.hideLoading(),!l){e.next=79;break}if(0!==u.length){e.next=76;break}return uni.showModal({title:"提示",content:"所有图片上传失败，是否继续提交打卡？",success:function(t){t.confirm||(n.loading=!1)}}),e.abrupt("return");case 76:if(u.length===n.imageList.length){e.next=79;break}return uni.showModal({title:"提示",content:"已成功上传".concat(u.length,"/").concat(n.imageList.length,"张图片，是否继续提交打卡？"),success:function(t){t.confirm||(n.loading=!1)}}),e.abrupt("return");case 79:return b=n.currentRound?n.currentRound.round:n.formData.round,m={task_id:n.taskInfo._id,point_id:n.pointInfo._id,round:b,location:{latitude:n.currentLocation.latitude,longitude:n.currentLocation.longitude,accuracy:n.currentLocation.accuracy},photos:u,remark:n.formData.remark,status:1,checkin_method:i?n.isInRange?"both":"qrcode":"gps"},i&&a&&a.qrcodeData&&(m.qrcode_verified=!0,m.qrcode_version=a.qrcodeData.v,m.qrcode_content=JSON.stringify({pid:a.qrcodeData.pid,v:a.qrcodeData.v,t:a.qrcodeData.t})),console.log("准备提交打卡数据:",m),uni.showLoading({title:"提交打卡信息...",mask:!0}),e.next=86,p.default.call({name:"patrol-record",action:"submitCheckIn",data:m});case 86:x=e.sent,uni.hideLoading(),0===x.code?n.onCheckinSuccess(x.data):uni.showToast({title:x.message||"打卡失败，请重试",icon:"none",duration:3e3}),e.next=96;break;case 91:e.prev=91,e.t1=e["catch"](5),uni.hideLoading(),console.error("提交打卡失败:",e.t1),uni.showToast({title:e.t1.message||"打卡失败，请重试",icon:"none",duration:3e3});case 96:return e.prev=96,n.loading=!1,e.finish(96);case 99:case"end":return e.stop()}}),e,null,[[5,91,96,99],[53,62]])})))()},handleScanResult:function(t){var n=this;return(0,u.default)((0,d.default)().mark((function e(){var i,a,o,r;return(0,d.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:e.prev=0,e.prev=1,i=JSON.parse(t),e.next=10;break;case 5:return e.prev=5,e.t0=e["catch"](1),console.error("二维码内容不是有效的JSON格式:",e.t0),uni.showToast({title:"不是有效的巡检点二维码",icon:"none",duration:2e3}),e.abrupt("return");case 10:if(i.type&&"PATROL_CHECK_IN"===i.type){e.next=14;break}return console.log("扫描到非巡检点二维码:",i),uni.showToast({title:"不是巡检点二维码",icon:"none",duration:2e3}),e.abrupt("return");case 14:if(a=i.pid,a){e.next=18;break}return uni.showToast({title:"无效的二维码格式",icon:"none",duration:2e3}),e.abrupt("return");case 18:return e.next=20,v.default.verifyQRCode(t,{pointId:n.pointInfo._id,checkExpired:!1});case 20:if(o=e.sent,n.qrcodeVerifyResult={valid:o.valid,title:o.valid?"验证成功":"验证失败",message:o.message,code:o.code,data:o.data,qrcodeData:i},!o.valid){e.next=33;break}if(n.qrcodeVerified=!0,n.qrcodeData=i,a===n.pointInfo._id){e.next=30;break}return e.next=28,p.default.getPointDetail(a);case 28:r=e.sent,0===r.code&&r.data&&n.setCurrentPoint(r.data);case 30:n.handleQRCodeSuccess(),e.next=34;break;case 33:uni.showToast({title:o.message||"二维码验证失败",icon:"none",duration:2e3});case 34:e.next=40;break;case 36:e.prev=36,e.t1=e["catch"](0),console.error("处理二维码失败",e.t1),uni.showToast({title:"二维码验证失败",icon:"none",duration:2e3});case 40:case"end":return e.stop()}}),e,null,[[0,36],[1,5]])})))()},setCurrentPoint:function(t){var n=this;if(t&&(this.pointInfo=t,this.taskInfo&&(this.taskInfo.point=this.pointInfo),this.updateMapMarkers(),this.updateCircles(),this.currentLocation.latitude&&this.currentLocation.longitude&&this.calculateDistance(),this.currentRound&&this.currentRound.points)){var e=this.currentRound.points.find((function(t){return t.point_id===n.pointInfo._id}));e&&e.status>0?(this.isRoundValid=!1,this.roundErrorMessage="该点位在当前轮次已完成打卡"):this.validateCurrentRound()}},onCheckinSuccess:function(t){var n=this;if(this.currentRound&&this.pointInfo){var e=this.currentRound.points.findIndex((function(t){return t.point_id===n.pointInfo._id}));-1!==e&&(this.currentRound.points[e].status=1,this.currentRound.points[e].checkin_time=new Date),this.currentRound.point_stats&&this.currentRound.point_stats.checked++,this.updateMapMarkers()}this.validateCurrentRound(),this.$nextTick((function(){if(n.getNextUnCheckedPoint(),!n.isAutoJumping){var t=n.nextUnCheckedPoint&&n.nextUnCheckedPoint.point_id;t?(n.isAutoJumping=!0,uni.showToast({title:"打卡成功，正在跳转下个点位...",icon:"success",duration:1500,mask:!1}),setTimeout((function(){n.isAutoJumping&&n.goToNextPoint()}),1500)):uni.showToast({title:"打卡成功，本轮次已完成！",icon:"success",duration:2e3,mask:!1})}})),getApp().globalData&&this.taskInfo&&this.taskInfo._id&&(console.log("设置打卡完成标记:",this.taskInfo._id),getApp().globalData.checkedInTaskId=this.taskInfo._id),uni.$emit("task-updated",{task_id:this.taskInfo._id,point_id:this.pointInfo._id,round:this.currentRound?this.currentRound.round:this.formData.round})},goBack:function(){uni.navigateBack()},stopLocationWatch:function(){this.locationWatchId&&(uni.stopLocationUpdate(),uni.offLocationChange(),this.locationWatchId=null)},scanQRCode:function(){var t=this;uni.authorize({scope:"scope.camera",success:function(){t.showScanner=!0},fail:function(){uni.showModal({title:"提示",content:"需要相机权限才能扫码，请在设置中允许使用相机",confirmText:"去设置",success:function(t){t.confirm&&uni.openSetting()}})}})},onScanCode:function(t){var n=this;return(0,u.default)((0,d.default)().mark((function e(){var i,a,o,r,s,c;return(0,d.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,i=t.detail.result,i){e.next=5;break}return uni.showToast({title:"无法识别二维码",icon:"none",duration:1e3}),e.abrupt("return");case 5:uni.vibrateShort({fail:function(){uni.vibrateLong()}}),a=Date.now(),uni.showLoading({title:"正在校验打卡信息，请稍候...",mask:!0}),n.$nextTick((function(){setTimeout((function(){n.showScanner=!1,n.updateMapMarkers(),n.updateCircles()}),50)})),e.prev=9,e.prev=10,o=JSON.parse(i),e.next=20;break;case 14:return e.prev=14,e.t0=e["catch"](10),e.next=18,n.ensureMinLoadingTime(a,1e3);case 18:return uni.showToast({title:"不是有效的巡检点二维码",icon:"none",duration:1e3}),e.abrupt("return");case 20:if(o.type&&"PATROL_CHECK_IN"===o.type){e.next=25;break}return e.next=23,n.ensureMinLoadingTime(a,1e3);case 23:return uni.showToast({title:"不是巡检点二维码",icon:"none",duration:1e3}),e.abrupt("return");case 25:if(r=o.pid,r){e.next=31;break}return e.next=29,n.ensureMinLoadingTime(a,1e3);case 29:return uni.showToast({title:"无效的二维码格式",icon:"none",duration:1e3}),e.abrupt("return");case 31:return e.next=33,v.default.verifyQRCode(i,{pointId:n.pointInfo._id,checkExpired:!1});case 33:return s=e.sent,e.next=36,n.ensureMinLoadingTime(a,1e3);case 36:if(n.qrcodeVerifyResult={valid:s.valid,title:s.valid?"验证成功":"验证失败",message:s.message,code:s.code,data:s.data,qrcodeData:o},!s.valid){e.next=48;break}if(n.qrcodeVerified=!0,n.qrcodeData=o,r===n.pointInfo._id){e.next=45;break}return e.next=43,p.default.getPointDetail(r);case 43:c=e.sent,0===c.code&&c.data&&n.setCurrentPoint(c.data);case 45:n.handleQRCodeSuccess(),e.next=49;break;case 48:uni.showToast({title:s.message||"二维码验证失败",icon:"none",duration:2e3});case 49:e.next=56;break;case 51:return e.prev=51,e.t1=e["catch"](9),e.next=55,n.ensureMinLoadingTime(a,1e3);case 55:uni.showToast({title:"二维码验证失败",icon:"none",duration:1e3});case 56:e.next=63;break;case 58:e.prev=58,e.t2=e["catch"](0),uni.hideLoading(),console.error("扫码处理失败",e.t2),uni.showToast({title:"二维码处理失败",icon:"none",duration:1e3});case 63:case"end":return e.stop()}}),e,null,[[0,58],[9,51],[10,14]])})))()}},(0,r.default)(o,"handleCameraError",(function(t){console.error("相机错误:",t),uni.showToast({title:"相机出错，请重试",icon:"none"}),this.showScanner=!1})),(0,r.default)(o,"closeScanner",(function(){this.showScanner=!1})),(0,r.default)(o,"ensureMinLoadingTime",(function(t,n){return(0,u.default)((0,d.default)().mark((function e(){var i;return(0,d.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=Date.now()-t,!(i<n)){e.next=4;break}return e.next=4,new Promise((function(t){return setTimeout(t,n-i)}));case 4:uni.hideLoading();case 5:case"end":return e.stop()}}),e)})))()})),(0,r.default)(o,"startRoundStatusTimer",(function(){var t=this;this.stopRoundStatusTimer(),this.roundStatusTimer=setInterval((function(){t.currentRound&&t.validateCurrentRound()}),1e3)})),(0,r.default)(o,"stopRoundStatusTimer",(function(){this.roundStatusTimer&&(clearInterval(this.roundStatusTimer),this.roundStatusTimer=null)})),(0,r.default)(o,"getNextUnCheckedPoint",(function(){var t=this;try{if(!this.currentRound||!this.currentRound.points||!Array.isArray(this.currentRound.points))return void(this.nextUnCheckedPoint=null);var n=this.currentRound.points.findIndex((function(n){return n.point_id===t.pointInfo._id})),e=null;if(-1!==n&&(e=this.currentRound.points.find((function(t,e){return e>n&&(!t.status||0===t.status)}))),!e&&(e=this.currentRound.points.find((function(t){return!t.status||0===t.status})),e&&e.point_id===this.pointInfo._id))return void(this.nextUnCheckedPoint=null);e&&e.location&&"object"===(0,c.default)(e.location)&&(e.latitude=e.latitude||e.location.latitude||e.location.lat,e.longitude=e.longitude||e.location.longitude||e.location.lng),this.nextUnCheckedPoint=e}catch(i){console.error("获取下个未打卡点位失败:",i),this.nextUnCheckedPoint=null}})),(0,r.default)(o,"getNextPointDistanceText",(function(){if(!this.nextUnCheckedPoint||!this.nextUnCheckedPoint.latitude||!this.nextUnCheckedPoint.longitude||!this.currentLocation.latitude||!this.currentLocation.longitude)return"点击查看";try{var t=h.calculateDistance(this.currentLocation,{latitude:this.nextUnCheckedPoint.latitude,longitude:this.nextUnCheckedPoint.longitude});return t<10?"很近":t<1e3?"".concat(Math.round(t),"米"):"".concat((t/1e3).toFixed(1),"公里")}catch(n){return console.error("计算下个点位距离失败:",n),"点击查看"}})),(0,r.default)(o,"goToNextPoint",(function(){var t=this;if(this.isAutoJumping=!1,this.nextUnCheckedPoint&&this.taskInfo){var n={point_id:this.nextUnCheckedPoint.point_id,task_id:this.taskInfo._id,round:this.currentRound?this.currentRound.round:this.formData.round},e=this.currentLocation.accuracy<=100?this.currentLocation:this.lastValidLocation||this.currentLocation;n.lat=e.latitude,n.lng=e.longitude,n.accuracy=e.accuracy||0,this.stopLocationWatch(),uni.redirectTo({url:"/pages/patrol_pkg/checkin/index?".concat(Object.keys(n).map((function(t){return"".concat(t,"=").concat(encodeURIComponent(n[t]))})).join("&")),success:function(){console.log("导航到下个点位成功")},fail:function(n){console.error("导航失败:",n),t.startLocationWatch(),t.isAutoJumping=!1,uni.showToast({title:"导航失败",icon:"none"})}})}else uni.showToast({title:"下个点位信息不完整",icon:"none"})})),(0,r.default)(o,"handleQRCodeClick",(function(){var t=this;if(this.isAutoJumping)this.showDisabledButtonFeedback("正在跳转下个点位，请稍后再试");else if(this.loading)this.showDisabledButtonFeedback("系统正在处理中，请稍后再试");else{if(this.currentRound&&this.currentRound.points){var n=this.currentRound.points.find((function(n){return n.point_id===t.pointInfo._id}));if(n&&n.status>0)return void this.showDisabledButtonFeedback("该点位在当前轮次已完成打卡")}this.isRoundValid?this.scanQRCode():this.showDisabledButtonFeedback(this.roundErrorMessage||"当前轮次暂不可用，请稍后再试")}})),(0,r.default)(o,"handleGPSClick",(function(){var t=this;if(this.isAutoJumping)this.showDisabledButtonFeedback("正在跳转下个点位，请稍后再试");else if(this.loading)this.showDisabledButtonFeedback("系统正在处理中，请稍后再试");else{if(this.currentRound&&this.currentRound.points){var n=this.currentRound.points.find((function(n){return n.point_id===t.pointInfo._id}));if(n&&n.status>0)return void this.showDisabledButtonFeedback("该点位在当前轮次已完成打卡")}this.isRoundValid?this.isInRange?this.submitCheckin():this.showDisabledButtonFeedback("您不在打卡范围内，请靠近点位后再试"):this.showDisabledButtonFeedback(this.roundErrorMessage||"当前轮次暂不可用，请稍后再试")}})),(0,r.default)(o,"showDisabledButtonFeedback",(function(t){uni.vibrateShort({fail:function(){uni.vibrateLong()}}),uni.showToast({title:t,icon:"none",duration:2e3})})),(0,r.default)(o,"showDistancePopup",(function(t){var n=this;this.distancePopupMessage=t,this.showDistanceMessage=!0,setTimeout((function(){n.showDistanceMessage=!1}),2e3)})),o)};n.default=g}).call(this,e("861b")["uniCloud"])},1003:function(t,n,e){e("aa9c");var i={utf8:{stringToBytes:function(t){return i.bin.stringToBytes(unescape(encodeURIComponent(t)))},bytesToString:function(t){return decodeURIComponent(escape(i.bin.bytesToString(t)))}},bin:{stringToBytes:function(t){for(var n=[],e=0;e<t.length;e++)n.push(255&t.charCodeAt(e));return n},bytesToString:function(t){for(var n=[],e=0;e<t.length;e++)n.push(String.fromCharCode(t[e]));return n.join("")}}};t.exports=i},"18fe":function(t,n,e){e("64aa"),e("aa9c"),e("c9b5"),e("bf0f"),e("ab80"),e("e966"),e("5c47"),e("a1c1"),function(){var n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",e={rotl:function(t,n){return t<<n|t>>>32-n},rotr:function(t,n){return t<<32-n|t>>>n},endian:function(t){if(t.constructor==Number)return 16711935&e.rotl(t,8)|4278255360&e.rotl(t,24);for(var n=0;n<t.length;n++)t[n]=e.endian(t[n]);return t},randomBytes:function(t){for(var n=[];t>0;t--)n.push(Math.floor(256*Math.random()));return n},bytesToWords:function(t){for(var n=[],e=0,i=0;e<t.length;e++,i+=8)n[i>>>5]|=t[e]<<24-i%32;return n},wordsToBytes:function(t){for(var n=[],e=0;e<32*t.length;e+=8)n.push(t[e>>>5]>>>24-e%32&255);return n},bytesToHex:function(t){for(var n=[],e=0;e<t.length;e++)n.push((t[e]>>>4).toString(16)),n.push((15&t[e]).toString(16));return n.join("")},hexToBytes:function(t){for(var n=[],e=0;e<t.length;e+=2)n.push(parseInt(t.substr(e,2),16));return n},bytesToBase64:function(t){for(var e=[],i=0;i<t.length;i+=3)for(var a=t[i]<<16|t[i+1]<<8|t[i+2],o=0;o<4;o++)8*i+6*o<=8*t.length?e.push(n.charAt(a>>>6*(3-o)&63)):e.push("=");return e.join("")},base64ToBytes:function(t){t=t.replace(/[^A-Z0-9+\/]/gi,"");for(var e=[],i=0,a=0;i<t.length;a=++i%4)0!=a&&e.push((n.indexOf(t.charAt(i-1))&Math.pow(2,-2*a+8)-1)<<2*a|n.indexOf(t.charAt(i))>>>6-2*a);return e}};t.exports=e}()},"45ed":function(t,n){function e(t){return!!t.constructor&&"function"===typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
t.exports=function(t){return null!=t&&(e(t)||function(t){return"function"===typeof t.readFloatLE&&"function"===typeof t.slice&&e(t.slice(0,0))}(t)||!!t._isBuffer)}},"5fcb":function(t,n,e){var i=e("c86c");n=i(!1),n.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.checkin-container[data-v-04585de4]{display:flex;flex-direction:column;height:100vh;background-color:#f7f8fa;position:relative;padding-top:calc(0px + %?24?%)}\n/* 导航头样式 */.nav-header[data-v-04585de4]{position:relative;width:calc(100% - %?48?%);height:%?88?%;margin:0 %?24?%;background:#fff;display:flex;align-items:center;justify-content:center;z-index:100;border-radius:%?16?%;box-shadow:0 %?2?% %?12?% rgba(0,0,0,.05);margin-bottom:%?12?%}.nav-left[data-v-04585de4]{position:absolute;left:%?24?%;height:%?88?%;display:flex;align-items:center}.nav-back[data-v-04585de4]{width:%?88?%;height:%?88?%;display:flex;align-items:center;justify-content:flex-start}.nav-title[data-v-04585de4]{font-size:%?34?%;font-weight:600;color:#000;line-height:%?88?%}\n/* 顶部信息区域样式重构 */.info-section[data-v-04585de4]{margin:%?24?% %?24?% %?20?%;padding:0;border-radius:%?16?%;background:#fff;box-shadow:0 %?2?% %?12?% rgba(0,0,0,.05);overflow:hidden}.info-content[data-v-04585de4]{padding:%?24?%}.status-indicator[data-v-04585de4]{display:flex;align-items:center;gap:%?12?%;margin-bottom:%?20?%}.status-indicator .indicator-dot[data-v-04585de4]{width:%?8?%;height:%?8?%;border-radius:50%;background:#ff3b30;position:relative}.status-indicator .indicator-dot[data-v-04585de4]::after{content:"";position:absolute;top:%?-4?%;left:%?-4?%;right:%?-4?%;bottom:%?-4?%;background:rgba(255,59,48,.2);border-radius:50%;-webkit-animation:pulse 2s infinite;animation:pulse 2s infinite}.status-indicator .status-text[data-v-04585de4]{font-size:%?28?%;color:#ff3b30;font-weight:500}.status-indicator--active .indicator-dot[data-v-04585de4]{background:#34c759}.status-indicator--active .indicator-dot[data-v-04585de4]::after{background:rgba(52,199,89,.2)}.status-indicator--active .status-text[data-v-04585de4]{color:#34c759}.distance-info[data-v-04585de4]{display:flex;justify-content:space-between;align-items:center}.distance-info .distance-value .value[data-v-04585de4]{font-size:%?48?%;font-weight:600;color:#1677ff;letter-spacing:%?-1?%}.distance-info .round-badge[data-v-04585de4]{padding:%?8?% %?16?%;background:rgba(22,119,255,.1);border-radius:%?8?%;display:flex;align-items:center;gap:%?6?%}.distance-info .round-badge .uni-icons[data-v-04585de4]{color:#1677ff!important}.distance-info .round-badge uni-text[data-v-04585de4]{font-size:%?24?%;color:#1677ff;font-weight:500}\n/* 加载状态样式优化 */.loading-container[data-v-04585de4]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;width:100%;position:absolute;top:calc(0px + %?88?%);\n  /* 调整top值，加上导航头高度 */left:0;background-color:hsla(0,0%,100%,.98);z-index:999}.loading-container .loading-spinner[data-v-04585de4]{width:%?80?%;height:%?80?%;position:relative;margin-bottom:%?30?%}.loading-container .loading-spinner .spinner-circle[data-v-04585de4]{width:100%;height:100%;border:%?6?% solid #1677ff;border-top-color:transparent;border-radius:50%;-webkit-animation:spin 1s linear infinite;animation:spin 1s linear infinite}.loading-container .loading-text[data-v-04585de4]{font-size:%?28?%;color:#666;letter-spacing:%?2?%}\n/* 地图区域优化 */.map-section[data-v-04585de4]{flex:none;height:35vh;\n  /* 减小地图高度 */position:relative;margin:%?20?% %?30?%;border-radius:%?24?%;overflow:hidden;box-shadow:0 %?8?% %?32?% rgba(0,0,0,.08)}.checkin-map[data-v-04585de4]{width:100%;height:100%}.map-controls[data-v-04585de4]{position:absolute;right:%?24?%;top:%?24?%;display:flex;flex-direction:column;gap:%?12?%}.map-controls .control-btn[data-v-04585de4]{width:%?72?%;height:%?72?%;background:hsla(0,0%,100%,.95);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:%?16?%;display:flex;align-items:center;justify-content:center;box-shadow:0 %?4?% %?12?% rgba(0,0,0,.1);transition:all .2s cubic-bezier(.4,0,.2,1)}.map-controls .control-btn .uni-icons[data-v-04585de4]{color:#8f959e!important}.map-controls .control-btn[data-v-04585de4]:active{-webkit-transform:scale(.92);transform:scale(.92);background:hsla(0,0%,100%,.98)}.map-controls .control-btn--active[data-v-04585de4]{background:rgba(52,199,89,.15)}.map-controls .control-btn--active .uni-icons[data-v-04585de4]{color:#34c759!important}\n/* 内容区域包装器 */.content-wrapper[data-v-04585de4]{flex:1;overflow:hidden;padding-bottom:calc(%?230?% + constant(safe-area-inset-bottom));\n  /* iOS 11.2以下 */padding-bottom:calc(%?230?% + env(safe-area-inset-bottom))\n  /* iOS 11.2+ */}\n/* 错误提示区域样式 */.warning-area[data-v-04585de4]{margin:%?20?% %?30?%;padding:%?24?%;border-radius:%?24?%;background:hsla(0,0%,100%,.95);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);box-shadow:0 %?8?% %?32?% rgba(0,0,0,.08);display:flex;\n  /* 添加flex布局 */align-items:center;\n  /* 垂直居中对齐 */gap:%?12?%\n  /* 添加图标和文字之间的间距 */}.warning-area .warning-icon[data-v-04585de4]{flex-shrink:0;\n  /* 防止图标缩小 */width:%?36?%;\n  /* 调整图标容器大小 */height:%?36?%;display:flex;\n  /* 使图标居中 */align-items:center;justify-content:center;margin-bottom:0\n  /* 移除底部间距 */}.warning-area .warning-text[data-v-04585de4]{font-size:%?28?%;color:#8e8e93;flex:1;\n  /* 文字占据剩余空间 */line-height:1.4\n  /* 添加适当的行高 */}\n/* 底部操作区域样式 */.action-section[data-v-04585de4]{position:fixed;bottom:0;left:0;right:0;padding:%?24?%;padding-bottom:calc(%?24?% + constant(safe-area-inset-bottom));padding-bottom:calc(%?24?% + env(safe-area-inset-bottom));background:#fff;box-shadow:0 %?-4?% %?16?% rgba(0,0,0,.05);z-index:100}.checkin-btn-container[data-v-04585de4]{display:flex;gap:%?20?%;width:100%;min-height:%?96?%}.btn-checkin[data-v-04585de4]{flex:1;height:%?96?%;min-height:%?96?%;border-radius:%?20?%;color:#fff!important;font-size:%?32?%;font-weight:600;display:flex;align-items:center;justify-content:center;gap:%?12?%;margin:0;padding:0;position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1)}.btn-checkin.gps-button[data-v-04585de4]{background:linear-gradient(135deg,#1677ff,#0056d6)}.btn-checkin.qrcode-button[data-v-04585de4]{background:linear-gradient(135deg,#34c759,#30d158)}.btn-checkin uni-text[data-v-04585de4]{color:#fff!important}.btn-checkin[data-v-04585de4]::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(180deg,hsla(0,0%,100%,.1),transparent);opacity:0;transition:opacity .3s ease}.btn-checkin[data-v-04585de4]:active::before{opacity:1}.btn-checkin.btn-disabled[data-v-04585de4]{background:linear-gradient(135deg,#8e8e93,#636366);opacity:.8;box-shadow:none}.btn-checkin.btn-disabled uni-text[data-v-04585de4]{color:hsla(0,0%,100%,.6)!important}.btn-checkin.btn-disabled .uni-icons[data-v-04585de4]{opacity:.6}\n/* 添加GPS精度显示样式 */.location-accuracy[data-v-04585de4]{position:absolute;left:%?20?%;bottom:%?20?%;background:hsla(0,0%,100%,.95);-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);padding:%?12?% %?20?%;border-radius:%?12?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.1);z-index:90;display:flex;align-items:center;gap:%?12?%}.status-dot[data-v-04585de4]{width:%?12?%;height:%?12?%;border-radius:50%;flex-shrink:0}.accuracy-text[data-v-04585de4]{font-size:%?26?%;color:#333;font-weight:500}\n/* 相机容器样式 */.camera-container[data-v-04585de4]{position:fixed;top:0;left:0;width:100%;height:100%;z-index:999;background-color:#000;display:flex;flex-direction:column}\n/* 相机控制栏样式 */.camera-controls[data-v-04585de4]{position:fixed;left:0;bottom:0;width:100%;height:%?180?%;\n  /* 增加高度，给底部安全区域留空间 */background-color:rgba(0,0,0,.7);display:flex;justify-content:space-around;\n  /* 改为space-around确保均匀分布 */align-items:center;padding-bottom:constant(safe-area-inset-bottom);\n  /* 兼容 iOS < 11.2 */padding-bottom:env(safe-area-inset-bottom)\n  /* 兼容 iOS >= 11.2 */}.control-item[data-v-04585de4]{display:flex;flex-direction:column;align-items:center;justify-content:center;width:%?140?%;\n  /* 统一宽度 */height:%?140?%\n  /* 统一高度 */}.photo-btn[data-v-04585de4]{width:%?140?%;height:%?140?%;border-radius:%?70?%;background-color:hsla(0,0%,100%,.2);display:flex;justify-content:center;align-items:center}.photo-circle[data-v-04585de4]{width:%?110?%;height:%?110?%;border-radius:%?55?%;background-color:#fff;border:%?4?% solid hsla(0,0%,100%,.8)}.control-item uni-text[data-v-04585de4]{font-size:%?24?%;color:#fff;margin-top:%?8?%}\n/* 修改闪光灯popup样式 */.popup-content[data-v-04585de4]{background-color:rgba(0,0,0,.75);color:#fff;padding:%?16?% %?32?%;border-radius:%?8?%;display:flex;align-items:center;justify-content:center;-webkit-column-gap:%?12?%;column-gap:%?12?%;margin:%?20?%;box-shadow:0 %?4?% %?12?% rgba(0,0,0,.2)}.popup-content uni-text[data-v-04585de4]{font-size:%?28?%}\n/* 移除不需要的样式 */.flash-status[data-v-04585de4], .flash-indicator[data-v-04585de4]{display:none}\n/* 确保删除按钮样式正确 */.delete-btn[data-v-04585de4]{position:absolute;top:%?10?%;right:%?10?%;width:%?44?%;height:%?44?%;background-color:rgba(0,0,0,.5);border-radius:50%;display:flex;align-items:center;justify-content:center;z-index:2}.checkin-btn-container[data-v-04585de4]{display:flex;justify-content:center;gap:%?20?%;margin:0\n  /* 移除上下边距 */}.checkin-btn[data-v-04585de4]{height:%?90?%;background-color:#1677ff;color:#fff;border-radius:%?999?%;display:flex;align-items:center;justify-content:center;padding:0 %?40?%;font-size:%?32?%;font-weight:500;box-shadow:0 %?6?% %?16?% rgba(22,119,255,.2);transition:all .3s ease;flex:1;max-width:%?300?%}.checkin-btn[data-v-04585de4]:active{-webkit-transform:scale(.95);transform:scale(.95);opacity:.9}.checkin-btn .btn-text[data-v-04585de4]{margin-left:%?12?%}.checkin-btn.qrcode-btn[data-v-04585de4]{background-color:#07c160;box-shadow:0 %?6?% %?16?% rgba(7,193,96,.2)}\n/* 二维码验证状态样式 */.qrcode-verified[data-v-04585de4]{margin:%?12?% 0;padding:%?8?% %?16?%;background:rgba(7,193,96,.1);border-radius:%?8?%;display:flex;align-items:center;z-index:95\n  /* 确保在GPS精度显示之上 */}.qrcode-verified uni-text[data-v-04585de4]{font-size:%?26?%;color:#07c160;margin-left:%?8?%;font-weight:500}\n/* 距离提示弹窗样式 */.distance-popup-content[data-v-04585de4]{background-color:rgba(0,0,0,.75);color:#fff;padding:%?16?% %?32?%;border-radius:%?8?%;display:flex;align-items:center;justify-content:center;-webkit-column-gap:%?12?%;column-gap:%?12?%;margin:%?20?%;box-shadow:0 %?4?% %?12?% rgba(0,0,0,.2)}.distance-popup-text[data-v-04585de4]{font-size:%?28?%}\n/* 扫码相机样式 */.scanner-container[data-v-04585de4]{position:fixed;top:0;left:0;width:100%;height:100vh;background-color:#000;z-index:999}.scanner-camera[data-v-04585de4]{width:100%;height:100vh}.scan-frame[data-v-04585de4]{position:absolute;left:50%;top:40%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:%?500?%;\n  /* 增大扫码框尺寸 */height:%?500?%}.scan-frame .frame-line[data-v-04585de4]{position:absolute;background-color:hsla(0,0%,100%,.25)\n  /* 增加边线颜色透明度，使其更明显 */}.scan-frame .frame-line.top[data-v-04585de4], .scan-frame .frame-line.bottom[data-v-04585de4]{left:%?40?%;\n  /* 稍微减小边距 */right:%?40?%;height:2px\n  /* 增加边线宽度 */}.scan-frame .frame-line.left[data-v-04585de4], .scan-frame .frame-line.right[data-v-04585de4]{top:%?40?%;\n  /* 稍微减小边距 */bottom:%?40?%;width:2px\n  /* 增加边线宽度 */}.scan-frame .frame-line.top[data-v-04585de4]{top:0}.scan-frame .frame-line.right[data-v-04585de4]{right:0}.scan-frame .frame-line.bottom[data-v-04585de4]{bottom:0}.scan-frame .frame-line.left[data-v-04585de4]{left:0}.scan-frame .corner-box[data-v-04585de4]{position:absolute;width:%?28?%;\n  /* 调小角标尺寸 */height:%?28?%;border-color:#34c759;border-style:solid;border-width:%?3?%;\n  /* 调细边框 */background-color:initial;box-shadow:0 0 %?8?% rgba(52,199,89,.3)\n  /* 添加微弱发光效果 */}.scan-frame .left-top[data-v-04585de4]{left:0;top:0;border-right:none;border-bottom:none}.scan-frame .right-top[data-v-04585de4]{right:0;top:0;border-left:none;border-bottom:none}.scan-frame .left-bottom[data-v-04585de4]{left:0;bottom:0;border-right:none;border-top:none}.scan-frame .right-bottom[data-v-04585de4]{right:0;bottom:0;border-left:none;border-top:none}.scan-frame .scan-line[data-v-04585de4]{position:absolute;left:%?48?%;\n  /* 调整扫描线边距，与frame-line对应 */right:%?48?%;top:0;height:2px;background:linear-gradient(90deg,rgba(52,199,89,0),rgba(52,199,89,.8),rgba(52,199,89,0));\n  /* 优化扫描线渐变效果 */-webkit-animation:scanMove-data-v-04585de4 2s linear infinite;animation:scanMove-data-v-04585de4 2s linear infinite;box-shadow:0 0 %?8?% rgba(52,199,89,.3)\n  /* 微弱发光效果 */}\n/* 优化扫描提示文字样式 */.scan-tips[data-v-04585de4]{position:absolute;left:0;bottom:%?300?%;width:100%;text-align:center}.scan-tips .tips-text[data-v-04585de4]{color:#fff;font-size:%?28?%;text-shadow:0 %?2?% %?4?% rgba(0,0,0,.3);background:rgba(0,0,0,.15);\n  /* 调整背景透明度 */padding:%?12?% %?24?%;border-radius:%?100?%;display:inline-block;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px)\n  /* 添加模糊效果 */}@-webkit-keyframes scanMove-data-v-04585de4{0%{top:0;opacity:.8}50%{top:calc(100% - 2px);opacity:.6\n    /* 扫描线动画过程中稍微淡化 */}100%{top:0;opacity:.8}}@keyframes scanMove-data-v-04585de4{0%{top:0;opacity:.8}50%{top:calc(100% - 2px);opacity:.6\n    /* 扫描线动画过程中稍微淡化 */}100%{top:0;opacity:.8}}.scanner-controls[data-v-04585de4]{position:fixed;left:0;bottom:0;width:100%;height:%?180?%;background:rgba(0,0,0,.7);display:flex;justify-content:space-around;align-items:center;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.control-item[data-v-04585de4]{display:flex;flex-direction:column;align-items:center;justify-content:center;width:%?140?%;height:%?140?%}.control-item .control-icon[data-v-04585de4]{width:%?48?%;height:%?48?%;margin-bottom:%?8?%}.control-item .control-text[data-v-04585de4]{font-size:%?24?%;color:#fff}\n/* 信息卡片样式 */.info-card[data-v-04585de4]{margin:%?20?% %?30?%;border-radius:%?16?%;background:#fff;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.06);overflow:hidden}.info-card.next-point[data-v-04585de4]{cursor:pointer;transition:all .2s ease}.info-card.next-point[data-v-04585de4]:active{-webkit-transform:scale(.98);transform:scale(.98);box-shadow:0 %?1?% %?4?% rgba(0,0,0,.1)}.card-header[data-v-04585de4]{display:flex;align-items:center;padding:%?24?%;gap:%?16?%}.point-icon[data-v-04585de4]{width:%?44?%;height:%?44?%;border-radius:50%;display:flex;align-items:center;justify-content:center;flex-shrink:0}.point-icon.current[data-v-04585de4]{background:#007aff}.point-icon.next[data-v-04585de4]{background:#34c759}.point-icon.completed[data-v-04585de4]{background:#ff9500}.point-icon.last[data-v-04585de4]{background:#5856d6}.point-info[data-v-04585de4]{flex:1;display:flex;flex-direction:column;gap:%?4?%}.point-title[data-v-04585de4]{font-size:%?24?%;color:#8e8e93;font-weight:500}.point-name[data-v-04585de4]{font-size:%?32?%;color:#1c1c1e;font-weight:600}\n/* 统一标签基础样式 */.base-badge[data-v-04585de4], .completed-badge[data-v-04585de4], .distance-badge[data-v-04585de4], .point-status[data-v-04585de4]{display:flex;align-items:center;gap:%?6?%;padding:%?4?% %?12?%;border-radius:%?16?%;flex-shrink:0;height:%?32?%}.point-status[data-v-04585de4]{margin-left:auto}.point-status.checked[data-v-04585de4]{background:rgba(52,199,89,.1)}.point-status.checked .status-text[data-v-04585de4]{color:#34c759}.point-status.pending[data-v-04585de4]{background:rgba(255,149,0,.1)}.point-status.pending .status-text[data-v-04585de4]{color:#ff9500}.point-status .status-text[data-v-04585de4]{font-size:%?22?%;font-weight:500;line-height:%?32?%}.distance-badge[data-v-04585de4]{background:rgba(0,122,255,.1)}.distance-badge .uni-icons[data-v-04585de4]{color:#007aff!important;font-size:%?24?%;line-height:%?32?%}.distance-text[data-v-04585de4]{font-size:%?22?%;color:#007aff;font-weight:600;line-height:%?32?%}.completed-badge[data-v-04585de4]{background:rgba(255,149,0,.1)}.completed-badge .status-text[data-v-04585de4]{font-size:%?22?%;color:#ff9500;font-weight:500;line-height:%?32?%}.last-badge[data-v-04585de4]{padding:%?8?% %?16?%;border-radius:%?12?%;background:rgba(88,86,214,.1);flex-shrink:0;display:flex;align-items:center;gap:%?6?%}.last-badge .status-text[data-v-04585de4]{font-size:%?22?%;color:#5856d6;font-weight:500}.card-details[data-v-04585de4]{padding:0 %?24?% %?24?%;display:flex;flex-direction:column;gap:%?8?%}.detail-text[data-v-04585de4]{font-size:%?26?%;color:#666;line-height:1.4}\n/* 下个点位卡片样式优化 */.info-card.next-point[data-v-04585de4]{-webkit-tap-highlight-color:transparent;\n  /* 移除默认点击态 */-webkit-user-select:none;user-select:none\n  /* 防止文本被选中 */}.info-card.next-point[data-v-04585de4]:active{background-color:#f5f5f5;\n  /* 点击时的背景色 */-webkit-transform:scale(.98);transform:scale(.98);\n  /* 轻微缩小效果 */transition:all .2s ease\n  /* 平滑过渡 */}.custom-distance-message[data-v-04585de4]{position:fixed;left:%?24?%;right:%?24?%;top:calc(0px + %?24?%);z-index:999;background-color:#ffe9e9;height:%?88?%;box-shadow:0 2px 8px rgba(255,59,48,.15);-webkit-animation:slideDown-data-v-04585de4 .3s ease-out;animation:slideDown-data-v-04585de4 .3s ease-out;border-radius:%?16?%;display:flex;align-items:center;justify-content:center}.custom-distance-message .message-text[data-v-04585de4]{font-size:%?28?%;color:#ff3b30;font-weight:600;text-align:center}@-webkit-keyframes slideDown-data-v-04585de4{from{-webkit-transform:translateY(%?-24?%);transform:translateY(%?-24?%);opacity:0}to{-webkit-transform:translateY(0);transform:translateY(0);opacity:1}}@keyframes slideDown-data-v-04585de4{from{-webkit-transform:translateY(%?-24?%);transform:translateY(%?-24?%);opacity:0}to{-webkit-transform:translateY(0);transform:translateY(0);opacity:1}}',""]),t.exports=n},"876f":function(t,n,e){e("f7a5"),e("4db2"),e("bf0f"),e("c976"),e("4d8f"),e("7b97"),e("668a"),e("c5b7"),e("8ff5"),e("2378"),e("641a"),e("64e0"),e("cce3"),e("efba"),e("d009"),e("bd7d"),e("7edd"),e("d798"),e("f547"),e("5e54"),e("b60a"),e("8c18"),e("12973"),e("f991"),e("198e"),e("8557"),e("63b1"),e("1954"),e("1cf1"),e("c9b5"),e("ab80"),e("7a76"),function(){var n=e("18fe"),i=e("1003").utf8,a=e("45ed"),o=e("1003").bin,r=function t(e,r){e.constructor==String?e=r&&"binary"===r.encoding?o.stringToBytes(e):i.stringToBytes(e):a(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var s=n.bytesToWords(e),c=8*e.length,d=1732584193,u=-271733879,l=-1732584194,f=271733878,h=0;h<s.length;h++)s[h]=16711935&(s[h]<<8|s[h]>>>24)|4278255360&(s[h]<<24|s[h]>>>8);s[c>>>5]|=128<<c%32,s[14+(c+64>>>9<<4)]=c;var p=t._ff,v=t._gg,g=t._hh,b=t._ii;for(h=0;h<s.length;h+=16){var m=d,x=u,k=l,w=f;d=p(d,u,l,f,s[h+0],7,-680876936),f=p(f,d,u,l,s[h+1],12,-389564586),l=p(l,f,d,u,s[h+2],17,606105819),u=p(u,l,f,d,s[h+3],22,-1044525330),d=p(d,u,l,f,s[h+4],7,-176418897),f=p(f,d,u,l,s[h+5],12,1200080426),l=p(l,f,d,u,s[h+6],17,-1473231341),u=p(u,l,f,d,s[h+7],22,-45705983),d=p(d,u,l,f,s[h+8],7,1770035416),f=p(f,d,u,l,s[h+9],12,-1958414417),l=p(l,f,d,u,s[h+10],17,-42063),u=p(u,l,f,d,s[h+11],22,-1990404162),d=p(d,u,l,f,s[h+12],7,1804603682),f=p(f,d,u,l,s[h+13],12,-40341101),l=p(l,f,d,u,s[h+14],17,-1502002290),u=p(u,l,f,d,s[h+15],22,1236535329),d=v(d,u,l,f,s[h+1],5,-165796510),f=v(f,d,u,l,s[h+6],9,-1069501632),l=v(l,f,d,u,s[h+11],14,643717713),u=v(u,l,f,d,s[h+0],20,-373897302),d=v(d,u,l,f,s[h+5],5,-701558691),f=v(f,d,u,l,s[h+10],9,38016083),l=v(l,f,d,u,s[h+15],14,-660478335),u=v(u,l,f,d,s[h+4],20,-405537848),d=v(d,u,l,f,s[h+9],5,568446438),f=v(f,d,u,l,s[h+14],9,-1019803690),l=v(l,f,d,u,s[h+3],14,-187363961),u=v(u,l,f,d,s[h+8],20,1163531501),d=v(d,u,l,f,s[h+13],5,-1444681467),f=v(f,d,u,l,s[h+2],9,-51403784),l=v(l,f,d,u,s[h+7],14,1735328473),u=v(u,l,f,d,s[h+12],20,-1926607734),d=g(d,u,l,f,s[h+5],4,-378558),f=g(f,d,u,l,s[h+8],11,-2022574463),l=g(l,f,d,u,s[h+11],16,1839030562),u=g(u,l,f,d,s[h+14],23,-35309556),d=g(d,u,l,f,s[h+1],4,-1530992060),f=g(f,d,u,l,s[h+4],11,1272893353),l=g(l,f,d,u,s[h+7],16,-155497632),u=g(u,l,f,d,s[h+10],23,-1094730640),d=g(d,u,l,f,s[h+13],4,681279174),f=g(f,d,u,l,s[h+0],11,-358537222),l=g(l,f,d,u,s[h+3],16,-722521979),u=g(u,l,f,d,s[h+6],23,76029189),d=g(d,u,l,f,s[h+9],4,-640364487),f=g(f,d,u,l,s[h+12],11,-421815835),l=g(l,f,d,u,s[h+15],16,530742520),u=g(u,l,f,d,s[h+2],23,-995338651),d=b(d,u,l,f,s[h+0],6,-198630844),f=b(f,d,u,l,s[h+7],10,1126891415),l=b(l,f,d,u,s[h+14],15,-1416354905),u=b(u,l,f,d,s[h+5],21,-57434055),d=b(d,u,l,f,s[h+12],6,1700485571),f=b(f,d,u,l,s[h+3],10,-1894986606),l=b(l,f,d,u,s[h+10],15,-1051523),u=b(u,l,f,d,s[h+1],21,-2054922799),d=b(d,u,l,f,s[h+8],6,1873313359),f=b(f,d,u,l,s[h+15],10,-30611744),l=b(l,f,d,u,s[h+6],15,-1560198380),u=b(u,l,f,d,s[h+13],21,1309151649),d=b(d,u,l,f,s[h+4],6,-145523070),f=b(f,d,u,l,s[h+11],10,-1120210379),l=b(l,f,d,u,s[h+2],15,718787259),u=b(u,l,f,d,s[h+9],21,-343485551),d=d+m>>>0,u=u+x>>>0,l=l+k>>>0,f=f+w>>>0}return n.endian([d,u,l,f])};r._ff=function(t,n,e,i,a,o,r){var s=t+(n&e|~n&i)+(a>>>0)+r;return(s<<o|s>>>32-o)+n},r._gg=function(t,n,e,i,a,o,r){var s=t+(n&i|e&~i)+(a>>>0)+r;return(s<<o|s>>>32-o)+n},r._hh=function(t,n,e,i,a,o,r){var s=t+(n^e^i)+(a>>>0)+r;return(s<<o|s>>>32-o)+n},r._ii=function(t,n,e,i,a,o,r){var s=t+(e^(n|~i))+(a>>>0)+r;return(s<<o|s>>>32-o)+n},r._blocksize=16,r._digestsize=16,t.exports=function(t,e){if(void 0===t||null===t)throw new Error("Illegal argument "+t);var i=n.wordsToBytes(r(t,e));return e&&e.asBytes?i:e&&e.asString?o.bytesToString(i):n.bytesToHex(i)}}()},"88de":function(t,n,e){"use strict";e.r(n);var i=e("0e01"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},"95a7":function(t,n,e){"use strict";e("6a54");var i=e("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,n.getQRCodeData=l,n.incrementQRCodeVersion=v,n.parseQRCodeContent=f,n.verifyQRCode=h,e("d4b5"),e("0c26"),e("7a76"),e("c9b5");var a=i(e("fcf3")),o=i(e("2634")),r=i(e("2fdc")),s=i(e("9b1b")),c=i(e("876f")),d=i(e("b96b")),u="CU5t0mPatr0lQrc0d3S3cr3tKey";function l(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t||!t._id)return console.error("点位信息不完整"),"";var e,i=!1!==n.includeTimestamp;if(t.qrcode_content)try{var a=JSON.parse(t.qrcode_content);e=a.t}catch(v){console.error("解析现有二维码内容失败:",v)}!e&&i&&(e=Date.now());var o=t.qrcode_version||1,r=t.qrcode_hash_key||u;console.log("生成二维码使用的密钥:",r);var d={type:"PATROL_CHECK_IN",pid:t._id,name:t.name||"",v:o,t:e||0},l=["type="+d.type,"pid="+d.pid,"name="+d.name,"v="+d.v,"t="+d.t].join("&"),f=l+"|"+r;console.log("生成二维码的内容字符串:",l),console.log("生成二维码的完整字符串:",f);var h=(0,c.default)(f).substring(0,8);console.log("生成的哈希值:",h);var p=(0,s.default)((0,s.default)({},d),{},{h:h});return JSON.stringify(p)}function f(t){try{var n=JSON.parse(t);return n.pid&&n.h&&n.type&&"PATROL_CHECK_IN"===n.type?n:(console.error("二维码内容缺失必要参数或类型不正确"),null)}catch(e){return console.error("解析二维码内容失败",e),null}}function h(t){return p.apply(this,arguments)}function p(){return p=(0,r.default)((0,o.default)().mark((function t(n){var e,i,a,r,l,h,p,v,g,b,m,x=arguments;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=x.length>1&&void 0!==x[1]?x[1]:{},i={pointId:null,checkExpired:!0,expireTime:864e5},a=(0,s.default)((0,s.default)({},i),e),r=f(n),r){t.next=6;break}return t.abrupt("return",{valid:!1,code:"PARSE_ERROR",message:"二维码格式错误"});case 6:if(!a.pointId||r.pid===a.pointId){t.next=8;break}return t.abrupt("return",{valid:!1,code:"POINT_MISMATCH",message:"二维码不匹配当前点位"});case 8:if(!a.checkExpired||!r.t){t.next=12;break}if(l=Date.now(),!(l-r.t>a.expireTime)){t.next=12;break}return t.abrupt("return",{valid:!1,code:"EXPIRED",message:"二维码已过期"});case 12:return t.prev=12,t.next=15,d.default.callPointFunction("getPointDetail",{point_id:r.pid});case 15:if(h=t.sent,h&&h.data){t.next=18;break}return t.abrupt("return",{valid:!1,code:"POINT_NOT_FOUND",message:"点位信息不存在"});case 18:if(p=h.data,v=p.qrcode_hash_key||u,console.log("验证二维码使用的密钥:",v),console.log("验证的二维码数据:",r),g=["type="+r.type,"pid="+r.pid,"name="+r.name,"v="+r.v,"t="+r.t].join("&"),b=g+"|"+v,console.log("验证时的内容字符串:",g),console.log("验证时的完整字符串:",b),m=(0,c.default)(b).substring(0,8),console.log("哈希值对比:",{expected:m,actual:r.h,content:g,secretKey:v,pointInfo:{id:p._id,name:p.name,hash_key:p.qrcode_hash_key,version:p.qrcode_version}}),r.h===m){t.next=30;break}return t.abrupt("return",{valid:!1,code:"INVALID_HASH",message:"二维码验证失败"});case 30:return t.abrupt("return",{valid:!0,code:"SUCCESS",message:"验证通过",data:p});case 33:return t.prev=33,t.t0=t["catch"](12),console.error("验证二维码时发生错误",t.t0),t.abrupt("return",{valid:!1,code:"VERIFY_ERROR",message:"验证过程发生错误"});case 37:case"end":return t.stop()}}),t,null,[[12,33]])}))),p.apply(this,arguments)}function v(t){return g.apply(this,arguments)}function g(){return g=(0,r.default)((0,o.default)().mark((function t(n){var e,i;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(console.log("incrementQRCodeVersion被调用，参数:",{pointId:n,type:(0,a.default)(n)}),n&&"string"===typeof n&&""!==n.trim()){t.next=4;break}throw console.error("incrementQRCodeVersion: 无效的点位ID",{pointId:n,type:(0,a.default)(n)}),new Error("缺少点位ID");case 4:return t.prev=4,console.log("调用云函数updatePointQRCode，参数:",{params:{point_id:n.trim(),increment_version:!0}}),t.next=8,d.default.callPointFunction("updatePointQRCode",{params:{point_id:n.trim(),increment_version:!0}});case 8:if(e=t.sent,console.log("云函数返回结果:",e),0!==e.code||!e.data){t.next=16;break}return i={updated:!0,oldVersion:e.data.qrcode_version-1,newVersion:e.data.qrcode_version,result:e.data},console.log("函数返回数据:",i),t.abrupt("return",i);case 16:throw console.error("云函数返回错误:",e),new Error(e.message||"更新失败");case 18:t.next=24;break;case 20:throw t.prev=20,t.t0=t["catch"](4),console.error("更新二维码版本失败",{error:t.t0,pointId:n,errorMessage:t.t0.message}),t.t0;case 24:case"end":return t.stop()}}),t,null,[[4,20]])}))),g.apply(this,arguments)}var b={getQRCodeData:l,parseQRCodeContent:f,verifyQRCode:h,incrementQRCodeVersion:v};n.default=b},ad15:function(t,n,e){"use strict";var i=e("fed5"),a=e.n(i);a.a},cd20:function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return i}));var i={uniIcons:e("6ddf").default},a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",{staticClass:"checkin-container"},[e("v-uni-view",{staticClass:"nav-header"},[e("v-uni-view",{staticClass:"nav-left"},[e("v-uni-view",{staticClass:"nav-back",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.goBack.apply(void 0,arguments)}}},[e("uni-icons",{attrs:{type:"back",size:"20",color:"#000000"}})],1)],1),e("v-uni-text",{staticClass:"nav-title"},[t._v("打卡签到")])],1),t.isLoading?e("v-uni-view",{staticClass:"loading-container"},[e("v-uni-view",{staticClass:"loading-spinner"},[e("v-uni-view",{staticClass:"spinner-circle"})],1),e("v-uni-text",{staticClass:"loading-text"},[t._v("加载中...")])],1):t._e(),t.showCamera?e("v-uni-view",{staticClass:"camera-container"},[e("v-uni-camera",{staticStyle:{width:"100%",height:"calc(100vh - 200rpx)"},attrs:{id:"nativeCamera","device-position":"back",flash:t.flashMode},on:{error:function(n){arguments[0]=n=t.$handleEvent(n),t.handleCameraError.apply(void 0,arguments)}}}),e("v-uni-view",{staticClass:"camera-controls"},[e("v-uni-view",{staticClass:"control-item",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.toggleFlash.apply(void 0,arguments)}}},[e("uni-icons",{attrs:{type:t.getFlashIcon(),size:"26",color:"#FFFFFF"}}),e("v-uni-text",[t._v(t._s(t.getFlashText()))])],1),e("v-uni-view",{staticClass:"control-item photo-btn",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.takePhoto.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"photo-circle"})],1),e("v-uni-view",{staticClass:"control-item",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.closeCamera.apply(void 0,arguments)}}},[e("uni-icons",{attrs:{type:"closeempty",size:"26",color:"#FFFFFF"}}),e("v-uni-text",[t._v("取消")])],1)],1)],1):t._e(),t.showScanner?e("v-uni-view",{staticClass:"scanner-container"},[e("v-uni-camera",{staticClass:"scanner-camera",attrs:{mode:"scanCode","device-position":"back",flash:t.flashMode,"scan-area":[[.1,.1,.8,.8]],"auto-focus":!0},on:{scancode:function(n){arguments[0]=n=t.$handleEvent(n),t.onScanCode.apply(void 0,arguments)},error:function(n){arguments[0]=n=t.$handleEvent(n),t.handleCameraError.apply(void 0,arguments)}}},[e("v-uni-cover-view",{staticClass:"scan-frame"},[e("v-uni-cover-view",{staticClass:"frame-line top"}),e("v-uni-cover-view",{staticClass:"frame-line right"}),e("v-uni-cover-view",{staticClass:"frame-line bottom"}),e("v-uni-cover-view",{staticClass:"frame-line left"}),e("v-uni-cover-view",{staticClass:"corner-box left-top"}),e("v-uni-cover-view",{staticClass:"corner-box right-top"}),e("v-uni-cover-view",{staticClass:"corner-box left-bottom"}),e("v-uni-cover-view",{staticClass:"corner-box right-bottom"}),e("v-uni-cover-view",{staticClass:"scan-line"})],1),e("v-uni-cover-view",{staticClass:"scan-tips"},[e("v-uni-cover-view",{staticClass:"tips-text"},[t._v("将二维码放入框内，即可自动扫描")])],1),e("v-uni-cover-view",{staticClass:"scanner-controls"},[e("v-uni-cover-view",{staticClass:"control-item",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.toggleScannerFlash.apply(void 0,arguments)}}},[e("v-uni-cover-view",{staticClass:"control-icon"},["off"===t.flashMode?e("v-uni-cover-image",{attrs:{src:"/static/icons/flashlight.png"}}):e("v-uni-cover-image",{attrs:{src:"/static/icons/flashlight-off.png"}})],1),e("v-uni-cover-view",{staticClass:"control-text"},[t._v(t._s("off"===t.flashMode?"打开照明":"关闭照明"))])],1),e("v-uni-cover-view",{staticClass:"control-item",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.closeScanner.apply(void 0,arguments)}}},[e("v-uni-cover-view",{staticClass:"control-icon"},[e("v-uni-cover-image",{attrs:{src:"/static/icons/close.png"}})],1),e("v-uni-cover-view",{staticClass:"control-text"},[t._v("取消")])],1)],1)],1)],1):[e("v-uni-view",{staticClass:"info-section",class:{"in-range":t.isInRange}},[e("v-uni-view",{staticClass:"info-content"},[e("v-uni-view",{staticClass:"status-indicator",class:{"status-indicator--active":t.isInRange}},[e("v-uni-view",{staticClass:"indicator-dot"}),e("v-uni-text",{staticClass:"status-text"},[t._v(t._s(t.isInRange?"已进入打卡范围":"请靠近打卡点"))])],1),e("v-uni-view",{staticClass:"distance-info"},[e("v-uni-view",{staticClass:"distance-value"},[e("v-uni-text",{staticClass:"value"},[t._v(t._s(t.distanceText))])],1),t.currentRound?e("v-uni-view",{staticClass:"round-badge"},[e("uni-icons",{attrs:{type:"calendar",color:"#8F959E",size:"12"}}),e("v-uni-text",[t._v("轮次 "+t._s(t.currentRound.round))])],1):t._e()],1)],1)],1),e("v-uni-view",{staticClass:"map-section"},[e("v-uni-map",{staticClass:"checkin-map",attrs:{id:"checkInMap",latitude:t.currentLocation.latitude,longitude:t.currentLocation.longitude,markers:t.markers,circles:t.circles,scale:19,"show-location":!0,"enable-zoom":!0,"enable-scroll":!0,"enable-rotate":!0},on:{regionchange:function(n){arguments[0]=n=t.$handleEvent(n),t.onMapRegionChange.apply(void 0,arguments)},markertap:function(n){arguments[0]=n=t.$handleEvent(n),t.onMarkerTap.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"map-controls"},[e("v-uni-view",{staticClass:"control-btn",class:{"control-btn--active":t.isFollowMode},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.relocate.apply(void 0,arguments)}}},[e("uni-icons",{attrs:{type:"location",size:"20",color:t.isFollowMode?"#34C759":"#8F959E"}})],1)],1),e("v-uni-view",{staticClass:"location-accuracy"},[e("v-uni-view",{staticClass:"status-dot",style:{background:t.getAccuracyColor()}}),e("v-uni-text",{staticClass:"accuracy-text"},[t._v("GPS精度: "+t._s(t.currentLocation.accuracy?t.currentLocation.accuracy.toFixed(1):"0")+"米")])],1)],1)],1),e("v-uni-scroll-view",{staticClass:"content-wrapper",attrs:{"scroll-y":!0}},[e("v-uni-view",{staticClass:"info-card current-point"},[e("v-uni-view",{staticClass:"card-header"},[e("v-uni-view",{staticClass:"point-icon current"},[e("uni-icons",{attrs:{type:"location-filled",size:"16",color:"#FFFFFF"}})],1),e("v-uni-view",{staticClass:"point-info"},[e("v-uni-text",{staticClass:"point-title"},[t._v("当前点位")]),e("v-uni-text",{staticClass:"point-name"},[t._v(t._s(t.formattedCurrentPointName))])],1),t.isCurrentPointChecked?e("v-uni-view",{staticClass:"point-status checked"},[e("v-uni-text",{staticClass:"status-text"},[t._v("已打卡")])],1):e("v-uni-view",{staticClass:"point-status pending"},[e("v-uni-text",{staticClass:"status-text"},[t._v("未打卡")])],1)],1)],1),t.nextUnCheckedPoint?e("v-uni-view",{staticClass:"info-card next-point",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.goToNextPoint.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"card-header"},[e("v-uni-view",{staticClass:"point-icon next"},[e("uni-icons",{attrs:{type:"arrowright",size:"16",color:"#FFFFFF"}})],1),e("v-uni-view",{staticClass:"point-info"},[e("v-uni-text",{staticClass:"point-title"},[t._v("下个点位")]),e("v-uni-text",{staticClass:"point-name"},[t._v(t._s(t.formattedNextPointName))])],1),e("v-uni-view",{staticClass:"distance-badge"},[e("v-uni-text",{staticClass:"distance-text"},[t._v(t._s(t.getNextPointDistanceText()))])],1)],1),e("v-uni-view",{staticClass:"card-details"},[e("v-uni-text",{staticClass:"detail-text"},[t._v("🚶‍♂️ 点击导航到下个点位"+t._s(t.nextUnCheckedPoint.qrcode_enabled?" · 🔍 需要扫码打卡":""))])],1)],1):t._e(),t.isRoundCompleted&&t.pointInfo?e("v-uni-view",{staticClass:"info-card completed"},[e("v-uni-view",{staticClass:"card-header"},[e("v-uni-view",{staticClass:"point-icon completed"},[e("uni-icons",{attrs:{type:"checkbox-filled",size:"16",color:"#FFFFFF"}})],1),e("v-uni-view",{staticClass:"point-info"},[e("v-uni-text",{staticClass:"point-title"},[t._v("🎉 恭喜")]),e("v-uni-text",{staticClass:"point-name"},[t._v("本轮次已完成")])],1),e("v-uni-view",{staticClass:"completed-badge"},[e("v-uni-text",{staticClass:"status-text"},[t._v("完成打卡")])],1)],1)],1):t._e(),t.isLastUnCheckedPoint&&!t.isRoundCompleted&&t.pointInfo?e("v-uni-view",{staticClass:"info-card last-point"},[e("v-uni-view",{staticClass:"card-header"},[e("v-uni-view",{staticClass:"point-icon last"},[e("uni-icons",{attrs:{type:"flag",size:"16",color:"#FFFFFF"}})],1),e("v-uni-view",{staticClass:"point-info"},[e("v-uni-text",{staticClass:"point-title"},[t._v("最后点位")]),e("v-uni-text",{staticClass:"point-name"},[t._v("完成后即可结束巡检")])],1),e("v-uni-view",{staticClass:"last-badge"},[e("v-uni-text",{staticClass:"status-text"},[t._v("最终点位")])],1)],1),e("v-uni-view",{staticClass:"card-details"},[e("v-uni-text",{staticClass:"detail-text"},[t._v("🎯 这是本轮次的最后一个点位，完成打卡后即可结束巡检")])],1)],1):t._e(),t.isRoundValid&&(t.isInRange||t.pointInfo.qrcode_enabled)?t._e():e("v-uni-view",{staticClass:"warning-area"},[e("v-uni-view",{staticClass:"warning-icon"},[e("uni-icons",{attrs:{type:"info",size:"24",color:"#FF9500"}})],1),t.isRoundValid?e("v-uni-text",{staticClass:"warning-text"},[t._v("您当前不在打卡范围内，请靠近点位再进行打卡")]):e("v-uni-text",{staticClass:"warning-text"},[t._v(t._s(t.roundErrorMessage))])],1)],1),e("v-uni-view",{staticClass:"action-section"},[e("v-uni-view",{staticClass:"checkin-btn-container"},[t.pointInfo&&!0===t.pointInfo.qrcode_enabled?e("v-uni-button",{staticClass:"btn-checkin qrcode-button",class:{"btn-checkin--active":t.qrcodeVerified||t.isInRange&&t.isRoundValid,"btn-disabled":t.loading||!t.isRoundValid||t.isCurrentPointChecked||t.isAutoJumping},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.handleQRCodeClick.apply(void 0,arguments)}}},[e("uni-icons",{attrs:{type:"scan",size:"20",color:"#FFFFFF"}}),e("v-uni-text",[t._v(t._s(t.isAutoJumping?"跳转中...":"扫码打卡"))])],1):t._e(),t.pointInfo&&!1!==t.pointInfo.qrcode_enabled?t._e():e("v-uni-button",{staticClass:"btn-checkin gps-button",class:{"btn-checkin--active":t.isInRange&&t.isRoundValid,"btn-disabled":t.loading||!t.isRoundValid||t.isCurrentPointChecked||!t.isInRange||t.isAutoJumping},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.handleGPSClick.apply(void 0,arguments)}}},[e("uni-icons",{attrs:{type:"paperplane",size:"20",color:"#FFFFFF"}}),e("v-uni-text",[t._v(t._s(t.isAutoJumping?"跳转中...":"GPS打卡"))])],1)],1)],1)],t.showDistanceMessage?e("v-uni-view",{staticClass:"custom-distance-message"},[e("v-uni-text",{staticClass:"message-text"},[t._v(t._s(t.distancePopupMessage))])],1):t._e()],2)},o=[]},f2cd:function(t,n,e){"use strict";e.r(n);var i=e("cd20"),a=e("88de");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("ad15");var r=e("828b"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"04585de4",null,!1,i["a"],void 0);n["default"]=s.exports},fed5:function(t,n,e){var i=e("5fcb");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=e("967d").default;a("bf1fde18",i,!0,{sourceMap:!1,shadowMode:!1})}}]);
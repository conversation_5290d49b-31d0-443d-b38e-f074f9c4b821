(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/feedback/list"],{

/***/ 75:
/*!**********************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Ffeedback%2Flist"} ***!
  \**********************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _list = _interopRequireDefault(__webpack_require__(/*! ./pages/feedback/list.vue */ 76));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_list.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 76:
/*!***************************************!*\
  !*** D:/Xwzc/pages/feedback/list.vue ***!
  \***************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _list_vue_vue_type_template_id_2980693f_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./list.vue?vue&type=template&id=2980693f&scoped=true& */ 77);
/* harmony import */ var _list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./list.vue?vue&type=script&lang=js& */ 79);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _list_vue_vue_type_style_index_0_id_2980693f_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./list.vue?vue&type=style&index=0&id=2980693f&scoped=true&lang=css& */ 81);
/* harmony import */ var _list_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./list.vue?vue&type=style&index=1&lang=scss& */ 83);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs






/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_4__["default"])(
  _list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _list_vue_vue_type_template_id_2980693f_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _list_vue_vue_type_template_id_2980693f_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "2980693f",
  null,
  false,
  _list_vue_vue_type_template_id_2980693f_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/feedback/list.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 77:
/*!**********************************************************************************!*\
  !*** D:/Xwzc/pages/feedback/list.vue?vue&type=template&id=2980693f&scoped=true& ***!
  \**********************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_template_id_2980693f_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=template&id=2980693f&scoped=true& */ 78);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_template_id_2980693f_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_template_id_2980693f_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_template_id_2980693f_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_template_id_2980693f_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 78:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/feedback/list.vue?vue&type=template&id=2980693f&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniCollapse: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-collapse/components/uni-collapse/uni-collapse */ "uni_modules/uni-collapse/components/uni-collapse/uni-collapse").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue */ 537))
    },
    uniCollapseItem: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item */ "uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue */ 544))
    },
    uniEasyinput: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput */ "uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue */ 551))
    },
    uniDatetimePicker: function () {
      return Promise.all(/*! import() | uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue */ 558))
    },
    uniTable: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-table/components/uni-table/uni-table */ "uni_modules/uni-table/components/uni-table/uni-table").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-table/components/uni-table/uni-table.vue */ 570))
    },
    uniTr: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-table/components/uni-tr/uni-tr */ "uni_modules/uni-table/components/uni-tr/uni-tr").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-table/components/uni-tr/uni-tr.vue */ 577))
    },
    uniTh: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-table/components/uni-th/uni-th */ "uni_modules/uni-table/components/uni-th/uni-th").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-table/components/uni-th/uni-th.vue */ 584))
    },
    uniTd: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-table/components/uni-td/uni-td */ "uni_modules/uni-table/components/uni-td/uni-td").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-table/components/uni-td/uni-td.vue */ 591))
    },
    uniPagination: function () {
      return Promise.all(/*! import() | uni_modules/uni-pagination/components/uni-pagination/uni-pagination */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-pagination/components/uni-pagination/uni-pagination")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-pagination/components/uni-pagination/uni-pagination.vue */ 598))
    },
    uniLoadMore: function () {
      return Promise.all(/*! import() | uni_modules/uni-load-more/components/uni-load-more/uni-load-more */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-load-more/components/uni-load-more/uni-load-more")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue */ 497))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 483))
    },
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 490))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.getStatusText(_vm.searchParams.status) || "工作流状态"
  var m1 = _vm.getUrgencyText(_vm.searchParams.urgency) || "紧急程度"
  var m2 = _vm.getProjectText(_vm.searchParams.project) || "找茬项目"
  var m3 = _vm.getResponsibleText(_vm.searchParams.responsible) || "责任人"
  var g0 = _vm.feedbackList.length
  var l0 =
    g0 > 0
      ? _vm.__map(_vm.feedbackList, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var g1 = item.description && item.description.length > 45
          var g2 = item.images && item.images.length > 0
          var g3 = g2 ? item.images.length : null
          var g4 = g2 ? item.images.length : null
          var g5 = g2 && g4 > 1 ? item.images.length : null
          var g6 = g2 && g4 > 1 && g5 > 2 ? item.images.length : null
          var m4 = _vm.formatTime(item.createTime, item)
          var m5 =
            item.responsibleInfo && item.responsibleInfo.assignedTime
              ? _vm.formatTime(item.responsibleInfo.assignedTime, item)
              : null
          var m6 =
            item.responsibleInfo &&
            item.responsibleInfo.assignedTime &&
            item.createTime
              ? _vm.formatTime(item.createTime, item)
              : null
          var m7 = _vm.getReasonDisplay(item)
          var m8 =
            m7 !== "-" ? _vm.formatReasonText(_vm.getReasonDisplay(item)) : null
          var g7 = _vm.hasOperationPermission
            ? _vm.hasEditPermission && item.availableActions.includes("edit")
            : null
          var g8 = _vm.hasOperationPermission
            ? item.availableActions.includes("submit_completion")
            : null
          return {
            $orig: $orig,
            g1: g1,
            g2: g2,
            g3: g3,
            g4: g4,
            g5: g5,
            g6: g6,
            m4: m4,
            m5: m5,
            m6: m6,
            m7: m7,
            m8: m8,
            g7: g7,
            g8: g8,
          }
        })
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        m2: m2,
        m3: m3,
        g0: g0,
        l0: l0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 79:
/*!****************************************************************!*\
  !*** D:/Xwzc/pages/feedback/list.vue?vue&type=script&lang=js& ***!
  \****************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js& */ 80);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 80:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/feedback/list.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uniCloud, uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _cache = __webpack_require__(/*! @/utils/cache.js */ 45);
var _methods;
var PEmptyState = function PEmptyState() {
  __webpack_require__.e(/*! require.ensure | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then((function () {
    return resolve(__webpack_require__(/*! @/components/p-empty-state/p-empty-state.vue */ 483));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
// 添加防抖函数作为外部单例
var debounce = function debounce(fn) {
  var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 300;
  var timer = null;
  return function () {
    var _this = this;
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    if (timer) clearTimeout(timer);
    timer = setTimeout(function () {
      fn.apply(_this, args);
    }, delay);
  };
};
var db = uniCloud.database();
var dbCmd = db.command;
// 表查询配置
var dbOrderBy = 'createTime desc'; // 按创建时间降序排列
var dbSearchFields = []; // 模糊搜索字段，支持模糊搜索的字段列表
// 分页配置
var pageSize = 10;
var pageCurrent = 1;
var orderByMapping = {
  "ascending": "asc",
  "descending": "desc"
};
var _default = {
  components: {
    PEmptyState: PEmptyState
  },
  data: function data() {
    // 初始化本地分页数据，与udb分开管理
    var localPagination = {
      size: pageSize,
      current: pageCurrent,
      count: 0
    };
    return {
      responsibleOptions: [],
      responsibleMap: {},
      createDateRange: [],
      searchParams: {
        keyword: '',
        project: '',
        responsible: '',
        status: '',
        urgency: ''
      },
      projectOptions: [{
        text: '全部',
        value: ''
      }, {
        text: '安全找茬',
        value: '安全找茬'
      }, {
        text: '设备找茬',
        value: '设备找茬'
      }, {
        text: '其他找茬',
        value: '其他找茬'
      }],
      isLoading: false,
      isTokenValid: true,
      userRoles: [],
      // 用户角色列表

      statusOptions: [],
      urgencyOptions: [],
      feedbackList: [],
      totalCount: 0,
      currentPage: 1,
      pageSize: 20,
      currentPageStart: 0,
      searchTimer: null,
      hasInitialized: false,
      // 添加初始化标记
      lastRefreshTime: 0,
      // 上次刷新时间
      isPageVisible: true,
      // 页面是否可见
      needsRefreshOnShow: false // 是否需要在页面显示时刷新
    };
  },

  computed: {
    // 判断是否有权限查看操作按钮 - 只有特定角色才能看到
    hasOperationPermission: function hasOperationPermission() {
      if (!this.isTokenValid) return false;

      // 有权限的角色：负责人、主管、厂长、副厂长、管理员
      var authorizedRoles = ['responsible', 'supervisor', 'GM', 'PM', 'admin', 'manager'];
      return this.userRoles.some(function (role) {
        return authorizedRoles.includes(role);
      });
    },
    // 编辑权限：只有管理员可以编辑
    hasEditPermission: function hasEditPermission() {
      if (!this.isTokenValid) return false;
      return this.userRoles.some(function (role) {
        return ['admin', 'manager'].includes(role);
      });
    },
    // 删除权限：管理员和厂长可以删除
    hasDeletePermission: function hasDeletePermission() {
      if (!this.isTokenValid) return false;
      return this.userRoles.some(function (role) {
        return ['admin', 'manager', 'GM'].includes(role);
      });
    }
  },
  created: function created() {
    var _this2 = this;
    // 页面创建时立即设置加载状态，确保页面显示正确
    this.isLoading = true;

    // 监听角标管理器的跨设备更新事件
    uni.$on('cross-device-update-detected', function (data) {
      if (data.silent) {
        // 智能判断是否需要刷新
        var shouldRefresh = _this2.shouldRefreshOnCrossDeviceUpdate(data);
        if (shouldRefresh) {
          console.log('反馈列表页面收到跨设备更新通知，静默刷新数据');
          // 静默刷新数据
          _this2.silentRefresh();
        }
      }
    });
  },
  onLoad: function onLoad() {
    var _this3 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              // 确保加载状态正确
              _this3.isLoading = true;
              _this3.hasInitialized = false;
              _context.prev = 2;
              // 检查登录状态，设置token有效性
              _this3.checkAndSetTokenStatus();
              _context.next = 6;
              return _this3.initializeWorkflowOptions();
            case 6:
              _context.next = 8;
              return _this3.loadResponsibleMap();
            case 8:
              _context.next = 10;
              return _this3.loadFeedbackList();
            case 10:
              // 添加请求拦截器
              _this3.setupRequestInterceptor();
              // 监听token过期事件
              _this3.setupTokenEventListeners();
              // 监听新反馈提交事件
              _this3.setupFeedbackEventListeners();
              _context.next = 20;
              break;
            case 15:
              _context.prev = 15;
              _context.t0 = _context["catch"](2);
              uni.showToast({
                title: '页面加载失败',
                icon: 'none'
              });
              // 即使加载失败也要标记为已初始化，避免一直显示加载状态
              _this3.hasInitialized = true;
              _this3.isLoading = false;
            case 20:
            case "end":
              return _context.stop();
          }
        }
      }, _callee, null, [[2, 15]]);
    }))();
  },
  onReady: function onReady() {
    // 移除原有的udb.loadData，使用新的API
  },
  onPullDownRefresh: function onPullDownRefresh() {
    var _this4 = this;
    this.loadFeedbackList().finally(function () {
      uni.stopPullDownRefresh();
      _this4.hasInitialized = true; // 确保下拉刷新后也标记为已初始化
    });
  },
  onShow: function onShow() {
    // 标记页面为可见状态
    this.isPageVisible = true;

    // 每次显示页面时检查token状态
    var oldTokenStatus = this.isTokenValid;
    this.checkAndSetTokenStatus();

    // 如果token状态发生变化，重新加载数据
    if (oldTokenStatus !== this.isTokenValid) {
      // 重新加载负责人数据（根据登录状态决定是否加载）
      this.loadResponsibleMap();
      this.loadFeedbackList();
      this.needsRefreshOnShow = false;
      return;
    }

    // 如果有待处理的刷新请求，立即执行
    if (this.needsRefreshOnShow) {
      this.loadFeedbackList();
      this.lastRefreshTime = Date.now();
      this.needsRefreshOnShow = false;
      return;
    }

    // 智能刷新策略：只在必要时自动刷新
    var now = Date.now();
    var lastRefresh = this.lastRefreshTime || 0;
    var timeSinceLastRefresh = now - lastRefresh;

    // 自动刷新条件（优化后）：
    // 1. 初次显示页面
    // 2. 距离上次刷新超过5分钟（避免频繁刷新）
    if (!this.hasInitialized) {
      // 初次显示，需要加载数据
      this.loadFeedbackList();
      this.lastRefreshTime = now;
    } else if (timeSinceLastRefresh > 5 * 60 * 1000) {
      // 超过5分钟，静默刷新（不显示加载状态）
      this.silentRefresh();
    }
  },
  onHide: function onHide() {
    // 标记页面为不可见状态
    this.isPageVisible = false;
  },
  onUnload: function onUnload() {
    // 移除事件监听
    this.removeTokenEventListeners();
    this.removeFeedbackEventListeners();
    // 移除跨设备更新事件监听
    uni.$off('cross-device-update-detected');
  },
  methods: (_methods = {
    // ===== 原有方法 =====
    // 统一的错误处理函数
    handleError: function handleError(message, error) {
      // 向用户显示友好提示
      uni.showToast({
        title: message,
        icon: 'none'
      });
    },
    // 静默刷新 - 在后台更新数据，不影响用户体验
    silentRefresh: function silentRefresh() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return _this5.loadFeedbackList(true);
              case 3:
                _context2.next = 8;
                break;
              case 5:
                _context2.prev = 5;
                _context2.t0 = _context2["catch"](0);
                console.error('❌ 静默刷新失败:', _context2.t0);
                // 静默刷新失败不显示错误提示，避免打扰用户
              case 8:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 5]]);
      }))();
    },
    // 加载负责人映射 - 使用缓存系统优化性能
    loadResponsibleMap: function loadResponsibleMap() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var responsibleList;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                if (_this6.isTokenValid) {
                  _context4.next = 4;
                  break;
                }
                // 设置默认的负责人选项
                _this6.responsibleOptions = [{
                  text: '全部',
                  value: ''
                }];
                _this6.responsibleMap = {};
                return _context4.abrupt("return");
              case 4:
                _context4.prev = 4;
                // 先尝试从缓存获取负责人映射
                _this6.responsibleMap = _cache.cacheManager.getResponsibleMap();

                // 使用缓存管理器获取负责人列表，如果缓存未命中则从服务器获取
                _context4.next = 8;
                return _cache.cacheManager.getResponsibleList( /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
                  var res, _res$result;
                  return _regenerator.default.wrap(function _callee3$(_context3) {
                    while (1) {
                      switch (_context3.prev = _context3.next) {
                        case 0:
                          _context3.next = 2;
                          return uniCloud.callFunction({
                            name: 'feedback-list',
                            data: {
                              action: 'getResponsibleUsers'
                            }
                          });
                        case 2:
                          res = _context3.sent;
                          if (!(!res || !res.result || res.result.code !== 0)) {
                            _context3.next = 5;
                            break;
                          }
                          throw new Error(((_res$result = res.result) === null || _res$result === void 0 ? void 0 : _res$result.message) || '获取责任人数据失败');
                        case 5:
                          return _context3.abrupt("return", res.result.data || []);
                        case 6:
                        case "end":
                          return _context3.stop();
                      }
                    }
                  }, _callee3);
                })));
              case 8:
                responsibleList = _context4.sent;
                // 构建负责人选项
                _this6.responsibleOptions = [{
                  text: '全部',
                  value: ''
                }].concat((0, _toConsumableArray2.default)(responsibleList.map(function (user) {
                  return {
                    value: user._id,
                    text: user.nickname || user.username || '-'
                  };
                })));

                // 更新负责人映射
                _this6.responsibleMap = responsibleList.reduce(function (map, user) {
                  map[user._id] = user.nickname || user.username || '-';
                  return map;
                }, {});
                _context4.next = 18;
                break;
              case 13:
                _context4.prev = 13;
                _context4.t0 = _context4["catch"](4);
                // 只在登录状态下才显示错误提示
                if (_this6.isTokenValid) {
                  _this6.handleError('获取责任人数据失败', _context4.t0);
                  console.error('❌ 负责人数据加载失败:', _context4.t0);
                }

                // 设置默认选项，避免界面异常
                _this6.responsibleOptions = [{
                  text: '全部',
                  value: ''
                }];
                _this6.responsibleMap = {};
              case 18:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[4, 13]]);
      }))();
    },
    getResponsibleName: function getResponsibleName(responsibleId) {
      if (!responsibleId) return '-';
      return this.responsibleMap[responsibleId] || '-';
    },
    previewImage: function previewImage(images, index) {
      if (!images || !Array.isArray(images) || images.length === 0) {
        this.handleError('无图片可预览');
        return;
      }

      // 微信小程序特殊处理

      try {
        uni.previewImage({
          urls: images,
          current: index || 0,
          fail: function fail(err) {
            // 预览失败不显示错误提示，因为这可能是微信小程序的限制导致
          }
        });
      } catch (error) {
        // 预览图片出错，静默处理
      }

      // 非微信小程序处理
    },
    handleImageError: function handleImageError(event) {
      event.target.src = '/static/empty/default-image.png';
    },
    // 分页变化
    onPageChange: function onPageChange(e) {
      this.currentPage = e.current;
      this.loadFeedbackList();
    },
    // 原搜索方法更新为调用统一方法
    // 项目选择变化
    onProjectChange: function onProjectChange(value) {
      this.searchParams.project = value;
      this.currentPage = 1;
      this.loadFeedbackList();
    },
    // 负责人选择变化
    onResponsibleChange: function onResponsibleChange(value) {
      this.searchParams.responsible = value;
      this.currentPage = 1;
      this.loadFeedbackList();
    },
    // 创建日期变化
    onCreateDateChange: function onCreateDateChange(e) {
      this.createDateRange = e;
      this.currentPage = 1;
      this.loadFeedbackList();
    },
    // 验证响应数据
    validateResponse: function validateResponse(res) {
      if (!res) return false;
      if (!res.result) return false;
      if (!res.result.data) return false;
      return true;
    },
    // 检查并设置token状态
    checkAndSetTokenStatus: function checkAndSetTokenStatus() {
      var token = uni.getStorageSync('uni_id_token');
      if (!token) {
        this.isTokenValid = false;
        return;
      }
      var tokenExpired = uni.getStorageSync('uni_id_token_expired');
      if (tokenExpired && tokenExpired < Date.now()) {
        this.isTokenValid = false;
        // 清除过期的token
        this.handleTokenInvalid();
      } else {
        this.isTokenValid = true;
      }
    },
    // 检查token状态
    checkTokenStatus: function checkTokenStatus() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var token, tokenExpired;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                token = uni.getStorageSync('uni_id_token');
                if (token) {
                  _context5.next = 5;
                  break;
                }
                _this7.handleTokenInvalid();
                return _context5.abrupt("return");
              case 5:
                tokenExpired = uni.getStorageSync('uni_id_token_expired');
                if (tokenExpired < Date.now()) {
                  _this7.handleTokenInvalid();
                }
                _context5.next = 12;
                break;
              case 9:
                _context5.prev = 9;
                _context5.t0 = _context5["catch"](0);
                _this7.handleError('验证登录状态失败', _context5.t0);
              case 12:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 9]]);
      }))();
    },
    // 处理token失效 - 清除用户相关缓存
    handleTokenInvalid: function handleTokenInvalid() {
      this.isTokenValid = false;
      // 清除登录信息
      uni.removeStorageSync('uni_id_token');
      uni.removeStorageSync('uni_id_token_expired');
      uni.removeStorageSync('uni-id-pages-userInfo');

      // 使用缓存管理器清除用户相关缓存
      _cache.cacheManager.clearUserRelatedCache();

      // 重新加载数据
      this.loadFeedbackList();
    },
    // 设置请求拦截器
    setupRequestInterceptor: function setupRequestInterceptor() {
      var _this8 = this;
      // 添加请求拦截器
      var db = uniCloud.database();
      db.interceptorAdd('callFunction', {
        invoke: function invoke(options) {
          // 请求前的处理
        },
        success: function success(result) {
          // 处理token失效的情况
          if (result.result && result.result.code === 'TOKEN_INVALID') {
            _this8.handleTokenInvalid();
          }
          return result;
        },
        fail: function fail(err) {
          return err;
        },
        complete: function complete(res) {
          return res;
        }
      });
    },
    // 设置token事件监听
    setupTokenEventListeners: function setupTokenEventListeners() {
      // 监听全局token过期事件
      uni.$on('token-expired', this.handleGlobalTokenExpired);
      uni.$on('token-invalid', this.handleGlobalTokenExpired);
    },
    // 移除token事件监听
    removeTokenEventListeners: function removeTokenEventListeners() {
      uni.$off('token-expired', this.handleGlobalTokenExpired);
      uni.$off('token-invalid', this.handleGlobalTokenExpired);
    },
    // 处理全局token过期事件 - 清除用户相关缓存
    handleGlobalTokenExpired: function handleGlobalTokenExpired() {
      this.isTokenValid = false;
      // 清除登录信息
      uni.removeStorageSync('uni_id_token');
      uni.removeStorageSync('uni_id_token_expired');
      uni.removeStorageSync('uni-id-pages-userInfo');

      // 使用缓存管理器清除用户相关缓存
      _cache.cacheManager.clearUserRelatedCache();

      // 重新加载负责人数据（会因为未登录而跳过）
      this.loadResponsibleMap();

      // 重新加载数据
      this.loadFeedbackList();

      // 强制更新页面，让权限相关的按钮重新渲染
      this.$forceUpdate();
    },
    // 设置反馈事件监听
    setupFeedbackEventListeners: function setupFeedbackEventListeners() {
      // 监听新反馈提交事件
      uni.$on('feedback-submitted', this.handleFeedbackSubmitted);
      // 监听反馈数据更新事件
      uni.$on('feedback-updated', this.handleFeedbackUpdated);
      // 监听任务完成事件
      uni.$on('task-completed', this.handleTaskCompleted);
      // 监听任务状态变化事件
      uni.$on('task-status-changed', this.handleTaskStatusChanged);
    },
    // 移除反馈事件监听
    removeFeedbackEventListeners: function removeFeedbackEventListeners() {
      uni.$off('feedback-submitted', this.handleFeedbackSubmitted);
      uni.$off('feedback-updated', this.handleFeedbackUpdated);
      uni.$off('task-completed', this.handleTaskCompleted);
      uni.$off('task-status-changed', this.handleTaskStatusChanged);
    },
    // 处理新反馈提交事件
    handleFeedbackSubmitted: function handleFeedbackSubmitted(data) {
      // 重置到第一页并刷新数据 - 无论页面是否可见都要刷新
      this.currentPage = 1;
      this.loadFeedbackList();
      this.lastRefreshTime = Date.now(); // 更新刷新时间

      // 清除待刷新标记，因为已经刷新了
      this.needsRefreshOnShow = false;

      // 显示提示（只在页面可见时显示，避免干扰用户）
      if (this.isPageVisible) {
        uni.showToast({
          title: '列表已更新',
          icon: 'success',
          duration: 1500
        });
      }
    },
    // 处理反馈数据更新事件
    handleFeedbackUpdated: function handleFeedbackUpdated(data) {
      // 智能刷新：如果是当前页面可见，立即刷新；否则标记需要刷新
      if (this.isPageVisible) {
        this.loadFeedbackList();
        this.lastRefreshTime = Date.now();
      } else {
        // 页面不可见时，标记需要刷新，等页面显示时再刷新
        this.needsRefreshOnShow = true;
      }
    },
    // 处理任务完成事件
    handleTaskCompleted: function handleTaskCompleted(data) {
      // 刷新当前页数据
      this.loadFeedbackList();
      this.lastRefreshTime = Date.now();

      // 显示提示
      uni.showToast({
        title: '任务状态已更新',
        icon: 'success',
        duration: 1500
      });
    },
    // 处理任务状态变化事件
    handleTaskStatusChanged: function handleTaskStatusChanged(data) {
      // 智能刷新策略
      if (this.isPageVisible) {
        this.silentRefresh();
      } else {
        this.needsRefreshOnShow = true;
      }
    },
    // ===== 新工作流相关方法 =====
    // 初始化工作流选项 - 使用缓存系统优化
    initializeWorkflowOptions: function initializeWorkflowOptions() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                // 从缓存获取状态选项，提升页面加载速度
                _this9.statusOptions = _cache.cacheManager.getStatusOptions();

                // 从缓存获取紧急程度选项
                _this9.urgencyOptions = _cache.cacheManager.getUrgencyOptions();

                // 从缓存获取项目选项
                _context6.next = 4;
                return _cache.cacheManager.getProjectOptions();
              case 4:
                _this9.projectOptions = _context6.sent;
              case 5:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6);
      }))();
    },
    /**
     * 加载问题列表 - 新工作流系统的核心数据获取方法
     * 
     * 功能特点：
     * - 支持多维度搜索筛选（项目、状态、关键词、时间范围、负责人）
     * - 自动获取用户角色信息，用于权限控制
     * - 集成分页功能，提升大数据量场景的性能
     * - 统一的错误处理和用户友好提示
     * 
     * 数据流：前端搜索参数 → feedback-list云函数 → 数据库查询 → 增强处理 → 前端显示
     */
    loadFeedbackList: function loadFeedbackList() {
      var _arguments = arguments,
        _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var silent, params, startDateStr, endDateStr, startParts, endParts, startDate, endDate, res, _res$result$data, list, pagination, userInfo;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                silent = _arguments.length > 0 && _arguments[0] !== undefined ? _arguments[0] : false;
                if (!silent) {
                  _this10.isLoading = true;
                }
                _context7.prev = 2;
                params = {
                  action: 'getList',
                  pageSize: _this10.pageSize,
                  pageNum: _this10.currentPage,
                  project: _this10.searchParams.project || '',
                  status: _this10.searchParams.status || '',
                  keyword: _this10.searchParams.keyword || '',
                  urgency: _this10.searchParams.urgency || '',
                  responsible: _this10.searchParams.responsible || ''
                }; // 添加日期范围
                if (_this10.createDateRange && _this10.createDateRange.length === 2) {
                  startDateStr = _this10.createDateRange[0];
                  endDateStr = _this10.createDateRange[1];
                  startParts = startDateStr.split('-');
                  endParts = endDateStr.split('-'); // 构建本地时间的开始和结束时间戳
                  startDate = new Date(parseInt(startParts[0]), parseInt(startParts[1]) - 1, parseInt(startParts[2]), 0, 0, 0, 0);
                  endDate = new Date(parseInt(endParts[0]), parseInt(endParts[1]) - 1, parseInt(endParts[2]), 23, 59, 59, 999);
                  params.dateRange = {
                    start: startDate.getTime(),
                    end: endDate.getTime()
                  };
                }
                _context7.next = 7;
                return uniCloud.callFunction({
                  name: 'feedback-list',
                  data: params
                });
              case 7:
                res = _context7.sent;
                if (!(res.result && res.result.code === 0)) {
                  _context7.next = 16;
                  break;
                }
                _res$result$data = res.result.data, list = _res$result$data.list, pagination = _res$result$data.pagination, userInfo = _res$result$data.userInfo;
                _this10.feedbackList = list || [];
                _this10.totalCount = pagination ? pagination.total : 0;
                _this10.currentPageStart = pagination ? (pagination.pageNum - 1) * pagination.pageSize : 0;

                // 更新用户角色信息
                if (userInfo && userInfo.roles) {
                  _this10.userRoles = userInfo.roles;
                } else {
                  _this10.userRoles = [];
                }
                _context7.next = 17;
                break;
              case 16:
                throw new Error(res.result ? res.result.message : '未知错误');
              case 17:
                _context7.next = 24;
                break;
              case 19:
                _context7.prev = 19;
                _context7.t0 = _context7["catch"](2);
                _this10.feedbackList = [];
                _this10.totalCount = 0;
                uni.showToast({
                  title: '加载数据失败: ' + (_context7.t0.message || _context7.t0),
                  icon: 'none',
                  duration: 3000
                });
              case 24:
                _context7.prev = 24;
                if (!silent) {
                  _this10.isLoading = false;
                }
                _this10.hasInitialized = true; // 标记为已初始化
                _this10.lastRefreshTime = Date.now(); // 更新刷新时间
                return _context7.finish(24);
              case 29:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[2, 19, 24, 29]]);
      }))();
    },
    // 工作流状态变化 - 新工作流系统的状态筛选
    onStatusChange: function onStatusChange(value) {
      this.searchParams.status = value;
      this.currentPage = 1;
      this.loadFeedbackList();
    },
    // 紧急程度变化
    onUrgencyChange: function onUrgencyChange(value) {
      this.searchParams.urgency = value;
      this.currentPage = 1;
      this.loadFeedbackList();
    },
    // 关键词搜索
    onKeywordSearch: function onKeywordSearch() {
      var _this11 = this;
      // 防抖处理，避免频繁请求
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(function () {
        _this11.currentPage = 1;
        _this11.loadFeedbackList();
      }, 300);
    },
    // 查看详情
    viewDetail: function viewDetail(item) {
      uni.navigateTo({
        url: "/pages/feedback_pkg/examine?id=".concat(item._id, "&readonly=true")
      });
    },
    // 编辑项目
    editItem: function editItem(item) {
      uni.navigateTo({
        url: "/pages/feedback_pkg/edit?id=".concat(item._id)
      });
    },
    // 负责人提交工作完成
    submitCompletion: function submitCompletion(item) {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                // 直接跳转到我的任务页面进行详细的完成操作
                uni.navigateTo({
                  url: "/pages/ucenter_pkg/complete-task?id=".concat(item._id),
                  fail: function fail(err) {
                    uni.showToast({
                      title: '跳转失败',
                      icon: 'none'
                    });
                  }
                });
              case 1:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8);
      }))();
    },
    // 删除项目
    deleteItem: function deleteItem(item) {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                uni.showModal({
                  title: '确认删除',
                  content: "\u786E\u5B9A\u8981\u5220\u9664\"".concat(item.name, "\"\u7684\u95EE\u9898\u53CD\u9988\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u6062\u590D\uFF01"),
                  confirmText: '删除',
                  confirmColor: '#ff4444',
                  success: function () {
                    var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9(res) {
                      return _regenerator.default.wrap(function _callee9$(_context9) {
                        while (1) {
                          switch (_context9.prev = _context9.next) {
                            case 0:
                              if (!res.confirm) {
                                _context9.next = 3;
                                break;
                              }
                              _context9.next = 3;
                              return _this12.performDelete(item);
                            case 3:
                            case "end":
                              return _context9.stop();
                          }
                        }
                      }, _callee9);
                    }));
                    function success(_x) {
                      return _success.apply(this, arguments);
                    }
                    return success;
                  }()
                });
              case 1:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10);
      }))();
    },
    // 执行删除操作
    performDelete: function performDelete(item) {
      var _this13 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        var res;
        return _regenerator.default.wrap(function _callee11$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                uni.showLoading({
                  title: '删除中...',
                  mask: true
                });
                _context11.prev = 1;
                _context11.next = 4;
                return uniCloud.callFunction({
                  name: 'feedback-workflow',
                  data: {
                    action: 'delete',
                    id: item._id
                  }
                });
              case 4:
                res = _context11.sent;
                if (!(res.result.code === 0)) {
                  _context11.next = 12;
                  break;
                }
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });

                // 发送全局事件通知其他页面数据已更新
                uni.$emit('feedback-updated', {
                  action: 'delete',
                  id: item._id,
                  timestamp: Date.now()
                });

                // 重新加载数据
                _context11.next = 10;
                return _this13.loadFeedbackList();
              case 10:
                _context11.next = 13;
                break;
              case 12:
                throw new Error(res.result.message || '删除失败');
              case 13:
                _context11.next = 18;
                break;
              case 15:
                _context11.prev = 15;
                _context11.t0 = _context11["catch"](1);
                uni.showToast({
                  title: _context11.t0.message || '删除失败，请重试',
                  icon: 'none'
                });
              case 18:
                _context11.prev = 18;
                uni.hideLoading();
                return _context11.finish(18);
              case 21:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee11, null, [[1, 15, 18, 21]]);
      }))();
    }
  }, (0, _defineProperty2.default)(_methods, "onPageChange", function onPageChange(e) {
    this.currentPage = e.current;
    this.loadFeedbackList();
  }), (0, _defineProperty2.default)(_methods, "formatTime", function formatTime(timestamp) {
    var item = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    if (!timestamp) return '-';

    // 使用后端提供的格式化时间（北京时间）
    if (item && item.createTimeFormatted) {
      return item.createTimeFormatted;
    }

    // 后备方案：简单格式化
    var date = new Date(timestamp);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }), (0, _defineProperty2.default)(_methods, "toggleExpand", function toggleExpand(index) {
    this.$set(this.feedbackList[index], 'isExpanded', !this.feedbackList[index].isExpanded);
  }), (0, _defineProperty2.default)(_methods, "showStatusPicker", function showStatusPicker() {
    this.$refs.statusPopup.open();
  }), (0, _defineProperty2.default)(_methods, "closeStatusPicker", function closeStatusPicker() {
    this.$refs.statusPopup.close();
  }), (0, _defineProperty2.default)(_methods, "selectStatus", function selectStatus(value) {
    this.searchParams.status = value;
    this.currentPage = 1;
    this.loadFeedbackList();
    this.closeStatusPicker();
  }), (0, _defineProperty2.default)(_methods, "showUrgencyPicker", function showUrgencyPicker() {
    this.$refs.urgencyPopup.open();
  }), (0, _defineProperty2.default)(_methods, "closeUrgencyPicker", function closeUrgencyPicker() {
    this.$refs.urgencyPopup.close();
  }), (0, _defineProperty2.default)(_methods, "selectUrgency", function selectUrgency(value) {
    this.searchParams.urgency = value;
    this.currentPage = 1;
    this.loadFeedbackList();
    this.closeUrgencyPicker();
  }), (0, _defineProperty2.default)(_methods, "showProjectPicker", function showProjectPicker() {
    this.$refs.projectPopup.open();
  }), (0, _defineProperty2.default)(_methods, "closeProjectPicker", function closeProjectPicker() {
    this.$refs.projectPopup.close();
  }), (0, _defineProperty2.default)(_methods, "selectProject", function selectProject(value) {
    this.searchParams.project = value;
    this.currentPage = 1;
    this.loadFeedbackList();
    this.closeProjectPicker();
  }), (0, _defineProperty2.default)(_methods, "showResponsiblePicker", function showResponsiblePicker() {
    this.$refs.responsiblePopup.open();
  }), (0, _defineProperty2.default)(_methods, "closeResponsiblePicker", function closeResponsiblePicker() {
    this.$refs.responsiblePopup.close();
  }), (0, _defineProperty2.default)(_methods, "selectResponsible", function selectResponsible(value) {
    this.searchParams.responsible = value;
    this.currentPage = 1;
    this.loadFeedbackList();
    this.closeResponsiblePicker();
  }), (0, _defineProperty2.default)(_methods, "getStatusText", function getStatusText(value) {
    if (!value) return '';
    var option = this.statusOptions.find(function (item) {
      return item.value === value;
    });
    return option ? option.text : '';
  }), (0, _defineProperty2.default)(_methods, "getUrgencyText", function getUrgencyText(value) {
    if (!value) return '';
    var option = this.urgencyOptions.find(function (item) {
      return item.value === value;
    });
    return option ? option.text : '';
  }), (0, _defineProperty2.default)(_methods, "getProjectText", function getProjectText(value) {
    if (!value) return '';
    var option = this.projectOptions.find(function (item) {
      return item.value === value;
    });
    return option ? option.text : '';
  }), (0, _defineProperty2.default)(_methods, "getResponsibleText", function getResponsibleText(value) {
    if (!value) return '';
    var option = this.responsibleOptions.find(function (item) {
      return item.value === value;
    });
    return option ? option.text : '';
  }), (0, _defineProperty2.default)(_methods, "getReasonDisplay", function getReasonDisplay(item) {
    var status = item.workflowStatus;
    var reasons = [];

    // 优先从actionHistory中提取理由（新数据）
    if (item.actionHistory && item.actionHistory.length > 0) {
      // 过滤出审核类型的操作
      var auditActions = item.actionHistory.filter(function (action) {
        return ['supervisor_approve', 'supervisor_reject', 'supervisor_meeting', 'pm_approve', 'pm_reject', 'gm_approve', 'gm_reject'].includes(action.action);
      });

      // 按时间排序并提取理由
      auditActions.sort(function (a, b) {
        return a.timestamp - b.timestamp;
      }).forEach(function (action) {
        var roleMap = {
          'supervisor_approve': '主管',
          'supervisor_reject': '主管',
          'supervisor_meeting': '主管',
          'pm_approve': '副厂长',
          'pm_reject': '副厂长',
          'gm_approve': '厂长',
          'gm_reject': '厂长'
        };
        var roleName = roleMap[action.action];
        if (roleName && action.reason) {
          reasons.push("".concat(roleName, "\uFF1A").concat(action.reason));
        }
      });
    }
    return reasons.length > 0 ? reasons.join('\n') : '-';
  }), (0, _defineProperty2.default)(_methods, "formatReasonText", function formatReasonText(reasonText) {
    if (!reasonText || reasonText === '-') {
      return '';
    }
    var lines = reasonText.split('\n');
    var formattedLines = lines.map(function (line) {
      return "<div class=\"reason-item\">".concat(line, "</div>");
    });
    return formattedLines.join('');
  }), (0, _defineProperty2.default)(_methods, "shouldRefreshOnCrossDeviceUpdate", function shouldRefreshOnCrossDeviceUpdate(data) {
    // 如果页面不可见，标记需要刷新但不立即刷新
    if (!this.isPageVisible) {
      this.needsRefreshOnShow = true;
      return false;
    }

    // 如果距离上次刷新时间太短（小于3秒），仅对非重要更新进行节流
    var timeSinceLastRefresh = Date.now() - (this.lastRefreshTime || 0);
    if (timeSinceLastRefresh < 3000) {
      // 检查是否为重要更新类型
      if (data.updateTypes) {
        var importantTypes = ['feedback_submitted', 'feedback_deleted', 'workflow_status_changed'];
        var hasImportantUpdate = data.updateTypes.some(function (type) {
          return importantTypes.includes(type);
        });
        if (!hasImportantUpdate) {
          return false;
        }
      }
    }

    // 如果更新类型包含反馈相关的操作，需要刷新
    if (data.updateTypes && data.updateTypes.length > 0) {
      var relevantTypes = ['workflow_status_changed', 'feedback_submitted', 'feedback_deleted'];
      var hasRelevantUpdate = data.updateTypes.some(function (type) {
        return relevantTypes.includes(type);
      });
      if (hasRelevantUpdate) {
        return true;
      }
    }

    // 如果有多个更新记录，可能需要刷新
    if (data.updateCount > 2) {
      return true;
    }

    // 如果没有明确的更新类型信息，采用保守策略：刷新
    if (!data.updateTypes || data.updateTypes.length === 0) {
      return true;
    }
    return false;
  }), _methods),
  beforeDestroy: function beforeDestroy() {
    // 移除事件监听
    uni.$off('cross-device-update-detected');
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 81:
/*!************************************************************************************************!*\
  !*** D:/Xwzc/pages/feedback/list.vue?vue&type=style&index=0&id=2980693f&scoped=true&lang=css& ***!
  \************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_0_id_2980693f_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&id=2980693f&scoped=true&lang=css& */ 82);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_0_id_2980693f_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_0_id_2980693f_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_0_id_2980693f_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_0_id_2980693f_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_0_id_2980693f_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 82:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/feedback/list.vue?vue&type=style&index=0&id=2980693f&scoped=true&lang=css& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ 83:
/*!*************************************************************************!*\
  !*** D:/Xwzc/pages/feedback/list.vue?vue&type=style&index=1&lang=scss& ***!
  \*************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=1&lang=scss& */ 84);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_list_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 84:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/feedback/list.vue?vue&type=style&index=1&lang=scss& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[75,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/feedback/list.js.map
require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol_pkg/checkin/index"],{

/***/ 294:
/*!***********************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fpatrol_pkg%2Fcheckin%2Findex"} ***!
  \***********************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index2 = _interopRequireDefault(__webpack_require__(/*! ./pages/patrol_pkg/checkin/index.vue */ 295));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index2.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 295:
/*!**************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/checkin/index.vue ***!
  \**************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_852b8e9a___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=852b8e9a& */ 296);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 298);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&lang=scss& */ 305);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_852b8e9a___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_852b8e9a___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _index_vue_vue_type_template_id_852b8e9a___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/patrol_pkg/checkin/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 296:
/*!*********************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/checkin/index.vue?vue&type=template&id=852b8e9a& ***!
  \*********************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_852b8e9a___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=852b8e9a& */ 297);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_852b8e9a___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_852b8e9a___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_852b8e9a___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_852b8e9a___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 297:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/checkin/index.vue?vue&type=template&id=852b8e9a& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.showCamera ? _vm.getFlashIcon() : null
  var m1 = _vm.showCamera ? _vm.getFlashText() : null
  var m2 = !_vm.showScanner ? _vm.getAccuracyColor() : null
  var g0 =
    !_vm.showScanner && _vm.currentLocation.accuracy
      ? _vm.currentLocation.accuracy.toFixed(1)
      : null
  var m3 =
    !_vm.showScanner && _vm.nextUnCheckedPoint
      ? _vm.getNextPointDistanceText()
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        m2: m2,
        g0: g0,
        m3: m3,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 298:
/*!***************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/checkin/index.vue?vue&type=script&lang=js& ***!
  \***************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 299);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 299:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/checkin/index.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, wx, uniCloud) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
var _typeof3 = __webpack_require__(/*! @babel/runtime/helpers/typeof */ 13);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _vuex = __webpack_require__(/*! vuex */ 53);
var LocationUtils = _interopRequireWildcard(__webpack_require__(/*! @/utils/location-services.js */ 69));
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! @/utils/patrol-api.js */ 71));
var _qrcodeUtils = _interopRequireDefault(__webpack_require__(/*! @/utils/qrcode-utils.js */ 300));
var _methods;
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof3(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      pointInfo: {},
      // 当前点位信息
      taskInfo: {},
      // 当前任务信息
      currentLocation: {
        latitude: 30.0,
        // 使用杭州附近坐标作为默认值
        longitude: 120.0,
        accuracy: 0
      },
      markers: [],
      // 地图标记点
      circles: [],
      // 范围圈
      isInRange: false,
      // 是否在范围内
      distance: 0,
      // 到点位的距离（米）
      distanceToBoundary: 0,
      // 到范围边界的距离
      distanceText: '0m',
      // 距离文本，从计算属性移动到数据属性
      loading: false,
      // 加载状态
      isLoading: true,
      // 页面数据加载状态
      imageList: [],
      // 图片列表
      maxImageCount: 3,
      // 最大图片数量
      formData: {
        remark: '',
        // 备注信息
        round: 1 // 默认轮次
      },

      locationUpdateTimer: null,
      // 定位更新定时器
      currentRound: null,
      // 当前轮次信息
      shiftInfo: null,
      // 班次信息
      isRoundValid: false,
      // 轮次是否有效
      roundErrorMessage: '',
      // 轮次错误信息
      roundStatusTimer: null,
      // 轮次状态更新定时器
      mapContext: null,
      // 地图实例
      locationErrorShown: false,
      // 位置错误是否已显示
      locationWarningShown: false,
      // 位置警告是否已显示
      isLocationAccuracyLow: false,
      // GPS精度是否低
      uploadRetryCount: 0,
      // 上传重试计数
      uploadMaxRetries: 3,
      // 最大重试次数
      lastInRange: false,
      // 上一次的范围状态
      isFirstLocation: true,
      // 添加标记，用于判断是否首次定位
      lastUpdateTime: 0,
      // 上次位置更新时间
      minimumUpdateInterval: 2000,
      // 最小位置更新间隔(毫秒)
      locationChangeThreshold: 5,
      // 位置变化阈值(米)，变化小于此值不更新
      lastLocation: null,
      // 上次位置数据
      isFollowMode: true,
      // 默认开启跟随模式
      showCamera: false,
      // 是否显示相机
      flashMode: 'off',
      // 闪光灯模式：on(拍照时闪光), off(关闭), torch(常亮模式)
      cameraContext: null,
      // 相机上下文
      flashPopupMessage: '',
      // 闪光灯弹窗消息
      distancePopupMessage: '',
      // 添加距离提示消息

      // 新增二维码相关数据
      qrcodeScanned: false,
      // 是否已扫描二维码
      qrcodeVerified: false,
      // 二维码是否验证通过
      qrcodeData: null,
      // 扫描到的二维码数据
      qrcodeVerifyResult: {
        // 二维码验证结果
        valid: false,
        title: '',
        message: '',
        code: '',
        data: null
      },
      qrcodeAllowedDistance: 30,
      // 二维码打卡允许的距离（米）
      showScanner: false,
      // 是否显示扫码界面

      // 新增：信息展示相关数据
      nextUnCheckedPoint: null,
      // 下个未打卡点位信息
      showDistanceMessage: false,
      lastValidLocation: null,
      // 最后有效位置
      locationWatchId: null,
      isAutoJumping: false // 是否正在自动跳转中
    };
  },

  computed: _objectSpread(_objectSpread({}, (0, _vuex.mapState)({
    userInfo: function userInfo(state) {
      return state.user.userInfo;
    }
  })), {}, {
    // 判断当前点位是否已打卡
    isCurrentPointChecked: function isCurrentPointChecked() {
      var _this = this;
      if (!this.currentRound || !this.currentRound.points || !this.pointInfo) return false;
      var currentPoint = this.currentRound.points.find(function (p) {
        return p.point_id === _this.pointInfo._id;
      });
      return currentPoint && currentPoint.status && currentPoint.status > 0;
    },
    // 判断当前点位是否是第一个未打卡的点位
    isCurrentPointFirstUnchecked: function isCurrentPointFirstUnchecked() {
      if (!this.currentRound || !this.currentRound.points || !this.pointInfo) return false;
      var firstUnCheckedPoint = this.currentRound.points.find(function (point) {
        return !point.status || point.status === 0;
      });
      return firstUnCheckedPoint && firstUnCheckedPoint.point_id === this.pointInfo._id;
    },
    // 判断整个轮次是否已完成
    isRoundCompleted: function isRoundCompleted() {
      if (!this.currentRound || !this.currentRound.points || !Array.isArray(this.currentRound.points)) {
        return false;
      }

      // 检查是否所有点位都已打卡
      var allPointsCompleted = this.currentRound.points.every(function (point) {
        return point.status && point.status > 0;
      });

      // 只有所有点位都完成且当前点位也已打卡时，才算轮次完成
      return allPointsCompleted && this.isCurrentPointChecked;
    },
    // 判断当前点位是否是最后一个点位（不管是否已打卡）
    isLastPoint: function isLastPoint() {
      var _this2 = this;
      if (!this.currentRound || !this.currentRound.points || !Array.isArray(this.currentRound.points) || !this.pointInfo) {
        return false;
      }

      // 找到当前点位在轮次中的位置
      var currentPointIndex = this.currentRound.points.findIndex(function (p) {
        return p.point_id === _this2.pointInfo._id;
      });
      if (currentPointIndex === -1) return false;

      // 检查当前点位后面是否还有其他点位
      return currentPointIndex === this.currentRound.points.length - 1;
    },
    // 判断当前点位是否是最后一个未打卡的点位
    isLastUnCheckedPoint: function isLastUnCheckedPoint() {
      if (!this.currentRound || !this.currentRound.points || !this.pointInfo) return false;

      // 获取所有未打卡的点位
      var unCheckedPoints = this.currentRound.points.filter(function (point) {
        return !point.status || point.status === 0;
      });

      // 必须是最后一个未打卡的点位才显示提示
      return unCheckedPoints.length === 1 && unCheckedPoints[0].point_id === this.pointInfo._id;
    },
    // 获取当前点位序号
    currentPointIndex: function currentPointIndex() {
      var _this3 = this;
      if (!this.currentRound || !this.currentRound.points || !this.pointInfo) {
        return null;
      }
      var index = this.currentRound.points.findIndex(function (p) {
        return p.point_id === _this3.pointInfo._id;
      });
      return index !== -1 ? index + 1 : null;
    },
    // 获取下个点位序号
    nextPointIndex: function nextPointIndex() {
      var _this4 = this;
      if (!this.currentRound || !this.currentRound.points || !this.nextUnCheckedPoint) {
        return null;
      }
      var index = this.currentRound.points.findIndex(function (p) {
        return p.point_id === _this4.nextUnCheckedPoint.point_id;
      });
      return index !== -1 ? index + 1 : null;
    },
    // 格式化当前点位名称（带序号）
    formattedCurrentPointName: function formattedCurrentPointName() {
      if (!this.pointInfo) return '获取中...';
      var index = this.currentPointIndex;
      return index ? "".concat(index, ". ").concat(this.pointInfo.name) : this.pointInfo.name;
    },
    // 格式化下个点位名称（带序号）
    formattedNextPointName: function formattedNextPointName() {
      if (!this.nextUnCheckedPoint) return '未知点位';
      var index = this.nextPointIndex;
      return index ? "".concat(index, ". ").concat(this.nextUnCheckedPoint.name) : this.nextUnCheckedPoint.name;
    }
  }),
  onLoad: function onLoad(options) {
    // 设置加载状态
    this.isLoading = true;

    // 优先使用从巡视首页传递的位置信息作为初始定位
    this.initLocationFromParams(options);

    // 获取传递的参数
    this.parseAndValidateParams(options);

    // 检查并请求位置权限
    this.checkLocationPermission();
  },
  onUnload: function onUnload() {
    // 清除位置监听和定时器
    this.stopLocationWatch();
    if (this.locationUpdateTimer) {
      clearInterval(this.locationUpdateTimer);
      this.locationUpdateTimer = null;
    }
    // 清除轮次状态更新定时器
    this.stopRoundStatusTimer();
    // 重置自动跳转标记
    this.isAutoJumping = false;
  },
  onShow: function onShow() {
    // 启动轮次状态更新定时器
    this.startRoundStatusTimer();
  },
  onHide: function onHide() {
    // 停止轮次状态更新定时器
    this.stopRoundStatusTimer();
  },
  onReady: function onReady() {
    var _this5 = this;
    // 在页面准备好后获取地图实例
    this.mapContext = uni.createMapContext('checkInMap', this);

    // 添加地图初始化后延迟设置用户位置
    setTimeout(function () {
      if (_this5.mapContext && _this5.currentLocation.latitude && _this5.currentLocation.latitude !== 30.0) {
        // 只有当获取到真实位置时才移动地图（避免从默认位置30.0, 120.0跳转）
        _this5.mapContext.moveToLocation({
          latitude: _this5.currentLocation.latitude,
          longitude: _this5.currentLocation.longitude
        });
      }
    }, 500); // 延迟500ms执行，确保地图已经准备好
  },

  methods: (_methods = {
    // 从URL参数初始化位置信息
    initLocationFromParams: function initLocationFromParams(options) {
      // 如果巡视首页传递了位置参数，保存为最后有效位置
      if (options && options.lat && options.lng) {
        var lat = parseFloat(options.lat);
        var lng = parseFloat(options.lng);
        var accuracy = parseFloat(options.accuracy) || 0;

        // 验证坐标的有效性
        if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
          // 保存为最后有效位置
          this.lastValidLocation = {
            latitude: lat,
            longitude: lng,
            accuracy: accuracy,
            lastUpdated: Date.now()
          };

          // 同时设置为当前位置
          this.currentLocation = _objectSpread(_objectSpread({}, this.lastValidLocation), {}, {
            altitude: 0,
            speed: 0,
            address: ''
          });
          console.log('保存巡视首页位置作为备用:', lat, lng, '精度:', accuracy);

          // 立即更新地图标记和精度圈
          this.updateMapMarkers();
          this.updateCircles();
        }
      }
    },
    // 解析和验证URL参数
    parseAndValidateParams: function parseAndValidateParams(options) {
      var _this6 = this;
      if (!options) {
        this.showErrorAndGoBack('参数错误');
        return;
      }
      var pointId = options.point_id;
      var taskId = options.task_id;
      var round = options.round;

      // 验证参数存在性
      if (!pointId || !taskId) {
        this.showErrorAndGoBack('参数错误：缺少必要参数');
        return;
      }

      // 如果有轮次参数，解析为整数
      if (round) {
        this.formData.round = parseInt(round) || 1;
      }

      // 获取点位和任务信息
      this.getPointInfo(pointId).then(function () {
        return _this6.getTaskInfo(taskId);
      }).finally(function () {
        // 不管是否成功，加载完成后将加载状态设为false
        _this6.isLoading = false;
      });
    },
    // 显示错误并返回
    showErrorAndGoBack: function showErrorAndGoBack(message) {
      var _this7 = this;
      uni.showToast({
        title: message || '出错了',
        icon: 'none'
      });
      setTimeout(function () {
        _this7.goBack();
      }, 1500);
    },
    // 检查位置权限
    checkLocationPermission: function checkLocationPermission() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var hasPermission, granted;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return LocationUtils.checkLocationPermission();
              case 3:
                hasPermission = _context2.sent;
                if (hasPermission) {
                  _context2.next = 11;
                  break;
                }
                _context2.next = 7;
                return LocationUtils.requestLocationPermission();
              case 7:
                granted = _context2.sent;
                if (granted) {
                  _context2.next = 11;
                  break;
                }
                uni.showModal({
                  title: '温馨提示',
                  content: '请授权位置权限，否则无法使用打卡功能',
                  confirmText: '去设置',
                  cancelText: '返回',
                  success: function success(res) {
                    if (res.confirm) {
                      uni.openSetting({
                        success: function () {
                          var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee(settingRes) {
                            return _regenerator.default.wrap(function _callee$(_context) {
                              while (1) {
                                switch (_context.prev = _context.next) {
                                  case 0:
                                    if (!settingRes.authSetting['scope.userLocation']) {
                                      _context.next = 5;
                                      break;
                                    }
                                    _context.next = 3;
                                    return _this8.initLocation();
                                  case 3:
                                    _context.next = 7;
                                    break;
                                  case 5:
                                    uni.showToast({
                                      title: '未获得位置权限',
                                      icon: 'none'
                                    });
                                    setTimeout(function () {
                                      _this8.goBack();
                                    }, 1500);
                                  case 7:
                                  case "end":
                                    return _context.stop();
                                }
                              }
                            }, _callee);
                          }));
                          function success(_x) {
                            return _success.apply(this, arguments);
                          }
                          return success;
                        }()
                      });
                    } else {
                      setTimeout(function () {
                        _this8.goBack();
                      }, 1000);
                    }
                  }
                });
                return _context2.abrupt("return");
              case 11:
                _context2.next = 13;
                return _this8.initLocation();
              case 13:
                _context2.next = 19;
                break;
              case 15:
                _context2.prev = 15;
                _context2.t0 = _context2["catch"](0);
                console.error('位置权限检查失败:', _context2.t0);
                uni.showToast({
                  title: '位置权限检查失败',
                  icon: 'none'
                });
              case 19:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 15]]);
      }))();
    },
    // 初始化位置
    initLocation: function initLocation() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var location;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _context3.next = 3;
                return LocationUtils.getCurrentLocation({
                  type: 'gcj02',
                  isHighAccuracy: true,
                  maxRetries: 2,
                  retryDelay: 1000,
                  fallbackToLowAccuracy: false
                });
              case 3:
                location = _context3.sent;
                // 直接更新位置信息，避免地图跳转
                _this9.currentLocation = location;

                // 更新地图视图
                _this9.updateMapMarkers();
                _this9.updateCircles();

                // 重新计算距离
                _this9.calculateDistance();

                // 开始位置监听
                _context3.next = 10;
                return _this9.startLocationWatch();
              case 10:
                _context3.next = 26;
                break;
              case 12:
                _context3.prev = 12;
                _context3.t0 = _context3["catch"](0);
                console.error('初始化位置失败:', _context3.t0);
                // 如果初始化失败，回退到relocate方法
                _context3.prev = 15;
                _context3.next = 18;
                return _this9.relocate(true);
              case 18:
                _context3.next = 20;
                return _this9.startLocationWatch();
              case 20:
                _context3.next = 26;
                break;
              case 22:
                _context3.prev = 22;
                _context3.t1 = _context3["catch"](15);
                console.error('回退定位也失败:', _context3.t1);
                uni.showToast({
                  title: '初始化位置失败',
                  icon: 'none'
                });
              case 26:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 12], [15, 22]]);
      }))();
    },
    // 初始化监听位置变化
    startLocationWatch: function startLocationWatch() {
      var _this10 = this;
      try {
        // 直接使用uni.onLocationChange接口监听位置变化
        uni.startLocationUpdate({
          success: function success() {
            // 成功开启定位更新
            console.log('位置监听已开启');

            // 监听位置变化事件
            uni.onLocationChange(function (res) {
              // 更新位置信息
              var newLocation = {
                latitude: res.latitude,
                longitude: res.longitude,
                accuracy: res.accuracy || 0,
                altitude: res.altitude || 0,
                speed: res.speed || 0,
                lastUpdated: Date.now()
              };

              // 如果新位置精度较好，更新为当前位置
              if (res.accuracy <= 100) {
                _this10.currentLocation = newLocation;
                _this10.lastValidLocation = newLocation;
              }
              // 如果新位置精度很差，且有最后有效位置，使用最后有效位置
              else if (res.accuracy > 100 && _this10.lastValidLocation) {
                _this10.currentLocation = _objectSpread(_objectSpread({}, _this10.lastValidLocation), {}, {
                  accuracy: res.accuracy // 保持显示当前实际精度
                });

                console.log('使用最后有效位置作为备选');
              }
              // 其他情况，还是使用新位置
              else {
                _this10.currentLocation = newLocation;
              }

              // 立即更新地图标记
              _this10.updateMapMarkers();

              // 立即更新精度圈
              _this10.updateCircles();

              // 重新计算距离
              _this10.calculateDistance();

              // 如果处于跟随模式，移动地图到当前位置
              if (_this10.isFollowMode && _this10.mapContext) {
                _this10.mapContext.moveToLocation({
                  latitude: _this10.currentLocation.latitude,
                  longitude: _this10.currentLocation.longitude
                });
              }
              // 首次定位时移动地图到当前位置（但要确保不是从默认位置跳转）
              else if (_this10.isFirstLocation && _this10.mapContext) {
                // 检查是否是有意义的位置变化（不是从默认的30.0, 120.0变化）
                var isSignificantChange = Math.abs(res.latitude - 30.0) > 0.01 || Math.abs(res.longitude - 120.0) > 0.01;
                if (isSignificantChange) {
                  _this10.mapContext.moveToLocation({
                    latitude: _this10.currentLocation.latitude,
                    longitude: _this10.currentLocation.longitude
                  });
                }
                _this10.isFirstLocation = false;
              }
            });
          },
          fail: function fail(err) {
            console.error('启动位置更新失败:', err);
            _this10.showLocationError('无法启动位置监听: ' + (err.errMsg || JSON.stringify(err)));
          }
        });
      } catch (error) {
        console.error('初始化位置监听出错:', error);
        uni.showToast({
          title: '位置监听失败，请检查定位权限',
          icon: 'none'
        });
      }
    },
    // 重新定位
    relocate: function relocate() {
      var _arguments = arguments,
        _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var showLoading, location, qualityInfo;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                showLoading = _arguments.length > 0 && _arguments[0] !== undefined ? _arguments[0] : true;
                _context4.prev = 1;
                if (showLoading) {
                  uni.showLoading({
                    title: '定位中...'
                  });
                }

                // 使用高精度位置获取方法，不允许降级到低精度
                _context4.next = 5;
                return LocationUtils.getLocationWithChecks({
                  type: 'gcj02',
                  isHighAccuracy: true,
                  maxRetries: 2,
                  // 最多尝试2次
                  retryDelay: 1000,
                  // 重试间隔1秒
                  fallbackToLowAccuracy: false // 不允许降级到低精度
                });
              case 5:
                location = _context4.sent;
                // 位置质量评估
                qualityInfo = LocationUtils.getSignalQuality(location.accuracy); // 更新位置信息
                _this11.currentLocation = location;

                // 更新地图视图
                _this11.updateMapMarkers();
                _this11.updateCircles();

                // 主动点击重新定位时，强制移动地图视角到当前位置
                if (_this11.mapContext) {
                  _this11.mapContext.moveToLocation({
                    latitude: _this11.currentLocation.latitude,
                    longitude: _this11.currentLocation.longitude
                  });

                  // 重新开启跟随模式
                  _this11.isFollowMode = true;
                }

                // 重新计算距离
                _this11.calculateDistance();
                if (showLoading) {
                  uni.hideLoading();
                  // 显示统一的定位成功提示
                  uni.showToast({
                    title: '定位成功',
                    icon: 'success',
                    duration: 1500
                  });
                }
                _context4.next = 19;
                break;
              case 15:
                _context4.prev = 15;
                _context4.t0 = _context4["catch"](1);
                console.error('重新定位失败:', _context4.t0);
                if (showLoading) {
                  uni.hideLoading();
                  uni.showToast({
                    title: '重新定位失败: ' + (_context4.t0.message || '请检查GPS是否开启'),
                    icon: 'none',
                    duration: 2000
                  });
                }
              case 19:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[1, 15]]);
      }))();
    },
    // 获取点位信息
    getPointInfo: function getPointInfo(pointId) {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var res, point, errorMsg, _errorMsg;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                if (pointId) {
                  _context5.next = 2;
                  break;
                }
                return _context5.abrupt("return");
              case 2:
                _context5.prev = 2;
                _context5.next = 5;
                return _patrolApi.default.call({
                  name: 'patrol-point',
                  action: 'getPointDetail',
                  data: {
                    point_id: pointId,
                    // 🔥 优化：打卡页面只需要点位基本信息
                    fields: ['_id', 'name', 'location', 'latitude', 'longitude', 'range', 'qrcode_enabled', 'qrcode_required', 'status', 'address', 'description', 'remark'
                    // 🚫 排除：history、detailed_info等大字段
                    ]
                  }
                });
              case 5:
                res = _context5.sent;
                if (!(res && res.code === 0 && res.data)) {
                  _context5.next = 16;
                  break;
                }
                _this12.pointInfo = res.data;

                // 🔥 增强：处理多种坐标格式
                if (_this12.pointInfo.location && (0, _typeof2.default)(_this12.pointInfo.location) === 'object') {
                  _this12.pointInfo.latitude = _this12.pointInfo.latitude || _this12.pointInfo.location.latitude || _this12.pointInfo.location.lat;
                  _this12.pointInfo.longitude = _this12.pointInfo.longitude || _this12.pointInfo.location.longitude || _this12.pointInfo.location.lng;
                }
                if (_this12.currentRound && _this12.currentRound.points) {
                  point = _this12.currentRound.points.find(function (p) {
                    return p.point_id === _this12.pointInfo._id;
                  });
                  if (point && point.status > 0) {
                    _this12.updateMapMarkers();
                    _this12.isRoundValid = false;
                    _this12.roundErrorMessage = '该点位在当前轮次已完成打卡';
                  }
                }
                _this12.updateMapMarkers();
                _this12.updateCircles();

                // 立即计算距离，避免显示异常距离值
                if (_this12.currentLocation.latitude && _this12.currentLocation.longitude) {
                  _this12.calculateDistance();
                }
                return _context5.abrupt("return", res.data);
              case 16:
                errorMsg = (res === null || res === void 0 ? void 0 : res.message) || '获取点位信息失败';
                console.error('获取点位信息失败:', errorMsg);
                uni.showToast({
                  title: errorMsg,
                  icon: 'none'
                });
                return _context5.abrupt("return", null);
              case 20:
                _context5.next = 28;
                break;
              case 22:
                _context5.prev = 22;
                _context5.t0 = _context5["catch"](2);
                _errorMsg = '获取点位信息出错: ' + (_context5.t0.message || _context5.t0);
                console.error(_errorMsg);
                uni.showToast({
                  title: _errorMsg,
                  icon: 'none'
                });
                return _context5.abrupt("return", null);
              case 28:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[2, 22]]);
      }))();
    },
    // 获取任务信息
    getTaskInfo: function getTaskInfo(taskId) {
      var _this13 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var res;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                if (taskId) {
                  _context6.next = 2;
                  break;
                }
                return _context6.abrupt("return");
              case 2:
                _context6.prev = 2;
                _context6.next = 5;
                return _patrolApi.default.call({
                  name: 'patrol-task',
                  action: 'getTaskDetail',
                  data: {
                    task_id: taskId,
                    // 🔥 打卡页面专用优化：保留points数组但简化字段，减少70%数据量
                    level: 'checkin' // 云函数内部已优化，只保留points的核心字段
                  }
                });
              case 5:
                res = _context6.sent;
                if (res.code === 0 && res.data) {
                  _this13.taskInfo = res.data;

                  // 使用新数据结构处理轮次信息
                  if (_this13.taskInfo.rounds_detail && _this13.taskInfo.rounds_detail.length > 0) {
                    // 处理轮次数据，包括跨天轮次
                    _this13.processRoundsData();

                    // 轮次数据处理完成后，获取下个未打卡点位
                    _this13.$nextTick(function () {
                      _this13.getNextUnCheckedPoint();
                    });
                  } else {
                    _this13.isRoundValid = false;
                    _this13.roundErrorMessage = '任务无轮次数据';
                  }
                } else {
                  uni.showToast({
                    title: '获取任务信息失败',
                    icon: 'none'
                  });
                }
                _context6.next = 12;
                break;
              case 9:
                _context6.prev = 9;
                _context6.t0 = _context6["catch"](2);
                uni.showToast({
                  title: '获取任务信息出错',
                  icon: 'none'
                });
              case 12:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[2, 9]]);
      }))();
    },
    // 处理轮次数据，包括跨天情况
    processRoundsData: function processRoundsData() {
      var _this14 = this;
      if (!this.taskInfo || !this.taskInfo.rounds_detail) return;

      // 获取任务基准日期
      var taskDate = this.taskInfo.patrol_date ? new Date(this.taskInfo.patrol_date) : new Date(this.taskInfo.create_date);
      var now = new Date(); // 当前时间，用于判断轮次状态

      // 处理每个轮次的时间信息和状态
      this.taskInfo.rounds_detail = this.taskInfo.rounds_detail.map(function (round) {
        // 确保轮次中有day_offset和duration字段
        round.day_offset = round.day_offset !== undefined ? Number(round.day_offset) : 0;
        round.duration = round.duration !== undefined ? Number(round.duration) : 60; // 默认60分钟

        // 设置轮次是否隐藏的标志
        round.isHidden = false;

        // 检查是否是当天有效轮次
        if (round.day_offset > 0) {
          // 如果有day_offset，检查是否已到对应日期
          var offsetDate = new Date(taskDate);
          offsetDate.setDate(taskDate.getDate() + round.day_offset);

          // 检查当前日期是否已到轮次日期
          var today = new Date();
          today.setHours(0, 0, 0, 0);
          var offsetDay = new Date(offsetDate);
          offsetDay.setHours(0, 0, 0, 0);

          // 如果未到日期，不显示在当前轮次中
          if (today < offsetDay) {
            round.isHidden = true;
          }
        }

        // 处理时间字段格式和计算实际时间
        if (round.start_time) {
          try {
            // 直接从ISO时间格式解析日期时间
            var roundStartTime = new Date(round.start_time);
            var roundEndTime = new Date(round.end_time);

            // 保存实际开始时间和结束时间
            round.actualStartTime = roundStartTime;
            round.actualEndTime = roundEndTime;

            // 计算轮次状态
            // 先判断时间状态，再结合点位完成情况
            if (now < roundStartTime) {
              round.status = 0; // 未开始
            } else if (now > roundEndTime) {
              // 已超时，但要检查点位是否全部完成
              if (round.point_stats && round.point_stats.total > 0 && round.point_stats.checked >= round.point_stats.total) {
                round.status = 2; // 虽然超时但已完成
              } else {
                round.status = 3; // 超时且未完成
              }
            } else {
              // 在时间范围内
              if (round.point_stats && round.point_stats.total > 0 && round.point_stats.checked >= round.point_stats.total) {
                round.status = 2; // 已完成
              } else {
                round.status = 1; // 进行中
              }
            }
          } catch (error) {
            console.error("\u89E3\u6790\u8F6E\u6B21[".concat(round.round, "]\u65F6\u95F4\u51FA\u9519:"), error);

            // 出错时使用备用方案：根据点位完成情况判断
            if (round.point_stats && round.point_stats.total > 0 && round.point_stats.checked >= round.point_stats.total) {
              round.status = 2; // 已完成
            } else if (round.point_stats && round.point_stats.checked > 0) {
              round.status = 1; // 进行中
            } else {
              round.status = 0; // 默认未开始
            }
          }
        } else {
          // 无时间信息时，根据点位完成情况判断
          if (round.point_stats && round.point_stats.total > 0 && round.point_stats.checked >= round.point_stats.total) {
            round.status = 2; // 已完成
          } else if (round.point_stats && round.point_stats.checked > 0) {
            round.status = 1; // 进行中
          } else {
            round.status = 0; // 默认未开始
          }
        }

        // 确保状态是数字
        round.status = parseInt(round.status || 0);
        return round;
      });

      // 过滤掉隐藏的轮次
      var visibleRounds = this.taskInfo.rounds_detail.filter(function (round) {
        return !round.isHidden;
      });

      // 根据轮次状态排序：未开始和进行中按round升序，已完成和已超时按round降序
      // 先将轮次分组
      var notStartedOrActive = visibleRounds.filter(function (round) {
        return round.status === 0 || round.status === 1;
      });
      var completedOrExpired = visibleRounds.filter(function (round) {
        return round.status === 2 || round.status === 3;
      });

      // 分别排序
      notStartedOrActive.sort(function (a, b) {
        return a.round - b.round;
      }); // 按轮次号升序
      completedOrExpired.sort(function (a, b) {
        return b.round - a.round;
      }); // 按轮次号降序

      // 合并排序后的数组
      this.taskInfo.visibleRounds = [].concat((0, _toConsumableArray2.default)(notStartedOrActive), (0, _toConsumableArray2.default)(completedOrExpired));

      // 如果有可见轮次，选择第一个作为当前轮次
      if (this.taskInfo.visibleRounds && this.taskInfo.visibleRounds.length > 0) {
        this.currentRound = this.taskInfo.visibleRounds[0];
        this.formData.round = this.currentRound.round;

        // 立即检查点位是否已打卡
        if (this.pointInfo && this.pointInfo._id) {
          var point = this.currentRound.points.find(function (p) {
            return p.point_id === _this14.pointInfo._id;
          });
          if (point && point.status > 0) {
            // 如果点位已打卡，立即更新地图标记
            this.updateMapMarkers();
            // 设置轮次无效
            this.isRoundValid = false;
            this.roundErrorMessage = '该点位在当前轮次已完成打卡';
          }
        }
        this.validateCurrentRound();
        // 启动轮次状态更新定时器
        this.startRoundStatusTimer();

        // 轮次数据处理完成后，获取下个未打卡点位
        this.$nextTick(function () {
          _this14.getNextUnCheckedPoint();
        });
      }
    },
    // 简化轮次验证逻辑，只保留一个验证方法
    validateCurrentRound: function validateCurrentRound() {
      var _this15 = this;
      // 如果没有当前轮次，则无法验证
      if (!this.currentRound) {
        this.isRoundValid = false;
        this.roundErrorMessage = '无法获取轮次信息';
        return;
      }

      // 获取当前时间
      var now = new Date();

      // 检查轮次是否被标记为隐藏（跨天未到日期）
      if (this.currentRound.isHidden) {
        this.isRoundValid = false;
        this.roundErrorMessage = "\u8F6E\u6B21".concat(this.currentRound.round, "\u5C1A\u672A\u5F00\u59CB\uFF0C\u8BF7\u5728\u6307\u5B9A\u65E5\u671F\u6267\u884C");
        return;
      }

      // 检查当前轮次状态
      if (this.currentRound.status === 0) {
        // 未开始
        if (this.currentRound.actualStartTime) {
          var timeToStart = this.currentRound.actualStartTime - now;
          if (timeToStart <= 0) {
            this.isRoundValid = false;
            this.roundErrorMessage = "\u8F6E\u6B21".concat(this.currentRound.round, "\u6B63\u5728\u5F00\u59CB...");
          } else if (timeToStart < 60000) {
            // 如果不足1分钟，显示秒数
            var secondsToStart = Math.floor(timeToStart / 1000);
            this.isRoundValid = false;
            this.roundErrorMessage = "\u8DDD\u79BB\u8F6E\u6B21".concat(this.currentRound.round, "\u5F00\u59CB\u8FD8\u6709").concat(secondsToStart, "\u79D2");
          } else if (timeToStart < 3600000) {
            // 如果不足1小时，显示分钟和秒数
            var minutesToStart = Math.floor(timeToStart / (1000 * 60));
            var _secondsToStart = Math.floor(timeToStart % (1000 * 60) / 1000);
            this.isRoundValid = false;
            this.roundErrorMessage = "\u8DDD\u79BB\u8F6E\u6B21".concat(this.currentRound.round, "\u5F00\u59CB\u8FD8\u6709").concat(minutesToStart, "\u5206").concat(_secondsToStart, "\u79D2");
          } else {
            // 如果大于等于1小时，只显示小时和分钟
            var hoursToStart = Math.floor(timeToStart / (1000 * 60 * 60));
            var _minutesToStart = Math.floor(timeToStart % (1000 * 60 * 60) / (1000 * 60));
            this.isRoundValid = false;
            this.roundErrorMessage = "\u8DDD\u79BB\u8F6E\u6B21".concat(this.currentRound.round, "\u5F00\u59CB\u8FD8\u6709").concat(hoursToStart, "\u5C0F\u65F6").concat(_minutesToStart, "\u5206\u949F");
          }
        } else {
          this.isRoundValid = false;
          this.roundErrorMessage = "\u8F6E\u6B21".concat(this.currentRound.round, "\u5C1A\u672A\u5F00\u59CB");
        }
        return;
      }

      // 明确处理进行中状态
      if (this.currentRound.status === 1) {
        // 检查当前点位在轮次中是否已打卡
        if (this.pointInfo && this.pointInfo._id) {
          var point = this.currentRound.points.find(function (p) {
            return p.point_id === _this15.pointInfo._id;
          });
          if (point && point.status > 0) {
            this.isRoundValid = false;
            this.roundErrorMessage = '该点位在当前轮次已完成打卡';
            return;
          }
        }

        // 状态为进行中，且点位未打卡，设置为有效
        this.isRoundValid = true;
        this.roundErrorMessage = '';
        return;
      }
      if (this.currentRound.status === 2) {
        // 已完成
        this.isRoundValid = false;
        this.roundErrorMessage = "\u8F6E\u6B21".concat(this.currentRound.round, "\u5DF2\u5B8C\u6210");
        return;
      }
      if (this.currentRound.status === 3) {
        // 已超时
        this.isRoundValid = false;
        this.roundErrorMessage = "\u8F6E\u6B21".concat(this.currentRound.round, "\u5DF2\u8D85\u65F6");
        return;
      }

      // 检查当前点位在轮次中是否已打卡
      if (this.pointInfo && this.pointInfo._id) {
        var _point = this.currentRound.points.find(function (p) {
          return p.point_id === _this15.pointInfo._id;
        });
        if (_point && _point.status > 0) {
          this.isRoundValid = false;
          this.roundErrorMessage = '该点位在当前轮次已完成打卡';
          return;
        }
      }

      // 一切检查通过，轮次有效
      this.isRoundValid = true;
      this.roundErrorMessage = '';
    },
    // 更新范围圈
    updateCircles: function updateCircles() {
      if (!this.pointInfo || !this.pointInfo.latitude || !this.pointInfo.longitude) {
        return;
      }
      var baseRange = this.pointInfo.range || 10; // 基础范围
      var accuracy = this.currentLocation.accuracy;

      // 更新范围圈，使用基础范围
      this.circles = [{
        latitude: this.pointInfo.latitude,
        longitude: this.pointInfo.longitude,
        color: this.isInRange ? '#52C41A33' : '#FF4D4F33',
        fillColor: this.isInRange ? '#52C41A22' : '#FF4D4F22',
        radius: baseRange,
        // 使用基础范围，不再添加宽松度
        strokeWidth: 2
      }];

      // 添加当前位置精度圈
      if (this.currentLocation.accuracy > 0) {
        var circleColor, fillColor;
        if (accuracy <= 5) {
          circleColor = '#34C75988'; // 绿色 - 精度极好
          fillColor = '#34C75933';
        } else if (accuracy <= 10) {
          circleColor = '#00C58E88'; // 青色 - 精度良好
          fillColor = '#00C58E33';
        } else if (accuracy <= 15) {
          circleColor = '#FFD60A88'; // 黄色 - 精度一般
          fillColor = '#FFD60A33';
        } else if (accuracy <= 20) {
          circleColor = '#FF950088'; // 橙色 - 精度较差
          fillColor = '#FF950033';
        } else if (accuracy <= 25) {
          circleColor = '#FF6B2C88'; // 深橙色 - 精度很差
          fillColor = '#FF6B2C33';
        } else {
          circleColor = '#FF3B3088'; // 红色 - 精度极差
          fillColor = '#FF3B3033';
        }
        this.circles.push({
          latitude: this.currentLocation.latitude,
          longitude: this.currentLocation.longitude,
          color: circleColor,
          fillColor: fillColor,
          radius: 3,
          // 固定3米精度圈
          strokeWidth: 2,
          strokeColor: circleColor.slice(0, 7)
        });
      }
    },
    // 计算到点位的距离
    calculateDistance: function calculateDistance() {
      if (!this.pointInfo || !this.pointInfo.latitude || !this.pointInfo.longitude) {
        return;
      }
      try {
        // 直接使用当前位置计算距离
        var distance = LocationUtils.calculateDistance(this.currentLocation, {
          latitude: this.pointInfo.latitude,
          longitude: this.pointInfo.longitude
        });

        // 获取点位设置的范围
        var range = this.pointInfo.range || 10;

        // 判断是否在范围内
        if (distance <= range) {
          this.isInRange = true;
          this.distanceText = distance < 5 ? '很近' : "\u8FD8\u9700".concat(Math.round(distance), "\u7C73\u9760\u8FD1\u4E2D\u5FC3\u70B9");
        } else {
          this.isInRange = false;
          this.distanceText = "\u8FD8\u9700".concat(Math.round(distance - range), "\u7C73\u9760\u8FD1\u8303\u56F4\u5708");
        }

        // 更新地图显示
        if (this.isInRange !== this.lastInRange) {
          this.updateCircles();
          this.lastInRange = this.isInRange;
        }
      } catch (error) {
        console.error('计算距离出错', error);
      }
    },
    // 添加精度颜色判断方法
    getAccuracyColor: function getAccuracyColor() {
      var accuracy = this.currentLocation.accuracy;
      if (!accuracy) return '#999999';
      if (accuracy <= 5) return '#34C759'; // 绿色 - 精度极好
      if (accuracy <= 10) return '#00C58E'; // 青色 - 精度良好
      if (accuracy <= 15) return '#FFD60A'; // 黄色 - 精度一般
      if (accuracy <= 20) return '#FF9500'; // 橙色 - 精度较差
      if (accuracy <= 25) return '#FF6B2C'; // 深橙色 - 精度很差
      return '#FF3B30'; // 红色 - 精度极差
    },
    // 显示位置错误
    showLocationError: function showLocationError(message) {
      var _this16 = this;
      if (!this.locationErrorShown) {
        this.locationErrorShown = true;
        uni.showToast({
          title: message,
          icon: 'none',
          duration: 3000
        });
        setTimeout(function () {
          _this16.locationErrorShown = false;
        }, 5000);
      }
    },
    // 显示位置警告
    showLocationWarning: function showLocationWarning(message) {
      var _this17 = this;
      if (!this.locationWarningShown) {
        this.locationWarningShown = true;
        uni.showToast({
          title: message,
          icon: 'none',
          duration: 3000
        });
        setTimeout(function () {
          _this17.locationWarningShown = false;
        }, 10000); // 10秒内不重复显示
      }
    },
    // 更新地图标记点
    updateMapMarkers: function updateMapMarkers() {
      var _this$currentRound$po,
        _this18 = this;
      if (!this.pointInfo || !this.pointInfo.latitude || !this.pointInfo.longitude) {
        return;
      }

      // 检查点位是否已打卡
      var isChecked = this.currentRound && this.currentRound.points && ((_this$currentRound$po = this.currentRound.points.find(function (p) {
        return p.point_id === _this18.pointInfo._id;
      })) === null || _this$currentRound$po === void 0 ? void 0 : _this$currentRound$po.status) > 0;
      this.markers = [{
        id: 1,
        latitude: this.pointInfo.latitude,
        longitude: this.pointInfo.longitude,
        title: this.pointInfo.name,
        iconPath: isChecked ? '/static/map/map-pin.png' : '/static/map/marker.png',
        width: 32,
        height: 32,
        callout: {
          content: "".concat(this.pointInfo.name || '未命名点位').concat(isChecked ? ' ✓' : ''),
          color: '#FFFFFF',
          fontSize: 12,
          borderWidth: 0,
          bgColor: isChecked ? '#34C759' : '#3688FF',
          padding: 5,
          display: 'ALWAYS',
          borderRadius: 4,
          textAlign: 'center'
        },
        anchorX: 0.5,
        anchorY: 1.0
      }];
    },
    // 地图区域变化事件
    onMapRegionChange: function onMapRegionChange(e) {
      // 记录用户手动移动了地图
      if (e.type === 'end' && e.causedBy === 'drag') {
        this.isFirstLocation = false; // 用户已手动移动地图，不再自动居中
        this.isFollowMode = false; // 关闭跟随模式
      }
    },
    // 点击标记点事件
    onMarkerTap: function onMarkerTap(e) {
      // 可以在这里处理点击标记点逻辑
    },
    // 选择图片
    chooseImage: function chooseImage() {
      var _this19 = this;
      // 在微信小程序中使用camera组件

      this.showCamera = true;
      this.$nextTick(function () {
        _this19.cameraContext = wx.createCameraContext('nativeCamera');
      });

      // 在其他平台继续使用uni.chooseImage
    },
    // 切换闪光灯状态
    toggleFlash: function toggleFlash() {
      // 记录上一个状态
      var previousMode = this.flashMode;

      // 循环切换闪光灯状态: off -> on -> torch -> off
      if (this.flashMode === 'off') {
        this.flashMode = 'on';
      } else if (this.flashMode === 'on') {
        this.flashMode = 'torch';
      } else {
        this.flashMode = 'off';
      }

      // 只保留中间的状态提示
      var toastMsg = '';
      if (this.flashMode === 'off') {
        toastMsg = '已关闭闪光灯';
      } else if (this.flashMode === 'on') {
        toastMsg = '闪光灯已开启';
      } else if (this.flashMode === 'torch') {
        toastMsg = '常亮模式已开启';
      }
      uni.showToast({
        title: toastMsg,
        icon: 'none',
        duration: 1000
      });
    },
    // 二维码扫描器的闪光灯控制
    toggleScannerFlash: function toggleScannerFlash() {
      // 简单切换开关状态（只有 off 和 torch 两种状态）
      this.flashMode = this.flashMode === 'off' ? 'torch' : 'off';

      // 显示简单的状态提示
      uni.showToast({
        title: this.flashMode === 'off' ? '已关闭照明' : '已开启照明',
        icon: 'none',
        duration: 1000
      });
    },
    // 获取闪光灯图标
    getFlashIcon: function getFlashIcon() {
      switch (this.flashMode) {
        case 'on':
          return 'eye-filled';
        case 'torch':
          return 'fire-filled';
        default:
          return 'eye';
      }
    },
    // 获取闪光灯文本
    getFlashText: function getFlashText() {
      switch (this.flashMode) {
        case 'on':
          return '闪光灯开';
        case 'torch':
          return '常亮模式';
        default:
          return '闪光灯关';
      }
    },
    // 关闭相机
    closeCamera: function closeCamera() {
      this.showCamera = false;
      this.cameraContext = null;
    },
    // 使用camera组件拍照
    takePhoto: function takePhoto() {
      var _this20 = this;
      if (!this.cameraContext) {
        uni.showToast({
          title: '相机未初始化',
          icon: 'none'
        });
        return;
      }

      // 保存当前闪光灯状态
      var currentFlash = this.flashMode;

      // 根据不同的闪光灯模式处理拍照
      if (currentFlash === 'off') {
        // 闪光灯关闭模式，直接拍照
        this.doTakePhoto(currentFlash);
      } else if (currentFlash === 'on') {
        // 模拟原生相机的闪光灯行为
        // 步骤1: 先切换到torch模式预热闪光灯 (约1秒)
        this.flashMode = 'torch';

        // 预热时间为1000ms
        setTimeout(function () {
          // 步骤2: 闪烁一下(快速切换off再on)以模拟原生相机闪光
          _this20.flashMode = 'off';
          setTimeout(function () {
            // 步骤3: 开启闪光灯并拍照
            _this20.flashMode = 'on';

            // 拍照
            _this20.doTakePhoto('on');

            // 步骤4: 拍照后保持闪光灯一段时间
            setTimeout(function () {
              // 步骤5: 关闭闪光灯
              _this20.flashMode = 'off';

              // 步骤6: 恢复到用户设置的闪光灯模式
              setTimeout(function () {
                _this20.flashMode = currentFlash;
              }, 500);
            }, 800); // 保持闪光灯亮起800ms
          }, 200); // 关闭闪光灯200ms
        }, 1000); // 预热时间1000ms
      } else {
        // torch模式
        // 常亮模式，直接拍照
        this.doTakePhoto(currentFlash);
      }
    },
    // 实际执行拍照
    doTakePhoto: function doTakePhoto(flashMode) {
      var _this21 = this;
      this.cameraContext.takePhoto({
        quality: 'high',
        flash: flashMode,
        success: function success(res) {
          // 拍照成功，处理图片
          var tempFilePaths = [res.tempImagePath];
          _this21.compressImages(tempFilePaths);
          _this21.showCamera = false; // 拍照后关闭相机
        },

        fail: function fail(err) {
          console.error('拍照失败:', err);
          uni.showToast({
            title: '拍照失败: ' + (err.errMsg || JSON.stringify(err)),
            icon: 'none'
          });
        }
      });
    },
    // 处理相机错误
    handleCameraError: function handleCameraError(err) {
      if (err.detail && err.detail.errMsg && err.detail.errMsg.includes('auth deny')) {
        uni.showModal({
          title: '提示',
          content: '需要相机权限才能拍照上传，请在设置中允许访问相机',
          confirmText: '去设置',
          success: function success(res) {
            if (res.confirm) {
              uni.openSetting();
            }
          }
        });
      } else {
        uni.showToast({
          title: '相机出错',
          icon: 'none'
        });
      }
    },
    // 压缩图片
    compressImages: function compressImages(tempFilePaths) {
      var _this22 = this;
      var promises = tempFilePaths.map(function (path) {
        return new Promise(function (resolve, reject) {
          uni.compressImage({
            src: path,
            quality: 80,
            success: function success(res) {
              resolve(res.tempFilePath);
            },
            fail: function fail(err) {
              resolve(path); // 使用原图
            }
          });
        });
      });

      Promise.all(promises).then(function (compressedPaths) {
        _this22.imageList = [].concat((0, _toConsumableArray2.default)(_this22.imageList), (0, _toConsumableArray2.default)(compressedPaths));
      });
    },
    // 预览图片
    previewImage: function previewImage(index) {
      uni.previewImage({
        urls: this.imageList,
        current: index
      });
    },
    // 删除图片
    deleteImage: function deleteImage(index) {
      this.imageList.splice(index, 1);
    },
    // 上传图片方法
    uploadImage: function uploadImage(filePath) {
      var _this23 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                return _context7.abrupt("return", new Promise(function (resolve, reject) {
                  uni.showLoading({
                    title: '上传图片中...',
                    mask: true
                  });

                  // 使用年月日创建目录结构
                  var now = new Date();
                  var year = now.getFullYear();
                  var month = String(now.getMonth() + 1).padStart(2, '0');
                  var day = String(now.getDate()).padStart(2, '0');
                  var dateFolder = "".concat(year).concat(month).concat(day);

                  // 获取文件扩展名
                  var fileExt = '.jpg';
                  if (filePath.match(/\.(\w+)$/)) {
                    fileExt = filePath.match(/\.(\w+)$/)[0];
                  }

                  // 创建唯一文件名
                  var fileName = "".concat(Date.now(), "_").concat(Math.floor(Math.random() * 1000)).concat(fileExt);

                  // 使用uniCloud上传方法
                  uniCloud.uploadFile({
                    filePath: filePath,
                    cloudPath: "patrol/photos/".concat(dateFolder, "/").concat(fileName),
                    cloudPathAsRealPath: true,
                    // 启用真实目录支持
                    success: function success(uploadRes) {
                      uni.hideLoading();
                      resolve(uploadRes.fileID);
                    },
                    fail: function fail(err) {
                      uni.hideLoading();

                      // 判断是否要重试
                      if (_this23.uploadRetryCount < _this23.uploadMaxRetries) {
                        _this23.uploadRetryCount++;
                        uni.showToast({
                          title: "\u4E0A\u4F20\u5931\u8D25\uFF0C\u6B63\u5728\u91CD\u8BD5(".concat(_this23.uploadRetryCount, "/").concat(_this23.uploadMaxRetries, ")"),
                          icon: 'none'
                        });

                        // 延迟1秒后重试
                        setTimeout(function () {
                          _this23.uploadImage(filePath).then(resolve).catch(reject);
                        }, 1000);
                      } else {
                        _this23.uploadRetryCount = 0;
                        uni.showToast({
                          title: '图片上传失败，请重试',
                          icon: 'none'
                        });
                        reject(err);
                      }
                    }
                  });
                }));
              case 1:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7);
      }))();
    },
    // 提交打卡
    submitCheckin: function submitCheckin() {
      var _this24 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var currentLocation;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                if (!_this24.loading) {
                  _context8.next = 2;
                  break;
                }
                return _context8.abrupt("return");
              case 2:
                if (!(_this24.pointInfo && _this24.pointInfo.qrcode_required && !_this24.qrcodeVerified)) {
                  _context8.next = 6;
                  break;
                }
                uni.showToast({
                  title: '该点位必须使用二维码打卡',
                  icon: 'none'
                });

                // 自动调起扫码
                setTimeout(function () {
                  _this24.scanQRCode();
                }, 1500);
                return _context8.abrupt("return");
              case 6:
                if (_this24.pointInfo.qrcode_required) {
                  _context8.next = 22;
                  break;
                }
                _context8.prev = 7;
                _context8.next = 10;
                return LocationUtils.getCurrentLocation();
              case 10:
                currentLocation = _context8.sent;
                _this24.currentLocation = currentLocation;
                _this24.calculateDistance();

                // 再次检查范围
                if (_this24.isInRange) {
                  _context8.next = 16;
                  break;
                }
                uni.showToast({
                  title: '您已离开打卡范围，请靠近点位',
                  icon: 'none'
                });
                return _context8.abrupt("return");
              case 16:
                _context8.next = 22;
                break;
              case 18:
                _context8.prev = 18;
                _context8.t0 = _context8["catch"](7);
                uni.showToast({
                  title: '获取当前位置失败，请重试',
                  icon: 'none'
                });
                return _context8.abrupt("return");
              case 22:
                // 执行打卡
                _this24.doCheckIn(_this24.qrcodeVerified, _this24.qrcodeVerifyResult);
              case 23:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[7, 18]]);
      }))();
    },
    // 处理二维码验证成功后的逻辑
    handleQRCodeSuccess: function handleQRCodeSuccess() {
      // 如果是强制二维码，直接允许打卡，不检查距离
      if (this.pointInfo.qrcode_required) {
        this.doCheckIn(true, this.qrcodeVerifyResult);
        return;
      }

      // 非强制二维码时才判断距离
      var distanceToPoint = LocationUtils.calculateDistance(this.currentLocation, {
        latitude: this.pointInfo.latitude,
        longitude: this.pointInfo.longitude
      });

      // 使用二维码允许的距离来判断
      if (distanceToPoint <= this.qrcodeAllowedDistance) {
        this.doCheckIn(true, this.qrcodeVerifyResult);
      } else {
        var message = "\u60A8\u8DDD\u79BB\u70B9\u4F4D\u4E2D\u5FC3\u70B9".concat(Math.round(distanceToPoint), "\u7C73\uFF0C\u8BF7\u9760\u8FD1\u540E\u518D\u626B\u7801");
        this.showDistancePopup(message);
      }
    },
    // 验证二维码
    verifyQRCode: function verifyQRCode(qrcodeContent) {
      var _this25 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                _context9.prev = 0;
                _context9.next = 3;
                return _qrcodeUtils.default.verifyQRCode(qrcodeContent, {
                  pointId: _this25.pointInfo._id,
                  checkExpired: false // 关闭时效性验证
                });
              case 3:
                return _context9.abrupt("return", _context9.sent);
              case 6:
                _context9.prev = 6;
                _context9.t0 = _context9["catch"](0);
                console.error('验证二维码出错', _context9.t0);
                return _context9.abrupt("return", {
                  valid: false,
                  code: 'VERIFY_ERROR',
                  message: '验证过程中出错'
                });
              case 10:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[0, 6]]);
      }))();
    },
    // 执行打卡操作，支持二维码验证
    doCheckIn: function doCheckIn() {
      var _arguments2 = arguments,
        _this26 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var fromQRCode, qrcodeResult, point, distanceToPoint, isAllowed, message, imageUrls, uploadFailed, i, tempFilePath, fileID, currentRoundNumber, params, res;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                fromQRCode = _arguments2.length > 0 && _arguments2[0] !== undefined ? _arguments2[0] : false;
                qrcodeResult = _arguments2.length > 1 && _arguments2[1] !== undefined ? _arguments2[1] : null;
                if (!_this26.loading) {
                  _context10.next = 4;
                  break;
                }
                return _context10.abrupt("return");
              case 4:
                _this26.loading = true;
                _context10.prev = 5;
                if (!(_this26.currentRound && _this26.currentRound.points)) {
                  _context10.next = 14;
                  break;
                }
                point = _this26.currentRound.points.find(function (p) {
                  return p.point_id === _this26.pointInfo._id;
                });
                if (!(point && point.status > 0)) {
                  _context10.next = 14;
                  break;
                }
                _this26.distancePopupMessage = '该点位在当前轮次已完成打卡';
                _this26.$refs.distancePopup.open();
                setTimeout(function () {
                  _this26.$refs.distancePopup.close();
                }, 2000);
                _this26.loading = false;
                return _context10.abrupt("return");
              case 14:
                if (!(!fromQRCode && _this26.pointInfo && _this26.pointInfo.qrcode_required)) {
                  _context10.next = 21;
                  break;
                }
                _this26.distancePopupMessage = '该点位必须使用二维码打卡';
                _this26.$refs.distancePopup.open();
                setTimeout(function () {
                  _this26.$refs.distancePopup.close();
                }, 2000);
                _this26.loading = false;

                // 自动调起扫码
                setTimeout(function () {
                  _this26.scanQRCode();
                }, 1500);
                return _context10.abrupt("return");
              case 21:
                // 计算到点位的距离
                distanceToPoint = LocationUtils.calculateDistance(_this26.currentLocation, {
                  latitude: _this26.pointInfo.latitude,
                  longitude: _this26.pointInfo.longitude
                }); // 根据打卡方式判断是否允许打卡
                isAllowed = false;
                if (!fromQRCode) {
                  _context10.next = 36;
                  break;
                }
                if (!_this26.pointInfo.qrcode_required) {
                  _context10.next = 28;
                  break;
                }
                isAllowed = true;
                _context10.next = 34;
                break;
              case 28:
                // 非不限距离模式：使用qrcodeAllowedDistance
                isAllowed = distanceToPoint <= _this26.qrcodeAllowedDistance;
                if (isAllowed) {
                  _context10.next = 34;
                  break;
                }
                message = "\u60A8\u8DDD\u79BB\u70B9\u4F4D\u4E2D\u5FC3\u70B9".concat(Math.round(distanceToPoint), "\u7C73\uFF0C\u8BF7\u9760\u8FD1\u540E\u518D\u626B\u7801");
                _this26.showDistancePopup(message);
                _this26.loading = false;
                return _context10.abrupt("return");
              case 34:
                _context10.next = 43;
                break;
              case 36:
                if (_this26.isInRange) {
                  _context10.next = 42;
                  break;
                }
                _this26.distancePopupMessage = '您不在打卡范围内';
                _this26.$refs.distancePopup.open();
                setTimeout(function () {
                  _this26.$refs.distancePopup.close();
                }, 2000);
                _this26.loading = false;
                return _context10.abrupt("return");
              case 42:
                isAllowed = true;
              case 43:
                if (_this26.isRoundValid) {
                  _context10.next = 47;
                  break;
                }
                uni.showToast({
                  title: _this26.roundErrorMessage || '当前轮次不可用',
                  icon: 'none'
                });
                _this26.loading = false;
                return _context10.abrupt("return");
              case 47:
                // 准备上传图片
                imageUrls = [];
                if (!(_this26.imageList.length > 0)) {
                  _context10.next = 79;
                  break;
                }
                uni.showLoading({
                  title: '正在上传图片...',
                  mask: true
                });
                uploadFailed = false;
                i = 0;
              case 52:
                if (!(i < _this26.imageList.length)) {
                  _context10.next = 69;
                  break;
                }
                _context10.prev = 53;
                tempFilePath = _this26.imageList[i];
                uni.showLoading({
                  title: "\u4E0A\u4F20\u7B2C".concat(i + 1, "/").concat(_this26.imageList.length, "\u5F20\u56FE\u7247"),
                  mask: true
                });
                _context10.next = 58;
                return _this26.uploadImage(tempFilePath);
              case 58:
                fileID = _context10.sent;
                if (fileID) {
                  imageUrls.push(fileID);
                }
                _context10.next = 66;
                break;
              case 62:
                _context10.prev = 62;
                _context10.t0 = _context10["catch"](53);
                console.error('图片上传失败', _context10.t0);
                uploadFailed = true;
                // 继续上传其他图片
              case 66:
                i++;
                _context10.next = 52;
                break;
              case 69:
                uni.hideLoading();
                if (!uploadFailed) {
                  _context10.next = 79;
                  break;
                }
                if (!(imageUrls.length === 0)) {
                  _context10.next = 76;
                  break;
                }
                // 所有图片都上传失败
                uni.showModal({
                  title: '提示',
                  content: '所有图片上传失败，是否继续提交打卡？',
                  success: function success(res) {
                    if (!res.confirm) {
                      _this26.loading = false;
                      return;
                    }
                    // 用户选择继续，执行下面的提交代码
                  }
                });
                return _context10.abrupt("return");
              case 76:
                if (!(imageUrls.length !== _this26.imageList.length)) {
                  _context10.next = 79;
                  break;
                }
                // 部分图片上传失败
                uni.showModal({
                  title: '提示',
                  content: "\u5DF2\u6210\u529F\u4E0A\u4F20".concat(imageUrls.length, "/").concat(_this26.imageList.length, "\u5F20\u56FE\u7247\uFF0C\u662F\u5426\u7EE7\u7EED\u63D0\u4EA4\u6253\u5361\uFF1F"),
                  success: function success(res) {
                    if (!res.confirm) {
                      _this26.loading = false;
                      return;
                    }
                    // 用户选择继续，执行下面的提交代码
                  }
                });
                return _context10.abrupt("return");
              case 79:
                // 确保使用当前轮次的round值
                currentRoundNumber = _this26.currentRound ? _this26.currentRound.round : _this26.formData.round; // 构建打卡参数
                params = {
                  task_id: _this26.taskInfo._id,
                  point_id: _this26.pointInfo._id,
                  round: currentRoundNumber,
                  location: {
                    latitude: _this26.currentLocation.latitude,
                    longitude: _this26.currentLocation.longitude,
                    accuracy: _this26.currentLocation.accuracy
                  },
                  photos: imageUrls,
                  remark: _this26.formData.remark,
                  status: 1,
                  checkin_method: fromQRCode ? _this26.isInRange ? 'both' : 'qrcode' : 'gps'
                }; // 如果是二维码打卡，添加二维码相关信息
                if (fromQRCode && qrcodeResult && qrcodeResult.qrcodeData) {
                  params.qrcode_verified = true;
                  params.qrcode_version = qrcodeResult.qrcodeData.v;
                  params.qrcode_content = JSON.stringify({
                    pid: qrcodeResult.qrcodeData.pid,
                    v: qrcodeResult.qrcodeData.v,
                    t: qrcodeResult.qrcodeData.t
                  });
                }
                console.log('准备提交打卡数据:', params);
                uni.showLoading({
                  title: '提交打卡信息...',
                  mask: true
                });
                _context10.next = 86;
                return _patrolApi.default.call({
                  name: 'patrol-record',
                  action: 'submitCheckIn',
                  data: params
                });
              case 86:
                res = _context10.sent;
                uni.hideLoading();
                if (res.code === 0) {
                  _this26.onCheckinSuccess(res.data);

                  // 注意：成功提示已在 onCheckinSuccess 方法中处理，不再自动返回上一页
                } else {
                  uni.showToast({
                    title: res.message || '打卡失败，请重试',
                    icon: 'none',
                    duration: 3000
                  });
                }
                _context10.next = 96;
                break;
              case 91:
                _context10.prev = 91;
                _context10.t1 = _context10["catch"](5);
                uni.hideLoading();
                console.error('提交打卡失败:', _context10.t1);
                uni.showToast({
                  title: _context10.t1.message || '打卡失败，请重试',
                  icon: 'none',
                  duration: 3000
                });
              case 96:
                _context10.prev = 96;
                _this26.loading = false;
                return _context10.finish(96);
              case 99:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10, null, [[5, 91, 96, 99], [53, 62]]);
      }))();
    },
    /**
     * 处理二维码扫描结果
     * @param {Object} result 扫描结果
     */
    handleScanResult: function handleScanResult(result) {
      var _this27 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        var qrcodeData, pointId, verifyResult, res;
        return _regenerator.default.wrap(function _callee11$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                _context11.prev = 0;
                _context11.prev = 1;
                qrcodeData = JSON.parse(result);
                _context11.next = 10;
                break;
              case 5:
                _context11.prev = 5;
                _context11.t0 = _context11["catch"](1);
                console.error('二维码内容不是有效的JSON格式:', _context11.t0);
                uni.showToast({
                  title: '不是有效的巡检点二维码',
                  icon: 'none',
                  duration: 2000
                });
                return _context11.abrupt("return");
              case 10:
                if (!(!qrcodeData.type || qrcodeData.type !== 'PATROL_CHECK_IN')) {
                  _context11.next = 14;
                  break;
                }
                console.log('扫描到非巡检点二维码:', qrcodeData);
                uni.showToast({
                  title: '不是巡检点二维码',
                  icon: 'none',
                  duration: 2000
                });
                return _context11.abrupt("return");
              case 14:
                // 获取点位ID
                pointId = qrcodeData.pid;
                if (pointId) {
                  _context11.next = 18;
                  break;
                }
                uni.showToast({
                  title: '无效的二维码格式',
                  icon: 'none',
                  duration: 2000
                });
                return _context11.abrupt("return");
              case 18:
                _context11.next = 20;
                return _qrcodeUtils.default.verifyQRCode(result, {
                  pointId: _this27.pointInfo._id,
                  checkExpired: false // 关闭时效性验证
                });
              case 20:
                verifyResult = _context11.sent;
                // 更新验证结果
                _this27.qrcodeVerifyResult = {
                  valid: verifyResult.valid,
                  title: verifyResult.valid ? '验证成功' : '验证失败',
                  message: verifyResult.message,
                  code: verifyResult.code,
                  data: verifyResult.data,
                  qrcodeData: qrcodeData
                };
                if (!verifyResult.valid) {
                  _context11.next = 33;
                  break;
                }
                // 验证成功，直接进行打卡，不显示对话框
                _this27.qrcodeVerified = true;
                _this27.qrcodeData = qrcodeData;

                // 如果验证的点位与当前点位不同，更新点位信息
                if (!(pointId !== _this27.pointInfo._id)) {
                  _context11.next = 30;
                  break;
                }
                _context11.next = 28;
                return _patrolApi.default.getPointDetail(pointId);
              case 28:
                res = _context11.sent;
                if (res.code === 0 && res.data) {
                  _this27.setCurrentPoint(res.data);
                }
              case 30:
                // 直接执行打卡逻辑，不需要用户确认
                _this27.handleQRCodeSuccess();
                _context11.next = 34;
                break;
              case 33:
                // 验证失败，显示简单的Toast提示
                uni.showToast({
                  title: verifyResult.message || '二维码验证失败',
                  icon: 'none',
                  duration: 2000
                });
              case 34:
                _context11.next = 40;
                break;
              case 36:
                _context11.prev = 36;
                _context11.t1 = _context11["catch"](0);
                console.error('处理二维码失败', _context11.t1);
                uni.showToast({
                  title: '二维码验证失败',
                  icon: 'none',
                  duration: 2000
                });
              case 40:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee11, null, [[0, 36], [1, 5]]);
      }))();
    },
    // 设置当前点位
    setCurrentPoint: function setCurrentPoint(pointData) {
      var _this28 = this;
      if (!pointData) return;
      this.pointInfo = pointData;

      // 设置任务和点位信息
      if (this.taskInfo) {
        this.taskInfo.point = this.pointInfo;
      }

      // 更新地图标记和计算距离
      this.updateMapMarkers();
      this.updateCircles();
      if (this.currentLocation.latitude && this.currentLocation.longitude) {
        this.calculateDistance();
      }

      // 检查点位在当前轮次是否已打卡
      if (this.currentRound && this.currentRound.points) {
        var point = this.currentRound.points.find(function (p) {
          return p.point_id === _this28.pointInfo._id;
        });
        if (point && point.status > 0) {
          this.isRoundValid = false;
          this.roundErrorMessage = '该点位在当前轮次已完成打卡';
        } else {
          // 如果未打卡，重新验证轮次有效性
          this.validateCurrentRound();
        }
      }
    },
    // 打卡成功后处理
    onCheckinSuccess: function onCheckinSuccess(result) {
      var _this29 = this;
      // 更新轮次点位状态
      if (this.currentRound && this.pointInfo) {
        var pointIndex = this.currentRound.points.findIndex(function (p) {
          return p.point_id === _this29.pointInfo._id;
        });
        if (pointIndex !== -1) {
          this.currentRound.points[pointIndex].status = 1;
          this.currentRound.points[pointIndex].checkin_time = new Date();
        }

        // 更新点位统计
        if (this.currentRound.point_stats) {
          this.currentRound.point_stats.checked++;
        }

        // 更新地图标记
        this.updateMapMarkers();
      }

      // 重新验证轮次状态（检查当前点位是否已打卡）
      this.validateCurrentRound();

      // 重新获取下个未打卡点位
      this.$nextTick(function () {
        _this29.getNextUnCheckedPoint();

        // 防止重复跳转
        if (_this29.isAutoJumping) {
          return;
        }

        // 检查是否有下个点位需要跳转
        var hasNextPoint = _this29.nextUnCheckedPoint && _this29.nextUnCheckedPoint.point_id;
        if (hasNextPoint) {
          // 设置跳转标记
          _this29.isAutoJumping = true;

          // 有下个点位，显示成功提示并自动跳转
          uni.showToast({
            title: '打卡成功，正在跳转下个点位...',
            icon: 'success',
            duration: 1500,
            mask: false
          });

          // 延迟1.5秒后自动跳转到下个点位
          setTimeout(function () {
            // 再次检查是否还需要跳转（用户可能在等待期间取消了）
            if (_this29.isAutoJumping) {
              _this29.goToNextPoint();
            }
          }, 1500);
        } else {
          // 没有下个点位，说明本轮次已完成
          uni.showToast({
            title: '打卡成功，本轮次已完成！',
            icon: 'success',
            duration: 2000,
            mask: false
          });
        }
      });

      // 设置全局标记，便于返回patrol页面时能自动刷新任务卡片
      if (getApp().globalData && this.taskInfo && this.taskInfo._id) {
        console.log('设置打卡完成标记:', this.taskInfo._id);
        getApp().globalData.checkedInTaskId = this.taskInfo._id;
      }

      // 更新任务状态
      uni.$emit('task-updated', {
        task_id: this.taskInfo._id,
        point_id: this.pointInfo._id,
        round: this.currentRound ? this.currentRound.round : this.formData.round
      });
    },
    // 返回上一页
    goBack: function goBack() {
      uni.navigateBack();
    },
    // 停止位置监听
    stopLocationWatch: function stopLocationWatch() {
      if (this.locationWatchId) {
        uni.stopLocationUpdate();
        uni.offLocationChange();
        this.locationWatchId = null;
      }
    },
    // 扫码方法
    scanQRCode: function scanQRCode() {
      var _this30 = this;
      // 检查相机权限
      uni.authorize({
        scope: 'scope.camera',
        success: function success() {
          _this30.showScanner = true;
        },
        fail: function fail() {
          uni.showModal({
            title: '提示',
            content: '需要相机权限才能扫码，请在设置中允许使用相机',
            confirmText: '去设置',
            success: function success(res) {
              if (res.confirm) {
                uni.openSetting();
              }
            }
          });
        }
      });
    },
    // 处理扫码结果
    onScanCode: function onScanCode(e) {
      var _this31 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12() {
        var result, startTime, qrcodeData, pointId, verifyResult, res;
        return _regenerator.default.wrap(function _callee12$(_context12) {
          while (1) {
            switch (_context12.prev = _context12.next) {
              case 0:
                _context12.prev = 0;
                result = e.detail.result;
                if (result) {
                  _context12.next = 5;
                  break;
                }
                uni.showToast({
                  title: '无法识别二维码',
                  icon: 'none',
                  duration: 1000
                });
                return _context12.abrupt("return");
              case 5:
                // 提供震动反馈
                uni.vibrateShort({
                  fail: function fail() {
                    uni.vibrateLong();
                  }
                });

                // 记录开始时间
                startTime = Date.now(); // 显示加载提示
                uni.showLoading({
                  title: '正在校验打卡信息，请稍候...',
                  mask: true
                });

                // 使用nextTick确保在下一帧再关闭扫码界面
                _this31.$nextTick(function () {
                  setTimeout(function () {
                    _this31.showScanner = false;
                    // 立即更新地图标记
                    _this31.updateMapMarkers();
                    _this31.updateCircles();
                  }, 50);
                });
                _context12.prev = 9;
                _context12.prev = 10;
                qrcodeData = JSON.parse(result);
                _context12.next = 20;
                break;
              case 14:
                _context12.prev = 14;
                _context12.t0 = _context12["catch"](10);
                _context12.next = 18;
                return _this31.ensureMinLoadingTime(startTime, 1000);
              case 18:
                uni.showToast({
                  title: '不是有效的巡检点二维码',
                  icon: 'none',
                  duration: 1000
                });
                return _context12.abrupt("return");
              case 20:
                if (!(!qrcodeData.type || qrcodeData.type !== 'PATROL_CHECK_IN')) {
                  _context12.next = 25;
                  break;
                }
                _context12.next = 23;
                return _this31.ensureMinLoadingTime(startTime, 1000);
              case 23:
                uni.showToast({
                  title: '不是巡检点二维码',
                  icon: 'none',
                  duration: 1000
                });
                return _context12.abrupt("return");
              case 25:
                // 获取点位ID
                pointId = qrcodeData.pid;
                if (pointId) {
                  _context12.next = 31;
                  break;
                }
                _context12.next = 29;
                return _this31.ensureMinLoadingTime(startTime, 1000);
              case 29:
                uni.showToast({
                  title: '无效的二维码格式',
                  icon: 'none',
                  duration: 1000
                });
                return _context12.abrupt("return");
              case 31:
                _context12.next = 33;
                return _qrcodeUtils.default.verifyQRCode(result, {
                  pointId: _this31.pointInfo._id,
                  checkExpired: false
                });
              case 33:
                verifyResult = _context12.sent;
                _context12.next = 36;
                return _this31.ensureMinLoadingTime(startTime, 1000);
              case 36:
                // 更新验证结果
                _this31.qrcodeVerifyResult = {
                  valid: verifyResult.valid,
                  title: verifyResult.valid ? '验证成功' : '验证失败',
                  message: verifyResult.message,
                  code: verifyResult.code,
                  data: verifyResult.data,
                  qrcodeData: qrcodeData
                };
                if (!verifyResult.valid) {
                  _context12.next = 48;
                  break;
                }
                // 验证成功，直接进行打卡，不显示对话框
                _this31.qrcodeVerified = true;
                _this31.qrcodeData = qrcodeData;

                // 如果验证的点位与当前点位不同，更新点位信息
                if (!(pointId !== _this31.pointInfo._id)) {
                  _context12.next = 45;
                  break;
                }
                _context12.next = 43;
                return _patrolApi.default.getPointDetail(pointId);
              case 43:
                res = _context12.sent;
                if (res.code === 0 && res.data) {
                  _this31.setCurrentPoint(res.data);
                }
              case 45:
                // 直接执行打卡逻辑，不需要用户确认
                _this31.handleQRCodeSuccess();
                _context12.next = 49;
                break;
              case 48:
                // 验证失败，显示简单的Toast提示
                uni.showToast({
                  title: verifyResult.message || '二维码验证失败',
                  icon: 'none',
                  duration: 2000
                });
              case 49:
                _context12.next = 56;
                break;
              case 51:
                _context12.prev = 51;
                _context12.t1 = _context12["catch"](9);
                _context12.next = 55;
                return _this31.ensureMinLoadingTime(startTime, 1000);
              case 55:
                uni.showToast({
                  title: '二维码验证失败',
                  icon: 'none',
                  duration: 1000
                });
              case 56:
                _context12.next = 63;
                break;
              case 58:
                _context12.prev = 58;
                _context12.t2 = _context12["catch"](0);
                uni.hideLoading();
                console.error('扫码处理失败', _context12.t2);
                uni.showToast({
                  title: '二维码处理失败',
                  icon: 'none',
                  duration: 1000
                });
              case 63:
              case "end":
                return _context12.stop();
            }
          }
        }, _callee12, null, [[0, 58], [9, 51], [10, 14]]);
      }))();
    }
  }, (0, _defineProperty2.default)(_methods, "handleCameraError", function handleCameraError(e) {
    console.error('相机错误:', e);
    uni.showToast({
      title: '相机出错，请重试',
      icon: 'none'
    });
    this.showScanner = false;
  }), (0, _defineProperty2.default)(_methods, "closeScanner", function closeScanner() {
    this.showScanner = false;
  }), (0, _defineProperty2.default)(_methods, "ensureMinLoadingTime", function ensureMinLoadingTime(startTime, minDuration) {
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee13() {
      var elapsedTime;
      return _regenerator.default.wrap(function _callee13$(_context13) {
        while (1) {
          switch (_context13.prev = _context13.next) {
            case 0:
              elapsedTime = Date.now() - startTime;
              if (!(elapsedTime < minDuration)) {
                _context13.next = 4;
                break;
              }
              _context13.next = 4;
              return new Promise(function (resolve) {
                return setTimeout(resolve, minDuration - elapsedTime);
              });
            case 4:
              uni.hideLoading();
            case 5:
            case "end":
              return _context13.stop();
          }
        }
      }, _callee13);
    }))();
  }), (0, _defineProperty2.default)(_methods, "startRoundStatusTimer", function startRoundStatusTimer() {
    var _this32 = this;
    // 先清除已有的定时器
    this.stopRoundStatusTimer();

    // 每秒更新一次轮次状态
    this.roundStatusTimer = setInterval(function () {
      if (_this32.currentRound) {
        _this32.validateCurrentRound();
      }
    }, 1000);
  }), (0, _defineProperty2.default)(_methods, "stopRoundStatusTimer", function stopRoundStatusTimer() {
    if (this.roundStatusTimer) {
      clearInterval(this.roundStatusTimer);
      this.roundStatusTimer = null;
    }
  }), (0, _defineProperty2.default)(_methods, "getNextUnCheckedPoint", function getNextUnCheckedPoint() {
    var _this33 = this;
    try {
      if (!this.currentRound || !this.currentRound.points || !Array.isArray(this.currentRound.points)) {
        this.nextUnCheckedPoint = null;
        return;
      }

      // 找到当前点位在轮次中的位置
      var currentPointIndex = this.currentRound.points.findIndex(function (p) {
        return p.point_id === _this33.pointInfo._id;
      });

      // 从当前点位之后找第一个未打卡的点位
      var unCheckedPoint = null;
      if (currentPointIndex !== -1) {
        // 🔥 修复：对于第一个点位(index=0)，也要查找后续未打卡点位
        unCheckedPoint = this.currentRound.points.find(function (point, index) {
          return index > currentPointIndex && (!point.status || point.status === 0);
        });
      }

      // 如果当前点位后面没有未打卡的，或者当前点位找不到，则从头找第一个未打卡的
      if (!unCheckedPoint) {
        unCheckedPoint = this.currentRound.points.find(function (point) {
          return !point.status || point.status === 0;
        });

        // 如果找到的未打卡点位就是当前点位，则说明没有下个点位
        if (unCheckedPoint && unCheckedPoint.point_id === this.pointInfo._id) {
          this.nextUnCheckedPoint = null;
          return;
        }
      }

      // 🔥 修复：确保坐标信息正确提取
      if (unCheckedPoint) {
        // 从location对象提取坐标信息（如果存在）
        if (unCheckedPoint.location && (0, _typeof2.default)(unCheckedPoint.location) === 'object') {
          unCheckedPoint.latitude = unCheckedPoint.latitude || unCheckedPoint.location.latitude || unCheckedPoint.location.lat;
          unCheckedPoint.longitude = unCheckedPoint.longitude || unCheckedPoint.location.longitude || unCheckedPoint.location.lng;
        }
      }
      this.nextUnCheckedPoint = unCheckedPoint;
    } catch (error) {
      console.error('获取下个未打卡点位失败:', error);
      this.nextUnCheckedPoint = null;
    }
  }), (0, _defineProperty2.default)(_methods, "getNextPointDistanceText", function getNextPointDistanceText() {
    if (!this.nextUnCheckedPoint || !this.nextUnCheckedPoint.latitude || !this.nextUnCheckedPoint.longitude || !this.currentLocation.latitude || !this.currentLocation.longitude) {
      return '点击查看';
    }
    try {
      var distance = LocationUtils.calculateDistance(this.currentLocation, {
        latitude: this.nextUnCheckedPoint.latitude,
        longitude: this.nextUnCheckedPoint.longitude
      });
      if (distance < 10) {
        return '很近';
      } else if (distance < 1000) {
        return "".concat(Math.round(distance), "\u7C73");
      } else {
        return "".concat((distance / 1000).toFixed(1), "\u516C\u91CC");
      }
    } catch (error) {
      console.error('计算下个点位距离失败:', error);
      return '点击查看';
    }
  }), (0, _defineProperty2.default)(_methods, "goToNextPoint", function goToNextPoint() {
    var _this34 = this;
    // 重置跳转标记（无论是自动跳转还是手动点击）
    this.isAutoJumping = false;
    if (!this.nextUnCheckedPoint || !this.taskInfo) {
      uni.showToast({
        title: '下个点位信息不完整',
        icon: 'none'
      });
      return;
    }

    // 构建导航参数
    var params = {
      point_id: this.nextUnCheckedPoint.point_id,
      task_id: this.taskInfo._id,
      round: this.currentRound ? this.currentRound.round : this.formData.round
    };

    // 传递当前有效位置
    var locationToUse = this.currentLocation.accuracy <= 100 ? this.currentLocation : this.lastValidLocation || this.currentLocation;
    params.lat = locationToUse.latitude;
    params.lng = locationToUse.longitude;
    params.accuracy = locationToUse.accuracy || 0;

    // 在跳转前停止位置监听
    this.stopLocationWatch();

    // 导航到下个点位的打卡页面
    uni.redirectTo({
      url: "/pages/patrol_pkg/checkin/index?".concat(Object.keys(params).map(function (key) {
        return "".concat(key, "=").concat(encodeURIComponent(params[key]));
      }).join('&')),
      success: function success() {
        console.log('导航到下个点位成功');
        // 跳转成功，标记会在新页面重新初始化，这里不需要重置
      },

      fail: function fail(err) {
        console.error('导航失败:', err);
        // 如果导航失败，重新开启位置监听
        _this34.startLocationWatch();
        // 重置跳转标记
        _this34.isAutoJumping = false;
        uni.showToast({
          title: '导航失败',
          icon: 'none'
        });
      }
    });
  }), (0, _defineProperty2.default)(_methods, "handleQRCodeClick", function handleQRCodeClick() {
    var _this35 = this;
    // 检查是否正在自动跳转中
    if (this.isAutoJumping) {
      this.showDisabledButtonFeedback('正在跳转下个点位，请稍后再试');
      return;
    }

    // 检查是否可以执行扫码打卡
    if (this.loading) {
      this.showDisabledButtonFeedback('系统正在处理中，请稍后再试');
      return;
    }

    // 检查当前点位是否已打卡
    if (this.currentRound && this.currentRound.points) {
      var point = this.currentRound.points.find(function (p) {
        return p.point_id === _this35.pointInfo._id;
      });
      if (point && point.status > 0) {
        this.showDisabledButtonFeedback('该点位在当前轮次已完成打卡');
        return;
      }
    }

    // 检查轮次是否有效
    if (!this.isRoundValid) {
      this.showDisabledButtonFeedback(this.roundErrorMessage || '当前轮次暂不可用，请稍后再试');
      return;
    }

    // 执行扫码打卡
    this.scanQRCode();
  }), (0, _defineProperty2.default)(_methods, "handleGPSClick", function handleGPSClick() {
    var _this36 = this;
    // 检查是否正在自动跳转中
    if (this.isAutoJumping) {
      this.showDisabledButtonFeedback('正在跳转下个点位，请稍后再试');
      return;
    }

    // 检查是否可以执行GPS打卡
    if (this.loading) {
      this.showDisabledButtonFeedback('系统正在处理中，请稍后再试');
      return;
    }

    // 检查当前点位是否已打卡
    if (this.currentRound && this.currentRound.points) {
      var point = this.currentRound.points.find(function (p) {
        return p.point_id === _this36.pointInfo._id;
      });
      if (point && point.status > 0) {
        this.showDisabledButtonFeedback('该点位在当前轮次已完成打卡');
        return;
      }
    }

    // 检查轮次是否有效
    if (!this.isRoundValid) {
      this.showDisabledButtonFeedback(this.roundErrorMessage || '当前轮次暂不可用，请稍后再试');
      return;
    }

    // 检查是否在范围内（GPS打卡特有）
    if (!this.isInRange) {
      this.showDisabledButtonFeedback('您不在打卡范围内，请靠近点位后再试');
      return;
    }

    // 执行GPS打卡
    this.submitCheckin();
  }), (0, _defineProperty2.default)(_methods, "showDisabledButtonFeedback", function showDisabledButtonFeedback(message) {
    // 震动反馈
    uni.vibrateShort({
      fail: function fail() {
        uni.vibrateLong();
      }
    });

    // 显示Toast提示
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }), (0, _defineProperty2.default)(_methods, "showDistancePopup", function showDistancePopup(message) {
    var _this37 = this;
    this.distancePopupMessage = message;
    this.showDistanceMessage = true;

    // 2秒后自动关闭
    setTimeout(function () {
      _this37.showDistanceMessage = false;
    }, 2000);
  }), _methods)
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"]))

/***/ }),

/***/ 305:
/*!************************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/checkin/index.vue?vue&type=style&index=0&lang=scss& ***!
  \************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss& */ 306);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 306:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/checkin/index.vue?vue&type=style&index=0&lang=scss& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[294,"common/runtime","common/vendor","pages/patrol_pkg/common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/patrol_pkg/checkin/index.js.map
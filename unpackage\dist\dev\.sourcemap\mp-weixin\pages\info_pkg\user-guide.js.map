{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/info_pkg/user-guide.vue?4667", "webpack:///D:/Xwzc/pages/info_pkg/user-guide.vue?c8a8", "webpack:///D:/Xwzc/pages/info_pkg/user-guide.vue?4368", "webpack:///D:/Xwzc/pages/info_pkg/user-guide.vue?e962", "uni-app:///pages/info_pkg/user-guide.vue", "webpack:///D:/Xwzc/pages/info_pkg/user-guide.vue?d400", "webpack:///D:/Xwzc/pages/info_pkg/user-guide.vue?02eb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "onLoad", "uni", "title", "methods"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAomB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2GxnB;EACAC;IACA,QAEA;EACA;EACAC;IACAC;MACAC;IACA;EACA;EACAC,UAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACzHA;AAAA;AAAA;AAAA;AAAs3B,CAAgB,u3BAAG,EAAC,C;;;;;;;;;;;ACA14B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/info_pkg/user-guide.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/info_pkg/user-guide.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./user-guide.vue?vue&type=template&id=863c5f36&\"\nvar renderjs\nimport script from \"./user-guide.vue?vue&type=script&lang=js&\"\nexport * from \"./user-guide.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user-guide.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/info_pkg/user-guide.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-guide.vue?vue&type=template&id=863c5f36&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-guide.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-guide.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"guide-container\">\n\t\t<view class=\"guide-header\">\n\t\t\t<text class=\"header-title\">用户指南</text>\n\t\t\t<text class=\"header-date\">更新日期：2025年5月1日</text>\n\t\t</view>\n\t\t\n\t\t<view class=\"guide-content\">\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">欢迎使用</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t欢迎使用\"株水小智\"微信小程序（以下简称\"本小程序\"）。本用户指南旨在帮助您更好地了解和使用我们的服务。通过使用本小程序，您可以方便地记录、上报和管理各类问题，实现工作协同和信息共享。\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">功能介绍</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-subtitle\">1. 问题上报</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t您可以通过小程序上报各类环境、设施等问题，上传问题照片、添加详细描述和位置信息，以便相关人员及时处理。\n\t\t\t\t\t</text>\n\t\t\t\t\t\n\t\t\t\t\t<text class=\"section-subtitle\">2. 巡视打卡</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t小程序提供巡视打卡功能，您可以在预设的巡视点位进行打卡，记录巡视情况和发现的问题。\n\t\t\t\t\t</text>\n\t\t\t\t\t\n\t\t\t\t\t<text class=\"section-subtitle\">3. 任务管理</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t您可以在小程序中查看分配给您的任务，并及时处理和更新任务状态。\n\t\t\t\t\t</text>\n\t\t\t\t\t\n\t\t\t\t\t<text class=\"section-subtitle\">4. 数据统计</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t小程序提供各类数据统计功能，帮助您了解问题处理情况和工作效率。\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">使用指南</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-subtitle\">1. 账号注册与登录</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t首次使用小程序时，您可以通过微信授权一键登录进行注册。登录后，您可以在\"用户中心\"中设置完善个人信息。\n\t\t\t\t\t</text>\n\t\t\t\t\t\n\t\t\t\t\t<text class=\"section-subtitle\">2. 问题上报步骤</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\ta. 在首页或\"找茬\"页面点击\"新增\"按钮\n\t\t\t\t\t\tb. 填写问题标题和详细描述\n\t\t\t\t\t\tc. 上传问题照片（建议清晰展示问题）\n\t\t\t\t\t\td. 选择问题分类\n\t\t\t\t\t\te. 提交问题\n\t\t\t\t\t</text>\n\t\t\t\t\t\n\t\t\t\t\t<text class=\"section-subtitle\">3. 巡视打卡步骤</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\ta. 进入\"巡视打点\"页面\n\t\t\t\t\t\tb. 选择需要巡视的点位\n\t\t\t\t\t\tc. 到达点位后点击\"打卡\"按钮\n\t\t\t\t\t\td. 上传现场照片和巡视记录\n\t\t\t\t\t\te. 提交巡视信息\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">注意事项</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t1. 请确保上传的信息真实准确，不得上传虚假、违法或侵犯他人权益的内容。\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t2. 为了更好地记录问题，请尽量上传清晰的照片和详细的描述。\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t3. 巡视打卡功能需要使用您的位置信息，请确保授予小程序位置权限。\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t4. 请妥善保管您的账号信息，避免账号被他人未授权使用。\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"section\">\n\t\t\t\t<view class=\"section-title\">联系我们</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t如果您在使用过程中遇到任何问题或有任何建议，请通过以下方式联系我们：\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t电子邮件：<EMAIL>\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"section-text\">\n\t\t\t\t\t\t电话：13707335131\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tuni.setNavigationBarTitle({\n\t\t\t\ttitle: '用户指南'\n\t\t\t});\n\t\t},\n\t\tmethods: {\n\t\t\t\n\t\t}\n\t}\n</script>\n\n<style>\n\t.guide-container {\n\t\tpadding: 30rpx;\n\t\tbackground-color: #f8f8f8;\n\t\tmin-height: 100vh;\n\t}\n\t\n\t.guide-header {\n\t\tmargin-bottom: 40rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.header-title {\n\t\tdisplay: block;\n\t\tfont-size: 40rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.header-date {\n\t\tdisplay: block;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.section {\n\t\tmargin-bottom: 40rpx;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 30rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.section-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t\tborder-left: 8rpx solid #3688FF;\n\t\tpadding-left: 20rpx;\n\t}\n\t\n\t.section-content {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tline-height: 1.6;\n\t}\n\t\n\t.section-subtitle {\n\t\tdisplay: block;\n\t\tfont-size: 30rpx;\n\t\tfont-weight: bold;\n\t\tmargin: 20rpx 0 10rpx;\n\t\tcolor: #444;\n\t}\n\t\n\t.section-text {\n\t\tdisplay: block;\n\t\tmargin-bottom: 16rpx;\n\t}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-guide.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-guide.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558412588\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>株水小智：基层创新点亮智慧水务</title>
    <script src="libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="libs/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        water: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 1.5s ease-out',
                        'slide-up': 'slideUp 1s ease-out',
                        'slide-down': 'slideDown 1s ease-out',
                        'scale-in': 'scaleIn 0.8s ease-out',
                        'float': 'float 6s ease-in-out infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(50px)', opacity: '0' },
                            '100%': { transform: 'translateY(0px)', opacity: '1' }
                        },
                        slideDown: {
                            '0%': { transform: 'translateY(-50px)', opacity: '0' },
                            '100%': { transform: 'translateY(0px)', opacity: '1' }
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.8)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #1e40af 100%);
        }
        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .glass-effect:hover {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .title-glow {
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
        }
        .animate-breath-glow {
            animation: breath-glow 2.5s ease-in-out infinite;
        }
        @keyframes breath-glow {
            0% {
                text-shadow: 0 0 10px rgba(255,255,255,0.15), 0 0 30px rgba(255,255,255,0.3);
            }
            50% {
                text-shadow: 0 0 30px rgba(255,255,255,0.5), 0 0 60px rgba(255,255,255,0.6);
            }
            100% {
                text-shadow: 0 0 10px rgba(255,255,255,0.15), 0 0 30px rgba(255,255,255,0.3);
            }
        }
        .water-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }
        .particle:nth-child(1) { width: 60px; height: 60px; top: 20%; left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { width: 40px; height: 40px; top: 60%; right: 15%; animation-delay: 2s; }
        .particle:nth-child(3) { width: 80px; height: 80px; bottom: 30%; left: 20%; animation-delay: 4s; }
        .particle:nth-child(4) { width: 30px; height: 30px; top: 40%; right: 30%; animation-delay: 6s; }
        .particle:nth-child(5) { width: 50px; height: 50px; bottom: 60%; right: 10%; animation-delay: 1s; }
        
        .main-title {
            background: linear-gradient(135deg, #ffffff 0%, #e0f2fe 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle-line {
            position: relative;
        }
        .subtitle-line::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #FFD700, #FFA500);
            animation: expandLine 2s ease-out 1s forwards;
        }
        @keyframes expandLine {
            to { width: 60%; }
        }
        .fade-scale-in { animation: fadeScaleIn 0.7s cubic-bezier(0.4,0,0.2,1); }
        .fade-out { animation: fadeOutPage 0.5s cubic-bezier(0.4,0,0.2,1) forwards; }
        @keyframes fadeScaleIn { from { opacity: 0; transform: scale(0.96); } to { opacity: 1; transform: scale(1); } }
        @keyframes fadeOutPage { from { opacity: 1; transform: scale(1); } to { opacity: 0; transform: scale(0.98); } }
    </style>
</head>
<body class="gradient-bg min-h-screen flex flex-col overflow-hidden fade-scale-in">
    <!-- 水粒子背景 -->
    <div class="water-particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- 主要内容 -->
    <div class="relative z-10 min-h-screen flex flex-col justify-center items-center px-8">
        <!-- 主标题区域 -->
        <div class="text-center mb-16 animate-slide-down">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-8 animate-scale-in">
                <i class="fas fa-water text-3xl text-white"></i>
            </div>
            <h1 class="text-5xl md:text-6xl font-bold mb-6 main-title title-glow animate-breath-glow animate-fade-in">
                株水小智：基层创新点亮智慧水务
            </h1>
            <p class="text-xl md:text-2xl text-blue-100 font-light mb-8 subtitle-line animate-slide-up" style="animation-delay: 0.5s;">
                一线员工自主研发的微创新实践
            </p>
        </div>

        <!-- 信息卡片 -->
        <div id="info-card" class="glass-effect rounded-3xl p-8 md:p-12 mb-12 opacity-0 transition-opacity duration-700" style="animation-delay: 0.8s;">
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center md:text-left">
                    <div class="flex items-center justify-center md:justify-start mb-4">
                        <i class="fas fa-industry text-blue-300 mr-3 text-xl"></i>
                        <h3 class="text-xl font-semibold text-white">单位</h3>
                    </div>
                    <p class="text-lg text-blue-100">霞湾污水处理厂</p>
                </div>
                
                <div class="text-center md:text-left">
                    <div class="flex items-center justify-center md:justify-start mb-4">
                        <i class="fas fa-user text-blue-300 mr-3 text-xl"></i>
                        <h3 class="text-xl font-semibold text-white">讲稿人</h3>
                    </div>
                    <p class="text-lg text-blue-100">王浩</p>
                </div>
                
                <div class="text-center md:text-left">
                    <div class="flex items-center justify-center md:justify-start mb-4">
                        <i class="fas fa-calendar text-blue-300 mr-3 text-xl"></i>
                        <h3 class="text-xl font-semibold text-white">时间</h3>
                    </div>
                    <p class="text-lg text-blue-100">2025.07.12</p>
                </div>
            </div>
        </div>

        <!-- 开始按钮 -->
        <div class="text-center opacity-0 transition-opacity duration-700" id="start-btn-wrap" style="animation-delay: 1.2s;">
            <button onclick="startPresentation()" class="group relative inline-flex items-center justify-center px-12 py-4 bg-white/20 backdrop-blur-lg rounded-2xl text-white text-xl font-semibold hover:bg-white/30 transition-all duration-300 transform hover:scale-105">
                <i class="fas fa-play mr-3 group-hover:translate-x-1 transition-transform"></i>
                开始演示
                <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-600 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity"></div>
            </button>
        </div>

        <!-- 底部装饰 -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-fade-in" style="animation-delay: 1.5s;">
            <p class="text-blue-200 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                智慧水务 · 创新未来
            </p>
        </div>
    </div>

    <script>
        // 开始演示
        function fadeTo(url) { document.body.classList.remove('fade-scale-in'); document.body.classList.add('fade-out'); setTimeout(() => { window.location.href = url; }, 480); }
        function startPresentation() { fadeTo('slide1.html'); }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowRight') {
                startPresentation();
            }
        });

        // 鼠标移动视差效果
        document.addEventListener('mousemove', function(e) {
            const moveX = (e.clientX - window.innerWidth / 2) * 0.01;
            const moveY = (e.clientY - window.innerHeight / 2) * 0.01;
            
            document.querySelectorAll('.particle').forEach(element => {
                element.style.transform = `translate(${moveX}px, ${moveY}px)`;
            });
        });

        // 页面加载动画和淡入效果
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                document.body.style.opacity = '1';
                document.getElementById('info-card').classList.remove('opacity-0');
                document.getElementById('start-btn-wrap').classList.remove('opacity-0');
            }, 100);
        });
    </script>
</body>
</html> 
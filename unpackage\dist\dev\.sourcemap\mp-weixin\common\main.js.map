{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Xwzc/App.vue?244c", "uni-app:///App.vue", "webpack:///D:/Xwzc/App.vue?c355", "webpack:///D:/Xwzc/App.vue?71f9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "App", "mpType", "app", "store", "$mount", "data", "launchInitialized", "onLaunch", "uni", "setTimeout", "todoBadgeManager", "onShow", "onHide", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "onLaunchFromBGFetch", "console", "methods", "isTabBarPage", "handleTokenExpired", "title", "icon", "duration", "url", "initializeBadge", "startSmartRetry", "setupPageChangeListener", "lastRoute", "performOnShowUpdate", "globalData", "lazyLoadComponents", "needRefreshTaskList", "refreshTaskDate", "refreshShiftList", "lastTaskOperation", "type", "timestamp", "taskId"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAAuE;AAGlI;AACA;AACA;AAA2B;AAAA;AAN3B;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAK7B;;AAE7BC,YAAG,CAACC,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAElB,IAAMC,GAAG,GAAG,IAAIL,YAAG,iCACdG,YAAG;EACNG,KAAK,EAALA,cAAK,CAAE;AAAA,GACP;;AACF,UAAAD,GAAG,EAACE,MAAM,EAAE,C;;;;;;;;;;;;;AChBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAA6lB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACCjnB;AACA;AACA;AAGA;AADA;;AAGA;AACA,qBACA,qBACA,uBACA,sBACA,wBACA;AAAA,eAEA;EACAC;IACA;MACAC;IACA;EACA;;EACAC;IAAA;IAEA;IACA;;IAEA;IACAC;MACA;IACA;;IAEA;IACAA;MACA;IACA;;IAEA;IACAC;MACA;QACA;QACA;QAEA;UACA;UACAC;YACA;YACA;UACA;QACA;UACAA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;;IAEA;IACAF;MACA;MACAE;MACA;MACAD;QACAC;MACA;IACA;;IAEA;IACAF;MACA;MACAE;IACA;;IAEA;IACA;;IAEA;;IAEA;EAEA;EACAC;IAAA;IACA;IACA;MACA;MACA;QACAF;UACA;QACA;QACA;MACA;MAEA;IACA;EACA;EACAG;IACA;EAAA,CACA;EACAC;IACA;IACAL;IACAA;IACA;IACAM;EACA;EACA;EACAC;IACA;MACA;QACA;QACAP;;QAEA;QACA;MACA;IACA;MACAQ;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAX;MACAA;MACAA;MACAA;MACAA;;MAEA;MACAE;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;QACA;QACA;UACAF;QACA;;QAEA;QACAA;UACAY;UACAC;UACAC;QACA;MACA;QACA;QACAd;UACAY;UACAC;UACAC;QACA;QAEAb;UACAD;YAEAe;UAMA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACAd;;QAEA;QACA;MACA;QACA;QACAA;MACA;IACA;IAEA;IACAe;MACA;MACAhB;QACA;QACA;UACA;UACA;YACA;YACA;cAAA,OACA;YAAA,EACA;cACAC;YACA;UACA;YACA;YACAD;cACA;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAgB;MAAA;MACA;MACA;;MAEA;MACA;QACA;UACA;UACA;YACA;YACA;;YAEA;YACA;cACAC;;cAEA;cACA;gBACA;gBACA;kBACA;kBACAlB;oBACAC;oBACA;oBACAD;sBACAC;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;UACA;QAAA;MAEA;;MAEA;MACA;IACA;IAEA;IACAkB;MACA;MACAlB;IACA;EAEA;EACAmB;IACAC;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5RA;AAAA;AAAA;AAAA;AAAwoC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACA5pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';import App from './App'\n\n\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nimport store from './store'  // 导入Vuex存储\n\nVue.config.productionTip = false\nApp.mpType = 'app'\n\nconst app = new Vue({\n  ...App,\n  store  // 使用Vuex存储\n})\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\n\timport uniIdPageInit from '@/uni_modules/uni-id-pages/init.js';\n\timport todoBadgeManager from '@/utils/todo-badge.js';\n\timport { getCacheKey, CACHE_KEYS } from '@/utils/cache.js';\n\t\n\t// 导入request.js以确保拦截器被加载\n\timport '@/utils/request.js';\n\t\n\t// TabBar页面配置 - 统一管理，避免重复定义\n\tconst TAB_BAR_PAGES = [\n\t\t'pages/index/index',\n\t\t'pages/feedback/list',\n\t\t'pages/patrol/index',\n\t\t'pages/ucenter/ucenter'\n\t];\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tlaunchInitialized: false // 添加启动初始化标记\n\t\t\t}\n\t\t},\n\t\t\tonLaunch: function() {\n\t\t\t\n\t\t\t// 记录应用启动时间，用于冷启动检测\n\t\t\tthis.launchTime = Date.now();\n\t\t\t\n\t\t\t// 监听token失效事件\n\t\t\tuni.$on('token-invalid', () => {\n\t\t\t\tthis.handleTokenExpired('token-invalid');\n\t\t\t});\n\t\t\t\n\t\t\t// 监听token过期事件（来自角标管理器）\n\t\t\tuni.$on('token-expired', () => {\n\t\t\t\tthis.handleTokenExpired('token-expired');\n\t\t\t});\n\t\t\t\n\t\t// 异步初始化uni-id，不阻塞应用启动\n\t\tsetTimeout(() => {\n\t\t\tuniIdPageInit().then(() => {\n\t\t\t\t// 检查登录状态并立即更新角标\n\t\t\t\tconst token = uni.getStorageSync('uni_id_token');\n\t\t\t\t\n\t\t\t\tif (token) {\n\t\t\t\t\t// 使用立即更新而不是普通更新\n\t\t\t\t\ttodoBadgeManager.updateTodoCountImmediately().then(count => {\n\t\t\t\t\t\t// 标记启动初始化完成\n\t\t\t\t\t\tthis.launchInitialized = true;\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\ttodoBadgeManager.clearBadge();\n\t\t\t\t\tthis.launchInitialized = true;\n\t\t\t\t}\n\t\t\t}).catch(e => {\n\t\t\t\ttodoBadgeManager.clearBadge();\n\t\t\t\tthis.launchInitialized = true;\n\t\t\t});\n\t\t}, 0);\n\t\t\t\n\t\t\t// 监听登录事件，登录后立即更新角标\n\t\t\tuni.$on('uni-id-pages-login-success', () => {\n\t\t\t\t// 登录成功后，先清除旧角标\n\t\t\t\ttodoBadgeManager.clearBadge();\n\t\t\t\t// 延迟执行，确保用户信息已完全更新\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\ttodoBadgeManager.getTodoCount();\n\t\t\t\t}, 1000); // 增加延迟到1秒\n\t\t\t});\n\t\t\t\n\t\t\t// 监听退出登录事件，退出后清除角标\n\t\t\tuni.$on('uni-id-pages-logout', () => {\n\t\t\t\t// 清除角标\n\t\t\t\ttodoBadgeManager.forceCleanBadge();\n\t\t\t});\n\t\t\t\n\t\t\t// 添加全局页面切换监听\n\t\t\tthis.setupPageChangeListener();\n\t\t\t\n\t\t\t// 配置全局组件懒加载\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.globalData.lazyLoadComponents = true;\n\t\t\t// #endif\n\t\t},\n\t\t\tonShow: function() {\n\t\tconst token = uni.getStorageSync('uni_id_token');\n\t\tif (token) {\n\t\t\t// 如果启动初始化还未完成，等待更长时间\n\t\t\tif (!this.launchInitialized) {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.performOnShowUpdate();\n\t\t\t\t}, 1000);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.performOnShowUpdate();\n\t\t}\n\t},\n\t\tonHide: function() {\n\t\t\t// 应用隐藏时的逻辑\n\t\t},\n\t\tbeforeDestroy() {\n\t\t\t// 移除token相关事件监听\n\t\t\tuni.$off('token-invalid');\n\t\t\tuni.$off('token-expired');\n\t\t\t// 移除页面切换监听\n\t\t\tclearInterval(this.pageChangeInterval);\n\t\t},\n\t\t// 处理后台获取的位置数据\n\t\tonLaunchFromBGFetch: function(e) {\n\t\t\ttry {\n\t\t\t\tif (e && e.fetchType === 'location') {\n\t\t\t\t\tconst locationData = e.fetchedData || {};\n\t\t\t\t\tuni.setStorageSync('lastBackgroundLocation', locationData);\n\t\t\t\t\t\n\t\t\t\t\t// 可以在这里添加上传轨迹点到服务器的逻辑\n\t\t\t\t\t// 例如记录巡视人员的行动轨迹\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('处理后台数据错误:', error);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 检查是否为TabBar页面的工具函数\n\t\t\tisTabBarPage(route) {\n\t\t\t\treturn TAB_BAR_PAGES.includes(route);\n\t\t\t},\n\t\t\t\n\t\t// 统一处理token过期\n\t\thandleTokenExpired(source = 'unknown') {\n\t\t\t// 清除所有登录相关存储\n\t\t\tuni.removeStorageSync('uni_id_token');\n\t\t\tuni.removeStorageSync('uni_id_token_expired');\n\t\t\tuni.removeStorageSync('uni_id_user');\n\t\t\tuni.removeStorageSync('uni-id-pages-userInfo');\n\t\t\tuni.removeStorageSync(getCacheKey(CACHE_KEYS.USER_ROLE));\n\t\t\t\n\t\t\t// 清除角标\n\t\t\ttodoBadgeManager.clearBadge();\n\t\t\t\t\n\t\t\t\t// 获取当前页面路径\n\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\tconst currentPage = pages[pages.length - 1];\n\t\t\t\tconst currentRoute = currentPage ? currentPage.route : '';\n\t\t\t\t\n\t\t\t\t// 如果是应用启动期间（页面信息不完整），默认按TabBar页面处理，避免误跳转\n\t\t\t\tconst isAppLaunching = !currentRoute || pages.length === 0;\n\t\t\t\tconst isTabBarPage = isAppLaunching || this.isTabBarPage(currentRoute);\n\t\t\t\t\n\t\t\t\tif (isTabBarPage) {\n\t\t\t\t\t// 如果在TabBar页面（包括启动期间），只显示提示，不跳转\n\t\t\t\t\t// 用户中心页面需要发送事件更新UI状态\n\t\t\t\t\tif (currentRoute === 'pages/ucenter/ucenter') {\n\t\t\t\t\t\tuni.$emit('force-logout-ui-update');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 显示提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '登录已过期，请重新登录',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 在非TabBar页面，跳转到登录页\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '登录已过期，即将跳转登录页',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd',\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/login/login-withpwd'\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t});\n\t\t\t\t\t}, 2000);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 初始化角标系统\n\t\t\tinitializeBadge() {\n\t\t\t\tconst token = uni.getStorageSync('uni_id_token');\n\t\t\t\tif (token) {\n\t\t\t\t\t// 立即尝试更新角标\n\t\t\t\t\ttodoBadgeManager.updateTodoCountImmediately();\n\n\t\t\t\t\t// 启动智能重试机制（只在初始化时启动一次）\n\t\t\t\t\tthis.startSmartRetry();\n\t\t\t\t} else {\n\t\t\t\t\t// 确保未登录状态下清除角标\n\t\t\t\t\ttodoBadgeManager.clearBadge();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 智能重试机制 - 只在真正需要时才重试\n\t\t\tstartSmartRetry() {\n\t\t\t\t// 3秒后检查角标是否成功显示\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tconst token = uni.getStorageSync('uni_id_token');\n\t\t\t\t\tif (token && todoBadgeManager.todoCount === 0 && !todoBadgeManager.isProcessing) {\n\t\t\t\t\t\t// 检查用户是否真的有管理权限\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst currentUserInfo = uniCloud.getCurrentUserInfo();\n\t\t\t\t\t\t\tif (currentUserInfo?.role && currentUserInfo.role.some(role =>\n\t\t\t\t\t\t\t\t['supervisor', 'PM', 'GM', 'admin'].includes(role)\n\t\t\t\t\t\t\t)) {\n\t\t\t\t\t\t\t\ttodoBadgeManager.forceRefresh();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\t\t\t\t\t// 如果获取用户信息失败，6秒后再试一次\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tif (token && todoBadgeManager.todoCount === 0) {\n\t\t\t\t\t\t\ttodoBadgeManager.forceRefresh();\n\t\t\t\t\t\t}\n\t\t\t\t\t}, 3000);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}, 3000);\n\t\t\t},\n\n\t\t\t// 设置页面切换监听\n\t\t\tsetupPageChangeListener() {\n\t\t\t\t// 获取当前页面路由\n\t\t\t\tlet lastRoute = '';\n\n\t\t\t\t// 监听页面切换\n\t\t\t\tconst updateInterval = setInterval(() => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\t\t\tif (pages.length > 0) {\n\t\t\t\t\t\t\tconst currentPage = pages[pages.length - 1];\n\t\t\t\t\t\t\tconst currentRoute = currentPage.route || '';\n\n\t\t\t\t\t\t\t// 如果页面发生变化\n\t\t\t\t\t\t\tif (currentRoute && currentRoute !== lastRoute) {\n\t\t\t\t\t\t\t\tlastRoute = currentRoute;\n\n\t\t\t\t\t\t\t\t// 检查是否切换到TabBar页面\n\t\t\t\t\t\t\t\tif (this.isTabBarPage(currentRoute)) {\n\t\t\t\t\t\t\t\t\tconst token = uni.getStorageSync('uni_id_token');\n\t\t\t\t\t\t\t\t\tif (token) {\n\t\t\t\t\t\t\t\t\t\t// 延迟一点执行，确保页面完全加载\n\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\t\ttodoBadgeManager.updateTodoCountImmediately();\n\t\t\t\t\t\t\t\t\t\t\t// 强制同步角标状态，确保显示正确\n\t\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\t\t\ttodoBadgeManager.forceSyncBadge();\n\t\t\t\t\t\t\t\t\t\t\t}, 100);\n\t\t\t\t\t\t\t\t\t\t}, 100);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t// 忽略页面切换监听错误\n\t\t\t\t\t}\n\t\t\t\t}, 1000); // 优化检查频率为1000毫秒，减少CPU占用\n\n\t\t\t\t// 存储interval ID，以便在需要时清除\n\t\t\t\tthis.pageChangeInterval = updateInterval;\n\t\t\t},\n\n\t\t// 执行onShow时的更新\n\t\tperformOnShowUpdate() {\n\t\t\t// 应用显示时立即更新角标，使用立即更新方法确保获取最新数据\n\t\t\ttodoBadgeManager.updateTodoCountImmediately();\n\t\t},\n\n\t\t},\n\t\tglobalData: {\n\t\t\tlazyLoadComponents: false,\n\t\t\tneedRefreshTaskList: false,  // 是否需要刷新任务列表\n\t\t\trefreshTaskDate: null,       // 需要刷新的任务日期\n\t\t\trefreshShiftList: false,     // 是否需要刷新班次列表\n\t\t\tlastTaskOperation: {         // 最后一次任务操作\n\t\t\t\ttype: null,              // 操作类型：add, update, delete\n\t\t\t\ttimestamp: 0,            // 操作时间戳\n\t\t\t\ttaskId: null             // 操作的任务ID\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/* 全局样式 */\n\t@font-face {\n\t\tfont-family: 'iconfont';\n\t\tsrc: url('/static/fonts/iconfont.ttf') format('truetype');\n\t\tfont-weight: normal;\n\t\tfont-style: normal;\n\t}\n\t\n\t.iconfont {\n\t\tfont-family: 'iconfont' !important;\n\t\tfont-size: 16px;\n\t\tfont-style: normal;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752558449871\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
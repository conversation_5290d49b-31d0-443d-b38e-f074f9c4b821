{"version": 3, "sources": ["webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?52eb", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?1dc0", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?d750", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?28e8", "uni-app:///uni_modules/sp-editor/components/sp-editor/sp-editor.vue", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?a94e", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/sp-editor.vue?9354"], "names": ["components", "ColorPicker", "LinkEdit", "FabTool", "props", "editorId", "type", "default", "placeholder", "readOnly", "maxlength", "toolbarConfig", "keys", "excludeKeys", "iconSize", "iconColumns", "watch", "deep", "immediate", "handler", "data", "formats", "curFab", "fabXY", "textColor", "backgroundColor", "curColor", "defaultColor", "r", "g", "b", "a", "toolbarList", "toolbarAllList", "fabTools", "header", "title", "name", "value", "icon", "fontFamily", "fontSize", "align", "lineHeight", "space", "methods", "onEditorReady", "uni", "createSelectorQuery", "in", "select", "context", "exec", "undo", "redo", "format", "fabTap", "fabTapSub", "showPicker", "confirmColor", "onStatusChange", "insertDivider", "clear", "content", "success", "removeFormat", "showCancel", "insertDate", "text", "insertLink", "confirmLink", "html", "insertImage", "mediaType", "fail", "insertVideo", "sourceType", "onEditorInput", "confirmText", "exportHtml", "eLongpress"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvGA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC0XvnB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,gBAEA;EACAA;IACAC;IACAC;IACAC;EACA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;QACA;UACAK;UAAA;UACAC;UAAA;;UAMAC;UAAA;UACAC;QAEA;MACA;IACA;EACA;;EACAC;IACAL;MACAM;MACAC;MACAC;QAAA;QACA;AACA;AACA;AACA;AACA;QACA;UACA;QACA;UAAA;UACA,mBACA,8IACA;YAAA;UAAA,KACA;QACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;MAAA;MACAjB;MAAA;MACAC;MAAA;MACAiB;MACAC,iBACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MACA;MACA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MACA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MACA;MACA;MACA;MAAA;MACA;MACA;MACA;MAAA;MACA;MAAA;MACA;MACA;MACA;MAAA,CACA;;MACAC;QACAC,SACA;UAAAC;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,EACA;QACAC,aACA;UAAAJ;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,EACA;QACAE,WACA;UAAAL;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,EACA;QACAG,QACA;UAAAN;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,EACA;QACAI,aACA;UAAAP;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,EACA;QACA;QACAK,QACA;UAAAR;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAH;UAAAC;UAAAC;UAAAC;QAAA;MAEA;IACA;EACA;EACAM;IACAC;MAAA;MACAC,IACAC,sBACAC,SACAC,4BACAC;QACA;QACA;MACA,GACAC;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAAlB;QAAAC;MACA;MACA;QACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;MAAA;IAEA;IACA;IACAkB;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;UACA,qCACA,oDACA;YAAA9B;YAAAC;YAAAC;YAAAC;UAAA;UACA;QACA;UACA,2CACA,0DACA;YAAAH;YAAAC;YAAAC;YAAAC;UAAA;UACA;MAAA;MAEA;IACA;IACA4B;MACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;MAAA;IAEA;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACAf;QACAX;QACA2B;QACAC;UAAA;UACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAlB;QACAX;QACA2B;QACAG;QACAF;UAAA;UACA;YACA;UACA;QACA;MACA;IACA;IACAG;MACA;MACA;MACA;QAAAC;MAAA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACA;QACA;UACAN;YACA;cAAAO;cAAAH;YAAA;UACA;QACA;MACA;MACA;IACA;IACAI;MAAA;MAmBA;MACAzB;QACA;QACA0B;QACAT;UACA;UACA;UACA;QACA;QACAU;UACA3B;YACAX;YACAG;UACA;QACA;MACA;IAEA;IACAoC;MAAA;MACA5B;QACA6B;QACAZ;UACA;UACA;UACA;QACA;QACAU;UACA3B;YACAX;YACAG;UACA;QACA;MACA;IACA;IACAsC;MAAA;MACA;MACA;MACA;MACA;QAAAN;QAAAH;MACA;MACA;MAEA;MACA;MACA;QACArB;UACAgB;UACAe;UACAZ;UACAF;YACA;cAAAO;cAAAH;YAAA;UACA;QACA;MACA;QACA;UAAAG;UAAAH;QAAA;MACA;IACA;IACA;IACAW;MAAA;MACA;QACAf;UACA;QACA;MACA;IACA;IACAgB;MACA;AACA;AACA;AACA;IAHA;EAKA;AACA;AAAA,4B;;;;;;;;;;;;;AC1wBA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,onCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/sp-editor/components/sp-editor/sp-editor.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./sp-editor.vue?vue&type=template&id=938741ae&\"\nvar renderjs\nimport script from \"./sp-editor.vue?vue&type=script&lang=js&\"\nexport * from \"./sp-editor.vue?vue&type=script&lang=js&\"\nimport style0 from \"./sp-editor.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/sp-editor/components/sp-editor/sp-editor.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sp-editor.vue?vue&type=template&id=938741ae&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.readOnly ? _vm.toolbarList.includes(\"header\") : null\n  var l0 = _vm.__map(_vm.fabTools.header, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var g1 = _vm.toolbarList.includes(item.name)\n    return {\n      $orig: $orig,\n      g1: g1,\n    }\n  })\n  var g2 = !_vm.readOnly ? _vm.toolbarList.includes(\"bold\") : null\n  var g3 = !_vm.readOnly ? _vm.toolbarList.includes(\"italic\") : null\n  var g4 = !_vm.readOnly ? _vm.toolbarList.includes(\"underline\") : null\n  var g5 = !_vm.readOnly ? _vm.toolbarList.includes(\"strike\") : null\n  var g6 = !_vm.readOnly ? _vm.toolbarList.includes(\"align\") : null\n  var l1 = _vm.__map(_vm.fabTools.align, function (item, __i1__) {\n    var $orig = _vm.__get_orig(item)\n    var g7 = _vm.toolbarList.includes(item.name)\n    return {\n      $orig: $orig,\n      g7: g7,\n    }\n  })\n  var g8 = !_vm.readOnly ? _vm.toolbarList.includes(\"lineHeight\") : null\n  var g9 = !_vm.readOnly ? _vm.toolbarList.includes(\"letterSpacing\") : null\n  var g10 = !_vm.readOnly ? _vm.toolbarList.includes(\"marginTop\") : null\n  var g11 = !_vm.readOnly ? _vm.toolbarList.includes(\"marginBottom\") : null\n  var g12 = !_vm.readOnly ? _vm.toolbarList.includes(\"fontFamily\") : null\n  var g13 = !_vm.readOnly ? _vm.toolbarList.includes(\"fontSize\") : null\n  var g14 = !_vm.readOnly ? _vm.toolbarList.includes(\"color\") : null\n  var g15 = !_vm.readOnly ? _vm.toolbarList.includes(\"backgroundColor\") : null\n  var g16 = !_vm.readOnly ? _vm.toolbarList.includes(\"date\") : null\n  var g17 = !_vm.readOnly ? _vm.toolbarList.includes(\"listCheck\") : null\n  var g18 = !_vm.readOnly ? _vm.toolbarList.includes(\"listOrdered\") : null\n  var g19 = !_vm.readOnly ? _vm.toolbarList.includes(\"listBullet\") : null\n  var g20 = !_vm.readOnly ? _vm.toolbarList.includes(\"divider\") : null\n  var g21 = !_vm.readOnly ? _vm.toolbarList.includes(\"indentDec\") : null\n  var g22 = !_vm.readOnly ? _vm.toolbarList.includes(\"indentInc\") : null\n  var g23 = !_vm.readOnly ? _vm.toolbarList.includes(\"scriptSub\") : null\n  var g24 = !_vm.readOnly ? _vm.toolbarList.includes(\"scriptSuper\") : null\n  var g25 = !_vm.readOnly ? _vm.toolbarList.includes(\"direction\") : null\n  var g26 = !_vm.readOnly ? _vm.toolbarList.includes(\"image\") : null\n  var g27 = !_vm.readOnly ? _vm.toolbarList.includes(\"video\") : null\n  var g28 = !_vm.readOnly ? _vm.toolbarList.includes(\"link\") : null\n  var g29 = !_vm.readOnly ? _vm.toolbarList.includes(\"undo\") : null\n  var g30 = !_vm.readOnly ? _vm.toolbarList.includes(\"redo\") : null\n  var g31 = !_vm.readOnly ? _vm.toolbarList.includes(\"removeFormat\") : null\n  var g32 = !_vm.readOnly ? _vm.toolbarList.includes(\"clear\") : null\n  var g33 = !_vm.readOnly ? _vm.toolbarList.includes(\"export\") : null\n  var g34 =\n    _vm.toolbarList.includes(\"color\") ||\n    _vm.toolbarList.includes(\"backgroundColor\")\n  var g35 = _vm.toolbarList.includes(\"link\") && !_vm.readOnly\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        l1: l1,\n        g8: g8,\n        g9: g9,\n        g10: g10,\n        g11: g11,\n        g12: g12,\n        g13: g13,\n        g14: g14,\n        g15: g15,\n        g16: g16,\n        g17: g17,\n        g18: g18,\n        g19: g19,\n        g20: g20,\n        g21: g21,\n        g22: g22,\n        g23: g23,\n        g24: g24,\n        g25: g25,\n        g26: g26,\n        g27: g27,\n        g28: g28,\n        g29: g29,\n        g30: g30,\n        g31: g31,\n        g32: g32,\n        g33: g33,\n        g34: g34,\n        g35: g35,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sp-editor.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sp-editor.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"sp-editor\" :style=\"{ '--icon-size': iconSize, '--icon-columns': iconColumns }\">\n\t\t<view class=\"sp-editor-toolbar\" v-if=\"!readOnly\" @tap=\"format\">\n\t\t\t<!-- 标题栏 -->\n\t\t\t<fab-tool v-if=\"toolbarList.includes('header')\" :visible=\"curFab == 'header'\">\n\t\t\t\t<view\n\t\t\t\t\t:class=\"formats.header ? 'ql-active' : ''\"\n\t\t\t\t\tclass=\"iconfont icon-header\"\n\t\t\t\t\ttitle=\"标题\"\n\t\t\t\t\tdata-name=\"header\"\n\t\t\t\t\************=\"fabTap('header')\"\n\t\t\t\t></view>\n\t\t\t\t<template #content>\n\t\t\t\t\t<view class=\"fab-tools\" @click.stop=\"fabTapSub($event, 'header')\">\n\t\t\t\t\t\t<view v-for=\"item in fabTools.header\" :key=\"item.value\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tv-if=\"toolbarList.includes(item.name)\"\n\t\t\t\t\t\t\t\tclass=\"fab-sub\"\n\t\t\t\t\t\t\t\t:class=\"[formats.header === item.value ? 'ql-active' : '', item.icon ? 'iconfont' : '', item.icon]\"\n\t\t\t\t\t\t\t\t:title=\"item.title\"\n\t\t\t\t\t\t\t\tdata-name=\"header\"\n\t\t\t\t\t\t\t\t:data-value=\"item.value\"\n\t\t\t\t\t\t\t></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t</fab-tool>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('bold')\"\n\t\t\t\t:class=\"formats.bold ? 'ql-active' : ''\"\n\t\t\t\tclass=\"iconfont icon-zitijiacu\"\n\t\t\t\ttitle=\"加粗\"\n\t\t\t\tdata-name=\"bold\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('italic')\"\n\t\t\t\t:class=\"formats.italic ? 'ql-active' : ''\"\n\t\t\t\tclass=\"iconfont icon-zitixieti\"\n\t\t\t\ttitle=\"斜体\"\n\t\t\t\tdata-name=\"italic\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('underline')\"\n\t\t\t\t:class=\"formats.underline ? 'ql-active' : ''\"\n\t\t\t\tclass=\"iconfont icon-zitixiahuaxian\"\n\t\t\t\ttitle=\"下划线\"\n\t\t\t\tdata-name=\"underline\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('strike')\"\n\t\t\t\t:class=\"formats.strike ? 'ql-active' : ''\"\n\t\t\t\tclass=\"iconfont icon-zitishanchuxian\"\n\t\t\t\ttitle=\"删除线\"\n\t\t\t\tdata-name=\"strike\"\n\t\t\t></view>\n\t\t\t<!-- 对齐方式 -->\n\t\t\t<fab-tool v-if=\"toolbarList.includes('align')\" :visible=\"curFab == 'align'\">\n\t\t\t\t<view\n\t\t\t\t\t:class=\"formats.align ? 'ql-active' : ''\"\n\t\t\t\t\tclass=\"iconfont icon-zuoyouduiqi\"\n\t\t\t\t\ttitle=\"对齐方式\"\n\t\t\t\t\tdata-name=\"align\"\n\t\t\t\t\************=\"fabTap('align')\"\n\t\t\t\t></view>\n\t\t\t\t<template #content>\n\t\t\t\t\t<view class=\"fab-tools\" @click.stop=\"fabTapSub($event, 'align')\">\n\t\t\t\t\t\t<view v-for=\"item in fabTools.align\" :key=\"item.value\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tv-if=\"toolbarList.includes(item.name)\"\n\t\t\t\t\t\t\t\tclass=\"fab-sub\"\n\t\t\t\t\t\t\t\t:class=\"[formats.align === item.value ? 'ql-active' : '', item.icon ? 'iconfont' : '', item.icon]\"\n\t\t\t\t\t\t\t\t:title=\"item.title\"\n\t\t\t\t\t\t\t\tdata-name=\"align\"\n\t\t\t\t\t\t\t\t:data-value=\"item.value\"\n\t\t\t\t\t\t\t></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t</fab-tool>\n\t\t\t<!-- 行间距 -->\n\t\t\t<fab-tool v-if=\"toolbarList.includes('lineHeight')\" :visible=\"curFab == 'lineHeight'\">\n\t\t\t\t<view\n\t\t\t\t\t:class=\"formats.lineHeight ? 'ql-active' : ''\"\n\t\t\t\t\tclass=\"iconfont icon-line-height\"\n\t\t\t\t\ttitle=\"行间距\"\n\t\t\t\t\tdata-name=\"lineHeight\"\n\t\t\t\t\************=\"fabTap('lineHeight')\"\n\t\t\t\t></view>\n\t\t\t\t<template #content>\n\t\t\t\t\t<view class=\"fab-tools\" @click.stop=\"fabTapSub($event, 'lineHeight')\">\n\t\t\t\t\t\t<view v-for=\"item in fabTools.lineHeight\" :key=\"item.value\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"fab-sub\"\n\t\t\t\t\t\t\t\t:class=\"[formats.lineHeight === item.value ? 'ql-active' : '', item.icon ? 'iconfont' : '', item.icon]\"\n\t\t\t\t\t\t\t\t:title=\"item.title\"\n\t\t\t\t\t\t\t\tdata-name=\"lineHeight\"\n\t\t\t\t\t\t\t\t:data-value=\"item.value\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{{ item.name }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t</fab-tool>\n\t\t\t<!-- 字间距 -->\n\t\t\t<fab-tool v-if=\"toolbarList.includes('letterSpacing')\" :visible=\"curFab == 'letterSpacing'\">\n\t\t\t\t<view\n\t\t\t\t\t:class=\"formats.letterSpacing ? 'ql-active' : ''\"\n\t\t\t\t\tclass=\"iconfont icon-Character-Spacing\"\n\t\t\t\t\ttitle=\"字间距\"\n\t\t\t\t\tdata-name=\"letterSpacing\"\n\t\t\t\t\************=\"fabTap('letterSpacing')\"\n\t\t\t\t></view>\n\t\t\t\t<template #content>\n\t\t\t\t\t<view class=\"fab-tools\" @click.stop=\"fabTapSub($event, 'letterSpacing')\">\n\t\t\t\t\t\t<view v-for=\"item in fabTools.space\" :key=\"item.value\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"fab-sub\"\n\t\t\t\t\t\t\t\t:class=\"[\n\t\t\t\t\t\t\t\t\tformats.letterSpacing === item.value ? 'ql-active' : '',\n\t\t\t\t\t\t\t\t\titem.icon ? 'iconfont' : '',\n\t\t\t\t\t\t\t\t\titem.icon\n\t\t\t\t\t\t\t\t]\"\n\t\t\t\t\t\t\t\t:title=\"item.title\"\n\t\t\t\t\t\t\t\tdata-name=\"letterSpacing\"\n\t\t\t\t\t\t\t\t:data-value=\"item.value\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{{ item.name }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t</fab-tool>\n\t\t\t<!-- 段前距 -->\n\t\t\t<fab-tool v-if=\"toolbarList.includes('marginTop')\" :visible=\"curFab == 'marginTop'\">\n\t\t\t\t<view\n\t\t\t\t\t:class=\"formats.marginTop ? 'ql-active' : ''\"\n\t\t\t\t\tclass=\"iconfont icon-722bianjiqi_duanqianju\"\n\t\t\t\t\ttitle=\"段前距\"\n\t\t\t\t\tdata-name=\"marginTop\"\n\t\t\t\t\************=\"fabTap('marginTop')\"\n\t\t\t\t></view>\n\t\t\t\t<template #content>\n\t\t\t\t\t<view class=\"fab-tools\" @click.stop=\"fabTapSub($event, 'marginTop')\">\n\t\t\t\t\t\t<view v-for=\"item in fabTools.space\" :key=\"item.value\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"fab-sub\"\n\t\t\t\t\t\t\t\t:class=\"[formats.marginTop === item.value ? 'ql-active' : '', item.icon ? 'iconfont' : '', item.icon]\"\n\t\t\t\t\t\t\t\t:title=\"item.title\"\n\t\t\t\t\t\t\t\tdata-name=\"marginTop\"\n\t\t\t\t\t\t\t\t:data-value=\"item.value\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{{ item.name }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t</fab-tool>\n\t\t\t<!-- 段后距 -->\n\t\t\t<fab-tool v-if=\"toolbarList.includes('marginBottom')\" :visible=\"curFab == 'marginBottom'\">\n\t\t\t\t<view\n\t\t\t\t\t:class=\"formats.marginBottom ? 'ql-active' : ''\"\n\t\t\t\t\tclass=\"iconfont icon-723bianjiqi_duanhouju\"\n\t\t\t\t\ttitle=\"段后距\"\n\t\t\t\t\tdata-name=\"marginBottom\"\n\t\t\t\t\************=\"fabTap('marginBottom')\"\n\t\t\t\t></view>\n\t\t\t\t<template #content>\n\t\t\t\t\t<view class=\"fab-tools\" @click.stop=\"fabTapSub($event, 'marginBottom')\">\n\t\t\t\t\t\t<view v-for=\"item in fabTools.space\" :key=\"item.value\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"fab-sub\"\n\t\t\t\t\t\t\t\t:class=\"[\n\t\t\t\t\t\t\t\t\tformats.marginBottom === item.value ? 'ql-active' : '',\n\t\t\t\t\t\t\t\t\titem.icon ? 'iconfont' : '',\n\t\t\t\t\t\t\t\t\titem.icon\n\t\t\t\t\t\t\t\t]\"\n\t\t\t\t\t\t\t\t:title=\"item.title\"\n\t\t\t\t\t\t\t\tdata-name=\"marginBottom\"\n\t\t\t\t\t\t\t\t:data-value=\"item.value\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{{ item.name }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t</fab-tool>\n\t\t\t<!-- 字体栏 -->\n\t\t\t<fab-tool v-if=\"toolbarList.includes('fontFamily')\" :visible=\"curFab == 'fontFamily'\">\n\t\t\t\t<view\n\t\t\t\t\t:class=\"formats.fontFamily ? 'ql-active' : ''\"\n\t\t\t\t\tclass=\"iconfont icon-font\"\n\t\t\t\t\ttitle=\"字体\"\n\t\t\t\t\tdata-name=\"fontFamily\"\n\t\t\t\t\************=\"fabTap('fontFamily')\"\n\t\t\t\t></view>\n\t\t\t\t<template #content>\n\t\t\t\t\t<view class=\"fab-tools\" @click.stop=\"fabTapSub($event, 'fontFamily')\">\n\t\t\t\t\t\t<view v-for=\"item in fabTools.fontFamily\" :key=\"item.value\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"fab-sub\"\n\t\t\t\t\t\t\t\t:class=\"[formats.fontFamily === item.value ? 'ql-active' : '', item.icon ? 'iconfont' : '', item.icon]\"\n\t\t\t\t\t\t\t\t:title=\"item.title\"\n\t\t\t\t\t\t\t\tdata-name=\"fontFamily\"\n\t\t\t\t\t\t\t\t:data-value=\"item.value\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{{ item.name }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t</fab-tool>\n\t\t\t<!-- 字体大小栏 -->\n\t\t\t<fab-tool v-if=\"toolbarList.includes('fontSize')\" :visible=\"curFab == 'fontSize'\">\n\t\t\t\t<view\n\t\t\t\t\t:class=\"formats.fontSize ? 'ql-active' : ''\"\n\t\t\t\t\tclass=\"iconfont icon-fontsize\"\n\t\t\t\t\ttitle=\"字号\"\n\t\t\t\t\tdata-name=\"fontSize\"\n\t\t\t\t\************=\"fabTap('fontSize')\"\n\t\t\t\t></view>\n\t\t\t\t<template #content>\n\t\t\t\t\t<view class=\"fab-tools\" @click.stop=\"fabTapSub($event, 'fontSize')\">\n\t\t\t\t\t\t<view v-for=\"item in fabTools.fontSize\" :key=\"item.value\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"fab-sub\"\n\t\t\t\t\t\t\t\t:class=\"[formats.fontSize === item.value ? 'ql-active' : '', item.icon ? 'iconfont' : '', item.icon]\"\n\t\t\t\t\t\t\t\t:title=\"item.title\"\n\t\t\t\t\t\t\t\tdata-name=\"fontSize\"\n\t\t\t\t\t\t\t\t:data-value=\"item.value\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{{ item.name }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t</fab-tool>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('color')\"\n\t\t\t\t:style=\"{ color: formats.color ? textColor : 'initial' }\"\n\t\t\t\tclass=\"iconfont icon-text_color\"\n\t\t\t\ttitle=\"文字颜色\"\n\t\t\t\tdata-name=\"color\"\n\t\t\t\t:data-value=\"textColor\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('backgroundColor')\"\n\t\t\t\t:style=\"{ color: formats.backgroundColor ? backgroundColor : 'initial' }\"\n\t\t\t\tclass=\"iconfont icon-fontbgcolor\"\n\t\t\t\ttitle=\"背景颜色\"\n\t\t\t\tdata-name=\"backgroundColor\"\n\t\t\t\t:data-value=\"backgroundColor\"\n\t\t\t></view>\n\t\t\t<view v-if=\"toolbarList.includes('date')\" class=\"iconfont icon-date\" title=\"日期\" @tap=\"insertDate\"></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('listCheck')\"\n\t\t\t\tclass=\"iconfont icon--checklist\"\n\t\t\t\ttitle=\"待办\"\n\t\t\t\tdata-name=\"list\"\n\t\t\t\tdata-value=\"check\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('listOrdered')\"\n\t\t\t\t:class=\"formats.list === 'ordered' ? 'ql-active' : ''\"\n\t\t\t\tclass=\"iconfont icon-youxupailie\"\n\t\t\t\ttitle=\"有序列表\"\n\t\t\t\tdata-name=\"list\"\n\t\t\t\tdata-value=\"ordered\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('listBullet')\"\n\t\t\t\t:class=\"formats.list === 'bullet' ? 'ql-active' : ''\"\n\t\t\t\tclass=\"iconfont icon-wuxupailie\"\n\t\t\t\ttitle=\"无序列表\"\n\t\t\t\tdata-name=\"list\"\n\t\t\t\tdata-value=\"bullet\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('divider')\"\n\t\t\t\tclass=\"iconfont icon-fengexian\"\n\t\t\t\ttitle=\"分割线\"\n\t\t\t\t@click=\"insertDivider\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('indentDec')\"\n\t\t\t\tclass=\"iconfont icon-outdent\"\n\t\t\t\ttitle=\"减少缩进\"\n\t\t\t\tdata-name=\"indent\"\n\t\t\t\tdata-value=\"-1\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('indentInc')\"\n\t\t\t\tclass=\"iconfont icon-indent\"\n\t\t\t\ttitle=\"增加缩进\"\n\t\t\t\tdata-name=\"indent\"\n\t\t\t\tdata-value=\"+1\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('scriptSub')\"\n\t\t\t\t:class=\"formats.script === 'sub' ? 'ql-active' : ''\"\n\t\t\t\tclass=\"iconfont icon-zitixiabiao\"\n\t\t\t\ttitle=\"下标\"\n\t\t\t\tdata-name=\"script\"\n\t\t\t\tdata-value=\"sub\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('scriptSuper')\"\n\t\t\t\t:class=\"formats.script === 'super' ? 'ql-active' : ''\"\n\t\t\t\tclass=\"iconfont icon-zitishangbiao\"\n\t\t\t\ttitle=\"上标\"\n\t\t\t\tdata-name=\"script\"\n\t\t\t\tdata-value=\"super\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('direction')\"\n\t\t\t\t:class=\"formats.direction === 'rtl' ? 'ql-active' : ''\"\n\t\t\t\tclass=\"iconfont icon-direction-rtl\"\n\t\t\t\ttitle=\"文本方向\"\n\t\t\t\tdata-name=\"direction\"\n\t\t\t\tdata-value=\"rtl\"\n\t\t\t></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('image')\"\n\t\t\t\tclass=\"iconfont icon-charutupian\"\n\t\t\t\ttitle=\"图片\"\n\t\t\t\t@tap=\"insertImage\"\n\t\t\t></view>\n\t\t\t<view v-if=\"toolbarList.includes('video')\" class=\"iconfont icon-video\" title=\"视频\" @tap=\"insertVideo\"></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('link')\"\n\t\t\t\tclass=\"iconfont icon-charulianjie\"\n\t\t\t\ttitle=\"超链接\"\n\t\t\t\t@tap=\"insertLink\"\n\t\t\t></view>\n\t\t\t<view v-if=\"toolbarList.includes('undo')\" class=\"iconfont icon-undo\" title=\"撤销\" @tap=\"undo\"></view>\n\t\t\t<view v-if=\"toolbarList.includes('redo')\" class=\"iconfont icon-redo\" title=\"重做\" @tap=\"redo\"></view>\n\t\t\t<view\n\t\t\t\tv-if=\"toolbarList.includes('removeFormat')\"\n\t\t\t\tclass=\"iconfont icon-clearedformat\"\n\t\t\t\ttitle=\"清除格式\"\n\t\t\t\t@tap=\"removeFormat\"\n\t\t\t></view>\n\t\t\t<view v-if=\"toolbarList.includes('clear')\" class=\"iconfont icon-shanchu\" title=\"清空\" @tap=\"clear\"></view>\n\t\t\t<view v-if=\"toolbarList.includes('export')\" class=\"iconfont icon-baocun\" title=\"导出\" @tap=\"exportHtml\"></view>\n\t\t</view>\n\n\t\t<!-- 自定义功能组件 -->\n\t\t<!-- 调色板 -->\n\t\t<color-picker\n\t\t\tv-if=\"toolbarList.includes('color') || toolbarList.includes('backgroundColor')\"\n\t\t\tref=\"colorPickerRef\"\n\t\t\t:color=\"defaultColor\"\n\t\t\t@confirm=\"confirmColor\"\n\t\t></color-picker>\n\t\t<!-- 添加链接的操作弹窗 -->\n\t\t<link-edit v-if=\"toolbarList.includes('link') && !readOnly\" ref=\"linkEditRef\" @confirm=\"confirmLink\"></link-edit>\n\t\t<view class=\"sp-editor-wrapper\" @longpress=\"eLongpress\">\n\t\t\t<editor\n\t\t\t\t:id=\"editorId\"\n\t\t\t\tclass=\"ql-editor editor-container\"\n\t\t\t\t:class=\"{ 'ql-image-overlay-none': readOnly }\"\n\t\t\t\tshow-img-size\n\t\t\t\tshow-img-toolbar\n\t\t\t\tshow-img-resize\n\t\t\t\t:placeholder=\"placeholder\"\n\t\t\t\t:read-only=\"readOnly\"\n\t\t\t\t@statuschange=\"onStatusChange\"\n\t\t\t\t@ready=\"onEditorReady\"\n\t\t\t\t@input=\"onEditorInput\"\n\t\t\t></editor>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport ColorPicker from './color-picker.vue'\nimport LinkEdit from './link-edit.vue'\nimport FabTool from './fab-tool.vue'\nimport { addLink, linkFlag } from '../../utils'\n\nexport default {\n\tcomponents: {\n\t\tColorPicker,\n\t\tLinkEdit,\n\t\tFabTool\n\t},\n\tprops: {\n\t\t// 编辑器id可传入，以便循环组件使用，防止id重复\n\t\teditorId: {\n\t\t\ttype: String,\n\t\t\tdefault: 'editor'\n\t\t},\n\t\tplaceholder: {\n\t\t\ttype: String,\n\t\t\tdefault: '写点什么吧 ~'\n\t\t},\n\t\t// 是否只读\n\t\treadOnly: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 最大字数限制，-1不限\n\t\tmaxlength: {\n\t\t\ttype: Number,\n\t\t\tdefault: -1\n\t\t},\n\t\t// 工具栏配置\n\t\ttoolbarConfig: {\n\t\t\ttype: Object,\n\t\t\tdefault: () => {\n\t\t\t\treturn {\n\t\t\t\t\tkeys: [], // 要显示的工具，优先级最大\n\t\t\t\t\texcludeKeys: [], // 除这些指定的工具外，其他都显示\n\t\t\t\t\t// #ifdef H5\n\t\t\t\t\ticonSize: '20px', // H5端工具栏字体大小\n\t\t\t\t\ticonColumns: 12, // H5端工具栏列数\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\ticonSize: '18px', // 小程序端工具栏字体大小\n\t\t\t\t\ticonColumns: 10 // 小程序端工具栏列数\n\t\t\t\t\t// #endif\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\twatch: {\n\t\ttoolbarConfig: {\n\t\t\tdeep: true,\n\t\t\timmediate: true,\n\t\t\thandler(newToolbar) {\n\t\t\t\t/**\n\t\t\t\t * 若工具栏配置中keys存在，则以keys为准\n\t\t\t\t * 否则以excludeKeys向toolbarAllList中排查\n\t\t\t\t * 若keys与excludeKeys皆为空，则以toolbarAllList为准\n\t\t\t\t */\n\t\t\t\tif (newToolbar.keys?.length > 0) {\n\t\t\t\t\tthis.toolbarList = newToolbar.keys\n\t\t\t\t} else {\n\t\t\t\t\tthis.toolbarList =\n\t\t\t\t\t\tnewToolbar.excludeKeys?.length > 0\n\t\t\t\t\t\t\t? this.toolbarAllList.filter((item) => !newToolbar.excludeKeys.includes(item))\n\t\t\t\t\t\t\t: this.toolbarAllList\n\t\t\t\t}\n\t\t\t\tthis.iconSize = newToolbar.iconSize || '18px'\n\t\t\t\tthis.iconColumns = newToolbar.iconColumns || 10\n\t\t\t}\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tformats: {},\n\t\t\tcurFab: '', // 当前悬浮工具栏\n\t\t\tfabXY: {},\n\t\t\ttextColor: '',\n\t\t\tbackgroundColor: '',\n\t\t\tcurColor: '',\n\t\t\tdefaultColor: { r: 0, g: 0, b: 0, a: 1 }, // 调色板默认颜色\n\t\t\ticonSize: '20px', // 工具栏图标字体大小\n\t\t\ticonColumns: 10, // 工具栏列数\n\t\t\ttoolbarList: [],\n\t\t\ttoolbarAllList: [\n\t\t\t\t'header', // 标题\n\t\t\t\t'H1', // 一级标题\n\t\t\t\t'H2', // 二级标题\n\t\t\t\t'H3', // 三级标题\n\t\t\t\t'H4', // 四级标题\n\t\t\t\t'H5', // 五级标题\n\t\t\t\t'H6', // 六级标题\n\t\t\t\t'bold', // 加粗\n\t\t\t\t'italic', // 斜体\n\t\t\t\t'underline', // 下划线\n\t\t\t\t'strike', // 删除线\n\t\t\t\t'align', // 对齐方式\n\t\t\t\t'alignLeft', // 左对齐\n\t\t\t\t'alignCenter', // 居中对齐\n\t\t\t\t'alignRight', // 右对齐\n\t\t\t\t'alignJustify', // 两端对齐\n\t\t\t\t'lineHeight', // 行间距\n\t\t\t\t//'letterSpacing', // 字间距\n\t\t\t\t//'marginTop', // 段前距\n\t\t\t\t//'marginBottom', // 段后距\n\t\t\t\t'fontFamily', // 字体\n\t\t\t\t'fontSize', // 字号\n\t\t\t\t'color', // 文字颜色\n\t\t\t\t'backgroundColor', // 背景颜色\n\t\t\t\t//'date', // 日期\n\t\t\t\t//'listCheck', // 待办\n\t\t\t\t'listOrdered', // 有序列表\n\t\t\t\t'listBullet', // 无序列表\n\t\t\t\t'indentInc', // 增加缩进\n\t\t\t\t'indentDec', // 减少缩进\n\t\t\t\t'divider', // 分割线\n\t\t\t\t//'scriptSub', // 下标\n\t\t\t\t//'scriptSuper', // 上标\n\t\t\t\t//'direction', // 文本方向\n\t\t\t\t'image', // 图片\n\t\t\t\t//'video', // 视频\n\t\t\t\t//'link', // 超链接\n\t\t\t\t'undo', // 撤销\n\t\t\t\t'redo', // 重做\n\t\t\t\t//'removeFormat', // 清除格式\n\t\t\t\t'clear', // 清空\n\t\t\t\t//'export' // 导出\n\t\t\t],\n\t\t\tfabTools: {\n\t\t\t\theader: [\n\t\t\t\t\t{ title: '一级标题', name: 'H1', value: 1, icon: 'icon-format-header-1' },\n\t\t\t\t\t{ title: '二级标题', name: 'H2', value: 2, icon: 'icon-format-header-2' },\n\t\t\t\t\t{ title: '三级标题', name: 'H3', value: 3, icon: 'icon-format-header-3' },\n\t\t\t\t\t{ title: '四级标题', name: 'H4', value: 4, icon: 'icon-format-header-4' },\n\t\t\t\t\t{ title: '五级标题', name: 'H5', value: 5, icon: 'icon-format-header-5' },\n\t\t\t\t\t{ title: '六级标题', name: 'H6', value: 6, icon: 'icon-format-header-6' }\n\t\t\t\t],\n\t\t\t\tfontFamily: [\n\t\t\t\t\t{ title: '宋体', name: '宋', value: '宋体', icon: '' },\n\t\t\t\t\t{ title: '黑体', name: '黑', value: '黑体', icon: '' },\n\t\t\t\t\t{ title: '楷体', name: '楷', value: '楷体', icon: '' },\n\t\t\t\t\t{ title: '仿宋', name: '仿', value: '仿宋', icon: '' },\n\t\t\t\t\t{ title: '华文隶书', name: '隶', value: 'STLiti', icon: '' },\n\t\t\t\t\t{ title: '华文行楷', name: '行', value: 'STXingkai', icon: '' },\n\t\t\t\t\t{ title: '幼圆', name: '圆', value: 'YouYuan', icon: '' }\n\t\t\t\t],\n\t\t\t\tfontSize: [\n\t\t\t\t\t{ title: '12', name: '12', value: '12px', icon: '' },\n\t\t\t\t\t{ title: '14', name: '14', value: '14px', icon: '' },\n\t\t\t\t\t{ title: '16', name: '16', value: '16px', icon: '' },\n\t\t\t\t\t{ title: '18', name: '18', value: '18px', icon: '' },\n\t\t\t\t\t{ title: '20', name: '20', value: '20px', icon: '' },\n\t\t\t\t\t{ title: '22', name: '22', value: '22px', icon: '' },\n\t\t\t\t\t{ title: '24', name: '24', value: '24px', icon: '' }\n\t\t\t\t],\n\t\t\t\talign: [\n\t\t\t\t\t{ title: '左对齐', name: 'alignLeft', value: 'left', icon: 'icon-zuoduiqi' },\n\t\t\t\t\t{ title: '居中对齐', name: 'alignCenter', value: 'center', icon: 'icon-juzhongduiqi' },\n\t\t\t\t\t{ title: '右对齐', name: 'alignRight', value: 'right', icon: 'icon-youduiqi' },\n\t\t\t\t\t{ title: '两端对齐', name: 'alignJustify', value: 'justify', icon: 'icon-zuoyouduiqi' }\n\t\t\t\t],\n\t\t\t\tlineHeight: [\n\t\t\t\t\t{ title: '1倍', name: '1', value: '1', icon: '' },\n\t\t\t\t\t{ title: '1.5倍', name: '1.5', value: '1.5', icon: '' },\n\t\t\t\t\t{ title: '2倍', name: '2', value: '2', icon: '' },\n\t\t\t\t\t{ title: '2.5倍', name: '2.5', value: '2.5', icon: '' },\n\t\t\t\t\t{ title: '3倍', name: '3', value: '3', icon: '' }\n\t\t\t\t],\n\t\t\t\t// 字间距/段前距/段后距\n\t\t\t\tspace: [\n\t\t\t\t\t{ title: '0.5倍', name: '0.5', value: '0.5em', icon: '' },\n\t\t\t\t\t{ title: '1倍', name: '1', value: '1em', icon: '' },\n\t\t\t\t\t{ title: '1.5倍', name: '1.5', value: '1.5em', icon: '' },\n\t\t\t\t\t{ title: '2倍', name: '2', value: '2em', icon: '' },\n\t\t\t\t\t{ title: '2.5倍', name: '2.5', value: '2.5em', icon: '' },\n\t\t\t\t\t{ title: '3倍', name: '3', value: '3em', icon: '' }\n\t\t\t\t]\n\t\t\t}\n\t\t}\n\t},\n\tmethods: {\n\t\tonEditorReady() {\n\t\t\tuni\n\t\t\t\t.createSelectorQuery()\n\t\t\t\t.in(this)\n\t\t\t\t.select('#' + this.editorId)\n\t\t\t\t.context((res) => {\n\t\t\t\t\tthis.editorCtx = res.context\n\t\t\t\t\tthis.$emit('init', this.editorCtx, this.editorId)\n\t\t\t\t})\n\t\t\t\t.exec()\n\t\t},\n\t\tundo() {\n\t\t\tthis.editorCtx.undo()\n\t\t},\n\t\tredo() {\n\t\t\tthis.editorCtx.redo()\n\t\t},\n\t\tformat(e) {\n\t\t\tlet { name, value } = e.target.dataset\n\t\t\tif (!name) return\n\t\t\tswitch (name) {\n\t\t\t\tcase 'color':\n\t\t\t\tcase 'backgroundColor':\n\t\t\t\t\tthis.curColor = name\n\t\t\t\t\tthis.showPicker()\n\t\t\t\t\tbreak\n\t\t\t\tdefault:\n\t\t\t\t\tthis.editorCtx.format(name, value)\n\t\t\t\t\tbreak\n\t\t\t}\n\t\t},\n\t\t// 悬浮工具点击\n\t\tfabTap(fabType) {\n\t\t\tif (this.curFab != fabType) {\n\t\t\t\tthis.curFab = fabType\n\t\t\t} else {\n\t\t\t\tthis.curFab = ''\n\t\t\t}\n\t\t},\n\t\t// 悬浮工具子集点击\n\t\tfabTapSub(e, fabType) {\n\t\t\tthis.format(e)\n\t\t\tthis.fabTap(fabType)\n\t\t},\n\t\tshowPicker() {\n\t\t\tswitch (this.curColor) {\n\t\t\t\tcase 'color':\n\t\t\t\t\tthis.defaultColor = this.textColor\n\t\t\t\t\t\t? this.$refs.colorPickerRef.hex2Rgb(this.textColor)\n\t\t\t\t\t\t: { r: 0, g: 0, b: 0, a: 1 }\n\t\t\t\t\tbreak\n\t\t\t\tcase 'backgroundColor':\n\t\t\t\t\tthis.defaultColor = this.backgroundColor\n\t\t\t\t\t\t? this.$refs.colorPickerRef.hex2Rgb(this.backgroundColor)\n\t\t\t\t\t\t: { r: 0, g: 0, b: 0, a: 0 }\n\t\t\t\t\tbreak\n\t\t\t}\n\t\t\tthis.$refs.colorPickerRef.open()\n\t\t},\n\t\tconfirmColor(e) {\n\t\t\tswitch (this.curColor) {\n\t\t\t\tcase 'color':\n\t\t\t\t\tthis.textColor = e.hex\n\t\t\t\t\tthis.editorCtx.format('color', this.textColor)\n\t\t\t\t\tbreak\n\t\t\t\tcase 'backgroundColor':\n\t\t\t\t\tthis.backgroundColor = e.hex\n\t\t\t\t\tthis.editorCtx.format('backgroundColor', this.backgroundColor)\n\t\t\t\t\tbreak\n\t\t\t}\n\t\t},\n\t\tonStatusChange(e) {\n\t\t\tif (e.detail.color) {\n\t\t\t\tthis.textColor = e.detail.color\n\t\t\t}\n\t\t\tif (e.detail.backgroundColor) {\n\t\t\t\tthis.backgroundColor = e.detail.backgroundColor\n\t\t\t}\n\t\t\tthis.formats = e.detail\n\t\t},\n\t\tinsertDivider() {\n\t\t\tthis.editorCtx.insertDivider()\n\t\t},\n\t\tclear() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '清空编辑器',\n\t\t\t\tcontent: '确定清空编辑器吗？',\n\t\t\t\tsuccess: ({ confirm }) => {\n\t\t\t\t\tif (confirm) {\n\t\t\t\t\t\tthis.editorCtx.clear()\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tremoveFormat() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '文本格式化',\n\t\t\t\tcontent: '确定要清除所选择部分文本块格式吗？',\n\t\t\t\tshowCancel: true,\n\t\t\t\tsuccess: ({ confirm }) => {\n\t\t\t\t\tif (confirm) {\n\t\t\t\t\t\tthis.editorCtx.removeFormat()\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tinsertDate() {\n\t\t\tconst date = new Date()\n\t\t\tconst formatDate = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`\n\t\t\tthis.editorCtx.insertText({ text: formatDate })\n\t\t},\n\t\tinsertLink() {\n\t\t\tthis.$refs.linkEditRef.open()\n\t\t},\n\t\t/**\n\t\t * 确认添加链接\n\t\t * @param {Object} e { text: '链接描述', href: '链接地址' }\n\t\t */\n\t\tconfirmLink(e) {\n\t\t\tthis.$refs.linkEditRef.close()\n\t\t\taddLink(this.editorCtx, e, () => {\n\t\t\t\t// 修复添加超链接后，不触发input更新当前最新内容的bug，这里做一下手动更新\n\t\t\t\tthis.editorCtx.getContents({\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tthis.$emit('input', { html: res.html, text: res.text }, this.editorId)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t})\n\t\t\tthis.$emit('addLink', e, this.editorId)\n\t\t},\n\t\tinsertImage() {\n\t\t\t// #ifdef APP-PLUS || H5\n\t\t\tuni.chooseImage({\n\t\t\t\t// count: 1, // 默认9\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconst { tempFiles } = res\n\t\t\t\t\t// 将文件和编辑器示例抛出，由开发者自行上传和插入图片\n\t\t\t\t\tthis.$emit('upinImage', tempFiles, this.editorCtx, this.editorId)\n\t\t\t\t},\n\t\t\t\tfail() {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '未授权访问相册权限，请授权后使用',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t\t// #endif\n\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\t// 微信小程序从基础库 2.21.0 开始， wx.chooseImage 停止维护，请使用 uni.chooseMedia 代替。\n\t\t\tuni.chooseMedia({\n\t\t\t\t// count: 1, // 默认9\n\t\t\t\tmediaType: ['image'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t// 同上chooseImage处理\n\t\t\t\t\tconst { tempFiles } = res\n\t\t\t\t\tthis.$emit('upinImage', tempFiles, this.editorCtx, this.editorId)\n\t\t\t\t},\n\t\t\t\tfail() {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '未授权访问相册权限，请授权后使用',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t\t// #endif\n\t\t},\n\t\tinsertVideo() {\n\t\t\tuni.chooseVideo({\n\t\t\t\tsourceType: ['camera', 'album'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconst { tempFilePath } = res\n\t\t\t\t\t// 将文件和编辑器示例抛出，由开发者自行上传和插入图片\n\t\t\t\t\tthis.$emit('upinVideo', tempFilePath, this.editorCtx, this.editorId)\n\t\t\t\t},\n\t\t\t\tfail() {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '未授权访问媒体权限，请授权后使用',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tonEditorInput(e) {\n\t\t\t// 注意不要使用getContents获取html和text，会导致重复触发onStatusChange从而失去toolbar工具的高亮状态\n\t\t\t// 复制粘贴的时候detail会为空，此时应当直接return\n\t\t\tif (Object.keys(e.detail).length <= 0) return\n\t\t\tconst { html, text } = e.detail\n\t\t\t// 识别到标识立即return\n\t\t\tif (text.indexOf(linkFlag) !== -1) return\n\n\t\t\tconst maxlength = parseInt(this.maxlength)\n\t\t\tconst textStr = text.replace(/[ \\t\\r\\n]/g, '')\n\t\t\tif (textStr.length > maxlength && maxlength != -1) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\tcontent: `超过${maxlength}字数啦~`,\n\t\t\t\t\tconfirmText: '确定',\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tthis.$emit('overMax', { html, text }, this.editorId)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t} else {\n\t\t\t\tthis.$emit('input', { html, text }, this.editorId)\n\t\t\t}\n\t\t},\n\t\t// 导出\n\t\texportHtml() {\n\t\t\tthis.editorCtx.getContents({\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.$emit('exportHtml', res.html, this.editorId)\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\teLongpress() {\n\t\t\t/**\n\t\t\t * 微信小程序官方editor的长按事件有bug，需要重写覆盖，不需做任何逻辑，可见下面小程序社区问题链接\n\t\t\t * @tutorial https://developers.weixin.qq.com/community/develop/doc/000c04b3e1c1006f660065e4f61000\n\t\t\t */\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n@import '@/uni_modules/sp-editor/icons/editor-icon.css';\n@import '@/uni_modules/sp-editor/icons/custom-icon.css';\n\n.sp-editor {\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n\tposition: relative;\n}\n\n.sp-editor-toolbar {\n\tbox-sizing: border-box;\n\tpadding: calc(var(--icon-size) / 4) 0;\n\tborder-bottom: 1px solid #e4e4e4;\n\tfont-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;\n\tdisplay: grid;\n\tgrid-template-columns: repeat(var(--icon-columns), 1fr);\n}\n\n.iconfont {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 100%;\n\theight: calc(var(--icon-size) * 1.8);\n\tcursor: pointer;\n\tfont-size: var(--icon-size);\n}\n\n.sp-editor-wrapper {\n\tflex: 1;\n\toverflow: hidden;\n\tposition: relative;\n}\n\n.editor-container {\n\tpadding: 8rpx 16rpx;\n\tbox-sizing: border-box;\n\twidth: 100%;\n\theight: 100%;\n\tfont-size: 16px;\n\tline-height: 1.5;\n}\n\n.ql-image-overlay-none {\n\t::v-deep .ql-image-overlay {\n\t\tpointer-events: none;\n\t\topacity: 0;\n\t}\n}\n\n::v-deep .ql-editor.ql-blank::before {\n\tfont-style: normal;\n\tcolor: #cccccc;\n}\n\n::v-deep .ql-container {\n\tmin-height: unset;\n}\n\n.ql-active {\n\tcolor: #66ccff;\n}\n\n.fab-tools {\n\tdisplay: flex;\n\tpadding: 0 10rpx;\n\tbox-sizing: border-box;\n\n\t.fab-sub {\n\t\twidth: auto;\n\t\theight: auto;\n\t\tmargin: 10rpx;\n\t}\n}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sp-editor.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sp-editor.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752571670475\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
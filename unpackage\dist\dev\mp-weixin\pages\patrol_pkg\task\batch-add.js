require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/patrol_pkg/task/batch-add"],{

/***/ 419:
/*!************************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2Fpatrol_pkg%2Ftask%2Fbatch-add"} ***!
  \************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _batchAdd = _interopRequireDefault(__webpack_require__(/*! ./pages/patrol_pkg/task/batch-add.vue */ 420));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_batchAdd.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 420:
/*!***************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/batch-add.vue ***!
  \***************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _batch_add_vue_vue_type_template_id_8393c05e___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./batch-add.vue?vue&type=template&id=8393c05e& */ 421);
/* harmony import */ var _batch_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./batch-add.vue?vue&type=script&lang=js& */ 423);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _batch_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _batch_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _batch_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./batch-add.vue?vue&type=style&index=0&lang=scss& */ 425);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _batch_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _batch_add_vue_vue_type_template_id_8393c05e___WEBPACK_IMPORTED_MODULE_0__["render"],
  _batch_add_vue_vue_type_template_id_8393c05e___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _batch_add_vue_vue_type_template_id_8393c05e___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/patrol_pkg/task/batch-add.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 421:
/*!**********************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?vue&type=template&id=8393c05e& ***!
  \**********************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_template_id_8393c05e___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./batch-add.vue?vue&type=template&id=8393c05e& */ 422);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_template_id_8393c05e___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_template_id_8393c05e___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_template_id_8393c05e___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_template_id_8393c05e___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 422:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?vue&type=template&id=8393c05e& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 109))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.selectedDates.length
  var l0 = _vm.__map(_vm.calendarDays, function (day, index) {
    var $orig = _vm.__get_orig(day)
    var m0 = _vm.isDateSelected(day.fullDate)
    return {
      $orig: $orig,
      m0: m0,
    }
  })
  var g1 =
    _vm.selectedRoute &&
    _vm.selectedRoute.pointsDetail &&
    _vm.selectedRoute.pointsDetail.length > 0
  var l1 = _vm.showUserSelect
    ? _vm.__map(_vm.filteredUsers, function (user, idx) {
        var $orig = _vm.__get_orig(user)
        var m1 = user.role && _vm.getRoleText(user.role)
        var m2 = m1 ? _vm.getRoleText(user.role) : null
        return {
          $orig: $orig,
          m1: m1,
          m2: m2,
        }
      })
    : null
  var g2 = _vm.selectedDates.length || 0
  var g3 = _vm.selectedDates.length || 0
  var g4 = _vm.selectedDates.length
  var g5 = _vm.submitting || _vm.selectedDates.length === 0
  var g6 = _vm.selectedDates.length
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      $event.stopPropagation()
      _vm.showUserSelect = true
    }
    _vm.e1 = function ($event) {
      $event.stopPropagation()
      _vm.showUserSelect = false
    }
    _vm.e2 = function ($event) {
      _vm.showUserSelect = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        l0: l0,
        g1: g1,
        l1: l1,
        g2: g2,
        g3: g3,
        g4: g4,
        g5: g5,
        g6: g6,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 423:
/*!****************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?vue&type=script&lang=js& ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./batch-add.vue?vue&type=script&lang=js& */ 424);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 424:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _patrolApi = _interopRequireDefault(__webpack_require__(/*! @/utils/patrol-api.js */ 71));
var _date = __webpack_require__(/*! @/utils/date.js */ 72);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      submitting: false,
      formData: {
        namePrefix: '',
        area: '',
        shift_id: '',
        route_id: '',
        user_id: ''
      },
      selectedDates: [],
      shiftOptions: [{
        _id: '',
        name: '请选择班次',
        rounds: []
      }],
      shiftIndex: 0,
      routeOptions: [{
        _id: '',
        name: '请选择路线'
      }],
      routeIndex: 0,
      userOptions: [{
        _id: '',
        nickname: '请选择人员'
      }],
      userIndex: 0,
      selectedRoute: null,
      selectedShift: null,
      selectedUser: null,
      // 角色映射表
      roleNameMap: {},
      // 状态常量
      STATUS: {
        NOT_STARTED: 0,
        // 未开始
        IN_PROGRESS: 1,
        // 进行中
        COMPLETED: 2,
        // 已完成
        EXPIRED: 3,
        // 已超时
        CANCELLED: 4 // 已取消
      },

      timeZoneIndicator: null,
      // 用于显示当前时区
      createdTaskCount: 0,
      // 已创建任务数
      totalTaskCount: 0,
      // 总任务数
      currentDate: (0, _date.formatDate)(new Date(), 'YYYY-MM-DD'),
      startDate: '',
      endDate: '',
      // 日历相关数据
      weekdays: ['日', '一', '二', '三', '四', '五', '六'],
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth(),
      calendarDays: [],
      minDate: '2020-01-01',
      maxDate: '2030-12-31',
      pointsRefreshed: false,
      // 用户选择相关
      searchUserName: '',
      filteredUsers: [],
      showUserSelect: false
    };
  },
  onLoad: function onLoad() {
    var _this = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              // 检测并记录当前时区
              _this.timeZoneIndicator = (0, _date.detectTimeZone)();

              // 🔥 优化：并行加载数据提升用户体验
              _context.prev = 1;
              uni.showLoading({
                title: '加载中...'
              });

              // 并行加载减少用户等待时间
              _context.next = 5;
              return Promise.all([_this.loadShifts(), _this.loadRoutes(), _this.loadRoles(), _this.loadUsers()]);
            case 5:
              _context.next = 11;
              break;
            case 7:
              _context.prev = 7;
              _context.t0 = _context["catch"](1);
              console.error('初始化数据加载失败:', _context.t0);
              uni.showToast({
                title: '数据加载失败',
                icon: 'none'
              });
            case 11:
              _context.prev = 11;
              uni.hideLoading();
              return _context.finish(11);
            case 14:
              // 生成初始日历数据
              _this.generateCalendarDays();
            case 15:
            case "end":
              return _context.stop();
          }
        }
      }, _callee, null, [[1, 7, 11, 14]]);
    }))();
  },
  watch: {
    // 监听用户列表变化，初始化过滤后的列表
    userOptions: {
      handler: function handler(val) {
        this.filteredUsers = (0, _toConsumableArray2.default)(val);
      },
      immediate: true
    }
  },
  methods: {
    // 加载班次数据
    loadShifts: function loadShifts() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var res, shifts, systemInfo, platform, isMobile;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return _patrolApi.default.call({
                  name: 'patrol-shift',
                  action: 'getShiftList',
                  data: {
                    status: 1,
                    with_rounds: true,
                    with_detail: true
                  }
                });
              case 3:
                res = _context2.sent;
                if (res.code === 0 && res.data && res.data.list) {
                  // 只加载启用的班次
                  shifts = res.data.list.map(function (shift) {
                    return _objectSpread(_objectSpread({}, shift), {}, {
                      name: shift.name,
                      across_day: shift.across_day || false,
                      rounds: shift.rounds && Array.isArray(shift.rounds) ? shift.rounds.filter(function (r) {
                        return r.status !== 0;
                      }).map(function (round) {
                        return {
                          round: round.round,
                          name: round.name || "\u8F6E\u6B21".concat(round.round),
                          time: round.time,
                          start_time: round.start_time || round.time,
                          end_time: round.end_time || round.time,
                          day_offset: round.day_offset || 0,
                          duration: round.duration || 60,
                          status: round.status
                        };
                      }) : []
                    });
                  }); // 按照班次名称字母顺序排序
                  systemInfo = uni.getSystemInfoSync();
                  platform = systemInfo.platform;
                  isMobile = ['android', 'ios'].includes(platform); // 对班次进行排序
                  shifts.sort(function (a, b) {
                    var nameA = String(a.name || '').trim();
                    var nameB = String(b.name || '').trim();

                    // 使用 localeCompare 进行中文排序，统一使用 A-Z 排序
                    var compareResult = nameA.localeCompare(nameB, 'zh-CN');
                    return isMobile ? -compareResult : compareResult;
                  });

                  // 添加默认选项到排序后的数组前面
                  _this2.shiftOptions = [{
                    _id: '',
                    name: '请选择班次',
                    rounds: []
                  }].concat((0, _toConsumableArray2.default)(shifts));
                } else {
                  _this2.shiftOptions = [{
                    _id: '',
                    name: '请选择班次',
                    rounds: []
                  }];
                }
                _context2.next = 12;
                break;
              case 7:
                _context2.prev = 7;
                _context2.t0 = _context2["catch"](0);
                console.error('加载班次失败:', _context2.t0);
                _this2.shiftOptions = [{
                  _id: '',
                  name: '请选择班次',
                  rounds: []
                }];
                uni.showToast({
                  title: '加载班次失败',
                  icon: 'none'
                });
              case 12:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 7]]);
      }))();
    },
    // 加载路线数据
    loadRoutes: function loadRoutes() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var res;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _context3.next = 3;
                return _patrolApi.default.call({
                  name: 'patrol-route',
                  action: 'getRouteList',
                  data: {
                    status: 1
                  }
                });
              case 3:
                res = _context3.sent;
                if (res.code === 0 && res.data && res.data.list) {
                  // 只加载启用的路线
                  _this3.routeOptions = [{
                    _id: '',
                    name: '请选择路线'
                  }].concat((0, _toConsumableArray2.default)(res.data.list));
                } else {
                  _this3.routeOptions = [{
                    _id: '',
                    name: '请选择路线'
                  }];
                }
                _context3.next = 11;
                break;
              case 7:
                _context3.prev = 7;
                _context3.t0 = _context3["catch"](0);
                _this3.routeOptions = [{
                  _id: '',
                  name: '请选择路线'
                }];
                uni.showToast({
                  title: '加载路线失败',
                  icon: 'none'
                });
              case 11:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 7]]);
      }))();
    },
    // 获取当前用户ID
    getCurrentUserId: function getCurrentUserId() {
      try {
        // 尝试从本地存储获取用户信息
        var userInfo = uni.getStorageSync('uni-id-pages-userInfo');
        if (userInfo) {
          return userInfo._id || '';
        }
        return '';
      } catch (e) {
        return '';
      }
    },
    // 加载用户列表
    loadUsers: function loadUsers() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var currentUserId, result, userList;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.prev = 0;
                currentUserId = _this4.getCurrentUserId();
                if (currentUserId) {
                  _context4.next = 4;
                  break;
                }
                throw new Error('未能获取当前用户ID');
              case 4:
                _context4.next = 6;
                return _patrolApi.default.call({
                  name: 'patrol-user',
                  action: 'getUsers',
                  data: {
                    userid: currentUserId,
                    params: {
                      pageSize: 100,
                      field: 'nickname,avatar,role,username,wx_openid,identities' // 请求所需字段
                    }
                  }
                });
              case 6:
                result = _context4.sent;
                if (result.code === 0 && result.data) {
                  // 添加一个空选项作为第一个选项
                  _this4.userOptions = [{
                    _id: '',
                    nickname: '请选择执行人',
                    avatar: '/static/user/default-avatar.png',
                    role: []
                  }];

                  // 处理用户数据，过滤掉admin角色的用户
                  userList = result.data.list.filter(function (user) {
                    // 过滤掉admin角色用户
                    if (!user.role) return true; // 没有角色的保留
                    if (Array.isArray(user.role)) {
                      // 如果是数组，检查是否包含admin
                      return !user.role.includes('admin');
                    } else {
                      // 如果是字符串，检查是否为admin
                      return user.role !== 'admin';
                    }
                  }).filter(function (user) {
                    // 过滤掉以"匿名"开头的用户
                    var nickname = user.real_name || user.nickname || user.username || '';
                    return !nickname.startsWith('匿名');
                  }).map(function (user) {
                    // 确保role是数组
                    var userRoles = Array.isArray(user.role) ? user.role : [];
                    // 避免在没有roleNameMap的情况下调用map
                    var roleNames = '普通用户';
                    try {
                      if (userRoles.length > 0 && _this4.roleNameMap) {
                        roleNames = userRoles.map(function (roleId) {
                          return _this4.roleNameMap[roleId] || '未知角色';
                        }).join('、');
                      }
                    } catch (e) {
                      console.error('处理用户角色时出错:', e);
                      roleNames = '角色解析错误';
                    }
                    return {
                      _id: user._id,
                      nickname: user.real_name || user.nickname || user.username || '未命名用户',
                      avatar: user.avatar || '/static/user/default-avatar.png',
                      role: userRoles,
                      roleName: roleNames || '普通用户',
                      status: user.status,
                      wx_openid: user.wx_openid || null,
                      identities: user.identities || []
                    };
                  }); // 合并空选项和用户列表
                  _this4.userOptions = [].concat((0, _toConsumableArray2.default)(_this4.userOptions), (0, _toConsumableArray2.default)(userList));

                  // 初始选择第一项（请选择执行人）
                  _this4.userIndex = 0;
                  _this4.selectedUser = null;
                  _this4.formData.user_id = '';
                  if (_this4.userOptions.length <= 1) {
                    uni.showToast({
                      title: '暂无可用执行人员',
                      icon: 'none'
                    });
                  }
                } else {
                  _this4.userOptions = [{
                    _id: '',
                    nickname: '请选择执行人',
                    avatar: '/static/user/default-avatar.png',
                    role: []
                  }];
                  _this4.userIndex = 0;
                  _this4.selectedUser = null;
                }
                _context4.next = 15;
                break;
              case 10:
                _context4.prev = 10;
                _context4.t0 = _context4["catch"](0);
                console.error('加载用户失败:', _context4.t0);
                _this4.userOptions = [{
                  _id: '',
                  nickname: '请选择人员'
                }];
                uni.showToast({
                  title: '加载用户数据失败',
                  icon: 'none'
                });
              case 15:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[0, 10]]);
      }))();
    },
    // 加载角色数据
    loadRoles: function loadRoles() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                try {
                  // 使用更完整的角色映射
                  _this5.roleNameMap = {
                    'admin': '管理员',
                    'responsible': '责任人',
                    'reviser': '发布人',
                    'supervisor': '主管',
                    'PM': '副厂长',
                    'GM': '厂长',
                    'logistics': '后勤员',
                    'dispatch': '调度员',
                    'Integrated': '综合员',
                    'operator': '设备员',
                    'technician': '工艺员',
                    'mechanic': '技术员',
                    'user': '普通员工',
                    'manager': '管理人员',
                    'worker': '普通员工'
                  };

                  // 如果后续需要从服务器获取角色，可以使用正确的云函数
                  // 例如使用用户云函数获取角色信息
                  /*
                  const result = await PatrolApi.call({
                  	name: 'uni-id-co', // 使用uni-id云对象获取角色信息
                  	action: 'getRoleList'
                  });
                  
                  if (result.code === 0 && result.roleList && Array.isArray(result.roleList)) {
                  	// 构建角色映射
                  	this.roleNameMap = {};
                  	result.roleList.forEach(role => {
                  		this.roleNameMap[role.roleID] = role.roleName || '未知角色';
                  	});
                  }
                  */
                } catch (e) {
                  console.error('角色数据处理错误:', e);
                }
              case 1:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    // 获取角色文本
    getRoleText: function getRoleText(role) {
      var _this6 = this;
      if (!role) return '普通员工';

      // 处理数组形式的角色
      if (Array.isArray(role)) {
        if (role.length === 0) return '普通员工';
        // 返回所有角色的文本，用逗号分隔
        return role.map(function (r) {
          return _this6.getSingleRoleText(r);
        }).join(', ');
      }
      return this.getSingleRoleText(role);
    },
    // 处理单个角色值
    getSingleRoleText: function getSingleRoleText(role) {
      // 首先从角色表的映射中查找
      if (this.roleNameMap[role]) {
        return this.roleNameMap[role];
      }

      // 如果在角色表中找不到，使用预定义的映射作为备用
      var roleMap = {
        'admin': '管理员',
        'responsible': '责任人',
        'reviser': '发布人',
        'supervisor': '主管',
        'PM': '副厂长',
        'GM': '厂长',
        'logistics': '后勤员',
        'dispatch': '调度员',
        'Integrated': '综合员',
        'operator': '设备员',
        'technician': '工艺员',
        'mechanic': '技术员',
        'user': '普通员工',
        'manager': '管理人员'
      };
      return roleMap[role] || '用户 (' + role + ')';
    },
    // 获取日期范围显示文本
    getDateRangeText: function getDateRangeText() {
      if (this.dateRange && this.dateRange.length === 2) {
        var startDate = this.dateRange[0];
        var endDate = this.dateRange[1];
        return "".concat(startDate, " \u81F3 ").concat(endDate);
      }
      return '请选择日期范围';
    },
    // 日期选择变化处理
    onDateChange: function onDateChange(e) {
      if (e.detail.value) {
        this.currentDate = e.detail.value;
      }
    },
    // 班次选择变化
    onShiftChange: function onShiftChange(e) {
      var index = e.detail.value;
      this.shiftIndex = Number(index);

      // 更新选中的班次和班次ID
      this.selectedShift = this.shiftOptions[this.shiftIndex];
      this.formData.shift_id = this.selectedShift._id;
    },
    // 路线选择变化
    onRouteChange: function onRouteChange(e) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var index, selectedRoute, res, pointsData;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                index = e.detail.value;
                _this7.routeIndex = Number(index);

                // 获取选中的路线
                selectedRoute = _this7.routeOptions[_this7.routeIndex]; // 如果选择的是空选项（第一个选项）
                if (!(index === 0 || !selectedRoute || !selectedRoute._id)) {
                  _context6.next = 7;
                  break;
                }
                _this7.selectedRoute = null;
                _this7.formData.route_id = '';
                return _context6.abrupt("return");
              case 7:
                // 更新选中的路线和路线ID
                _this7.selectedRoute = selectedRoute;
                _this7.formData.route_id = selectedRoute._id;

                // 加载路线详情
                uni.showLoading({
                  title: '加载路线点位...',
                  mask: true
                });
                _context6.prev = 10;
                _context6.next = 13;
                return _patrolApi.default.call({
                  name: 'patrol-route',
                  action: 'getRouteDetail',
                  data: {
                    params: {
                      route_id: selectedRoute._id
                    },
                    with_points: true,
                    with_detail: true,
                    include_point_details: true
                  }
                });
              case 13:
                res = _context6.sent;
                uni.hideLoading();
                if (!(res.code === 0 && res.data)) {
                  _context6.next = 21;
                  break;
                }
                pointsData = [];
                if (res.data.pointsDetail && res.data.pointsDetail.length > 0) {
                  pointsData = _this7.processPointData(res.data.pointsDetail);
                } else if (res.data.points && res.data.points.length > 0) {
                  pointsData = _this7.processPointData(res.data.points);
                }
                _this7.selectedRoute = _objectSpread(_objectSpread(_objectSpread({}, _this7.selectedRoute), res.data), {}, {
                  pointsDetail: pointsData
                });
                _context6.next = 22;
                break;
              case 21:
                throw new Error(res.message || '获取路线详情失败');
              case 22:
                _context6.next = 29;
                break;
              case 24:
                _context6.prev = 24;
                _context6.t0 = _context6["catch"](10);
                console.error('加载路线详情失败:', _context6.t0);
                uni.hideLoading();
                uni.showToast({
                  title: _context6.t0.message || '加载路线详情失败',
                  icon: 'none'
                });
              case 29:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[10, 24]]);
      }))();
    },
    // 用户选择变化
    onUserChange: function onUserChange(e) {
      var index = e.detail.value;
      this.userIndex = Number(index);

      // 更新选中的用户和用户ID
      this.selectedUser = this.userOptions[this.userIndex];
      this.formData.user_id = this.selectedUser._id;
    },
    // 验证表单
    validateForm: function validateForm() {
      // 验证任务名称前缀
      if (!this.formData.namePrefix.trim()) {
        uni.showToast({
          title: '请输入任务名称前缀',
          icon: 'none'
        });
        return false;
      }

      // 验证日期范围
      if (this.selectedDates.length === 0) {
        uni.showToast({
          title: '请选择日期范围',
          icon: 'none'
        });
        return false;
      }

      // 验证班次
      if (!this.formData.shift_id) {
        uni.showToast({
          title: '请选择班次',
          icon: 'none'
        });
        return false;
      }

      // 验证路线
      if (!this.formData.route_id) {
        uni.showToast({
          title: '请选择巡检路线',
          icon: 'none'
        });
        return false;
      }

      // 验证执行人员
      if (!this.formData.user_id) {
        uni.showToast({
          title: '请选择执行人员',
          icon: 'none'
        });
        return false;
      }
      return true;
    },
    // 计算任务开始时间
    calculateTaskStatus: function calculateTaskStatus(startTimeStr) {
      // 如果没有有效的开始时间，默认为未开始
      if (!startTimeStr) {
        return this.STATUS.NOT_STARTED;
      }

      // 将字符串转为Date对象
      var startTime;
      try {
        startTime = new Date(startTimeStr);
      } catch (e) {
        console.error('解析任务开始时间出错:', e);
        return this.STATUS.NOT_STARTED;
      }

      // 获取当前时间
      var now = new Date();

      // 计算任务状态
      if (startTime > now) {
        // 开始时间在将来，任务未开始
        return this.STATUS.NOT_STARTED;
      } else {
        // 开始时间已过，任务进行中
        return this.STATUS.IN_PROGRESS;
      }
    },
    // 处理轮次数据
    processRoundsData: function processRoundsData(date) {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var shift, route, routePoints;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                if (!(!_this8.selectedShift || !_this8.selectedShift.rounds || !_this8.selectedRoute)) {
                  _context7.next = 3;
                  break;
                }
                console.warn('处理轮次数据: 缺少必要条件');
                return _context7.abrupt("return", []);
              case 3:
                shift = _this8.selectedShift;
                route = _this8.selectedRoute; // 验证路线是否有点位
                if (!(!route.pointsDetail || !Array.isArray(route.pointsDetail) || route.pointsDetail.length === 0)) {
                  _context7.next = 8;
                  break;
                }
                console.warn('处理轮次数据: 路线没有点位');
                return _context7.abrupt("return", []);
              case 8:
                // 提取路线点位 - 保持与单个任务创建页面相同的数据结构
                routePoints = route.pointsDetail.map(function (point, index) {
                  return {
                    point_id: point._id,
                    name: point.name || "\u70B9\u4F4D".concat(index + 1),
                    order: point.order || index,
                    status: 0,
                    // 初始状态：未打卡
                    location: point.location,
                    range: point.range || 50,
                    // 修正二维码配置的读取方式
                    qrcode_enabled: !!point.qrcode_enabled,
                    qrcode_required: !!point.qrcode_required,
                    qrcode_version: Number(point.qrcode_version || 0)
                  };
                }); // 处理每个轮次的数据
                return _context7.abrupt("return", shift.rounds.map(function (round) {
                  // 计算轮次的打卡时间
                  var checkDateTime = _this8.calculateRoundTime(date, round.start_time || round.time, round.day_offset || 0);
                  var checkTime = _this8.formatDate(checkDateTime, 'HH:mm');

                  // 计算轮次的实际有效天数偏移
                  var actualDayOffset = round.day_offset || 0;
                  if (shift.across_day) {
                    // 如果是跨天班次，且轮次时间在班次开始时间之前（如班次17:00开始，轮次在第二天8:00）
                    // 需要确保天数偏移至少为1
                    var _split$map = (round.start_time || round.time).split(':').map(Number),
                      _split$map2 = (0, _slicedToArray2.default)(_split$map, 1),
                      roundHours = _split$map2[0];
                    var _split$map3 = (shift.start_time || '00:00').split(':').map(Number),
                      _split$map4 = (0, _slicedToArray2.default)(_split$map3, 1),
                      shiftStartHours = _split$map4[0];
                    if (roundHours < shiftStartHours && actualDayOffset === 0) {
                      actualDayOffset = 1;
                      // 重新计算轮次打卡时间
                      checkDateTime = _this8.calculateRoundTime(date, round.start_time || round.time, actualDayOffset);
                      checkTime = _this8.formatDate(checkDateTime, 'HH:mm');
                    }
                  }

                  // 计算轮次结束时间
                  var endDateTime = _this8.calculateEndTime(checkDateTime, round.duration || 60);

                  // 为轮次中的每个点位分配相同的初始状态 - 保持与单个任务相同的结构
                  var roundPoints = routePoints.map(function (point) {
                    return _objectSpread(_objectSpread({}, point), {}, {
                      status: 0 // 初始状态：未打卡
                    });
                  });

                  // 计算轮次状态 - 根据当前时间和轮次预计开始时间
                  var now = new Date();
                  var roundStatus = 0; // 默认未开始

                  if (now > endDateTime) {
                    // 当前时间已超过轮次结束时间，标记为"已超时"
                    roundStatus = 3;
                    console.log("\u8F6E\u6B21".concat(round.round, "\u5DF2\u8D85\u65F6: \u5F53\u524D\u65F6\u95F4").concat(now.toLocaleString(), " > \u7ED3\u675F\u65F6\u95F4").concat(endDateTime.toLocaleString()));
                  } else if (now >= checkDateTime) {
                    // 当前时间在轮次的开始和结束时间之间，标记为"进行中"
                    roundStatus = 1;
                    console.log("\u8F6E\u6B21".concat(round.round, "\u8FDB\u884C\u4E2D: \u5F00\u59CB\u65F6\u95F4").concat(checkDateTime.toLocaleString(), " <= \u5F53\u524D\u65F6\u95F4").concat(now.toLocaleString(), " <= \u7ED3\u675F\u65F6\u95F4").concat(endDateTime.toLocaleString()));
                  } else {
                    // 未到开始时间，未开始
                    roundStatus = 0;
                    console.log("\u8F6E\u6B21".concat(round.round, "\u672A\u5F00\u59CB: \u5F53\u524D\u65F6\u95F4").concat(now.toLocaleString(), " < \u5F00\u59CB\u65F6\u95F4").concat(checkDateTime.toLocaleString()));
                  }
                  return {
                    round: round.round,
                    name: round.name || "\u8F6E\u6B21".concat(round.round),
                    time: checkTime,
                    check_time: checkDateTime.toISOString(),
                    start_time: checkDateTime.toISOString(),
                    end_time: endDateTime.toISOString(),
                    day_offset: actualDayOffset,
                    duration: round.duration || 60,
                    status: roundStatus,
                    // 强制设置为未开始状态
                    across_day: shift.across_day,
                    points: roundPoints,
                    stats: {
                      total_points: roundPoints.length,
                      completed_points: 0,
                      missed_points: roundPoints.length,
                      completion_rate: 0,
                      abnormal_count: 0
                    }
                  };
                }));
              case 10:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7);
      }))();
    },
    // 计算整体统计数据
    calculateOverallStats: function calculateOverallStats(rounds) {
      // 如果没有轮次，返回默认统计数据
      if (!rounds || rounds.length === 0) {
        return {
          total_points: 0,
          completed_points: 0,
          missed_points: 0,
          completion_rate: 0,
          abnormal_count: 0,
          last_checkin_time: null // 初始无打卡记录
        };
      }

      // 计算所有轮次的统计数据
      var totalPoints = 0;
      var completedPoints = 0;
      var abnormalCount = 0;
      rounds.forEach(function (round) {
        if (round.stats) {
          totalPoints += round.stats.total_points || 0;
          completedPoints += round.stats.completed_points || 0;
          abnormalCount += round.stats.abnormal_count || 0;
        }
      });

      // 计算未完成点位数和完成率
      var missedPoints = totalPoints - completedPoints;
      var completionRate = totalPoints > 0 ? completedPoints / totalPoints : 0;
      return {
        total_points: totalPoints,
        completed_points: completedPoints,
        missed_points: missedPoints,
        completion_rate: completionRate,
        abnormal_count: abnormalCount,
        last_checkin_time: null // 初始无打卡记录
      };
    },
    // 处理批量任务表单数据
    prepareTaskData: function prepareTaskData() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var tasks, i, _this9$selectedShift, _this9$selectedShift$, _roundsData$, date, roundsData, statsData, enabledRounds, task;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                if (!(!_this9.selectedDates || _this9.selectedDates.length === 0)) {
                  _context8.next = 3;
                  break;
                }
                uni.showToast({
                  title: '请选择执行日期',
                  icon: 'none'
                });
                return _context8.abrupt("return", null);
              case 3:
                if (!(!_this9.selectedRoute || !_this9.selectedRoute._id)) {
                  _context8.next = 6;
                  break;
                }
                uni.showToast({
                  title: '请选择巡检路线',
                  icon: 'none'
                });
                return _context8.abrupt("return", null);
              case 6:
                if (_this9.selectedUser) {
                  _context8.next = 9;
                  break;
                }
                uni.showToast({
                  title: '请选择执行人员',
                  icon: 'none'
                });
                return _context8.abrupt("return", null);
              case 9:
                if (_this9.selectedShift) {
                  _context8.next = 12;
                  break;
                }
                uni.showToast({
                  title: '请选择班次',
                  icon: 'none'
                });
                return _context8.abrupt("return", null);
              case 12:
                // 显示加载提示
                uni.showLoading({
                  title: '正在处理数据...'
                });
                _context8.prev = 13;
                // 创建任务基础对象
                tasks = []; // 处理选中的每一天
                i = 0;
              case 16:
                if (!(i < _this9.selectedDates.length)) {
                  _context8.next = 31;
                  break;
                }
                date = _this9.selectedDates[i]; // 为当前日期处理轮次数据
                _context8.next = 20;
                return _this9.processRoundsData(date);
              case 20:
                roundsData = _context8.sent;
                if (!(!roundsData || roundsData.length === 0)) {
                  _context8.next = 24;
                  break;
                }
                console.error('处理轮次数据失败:', date);
                return _context8.abrupt("continue", 28);
              case 24:
                // 获取当前日期的统计数据
                statsData = _this9.calculateOverallStats(roundsData); // 获取enabled_rounds（启用的轮次编号数组）
                enabledRounds = ((_this9$selectedShift = _this9.selectedShift) === null || _this9$selectedShift === void 0 ? void 0 : (_this9$selectedShift$ = _this9$selectedShift.rounds) === null || _this9$selectedShift$ === void 0 ? void 0 : _this9$selectedShift$.map(function (r) {
                  return r.round;
                })) || []; // 创建任务对象
                task = {
                  patrol_date: _this9.formatDate(date),
                  // 格式化为'YYYY-MM-DD'
                  route_id: _this9.selectedRoute._id,
                  route_name: _this9.selectedRoute.name,
                  area: _this9.selectedRoute.name,
                  // 区域默认使用路线名称
                  shift_id: _this9.selectedShift._id,
                  shift_name: _this9.selectedShift.name,
                  shift_type: _this9.selectedShift.type || 1,
                  across_day: !!_this9.selectedShift.across_day,
                  user_id: _this9.selectedUser._id,
                  user_name: _this9.selectedUser.name,
                  role_id: _this9.selectedUser.role || '',
                  role_name: _this9.selectedUser.roleName || _this9.getRoleText(_this9.selectedUser.role),
                  rounds_detail: roundsData,
                  enabled_rounds: enabledRounds,
                  // 添加启用的轮次
                  is_urgent: _this9.isUrgent ? 1 : 0,
                  status: _this9.calculateTaskStatus((_roundsData$ = roundsData[0]) === null || _roundsData$ === void 0 ? void 0 : _roundsData$.start_time),
                  // 使用calculateTaskStatus计算正确的状态
                  remark: _this9.remark || '',
                  stats: statsData,
                  overall_stats: statsData,
                  // 添加overall_stats字段
                  last_check_time: new Date().toISOString(),
                  // 添加last_check_time字段
                  create_time: new Date().toISOString(),
                  update_time: new Date().toISOString(),
                  auto_closed: false
                };
                tasks.push(task);
              case 28:
                i++;
                _context8.next = 16;
                break;
              case 31:
                return _context8.abrupt("return", tasks);
              case 34:
                _context8.prev = 34;
                _context8.t0 = _context8["catch"](13);
                console.error('准备批量任务数据时出错:', _context8.t0);
                return _context8.abrupt("return", null);
              case 38:
                _context8.prev = 38;
                uni.hideLoading();
                return _context8.finish(38);
              case 41:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[13, 34, 38, 41]]);
      }))();
    },
    // 批量创建任务
    submitForm: function submitForm() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                if (_this10.validateForm()) {
                  _context10.next = 2;
                  break;
                }
                return _context10.abrupt("return");
              case 2:
                // 确认创建多个任务
                uni.showModal({
                  title: '确认创建',
                  content: "\u60A8\u5C06\u4E3A ".concat(_this10.selectedUser.nickname, " \u6279\u91CF\u521B\u5EFA ").concat(_this10.selectedDates.length, " \u4E2A\u4EFB\u52A1\uFF0C\u786E\u5B9A\u7EE7\u7EED\u5417\uFF1F"),
                  success: function () {
                    var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9(res) {
                      return _regenerator.default.wrap(function _callee9$(_context9) {
                        while (1) {
                          switch (_context9.prev = _context9.next) {
                            case 0:
                              if (!res.confirm) {
                                _context9.next = 3;
                                break;
                              }
                              _context9.next = 3;
                              return _this10.createBatchTasks();
                            case 3:
                            case "end":
                              return _context9.stop();
                          }
                        }
                      }, _callee9);
                    }));
                    function success(_x) {
                      return _success.apply(this, arguments);
                    }
                    return success;
                  }()
                });
              case 3:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10);
      }))();
    },
    // 执行批量创建任务
    createBatchTasks: function createBatchTasks() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        var successTasks, failedTasks, allTasks, i, taskData, date, response;
        return _regenerator.default.wrap(function _callee11$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                // 显示加载状态
                _this11.submitting = true;
                _context11.prev = 1;
                // 初始化计数器
                _this11.createdTaskCount = 0;
                _this11.totalTaskCount = _this11.selectedDates.length;

                // 显示进度弹窗
                uni.showLoading({
                  title: "\u521B\u5EFA\u4E2D(0/".concat(_this11.totalTaskCount, ")"),
                  mask: true
                });

                // 成功和失败的任务列表
                successTasks = [];
                failedTasks = []; // 预先准备所有任务数据
                _context11.next = 9;
                return _this11.prepareTaskData();
              case 9:
                allTasks = _context11.sent;
                if (!(!allTasks || allTasks.length === 0)) {
                  _context11.next = 12;
                  break;
                }
                throw new Error('准备任务数据失败');
              case 12:
                i = 0;
              case 13:
                if (!(i < allTasks.length)) {
                  _context11.next = 34;
                  break;
                }
                taskData = allTasks[i];
                date = _this11.selectedDates[i];
                _context11.prev = 16;
                // 更新进度提示
                uni.showLoading({
                  title: "\u521B\u5EFA\u4E2D(".concat(i + 1, "/").concat(_this11.totalTaskCount, ")"),
                  mask: true
                });

                // 使用纯前缀作为任务名称，不再添加日期
                taskData.name = _this11.formData.namePrefix;

                // 确保必要字段都存在，服务器期望的数据结构
                if (!taskData.last_check_time) {
                  taskData.last_check_time = new Date().toISOString();
                }
                if (!taskData.overall_stats && taskData.stats) {
                  taskData.overall_stats = _objectSpread(_objectSpread({}, taskData.stats), {}, {
                    last_checkin_time: new Date().toISOString()
                  });
                }

                // 添加必须的auto_closed字段
                taskData.auto_closed = false;

                // 发送请求创建任务
                _context11.next = 24;
                return _patrolApi.default.addTask(taskData);
              case 24:
                response = _context11.sent;
                // 处理响应
                if (response && response.code === 0 && response.data && response.data._id) {
                  successTasks.push({
                    date: date,
                    name: taskData.name,
                    id: response.data._id
                  });
                  _this11.createdTaskCount++;
                } else {
                  failedTasks.push({
                    date: date,
                    name: taskData.name,
                    error: response.message || '未知错误'
                  });
                }
                _context11.next = 31;
                break;
              case 28:
                _context11.prev = 28;
                _context11.t0 = _context11["catch"](16);
                failedTasks.push({
                  date: date,
                  name: taskData.name,
                  error: _context11.t0.message || '请求异常'
                });
              case 31:
                i++;
                _context11.next = 13;
                break;
              case 34:
                // 关闭加载提示
                uni.hideLoading();

                // 显示结果
                if (successTasks.length > 0) {
                  uni.showToast({
                    title: "\u6210\u529F\u521B\u5EFA".concat(successTasks.length, "\u4E2A\u4EFB\u52A1"),
                    icon: 'success',
                    duration: 2000
                  });

                  // 触发刷新任务列表事件
                  uni.$emit('refresh-task-list');

                  // 如果有失败的任务，显示详情
                  if (failedTasks.length > 0) {
                    setTimeout(function () {
                      uni.showModal({
                        title: '部分任务创建失败',
                        content: "\u6210\u529F: ".concat(successTasks.length, "\u4E2A, \u5931\u8D25: ").concat(failedTasks.length, "\u4E2A"),
                        showCancel: false
                      });
                    }, 1500);
                  } else {
                    // 全部成功，返回上一页
                    setTimeout(function () {
                      uni.navigateBack();
                    }, 1500);
                  }
                } else {
                  uni.showModal({
                    title: '创建失败',
                    content: '所有任务创建失败，请检查网络或参数设置',
                    showCancel: false
                  });
                }
                _context11.next = 42;
                break;
              case 38:
                _context11.prev = 38;
                _context11.t1 = _context11["catch"](1);
                uni.hideLoading();
                uni.showToast({
                  title: '批量创建失败: ' + (_context11.t1.message || '未知错误'),
                  icon: 'none'
                });
              case 42:
                _context11.prev = 42;
                _this11.submitting = false;
                return _context11.finish(42);
              case 45:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee11, null, [[1, 38, 42, 45], [16, 28]]);
      }))();
    },
    // 返回上一页
    navigateBack: function navigateBack() {
      uni.navigateBack();
    },
    // 移除单个日期
    removeDate: function removeDate(date) {
      var index = this.selectedDates.findIndex(function (d) {
        return d === date;
      });
      if (index !== -1) {
        this.selectedDates.splice(index, 1);
      }
    },
    // 清空所有已选日期
    clearSelectedDates: function clearSelectedDates() {
      // 如果没有选择日期，不执行操作
      if (this.selectedDates.length === 0) {
        return;
      }

      // 清空选中的日期
      this.selectedDates = [];

      // 重新生成日历日期，确保UI更新
      this.generateCalendarDays();

      // 提示用户
      uni.showToast({
        title: '已清空所选日期',
        icon: 'none',
        duration: 1500
      });
    },
    addSelectedDate: function addSelectedDate() {
      if (!this.currentDate) {
        uni.showToast({
          title: '请先选择日期',
          icon: 'none'
        });
        return;
      }

      // 检查是否已存在相同日期
      var exists = this.selectedDates.includes(this.currentDate);
      if (exists) {
        uni.showToast({
          title: '该日期已添加',
          icon: 'none'
        });
        return;
      }

      // 添加到已选日期列表
      this.selectedDates.push(this.currentDate);

      // 显示成功提示
      uni.showToast({
        title: '已添加',
        icon: 'success'
      });
    },
    formatDisplayDate: function formatDisplayDate(dateStr) {
      try {
        var date = new Date(dateStr);
        return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5");
      } catch (e) {
        return dateStr;
      }
    },
    onStartDateChange: function onStartDateChange(e) {
      if (e.detail.value) {
        this.startDate = e.detail.value;
      }
    },
    onEndDateChange: function onEndDateChange(e) {
      if (e.detail.value) {
        this.endDate = e.detail.value;
      }
    },
    addDateRange: function addDateRange() {
      var _this12 = this;
      if (!this.startDate || !this.endDate) {
        uni.showToast({
          title: '请选择开始和结束日期',
          icon: 'none'
        });
        return;
      }
      var startDate = new Date(this.startDate.replace(/-/g, '/'));
      var endDate = new Date(this.endDate.replace(/-/g, '/'));

      // 验证开始日期不能大于结束日期
      if (startDate > endDate) {
        uni.showToast({
          title: '开始日期不能晚于结束日期',
          icon: 'none'
        });
        return;
      }

      // 计算日期差
      var daysDiff = (0, _date.getDaysDiff)(startDate, endDate);

      // 日期范围过大时提示
      if (daysDiff > 60) {
        uni.showModal({
          title: '提示',
          content: "\u60A8\u9009\u62E9\u4E86".concat(daysDiff + 1, "\u5929\u7684\u65E5\u671F\u8303\u56F4\uFF0C\u53EF\u80FD\u4F1A\u521B\u5EFA\u5927\u91CF\u4EFB\u52A1\uFF0C\u786E\u5B9A\u7EE7\u7EED\u5417\uFF1F"),
          success: function success(res) {
            if (res.confirm) {
              _this12.generateDatesFromRange(startDate, endDate);
            }
          }
        });
      } else {
        this.generateDatesFromRange(startDate, endDate);
      }
    },
    // 从范围生成日期数组
    generateDatesFromRange: function generateDatesFromRange(startDate, endDate) {
      var _this13 = this;
      var dates = [];
      var currentDate = new Date(startDate);
      while (currentDate <= endDate) {
        dates.push((0, _date.formatDate)(currentDate, 'YYYY-MM-DD'));
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // 合并到已选日期，去重
      var newDates = dates.filter(function (date) {
        return !_this13.selectedDates.includes(date);
      });
      if (newDates.length === 0) {
        uni.showToast({
          title: '所选日期已全部添加',
          icon: 'none'
        });
        return;
      }
      this.selectedDates = [].concat((0, _toConsumableArray2.default)(this.selectedDates), (0, _toConsumableArray2.default)(newDates));
      uni.showToast({
        title: "\u5DF2\u6DFB\u52A0".concat(newDates.length, "\u4E2A\u65E5\u671F"),
        icon: 'success'
      });

      // 清空范围选择
      // this.startDate = '';
      // this.endDate = '';
    },
    // 生成日历数据
    generateCalendarDays: function generateCalendarDays() {
      var year = this.currentYear;
      var month = this.currentMonth;

      // 获取当月第一天
      var firstDay = new Date(year, month, 1);
      // 获取当月最后一天
      var lastDay = new Date(year, month + 1, 0);

      // 当月天数
      var daysInMonth = lastDay.getDate();

      // 当月第一天是星期几
      var firstDayWeek = firstDay.getDay();

      // 日历中显示的总天数（前一月尾部 + 当月 + 下一月头部）
      var totalDays = 42; // 6行7列

      // 获取今天日期
      var today = new Date();
      var todayFullDate = (0, _date.formatDate)(today, 'YYYY-MM-DD');

      // 准备日历数据
      this.calendarDays = [];

      // 添加上个月的日期
      var prevMonthLastDay = new Date(year, month, 0).getDate();
      for (var i = 0; i < firstDayWeek; i++) {
        var date = prevMonthLastDay - firstDayWeek + i + 1;
        var prevYear = month === 0 ? year - 1 : year;
        var prevMonth = month === 0 ? 11 : month - 1;
        var fullDate = (0, _date.formatDate)(new Date(prevYear, prevMonth, date), 'YYYY-MM-DD');
        var disabled = this.isDisabledDate(fullDate);
        this.calendarDays.push({
          date: date,
          fullDate: fullDate,
          currentMonth: false,
          isToday: fullDate === todayFullDate,
          disabled: disabled
        });
      }

      // 添加当月日期
      for (var _i = 1; _i <= daysInMonth; _i++) {
        var _fullDate = (0, _date.formatDate)(new Date(year, month, _i), 'YYYY-MM-DD');
        var _disabled = this.isDisabledDate(_fullDate);
        this.calendarDays.push({
          date: _i,
          fullDate: _fullDate,
          currentMonth: true,
          isToday: _fullDate === todayFullDate,
          disabled: _disabled
        });
      }

      // 添加下个月的日期
      var remainingDays = totalDays - this.calendarDays.length;
      for (var _i2 = 1; _i2 <= remainingDays; _i2++) {
        var nextYear = month === 11 ? year + 1 : year;
        var nextMonth = month === 11 ? 0 : month + 1;
        var _fullDate2 = (0, _date.formatDate)(new Date(nextYear, nextMonth, _i2), 'YYYY-MM-DD');
        var _disabled2 = this.isDisabledDate(_fullDate2);
        this.calendarDays.push({
          date: _i2,
          fullDate: _fullDate2,
          currentMonth: false,
          isToday: _fullDate2 === todayFullDate,
          disabled: _disabled2
        });
      }
    },
    // 切换到上个月
    prevMonth: function prevMonth() {
      if (this.currentMonth === 0) {
        this.currentYear--;
        this.currentMonth = 11;
      } else {
        this.currentMonth--;
      }
      this.generateCalendarDays();
    },
    // 切换到下个月
    nextMonth: function nextMonth() {
      if (this.currentMonth === 11) {
        this.currentYear++;
        this.currentMonth = 0;
      } else {
        this.currentMonth++;
      }
      this.generateCalendarDays();
    },
    // 检查日期是否被选中
    isDateSelected: function isDateSelected(date) {
      return this.selectedDates.includes(date);
    },
    // 检查日期是否禁用
    isDisabledDate: function isDisabledDate(dateStr) {
      try {
        var date = new Date(dateStr.replace(/-/g, '/'));
        var minDate = new Date(this.minDate.replace(/-/g, '/'));
        var maxDate = new Date(this.maxDate.replace(/-/g, '/'));
        return date < minDate || date > maxDate;
      } catch (e) {
        return true;
      }
    },
    // 切换日期选择状态
    toggleDateSelection: function toggleDateSelection(day) {
      if (day.disabled) return;
      var fullDate = day.fullDate;
      var index = this.selectedDates.indexOf(fullDate);
      if (index === -1) {
        // 添加日期
        this.selectedDates.push(fullDate);
      } else {
        // 移除日期
        this.selectedDates.splice(index, 1);
      }
    },
    // 刷新路线点位数据
    refreshRoutePointsData: function refreshRoutePointsData() {
      var _this14 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12() {
        var res, pointsData;
        return _regenerator.default.wrap(function _callee12$(_context12) {
          while (1) {
            switch (_context12.prev = _context12.next) {
              case 0:
                if (_this14.formData.route_id) {
                  _context12.next = 3;
                  break;
                }
                uni.showToast({
                  title: '请先选择路线',
                  icon: 'none'
                });
                return _context12.abrupt("return");
              case 3:
                _context12.prev = 3;
                uni.showLoading({
                  title: '刷新中...',
                  mask: true
                });
                _context12.next = 7;
                return _patrolApi.default.call({
                  name: 'patrol-route',
                  action: 'getRouteDetail',
                  data: {
                    params: {
                      route_id: _this14.formData.route_id,
                      with_points: true,
                      with_detail: true,
                      include_point_details: true,
                      force_refresh: true
                    }
                  }
                });
              case 7:
                res = _context12.sent;
                uni.hideLoading();
                if (!(res.code === 0 && res.data)) {
                  _context12.next = 17;
                  break;
                }
                pointsData = [];
                if (res.data.pointsDetail && res.data.pointsDetail.length > 0) {
                  pointsData = _this14.processPointData(res.data.pointsDetail);
                } else if (res.data.points && res.data.points.length > 0) {
                  pointsData = _this14.processPointData(res.data.points);
                }
                _this14.selectedRoute = _objectSpread(_objectSpread({}, _this14.selectedRoute), {}, {
                  pointsDetail: pointsData
                });
                _this14.pointsRefreshed = true;
                uni.showToast({
                  title: '点位数据已更新',
                  icon: 'success'
                });
                _context12.next = 18;
                break;
              case 17:
                throw new Error(res.message || '获取最新点位数据失败');
              case 18:
                _context12.next = 24;
                break;
              case 20:
                _context12.prev = 20;
                _context12.t0 = _context12["catch"](3);
                uni.hideLoading();
                uni.showToast({
                  title: '刷新点位数据失败',
                  icon: 'none'
                });
              case 24:
              case "end":
                return _context12.stop();
            }
          }
        }, _callee12, null, [[3, 20]]);
      }))();
    },
    // 处理API返回的点位数据
    processPointData: function processPointData(data) {
      if (!data || !Array.isArray(data) || data.length === 0) {
        return [];
      }
      var hasDetailedInfo = data[0].name !== undefined && data[0].name !== null;
      if (hasDetailedInfo) {
        return data.map(function (point) {
          return {
            _id: point._id || point.point_id,
            name: point.name,
            range: point.range || 50,
            order: point.order || 0,
            location: point.location || null,
            qrcode_enabled: !!point.qrcode_enabled,
            qrcode_required: !!point.qrcode_required,
            qrcode_version: Number(point.qrcode_version || 0),
            original: point
          };
        });
      } else {
        return data.map(function (point, index) {
          return {
            _id: point._id || point.point_id || "point_".concat(index),
            name: "\u70B9\u4F4D".concat(index + 1),
            range: 50,
            order: point.order || index,
            location: null,
            qrcode_enabled: false,
            qrcode_required: false,
            qrcode_version: 0,
            original: point
          };
        });
      }
    },
    // 日期格式化工具函数
    formatDate: function formatDate(date) {
      var format = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'YYYY-MM-DD';
      // 确保date是Date对象
      var d;
      if (typeof date === 'string') {
        // 处理ISO字符串
        d = new Date(date);
      } else if (date instanceof Date) {
        d = date;
      } else {
        // 默认使用当前日期
        d = new Date();
        console.warn('格式化日期: 使用当前日期作为默认值');
      }

      // 防止无效日期
      if (isNaN(d.getTime())) {
        console.error('格式化日期: 无效日期', date);
        return '';
      }
      var year = d.getFullYear();
      var month = String(d.getMonth() + 1).padStart(2, '0');
      var day = String(d.getDate()).padStart(2, '0');
      var hours = String(d.getHours()).padStart(2, '0');
      var minutes = String(d.getMinutes()).padStart(2, '0');
      var seconds = String(d.getSeconds()).padStart(2, '0');

      // 替换格式字符串
      return format.replace('YYYY', year).replace('MM', month).replace('DD', day).replace('HH', hours).replace('mm', minutes).replace('ss', seconds);
    },
    // 计算轮次时间工具函数
    calculateRoundTime: function calculateRoundTime(date, timeStr) {
      var dayOffset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
      // 确保date是Date对象
      var baseDate;
      if (typeof date === 'string') {
        // 处理可能的格式，包括YYYY-MM-DD或带时区的ISO字符串
        if (date.includes('T')) {
          baseDate = new Date(date);
        } else {
          // 纯日期字符串，添加时间部分
          baseDate = new Date(date + 'T00:00:00');
        }
      } else if (date instanceof Date) {
        baseDate = new Date(date);
      } else {
        // 默认使用当前日期
        baseDate = new Date();
        console.warn('计算轮次时间: 使用当前日期作为默认值');
      }

      // 重置时间部分为00:00:00
      baseDate.setHours(0, 0, 0, 0);

      // 应用天数偏移
      if (dayOffset && !isNaN(dayOffset)) {
        baseDate.setDate(baseDate.getDate() + parseInt(dayOffset));
      }

      // 解析时间字符串
      if (timeStr && typeof timeStr === 'string') {
        var _timeStr$split$map = timeStr.split(':').map(function (num) {
            return parseInt(num, 10);
          }),
          _timeStr$split$map2 = (0, _slicedToArray2.default)(_timeStr$split$map, 2),
          hours = _timeStr$split$map2[0],
          minutes = _timeStr$split$map2[1];

        // 应用时间
        if (!isNaN(hours) && !isNaN(minutes)) {
          baseDate.setHours(hours, minutes, 0, 0);
        }
      }
      return baseDate;
    },
    // 计算结束时间工具函数
    calculateEndTime: function calculateEndTime(startTime) {
      var durationMinutes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 60;
      // 确保startTime是Date对象
      var start = new Date(startTime);
      // 添加持续时间
      return new Date(start.getTime() + durationMinutes * 60 * 1000);
    },
    // 过滤用户选项
    filterUserOptions: function filterUserOptions() {
      var searchText = this.searchUserName.toLowerCase();
      if (!searchText) {
        // 如果搜索框为空，显示所有用户
        this.filteredUsers = (0, _toConsumableArray2.default)(this.userOptions);
      } else {
        this.filteredUsers = this.userOptions.filter(function (user) {
          return user.nickname && user.nickname.toLowerCase().includes(searchText) && (user._id === '' || !user.nickname.startsWith('匿名'));
        } // 确保搜索结果也遵循匿名用户过滤规则，但保留默认选项
        );
      }
    },
    // 选择用户
    selectUser: function selectUser(index) {
      var user = this.filteredUsers[index];
      if (user && user._id) {
        this.formData.user_id = user._id;
        this.selectedUser = user;
        // 更新userIndex以保持与之前逻辑的兼容性
        var originalIndex = this.userOptions.findIndex(function (u) {
          return u._id === user._id;
        });
        if (originalIndex !== -1) {
          this.userIndex = originalIndex;
        }
      } else {
        this.formData.user_id = '';
        this.selectedUser = null;
      }
      this.showUserSelect = false;
    },
    // 隐藏用户选择器
    hideUserSelect: function hideUserSelect() {
      this.showUserSelect = false;
    }
  },
  computed: {
    calendarTitle: function calendarTitle() {
      return "".concat(this.currentYear, "\u5E74").concat(this.currentMonth + 1, "\u6708");
    }
  },
  mounted: function mounted() {
    this.generateCalendarDays();
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 425:
/*!*************************************************************************************!*\
  !*** D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?vue&type=style&index=0&lang=scss& ***!
  \*************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./batch-add.vue?vue&type=style&index=0&lang=scss& */ 426);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_batch_add_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 426:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?vue&type=style&index=0&lang=scss& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[419,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/patrol_pkg/task/batch-add.js.map